module.exports = {
  apps: [
    {
      name: 'dstatus',
      script: './dstatus.js',
      cwd: '/root/repo',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 5555
      },
      error_file: '/root/repo/logs/dstatus-error.log',
      out_file: '/root/repo/logs/dstatus-out.log',
      log_file: '/root/repo/logs/dstatus-combined.log',
      time: true,
      max_restarts: 10,
      min_uptime: '10s',
      restart_delay: 5000
    }
  ]
};