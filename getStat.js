"use strict";
const ssh=require("./ssh");
function sleep(ms){return new Promise(resolve=>setTimeout(()=>resolve(),ms));};
function analyze(data,interval=0.2){
    try{
    var [s1,s2,mem_res]=data.split('\n----\n'),
        [cpu1,net1]=s1.split('\n---\n'),
        [cpu2,net2]=s2.split('\n---\n');
    mem_res=mem_res.split('\n');
    }
    catch(e){return false}

    var sys1=[],cput1=[],sys2=[],cput2=[],per=[];
    for(var l of cpu1.split('\n')){
        var t=l.split(' '),tot=0;
        sys1.push(Number(t[3]));
        for(var x of t)tot+=Number(x);
        cput1.push(tot);
    }
    for(var l of cpu2.split('\n')){
        var t=l.split(' '),tot=0;
        sys2.push(Number(t[3]));
        for(var x of t)tot+=Number(x);
        cput2.push(tot);
    }
    for(var i in sys1)
        per.push(1-(sys2[i]-sys1[i])/(cput2[i]-cput1[i]));
    var cpu={
        multi:per.shift(),
        single:per,
    };

    var i1=0,o1=0,i2=0,o2=0;
    for(var l of net1.split('\n')){
        var t=l.trim().split(/\s+/);
        i1+=Number(t[1]),o1+=Number(t[9]);
    }
    for(var l of net2.split('\n')){
        var t=l.trim().split(/\s+/);
        i2+=Number(t[1]),o2+=Number(t[9]);
    }
    
    // 验证流量数据的合理性
    if(i2 < i1 || o2 < o1) {
        console.warn('检测到流量数据异常：流量值减少，可能是计数器重置');
        // 如果流量计数器重置，返回特殊标记
        return {
            cpu,
            mem: parseMemory(mem_res),
            net: {
                delta: {in: 0, out: 0},
                total: {in: i2, out: o2},
                reset: true  // 标记为重置
            }
        };
    }
    
    // 检查增量是否异常大（例如超过1Gbps）
    const maxBytesPerSecond = 125000000; // 1Gbps = 125MB/s
    let deltaIn = (i2-i1)/interval;
    let deltaOut = (o2-o1)/interval;
    
    if(deltaIn > maxBytesPerSecond || deltaOut > maxBytesPerSecond) {
        console.warn(`检测到异常高的流量速率：in=${deltaIn/1000000}MB/s, out=${deltaOut/1000000}MB/s`);
        // 限制到合理范围
        deltaIn = Math.min(deltaIn, maxBytesPerSecond);
        deltaOut = Math.min(deltaOut, maxBytesPerSecond);
    }
    
    var net={
        delta:{in: deltaIn, out: deltaOut},
        total:{in: i2, out: o2}
    };

    // 解析内存信息
    function parseMemory(mem_res) {
        var Mem=mem_res[0].split(/\s+/),Swap=mem_res[1].split(/\s+/),MEM=[],SWAP=[];
        for(var x of Mem)MEM.push(Number(x)*1000);
        for(var x of Swap)SWAP.push(Number(x)*1000);
        return {
            virtual:{total:MEM[1],used:MEM[2],free:MEM[3],shared:MEM[4],cache:MEM[5],available:MEM[6]},
            swap:{total:SWAP[1],used:SWAP[2],free:SWAP[3]}
        };
    }
    
    /**
     * 计算CPU使用率差分
     */
    function calculateCpuDelta(lastCpuStr, currentCpuStr) {
        try {
            const lastLines = lastCpuStr.split('\n');
            const currentLines = currentCpuStr.split('\n');
            
            var sys1=[], cput1=[], sys2=[], cput2=[], per=[];
            
            for(var l of lastLines) {
                var t = l.split(' '), tot = 0;
                sys1.push(Number(t[3]));
                for(var x of t) tot += Number(x);
                cput1.push(tot);
            }
            
            for(var l of currentLines) {
                var t = l.split(' '), tot = 0;
                sys2.push(Number(t[3]));
                for(var x of t) tot += Number(x);
                cput2.push(tot);
            }
            
            for(var i in sys1) {
                const delta = cput2[i] - cput1[i];
                if (delta > 0) {
                    per.push(1 - (sys2[i] - sys1[i]) / delta);
                } else {
                    per.push(0);
                }
            }
            
            return {
                multi: per.shift(),
                single: per
            };
        } catch(e) {
            return { multi: 0, single: [] };
        }
    }
    
    /**
     * 计算网络速率差分
     */
    function calculateNetDelta(lastNetStr, currentNetStr, timeDelta) {
        try {
            var i1=0, o1=0, i2=0, o2=0;
            
            for(var l of lastNetStr.split('\n')) {
                var t = l.trim().split(/\s+/);
                i1 += Number(t[1]);
                o1 += Number(t[9]);
            }
            
            for(var l of currentNetStr.split('\n')) {
                var t = l.trim().split(/\s+/);
                i2 += Number(t[1]);
                o2 += Number(t[9]);
            }
            
            // 处理计数器溢出的情况
            if(i2 < i1 || o2 < o1) {
                return { in: 0, out: 0, reset: true };
            }
            
            return {
                in: Math.round((i2 - i1) / timeDelta),
                out: Math.round((o2 - o1) / timeDelta)
            };
        } catch(e) {
            return { in: 0, out: 0 };
        }
    }
    
    var mem = parseMemory(mem_res);
    
    return {cpu,mem,net};
}
// 引入缓存管理器
const cache = require('./getStat-cache');

async function get(key,interval=0.1){
    // 支持快速模式：interval=0 时使用缓存差分计算，无需sleep
    const fastMode = interval === 0;
    if(key.privateKey=='')delete key.privateKey;
    var con=await ssh.ssh_con(key);
    if(!con||!con.isConnected())return false;

    // 获取服务器标识（用于缓存）
    const serverId = key.host || 'default';
    const serverCache = cache.getServerCache(serverId);

    // 快速模式：使用缓存差分计算
    if (fastMode) {
        var sh=`
cat /proc/stat | grep cpu | awk '{print $2,$3,$4,$5,$6,$7,$8}'
echo '---'
cat /proc/net/dev | tail -n +3 | grep -v lo
echo '---'
free | tail -n +2
`;
        var data=await ssh.ssh_exec(con,sh);
        if(!data)return false;
        con.dispose();
        
        try {
            var [cpuData, netData, memData] = data.split('\n---\n');
            
            // 解析内存（不需要缓存）
            var mem = parseMemory(memData.split('\n'));
            
            // 计算CPU使用率
            var cpu = { multi: 0, single: [] };
            if (serverCache.lastCpuData && serverCache.lastTimestamp) {
                // 使用缓存的上次数据计算差分
                const timeDelta = (Date.now() - serverCache.lastTimestamp) / 1000; // 转换为秒
                if (timeDelta > 0 && timeDelta < 60) { // 只在合理时间范围内计算
                    cpu = calculateCpuDelta(serverCache.lastCpuData, cpuData);
                }
            }
            
            // 计算网络速率
            var net = { in: 0, out: 0 };
            if (serverCache.lastNetData && serverCache.lastTimestamp) {
                const timeDelta = (Date.now() - serverCache.lastTimestamp) / 1000;
                if (timeDelta > 0 && timeDelta < 60) {
                    net = calculateNetDelta(serverCache.lastNetData, netData, timeDelta);
                }
            }
            
            // 更新缓存
            cache.updateServerCache(serverId, cpuData, netData);
            
            return {
                cpu: cpu,
                mem: mem,
                net: net,
                fastMode: true,
                warmup: !serverCache.lastTimestamp  // 标记是否是首次采样
            };
        } catch(e) {
            console.error('快速模式解析错误:', e);
            return false;
        }
    }
    
    // 标准模式：包含速率计算
    var sh=`
cat /proc/stat | grep cpu | awk '{print $2,$3,$4,$5,$6,$7,$8}'
echo '---'
cat /proc/net/dev | tail -n +3 | grep -v lo
echo '----'
sleep ${interval}
cat /proc/stat | grep cpu | awk '{print $2,$3,$4,$5,$6,$7,$8}'
echo '---'
cat /proc/net/dev | tail -n +3 | grep -v lo
echo '----'
free | tail -n +2
`;
    var data=await ssh.ssh_exec(con,sh);
    if(!data)return false;
    con.dispose();
    return analyze(data,interval);
}
module.exports={
    analyze,get
}
