{%set title = "节点状态"%}
{%extends "./base.html"%}

{%block content%}

<!-- 自定义响应式样式 -->
<style>
/* 系统概览卡片容器 - 响应式Grid布局 */
.overview-cards-container {
    display: grid;
    gap: 1rem;
    width: 100%;
}

/* 响应式网格布局 - 明确的断点控制 */

/* 大屏幕：4列布局 */
@media (min-width: 1200px) {
    .overview-cards-container {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* 中等屏幕：双列布局 */
@media (min-width: 600px) and (max-width: 1199px) {
    .overview-cards-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 小屏幕：单列布局 */
@media (max-width: 599px) {
    .overview-cards-container {
        grid-template-columns: 1fr;
        gap: 0.875rem;
    }
}

/* 卡片基础样式 */
.overview-cards-container > .card {
    min-height: 160px;
    display: flex;
    flex-direction: column;
    /* 防止内容溢出 */
    overflow: hidden;
    /* 确保卡片有最小宽度 */
    min-width: 0;
}

/* 使用纯Tailwind实现对齐，避免自定义CSS */

/* 圆形进度条容器 - 使用相对单位 */
.overview-cards-container .chart-container {
    width: 7rem;
    height: 7rem;
    position: relative;
    flex-shrink: 0;
    /* 确保圆环对齐 */
    display: flex;
    align-items: center;
    justify-content: center;
}

.overview-cards-container .chart-container svg {
    width: 100%;
    height: 100%;
}

/* 确保内容区域最小高度一致，以便圆环对齐 */
.overview-cards-container .card > div {
    min-height: 100%;
    display: flex;
    flex-direction: column;
}

/* 确保内容区域（数值和圆环）垂直居中对齐 */
.overview-cards-container .card .flex.justify-between {
    flex: 1;
    align-items: center;
}

/* 中小屏幕调整 (421px - 600px) */
@media (min-width: 421px) and (max-width: 600px) {
    /* 适度减小圆形图表尺寸 */
    .overview-cards-container .chart-container {
        width: 5.5rem;
        height: 5.5rem;
    }

    /* 调整字体大小 */
    .overview-cards-container .text-3xl {
        font-size: 1.75rem;
        line-height: 1.2;
    }

    .overview-cards-container .text-2xl {
        font-size: 1.5rem;
    }

    /* 减小卡片内边距 */
    .overview-cards-container .card .p-4 {
        padding: 0.875rem;
    }

    /* 网络卡片特殊处理 */
    .overview-cards-container .network-card .grid-cols-2 {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
}

/* 单列布局调整 (< 420px) */
@media (max-width: 420px) {
    /* 单列时可以保持稍大的元素 */
    .overview-cards-container .chart-container {
        width: 6rem;
        height: 6rem;
    }

    /* 调整字体大小 */
    .overview-cards-container .text-3xl {
        font-size: 2rem;
        line-height: 1.2;
    }

    .overview-cards-container .text-2xl {
        font-size: 1.625rem;
    }

    .overview-cards-container .text-lg {
        font-size: 1rem;
    }

    /* 内存卡片优化 */
    .overview-cards-container .space-y-2 > div {
        margin-bottom: 0.5rem;
    }

    /* 网络卡片可以使用2列 */
    .overview-cards-container .network-card .grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
}

/* 确保metric-unit继承父元素颜色 */
.metric-unit {
    color: inherit;
    font-size: 0.875em; /* 单位字体缩小到87.5% */
    opacity: 0.8; /* 稍微降低透明度，让单位不那么显眼 */
}

/* 优化百分号显示 */
.overview-cards-container .text-3xl .text-lg,
.overview-cards-container .text-2xl .text-sm {
    font-size: 0.6em; /* 百分号相对主数字缩小 */
    margin-left: 0.1em; /* 微调间距 */
}

/* 优化详细信息中的单位显示 */
.overview-cards-container .text-xs .metric-unit {
    font-size: 0.875em;
}

/* 紧凑单位样式 - 用于实时速度显示 */
.metric-unit-compact {
    font-size: 0.65em; /* 更小的字体 */
    opacity: 0.7; /* 更低的透明度 */
    margin-left: 0.05em; /* 更紧凑的间距 */
    font-weight: normal;
    vertical-align: baseline;
}

/* 内存卡片内容区域调整 */
.overview-cards-container .memory-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 80px;
}

/* 双列布局时的对齐优化 */
@media (min-width: 600px) and (max-width: 1199px) {
    /* 确保所有卡片高度一致 */
    .overview-cards-container > .card {
        min-height: 180px;
    }

    /* 统一左侧内容区域的最小宽度，防止文字过长影响圆环对齐 */
    .overview-cards-container .flex-1.pr-3 {
        min-width: 0;
        flex: 1 1 60%;
    }

    /* 确保圆环区域宽度固定 */
    .overview-cards-container .chart-container {
        flex: 0 0 7rem;
    }
}

/* 月度流量进度条样式 */
#monthly-progress-overview {
    transition: width 0.3s ease-out;
}

/* 并排图表高度一致性样式 */
.chart-grid-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

/* 在小屏幕上垂直堆叠 */
@media (max-width: 1024px) {
    .chart-grid-container {
        grid-template-columns: 1fr;
    }
}

/* 确保图表容器高度一致 */
.chart-grid-container .card {
    display: flex;
    flex-direction: column;
}

.chart-grid-container .card > div:last-child {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* 隐藏并排布局中不需要的元素 */
#granularity-selector,
#custom-time-range-selector {
    display: none !important;
}

/* CPU核心热力图自适应网格 */
.cpu-cores-heatmap {
    /* 默认布局 - 自动适应 */
    grid-template-columns: repeat(auto-fill, minmax(20px, 1fr));
    max-width: 100%;
}

/* 根据核心数调整布局 */
/* 1-8核：4列 */
.cpu-cores-heatmap[data-cores="1"],
.cpu-cores-heatmap[data-cores="2"],
.cpu-cores-heatmap[data-cores="3"],
.cpu-cores-heatmap[data-cores="4"],
.cpu-cores-heatmap[data-cores="5"],
.cpu-cores-heatmap[data-cores="6"],
.cpu-cores-heatmap[data-cores="7"],
.cpu-cores-heatmap[data-cores="8"] {
    grid-template-columns: repeat(4, 1fr);
}

/* 9-16核：8列 */
.cpu-cores-heatmap[data-cores="9"],
.cpu-cores-heatmap[data-cores="10"],
.cpu-cores-heatmap[data-cores="11"],
.cpu-cores-heatmap[data-cores="12"],
.cpu-cores-heatmap[data-cores="13"],
.cpu-cores-heatmap[data-cores="14"],
.cpu-cores-heatmap[data-cores="15"],
.cpu-cores-heatmap[data-cores="16"] {
    grid-template-columns: repeat(8, 1fr);
}

/* 17-32核：8列 */
.cpu-cores-heatmap[data-cores="17"],
.cpu-cores-heatmap[data-cores="18"],
.cpu-cores-heatmap[data-cores="19"],
.cpu-cores-heatmap[data-cores="20"],
.cpu-cores-heatmap[data-cores="21"],
.cpu-cores-heatmap[data-cores="22"],
.cpu-cores-heatmap[data-cores="23"],
.cpu-cores-heatmap[data-cores="24"],
.cpu-cores-heatmap[data-cores="25"],
.cpu-cores-heatmap[data-cores="26"],
.cpu-cores-heatmap[data-cores="27"],
.cpu-cores-heatmap[data-cores="28"],
.cpu-cores-heatmap[data-cores="29"],
.cpu-cores-heatmap[data-cores="30"],
.cpu-cores-heatmap[data-cores="31"],
.cpu-cores-heatmap[data-cores="32"] {
    grid-template-columns: repeat(8, 1fr);
}

/* 33-64核：16列 */
.cpu-cores-heatmap[data-cores^="3"]:not([data-cores="3"]),
.cpu-cores-heatmap[data-cores^="4"],
.cpu-cores-heatmap[data-cores^="5"],
.cpu-cores-heatmap[data-cores="60"],
.cpu-cores-heatmap[data-cores="61"],
.cpu-cores-heatmap[data-cores="62"],
.cpu-cores-heatmap[data-cores="63"],
.cpu-cores-heatmap[data-cores="64"] {
    grid-template-columns: repeat(16, 1fr);
}

/* 超过64核：自动填充，最小12px */
.cpu-cores-heatmap[data-cores="65"],
.cpu-cores-heatmap[data-cores="66"],
.cpu-cores-heatmap[data-cores="67"],
.cpu-cores-heatmap[data-cores="68"],
.cpu-cores-heatmap[data-cores="69"],
.cpu-cores-heatmap[data-cores^="7"],
.cpu-cores-heatmap[data-cores^="8"],
.cpu-cores-heatmap[data-cores^="9"],
.cpu-cores-heatmap[data-cores^="10"],
.cpu-cores-heatmap[data-cores^="11"],
.cpu-cores-heatmap[data-cores^="12"] {
    grid-template-columns: repeat(auto-fill, minmax(12px, 1fr));
}

/* 热力块样式 */
.cpu-core-cell {
    min-width: 12px;
    min-height: 12px;
    max-width: 30px;
    max-height: 30px;
}

/* 确保热力图容器不会溢出 */
.cpu-cores-heatmap-container {
    max-width: 100%;
    overflow: auto;
}

</style>

<!-- 引入ECharts库 -->
<!-- 缓存迁移工具 - 清理旧版本缓存 -->
<script src="/js/cache-migration.js"></script>

<!-- 缓存管理器 -->
<script src="/js/stats/cacheManager.js"></script>

<script src="/js/libs/echarts.min.js"></script>

<!-- 引入图表工具函数 -->
<script src="/js/utils/echarts-loader.js"></script>
<script src="/js/utils/container-manager.js"></script>
<script src="/js/utils/error-handler.js"></script>

<!-- 数据存储区域 -->
<textarea id='traffic_data' class="hidden">{{traffic|dump}}</textarea>
<textarea id='load_m_data' class="hidden">{{load_m|dump}}</textarea>
<textarea id='load_h_data' class="hidden">{{load_h|dump}}</textarea>
<textarea id='node-data' class="hidden">{{node|dump|safe}}</textarea>
<textarea id="preprocessed-data" style="display: none;">{{preProcessedData|safe}}</textarea>

<div class="space-y-4">
    <!-- 节点信息卡片 -->
    <div class="card theme-border card-hover overflow-hidden blur-enabled" data-quality="normal">
        <!-- 优化的内边距和渐变背景 -->
        <div class="p-3 bg-gradient-to-br from-slate-50/60 via-slate-50/40 to-transparent dark:from-slate-800/60 dark:via-slate-800/40 dark:to-transparent">
            <div class="flex justify-between items-start">
                <!-- 左侧主机信息 -->
                <div class="flex-1">
                    <!-- 主标题行 -->
                    <div class="flex items-center gap-3 mb-3">
                        <!-- 系统图标容器 -->
                        <div id="main-system-icon-container" class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary-500/20 to-primary-600/10 dark:from-primary-400/20 dark:to-primary-500/10 flex items-center justify-center shadow-sm">
                            <i id="main-system-icon" class="ti ti-server text-primary-600 dark:text-primary-400 text-2xl" title="系统"></i>
                        </div>
                        <div class="flex-1">
                            <h2 class="text-lg font-bold text-slate-900 dark:text-white">
                                {{node.name}}
                            </h2>
                            <!-- 系统信息标签行 -->
                            <div class="flex items-center gap-2 mt-2">
                                <!-- 主机名标签 -->
                                <div class="inline-flex items-center gap-1 px-2 py-0.5 rounded-md bg-gradient-to-r from-slate-100 to-slate-50 dark:from-slate-700 dark:to-slate-800 border border-slate-200 dark:border-slate-600 shadow-sm">
                                    <i class="ti ti-device-desktop text-slate-500 dark:text-slate-400 text-xs"></i>
                                    <span id="system-hostname-skeleton" class="inline-block w-12 h-3 bg-slate-200 dark:bg-slate-600 rounded animate-pulse"></span>
                                    <span id="system-hostname" class="text-xs text-slate-700 dark:text-slate-200 font-medium">{{node.host.hostname|default('--')}}</span>
                                </div>
                                <!-- 运行时间标签 -->
                                <div class="inline-flex items-center gap-1 px-2 py-0.5 rounded-md bg-gradient-to-r from-emerald-50 to-emerald-100/50 dark:from-emerald-900/20 dark:to-emerald-800/10 border border-emerald-200/60 dark:border-emerald-700/30 shadow-sm">
                                    <i class="ti ti-clock text-emerald-600 dark:text-emerald-400 text-xs"></i>
                                    <span id="uptime-display-skeleton" class="inline-block w-8 h-3 bg-emerald-200 dark:bg-emerald-700 rounded animate-pulse"></span>
                                    <span id="uptime-display" class="text-xs text-emerald-700 dark:text-emerald-300 font-medium">{{node.host.uptime|default('--')}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧操作按钮组 -->
                <div class="flex items-center gap-2 ml-4">
                    {%if admin%}
                    <a href="/admin/servers/{{sid}}/" class="p-2 rounded-lg bg-white/70 dark:bg-slate-800/70 hover:bg-white dark:hover:bg-slate-700 backdrop-blur-sm transition-all duration-200 shadow-sm hover:shadow-md" title="编辑">
                        <i class="ti ti-edit text-lg text-slate-600 dark:text-slate-300"></i>
                    </a>
                    <button onclick="webssh('{{sid}}')" class="p-2 rounded-lg bg-white/70 dark:bg-slate-800/70 hover:bg-white dark:hover:bg-slate-700 backdrop-blur-sm transition-all duration-200 shadow-sm hover:shadow-md" title="Web SSH" data-feature="webssh">
                        <i class="ti ti-terminal text-lg text-slate-600 dark:text-slate-300"></i>
                    </button>
                    {%endif%}
                </div>
            </div>
        </div>
    </div>

    <!-- 系统概览卡片 - 黄金比例设计 -->
    <div class="overview-cards-container">
            <!-- CPU使用率卡片 - 主要指标 -->
            <div class="card theme-border card-hover overflow-hidden">
            <div class="p-4 h-full flex flex-col">
                <!-- 标题区域 - 紧凑设计 -->
                <div class="flex items-center gap-2.5 h-9 mb-3">
                    <div class="w-7 h-7 rounded-md bg-blue-100 dark:bg-blue-800/20 flex items-center justify-center">
                        <i class="ti ti-cpu text-blue-600 dark:text-blue-400 text-sm"></i>
                    </div>
                    <span class="text-sm font-medium text-slate-700 dark:text-slate-300">处理器</span>
                    <span class="text-xs text-slate-500 dark:text-slate-400 ml-auto" id="cpu-cores-overview">
                        {{node.stat.cpu.single.length}} 核心
                    </span>
                </div>

                <!-- 内容区域 -->
                <div class="flex justify-between items-center">
                    <div class="flex-1 pr-3">
                        <div class="text-3xl font-bold text-slate-800 dark:text-white leading-tight" id="cpu-percent-overview">
                            {{(100*node.stat.cpu.multi).toFixed(0)}}<span class="text-lg font-normal text-slate-600 dark:text-slate-400">%</span>
                        </div>
                        <div class="text-xs text-slate-500 dark:text-slate-400 mt-2">使用率</div>
                    </div>
                    <!-- 圆形进度图 -->
                    <div class="chart-container relative flex-shrink-0">
                    <svg viewBox="0 0 80 80" class="transform -rotate-90">
                        <circle cx="40" cy="40" r="32" stroke-width="5" stroke="currentColor" fill="none" class="text-gray-200 dark:text-gray-700/50"></circle>
                        <circle cx="40" cy="40" r="32" stroke-width="5" stroke="currentColor" fill="none"
                            class="text-blue-500 dark:text-blue-400 transition-all duration-300"
                            stroke-dasharray="201.06"
                            stroke-dashoffset="{{ 201.06 - (201.06 * node.stat.cpu.multi) }}"
                            stroke-linecap="round"
                            id="cpu-progress-circle"></circle>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="text-center">
                            <div class="text-xs font-semibold text-blue-600 dark:text-blue-400 leading-none">CPU</div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内存使用率卡片 -->
        <div class="card theme-border card-hover overflow-hidden">
            <div class="p-4">
                <!-- 标题独立一行 -->
                <div class="flex items-center gap-2.5 h-9 mb-3">
                    <i class="ti ti-device-sd-card text-purple-600 dark:text-purple-400 w-7 h-7 flex items-center justify-center rounded-md bg-purple-100 dark:bg-purple-800/20 text-lg"></i>
                    <span class="text-sm font-medium text-slate-700 dark:text-slate-300">内存</span>
                </div>
                <!-- 内容区域 -->
                <div class="flex justify-between items-center">
                    <div class="flex-1 pr-3">
                        <!-- 分离显示RAM和Swap -->
                        <div class="space-y-2 memory-content">
                        <!-- RAM显示 -->
                        <div>
                            <div class="flex items-baseline gap-1">
                                <span class="text-2xl font-bold text-slate-800 dark:text-white" id="mem-percent-overview">{{((node.stat.mem.virtual.used/node.stat.mem.virtual.total)*100).toFixed(0)}}<span class="text-sm font-normal text-slate-600 dark:text-slate-400">%</span></span>
                                <span class="text-xs text-purple-600 dark:text-purple-400 font-medium">RAM</span>
                            </div>
                            <div class="text-xs text-slate-500 dark:text-slate-400" id="mem-detail-overview">{{strB(node.stat.mem.virtual.used)}} / {{strB(node.stat.mem.virtual.total)}}</div>
                        </div>
                        <!-- Swap显示 -->
                        {% if node.stat.mem.swap and node.stat.mem.swap.total > 0 %}
                        <div>
                            <div class="flex items-baseline gap-1">
                                <span class="text-lg font-semibold text-slate-700 dark:text-slate-300" id="swap-percent-overview">{{((node.stat.mem.swap.used/node.stat.mem.swap.total)*100).toFixed(0)}}<span class="text-sm font-normal text-slate-600 dark:text-slate-400">%</span></span>
                                <span class="text-xs text-orange-600 dark:text-orange-400 font-medium">Swap</span>
                            </div>
                            <div class="text-xs text-slate-500 dark:text-slate-400" id="swap-detail-overview">{{strB(node.stat.mem.swap.used)}} / {{strB(node.stat.mem.swap.total)}}</div>
                        </div>
                        {% endif %}
                        </div>
                    </div>
                    <!-- 双圆环进度图 -->
                    <div class="chart-container relative flex-shrink-0">
                    <svg viewBox="0 0 80 80" class="transform -rotate-90">
                        <!-- 外圈背景 (RAM) -->
                        <circle cx="40" cy="40" r="32" stroke-width="5" stroke="currentColor" fill="none" class="text-gray-200 dark:text-gray-700/50"></circle>
                        <!-- 外圈进度 (RAM) -->
                        <circle cx="40" cy="40" r="32" stroke-width="5" stroke="currentColor" fill="none"
                            class="text-purple-500 dark:text-purple-400 transition-all duration-300"
                            stroke-dasharray="201.06"
                            stroke-dashoffset="{{ 201.06 - (201.06 * (node.stat.mem.virtual.used/node.stat.mem.virtual.total)) }}"
                            stroke-linecap="round"
                            id="mem-progress-circle"></circle>

                        {% if node.stat.mem.swap and node.stat.mem.swap.total > 0 %}
                        <!-- 内圈背景 (Swap) -->
                        <circle cx="40" cy="40" r="20" stroke-width="4" stroke="currentColor" fill="none" class="text-gray-200 dark:text-gray-700/30"></circle>
                        <!-- 内圈进度 (Swap) -->
                        <circle cx="40" cy="40" r="20" stroke-width="4" stroke="currentColor" fill="none"
                            class="text-orange-500 dark:text-orange-400 transition-all duration-300"
                            stroke-dasharray="125.66"
                            stroke-dashoffset="{{ 125.66 - (125.66 * (node.stat.mem.swap.used/node.stat.mem.swap.total)) }}"
                            stroke-linecap="round"
                            id="swap-progress-circle"></circle>
                        {% endif %}
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="text-center">
                            <div class="text-xs font-semibold text-purple-600 dark:text-purple-400 leading-none">RAM</div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 硬盘使用率卡片 -->
        <div class="card theme-border card-hover overflow-hidden">
            <div class="p-4">
                <!-- 标题独立一行 -->
                <div class="flex items-center gap-2.5 h-9 mb-3">
                    <i class="ti ti-database text-amber-600 dark:text-amber-400 w-7 h-7 flex items-center justify-center rounded-md bg-amber-100 dark:bg-amber-800/20 text-lg"></i>
                    <span class="text-sm font-medium text-slate-700 dark:text-slate-300">硬盘</span>
                </div>
                <!-- 内容区域 -->
                <div class="flex justify-between items-center">
                    <div class="flex-1 pr-3">
                        <div class="text-3xl font-bold text-slate-800 dark:text-white leading-tight">
                            {% if node.stat.disk.total > 0 %}{{ (100*node.stat.disk.used/node.stat.disk.total)|round(0) }}{% else %}0{% endif %}<span class="text-lg font-normal text-slate-600 dark:text-slate-400">%</span>
                        </div>
                        <div class="text-xs text-slate-500 dark:text-slate-400 mt-2">{{strB(node.stat.disk.used)}} / {{strB(node.stat.disk.total)}}</div>
                    </div>
                    <!-- 圆形进度图 -->
                    <div class="chart-container relative flex-shrink-0">
                    <svg viewBox="0 0 80 80" class="transform -rotate-90">
                        <circle cx="40" cy="40" r="32" stroke-width="5" stroke="currentColor" fill="none" class="text-gray-200 dark:text-gray-700/50"></circle>
                        <circle cx="40" cy="40" r="32" stroke-width="5" stroke="currentColor" fill="none"
                            class="text-amber-500 dark:text-amber-400 transition-all duration-300"
                            stroke-dasharray="201.06"
                            stroke-dashoffset="{% if node.stat.disk.total > 0 %}{{ 201.06 - (201.06 * (node.stat.disk.used/node.stat.disk.total)) }}{% else %}201.06{% endif %}"
                            stroke-linecap="round"></circle>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="text-center">
                            <div class="text-xs font-semibold text-amber-600 dark:text-amber-400 leading-none">DISK</div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 网络流量综合卡片 -->
        <div class="card theme-border card-hover overflow-hidden">
            <div class="p-4">
                <!-- 标题独立一行 -->
                <div class="flex items-center gap-2.5 h-9 mb-3">
                    <i class="ti ti-network text-green-600 dark:text-green-400 w-7 h-7 flex items-center justify-center rounded-md bg-green-100 dark:bg-green-800/20 text-lg"></i>
                    <span class="text-sm font-medium text-slate-700 dark:text-slate-300">网络流量</span>
                    <!-- 月度流量使用率 -->
                    <div class="flex items-center gap-2 ml-auto">
                        <span class="text-xs text-slate-500 dark:text-slate-400">月度:</span>
                        <span id="monthly-percent-overview" class="text-sm font-semibold text-slate-700 dark:text-slate-300">0%</span>
                    </div>
                </div>

                <!-- 内容区域 -->
                <div class="space-y-2">
                    <!-- 速度显示区域 -->
                    <div class="flex items-center gap-2">
                        <!-- 左侧：上下传速度 -->
                        <div class="flex-1 space-y-1.5">
                            <!-- 下载速度 -->
                            <div class="flex items-center justify-between py-1.5 px-2.5 rounded-lg bg-green-50/50 dark:bg-green-900/10">
                                <i class="ti ti-arrow-down text-green-600 dark:text-green-400 text-base"></i>
                                <span id="NET_IN_overview" class="text-lg font-bold text-slate-800 dark:text-white">0<span class="metric-unit-compact">b</span></span>
                            </div>
                            <!-- 上传速度 -->
                            <div class="flex items-center justify-between py-1.5 px-2.5 rounded-lg bg-blue-50/50 dark:bg-blue-900/10">
                                <i class="ti ti-arrow-up text-blue-600 dark:text-blue-400 text-base"></i>
                                <span id="NET_OUT_overview" class="text-lg font-bold text-slate-800 dark:text-white">0<span class="metric-unit-compact">b</span></span>
                            </div>
                        </div>

                        <!-- 右侧：ECharts速度仪表盘 - 响应式隐藏 -->
                        <div class="hidden md:block flex-shrink-0 rounded-lg bg-slate-50/50 dark:bg-slate-800/30 p-2">
                            <div id="network-speed-gauge" class="w-24 h-20"></div>
                        </div>
                    </div>

                    <!-- 月度流量进度条 - 全宽 -->
                    <div class="rounded-lg bg-slate-50/50 dark:bg-slate-800/30 p-2.5">
                        <div class="flex justify-between items-center mb-1.5">
                            <span class="text-xs font-medium text-slate-700 dark:text-slate-300">月度流量</span>
                            <span class="text-xs text-slate-600 dark:text-slate-400">
                                <span id="monthly-used-overview" class="font-medium">0B</span> / <span id="monthly-limit-overview">0B</span>
                            </span>
                        </div>
                        <div class="w-full h-1.5 bg-gray-200 dark:bg-gray-700/50 rounded-full overflow-hidden">
                            <div id="monthly-progress-overview"
                                 class="h-full bg-gradient-to-r from-green-500 to-blue-500 rounded-full transition-all duration-300"
                                 style="width: 0%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!-- 关闭 overview-cards-container -->

    <!-- 网格布局容器：负载和带宽占满整行 -->
    <div class="grid grid-cols-1 gap-4 mt-4">
        <!-- 容器 - 占满整行，包含负载详情和带宽监控 -->
    <div class="space-y-4">
        <!-- 负载详情卡片 (ECharts版本) -->
        <div class="card theme-border card-hover overflow-hidden load-echarts-card flex flex-col">
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700/20">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <i class="ti ti-gauge text-primary-500 dark:text-primary-400 text-xl"></i>
                        <h3 class="text-lg font-medium text-slate-800 dark:text-white">负载详情</h3>
                    </div>
                    <div class="flex gap-2">
                        <button data-range="10m" class="load-range-btn px-3 py-1 text-xs font-medium rounded bg-blue-50 text-primary-600 border border-blue-200 dark:bg-slate-700 dark:text-white dark:border-slate-700 flex items-center justify-center">
                            实时
                        </button>
                        <button data-range="1h" class="load-range-btn px-3 py-1 text-xs font-medium rounded bg-gray-50 dark:bg-slate-800 text-gray-600 dark:text-slate-300 border border-gray-200 dark:border-slate-700 flex items-center justify-center">
                            1小时
                        </button>
                        <button data-range="24h" class="load-range-btn px-3 py-1 text-xs font-medium rounded bg-gray-50 dark:bg-slate-800 text-gray-600 dark:text-slate-300 border border-gray-200 dark:border-slate-700 flex items-center justify-center">
                            24小时
                        </button>
                        <div class="relative" id="load-history-dropdown">
                            <button id="load-history-toggle" class="px-3 py-1 text-xs font-medium rounded bg-gray-50 dark:bg-slate-800 text-gray-600 dark:text-slate-300 border border-gray-200 dark:border-slate-700 flex items-center">
                                历史
                                <svg class="ml-1 h-3 w-3" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.17l3.71-3.94a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" /></svg>
                            </button>
                            <div id="load-history-menu" class="hidden absolute right-0 mt-1 w-24 rounded-md border border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-800 shadow-lg z-10">
                                <a href="#" class="block px-3 py-1 text-xs text-slate-700 dark:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-700 load-range-btn" data-range="7d">7天</a>
                                <a href="#" class="block px-3 py-1 text-xs text-slate-700 dark:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-700 load-range-btn" data-range="30d">30天</a>
                                <a href="#" class="block px-3 py-1 text-xs text-slate-700 dark:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-700 load-range-btn" data-range="60d">60天</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-4 flex-1 flex flex-col">
                <div id="load-echarts-chart" class="w-full h-full min-h-[280px] relative overflow-visible"></div>
            </div>
        </div>
        <script>
            (function(){
              // 负载历史下拉交互：点击展开/点击外部关闭，hover不展开
              const toggle = document.getElementById('load-history-toggle');
              const menu = document.getElementById('load-history-menu');
              const wrap = document.getElementById('load-history-dropdown');
              if (!toggle || !menu || !wrap) return;
              // 切换显示
              toggle.addEventListener('click', function(e){
                e.preventDefault();
                e.stopPropagation();
                const isHidden = menu.classList.contains('hidden');
                if (isHidden) {
                  menu.classList.remove('hidden');
                } else {
                  menu.classList.add('hidden');
                }
              });
              // 点击菜单内的项时阻止默认跳转
              menu.addEventListener('click', function(e){
                if (e.target && (e.target.closest && e.target.closest('.load-range-btn'))) {
                  e.preventDefault();
                }
                e.stopPropagation();
                // 选择后自动收起
                if (!menu.classList.contains('hidden')) menu.classList.add('hidden');
              });
              // 点击外部关闭
              document.addEventListener('click', function(e){
                if (!wrap.contains(e.target)) {
                  if (!menu.classList.contains('hidden')) {
                    menu.classList.add('hidden');
                  }
                }
              });
            })();
        </script>

        <!-- 带宽监控面板（极简版） -->
        <div class="card theme-border card-hover overflow-hidden">
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700/20">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <i class="ti ti-network text-primary-500 dark:text-primary-400 text-xl"></i>
                        <h3 class="text-lg font-medium text-slate-800 dark:text-white">带宽监控</h3>
                    </div>
                    <!-- 切换时间范围按钮 -->
                    <div class="flex gap-2 items-center">
                        <button class="bandwidth-range-btn px-3 py-1 text-xs font-medium rounded bg-blue-50 text-primary-600 border border-blue-200 dark:bg-slate-700 dark:text-white dark:border-slate-700" data-range="10min">实时</button>
                        <button class="bandwidth-range-btn px-3 py-1 text-xs font-medium rounded bg-gray-50 dark:bg-slate-800 text-gray-600 dark:text-slate-300 border border-gray-200 dark:border-slate-700" data-range="1h">1小时</button>
                        <button class="bandwidth-range-btn px-3 py-1 text-xs font-medium rounded bg-gray-50 dark:bg-slate-800 text-gray-600 dark:text-slate-300 border border-gray-200 dark:border-slate-700" data-range="24h">24小时</button>
                        <div class="relative" id="bw-history-dropdown">
                            <button id="bw-history-toggle" class="px-3 py-1 text-xs font-medium rounded bg-gray-50 dark:bg-slate-800 text-gray-600 dark:text-slate-300 border border-gray-200 dark:border-slate-700 flex items-center">
                                历史
                                <svg class="ml-1 h-3 w-3" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.17l3.71-3.94a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" /></svg>
                            </button>
                            <div id="bw-history-menu" class="hidden absolute right-0 mt-1 w-24 rounded-md border border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-800 shadow-lg z-10">
                                <a href="#" class="block px-3 py-1 text-xs text-slate-700 dark:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-700 bandwidth-range-btn" data-range="7d">7天</a>
                                <a href="#" class="block px-3 py-1 text-xs text-slate-700 dark:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-700 bandwidth-range-btn" data-range="30d">30天</a>
                                <a href="#" class="block px-3 py-1 text-xs text-slate-700 dark:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-700 bandwidth-range-btn" data-range="60d">60天</a>
                            </div>
                        </div>
                    </div>
                    <script>
                        (function(){
                          // 带宽历史下拉交互：点击展开/点击外部关闭，hover不展开
                          const toggle = document.getElementById('bw-history-toggle');
                          const menu = document.getElementById('bw-history-menu');
                          const wrap = document.getElementById('bw-history-dropdown');
                          if (!toggle || !menu || !wrap) return;
                          // 切换显示
                          toggle.addEventListener('click', function(e){
                            e.preventDefault();
                            e.stopPropagation();
                            const isHidden = menu.classList.contains('hidden');
                            if (isHidden) {
                              menu.classList.remove('hidden');
                            } else {
                              menu.classList.add('hidden');
                            }
                          });
                          // 点击菜单内的项时阻止冒泡，交由现有 .bandwidth-range-btn 逻辑处理
                          menu.addEventListener('click', function(e){
                            // 若点击的是下拉项，阻止默认跳转到#
                            if (e.target && (e.target.closest && e.target.closest('.bandwidth-range-btn'))) {
                              e.preventDefault();
                            }
                            e.stopPropagation();
                          });
                          // 点击外部关闭
                          document.addEventListener('click', function(e){
                            // 如果点击不在wrap内，则关闭
                            if (!wrap.contains(e.target)) {
                              if (!menu.classList.contains('hidden')) {
                                menu.classList.add('hidden');
                              }
                            }
                          });
                        })();
                    </script>

                </div>
            </div>
            <div class="p-4">
                <div id="bandwidth-realtime-chart" class="w-full h-[280px] relative overflow-visible"></div>
            </div>
        </div>
    </div>
    </div><!-- 关闭网格布局容器 -->

    <!-- 第二行：网络质量监控独占一行 -->
    <div class="card theme-border card-hover overflow-hidden mt-4">
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700/20">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <i class="ti ti-gauge text-primary-500 dark:text-primary-400 text-xl"></i>
                    <h3 class="text-lg font-medium text-slate-800 dark:text-white">网络质量监控</h3>
                </div>
                <!-- 简化的时间范围按钮 -->
                <div class="flex gap-2">
                    <button class="time-range-selector px-3 py-1 text-xs font-medium rounded bg-blue-50 dark:bg-slate-700 text-primary-600 dark:text-white border border-blue-200 dark:border-slate-700" data-range="4h">4小时</button>
                    <button class="time-range-selector px-3 py-1 text-xs font-medium rounded bg-gray-50 dark:bg-slate-800 text-gray-600 dark:text-slate-300 border border-gray-200 dark:border-slate-700" data-range="24h">24小时</button>
                    <button class="time-range-selector px-3 py-1 text-xs font-medium rounded bg-gray-50 dark:bg-slate-800 text-gray-600 dark:text-slate-300 border border-gray-200 dark:border-slate-700" data-range="7d">7天</button>
                </div>
            </div>
        </div>
        <div class="p-4">
            <!-- 简化的控制面板 -->
            <div id="network-quality-controls" class="mb-4">
                <!-- 目标标签 -->
                <div id="network-quality-tags" class="flex flex-wrap justify-center gap-2">
                    <!-- 目标标签将通过JavaScript动态添加 -->
                </div>
</div>

<script>
  (function(){
    // 在主卡片容器中渲染自建彩色 OS 图标（Streamline 本地 SVG）
    function parseJSON(id){
      try { var el=document.getElementById(id); if(!el) return null; return JSON.parse(el.value||el.textContent||'null'); } catch(e){ return null; }
    }
    function currentSid(){
      try { return window.location.pathname.split('/').filter(Boolean)[1] || ''; } catch(e){ return ''; }
    }
    function extractPlatform(){
      // 1) 最新的 statsData
      var sid = currentSid();
      if (sid && window.statsData && window.statsData[sid] && window.statsData[sid].stat && window.statsData[sid].stat.host){
        var h = window.statsData[sid].stat.host;
        return (h.platform || h.platformVersion || '').toString();
      }
      // 2) 页面内嵌 node 数据（包含 stat.host）
      var nodeRaw = parseJSON('node-data');
      if (nodeRaw && nodeRaw.stat && nodeRaw.stat.host){
        var hh = nodeRaw.stat.host;
        return (hh.platform || hh.platformVersion || '').toString();
      }
      // 3) 退一步：preprocessed-data（若含 host）
      var pre = parseJSON('preprocessed-data');
      if (pre && pre.host){
        return (pre.host.platform || pre.host.platformVersion || '').toString();
      }
      return '';
    }
    function renderOnce(){
      var platformInfo = extractPlatform();
      var container = document.getElementById('main-system-icon-container');
      if (!container || !window.OsIcons) return false;
      var el = window.OsIcons.create(platformInfo, { size: 20, className: 'text-slate-700 dark:text-slate-200', title: platformInfo || '系统' });
      if (el) { container.innerHTML=''; container.appendChild(el); return true; }
      return false;
    }
    if (!renderOnce()) {
      var tries = 0; var timer = setInterval(function(){
        tries++; if (renderOnce() || tries > 10) clearInterval(timer);
      }, 500);
    }
  })();
  </script>
            <!-- 图表容器 -->
            <div class="relative">
                <!-- 加载指示器 -->
                <div id="network-quality-loading" class="absolute inset-0 flex items-center justify-center bg-slate-100 dark:bg-slate-800 rounded-md z-10 hidden">
                    <div class="flex items-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 dark:border-primary-400"></div>
                        <span class="ml-3 text-slate-600 dark:text-slate-300">加载中...</span>
                    </div>
                </div>

                <!-- 图表容器 - 高度与带宽监控一致 -->
                <div id="network-quality-chart" class="w-full h-[280px] md:h-[320px] lg:h-[360px] relative overflow-visible"></div>
            </div>
        </div>
    </div>

    <!-- 流量统计等其他内容 -->
    <div class="space-y-4 mt-4">

        <!-- 流量统计面板（极简版） -->
        <div class="card theme-border card-hover overflow-hidden mt-6">
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700/20">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <i class="ti ti-chart-bar text-primary-500 dark:text-primary-400 text-xl"></i>
                        <h3 class="text-lg font-medium text-slate-800 dark:text-white">流量统计</h3>
                    </div>
                    <div class="flex gap-2">
                        <button class="traffic-range-btn px-3 py-1 text-xs font-medium rounded bg-blue-50 text-primary-600 border border-blue-200 dark:bg-slate-700 dark:text-white dark:border-slate-700" data-range="hs">24小时</button>
                        <button class="traffic-range-btn px-3 py-1 text-xs font-medium rounded bg-gray-50 dark:bg-slate-800 text-gray-600 dark:text-slate-300 border border-gray-200 dark:border-slate-700" data-range="ds">31天</button>
                        <button class="traffic-range-btn px-3 py-1 text-xs font-medium rounded bg-gray-50 dark:bg-slate-800 text-gray-600 dark:text-slate-300 border border-gray-200 dark:border-slate-700" data-range="ms">12个月</button>
                    </div>
                </div>
            </div>
            <div class="p-4">
                <div id="traffic-chart" style="width:100%;height:320px;"></div>
            </div>
        </div>
    </div>

    <!-- 将网络设备和硬盘使用详情移入同一个父容器 -->
    <div class="space-y-4">
        <!-- Network Devices Table -->
        <div class="card theme-border card-hover overflow-hidden">
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700/20">
                <div class="flex items-center gap-2">
                    <i class="ti ti-router text-primary-500 dark:text-primary-400 text-xl"></i>
                    <h3 class="text-lg font-medium text-slate-800 dark:text-white">网络设备</h3>
                </div>
            </div>
            <div class="p-4">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b border-gray-200 dark:border-gray-700/20">
                                <th class="px-6 py-3 text-left text-xs font-medium text-slate-600 dark:text-slate-300 uppercase w-1/3">设备</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-slate-600 dark:text-slate-300 uppercase w-1/3">总下行流量</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-slate-600 dark:text-slate-300 uppercase w-1/3">总上行流量</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 dark:divide-gray-700" id="network-devices-table">
                            {%if node.stat.net.devices%}
                            {%for device,net in node.stat.net.devices%}
                            <tr data-device-name="{{device}}">
                                <td class="px-6 py-3 text-sm text-slate-800 dark:text-white truncate max-w-[33%]">{{device}}</td>
                                <td class="px-6 py-3 text-sm text-slate-800 dark:text-white whitespace-nowrap w-[33%]" id="net_{{device}}_total_in">{{strB(net.total.in)}}</td>
                                <td class="px-6 py-3 text-sm text-slate-800 dark:text-white whitespace-nowrap w-[33%]" id="net_{{device}}_total_out">{{strB(net.total.out)}}</td>
                            </tr>
                            {%endfor%}
                            {%else%}
                            <tr>
                                <td colspan="3" class="px-6 py-4 text-sm text-slate-600 dark:text-slate-300 text-center">暂无网络设备数据</td>
                            </tr>
                            {%endif%}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Disk Usage Details Table -->
        <div class="card theme-border card-hover overflow-hidden">
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700/20">
                <div class="flex items-center gap-2">
                    <i class="ti ti-database text-primary-500 dark:text-primary-400 text-xl"></i>
                    <h3 class="text-lg font-medium text-slate-800 dark:text-white">硬盘使用详情</h3>
                </div>
            </div>
            <div class="p-4">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b border-gray-200 dark:border-gray-700/20">
                                <th class="px-6 py-3 text-left text-xs font-medium text-slate-600 dark:text-slate-300 uppercase">挂载点</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-slate-600 dark:text-slate-300 uppercase">文件系统</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-slate-600 dark:text-slate-300 uppercase">总容量</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-slate-600 dark:text-slate-300 uppercase">已用空间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-slate-600 dark:text-slate-300 uppercase">可用空间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-slate-600 dark:text-slate-300 uppercase">使用率</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 dark:divide-gray-700" id="disk-usage-table">
                            {%if node.stat.disks%}
                            {%for disk in node.stat.disks%}
                            <tr>
                                <td class="px-6 py-3 text-sm text-slate-800 dark:text-white">{{disk.mount}}</td>
                                <td class="px-6 py-3 text-sm text-slate-800 dark:text-white">{{disk.fstype}}</td>
                                <td class="px-6 py-3 text-sm text-slate-800 dark:text-white">{{strB(disk.total)}}</td>
                                <td class="px-6 py-3 text-sm text-slate-800 dark:text-white">{{strB(disk.used)}}</td>
                                <td class="px-6 py-3 text-sm text-slate-800 dark:text-white">{{strB(disk.free)}}</td>
                                <td class="px-6 py-3 text-sm text-slate-800 dark:text-white">
                                    <div class="flex items-center">
                                        <div class="w-16 rounded-full h-2 mr-2 bg-gray-100 dark:bg-gray-700">
                                            <div class="h-2 rounded-full transition-all duration-300 ease-out bg-amber-500 dark:bg-amber-400" style="width: {% if disk.total > 0 %}{{ (disk.used/disk.total*100)|round(2) }}{% else %}0{% endif %}%;"></div>
                                        </div>
                                        <span>{% if disk.total > 0 %}{{ (disk.used/disk.total*100)|round(2) }}{% else %}0{% endif %}%</span>
                                    </div>
                                </td>
                            </tr>
                            {%endfor%}
                            {%else%}
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-sm text-slate-600 dark:text-slate-300 text-center">暂无分区详情数据</td>
                            </tr>
                            {%endif%}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

</div>
{%endblock%}

{%block js%}
<!-- 所有样式已在base.html中引入 -->

<!-- 传递服务器调试模式标志 -->
<script>
    // 从服务器设置中获取调试模式状态
    window.SERVER_DEBUG_MODE = {{ 1 if setting.debug else 0 }};
    // 强制启用调试模式，以便于排错
    window.DEBUG_MODE = true;
    console.log('强制启用调试模式');
</script>

<!-- 统一图表配置 -->
<script src="/js/charts/unified-chart-config.js"></script>

<!-- 共享图表标签工具 -->
<script src="/js/utils/chart-labels.js"></script>

<!-- 图表模块 -->
<script src="/js/charts/bandwidth-simple.js"></script>
<script src="/js/charts/load-simple.js"></script>
<script src="/js/charts/traffic-simple.js"></script>
<script src="/js/charts/network-quality-unified.js"></script>

<!-- 保留核心数据处理模块 -->
<script src="/js/stat-unified.js"></script>

<!-- 系统图标管理 -->
<script src="/js/card/system-icons.js"></script>

<!-- 确保流量汇总数据显示 -->
<script>
    // 添加一个标志变量，防止重复初始化
    window.trafficSummaryInitialized = false;

    // 监听StatManager初始化完成事件
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[Stats] 页面加载完成，等待StatManager初始化...');

        // 检查StatManager是否已经初始化
        if (window.StatManager) {
            window.trafficSummaryInitialized = true;
            console.log('[Stats] StatManager已就绪');
        }
    });

    // 如果3秒后仍未完成初始化，显示错误信息
    setTimeout(function() {
        if (!window.trafficSummaryInitialized) {
            console.warn('[Stats] StatManager初始化超时');
            const errorMsg = document.createElement('div');
            errorMsg.className = 'text-red-500 text-sm mt-2';
            errorMsg.textContent = '加载流量统计数据失败，请刷新页面重试';
            document.querySelector('.traffic-stats')?.appendChild(errorMsg);
        }
    }, 3000);
</script>

<!-- 系统图标初始化 -->
<script>
// 格式化运行时间显示（只显示天数）
function formatUptimeDisplay() {
    const uptimeElement = document.getElementById('uptime-display');
    if (!uptimeElement) return;

    const uptimeText = uptimeElement.textContent;
    if (!uptimeText || uptimeText === '--') return;

    // 处理各种运行时间格式
    let days = 0;

    // 1. 匹配包含"天"的中文格式: "26天15小时30分钟" -> "26天"
    const dayMatch = uptimeText.match(/(\d+)天/);
    if (dayMatch) {
        days = parseInt(dayMatch[1]);
    }
    // 2. 匹配纯数字秒格式 (需要转换)
    else if (/^\d+$/.test(uptimeText.trim())) {
        const uptimeSeconds = parseInt(uptimeText);
        days = Math.floor(uptimeSeconds / (24 * 60 * 60));
    }
    // 3. 匹配英文格式: "26 days, 15:30:45" -> "26天"
    else if (uptimeText.includes('day')) {
        const englishDayMatch = uptimeText.match(/(\d+)\s*days?/);
        if (englishDayMatch) {
            days = parseInt(englishDayMatch[1]);
        }
    }
    // 4. 匹配时分秒格式: "15:30:45" (小于1天)
    else if (/^\d{1,2}:\d{2}:\d{2}$/.test(uptimeText.trim())) {
        days = 0; // 小于1天显示为0天
    }

    // 更新显示
    if (days >= 0) {
        uptimeElement.textContent = `${days}天`;
        console.log('[Uptime] 格式化运行时间:', `${uptimeText} -> ${days}天`);
    }
}

// 创建模拟的服务器卡片来复用SystemIconManager逻辑
function createVirtualServerCard() {
    // 从URL获取当前页面的SID
    const currentSid = window.location.pathname.split('/').filter(Boolean)[1] || 'unknown';
    console.log('[SystemIcon] 当前SID:', currentSid);

    // 创建虚拟的server-card元素，模拟card.html的结构
    const virtualCard = document.createElement('div');
    virtualCard.className = 'server-card';
    virtualCard.dataset.sid = currentSid;

    // 创建system-icon-container，模拟card.html的结构
    const iconContainer = document.createElement('div');
    iconContainer.className = 'system-icon-container';

    // 多源数据获取策略
    let platformData = '';

    // 1. 优先从全局statsData获取（最新数据）
    if (window.statsData && window.statsData[currentSid]) {
        const statData = window.statsData[currentSid].stat;
        if (statData && statData.host) {
            // 优先使用 platform 字段，platformVersion 通常是版本号
            platformData = statData.host.platform || statData.host.platformVersion || '';
            console.log('[SystemIcon] 从statsData获取平台信息:', platformData, '完整host数据:', statData.host);
        }
    }

    // 2. 如果statsData没有，尝试从node-data获取
    if (!platformData) {
        const nodeData = document.getElementById('node-data');
        if (nodeData && nodeData.value) {
            try {
                const node = JSON.parse(nodeData.value);
                if (node && node.stat && node.stat.host) {
                    // 优先使用 platform 字段，platformVersion 通常是版本号
                    platformData = node.stat.host.platform || node.stat.host.platformVersion || '';
                    console.log('[SystemIcon] 从node-data获取平台信息:', platformData, '完整host数据:', node.stat.host);
                }
            } catch (e) {
                console.warn('[SystemIcon] 解析node-data失败:', e);
            }
        }
    }

    // 3. 如果仍然没有数据，尝试从主机名推断
    if (!platformData) {
        const hostnameElement = document.getElementById('system-hostname');
        if (hostnameElement && hostnameElement.textContent && hostnameElement.textContent !== '--') {
            const hostname = hostnameElement.textContent.toLowerCase();
            // 基于主机名的简单推断
            if (hostname.includes('debian')) platformData = 'debian';
            else if (hostname.includes('ubuntu')) platformData = 'ubuntu';
            else if (hostname.includes('centos')) platformData = 'centos';
            else if (hostname.includes('rhel') || hostname.includes('redhat')) platformData = 'redhat';
            console.log('[SystemIcon] 从主机名推断平台:', platformData, '主机名:', hostname);
        }
    }

    iconContainer.dataset.platform = platformData;
    virtualCard.appendChild(iconContainer);

    console.log('[SystemIcon] 创建虚拟卡片完成，最终平台数据:', platformData);
    return virtualCard;
}

// 防重复更新标志
let systemIconUpdated = false;
let systemIconUpdateInProgress = false;

// 使用SystemIconManager的逻辑获取系统图标
function updateSystemIconsUsingManager() {
    // 防止重复更新
    if (systemIconUpdateInProgress) {
        console.log('[SystemIcon] 更新正在进行中，跳过重复调用');
        return;
    }

    if (systemIconUpdated) {
        console.log('[SystemIcon] 图标已更新，跳过重复调用');
        return;
    }

    systemIconUpdateInProgress = true;
    console.log('[SystemIcon] 使用SystemIconManager更新图标...');

    if (!window.systemIconManager) {
        console.warn('[SystemIcon] SystemIconManager 未初始化，延迟重试...');
        systemIconUpdateInProgress = false;
        setTimeout(updateSystemIconsUsingManager, 500);
        return;
    }

    // 创建虚拟卡片来获取平台信息
    const virtualCard = createVirtualServerCard();
    if (!virtualCard) {
        console.warn('[SystemIcon] 无法创建虚拟卡片，延迟重试...');
        systemIconUpdateInProgress = false;
        return;
    }

    // 使用SystemIconManager的getSystemInfoFromCard方法
    const platformInfo = window.systemIconManager.getSystemInfoFromCard(virtualCard);
    console.log('[SystemIcon] 通过SystemIconManager获取平台信息:', platformInfo);

    // 检查平台信息是否有效
    if (!platformInfo || platformInfo.trim() === '') {
        console.warn('[SystemIcon] 平台信息为空，延迟重试...');
        systemIconUpdateInProgress = false;
        return;
    }

    // 获取图标配置
    const iconConfig = window.systemIconManager.getSystemIcon(platformInfo);
    console.log('[SystemIcon] 图标配置:', iconConfig);

    // 验证图标配置
    if (!iconConfig || !iconConfig.icon) {
        console.error('[SystemIcon] 获取的图标配置无效:', iconConfig);
        systemIconUpdateInProgress = false;
        return;
    }

    // 更新主要系统图标
    const mainIconElement = document.getElementById('main-system-icon');
    if (mainIconElement) {
        const oldClassName = mainIconElement.className;
        const oldTitle = mainIconElement.title;

        mainIconElement.className = `${iconConfig.icon} ${iconConfig.color} text-2xl`;
        mainIconElement.title = iconConfig.name;

        // 标记为已更新
        systemIconUpdated = true;

        console.log('[SystemIcon] 图标更新成功:');
        console.log('  - 旧样式:', oldClassName);
        console.log('  - 新样式:', mainIconElement.className);
        console.log('  - 旧标题:', oldTitle);
        console.log('  - 新标题:', mainIconElement.title);
        console.log('  - 系统类型:', iconConfig.name);
        console.log('  - 平台信息:', platformInfo);
    } else {
        console.error('[SystemIcon] 未找到main-system-icon元素');
    }

    // 格式化运行时间显示
    formatUptimeDisplay();

    // 移除骨架屏
    const skeletons = document.querySelectorAll('#system-hostname-skeleton, #uptime-display-skeleton');
    skeletons.forEach(skeleton => skeleton.style.display = 'none');

    systemIconUpdateInProgress = false;
}

// 监听StatManager的数据更新，复用card.html的逻辑
document.addEventListener('statsUpdated', function(e) {
    console.log('[SystemIcon] 监听到statsUpdated事件');
    // 只有还没更新过图标时才更新
    if (!systemIconUpdated) {
        updateSystemIconsUsingManager();
    } else {
        console.log('[SystemIcon] 图标已更新，跳过statsUpdated触发的更新');
    }
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('[SystemIcon] 页面加载完成，初始化系统图标...');

    // 立即格式化运行时间显示
    setTimeout(formatUptimeDisplay, 100);

    // 延迟执行一次，确保SystemIconManager和其他脚本已加载
    setTimeout(updateSystemIconsUsingManager, 800);
});
</script>

<!-- 其他功能模块 -->
{%include "./webssh.html"%}

<style>
/* 注意：metric-unit 样式已在顶部定义，这里不再重复定义 */

/* Tabler Icons 尺寸类 - 与card.html保持一致 */
.ti.md-14 {
    font-size: 14px;
}
.ti.md-12 {
    font-size: 12px;
}

/* CPU核心迷你图表样式 */
.cpu-cores-chart {
    background-color: rgba(156, 163, 175, 0.1);
    border-radius: 2px;
    padding: 2px;
}

.cpu-cores-chart > div {
    position: relative;
}

/* 响应式调整 */
@media (max-width: 640px) {
    .cpu-cores-chart {
        height: 2.5rem; /* 在小屏幕上稍微降低高度 */
    }
}

/* 动画效果 */
.cpu-cores-chart > div > div {
    transition: height 0.3s ease;
}
</style>


{%endblock%}
