{%set title = "SSH脚本管理"%}
{%set admin = true%}
{%extends "../base.html"%}

{%block head%}
<!-- SSH脚本管理样式已移动到 static/css/components/components.css -->
{%endblock%}

{%block content%}
<div>
    <!-- 引入侧边栏 -->
    {% include "admin/sidebar.html" %}

    <!-- 主内容区域 -->
    <div class="flex-1 space-y-6">

        <!-- 页面标题卡片 -->
        <div class="admin-card">
            <div class="admin-card-header">
                <div class="admin-page-header">
                    <div class="admin-page-title">
                        <div class="admin-title-icon admin-icon-green">
                            <i class="ti ti-terminal text-lg text-green-600 dark:text-green-400"></i>
                        </div>
                        <div class="admin-title-text">
                            <h1>SSH脚本管理</h1>
                            <p class="text-xs">管理和使用可复用的Shell脚本片段</p>
                        </div>
                    </div>
                    <button onclick="showAddScriptModal()"
                            class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95">
                        <i class="ti ti-plus text-sm"></i>
                        <span>新增脚本</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选区域 -->
        <div class="admin-card p-4 space-y-4">
            <!-- 搜索框 -->
            <div class="relative">
                <input type="text"
                       id="searchInput"
                       placeholder="搜索脚本名称、内容、描述或标签..."
                       value="{{search}}"
                       class="w-full px-4 py-2 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg text-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent pl-10">
                <i class="ti ti-search absolute left-3 top-2.5 text-slate-400 text-sm"></i>
            </div>

            <!-- 分类筛选 -->
            <div class="flex flex-wrap gap-2">
                <button class="px-3 py-1.5 text-sm font-medium rounded-lg transition-all duration-200 {%if selectedCategory == 'all' or not selectedCategory%}bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300{%else%}bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400 hover:bg-slate-200 dark:hover:bg-slate-700{%endif%}"
                        onclick="filterByCategory('all')">
                    <span>全部 ({{ssh_scripts|length}})</span>
                </button>
                {%for cat in categories%}
                <button class="px-3 py-1.5 text-sm font-medium rounded-lg transition-all duration-200 {%if selectedCategory == cat.category%}bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300{%else%}bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400 hover:bg-slate-200 dark:hover:bg-slate-700{%endif%}"
                        onclick="filterByCategory('{{cat.category}}')">
                    <span>{{CATEGORIES[cat.category].icon}} {{CATEGORIES[cat.category].name}} ({{cat.count}})</span>
                </button>
                {%endfor%}
            </div>
        </div>

        <!-- 脚本列表 -->
        <div class="grid gap-4" id="scriptsList">
            {%for script in ssh_scripts%}
            <div class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 shadow-sm hover:shadow-md hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-200" data-script-id="{{script.id}}">
                <div class="p-4 space-y-3">
                    <!-- 脚本头部 -->
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center gap-2 mb-1">
                                <h3 class="text-base font-medium text-slate-800 dark:text-slate-200">{{script.name}}</h3>
                                <span class="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300">
                                    {{CATEGORIES[script.category].icon}} {{CATEGORIES[script.category].name}}
                                </span>
                            </div>
                            {%if script.description%}
                            <p class="text-sm text-slate-600 dark:text-slate-400">{{script.description}}</p>
                            {%endif%}
                        </div>

                        <!-- 快捷操作 -->
                        <div class="flex items-center gap-1">
                            <button onclick="copyScript('{{script.id}}')"
                                    class="p-1.5 text-slate-500 hover:text-green-600 dark:text-slate-400 dark:hover:text-green-400 transition-colors"
                                    title="复制脚本">
                                <i class="ti ti-copy text-sm"></i>
                            </button>
                            <button onclick="togglePreview('{{script.id}}')"
                                    class="p-1.5 text-slate-500 hover:text-blue-600 dark:text-slate-400 dark:hover:text-blue-400 transition-colors"
                                    title="预览脚本">
                                <i class="ti ti-eye text-sm"></i>
                            </button>
                            <button onclick="editScript('{{script.id}}')"
                                    class="p-1.5 text-slate-500 hover:text-purple-600 dark:text-slate-400 dark:hover:text-purple-400 transition-colors"
                                    title="编辑脚本">
                                <i class="ti ti-edit text-sm"></i>
                            </button>
                            <button onclick="deleteScript('{{script.id}}')"
                                    class="p-1.5 text-slate-500 hover:text-red-600 dark:text-slate-400 dark:hover:text-red-400 transition-colors"
                                    title="删除脚本">
                                <i class="ti ti-trash text-sm"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 标签和统计 -->
                    <div class="flex items-center justify-between text-xs">
                        <div class="flex items-center gap-3">
                            {%if script.tags%}
                            <div class="flex items-center gap-1">
                                <i class="ti ti-tag text-xs text-slate-400"></i>
                                <span class="text-slate-600 dark:text-slate-400">{{script.tags}}</span>
                            </div>
                            {%endif%}
                            {%if script.variables and script.variables|length > 0%}
                            <div class="flex items-center gap-1">
                                <i class="ti ti-code text-xs text-slate-400"></i>
                                <span class="text-slate-600 dark:text-slate-400">{{script.variables|length}} 个变量</span>
                            </div>
                            {%endif%}
                        </div>
                        <div class="flex items-center gap-3 text-slate-500 dark:text-slate-400">
                            <span>使用 {% if script.usage_count %}{{script.usage_count}}{% else %}0{% endif %} 次</span>
                        </div>
                    </div>

                    <!-- 脚本预览（默认隐藏） -->
                    <div id="preview-{{script.id}}" class="hidden space-y-3 pt-3 border-t border-slate-200 dark:border-slate-700">
                        <!-- 变量说明 -->
                        {%if script.variables and script.variables|length > 0%}
                        <div>
                            <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">环境变量</h4>
                            <div class="space-y-1">
                                {%for var in script.variables%}
                                <div class="flex items-start gap-2 text-xs">
                                    <code class="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 px-1 rounded">{{var.name}}</code>
                                    <span class="text-slate-600 dark:text-slate-400">{{var.description}}</span>
                                    {%if var.default%}
                                    <span class="text-slate-500 dark:text-slate-500">(默认: {{var.default}})</span>
                                    {%endif%}
                                </div>
                                {%endfor%}
                            </div>
                        </div>
                        {%endif%}

                        <!-- 代码预览 -->
                        <div>
                            <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">脚本内容</h4>
                            <pre class="font-mono text-xs bg-slate-50 dark:bg-slate-900 p-3 rounded-md overflow-x-auto"><code>{{script.content}}</code></pre>
                        </div>

                        <!-- 使用示例 -->
                        {%if script.examples and script.examples|length > 0%}
                        <div>
                            <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">使用示例</h4>
                            <div class="space-y-2">
                                {%for example in script.examples%}
                                <div class="bg-slate-50 dark:bg-slate-900 p-3 rounded-md">
                                    <p class="text-xs text-slate-600 dark:text-slate-400 mb-1">{{example.description}}</p>
                                    <pre class="font-mono text-xs text-slate-800 dark:text-slate-200">{{example.command}}</pre>
                                </div>
                                {%endfor%}
                            </div>
                        </div>
                        {%endif%}
                    </div>
                </div>
            </div>
            {%endfor%}

            <!-- 空状态 -->
            {%if ssh_scripts|length == 0%}
            <div class="text-center py-12">
                <i class="ti ti-terminal text-4xl text-slate-300 dark:text-slate-600"></i>
                <p class="mt-2 text-slate-500 dark:text-slate-400">暂无脚本</p>
                <button onclick="showAddScriptModal()"
                        class="mt-4 text-sm text-purple-600 dark:text-purple-400 hover:underline">
                    添加第一个脚本
                </button>
            </div>
            {%endif%}
        </div>
    </div>
</div>

<!-- 添加/编辑脚本模态框 -->
<div id="scriptModal" class="fixed inset-0 bg-black/50 hidden items-center justify-center z-50 p-4 overflow-y-auto">
    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div class="sticky top-0 bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-4">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-slate-800 dark:text-slate-200" id="modalTitle">新增脚本</h3>
                <button onclick="closeScriptModal()"
                        class="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200">
                    <i class="ti ti-x"></i>
                </button>
            </div>
        </div>

        <form id="scriptForm" class="p-6 space-y-6">
            <input type="hidden" id="scriptId">

            <!-- 基本信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">脚本名称</label>
                    <input type="text"
                           id="scriptName"
                           class="w-full px-3 py-2 bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-md text-sm"
                           placeholder="例如：清理系统日志">
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">分类</label>
                    <select id="scriptCategory"
                            class="w-full px-3 py-2 bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-md text-sm">
                        {%for key, cat in CATEGORIES%}
                        <option value="{{key}}">{{cat.icon}} {{cat.name}}</option>
                        {%endfor%}
                    </select>
                </div>
            </div>

            <!-- 描述 -->
            <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">描述</label>
                <textarea id="scriptDescription"
                          rows="2"
                          class="w-full px-3 py-2 bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-md text-sm"
                          placeholder="简要描述脚本的功能和用途"></textarea>
            </div>

            <!-- 标签 -->
            <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">标签</label>
                <input type="text"
                       id="scriptTags"
                       class="w-full px-3 py-2 bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-md text-sm"
                       placeholder="多个标签用逗号分隔，如：日志,清理,维护">
            </div>

            <!-- 脚本内容 -->
            <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">脚本内容</label>
                <div class="flex justify-end mb-2">
                    <button type="button"
                            onclick="insertTemplate()"
                            class="text-xs text-purple-600 dark:text-purple-400 hover:underline">
                        插入模板
                    </button>
                </div>
                <textarea id="scriptContent"
                          rows="10"
                          class="w-full px-3 py-2 bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-md text-sm font-mono"
                          placeholder="#!/bin/bash"></textarea>
            </div>

            <!-- 变量管理 -->
            <div>
                <div class="flex items-center justify-between mb-2">
                    <label class="text-sm font-medium text-slate-700 dark:text-slate-300">环境变量</label>
                    <button type="button"
                            onclick="addVariable()"
                            class="text-xs text-purple-600 dark:text-purple-400 hover:underline">
                        + 添加变量
                    </button>
                </div>
                <div id="variablesList" class="space-y-2"></div>
            </div>

            <!-- 使用示例 -->
            <div>
                <div class="flex items-center justify-between mb-2">
                    <label class="text-sm font-medium text-slate-700 dark:text-slate-300">使用示例</label>
                    <button type="button"
                            onclick="addExample()"
                            class="text-xs text-purple-600 dark:text-purple-400 hover:underline">
                        + 添加示例
                    </button>
                </div>
                <div id="examplesList" class="space-y-2"></div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex justify-end gap-3 pt-4 border-t border-slate-200 dark:border-slate-700">
                <button type="button"
                        onclick="closeScriptModal()"
                        class="px-4 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 dark:text-slate-300 dark:hover:text-white bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700 rounded-md transition-colors">
                    取消
                </button>
                <button type="submit"
                        class="px-4 py-2 text-sm font-medium text-white bg-purple-500 hover:bg-purple-600 rounded-md transition-colors">
                    保存
                </button>
            </div>
        </form>
    </div>
</div>
{%endblock%}

{%block js%}
<script>
// 全局变量
let currentScript = null;
let variables = [];
let examples = [];

// 搜索功能
const searchInput = document.getElementById('searchInput');
let searchTimeout;

searchInput.addEventListener('input', (e) => {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        const search = e.target.value;
        const url = new URL(window.location);
        url.searchParams.set('search', search);
        window.location = url;
    }, 500);
});

// 分类筛选
function filterByCategory(category) {
    const url = new URL(window.location);
    url.searchParams.set('category', category);
    if (category === 'all') {
        url.searchParams.delete('category');
    }
    window.location = url;
}

// 切换预览
function togglePreview(id) {
    const preview = document.getElementById(`preview-${id}`);
    if (preview.classList.contains('hidden')) {
        preview.classList.remove('hidden');
        // 记录使用
        fetch('/admin/ssh_scripts/use', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ id })
        });
    } else {
        preview.classList.add('hidden');
    }
}

// 复制脚本
async function copyScript(id) {
    try {
        const res = await postjson('/admin/ssh_scripts/get', { id });
        if (res.status && res.data) {
            await navigator.clipboard.writeText(res.data.content);
            notice('脚本已复制到剪贴板', 'success');
            // 记录使用
            fetch('/admin/ssh_scripts/use', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id })
            });
        }
    } catch (error) {
        notice('复制失败', 'error');
    }
}

// 显示添加脚本模态框
function showAddScriptModal() {
    document.getElementById('modalTitle').textContent = '新增脚本';
    document.getElementById('scriptId').value = '';
    document.getElementById('scriptForm').reset();
    variables = [];
    examples = [];
    updateVariablesList();
    updateExamplesList();
    document.getElementById('scriptModal').classList.remove('hidden');
    document.getElementById('scriptModal').classList.add('flex');
}

// 编辑脚本
async function editScript(id) {
    try {
        const res = await postjson('/admin/ssh_scripts/get', { id });
        if (res.status && res.data) {
            currentScript = res.data;
            document.getElementById('modalTitle').textContent = '编辑脚本';
            document.getElementById('scriptId').value = id;
            document.getElementById('scriptName').value = res.data.name;
            document.getElementById('scriptCategory').value = res.data.category || 'custom';
            document.getElementById('scriptDescription').value = res.data.description || '';
            document.getElementById('scriptTags').value = res.data.tags || '';
            document.getElementById('scriptContent').value = res.data.content;

            variables = res.data.variables || [];
            examples = res.data.examples || [];
            updateVariablesList();
            updateExamplesList();

            document.getElementById('scriptModal').classList.remove('hidden');
            document.getElementById('scriptModal').classList.add('flex');
        }
    } catch (error) {
        notice('获取脚本失败', 'error');
    }
}

// 关闭模态框
function closeScriptModal() {
    document.getElementById('scriptModal').classList.add('hidden');
    document.getElementById('scriptModal').classList.remove('flex');
    currentScript = null;
}

// 插入模板
function insertTemplate() {
    const template = `#!/bin/bash
#
# 名称: 脚本名称
# 描述: 脚本描述
# 作者: $(whoami)
# 日期: $(date +%Y-%m-%d)
#

# 检查必需的环境变量
: \${VAR1:?错误: 请设置VAR1变量}
: \${VAR2:?错误: 请设置VAR2变量}

# 主逻辑
echo "开始执行..."

# 在这里添加你的脚本逻辑

echo "执行完成"`;

    document.getElementById('scriptContent').value = template;
}

// 添加变量
function addVariable() {
    variables.push({ name: '', description: '', default: '' });
    updateVariablesList();
}

// 更新变量列表
function updateVariablesList() {
    const container = document.getElementById('variablesList');
    container.innerHTML = variables.map((v, i) => `
        <div class="flex gap-2 items-start">
            <input type="text"
                   placeholder="变量名"
                   value="${v.name}"
                   onchange="updateVariable(${i}, 'name', this.value)"
                   class="flex-1 px-2 py-1 bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded text-xs">
            <input type="text"
                   placeholder="描述"
                   value="${v.description}"
                   onchange="updateVariable(${i}, 'description', this.value)"
                   class="flex-2 px-2 py-1 bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded text-xs">
            <input type="text"
                   placeholder="默认值"
                   value="${v.default || ''}"
                   onchange="updateVariable(${i}, 'default', this.value)"
                   class="flex-1 px-2 py-1 bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded text-xs">
            <button type="button"
                    onclick="removeVariable(${i})"
                    class="text-red-500 hover:text-red-700">
                <i class="ti ti-x text-sm"></i>
            </button>
        </div>
    `).join('');
}

// 更新变量
function updateVariable(index, field, value) {
    variables[index][field] = value;
}

// 删除变量
function removeVariable(index) {
    variables.splice(index, 1);
    updateVariablesList();
}

// 添加示例
function addExample() {
    examples.push({ description: '', command: '' });
    updateExamplesList();
}

// 更新示例列表
function updateExamplesList() {
    const container = document.getElementById('examplesList');
    container.innerHTML = examples.map((e, i) => `
        <div class="space-y-2 p-3 bg-slate-50 dark:bg-slate-900 rounded-md">
            <input type="text"
                   placeholder="示例描述"
                   value="${e.description}"
                   onchange="updateExample(${i}, 'description', this.value)"
                   class="w-full px-2 py-1 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded text-xs">
            <textarea placeholder="命令示例"
                      onchange="updateExample(${i}, 'command', this.value)"
                      rows="2"
                      class="w-full px-2 py-1 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded text-xs font-mono">${e.command}</textarea>
            <button type="button"
                    onclick="removeExample(${i})"
                    class="text-xs text-red-500 hover:text-red-700">
                删除示例
            </button>
        </div>
    `).join('');
}

// 更新示例
function updateExample(index, field, value) {
    examples[index][field] = value;
}

// 删除示例
function removeExample(index) {
    examples.splice(index, 1);
    updateExamplesList();
}

// 删除脚本
async function deleteScript(id) {
    if (!confirm('确定要删除这个脚本吗？')) return;

    try {
        const res = await postjson('/admin/ssh_scripts/del', { id });
        if (res.status) {
            notice('删除成功', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            notice(res.data || '删除失败', 'error');
        }
    } catch (error) {
        notice('删除失败', 'error');
    }
}

// 保存脚本
document.getElementById('scriptForm').addEventListener('submit', async (e) => {
    e.preventDefault();

    const id = document.getElementById('scriptId').value;
    const data = {
        name: document.getElementById('scriptName').value,
        content: document.getElementById('scriptContent').value,
        category: document.getElementById('scriptCategory').value,
        description: document.getElementById('scriptDescription').value,
        tags: document.getElementById('scriptTags').value,
        variables: variables.filter(v => v.name),
        examples: examples.filter(e => e.command)
    };

    if (!data.name || !data.content) {
        notice('请填写脚本名称和内容', 'error');
        return;
    }

    try {
        const url = id ? '/admin/ssh_scripts/upd' : '/admin/ssh_scripts/add';
        if (id) data.id = id;

        const res = await postjson(url, data);
        if (res.status) {
            notice(id ? '更新成功' : '添加成功', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            notice(res.data || '操作失败', 'error');
        }
    } catch (error) {
        notice('操作失败', 'error');
    }
});

// ESC键关闭模态框
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        closeScriptModal();
    }
});

// 工具函数
function V(id) {
    return document.getElementById(id).value;
}
</script>
{%endblock%}