<!DOCTYPE html>
<html lang="zh-CN" data-theme="<%=theme%>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分析 - DStatus</title>
    
    <!-- ECharts 图表库 -->
    <script src="/js/libs/echarts.min.js"></script>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="/css/style.min.css">
    <!-- Material Icons removed - using Tabler Icons instead -->
    
    <style>
        .ai-analytics-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .ai-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .ai-header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5rem;
            font-weight: 300;
        }
        
        .ai-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .ai-controls-panel {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .control-row {
            display: flex;
            gap: 20px;
            align-items: end;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            min-width: 150px;
        }
        
        .control-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        
        .form-control {
            padding: 10px 15px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            background: var(--input-bg);
            color: var(--text-color);
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .chart-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .chart-card h3 {
            margin: 0 0 20px 0;
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .chart-card [data-lucide] {
            color: #667eea;
            width: 1.6rem;
            height: 1.6rem;
        }
        
        .anomaly-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .results-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        .anomaly-results {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 25px;
        }
        
        .result-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .summary-card {
            background: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .summary-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .summary-value.normal { color: #28a745; }
        .summary-value.warning { color: #ffc107; }
        .summary-value.error { color: #dc3545; }
        
        .summary-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .status-indicator {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
        
        .status-indicator.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #b8daff;
        }
        
        .status-indicator.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-indicator.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-indicator.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        @media (max-width: 768px) {
            .control-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
            
            .result-summary {
                grid-template-columns: 1fr 1fr;
            }
            
            .ai-header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <%- include('../sidebar') %>
    
    <div class="ai-analytics-container">
        <!-- AI分析标题区域 -->
        <div class="ai-header">
            <h1>
                <i data-lucide="brain" class="inline w-12 h-12 mr-4"></i>
                AI智能分析
            </h1>
            <p>基于人工智能的自动化分析、异常检测和智能报告</p>
        </div>
        
        <!-- 状态指示器 -->
        <div id="status-indicator" class="status-indicator"></div>
        
        <!-- 控制面板 -->
        <div class="ai-controls-panel">
            <h3>
                <i data-lucide="settings" class="w-5 h-5"></i>
                分析配置
            </h3>
            
            <div class="control-row">
                <div class="control-group">
                    <label for="server-selector">目标服务器</label>
                    <select id="server-selector" class="form-control">
                        <option value="">加载中...</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="datatype-selector">数据类型</label>
                    <select id="datatype-selector" class="form-control">
                        <option value="tcping">TCPing延迟</option>
                        <option value="load">系统负载</option>
                        <option value="traffic">流量统计</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="timerange-selector">时间范围</label>
                    <select id="timerange-selector" class="form-control">
                        <option value="1h">最近1小时</option>
                        <option value="6h">最近6小时</option>
                        <option value="24h">最近24小时</option>
                        <option value="7d">最近7天</option>
                        <option value="30d">最近30天</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="anomaly-algorithm">检测算法</label>
                    <select id="anomaly-algorithm" class="form-control">
                        <option value="threeSigma">3-Sigma规则</option>
                        <option value="iqr">IQR方法</option>
                        <option value="zScore">Z-Score方法</option>
                        <option value="ensemble">集成算法</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label>&nbsp;</label>
                    <button id="analyze-btn" class="btn btn-primary">
                        <i data-lucide="brain" class="w-5 h-5"></i>
                        开始AI分析
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 分析结果区域 -->
        <div class="results-grid">
            <!-- 异常检测图表 -->
            <div class="chart-card">
                <h3>
                    <i data-lucide="bar-chart-3" class="w-5 h-5"></i>
                    异常检测结果
                </h3>
                <div id="anomaly-chart" style="height: 500px;"></div>
            </div>
            
            <!-- 分析结果面板 -->
            <div class="anomaly-results">
                <h3>
                    <i data-lucide="file-bar-chart" class="w-5 h-5"></i>
                    分析报告
                </h3>
                
                <div id="analysis-summary" class="result-summary">
                    <div class="summary-card">
                        <div class="summary-value normal" id="total-points">-</div>
                        <div class="summary-label">总数据点</div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-value error" id="anomaly-count">-</div>
                        <div class="summary-label">异常点数</div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-value warning" id="anomaly-rate">-</div>
                        <div class="summary-label">异常率</div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-value normal" id="confidence">-</div>
                        <div class="summary-label">置信度</div>
                    </div>
                </div>
                
                <div id="detailed-results" class="detailed-results">
                    <h4>
                        <i data-lucide="lightbulb" class="w-5 h-5"></i>
                        详细统计
                    </h4>
                    <div id="statistics-content">
                        <p style="text-align: center; color: var(--text-secondary); padding: 40px;">
                            点击"开始AI分析"查看详细结果
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- AI智能洞察 (未来功能预留) -->
        <div class="chart-card" style="margin-top: 30px;">
            <h3>
                <i data-lucide="lightbulb" class="w-5 h-5"></i>
                AI智能洞察
                <span style="font-size: 0.8rem; background: #ffc107; color: #212529; padding: 4px 8px; border-radius: 4px; margin-left: 10px;">即将推出</span>
            </h3>
            <div style="height: 200px; display: flex; align-items: center; justify-content: center; background: var(--bg-color); border-radius: 8px;">
                <div style="text-align: center; color: var(--text-secondary);">
                    <i data-lucide="sparkles" class="w-16 h-16 mb-4 opacity-50"></i>
                    <p>基于Gemini AI的智能分析和建议即将推出</p>
                    <p style="font-size: 0.9rem;">将提供自动化性能优化建议、异常原因分析和预测性维护提醒</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="/js/feature-wall.js"></script>
    <script>
        // AI分析页面控制器
        class AIAnalyticsPage {
            constructor() {
                this.selectedServer = null;
                this.dataType = 'tcping';
                this.timeRange = { start: null, end: null };
                this.algorithm = 'threeSigma';
                this.chart = null;
                
                this.init();
            }
            
            async init() {
                console.log('[AI Analytics] 初始化AI分析页面');
                
                // 检查功能权限
                if (!await this.checkPermission()) {
                    return;
                }
                
                // 设置默认时间范围
                this.setTimeRange('1h');
                
                // 加载服务器列表
                await this.loadServers();
                
                // 设置事件监听
                this.setupEventListeners();
                
                console.log('[AI Analytics] 初始化完成');
            }
            
            async checkPermission() {
                try {
                    // 等待FeatureWall初始化
                    if (typeof FeatureWall !== 'undefined') {
                        await FeatureWall.init();
                        
                        if (!FeatureWall.hasFeature('ai-analytics') && !FeatureWall.hasFeature('AI_ANALYTICS')) {
                            this.showPermissionError();
                            return false;
                        }
                    }
                    return true;
                } catch (error) {
                    console.error('[AI Analytics] 权限检查失败:', error);
                    return true; // 出错时允许访问
                }
            }
            
            showPermissionError() {
                document.querySelector('.ai-analytics-container').innerHTML = `
                    <div style="text-align: center; padding: 100px 20px;">
                        <i data-lucide="lock" class="w-20 h-20 mb-5" style="color: #ffc107;"></i>
                        <h2 style="color: var(--text-color); margin-bottom: 15px;">需要AI分析功能</h2>
                        <p style="color: var(--text-secondary); margin-bottom: 30px;">
                            当前套餐不包含AI分析功能，请升级套餐以使用基于人工智能的异常检测和智能分析
                        </p>
                        <a href="/admin" class="btn btn-primary">返回管理面板</a>
                    </div>
                `;
            }
            
            setTimeRange(range) {
                const now = Math.floor(Date.now() / 1000);
                const ranges = {
                    '1h': 3600,
                    '6h': 21600,
                    '24h': 86400,
                    '7d': 604800,
                    '30d': 2592000
                };
                
                const duration = ranges[range] || 3600;
                this.timeRange = {
                    start: now - duration,
                    end: now
                };
            }
            
            async loadServers() {
                try {
                    const response = await fetch('/api/analytics/servers');
                    const result = await response.json();
                    
                    if (result.success) {
                        const selector = document.getElementById('server-selector');
                        selector.innerHTML = '<option value="">选择服务器</option>';
                        
                        result.data.forEach(server => {
                            const option = document.createElement('option');
                            option.value = server.id;
                            option.textContent = server.name;
                            selector.appendChild(option);
                        });
                        
                        // 选择第一个服务器
                        if (result.data.length > 0) {
                            this.selectedServer = result.data[0].id;
                            selector.value = this.selectedServer;
                        }
                    }
                } catch (error) {
                    console.error('[AI Analytics] 加载服务器列表失败:', error);
                    this.showStatus('加载服务器列表失败: ' + error.message, 'error');
                }
            }
            
            setupEventListeners() {
                // 服务器选择
                document.getElementById('server-selector').addEventListener('change', (e) => {
                    this.selectedServer = e.target.value;
                });
                
                // 数据类型选择
                document.getElementById('datatype-selector').addEventListener('change', (e) => {
                    this.dataType = e.target.value;
                });
                
                // 时间范围选择
                document.getElementById('timerange-selector').addEventListener('change', (e) => {
                    this.setTimeRange(e.target.value);
                });
                
                // 算法选择
                document.getElementById('anomaly-algorithm').addEventListener('change', (e) => {
                    this.algorithm = e.target.value;
                });
                
                // 分析按钮
                document.getElementById('analyze-btn').addEventListener('click', () => {
                    this.startAnalysis();
                });
            }
            
            async startAnalysis() {
                if (!this.selectedServer) {
                    this.showStatus('请选择服务器', 'warning');
                    return;
                }
                
                this.showStatus('正在执行AI分析...', 'info');
                
                try {
                    const requestData = {
                        serverId: this.selectedServer,
                        startTime: this.timeRange.start,
                        endTime: this.timeRange.end,
                        dataType: this.dataType,
                        algorithm: this.algorithm,
                        options: {
                            granularity: this.getGranularity()
                        }
                    };
                    
                    const response = await fetch('/api/analytics/anomalies/detect', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestData)
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        this.updateResults(result.data);
                        this.showStatus(`AI分析完成，发现 ${result.data.summary.anomalyCount} 个异常点`, 'success');
                    } else {
                        if (response.status === 403) {
                            this.showStatus('当前套餐不支持AI分析功能，请升级套餐', 'error');
                        } else {
                            throw new Error(result.error || 'AI分析失败');
                        }
                    }
                } catch (error) {
                    console.error('[AI Analytics] 分析失败:', error);
                    this.showStatus('AI分析失败: ' + error.message, 'error');
                }
            }
            
            updateResults(data) {
                // 更新摘要数据
                document.getElementById('total-points').textContent = data.summary.totalPoints;
                document.getElementById('anomaly-count').textContent = data.summary.anomalyCount;
                document.getElementById('anomaly-rate').textContent = data.summary.anomalyRate;
                document.getElementById('confidence').textContent = '95%'; // 固定置信度
                
                // 更新详细统计
                const statsContent = document.getElementById('statistics-content');
                const { statistics } = data;
                
                statsContent.innerHTML = `
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                        <div>
                            <strong>数据均值:</strong><br>
                            <span style="font-size: 1.2rem; color: #667eea;">${statistics.mean.toFixed(2)}</span>
                        </div>
                        <div>
                            <strong>标准差:</strong><br>
                            <span style="font-size: 1.2rem; color: #667eea;">${statistics.std.toFixed(2)}</span>
                        </div>
                        <div>
                            <strong>最小值:</strong><br>
                            <span style="font-size: 1.2rem; color: #28a745;">${statistics.min}</span>
                        </div>
                        <div>
                            <strong>最大值:</strong><br>
                            <span style="font-size: 1.2rem; color: #dc3545;">${statistics.max}</span>
                        </div>
                    </div>
                    <div style="padding: 15px; background: var(--bg-color); border-radius: 8px;">
                        <strong>算法:</strong> ${data.algorithm}<br>
                        <strong>检测阈值:</strong> ${statistics.threshold ? statistics.threshold.toFixed(2) : 'N/A'}<br>
                        <strong>数据粒度:</strong> ${this.getGranularity()}
                    </div>
                `;
                
                // 更新图表
                this.updateChart(data);
            }
            
            updateChart(data) {
                const container = document.getElementById('anomaly-chart');
                
                if (!this.chart) {
                    this.chart = echarts.init(container, window.theme || null);
                }
                
                // 准备数据
                const normalData = [];
                const anomalyData = [];
                
                data.anomalies.forEach(point => {
                    if (point.isAnomaly) {
                        anomalyData.push([point.timestamp * 1000, point.value]);
                    } else {
                        normalData.push([point.timestamp * 1000, point.value]);
                    }
                });
                
                const option = {
                    title: {
                        text: `AI异常检测结果 (${data.algorithm})`,
                        left: 'center',
                        textStyle: {
                            color: 'var(--text-color)'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: (params) => {
                            const timestamp = params[0].value[0];
                            const date = new Date(timestamp);
                            let html = date.toLocaleString() + '<br/>';
                            
                            params.forEach(param => {
                                const isAnomaly = param.seriesName === '异常点';
                                const marker = isAnomaly ? '🔴' : '🟢';
                                html += `${marker} ${param.seriesName}: ${param.value[1].toFixed(2)}<br/>`;
                            });
                            
                            return html;
                        }
                    },
                    legend: {
                        data: ['正常数据', '异常点'],
                        top: 30,
                        textStyle: {
                            color: 'var(--text-color)'
                        }
                    },
                    xAxis: {
                        type: 'time',
                        name: '时间',
                        axisLabel: {
                            color: 'var(--text-color)'
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: this.getYAxisLabel(),
                        axisLabel: {
                            color: 'var(--text-color)'
                        }
                    },
                    series: [
                        {
                            name: '正常数据',
                            type: 'scatter',
                            data: normalData,
                            itemStyle: {
                                color: '#5470c6'
                            },
                            symbolSize: 6
                        },
                        {
                            name: '异常点',
                            type: 'scatter',
                            data: anomalyData,
                            itemStyle: {
                                color: '#ff4757'
                            },
                            symbolSize: 10
                        }
                    ]
                };
                
                this.chart.setOption(option);
            }
            
            getGranularity() {
                const duration = this.timeRange.end - this.timeRange.start;
                
                if (duration <= 3600) return 'raw';
                if (duration <= 21600) return 'minute';
                if (duration <= 86400) return '5min';
                if (duration <= 604800) return 'hourly';
                return 'daily';
            }
            
            getYAxisLabel() {
                switch (this.dataType) {
                    case 'tcping': return '延迟 (ms)';
                    case 'load': return '负载';
                    case 'traffic': return '流量 (bytes)';
                    default: return '值';
                }
            }
            
            showStatus(message, type = 'info') {
                const indicator = document.getElementById('status-indicator');
                indicator.className = `status-indicator ${type}`;
                indicator.textContent = message;
                indicator.style.display = 'block';
                
                if (type === 'success') {
                    setTimeout(() => {
                        indicator.style.display = 'none';
                    }, 5000);
                }
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new AIAnalyticsPage();
        });
        
        // 响应式图表大小调整
        window.addEventListener('resize', () => {
            if (window.aiAnalyticsPage && window.aiAnalyticsPage.chart) {
                window.aiAnalyticsPage.chart.resize();
            }
        });
    </script>
</body>
</html>