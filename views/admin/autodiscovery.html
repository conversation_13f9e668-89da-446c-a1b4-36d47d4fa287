{% set title = "自动发现管理" %}
{%set admin = true%}
{% extends "../base.html" %}

{%block content%}
<!-- 页面容器 - 简洁布局 -->
<div class="flex flex-col md:flex-row gap-6">
    <!-- 引入侧边栏 -->
    {% include "admin/sidebar.html" %}

    <!-- 主内容区域 -->
    <div class="flex-1 space-y-6">
        
        <!-- 页面标题 -->
        <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700">
            <div class="p-6">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center">
                        <i class="ti ti-search text-lg text-indigo-600 dark:text-indigo-400"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-semibold text-slate-900 dark:text-white">自动发现管理</h1>
                        <p class="text-sm text-slate-500 dark:text-slate-400">管理服务器自动发现和注册功能</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 自动发现设置 -->
        <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700">
            <!-- 卡片头部 -->
            <div class="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                        <i class="ti ti-settings text-base text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-slate-900 dark:text-white">自动发现设置</h2>
                        <p class="text-sm text-slate-500 dark:text-slate-400">配置服务器自动发现功能</p>
                    </div>
                </div>
                <!-- 保存按钮 -->
                <button onclick="saveSettings()"
                                                class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white rounded-lg border border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                    <i class="ti ti-device-floppy text-sm"></i>
                    <span>保存设置</span>
                </button>
            </div>

            <!-- 设置内容 -->
            <div class="p-6 space-y-6">
                <!-- 启用开关 -->
                <div class="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                    <div class="flex items-center space-x-4">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox"
                                   {%if setting.autodiscovery.enabled%}checked{%endif%}
                                   key="autodiscovery.enabled"
                                   class="sr-only peer">
                            <div class="w-9 h-5 bg-slate-300 dark:bg-slate-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-500"></div>
                        </label>
                        <div>
                            <span class="text-sm font-medium text-slate-900 dark:text-white">启用自动发现功能</span>
                            <p class="text-xs text-slate-500 dark:text-slate-400 mt-1">允许服务器自动注册到系统中</p>
                        </div>
                    </div>
                </div>

                <!-- 表单字段 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 注册密钥 -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-slate-900 dark:text-white">注册密钥</label>
                        <div class="relative">
                            <input type="text"
                                   value="{{setting.autodiscovery.registrationKey}}"
                                   key="autodiscovery.registrationKey"
                                   class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-32">
                            <div class="absolute right-2 top-1/2 -translate-y-1/2 flex space-x-1">
                                <button type="button"
                                        onclick="generateRegistrationKey()"
                                                                                class="px-2 py-1 text-xs font-medium bg-blue-600 hover:bg-blue-700 text-white rounded focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                                    生成
                                </button>
                                <button type="button"
                                        onclick="copyInstallCommand()"
                                                                                class="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-green-600 hover:bg-green-700 text-white rounded focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">
                                    <i class="ti ti-copy text-xs"></i>
                                    <span>命令</span>
                                </button>
                            </div>
                        </div>
                        <p class="text-xs text-slate-500 dark:text-slate-400">用于验证服务器注册请求的密钥</p>
                    </div>

                    <!-- 默认分组 -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-slate-900 dark:text-white">默认分组</label>
                        <select key="autodiscovery.defaultGroup"
                                class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            {% for group in groups %}
                            <option value="{{group.id}}" {%if setting.autodiscovery.defaultGroup == group.id%}selected{%endif%}>{{group.name}}</option>
                            {% endfor %}
                        </select>
                        <p class="text-xs text-slate-500 dark:text-slate-400">自动添加模式下，新发现的服务器将被添加到此分组。审核模式下，此分组将作为默认选中项。</p>
                    </div>
                </div>

                <!-- 其他设置选项 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- 要求注册密钥 -->
                    <div class="flex items-center space-x-4 p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox"
                                   {%if setting.autodiscovery.requireKey%}checked{%endif%}
                                   key="autodiscovery.requireKey"
                                   class="sr-only peer">
                            <div class="w-9 h-5 bg-slate-300 dark:bg-slate-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-500"></div>
                        </label>
                        <div>
                            <span class="text-sm font-medium text-slate-900 dark:text-white">要求注册密钥</span>
                            <p class="text-xs text-slate-500 dark:text-slate-400 mt-1">启用后，服务器注册时必须提供正确的密钥</p>
                        </div>
                    </div>

                    <!-- 需要审核 -->
                    <div class="flex items-center space-x-4 p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox"
                                   {%if setting.autodiscovery.requireApproval%}checked{%endif%}
                                   key="autodiscovery.requireApproval"
                                   class="sr-only peer">
                            <div class="w-9 h-5 bg-slate-300 dark:bg-slate-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-500"></div>
                        </label>
                        <div>
                            <span class="text-sm font-medium text-slate-900 dark:text-white">需要审核</span>
                            <p class="text-xs text-slate-500 dark:text-slate-400 mt-1">启用后，新注册的服务器需要管理员审核后才能添加</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 待审核服务器 -->
        <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700" id="pendingServersCard" style="display: none;">
            <!-- 卡片头部 -->
            <div class="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                        <i class="ti ti-clock text-base text-orange-600 dark:text-orange-400"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-slate-900 dark:text-white">待审核服务器</h2>
                        <p class="text-sm text-slate-500 dark:text-slate-400">等待管理员审核的服务器</p>
                    </div>
                </div>
                <!-- 按钮组 -->
                <div class="flex items-center gap-3">
                    <button onclick="clearAllPendingServers()"
                                                        class="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium bg-red-600 hover:bg-red-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors">
                        <i class="ti ti-eraser text-sm"></i>
                        <span>清空全部</span>
                    </button>
                    <button onclick="refreshPendingServers()"
                                                        class="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 transition-colors">
                        <i class="ti ti-refresh text-sm"></i>
                        <span>刷新</span>
                    </button>
                </div>
            </div>

            <!-- 列表内容 -->
            <div class="p-6">
                <div id="pendingServersContainer" class="space-y-4 min-h-[200px]">
                    <div class="flex items-center justify-center py-8">
                        <div class="w-8 h-8 border-4 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
                        <span class="ml-3 text-slate-500 dark:text-slate-400">加载中...</span>
                    </div>
                </div>

                <!-- 无数据提示 -->
                <div id="noPendingMessage" class="hidden text-center py-12">
                    <div class="flex flex-col items-center gap-4">
                        <div class="w-16 h-16 bg-slate-100 dark:bg-slate-700 rounded-lg flex items-center justify-center">
                            <i class="ti ti-circle-check text-2xl text-slate-400"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium text-slate-600 dark:text-slate-300">暂无待审核服务器</h4>
                            <p class="text-sm text-slate-500 dark:text-slate-400 mt-1">所有服务器都已处理完成</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 已发现服务器 -->
        <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700">
            <!-- 卡片头部 -->
            <div class="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                        <i class="ti ti-world text-base text-green-600 dark:text-green-400"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-slate-900 dark:text-white">已发现服务器</h2>
                        <p class="text-sm text-slate-500 dark:text-slate-400">系统中已注册的服务器</p>
                    </div>
                </div>
                <!-- 按钮组 -->
                <div class="flex items-center gap-3">
                    <button onclick="clearOfflineDiscoveredServers()"
                                                        class="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium bg-red-600 hover:bg-red-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors">
                        <i class="ti ti-broom text-sm"></i>
                        <span>清空离线</span>
                    </button>
                    <button onclick="refreshDiscoveredServers()"
                                                        class="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 transition-colors">
                        <i class="ti ti-refresh text-sm"></i>
                        <span>刷新</span>
                    </button>
                </div>
            </div>

            <!-- 卡片内容 -->
            <div class="p-6">
                <!-- 搜索和分页控制 -->
                <div class="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
                    <div class="relative w-full md:w-80">
                        <input type="text" id="serverSearchInput" placeholder="搜索服务器..."
                                                              class="w-full px-3 py-2 pr-10 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <button onclick="searchServers()" class="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 hover:text-blue-600 dark:text-slate-400 dark:hover:text-blue-400 transition-colors">
                            <i class="ti ti-search"></i>
                        </button>
                    </div>
                    <div class="flex items-center gap-3">
                        <span class="text-sm font-medium text-slate-600 dark:text-slate-400">每页显示:</span>
                        <select id="pageSize" onchange="changePageSize()"
                                                                class="px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="10">10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                </div>

                <!-- 服务器列表 -->
                <div id="discoveredServersContainer" class="space-y-4 min-h-[400px]">
                    <div class="flex items-center justify-center py-8">
                        <div class="w-8 h-8 border-4 border-green-500 border-t-transparent rounded-full animate-spin"></div>
                        <span class="ml-3 text-slate-500 dark:text-slate-400">加载中...</span>
                    </div>
                </div>

                <!-- 无数据提示 -->
                <div id="noDiscoveredMessage" class="hidden text-center py-12">
                    <div class="flex flex-col items-center gap-4">
                        <div class="w-16 h-16 bg-slate-100 dark:bg-slate-700 rounded-lg flex items-center justify-center">
                            <i class="ti ti-search-off text-2xl text-slate-400"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium text-slate-600 dark:text-slate-300">暂无已发现服务器</h4>
                            <p class="text-sm text-slate-500 dark:text-slate-400 mt-1">还没有服务器注册到系统中</p>
                        </div>
                    </div>
                </div>

                <!-- 分页控制 -->
                <div id="paginationControls" class="hidden flex justify-between items-center mt-6 pt-4 border-t border-slate-200 dark:border-slate-700">
                    <div class="text-sm text-slate-500 dark:text-slate-400">
                        显示 <span id="currentRange" class="font-medium text-slate-700 dark:text-slate-300">0-0</span> / <span id="totalServers" class="font-medium text-slate-700 dark:text-slate-300">0</span> 个服务器
                    </div>
                    <div class="flex items-center gap-2">
                        <button id="prevPageBtn" onclick="goToPrevPage()"
                                                                class="inline-flex items-center px-3 py-2 bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                            <i class="ti ti-chevron-left text-sm"></i>
                        </button>
                        <span id="currentPage" class="px-4 py-2 bg-blue-50 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-lg font-medium border border-blue-200 dark:border-blue-700">1</span>
                        <button id="nextPageBtn" onclick="goToNextPage()"
                                                                class="inline-flex items-center px-3 py-2 bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                            <i class="ti ti-chevron-right text-sm"></i>
                        </button>
                    </div>
                </div>
                </div>
            </div>
    </div>
</div>

<!-- 服务器详情弹窗 -->
<div id="serverDetailModal" class="fixed inset-0 bg-black/50 hidden items-center justify-center z-50">
    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden border border-slate-200 dark:border-slate-700">
        <!-- 模态框头部 -->
        <div class="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <i class="ti ti-info-circle text-base text-blue-600 dark:text-blue-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-900 dark:text-white" id="modalTitle">服务器详情</h3>
            </div>
            <button onclick="closeModal()" class="p-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors">
                <i class="ti ti-x"></i>
            </button>
        </div>

        <!-- 模态框内容 -->
        <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
            <div id="serverDetailContent" class="space-y-4">
                <!-- 服务器详情内容 -->
            </div>
        </div>

        <!-- 模态框底部 -->
        <div class="flex justify-end gap-3 p-6 border-t border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800">
            <button onclick="closeModal()" class="px-4 py-2 text-sm font-medium text-slate-700 dark:text-slate-300 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-slate-500 transition-colors">
                关闭
            </button>
            <button id="approveButton" class="hidden inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-green-600 hover:bg-green-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">
                <i class="ti ti-check text-sm"></i>
                <span>批准</span>
            </button>
            <button id="rejectButton" class="hidden inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-red-600 hover:bg-red-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors">
                <i class="ti ti-x text-sm"></i>
                <span>拒绝</span>
            </button>
        </div>
    </div>
</div>

<!-- 安装命令弹窗 -->
<div id="copyCommandModal" class="fixed inset-0 bg-black/50 hidden items-center justify-center z-50">
    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 overflow-hidden border border-slate-200 dark:border-slate-700">
        <!-- 模态框头部 -->
        <div class="flex items-center justify-between p-4 border-b border-slate-200 dark:border-slate-700">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                    <i class="ti ti-terminal-2 text-base text-green-600 dark:text-green-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-900 dark:text-white">Agent 管理命令</h3>
            </div>
            <button onclick="closeCopyCommandModal()" class="p-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors">
                <i class="ti ti-x"></i>
            </button>
        </div>

        <!-- 模态框内容 -->
        <div class="p-4 space-y-3">
            <!-- 标签页导航 -->
            <div class="flex gap-1 p-1 bg-slate-100 dark:bg-slate-700 rounded-lg">
                <button data-tab="install" onclick="autodiscoveryManager.showCommandTab('install')" 
                        class="flex-1 px-4 py-2 text-sm font-medium rounded-md bg-blue-600 text-white transition-colors">
                    <i class="ti ti-download text-sm mr-1 align-middle"></i>
                    安装
                </button>
                <button data-tab="update" onclick="autodiscoveryManager.showCommandTab('update')" 
                        class="flex-1 px-4 py-2 text-sm font-medium rounded-md bg-transparent text-slate-600 dark:text-slate-400 hover:bg-white/50 dark:hover:bg-slate-600 transition-colors">
                    <i class="ti ti-refresh text-sm mr-1 align-middle"></i>
                    更新
                </button>
                <button data-tab="uninstall" onclick="autodiscoveryManager.showCommandTab('uninstall')" 
                        class="flex-1 px-4 py-2 text-sm font-medium rounded-md bg-transparent text-slate-600 dark:text-slate-400 hover:bg-white/50 dark:hover:bg-slate-600 transition-colors">
                    <i class="ti ti-trash text-sm mr-1 align-middle"></i>
                    卸载
                </button>
            </div>
            
            <!-- 配置选项 -->
            <div class="bg-slate-50 dark:bg-slate-900/20 rounded-lg p-3 border border-slate-200 dark:border-slate-800">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- 主动上报选项 -->
                    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800">
                        <label class="flex items-center justify-between cursor-pointer">
                            <div class="flex items-center gap-2">
                                <i class="ti ti-refresh text-blue-600 dark:text-blue-400 text-sm"></i>
                                <div>
                                    <span class="text-sm font-medium text-blue-800 dark:text-blue-200">主动上报</span>
                                    <p class="text-xs text-blue-600 dark:text-blue-300">每2秒主动上报状态</p>
                                </div>
                            </div>
                            <input type="checkbox" id="enable-active-report" 
                                   onchange="toggleReportMode()"
                                   class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        </label>
                    </div>
                    
                    <!-- 被动模式端口选择 -->
                    <div id="passive-port-option" class="bg-slate-100 dark:bg-slate-800/20 rounded-lg p-3 border border-slate-300 dark:border-slate-600">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <i class="ti ti-ethernet text-slate-600 dark:text-slate-400 text-sm"></i>
                                <div>
                                    <span class="text-sm font-medium text-slate-800 dark:text-slate-200">监听端口</span>
                                    <p class="text-xs text-slate-600 dark:text-slate-400">被动模式端口号</p>
                                </div>
                            </div>
                            <input type="number" id="agent-port" 
                                   value="9999" min="1024" max="65535"
                                   onchange="updateCommandDisplay()"
                                   class="w-20 px-2 py-1 text-sm border border-slate-300 dark:border-slate-600 rounded bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                <div class="flex items-center gap-3">
                    <i class="ti ti-info-circle text-blue-600 dark:text-blue-400 text-sm"></i>
                    <div>
                        <p id="commandTitle" class="text-sm font-medium text-blue-800 dark:text-blue-200">安装命令</p>
                        <p id="commandDescription" class="text-xs text-blue-700 dark:text-blue-300">全新安装 DStatus Agent（如果已有配置会保留）</p>
                    </div>
                </div>
            </div>

            <div class="relative">
                <pre id="installCommand" class="bg-slate-900 dark:bg-slate-950 p-3 rounded-lg text-green-400 text-sm font-mono overflow-x-auto whitespace-pre-wrap break-all border border-slate-700"></pre>
                <button onclick="copyToClipboard('installCommand')" class="absolute top-3 right-3 inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-slate-700 hover:bg-slate-600 text-slate-200 rounded focus:outline-none focus:ring-2 focus:ring-slate-500 transition-colors">
                    <i class="ti ti-copy text-sm"></i>
                    <span>复制</span>
                </button>
            </div>

            <div class="bg-slate-50 dark:bg-slate-800 rounded-lg p-3 border border-slate-200 dark:border-slate-700">
                <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 flex items-center gap-2">
                    <i class="ti ti-help-circle text-sm text-slate-500"></i>
                    安装说明
                </h4>
                <ul class="text-xs text-slate-600 dark:text-slate-400 space-y-1">
                    <li class="flex items-start gap-2">
                        <span class="text-slate-400 mt-0.5">•</span>
                        <span>需要root权限，普通用户请使用sudo</span>
                    </li>
                    <li class="flex items-start gap-2">
                        <span class="text-slate-400 mt-0.5">•</span>
                        <span>安装完成后自动注册到系统</span>
                    </li>
                    <li class="flex items-start gap-2">
                        <span class="text-slate-400 mt-0.5">•</span>
                        <span>如启用审核需在管理界面批准</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 模态框底部 -->
        <div class="flex justify-end p-4 border-t border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800">
            <button onclick="closeCopyCommandModal()" class="px-4 py-2 text-sm font-medium text-slate-700 dark:text-slate-300 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-slate-500 transition-colors">
                关闭
            </button>
        </div>
    </div>
</div>

<!-- 刷新状态指示器 -->
<div class="fixed bottom-6 left-6 md:left-70 bg-white dark:bg-slate-800 rounded-lg p-3 text-xs text-slate-500 dark:text-slate-400 shadow-lg border border-slate-200 dark:border-slate-700">
    <div class="flex items-center gap-2">
        <i class="ti ti-refresh text-blue-500 text-sm"></i>
        <div>
            <div>上次刷新: <span id="lastRefreshTime">刚刚</span></div>
            <div>下次刷新: <span id="nextRefreshTime">30秒后</span></div>
        </div>
    </div>
</div>
{%endblock%}

{%block js%}
<script src="/js/core.js"></script>
<script src="/js/admin-buttons.js"></script>

<script src="/js/admin/autodiscovery-manager.js?v=1.0.1"></script>
<script>
// 全局函数供HTML调用
function closeCopyCommandModal() {
    if (autodiscoveryManager) {
        autodiscoveryManager.closeCopyCommandModal();
    }
}
</script>
{%endblock%}
