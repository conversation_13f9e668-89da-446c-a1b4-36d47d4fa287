{% set title = "通知设置" %}
{%set admin = true%}
{% extends "../base.html" %}

{%block head%}
<!-- PWA支持 -->
<meta name="mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

<!-- 主题样式由 theme-manager.js 动态管理 -->

<!-- 页面特定样式 -->
<style>
    /* 确保SVG图标不受强调色影响 */
    .ti {
        color: inherit !important;
    }

    /* 正确设置强调色图标 */
    .text-blue-400 .ti,
    .text-blue-500 .ti {
        color: inherit !important;
    }

    /* 侧边栏菜单项图标颜色 */
    #admin-sidebar .ti {
        color: var(--color-slate-500) !important;
    }

    #admin-sidebar a:hover .ti,
    #admin-sidebar a.active .ti,
    #admin-sidebar .text-blue-400 .ti {
        color: var(--color-purple-400) !important;
    }
</style>

<!-- 背景加载脚本 -->
<script>
// 初始化背景设置
document.addEventListener('DOMContentLoaded', function() {
    const body = document.body;

    // 从localStorage获取背景设置
    try {
        // 尝试从localStorage中获取本地保存的背景设置
        const personalizationSettings = localStorage.getItem('personalization_settings');
        if (personalizationSettings) {
            const settings = JSON.parse(personalizationSettings);
            const background = settings.background;
            
            if (background && background.enabled) {
                if (background.type === 'image' && background.imageUrl) {
                    body.style.setProperty('--bg-image', `url(${background.imageUrl})`);
                    body.classList.add('bg-custom-image');
                } else if (background.type === 'gradient' && background.gradient) {
                    body.style.setProperty('--bg-gradient', background.gradient);
                    body.classList.add('bg-custom-gradient');
                }
                
                if (background.blur) {
                    body.style.setProperty('--bg-blur', `${background.blur}px`);
                    body.classList.add('bg-blur');
                }
            }
        }
    } catch (error) {
        console.error('加载背景设置失败:', error);
    }
});
</script>
{%endblock%}

{%block content%}
    {% include "admin/sidebar.html" %}
<div class="admin-content">
    <div class="admin-content-header">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/40 dark:to-purple-800/30 rounded-xl shadow-sm flex-shrink-0 border border-purple-200/50 dark:border-purple-700/30 flex items-center justify-center">
                    <i class="ti ti-bell text-lg text-purple-600 dark:text-purple-400"></i>
                </div>
                <div>
                    <h1 class="text-lg font-semibold text-slate-800 dark:text-slate-200 tracking-wide">通知设置</h1>
                    <p class="text-sm text-slate-600 dark:text-slate-400 leading-relaxed mt-1">配置系统通知功能和告警设置</p>
                </div>
            </div>
        </div>
    </div>

    <div class="admin-content-body">
        <!-- Telegram通知设置卡片 - 独立设计 -->
        <div class="admin-card">
            <!-- 卡片头部 - 使用标准admin-list-header类 -->
            <div class="admin-list-header flex items-center gap-3">
                <div class="w-8 h-8 bg-gradient-to-br from-cyan-100 to-cyan-200 dark:from-cyan-900/40 dark:to-cyan-800/30 rounded-lg shadow-sm flex-shrink-0 border border-cyan-200/50 dark:border-cyan-700/30 flex items-center justify-center">
                    <i class="ti ti-bell text-base text-cyan-600 dark:text-cyan-400"></i>
                </div>
                <div>
                    <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">Telegram 通知设置</h2>
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">配置Telegram机器人通知功能</p>
                </div>
            </div>

            <!-- 卡片内容 - iPhone风格表单 -->
            <div class="p-4 space-y-4">
                <!-- 基本设置 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">Bot Token</label>
                        <input type="text"
                               value="{{setting.telegram.token}}"
                               key="telegram.token"
                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">从BotFather获取的机器人令牌</p>
                    </div>
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">Chat ID 列表 (用,分割)</label>
                        <input type="text"
                               value="{{setting.telegram.chatIds}}"
                               key="telegram.chatIds"
                               isarray=1
                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">接收通知的聊天ID，多个用逗号分隔</p>
                    </div>
                </div>

                <!-- 连接方式选择 - iPhone风格 -->
                <div class="bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 rounded-xl p-4 border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm">
                    <label class="flex items-center gap-2 text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3">
                        <i class="ti ti-network text-sm text-slate-500"></i>
                        连接方式
                    </label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Webhook方式 -->
                        <label class="flex items-start space-x-3 cursor-pointer p-3 bg-white/60 dark:bg-slate-700/30 rounded-lg border border-slate-200/40 dark:border-slate-600/30 hover:bg-white/80 dark:hover:bg-slate-700/50 transition-all duration-200">
                            <input type="radio"
                                   name="telegramMode"
                                   value="webhook"
                                   {%if setting.telegram.webhook%}checked{%endif%}
                                   key="telegram.webhook"
                                   class="mt-1 h-4 w-4 text-purple-500 bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 focus:ring-purple-500/50 focus:ring-2">
                            <div>
                                <span class="text-sm font-semibold text-slate-700 dark:text-slate-200 tracking-wide">Webhook方式</span>
                                <p class="text-xs text-slate-600 dark:text-slate-400 mt-1 leading-relaxed">服务器接收推送通知，适合有公网IP的服务器</p>
                            </div>
                        </label>

                        <!-- Polling方式 -->
                        <label class="flex items-start space-x-3 cursor-pointer p-3 bg-white/60 dark:bg-slate-700/30 rounded-lg border border-slate-200/40 dark:border-slate-600/30 hover:bg-white/80 dark:hover:bg-slate-700/50 transition-all duration-200">
                            <input type="radio"
                                   name="telegramMode"
                                   value="polling"
                                   {%if not setting.telegram.webhook%}checked{%endif%}
                                   key="telegram.polling"
                                   class="mt-1 h-4 w-4 text-purple-500 bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 focus:ring-purple-500/50 focus:ring-2">
                            <div>
                                <span class="text-sm font-semibold text-slate-700 dark:text-slate-200 tracking-wide">Polling方式</span>
                                <p class="text-xs text-slate-600 dark:text-slate-400 mt-1 leading-relaxed">服务器主动轮询消息，适合无公网IP的环境</p>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- API设置 - iPhone风格 -->
                <div class="space-y-3">
                    <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">API基础URL (网络问题时修改)</label>
                    <input type="text"
                           value="{%if setting.telegram.baseApiUrl%}{{setting.telegram.baseApiUrl}}{%else%}https://api.telegram.org{%endif%}"
                           key="telegram.baseApiUrl"
                           placeholder="https://api.telegram.org"
                           class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">默认使用官方 API，如果无法访问可以使用反向代理地址，例如: https://botapi.ipxxxx.com</p>
                </div>

                <!-- 启用和测试 - iPhone风格 -->
                <div class="flex items-center justify-between bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 p-4 rounded-xl border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm">
                    <div class="flex items-center space-x-4">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox"
                                   {%if setting.telegram.enabled%}checked{%endif%}
                                   key="telegram.enabled"
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-slate-300 dark:bg-slate-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
                        </label>
                        <div>
                            <span class="text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">启用通知</span>
                            <p class="text-xs text-slate-500 dark:text-slate-400 mt-1 leading-relaxed">开启Telegram通知功能</p>
                        </div>
                    </div>
                    <button onclick="testTelegramNotification()"
                            class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95">
                        <i class="ti ti-bell text-sm"></i>
                        <span>测试通知</span>
                    </button>
                </div>

                <!-- 添加通知类型控制 -->
                <div class="col-span-1 md:col-span-2 bg-slate-100 dark:bg-slate-800/30 p-3 md:p-4 rounded-lg">
                    <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 md:mb-3">通知类型</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-3">
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="server_online_notify"
                                   key="telegram.notificationTypes.serverOnline"
                                   {%if setting.telegram.notificationTypes.serverOnline%}checked{%endif%}
                                   class="w-4 h-4 text-purple-500 bg-slate-100 dark:bg-slate-800 border-slate-300 dark:border-slate-700 rounded focus:ring-purple-500">
                            <label for="server_online_notify" class="ml-2 text-xs md:text-sm text-slate-700 dark:text-slate-300">服务器上线通知</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="server_offline_notify"
                                   key="telegram.notificationTypes.serverOffline"
                                   {%if setting.telegram.notificationTypes.serverOffline%}checked{%endif%}
                                   class="w-4 h-4 text-purple-500 bg-slate-100 dark:bg-slate-800 border-slate-300 dark:border-slate-700 rounded focus:ring-purple-500">
                            <label for="server_offline_notify" class="ml-2 text-xs md:text-sm text-slate-700 dark:text-slate-300">服务器下线通知</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="traffic_limit_notify"
                                   key="telegram.notificationTypes.trafficLimit"
                                   {%if setting.telegram.notificationTypes.trafficLimit%}checked{%endif%}
                                   class="w-4 h-4 text-purple-500 bg-slate-100 dark:bg-slate-800 border-slate-300 dark:border-slate-700 rounded focus:ring-purple-500">
                            <label for="traffic_limit_notify" class="ml-2 text-xs md:text-sm text-slate-700 dark:text-slate-300">流量超限通知</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="test_notification"
                                   key="telegram.notificationTypes.testNotification"
                                   {%if setting.telegram.notificationTypes.testNotification%}checked{%endif%}
                                   class="w-4 h-4 text-purple-500 bg-slate-100 dark:bg-slate-800 border-slate-300 dark:border-slate-700 rounded focus:ring-purple-500">
                            <label for="test_notification" class="ml-2 text-xs md:text-sm text-slate-700 dark:text-slate-300">测试通知</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="status_summary_notify"
                                   key="telegram.notificationTypes.statusSummary"
                                   {%if setting.telegram.notificationTypes.statusSummary%}checked{%endif%}
                                   class="w-4 h-4 text-purple-500 bg-slate-100 dark:bg-slate-800 border-slate-300 dark:border-slate-700 rounded focus:ring-purple-500">
                            <label for="status_summary_notify" class="ml-2 text-xs md:text-sm text-slate-700 dark:text-slate-300">状态汇总通知</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="new_server_discovered_notify"
                                   key="telegram.notificationTypes.newServerDiscovered"
                                   {%if setting.telegram.notificationTypes.newServerDiscovered%}checked{%endif%}
                                   class="w-4 h-4 text-purple-500 bg-slate-100 dark:bg-slate-800 border-slate-300 dark:border-slate-700 rounded focus:ring-purple-500">
                            <label for="new_server_discovered_notify" class="ml-2 text-xs md:text-sm text-slate-700 dark:text-slate-300">新服务器发现通知</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="server_approved_notify"
                                   key="telegram.notificationTypes.serverApproved"
                                   {%if setting.telegram.notificationTypes.serverApproved%}checked{%endif%}
                                   class="w-4 h-4 text-purple-500 bg-slate-100 dark:bg-slate-800 border-slate-300 dark:border-slate-700 rounded focus:ring-purple-500">
                            <label for="server_approved_notify" class="ml-2 text-xs md:text-sm text-slate-700 dark:text-slate-300">服务器批准通知</label>
                        </div>
                    </div>
                </div>

                <!-- 通知等待时间设置 -->
                <div class="bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 rounded-xl p-4 border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm">
                    <label class="flex items-center gap-2 text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3">
                        <i class="ti ti-clock text-sm text-slate-500"></i>
                        离线通知等待时间
                    </label>
                    <div class="space-y-3">
                        <input type="number"
                               value="{%if setting.telegram.offlineNotificationDelay%}{{setting.telegram.offlineNotificationDelay}}{%else%}30{%endif%}"
                               key="telegram.offlineNotificationDelay"
                               min="5"
                               max="300"
                               step="5"
                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">服务器离线后延迟多少秒发送通知，避免短暂网络波动导致误报 (5-300秒)</p>
                    </div>
                </div>

                <!-- 保存按钮 -->
                <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 mt-6 pt-4 border-t border-slate-200 dark:border-slate-700">
                    <button onclick="edit()" class="flex-1 sm:flex-none inline-flex items-center justify-center gap-2 px-6 py-3 text-sm font-medium bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95">
                        <div class="spinner hidden w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        <i class="ti ti-check text-sm"></i>
                        <span>保存设置</span>
                    </button>
                    <p class="text-xs text-slate-500 dark:text-slate-400 text-center sm:text-left">
                        设置保存后立即生效，无需重启服务
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 底部脚本 -->
<script>
// 生成深层对象
function gen(obj, keys, value) {
    let current = obj;
    for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
            current[keys[i]] = {};
        }
        current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
}

// 编辑设置 - 只操作telegram设置
async function edit() {
    const spinner = document.querySelector('.spinner');
    
    try {
        if (spinner) spinner.classList.remove('hidden');
        
        var setting = {};
        for(var x of document.querySelectorAll("[key]")){
            var val = x.value;
            if(x.type=="checkbox")val=x.checked;
            if(x.getAttribute("isarray"))val=val.split(",");
            gen(setting,x.getAttribute("key").split('.'),val);
        }

        const response = await fetch('/admin/notification/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(setting)
        });

        if (response.ok) {
            notice('设置保存成功', 'success');
            if (spinner) spinner.classList.add('hidden');
        } else {
            const data = await response.json();
            notice(data.msg || '保存设置失败', 'error');
            if (spinner) spinner.classList.add('hidden');
        }
    } catch (error) {
        console.error('保存设置失败:', error);
        notice('保存设置失败', 'error');
        if (spinner) spinner.classList.add('hidden');
    }
}

// 测试Telegram通知
async function testTelegramNotification() {
    try {
        // 复用edit函数的设置收集逻辑
        var setting = {};
        for(var x of document.querySelectorAll("[key]")){
            var val = x.value;
            if(x.type=="checkbox")val=x.checked;
            if(x.getAttribute("isarray"))val=val.split(",");
            gen(setting,x.getAttribute("key").split('.'),val);
        }

        const response = await fetch('/admin/notification/test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({telegram: setting.telegram})
        });

        // 检查HTTP状态
        if (response.ok) {
            notice('测试通知已发送', 'success');
        } else {
            const data = await response.json();
            notice(data.msg || '发送测试通知失败', 'error');
        }
    } catch (error) {
        console.error('发送测试通知失败:', error);
        notice('发送测试通知失败', 'error');
    }
}
</script>
{%endblock%}