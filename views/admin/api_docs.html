{% set title = "API文档管理" %}
{% set admin = true %}
{% extends "../base.html" %}

{% block content %}
<!-- 页面容器 -->
<div>
    <!-- 引入侧边栏 -->
    {% include "admin/sidebar.html" %}

    <!-- 主内容区域 -->
    <div class="flex-1 space-y-6">
        
        <!-- 页面标题卡片 - 标准iPhone风格 -->
        <div class="admin-card">
            <div class="flex items-center justify-between p-4">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/30 rounded-lg shadow-sm flex-shrink-0 border border-blue-200/50 dark:border-blue-700/30 flex items-center justify-center">
                        <i data-lucide="api" class="text-base text-blue-600 dark:text-blue-400 w-4 h-4"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">API文档管理</h1>
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">API Documentation Management</p>
                    </div>
                </div>
                <div class="flex items-center gap-2 flex-shrink-0">
                    <a href="/api-docs" target="_blank" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95">
                        <i data-lucide="external-link" class="text-sm w-4 h-4"></i>
                        <span>查看文档</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- 主内容卡片 -->
        <div class="admin-card">
            <!-- 所有内容 -->
            <div class="space-y-4 sm:space-y-6 lg:space-y-8 p-4 sm:p-6">
                <!-- 文档状态 -->
                <div class="admin-inner-card animate-fade-in">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4 sm:mb-6">
                        <h4 class="text-base sm:text-lg font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2">
                            <i data-lucide="file-text" class="text-success-500 w-5 h-5"></i>
                            <span class="truncate">文档状态</span>
                        </h4>
                        <div class="flex items-center gap-2 flex-shrink-0">
                            {% if hasSwaggerDoc %}
                            <div class="px-2 sm:px-3 py-1 bg-success-100 dark:bg-success-900/30 text-success-800 dark:text-success-200 rounded-full text-xs font-medium">
                                <i data-lucide="check-circle" class="w-4 h-4 mr-1"></i>
                                <span class="hidden sm:inline">文档已生成</span>
                                <span class="sm:hidden">已生成</span>
                            </div>
                            {% else %}
                            <div class="px-2 sm:px-3 py-1 bg-warning-100 dark:bg-warning-900/30 text-warning-800 dark:text-warning-200 rounded-full text-xs font-medium">
                                <i data-lucide="alert-triangle" class="w-4 h-4 mr-1"></i>
                                <span class="hidden sm:inline">文档未生成</span>
                                <span class="sm:hidden">未生成</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div id="doc-stats" class="text-center py-12 text-slate-600 dark:text-slate-400">
                        <div class="flex items-center justify-center gap-3">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"></div>
                            正在加载文档统计...
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="admin-inner-card animate-fade-in">
                    <h4 class="text-base sm:text-lg font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2 mb-4 sm:mb-6">
                        <i data-lucide="wrench" class="text-accent-500 w-5 h-5"></i>
                        <span class="truncate">文档管理</span>
                    </h4>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                        <button class="px-3 sm:px-4 py-3 sm:py-4 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex items-center gap-2 shadow-sm hover:shadow-md bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white justify-center min-h-[44px] touch-manipulation" onclick="regenerateDocs()">
                            <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                            <span class="truncate">重新生成</span>
                        </button>
                        <button class="px-3 sm:px-4 py-3 sm:py-4 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex items-center gap-2 shadow-sm hover:shadow-md bg-gradient-to-r from-success-500 to-success-600 hover:from-success-600 hover:to-success-700 text-white justify-center min-h-[44px] touch-manipulation" onclick="refreshStats()">
                            <i data-lucide="bar-chart-3" class="w-4 h-4"></i>
                            <span class="truncate">刷新统计</span>
                        </button>
                        <a href="/api-docs" target="_blank" class="px-3 sm:px-4 py-3 sm:py-4 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex items-center gap-2 shadow-sm hover:shadow-md bg-gradient-to-r from-accent-500 to-accent-600 hover:from-accent-600 hover:to-accent-700 text-white justify-center min-h-[44px] touch-manipulation">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                            <span class="truncate">查看文档</span>
                        </a>
                        <button class="px-3 sm:px-4 py-3 sm:py-4 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex items-center gap-2 shadow-sm hover:shadow-md bg-gradient-to-r from-warning-500 to-warning-600 hover:from-warning-600 hover:to-warning-700 text-white justify-center min-h-[44px] touch-manipulation" onclick="showApiExamples()">
                            <i data-lucide="code" class="w-4 h-4"></i>
                            <span class="truncate">API示例</span>
                        </button>
                    </div>
                </div>

                <!-- API使用指南 -->
                <div class="admin-inner-card animate-fade-in">
                    <h4 class="text-base sm:text-lg font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2 mb-4 sm:mb-6">
                        <i data-lucide="graduation-cap" class="text-warning-500 w-5 h-5"></i>
                        <span class="truncate">API使用指南</span>
                    </h4>
                    
                    <div class="space-y-4 sm:space-y-6">
                        <!-- 认证说明 -->
                        <div>
                            <h5 class="text-sm sm:text-base font-medium text-slate-700 dark:text-slate-200 mb-3">🔐 API认证</h5>
                            <div class="bg-slate-50 dark:bg-slate-800/30 p-3 sm:p-4 rounded-lg">
                                <p class="text-xs sm:text-sm text-slate-600 dark:text-slate-400 mb-3">
                                    大部分API需要使用API密钥进行认证，请在请求中添加 <code class="bg-slate-200 dark:bg-slate-700 px-1 rounded text-xs">key</code> 参数：
                                </p>
                                <div class="bg-slate-900 text-slate-100 p-3 sm:p-4 rounded-lg text-xs sm:text-sm font-mono overflow-x-auto">
curl -X GET "{{apiBaseUrl}}/api/servers?key=YOUR_API_KEY"
                                </div>
                            </div>
                        </div>

                        <!-- 常用端点 -->
                        <div>
                            <h5 class="text-sm sm:text-base font-medium text-slate-700 dark:text-slate-200 mb-3">🚀 常用API端点</h5>
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4">
                                <div class="bg-slate-50 dark:bg-slate-800/30 p-3 sm:p-4 rounded-lg">
                                    <h6 class="text-sm sm:text-base font-medium text-slate-700 dark:text-slate-200 mb-2">服务器管理</h6>
                                    <div class="space-y-2 text-xs sm:text-sm">
                                        <div class="flex items-center gap-2 flex-wrap">
                                            <span class="px-2 py-1 rounded-md text-xs font-medium bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-200 flex-shrink-0">GET</span>
                                            <code class="text-xs break-all">/api/servers</code>
                                        </div>
                                        <div class="flex items-center gap-2 flex-wrap">
                                            <span class="px-2 py-1 rounded-md text-xs font-medium bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-200 flex-shrink-0">GET</span>
                                            <code class="text-xs break-all">/api/monitor/nodes</code>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bg-slate-50 dark:bg-slate-800/30 p-3 sm:p-4 rounded-lg">
                                    <h6 class="text-sm sm:text-base font-medium text-slate-700 dark:text-slate-200 mb-2">网络测试</h6>
                                    <div class="space-y-2 text-xs sm:text-sm">
                                        <div class="flex items-center gap-2 flex-wrap">
                                            <span class="px-2 py-1 rounded-md text-xs font-medium bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-200 flex-shrink-0">GET</span>
                                            <code class="text-xs break-all">/tcping</code>
                                        </div>
                                        <div class="flex items-center gap-2 flex-wrap">
                                            <span class="px-2 py-1 rounded-md text-xs font-medium bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-200 flex-shrink-0">GET</span>
                                            <code class="text-xs break-all">/ping</code>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bg-slate-50 dark:bg-slate-800/30 p-3 sm:p-4 rounded-lg">
                                    <h6 class="text-sm sm:text-base font-medium text-slate-700 dark:text-slate-200 mb-2">监控数据</h6>
                                    <div class="space-y-2 text-xs sm:text-sm">
                                        <div class="flex items-center gap-2 flex-wrap">
                                            <span class="px-2 py-1 rounded-md text-xs font-medium bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-200 flex-shrink-0">GET</span>
                                            <code class="text-xs break-all">/api/allnode_status</code>
                                        </div>
                                        <div class="flex items-center gap-2 flex-wrap">
                                            <span class="px-2 py-1 rounded-md text-xs font-medium bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-200 flex-shrink-0">GET</span>
                                            <code class="text-xs break-all">/stats/:sid/latest</code>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bg-slate-50 dark:bg-slate-800/30 p-3 sm:p-4 rounded-lg">
                                    <h6 class="text-sm sm:text-base font-medium text-slate-700 dark:text-slate-200 mb-2">性能监控</h6>
                                    <div class="space-y-2 text-xs sm:text-sm">
                                        <div class="flex items-center gap-2 flex-wrap">
                                            <span class="px-2 py-1 rounded-md text-xs font-medium bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-200 flex-shrink-0">GET</span>
                                            <code class="text-xs break-all">/api/performance/data</code>
                                        </div>
                                        <div class="flex items-center gap-2 flex-wrap">
                                            <span class="px-2 py-1 rounded-md text-xs font-medium bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-200 flex-shrink-0">GET</span>
                                            <code class="text-xs break-all">/api/stats/:nodeId/load</code>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 响应格式 -->
                        <div>
                            <h5 class="text-sm sm:text-base font-medium text-slate-700 dark:text-slate-200 mb-3">📋 响应格式</h5>
                            <div class="bg-slate-50 dark:bg-slate-800/30 p-3 sm:p-4 rounded-lg">
                                <p class="text-xs sm:text-sm text-slate-600 dark:text-slate-400 mb-3">
                                    API响应通常采用以下格式：
                                </p>
                                <div class="bg-slate-900 text-slate-100 p-3 sm:p-4 rounded-lg text-xs sm:text-sm font-mono overflow-x-auto">
{
  "success": true,
  "data": { ... },
  "message": "操作成功"
}

// 或者管理员API格式：
{
  "status": 1,
  "data": { ... },
  "msg": "操作成功"
}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 页面加载时获取统计信息
document.addEventListener('DOMContentLoaded', function() {
    refreshStats();
    
    // 添加页面加载动画
    document.querySelectorAll('.animate-fade-in').forEach((el, index) => {
        el.style.animationDelay = `${index * 0.1}s`;
    });
});

// 刷新文档统计
async function refreshStats() {
    try {
        const response = await fetch('/admin/api/docs-stats');
        const result = await response.json();
        
        if (result.status === 1) {
            displayStats(result.data);
        } else {
            showAlert('获取统计信息失败: ' + (result.msg || '未知错误'), 'error');
        }
    } catch (error) {
        console.error('获取统计信息失败:', error);
        showAlert('获取统计信息失败: ' + error.message, 'error');
    }
}

// 显示统计信息
function displayStats(stats) {
    const container = document.getElementById('doc-stats');
    
    if (!stats.hasDocument) {
        container.innerHTML = `
            <div class="text-center py-8">
                <i data-lucide="alert-triangle" class="w-16 h-16 text-warning-400 mb-4"></i>
                <h5 class="text-lg font-medium text-slate-700 dark:text-slate-200 mb-2">API文档尚未生成</h5>
                <p class="text-sm text-slate-600 dark:text-slate-400 mb-4">
                    请点击"重新生成"按钮生成API文档
                </p>
                <button class="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 shadow-sm hover:shadow-md bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white" onclick="regenerateDocs()">
                    <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                    立即生成
                </button>
            </div>
        `;
        return;
    }

    // 格式化文件大小
    const formatSize = (bytes) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    container.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-slate-50 dark:bg-slate-800/30 p-4 rounded-lg text-center border border-slate-200 dark:border-slate-700/50">
                <div class="text-2xl font-bold text-primary-600 mb-1">${stats.totalEndpoints}</div>
                <div class="text-sm text-slate-600 dark:text-slate-400">API端点总数</div>
            </div>
            <div class="bg-slate-50 dark:bg-slate-800/30 p-4 rounded-lg text-center border border-slate-200 dark:border-slate-700/50">
                <div class="text-2xl font-bold text-success-600 mb-1">${formatSize(stats.documentSize)}</div>
                <div class="text-sm text-slate-600 dark:text-slate-400">文档大小</div>
            </div>
            <div class="bg-slate-50 dark:bg-slate-800/30 p-4 rounded-lg text-center border border-slate-200 dark:border-slate-700/50">
                <div class="text-2xl font-bold text-accent-600 mb-1">${Object.keys(stats.endpointsByTag).length}</div>
                <div class="text-sm text-slate-600 dark:text-slate-400">API分类</div>
            </div>
            <div class="bg-slate-50 dark:bg-slate-800/30 p-4 rounded-lg text-center border border-slate-200 dark:border-slate-700/50">
                <div class="text-2xl font-bold text-warning-600 mb-1">${new Date(stats.lastModified).toLocaleDateString()}</div>
                <div class="text-sm text-slate-600 dark:text-slate-400">最后更新</div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h6 class="font-medium text-slate-700 dark:text-slate-200 mb-3">HTTP方法分布</h6>
                <div class="space-y-2">
                    ${Object.entries(stats.endpointsByMethod).map(([method, count]) => `
                        <div class="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800/30 rounded">
                            <span class="px-2 py-1 rounded-md text-xs font-medium bg-${method.toLowerCase() === 'get' ? 'success' : method.toLowerCase() === 'post' ? 'primary' : method.toLowerCase() === 'put' ? 'warning' : method.toLowerCase() === 'delete' ? 'error' : 'accent'}-100 text-${method.toLowerCase() === 'get' ? 'success' : method.toLowerCase() === 'post' ? 'primary' : method.toLowerCase() === 'put' ? 'warning' : method.toLowerCase() === 'delete' ? 'error' : 'accent'}-800 dark:bg-${method.toLowerCase() === 'get' ? 'success' : method.toLowerCase() === 'post' ? 'primary' : method.toLowerCase() === 'put' ? 'warning' : method.toLowerCase() === 'delete' ? 'error' : 'accent'}-900/30 dark:text-${method.toLowerCase() === 'get' ? 'success' : method.toLowerCase() === 'post' ? 'primary' : method.toLowerCase() === 'put' ? 'warning' : method.toLowerCase() === 'delete' ? 'error' : 'accent'}-200">${method.toUpperCase()}</span>
                            <span class="text-sm font-medium">${count} 个端点</span>
                        </div>
                    `).join('')}
                </div>
            </div>
            
            <div>
                <h6 class="font-medium text-slate-700 dark:text-slate-200 mb-3">API分类分布</h6>
                <div class="space-y-2 max-h-48 overflow-y-auto">
                    ${Object.entries(stats.endpointsByTag).map(([tag, count]) => `
                        <div class="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800/30 rounded">
                            <span class="text-sm">${tag}</span>
                            <span class="text-sm font-medium text-primary-600">${count}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
}

// 重新生成文档
async function regenerateDocs() {
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    try {
        button.innerHTML = `
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            生成中...
        `;
        button.disabled = true;

        const response = await fetch('/admin/api/regenerate-docs', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.status === 1) {
            showAlert('API文档重新生成成功！', 'success');
            refreshStats();
        } else {
            showAlert('文档生成失败: ' + (result.msg || result.error || '未知错误'), 'error');
        }
    } catch (error) {
        console.error('重新生成文档失败:', error);
        showAlert('重新生成文档失败: ' + error.message, 'error');
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// 显示API示例
function showApiExamples() {
    const examples = `
# 获取所有服务器
curl -X GET "{{apiBaseUrl}}/api/servers?key=YOUR_API_KEY"

# 执行TCPing测试
curl -X GET "{{apiBaseUrl}}/tcping?key=YOUR_API_KEY&host=google.com&port=80&count=4"

# 获取节点状态
curl -X GET "{{apiBaseUrl}}/api/allnode_status"

# 获取性能数据
curl -X GET "{{apiBaseUrl}}/api/performance/data?key=YOUR_API_KEY"
    `;

    showAlert(`
        <div>
            <h5 class="font-medium mb-3">API调用示例</h5>
            <div class="bg-slate-900 text-slate-100 p-4 rounded-lg text-sm font-mono overflow-x-auto">${examples.trim()}</div>
            <p class="text-xs mt-3 opacity-75">请将 YOUR_API_KEY 替换为实际的API密钥</p>
        </div>
    `, 'info');
}

// 显示提示信息
function showAlert(message, type) {
    // 移除现有的提示
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    // 创建新的提示
    const alert = document.createElement('div');
    const typeClasses = {
        success: 'bg-success-50 dark:bg-success-900/20 text-success-800 dark:text-success-200 border-success-400',
        error: 'bg-error-50 dark:bg-error-900/20 text-error-800 dark:text-error-200 border-error-400',
        info: 'bg-primary-50 dark:bg-primary-900/20 text-primary-800 dark:text-primary-200 border-primary-400'
    };
    
    alert.className = `px-6 py-4 rounded-xl mb-6 border-l-4 shadow-sm animate-fade-in ${typeClasses[type]}`;
    
    const icon = type === 'success' ? 'check-circle' : 
                 type === 'error' ? 'alert-circle' : 'info';
    
    alert.innerHTML = `
        <div class="flex items-start gap-3">
            <i data-lucide="${icon}" class="w-4 h-4"></i>
            <div class="flex-1">${message}</div>
        </div>
    `;
    
    // 插入到页面顶部
    const container = document.querySelector('.container');
    container.insertBefore(alert, container.firstChild);
    
    // 5秒后自动移除
    setTimeout(() => {
        if (alert.parentNode) {
            alert.style.opacity = '0';
            alert.style.transform = 'translateY(-10px)';
            setTimeout(() => alert.remove(), 300);
        }
    }, 5000);
}
</script>
{% endblock %} 