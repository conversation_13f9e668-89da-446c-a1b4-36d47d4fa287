{% set title = "日志管理" %}
{% set admin = true %}
{% extends "../base.html" %}

{% block content %}
<!-- 页面容器 -->
<div class="container mx-auto px-3 sm:px-4 flex flex-col lg:flex-row gap-4 lg:gap-6 justify-center" style="padding-top: calc(1rem + env(safe-area-inset-top));">
    <!-- 引入侧边栏 -->
    {% include "admin/sidebar.html" %}

    <!-- 主内容区域 -->
    <div class="flex-1 admin-card ">
        <!-- 页面标题 -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 sm:p-6 border-b border-slate-200 dark:border-slate-700">
            <div class="flex items-center gap-3 sm:gap-4">
                <div class="flex-shrink-0">
                    <i class="ti ti-file-text text-xl sm:text-2xl text-primary-500"></i>
                </div>
                <div class="min-w-0">
                    <h3 class="text-base sm:text-lg lg:text-xl font-bold text-slate-800 dark:text-white truncate">日志管理</h3>
                    <p class="text-xs sm:text-sm lg:text-base text-slate-500 dark:text-slate-400 truncate">Log Management System</p>
                </div>
            </div>
            <div class="flex items-center gap-2 flex-shrink-0">
                <div class="px-2 sm:px-3 py-1 bg-success-100 dark:bg-success-900/30 text-success-800 dark:text-success-200 rounded-full text-xs font-medium">
                    <i class="ti ti-circle-check text-xs sm:text-sm mr-1"></i>
                    <span class="hidden sm:inline">系统正常</span>
                    <span class="sm:hidden">正常</span>
                </div>
            </div>
        </div>

        <!-- 所有内容 -->
        <div class="space-y-4 sm:space-y-6 lg:space-y-8 p-4 sm:p-6">
            <!-- 统计信息 -->
            <div class="admin-card space-y-4 sm:space-y-6 animate-fade-in">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
                    <h4 class="text-base sm:text-lg font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2">
                        <i class="ti ti-chart-line text-accent-500 text-lg sm:text-xl"></i>
                        <span class="truncate">日志统计概览</span>
                    </h4>
                    <button class="px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex items-center justify-center gap-2 shadow-sm hover:shadow-md bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white min-w-0 flex-shrink-0" onclick="refreshStats()">
                        <i class="ti ti-refresh text-sm"></i>
                        <span class="hidden sm:inline">刷新数据</span>
                        <span class="sm:hidden">刷新</span>
                    </button>
                </div>
                <div id="stats-container" class="text-center py-12 text-slate-600 dark:text-slate-400">
                    <div class="flex items-center justify-center gap-3">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"></div>
                        正在加载统计信息...
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="admin-card space-y-4 sm:space-y-6 animate-fade-in">
                <h4 class="text-base sm:text-lg font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2">
                    <i class="ti ti-tool text-primary-500 text-lg sm:text-xl"></i>
                    <span class="truncate">快速操作</span>
                </h4>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                    <button class="px-3 sm:px-4 py-3 sm:py-4 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex items-center gap-2 shadow-sm hover:shadow-md bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white justify-center min-h-[44px] touch-manipulation" onclick="refreshStats()">
                        <i class="ti ti-refresh text-sm sm:text-base"></i>
                        <span class="truncate">刷新统计</span>
                    </button>
                    <button class="px-3 sm:px-4 py-3 sm:py-4 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex items-center gap-2 shadow-sm hover:shadow-md bg-gradient-to-r from-warning-500 to-warning-600 hover:from-warning-600 hover:to-warning-700 text-white justify-center min-h-[44px] touch-manipulation" onclick="checkAllLogs()">
                        <i class="ti ti-search text-sm sm:text-base"></i>
                        <span class="truncate">检查日志</span>
                    </button>
                    <button class="px-3 sm:px-4 py-3 sm:py-4 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex items-center gap-2 shadow-sm hover:shadow-md bg-gradient-to-r from-error-500 to-error-600 hover:from-error-600 hover:to-error-700 text-white justify-center min-h-[44px] touch-manipulation sm:col-span-2 lg:col-span-1" onclick="cleanAllLogs()">
                        <i class="ti ti-trash text-sm sm:text-base"></i>
                        <span class="truncate">清理日志</span>
                    </button>
                </div>
            </div>

            <!-- 日志文件列表 -->
            <div class="admin-card space-y-6 animate-fade-in">
                <div class="flex items-center justify-between">
                    <h4 class="text-lg font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2">
                        <i class="ti ti-folder text-success-500"></i>
                        日志文件管理
                    </h4>
                    <div class="text-sm text-slate-500 dark:text-slate-400">
                        点击轮转按钮可以归档大文件
                    </div>
                </div>
                <div id="files-container" class="text-center py-12 text-slate-600 dark:text-slate-400">
                    <div class="flex items-center justify-center gap-3">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-success-500"></div>
                        正在加载文件列表...
                    </div>
                </div>
            </div>

            <!-- 日志查看器 -->
            <div class="admin-card space-y-4 sm:space-y-6 animate-fade-in">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <h4 class="text-base sm:text-lg font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2">
                        <i class="ti ti-eye text-warning-500 text-lg sm:text-xl"></i>
                        <span class="truncate">实时日志查看器</span>
                    </h4>
                    <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 min-w-0">
                        <select id="logTypeSelect" class="bg-white dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700 rounded-lg px-3 py-2 text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500 transition-all duration-200 min-w-0 flex-1 sm:min-w-[120px] sm:flex-initial">
                            <option value="notification">📢 通知日志</option>
                            <option value="performance">⚡ 性能日志</option>
                            <option value="system">🔧 系统日志</option>
                            <option value="error">❌ 错误日志</option>
                        </select>
                        <select id="monthSelect" class="bg-white dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700 rounded-lg px-3 py-2 text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500 transition-all duration-200 min-w-0 flex-1 sm:min-w-[120px] sm:flex-initial">
                            <!-- 动态填充 -->
                        </select>
                        <button class="px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex items-center justify-center gap-2 shadow-sm hover:shadow-md bg-gradient-to-r from-success-500 to-success-600 hover:from-success-600 hover:to-success-700 text-white min-h-[44px] touch-manipulation flex-shrink-0" onclick="loadLogContent()">
                            <i class="ti ti-eye text-sm"></i>
                            <span class="hidden sm:inline">查看日志</span>
                            <span class="sm:hidden">查看</span>
                        </button>
                    </div>
                </div>
                
                <div id="log-content" class="bg-slate-50 dark:bg-slate-900/50 rounded-xl p-4 max-h-96 overflow-y-auto border border-slate-200 dark:border-slate-700/50">
                    <div class="text-center py-12 text-slate-500 dark:text-slate-400">
                        <i class="ti ti-file-text text-6xl text-slate-300 dark:text-slate-600 mb-4"></i>
                        <p class="text-sm">选择日志类型和月份后点击查看按钮</p>
                        <p class="text-xs mt-2 text-slate-400">支持查看最近50条日志记录</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
let currentFilePath = null;

// 页面加载时获取统计信息
document.addEventListener('DOMContentLoaded', function() {
    refreshStats();
    initMonthSelect();
    
    // 添加页面加载动画
    document.querySelectorAll('.animate-fade-in').forEach((el, index) => {
        el.style.animationDelay = `${index * 0.1}s`;
    });
});

// 刷新统计信息
async function refreshStats() {
    try {
        const response = await fetch('/admin/api/log-stats');
        const result = await response.json();
        
        if (result.status === 1) {
            displayStats(result.data);
            displayFiles(result.data.files);
        } else {
            showAlert('获取统计信息失败: ' + (result.data || result.msg || '未知错误'), 'error');
        }
    } catch (error) {
        console.error('获取统计信息失败:', error);
        showAlert('获取统计信息失败: ' + error.message, 'error');
    }
}

// 显示统计信息
function displayStats(stats) {
    const container = document.getElementById('stats-container');
    container.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-slate-50 dark:bg-slate-800/30 p-4 rounded-lg text-center border border-slate-200 dark:border-slate-700/50">
                <div class="text-2xl font-bold text-primary-600 mb-1">${stats.totalFiles}</div>
                <div class="text-sm text-slate-600 dark:text-slate-400">日志文件数量</div>
            </div>
            <div class="bg-slate-50 dark:bg-slate-800/30 p-4 rounded-lg text-center border border-slate-200 dark:border-slate-700/50">
                <div class="text-2xl font-bold text-success-600 mb-1">${stats.totalSizeFormatted}</div>
                <div class="text-sm text-slate-600 dark:text-slate-400">总占用空间</div>
            </div>
            <div class="bg-slate-50 dark:bg-slate-800/30 p-4 rounded-lg text-center border border-slate-200 dark:border-slate-700/50">
                <div class="text-2xl font-bold text-error-600 mb-1">${stats.files.filter(f => f.size > 10*1024*1024).length}</div>
                <div class="text-sm text-slate-600 dark:text-slate-400">超大文件 (>10MB)</div>
            </div>
            <div class="bg-slate-50 dark:bg-slate-800/30 p-4 rounded-lg text-center border border-slate-200 dark:border-slate-700/50">
                <div class="text-2xl font-bold text-warning-600 mb-1">${stats.files.filter(f => f.size > 5*1024*1024 && f.size <= 10*1024*1024).length}</div>
                <div class="text-sm text-slate-600 dark:text-slate-400">大文件 (5-10MB)</div>
            </div>
        </div>
    `;
}

// 显示文件列表
function displayFiles(files) {
    const container = document.getElementById('files-container');
    
    if (files.length === 0) {
        container.innerHTML = `
            <div class="text-center py-12 text-slate-500 dark:text-slate-400">
                <i class="ti ti-folder-open text-6xl text-slate-300 dark:text-slate-600 mb-4"></i>
                <p>没有找到日志文件</p>
                <p class="text-xs mt-2">系统将自动创建日志文件</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    files.forEach(file => {
        const sizeClass = file.size > 10*1024*1024 ? 'text-error-600 dark:text-error-400 font-semibold px-2 py-1 bg-error-100 dark:bg-error-900/30 rounded-md' : 
                         file.size > 5*1024*1024 ? 'text-warning-600 dark:text-warning-400 font-semibold px-2 py-1 bg-warning-100 dark:bg-warning-900/30 rounded-md' : '';
        
        const fileIcon = getFileIcon(file.name);
        const fileType = getFileType(file.name);
        
        html += `
            <div class="flex justify-between items-center p-4 border border-slate-200 dark:border-slate-700 rounded-xl mb-3 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800/50 dark:to-slate-800/30 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                <div class="flex-1">
                    <div class="font-semibold text-slate-800 dark:text-slate-200 mb-2 text-sm flex items-center gap-2">
                        <i class="ti ${fileIcon} w-4 h-4 text-slate-500"></i>
                        ${file.name}
                        <span class="px-2 py-1 bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-300 rounded-md text-xs">${fileType}</span>
                    </div>
                    <div class="text-xs text-slate-600 dark:text-slate-400 flex items-center gap-4">
                        <span class="flex items-center gap-1">
                            <i class="ti ti-database text-xs"></i>
                            大小: <span class="${sizeClass}">${file.sizeFormatted}</span>
                        </span>
                        <span class="flex items-center gap-1">
                            <i class="ti ti-clock text-xs"></i>
                            修改: ${new Date(file.modified).toLocaleString()}
                        </span>
                    </div>
                </div>
                <div class="flex gap-2">
                    <button class="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 shadow-sm hover:shadow-md bg-gradient-to-r from-warning-500 to-warning-600 hover:from-warning-600 hover:to-warning-700 text-white" onclick="rotateLog('${file.path}', '${file.name}')" title="轮转此文件">
                        <i class="ti ti-rotate-clockwise text-sm"></i>
                        轮转
                    </button>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// 获取文件图标
function getFileIcon(filename) {
    if (filename.includes('performance')) return 'ti-gauge';
    if (filename.includes('notification')) return 'ti-bell';
    if (filename.includes('system')) return 'ti-settings';
    if (filename.includes('error')) return 'ti-alert-circle';
    return 'ti-file-text';
}

// 获取文件类型
function getFileType(filename) {
    if (filename.includes('performance')) return '性能';
    if (filename.includes('notification')) return '通知';
    if (filename.includes('system')) return '系统';
    if (filename.includes('error')) return '错误';
    return '日志';
}

// 轮转单个日志文件
function rotateLog(filePath, fileName) {
    showConfirm(
        '轮转日志文件',
        `确定要轮转文件 "${fileName}" 吗？\n\n当前文件将被重命名为 .1，并创建新的空文件。此操作不可撤销。`,
        () => executeRotateLog(filePath)
    );
}

// 执行轮转操作
async function executeRotateLog(filePath) {
    try {
        const response = await fetch('/admin/api/rotate-log', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ filePath })
        });
        
        const result = await response.json();
        
        if (result.status === 1) {
            showAlert('日志文件轮转成功！文件已归档。', 'success');
            refreshStats();
        } else {
            showAlert('轮转失败: ' + (result.data || result.msg || '未知错误'), 'error');
        }
    } catch (error) {
        console.error('轮转失败:', error);
        showAlert('轮转失败: ' + error.message, 'error');
    }
}

// 检查所有日志
function checkAllLogs() {
    showAlert('正在检查所有日志文件...', 'info');
    refreshStats();
}

// 清理所有日志
function cleanAllLogs() {
    const confirmMessage = '⚠️ 警告：此操作将清空所有日志文件的内容（不会删除文件）。\n\n此操作不可撤销，请确认您真的要执行此操作！';

    if (confirm(confirmMessage)) {
        executeCleanAllLogs();
    }
}

// 执行清理操作
async function executeCleanAllLogs() {
    try {
        const response = await fetch('/admin/api/clean-logs', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.status === 1) {
            showAlert('所有日志文件清理成功！', 'success');
            refreshStats();
        } else {
            showAlert('清理失败: ' + (result.data || result.msg || '未知错误'), 'error');
        }
    } catch (error) {
        console.error('清理失败:', error);
        showAlert('清理失败: ' + error.message, 'error');
    }
}



// 初始化月份选择器
function initMonthSelect() {
    const select = document.getElementById('monthSelect');
    const currentDate = new Date();
    
    // 生成最近12个月的选项
    for (let i = 0; i < 12; i++) {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
        const value = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
        const text = `${date.getFullYear()}年${date.getMonth() + 1}月`;
        
        const option = document.createElement('option');
        option.value = value;
        option.textContent = text;
        select.appendChild(option);
    }
}

// 加载日志内容
async function loadLogContent() {
    const logType = document.getElementById('logTypeSelect').value;
    const month = document.getElementById('monthSelect').value;
    const contentDiv = document.getElementById('log-content');
    
    try {
        contentDiv.innerHTML = `
            <div class="flex items-center justify-center gap-3 py-8">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-warning-500"></div>
                正在加载 ${logType} 日志...
            </div>
        `;
        
        let url;
        if (logType === 'notification') {
            url = `/admin/notification-logs?month=${month}&type=all`;
        } else {
            url = `/admin/api/log-content?type=${logType}&month=${month}`;
        }
        
        const response = await fetch(url);
        const logs = await response.json();
        
        if (logs.length === 0) {
            contentDiv.innerHTML = `
                <div class="text-center py-12 text-slate-500 dark:text-slate-400">
                    <i class="ti ti-inbox text-6xl text-slate-300 dark:text-slate-600 mb-4"></i>
                    <p>该月份暂无 ${getLogTypeName(logType)} 记录</p>
                    <p class="text-xs mt-2 text-slate-400">请选择其他月份或日志类型</p>
                </div>
            `;
            return;
        }
        
        // 渲染日志内容
        let html = '<div class="space-y-3">';
        logs.slice(0, 50).forEach(log => {
            const timestamp = new Date(log.timestamp).toLocaleString();
            
            // 根据日志类型确定状态和消息
            let status, message, statusClass;
            
            if (log.status) {
                // 通知日志格式
                status = log.status;
                message = log.message || log.error || JSON.stringify(log);
                statusClass = status === 'success' ? 'bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-200' : 
                             status === 'error' ? 'bg-error-100 text-error-800 dark:bg-error-900/30 dark:text-error-200' : 
                             'bg-primary-100 text-primary-800 dark:bg-primary-900/30 dark:text-primary-200';
            } else {
                // 系统日志和性能日志格式
                status = log.type || 'info';
                message = log.message || JSON.stringify(log);
                statusClass = log.type === 'performance' ? 'bg-accent-100 text-accent-800 dark:bg-accent-900/30 dark:text-accent-200' :
                             log.type === 'system' ? 'bg-primary-100 text-primary-800 dark:bg-primary-900/30 dark:text-primary-200' :
                             log.type === 'error' ? 'bg-error-100 text-error-800 dark:bg-error-900/30 dark:text-error-200' : 
                             'bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-200';
            }
            
            html += `
                                    <div class="text-xs border-b border-slate-200 dark:border-slate-700/50 pb-3 mb-3 last:border-b-0 last:mb-0">
                        <div class="flex justify-between items-start mb-2">
                            <span class="text-slate-500 dark:text-slate-400 font-mono break-all">${timestamp}</span>
                            <span class="font-medium px-2 py-1 rounded-md text-xs ${statusClass} flex-shrink-0">${status}</span>
                        </div>
                        <div class="text-slate-700 dark:text-slate-300 leading-relaxed break-all">${message}</div>
                    </div>
            `;
        });
        html += '</div>';
        
        if (logs.length > 50) {
            html += `
                <div class="text-center mt-4 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg">
                    <p class="text-primary-600 dark:text-primary-400 text-sm">
                        <i class="ti ti-info-circle text-sm mr-1"></i>
                        显示最新50条记录，共${logs.length}条
                    </p>
                </div>
            `;
        }
        
        contentDiv.innerHTML = html;
    } catch (error) {
        console.error('加载日志失败:', error);
        contentDiv.innerHTML = `
            <div class="text-center py-12 text-slate-500 dark:text-slate-400">
                <i class="ti ti-alert-circle text-6xl text-error-400 mb-4"></i>
                <p class="text-error-600 dark:text-error-400">加载日志失败</p>
                <p class="text-xs mt-2">${error.message}</p>
            </div>
        `;
    }
}

// 获取日志类型名称
function getLogTypeName(type) {
    const names = {
        'notification': '通知日志',
        'performance': '性能日志',
        'system': '系统日志',
        'error': '错误日志'
    };
    return names[type] || '日志';
}

// 使用全局通知系统
function showAlert(message, type = 'info') {
    // 使用全局的 notice 函数
    if (typeof notice === 'function') {
        notice(message, type);
    } else {
        // 降级到控制台输出
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}
</script>
{% endblock %} 