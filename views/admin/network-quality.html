{%set admin=true%}
{%extends "../base.html"%}

{%block head%}
<!-- ECharts -->
<script src="/js/libs/echarts.min.js"></script>
<!-- Network Quality Styles -->
<link rel="stylesheet" href="/css/components/network-quality.css">
{%endblock%}

{%block content%}
<!-- 页面容器 -->
<div>
    <!-- 引入侧边栏 -->
    {% include "admin/sidebar.html" %}

    <!-- 主内容区域 -->
    <div class="flex-1 space-y-6">
        <!-- 页面标题卡片 -->
        <div class="bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl backdrop-blur-sm shadow-sm">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 p-4">
                <div class="flex items-center gap-3">
                    <div class="w-9 h-9 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/30 rounded-lg shadow-sm flex-shrink-0 border border-blue-200/50 dark:border-blue-700/30 flex items-center justify-center">
                        <i class="ti ti-router text-base text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div>
                        <h1 class="text-xl sm:text-lg font-bold text-slate-800 dark:text-slate-200 tracking-wide leading-tight">网络质量监控</h1>
                        <p class="text-sm sm:text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">按节点查看网络延迟、丢包率和可用性监控数据</p>
                    </div>
                </div>
                
                <!-- 时间范围选择器 -->
                <div class="flex items-center gap-2">
                    <label class="text-sm font-medium text-slate-700 dark:text-slate-300">时间范围:</label>
                    <select id="time-range-select" class="px-3 py-2 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-lg text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 transition-all duration-200">
                        <option value="1h">最近1小时</option>
                        <option value="6h">最近6小时</option>
                        <option value="24h" selected>最近24小时</option>
                        <option value="7d">最近7天</option>
                        <option value="30d">最近30天</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 概览统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/20 rounded-lg p-4 border border-green-200/50 dark:border-green-700/30">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-green-500/20 dark:bg-green-400/20 rounded-lg flex items-center justify-center">
                        <i class="ti ti-shield-check text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="flex-1">
                        <div class="text-xs text-green-700 dark:text-green-300 font-medium mb-1">整体健康度</div>
                        <div class="text-lg font-bold text-green-900 dark:text-green-100" id="overall-health">--</div>
                        <div class="text-xs text-green-600 dark:text-green-400">平均可用性</div>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/20 rounded-lg p-4 border border-blue-200/50 dark:border-blue-700/30">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-blue-500/20 dark:bg-blue-400/20 rounded-lg flex items-center justify-center">
                        <i class="ti ti-server text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="flex-1">
                        <div class="text-xs text-blue-700 dark:text-blue-300 font-medium mb-1">监控节点</div>
                        <div class="text-lg font-bold text-blue-900 dark:text-blue-100" id="total-nodes">--</div>
                        <div class="text-xs text-blue-600 dark:text-blue-400" id="active-nodes">--</div>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/20 rounded-lg p-4 border border-purple-200/50 dark:border-purple-700/30">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-purple-500/20 dark:bg-purple-400/20 rounded-lg flex items-center justify-center">
                        <i class="ti ti-gauge text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="flex-1">
                        <div class="text-xs text-purple-700 dark:text-purple-300 font-medium mb-1">平均延迟</div>
                        <div class="text-lg font-bold text-purple-900 dark:text-purple-100" id="avg-latency">--</div>
                        <div class="text-xs text-purple-600 dark:text-purple-400">毫秒</div>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/30 dark:to-amber-800/20 rounded-lg p-4 border border-amber-200/50 dark:border-amber-700/30">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-amber-500/20 dark:bg-amber-400/20 rounded-lg flex items-center justify-center">
                        <i class="ti ti-target text-amber-600 dark:text-amber-400"></i>
                    </div>
                    <div class="flex-1">
                        <div class="text-xs text-amber-700 dark:text-amber-300 font-medium mb-1">监控目标</div>
                        <div class="text-lg font-bold text-amber-900 dark:text-amber-100" id="total-targets">--</div>
                        <div class="text-xs text-amber-600 dark:text-amber-400">总数</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div id="loading-indicator" class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="ml-2 text-slate-600 dark:text-slate-400">正在加载监控数据...</span>
        </div>

        <!-- 错误提示 -->
        <div id="error-message" class="hidden bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div class="flex items-center gap-2">
                <i class="ti ti-alert-circle text-red-600 dark:text-red-400"></i>
                <span class="text-red-800 dark:text-red-200 font-medium">加载失败</span>
            </div>
            <p class="text-red-700 dark:text-red-300 mt-1" id="error-text"></p>
            <button id="retry-btn" class="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                重试
            </button>
        </div>

        <!-- 节点监控数据容器 -->
        <div id="nodes-container" class="space-y-6">
            <!-- 节点卡片将动态生成到这里 -->
        </div>

        <!-- 空状态提示 -->
        <div id="empty-state" class="hidden text-center py-12">
            <div class="w-16 h-16 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="ti ti-router text-2xl text-slate-400"></i>
            </div>
            <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">暂无监控数据</h3>
            <p class="text-slate-600 dark:text-slate-400 mb-6 max-w-md mx-auto">
                当前没有配置网络质量监控的节点。请先在监控目标管理中配置监控节点。
            </p>
            <a href="/admin/monitor-crud" class="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="ti ti-settings text-sm"></i>
                配置监控目标
            </a>
        </div>
    </div>
</div>

<!-- 节点卡片模板 -->
<template id="node-card-template">
    <div class="node-card bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl backdrop-blur-sm shadow-sm">
        <!-- 节点标题 -->
        <div class="node-header p-4 border-b border-slate-200/50 dark:border-slate-700/50">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="node-status-indicator w-3 h-3 rounded-full"></div>
                    <div>
                        <h3 class="node-name text-lg font-semibold text-slate-900 dark:text-slate-100"></h3>
                        <p class="node-id text-sm text-slate-500 dark:text-slate-400"></p>
                    </div>
                </div>
                <div class="flex items-center gap-4 text-sm">
                    <div class="text-center">
                        <div class="node-avg-latency text-lg font-bold text-slate-900 dark:text-slate-100"></div>
                        <div class="text-slate-500 dark:text-slate-400">平均延迟</div>
                    </div>
                    <div class="text-center">
                        <div class="node-health-score text-lg font-bold text-slate-900 dark:text-slate-100"></div>
                        <div class="text-slate-500 dark:text-slate-400">健康度</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 监控目标列表 -->
        <div class="targets-container p-4">
            <div class="targets-grid grid gap-4">
                <!-- 目标卡片将动态添加到这里 -->
            </div>
        </div>
    </div>
</template>

<!-- 目标卡片模板 -->
<template id="target-card-template">
    <div class="target-card bg-slate-50/80 dark:bg-slate-900/40 rounded-lg p-4 border border-slate-200/50 dark:border-slate-700/50">
        <div class="flex items-center justify-between mb-3">
            <div>
                <h4 class="target-name font-medium text-slate-900 dark:text-slate-100"></h4>
                <p class="target-endpoint text-sm text-slate-600 dark:text-slate-400"></p>
            </div>
            <div class="target-status-badge px-2 py-1 rounded-full text-xs font-medium"></div>
        </div>
        
        <!-- 指标显示 -->
        <div class="grid grid-cols-3 gap-3 mb-4">
            <div class="text-center">
                <div class="target-latency text-lg font-bold text-slate-900 dark:text-slate-100"></div>
                <div class="text-xs text-slate-500 dark:text-slate-400">延迟(ms)</div>
            </div>
            <div class="text-center">
                <div class="target-availability text-lg font-bold text-slate-900 dark:text-slate-100"></div>
                <div class="text-xs text-slate-500 dark:text-slate-400">可用性(%)</div>
            </div>
            <div class="text-center">
                <div class="target-packet-loss text-lg font-bold text-slate-900 dark:text-slate-100"></div>
                <div class="text-xs text-slate-500 dark:text-slate-400">丢包率(%)</div>
            </div>
        </div>
        
        <!-- 图表容器 -->
        <div class="target-chart" style="height: 200px;"></div>
    </div>
</template>

<script src="/js/network-quality.js"></script>
{%endblock%}