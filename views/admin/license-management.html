{% set title = "授权管理" %}
{%set admin = true%}
{% extends "../base.html" %}

{%block head%}
<!-- 授权管理页面样式 -->
<style>
/* 使用与其他 admin 页面一致的样式 */
.license-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.license-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .license-input {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(51, 65, 85, 0.6);
    color: #e2e8f0;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    transform: translateY(-1px);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: #64748b;
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.btn-secondary:hover {
    background: rgba(248, 250, 252, 0.95);
    color: #475569;
}

.dark .btn-secondary {
    background: rgba(30, 41, 59, 0.9);
    color: #94a3b8;
    border-color: rgba(51, 65, 85, 0.8);
}

.dark .btn-secondary:hover {
    background: rgba(51, 65, 85, 0.95);
    color: #e2e8f0;
}

.status-display {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.status-active {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    color: #059669;
}

.status-inactive {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #dc2626;
}

.status-none {
    background: rgba(107, 114, 128, 0.1);
    border: 1px solid rgba(107, 114, 128, 0.3);
    color: #6b7280;
}

.external-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
}

.external-link:hover {
    text-decoration: underline;
}

.actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

/* 连接状态指示器样式 */
.status-online {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-online #statusDot {
    background: #10b981;
    animation: pulse 2s infinite;
}

.status-cached {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-cached #statusDot {
    background: #f59e0b;
}

.status-limited {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-limited #statusDot {
    background: #ef4444;
}

.status-loading {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.3);
}

.status-loading #statusDot {
    background: #6b7280;
    animation: pulse 1s infinite;
}

.status-disconnected {
    background: rgba(148, 163, 184, 0.1);
    color: #64748b;
    border: 1px solid rgba(148, 163, 184, 0.3);
}

.status-disconnected #statusDot {
    background: #94a3b8;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 缓存模式下的特殊样式 */
.cache-info-badge {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    margin-left: 8px;
}

.emergency-warning {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #dc2626;
    padding: 12px;
    border-radius: 8px;
    margin-top: 12px;
    font-size: 14px;
}

/* 许可证密钥显示样式 */
.license-key-display {
    background: rgba(99, 102, 241, 0.05);
    border: 1px solid rgba(99, 102, 241, 0.2);
    transition: all 0.3s ease;
}

.license-key-display:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.3);
}

.dark .license-key-display {
    background: rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.3);
}

.dark .license-key-display:hover {
    background: rgba(99, 102, 241, 0.15);
    border-color: rgba(99, 102, 241, 0.4);
}

/* 复制按钮样式 */
.copy-btn {
    opacity: 0.7;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    opacity: 1;
    transform: translateY(-1px);
}

@media (max-width: 640px) {
    .license-container {
        padding: 1rem;
    }

    .actions {
        flex-direction: column;
    }

    .btn {
        justify-content: center;
    }

    #connectionStatus {
        font-size: 10px;
        padding: 2px 8px;
    }

    /* 移动端许可证密钥显示优化 */
    .license-key-display {
        font-size: 10px;
        padding: 8px;
    }

    .copy-btn {
        padding: 8px 12px;
        font-size: 10px;
    }
}
</style>
{%endblock%}

{%block content%}
<!-- 授权管理界面 -->
<div>
    <!-- 侧边栏 -->
    {% include "admin/sidebar.html" %}
    
    <!-- 主内容区域 -->
    <div class="flex-1 space-y-6">
        <!-- 页面标题 -->
        <div class="admin-card">
            <div class="admin-card-header">
                <div class="admin-page-header">
                    <div class="admin-page-title">
                        <div class="admin-title-icon admin-icon-purple">
                            <i class="ti ti-shield-check text-lg text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <div class="admin-title-text">
                            <h1>授权管理</h1>
                            <p class="text-xs">DStatus License Management</p>
                        </div>
                    </div>
                    <!-- 右侧操作区域 -->
                    <div class="flex items-center gap-3">
                        <div id="connectionStatus" class="flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium status-loading">
                            <div id="statusDot" class="w-2 h-2 rounded-full"></div>
                            <span id="statusText">检查中...</span>
                        </div>
                        <button id="refreshBtn" class="btn btn-secondary">
                            <i class="ti ti-refresh text-sm"></i>
                            刷新状态
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 当前授权状态 -->
        <div class="admin-card">
        <h2 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-4">当前授权状态</h2>
        
        <div id="licenseStatus" class="status-display status-none">
            <div class="flex items-center gap-2">
                <i class="ti ti-info-circle text-sm"></i>
                <span>暂无有效许可证</span>
            </div>
        </div>

        <div id="licenseDetails" class="hidden">
            <div class="bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 p-6 rounded-xl border border-slate-200/60 dark:border-slate-700/40 mb-6">
                <h3 class="text-base font-semibold text-slate-700 dark:text-slate-300 mb-4 flex items-center gap-2">
                    <i class="ti ti-shield-check text-lg text-blue-600 dark:text-blue-400"></i>
                    许可证详情
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="flex items-center gap-3 p-4 bg-white/60 dark:bg-slate-800/60 rounded-lg border border-slate-200/40 dark:border-slate-700/40">
                        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-800 rounded-lg flex items-center justify-center">
                            <i class="ti ti-crown text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div>
                            <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">套餐类型</div>
                            <div id="planName" class="text-sm font-semibold text-slate-700 dark:text-slate-300">-</div>
                        </div>
                    </div>

                    <div class="flex items-center gap-3 p-4 bg-white/60 dark:bg-slate-800/60 rounded-lg border border-slate-200/40 dark:border-slate-700/40">
                        <div class="w-10 h-10 bg-green-100 dark:bg-green-800 rounded-lg flex items-center justify-center">
                            <i class="ti ti-clock text-green-600 dark:text-green-400"></i>
                        </div>
                        <div>
                            <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">到期时间</div>
                            <div id="expiryDate" class="text-sm font-semibold text-slate-700 dark:text-slate-300">-</div>
                        </div>
                    </div>

                    <div class="flex items-center gap-3 p-4 bg-white/60 dark:bg-slate-800/60 rounded-lg border border-slate-200/40 dark:border-slate-700/40">
                        <div class="w-10 h-10 bg-purple-100 dark:bg-purple-800 rounded-lg flex items-center justify-center">
                            <i class="ti ti-server text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <div>
                            <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">节点限制</div>
                            <div id="nodeLimit" class="text-sm font-semibold text-slate-700 dark:text-slate-300">-</div>
                        </div>
                    </div>

                    <div class="flex items-center gap-3 p-4 bg-white/60 dark:bg-slate-800/60 rounded-lg border border-slate-200/40 dark:border-slate-700/40">
                        <div class="w-10 h-10 bg-orange-100 dark:bg-orange-800 rounded-lg flex items-center justify-center">
                            <i class="ti ti-fingerprint text-orange-600 dark:text-orange-400"></i>
                        </div>
                        <div class="flex-1">
                            <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">实例ID</div>
                            <div id="instanceId" class="text-xs font-mono text-slate-600 dark:text-slate-400 break-all cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800/50 p-1 rounded transition-colors"
                                 onclick="navigator.clipboard.writeText(this.textContent).then(() => showMessage('实例ID已复制到剪贴板', 'success'))"
                                 title="点击复制">-</div>
                        </div>
                    </div>

                    <div class="md:col-span-2 flex items-center gap-3 p-4 bg-white/60 dark:bg-slate-800/60 rounded-lg border border-slate-200/40 dark:border-slate-700/40">
                        <div class="w-10 h-10 bg-indigo-100 dark:bg-indigo-800 rounded-lg flex items-center justify-center">
                            <i class="ti ti-key text-indigo-600 dark:text-indigo-400"></i>
                        </div>
                        <div class="flex-1">
                            <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">许可证密钥</div>
                            <div id="licenseKey" class="license-key-display text-xs font-mono text-slate-600 dark:text-slate-400 break-all cursor-pointer p-2 rounded transition-colors"
                                 onclick="copyLicenseKey()"
                                 title="点击复制许可证密钥">-</div>
                        </div>
                        <button onclick="copyLicenseKey()" class="copy-btn flex items-center gap-1 px-3 py-2 text-xs font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-lg transition-colors">
                            <i class="ti ti-copy text-sm"></i>
                            复制
                        </button>
                    </div>
                </div>
            </div>

            <!-- 功能列表 -->
            <div id="featureListSection" class="mt-4">
                <h3 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3 flex items-center gap-2">
                    <i class="ti ti-puzzle text-sm"></i>
                    可用功能
                </h3>
                <div id="featureList" class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <!-- 功能项将通过 JavaScript 动态生成 -->
                </div>
            </div>
        </div>
    </div>

        <!-- 许可证激活 -->
        <div id="licenseActivationCard" class="admin-card">
        <h2 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-4">许可证激活</h2>

        <div class="mb-4">
            <label for="licenseKeyInput" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                License Key
            </label>
            <input
                type="text"
                id="licenseKeyInput"
                class="license-input"
                placeholder="请输入完整的License Key (格式: XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXX)"
                autocomplete="off"
            >
            <div class="mt-2 space-y-1">
                <p class="text-xs text-slate-500 dark:text-slate-400">
                    输入许可证密钥后点击激活，系统将自动验证并绑定到当前实例
                </p>
                <div class="text-xs text-slate-400 dark:text-slate-500">
                    <span class="font-medium">支持格式：</span>
                    <span class="font-mono">XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXX</span>
                    <span class="text-slate-300 dark:text-slate-600 mx-1">|</span>
                    <span class="font-mono">lic_xxxxx</span>
                    <span class="text-slate-300 dark:text-slate-600 mx-1">|</span>
                    <span class="font-mono">LS-xxxxx</span>
                </div>
            </div>
        </div>

        <div class="actions">
            <button id="activateBtn" class="btn btn-primary">
                <i class="ti ti-play text-sm"></i>
                激活许可证
            </button>

            <button id="clearBtn" class="btn btn-secondary">
                <i class="ti ti-x text-sm"></i>
                清除输入
            </button>
        </div>
    </div>

        <!-- 许可证管理 -->
        <div class="admin-card">
        <h2 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-4">许可证管理</h2>

        <!-- 已激活许可证的操作按钮 -->
        <div id="activatedLicenseActions" class="hidden mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                <button id="refreshLicenseBtn" class="flex items-center gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700/40 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-800 rounded-lg flex items-center justify-center">
                        <i class="ti ti-refresh text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="text-left">
                        <div class="text-sm font-medium text-blue-700 dark:text-blue-300">刷新状态</div>
                        <div class="text-xs text-blue-600/70 dark:text-blue-400/70">更新许可证信息</div>
                    </div>
                </button>

                <button id="unbindLicenseBtn" class="flex items-center gap-3 p-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700/40 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors">
                    <div class="w-10 h-10 bg-orange-100 dark:bg-orange-800 rounded-lg flex items-center justify-center">
                        <i class="ti ti-unlink text-orange-600 dark:text-orange-400"></i>
                    </div>
                    <div class="text-left">
                        <div class="text-sm font-medium text-orange-700 dark:text-orange-300">解绑许可证</div>
                        <div class="text-xs text-orange-600/70 dark:text-orange-400/70">从当前实例解绑</div>
                    </div>
                </button>

                <!-- 转移许可证功能已移除 - 应在授权系统中操作，不在客户端提供 -->
            </div>
        </div>

        <!-- 外部链接 -->
        <div class="space-y-3">
            <div class="flex items-center gap-3 p-4 bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 rounded-lg border border-slate-200/60 dark:border-slate-700/40">
                <div class="w-10 h-10 bg-green-100 dark:bg-green-800 rounded-lg flex items-center justify-center">
                    <i class="ti ti-shopping-cart text-green-600 dark:text-green-400"></i>
                </div>
                <div class="flex-1">
                    <div class="text-sm font-medium text-slate-700 dark:text-slate-300">购买许可证</div>
                    <div class="text-xs text-slate-500 dark:text-slate-400">获取更多功能和节点配额</div>
                </div>
                <a href="https://client.vps.mom" target="_blank" class="inline-flex items-center gap-1 px-3 py-2 text-sm font-medium text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 transition-colors">
                    前往购买
                    <i class="ti ti-external-link text-sm"></i>
                </a>
            </div>

            <div class="flex items-center gap-3 p-4 bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 rounded-lg border border-slate-200/60 dark:border-slate-700/40">
                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-800 rounded-lg flex items-center justify-center">
                    <i class="ti ti-settings text-blue-600 dark:text-blue-400"></i>
                </div>
                <div class="flex-1">
                    <div class="text-sm font-medium text-slate-700 dark:text-slate-300">管理现有许可证</div>
                    <div class="text-xs text-slate-500 dark:text-slate-400">查看和管理您的所有许可证</div>
                </div>
                <a href="https://client.vps.mom" target="_blank" class="inline-flex items-center gap-1 px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
                    前往管理
                    <i class="ti ti-external-link text-sm"></i>
                </a>
            </div>
        </div>
    </div>

        <!-- 操作结果提示 -->
        <div id="messageArea"></div>
    </div>
</div>

<!-- 核心功能脚本 -->
<script src="/js/core.js"></script>
<script src="/js/admin/show-message.js"></script>

<!-- 简化版许可证管理脚本 -->
<script>
class SimplifiedLicenseManager {
    constructor() {
        this.instanceId = null;
        this.currentLicense = null;
        this.connectionMode = 'unknown'; // online, cached, limited, disconnected
        this.reconnectTimer = null;
        this.init();
    }

    /**
     * 验证许可证密钥格式 - 与后端保持一致
     * @param {string} licenseKey - 许可证密钥
     * @returns {Object} 验证结果
     */
    validateLicenseKey(licenseKey) {
        if (!licenseKey || typeof licenseKey !== 'string') {
            return { valid: false, message: 'License Key不能为空' };
        }

        const trimmed = licenseKey.trim();

        if (trimmed.length < 10) {
            return { valid: false, message: 'License Key长度不足' };
        }

        // 标准格式：XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXX
        const standardPattern = /^[A-F0-9]{8}-[A-F0-9]{8}-[A-F0-9]{8}-[A-F0-9]{8}$/i;

        // 兼容格式
        const compatiblePatterns = [
            /^lic_[a-f0-9]{32}$/i,  // frontend格式
            /^LS-[A-F0-9]{32}$/i,   // payment格式
            /^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$/i  // enhanced格式
        ];

        if (standardPattern.test(trimmed)) {
            return { valid: true, licenseKey: trimmed, format: 'standard' };
        }

        for (const pattern of compatiblePatterns) {
            if (pattern.test(trimmed)) {
                return { valid: true, licenseKey: trimmed, format: 'compatible' };
            }
        }

        return {
            valid: false,
            message: '许可证格式不正确。请检查格式：XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXX'
        };
    }

    async init() {
        console.log('[License] 简化版许可证管理器初始化...');
        
        // 绑定事件
        this.bindEvents();
        
        // 添加智能重试机制 - 解决重启后状态显示问题
        let retryCount = 0;
        const maxRetries = 3;
        const retryDelay = 2000; // 2秒
        
        const loadWithRetry = async () => {
            await this.loadCurrentStatus();
            
            // 检查是否需要重试
            const statusDiv = document.getElementById('licenseStatus');
            const hasValidLicense = statusDiv && !statusDiv.classList.contains('status-none');
            
            if (!hasValidLicense && retryCount < maxRetries) {
                retryCount++;
                console.log(`[License] 未检测到许可证，${retryDelay/1000}秒后重试 (${retryCount}/${maxRetries})`);
                
                // 显示重试提示
                const statusText = statusDiv.querySelector('span');
                if (statusText) {
                    statusText.textContent = `正在检测许可证状态... (${retryCount}/${maxRetries})`;
                }
                
                setTimeout(loadWithRetry, retryDelay);
            } else if (hasValidLicense) {
                console.log('[License] 成功检测到有效许可证');
            } else {
                console.log('[License] 重试完成，未检测到许可证');
                // 恢复原始提示
                const statusText = statusDiv.querySelector('span');
                if (statusText) {
                    statusText.textContent = '暂无有效许可证';
                }
            }
        };
        
        // 开始加载
        await loadWithRetry();
        
        console.log('[License] 初始化完成');
    }

    bindEvents() {
        console.log('[License] 绑定事件处理器...');

        // 页面顶部刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                console.log('[License] 顶部刷新按钮被点击');
                this.refreshWithRetry();
            });
        }

        // 激活按钮
        const activateBtn = document.getElementById('activateBtn');
        if (activateBtn) {
            activateBtn.addEventListener('click', () => {
                console.log('[License] 激活按钮被点击');
                this.activateLicense();
            });
        }

        // 清除按钮
        const clearBtn = document.getElementById('clearBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                console.log('[License] 清除按钮被点击');
                document.getElementById('licenseKeyInput').value = '';
            });
        }

        // 回车键激活
        const licenseKeyInput = document.getElementById('licenseKeyInput');
        if (licenseKeyInput) {
            licenseKeyInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    console.log('[License] 回车键被按下');
                    this.activateLicense();
                }
            });
        }

        // 许可证管理按钮 - 统一使用 refreshWithRetry 方法
        const refreshLicenseBtn = document.getElementById('refreshLicenseBtn');
        if (refreshLicenseBtn) {
            refreshLicenseBtn.addEventListener('click', () => {
                console.log('[License] 刷新许可证按钮被点击');
                this.refreshWithRetry();
            });
        }

        const unbindLicenseBtn = document.getElementById('unbindLicenseBtn');
        if (unbindLicenseBtn) {
            unbindLicenseBtn.addEventListener('click', () => {
                console.log('[License] 解绑许可证按钮被点击');
                this.unbindLicense();
            });
        }

        // 转移许可证功能已移除 - 应在授权系统中操作
    }

    async loadCurrentStatus() {
        // 显示加载状态
        this.showConnectionStatus('loading');
        
        try {
            // 第一优先级：尝试在线API
            console.log('[License] 尝试在线API...');
            const onlineResponse = await this.fetchOnlineStatus();
            if (onlineResponse.ok) {
                const data = await onlineResponse.json();
                console.log('[License] 在线API成功');
                this.updateStatusDisplay(data, 'online');
                this.showConnectionStatus('online');
                return;
            }
        } catch (error) {
            console.warn('[License] 在线API失败，尝试缓存:', error.message);
        }
        
        // 第二优先级：本地缓存
        try {
            console.log('[License] 尝试本地缓存...');
            const cachedData = await this.fetchCachedStatus();
            if (cachedData && cachedData.status === 1) {
                console.log('[License] 缓存数据可用');
                this.updateStatusDisplay(cachedData, 'cached');
                this.showConnectionStatus('cached');
                this.startAutoReconnect(); // 开始自动重连
                return;
            }
        } catch (error) {
            console.warn('[License] 缓存读取失败:', error.message);
        }
        
        // 第三优先级：应急缓存
        try {
            console.log('[License] 尝试应急缓存...');
            const emergencyData = await this.fetchEmergencyCache();
            if (emergencyData && emergencyData.status === 1) {
                console.log('[License] 应急缓存可用');
                this.updateStatusDisplay(emergencyData, 'limited');
                this.showConnectionStatus('limited');
                this.startAutoReconnect();
                return;
            }
        } catch (error) {
            console.warn('[License] 应急缓存失败:', error.message);
        }
        
        // 所有方法都失败
        console.log('[License] 所有数据源都失败');
        this.updateStatusDisplay(null, 'disconnected');
        this.showConnectionStatus('disconnected');
        this.startAutoReconnect();
    }

    async fetchOnlineStatus() {
        return await fetch('/admin/api/license-enhanced/status', {
            timeout: 10000 // 10秒超时
        });
    }

    async fetchCachedStatus() {
        const response = await fetch('/admin/api/license-enhanced/cache-status');
        return response.ok ? await response.json() : null;
    }

    async fetchEmergencyCache() {
        const response = await fetch('/admin/api/license-enhanced/emergency-cache');
        return response.ok ? await response.json() : null;
    }

    updateStatusDisplay(licenseData, connectionMode) {
        const statusDiv = document.getElementById('licenseStatus');
        const detailsDiv = document.getElementById('licenseDetails');
        const activationCard = document.getElementById('licenseActivationCard');
        const activatedActions = document.getElementById('activatedLicenseActions');

        this.connectionMode = connectionMode;
        
        const hasValidLicense = licenseData && licenseData.status === 1 && licenseData.data && licenseData.data.license && licenseData.data.license.licenseKey;

        if (hasValidLicense) {
            // 根据连接模式显示不同状态
            this.showLicenseStatus(licenseData, connectionMode, statusDiv);

            // 显示详细信息
            const license = licenseData.data.license;
            const module = licenseData.data.module;

            // 优先使用模块数据中的实例ID，如果没有则从许可证数据中获取
            const instanceId = module?.instanceId || license?.instanceId || 'N/A';

            document.getElementById('planName').textContent = license.planName || '未知';
            document.getElementById('expiryDate').textContent = license.expiresAt ?
                new Date(license.expiresAt).toLocaleDateString() : '永久';
            const currentNodes = (typeof license.currentNodes === 'number') ? license.currentNodes : 0;
            const maxNodes = (typeof license.maxNodes === 'number') ? license.maxNodes : '∞';
            document.getElementById('nodeLimit').textContent = `${currentNodes}/${maxNodes}`;
            document.getElementById('instanceId').textContent = instanceId;
            document.getElementById('licenseKey').textContent = license.licenseKey || '未知';

            // 显示功能列表
            this.displayFeatures(license.featureDetails || []);

            // 隐藏激活区块，显示管理按钮
            if (activationCard) activationCard.style.display = 'none';
            if (activatedActions) activatedActions.classList.remove('hidden');

            detailsDiv.classList.remove('hidden');
            this.currentLicense = license;

            // 显示缓存信息（如果适用）
            this.showCacheInfo(licenseData, connectionMode);
        } else {
            // 无有效许可证
            this.showNoLicenseStatus(connectionMode, statusDiv);

            // 即使没有许可证，也要显示实例ID
            if (licenseData && licenseData.status === 1 && licenseData.data && licenseData.data.module) {
                const module = licenseData.data.module;
                const instanceId = module.instanceId || 'N/A';
                document.getElementById('instanceId').textContent = instanceId;
            }

            // 显示激活区块，隐藏管理按钮
            if (activationCard) activationCard.style.display = 'block';
            if (activatedActions) activatedActions.classList.add('hidden');

            detailsDiv.classList.add('hidden');
            this.currentLicense = null;
        }
    }

    showLicenseStatus(licenseData, connectionMode, statusDiv) {
        statusDiv.className = 'status-display status-active';
        
        let statusText = '许可证已激活';
        let badge = '';
        
        switch (connectionMode) {
            case 'online':
                statusText = '许可证已激活';
                break;
            case 'cached':
                statusText = '许可证已激活';
                badge = '<span class="cache-info-badge">离线模式</span>';
                break;
            case 'limited':
                statusText = '许可证已激活';
                badge = '<span class="cache-info-badge">应急模式</span>';
                break;
        }

        statusDiv.innerHTML = `
            <div class="flex items-center gap-2">
                <i class="ti ti-circle-check text-sm"></i>
                <span>${statusText}</span>
                ${badge}
            </div>
        `;
    }

    showNoLicenseStatus(connectionMode, statusDiv) {
        statusDiv.className = 'status-display status-none';
        
        let message = '暂无有效许可证';
        if (connectionMode === 'disconnected') {
            message = '网络连接异常，无法验证许可证';
        }

        statusDiv.innerHTML = `
            <div class="flex items-center gap-2">
                <i class="ti ti-info-circle text-sm"></i>
                <span>${message}</span>
            </div>
        `;
    }

    showCacheInfo(licenseData, connectionMode) {
        // 移除现有的缓存信息
        const existingCacheInfo = document.querySelector('.cache-info-display');
        if (existingCacheInfo) {
            existingCacheInfo.remove();
        }

        if (connectionMode === 'cached' && licenseData.data.cacheInfo) {
            const cacheInfo = licenseData.data.cacheInfo;
            const remainingHours = Math.floor(cacheInfo.remainingTime / (1000 * 60 * 60));
            
            const cacheInfoDiv = document.createElement('div');
            cacheInfoDiv.className = 'cache-info-display';
            cacheInfoDiv.innerHTML = `
                <div class="text-xs text-yellow-700 dark:text-yellow-300 mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-700/40">
                    <i class="ti ti-refresh text-xs"></i>
                    使用本地缓存数据，剩余有效时间：${remainingHours}小时
                    <button class="text-blue-600 hover:underline ml-2" onclick="simplifiedLicenseManager.loadCurrentStatus()">
                        尝试重新连接
                    </button>
                </div>
            `;
            
            document.getElementById('licenseStatus').appendChild(cacheInfoDiv);
        } else if (connectionMode === 'limited') {
            const warningDiv = document.createElement('div');
            warningDiv.className = 'emergency-warning';
            warningDiv.innerHTML = `
                <i class="ti ti-alert-triangle text-sm mr-2"></i>
                当前处于应急模式，部分功能受限。请检查网络连接或联系管理员。
            `;
            
            document.getElementById('licenseStatus').appendChild(warningDiv);
        }
    }

    displayFeatures(featureDetails) {
        const featureListElement = document.getElementById('featureList');
        if (!featureListElement) return;

        if (!featureDetails || featureDetails.length === 0) {
            featureListElement.innerHTML = '<div class="text-sm text-slate-500 dark:text-slate-400">暂无功能信息</div>';
            return;
        }

        // 对功能进行排序：可用的功能在前，不可用的在后
        const sortedFeatures = [...featureDetails].sort((a, b) => {
            // 可用的功能排在前面
            if (a.available && !b.available) return -1;
            if (!a.available && b.available) return 1;
            // 如果可用性相同，按名称排序
            return (a.displayName || a.name).localeCompare(b.displayName || b.name);
        });

        // 生成功能列表HTML
        let featuresHtml = '';
        
        // 图标映射表：将功能名称映射到正确的 Tabler Icons（包含完整类名）
        const iconMapping = {
            'analytics': 'ti ti-chart-line',
            'monitoring': 'ti ti-device-desktop',
            'network_check': 'ti ti-wifi',
            'notifications': 'ti ti-bell',
            'search': 'ti ti-search',
            'brain': 'ti ti-brain',
            'terminal': 'ti ti-terminal-2',
            'webssh': 'ti ti-terminal-2',
            'auto_discovery': 'ti ti-search',
            'advanced_analytics': 'ti ti-chart-line',
            'custom_alerts': 'ti ti-bell',
            'basic_monitoring': 'ti ti-device-desktop',
            'ai_analysis': 'ti ti-brain',
            'network_quality': 'ti ti-wifi'
        };
        
        sortedFeatures.forEach(feature => {
            const isAvailable = feature.available;
            // 使用映射表转换图标，如果没有映射则使用 ti ti-puzzle
            const rawIcon = feature.icon || feature.name || 'puzzle';
            const icon = iconMapping[rawIcon] || `ti ti-${rawIcon}` || 'ti ti-puzzle';

            featuresHtml += `
                <div class="flex items-center gap-3 p-3 rounded-lg border ${isAvailable
                    ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700/40'
                    : 'bg-slate-50 dark:bg-slate-800/40 border-slate-200 dark:border-slate-700/40'
                }">
                    <div class="w-8 h-8 ${isAvailable
                        ? 'bg-green-100 dark:bg-green-800'
                        : 'bg-slate-100 dark:bg-slate-700'
                    } rounded-lg flex items-center justify-center">
                        <i class="${icon} text-sm ${isAvailable
                            ? 'text-green-600 dark:text-green-400'
                            : 'text-slate-400 dark:text-slate-500'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <div class="text-sm font-medium ${isAvailable
                            ? 'text-green-700 dark:text-green-300'
                            : 'text-slate-600 dark:text-slate-400'
                        }">${feature.displayName || feature.name}</div>
                        ${feature.description ? `
                            <div class="text-xs ${isAvailable
                                ? 'text-green-600/70 dark:text-green-400/70'
                                : 'text-slate-500 dark:text-slate-500'
                            }">${feature.description}</div>
                        ` : ''}
                    </div>
                    <div>
                        ${isAvailable
                            ? '<i class="ti ti-circle-check text-green-500 dark:text-green-400 text-sm"></i>'
                            : '<i class="ti ti-lock text-slate-400 dark:text-slate-500 text-sm"></i>'
                        }
                    </div>
                </div>
            `;
        });

        featureListElement.innerHTML = featuresHtml;
    }

    // 连接状态指示器管理
    showConnectionStatus(status) {
        const statusElement = document.getElementById('connectionStatus');
        const statusText = document.getElementById('statusText');
        
        if (!statusElement || !statusText) return;

        // 移除所有状态类
        statusElement.className = statusElement.className.replace(/status-\w+/g, '');
        
        switch (status) {
            case 'online':
                statusElement.classList.add('status-online');
                statusText.textContent = '已连接';
                this.stopAutoReconnect();
                break;
            case 'cached':
                statusElement.classList.add('status-cached');
                statusText.textContent = '离线模式';
                break;
            case 'limited':
                statusElement.classList.add('status-limited');
                statusText.textContent = '应急模式';
                break;
            case 'loading':
                statusElement.classList.add('status-loading');
                statusText.textContent = '检查中...';
                break;
            case 'disconnected':
                statusElement.classList.add('status-disconnected');
                statusText.textContent = '连接异常';
                break;
            default:
                statusElement.classList.add('status-loading');
                statusText.textContent = '未知状态';
        }
    }

    // 自动重连机制
    startAutoReconnect() {
        if (this.reconnectTimer) return;
        
        let interval = 30000; // 30秒
        let attempts = 0;
        const maxInterval = 300000; // 5分钟
        
        console.log('[License] 开始自动重连...');
        
        const attemptReconnect = async () => {
            attempts++;
            console.log(`[License] 自动重连尝试 ${attempts}`);
            
            try {
                const response = await this.fetchOnlineStatus();
                if (response.ok) {
                    console.log('[License] 重连成功');
                    this.stopAutoReconnect();
                    await this.loadCurrentStatus();
                    showMessage('网络连接已恢复', 'success');
                    return;
                }
            } catch (error) {
                console.log(`[License] 重连失败: ${error.message}`);
            }
            
            // 指数退避策略
            interval = Math.min(interval * 1.2, maxInterval);
            this.reconnectTimer = setTimeout(attemptReconnect, interval);
        };
        
        this.reconnectTimer = setTimeout(attemptReconnect, interval);
    }

    stopAutoReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
            console.log('[License] 停止自动重连');
        }
    }



    async refreshWithRetry() {
        if (typeof startloading === 'function') startloading();

        try {
            showMessage('正在强制在线刷新许可证...', 'info');

            // 调用后端有效刷新接口：清理缓存并联机验证写入权威信息
            const resp = await fetch('/admin/api/license-enhanced/refresh', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            });
            const result = await resp.json().catch(() => ({ status: 0, data: '无效响应' }));

            if (result && result.status === 1) {
                // 刷新前端显示状态
                await this.loadCurrentStatus();
                // 刷新功能墙权限（若存在）
                if (window.FeatureWall && typeof window.FeatureWall.manualRefresh === 'function') {
                    await window.FeatureWall.manualRefresh();
                }
                // 触发节点状态刷新事件
                window.dispatchEvent(new CustomEvent('licenseActivated', { detail: { action: 'manual_refresh' } }));
                showMessage('状态已刷新（已联机校验并更新缓存）', 'success');
            } else {
                const msg = (result && (result.data?.error || result.data || result.message)) || '刷新失败';
                throw new Error(typeof msg === 'string' ? msg : '刷新失败');
            }

        } catch (error) {
            console.error('[License] 刷新失败:', error);
            showMessage('刷新失败: ' + error.message, 'error');
        } finally {
            if (typeof endloading === 'function') endloading();
        }
    }

    async unbindLicense() {
        if (!this.currentLicense) {
            showMessage('没有可解绑的许可证', 'warning');
            return;
        }

        if (!confirm('确定要解绑当前许可证吗？解绑后将无法使用高级功能。')) {
            return;
        }

        if (typeof startloading === 'function') startloading();

        try {
            showMessage('正在解绑许可证...', 'info');

            const response = await fetch('/admin/api/license-enhanced/unbind', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    licenseKey: this.currentLicense.licenseKey
                })
            });

            const result = await response.json();

            if (result.status === 1) {
                showMessage('许可证解绑成功！', 'success');
                await this.loadCurrentStatus();

                // 刷新功能墙权限
                if (window.FeatureWall && typeof window.FeatureWall.refresh === 'function') {
                    try {
                        await window.FeatureWall.refresh();
                        console.log('[License] 解绑后功能权限已刷新');
                    } catch (error) {
                        console.warn('[License] 刷新功能权限失败:', error);
                    }
                }
            } else {
                const errorMsg = typeof result.data === 'string' ? result.data :
                                (result.data && result.data.error) ? result.data.error : '未知错误';
                showMessage('解绑失败: ' + errorMsg, 'error');
            }
        } catch (error) {
            console.error('[License] 解绑失败:', error);
            showMessage('解绑失败: 网络错误', 'error');
        } finally {
            if (typeof endloading === 'function') endloading();
        }
    }

    // transferLicense 方法已移除 - 转移许可证功能应在授权系统中操作，不在客户端提供

    async activateLicense() {
        console.log('[License] activateLicense 方法被调用');
        const licenseKey = document.getElementById('licenseKeyInput').value.trim();
        
        if (!licenseKey) {
            showMessage('请输入License Key', 'warning');
            return;
        }

        // 统一的许可证格式验证 - 与后端保持一致
        const validation = this.validateLicenseKey(licenseKey);
        if (!validation.valid) {
            showMessage(validation.message, 'error');
            return;
        }

        console.log('[License] 准备发送激活请求，License Key:', licenseKey);
        if (typeof startloading === 'function') startloading();

        try {
            showMessage('正在激活许可证...', 'info');

            const response = await fetch('/admin/api/license-enhanced/activate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    licenseKey: licenseKey
                })
            });

            console.log('[License] 收到响应，状态码:', response.status);
            const result = await response.json();
            console.log('[License] 响应内容:', result);

            if (result.status === 1) {
                showMessage('许可证激活成功！', 'success');
                document.getElementById('licenseKeyInput').value = '';

                // 等待后端状态同步完成，然后刷新状态
                console.log('[License] 等待后端状态同步...');
                setTimeout(async () => {
                    try {
                        await this.loadCurrentStatus();

                        // 刷新功能墙权限
                        if (window.FeatureWall && typeof window.FeatureWall.manualRefresh === 'function') {
                            await window.FeatureWall.manualRefresh();
                            console.log('[License] 激活后功能权限已刷新');
                        }

                        // 触发节点状态刷新事件
                        window.dispatchEvent(new CustomEvent('licenseActivated', {
                            detail: { action: 'license_activated' }
                        }));

                        console.log('[License] 激活后状态刷新完成');
                    } catch (error) {
                        console.error('[License] 激活后状态刷新失败:', error);
                    }
                }, 1000); // 等待1秒让后端完成状态同步
            } else {
                // result.data 包含错误信息
                const errorMsg = typeof result.data === 'string' ? result.data : 
                                (result.data && result.data.error) ? result.data.error : '未知错误';
                console.error('[License] 激活失败，错误信息:', errorMsg);
                showMessage('激活失败: ' + errorMsg, 'error');
            }
        } catch (error) {
            console.error('[License] 激活失败:', error);
            showMessage('激活失败: 网络错误', 'error');
        } finally {
            if (typeof endloading === 'function') endloading();
        }
    }
}

// 复制许可证密钥的全局函数
function copyLicenseKey() {
    const licenseKeyElement = document.getElementById('licenseKey');
    const licenseKey = licenseKeyElement.textContent.trim();

    if (!licenseKey || licenseKey === '-' || licenseKey === '未知') {
        showMessage('没有可复制的许可证密钥', 'warning');
        return;
    }

    navigator.clipboard.writeText(licenseKey).then(() => {
        showMessage('许可证密钥已复制到剪贴板', 'success');

        // 添加视觉反馈
        licenseKeyElement.style.backgroundColor = '#dbeafe';
        setTimeout(() => {
            licenseKeyElement.style.backgroundColor = '';
        }, 300);
    }).catch(err => {
        console.error('复制失败:', err);
        showMessage('复制失败，请手动选择复制', 'error');

        // 备用方案：选中文本
        const range = document.createRange();
        range.selectNodeContents(licenseKeyElement);
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);
    });
}

// DOM ready event
document.addEventListener('DOMContentLoaded', function() {
    window.simplifiedLicenseManager = new SimplifiedLicenseManager();
});
</script>
{%endblock%}
