{% set title = "添加服务器" %}
{% set admin = true %}
{% extends "../../base.html" %}

{% block content %}
<!-- 页面容器 -->
<div>
    <!-- 引入侧边栏 -->
    {% include "../sidebar.html" %}

    <!-- 主内容区域 - 单一表单卡片 -->
    <div class="flex-1">
        <div class="space-y-6">
            <!-- 页面标题卡片 - 独立设计 -->
            <div class="admin-card">
                <div class="flex items-center gap-3 p-4">
                    <div class="w-8 h-8 bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900/40 dark:to-green-800/30 rounded-lg shadow-sm flex-shrink-0 border border-green-200/50 dark:border-green-700/30 flex items-center justify-center">
                        <i class="ti ti-circle-plus text-base text-green-600 dark:text-green-400"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">新增服务器</h1>
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">添加新的服务器节点</p>
                    </div>
                </div>
            </div>

            <!-- 📋 基本信息卡片 -->
            <div class="admin-card">
                <div class="p-6">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg flex items-center justify-center">
                        <i class="ti ti-info-circle text-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">基本信息</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-400">配置服务器的基本属性和监控设置</p>
                    </div>
                </div>

                    <div class="space-y-6">
                        <!-- 必填信息 - 突出显示 -->
                        <div class="bg-white/60 dark:bg-slate-800/40 rounded-xl p-4 border border-blue-200/50 dark:border-slate-600/50">
                            <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-4 flex items-center gap-2">
                                <span class="text-red-500">*</span>
                                <i class="ti ti-star text-sm text-blue-600 dark:text-blue-400"></i>
                                必填信息
                            </h4>
                            <div>
                                <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 mb-2 tracking-wide">
                                    <span class="text-red-500">*</span> 服务器名称
                                </label>
                                <input type="text"
                                       id="add_name"
                                       class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-blue-300/60 dark:border-blue-700/40 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 transition-all duration-200 hover:border-blue-400/80 dark:hover:border-blue-600/60 backdrop-blur-sm shadow-sm"
                                       placeholder="填入服务器名称">
                            </div>
                        </div>

                        <!-- 配置选项 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">节点权限</label>
                                <select id="server_status"
                                        class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 transition-all duration-200 cursor-pointer backdrop-blur-sm shadow-sm">
                                    <option value="1">公开</option>
                                    <option value="2">私有</option>
                                    <option value="0">停用</option>
                                </select>
                                <div class="mt-2 text-xs text-slate-500 dark:text-slate-400 space-y-1">
                                    <div><span class="font-medium text-green-600">公开</span>：正常监控且对外展示</div>
                                    <div><span class="font-medium text-blue-600">私有</span>：正常监控但仅管理员可见</div>
                                    <div><span class="font-medium text-gray-600">停用</span>：停止数据收集</div>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">服务器分组</label>
                                <select id="add_group_id"
                                        class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 transition-all duration-200 cursor-pointer backdrop-blur-sm shadow-sm">
                                    {% for group in groups %}
                                    <option value="{{group.id}}">{{group.name}}</option>
                                    {% endfor %}
                                    {% if not groups or groups.length == 0 %}
                                    <option value="default">默认分组</option>
                                    {% endif %}
                                </select>
                                <p class="mt-2 text-xs text-slate-500 dark:text-slate-400">选择服务器所属的分组，便于分类管理</p>
                            </div>
                        </div>

                        <!-- 可选设置 -->
                        <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">
                                到期时间 <span class="text-xs text-slate-500">(可选)</span>
                            </label>
                            <input type="date"
                                   id="add_expire_time"
                                   class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                            <p class="mt-2 text-xs text-slate-500 dark:text-slate-400">设置服务器的到期时间，方便了解服务器是否过期</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 🔐 SSH连接配置卡片 -->
            <div class="admin-card">
                <div class="p-6">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg flex items-center justify-center">
                        <i class="ti ti-shield-check text-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">SSH连接配置</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-400">配置SSH连接参数和认证信息</p>
                    </div>
                </div>

                    <div class="space-y-6">
                        <!-- 连接信息 -->
                        <div class="bg-white/60 dark:bg-slate-800/40 rounded-xl p-4 border border-green-200/50 dark:border-slate-600/50">
                            <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-4 flex items-center gap-2">
                                <span class="text-red-500">*</span>
                                <i class="ti ti-link text-sm text-green-600 dark:text-green-400"></i>
                                连接信息
                            </h4>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 mb-2 tracking-wide">
                                        <span class="text-red-500">*</span> 服务器地址
                                    </label>
                                    <input type="text"
                                           id="add_ssh_host"
                                           placeholder="************* 或 example.com"
                                           class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-green-300/60 dark:border-green-700/40 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:border-green-500/60 transition-all duration-200 hover:border-green-400/80 dark:hover:border-green-600/60 backdrop-blur-sm shadow-sm">
                                    <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">支持IPv4、IPv6地址或域名</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">SSH端口</label>
                                    <input type="number"
                                           id="add_ssh_port"
                                           value="22"
                                           min="1"
                                           max="65535"
                                           class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:border-green-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                                    <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">默认SSH端口为22</p>
                                    <p id="ssh_port_error" class="mt-1 text-xs text-red-600 dark:text-red-400 hidden"></p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">用户名</label>
                                    <input type="text"
                                           id="add_ssh_username"
                                           value="root"
                                           placeholder="root"
                                           class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:border-green-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                                    <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">SSH登录用户名</p>
                                </div>
                            </div>
                        </div>

                        <!-- 认证方式 -->
                        <div class="bg-amber-50/50 dark:bg-slate-700/30 rounded-xl p-4 border border-amber-200/50 dark:border-slate-600/30">
                            <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-4 flex items-center gap-2">
                                <i class="ti ti-key text-sm text-amber-600 dark:text-amber-400"></i>
                                认证方式 <span class="text-xs text-slate-500">(密码或私钥，至少选择一种)</span>
                            </h4>

                            <div class="space-y-4">
                                <!-- 密码认证 -->
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">SSH密码</label>
                                    <div class="relative">
                                        <input type="password"
                                               id="add_ssh_password"
                                               placeholder="输入SSH登录密码"
                                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-amber-500/50 focus:border-amber-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm pr-12">
                                        <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer toggle-password">
                                            <i class="ti ti-eye text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 transition-colors"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="text-center">
                                    <span class="bg-slate-100 dark:bg-slate-600 px-3 py-1 rounded-full text-xs text-slate-500 dark:text-slate-400">或</span>
                                </div>

                                <!-- 私钥认证 -->
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">SSH私钥</label>
                                    <div class="relative">
                                        <textarea id="add_ssh_privateKey"
                                               rows="4"
                                               placeholder="-----BEGIN RSA PRIVATE KEY-----&#10;...&#10;-----END RSA PRIVATE KEY-----"
                                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-amber-500/50 focus:border-amber-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm font-mono text-sm pr-12"
                                               style="-webkit-text-security: disc;"></textarea>
                                        <button type="button"
                                                onclick="togglePrivateKeyVisibility('add')"
                                                class="absolute right-3 top-3 p-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 transition-colors">
                                            <i class="ti ti-eye-off text-xl" id="add_ssh_privateKey_toggle_icon"></i>
                                        </button>
                                    </div>

                                    <div class="mt-3 flex items-center gap-3">
                                        <input type="file"
                                               id="add_ssh_privateKey_file"
                                               class="hidden"
                                               accept=".pem,.key,.ppk">
                                        <button type="button"
                                                onclick="document.getElementById('add_ssh_privateKey_file').click()"
                                                class="inline-flex items-center gap-2 px-3 py-2 text-xs font-medium bg-white/80 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-slate-500/50 transition-all duration-200 backdrop-blur-sm active:scale-95">
                                            <i class="ti ti-upload text-sm"></i>
                                            <span>选择私钥文件</span>
                                        </button>
                                        <span class="text-xs text-slate-500 dark:text-slate-400">支持 RSA、ED25519、PKCS8 格式</span>
                                    </div>
                                </div>

                                <!-- 私钥密码 -->
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">
                                        私钥密码 <span class="text-xs text-slate-500">(如果私钥有密码保护)</span>
                                    </label>
                                    <div class="relative">
                                        <input type="password"
                                               id="add_ssh_passphrase"
                                               placeholder="私钥密码（可选）"
                                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-amber-500/50 focus:border-amber-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm pr-12">
                                        <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer toggle-password">
                                            <i class="ti ti-eye text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 transition-colors"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- SSH测试区域 -->
                        <div class="bg-slate-50/50 dark:bg-slate-700/30 rounded-xl p-4 border border-slate-200/50 dark:border-slate-600/30">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 flex items-center gap-2">
                                    <i class="ti ti-circle-check text-sm text-green-600 dark:text-green-400"></i>
                                    连接测试
                                </h4>
                                <button type="button"
                                        onclick="testSSHConnectionForAdd()"
                                        class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-green-500/50 transition-all duration-200 active:scale-95">
                                    <i class="ti ti-circle-check text-sm"></i>
                                    <span>测试SSH连接</span>
                                </button>
                            </div>
                            <p class="text-xs text-slate-500 dark:text-slate-400 mb-3">建议在保存前测试SSH连接，确保配置正确</p>
                            <!-- 连接结果显示区域 -->
                            <div id="ssh-test-result" class="hidden text-sm font-medium p-3 rounded-lg border"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ⚙️ 高级设置卡片 -->
            <div class="admin-card">
                <div class="p-6">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg flex items-center justify-center">
                        <i class="ti ti-adjustments-horizontal text-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">高级设置</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-400">配置流量监控、API通讯和其他高级选项</p>
                    </div>
                </div>

                    <div class="space-y-6">
                        <!-- 通讯模式设置 -->
                        <div class="bg-white/60 dark:bg-slate-800/40 rounded-xl p-4 border border-purple-200/50 dark:border-slate-600/50">
                            <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-4 flex items-center gap-2">
                                <i class="ti ti-arrows-left-right text-sm text-purple-600 dark:text-purple-400"></i>
                                通讯模式配置
                            </h4>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">通讯密钥</label>
                                    <input type="text"
                                           id="add_api_key"
                                           value="{{uuid.v4()}}"
                                           class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm font-mono text-sm">
                                    <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">用于客户端与服务器通讯的密钥</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">通讯端口</label>
                                    <input type="number"
                                           id="add_api_port"
                                           value="9999"
                                           min="1"
                                           max="65535"
                                           class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                                    <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">客户端监听的端口号</p>
                                    <p id="api_port_error" class="mt-1 text-xs text-red-600 dark:text-red-400 hidden"></p>
                                    <p class="mt-1 text-xs text-amber-600 dark:text-amber-400 flex items-start gap-1">
                                        <i class="ti ti-info-circle text-xs mt-0.5"></i>
                                        <span>探针端口，需开放防火墙</span>
                                    </p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">
                                        网络接口 <span class="text-xs text-slate-500">(可选)</span>
                                    </label>
                                    <input type="text"
                                           id="add_device"
                                           placeholder="eth0"
                                           class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                                    <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">指定监控的网络接口，留空自动检测</p>
                                </div>
                            </div>

                            <!-- 通讯模式开关 -->
                            <div class="mt-4 bg-slate-50/80 dark:bg-slate-700/30 rounded-xl p-4 border border-slate-200/50 dark:border-slate-600/30">
                                <div class="flex items-center gap-4 mb-3">
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 tracking-wide">通讯模式</label>
                                    <div class="relative inline-block">
                                        <input type="checkbox"
                                               id="add_api_mode"
                                               class="sr-only">
                                        <div onclick="toggleApiMode('add')"
                                             class="w-11 h-6 bg-slate-300 dark:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-300 dark:focus:ring-blue-800 rounded-full cursor-pointer relative transition-colors duration-200">
                                            <div id="add-api-mode-toggle"
                                                 class="w-5 h-5 rounded-full bg-white absolute left-0.5 top-0.5 transition-transform duration-200 transform shadow-sm">
                                            </div>
                                        </div>
                                    </div>
                                    <span id="add-api-mode-text" class="text-sm font-medium text-slate-700 dark:text-slate-300">被动模式</span>
                                </div>
                                <div class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">
                                    <div class="mb-1"><span class="font-medium">主动模式：</span>客户端主动向服务器上报监控数据</div>
                                    <div><span class="font-medium">被动模式：</span>服务器主动从客户端获取监控数据</div>
                                </div>
                            </div>
                        </div>

                        <!-- 流量监控设置 -->
                        <div class="bg-orange-50/50 dark:bg-slate-700/30 rounded-xl p-4 border border-orange-200/50 dark:border-slate-600/30">
                            <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-4 flex items-center gap-2">
                                <i class="ti ti-chart-line text-sm text-orange-600 dark:text-orange-400"></i>
                                流量监控设置 <span class="text-xs text-slate-500">(可选)</span>
                            </h4>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">月流量限制 (GB)</label>
                                    <input type="number"
                                           id="add_traffic_limit"
                                           placeholder="0 = 无限制"
                                           min="0"
                                           class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">流量重置日</label>
                                    <input type="number"
                                           id="add_traffic_reset_day"
                                           placeholder="1-31"
                                           min="1"
                                           max="31"
                                           value="1"
                                           class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">警告阈值 (%)</label>
                                    <input type="number"
                                           id="add_traffic_alert_percent"
                                           placeholder="80"
                                           min="1"
                                           max="100"
                                           value="80"
                                           class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                                </div>
                            </div>

                            <!-- 流量统计方向和校准设置 -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">流量统计方向</label>
                                    <select id="add_traffic_direction"
                                            class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/60 transition-all duration-200 cursor-pointer backdrop-blur-sm shadow-sm">
                                        <option value="both" selected>双向流量 (入站+出站)</option>
                                        <option value="in">仅入站流量</option>
                                        <option value="out">仅出站流量</option>
                                        <option value="max">单向最大 (取较大值)</option>
                                    </select>
                                    <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">选择月度流量的统计方式</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">流量校准日期</label>
                                    <input type="date"
                                           id="add_traffic_calibration_date"
                                           class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">校准时已用流量 (GB)</label>
                                    <input type="number"
                                           id="add_traffic_calibration_value"
                                           step="0.01"
                                           min="0"
                                           placeholder="0"
                                           class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                                </div>
                            </div>

                            <p class="mt-3 text-xs text-slate-500 dark:text-slate-400">流量校准用于设置流量统计的起始点，适用于已有流量使用的服务器</p>
                        </div>
                    </div>
                </div>
            </div>


            <!-- 操作按钮区域 - 使用相同的卡片样式 -->
            <div class="admin-card" id="action-bar">
                <div class="p-6">
                    <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                        <!-- 左侧：返回按钮和提示 -->
                        <div class="flex items-center gap-4 w-full sm:w-auto">
                            <a href="/admin/servers"
                               class="inline-flex items-center gap-2 px-4 py-2.5 text-sm font-medium bg-white/80 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-xl hover:bg-slate-50 dark:hover:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-slate-500/50 transition-all duration-200 backdrop-blur-sm active:scale-95 shadow-sm">
                                <i class="ti ti-arrow-left text-sm"></i>
                                <span>返回服务器列表</span>
                            </a>
                            
                            <!-- 快捷键提示（桌面端显示） -->
                            <div class="hidden md:flex items-center gap-2 text-xs text-slate-500 dark:text-slate-400">
                                <i class="ti ti-keyboard text-sm"></i>
                                <span>Ctrl+S 快速保存</span>
                            </div>
                        </div>

                        <!-- 右侧：主要操作按钮 -->
                        <div class="flex items-center gap-3 w-full sm:w-auto">
                            <!-- 测试连接按钮 -->
                            <button type="button"
                                    onclick="testSSHConnectionForAdd()"
                                    class="flex-1 sm:flex-none inline-flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium bg-white/80 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-xl hover:bg-slate-50 dark:hover:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-slate-500/50 transition-all duration-200 backdrop-blur-sm active:scale-95 shadow-sm">
                                <i class="ti ti-circle-check text-sm"></i>
                                <span>测试连接</span>
                            </button>

                            <!-- 保存按钮 - 主要操作 -->
                            <button onclick="add()"
                                    class="flex-1 sm:flex-none inline-flex items-center justify-center gap-2 px-6 py-2.5 text-sm font-medium bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white rounded-xl shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95">
                                <i class="ti ti-circle-plus text-sm"></i>
                                <span>添加服务器</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div> <!-- 关闭表单容器 -->
    </div> <!-- 关闭主内容区域 -->
</div> <!-- 关闭页面容器 -->

<!-- 包含共享的安装进度模态框 -->
{% include "./partials/install-progress-modal.html" %}

<!-- T056: 节点限制对话框 -->
<div id="node-limit-modal" class="fixed inset-0 bg-black/50 hidden items-center justify-center backdrop-blur-sm z-50">
    <div class="bg-white/95 dark:bg-slate-800/95 backdrop-blur-md rounded-2xl shadow-2xl max-w-lg w-full mx-4 overflow-hidden border border-slate-200/50 dark:border-slate-700/50">
        <!-- 模态框头部 -->
        <div class="flex items-center justify-between p-4 pb-3 border-b border-slate-200/60 dark:border-slate-700/40">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/40 dark:to-orange-800/30 rounded-lg border border-orange-200/50 dark:border-orange-700/30 flex items-center justify-center">
                    <i class="ti ti-alert-triangle text-base text-orange-600 dark:text-orange-400"></i>
                </div>
                <h3 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide">节点数量已达上限</h3>
            </div>
            <button onclick="hideNodeLimitModal()" class="w-8 h-8 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 hover:bg-slate-100/60 dark:hover:bg-slate-700/50 rounded-lg transition-all duration-200 active:scale-95 flex items-center justify-center">
                <i class="ti ti-x text-base"></i>
            </button>
        </div>

        <!-- 模态框内容 -->
        <div class="p-4">
            <div id="node-limit-content" class="space-y-4">
                <!-- 内容将通过JavaScript动态填充 -->
            </div>

            <div class="flex items-center gap-3 mt-6 pt-4 border-t border-slate-200/60 dark:border-slate-700/40">
                <button onclick="hideNodeLimitModal()"
                        class="flex-1 px-4 py-2 text-sm font-medium bg-white/80 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-slate-500/50 transition-all duration-200 active:scale-95">
                    取消
                </button>
                <a id="upgrade-link" href="/upgrade"
                   class="flex-1 px-4 py-2 text-sm font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-200 active:scale-95 text-center">
                    查看升级选项
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<!-- 引入核心工具函数 -->
<script src="/js/core.js"></script>

<!-- 引入服务器添加相关的JavaScript模块 -->
<script src="/js/admin/servers/traffic-format.js"></script>
<script src="/js/admin/servers/ssh-test-common.js"></script>
<script src="/js/admin/servers/location-common.js"></script>
<script src="/js/admin/servers/file-upload.js"></script>
<script src="/js/admin/servers/api-mode-toggle.js"></script>
<script src="/js/admin/servers/floating-action-bar.js"></script>
<script src="/js/admin/servers/password-toggle.js"></script>
<script src="/js/admin/servers/keyboard-shortcuts.js"></script>
<script src="/js/admin/servers/node-limit-dialog.js"></script>
<script src="/js/admin/servers/install-progress.js"></script>
<script src="/js/admin/servers/form-utils.js"></script>
<script src="/js/admin/servers/server-add.js"></script>
<script src="/js/admin/servers/add.js"></script>

<!-- SSH 私钥格式化工具 -->
<script src="/js/admin/servers/ssh-key-formatter.js"></script>

<!-- 初始化私钥格式化和显示切换 -->
<script>
// 切换私钥显示/隐藏
function togglePrivateKeyVisibility(prefix) {
    const textarea = document.getElementById(prefix + '_ssh_privateKey');
    const icon = document.getElementById(prefix + '_ssh_privateKey_toggle_icon');
    
    if (textarea && icon) {
        if (textarea.style.webkitTextSecurity === 'disc') {
            textarea.style.webkitTextSecurity = 'none';
            textarea.style.fontFamily = 'monospace';
            icon.className = 'ti ti-eye text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 transition-colors';
        } else {
            textarea.style.webkitTextSecurity = 'disc';
            textarea.style.fontFamily = 'monospace';
            icon.className = 'ti ti-eye-off text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 transition-colors';
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // 绑定私钥输入框
    const privateKeyTextarea = document.getElementById('add_ssh_privateKey');
    if (privateKeyTextarea) {
        SSHKeyFormatter.bindToTextarea(privateKeyTextarea);
    }
});
</script>

<!-- 页面特定数据初始化 -->
{% endblock %}
