{% extends 'admin/sidebar.html' %}

{% block title %}监控仪表板 - {{ site.name }}{% endblock %}

{% block head %}
<link rel="stylesheet" href="/css/components/monitor-dashboard.css">
<script src="/js/libs/echarts.min.js"></script>
{% endblock %}

{% block content %}
<div class="monitor-dashboard">
    <!-- 顶部统计卡片 -->
    <div class="stats-cards">
        <div class="stat-card">
            <div class="stat-icon online">
                <i data-lucide="cloud-check" class="w-5 h-5"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value" id="online-count">0</div>
                <div class="stat-label">在线节点</div>
            </div>
            <div class="stat-trend up">+5%</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon warning">
                <i data-lucide="alert-triangle" class="w-5 h-5"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value" id="warning-count">0</div>
                <div class="stat-label">告警节点</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon traffic">
                <i data-lucide="arrow-up-down" class="w-5 h-5"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value" id="total-traffic">0 GB</div>
                <div class="stat-label">今日流量</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon performance">
                <i data-lucide="gauge" class="w-5 h-5"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value" id="avg-latency">0 ms</div>
                <div class="stat-label">平均延迟</div>
            </div>
        </div>
    </div>

    <!-- 主要图表区域 -->
    <div class="charts-grid">
        <!-- 实时延迟热力图 -->
        <div class="chart-panel">
            <div class="panel-header">
                <h3>全球延迟热力图</h3>
                <div class="panel-actions">
                    <button class="btn-icon" onclick="refreshHeatmap()">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
            <div id="latency-heatmap" class="chart-container"></div>
        </div>

        <!-- 节点健康度分布 -->
        <div class="chart-panel">
            <div class="panel-header">
                <h3>节点健康度分布</h3>
                <select id="health-filter" onchange="updateHealthChart()">
                    <option value="all">所有节点</option>
                    <option value="region">按地区</option>
                    <option value="provider">按提供商</option>
                </select>
            </div>
            <div id="health-distribution" class="chart-container"></div>
        </div>

        <!-- 流量趋势图 -->
        <div class="chart-panel full-width">
            <div class="panel-header">
                <h3>24小时流量趋势</h3>
                <div class="time-selector">
                    <button class="time-btn active" data-range="24h">24小时</button>
                    <button class="time-btn" data-range="7d">7天</button>
                    <button class="time-btn" data-range="30d">30天</button>
                </div>
            </div>
            <div id="traffic-trend" class="chart-container"></div>
        </div>
    </div>

    <!-- 节点列表（优化版） -->
    <div class="nodes-section">
        <div class="section-header">
            <h3>节点监控</h3>
            <div class="view-switcher">
                <button class="view-btn active" data-view="cards">
                    <i data-lucide="grid-3x3" class="w-4 h-4"></i>
                </button>
                <button class="view-btn" data-view="table">
                    <i data-lucide="list" class="w-4 h-4"></i>
                </button>
                <button class="view-btn" data-view="map">
                    <i data-lucide="map" class="w-4 h-4"></i>
                </button>
            </div>
        </div>

        <!-- 搜索和筛选栏 -->
        <div class="filter-bar">
            <div class="search-box">
                <i data-lucide="search" class="w-4 h-4"></i>
                <input type="text" placeholder="搜索节点..." id="node-search">
            </div>
            
            <div class="filter-chips">
                <div class="chip active" data-filter="all">全部</div>
                <div class="chip" data-filter="online">在线</div>
                <div class="chip" data-filter="offline">离线</div>
                <div class="chip" data-filter="warning">告警</div>
            </div>
            
            <button class="btn-filter" onclick="showAdvancedFilters()">
                <i data-lucide="filter" class="w-4 h-4"></i>
                高级筛选
            </button>
        </div>

        <!-- 节点卡片视图 -->
        <div id="nodes-container" class="nodes-grid">
            <!-- 动态加载节点卡片 -->
        </div>
    </div>

    <!-- 实时事件流 -->
    <div class="events-panel">
        <div class="panel-header">
            <h3>实时事件</h3>
            <span class="event-badge" id="new-events">0</span>
        </div>
        <div class="events-list" id="events-container">
            <!-- 动态加载事件 -->
        </div>
    </div>
</div>

<!-- 节点详情模态框 -->
<div id="node-detail-modal" class="modal">
    <div class="modal-content large">
        <div class="modal-header">
            <h2 id="modal-node-name">节点详情</h2>
            <button class="modal-close" onclick="closeNodeDetail()">×</button>
        </div>
        <div class="modal-body">
            <!-- 节点详细信息和图表 -->
        </div>
    </div>
</div>

<script src="/js/monitor-dashboard.js"></script>
{% endblock %}