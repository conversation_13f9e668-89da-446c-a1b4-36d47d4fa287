{% set title = "修改密码" %}
{%set admin = true%}
{% extends "../base.html" %}

{%block content%}
<!-- 覆盖容器样式 -->
<div class="fixed inset-0 -mt-6 -mx-4">
    <div class="flex items-center justify-center w-full h-full bg-gray-50 dark:bg-gray-900">
        <!-- 修改密码卡片 -->
        <div class="w-full max-w-md p-6 m-4 bg-white rounded-lg shadow-lg dark:bg-gray-800">
            <!-- 标题区域 -->
            <div class="mb-6">
                <h1 class="text-2xl font-medium text-gray-900 dark:text-white">修改密码</h1>
                <p class="text-sm text-gray-600 dark:text-gray-400">设置一个更安全的新密码</p>
                {% if forceChange %}
                <div class="mt-3 p-2 bg-yellow-100 dark:bg-yellow-900 rounded-md">
                    <p class="text-sm text-yellow-800 dark:text-yellow-200">
                        <i data-lucide="alert-triangle" class="inline w-4 h-4 text-yellow-600 dark:text-yellow-400"></i>
                        您正在使用默认密码，为了系统安全，请立即修改密码。
                    </p>
                </div>
                {% endif %}
            </div>
            
            <!-- 隐藏forceChange值的数据属性 -->
            <div id="passwordFormData" 
                 data-force-change="{% if forceChange %}true{% else %}false{% endif %}" 
                 class="hidden"></div>
            
            <!-- 新密码输入 -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">新密码</label>
                <div class="relative">
                    <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                        <i data-lucide="lock" class="text-gray-500 w-4 h-4"></i>
                    </span>
                    <input type="password" 
                           id="newPassword"
                           class="w-full pl-10 pr-10 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                           placeholder="请输入新密码">
                    <span class="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer toggle-password">
                        <i data-lucide="eye" class="text-gray-500 w-4 h-4"></i>
                    </span>
                </div>
                <div class="mt-1 text-sm text-gray-500 dark:text-gray-400" id="new-password-helper"></div>
            </div>
            
            <!-- 确认密码输入 -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">确认密码</label>
                <div class="relative">
                    <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                        <i data-lucide="lock" class="text-gray-500 w-4 h-4"></i>
                    </span>
                    <input type="password" 
                           id="confirmPassword"
                           class="w-full pl-10 pr-10 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                           placeholder="请再次输入新密码">
                    <span class="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer toggle-password">
                        <i data-lucide="eye" class="text-gray-500 w-4 h-4"></i>
                    </span>
                </div>
                <div class="mt-1 text-sm text-gray-500 dark:text-gray-400" id="confirm-password-helper"></div>
            </div>

            <!-- 按钮区域 -->
            <div class="flex space-x-3">
                {% if not forceChange %}
                <button id="cancel" 
                        class="flex items-center justify-center w-1/3 px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600">
                    <i data-lucide="arrow-left" class="mr-2 w-4 h-4"></i>
                    <span>返回</span>
                </button>
                {% endif %}
                <button id="savePassword" 
                        class="flex items-center justify-center {% if forceChange %}w-full{% else %}w-2/3{% endif %} px-4 py-2 text-white bg-primary-500 rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                    <i data-lucide="save" class="mr-2 w-4 h-4"></i>
                    <span>保存新密码</span>
                </button>
            </div>
        </div>
    </div>
</div>
{%endblock%}

{%block js%}
<script src="/js/core.js"></script>
<script src="/js/md5.min.js"></script>
<script>
// 获取元素值的简化函数
function V(id) { return document.getElementById(id).value; }

// 密码修改函数
async function changePassword() {
    try {
        // 获取密码值
        const newPassword = V('newPassword');
        const confirmPassword = V('confirmPassword');
        
        // 验证输入
        if (!newPassword) {
            notice('请输入新密码', 'warning');
            return;
        }
        
        if (newPassword.length < 6) {
            notice('密码长度应至少为6个字符', 'warning');
            return;
        }
        
        if (newPassword !== confirmPassword) {
            notice('两次输入的密码不一致', 'error');
            return;
        }
        
        // 显示加载中状态
        startloading();
        
        // 发送修改密码请求 - 直接发送明文密码，不再进行MD5加密
        const res = await postjson('/admin/change-password', {
            newPassword: newPassword  // 修改：不再对密码进行MD5加密
        });
        
        // 处理响应
        endloading();
        
        if (res.status) {
            notice('密码修改成功', 'success');
            
            // 从DOM中获取forceChange值
            const formData = document.getElementById('passwordFormData');
            const forceChange = formData.dataset.forceChange === 'true';
            
            setTimeout(() => {
                if (forceChange) {
                    window.location.href = '/';  // 重定向到首页
                } else {
                    window.history.back();  // 返回上一页
                }
            }, 1000);
        } else {
            notice(res.message || '密码修改失败', 'error');
        }
    } catch (error) {
        console.error('修改密码错误:', error);
        endloading();
        notice('修改密码过程发生错误', 'error');
    }
}

// 绑定保存按钮点击事件
document.getElementById('savePassword').addEventListener('click', changePassword);

// 绑定回车键事件
document.addEventListener('keyup', function(e) {
    if (e.key === 'Enter') changePassword();
});

// 绑定取消按钮事件（如果存在）
const cancelButton = document.getElementById('cancel');
if (cancelButton) {
    cancelButton.addEventListener('click', function() {
        window.history.back();
    });
}

// 绑定密码显示/隐藏功能
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.toggle-password').forEach(toggle => {
        toggle.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
            input.setAttribute('type', type);
            
            // 切换图标
            const icon = this.querySelector('i');
            const iconName = type === 'password' ? 'eye' : 'eye-off';
            icon.setAttribute('data-lucide', iconName);
            if (window.lucide) {
                window.lucide.createIcons();
            }
        });
    });
});
</script>
{%endblock%} 