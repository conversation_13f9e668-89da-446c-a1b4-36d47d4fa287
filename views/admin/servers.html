{% set title = "管理服务器" %}
{%set admin = true%}
{% extends "../base.html" %}

{% block content %}
<!-- 页面容器 - 简化布局 -->
<div>
    <!-- 分页组件样式 -->
    <link rel="stylesheet" href="/css/components/servers-pagination.css">
    
    <!-- 引入侧边栏 -->
    {% include "admin/sidebar.html" %}

    <!-- 主内容区域 - 独立卡片布局，居中显示 -->
    <div class="flex-1 space-y-6">
        
        <!-- 页面标题卡片 - 独立设计 -->
        <div class="admin-card">
            <div class="admin-card-header">
                <div class="admin-page-header">
                    <div class="admin-page-title">
                        <div class="admin-title-icon admin-icon-blue">
                            <i class="ti ti-server text-lg text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div class="admin-title-text">
                            <h1>服务器管理</h1>
                            <div class="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3">
                                <p class="text-xs text-slate-500 dark:text-slate-400">Server Management</p>
                                <!-- T056: Mini节点使用状态 -->
                                <div id="nodeLimitStatusMini" class="flex items-center gap-2 text-xs">
                                    <span class="text-slate-400 dark:text-slate-500">节点:</span>
                                    <div class="flex items-center gap-1">
                                        <span class="font-medium text-slate-700 dark:text-slate-300" id="miniNodeCount">-</span>
                                        <span class="text-slate-400">/</span>
                                        <span class="text-slate-500 dark:text-slate-400" id="miniNodeLimit">-</span>
                                    </div>
                                    <div class="w-8 h-1.5 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
                                        <div class="h-full bg-blue-500 transition-all duration-300 rounded-full" id="miniProgressBar" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 操作按钮组 - 移动端优化 -->
                    <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3">
                        <a href="/admin/servers/add"
                           class="inline-flex items-center justify-center gap-2 px-4 py-3 sm:py-2 text-sm font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95">
                            <i class="ti ti-plus text-sm"></i>
                            <span>新增服务器</span>
                        </a>
                        <div class="flex gap-2 sm:gap-3">
                            <a href="/admin/ssh_scripts"
                               class="flex-1 sm:flex-initial inline-flex items-center justify-center gap-2 px-4 py-3 sm:py-2 text-sm font-medium bg-white/80 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-slate-500/50 transition-all duration-200 backdrop-blur-sm active:scale-95">
                                <i class="ti ti-terminal text-sm"></i>
                                <span class="hidden sm:inline">SSH脚本</span>
                                <span class="sm:hidden">脚本</span>
                            </a>
                            <button onclick="cleanupOrphanData()"
                                    class="flex-1 sm:flex-initial inline-flex items-center justify-center gap-2 px-4 py-3 sm:py-2 text-sm font-medium bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 border border-orange-300/60 dark:border-orange-600/60 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/40 focus:outline-none focus:ring-2 focus:ring-orange-500/50 transition-all duration-200 backdrop-blur-sm active:scale-95">
                                <i class="ti ti-trash text-sm"></i>
                                <span class="hidden sm:inline">清理孤立数据</span>
                                <span class="sm:hidden">清理</span>
                            </button>
                            <button onclick="saveOrder()"
                                    class="flex-1 sm:flex-initial inline-flex items-center justify-center gap-2 px-4 py-3 sm:py-2 text-sm font-medium bg-white/80 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-slate-500/50 transition-all duration-200 backdrop-blur-sm active:scale-95">
                                <i class="ti ti-device-floppy text-sm"></i>
                                <span class="hidden sm:inline">保存排序</span>
                                <span class="sm:hidden">保存</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 服务器列表卡片 - 独立设计 -->
        <div class="admin-card">
            <!-- 卡片头部 - 包含分页信息 -->
            <div class="admin-list-header">
                <div class="flex items-center justify-between w-full">
                    <div class="flex items-center gap-2">
                        <div class="admin-title-icon admin-icon-green">
                            <i class="ti ti-list text-lg text-green-600 dark:text-green-400"></i>
                        </div>
                        <div class="admin-list-title">
                            <h2 class="text-base">服务器列表</h2>
                            <p class="text-xs">拖拽调整服务器顺序</p>
                        </div>
                    </div>
                    
                    <!-- 分页信息和每页数量控制 -->
                    <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4">
                        <div class="pagination-info order-2 sm:order-1">
                            <i class="ti ti-info-circle text-sm"></i>
                            <span id="pagination-info" class="text-sm font-medium">显示 1-20 共 {{servers|length}} 条</span>
                        </div>
                        <select id="page-size-selector" class="order-1 sm:order-2 px-3 py-2 text-sm border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50">
                            <option value="10">10条/页</option>
                            <option value="20">20条/页</option>
                            <option value="50" selected>50条/页</option>
                            <option value="100">100条/页</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 服务器列表容器 - 简化设计，去掉左侧缝隙 -->
            <div class="overflow-hidden">
                <!-- 表格容器 - 完全透明背景 -->
                <div class="servers-pagination-container bg-transparent">
                    <!-- 内容区域 - 居中显示，平衡间距 -->
                    <div class="px-4 py-6 mx-auto max-w-full">
                        <div class="w-full">
                            <!-- 移动端卡片视图 -->
                            <div class="block md:hidden space-y-3" id="mobile-servers-list">
                                <!-- 移动端服务器卡片将通过JavaScript动态填充 -->
                            </div>
                            
                            <!-- 桌面端表格视图 -->
                            <div class="hidden md:block overflow-visible">
                                <table class="w-full bg-transparent">
                                    <thead>
                                        <tr class="text-left border-b-2 border-slate-200/60 dark:border-slate-700/40 bg-transparent">
                                            <th class="p-4 text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide min-w-[60px]">排序</th>
                                            <th class="p-4 text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide min-w-[200px]">服务器信息</th>
                                            <th class="p-4 text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide min-w-[80px]">节点权限</th>
                                            <th class="p-4 text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide min-w-[140px]">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="servers" class="bg-transparent">
                                        <!-- 服务器数据将通过JavaScript动态填充 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- 无数据提示 -->
                        <div id="no-data-message" class="hidden servers-empty-state text-center">
                            <div class="flex flex-col items-center gap-4">
                                <div class="w-16 h-16 bg-transparent rounded-2xl border border-slate-200/50 dark:border-slate-700/30 flex items-center justify-center">
                                    <i class="ti ti-server text-2xl text-slate-400"></i>
                                </div>
                                <div>
                                    <h4 class="text-lg font-medium text-slate-600 dark:text-slate-300">暂无服务器</h4>
                                    <p class="text-sm text-slate-500 dark:text-slate-400 mt-1">点击上方按钮添加第一台服务器</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页控制器 - 增加间距，透明背景 -->
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 p-4 pt-6 border-t-2 border-slate-200/60 dark:border-slate-700/40 bg-transparent">
                <!-- 分页信息 -->
                <div class="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                    <div class="w-5 h-5 bg-transparent rounded-md flex items-center justify-center border border-blue-200/50 dark:border-blue-700/30">
                        <i class="ti ti-files text-xs text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <span id="pagination-summary" class="font-medium">第 1 页，共 1 页</span>
                </div>
                
                <!-- 分页控制按钮组 - 移动端优化 -->
                <div class="flex items-center gap-1 sm:gap-2 order-last sm:order-none">
                    <button id="first-page" 
                            class="inline-flex items-center justify-center w-10 h-10 sm:w-9 sm:h-9 bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95" 
                            title="首页">
                        <i class="ti ti-chevrons-left text-sm"></i>
                    </button>
                    <button id="prev-page" 
                            class="inline-flex items-center justify-center w-10 h-10 sm:w-9 sm:h-9 bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95" 
                            title="上一页">
                        <i class="ti ti-chevron-left text-sm"></i>
                    </button>
                    
                    <!-- 页码按钮容器 -->
                    <div id="page-numbers" class="flex items-center gap-1">
                        <!-- 页码按钮将通过JavaScript动态生成 -->
                    </div>
                    
                    <button id="next-page" 
                            class="inline-flex items-center justify-center w-10 h-10 sm:w-9 sm:h-9 bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95" 
                            title="下一页">
                        <i class="ti ti-chevron-right text-sm"></i>
                    </button>
                    <button id="last-page" 
                            class="inline-flex items-center justify-center w-10 h-10 sm:w-9 sm:h-9 bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95" 
                            title="末页">
                        <i class="ti ti-chevrons-right text-sm"></i>
                    </button>
                </div>
                
                <!-- 快速跳转 - 移动端隐藏/简化 -->
                <div class="hidden sm:flex items-center gap-2 text-sm">
                    <span class="text-slate-500 dark:text-slate-400 whitespace-nowrap">跳转到</span>
                    <input id="jump-page" 
                           type="number" 
                           min="1" 
                           class="w-12 sm:w-16 px-2 py-1.5 text-sm border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 text-center transition-all duration-200" 
                           placeholder="1">
                    <button id="jump-btn" 
                            class="inline-flex items-center px-3 py-1.5 text-sm font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-200 active:scale-95">
                        跳转
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 隐藏的原始服务器数据 (修复JSON转义) -->
<script type="application/json" id="servers-data">
[
{%for server in servers%}
    {
        "sid": "{{server.sid}}",
        "name": "{{server.name|e}}",
        "host": "{{server.data.ssh.host|e}}",
        "status": {{server.status}},
        "tags": {{server.data.tags|default([])|tojson}}
    }{% if not loop.last %},{% endif %}
{%endfor%}
]
</script>

{%endblock%}

{%block js%}
<script src="/js/ConnectionManager.js"></script>
<script src="/js/libs/sortable.min.js"></script>
<script src="/js/admin/servers-pagination.js?v=2.0.4"></script>
<!-- T056: 节点限制状态组件 -->
<script src="/js/components/NodeLimitStatus.js"></script>
<script>
// 添加E函数定义
function E(id) {
    return document.getElementById(id);
}

// T056: 初始化节点限制状态组件
let nodeLimitStatusComponent = null;

// 页面加载完成后初始化组件
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Mini模式的节点状态组件
    nodeLimitStatusComponent = new NodeLimitStatus('nodeLimitStatusMini', true);
});

// saveOrder函数已在servers-pagination.js中重写

async function init(sid){
    // 跳转到编辑页面进行安装，这样可以看到详细的安装日志
    if(confirm('是否前往编辑页面进行安装？那里可以看到详细的安装日志。')) {
        window.location.href = `/admin/servers/${sid}/#install`;
        return;
    }

    // 如果用户选择直接安装，则在当前页面进行
    startloading();
    var res = await postjson(`/admin/servers/${sid}/init`);
    endloading();

    // 显示结果
    if(res.status) {
        notice('安装成功！', 'success');
    } else {
        notice(res.data || '安装失败', 'error');
    }
}

async function resetTraffic(sid, serverName){
    if(!confirm(`确认初始化 ${serverName} 的流量数据?\n这将清空所有历史流量记录，只保留未来新增的流量数据。`)) return;
    startloading();
    try {
        const res = await postjson(`/admin/servers/${sid}/reset-traffic`);
        endloading();
        notice(res.data || '流量数据已初始化');
        if(res.status) window.location.reload();
    } catch(error) {
        endloading();
        notice('初始化流量数据失败: ' + (error.message || '未知错误'));
    }
}

/**
 * 删除服务器
 * @param {string} sid - 服务器ID
 * @param {string} serverName - 服务器名称
 */
async function del(sid, serverName){
    // 获取服务器详细信息
    startloading();
    try {
        // 获取相关数据统计
        const res = await postjson(`/admin/servers/${sid}/stats`);
        endloading();

        if (!res.status) {
            notice(res.data || '获取服务器统计信息失败');
            return;
        }

        // 获取统计数据
        const stats = res.data;
        console.log('服务器统计信息:', stats);

        // 构建确认消息
        let confirmMessage = `确认删除服务器 "${serverName}" 及其所有相关数据?\n\n`;
        confirmMessage += "此操作将同时删除以下数据:\n";
        confirmMessage += `- 服务器基本信息\n`;

        // 流量统计数据
        if (stats.traffic > 0) {
            confirmMessage += `- 流量统计数据: ${stats.traffic} 条记录\n`;
        }

        // 负载统计数据
        if (stats.load > 0) {
            confirmMessage += `- 负载统计数据: ${stats.load} 条记录\n`;
        }

        // 网络质量监控数据
        if (stats.tcping > 0) {
            confirmMessage += `- 网络质量监控数据: ${stats.tcping} 条记录\n`;
        }

        confirmMessage += "\n此操作不可恢复，请谨慎操作！";

        if (!confirm(confirmMessage)) return;

        // 执行删除
        startloading();
        const deleteRes = await postjson(`/admin/servers/${sid}/del`);
        endloading();

        if (deleteRes.status) {
            notice('删除成功');
            // T056: 刷新节点限制状态
            if (nodeLimitStatusComponent) {
                nodeLimitStatusComponent.refresh();
            }
            location.reload();
        } else {
            notice(deleteRes.data || '删除失败');
        }
    } catch(error) {
        endloading();
        notice('操作失败: ' + (error.message || '未知错误'));
    }
}

/**
 * 清理孤立数据
 */
async function cleanupOrphanData() {
    try {
        // 先获取孤立数据统计
        startloading();
        const statsRes = await postjson('/admin/servers/orphan-stats');
        endloading();

        if (!statsRes.status) {
            notice(statsRes.data || '获取孤立数据统计失败');
            return;
        }

        const stats = statsRes.data;
        console.log('孤立数据统计:', stats);

        if (stats.total === 0) {
            notice('没有发现孤立数据，无需清理');
            return;
        }

        // 构建确认消息
        let confirmMessage = `发现 ${stats.total} 条孤立数据，确认清理？\n\n`;
        confirmMessage += "将清理以下数据:\n";

        const tableNames = {
            'traffic': '流量统计',
            'lt': '长期流量',
            'load_m': '分钟级负载',
            'load_h': '小时级负载',
            'load_archive': '负载归档',
            'tcping_m': '分钟级网络监控',
            'tcping_5m': '5分钟级网络监控',
            'tcping_h': '小时级网络监控',
            'tcping_d': '天级网络监控',
            'tcping_month': '月级网络监控',
            'tcping_archive': '网络监控归档',
            'traffic_calibration': '流量校准'
        };

        for (const [table, count] of Object.entries(stats)) {
            if (table !== 'total' && count > 0) {
                const displayName = tableNames[table] || table;
                confirmMessage += `- ${displayName}: ${count} 条\n`;
            }
        }

        confirmMessage += "\n⚠️ 此操作不可恢复，请确认！";

        if (!confirm(confirmMessage)) return;

        // 执行清理
        startloading();
        const cleanupRes = await postjson('/admin/servers/cleanup-orphans');
        endloading();

        if (cleanupRes.status) {
            const results = cleanupRes.data;
            notice(`清理完成！共清理了 ${results.total} 条孤立数据`);
            console.log('清理结果:', results);
        } else {
            notice(cleanupRes.data || '清理失败');
        }
    } catch(error) {
        endloading();
        notice('清理操作失败: ' + (error.message || '未知错误'));
    }
}
</script>
{%endblock%}