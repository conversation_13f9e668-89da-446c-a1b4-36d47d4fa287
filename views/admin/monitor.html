{% set title = "网络监控配置" %}
{% set admin = true %}
{% set fluid = true %}
{% extends "../base.html" %}
{% import "../components/table-macros.html" as table %}

{% block head %}
{{ super() }}
<script src="/js/admin/monitor-api.js"></script>
<script src="/js/admin/monitor-page.js"></script>
{% endblock %}

{% block content %}
<!-- 引入侧边栏 -->
{% include "admin/sidebar.html" %}

<!-- 主内容区域 -->
<div class="flex-1 space-y-6">

        <!-- 页面标题卡片 - 优化移动端布局 -->
        <div class="admin-card">
            <div class="admin-card-header">
                <!-- 标题区域 - 作为直接子元素 -->
                <div class="admin-page-header">
                    <div class="admin-page-title">
                        <div class="admin-title-icon admin-icon-purple">
                            <i class="ti ti-network text-lg text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <div class="admin-title-text">
                            <h1>网络监控配置</h1>
                            <p class="text-xs">Network Monitoring Configuration</p>
                        </div>
                    </div>
                </div>
                
                <!-- 统计信息和操作按钮区域 - 作为兄弟元素，添加顶部间距 -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mt-4">
                    <!-- Mini Dashboard 统计卡片 - 移动端优化 -->
                    <div id="stats-info" class="grid grid-cols-3 gap-2 sm:flex sm:gap-3 w-full sm:w-auto">
                        <!-- 总目标数 - 移动端优化 -->
                        <div class="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 sm:py-1.5 bg-slate-100 dark:bg-slate-700/50 rounded-lg text-center sm:text-left">
                            <i class="ti ti-target text-xs sm:text-sm text-slate-600 dark:text-slate-400"></i>
                            <div class="flex flex-col sm:flex-row sm:items-baseline gap-0.5 sm:gap-1">
                                <span id="total-count" class="text-base sm:text-sm font-semibold text-slate-800 dark:text-slate-200">0</span>
                                <span class="text-[10px] sm:text-xs text-slate-500 dark:text-slate-400">目标</span>
                            </div>
                        </div>
                        
                        <!-- 在线数 - 移动端优化 -->
                        <div class="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 sm:py-1.5 bg-green-50 dark:bg-green-900/20 rounded-lg text-center sm:text-left">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <div class="flex flex-col sm:flex-row sm:items-baseline gap-0.5 sm:gap-1">
                                <span id="online-count" class="text-base sm:text-sm font-semibold text-green-700 dark:text-green-400">0</span>
                                <span class="text-[10px] sm:text-xs text-green-600 dark:text-green-400/80">在线</span>
                            </div>
                        </div>
                        
                        <!-- 离线数 - 移动端优化 -->
                        <div class="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 sm:py-1.5 bg-red-50 dark:bg-red-900/20 rounded-lg text-center sm:text-left">
                            <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                            <div class="flex flex-col sm:flex-row sm:items-baseline gap-0.5 sm:gap-1">
                                <span id="offline-count" class="text-base sm:text-sm font-semibold text-red-700 dark:text-red-400">0</span>
                                <span class="text-[10px] sm:text-xs text-red-600 dark:text-red-400/80">离线</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 刷新按钮 - 移动端全宽 -->
                    <button id="btn-refresh" class="w-full sm:w-auto inline-flex items-center justify-center gap-2 px-4 py-2.5 sm:py-2 text-sm font-medium 
                                                   bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-300 
                                                   border border-slate-300/60 dark:border-slate-600/60 
                                                   rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600/80 
                                                   focus:outline-none focus:ring-2 focus:ring-slate-500/50 
                                                   transition-all duration-200 backdrop-blur-sm active:scale-95">
                        <i class="ti ti-refresh text-sm"></i>
                        <span>刷新</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 地区管理区域 - 可折叠Mini设计 -->
        <div class="admin-card">
            <!-- 标题栏 - 可点击折叠 -->
            <div class="admin-list-header cursor-pointer" onclick="toggleRegionsPanel()">
                <div class="flex items-center justify-between py-2 px-4">
                    <!-- 左侧：折叠图标 + 标题 + 统计 -->
                    <div class="flex items-center gap-2">
                        <i id="regions-chevron" class="ti ti-chevron-right transition-transform duration-200 text-slate-400"></i>
                        <i class="ti ti-map-2 text-blue-600 dark:text-blue-400"></i>
                        <span class="text-sm font-medium text-slate-700 dark:text-slate-300">地区管理</span>
                        <span id="regions-total" class="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 px-2 py-0.5 rounded-full">0 个地区</span>
                    </div>
                    
                    <!-- 右侧：新增按钮 -->
                    <button id="btn-add-region-mini" class="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors"
                            title="新增地区">
                        <i class="ti ti-plus text-xs"></i>
                        <span>新增</span>
                    </button>
                </div>
            </div>

            <!-- 折叠内容 - Mini卡片区域 -->
            <div id="regions-panel" class="hidden border-t border-slate-200 dark:border-slate-700 px-4 py-3">
                <div id="regions-container" class="flex gap-2 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-slate-300 dark:scrollbar-thumb-slate-600 scrollbar-track-slate-100 dark:scrollbar-track-slate-700">
                    <!-- Mini地区卡片将通过JavaScript动态加载 -->
                    <div class="flex items-center justify-center min-w-[120px] h-12 text-center">
                        <div class="flex items-center gap-2">
                            <i class="ti ti-loader-2 text-sm text-slate-400 animate-spin"></i>
                            <span class="text-xs text-slate-500">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 监控目标列表 -->
        <div class="admin-card overflow-hidden">
            <!-- 新增：列表标题区域 -->
            <div class="admin-list-header">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 w-full">
                    <!-- 标题部分 -->
                    <div class="flex items-center gap-2">
                        <div class="admin-title-icon admin-icon-purple">
                            <i class="ti ti-list text-lg text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <div class="admin-list-title">
                            <h2 class="text-base">监控目标列表</h2>
                            <p class="text-xs">管理网络监控目标和检测配置</p>
                        </div>
                    </div>
                    
                    <!-- 操作按钮部分 - 移动端换行，桌面端右对齐 -->
                    <div class="flex gap-2 sm:gap-3">
                        <button id="btn-add-region" class="flex-1 sm:flex-initial inline-flex items-center justify-center gap-2 px-4 py-2.5 sm:py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-800 rounded-lg shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-blue-600 dark:hover:bg-blue-500 dark:focus:bg-blue-500 dark:active:bg-blue-700">
                            <i class="ti ti-map-pin-plus text-sm"></i>
                            <span class="hidden sm:inline">新增地区</span>
                            <span class="sm:hidden">地区</span>
                        </button>
                        <button id="btn-add-target" class="flex-1 sm:flex-initial inline-flex items-center justify-center gap-2 px-4 py-2.5 sm:py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-800 rounded-lg shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-blue-600 dark:hover:bg-blue-500 dark:focus:bg-blue-500 dark:active:bg-blue-700">
                            <i class="ti ti-circle-plus text-sm"></i>
                            <span class="hidden sm:inline">新增目标</span>
                            <span class="sm:hidden">目标</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 批量操作栏 - 选中项时显示 -->
            <div id="batch-operations-bar" class="hidden">
                <div class="flex items-center justify-between px-4 sm:px-6 py-3 bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200/60 dark:border-blue-800/60 transition-all duration-300">
                    <div class="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
                        <i class="ti ti-checkbox text-lg"></i>
                        <span>已选择 <span id="selected-count" class="font-semibold">0</span> 项</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <button id="btn-batch-delete" class="inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:bg-red-700 active:bg-red-800 rounded-lg shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 dark:bg-red-600 dark:hover:bg-red-500 dark:focus:bg-red-500 dark:active:bg-red-700">
                            <i class="ti ti-trash-x text-sm"></i>
                            <span class="hidden sm:inline">批量删除</span>
                            <span class="sm:hidden">删除</span>
                        </button>
                        <button id="btn-cancel-selection" class="inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-blue-700 dark:text-blue-300 bg-white dark:bg-slate-800 hover:bg-blue-100 dark:hover:bg-blue-800/30 rounded-lg border border-blue-200 dark:border-blue-700/30 transition-all duration-200">
                            <i class="ti ti-x text-sm"></i>
                            <span class="hidden sm:inline">取消选择</span>
                            <span class="sm:hidden">取消</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 搜索筛选区域 -->
            <div class="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 px-4 sm:px-6 py-3 bg-white dark:bg-slate-800 border-b border-slate-200/60 dark:border-slate-800/60">
                <!-- 搜索框 -->
                <div class="flex-1 relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="ti ti-search text-sm text-slate-400 dark:text-slate-500"></i>
                    </div>
                    <input id="search-input" type="text" placeholder="搜索目标名称、地址或描述..."
                           class="w-full pl-10 pr-4 py-2 text-sm bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-700/30 rounded-md text-slate-900 dark:text-slate-100 placeholder-slate-500 dark:placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                </div>

                <!-- 筛选器组 -->
                <div class="flex items-center gap-2 sm:gap-3 w-full sm:w-auto">
                    <!-- 地区筛选 -->
                    <div class="relative flex-1 sm:flex-none">
                        <select id="region-filter" class="appearance-none bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-700/30 rounded-md px-3 py-2 pr-8 text-sm text-slate-700 dark:text-slate-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors cursor-pointer w-full sm:min-w-[120px]">
                            <option value="">全部地区</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                            <i class="ti ti-chevron-down text-sm text-slate-400"></i>
                        </div>
                    </div>

                    <!-- 状态筛选 -->
                    <div class="relative flex-1 sm:flex-none">
                        <select id="status-filter" class="appearance-none bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-700/30 rounded-md px-3 py-2 pr-8 text-sm text-slate-700 dark:text-slate-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors cursor-pointer w-full sm:min-w-[120px]">
                            <option value="">全部状态</option>
                            <option value="online">在线</option>
                            <option value="offline">离线</option>
                            <option value="warning">异常</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                            <i class="ti ti-chevron-down text-sm text-slate-400"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 监控目标表格 - 使用新的表格组件 -->
            {{ table.adminTable(
                id="monitor-targets",
                columns=[
                    {"key": "name", "label": "目标名称", "sortable": true},
                    {"key": "address", "label": "地址:端口", "sortable": false},
                    {"key": "region", "label": "所属地区", "sortable": false},
                    {"key": "node", "label": "绑定节点", "sortable": false},
                    {"key": "status", "label": "状态", "sortable": false},
                    {"key": "actions", "label": "操作", "sortable": false}
                ],
                hasCheckbox=true,
                hasPagination=true,
                colorScheme="slate"
            ) }}
        </div>

    </div>
</div>

<!-- 地区管理模态框 -->
<div id="region-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 hidden">
    <div id="region-modal-backdrop" class="absolute inset-0"></div>
    <div class="bg-white dark:bg-slate-800 rounded-xl p-6 w-full max-w-md mx-4 relative border border-slate-200 dark:border-slate-700">
        <h3 id="region-modal-title" class="text-lg font-semibold mb-4 text-slate-800 dark:text-slate-200">添加监控地区</h3>
        <form id="region-form" class="space-y-4">
            <input type="hidden" id="region-id">
            <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">地区名称</label>
                <input type="text" id="region-name" class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-800 dark:text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500" placeholder="例如: 华北地区" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">地区描述</label>
                <textarea id="region-description" class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-800 dark:text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 h-20 resize-none" placeholder="可选，描述这个监控地区"></textarea>
            </div>
            <div class="flex justify-end gap-3 pt-4">
                <button type="button" id="region-cancel" class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-700">
                    取消
                </button>
                <button type="submit" class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-800 rounded-lg shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-blue-600 dark:hover:bg-blue-500 dark:focus:bg-blue-500 dark:active:bg-blue-700">
                    保存
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 目标管理模态框 -->
<div id="target-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 hidden">
    <div id="target-modal-backdrop" class="absolute inset-0"></div>
    <div class="bg-white dark:bg-slate-800 rounded-xl p-6 w-full max-w-2xl mx-4 relative border border-slate-200 dark:border-slate-700 max-h-[80vh] overflow-y-auto scrollbar-thin scrollbar-thumb-slate-300 dark:scrollbar-thumb-slate-600 scrollbar-track-slate-100 dark:scrollbar-track-slate-700">
        <h3 id="target-modal-title" class="text-lg font-semibold mb-4 text-slate-800 dark:text-slate-200">添加监控目标</h3>
        <form id="target-form" class="space-y-4">
            <input type="hidden" id="target-id">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">目标名称</label>
                    <input type="text" id="target-name" class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-800 dark:text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="例如: API服务器" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">所属地区</label>
                    <select id="target-region-id" class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        <option value="">请选择地区</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">主机地址</label>
                    <input type="text" id="target-host" class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-800 dark:text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="例如: api.example.com" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">端口</label>
                    <input type="number" id="target-port" class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-800 dark:text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="80" min="1" max="65535">
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">检测类型</label>
                <div class="flex gap-4">
                    <label class="flex items-center">
                        <input type="radio" name="target-test-type" value="tcping" class="mr-2" checked>
                        <span class="text-slate-700 dark:text-slate-300">TCP连接</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="target-test-type" value="ping" class="mr-2">
                        <span class="text-slate-700 dark:text-slate-300">Ping</span>
                    </label>
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">描述</label>
                <textarea id="target-description" class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-800 dark:text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 h-20 resize-none" placeholder="可选，描述这个监控目标"></textarea>
            </div>

            <div class="flex justify-end">
                <button type="button" id="open-node-drawer" class="inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-blue-700 dark:text-blue-300 bg-white dark:bg-slate-800 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700/30 transition-all">
                    <i class="ti ti-device-lan text-sm"></i>
                    选择节点
                </button>
            </div>

            <div class="flex justify-end gap-3 pt-4">
                <button type="button" id="target-cancel" class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-700">
                    取消
                </button>
                <button type="submit" class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-800 rounded-lg shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-blue-600 dark:hover:bg-blue-500 dark:focus:bg-blue-500 dark:active:bg-blue-700">
                    保存
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 节点选择抽屉 -->
<div id="node-selection-drawer" class="fixed inset-y-0 right-0 z-50 hidden">
    <!-- 背景遮罩 -->
    <div id="node-drawer-backdrop" class="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300"></div>

    <!-- 抽屉内容 -->
    <div id="node-drawer-content" class="absolute right-0 h-full bg-white dark:bg-slate-800 shadow-2xl z-50 flex flex-col"
         style="transform: translateX(100%); transition: transform 300ms ease-out;">
        <!-- 抽屉头部 -->
        <div class="flex-shrink-0 bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 px-4 sm:px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                    <h2 class="text-base font-semibold text-slate-900 dark:text-white">选择监控节点</h2>
                    <p id="drawer-target-name" class="text-xs text-slate-600 dark:text-slate-400 mt-1 truncate"></p>
                </div>
                <button id="close-node-drawer" class="inline-flex items-center justify-center p-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-gray-100 flex-shrink-0 ml-2">
                    <i class="ti ti-x text-sm text-slate-500 dark:text-slate-400"></i>
                </button>
            </div>

            <!-- 批量操作工具栏 - 响应式布局 -->
            <div class="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-2 mt-4">
                <!-- 操作按钮组 -->
                <div class="flex items-center gap-2 flex-wrap">
                    <button id="select-all-nodes" class="flex-1 sm:flex-initial inline-flex items-center justify-center gap-1 px-3 py-2 sm:px-2.5 sm:py-1 text-xs font-medium text-slate-700 bg-white border border-slate-300/60 rounded-md shadow-sm hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-all duration-200 dark:bg-slate-800 dark:text-slate-200 dark:border-slate-700/30 dark:hover:bg-slate-700">
                        全选
                    </button>
                    <button id="deselect-all-nodes" class="flex-1 sm:flex-initial inline-flex items-center justify-center gap-1 px-3 py-2 sm:px-2.5 sm:py-1 text-xs font-medium text-slate-700 bg-white border border-slate-300/60 rounded-md shadow-sm hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-all duration-200 dark:bg-slate-800 dark:text-slate-200 dark:border-slate-700/30 dark:hover:bg-slate-700">
                        反选
                    </button>
                    <button id="select-online-nodes" class="flex-1 sm:flex-initial inline-flex items-center justify-center gap-1 px-3 py-2 sm:px-2.5 sm:py-1 text-xs font-medium text-slate-700 bg-white border border-slate-300/60 rounded-md shadow-sm hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-all duration-200 dark:bg-slate-800 dark:text-slate-200 dark:border-slate-700/30 dark:hover:bg-slate-700">
                        在线
                    </button>
                </div>

                <!-- 计数显示 -->
                <div class="text-xs text-slate-600 dark:text-slate-400 sm:ml-auto text-center sm:text-left">
                    已选择: <span id="selected-nodes-count" class="font-medium text-blue-600 dark:text-blue-400">0</span> / <span id="total-nodes-count" class="font-medium">0</span> 个节点
                </div>
            </div>

            <!-- 筛选器面板 -->
            <div class="filter-panel mt-3 p-3 bg-slate-50 dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700">
                <!-- 分组筛选 -->
                <div class="filter-section mb-2">
                    <button class="filter-toggle w-full flex items-center justify-between text-xs font-semibold py-1 text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100 transition-colors" data-target="group-filters">
                        <span>分组</span>
                        <i class="ti ti-chevron-down text-sm transform transition-transform"></i>
                    </button>
                    <div class="filter-content mt-1" id="group-filters-container">
                        <div class="overflow-x-auto">
                            <div class="flex gap-1 pb-1" id="group-filters" style="min-width: max-content;">
                                <div class="text-xs text-slate-500 dark:text-slate-400">加载中...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 国家筛选 -->
                <div class="filter-section mb-2">
                    <button class="filter-toggle w-full flex items-center justify-between text-xs font-semibold py-1 text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100 transition-colors" data-target="country-filters">
                        <span>国家</span>
                        <i class="ti ti-chevron-down text-sm transform transition-transform"></i>
                    </button>
                    <div class="filter-content mt-1 hidden" id="country-filters-container">
                        <div class="overflow-x-auto">
                            <div class="flex gap-1 pb-1" id="country-filters" style="min-width: max-content;">
                                <div class="text-xs text-slate-500 dark:text-slate-400">加载中...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标签筛选 -->
                <div class="filter-section mb-2">
                    <button class="filter-toggle w-full flex items-center justify-between text-xs font-semibold py-1 text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100 transition-colors" data-target="tag-filters">
                        <span>标签</span>
                        <i class="ti ti-chevron-down text-sm transform transition-transform"></i>
                    </button>
                    <div class="filter-content mt-1 hidden" id="tag-filters-container">
                        <div class="overflow-x-auto">
                            <div class="flex gap-1 pb-1" id="tag-filters" style="min-width: max-content;">
                                <div class="text-xs text-slate-500 dark:text-slate-400">加载中...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 筛选操作 -->
                <div class="filter-actions flex justify-between items-center pt-2 border-t border-slate-200 dark:border-slate-700">
                    <div class="flex gap-1">
                        <button id="clear-filters" class="inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-700">
                            清除
                        </button>
                        <button id="show-online-only" class="inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-700">
                            在线
                        </button>
                    </div>
                    <span id="filter-count" class="text-xs text-slate-600 dark:text-slate-400 font-medium">
                        显示所有节点
                    </span>
                </div>
            </div>
        </div>

        <!-- 抽屉主体 -->
        <div class="flex-1 overflow-y-auto min-h-0">
            <div id="node-groups-container" class="p-4 sm:p-6 pb-12 sm:pb-16 space-y-3 sm:space-y-4">
                <!-- 节点分组将通过JavaScript动态渲染 -->
                <div class="flex items-center justify-center h-64">
                    <div class="text-center">
                        <i class="ti ti-cloud-download text-4xl text-slate-300 dark:text-slate-600 mb-2"></i>
                        <p class="text-sm text-slate-600 dark:text-slate-400">正在加载节点数据...</p>
                        <p class="text-xs text-slate-500 dark:text-slate-500 mt-1">请稍候</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 抽屉底部操作栏 -->
        <div class="flex-shrink-0 bg-white dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700 px-4 sm:px-6 py-3 flex justify-end gap-2">
            <button id="save-node-selection" class="inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg shadow-sm transition-colors">
                <i class="ti ti-check text-sm"></i>
                保存选择
            </button>
        </div>


    </div>
</div>

<!-- 通知函数 -->
<script>
    // 全局通知函数
    window.showNotification = function(message, type = 'info') {
        const notification = document.createElement('div');
        const colorClasses = {
            error: 'bg-red-500',
            success: 'bg-green-500',
            warning: 'bg-orange-500',
            info: 'bg-blue-500'
        };

        notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white text-sm max-w-sm ${colorClasses[type] || colorClasses.info}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    };

    // 动态样式类定义（用于 JavaScript 创建的元素）
    window.cardStyles = {
        // 分类卡片样式
        categoryCard: 'w-40 h-28 sm:w-44 sm:h-32 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 active:scale-95 relative group',
        categoryCardExpanded: 'ring-2 ring-purple-300 dark:ring-purple-700',
        categoryContent: 'p-4 h-full flex flex-col justify-center items-center text-white text-center',
        categoryIcon: 'text-2xl mb-2 opacity-90',
        categoryTitle: 'text-sm font-semibold mb-1 leading-tight',
        categoryCount: 'text-xs opacity-75 bg-white/20 px-2 py-0.5 rounded-full',
        expandIndicator: 'absolute top-2 right-2 transition-transform duration-300',
        expandIndicatorExpanded: 'rotate-180',
        actionButtons: 'absolute bottom-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200',
        actionBtn: 'w-6 h-6 bg-white/20 hover:bg-white/30 rounded flex items-center justify-center text-white text-xs transition-all duration-200 hover:scale-110 active:scale-95',

        // 目标卡片样式
        targetCard: 'w-36 h-24 sm:w-40 sm:h-28 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 active:scale-95 relative group',
        targetCardCollapsed: 'bg-gradient-to-br from-blue-50 to-blue-100 border-2 border-blue-200 dark:from-blue-900/20 dark:to-blue-800/20 dark:border-blue-700/40 text-slate-800 dark:text-slate-200',
        targetCardExpanded: 'bg-gradient-to-br from-blue-500 to-blue-600 text-white',
        targetContent: 'p-3 h-full flex flex-col justify-center items-center text-center',
        targetIcon: 'text-xl mb-1 opacity-90',
        targetTitle: 'text-xs font-semibold mb-1 leading-tight',
        targetHost: 'text-xs opacity-75 mb-1 break-all',
        targetCount: 'text-xs px-2 py-0.5 rounded-full',
        targetCountCollapsed: 'bg-blue-200 dark:bg-blue-800/40',
        targetCountExpanded: 'bg-white/20',
        targetExpandIndicator: 'absolute top-1.5 right-1.5 transition-transform duration-300',
        targetExpandIndicatorExpanded: 'rotate-180',
        targetActionButtons: 'absolute bottom-1.5 right-1.5 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200',
        targetActionBtn: 'w-5 h-5 rounded flex items-center justify-center text-xs transition-all duration-200 hover:scale-110 active:scale-95',
        targetActionBtnCollapsed: 'bg-blue-200/60 hover:bg-blue-300 text-blue-700',
        targetActionBtnExpanded: 'bg-white/20 hover:bg-white/30 text-white',
        targetSaveBtn: 'bg-orange-400/80 hover:bg-orange-500 text-white',

        // 节点卡片样式
        nodeCard: 'w-full h-20 sm:h-24 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-0.5 active:scale-95',
        nodeCardUnselected: 'bg-white border-2 border-slate-200 dark:bg-slate-800 dark:border-slate-700 text-slate-800 dark:text-slate-200',
        nodeCardSelected: 'bg-slate-50 border-2 border-slate-300 dark:bg-slate-900 dark:border-slate-600 text-slate-800 dark:text-slate-200',
        nodeContent: 'p-2 h-full flex flex-col justify-center items-center text-center',
        nodeCheckbox: 'w-4 h-4 mb-1 accent-blue-500 rounded transition-all',
        nodeIcon: 'text-sm mb-1 opacity-80',
        nodeTitle: 'text-xs font-medium leading-tight mb-0.5',
        nodeRegion: 'text-xs opacity-75 break-all',
        nodeStatus: 'text-xs mt-1 px-1.5 py-0.5 rounded-full',
        nodeStatusUnselected: 'bg-slate-200 text-slate-600 dark:bg-slate-600 dark:text-slate-300',
        nodeStatusSelected: 'bg-slate-300 text-slate-700 dark:bg-slate-500 dark:text-slate-200',

        // 连接线样式
        levelConnector: 'w-0.5 h-12 sm:h-16 bg-gradient-to-b from-purple-500 to-blue-500 transition-all duration-300',
        targetConnector: 'w-0.5 h-12 sm:h-16 bg-gradient-to-b from-blue-500 to-green-500 transition-all duration-300',

        // 动画类
        cardEnter: 'animate-cardEnter'
    };
</script>

<!-- 自定义动画和样式 -->
<style>
    /* 自定义滚动条 - 使用 Tailwind 插件 */
    .scrollbar-thin::-webkit-scrollbar {
        width: 4px;
    }

    /* 卡片进入动画 */
    @keyframes cardEnter {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-cardEnter {
        animation: cardEnter 0.3s ease-out;
    }

    /* 连接线样式 - 备用定义（主要样式在 monitor.css 中） */
    .level-connector {
        width: 0.125rem;
        height: 1.5rem;
        background-image: linear-gradient(to bottom, #8b5cf6, #3b82f6);
        transition: all 0.3s ease;
    }

    @media (min-width: 640px) {
        .level-connector {
            height: 2rem;
        }
    }

    .target-connector {
        width: 0.125rem;
        height: 1.5rem;
        background-image: linear-gradient(to bottom, #3b82f6, #22c55e);
        transition: all 0.3s ease;
    }

    @media (min-width: 640px) {
        .target-connector {
            height: 2rem;
        }
    }

    /* 卡片动画类 */
    .card-enter {
        animation: cardEnter 0.3s ease-out;
    }

    /* Mini地区卡片优化样式 */
    .region-mini-card {
        cursor: pointer;
        flex-shrink: 0;
    }
    
    .region-mini-card:hover {
        border-color: #3b82f6;
        transform: translateY(-1px);
    }
    
    .region-actions {
        backdrop-filter: blur(4px);
        background: rgba(255, 255, 255, 0.9);
        border-radius: 4px;
        padding: 1px;
    }
    
    .dark .region-actions {
        background: rgba(30, 41, 59, 0.9);
    }
    
    /* 自定义滚动条样式优化 */
    /* 滚动条样式现在使用全局样式，无需在此重复定义 */
</style>

<!-- Monitor 专用样式 -->
<!-- <link rel="stylesheet" href="/css/components/monitor.css"> -->
<!-- 拆分后的监控样式文件 -->
<link rel="stylesheet" href="/css/components/charts.css">
<link rel="stylesheet" href="/css/components/metrics.css">
<link rel="stylesheet" href="/css/components/dashboard-monitor.css">

<!-- JavaScript -->
<!-- 引入表格管理工具 -->
<script src="/js/admin-table-utils.js"></script>
<script src="/js/monitor-table.js"></script>

</div>
{% endblock %}
