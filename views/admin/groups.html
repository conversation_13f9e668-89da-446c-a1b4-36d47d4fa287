{% set title = "分组管理" %}
{%set admin = true%}
{%set fluid = true%}
{% extends "../base.html" %}

{%block content%}
<!-- 页面容器 - 简化布局 -->
<div>
    <!-- 引入侧边栏 -->
    {% include "admin/sidebar.html" %}

    <!-- 主内容区域 - 独立卡片布局 -->
    <div class="flex-1 space-y-6">
        
        <!-- 页面标题卡片 - 独立设计 -->
        <div class="admin-card">
            <div class="admin-card-header">
                <div class="admin-page-header">
                    <div class="admin-page-title">
                        <div class="admin-title-icon admin-icon-blue">
                            <i class="ti ti-folder text-lg text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div class="admin-title-text">
                            <h1>分组管理</h1>
                            <p class="text-xs">Group Management</p>
                        </div>
                    </div>
                    <!-- 新增分组按钮 - iPhone风格 -->
                    <button onclick="showAddDialog()"
                            class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95">
                        <i class="ti ti-plus text-sm"></i>
                        <span>新增分组</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 分组列表卡片 - 独立设计 -->
        <div class="admin-card">
            <!-- 卡片头部 - 使用标准admin-list-header类 -->
            <div class="admin-list-header flex items-center gap-3">
                <div class="w-8 h-8 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/30 rounded-lg shadow-sm flex-shrink-0 border border-blue-200/50 dark:border-blue-700/30 flex items-center justify-center">
                    <i class="ti ti-list text-base text-blue-600 dark:text-blue-400"></i>
                </div>
                <div>
                    <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">分组列表</h2>
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">拖拽调整分组顺序</p>
                </div>
            </div>

            <!-- 分组列表容器 - iPhone风格图表容器 -->
            <div class="p-4">
                <div class="relative">
                    <!-- iPhone风格图表背景 -->
                    <div class="absolute inset-0 bg-gradient-to-br from-slate-50/80 via-white/60 to-slate-100/40 dark:from-slate-800/40 dark:via-slate-800/20 dark:to-slate-900/30 rounded-2xl border border-slate-200/40 dark:border-slate-700/30 backdrop-blur-sm shadow-inner"></div>
                    
                    <!-- 黄金比例容器 -->
                    <div class="relative bg-white dark:bg-slate-800" style="aspect-ratio: 1.618 / 1; min-height: 300px;">
                        <!-- 内容区域 -->
                        <div class="p-4 h-full overflow-y-auto">
                            <div id="groupList" class="space-y-3">
            {%for group in groups%}
                                <div class="group bg-white/80 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/60 dark:border-slate-700/40 rounded-xl p-4 hover:shadow-md transition-all duration-200" data-id="{{group.id}}">
                                    <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                                            <div class="drag-handle w-6 h-6 bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-600 rounded-lg flex items-center justify-center border border-slate-200/50 dark:border-slate-600/50 cursor-grab active:cursor-grabbing">
                                                <i class="ti ti-grip-vertical text-sm text-slate-600 dark:text-slate-300"></i>
                                            </div>
                                            <div class="w-6 h-6 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/40 dark:to-purple-800/30 rounded-lg flex items-center justify-center border border-purple-200/50 dark:border-purple-700/30">
                                                <i class="ti ti-folder text-sm text-purple-600 dark:text-purple-400"></i>
                                            </div>
                    <div>
                                                <h4 class="text-sm font-medium text-slate-800 dark:text-slate-200">{{group.name}}</h4>
                                                <p class="text-xs text-slate-500 dark:text-slate-400">{{group.server_count}} 个服务器</p>
                    </div>
                </div>
                                        <div class="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    {%if group.id != 'default'%}
                                            <button class="w-8 h-8 bg-white/80 dark:bg-slate-700/60 text-slate-600 dark:text-slate-300 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-slate-500/50 transition-all duration-200 backdrop-blur-sm border border-slate-200/60 dark:border-slate-600/60 active:scale-95 flex items-center justify-center"
                                                    onclick="showEditDialog('{{group.id}}', '{{group.name}}')"
                                                    title="编辑分组">
                                                <i class="ti ti-edit text-sm"></i>
                    </button>
                                            <button class="w-8 h-8 bg-white/80 dark:bg-slate-700/60 text-slate-600 dark:text-slate-300 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400 focus:outline-none focus:ring-2 focus:ring-red-500/50 transition-all duration-200 backdrop-blur-sm border border-slate-200/60 dark:border-slate-600/60 active:scale-95 flex items-center justify-center"
                                                    onclick="deleteGroup('{{group.id}}')"
                                                    title="删除分组">
                                                <i class="ti ti-trash text-sm"></i>
                    </button>
                    {%endif%}
                                        </div>
                                    </div>
                                </div>
                                {%endfor%}
                            </div>

                            <!-- 无数据提示 - iPhone风格 -->
                            {%if not groups or groups.length == 0%}
                            <div class="text-center py-12">
                                <div class="flex flex-col items-center gap-4">
                                    <div class="w-16 h-16 bg-gradient-to-br from-slate-100/80 to-slate-200/60 dark:from-slate-800/60 dark:to-slate-700/40 rounded-2xl border border-slate-200/50 dark:border-slate-700/30 flex items-center justify-center">
                                        <i class="ti ti-folder-open text-2xl text-slate-400"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-medium text-slate-600 dark:text-slate-300">暂无分组</h4>
                                        <p class="text-sm text-slate-500 dark:text-slate-400 mt-1">点击上方按钮创建第一个分组</p>
                                    </div>
                                </div>
                            </div>
                            {%endif%}
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- 添加/编辑分组弹窗 - iPhone风格设计 -->
<div id="groupDialog" class="fixed inset-0 bg-black/50 hidden items-center justify-center backdrop-blur-sm z-50">
    <div class="bg-white/95 dark:bg-slate-800/95 backdrop-blur-md rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden border border-slate-200/50 dark:border-slate-700/50">
        <!-- 模态框头部 - iPhone风格 -->
        <div class="flex items-center justify-between p-4 pb-3 border-b border-slate-200/60 dark:border-slate-700/40">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/40 dark:to-purple-800/30 rounded-lg border border-purple-200/50 dark:border-purple-700/30 flex items-center justify-center">
                    <i class="ti ti-folder text-base text-purple-600 dark:text-purple-400"></i>
                </div>
                <h3 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide" id="dialogTitle">新增分组</h3>
            </div>
            <button onclick="closeDialog()" class="w-8 h-8 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 hover:bg-slate-100/60 dark:hover:bg-slate-700/50 rounded-lg transition-all duration-200 active:scale-95 flex items-center justify-center">
                <i class="ti ti-x text-base"></i>
                    </button>
                </div>

        <!-- 模态框内容 -->
        <div class="p-4 space-y-4">
            <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">分组名称</label>
                <input type="text"
                       id="groupName"
                       class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm"
                       placeholder="请输入分组名称">
            </div>
        </div>

        <!-- 模态框底部 - iPhone风格 -->
        <div class="flex justify-end gap-3 p-4 pt-3 border-t border-slate-200/60 dark:border-slate-700/40 bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 backdrop-blur-sm">
            <button onclick="closeDialog()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-white/80 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-xl hover:bg-slate-50 dark:hover:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-slate-500/50 transition-all duration-200 backdrop-blur-sm active:scale-95">
                取消
            </button>
            <button onclick="saveGroup()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500/50 transition-all duration-200 active:scale-95">
                <i class="ti ti-device-floppy text-sm"></i>
                <span>保存</span>
            </button>
        </div>
    </div>
</div>
{%endblock%}

{%block js%}
<script src="/js/libs/sortable.min.js"></script>
<style>
/* 拖拽手柄样式 */
.drag-handle {
    position: relative;
    transition: all 0.2s ease;
}

.drag-handle:hover {
    background: linear-gradient(135deg, #60a5fa, #3b82f6) !important;
    transform: scale(1.1);
    border-color: #3b82f6 !important;
}

.drag-handle:hover i {
    color: white !important;
}

/* 拖拽状态样式 */
.sortable-ghost {
    opacity: 0.5;
    transform: rotate(2deg);
}

.sortable-chosen {
    transform: scale(1.05);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.sortable-drag {
    transform: rotate(2deg);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
}

/* 确保拖拽元素可见 */
.group {
    position: relative;
    z-index: 1;
}

.group:hover {
    z-index: 2;
}

.sortable-chosen {
    z-index: 999;
}

/* 添加提示文字 */
.drag-handle::after {
    content: '拖动';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%) scale(0);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    pointer-events: none;
    transition: transform 0.2s ease;
}

.drag-handle:hover::after {
    transform: translateX(-50%) scale(1);
}
</style>
<script>
// 分组管理
const groupDialog = {
    mode: 'add',
    currentId: null,

    show(mode = 'add', id = null, name = '') {
        this.mode = mode;
        this.currentId = id;
        document.getElementById('dialogTitle').textContent = mode === 'add' ? '新增分组' : '编辑分组';
        document.getElementById('groupName').value = name;
        document.getElementById('groupDialog').classList.remove('hidden');
        document.getElementById('groupDialog').classList.add('flex');
    },

    close() {
        document.getElementById('groupDialog').classList.add('hidden');
        document.getElementById('groupDialog').classList.remove('flex');
        document.getElementById('groupName').value = '';
    },

    async save() {
        const name = document.getElementById('groupName').value.trim();
        if (!name) {
            notice('请输入分组名称');
            return;
        }

        try {
            startloading();
            const url = this.mode === 'add' ? '/admin/groups/add' : `/admin/groups/${this.currentId}/edit`;
            const res = await postjson(url, { name });

            if (res.status) {
                notice('保存成功');
                this.close();
                location.reload();
            } else {
                notice(res.data || '保存失败');
            }
        } catch (error) {
            notice('操作失败');
        } finally {
            endloading();
        }
    }
};

// 显示添加对话框
function showAddDialog() {
    groupDialog.show('add');
}

// 显示编辑对话框
function showEditDialog(id, name) {
    groupDialog.show('edit', id, name);
}

// 关闭对话框
function closeDialog() {
    groupDialog.close();
}

// 保存分组
function saveGroup() {
    groupDialog.save();
}

// 删除分组
async function deleteGroup(id) {
    if (!confirm('确认删除此分组？组内服务器将被移动到默认分组。')) return;

    try {
        startloading();
        const res = await postjson(`/admin/groups/${id}/del`);

        if (res.status) {
            notice('删除成功');
            location.reload();
        } else {
            notice(res.data || '删除失败');
        }
    } catch (error) {
        notice('操作失败');
    } finally {
        endloading();
    }
}

// 初始化拖拽排序
document.addEventListener('DOMContentLoaded', function() {
    console.log('Groups page loaded, initializing drag sort...');

    const groupList = document.getElementById('groupList');
    if (!groupList) {
        console.error('Group list container not found');
        return;
    }

    if (typeof Sortable !== 'undefined') {
        console.log('SortableJS loaded, creating sortable instance...');
        const sortable = new Sortable(groupList, {
            animation: 150,
            ghostClass: 'opacity-50',
            chosenClass: 'scale-105',
            dragClass: 'rotate-2',
            handle: '.drag-handle', // 只有拖拽手柄可以拖拽
            forceFallback: false,
            fallbackOnBody: true,
            onStart: function(evt) {
                console.log('Drag started:', evt.item.dataset.id);
            },
            onEnd: async function(evt) {
                console.log('Drag ended:', evt.oldIndex, '->', evt.newIndex);
                const groups = Array.from(document.querySelectorAll('#groupList > div')).map(el => el.dataset.id);
                console.log('New order:', groups);

                try {
                    startloading();
                    const res = await postjson('/admin/groups/order', { groups });
                    if (res.status) {
                        notice('排序已保存');
                    } else {
                        notice(res.data || '保存失败');
                        location.reload();
                    }
                } catch (error) {
                    console.error('Save order error:', error);
                    notice('操作失败');
                    location.reload();
                } finally {
                    endloading();
                }
            }
        });
        console.log('Sortable instance created successfully');
    } else {
        console.error('SortableJS library not loaded');
    }
});
</script>
{%endblock%}