{%set title = "高级分析"%}
{%set admin=true%}
{%extends "../base.html"%}

{%block head%}
<!-- ECharts -->
<script src="/js/libs/echarts.min.js"></script>
<!-- AI Dashboard Styles -->
<link rel="stylesheet" href="/css/components/ai-dashboard.css">
{%endblock%}

{%block content%}
<!-- 页面容器 -->
<div>
    <!-- 引入侧边栏 -->
    {% include "admin/sidebar.html" %}

    <!-- 主内容区域 -->
    <div class="flex-1">
        <!-- 页面标题卡片 - 使用 Tailwind 类 -->
        <div class="bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl backdrop-blur-sm shadow-sm">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 p-4">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/40 dark:to-purple-800/30 rounded-lg shadow-sm flex-shrink-0 border border-purple-200/50 dark:border-purple-700/30 flex items-center justify-center">
                        <i class="ti ti-chart-line text-base text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div>
                        <h1 class="text-xl sm:text-lg font-bold text-slate-800 dark:text-slate-200 tracking-wide leading-tight">AI 智能分析</h1>
                        <p class="text-sm sm:text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">基于机器学习的服务器监控数据分析与预测</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Analytics 主界面 - 使用 Tailwind 类 -->
        <div class="bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl backdrop-blur-sm shadow-sm">
            <div id="ai-analytics-container" class="p-6">
                <!-- 工具栏 -->
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                    <div class="flex flex-wrap gap-3">
                        <button id="generate-report-btn" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95">
                            <i class="ti ti-chart-bar text-sm"></i>
                            <span>生成智能报告</span>
                        </button>
                        <button id="export-report-btn" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-slate-500 to-slate-600 hover:from-slate-600 hover:to-slate-700 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-slate-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none" disabled>
                            <i class="ti ti-download text-sm"></i>
                            <span>导出报告</span>
                        </button>
                        <button id="view-history-btn" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95">
                            <i class="ti ti-history text-sm"></i>
                            <span>历史报告</span>
                        </button>
                        <button id="ai-settings-btn" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95">
                            <i class="ti ti-settings text-sm"></i>
                            <span>AI 设置</span>
                        </button>
                    </div>
                    
                    <div class="flex items-center gap-2">
                        <label class="text-sm font-medium text-slate-700 dark:text-slate-300">时间范围:</label>
                        <select id="time-range-select" class="px-3 py-2 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-lg text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200">
                            <option value="1h">最近1小时</option>
                            <option value="6h">最近6小时</option>
                            <option value="24h" selected>最近24小时</option>
                            <option value="7d">最近7天</option>
                            <option value="30d">最近30天</option>
                        </select>
                    </div>
                </div>

                <!-- 概览卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/20 rounded-lg p-4 border border-green-200/50 dark:border-green-700/30">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-green-500/20 dark:bg-green-400/20 rounded-lg flex items-center justify-center">
                                <i class="ti ti-shield-check text-green-600 dark:text-green-400"></i>
                            </div>
                            <div class="flex-1">
                                <div class="text-xs text-green-700 dark:text-green-300 font-medium mb-1">整体健康度</div>
                                <div class="text-lg font-bold text-green-900 dark:text-green-100" id="overall-health">--</div>
                                <div class="text-xs text-green-600 dark:text-green-400" id="health-change">--</div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/20 rounded-lg p-4 border border-blue-200/50 dark:border-blue-700/30">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-blue-500/20 dark:bg-blue-400/20 rounded-lg flex items-center justify-center">
                                <i class="ti ti-server text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div class="flex-1">
                                <div class="text-xs text-blue-700 dark:text-blue-300 font-medium mb-1">监控服务器</div>
                                <div class="text-lg font-bold text-blue-900 dark:text-blue-100" id="total-servers">--</div>
                                <div class="text-xs text-blue-600 dark:text-blue-400" id="servers-change">--</div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/30 dark:to-amber-800/20 rounded-lg p-4 border border-amber-200/50 dark:border-amber-700/30">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-amber-500/20 dark:bg-amber-400/20 rounded-lg flex items-center justify-center">
                                <i class="ti ti-alert-triangle text-amber-600 dark:text-amber-400"></i>
                            </div>
                            <div class="flex-1">
                                <div class="text-xs text-amber-700 dark:text-amber-300 font-medium mb-1">告警数量</div>
                                <div class="text-lg font-bold text-amber-900 dark:text-amber-100" id="total-issues">--</div>
                                <div class="text-xs text-amber-600 dark:text-amber-400" id="issues-change">--</div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/30 dark:to-indigo-800/20 rounded-lg p-4 border border-indigo-200/50 dark:border-indigo-700/30">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-indigo-500/20 dark:bg-indigo-400/20 rounded-lg flex items-center justify-center">
                                <i class="ti ti-gauge text-indigo-600 dark:text-indigo-400"></i>
                            </div>
                            <div class="flex-1">
                                <div class="text-xs text-indigo-700 dark:text-indigo-300 font-medium mb-1">性能评分</div>
                                <div class="text-lg font-bold text-indigo-900 dark:text-indigo-100" id="performance-score">--</div>
                                <div class="text-xs text-indigo-600 dark:text-indigo-400" id="performance-change">--</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 报告显示区域 -->
                <div class="bg-gradient-to-br from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 rounded-xl p-6 border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">AI 分析报告</h3>
                        <span class="text-sm text-slate-500 dark:text-slate-400" id="last-updated">未生成</span>
                    </div>
                    
                    <div id="report-content" class="min-h-[200px]">
                        <div class="text-center py-12">
                            <i class="ti ti-chart-bar text-4xl text-slate-400 dark:text-slate-500 mb-3"></i>
                            <p class="text-slate-600 dark:text-slate-400">点击"生成智能报告"开始AI分析</p>
                        </div>
                    </div>
                </div>


                <!-- 推荐建议区域 -->
                <div id="recommendations-section" class="bg-gradient-to-br from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 rounded-xl p-6 border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm" style="display: none;">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">AI 优化建议</h3>
                    </div>
                    
                    <div id="recommendations-content">
                        <!-- 建议内容将由JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 历史报告列表 -->
                <div id="history-section" class="bg-gradient-to-br from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 rounded-xl p-6 border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm" style="display: none;">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">历史报告</h3>
                        <button id="close-history-btn" class="text-slate-400 hover:text-slate-600 dark:text-slate-500 dark:hover:text-slate-300 transition-colors">
                            <i class="ti ti-x"></i>
                        </button>
                    </div>
                    
                    <div id="history-stats" class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
                        <div class="bg-white/50 dark:bg-slate-700/30 rounded-lg p-3 text-center">
                            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400" id="total-reports-count">0</div>
                            <div class="text-xs text-slate-600 dark:text-slate-400">总报告数</div>
                        </div>
                        <div class="bg-white/50 dark:bg-slate-700/30 rounded-lg p-3 text-center">
                            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="reports-24h-count">0</div>
                            <div class="text-xs text-slate-600 dark:text-slate-400">24小时内</div>
                        </div>
                        <div class="bg-white/50 dark:bg-slate-700/30 rounded-lg p-3 text-center">
                            <div class="text-2xl font-bold text-green-600 dark:text-green-400" id="avg-score">0</div>
                            <div class="text-xs text-slate-600 dark:text-slate-400">平均评分</div>
                        </div>
                    </div>
                    
                    <div id="history-list" class="space-y-3 max-h-96 overflow-y-auto">
                        <!-- 历史报告列表将由JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 加载状态遮罩 -->
                <div id="loading-overlay" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center" style="display: none;">
                    <div class="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-2xl">
                        <div class="flex flex-col items-center gap-4">
                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
                            <p class="text-slate-800 dark:text-slate-200 font-medium">AI 正在分析数据...</p>
                        </div>
                    </div>
                </div>
                
                <!-- AI 设置模态框 -->
                <div id="ai-settings-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center" style="display: none;">
                    <div class="bg-white dark:bg-slate-800 rounded-xl shadow-2xl w-full max-w-lg mx-4">
                        <div class="border-b border-slate-200 dark:border-slate-700 px-6 py-4">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">AI 配置设置</h3>
                                <button id="close-ai-settings-btn" class="text-slate-400 hover:text-slate-600 dark:text-slate-500 dark:hover:text-slate-300 transition-colors">
                                    <i class="ti ti-x text-xl"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="p-6">
                            <form id="ai-settings-form">
                                <!-- 开关 -->
                                <div class="mb-6">
                                    <label class="flex items-center gap-3">
                                        <input type="checkbox" id="ai-enabled" class="w-5 h-5 text-indigo-600 bg-white dark:bg-slate-700 border-slate-300 dark:border-slate-600 rounded focus:ring-indigo-500 focus:ring-2">
                                        <span class="text-slate-700 dark:text-slate-300 font-medium">启用 AI 功能</span>
                                    </label>
                                </div>
                                
                                <!-- 服务名称 -->
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                        AI 服务名称
                                    </label>
                                    <input type="text" id="ai-name" class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="OpenAI">
                                </div>
                                
                                <!-- API 地址 -->
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                        API 地址
                                    </label>
                                    <input type="url" id="ai-api-base" class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="https://api.openai.com/v1">
                                </div>
                                
                                <!-- 模型 -->
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                        模型
                                    </label>
                                    <input type="text" id="ai-model" class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="gpt-3.5-turbo">
                                </div>
                                
                                <!-- API 密钥 -->
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                        API 密钥
                                    </label>
                                    <input type="password" id="ai-api-key" class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="留空不更改">
                                    <p class="text-xs text-slate-500 dark:text-slate-400 mt-1">密钥已加密存储，留空将保留现有密钥</p>
                                </div>
                                
                                <!-- 测试结果显示区 -->
                                <div id="ai-test-result" class="mb-4 p-3 rounded-lg" style="display: none;"></div>
                                
                                <!-- 按钮组 -->
                                <div class="flex justify-end gap-3">
                                    <button type="button" id="test-ai-connection-btn" class="px-4 py-2 text-sm font-medium text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30 hover:bg-indigo-100 dark:hover:bg-indigo-900/50 rounded-lg transition-colors">
                                        测试连接
                                    </button>
                                    <button type="button" id="cancel-ai-settings-btn" class="px-4 py-2 text-sm font-medium text-slate-600 dark:text-slate-400 bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 rounded-lg transition-colors">
                                        取消
                                    </button>
                                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 rounded-lg shadow-md hover:shadow-lg transition-all">
                                        保存设置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{%endblock%}

{%block js%}
<!-- AI Analytics Scripts -->
<script src="/js/ai-analytics/ai-analytics-core.js"></script>
<script src="/js/ai-analytics/ai-dashboard.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 等待功能墙初始化完成后检查权限
    const checkFeatureAndInit = async () => {
        // 等待功能墙初始化
        let retryCount = 0;
        const maxRetries = 10;
        
        while (retryCount < maxRetries) {
            if (window.FeatureWall && window.FeatureWall.hasFeature) {
                console.log('[AI Analytics] 功能墙已初始化，检查权限...');
                // 如果功能墙信息为空，尝试手动刷新
                if (!window.FeatureWall.getFeatureWallInfo()) {
                    console.log('[AI Analytics] 功能墙信息为空，尝试刷新...');
                    await window.FeatureWall.refresh();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                break;
            }
            
            console.log('[AI Analytics] 等待功能墙初始化...', retryCount + 1);
            await new Promise(resolve => setTimeout(resolve, 500));
            retryCount++;
        }
        
        // 检查是否有分析权限，支持多种功能名称格式
        const featureNames = ['advancedanalytics', 'ADVANCED_ANALYTICS', 'advanced_analytics'];
        let hasAnalyticsFeature = true;  // 临时关闭前端限制
        
        /*
        for (const featureName of featureNames) {
            if (window.FeatureWall && window.FeatureWall.hasFeature && window.FeatureWall.hasFeature(featureName)) {
                hasAnalyticsFeature = true;
                console.log('[AI Analytics] 检测到权限:', featureName);
                break;
            }
        }
        */
        
        // 调试：输出功能墙信息
        if (window.FeatureWall && window.FeatureWall.getFeatureWallInfo) {
            const featureWallInfo = window.FeatureWall.getFeatureWallInfo();
            console.log('[AI Analytics] 功能墙信息:', featureWallInfo);
            console.log('[AI Analytics] advancedanalytics功能详情:', featureWallInfo?.features?.advancedanalytics);
        }
        
        if (!hasAnalyticsFeature) {
            console.log('[AI Analytics] 无权限访问高级分析功能');
            // 如果没有权限，显示提示
            document.getElementById('ai-analytics-container').innerHTML = `
                <div class="text-center py-16">
                    <div class="text-6xl mb-6">🔒</div>
                    <h2 class="text-2xl font-bold text-slate-800 dark:text-slate-200 mb-4">AI 分析功能需要升级</h2>
                    <p class="text-slate-600 dark:text-slate-400 mb-2">此功能仅对专业版及以上用户开放</p>
                    <p class="text-slate-600 dark:text-slate-400 mb-6">包含智能趋势分析、异常检测、性能预测等AI功能</p>
                    <button class="inline-flex items-center gap-2 px-6 py-3 text-sm font-medium bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95" onclick="window.FeatureWall.showUpgradeDialog('ADVANCED_ANALYTICS')">
                        <i class="ti ti-arrow-up text-sm"></i>
                        <span>升级以解锁</span>
                    </button>
                </div>
            `;
            return;
        }
        
        console.log('[AI Analytics] 权限验证通过，初始化AI分析系统...');
        await initializeAIAnalytics();
    };
    
    // AI分析系统初始化函数
    const initializeAIAnalytics = async () => {
        // 初始化AI分析系统
        window.AIAnalytics = window.AIAnalytics || {};
        
        try {
            // 等待一段时间让脚本和DOM完全加载
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 检查组件是否已加载
            console.log('[AI Analytics] 检查组件可用性:');
            console.log('- core:', !!window.AIAnalytics?.core);
            console.log('- Dashboard (class):', !!window.AIAnalytics?.Dashboard);
            
            // 直接使用现有的UI，不创建Dashboard组件实例
            // 因为我们已经有了Tailwind CSS版本的UI
            console.log('[AI Analytics] 使用现有的Tailwind UI，跳过Dashboard组件创建');
            
            // 只初始化核心组件
            if (window.AIAnalytics?.core) {
                console.log('[AI Analytics] 开始初始化核心组件...');

                try {
                    await window.AIAnalytics.core.init();
                    console.log('[AI Analytics] 核心组件初始化完成');

                    // 自动加载服务器列表
                    if (window.AIAnalytics.core.loadServerList) {
                        await window.AIAnalytics.core.loadServerList();
                    }

                    // 设置按钮事件监听
                    setupUIEventListeners();

                    console.log('[AI Analytics] 系统初始化完成');

                } catch (initError) {
                    console.error('[AI Analytics] 组件初始化失败:', initError);
                    throw initError;
                }
            } else {
                throw new Error('AI分析核心组件未正确加载');
            }
            
        } catch (error) {
            console.error('[AI Analytics] 类加载失败:', error);
            document.getElementById('ai-analytics-container').innerHTML = `
                <div class="text-center py-16">
                    <div class="text-red-500 mb-6">
                        <i class="ti ti-alert-circle text-6xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-slate-800 dark:text-slate-200 mb-4">AI 分析模块加载失败</h3>
                    <p class="text-slate-600 dark:text-slate-400 mb-2">请检查相关 JavaScript 文件是否正确加载</p>
                    <p class="text-slate-600 dark:text-slate-400 mb-6">错误详情: ${error.message}</p>
                    <button class="inline-flex items-center gap-2 px-6 py-3 text-sm font-medium bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95" onclick="location.reload()">
                        <i class="ti ti-refresh text-sm"></i>
                        <span>重新加载</span>
                    </button>
                </div>
            `;
        }
    };

    // 设置UI事件监听器
    const setupUIEventListeners = () => {
        // 生成智能报告按钮
        document.getElementById('generate-report-btn').addEventListener('click', async () => {
            try {
                showLoading(true);
                const timeRange = getSelectedTimeRange();
                const report = await window.AIAnalytics.core.generateReport({
                    timeRange: timeRange,
                    includeData: true
                });

                displayReport(report);
                updateOverviewCards(report.summary);
                showRecommendations(report.recommendations);

                document.getElementById('export-report-btn').disabled = false;
                document.getElementById('last-updated').textContent = '刚刚';

            } catch (error) {
                console.error('[AI Analytics] 生成报告失败:', error);
                showError('生成报告失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        });

        // 导出报告按钮
        document.getElementById('export-report-btn').addEventListener('click', () => {
            // TODO: 实现报告导出功能
            alert('报告导出功能开发中...');
        });

        // 历史报告按钮
        document.getElementById('view-history-btn').addEventListener('click', async () => {
            await loadHistoryReports();
            document.getElementById('history-section').style.display = 'block';
            document.getElementById('report-content').parentElement.style.display = 'none';
            document.getElementById('recommendations-section').style.display = 'none';
        });

        // 关闭历史报告按钮
        document.getElementById('close-history-btn').addEventListener('click', () => {
            document.getElementById('history-section').style.display = 'none';
            document.getElementById('report-content').parentElement.style.display = 'block';
        });
        
        // AI 设置按钮
        document.getElementById('ai-settings-btn').addEventListener('click', async () => {
            await loadAISettings();
            document.getElementById('ai-settings-modal').style.display = 'flex';
        });
        
        // 关闭 AI 设置模态框
        document.getElementById('close-ai-settings-btn').addEventListener('click', () => {
            document.getElementById('ai-settings-modal').style.display = 'none';
        });
        
        document.getElementById('cancel-ai-settings-btn').addEventListener('click', () => {
            document.getElementById('ai-settings-modal').style.display = 'none';
        });
        
        // AI 设置表单提交
        document.getElementById('ai-settings-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            await saveAISettings();
        });
        
        // 测试 AI 连接
        document.getElementById('test-ai-connection-btn').addEventListener('click', async () => {
            await testAIConnection();
        });

    };

    // 获取选择的时间范围
    const getSelectedTimeRange = () => {
        const range = document.getElementById('time-range-select').value;
        const now = Math.floor(Date.now() / 1000);
        const ranges = {
            '1h': 3600,
            '6h': 21600,
            '24h': 86400,
            '7d': 604800,
            '30d': 2592000
        };
        const duration = ranges[range] || 86400;
        return {
            start: now - duration,
            end: now
        };
    };

    // 显示加载状态
    const showLoading = (show) => {
        const overlay = document.getElementById('loading-overlay');
        overlay.style.display = show ? 'flex' : 'none';
    };

    // 显示错误信息
    const showError = (message) => {
        // 简单的错误提示，可以后续改进
        alert(message);
    };

    // 显示报告内容 - 增强版
    const displayReport = (report) => {
        const content = document.getElementById('report-content');
        content.innerHTML = `
            <!-- 核心指标总览 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/20 rounded-lg p-4 border border-green-200/50 dark:border-green-700/30">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="text-sm font-semibold text-green-800 dark:text-green-200">网络质量</h4>
                        <span class="text-xs text-green-600 dark:text-green-400">${getQualityLevel(report.networkQuality.qualityScore)}</span>
                    </div>
                    <div class="text-2xl font-bold text-green-900 dark:text-green-100 mb-1">${report.networkQuality.qualityScore}/100</div>
                    <div class="text-xs text-green-700 dark:text-green-300">平均延迟 ${report.networkQuality.averageLatency}ms</div>
                    ${report.networkQuality.issueServers.length > 0 ? 
                        `<div class="text-xs text-red-600 dark:text-red-400 mt-1">⚠️ ${report.networkQuality.issueServers.length} 个服务器异常</div>` : 
                        `<div class="text-xs text-green-600 dark:text-green-400 mt-1">✅ 所有网络正常</div>`
                    }
                </div>

                <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/20 rounded-lg p-4 border border-blue-200/50 dark:border-blue-700/30">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="text-sm font-semibold text-blue-800 dark:text-blue-200">资源利用</h4>
                        <span class="text-xs text-blue-600 dark:text-blue-400">${getLoadLevel(report.loadAnalysis.averageCpuUsage, report.loadAnalysis.averageMemoryUsage)}</span>
                    </div>
                    <div class="text-2xl font-bold text-blue-900 dark:text-blue-100 mb-1">${Math.round((report.loadAnalysis.averageCpuUsage + report.loadAnalysis.averageMemoryUsage) / 2)}%</div>
                    <div class="text-xs text-blue-700 dark:text-blue-300">CPU ${report.loadAnalysis.averageCpuUsage}% · 内存 ${report.loadAnalysis.averageMemoryUsage}%</div>
                    ${report.loadAnalysis.highLoadServers.length > 0 ? 
                        `<div class="text-xs text-amber-600 dark:text-amber-400 mt-1">⚠️ ${report.loadAnalysis.highLoadServers.length} 个高负载服务器</div>` : 
                        `<div class="text-xs text-green-600 dark:text-green-400 mt-1">✅ 负载均衡良好</div>`
                    }
                </div>

                <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/20 rounded-lg p-4 border border-purple-200/50 dark:border-purple-700/30">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="text-sm font-semibold text-purple-800 dark:text-purple-200">流量分析</h4>
                        <span class="text-xs text-purple-600 dark:text-purple-400">${getTrafficStatus(report.trafficAnalysis)}</span>
                    </div>
                    <div class="text-2xl font-bold text-purple-900 dark:text-purple-100 mb-1">${report.trafficAnalysis.totalTrafficUsed || 'N/A'}</div>
                    <div class="text-xs text-purple-700 dark:text-purple-300">平均使用率 ${report.trafficAnalysis.averageUsage || 'N/A'}</div>
                    ${report.trafficAnalysis.warnings?.length > 0 ? 
                        `<div class="text-xs text-amber-600 dark:text-amber-400 mt-1">⚠️ ${report.trafficAnalysis.warnings.length} 个流量预警</div>` : 
                        `<div class="text-xs text-green-600 dark:text-green-400 mt-1">✅ 流量使用正常</div>`
                    }
                </div>
            </div>

            <!-- 问题服务器详情 -->
            ${generateIssueServerDetails(report)}

            <!-- 服务器价值分析 -->
            ${generateServerUtilizationSection(report)}

            <!-- 详细分析图表 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                <div class="bg-white/60 dark:bg-slate-700/40 rounded-lg p-4 border border-slate-200/60 dark:border-slate-600/40">
                    <h4 class="font-semibold text-slate-800 dark:text-slate-200 mb-3">性能分布图</h4>
                    <div id="performance-chart" style="height: 200px;"></div>
                </div>
                
                <div class="bg-white/60 dark:bg-slate-700/40 rounded-lg p-4 border border-slate-200/60 dark:border-slate-600/40">
                    <h4 class="font-semibold text-slate-800 dark:text-slate-200 mb-3">问题趋势</h4>
                    <div id="issue-trend-chart" style="height: 200px;"></div>
                </div>
            </div>

            <!-- 新增：流量分析和地理分布图表 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                <div class="bg-white/60 dark:bg-slate-700/40 rounded-lg p-4 border border-slate-200/60 dark:border-slate-600/40">
                    <h4 class="font-semibold text-slate-800 dark:text-slate-200 mb-3">流量占比分布</h4>
                    <div id="traffic-distribution-chart" style="height: 300px;"></div>
                </div>
                
                <div class="bg-white/60 dark:bg-slate-700/40 rounded-lg p-4 border border-slate-200/60 dark:border-slate-600/40">
                    <h4 class="font-semibold text-slate-800 dark:text-slate-200 mb-3">节点地理分布</h4>
                    <div id="geo-distribution-chart" style="height: 300px;"></div>
                </div>
            </div>
        `;
        
        // 渲染图表
        setTimeout(() => {
            renderPerformanceChart(report);
            renderIssueTrendChart(report);
            renderTrafficDistributionChart(report);
            renderGeoDistributionChart(report);
        }, 100);
    };

    // 更新概览卡片
    const updateOverviewCards = (summary) => {
        document.getElementById('overall-health').textContent = summary.overallScore || '--';
        document.getElementById('total-servers').textContent = summary.totalServers || '--';
        document.getElementById('total-issues').textContent = (summary.warningServers + summary.criticalServers) || '--';
        document.getElementById('performance-score').textContent = summary.overallScore || '--';
    };

    // 辅助函数 - 获取质量等级
    const getQualityLevel = (score) => {
        if (score >= 90) return '优秀';
        if (score >= 80) return '良好';
        if (score >= 70) return '一般';
        if (score >= 60) return '较差';
        return '异常';
    };

    // 辅助函数 - 获取负载等级
    const getLoadLevel = (cpu, memory) => {
        const avg = (cpu + memory) / 2;
        if (avg >= 90) return '高负载';
        if (avg >= 70) return '中等负载';
        if (avg >= 50) return '正常';
        return '轻负载';
    };

    // 辅助函数 - 获取流量状态
    const getTrafficStatus = (traffic) => {
        if (traffic.warnings && traffic.warnings.length > 0) {
            return traffic.warnings.some(w => w.severity === 'critical') ? '严重预警' : '有预警';
        }
        return '正常';
    };

    // 生成问题服务器详情
    const generateIssueServerDetails = (report) => {
        const issues = [];
        
        // 网络问题服务器
        if (report.networkQuality.issueServers.length > 0) {
            issues.push({
                type: 'network',
                title: '网络连接异常',
                servers: report.networkQuality.issueServers,
                color: 'red'
            });
        }
        
        // 高负载服务器
        if (report.loadAnalysis.highLoadServers.length > 0) {
            issues.push({
                type: 'load',
                title: '高负载服务器',
                servers: report.loadAnalysis.highLoadServers,
                color: 'amber'
            });
        }
        
        // 磁盘警告
        if (report.diskWarnings.length > 0) {
            issues.push({
                type: 'disk',
                title: '磁盘空间警告',
                servers: report.diskWarnings,
                color: 'orange'
            });
        }

        if (issues.length === 0) {
            return `
                <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/20 rounded-lg p-6 border border-green-200/50 dark:border-green-700/30 mb-6">
                    <div class="text-center">
                        <i class="ti ti-circle-check text-4xl text-green-600 dark:text-green-400 mb-2"></i>
                        <h3 class="text-lg font-semibold text-green-800 dark:text-green-200 mb-1">系统运行良好</h3>
                        <p class="text-green-700 dark:text-green-300">所有监控服务器状态正常，未发现异常情况</p>
                    </div>
                </div>
            `;
        }

        return `
            <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/30 dark:to-red-800/20 rounded-lg p-6 border border-red-200/50 dark:border-red-700/30 mb-6">
                <h3 class="text-lg font-semibold text-red-800 dark:text-red-200 mb-4">
                    <i class="ti ti-alert-triangle text-red-600 dark:text-red-400 mr-2"></i>
                    需要关注的服务器 (${issues.reduce((sum, issue) => sum + issue.servers.length, 0)} 台)
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    ${issues.map(issue => `
                        <div class="bg-white/60 dark:bg-slate-700/40 rounded-lg p-4 border border-slate-200/60 dark:border-slate-600/40">
                            <h4 class="font-semibold text-${issue.color}-800 dark:text-${issue.color}-200 mb-2">${issue.title}</h4>
                            ${issue.servers.slice(0, 3).map(server => `
                                <div class="text-xs text-slate-600 dark:text-slate-400 mb-1">
                                    <span class="font-mono text-xs bg-slate-100 dark:bg-slate-600 px-1 rounded">${server.serverName}</span>
                                    ${issue.type === 'network' ? `延迟 ${server.avgLatency}ms` : 
                                      issue.type === 'load' ? `CPU ${server.cpuUsage}%` : 
                                      `使用率 ${server.currentUsage}`}
                                </div>
                            `).join('')}
                            ${issue.servers.length > 3 ? `<div class="text-xs text-slate-500">... 还有 ${issue.servers.length - 3} 台</div>` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    };

    // 生成服务器价值分析
    const generateServerUtilizationSection = (report) => {
        const { highValueServers, underutilizedServers } = report.serverUtilization;
        
        if (highValueServers.length === 0 && underutilizedServers.length === 0) {
            return '';
        }

        return `
            <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/20 rounded-lg p-6 border border-blue-200/50 dark:border-blue-700/30 mb-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-4">
                    <i class="ti ti-chart-line text-blue-600 dark:text-blue-400 mr-2"></i>
                    服务器价值分析
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    ${highValueServers.length > 0 ? `
                        <div>
                            <h4 class="font-semibold text-green-800 dark:text-green-200 mb-3">高价值服务器 (${highValueServers.length} 台)</h4>
                            ${highValueServers.map(server => `
                                <div class="bg-white/60 dark:bg-slate-700/40 rounded-lg p-3 border border-slate-200/60 dark:border-slate-600/40 mb-2">
                                    <div class="flex justify-between items-center mb-1">
                                        <span class="font-mono text-sm font-medium">${server.serverName}</span>
                                        <span class="text-green-600 dark:text-green-400 text-sm font-bold">${server.utilizationScore}/100</span>
                                    </div>
                                    <p class="text-xs text-slate-600 dark:text-slate-400">${server.reason}</p>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                    ${underutilizedServers.length > 0 ? `
                        <div>
                            <h4 class="font-semibold text-amber-800 dark:text-amber-200 mb-3">低利用率服务器 (${underutilizedServers.length} 台)</h4>
                            ${underutilizedServers.map(server => `
                                <div class="bg-white/60 dark:bg-slate-700/40 rounded-lg p-3 border border-slate-200/60 dark:border-slate-600/40 mb-2">
                                    <div class="flex justify-between items-center mb-1">
                                        <span class="font-mono text-sm font-medium">${server.serverName}</span>
                                        <span class="text-amber-600 dark:text-amber-400 text-sm font-bold">${server.utilizationScore}/100</span>
                                    </div>
                                    <p class="text-xs text-slate-600 dark:text-slate-400">${server.reason}</p>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    };

    // 渲染性能分布图
    const renderPerformanceChart = (report) => {
        const chartDom = document.getElementById('performance-chart');
        if (!chartDom) return;
        
        const myChart = echarts.init(chartDom);
        const option = {
            tooltip: { trigger: 'item' },
            series: [{
                type: 'pie',
                radius: '60%',
                data: [
                    { value: report.summary.healthyServers, name: '健康', itemStyle: { color: '#10b981' } },
                    { value: report.summary.warningServers, name: '警告', itemStyle: { color: '#f59e0b' } },
                    { value: report.summary.criticalServers, name: '严重', itemStyle: { color: '#ef4444' } }
                ]
            }]
        };
        myChart.setOption(option);
    };

    // 渲染问题趋势图
    const renderIssueTrendChart = (report) => {
        const chartDom = document.getElementById('issue-trend-chart');
        if (!chartDom) return;
        
        const myChart = echarts.init(chartDom);
        const option = {
            tooltip: { trigger: 'axis' },
            xAxis: { type: 'category', data: ['网络', '负载', '磁盘', '流量'] },
            yAxis: { type: 'value' },
            series: [{
                type: 'bar',
                data: [
                    report.networkQuality.issueServers.length,
                    report.loadAnalysis.highLoadServers.length,
                    report.diskWarnings.length,
                    report.trafficAnalysis.warnings?.length || 0
                ],
                itemStyle: { color: '#8b5cf6' }
            }]
        };
        myChart.setOption(option);
    };

    // 渲染流量占比分布图
    const renderTrafficDistributionChart = (report) => {
        const chartDom = document.getElementById('traffic-distribution-chart');
        if (!chartDom) return;
        
        const myChart = echarts.init(chartDom);
        
        // 从报告中提取服务器流量数据
        const trafficData = [];
        if (report.serverTrafficDistribution) {
            // 如果AI返回了流量分布数据
            trafficData.push(...report.serverTrafficDistribution);
        } else if (report.servers && Array.isArray(report.servers)) {
            // 否则从原始数据计算
            report.servers.forEach(server => {
                if (server.traffic && server.traffic.total > 0) {
                    trafficData.push({
                        name: server.name || server.id,
                        value: server.traffic.total
                    });
                }
            });
        }
        
        // 按流量排序，取前10个
        trafficData.sort((a, b) => b.value - a.value);
        const top10 = trafficData.slice(0, 10);
        const othersValue = trafficData.slice(10).reduce((sum, item) => sum + item.value, 0);
        
        if (othersValue > 0) {
            top10.push({ name: '其他', value: othersValue });
        }
        
        const option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                type: 'scroll',
                orient: 'vertical',
                right: 10,
                top: 20,
                bottom: 20,
                textStyle: {
                    color: '#666'
                }
            },
            series: [{
                name: '流量占比',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['40%', '50%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 20,
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: top10.length > 0 ? top10 : [{ name: '无数据', value: 1 }]
            }]
        };
        
        myChart.setOption(option);
    };

    // 渲染节点地理分布图
    const renderGeoDistributionChart = (report) => {
        const chartDom = document.getElementById('geo-distribution-chart');
        if (!chartDom) return;
        
        const myChart = echarts.init(chartDom);
        
        // 从报告中提取国家数据
        const countryData = {};
        if (report.geoDistribution) {
            // 如果AI返回了地理分布数据
            Object.assign(countryData, report.geoDistribution);
        } else if (report.servers && Array.isArray(report.servers)) {
            // 否则从服务器信息提取
            report.servers.forEach(server => {
                if (server.info && server.info.country) {
                    const country = server.info.country;
                    countryData[country] = (countryData[country] || 0) + 1;
                }
            });
        }
        
        // 转换为地图数据格式
        const mapData = Object.entries(countryData).map(([country, count]) => ({
            name: country,
            value: count
        }));
        
        // 简化的世界地图（使用散点图模拟）
        const geoCoordMap = {
            '中国': [116.46, 39.92],
            '美国': [-95.71, 37.09],
            '日本': [139.69, 35.68],
            '韩国': [126.98, 37.56],
            '新加坡': [103.85, 1.29],
            '德国': [10.45, 51.16],
            '英国': [-0.12, 51.50],
            '法国': [2.21, 46.23],
            '加拿大': [-106.35, 56.13],
            '澳大利亚': [133.78, -25.27],
            '俄罗斯': [105.32, 61.52],
            '印度': [78.96, 20.59],
            '巴西': [-51.93, -14.24],
            '南非': [22.94, -30.56],
            '香港': [114.17, 22.30],
            '台湾': [121.00, 23.50]
        };
        
        // 转换为散点数据
        const scatterData = mapData.map(item => {
            const coord = geoCoordMap[item.name];
            if (coord) {
                return {
                    name: item.name,
                    value: coord.concat(item.value)
                };
            }
            return null;
        }).filter(item => item !== null);
        
        const option = {
            backgroundColor: 'transparent',
            title: {
                text: `共 ${Object.keys(countryData).length} 个国家/地区`,
                subtext: `总计 ${report.summary?.totalServers || 0} 个节点`,
                left: 'center',
                top: 'top',
                textStyle: {
                    color: '#666',
                    fontSize: 14
                },
                subtextStyle: {
                    color: '#999',
                    fontSize: 12
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    if (params.value && params.value[2]) {
                        return params.name + '<br/>节点数: ' + params.value[2];
                    }
                    return params.name;
                }
            },
            geo: {
                map: 'world',
                roam: true,
                label: {
                    emphasis: {
                        show: false
                    }
                },
                itemStyle: {
                    normal: {
                        areaColor: '#f3f3f3',
                        borderColor: '#999'
                    },
                    emphasis: {
                        areaColor: '#e6e6e6'
                    }
                }
            },
            series: [{
                name: '节点分布',
                type: 'scatter',
                coordinateSystem: 'geo',
                data: scatterData,
                symbolSize: function(val) {
                    return Math.min(Math.max(val[2] * 5, 10), 50);
                },
                label: {
                    normal: {
                        formatter: '{b}',
                        position: 'right',
                        show: false
                    },
                    emphasis: {
                        show: true
                    }
                },
                itemStyle: {
                    normal: {
                        color: '#8b5cf6',
                        shadowBlur: 10,
                        shadowColor: '#333'
                    }
                }
            }]
        };
        
        // 如果没有地图数据，使用备选方案
        if (scatterData.length === 0) {
            option = {
                title: {
                    text: '节点地理分布',
                    subtext: '暂无地理位置数据',
                    left: 'center',
                    top: 'middle',
                    textStyle: {
                        color: '#999',
                        fontSize: 16
                    },
                    subtextStyle: {
                        color: '#ccc',
                        fontSize: 14
                    }
                }
            };
        }
        
        myChart.setOption(option);
    };

    // 显示推荐建议 - 增强版
    const showRecommendations = (recommendations) => {
        const section = document.getElementById('recommendations-section');
        const content = document.getElementById('recommendations-content');

        if (!recommendations || recommendations.length === 0) {
            section.style.display = 'none';
            return;
        }

        section.style.display = 'block';
        
        const priorityGroups = {
            critical: recommendations.filter(r => r.priority === 'critical'),
            high: recommendations.filter(r => r.priority === 'high'),
            medium: recommendations.filter(r => r.priority === 'medium'),
            low: recommendations.filter(r => r.priority === 'low')
        };

        content.innerHTML = Object.entries(priorityGroups)
            .filter(([_, recs]) => recs.length > 0)
            .map(([priority, recs]) => `
                <div class="mb-6">
                    <h4 class="text-sm font-semibold text-slate-800 dark:text-slate-200 mb-3 flex items-center gap-2">
                        <span class="w-3 h-3 rounded-full ${getPriorityColor(priority)}"></span>
                        ${getPriorityLabel(priority)} (${recs.length} 项)
                    </h4>
                    ${recs.map(rec => `
                        <div class="bg-white/60 dark:bg-slate-700/40 rounded-lg p-4 border border-slate-200/60 dark:border-slate-600/40 mb-3">
                            <div class="flex items-start gap-3">
                                <div class="w-8 h-8 ${getPriorityBg(rec.priority)} rounded-lg flex items-center justify-center flex-shrink-0">
                                    <i class="ti ${getTypeIcon(rec.type)} ${getPriorityTextColor(rec.priority)} text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <h5 class="font-semibold text-slate-800 dark:text-slate-200 mb-2">${rec.title}</h5>
                                    <p class="text-slate-600 dark:text-slate-400 text-sm mb-3">${rec.description}</p>
                                    <div class="bg-slate-50 dark:bg-slate-800 rounded-lg p-3 mb-2">
                                        <div class="text-xs font-medium text-slate-700 dark:text-slate-300 mb-1">🔧 操作步骤:</div>
                                        <div class="text-xs text-slate-600 dark:text-slate-400">${rec.action}</div>
                                    </div>
                                    <div class="text-xs text-green-600 dark:text-green-400">
                                        <strong>预期效果:</strong> ${rec.estimatedImpact}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `).join('');
    };

    // 优先级相关辅助函数
    const getPriorityColor = (priority) => {
        const colors = {
            critical: 'bg-red-500',
            high: 'bg-orange-500',
            medium: 'bg-yellow-500',
            low: 'bg-blue-500'
        };
        return colors[priority] || 'bg-gray-500';
    };

    const getPriorityBg = (priority) => {
        const colors = {
            critical: 'bg-red-100 dark:bg-red-900/30',
            high: 'bg-orange-100 dark:bg-orange-900/30',
            medium: 'bg-yellow-100 dark:bg-yellow-900/30',
            low: 'bg-blue-100 dark:bg-blue-900/30'
        };
        return colors[priority] || 'bg-gray-100 dark:bg-gray-900/30';
    };

    const getPriorityTextColor = (priority) => {
        const colors = {
            critical: 'text-red-600 dark:text-red-400',
            high: 'text-orange-600 dark:text-orange-400',
            medium: 'text-yellow-600 dark:text-yellow-400',
            low: 'text-blue-600 dark:text-blue-400'
        };
        return colors[priority] || 'text-gray-600 dark:text-gray-400';
    };

    const getPriorityLabel = (priority) => {
        const labels = {
            critical: '紧急处理',
            high: '高优先级',
            medium: '中等优先级',
            low: '建议优化'
        };
        return labels[priority] || priority;
    };

    const getTypeIcon = (type) => {
        const icons = {
            optimization: 'ti-adjustments',
            warning: 'ti-alert-triangle',
            maintenance: 'ti-tools'
        };
        return icons[type] || 'ti-bulb';
    };


    // 加载历史报告列表
    const loadHistoryReports = async () => {
        try {
            const response = await fetch('/api/analytics/ai/reports');
            const result = await response.json();
            
            if (result.success) {
                // 更新统计信息
                document.getElementById('total-reports-count').textContent = result.stats.totalReports;
                document.getElementById('reports-24h-count').textContent = result.stats.reportsLast24Hours;
                document.getElementById('avg-score').textContent = result.stats.averageScore;
                
                // 显示报告列表
                const historyList = document.getElementById('history-list');
                if (result.data.length === 0) {
                    historyList.innerHTML = `
                        <div class="text-center py-8 text-slate-500 dark:text-slate-400">
                            <i class="ti ti-history text-4xl mb-2"></i>
                            <p>暂无历史报告</p>
                        </div>
                    `;
                } else {
                    historyList.innerHTML = result.data.map(report => `
                        <div class="bg-white/60 dark:bg-slate-700/40 rounded-lg p-4 border border-slate-200/60 dark:border-slate-600/40 hover:bg-white/80 dark:hover:bg-slate-700/50 transition-colors cursor-pointer" onclick="viewHistoryReport('${report.reportId}')">
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="font-semibold text-slate-800 dark:text-slate-200">${report.title}</h4>
                                <span class="text-xs text-slate-500 dark:text-slate-400">${formatTimestamp(report.createdAt)}</span>
                            </div>
                            <div class="flex items-center gap-4 text-xs">
                                <span class="text-slate-600 dark:text-slate-400">
                                    <i class="ti ti-server text-xs mr-1"></i>
                                    ${report.serversAnalyzed} 台服务器
                                </span>
                                <span class="text-slate-600 dark:text-slate-400">
                                    <i class="ti ti-clock text-xs mr-1"></i>
                                    ${formatTimeRange(report.timeRangeStart, report.timeRangeEnd)}
                                </span>
                                <span class="font-semibold ${getScoreColor(report.overallScore)}">
                                    评分: ${report.overallScore}/100
                                </span>
                            </div>
                            <div class="flex justify-end mt-2">
                                <button class="text-red-500 hover:text-red-600 text-xs" onclick="event.stopPropagation(); deleteHistoryReport('${report.reportId}')">
                                    <i class="ti ti-trash text-sm"></i>
                                    删除
                                </button>
                            </div>
                        </div>
                    `).join('');
                }
            } else {
                console.error('加载历史报告失败:', result.error);
                showError('加载历史报告失败');
            }
        } catch (error) {
            console.error('加载历史报告失败:', error);
            showError('加载历史报告失败');
        }
    };

    // 查看历史报告
    window.viewHistoryReport = async (reportId) => {
        try {
            showLoading(true);
            const response = await fetch(`/api/analytics/ai/reports/${reportId}`);
            const result = await response.json();
            
            if (result.success) {
                const report = result.data;
                
                // 隐藏历史列表，显示报告
                document.getElementById('history-section').style.display = 'none';
                document.getElementById('report-content').parentElement.style.display = 'block';
                
                // 显示报告内容
                displayReport(report.reportData);
                updateOverviewCards(report.reportData.summary);
                showRecommendations(report.reportData.recommendations);
                
                // 更新时间显示
                document.getElementById('last-updated').textContent = formatTimestamp(report.createdAt);
                
                // 启用导出按钮
                document.getElementById('export-report-btn').disabled = false;
            } else {
                showError('加载报告详情失败');
            }
        } catch (error) {
            console.error('加载报告详情失败:', error);
            showError('加载报告详情失败');
        } finally {
            showLoading(false);
        }
    };

    // 删除历史报告
    window.deleteHistoryReport = async (reportId) => {
        if (!confirm('确定要删除这份报告吗？')) {
            return;
        }
        
        try {
            const response = await fetch(`/api/analytics/ai/reports/${reportId}`, {
                method: 'DELETE'
            });
            const result = await response.json();
            
            if (result.success) {
                // 重新加载列表
                await loadHistoryReports();
            } else {
                showError('删除报告失败');
            }
        } catch (error) {
            console.error('删除报告失败:', error);
            showError('删除报告失败');
        }
    };

    // 格式化时间戳
    const formatTimestamp = (timestamp) => {
        const date = new Date(timestamp * 1000);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) {
            return '刚刚';
        } else if (diff < 3600000) {
            return Math.floor(diff / 60000) + ' 分钟前';
        } else if (diff < 86400000) {
            return Math.floor(diff / 3600000) + ' 小时前';
        } else {
            return date.toLocaleString('zh-CN');
        }
    };

    // 格式化时间范围
    const formatTimeRange = (start, end) => {
        const duration = end - start;
        if (duration < 3600) {
            return Math.floor(duration / 60) + ' 分钟';
        } else if (duration < 86400) {
            return Math.floor(duration / 3600) + ' 小时';
        } else {
            return Math.floor(duration / 86400) + ' 天';
        }
    };

    // 获取评分颜色
    const getScoreColor = (score) => {
        if (score >= 80) return 'text-green-600 dark:text-green-400';
        if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
        if (score >= 40) return 'text-orange-600 dark:text-orange-400';
        return 'text-red-600 dark:text-red-400';
    };
    
    // ==================== AI 设置相关功能 ====================
    
    // 加载 AI 设置
    const loadAISettings = async () => {
        try {
            const response = await fetch('/admin/api/ai/config');
            const result = await response.json();
            
            if (result.code === 1) {
                const config = result.msg;
                
                // 填充表单
                document.getElementById('ai-enabled').checked = config.enabled;
                document.getElementById('ai-name').value = config.name || '';
                document.getElementById('ai-api-base').value = config.apiBase || '';
                document.getElementById('ai-model').value = config.model || '';
                
                // 密钥处理
                if (config.hasKey) {
                    document.getElementById('ai-api-key').placeholder = '已设置，留空不更改';
                } else {
                    document.getElementById('ai-api-key').placeholder = '请输入 API 密钥';
                }
                
                // 环境变量锁定处理
                if (config.envLocks) {
                    if (config.envLocks.apiKey) {
                        document.getElementById('ai-api-key').disabled = true;
                        document.getElementById('ai-api-key').placeholder = '由环境变量控制';
                    }
                    if (config.envLocks.apiBase) {
                        document.getElementById('ai-api-base').disabled = true;
                        document.getElementById('ai-api-base').placeholder = '由环境变量控制';
                    }
                    if (config.envLocks.model) {
                        document.getElementById('ai-model').disabled = true;
                        document.getElementById('ai-model').placeholder = '由环境变量控制';
                    }
                }
            } else {
                console.error('加载 AI 设置失败:', result.msg);
            }
        } catch (error) {
            console.error('加载 AI 设置失败:', error);
        }
    };
    
    // 保存 AI 设置
    const saveAISettings = async () => {
        try {
            const config = {
                enabled: document.getElementById('ai-enabled').checked,
                name: document.getElementById('ai-name').value,
                apiBase: document.getElementById('ai-api-base').value,
                model: document.getElementById('ai-model').value,
                apiKey: document.getElementById('ai-api-key').value
            };
            
            const response = await fetch('/admin/api/ai/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });
            
            const result = await response.json();
            
            if (result.code === 1) {
                // 显示成功消息
                showAITestResult(true, '设置保存成功');
                
                // 1秒后关闭模态框
                setTimeout(() => {
                    document.getElementById('ai-settings-modal').style.display = 'none';
                    document.getElementById('ai-test-result').style.display = 'none';
                }, 1000);
                
                // 重新加载配置（验证热更新）
                await loadAISettings();
            } else {
                showAITestResult(false, '保存失败: ' + result.msg);
            }
        } catch (error) {
            console.error('保存 AI 设置失败:', error);
            showAITestResult(false, '保存失败: ' + error.message);
        }
    };
    
    // 测试 AI 连接
    const testAIConnection = async () => {
        try {
            // 显示测试中状态
            const resultDiv = document.getElementById('ai-test-result');
            resultDiv.className = 'mb-4 p-3 rounded-lg bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700';
            resultDiv.innerHTML = '<i class="ti ti-loader animate-spin mr-2"></i>正在测试连接...';
            resultDiv.style.display = 'block';
            
            const response = await fetch('/admin/api/ai/test');
            const result = await response.json();
            
            if (result.code === 1 && result.msg && result.msg.success) {
                showAITestResult(true, '连接成功！' + (result.msg.details ? ' (' + result.msg.details.provider + ')' : ''));
            } else {
                const errorMessage = (result.msg && result.msg.message) || result.msg || '连接失败';
                showAITestResult(false, errorMessage);
            }
        } catch (error) {
            console.error('测试 AI 连接失败:', error);
            showAITestResult(false, '测试失败: ' + error.message);
        }
    };
    
    // 显示测试结果
    const showAITestResult = (success, message) => {
        const resultDiv = document.getElementById('ai-test-result');
        
        if (success) {
            resultDiv.className = 'mb-4 p-3 rounded-lg bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700';
            resultDiv.innerHTML = `<i class="ti ti-check text-green-600 dark:text-green-400 mr-2"></i><span class="text-green-700 dark:text-green-300">${message}</span>`;
        } else {
            resultDiv.className = 'mb-4 p-3 rounded-lg bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700';
            resultDiv.innerHTML = `<i class="ti ti-x text-red-600 dark:text-red-400 mr-2"></i><span class="text-red-700 dark:text-red-300">${message}</span>`;
        }
        
        resultDiv.style.display = 'block';
        
        // 5秒后自动隐藏
        setTimeout(() => {
            resultDiv.style.display = 'none';
        }, 5000);
    };
    
    // ==================== AI 设置功能结束 ====================

    // 启动权限检查和初始化流程
    checkFeatureAndInit();
});
</script>
{%endblock%}