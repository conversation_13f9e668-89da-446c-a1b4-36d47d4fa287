<!-- 管理后台侧边栏 -->
{# 导入侧边栏宏 #}
{% import 'admin/sidebar-macros.html' as sidebar %}

<div class="fixed left-0 top-16 h-[calc(100vh-4rem)] w-64 bg-white dark:bg-slate-900 border-r border-slate-200 dark:border-slate-700 z-30" id="admin-sidebar">
    <!-- 侧边栏头部 -->
    <div class="px-5 py-4 border-b border-slate-200 dark:border-slate-700">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i class="ti ti-shield-check text-white text-base"></i>
                </div>
                <h2 class="text-lg font-semibold text-slate-900 dark:text-white">管理控制台</h2>
            </div>
            <!-- 移动端关闭按钮 -->
            <button id="close-sidebar" class="lg:hidden p-2 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-white rounded-md hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors">
                <i class="ti ti-x"></i>
            </button>
        </div>
    </div>

    <!-- 侧边栏菜单 -->
    <nav class="px-4 py-3 overflow-y-auto relative" style="height: calc(100vh - 5rem);" role="navigation" aria-label="管理菜单">
        <ul class="space-y-1" role="list">
            {# 🔧 简化：直接显示所有菜单项，不做权限判断 #}
            <!-- 监控管理 -->
            {{ sidebar.sectionTitle('监控管理', marginTop='') }}
            {{ sidebar.menuItem('/admin/monitor', 'activity', '网络监控配置', null, 'text-base', '/admin/monitor' == request.path, 'monitor') }}
            {{ sidebar.menuItem('/admin/servers', 'server-2', '服务器管理', null, 'text-base', '/admin/servers' == request.path, 'servers') }}
            {{ sidebar.menuItem('/admin/groups', 'folders', '分组管理', null, 'text-base', '/admin/groups' == request.path, 'groups') }}
            {{ sidebar.menuItem('/admin/autodiscovery', 'device-tablet', '自动发现', null, 'text-base', '/admin/autodiscovery' == request.path, 'autodiscovery') }}

            <!-- 系统管理 -->
            {{ sidebar.sectionTitle('系统管理') }}
            {{ sidebar.menuItem('/admin/ssh_scripts', 'terminal-2', 'SSH脚本', null, 'text-base', '/admin/ssh_scripts' == request.path, 'ssh-scripts') }}
            {{ sidebar.menuItem('/admin/setting', 'settings', '系统设置', null, 'text-base', '/admin/setting' == request.path, 'setting') }}
            {{ sidebar.menuItem('/admin/notification', 'bell', '通知设置', null, 'text-base', '/admin/notification' == request.path, 'notification') }}
            {{ sidebar.menuItem('/admin/advanced-settings', 'adjustments', '高级设置', null, 'text-base', '/admin/advanced-settings' == request.path, 'advanced-settings') }}
            {{ sidebar.menuItem('/admin/personalization', 'brush', '美化设置', null, 'text-base', '/admin/personalization' == request.path, 'personalization') }}
            {{ sidebar.menuItem('/admin/log-management', 'file-text', '日志管理', null, 'text-base', '/admin/log-management' == request.path, 'log-management') }}
            {{ sidebar.menuItem('/admin/license-management', 'key', '授权管理', null, 'text-base', '/admin/license-management' == request.path, 'license-management') }}
            {{ sidebar.menuItem('/admin/analytics', 'chart-line', '高级分析', null, 'text-base', '/admin/analytics' == request.path, 'analytics') }}

            <!-- 返回前台 -->
            {{ sidebar.divider() }}
            {{ sidebar.menuItem('/', 'home', '返回前台', null, 'text-base', '/' == request.path, 'back-to-frontend') }}
        </ul>
    </nav>
</div>

<!-- 移动端菜单按钮 -->
<button id="mobile-toggle-sidebar" class="fixed top-20 left-4 lg:hidden p-2.5 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm text-slate-700 dark:text-slate-300 rounded-full shadow-md hover:shadow-lg z-40 hover:bg-white dark:hover:bg-slate-700 border border-slate-200/50 dark:border-slate-600/50 transition-all duration-300 hover:scale-105 active:scale-95">
    <i class="ti ti-menu-2 toggle-icon text-xl"></i>
</button>

<!-- 移动端遮罩层 -->
<div id="sidebar-overlay" class="fixed inset-0 bg-black/50 opacity-0 pointer-events-none z-20 transition-opacity duration-300 lg:hidden"></div>

<!-- 引入 AdminSidebar 组件 -->
<script src="/js/components/admin-sidebar.js"></script>

<!-- 简化移动端侧边栏脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 如果组件加载失败，使用简化的移动端控制
    if (typeof window.AdminSidebar === 'undefined') {
        const sidebar = document.getElementById('admin-sidebar');
        const mobileToggle = document.getElementById('mobile-toggle-sidebar');
        const closeButton = document.getElementById('close-sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        const mobileToggleIcon = mobileToggle ? mobileToggle.querySelector('.toggle-icon') : null;
        
        // 移动端切换侧边栏
        function toggleMobileSidebar() {
            const isOpen = document.body.classList.contains('mobile-sidebar-open');
            
            if (!isOpen) {
                // 打开菜单
                document.body.classList.add('mobile-sidebar-open', 'overflow-hidden');
                if (overlay) {
                    overlay.classList.remove('opacity-0', 'pointer-events-none');
                    overlay.classList.add('opacity-100');
                }
                if (mobileToggleIcon) {
                    mobileToggleIcon.className = 'ti ti-x toggle-icon text-xl';
                }
            } else {
                // 关闭菜单
                document.body.classList.remove('mobile-sidebar-open', 'overflow-hidden');
                if (overlay) {
                    overlay.classList.add('opacity-0', 'pointer-events-none');
                    overlay.classList.remove('opacity-100');
                }
                if (mobileToggleIcon) {
                    mobileToggleIcon.className = 'ti ti-menu-2 toggle-icon text-xl';
                }
            }
        }
        
        // 事件监听
        if (mobileToggle) {
            mobileToggle.addEventListener('click', toggleMobileSidebar);
        }
        
        if (closeButton) {
            closeButton.addEventListener('click', toggleMobileSidebar);
        }
        
        if (overlay) {
            overlay.addEventListener('click', toggleMobileSidebar);
        }
        
        // ESC键关闭
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && document.body.classList.contains('mobile-sidebar-open')) {
                toggleMobileSidebar();
            }
        });
    }
});
</script>