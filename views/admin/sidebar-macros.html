{# 管理后台侧边栏宏定义 #}

{# 菜单项宏 #}
{% macro menuItem(href, icon, text, badge=null, textSize='text-base', isActive=false, menuId=null) %}
<li role="none">
    <a href="{{ href }}" 
       class="sidebar-item group flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-white transition-colors relative focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-slate-900"
       role="menuitem"
       aria-current="{{ 'page' if isActive else 'false' }}"
       tabindex="0"
       {% if menuId %}data-menu-id="{{ menuId }}"{% endif %}>
        <i class="ti ti-{{ icon }} {{ textSize }} sidebar-icon flex-shrink-0" aria-hidden="true"></i>
        <span class="sidebar-text whitespace-nowrap">{{ text }}</span>
        {% if badge %}
            <span class="sidebar-badge ml-auto bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 text-xs px-2 py-0.5 rounded-full" aria-label="{{ badge }} 个新项目">{{ badge }}</span>
        {% endif %}
        {# Tooltip for collapsed state #}
        <div class="sidebar-tooltip absolute left-full ml-2 px-2 py-1 bg-slate-900 dark:bg-slate-700 text-white text-xs rounded whitespace-nowrap invisible opacity-0 group-hover:visible group-hover:opacity-100 transition-all duration-200 pointer-events-none z-50 hidden" role="tooltip">
            {{ text }}
        </div>
    </a>
</li>
{% endmacro %}

{# 分组标题宏 #}
{% macro sectionTitle(title, marginTop='mt-4') %}
<li class="{{ marginTop }}">
    <div class="sidebar-section-title px-3 py-2 text-xs font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider sidebar-text">{{ title }}</div>
</li>
{% endmacro %}

{# 分隔线宏 #}
{% macro divider(marginTop='mt-6', paddingTop='pt-3') %}
<li class="{{ marginTop }} {{ paddingTop }} border-t border-slate-200 dark:border-slate-700"></li>
{% endmacro %}