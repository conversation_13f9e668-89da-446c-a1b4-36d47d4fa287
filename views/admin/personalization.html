{% set title = "个性化设置" %}
{%set admin = true%}
{% extends "../base.html" %}

{%block styles%}
{%endblock%}

{%block content%}
<div class="flex flex-col lg:flex-row gap-6 justify-center" style="padding-top: calc(1.5rem + env(safe-area-inset-top));">
  <!-- 引入侧边栏 -->
  {% include "admin/sidebar.html" %}

  <!-- 主内容区域 - 单列布局 -->
  <div class="flex-1 space-y-6">
    
    <!-- 页面标题卡片 - 参考系统设置样式 -->
    <div class="admin-card">
      <div class="flex items-center gap-3 p-4">
        <div class="w-8 h-8 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/40 dark:to-purple-800/30 rounded-lg shadow-sm flex-shrink-0 border border-purple-200/50 dark:border-purple-700/30 flex items-center justify-center">
          <i class="ti ti-palette text-base text-purple-600 dark:text-purple-400"></i>
        </div>
        <div>
          <h1 class="text-lg font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">个性化设置</h1>
          <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">Personalization Settings</p>
        </div>
      </div>
    </div>

    <!-- 设置控制面板 - 全宽布局 -->
    <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700">
      <!-- 控制面板标题 -->
      <div class="px-6 py-4 border-b border-slate-200 dark:border-slate-700">
        <h2 class="text-lg font-semibold text-slate-800 dark:text-white flex items-center gap-2">
          <i class="ti ti-adjustments text-indigo-500"></i>
          壁纸设置
        </h2>
        <p class="text-sm text-slate-500 dark:text-slate-400 mt-1">自定义页面背景和视觉效果</p>
      </div>

      <!-- 设置内容 -->
      <div class="p-6 space-y-8">
        
        <!-- 启用开关 -->
        <div class="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-700/50 rounded-xl">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center">
              <i class="ti ti-power text-white text-lg"></i>
            </div>
            <div>
              <p class="font-medium text-slate-800 dark:text-white">启用壁纸</p>
              <p class="text-sm text-slate-500 dark:text-slate-400">开启自定义背景</p>
            </div>
          </div>
          <label class="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" id="wallpaper-enabled" 
                   {%if personalization and personalization.wallpaper and personalization.wallpaper.enabled%}checked{%endif%}
                   class="sr-only peer">
            <div class="w-11 h-6 bg-slate-300 dark:bg-slate-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
          </label>
        </div>

        <!-- 壁纸设置区域 -->
        <div id="wallpaper-settings" class="space-y-8" {%if not personalization or not personalization.wallpaper or not personalization.wallpaper.enabled%}style="display:none"{%endif%}>
          
          <!-- 壁纸URL输入 -->
          <div class="space-y-3">
            <label class="flex items-center gap-2 text-sm font-medium text-slate-700 dark:text-slate-300">
              <i class="ti ti-link text-lg text-blue-500"></i>
              壁纸URL
            </label>
            <input type="text" id="wallpaper-url"
                   value="{%if personalization and personalization.wallpaper and personalization.wallpaper.url%}{{personalization.wallpaper.url}}{%endif%}"
                   class="w-full px-4 py-3 bg-slate-50 dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                   placeholder="输入图片URL或选择预设壁纸">
          </div>

          <!-- 预设壁纸选择 - 优化布局 -->
          <div class="space-y-4">
            <label class="flex items-center gap-2 text-sm font-medium text-slate-700 dark:text-slate-300">
              <i class="ti ti-photo text-lg text-orange-500"></i>
              预设壁纸
            </label>
            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              <!-- 星空背景 -->
              <div class="wallpaper-preset-item p-3 border-2 border-slate-200 dark:border-slate-600 rounded-xl cursor-pointer hover:border-purple-400 hover:shadow-md transition-all duration-200 group" data-url="/bgsvg/starry-night.svg">
                <div class="bg-slate-100 dark:bg-slate-700 rounded-lg h-24 flex items-center justify-center overflow-hidden mb-3">
                  <img src="/bgsvg/starry-night.svg" alt="星空背景" class="w-full h-full object-cover group-hover:scale-105 transition-transform">
                </div>
                <p class="text-xs text-center text-slate-600 dark:text-slate-400 font-medium">星空背景</p>
              </div>
              
              <!-- 监控仪表板 -->
              <div class="wallpaper-preset-item p-3 border-2 border-slate-200 dark:border-slate-600 rounded-xl cursor-pointer hover:border-purple-400 hover:shadow-md transition-all duration-200 group" data-url="/bgsvg/monitor-dashboard.svg">
                <div class="bg-slate-100 dark:bg-slate-700 rounded-lg h-24 flex items-center justify-center overflow-hidden mb-3">
                  <img src="/bgsvg/monitor-dashboard.svg" alt="监控仪表板" class="w-full h-full object-cover group-hover:scale-105 transition-transform">
                </div>
                <p class="text-xs text-center text-slate-600 dark:text-slate-400 font-medium">监控仪表</p>
              </div>
              
              <!-- 数据流 -->
              <div class="wallpaper-preset-item p-3 border-2 border-slate-200 dark:border-slate-600 rounded-xl cursor-pointer hover:border-purple-400 hover:shadow-md transition-all duration-200 group" data-url="/bgsvg/data-stream.svg">
                <div class="bg-slate-100 dark:bg-slate-700 rounded-lg h-24 flex items-center justify-center overflow-hidden mb-3">
                  <img src="/bgsvg/data-stream.svg" alt="数据流" class="w-full h-full object-cover group-hover:scale-105 transition-transform">
                </div>
                <p class="text-xs text-center text-slate-600 dark:text-slate-400 font-medium">数据流</p>
              </div>
              
              <!-- 安全监控 -->
              <div class="wallpaper-preset-item p-3 border-2 border-slate-200 dark:border-slate-600 rounded-xl cursor-pointer hover:border-purple-400 hover:shadow-md transition-all duration-200 group" data-url="/bgsvg/security-monitor.svg">
                <div class="bg-slate-100 dark:bg-slate-700 rounded-lg h-24 flex items-center justify-center overflow-hidden mb-3">
                  <img src="/bgsvg/security-monitor.svg" alt="安全监控" class="w-full h-full object-cover group-hover:scale-105 transition-transform">
                </div>
                <p class="text-xs text-center text-slate-600 dark:text-slate-400 font-medium">安全监控</p>
              </div>
              
              <!-- 深空数据 -->
              <div class="wallpaper-preset-item p-3 border-2 border-slate-200 dark:border-slate-600 rounded-xl cursor-pointer hover:border-purple-400 hover:shadow-md transition-all duration-200 group" data-url="/bgsvg/deep-space.svg">
                <div class="bg-slate-100 dark:bg-slate-700 rounded-lg h-24 flex items-center justify-center overflow-hidden mb-3">
                  <img src="/bgsvg/deep-space.svg" alt="深空数据" class="w-full h-full object-cover group-hover:scale-105 transition-transform">
                </div>
                <p class="text-xs text-center text-slate-600 dark:text-slate-400 font-medium">深空数据</p>
              </div>
            </div>
          </div>

          <!-- 高级设置 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 左列设置 -->
            <div class="space-y-6 p-6 bg-slate-50 dark:bg-slate-700/30 rounded-xl">
              <h3 class="text-sm font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-2">
                <i class="ti ti-settings text-lg text-purple-500"></i>
                基础设置
              </h3>
              
              <!-- 填充模式 -->
              <div class="space-y-2">
                <label class="text-xs font-medium text-slate-600 dark:text-slate-400">填充模式</label>
                <select id="wallpaper-size" class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg text-sm text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-purple-500">
                  <option value="cover" {%if personalization and personalization.wallpaper and personalization.wallpaper.size == 'cover'%}selected{%endif%}>等比例填充</option>
                  <option value="100% 100%" {%if personalization and personalization.wallpaper and personalization.wallpaper.size == '100% 100%'%}selected{%endif%}>拉伸填充</option>
                  <option value="repeat" {%if personalization and personalization.wallpaper and personalization.wallpaper.size == 'repeat'%}selected{%endif%}>平铺填充</option>
                </select>
              </div>

              <!-- 亮度调节 -->
              <div class="space-y-2">
                <div class="flex justify-between items-center">
                  <label class="text-xs font-medium text-slate-600 dark:text-slate-400">亮度</label>
                  <output class="text-xs text-purple-600 dark:text-purple-400 font-medium" id="brightness-value">{%if personalization and personalization.wallpaper and personalization.wallpaper.brightness%}{{personalization.wallpaper.brightness}}{%else%}75{%endif%}%</output>
                </div>
                <input type="range" id="wallpaper-brightness" min="10" max="100" step="5"
                       value="{%if personalization and personalization.wallpaper and personalization.wallpaper.brightness%}{{personalization.wallpaper.brightness}}{%else%}75{%endif%}"
                       class="w-full h-2 bg-slate-200 dark:bg-slate-600 rounded-lg appearance-none cursor-pointer slider-purple">
              </div>

              <!-- 固定背景 -->
              <div class="flex items-center justify-between">
                <label class="text-xs font-medium text-slate-600 dark:text-slate-400 flex items-center gap-1">
                  <i class="ti ti-lock text-sm"></i>
                  固定背景
                </label>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" id="wallpaper-fixed"
                         {%if personalization and personalization.wallpaper and personalization.wallpaper.fixed%}checked{%endif%}
                         class="sr-only peer">
                  <div class="w-11 h-6 bg-slate-300 dark:bg-slate-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
                </label>
              </div>
            </div>

            <!-- 右列设置 -->
            <div class="space-y-6 p-6 bg-slate-50 dark:bg-slate-700/30 rounded-xl">
              <h3 class="text-sm font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-2">
                <i class="ti ti-blur text-lg text-indigo-500"></i>
                模糊效果
              </h3>

              <!-- 模糊效果 -->
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <label class="text-xs font-medium text-slate-600 dark:text-slate-400 flex items-center gap-1">
                    <i class="ti ti-blur text-sm"></i>
                    启用模糊
                  </label>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="wallpaper_blur_enabled"
                           {% if personalization and personalization.wallpaper and personalization.wallpaper.blur and personalization.wallpaper.blur.enabled %}checked{% endif %}
                           class="sr-only peer">
                    <div class="w-11 h-6 bg-slate-300 dark:bg-slate-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
                  </label>
                </div>
                
                <div id="wallpaper_blur_settings" class="space-y-2" {% if not personalization or not personalization.wallpaper or not personalization.wallpaper.blur or not personalization.wallpaper.blur.enabled %}style="display:none"{% endif %}>
                  <div class="flex justify-between items-center">
                    <label class="text-xs text-slate-600 dark:text-slate-400">强度</label>
                    <output class="text-xs text-purple-600 dark:text-purple-400 font-medium" id="blur-value">{{personalization.wallpaper.blur.amount|default(5)}}px</output>
                  </div>
                  <input type="range" id="wallpaper_blur_amount" min="0" max="20" step="1"
                         value="{{personalization.wallpaper.blur.amount|default(5)}}"
                         class="w-full h-2 bg-slate-200 dark:bg-slate-600 rounded-lg appearance-none cursor-pointer slider-purple">
                </div>
              </div>
            </div>
          </div>

          <!-- 界面毛玻璃效果 -->
          <div class="border-t border-slate-200 dark:border-slate-600 pt-4 mt-4">
            <h3 class="text-sm font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-2 mb-4">
              <i class="ti ti-layers-subtract text-lg text-purple-500"></i>
              界面效果
            </h3>
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <div>
                  <label class="text-xs font-medium text-slate-600 dark:text-slate-400 flex items-center gap-1">
                    <i class="ti ti-sparkles text-sm"></i>
                    卡片毛玻璃效果
                  </label>
                  <p class="text-[10px] text-slate-500 dark:text-slate-500 mt-1">为界面卡片启用半透明模糊背景</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" id="glassmorphism_enabled" class="sr-only peer">
                  <div class="w-11 h-6 bg-slate-300 dark:bg-slate-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-500"></div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="px-6 py-4 border-t border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-700/30 rounded-b-2xl">
        <div class="flex flex-col sm:flex-row gap-3">
          <button onclick="saveAllSettings()" 
                  class="flex-1 bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white font-medium py-3 px-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center gap-2">
            <div id="saveSpinner" class="hidden w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            <i class="ti ti-device-floppy text-lg"></i>
            保存设置
          </button>
          <button onclick="resetSettings()" 
                  class="px-6 py-3 bg-slate-200 dark:bg-slate-600 hover:bg-slate-300 dark:hover:bg-slate-500 text-slate-700 dark:text-slate-200 font-medium rounded-xl transition-colors flex items-center justify-center gap-2">
            <i class="ti ti-refresh text-lg"></i>
            重置
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 隐藏的设置数据 -->
<div id="personalization_data" class="hidden">
  {% if personalization %}
    {{personalization|dump|safe}}
  {% else %}
    {"wallpaper":{"enabled":false,"url":"","brightness":75,"fixed":false,"size":"cover","repeat":"repeat","blur":{"enabled":false,"amount":5}}}
  {% endif %}
</div>
{%endblock%}

{%block js%}
<script src="/js/glassmorphism-toggle.js"></script>
<script>
/**
 * @description 个性化设置页面JavaScript - 简化版（移除预览功能）
 * @modified 2025-01-26
 */

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeSettings();
    bindAllEvents();
});

// 初始化设置
function initializeSettings() {
    const settingDataElem = document.getElementById('personalization_data');
    let personalizationData = {};
    
    if (settingDataElem && settingDataElem.textContent) {
        try {
            personalizationData = JSON.parse(settingDataElem.textContent);
            console.log('初始化设置:', personalizationData);
            
            // 缓存到sessionStorage
            sessionStorage.setItem('personalization-settings', JSON.stringify(personalizationData));
            
            // 应用设置到表单
            applySettingsToForm(personalizationData);
        } catch (e) {
            console.error('解析设置失败:', e);
        }
    }
    
    // 初始化毛玻璃效果设置
    const glassSwitch = document.getElementById('glassmorphism_enabled');
    if (glassSwitch) {
        // 从localStorage读取设置
        const savedSetting = localStorage.getItem('glassmorphism_enabled');
        if (savedSetting !== null) {
            glassSwitch.checked = savedSetting === 'true';
            
            // 确保加载完成后应用设置
            setTimeout(() => {
                if (typeof window.applyGlassmorphism === 'function') {
                    window.applyGlassmorphism();
                }
            }, 100);
        }
    }
}

// 应用设置到表单
function applySettingsToForm(data) {
    if (!data.wallpaper) return;
    
    const wp = data.wallpaper;
    
    // 基本设置
    setElementValue('wallpaper-enabled', wp.enabled, 'checkbox');
    setElementValue('wallpaper-url', wp.url || '');
    setElementValue('wallpaper-brightness', wp.brightness || 75, 'range');
    setElementValue('wallpaper-fixed', wp.fixed, 'checkbox');
    setElementValue('wallpaper-size', wp.size || 'cover');
    
    // 模糊设置 - 修复0值处理
    if (wp.blur) {
        setElementValue('wallpaper_blur_enabled', wp.blur.enabled, 'checkbox');
        // 修复：确保0值不被||运算符替换
        const blurAmount = wp.blur.amount !== undefined ? wp.blur.amount : 5;
        setElementValue('wallpaper_blur_amount', blurAmount, 'range');
        
        // 显示/隐藏模糊设置
        const blurSettings = document.getElementById('wallpaper_blur_settings');
        if (blurSettings) {
            blurSettings.style.display = wp.blur.enabled ? 'block' : 'none';
        }
    }
    
    // 显示/隐藏主设置区域
    const wallpaperSettings = document.getElementById('wallpaper-settings');
    if (wallpaperSettings) {
        wallpaperSettings.style.display = wp.enabled ? 'block' : 'none';
    }
    
    // 高亮选中的预设壁纸
    if (wp.url) {
        highlightActivePreset(wp.url);
    }
    
    // 应用毛玻璃设置
    if (data.glassmorphism) {
        setElementValue('glassmorphism_enabled', data.glassmorphism.enabled, 'checkbox');
    }
}

// 设置表单元素值
function setElementValue(id, value, type = 'input') {
    const element = document.getElementById(id);
    if (!element) return;
    
    if (type === 'checkbox') {
        element.checked = Boolean(value);
    } else if (type === 'range') {
        element.value = value;
        updateRangeOutput(element);
    } else {
        element.value = value;
    }
}

// 更新范围输入的输出显示
function updateRangeOutput(rangeElement) {
    const id = rangeElement.id;
    let outputElement;
    
    if (id === 'wallpaper-brightness') {
        outputElement = document.getElementById('brightness-value');
        if (outputElement) outputElement.textContent = rangeElement.value + '%';
    } else if (id === 'wallpaper_blur_amount') {
        outputElement = document.getElementById('blur-value');
        if (outputElement) outputElement.textContent = rangeElement.value + 'px';
    }
}

// 高亮激活的预设壁纸
function highlightActivePreset(url) {
    const presetItems = document.querySelectorAll('.wallpaper-preset-item');
    presetItems.forEach(item => {
        const presetUrl = item.getAttribute('data-url');
        if (presetUrl === url) {
            item.classList.add('selected');
        } else {
            item.classList.remove('selected');
        }
    });
}

// 绑定所有事件
function bindAllEvents() {
    // 壁纸开关
    const wallpaperEnabled = document.getElementById('wallpaper-enabled');
    if (wallpaperEnabled) {
        wallpaperEnabled.addEventListener('change', function(e) {
            const wallpaperSettings = document.getElementById('wallpaper-settings');
            if (wallpaperSettings) {
                wallpaperSettings.style.display = e.target.checked ? 'block' : 'none';
            }
            applySettingsIfReady();
        });
    }
    
    // 壁纸URL
    const wallpaperUrl = document.getElementById('wallpaper-url');
    if (wallpaperUrl) {
        wallpaperUrl.addEventListener('input', function() {
            highlightActivePreset(this.value);
            applySettingsIfReady();
        });
    }
    
    // 填充模式
    const wallpaperSize = document.getElementById('wallpaper-size');
    if (wallpaperSize) {
        wallpaperSize.addEventListener('change', applySettingsIfReady);
    }
    
    // 亮度滑块
    const wallpaperBrightness = document.getElementById('wallpaper-brightness');
    if (wallpaperBrightness) {
        wallpaperBrightness.addEventListener('input', function(e) {
            updateRangeOutput(e.target);
            applySettingsIfReady();
        });
    }
    
    // 模糊开关
    const wallpaperBlurEnabled = document.getElementById('wallpaper_blur_enabled');
    if (wallpaperBlurEnabled) {
        wallpaperBlurEnabled.addEventListener('change', function() {
            const wallpaperBlurSettings = document.getElementById('wallpaper_blur_settings');
            if (wallpaperBlurSettings) {
                wallpaperBlurSettings.style.display = this.checked ? 'block' : 'none';
            }
            applySettingsIfReady();
        });
    }
    
    // 模糊强度滑块
    const wallpaperBlurAmount = document.getElementById('wallpaper_blur_amount');
    if (wallpaperBlurAmount) {
        wallpaperBlurAmount.addEventListener('input', function(e) {
            updateRangeOutput(e.target);
            applySettingsIfReady();
        });
    }
    
    // 固定背景
    const wallpaperFixed = document.getElementById('wallpaper-fixed');
    if (wallpaperFixed) {
        wallpaperFixed.addEventListener('change', applySettingsIfReady);
    }
    
    // 预设壁纸点击事件
    bindPresetEvents();
    
    // 毛玻璃开关事件
    const glassSwitch = document.getElementById('glassmorphism_enabled');
    if (glassSwitch) {
        glassSwitch.addEventListener('change', function() {
            // 保存到localStorage
            localStorage.setItem('glassmorphism_enabled', this.checked.toString());
            
            // 直接应用毛玻璃效果，不使用toggleGlassmorphism
            if (typeof window.applyGlassmorphism === 'function') {
                window.applyGlassmorphism();
            } else if (typeof toggleGlassmorphism === 'function') {
                toggleGlassmorphism();
            } else {
                console.warn('⚠️ 毛玻璃效果控制函数未找到');
            }
        });
    }
}

// 绑定预设壁纸事件
function bindPresetEvents() {
    const presetItems = document.querySelectorAll('.wallpaper-preset-item');
    const wallpaperUrl = document.getElementById('wallpaper-url');
    const wallpaperEnabled = document.getElementById('wallpaper-enabled');
    const wallpaperSettings = document.getElementById('wallpaper-settings');
    
    presetItems.forEach(item => {
        item.addEventListener('click', function() {
            const svgUrl = this.getAttribute('data-url');
            if (!svgUrl || !wallpaperUrl) return;
            
            // 预加载图片
            const preloadImg = new Image();
            preloadImg.onload = function() {
                wallpaperUrl.value = svgUrl;
                highlightActivePreset(svgUrl);
                
                // 自动启用壁纸
                if (wallpaperEnabled && !wallpaperEnabled.checked) {
                    wallpaperEnabled.checked = true;
                    if (wallpaperSettings) {
                        wallpaperSettings.style.display = 'block';
                    }
                }
                
                applySettingsIfReady();
                
                // 添加成功反馈
                notice('壁纸已选择', 'success');
            };
            
            preloadImg.onerror = function() {
                notice(`壁纸加载失败: ${svgUrl}`, 'error');
            };
            
            preloadImg.src = svgUrl;
        });
    });
}

// 应用设置（如果可以的话）
function applySettingsIfReady() {
    const settings = collectCurrentSettings();
    
    // 使用全局设置同步器立即应用设置
    if (window.settingsSync?.applyWallpaperSettings) {
        window.settingsSync.applyWallpaperSettings(settings);
    }
    
    // 缓存设置
    try {
        sessionStorage.setItem('personalization-settings', JSON.stringify(settings));
    } catch (e) {
        console.warn('无法缓存设置:', e);
    }
}

// 收集当前设置
function collectCurrentSettings() {
    // 修复模糊量的获取，确保0值不被替换为5
    const blurAmountElement = document.getElementById('wallpaper_blur_amount');
    const blurAmountValue = blurAmountElement ? parseInt(blurAmountElement.value) : 5;
    const blurAmount = isNaN(blurAmountValue) ? 5 : blurAmountValue; // 只有NaN时才用默认值5
    
    return {
        wallpaper: {
            enabled: document.getElementById('wallpaper-enabled')?.checked || false,
            url: document.getElementById('wallpaper-url')?.value?.trim() || '',
            brightness: parseInt(document.getElementById('wallpaper-brightness')?.value) || 75,
            fixed: document.getElementById('wallpaper-fixed')?.checked || false,
            size: document.getElementById('wallpaper-size')?.value || 'cover',
            repeat: 'repeat', // 简化处理
            blur: {
                enabled: document.getElementById('wallpaper_blur_enabled')?.checked || false,
                amount: blurAmount // 使用修复后的值，支持0px
            }
        },
        glassmorphism: {
            enabled: document.getElementById('glassmorphism_enabled')?.checked || false
        }
    };
}

// 保存设置
async function saveAllSettings() {
    const spinner = document.getElementById('saveSpinner');
    if (spinner) spinner.classList.remove('hidden');
    
    startloading();
    
    try {
        // 获取当前设置
        const settingDataElem = document.getElementById('personalization_data');
        let currentSettings = {};
        
        if (settingDataElem?.textContent) {
            try {
                currentSettings = JSON.parse(settingDataElem.textContent);
            } catch (e) {
                console.error('解析当前设置失败:', e);
                currentSettings = getDefaultSettings();
            }
        }
        
        // 收集新设置
        const newSettings = collectCurrentSettings();
        
        // 检测变化
        const changedSettings = detectChanges(currentSettings, newSettings);
        
        if (Object.keys(changedSettings).length === 0) {
            notice('没有检测到设置变化', 'info');
            return;
        }
        
        // 发送到服务器
        const response = await fetch('/admin/personalization', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ personalization: changedSettings })
        });
        
        const result = await response.json();
        
        if (result.code === 1) {
            // 合并并更新设置
            const mergedSettings = mergeSettings(currentSettings, newSettings);
            
            if (settingDataElem) {
                settingDataElem.textContent = JSON.stringify(mergedSettings);
            }
            
            // 更新缓存和通知其他页面
            sessionStorage.setItem('personalization-settings', JSON.stringify(mergedSettings));
            
            // 派发更新事件
            document.dispatchEvent(new CustomEvent('personalization-settings-updated', {
                detail: mergedSettings,
                bubbles: true
            }));
            
            // 通知其他窗口
            window.postMessage({
                type: 'personalization-settings-updated',
                settings: mergedSettings
            }, '*');
            
            notice('设置保存成功', 'success');
            
            // 立即应用
            if (window.settingsSync?.applyWallpaperSettings) {
                window.settingsSync.applyWallpaperSettings(mergedSettings);
            }
        } else {
            notice(result.msg || '设置保存失败', 'error');
        }
    } catch (error) {
        console.error('保存设置失败:', error);
        notice('保存设置失败: ' + error.message, 'error');
    } finally {
        if (spinner) spinner.classList.add('hidden');
        endloading();
    }
}

// 重置设置
async function resetSettings() {
    if (!confirm('确定要重置所有个性化设置吗？这将清除当前的壁纸和所有自定义配置。')) {
        return;
    }
    
    startloading();
    
    try {
        // 发送重置请求
        const response = await fetch('/admin/personalization', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                personalization: getDefaultSettings(),
                action: 'reset'
            })
        });
        
        const result = await response.json();
        
        if (result.code === 1) {
            // 更新页面数据
            const settingDataElem = document.getElementById('personalization_data');
            const defaultSettings = getDefaultSettings();
            
            if (settingDataElem) {
                settingDataElem.textContent = JSON.stringify(defaultSettings);
            }
            
            // 重新应用到表单
            applySettingsToForm(defaultSettings);
            
            // 清除缓存
            sessionStorage.removeItem('personalization-settings');
            
            // 立即应用重置的设置
            if (window.settingsSync?.applyWallpaperSettings) {
                window.settingsSync.applyWallpaperSettings(defaultSettings);
            }
            
            notice('设置已重置', 'success');
        } else {
            notice(result.msg || '设置重置失败', 'error');
        }
    } catch (error) {
        console.error('重置设置失败:', error);
        notice('重置设置失败: ' + error.message, 'error');
    } finally {
        endloading();
    }
}

// 获取默认设置
function getDefaultSettings() {
    return {
        wallpaper: {
            enabled: false,
            url: "",
            brightness: 75,
            fixed: false,
            size: "cover",
            repeat: "repeat",
            blur: { enabled: false, amount: 5 }
        },
        glassmorphism: {
            enabled: false
        }
    };
}

// 检测设置变化
function detectChanges(current, newSettings) {
    const changes = {};
    
    if (!current.wallpaper) current.wallpaper = {};
    
    const wp = newSettings.wallpaper;
    const currentWp = current.wallpaper;
    
    // 检查每个字段的变化
    if (currentWp.enabled !== wp.enabled) {
        changes.wallpaper = changes.wallpaper || {};
        changes.wallpaper.enabled = wp.enabled;
    }
    
    if (currentWp.url !== wp.url) {
        changes.wallpaper = changes.wallpaper || {};
        changes.wallpaper.url = wp.url;
    }
    
    if (currentWp.brightness !== wp.brightness) {
        changes.wallpaper = changes.wallpaper || {};
        changes.wallpaper.brightness = wp.brightness;
    }
    
    if (currentWp.fixed !== wp.fixed) {
        changes.wallpaper = changes.wallpaper || {};
        changes.wallpaper.fixed = wp.fixed;
    }
    
    if (currentWp.size !== wp.size) {
        changes.wallpaper = changes.wallpaper || {};
        changes.wallpaper.size = wp.size;
    }
    
    if (currentWp.repeat !== wp.repeat) {
        changes.wallpaper = changes.wallpaper || {};
        changes.wallpaper.repeat = wp.repeat;
    }
    
    // 检查模糊设置变化
    const currentBlur = currentWp.blur || {};
    const newBlur = wp.blur || {};
    
    if (currentBlur.enabled !== newBlur.enabled || currentBlur.amount !== newBlur.amount) {
        changes.wallpaper = changes.wallpaper || {};
        changes.wallpaper.blur = newBlur;
    }
    
    // 检查毛玻璃设置变化
    if (!current.glassmorphism) current.glassmorphism = {};
    const currentGlass = current.glassmorphism;
    const newGlass = newSettings.glassmorphism;
    
    if (currentGlass.enabled !== newGlass.enabled) {
        changes.glassmorphism = changes.glassmorphism || {};
        changes.glassmorphism.enabled = newGlass.enabled;
    }
    
    return changes;
}

// 深度合并设置
function mergeSettings(target, source) {
    const result = JSON.parse(JSON.stringify(target || {}));
    
    for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
            result[key] = mergeSettings(result[key] || {}, source[key]);
        } else {
            result[key] = source[key];
        }
    }
    
    return result;
}
</script>
{%endblock%}