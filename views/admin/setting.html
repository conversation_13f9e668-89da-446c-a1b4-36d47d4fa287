{% set title = "管理设置" %}
{%set admin = true%}
{% extends "../base.html" %}

{%block head%}
<!-- PWA支持 -->
<meta name="mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

<!-- 主题样式由 theme-manager.js 动态管理 -->

<!-- 页面特定样式 -->
<style>
    /* 确保SVG图标不受强调色影响 */
    .ti {
        color: inherit !important;
    }

    /* 正确设置强调色图标 */
    .text-blue-400 .ti,
    .text-blue-500 .ti {
        color: inherit !important;
    }

    /* 侧边栏菜单项图标颜色 */
    #admin-sidebar .ti {
        color: var(--color-slate-500) !important;
    }

    #admin-sidebar a:hover .ti,
    #admin-sidebar a.active .ti,
    #admin-sidebar .text-blue-400 .ti {
        color: var(--color-purple-400) !important;
    }
</style>

<!-- 背景加载脚本 -->
<script>
// 初始化背景设置
document.addEventListener('DOMContentLoaded', function() {
    const body = document.body;

    // 从localStorage获取背景设置
    try {
        // 尝试从localStorage中获取本地保存的背景设置
        const personalizationSettings = localStorage.getItem('personalization_settings');
        if (personalizationSettings) {
            const settings = JSON.parse(personalizationSettings);
            const background = settings.background;

            // 如果有背景图片
            if (background && background.type === 'image' && background.url) {
                body.classList.add('has-background-image');
                const darkenAmount = background.darken_amount || 60;
                body.style.backgroundImage = `linear-gradient(rgba(0, 0, 0, ${darkenAmount / 100}), rgba(0, 0, 0, ${darkenAmount / 100})), url("${background.url}")`;
            }
            // 如果是服务器保存的数据
            else {
                // 尝试从服务器设置中获取
                fetch('/api/personalization-settings')
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 1 && data.settings && data.settings.background) {
                            const background = data.settings.background;
                            if (background.type === 'image' && background.url) {
                                body.classList.add('has-background-image');
                                const darkenAmount = background.darken_amount || 60;
                                body.style.backgroundImage = `linear-gradient(rgba(0, 0, 0, ${darkenAmount / 100}), rgba(0, 0, 0, ${darkenAmount / 100})), url("${background.url}")`;
                            }
                        }
                    })
                    .catch(error => console.error('获取设置失败:', error));
            }
        }
    } catch (error) {
        console.error('加载背景设置错误:', error);
    }
});
</script>
{%endblock%}

{%block content%}
<!-- 页面容器 - 简化布局 -->
<div>
    <!-- 引入侧边栏 -->
    {% include "admin/sidebar.html" %}

    <!-- 主内容区域 - 独立卡片布局 -->
    <div class="flex-1 space-y-6">

        <!-- 页面标题卡片 - 独立设计 -->
        <div class="admin-card">
            <div class="admin-card-header">
                <div class="admin-page-header">
                    <div class="admin-page-title">
                        <div class="admin-title-icon admin-icon-purple">
                            <i class="ti ti-settings text-lg text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <div class="admin-title-text">
                            <h1>管理设置</h1>
                            <p class="text-xs">System Settings</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <textarea id="setting_data" class="hidden">{{setting|dump}}</textarea>

        <!-- 账户设置卡片 - 独立设计 -->
        <div class="admin-card">
            <!-- 卡片头部 - 使用标准admin-list-header类 -->
            <div class="admin-list-header flex items-center gap-3">
                <div class="w-8 h-8 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/40 dark:to-purple-800/30 rounded-lg shadow-sm flex-shrink-0 border border-purple-200/50 dark:border-purple-700/30 flex items-center justify-center">
                    <i class="ti ti-user-circle text-base text-purple-600 dark:text-purple-400"></i>
                </div>
                <div>
                    <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">账户设置</h2>
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">配置系统监听端口和管理密码</p>
                </div>
            </div>

            <!-- 卡片内容 - iPhone风格表单 -->
            <div class="p-4 space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">监听端口</label>
                        <input type="number"
                               value="{{setting.listen}}"
                               key="listen"
                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">系统Web界面监听的端口号</p>
                    </div>
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">管理密码</label>
                        <div class="relative">
                            <input type="password"
                                   value="{{setting.password}}"
                                   key="password"
                                   class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm pr-12">
                            <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-4 cursor-pointer toggle-password text-slate-500 hover:text-purple-600 dark:text-slate-400 dark:hover:text-purple-400 transition-colors duration-200">
                                <i class="ti ti-eye text-lg"></i>
                            </button>
                        </div>
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">用于访问管理界面的密码</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 站点设置卡片 - 独立设计 -->
        <div class="admin-card">
            <!-- 卡片头部 - 使用标准admin-list-header类 -->
            <div class="admin-list-header flex items-center gap-3">
                <div class="w-8 h-8 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/30 rounded-lg shadow-sm flex-shrink-0 border border-blue-200/50 dark:border-blue-700/30 flex items-center justify-center">
                    <i class="ti ti-world text-base text-blue-600 dark:text-blue-400"></i>
                </div>
                <div>
                    <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">站点设置</h2>
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">配置站点基本信息和显示主题</p>
                </div>
            </div>

            <!-- 卡片内容 - iPhone风格表单 -->
            <div class="p-4 space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">站点名称</label>
                        <input type="text"
                               value="{{setting.site.name}}"
                               key="site.name"
                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">显示在页面标题和导航栏的站点名称</p>
                    </div>
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">站点网址</label>
                        <input type="text"
                               value="{{setting.site.url}}"
                               key="site.url"
                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">站点的完整访问地址</p>
                    </div>
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">默认主题</label>
                        <select key="theme"
                                class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 cursor-pointer backdrop-blur-sm shadow-sm">
                            <option value="card" {%if setting.theme == "card"%}selected{%endif%}>卡片</option>
                            <option value="list" {%if setting.theme == "list"%}selected{%endif%}>列表</option>
                        </select>
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">用户访问时的默认显示主题</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 客户端设置卡片 - 独立设计 -->
        <div class="admin-card">
            <!-- 卡片头部 - 使用标准admin-list-header类 -->
            <div class="admin-list-header flex items-center gap-3">
                <div class="w-8 h-8 bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900/40 dark:to-green-800/30 rounded-lg shadow-sm flex-shrink-0 border border-green-200/50 dark:border-green-700/30 flex items-center justify-center">
                    <i class="ti ti-download text-base text-green-600 dark:text-green-400"></i>
                </div>
                <div>
                    <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">客户端设置</h2>
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">配置neko-status客户端下载链接</p>
                </div>
            </div>

            <!-- 卡片内容 - iPhone风格表单 -->
            <div class="p-4 space-y-4">
                <div class="space-y-3">
                    <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">neko-status 基础下载链接前缀</label>
                    <input type="text"
                           value="{{setting.neko_status_url}}"
                           key="neko_status_url"
                           placeholder="https://fev125.github.io/dstatus"
                           class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">推荐使用GitHub Pages链接前缀: https://fev125.github.io/dstatus</p>
                </div>

                <!-- 系统架构适配说明 - iPhone风格信息卡片 -->
                <div class="bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 rounded-xl p-4 border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm">
                    <h5 class="text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3 flex items-center gap-2">
                        <i class="ti ti-info-circle text-sm text-slate-500"></i>
                        系统架构适配说明
                    </h5>
                    <p class="text-xs text-slate-600 dark:text-slate-400 mb-4 leading-relaxed">安装脚本会自动检测系统架构并下载对应版本的客户端。下载链接格式为: <span class="text-purple-600 dark:text-purple-400 font-medium">基础链接前缀/neko-status_系统_架构</span></p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-white/60 dark:bg-slate-700/30 rounded-lg p-3 border border-slate-200/40 dark:border-slate-600/30">
                            <h6 class="text-xs font-semibold text-slate-700 dark:text-slate-300 mb-2 flex items-center gap-1">
                                <i class="ti ti-device-desktop text-xs text-green-600"></i>
                                支持的系统类型
                            </h6>
                            <ul class="text-xs text-slate-600 dark:text-slate-400 space-y-1">
                                <li class="flex items-center gap-2"><span class="w-1 h-1 bg-slate-400 rounded-full"></span>darwin (苹果 macOS)</li>
                                <li class="flex items-center gap-2"><span class="w-1 h-1 bg-slate-400 rounded-full"></span>linux (各种 Linux 发行版)</li>
                                <li class="flex items-center gap-2"><span class="w-1 h-1 bg-slate-400 rounded-full"></span>freebsd (FreeBSD)</li>
                                <li class="flex items-center gap-2"><span class="w-1 h-1 bg-slate-400 rounded-full"></span>openbsd (OpenBSD)</li>
                                <li class="flex items-center gap-2"><span class="w-1 h-1 bg-slate-400 rounded-full"></span>netbsd (NetBSD)</li>
                                <li class="flex items-center gap-2"><span class="w-1 h-1 bg-slate-400 rounded-full"></span>dragonfly (DragonFly BSD)</li>
                            </ul>
                        </div>
                        <div class="bg-white/60 dark:bg-slate-700/30 rounded-lg p-3 border border-slate-200/40 dark:border-slate-600/30">
                            <h6 class="text-xs font-semibold text-slate-700 dark:text-slate-300 mb-2 flex items-center gap-1">
                                <i class="ti ti-cpu text-xs text-blue-600"></i>
                                支持的架构类型
                            </h6>
                            <ul class="text-xs text-slate-600 dark:text-slate-400 space-y-1">
                                <li class="flex items-center gap-2"><span class="w-1 h-1 bg-slate-400 rounded-full"></span>amd64 (x86_64)</li>
                                <li class="flex items-center gap-2"><span class="w-1 h-1 bg-slate-400 rounded-full"></span>386 (x86)</li>
                                <li class="flex items-center gap-2"><span class="w-1 h-1 bg-slate-400 rounded-full"></span>arm64 (aarch64)</li>
                                <li class="flex items-center gap-2"><span class="w-1 h-1 bg-slate-400 rounded-full"></span>arm5/arm6/arm7 (ARM 系列)</li>
                                <li class="flex items-center gap-2"><span class="w-1 h-1 bg-slate-400 rounded-full"></span>mips/mips64/mipsle/mips64le</li>
                                <li class="flex items-center gap-2"><span class="w-1 h-1 bg-slate-400 rounded-full"></span>ppc64le/riscv64/s390x</li>
                                <li class="flex items-center gap-2"><span class="w-1 h-1 bg-slate-400 rounded-full"></span>universal (通用版本)</li>
                            </ul>
                        </div>
                    </div>

                    <div class="mt-4 bg-purple-50/60 dark:bg-purple-900/20 rounded-lg p-3 border border-purple-200/40 dark:border-purple-800/30">
                        <h6 class="text-xs font-semibold text-purple-700 dark:text-purple-300 mb-2 flex items-center gap-1">
                            <i class="ti ti-bulb text-xs text-purple-600"></i>
                            示例
                        </h6>
                        <p class="text-xs text-purple-600 dark:text-purple-400 leading-relaxed">如果基础链接前缀为 <span class="font-medium bg-purple-100/60 dark:bg-purple-800/30 px-1 rounded">https://example.com/files</span>，那么 Linux ARM64 系统的下载链接将是: <span class="font-medium bg-purple-100/60 dark:bg-purple-800/30 px-1 rounded">https://example.com/files/neko-status_linux_arm64</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 调试设置卡片 - 独立设计 -->
        <div class="admin-card">
            <!-- 卡片头部 - 使用标准admin-list-header类 -->
            <div class="admin-list-header flex items-center gap-3">
                <div class="w-8 h-8 bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/40 dark:to-orange-800/30 rounded-lg shadow-sm flex-shrink-0 border border-orange-200/50 dark:border-orange-700/30 flex items-center justify-center">
                    <i class="ti ti-bug text-base text-orange-600 dark:text-orange-400"></i>
                </div>
                <div>
                    <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">调试设置</h2>
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">开启调试模式以获取详细日志信息</p>
                </div>
            </div>

            <!-- 卡片内容 - iPhone风格开关 -->
            <div class="p-4">
                <div class="flex items-center justify-between bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 p-4 rounded-xl border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm">
                    <div class="flex items-center space-x-4">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox"
                                   {%if setting.debug%}checked{%endif%}
                                   key="debug"
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-slate-300 dark:bg-slate-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
                        </label>
                        <div>
                            <span class="text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">启用调试模式</span>
                            <p class="text-xs text-slate-500 dark:text-slate-400 mt-1 leading-relaxed">开启后将输出详细的系统运行日志</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- 数据库管理部分 -->
        <div class="admin-inner-card p-3 md:p-4 space-y-3 md:space-y-4 mt-4 md:mt-6">
            <h4 class="text-sm md:text-base font-medium text-slate-700 dark:text-slate-200">数据库管理</h4>
            <div class="flex flex-wrap items-center gap-2 md:gap-4">
                <button onclick="DatabaseManager.downloadBackup()"
                        class="inline-flex items-center px-3 md:px-4 py-1.5 md:py-2 text-xs md:text-sm bg-purple-500 hover:bg-purple-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-colors">
                    <span class="mr-1 md:mr-2">
                        <i class="ti ti-download text-[16px] md:text-[18px]"></i>
                    </span>
                    下载数据库备份
                </button>

                <button onclick="DatabaseManager.startRestore()"
                        class="inline-flex items-center px-3 md:px-4 py-1.5 md:py-2 text-xs md:text-sm bg-yellow-500 hover:bg-yellow-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-colors">
                    <span class="mr-1 md:mr-2">
                        <i class="ti ti-restore text-[16px] md:text-[18px]"></i>
                    </span>
                    恢复数据库
                </button>
            </div>

            <!-- 隐藏的文件输入 -->
            <input type="file"
                   id="dbFileInput"
                   accept=".db"
                   class="hidden"
                   onchange="DatabaseManager.handleFileSelect(this)">
        </div>

        <!-- 恢复进度对话框 -->
        <div id="restoreDialog" class="fixed inset-0 bg-black/50 hidden items-center justify-center backdrop-blur-sm z-50">
            <div class="bg-white dark:bg-slate-800 p-4 md:p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
                <div id="restoreProgress" class="space-y-3 md:space-y-4">
                    <!-- 上传状态 -->
                    <div id="uploadState" class="hidden">
                        <div class="flex items-center gap-3">
                            <div class="animate-spin rounded-full h-6 md:h-8 w-6 md:w-8 border-b-2 border-purple-500"></div>
                            <span class="text-slate-800 dark:text-slate-200 text-xs md:text-sm">正在上传数据库文件...</span>
                        </div>
                    </div>

                    <!-- 恢复状态 -->
                    <div id="restoreState" class="hidden">
                        <div class="flex items-center gap-3">
                            <div class="animate-bounce text-yellow-500">
                                <i class="ti ti-settings text-[18px] md:text-[20px]"></i>
                            </div>
                            <span class="text-slate-800 dark:text-slate-200 text-xs md:text-sm">正在恢复数据库...</span>
                        </div>
                    </div>

                    <!-- 重启状态 -->
                    <div id="restartState" class="hidden">
                        <div class="flex flex-col gap-2 md:gap-3">
                            <div class="flex items-center gap-3">
                                <div class="animate-spin text-green-500">
                                    <i class="ti ti-refresh text-[18px] md:text-[20px]"></i>
                                </div>
                                <span class="text-slate-800 dark:text-slate-200 text-xs md:text-sm">系统正在重启...</span>
                            </div>
                            <div class="text-xs md:text-sm text-slate-500 dark:text-slate-400" id="restartStatus"></div>
                        </div>
                    </div>

                    <!-- 成功状态 -->
                    <div id="successState" class="hidden">
                        <div class="text-center space-y-3 md:space-y-4">
                            <div class="text-green-500">
                                <i class="ti ti-circle-check text-3xl md:text-4xl"></i>
                            </div>
                            <div class="space-y-2">
                                <p class="font-medium text-slate-800 dark:text-slate-200 text-xs md:text-sm">数据库恢复完成</p>
                                <div class="text-xs md:text-sm text-slate-500 dark:text-slate-400">
                                    <p>✓ 数据库文件已更新</p>
                                    <p>✓ 备份已创建</p>
                                </div>
                                <div class="mt-3 md:mt-4 p-3 md:p-4 bg-slate-100 dark:bg-slate-700/50 rounded-md text-left">
                                    <p class="text-yellow-600 dark:text-yellow-400 font-medium mb-1 md:mb-2 text-xs md:text-sm">⚠️ 重要提示</p>
                                    <div class="text-slate-800 dark:text-slate-200 text-xs md:text-sm" id="restartMessage"></div>
                                </div>
                            </div>
                            <button onclick="DatabaseManager.closeDialog()"
                                    class="w-full px-3 md:px-4 py-1.5 md:py-2 text-xs md:text-sm bg-purple-500 hover:bg-purple-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-colors">
                                关闭
                            </button>
                        </div>
                    </div>

                    <!-- 错误状态 -->
                    <div id="errorState" class="hidden">
                        <div class="text-center space-y-3 md:space-y-4">
                            <div class="text-red-500">
                                <i class="ti ti-alert-circle text-3xl md:text-4xl"></i>
                            </div>
                            <p class="text-slate-800 dark:text-slate-200 text-xs md:text-sm" id="errorMessage"></p>
                            <button onclick="DatabaseManager.closeDialog()"
                                    class="w-full px-3 md:px-4 py-1.5 md:py-2 text-xs md:text-sm bg-purple-500 hover:bg-purple-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-colors">
                                关闭
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 保存按钮 -->
        <div class="mt-4 md:mt-6 flex justify-end">
            <button id="saveSettingsButton" onclick="edit()"
                    class="inline-flex items-center px-3 md:px-4 py-1.5 md:py-2 text-xs md:text-sm bg-purple-500 hover:bg-purple-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-colors">
                <i class="ti ti-device-floppy text-[16px] md:text-[18px] mr-1 md:mr-2"></i>
                保存设置
            </button>
        </div>
    </div>

    <!-- 移动端添加到主屏幕引导按钮 -->
    <div id="pwa-install-button" class="hidden fixed bottom-4 right-4 sm:hidden z-50">
        <button class="flex items-center space-x-2 px-3 py-1.5 text-xs bg-purple-500 text-white rounded-full shadow-lg backdrop-blur-sm bg-opacity-90 hover:bg-opacity-100 transition-all">
            <svg class="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
            </svg>
            <span>添加到主屏幕</span>
        </button>
    </div>

    <!-- 添加到主屏幕的引导弹窗 -->
    <div id="pwa-install-modal" class="hidden fixed inset-0 z-50">
        <div class="absolute inset-0 bg-black/60 backdrop-blur-sm"></div>
        <div class="absolute left-4 right-4 bottom-4 p-3 md:p-4 bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700/50 shadow-lg">
            <div class="flex items-start space-x-3 md:space-x-4">
                <div class="flex-shrink-0">
                    <img src="/img/logo.png" class="w-10 h-10 md:w-12 md:h-12 object-contain" alt="Logo">
                </div>
                <div class="flex-1">
                    <h3 class="text-base md:text-lg font-medium text-slate-800 dark:text-white mb-1">添加到主屏幕</h3>
                    <p class="text-xs md:text-sm text-slate-600 dark:text-gray-300 mb-3 md:mb-4">添加后可以快速访问节点状态</p>
                    <div class="text-xs text-slate-500 dark:text-gray-400 space-y-1 md:space-y-2">
                        <p>1. 点击浏览器底部分享按钮</p>
                        <p>2. 选择"添加到主屏幕"</p>
                    </div>
                </div>
            </div>
            <div class="mt-3 md:mt-4 flex justify-end space-x-2 md:space-x-3">
                <button onclick="dismissPWAGuide()" class="px-3 md:px-4 py-1.5 md:py-2 text-xs md:text-sm text-slate-500 dark:text-gray-400 hover:text-slate-700 dark:hover:text-gray-300">稍后再说</button>
                <button onclick="showAddToHomeScreen()" class="px-3 md:px-4 py-1.5 md:py-2 text-xs md:text-sm bg-purple-500 text-white rounded-md shadow-sm hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-colors">
                    立即添加
                </button>
            </div>
        </div>
    </div>
</div>
{%endblock%}

{%block js%}
<!-- 基础函数和工具 -->
<script src="/js/admin-buttons.js"></script>
<script>
function V(id){return document.getElementById(id).value;}
</script>

<!-- 设置管理脚本 -->
<script>
// 用于生成嵌套对象的辅助函数
function gen(x,keys,val){
    if(keys.length==0)return;
    var key=keys[0];
    keys.shift();
    if(keys.length==0)x[key]=val;
    else{
        if(!x[key])x[key]={};
        gen(x[key],keys,val)
    };
}

async function edit(){
    // 显示加载动画
    const spinner = document.getElementById('saveSpinner');
    if (spinner) spinner.classList.remove('hidden');

    try {
        var setting={};
        for(var x of document.querySelectorAll("[key]")){
            var val=x.value;
            if(x.type=="number")val=Number(x.value);
            if(x.type=="checkbox")val=x.checked;
            if(x.getAttribute("isarray"))val=val.split(",");
            gen(setting,x.getAttribute("key").split('.'),val);
        }
        console.log(setting);
        
        // 验证监听端口
        if(setting.listen !== undefined) {
            const listenPort = parseInt(setting.listen);
            if (listenPort < 1 || listenPort > 65535) {
                notice('监听端口必须在1-65535之间', 'error');
                return;
            }
        }
        
        for(var [key,val] of Object.entries(JSON.parse(V("setting_data"))))
            if(setting[key]==val)delete setting[key];

        const res = await fetch('/admin/setting', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(setting)
        }).then(r => r.json());

        if(res.code === 1){
            notice('保存成功', 'success');

            // 更新页面标题和导航栏标题，无需刷新整个页面
            if(setting.site && setting.site.name) {
                // 更新页面标题
                document.title = setting.site.name + ' - Dstatus';

                // 更新导航栏标题
                const siteNameElement = document.querySelector('a[href="/"] span.text-lg');
                if(siteNameElement) {
                    siteNameElement.textContent = setting.site.name;
                }

                console.log('已更新页面标题为:', setting.site.name);
            }

            // 更新设置数据缓存
            // 将新的设置合并到现有的设置中
            const currentSettings = JSON.parse(document.getElementById('setting_data').textContent);
            const updatedSettings = {...currentSettings, ...setting};
            document.getElementById('setting_data').textContent = JSON.stringify(updatedSettings);

            // 隐藏加载动画
            if (spinner) spinner.classList.add('hidden');
        } else {
            notice(res.msg || '保存失败', 'error');
            if (spinner) spinner.classList.add('hidden');
        }
    } catch (error) {
        console.error('保存设置失败:', error);
        notice('保存设置失败', 'error');
        if (spinner) spinner.classList.add('hidden');
    }
}


// 绑定事件监听器
document.addEventListener('DOMContentLoaded', function() {
    // 绑定密码显示/隐藏功能
    document.querySelectorAll('.toggle-password').forEach(toggle => {
        toggle.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
            input.setAttribute('type', type);

            // 切换图标
            const icon = this.querySelector('i');
            // Toggle Tabler icon classes
            if (type === 'password') {
                icon.className = 'ti ti-eye text-lg';
            } else {
                icon.className = 'ti ti-eye-off text-lg';
            }
        });
    });
});

document.addEventListener("keydown", (e)=>{
    if ((window.navigator.platform.match("Mac") ? e.metaKey : e.ctrlKey) && e.keyCode == 83) {
        e.preventDefault();
        edit();
    }
}, false);
</script>

<!-- 数据库管理脚本 -->
<script src="/js/database.js"></script>

<!-- 初始化数据库管理功能 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    if (typeof DatabaseManager === 'undefined') {
        console.error('DatabaseManager 未加载');
    }
});
</script>
{%endblock%}
