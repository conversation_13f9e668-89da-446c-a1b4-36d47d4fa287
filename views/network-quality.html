{%set title = "网络质量监控"%}
{%extends "./base.html"%}

{%block head%}
<!-- 引入ECharts库 -->
<script src="/js/libs/echarts.min.js"></script>

<!-- 引入节点卡片放大功能组件 -->
<link rel="stylesheet" href="/css/components/node-card-modal.css">
<link rel="stylesheet" href="/css/components/chart-enlarge-hints.css">
<script src="/js/utils/echarts-loader.js"></script>

<!-- 网络质量筛选和搜索组件现在使用 Tailwind CSS -->

<!-- 网络质量聚合页面样式 -->
<style>
.network-quality-grid {
    display: grid;
    grid-template-columns: 1fr; /* 默认1列 */
    gap: 1rem;
}

/* PC端优化：固定最多2列 */
@media (min-width: 768px) {
    .network-quality-grid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
        gap: 1.25rem;
    }
}

.network-status-card {
    border-radius: 12px;
    overflow: hidden;
}

/* 网络质量页面专用图表容器样式 - 避免与其他页面冲突 */
.network-quality-grid .chart-container {
    min-height: 220px !important;
    width: 100% !important;
    height: auto !important; /* 覆盖固定高度 */
    position: relative;
}

/* PC端优化：更紧凑的图表尺寸 */
@media (min-width: 1200px) {
    .network-quality-grid .chart-container {
        min-height: 250px !important;
    }
}

@media (min-width: 1600px) {
    .network-quality-grid .chart-container {
        min-height: 280px !important;
    }
}

@media (min-width: 2000px) {
    .network-quality-grid .chart-container {
        min-height: 320px !important;
    }
}

@media (min-width: 2400px) {
    .network-quality-grid .chart-container {
        min-height: 360px !important;
    }
}

.server-status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.server-status-online {
    background-color: #10b981;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.server-status-offline {
    background-color: #ef4444;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
}

.server-status-warning {
    background-color: #f59e0b;
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
}

.server-status-error {
    background-color: #dc2626;
    box-shadow: 0 0 8px rgba(220, 38, 38, 0.4);
}

.metric-badge {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1));
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    color: rgb(59, 130, 246);
    font-weight: 600;
    font-size: 0.875rem;
    text-align: center; /* 居中对齐标签与数值 */
}

/* 移动端优化 */
@media (max-width: 768px) {
    .network-quality-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .network-quality-grid .chart-container {
        min-height: 200px !important;
    }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1199px) {
    .network-quality-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 1rem;
    }
    
    .network-quality-grid .chart-container {
        min-height: 230px !important;
    }
}
</style>
{%endblock%}

{%block content%}

<!-- 数据存储区域 - 不再需要预加载数据，改为动态API调用 -->

<div class="space-y-4 lg:space-y-6">
    <!-- 页面标题和概览 - 压缩布局 -->
    <div class="card theme-border overflow-hidden">
        <div class="px-4 py-3">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                <div class="flex items-center gap-3">
                    <i class="ti ti-wifi text-primary-500 dark:text-primary-400 w-6 h-6"></i>
                    <div>
                        <h1 class="text-xl font-bold text-slate-800 dark:text-white flex items-center gap-2">
                            网络质量监控
                            <span id="overview-scope-label" class="hidden px-2 py-0.5 text-[11px] font-medium rounded bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-700/40">汇总（按筛选）</span>
                        </h1>
                        <p class="text-xs text-slate-600 dark:text-slate-400">实时监控所有节点的网络连接质量和延迟状况</p>
                    </div>
                </div>
                
                <!-- 概览统计 - 压缩尺寸 -->
                <div class="flex flex-wrap gap-2 lg:gap-3" id="overview-stats">
                    <div class="metric-badge">
                        <span class="block text-xs opacity-75">总节点</span>
                        <span class="text-sm font-bold" id="stat-total-nodes">--</span>
                    </div>
                    <div class="metric-badge" style="background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.1)); border-color: rgba(16, 185, 129, 0.2); color: rgb(16, 185, 129);">
                        <span class="block text-xs opacity-75">监控目标</span>
                        <span class="text-sm font-bold" id="stat-total-targets">--</span>
                    </div>
                    <div class="metric-badge" style="background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1)); border-color: rgba(59, 130, 246, 0.2); color: rgb(59, 130, 246);">
                        <span class="block text-xs opacity-75">平均延迟</span>
                        <span class="text-sm font-bold" id="stat-avg-latency">--</span>
                    </div>
                    <div class="metric-badge" style="background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(147, 51, 234, 0.1)); border-color: rgba(168, 85, 247, 0.2); color: rgb(168, 85, 247);">
                        <span class="block text-xs opacity-75">健康度</span>
                        <span class="text-sm font-bold" id="stat-health-score">--</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和筛选工具栏 - 一行集成设计 -->
    <div class="card theme-border">
        <div class="px-4 py-3">
            <!-- 主要搜索筛选区域 - 一行布局 -->
            <div class="flex flex-col lg:flex-row lg:items-center gap-3">
                <!-- 搜索组件容器 -->
                <div id="nq-search-container" class="flex-1 min-w-0"></div>

                <!-- 筛选器组 - 直接集成到一行 -->
                <div class="flex flex-wrap items-center gap-2 flex-shrink-0">
                    <!-- 分组筛选器 -->
                    <div class="relative" id="nq-group-filter">
                        <button class="nq-filter-btn flex items-center gap-1 px-3 py-2 text-sm border border-slate-200 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 hover:border-slate-300 dark:hover:border-slate-500 focus:outline-none transition-colors" type="button">
                            <i class="ti ti-folder w-4 h-4"></i>
                            <span class="nq-group-text">全部分组</span>
                            <i class="ti ti-chevron-down w-4 h-4"></i>
                        </button>
                        <div class="nq-dropdown-menu absolute top-full left-0 mt-1 min-w-40 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg shadow-lg z-50 opacity-0 invisible transform scale-95 transition-all duration-200 origin-top">
                            <div class="py-1 max-h-48 overflow-y-auto">
                                <div class="nq-dropdown-item active flex items-center justify-between px-3 py-2 hover:bg-slate-50 dark:hover:bg-slate-600 cursor-pointer transition-colors" data-group="all">
                                    <span class="text-sm text-slate-800 dark:text-slate-200">全部分组</span>
                                    <span class="nq-item-count text-xs text-slate-500 dark:text-slate-400">0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 地区筛选器 -->
                    <div class="relative" id="nq-region-filter">
                        <button class="nq-filter-btn flex items-center gap-1 px-3 py-2 text-sm border border-slate-200 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 hover:border-slate-300 dark:hover:border-slate-500 focus:outline-none transition-colors" type="button">
                            <i class="ti ti-world w-4 h-4"></i>
                            <span class="nq-region-text">全部地区</span>
                            <i class="ti ti-chevron-down w-4 h-4"></i>
                        </button>
                        <div class="nq-dropdown-menu absolute top-full left-0 mt-1 min-w-40 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg shadow-lg z-50 opacity-0 invisible transform scale-95 transition-all duration-200 origin-top">
                            <div class="py-1 max-h-48 overflow-y-auto">
                                <div class="nq-dropdown-item active flex items-center justify-between px-3 py-2 hover:bg-slate-50 dark:hover:bg-slate-600 cursor-pointer transition-colors" data-region="all">
                                    <span class="text-sm text-slate-800 dark:text-slate-200">全部地区</span>
                                    <span class="nq-item-count text-xs text-slate-500 dark:text-slate-400">0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 状态筛选器 -->
                    <div class="flex items-center gap-1">
                        <button class="nq-status-btn active px-2 py-1.5 text-xs border border-slate-200 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 hover:border-blue-300 dark:hover:border-blue-500 focus:outline-none transition-colors"
                                data-status="all" type="button">全部</button>
                        <button class="nq-status-btn px-2 py-1.5 text-xs border border-slate-200 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 hover:border-green-300 dark:hover:border-green-500 focus:outline-none transition-colors"
                                data-status="online" type="button">
                            <span class="w-1.5 h-1.5 bg-green-500 rounded-full inline-block mr-1"></span>在线
                        </button>
                        <button class="nq-status-btn px-2 py-1.5 text-xs border border-slate-200 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 hover:border-red-300 dark:hover:border-red-500 focus:outline-none transition-colors"
                                data-status="offline" type="button">
                            <span class="w-1.5 h-1.5 bg-red-500 rounded-full inline-block mr-1"></span>离线
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 时间范围选择器 - 压缩版 -->
    <div class="card theme-border overflow-hidden">
        <div class="px-4 py-2.5">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                <h3 class="text-base font-medium text-slate-800 dark:text-white flex items-center gap-2">
                    <i class="ti ti-clock text-primary-500 dark:text-primary-400 w-5 h-5"></i>
                    时间范围
                </h3>
                
                <div class="flex flex-col sm:flex-row sm:items-center gap-3">
                    <!-- 时间范围选择器 -->
                    <div class="flex flex-wrap gap-1.5">
                        <button class="time-range-btn active" data-range="1h">
                            <span>1小时</span>
                        </button>
                        <button class="time-range-btn" data-range="6h">
                            <span>6小时</span>
                        </button>
                        <button class="time-range-btn" data-range="24h">
                            <span>24小时</span>
                        </button>
                        <button class="time-range-btn" data-range="7d">
                            <span>7天</span>
                        </button>
                        <button class="time-range-btn" data-range="30d">
                            <span>30天</span>
                        </button>
                    </div>
                    
                    <!-- 刷新按钮、宽度切换和更新时间 -->
                    <div class="flex items-center gap-2">
                        <button id="width-toggle-btn" class="width-toggle-btn" title="切换全宽显示">
                            <i class="ti ti-arrows-maximize w-4 h-4"></i>
                            <span>全宽</span>
                        </button>
                        <button id="refresh-btn" class="refresh-btn">
                            <i class="ti ti-refresh w-4 h-4"></i>
                            <span>刷新</span>
                        </button>
                        <div class="text-xs text-slate-600 dark:text-slate-400">
                            <span class="hidden sm:inline">最后更新: </span>
                            <span id="last-updated-time" class="font-medium">--</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载状态 -->
    <div id="loading-container" class="card theme-border overflow-hidden">
        <div class="p-8">
            <div class="flex items-center justify-center">
                <div class="flex flex-col items-center gap-4">
                    <i class="ti ti-refresh w-16 h-16 text-slate-400"></i>
                    <span class="text-lg text-slate-500 dark:text-slate-400">加载网络质量数据中...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 节点网络质量详情 -->
    <div id="nodes-container" class="space-y-3 hidden transition-opacity duration-300">
        <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
            <h3 class="text-lg font-bold text-slate-800 dark:text-white flex items-center gap-2">
                <i class="ti ti-server text-primary-500 dark:text-primary-400 w-5 h-5"></i>
                节点网络质量监控
            </h3>
            <!-- 节点计数指示器 -->
            <div class="flex items-center gap-3 text-sm text-slate-600 dark:text-slate-400">
                <span id="node-counter" class="bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded-md font-medium">
                    显示 0 个节点（共 0 个）
                </span>
                <button id="scroll-to-top-btn" class="hidden p-1 rounded-md hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors" title="回到顶部">
                    <i class="ti ti-chevron-up w-4 h-4"></i>
                </button>
            </div>
        </div>
        
        <div class="network-quality-grid" id="nodes-grid">
            <!-- 节点将通过JavaScript动态生成 -->
        </div>
        
        <!-- 滚动提示 -->
        <div id="scroll-hint" class="hidden text-center py-2 text-sm text-slate-500 dark:text-slate-400">
            <i class="ti ti-chevron-down w-4 h-4 mr-1"></i>
            向下滚动查看更多节点
        </div>
    </div>

    <!-- 空状态 -->
    <div id="empty-container" class="card theme-border overflow-hidden hidden">
        <div class="p-8">
            <div class="flex flex-col items-center gap-4 text-center">
                <i class="ti ti-wifi w-16 h-16 text-slate-400"></i>
                <h3 class="text-xl font-semibold text-slate-800 dark:text-white">暂无网络质量监控数据</h3>
                <p class="text-slate-600 dark:text-slate-400">请确保已配置监控目标并且有节点在线</p>
            </div>
        </div>
    </div>

    <!-- 功能墙提示区域 -->
    <div id="feature-wall-container" class="hidden">
        <div class="card theme-border overflow-hidden">
            <div class="relative">
                <!-- 装饰性渐变背景 -->
                <div class="absolute inset-0 bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 dark:from-amber-900/20 dark:via-orange-900/10 dark:to-red-900/5"></div>
                
                <!-- 内容 -->
                <div class="relative p-6 sm:p-8">
                    <div class="flex flex-col lg:flex-row lg:items-center gap-6">
                        <!-- 左侧：图标和说明 -->
                        <div class="flex-1">
                            <div class="flex items-start gap-4">
                                <!-- 功能图标 -->
                                <div class="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-amber-100 to-orange-200 dark:from-amber-800/40 dark:to-orange-700/30 rounded-xl flex items-center justify-center border border-amber-200/50 dark:border-amber-700/30 shadow-lg">
                                    <i class="ti ti-activity w-8 h-8 text-amber-600 dark:text-amber-400"></i>
                                </div>
                                
                                <!-- 功能说明 -->
                                <div class="flex-1">
                                    <h3 class="text-xl font-bold text-slate-800 dark:text-white mb-2 flex items-center gap-2">
                                        网络质量监控
                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400 rounded-md border border-amber-200 dark:border-amber-700/50">
                                            <i class="ti ti-lock w-3 h-3 mr-1"></i>
                                            高级功能
                                        </span>
                                    </h3>
                                    <p class="text-slate-600 dark:text-slate-300 mb-4 leading-relaxed" id="feature-wall-description">
                                        实时监控所有节点的网络连接质量、延迟状况和连通性指标，提供详细的TCPing数据分析和历史趋势。
                                    </p>
                                    
                                    <!-- 当前套餐信息 -->
                                    <div class="flex flex-wrap items-center gap-3">
                                        <div class="inline-flex items-center px-3 py-1.5 bg-slate-100 dark:bg-slate-700/60 rounded-lg border border-slate-200 dark:border-slate-600/50">
                                            <i class="ti ti-user w-4 h-4 text-slate-500 dark:text-slate-400 mr-1.5"></i>
                                            <span class="text-sm font-medium text-slate-700 dark:text-slate-300">当前套餐：</span>
                                            <span class="text-sm font-bold text-blue-600 dark:text-blue-400 ml-1" id="current-plan-name">--</span>
                                        </div>
                                        <div class="inline-flex items-center px-3 py-1.5 bg-slate-100 dark:bg-slate-700/60 rounded-lg border border-slate-200 dark:border-slate-600/50" id="time-range-limit-info">
                                            <i class="ti ti-clock w-4 h-4 text-slate-500 dark:text-slate-400 mr-1.5"></i>
                                            <span class="text-sm text-slate-700 dark:text-slate-300" id="time-range-limit-text">数据查询限制：--</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧：升级按钮 -->
                        <div class="flex-shrink-0">
                            <div class="text-center lg:text-right">
                                <button id="upgrade-plan-btn" class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900">
                                    <i class="ti ti-arrow-up w-5 h-5"></i>
                                    <span>升级套餐</span>
                                </button>
                                <p class="text-xs text-slate-500 dark:text-slate-400 mt-2 max-w-32 lg:max-w-none">
                                    解锁所有高级功能
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 功能特性列表 (可选显示) -->
                    <div id="feature-benefits" class="hidden mt-6 pt-6 border-t border-amber-200/50 dark:border-amber-700/30">
                        <h4 class="text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3">升级后您将获得：</h4>
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                            <div class="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                                <i class="ti ti-circle-check w-4 h-4 text-green-500"></i>
                                <span>实时网络质量监控</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                                <i class="ti ti-circle-check w-4 h-4 text-green-500"></i>
                                <span>历史数据趋势分析</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                                <i class="ti ti-circle-check w-4 h-4 text-green-500"></i>
                                <span>多时间范围查询</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                                <i class="ti ti-circle-check w-4 h-4 text-green-500"></i>
                                <span>延迟和丢包率统计</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                                <i class="ti ti-circle-check w-4 h-4 text-green-500"></i>
                                <span>网络健康度评分</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                                <i data-lucide="check-circle" class="w-4 h-4 text-green-500"></i>
                                <span>可视化图表展示</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.time-range-btn {
    padding: 0.375rem 0.75rem;
    border: 1px solid rgb(226 232 240);
    background: white;
    color: rgb(71 85 105);
    border-radius: 0.375rem;
    font-size: 0.8125rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.time-range-btn:hover {
    border-color: rgb(59 130 246);
    color: rgb(59 130 246);
    background: rgba(59, 130, 246, 0.05);
}

.time-range-btn.active {
    border-color: rgb(59 130 246);
    background: rgb(59 130 246);
    color: white;
}

.time-range-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.dark .time-range-btn {
    border-color: rgb(51 65 85);
    background: rgb(30 41 59);
    color: rgb(203 213 225);
}

.dark .time-range-btn:hover {
    border-color: rgb(99 102 241);
    color: rgb(99 102 241);
    background: rgba(99, 102, 241, 0.1);
}

.dark .time-range-btn.active {
    border-color: rgb(99 102 241);
    background: rgb(99 102 241);
    color: white;
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid rgb(34 197 94);
    background: white;
    color: rgb(34 197 94);
    border-radius: 0.375rem;
    font-size: 0.8125rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.refresh-btn:hover {
    background: rgb(34 197 94);
    color: white;
}

.refresh-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.refresh-btn.loading {
    pointer-events: none;
}

.refresh-btn.loading [data-lucide] {
}

.dark .refresh-btn {
    border-color: rgb(34 197 94);
    background: rgb(30 41 59);
    color: rgb(34 197 94);
}

.dark .refresh-btn:hover {
    background: rgb(34 197 94);
    color: white;
}


.width-toggle-btn {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid rgb(168 85 247);
    background: white;
    color: rgb(168 85 247);
    border-radius: 0.375rem;
    font-size: 0.8125rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.width-toggle-btn:hover {
    background: rgb(168 85 247);
    color: white;
}

.width-toggle-btn.active {
    background: rgb(168 85 247);
    color: white;
}

.dark .width-toggle-btn {
    border-color: rgb(168 85 247);
    background: rgb(30 41 59);
    color: rgb(168 85 247);
}

.dark .width-toggle-btn:hover,
.dark .width-toggle-btn.active {
    background: rgb(168 85 247);
    color: white;
}

/* 全宽模式样式 */
body.full-width-mode #container {
    max-width: none !important;
    width: 100% !important;
}

/* 按钮图标与文字对齐统一样式 */
.width-toggle-btn,
.refresh-btn,
.nq-filter-btn,
.nq-status-btn {
    display: inline-flex;
    align-items: center;
    line-height: 1;
}

.width-toggle-btn i,
.refresh-btn i,
.nq-filter-btn i,
.nq-status-btn i {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1rem;   /* 16px */
    height: 1rem;  /* 16px */
    font-size: 1rem;
    line-height: 1;
    vertical-align: middle;
}

.width-toggle-btn span,
.refresh-btn span,
.nq-filter-btn span,
.nq-status-btn span {
    display: inline-flex;
    align-items: center;
    line-height: 1;
}

/* 筛选器按钮样式 */
.nq-status-btn.active,
.nq-filter-btn.active {
    border-color: rgb(59 130 246) !important;
    background-color: rgb(239 246 255) !important;
    color: rgb(29 78 216) !important;
}

.dark .nq-status-btn.active,
.dark .nq-filter-btn.active {
    border-color: rgb(99 102 241) !important;
    background-color: rgba(99, 102, 241, 0.2) !important;
    color: rgb(165 180 252) !important;
}

/* 下拉菜单样式 */
.nq-dropdown-menu {
    z-index: 1000;
    max-width: 280px;
    min-width: 160px;
}

.nq-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
    pointer-events: auto;
}

.nq-dropdown-item.active {
    background-color: rgb(239 246 255);
    color: rgb(29 78 216);
}

.dark .nq-dropdown-item.active {
    background-color: rgba(99, 102, 241, 0.2);
    color: rgb(165 180 252);
}

/* 确保下拉菜单不被容器裁剪 */
.card {
    overflow: visible !important;
}

.relative {
    z-index: auto;
}

.relative:focus-within {
    z-index: 10;
}

/* 搜索框状态文本优化 */
.nq-search-status-text {
    font-size: 0.75rem;
    line-height: 1rem;
    white-space: nowrap;
}

/* 展开筛选器动画 */
#nq-expanded-filters {
    transition: all 0.3s ease;
}

#nq-expanded-filters.hidden {
    opacity: 0;
    max-height: 0;
    overflow: hidden;
}

#nq-expanded-filters:not(.hidden) {
    opacity: 1;
    max-height: 500px;
}

/* 更多筛选按钮状态 */
#nq-more-filters-btn i {
    transition: transform 0.2s ease;
}

#nq-more-filters-btn.expanded i {
    transform: rotate(180deg);
}

/* 响应式优化 */
@media (max-width: 1024px) {
    /* 保持移动端显示文字 */
    .nq-filter-btn span:not([data-lucide]) {
        display: inline;
    }

    .nq-filter-btn {
        padding: 0.5rem;
    }
}

@media (max-width: 640px) {
    .nq-search-status-text {
        display: none !important;
    }

    .nq-status-btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .nq-dropdown-menu {
        position: fixed;
        left: 1rem;
        right: 1rem;
        top: auto;
        transform: none;
        max-width: none;
    }
}
</style>

<!-- 网络质量页面组件脚本 - 使用 Tailwind CSS 重构 -->
<script src="/js/components/network-quality-search.js"></script>
<script src="/js/components/network-quality-inline-filters.js"></script>
<script src="/js/components/network-quality-filter-manager.js"></script>

<!-- 网络质量功能墙脚本 -->
<script src="/js/pages/network-quality-feature-wall.js"></script>

<!-- 网络质量页面模块化脚本 -->
<script src="/js/pages/network-quality/state.js"></script>
<script src="/js/pages/network-quality/data.js"></script>
<script src="/js/pages/network-quality/charts.js"></script>
<script src="/js/pages/network-quality/ui.js"></script>
<script src="/js/pages/network-quality/main.js"></script>

{%endblock%}
