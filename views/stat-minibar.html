<!-- 系统状况卡片 (Minibar风格) -->
<div class="col-span-1 lg:col-span-2 card theme-border card-hover overflow-hidden">
    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center gap-2">
            <i class="ti ti-cpu text-primary-500 dark:text-primary-400 text-xl"></i>
            <h3 class="text-lg font-medium text-slate-800 dark:text-white">系统状况 (Minibar)</h3>
            <span id="system-os-minibar" class="text-sm text-slate-600 dark:text-slate-300"></span>
        </div>
    </div>
    <div class="p-4">
        <div class="space-y-4">
            <!-- CPU -->
            <div class="space-y-2">
                <div class="flex items-center gap-3">
                    <div class="flex-1">
                        <div class="flex justify-between">
                            <div class="flex items-center gap-1 text-slate-600 dark:text-slate-300">
                                <i class="ti ti-cpu text-lg"></i>
                                <span class="text-sm">CPU</span>
                                <span id="system-cpu-cores-minibar" class="text-sm text-slate-600 dark:text-slate-300"></span>
                            </div>
                            <span id="CPU-minibar" class="text-sm text-slate-800 dark:text-white">0%</span>
                        </div>
                    </div>
                </div>
                <!-- CPU柱状图容器 -->
                <div id="cpu-chart-minibar" class="w-[150px] h-[50px]"></div>
            </div>

            <!-- Memory -->
            <div class="space-y-2" id="MEM_item-minibar">
                <div class="flex items-center gap-3">
                    <div class="flex-1">
                        <div class="flex justify-between">
                            <div class="flex items-center gap-1 text-slate-600 dark:text-slate-300">
                                <i class="ti ti-device-sd-card text-lg"></i>
                                <span class="text-sm">内存</span>
                                <span id="mem-total-minibar" class="text-sm text-slate-600 dark:text-slate-300"></span>
                            </div>
                            <span id="MEM-minibar" class="text-sm text-slate-800 dark:text-white">0%</span>
                        </div>
                    </div>
                </div>
                <div class="flex items-center">
                    <!-- 内存饼图容器 -->
                    <div id="mem-chart-minibar" class="w-[50px] h-[50px] inline-block"></div>
                    <div id="mem-detail-minibar" class="text-xs text-slate-600 dark:text-slate-300 inline-block ml-2 align-middle">
                        0 B / 0 B
                    </div>
                </div>
            </div>

            <!-- SWAP -->
            <div class="space-y-2" id="SWAP_item-minibar">
                <div class="flex items-center gap-3">
                    <div class="flex-1">
                        <div class="flex justify-between">
                            <div class="flex items-center gap-1 text-slate-600 dark:text-slate-300">
                                <i class="ti ti-arrows-horizontal text-lg"></i>
                                <span class="text-sm">SWAP</span>
                                <span id="swap-total-minibar" class="text-sm text-slate-600 dark:text-slate-300"></span>
                            </div>
                            <span id="SWAP-minibar" class="text-sm text-slate-800 dark:text-white">0%</span>
                        </div>
                    </div>
                </div>
                <div class="flex items-center">
                    <!-- SWAP饼图容器 -->
                    <div id="swap-chart-minibar" class="w-[50px] h-[50px] inline-block"></div>
                    <div id="swap-detail-minibar" class="text-xs text-slate-600 dark:text-slate-300 inline-block ml-2 align-middle">
                        0 B / 0 B
                    </div>
                </div>
            </div>

            <!-- Disk Usage -->
            <div class="space-y-2" id="DISK_item-minibar">
                <div class="flex items-center gap-3">
                    <div class="flex-1">
                        <div class="flex justify-between">
                            <div class="flex items-center gap-1 text-slate-600 dark:text-slate-300">
                                <i class="ti ti-database text-lg"></i>
                                <span class="text-sm">硬盘</span>
                                <span id="disk-total-minibar" class="text-sm text-slate-600 dark:text-slate-300"></span>
                            </div>
                            <span id="DISK-minibar" class="text-sm text-slate-800 dark:text-white">0%</span>
                        </div>
                    </div>
                </div>
                <div class="flex items-center">
                    <!-- 硬盘饼图容器 -->
                    <div id="disk-chart-minibar" class="w-[50px] h-[50px] inline-block"></div>
                    <div id="disk-detail-minibar" class="text-xs text-slate-600 dark:text-slate-300 inline-block ml-2 align-middle">
                        0 B / 0 B
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
