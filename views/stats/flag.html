{# 根据国家代码显示对应的国旗图标 #}
{%- macro get_flag(country_code, flag_url, use_icon) -%}
    {%- if not use_icon and flag_url -%}
        <img src="{{ flag_url }}?v=1" alt="{{ country_code }}" class="flag-img" title="{{ country_code }}" loading="lazy" onerror="this.style.display='none'; var icon = document.createElement('i'); icon.className='ti ti-help-circle md-14'; icon.title='Flag loading failed'; this.parentNode.appendChild(icon);">
    {%- else -%}
        {# 使用Material Icons字体图标 #}
        {%- if country_code == "LO" -%}
            <i class="ti ti-home md-14" title="本地网络"></i>
        {%- elif country_code == "OT" -%}
            <i class="ti ti-world md-14" title="其他网络"></i>
        {%- else -%}
            <i class="ti ti-help-circle md-14" title="未知国家"></i>
        {%- endif -%}
    {%- endif -%}
{%- endmacro -%}

{# 获取国旗URL - 只使用新的数据结构 #}
{%- if node and node.data and node.data.location and node.data.location.code -%}
    {%- set country_code = node.data.location.code -%}
    {%- set flag_url = node.data.location.flag -%}

    {# 特殊情况处理 #}
    {%- if country_code == "LO" or country_code == "OT" or not country_code or country_code == "--" -%}
        {{ get_flag(country_code, null, true) }}
    {%- elif country_code == "UK" -%}
        {{ get_flag(country_code, "/img/flags/GB.SVG", false) }}
    {%- else -%}
        {{ get_flag(country_code, "/img/flags/" + country_code + ".SVG", false) }}
    {%- endif -%}
{%- else -%}
    {{ get_flag("unknown", null, true) }}
{%- endif -%}
