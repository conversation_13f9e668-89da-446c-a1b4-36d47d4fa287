<div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
    <div class="rounded-lg border border-slate-200 dark:border-slate-700 relative overflow-hidden bg-white dark:bg-slate-700 duration-150 ease-in-out">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center gap-2">
                    <i data-lucide="layout-dashboard" class="text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30 p-1.5 rounded-lg w-4 h-4"></i>
                    <h2 class="text-lg font-medium text-slate-800 dark:text-white">节点情况</h2>
                </div>
                <div class="flex items-center gap-2">
                    <div class="flex items-center gap-1 cursor-pointer expiry-filter bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg px-2 py-1 transition-all duration-200 border border-red-100 dark:border-red-800/30 hover:border-red-200 dark:hover:border-red-700/50" data-days="3" title="点击查看3天内到期节点">
                        <i data-lucide="timer" class="text-red-600 dark:text-red-400 w-4 h-4"></i>
                        <span class="text-xs text-red-700 dark:text-red-300">3天:</span>
                        <span class="text-sm text-red-800 dark:text-red-200 font-medium" id="expiring-nodes-3">0</span>
                    </div>
                    <div class="flex items-center gap-1 cursor-pointer expiry-filter bg-orange-50 dark:bg-orange-900/20 hover:bg-orange-100 dark:hover:bg-orange-900/30 rounded-lg px-2 py-1 transition-all duration-200 border border-orange-100 dark:border-orange-800/30 hover:border-orange-200 dark:hover:border-orange-700/50" data-days="7" title="点击查看7天内到期节点">
                        <i data-lucide="timer" class="text-orange-600 dark:text-orange-400 w-4 h-4"></i>
                        <span class="text-xs text-orange-700 dark:text-orange-300">7天:</span>
                        <span class="text-sm text-orange-800 dark:text-orange-200 font-medium" id="expiring-nodes-7">0</span>
                    </div>
                    <div class="flex items-center gap-1 cursor-pointer expiry-filter bg-yellow-50 dark:bg-yellow-900/20 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 rounded-lg px-2 py-1 transition-all duration-200 border border-yellow-100 dark:border-yellow-800/30 hover:border-yellow-200 dark:hover:border-yellow-700/50" data-days="30" title="点击查看30天内到期节点">
                        <i data-lucide="timer" class="text-yellow-600 dark:text-yellow-400 w-4 h-4"></i>
                        <span class="text-xs text-yellow-700 dark:text-yellow-300">30天:</span>
                        <span class="text-sm text-yellow-800 dark:text-yellow-200 font-medium" id="expiring-nodes-30">0</span>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="rounded-xl border border-slate-200 dark:border-slate-700 relative overflow-hidden bg-white dark:bg-slate-800 shadow cursor-pointer status-filter hover:bg-slate-50 dark:hover:bg-slate-800/80 p-3 transition-all duration-200" data-status="ALL" title="点击查看所有节点">
                    <div class="flex flex-col items-center">
                        <div class="text-2xl font-semibold text-slate-800 dark:text-white" id="total-nodes">0</div>
                        <div class="text-xs text-slate-600 dark:text-slate-400 flex items-center justify-center gap-1 mt-1">
                            <i data-lucide="monitor" class="text-indigo-600 dark:text-indigo-400 w-4 h-4"></i>
                            <span>总节点</span>
                        </div>
                    </div>
                </div>
                <div class="rounded-xl border border-slate-200 dark:border-slate-700 relative overflow-hidden bg-white dark:bg-slate-800 shadow cursor-pointer status-filter hover:bg-green-50 dark:hover:bg-green-900/30 p-3 transition-all duration-200" data-status="ONLINE" title="点击只查看在线节点">
                    <div class="flex flex-col items-center">
                        <div class="text-2xl font-semibold text-green-600 dark:text-green-400" id="online-nodes">0</div>
                        <div class="text-xs text-green-700 dark:text-green-300 flex items-center justify-center gap-1 mt-1">
                            <i data-lucide="check-circle" class="text-green-600 dark:text-green-400 w-4 h-4"></i>
                            <span>在线节点</span>
                        </div>
                    </div>
                </div>
                <div class="rounded-xl border border-slate-200 dark:border-slate-700 relative overflow-hidden bg-white dark:bg-slate-800 shadow cursor-pointer status-filter hover:bg-red-50 dark:hover:bg-red-900/30 p-3 transition-all duration-200" data-status="OFFLINE" title="点击只查看离线节点">
                    <div class="flex flex-col items-center">
                        <div class="text-2xl font-semibold text-red-600 dark:text-red-400" id="offline-nodes">0</div>
                        <div class="text-xs text-red-700 dark:text-red-300 flex items-center justify-center gap-1 mt-1">
                            <i data-lucide="alert-circle" class="text-red-600 dark:text-red-400 w-4 h-4"></i>
                            <span>离线节点</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-slate-50 dark:bg-slate-800/50 rounded-xl p-4 border border-slate-200/10 dark:border-slate-700/80">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-medium text-slate-600 dark:text-slate-400 flex items-center gap-1">
                        <i data-lucide="globe" class="text-indigo-600 dark:text-indigo-400 w-4 h-4"></i>
                        <span>地区分布</span>
                        <span class="text-xs text-slate-500 dark:text-slate-500">(点击可筛选)</span>
                    </h3>
                    <button id="refresh-region-stats" class="btn-text !text-xs !text-indigo-600 dark:!text-indigo-400 hover:!text-indigo-700 dark:hover:!text-indigo-300 !px-1 !py-0.5 hover:!bg-indigo-500/10 dark:hover:!bg-indigo-500/20">
                        <i data-lucide="refresh-cw" class="w-3 h-3 mr-0.5"></i>
                        <span>刷新</span>
                    </button>
                </div>
                <!-- 使用auto-fill和minmax实现自适应布局 -->
                <div class="grid grid-cols-4 gap-1.5 xs:gap-2 sm:gap-2 md:gap-3" id="region-stats" style="grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));">
                    {% for region in ['CN', 'US', 'JP', 'SG', 'LO', 'DE', 'CL', 'OT'] %}
                    <div class="flex items-center justify-between rounded-full px-2 py-1 border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 text-slate-800 dark:text-white shadow cursor-pointer transition-all duration-200 hover:bg-slate-50 dark:hover:bg-slate-700/50 min-w-[70px] max-w-full">
                        {% set code = region %}
                        <span class="text-sm mr-1">{% include "stats/flag.html" %}</span>
                        <span class="text-xs font-medium">{{region}}</span>
                        <span class="text-xs font-bold ml-1 region-count">0</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <div class="rounded-lg border border-slate-200 dark:border-slate-700 relative overflow-hidden bg-white dark:bg-slate-700 duration-150 ease-in-out">
        <div class="p-6">
            <div class="hidden md:flex items-center justify-between mb-6">
                <div class="flex items-center gap-2">
                    <i data-lucide="wifi" class="text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30 p-1.5 rounded-lg w-4 h-4"></i>
                    <h2 class="text-lg font-medium text-slate-800 dark:text-white">网络情况</h2>
                </div>
                <div class="flex items-center gap-1">
                    <span class="text-xs text-gray-500 dark:text-gray-400 mr-1" title="只影响前端动画效果，不影响数据获取频率">动画速率:</span>
                    <button id="animation-speed-normal" class="animation-speed-btn px-2 py-0.5 text-xs rounded bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800/70 active" title="正常动画速度 (只影响前端动画效果)">正常</button>
                    <button id="animation-speed-fast" class="animation-speed-btn px-2 py-0.5 text-xs rounded bg-gray-100 text-gray-700 dark:bg-gray-800/50 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700/70" title="较快动画速度 (只影响前端动画效果)">迅速</button>
                    <button id="animation-speed-slow" class="animation-speed-btn px-2 py-0.5 text-xs rounded bg-gray-100 text-gray-700 dark:bg-gray-800/50 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700/70" title="关闭动画效果 (只影响前端动画效果)">关闭</button>
                </div>
            </div>

            <div class="flex md:hidden items-center justify-between mb-4">
                <div class="flex items-center gap-2">
                    <i data-lucide="wifi" class="text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30 p-1.5 rounded-lg w-4 h-4"></i>
                    <h2 class="text-lg font-medium text-slate-800 dark:text-white">网络情况</h2>
                </div>
                <div class="flex items-center gap-1 ml-2" title="只影响前端动画效果，不影响数据获取频率">
                    <button id="animation-speed-normal-mobile" class="animation-speed-btn px-1.5 py-0.5 text-[10px] rounded bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800/70 active" title="正常动画速度 (只影响前端动画效果)">正常</button>
                    <button id="animation-speed-fast-mobile" class="animation-speed-btn px-1.5 py-0.5 text-[10px] rounded bg-gray-100 text-gray-700 dark:bg-gray-800/50 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700/70" title="较快动画速度 (只影响前端动画效果)">迅速</button>
                    <button id="animation-speed-slow-mobile" class="animation-speed-btn px-1.5 py-0.5 text-[10px] rounded bg-gray-100 text-gray-700 dark:bg-gray-800/50 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700/70" title="关闭动画效果 (只影响前端动画效果)">关闭</button>
                </div>
            </div>

            <div class="hidden md:grid grid-cols-1 gap-6">
                <div class="bg-slate-50 dark:bg-slate-800/50 rounded-xl p-4 border border-slate-200/10 dark:border-slate-700/80">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <div class="flex items-center mb-1.5">
                                <span class="inline-flex items-center justify-center font-bold w-6 h-6 text-green-500 dark:text-green-400 text-xl">↓</span>
                                <span class="text-2xl metric-number metric-large transition-all duration-200 ease-in-out" id="current-download-speed" data-unit="">--</span>
                            </div>
                            <div class="h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden relative" id="download-progress-container">
                                <div class="h-full rounded-full bg-green-500 dark:bg-green-400 transition-width duration-800 ease-network" style="width:0%" id="download-speed-progress"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center mb-1.5">
                                <span class="inline-flex items-center justify-center font-bold w-6 h-6 text-blue-500 dark:text-blue-400 text-xl">↑</span>
                                <span class="text-2xl metric-number metric-large transition-all duration-200 ease-in-out" id="current-upload-speed" data-unit="">--</span>
                            </div>
                            <div class="h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden relative" id="upload-progress-container">
                                <div class="h-full rounded-full bg-blue-500 dark:bg-blue-400 transition-width duration-800 ease-network" style="width:0%" id="upload-speed-progress"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-slate-50 dark:bg-slate-800/50 rounded-xl p-4 border border-slate-200/10 dark:border-slate-700/80">
                    <h3 class="text-sm font-medium text-slate-600 dark:text-slate-400 mb-3">总流量</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <div class="flex items-center">
                                <span class="inline-flex items-center justify-center font-bold w-6 h-6 text-green-500 dark:text-green-400 text-xl">↓</span>
                                <span class="text-xl font-medium metric-number transition-all duration-200 ease-in-out" id="total-download">--</span>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center">
                                <span class="inline-flex items-center justify-center font-bold w-6 h-6 text-blue-500 dark:text-blue-400 text-xl">↑</span>
                                <span class="text-xl font-medium metric-number transition-all duration-200 ease-in-out" id="total-upload">--</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="md:hidden grid grid-cols-1 gap-4">
                <div class="bg-slate-50 dark:bg-slate-800/50 rounded-xl p-4 border border-slate-200/10 dark:border-slate-700/80">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <div class="flex items-center justify-between mb-1">
                                <div class="flex items-center gap-1">
                                    <span class="text-green-500 dark:text-green-400 text-base">↓</span>
                                </div>
                                <span class="text-lg metric-number transition-all duration-200 ease-in-out mobile-download-speed" data-unit="">--</span>
                            </div>
                            <div class="h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden relative" id="mobile-download-progress-container">
                                <div class="h-full rounded-full bg-green-500 dark:bg-green-400 transition-width duration-800 ease-network" style="width:0%" id="mobile-download-speed-progress"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center justify-between mb-1">
                                <div class="flex items-center gap-1">
                                    <span class="text-blue-500 dark:text-blue-400 text-base">↑</span>
                                </div>
                                <span class="text-lg metric-number transition-all duration-200 ease-in-out mobile-upload-speed" data-unit="">--</span>
                            </div>
                            <div class="h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden relative" id="mobile-upload-progress-container">
                                <div class="h-full rounded-full bg-blue-500 dark:bg-blue-400 transition-width duration-800 ease-network" style="width:0%" id="mobile-upload-speed-progress"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="rounded-lg border border-slate-200 dark:border-slate-700 relative overflow-hidden bg-white dark:bg-slate-700 transition-shadow duration-150 ease-in-out mt-4 mb-4">
    <div class="p-3 overflow-x-auto hide-scrollbar">
        <div class="flex items-center justify-between flex-wrap gap-2">
            <div class="flex items-center gap-2 min-w-0 flex-wrap sm:flex-nowrap">
                <div class="sort-dropdown relative">
                    <button id="sort-dropdown-btn" class="group btn-text !h-8 !px-3 !text-xs sm:!text-sm hover:!border-indigo-500/70 dark:hover:!border-indigo-400/70 transition-all duration-200 shadow-sm hover:shadow-md rounded-lg" aria-haspopup="true" aria-expanded="false" aria-controls="sort-dropdown-menu">
                        <i data-lucide="arrow-up-down" class="w-4 h-4 mr-1 text-indigo-600 dark:text-indigo-400"></i>
                        <span id="current-sort-text" class="text-slate-800 dark:text-white font-medium">默认排序</span>
                        <i data-lucide="chevron-down" class="w-4 h-4 ml-1 text-slate-600 dark:text-slate-400 group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors duration-200"></i>
                    </button>
                </div>

                <div class="group-dropdown relative">
                    <button id="group-dropdown-btn" class="group btn-text !h-8 !px-3 !text-xs sm:!text-sm hover:!border-indigo-500/70 dark:hover:!border-indigo-400/70 transition-all duration-200 shadow-sm hover:shadow-md rounded-lg" aria-haspopup="true" aria-expanded="false" aria-controls="group-dropdown-menu">
                        <i data-lucide="folder" class="w-4 h-4 mr-1 text-indigo-600 dark:text-indigo-400"></i>
                        <span id="current-group-text" class="text-slate-800 dark:text-white font-medium">全部节点</span>
                        <i data-lucide="chevron-down" class="w-4 h-4 ml-1 text-slate-600 dark:text-slate-400 group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors duration-200"></i>
                    </button>
                </div>

                {% set viewToggleCurrentTheme = theme %}
                {% set viewToggleTargetTheme = 'card' if viewToggleCurrentTheme == 'list' else 'list' %}
                <a id="view-toggle-btn" href="/?theme={{ viewToggleTargetTheme }}" class="group btn-text !h-8 !px-3 !text-xs sm:!text-sm hover:!border-amber-500/70 dark:hover:!border-amber-400/70 transition-all duration-200 shadow-sm hover:shadow-md rounded-lg !flex !items-center !gap-2" onclick="handleViewToggle('{{ viewToggleCurrentTheme }}', '{{ viewToggleTargetTheme }}');">
                    <div class="w-4 h-4 flex items-center justify-center flex-shrink-0">
                        <i data-lucide="{%if viewToggleCurrentTheme == 'list'%}grid-3x3{%else%}list{%endif%}" class="w-4 h-4 text-amber-500 dark:text-amber-400"></i>
                    </div>
                    <span class="text-slate-800 dark:text-white font-medium">{%if viewToggleCurrentTheme == 'list'%}卡片{%else%}列表{%endif%}</span>
                </a>
            </div>

            {%if admin%}
            <div class="flex items-center gap-2 flex-wrap sm:flex-nowrap">
                <a href="/admin/servers/add" class="btn-text !h-8 !px-3 !text-xs sm:!text-sm hover:!border-green-500/70 dark:hover:!border-green-400/70 transition-all duration-200 shadow-sm hover:shadow-md rounded-lg">
                    <i data-lucide="plus" class="w-4 h-4 mr-1 text-green-500 dark:text-green-400"></i>
                    <span class="text-slate-800 dark:text-white font-medium">新增</span>
                </a>
                <a href="/admin/groups" class="btn-text !h-8 !px-3 !text-xs sm:!text-sm hover:!border-blue-500/70 dark:hover:!border-blue-400/70 transition-all duration-200 shadow-sm hover:shadow-md rounded-lg">
                    <i data-lucide="folder" class="w-4 h-4 mr-1 text-blue-500 dark:text-blue-400"></i>
                    <span class="text-slate-800 dark:text-white font-medium">分组</span>
                </a>
                <div class="hidden md:flex items-center text-xs sm:text-sm text-slate-800 dark:text-white">
                    <label class="flex items-center px-3 h-8 rounded-lg border hover:border-blue-500/70 dark:hover:border-blue-400/70 transition-all duration-200 cursor-pointer btn-text">
                        <input type="checkbox" id="enable-drag-sort" class="sr-only peer">
                        <div class="relative w-8 sm:w-9 h-4 sm:h-5 bg-slate-200 dark:bg-slate-600 rounded-full peer peer-checked:bg-indigo-500 dark:peer-checked:bg-indigo-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:after:bg-slate-900 after:rounded-full after:h-3 sm:after:h-4 after:w-3 sm:after:w-4 after:transition-all after:shadow-sm"></div>
                        <span class="ml-2 font-medium">拖拽</span>
                    </label>
                </div>
            </div>
            {%endif%}
        </div>
    </div>
</div>

<div id="sort-dropdown-menu" class="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl shadow-lg max-h-[400px] overflow-y-auto z-50 w-60 fixed mt-2 hidden" role="menu" aria-labelledby="sort-dropdown-btn">
    <div class="p-3">
        <div class="text-sm font-medium text-slate-700 dark:text-slate-300 px-1 pb-1 mb-2 border-b border-slate-200 dark:border-slate-600">选择排序方式</div>
        <div class="grid grid-cols-2 gap-2">
            <div>
                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option active" data-sort="default" data-direction="desc">
                    <span class="text-xs font-medium">默认排序</span>
                    <i data-lucide="chevron-down" class="w-3 h-3 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="name" data-direction="asc">
                    <span class="text-xs">名称</span>
                    <i data-lucide="chevrons-up-down" class="w-3 h-3 text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="total-traffic" data-direction="desc">
                    <span class="text-xs">总流量</span>
                    <i data-lucide="chevrons-up-down" class="w-3 h-3 text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="expiration" data-direction="asc">
                    <span class="text-xs">到期时间</span>
                    <i data-lucide="chevrons-up-down" class="w-3 h-3 text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="uptime" data-direction="desc">
                    <span class="text-xs">在线时间</span>
                    <i data-lucide="chevrons-up-down" class="w-3 h-3 text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>
            </div>

            <div>
                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="cpu" data-direction="desc">
                    <span class="text-xs">CPU使用率</span>
                    <i data-lucide="chevrons-up-down" class="w-3 h-3 text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="memory" data-direction="desc">
                    <span class="text-xs">内存使用率</span>
                    <i data-lucide="chevrons-up-down" class="w-3 h-3 text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="disk-usage" data-direction="desc">
                    <span class="text-xs">硬盘使用率</span>
                    <i data-lucide="chevrons-up-down" class="w-3 h-3 text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="download" data-direction="desc">
                    <span class="text-xs">下载速度</span>
                    <i data-lucide="chevrons-up-down" class="w-3 h-3 text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="upload" data-direction="desc">
                    <span class="text-xs">上传速度</span>
                    <i data-lucide="chevrons-up-down" class="w-3 h-3 text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>
            </div>
        </div>

        <div class="mt-3 pt-3 border-t border-slate-200 dark:border-slate-600">
            <label class="flex items-center justify-between px-2 py-1 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-600/50 transition-all duration-200 cursor-pointer">
                <div class="flex items-center gap-2">
                    <i data-lucide="rotate-cw" class="w-4 h-4 text-indigo-600 dark:text-indigo-400"></i>
                    <span class="text-xs font-medium text-slate-800 dark:text-white">实时排序</span>
                </div>
                <div class="relative">
                    <input type="checkbox" id="realtime-sort" class="sr-only peer" checked>
                    <div class="w-9 h-5 bg-slate-200 dark:bg-slate-600 rounded-full peer peer-checked:bg-indigo-500 dark:peer-checked:bg-indigo-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:after:bg-slate-900 after:rounded-full after:h-4 after:w-4 after:transition-all after:shadow-sm"></div>
                </div>
            </label>
        </div>
    </div>
</div>

<div id="group-dropdown-menu" class="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl shadow-lg max-h-[400px] overflow-y-auto z-50 w-60 fixed mt-2 hidden" role="menu" aria-labelledby="group-dropdown-btn">
    <div class="p-3">
        <div class="text-sm font-medium text-slate-700 dark:text-slate-300 px-1 pb-1 mb-2 border-b border-slate-200 dark:border-slate-600">选择分组</div>
        <div class="max-h-[300px] overflow-y-auto hide-scrollbar pr-1">
            <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400 rounded-lg group-option" data-group="all">
                <div class="flex items-center gap-2">
                    <i data-lucide="folder" class="w-4 h-4 text-indigo-600 dark:text-indigo-400"></i>
                    <span class="text-xs font-medium">全部节点</span>
                </div>
                <span class="text-xs text-slate-600 dark:text-slate-400 group-count font-medium">0</span>
            </div>

            {% for group in groups %}
            <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg group-option" data-group="{{group.id}}">
                <div class="flex items-center gap-2">
                    <i data-lucide="folder" class="w-4 h-4 text-slate-500 dark:text-slate-400"></i>
                    <span class="text-xs">{{group.name}}</span>
                </div>
                <span class="text-xs text-slate-600 dark:text-slate-400 group-count">0</span>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
// 为图表应用自定义主题颜色
function getThemeColors() {
    // 检查dark类是否存在来确定当前主题模式
    const isDark = document.documentElement.classList.contains('dark');

    // 设置默认颜色 - 使用indigo作为主色调
    const defaultLightColors = ['#4f46e5', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
    const defaultDarkColors = ['#6366f1', '#34d399', '#fbbf24', '#f87171', '#a78bfa'];

    // 初始化颜色数组
    let chartColors = isDark ? defaultDarkColors : defaultLightColors;

    // 尝试从CSS变量获取颜色
    const getComputedVar = (name) => getComputedStyle(document.documentElement).getPropertyValue(name).trim();

    // 检查是否存在主题变量
    if (getComputedVar('--accent-color')) {
        const accentColor = getComputedVar('--accent-color');
        chartColors[0] = accentColor; // 使用强调色作为第一个图表颜色
    }

    return chartColors;
}

// 使用新的主题监听器
function setupThemeChangeListener() {
    // 监听主题变化事件
    document.addEventListener('theme:changed', function(e) {
        if (!e.detail) return; // 防止空对象

        const isDark = e.detail.isDark;
        const theme = e.detail.theme || (isDark ? 'dark' : 'light'); // 确保始终有theme值

        console.log(`主题已切换: ${theme} (isDark: ${isDark})`);

        // 更新图表颜色
        updateChartThemes(isDark);
    });

    // 初始化时检查当前主题
    const isDark = document.documentElement.classList.contains('dark');
    updateChartThemes(isDark);

    console.log('主题变化监听器已设置');
}

// 更新图表主题
function updateChartThemes(isDark) {
    // 更新图表颜色（如果有）
    const charts = window.charts || [];
    for (const chartId in charts) {
        const chart = charts[chartId];
        if (chart && typeof chart.update === 'function') {
            // 更新图表主题
            const newOptions = getChartThemeOptions(isDark);
            chart.update({
                options: newOptions
            });
        }
    }
}

// 获取图表主题选项
function getChartThemeOptions(isDark) {
    return {
        theme: {
            mode: isDark ? 'dark' : 'light'
        },
        grid: {
            borderColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
        },
        tooltip: {
            theme: isDark ? 'dark' : 'light'
        },
        colors: getThemeColors()
    };
}

// 在页面加载时设置主题监听
document.addEventListener('DOMContentLoaded', setupThemeChangeListener);
</script>

<!-- 加载增强的度量样式系统 -->
<link rel="stylesheet" href="/css/components/metrics-enhanced.css">

<!-- 优先加载统一格式化函数 -->
<script src="/js/utils/MetricFormatter.js"></script>
<script src="/js/stats.js"></script>
<script src="/js/core.js"></script>
<script src="/js/animation.js"></script>
<script src="/js/tab-menu.js"></script>
<script src="/js/sort.js"></script>
<script src="/js/stats/cacheManager.js"></script>
<script src="/js/ConnectionManager.js"></script>
<script src="/js/DataProcessor.js"></script>
<script src="/js/dashboard.js"></script>
<div id="connection-status" class="fixed bottom-4 right-4 bg-red-500 text-white px-3 py-2 rounded-lg shadow-lg flex items-center gap-2 opacity-0 transition-opacity duration-500 z-50">
    <i data-lucide="wifi-off" class="w-4 h-4"></i>
    <span>连接已断开，正在重连...</span>
</div>

<script>
// 处理视图切换
function handleViewToggle(fromView, toView) {
    // 清除第一次数据标记
    localStorage.removeItem('stats_first_data_received');

    // 确保网络数据缓存不会被清除
    const networkDataCache = localStorage.getItem('network_data_cache');
    if (networkDataCache) {
        // 更新缓存时间戳，确保缓存不会过期
        try {
            const data = JSON.parse(networkDataCache);
            data.timestamp = Date.now();
            localStorage.setItem('network_data_cache', JSON.stringify(data));
            console.log('视图切换前已更新网络数据缓存时间戳');
        } catch (e) {
            console.error('更新网络数据缓存时间戳失败:', e);
        }
    }

    // 更新连接状态，确保连接保持活跃
    localStorage.setItem('stats_connection_timestamp', Date.now().toString());

    // 如果有SharedWorker客户端，请求最新数据
    if (window.sharedClient && typeof window.sharedClient.requestLastData === 'function') {
        // 添加延迟，确保在视图切换后请求数据
        setTimeout(() => {
            window.sharedClient.requestLastData();
        }, 300);
    }

    // 应用视图过渡效果
    if (window.SmoothTransition && typeof window.SmoothTransition.viewTransition === 'function') {
        window.SmoothTransition.viewTransition(fromView, toView);
    }
}

// 连接状态管理
document.addEventListener('DOMContentLoaded', function() {
    const connectionIndicator = document.getElementById('connection-status');
    let connectionTimer = null;

    // 监听连接状态变化
    document.addEventListener('connection:status', function(e) {
        if (!connectionIndicator) return;

        if (e.detail.status === 'disconnected') {
            // 设置一个计时器，只有断开超过3秒才显示
            clearTimeout(connectionTimer);
            connectionTimer = setTimeout(() => {
                connectionIndicator.style.opacity = '1';
            }, 3000);
        } else {
            clearTimeout(connectionTimer);
            connectionIndicator.style.opacity = '0';
        }
    });
});
</script>

<script src="/js/region-stats.js"></script>
<script src="/js/region-refresh.js"></script>