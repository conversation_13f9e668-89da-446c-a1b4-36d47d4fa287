<!-- 带宽监控卡片 (ECharts版本) -->
<div id="bandwidth-monitor-card" class="card theme-border card-hover overflow-hidden">
    <!-- 卡片标题和工具栏 -->
    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700/20 flex justify-between items-center">
        <div class="flex items-center gap-2">
            <i data-lucide="wifi" class="text-primary-500 dark:text-primary-400 w-5 h-5"></i>
            <h3 class="text-lg font-medium text-slate-800 dark:text-white">带宽监控 (ECharts)</h3>
        </div>
        <div class="flex items-center gap-2">
            <button id="bandwidth-monitor-chart-type-toggle" class="btn-text px-2 py-1 text-xs font-medium text-slate-300 hover:text-white bg-slate-800/50 hover:bg-slate-700/50 rounded-lg transition-colors flex items-center gap-1" title="切换图表类型">
                <i data-lucide="bar-chart" class="w-4 h-4"></i>
                <span class="hidden sm:inline">柱状图</span>
            </button>
        </div>
    </div>

    <!-- 标签页导航 -->
    <div class="flex items-center gap-2 mx-4 mt-4">
        <div class="flex bg-gray-100 dark:bg-slate-800 rounded-md overflow-hidden border border-gray-200 dark:border-slate-700" role="tablist" aria-label="带宽监控选项卡">
            <button data-tab="bandwidth-realtime" class="tab-button relative px-3 py-2 text-xs font-medium text-gray-700 dark:text-white bg-blue-50 dark:bg-slate-700 border-r border-gray-200 dark:border-slate-700 flex items-center justify-center min-w-[80px]" role="tab" aria-controls="bandwidth-realtime" aria-selected="true" tabindex="0">
                <i data-lucide="gauge" class="w-4 h-4 mr-1 text-primary-500"></i>
                <span>实时3分钟</span>
                <!-- 激活指示器 -->
                <span class="absolute bottom-0 left-0 right-0 h-0.5 bg-primary-500"></span>
            </button>
            <button data-tab="bandwidth-60m" class="tab-button relative px-3 py-2 text-xs font-medium text-gray-500 dark:text-slate-300 hover:bg-gray-50 dark:hover:bg-slate-700 hover:text-gray-700 dark:hover:text-white border-r border-gray-200 dark:border-slate-700 flex items-center justify-center min-w-[80px]" role="tab" aria-controls="bandwidth-60m" aria-selected="false" tabindex="-1">
                <i data-lucide="clock" class="w-4 h-4 mr-1"></i>
                <span>过去24小时</span>
            </button>
            <button data-tab="bandwidth-24h" class="tab-button relative px-3 py-2 text-xs font-medium text-gray-500 dark:text-slate-300 hover:bg-gray-50 dark:hover:bg-slate-700 hover:text-gray-700 dark:hover:text-white flex items-center justify-center min-w-[80px]" role="tab" aria-controls="bandwidth-24h" aria-selected="false" tabindex="-1">
                <i data-lucide="calendar" class="w-4 h-4 mr-1"></i>
                <span>过去24小时</span>
            </button>
        </div>
    </div>

    <!-- 标签页内容 -->
    <div class="p-4">
        <!-- 实时带宽图表 -->
        <div id="bandwidth-realtime" class="w-full relative h-auto" role="tabpanel" aria-labelledby="bandwidth-realtime-tab">
            <div id="bandwidth-realtime-chart" class="chart-container w-full h-[350px] md:h-[280px] lg:h-[400px] relative overflow-visible"></div>
        </div>

        <!-- 分钟带宽图表 -->
        <div id="bandwidth-60m" class="w-full relative h-auto hidden" role="tabpanel" aria-labelledby="bandwidth-60m-tab" hidden>
            <div id="bandwidth-60m-chart" class="chart-container w-full h-[350px] md:h-[280px] lg:h-[400px] relative overflow-visible"></div>
        </div>

        <!-- 小时带宽图表 -->
        <div id="bandwidth-24h" class="w-full relative h-auto hidden" role="tabpanel" aria-labelledby="bandwidth-24h-tab" hidden>
            <div id="bandwidth-24h-chart" class="chart-container w-full h-[350px] md:h-[280px] lg:h-[400px] relative overflow-visible"></div>
        </div>
    </div>
</div>
