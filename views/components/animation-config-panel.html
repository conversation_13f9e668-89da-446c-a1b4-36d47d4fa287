<!-- 动画配置面板 -->
<div id="animation-config-panel" class="hidden fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- 背景遮罩 -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        <!-- 居中对齐 -->
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <!-- 面板内容 -->
        <div class="inline-block align-bottom bg-white dark:bg-slate-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-slate-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            实时带宽动画配置
                        </h3>
                        <div class="mt-4 space-y-4">
                            <!-- 动画持续时间 -->
                            <div>
                                <label for="animation-duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    动画持续时间 (毫秒)
                                </label>
                                <div class="mt-1 flex items-center">
                                    <input type="range" id="animation-duration" name="animation-duration" min="500" max="3000" step="100" class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="animation-duration-value" class="ml-2 w-16 text-sm text-gray-700 dark:text-gray-300">1500</span>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    数值变化动画的持续时间，值越大动画越慢
                                </p>
                            </div>
                            
                            <!-- 下载速度更新间隔 -->
                            <div>
                                <label for="download-update-interval" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    下载速度更新间隔 (毫秒)
                                </label>
                                <div class="mt-1 flex items-center">
                                    <input type="range" id="download-update-interval" name="download-update-interval" min="50" max="1000" step="50" class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="download-update-interval-value" class="ml-2 w-16 text-sm text-gray-700 dark:text-gray-300">200</span>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    下载速度数值更新的频率，值越大更新越慢
                                </p>
                            </div>
                            
                            <!-- 上传速度更新间隔 -->
                            <div>
                                <label for="upload-update-interval" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    上传速度更新间隔 (毫秒)
                                </label>
                                <div class="mt-1 flex items-center">
                                    <input type="range" id="upload-update-interval" name="upload-update-interval" min="50" max="1000" step="50" class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="upload-update-interval-value" class="ml-2 w-16 text-sm text-gray-700 dark:text-gray-300">300</span>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    上传速度数值更新的频率，值越大更新越慢
                                </p>
                            </div>
                            
                            <!-- 平滑因子 -->
                            <div>
                                <label for="smooth-factor" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    平滑因子
                                </label>
                                <div class="mt-1 flex items-center">
                                    <input type="range" id="smooth-factor" name="smooth-factor" min="0.1" max="0.9" step="0.1" class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="smooth-factor-value" class="ml-2 w-16 text-sm text-gray-700 dark:text-gray-300">0.3</span>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    数值变化的平滑程度，值越小过渡越平滑
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-slate-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="save-animation-config" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                    保存
                </button>
                <button type="button" id="reset-animation-config" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-slate-600 dark:text-white dark:border-slate-500 dark:hover:bg-slate-500">
                    重置
                </button>
                <button type="button" id="close-animation-config" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-slate-600 dark:text-white dark:border-slate-500 dark:hover:bg-slate-500">
                    取消
                </button>
            </div>
        </div>
    </div>
</div>
