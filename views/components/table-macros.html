{# 
  Admin表格宏组件
  提供统一的表格样式和功能
#}

{# 标准管理表格组件 #}
{% macro adminTable(
  id="", 
  columns=[], 
  hasCheckbox=false,
  hasPagination=true,
  colorScheme="slate"
) %}
<!-- 移动端卡片布局 -->
<div class="sm:hidden space-y-3" id="{{ id }}-mobile-container">
    <div id="{{ id }}-mobile-cards" class="space-y-3">
        <!-- 移动端卡片通过JavaScript动态生成 -->
    </div>
</div>

<!-- 桌面端表格布局 -->
<div class="hidden sm:block overflow-x-auto rounded-lg shadow-sm border-0">
    <table class="min-w-full divide-y divide-slate-200/60 dark:divide-slate-800/60">
        <thead class="bg-slate-50 dark:bg-slate-700">
            <tr>
                {% if hasCheckbox %}
                <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                    <input type="checkbox" id="select-all-{{ id }}" 
                           class="rounded border-slate-300 text-blue-600 focus:ring-blue-500 transition-all duration-200">
                </th>
                {% endif %}
                
                {% for column in columns %}
                <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider transition-all duration-200
                          {% if column.sortable %} cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-600{% endif %}"
                    {% if column.sortable %}onclick="sortTable('{{ column.key }}')"{% endif %}>
                    {{ column.label }}
                    {% if column.sortable %}
                    <i class="ti ti-arrows-sort text-sm ml-1"></i>
                    {% endif %}
                </th>
                {% endfor %}
            </tr>
        </thead>
        <tbody id="{{ id }}-tbody" class="bg-white dark:bg-slate-800 divide-y divide-slate-200/60 dark:divide-slate-800/60">
            <!-- 数据行将通过JavaScript动态生成 -->
        </tbody>
    </table>
</div>

{% if hasPagination %}
    {{ tablePagination(id, colorScheme) }}
{% endif %}
{% endmacro %}

{# 分页组件 #}
{% macro tablePagination(tableId, colorScheme="slate") %}
<div class="bg-white dark:bg-slate-800 px-4 py-3 border-t border-slate-200/60 dark:border-slate-800/60 sm:px-6">
    <div class="flex items-center justify-between">
        <!-- 移动端分页 -->
        <div class="flex-1 flex justify-between sm:hidden">
            <button id="prev-page-mobile-{{ tableId }}" 
                    class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-slate-700 bg-white border border-slate-300/60 rounded-lg shadow-sm hover:bg-slate-100 dark:hover:bg-slate-600 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-slate-800 dark:text-slate-200 dark:border-slate-700/20">
                上一页
            </button>
            <button id="next-page-mobile-{{ tableId }}" 
                    class="ml-3 inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-slate-700 bg-white border border-slate-300/60 rounded-lg shadow-sm hover:bg-slate-100 dark:hover:bg-slate-600 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-slate-800 dark:text-slate-200 dark:border-slate-700/20">
                下一页
            </button>
        </div>
        
        <!-- 桌面端分页 -->
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-slate-700 dark:text-slate-300">
                    显示第 <span id="start-index-{{ tableId }}">1</span> 到 
                    <span id="end-index-{{ tableId }}">20</span> 条记录，
                    共 <span id="total-records-{{ tableId }}">0</span> 条
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button id="prev-page-{{ tableId }}" 
                            class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-slate-700 bg-white border border-slate-300/60 rounded-l-lg border-r-0 shadow-sm hover:bg-slate-100 dark:hover:bg-slate-600 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-slate-800 dark:text-slate-200 dark:border-slate-700/20">
                        <i class="ti ti-chevron-left text-sm"></i>
                    </button>
                    <div id="page-numbers-{{ tableId }}" class="flex">
                        <!-- 页码按钮将通过JavaScript动态生成 -->
                    </div>
                    <button id="next-page-{{ tableId }}" 
                            class="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-slate-700 bg-white border border-slate-300/60 rounded-r-lg border-l-0 shadow-sm hover:bg-slate-100 dark:hover:bg-slate-600 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-slate-800 dark:text-slate-200 dark:border-slate-700/20">
                        <i class="ti ti-chevron-right text-sm"></i>
                    </button>
                </nav>
            </div>
        </div>
    </div>
</div>
{% endmacro %}

{# 空数据提示组件 #}
{% macro emptyState(
    icon="ti-database-off",
    title="暂无数据",
    description="当前没有任何记录",
    actionText="",
    actionUrl=""
) %}
<div class="text-center py-12">
    <div class="w-16 h-16 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center mx-auto mb-4">
        <i class="ti {{ icon }} text-2xl text-slate-400"></i>
    </div>
    <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">{{ title }}</h3>
    <p class="text-slate-600 dark:text-slate-400 mb-6 max-w-md mx-auto">{{ description }}</p>
    {% if actionText and actionUrl %}
    <a href="{{ actionUrl }}" class="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
        <i class="ti ti-plus text-sm"></i>
        {{ actionText }}
    </a>
    {% endif %}
</div>
{% endmacro %}