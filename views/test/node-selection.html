{% extends "base.html" %}

{% block head %}
<!-- 抽屉组件专用样式 -->
<style>
/* 抽屉容器样式 */
.drawer-container {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    max-width: 480px;
    height: 100vh;
    background: white;
    border-left: 1px solid #e0e0e0;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease-out;
}

.drawer-container.open {
    transform: translateX(0);
}

.drawer-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-out;
}

.drawer-backdrop.open {
    opacity: 1;
    visibility: visible;
}

/* 紧凑样式 */
.section {
    border-bottom: 1px solid #e0e0e0;
    padding: 12px;
}

.compact-input, .compact-select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 13px;
}

.action-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    margin-right: 8px;
    margin-bottom: 4px;
}

.action-btn:hover {
    background: #2563eb;
}

.mode-btn {
    background: #6b7280;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    margin-right: 4px;
}

.mode-btn.active {
    background: #10b981;
}

.tag-button {
    display: inline-block;
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    margin: 2px;
}

.tag-button.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.node-card {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 8px;
    margin: 4px 0;
    background: white;
    cursor: pointer;
    transition: all 0.2s;
}

.node-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.node-card.selected {
    border-color: #10b981;
    background: #f0fdf4;
}

.node-card.excluded {
    border-color: #ef4444;
    background: #fef2f2;
}

.node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.node-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.country-flag {
    font-size: 16px;
}

.node-status {
    text-align: right;
}

.node-meta {
    margin-top: 4px;
}

.tag-chip {
    display: inline-block;
    background: #e5e7eb;
    color: #374151;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    margin-right: 4px;
}

.group-header {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 8px 0 4px 0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.group-actions {
    display: flex;
    gap: 4px;
}

.group-action-btn {
    background: #6b7280;
    color: white;
    border: none;
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 10px;
    cursor: pointer;
}

.group-action-btn:hover {
    background: #4b5563;
}

.collapsible-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.collapsible-content.expanded {
    max-height: 2000px;
}

.checkbox-node {
    margin-right: 8px;
}

/* 测试控制样式 */
.test-controls {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1001;
}

.test-btn {
    background: #8b5cf6;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    margin-right: 8px;
}

.test-btn:hover {
    background: #7c3aed;
}

.debug-log {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 300px;
    max-height: 200px;
    background: #1f2937;
    color: #f9fafb;
    border-radius: 6px;
    padding: 8px;
    font-family: monospace;
    font-size: 11px;
    overflow-y: auto;
    z-index: 1002;
}
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- 主页面内容 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">节点选择测试</h1>
            <button id="openDrawer" class="test-btn">打开选择抽屉</button>
        </div>

        <!-- 基础信息显示 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                <div class="text-sm text-blue-600 dark:text-blue-300">总节点数</div>
                <div class="text-2xl font-bold text-blue-900 dark:text-blue-100" id="totalNodes">0</div>
            </div>
            <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                <div class="text-sm text-green-600 dark:text-green-300">在线节点</div>
                <div class="text-2xl font-bold text-green-900 dark:text-green-100" id="onlineNodes">0</div>
            </div>
            <div class="bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg">
                <div class="text-sm text-yellow-600 dark:text-yellow-300">筛选节点</div>
                <div class="text-2xl font-bold text-yellow-900 dark:text-yellow-100" id="filteredNodes">0</div>
            </div>
            <div class="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg">
                <div class="text-sm text-purple-600 dark:text-purple-300">选中节点</div>
                <div class="text-2xl font-bold text-purple-900 dark:text-purple-100" id="selectedNodes">0</div>
            </div>
        </div>

        <!-- 简化的筛选控件（主页面用） -->
        <div class="mb-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">分组筛选</label>
                    <select id="groupFilter" class="compact-select">
                        <option value="">所有分组</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">国家筛选</label>
                    <select id="countryFilter" class="compact-select">
                        <option value="">所有国家</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">标签筛选</label>
                    <div id="tagFilters" class="flex flex-wrap gap-1"></div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">搜索</label>
                    <input type="text" id="searchInput" placeholder="搜索节点..." class="compact-input">
                </div>
            </div>
        </div>

        <!-- 基础筛选选项 -->
        <div class="mb-4">
            <div class="flex flex-wrap gap-4">
                <label class="flex items-center">
                    <input type="checkbox" id="onlineOnly" class="mr-2">
                    <span class="text-sm">仅显示在线节点</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="highPerformance" class="mr-2">
                    <span class="text-sm">仅显示高性能节点</span>
                </label>
            </div>
        </div>

        <!-- 选择模式 -->
        <div class="mb-4">
            <div class="flex items-center gap-4">
                <span class="text-sm font-medium">选择模式:</span>
                <button id="includeMode" class="mode-btn active">包含模式</button>
                <button id="excludeMode" class="mode-btn">排除模式</button>
                <span class="text-sm text-gray-500">当前: <span id="currentMode">包含模式</span></span>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="mb-4 action-bar">
            <button id="selectAll" class="action-btn">全选</button>
            <button id="selectNone" class="action-btn">全不选</button>
            <button id="selectOnline" class="action-btn">选择在线</button>
            <button id="clearFilters" class="action-btn">清除筛选</button>
            <button id="toggleCollapse" class="action-btn">收起所有</button>
        </div>

        <!-- 节点列表 -->
        <div id="nodeList" class="min-h-[400px]">
            <div class="flex items-center justify-center p-8">
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p class="mt-2 text-sm text-gray-600">正在加载节点数据...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 选择抽屉 -->
<div id="nodeSelectionBackdrop" class="drawer-backdrop"></div>
<div id="nodeSelectionDrawer" class="drawer-container">
    <div class="section">
        <div class="flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-900">选择监控节点</h2>
            <button id="closeDrawer" class="text-gray-500 hover:text-gray-700">✕</button>
        </div>
    </div>

    <!-- 抽屉内的筛选控件 -->
    <div class="section">
        <div class="space-y-3">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">分组</label>
                <select id="drawerGroupFilter" class="compact-select">
                    <option value="">所有分组</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">国家</label>
                <select id="drawerCountryFilter" class="compact-select">
                    <option value="">所有国家</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
                <input type="text" id="drawerSearchInput" placeholder="搜索节点..." class="compact-input">
            </div>
        </div>
    </div>

    <!-- 抽屉内的选择模式 -->
    <div class="section">
        <div class="flex items-center gap-2">
            <span class="text-sm font-medium">模式:</span>
            <button id="drawerIncludeMode" class="mode-btn active">包含</button>
            <button id="drawerExcludeMode" class="mode-btn">排除</button>
        </div>
        <div class="text-xs text-gray-500 mt-1">
            当前: <span id="drawerCurrentMode">包含模式</span>
        </div>
    </div>

    <!-- 抽屉内的操作按钮 -->
    <div class="section">
        <div class="flex flex-wrap gap-1">
            <button id="drawerSelectAll" class="action-btn">全选</button>
            <button id="drawerSelectNone" class="action-btn">全不选</button>
            <button id="drawerSelectOnline" class="action-btn">选择在线</button>
            <button id="drawerClearFilters" class="action-btn">清除筛选</button>
        </div>
    </div>

    <!-- 抽屉内的统计信息 -->
    <div class="section">
        <div class="text-sm text-gray-600">
            <div>筛选: <span id="drawerFilterStats">0/0</span></div>
            <div>选中: <span id="drawerSelectedCount">0</span></div>
            <div>实际: <span id="drawerActualCount">0</span></div>
        </div>
    </div>

    <!-- 抽屉内的节点列表 -->
    <div class="flex-1 overflow-y-auto">
        <div id="drawerNodeList">
            <div class="flex items-center justify-center p-8">
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p class="mt-2 text-sm text-gray-600">正在加载节点数据...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 抽屉底部操作 -->
    <div class="section border-t">
        <div class="flex gap-2">
            <button id="applySelection" class="action-btn flex-1">应用选择</button>
            <button id="cancelSelection" class="action-btn bg-gray-500 hover:bg-gray-600">取消</button>
        </div>
    </div>
</div>

<!-- 测试控制面板 -->
<div class="test-controls">
    <div class="text-sm font-medium mb-2">测试控制</div>
    <div class="flex flex-wrap gap-1">
        <button id="testOpenDrawer" class="test-btn">打开抽屉</button>
        <button id="testCloseDrawer" class="test-btn">关闭抽屉</button>
        <button id="testLoadData" class="test-btn">重新加载</button>
        <button id="testShowLog" class="test-btn">显示日志</button>
    </div>
</div>

<!-- 调试日志 -->
<div id="debugLog" class="debug-log" style="display: none;">
    <div class="text-xs text-gray-300 mb-2">调试日志</div>
    <div id="debugLogContent"></div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 调试日志系统
let debugLogs = [];
function addDebugLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;
    debugLogs.push(logEntry);
    console.log(logEntry);
    
    const logContent = document.getElementById('debugLogContent');
    if (logContent) {
        logContent.innerHTML = debugLogs.slice(-20).map(log => `<div>${log}</div>`).join('');
        logContent.scrollTop = logContent.scrollHeight;
    }
}

// 测试控制功能
document.addEventListener('DOMContentLoaded', function() {
    const debugLog = document.getElementById('debugLog');
    const testShowLog = document.getElementById('testShowLog');
    
    if (testShowLog) {
        testShowLog.addEventListener('click', function() {
            debugLog.style.display = debugLog.style.display === 'none' ? 'block' : 'none';
        });
    }
    
    addDebugLog('📋 测试页面DOM加载完成');
});

// ================================
// NodeSelectionAPI 类 (API抽象层)
// ================================
class NodeSelectionAPI {
    constructor() {
        this.baseURL = window.location.origin;
        this.cache = new Map();
        this.cacheTimeout = 30000; // 30秒缓存
        
        // 国家代码映射
        this.countryMap = {
            'CN': { name: '中国', flag: '🇨🇳' },
            'HK': { name: '香港', flag: '🇭🇰' },
            'TW': { name: '台湾', flag: '🇹🇼' },
            'JP': { name: '日本', flag: '🇯🇵' },
            'KR': { name: '韩国', flag: '🇰🇷' },
            'SG': { name: '新加坡', flag: '🇸🇬' },
            'US': { name: '美国', flag: '🇺🇸' },
            'CA': { name: '加拿大', flag: '🇨🇦' },
            'GB': { name: '英国', flag: '🇬🇧' },
            'UK': { name: '英国', flag: '🇬🇧' },
            'DE': { name: '德国', flag: '🇩🇪' },
            'FR': { name: '法国', flag: '🇫🇷' },
            'AU': { name: '澳大利亚', flag: '🇦🇺' },
            'VN': { name: '越南', flag: '🇻🇳' },
            'TH': { name: '泰国', flag: '🇹🇭' },
            'MY': { name: '马来西亚', flag: '🇲🇾' },
            'ID': { name: '印度尼西亚', flag: '🇮🇩' },
            'PH': { name: '菲律宾', flag: '🇵🇭' },
            'RU': { name: '俄罗斯', flag: '🇷🇺' },
            'UA': { name: '乌克兰', flag: '🇺🇦' },
            'BR': { name: '巴西', flag: '🇧🇷' },
            'IN': { name: '印度', flag: '🇮🇳' },
            'ZA': { name: '南非', flag: '🇿🇦' },
            'NL': { name: '荷兰', flag: '🇳🇱' },
            'IT': { name: '意大利', flag: '🇮🇹' },
            'ES': { name: '西班牙', flag: '🇪🇸' },
            'SE': { name: '瑞典', flag: '🇸🇪' },
            'CH': { name: '瑞士', flag: '🇨🇭' }
        };
        
        // 地区映射
        this.regionMap = {
            'CN': '亚洲', 'US': '北美洲', 'JP': '亚洲', 'KR': '亚洲', 'SG': '亚洲',
            'DE': '欧洲', 'GB': '欧洲', 'FR': '欧洲', 'CA': '北美洲', 'AU': '大洋洲',
            'RU': '欧洲', 'BR': '南美洲', 'IN': '亚洲', 'HK': '亚洲', 'TW': '亚洲',
            'NL': '欧洲', 'IT': '欧洲', 'ES': '欧洲', 'SE': '欧洲', 'CH': '欧洲'
        };
    }

    // 通用API请求方法
    async request(url, options = {}) {
        const cacheKey = `${url}_${JSON.stringify(options)}`;
        
        // 检查缓存
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        try {
            const response = await fetch(`${this.baseURL}${url}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            // 缓存成功的响应
            this.cache.set(cacheKey, {
                data,
                timestamp: Date.now()
            });

            return data;
        } catch (error) {
            console.error(`API请求失败: ${url}`, error);
            throw error;
        }
    }

    // 获取服务器列表
    async getServers() {
        try {
            const response = await this.request('/api/servers');
            return response.success ? response.data : [];
        } catch (error) {
            console.error('获取服务器列表失败:', error);
            return [];
        }
    }

    // 获取节点状态
    async getNodeStatus() {
        try {
            const response = await this.request('/api/allnode_status');
            return response.success ? response.data : {};
        } catch (error) {
            console.error('获取节点状态失败:', error);
            return {};
        }
    }

    // 获取分组信息
    async getGroups() {
        try {
            const response = await this.request('/api/groups');
            return response.success ? response.data : [];
        } catch (error) {
            console.error('获取分组信息失败:', error);
            return [];
        }
    }

    // 数据增强：扩展服务器数据
    enhanceServerData(server, status = {}, groups = []) {
        const serverData = this.safeParseJSON(server.data);
        
        // 获取国家信息
        const countryCode = serverData?.location?.code || 'CN';
        const countryInfo = this.countryMap[countryCode] || { name: '未知', flag: '🏳️' };
        
        // 获取分组信息
        const group = groups.find(g => g.id === server.group_id) || { id: 'default', name: '默认分组' };
        
        // 获取标签信息
        const tags = serverData?.tags || [];
        
        // 计算性能指标
        const performance = this.calculatePerformance(server.sid, status);
        
        return {
            id: server.sid,
            name: server.name,
            ip: server.ip || serverData?.ssh?.host || '',
            region: this.regionMap[countryCode] || '未知地区',
            online: this.isNodeOnline(server.sid, status),
            score: performance.score,
            groupId: group.id,
            groupName: group.name,
            countryCode: countryCode,
            countryName: countryInfo.name,
            countryFlag: countryInfo.flag,
            tags: tags.map(tag => tag.name || tag),
            latency: performance.latency,
            uptime: performance.uptime,
            lastOnline: server.last_online,
            disabled: server.status === 0,
            apiMode: serverData?.api?.mode || false,
            apiPort: serverData?.api?.port || 9999,
            trafficUsed: status[server.sid]?.traffic_used || 0,
            trafficLimit: server.traffic_limit || 0,
            expireTime: server.expire_time || 0
        };
    }

    // 判断节点是否在线
    isNodeOnline(nodeId, statusData) {
        const status = statusData[nodeId];
        if (!status) return false;
        
        // 根据之前的分析，stat字段的含义：
        // -1: 初始状态, 0: 离线, false: 离线, 对象: 在线
        if (status.stat === -1 || status.stat === 0 || status.stat === false) {
            return false;
        }
        
        // 如果是对象且有offline标记
        if (typeof status.stat === 'object' && status.stat.offline) {
            return false;
        }
        
        return true;
    }

    // 计算性能指标
    calculatePerformance(nodeId, statusData) {
        const status = statusData[nodeId];
        
        if (!status || !this.isNodeOnline(nodeId, statusData)) {
            return {
                score: 0,
                latency: 999,
                uptime: 0
            };
        }

        // 基于状态数据计算性能指标
        let score = 60; // 基础分数
        let latency = 100; // 默认延迟
        let uptime = 50; // 默认可用率

        // 如果节点在线，给予基础分数
        if (this.isNodeOnline(nodeId, statusData)) {
            score += 20;
            uptime = 85;
        }

        // 根据历史数据调整（这里使用模拟数据，实际应从数据库获取）
        const randomFactor = Math.random();
        latency = Math.floor(20 + randomFactor * 180); // 20-200ms
        uptime = Math.floor(80 + randomFactor * 19); // 80-99%
        
        // 计算综合评分
        const latencyScore = Math.max(0, (200 - latency) / 200 * 30);
        const uptimeScore = (uptime / 100) * 30;
        score = Math.min(100, score + latencyScore + uptimeScore);

        return {
            score: Math.floor(score),
            latency,
            uptime
        };
    }

    // 安全的JSON解析
    safeParseJSON(str, defaultValue = {}) {
        try {
            if (typeof str !== 'string') {
                return typeof str === 'object' ? str : defaultValue;
            }
            const cleaned = str.replace(/^\uFEFF/, '').trim();
            if (!cleaned) return defaultValue;
            return JSON.parse(cleaned);
        } catch (error) {
            console.warn('JSON解析失败:', error.message);
            return defaultValue;
        }
    }

    // 获取增强的节点数据
    async getEnhancedNodes() {
        try {
            const [servers, statusData, groups] = await Promise.all([
                this.getServers(),
                this.getNodeStatus(),
                this.getGroups()
            ]);

            const enhancedNodes = servers
                .filter(server => server.status !== 0) // 过滤禁用的服务器
                .map(server => this.enhanceServerData(server, statusData, groups));

            return enhancedNodes;
        } catch (error) {
            console.error('获取增强节点数据失败:', error);
            return [];
        }
    }

    // 获取分组统计信息（基于已有节点数据，避免重复API调用）
    getGroupStatsFromNodes(nodes) {
        const groupStats = {};

        nodes.forEach(node => {
            const groupId = node.groupId;
            if (!groupStats[groupId]) {
                groupStats[groupId] = {
                    id: groupId,
                    name: node.groupName,
                    totalCount: 0,
                    onlineCount: 0,
                    nodes: []
                };
            }

            groupStats[groupId].totalCount++;
            if (node.online) {
                groupStats[groupId].onlineCount++;
            }
            groupStats[groupId].nodes.push(node);
        });

        return Object.values(groupStats);
    }

    // 获取所有可用的标签
    async getAvailableTags() {
        try {
            const nodes = await this.getEnhancedNodes();
            const tagSet = new Set();

            nodes.forEach(node => {
                node.tags.forEach(tag => tagSet.add(tag));
            });

            return Array.from(tagSet).sort();
        } catch (error) {
            console.error('获取可用标签失败:', error);
            return [];
        }
    }

    // 获取所有可用的国家
    async getAvailableCountries() {
        try {
            const nodes = await this.getEnhancedNodes();
            const countrySet = new Set();

            nodes.forEach(node => {
                countrySet.add(JSON.stringify({
                    code: node.countryCode,
                    name: node.countryName,
                    flag: node.countryFlag
                }));
            });

            return Array.from(countrySet)
                .map(str => JSON.parse(str))
                .sort((a, b) => a.name.localeCompare(b.name));
        } catch (error) {
            console.error('获取可用国家失败:', error);
            return [];
        }
    }

    // 清除缓存
    clearCache() {
        this.cache.clear();
    }

    // 获取缓存状态
    getCacheStatus() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys()),
            timeout: this.cacheTimeout
        };
    }
}

// ================================
// NodeFilter 类 (筛选器)
// ================================
class NodeFilter {
    constructor() {
        this.filters = {
            search: '',
            group: '',
            tags: new Set(),
            country: '',
            onlineOnly: false,
            highPerformance: false,
            minScore: 0
        };
    }

    // 设置筛选条件
    setFilter(key, value) {
        if (key === 'tags' && !value instanceof Set) {
            this.filters.tags = new Set(value);
        } else {
            this.filters[key] = value;
        }
    }

    // 获取筛选条件
    getFilter(key) {
        return this.filters[key];
    }

    // 清除所有筛选条件
    clearFilters() {
        this.filters = {
            search: '',
            group: '',
            tags: new Set(),
            country: '',
            onlineOnly: false,
            highPerformance: false,
            minScore: 0
        };
    }

    // 应用筛选
    applyFilters(nodes) {
        return nodes.filter(node => {
            // 搜索筛选
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                const searchMatch = node.name.toLowerCase().includes(searchTerm) ||
                                  node.ip.includes(searchTerm) ||
                                  node.groupName.toLowerCase().includes(searchTerm) ||
                                  node.countryName.toLowerCase().includes(searchTerm);
                if (!searchMatch) return false;
            }

            // 分组筛选
            if (this.filters.group && node.groupId !== this.filters.group) {
                return false;
            }

            // 国家筛选
            if (this.filters.country && node.countryCode !== this.filters.country) {
                return false;
            }

            // 在线筛选
            if (this.filters.onlineOnly && !node.online) {
                return false;
            }

            // 高性能筛选
            if (this.filters.highPerformance && node.score < 80) {
                return false;
            }

            // 最低分数筛选
            if (this.filters.minScore > 0 && node.score < this.filters.minScore) {
                return false;
            }

            // 标签筛选
            if (this.filters.tags.size > 0) {
                const hasMatchingTag = [...this.filters.tags].some(tag => 
                    node.tags.includes(tag)
                );
                if (!hasMatchingTag) return false;
            }

            return true;
        });
    }
}

// ================================
// NodeSelectionIntegrated 类 (主控制器)
// ================================
class NodeSelectionIntegrated {
    constructor() {
        this.api = new NodeSelectionAPI();
        this.filter = new NodeFilter();
        this.nodes = [];
        this.filteredNodes = [];
        this.selectedNodes = new Set();
        this.currentMode = 'include'; // 'include' or 'exclude'
        this.isLoading = false;
        this.allCollapsed = false;
        
        // 错误处理
        this.maxRetries = 3;
        this.retryCount = 0;
        
        this.init();
    }

    async init() {
        addDebugLog('🔄 开始初始化节点选择系统');
        this.showLoading(true);
        
        try {
            await this.loadData();
            this.setupEventListeners();
            this.populateFilterOptions();
            this.applyFilters();
            this.updateDisplay();
            addDebugLog('✅ 节点选择系统初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
            addDebugLog('❌ 初始化失败: ' + error.message);
            this.showError('初始化失败，请刷新页面重试');
        } finally {
            this.showLoading(false);
        }
    }

    // 加载数据
    async loadData() {
        try {
            addDebugLog('📡 开始加载节点数据');
            
            const enhancedNodes = await this.api.getEnhancedNodes();
            const groupStats = this.api.getGroupStatsFromNodes(enhancedNodes);

            this.nodes = enhancedNodes;
            this.groupStats = groupStats;
            
            addDebugLog(`✅ 成功加载 ${this.nodes.length} 个节点`);
            
            // 如果没有数据，尝试降级到模拟数据
            if (this.nodes.length === 0) {
                addDebugLog('⚠️ 未获取到节点数据，尝试使用模拟数据');
                await this.loadMockData();
            }
            
            this.retryCount = 0; // 重置重试计数
        } catch (error) {
            console.error('加载数据失败:', error);
            addDebugLog('❌ 加载数据失败: ' + error.message);
            
            // 重试机制
            if (this.retryCount < this.maxRetries) {
                this.retryCount++;
                addDebugLog(`🔄 重试加载数据 (${this.retryCount}/${this.maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, 1000 * this.retryCount));
                return this.loadData();
            }
            
            // 降级到模拟数据
            addDebugLog('⚠️ 加载真实数据失败，使用模拟数据');
            await this.loadMockData();
        }
    }

    // 降级：加载模拟数据
    async loadMockData() {
        addDebugLog('📊 使用模拟数据模式');
        
        const mockNodes = [];
        const groups = ['default', 'beijing', 'shanghai', 'guangzhou', 'overseas'];
        const countries = ['CN', 'US', 'JP', 'KR', 'SG', 'HK', 'TW'];
        const tags = ['高性能', '低延迟', '稳定', '测试', 'CDN', '备用'];

        for (let i = 1; i <= 34; i++) {
            const groupId = groups[Math.floor(Math.random() * groups.length)];
            const countryCode = countries[Math.floor(Math.random() * countries.length)];
            const countryInfo = this.api.countryMap[countryCode] || { name: '中国', flag: '🇨🇳' };
            
            mockNodes.push({
                id: `node_${i}`,
                name: `监控节点${i}`,
                ip: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
                region: this.api.regionMap[countryCode] || '亚洲',
                online: Math.random() > 0.2,
                score: Math.floor(Math.random() * 40) + 60,
                groupId: groupId,
                groupName: groupId === 'default' ? '默认分组' : `${groupId}机房`,
                countryCode: countryCode,
                countryName: countryInfo.name,
                countryFlag: countryInfo.flag,
                tags: this.getRandomTags(tags, Math.floor(Math.random() * 3) + 1),
                latency: Math.floor(Math.random() * 200) + 10,
                uptime: Math.floor(Math.random() * 20) + 80,
                disabled: false,
                mockData: true
            });
        }

        this.nodes = mockNodes;
        this.groupStats = this.calculateGroupStats(mockNodes);
        addDebugLog(`📊 模拟数据加载完成，生成 ${this.nodes.length} 个节点`);
    }

    // 获取随机标签
    getRandomTags(tags, count) {
        const shuffled = [...tags].sort(() => 0.5 - Math.random());
        return shuffled.slice(0, count);
    }

    // 计算分组统计
    calculateGroupStats(nodes) {
        const stats = {};
        nodes.forEach(node => {
            if (!stats[node.groupId]) {
                stats[node.groupId] = {
                    id: node.groupId,
                    name: node.groupName,
                    totalCount: 0,
                    onlineCount: 0,
                    nodes: []
                };
            }
            stats[node.groupId].totalCount++;
            if (node.online) {
                stats[node.groupId].onlineCount++;
            }
            stats[node.groupId].nodes.push(node);
        });
        return Object.values(stats);
    }

    // 显示加载状态
    showLoading(show) {
        this.isLoading = show;
        
        // 主页面加载状态
        const nodeList = document.getElementById('nodeList');
        if (nodeList && show) {
            nodeList.innerHTML = `
                <div class="flex items-center justify-center p-8">
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <p class="mt-2 text-sm text-gray-600">正在加载节点数据...</p>
                    </div>
                </div>
            `;
        }
        
        // 抽屉加载状态
        const drawerNodeList = document.getElementById('drawerNodeList');
        if (drawerNodeList && show) {
            drawerNodeList.innerHTML = `
                <div class="flex items-center justify-center p-8">
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <p class="mt-2 text-sm text-gray-600">正在加载节点数据...</p>
                    </div>
                </div>
            `;
        }
    }

    // 显示错误信息
    showError(message) {
        const nodeList = document.getElementById('nodeList');
        if (nodeList) {
            nodeList.innerHTML = `
                <div class="flex items-center justify-center p-8">
                    <div class="text-center">
                        <div class="text-red-600 mb-2">⚠️</div>
                        <p class="text-sm text-red-600">${message}</p>
                        <button onclick="location.reload()" class="mt-2 px-4 py-2 bg-blue-500 text-white rounded text-sm">
                            重新加载
                        </button>
                    </div>
                </div>
            `;
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        addDebugLog('🔗 设置事件监听器');
        
        // 主页面搜索
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filter.setFilter('search', e.target.value.toLowerCase());
                this.applyFilters();
            });
        }

        // 基础筛选
        const onlineOnly = document.getElementById('onlineOnly');
        if (onlineOnly) {
            onlineOnly.addEventListener('change', (e) => {
                this.filter.setFilter('onlineOnly', e.target.checked);
                this.applyFilters();
            });
        }

        const highPerformance = document.getElementById('highPerformance');
        if (highPerformance) {
            highPerformance.addEventListener('change', (e) => {
                this.filter.setFilter('highPerformance', e.target.checked);
                this.applyFilters();
            });
        }

        // 分组筛选
        const groupFilter = document.getElementById('groupFilter');
        if (groupFilter) {
            groupFilter.addEventListener('change', (e) => {
                this.filter.setFilter('group', e.target.value);
                this.applyFilters();
            });
        }

        // 国家筛选
        const countryFilter = document.getElementById('countryFilter');
        if (countryFilter) {
            countryFilter.addEventListener('change', (e) => {
                this.filter.setFilter('country', e.target.value);
                this.applyFilters();
            });
        }

        // 选择模式
        const includeMode = document.getElementById('includeMode');
        const excludeMode = document.getElementById('excludeMode');
        
        if (includeMode) {
            includeMode.addEventListener('click', () => {
                this.setMode('include');
            });
        }
        
        if (excludeMode) {
            excludeMode.addEventListener('click', () => {
                this.setMode('exclude');
            });
        }

        // 收起/展开按钮
        const toggleCollapse = document.getElementById('toggleCollapse');
        if (toggleCollapse) {
            toggleCollapse.addEventListener('click', () => {
                this.toggleAllGroups();
            });
        }

        // 批量操作
        const selectAll = document.getElementById('selectAll');
        const selectNone = document.getElementById('selectNone');
        const selectOnline = document.getElementById('selectOnline');
        const clearFilters = document.getElementById('clearFilters');
        
        if (selectAll) {
            selectAll.addEventListener('click', () => this.selectAll());
        }
        
        if (selectNone) {
            selectNone.addEventListener('click', () => this.selectNone());
        }
        
        if (selectOnline) {
            selectOnline.addEventListener('click', () => this.selectOnline());
        }
        
        if (clearFilters) {
            clearFilters.addEventListener('click', () => this.clearFilters());
        }

        // 抽屉控制
        this.setupDrawerControls();
        
        // 测试控制
        this.setupTestControls();
        
        addDebugLog('✅ 事件监听器设置完成');
    }

    // 设置抽屉控制
    setupDrawerControls() {
        const openDrawer = document.getElementById('openDrawer');
        const closeDrawer = document.getElementById('closeDrawer');
        const backdrop = document.getElementById('nodeSelectionBackdrop');
        const drawer = document.getElementById('nodeSelectionDrawer');
        
        if (openDrawer) {
            openDrawer.addEventListener('click', () => this.openDrawer());
        }
        
        if (closeDrawer) {
            closeDrawer.addEventListener('click', () => this.closeDrawer());
        }
        
        if (backdrop) {
            backdrop.addEventListener('click', () => this.closeDrawer());
        }
        
        // 抽屉内的控件
        const drawerSearchInput = document.getElementById('drawerSearchInput');
        if (drawerSearchInput) {
            drawerSearchInput.addEventListener('input', (e) => {
                this.filter.setFilter('search', e.target.value.toLowerCase());
                this.applyFilters();
            });
        }
        
        const drawerGroupFilter = document.getElementById('drawerGroupFilter');
        if (drawerGroupFilter) {
            drawerGroupFilter.addEventListener('change', (e) => {
                this.filter.setFilter('group', e.target.value);
                this.applyFilters();
            });
        }
        
        const drawerCountryFilter = document.getElementById('drawerCountryFilter');
        if (drawerCountryFilter) {
            drawerCountryFilter.addEventListener('change', (e) => {
                this.filter.setFilter('country', e.target.value);
                this.applyFilters();
            });
        }
        
        // 抽屉内的模式按钮
        const drawerIncludeMode = document.getElementById('drawerIncludeMode');
        const drawerExcludeMode = document.getElementById('drawerExcludeMode');
        
        if (drawerIncludeMode) {
            drawerIncludeMode.addEventListener('click', () => this.setMode('include'));
        }
        
        if (drawerExcludeMode) {
            drawerExcludeMode.addEventListener('click', () => this.setMode('exclude'));
        }
        
        // 抽屉内的批量操作
        const drawerSelectAll = document.getElementById('drawerSelectAll');
        const drawerSelectNone = document.getElementById('drawerSelectNone');
        const drawerSelectOnline = document.getElementById('drawerSelectOnline');
        const drawerClearFilters = document.getElementById('drawerClearFilters');
        
        if (drawerSelectAll) {
            drawerSelectAll.addEventListener('click', () => this.selectAll());
        }
        
        if (drawerSelectNone) {
            drawerSelectNone.addEventListener('click', () => this.selectNone());
        }
        
        if (drawerSelectOnline) {
            drawerSelectOnline.addEventListener('click', () => this.selectOnline());
        }
        
        if (drawerClearFilters) {
            drawerClearFilters.addEventListener('click', () => this.clearFilters());
        }
        
        // 抽屉底部操作
        const applySelection = document.getElementById('applySelection');
        const cancelSelection = document.getElementById('cancelSelection');
        
        if (applySelection) {
            applySelection.addEventListener('click', () => this.applySelection());
        }
        
        if (cancelSelection) {
            cancelSelection.addEventListener('click', () => this.closeDrawer());
        }
    }

    // 设置测试控制
    setupTestControls() {
        const testOpenDrawer = document.getElementById('testOpenDrawer');
        const testCloseDrawer = document.getElementById('testCloseDrawer');
        const testLoadData = document.getElementById('testLoadData');
        
        if (testOpenDrawer) {
            testOpenDrawer.addEventListener('click', () => {
                addDebugLog('🔧 测试：打开抽屉');
                this.openDrawer();
            });
        }
        
        if (testCloseDrawer) {
            testCloseDrawer.addEventListener('click', () => {
                addDebugLog('🔧 测试：关闭抽屉');
                this.closeDrawer();
            });
        }
        
        if (testLoadData) {
            testLoadData.addEventListener('click', () => {
                addDebugLog('🔧 测试：重新加载数据');
                this.refreshData();
            });
        }
    }

    // 打开抽屉
    openDrawer() {
        addDebugLog('📂 打开选择抽屉');
        const drawer = document.getElementById('nodeSelectionDrawer');
        const backdrop = document.getElementById('nodeSelectionBackdrop');
        
        if (drawer && backdrop) {
            drawer.classList.add('open');
            backdrop.classList.add('open');
            this.updateDrawerDisplay();
        }
    }

    // 关闭抽屉
    closeDrawer() {
        addDebugLog('📁 关闭选择抽屉');
        const drawer = document.getElementById('nodeSelectionDrawer');
        const backdrop = document.getElementById('nodeSelectionBackdrop');
        
        if (drawer && backdrop) {
            drawer.classList.remove('open');
            backdrop.classList.remove('open');
        }
    }

    // 应用选择
    applySelection() {
        addDebugLog(`💾 应用选择：已选中 ${this.selectedNodes.size} 个节点，模式：${this.currentMode}`);
        const selectedData = this.getSelectedNodesData();
        const monitoringData = this.getMonitoringNodesData();
        
        console.log('选中的节点:', selectedData);
        console.log('实际监控的节点:', monitoringData);
        
        this.closeDrawer();
    }

    // 刷新数据
    async refreshData() {
        addDebugLog('🔄 刷新节点数据');
        this.api.clearCache();
        this.showLoading(true);
        
        try {
            await this.loadData();
            this.populateFilterOptions();
            this.applyFilters();
            this.updateDisplay();
            addDebugLog('✅ 数据刷新成功');
        } catch (error) {
            console.error('刷新数据失败:', error);
            addDebugLog('❌ 刷新失败: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    // 填充筛选选项
    async populateFilterOptions() {
        try {
            addDebugLog('🔽 填充筛选选项');
            
            // 主页面分组选项
            const groupSelect = document.getElementById('groupFilter');
            if (groupSelect) {
                groupSelect.innerHTML = '<option value="">所有分组</option>';
                
                if (this.groupStats && Array.isArray(this.groupStats)) {
                    this.groupStats.forEach(group => {
                        const option = document.createElement('option');
                        option.value = group.id;
                        option.textContent = `${group.name} (${group.onlineCount}/${group.totalCount})`;
                        groupSelect.appendChild(option);
                    });
                }
            }
            
            // 抽屉分组选项
            const drawerGroupSelect = document.getElementById('drawerGroupFilter');
            if (drawerGroupSelect) {
                drawerGroupSelect.innerHTML = '<option value="">所有分组</option>';
                
                if (this.groupStats && Array.isArray(this.groupStats)) {
                    this.groupStats.forEach(group => {
                        const option = document.createElement('option');
                        option.value = group.id;
                        option.textContent = `${group.name} (${group.onlineCount}/${group.totalCount})`;
                        drawerGroupSelect.appendChild(option);
                    });
                }
            }

            // 国家选项
            const countries = await this.api.getAvailableCountries();
            const countrySelects = [
                document.getElementById('countryFilter'),
                document.getElementById('drawerCountryFilter')
            ];
            
            countrySelects.forEach(countrySelect => {
                if (countrySelect) {
                    countrySelect.innerHTML = '<option value="">所有国家</option>';
                    countries.forEach(country => {
                        const option = document.createElement('option');
                        option.value = country.code;
                        option.textContent = `${country.flag} ${country.name}`;
                        countrySelect.appendChild(option);
                    });
                }
            });

            // 标签选项
            const tagContainer = document.getElementById('tagFilters');
            if (tagContainer) {
                tagContainer.innerHTML = '';
                
                const tags = await this.api.getAvailableTags();
                tags.forEach(tag => {
                    const button = document.createElement('button');
                    button.className = 'tag-button';
                    button.textContent = tag;
                    button.addEventListener('click', () => {
                        this.toggleTag(tag);
                        button.classList.toggle('active');
                    });
                    tagContainer.appendChild(button);
                });
            }

            addDebugLog('✅ 筛选选项填充完成');
        } catch (error) {
            console.error('填充筛选选项失败:', error);
            addDebugLog('❌ 筛选选项填充失败: ' + error.message);
        }
    }

    // 标签切换
    toggleTag(tag) {
        const currentTags = this.filter.getFilter('tags');
        if (currentTags.has(tag)) {
            currentTags.delete(tag);
        } else {
            currentTags.add(tag);
        }
        this.filter.setFilter('tags', currentTags);
        this.applyFilters();
    }

    // 应用筛选
    applyFilters() {
        if (this.isLoading) return;
        
        this.filteredNodes = this.filter.applyFilters(this.nodes);
        this.updateDisplay();
    }

    // 设置选择模式
    setMode(mode) {
        this.currentMode = mode;
        addDebugLog(`🔄 切换选择模式: ${mode}`);
        
        // 更新主页面按钮状态
        const includeMode = document.getElementById('includeMode');
        const excludeMode = document.getElementById('excludeMode');
        const currentMode = document.getElementById('currentMode');
        
        if (includeMode) includeMode.classList.toggle('active', mode === 'include');
        if (excludeMode) excludeMode.classList.toggle('active', mode === 'exclude');
        if (currentMode) currentMode.textContent = mode === 'include' ? '包含模式' : '排除模式';
        
        // 更新抽屉按钮状态
        const drawerIncludeMode = document.getElementById('drawerIncludeMode');
        const drawerExcludeMode = document.getElementById('drawerExcludeMode');
        const drawerCurrentMode = document.getElementById('drawerCurrentMode');
        
        if (drawerIncludeMode) drawerIncludeMode.classList.toggle('active', mode === 'include');
        if (drawerExcludeMode) drawerExcludeMode.classList.toggle('active', mode === 'exclude');
        if (drawerCurrentMode) drawerCurrentMode.textContent = mode === 'include' ? '包含模式' : '排除模式';
        
        this.updateDisplay();
    }

    // 节点选择切换
    toggleNode(nodeId) {
        if (this.selectedNodes.has(nodeId)) {
            this.selectedNodes.delete(nodeId);
        } else {
            this.selectedNodes.add(nodeId);
        }
        this.updateDisplay();
    }

    // 批量选择
    selectAll() {
        this.filteredNodes.forEach(node => {
            this.selectedNodes.add(node.id);
        });
        this.updateDisplay();
    }

    selectNone() {
        this.selectedNodes.clear();
        this.updateDisplay();
    }

    selectOnline() {
        this.filteredNodes.forEach(node => {
            if (node.online) {
                this.selectedNodes.add(node.id);
            }
        });
        this.updateDisplay();
    }

    // 清除筛选
    clearFilters() {
        this.filter.clearFilters();

        // 重置主页面UI
        const searchInput = document.getElementById('searchInput');
        const onlineOnly = document.getElementById('onlineOnly');
        const highPerformance = document.getElementById('highPerformance');
        const groupFilter = document.getElementById('groupFilter');
        const countryFilter = document.getElementById('countryFilter');
        
        if (searchInput) searchInput.value = '';
        if (onlineOnly) onlineOnly.checked = false;
        if (highPerformance) highPerformance.checked = false;
        if (groupFilter) groupFilter.value = '';
        if (countryFilter) countryFilter.value = '';
        
        // 重置抽屉UI
        const drawerSearchInput = document.getElementById('drawerSearchInput');
        const drawerGroupFilter = document.getElementById('drawerGroupFilter');
        const drawerCountryFilter = document.getElementById('drawerCountryFilter');
        
        if (drawerSearchInput) drawerSearchInput.value = '';
        if (drawerGroupFilter) drawerGroupFilter.value = '';
        if (drawerCountryFilter) drawerCountryFilter.value = '';

        // 重置标签按钮
        document.querySelectorAll('#tagFilters button').forEach(btn => {
            btn.classList.remove('active');
        });

        this.applyFilters();
    }

    // 收起/展开所有分组
    toggleAllGroups() {
        this.allCollapsed = !this.allCollapsed;
        const toggleBtn = document.getElementById('toggleCollapse');
        if (toggleBtn) {
            toggleBtn.textContent = this.allCollapsed ? '展开所有' : '收起所有';
        }
        
        // 更新所有分组的展开状态
        document.querySelectorAll('.collapsible-content').forEach(content => {
            if (this.allCollapsed) {
                content.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
            }
        });
    }

    // 更新显示
    updateDisplay() {
        if (this.isLoading) return;
        
        this.updateNodeList();
        this.updateDrawerNodeList();
        this.updateStats();
    }

    // 更新主页面节点列表
    updateNodeList() {
        const container = document.getElementById('nodeList');
        if (!container) return;
        
        container.innerHTML = '';

        if (this.filteredNodes.length === 0) {
            container.innerHTML = `
                <div class="text-center p-8">
                    <p class="text-gray-500 text-sm">暂无符合条件的节点</p>
                </div>
            `;
            return;
        }

        // 按分组分组显示
        const groupedNodes = this.groupNodesByGroup(this.filteredNodes);

        Object.entries(groupedNodes).forEach(([groupName, nodes]) => {
            // 分组标题
            const groupHeader = document.createElement('div');
            groupHeader.className = 'group-header';
            groupHeader.innerHTML = `
                <div class="flex items-center">
                    <span class="text-sm font-medium">${groupName}</span>
                    <span class="ml-2 text-xs text-gray-500">(${nodes.length})</span>
                </div>
                <div class="group-actions">
                    <button class="group-action-btn" onclick="nodeSelection.selectGroupNodes('${groupName}')">全选</button>
                    <button class="group-action-btn" onclick="nodeSelection.deselectGroupNodes('${groupName}')">取消</button>
                </div>
            `;
            
            // 点击分组标题切换展开/收起
            groupHeader.addEventListener('click', (e) => {
                if (!e.target.classList.contains('group-action-btn')) {
                    this.toggleGroupExpansion(groupName);
                }
            });
            
            container.appendChild(groupHeader);

            // 分组内容
            const groupContent = document.createElement('div');
            groupContent.className = 'collapsible-content expanded';
            groupContent.setAttribute('data-group', groupName);

            nodes.forEach(node => {
                const nodeCard = this.createNodeCard(node);
                groupContent.appendChild(nodeCard);
            });

            container.appendChild(groupContent);
        });
    }

    // 更新抽屉节点列表
    updateDrawerNodeList() {
        const container = document.getElementById('drawerNodeList');
        if (!container) return;
        
        container.innerHTML = '';

        if (this.filteredNodes.length === 0) {
            container.innerHTML = `
                <div class="text-center p-8">
                    <p class="text-gray-500 text-sm">暂无符合条件的节点</p>
                </div>
            `;
            return;
        }

        // 简化显示，不分组
        this.filteredNodes.forEach(node => {
            const nodeCard = this.createNodeCard(node, 'drawer');
            container.appendChild(nodeCard);
        });
    }

    // 按分组分组节点
    groupNodesByGroup(nodes) {
        const grouped = {};
        nodes.forEach(node => {
            const groupName = node.groupName || '默认分组';
            if (!grouped[groupName]) {
                grouped[groupName] = [];
            }
            grouped[groupName].push(node);
        });
        return grouped;
    }

    // 切换分组展开/收起
    toggleGroupExpansion(groupName) {
        const groupContent = document.querySelector(`[data-group="${groupName}"]`);
        if (groupContent) {
            groupContent.classList.toggle('expanded');
        }
    }

    // 创建节点卡片
    createNodeCard(node, context = 'main') {
        const isSelected = this.selectedNodes.has(node.id);
        
        const div = document.createElement('div');
        let cardClass = 'node-card';
        if (isSelected) {
            cardClass += ' selected';
        }
        if (this.currentMode === 'exclude' && isSelected) {
            cardClass += ' excluded';
        }
        div.className = cardClass;
        div.onclick = () => this.toggleNode(node.id);

        // 紧凑的标签显示
        const tagsHtml = node.tags.slice(0, 2).map(tag => `<span class="tag-chip">${tag}</span>`).join('');
        const moreTagsText = node.tags.length > 2 ? `+${node.tags.length - 2}` : '';
        
        // 数据源指示
        const dataSource = node.mockData ? '<span class="text-xs text-orange-500" title="模拟数据">📊</span>' : '<span class="text-xs text-green-500" title="真实数据">🔗</span>';
        
        div.innerHTML = `
            <div class="node-header">
                <div class="node-info">
                    <input type="checkbox" ${isSelected ? 'checked' : ''} class="checkbox-node" onclick="event.stopPropagation()">
                    <span class="country-flag">${node.countryFlag}</span>
                    <div>
                        <div class="text-sm font-medium">${node.name} ${dataSource}</div>
                        <div class="text-xs text-gray-500">${node.ip}</div>
                    </div>
                </div>
                <div class="node-status">
                    <div class="text-xs ${node.online ? 'text-green-600' : 'text-red-600'} font-medium">
                        ${node.online ? '在线' : '离线'}
                    </div>
                    <div class="text-xs text-gray-500">${node.score}分</div>
                </div>
            </div>
            <div class="node-meta">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        ${tagsHtml}
                        ${moreTagsText ? `<span class="text-xs text-gray-400">${moreTagsText}</span>` : ''}
                    </div>
                    <div class="text-xs text-gray-500">
                        ${node.latency}ms
                    </div>
                </div>
            </div>
        `;

        return div;
    }

    // 分组选择
    selectGroupNodes(groupName) {
        this.filteredNodes.forEach(node => {
            if (node.groupName === groupName) {
                this.selectedNodes.add(node.id);
            }
        });
        this.updateDisplay();
        this.updateDrawerDisplay();
    }

    deselectGroupNodes(groupName) {
        this.filteredNodes.forEach(node => {
            if (node.groupName === groupName) {
                this.selectedNodes.delete(node.id);
            }
        });
        this.updateDisplay();
        this.updateDrawerDisplay();
    }

    updateStats() {
        const total = this.nodes.length;
        const filtered = this.filteredNodes.length;
        const selected = this.selectedNodes.size;
        const online = this.filteredNodes.filter(n => n.online).length;

        let actualCount;
        if (this.currentMode === 'include') {
            actualCount = selected;
        } else {
            actualCount = filtered - selected;
        }

        const totalNodesEl = document.getElementById('totalNodes');
        const onlineNodesEl = document.getElementById('onlineNodes');
        const filteredNodesEl = document.getElementById('filteredNodes');
        const selectedNodesEl = document.getElementById('selectedNodes');
        
        if (totalNodesEl) totalNodesEl.textContent = total;
        if (onlineNodesEl) onlineNodesEl.textContent = online;
        if (filteredNodesEl) filteredNodesEl.textContent = filtered;
        if (selectedNodesEl) selectedNodesEl.textContent = selected;
    }

    updateDrawerStats() {
        const total = this.nodes.length;
        const filtered = this.filteredNodes.length;
        const selected = this.selectedNodes.size;

        let actualCount;
        if (this.currentMode === 'include') {
            actualCount = selected;
        } else {
            actualCount = filtered - selected;
        }

        const drawerFilterStatsEl = document.getElementById('drawerFilterStats');
        const drawerSelectedCountEl = document.getElementById('drawerSelectedCount');
        const drawerActualCountEl = document.getElementById('drawerActualCount');
        const drawerCurrentModeEl = document.getElementById('drawerCurrentMode');
        
        if (drawerFilterStatsEl) drawerFilterStatsEl.textContent = `${filtered}/${total}`;
        if (drawerSelectedCountEl) drawerSelectedCountEl.textContent = selected;
        if (drawerActualCountEl) drawerActualCountEl.textContent = actualCount;
        if (drawerCurrentModeEl) drawerCurrentModeEl.textContent = this.currentMode === 'include' ? '包含模式' : '排除模式';
    }

    // 更新抽屉显示
    updateDrawerDisplay() {
        this.updateDrawerNodeList();
        this.updateDrawerStats();
    }

    // 获取当前选择的节点数据
    getSelectedNodesData() {
        const selectedData = [];
        this.selectedNodes.forEach(nodeId => {
            const node = this.nodes.find(n => n.id === nodeId);
            if (node) {
                selectedData.push(node);
            }
        });
        return selectedData;
    }

    // 获取实际监控的节点数据
    getMonitoringNodesData() {
        if (this.currentMode === 'include') {
            return this.getSelectedNodesData();
        } else {
            return this.filteredNodes.filter(node => !this.selectedNodes.has(node.id));
        }
    }
}

// 初始化节点选择系统
let nodeSelection;
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        nodeSelection = new NodeSelectionIntegrated();
        window.nodeSelection = nodeSelection;
    });
} else {
    nodeSelection = new NodeSelectionIntegrated();
    window.nodeSelection = nodeSelection;
}

addDebugLog('✅ 节点选择系统脚本加载完成');
</script>
{% endblock %}