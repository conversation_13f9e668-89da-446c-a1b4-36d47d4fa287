{% extends "base.html" %}

{% block head %}
<!-- 抽屉组件专用样式 -->
<style>
/* 抽屉容器样式 */
.drawer-container {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    max-width: 480px;
    height: 100vh;
    background: white;
    border-left: 1px solid #e0e0e0;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease-out;
}

.drawer-container.open {
    transform: translateX(0);
}

.drawer-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-out;
}

.drawer-backdrop.open {
    opacity: 1;
    visibility: visible;
}

/* 紧凑样式 */
.section {
    border-bottom: 1px solid #e0e0e0;
    padding: 12px;
}

.compact-input, .compact-select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.compact-input:focus, .compact-select:focus {
    outline: none;
    border-color: #4285f4;
}

.tag-button {
    display: inline-block;
    padding: 4px 8px;
    margin: 2px;
    border: 1px solid #ddd;
    border-radius: 12px;
    background: #f8f9fa;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.tag-button:hover {
    background: #e9ecef;
}

.tag-button.active {
    background: #4285f4;
    color: white;
    border-color: #4285f4;
}

.mode-toggle {
    display: flex;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.mode-button {
    flex: 1;
    padding: 8px 12px;
    background: #f8f9fa;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
}

.mode-button:not(:last-child) {
    border-right: 1px solid #ddd;
}

.mode-button.active {
    background: #4285f4;
    color: white;
}

.action-bar {
    display: flex;
    gap: 4px;
    margin-bottom: 8px;
}

.action-btn {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f8f9fa;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.2s;
}

.action-btn:hover {
    background: #e9ecef;
}

.status-panel {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 12px;
    font-size: 14px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2px 0;
}

.node-card {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;
    background: white;
}

.node-card:hover {
    border-color: #4285f4;
}

.node-card.selected {
    background: #e3f2fd;
    border-color: #4285f4;
}

.node-card.excluded {
    background: #ffebee;
    border-color: #f44336;
}

.group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 8px;
    cursor: pointer;
}

.group-header:hover {
    background: #e9ecef;
}

.collapsible-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.collapsible-content.expanded {
    max-height: 500px;
}

.tag-chip {
    display: inline-block;
    padding: 2px 6px;
    background: #e9ecef;
    color: #495057;
    border-radius: 2px;
    font-size: 11px;
    margin-right: 4px;
}

/* 响应式 */
@media (max-width: 480px) {
    .drawer-container {
        max-width: 100%;
        border-left: none;
    }
}

/* 深色主题支持 */
.dark .drawer-container {
    background: #1f2937;
    border-left-color: #374151;
    color: #f3f4f6;
}

.dark .section {
    border-bottom-color: #374151;
}

.dark .compact-input,
.dark .compact-select {
    background: #374151;
    border-color: #4b5563;
    color: #f3f4f6;
}

.dark .node-card {
    background: #374151;
    border-color: #4b5563;
    color: #f3f4f6;
}

.dark .node-card.selected {
    background: #1e3a8a;
    border-color: #3b82f6;
}

.dark .group-header {
    background: #374151;
}

.dark .status-panel {
    background: #374151;
}
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">节点选择分组功能测试</h1>
        
        <!-- 测试说明 -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">🧪 测试目标</h3>
            <ul class="text-blue-800 dark:text-blue-200 space-y-1">
                <li>• 验证分组首次加载是否正常显示</li>
                <li>• 测试34个节点加载状态</li>
                <li>• 检查分组选择器功能</li>
                <li>• 验证DOM就绪时序修复效果</li>
            </ul>
        </div>

        <!-- 抽屉容器和背景遮罩 -->
        <div id="drawerBackdrop" class="drawer-backdrop"></div>
        <div id="nodeSelectionDrawer" class="drawer-container">
            <!-- 抽屉头部 -->
            <div class="section">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold">选择监控节点</h2>
                    <button id="closeDrawer" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded text-sm">关闭</button>
                </div>
            </div>
            
            <!-- 快速搜索 -->
            <div class="section">
                <input type="text" id="drawerSearchInput" placeholder="搜索节点名称、IP..." class="compact-input">
            </div>
            
            <!-- 筛选选项 -->
            <div class="section">
                <div class="grid grid-cols-2 gap-2 mb-3">
                    <select id="drawerGroupFilter" class="compact-select">
                        <option value="">所有分组</option>
                    </select>
                    <select id="drawerCountryFilter" class="compact-select">
                        <option value="">所有国家</option>
                    </select>
                </div>
                
                <div class="grid grid-cols-2 gap-2 mb-3">
                    <label class="flex items-center text-sm">
                        <input type="checkbox" id="drawerOnlineOnly" class="mr-2">
                        仅在线
                    </label>
                    <label class="flex items-center text-sm">
                        <input type="checkbox" id="drawerHighPerformance" class="mr-2">
                        高性能
                    </label>
                </div>

                <!-- 标签筛选 -->
                <div class="mb-3">
                    <div class="text-sm font-medium mb-1">标签筛选</div>
                    <div id="drawerTagFilters" class="flex flex-wrap"></div>
                </div>

                <!-- 选择模式 -->
                <div class="mb-3">
                    <div class="text-sm font-medium mb-1">选择模式</div>
                    <div class="mode-toggle">
                        <button id="drawerIncludeMode" class="mode-button active">包含</button>
                        <button id="drawerExcludeMode" class="mode-button">排除</button>
                    </div>
                </div>
            </div>
            
            <!-- 快速操作 -->
            <div class="section">
                <div class="action-bar">
                    <button id="drawerSelectAll" class="action-btn">全选</button>
                    <button id="drawerSelectNone" class="action-btn">全不选</button>
                    <button id="drawerSelectOnline" class="action-btn">选在线</button>
                    <button id="drawerClearFilters" class="action-btn">清除</button>
                </div>
            </div>
            
            <!-- 状态统计 -->
            <div class="section">
                <div class="status-panel">
                    <div class="status-item">
                        <span>筛选结果:</span>
                        <span id="drawerFilterStats">0/0</span>
                    </div>
                    <div class="status-item">
                        <span>已选节点:</span>
                        <span id="drawerSelectedCount">0</span>
                    </div>
                    <div class="status-item">
                        <span>实际监控:</span>
                        <span id="drawerActualCount" class="font-medium text-blue-600">0</span>
                    </div>
                    <div class="status-item">
                        <span>当前模式:</span>
                        <span id="drawerCurrentMode" class="font-medium text-blue-600">包含模式</span>
                    </div>
                </div>
            </div>
            
            <!-- 节点列表 -->
            <div class="section flex-1 overflow-hidden">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium">节点列表</span>
                    <button id="drawerToggleCollapse" class="text-xs text-blue-600 hover:text-blue-800 cursor-pointer">
                        收起所有
                    </button>
                </div>
                <div id="drawerNodeList" class="space-y-1 max-h-96 overflow-y-auto"></div>
            </div>
        </div>

        <!-- 节点选择测试区域 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">节点选择器</h2>
                <div class="flex gap-2">
                    <button id="openDrawer" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                        打开抽屉测试
                    </button>
                    <button onclick="window.nodeSelection?.loadData()" 
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                        重新加载数据
                    </button>
                </div>
            </div>

            <!-- 筛选器行 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">分组筛选</label>
                    <select id="groupFilter" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">所有分组</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">国家筛选</label>
                    <select id="countryFilter" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">所有国家</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">标签筛选</label>
                    <select id="tagFilter" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">所有标签</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">搜索</label>
                    <input type="text" id="searchInput" placeholder="搜索节点名称或IP"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500">
                </div>
            </div>

            <!-- 选择模式切换 -->
            <div class="flex items-center gap-4 mb-6">
                <div class="flex items-center">
                    <input type="radio" id="includeMode" name="selectionMode" value="include" checked 
                           class="mr-2 text-blue-600 focus:ring-blue-500">
                    <label for="includeMode" class="text-gray-700 dark:text-gray-300">包含模式</label>
                </div>
                <div class="flex items-center">
                    <input type="radio" id="excludeMode" name="selectionMode" value="exclude" 
                           class="mr-2 text-blue-600 focus:ring-blue-500">
                    <label for="excludeMode" class="text-gray-700 dark:text-gray-300">排除模式</label>
                </div>
            </div>

            <!-- 节点显示区域 -->
            <div id="nodeContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                    正在加载节点数据...
                </div>
            </div>

            <!-- 统计信息 -->
            <div id="statsContainer" class="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                    <div>
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="totalNodes">0</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">总节点</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400" id="onlineNodes">0</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">在线节点</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400" id="filteredNodes">0</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">筛选结果</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-purple-600 dark:text-purple-400" id="selectedNodes">0</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">已选择</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能测试区域 -->
        <div class="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">🧪 抽屉组件测试</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 class="font-medium mb-2">抽屉控制</h4>
                    <div class="space-y-2">
                        <button id="testOpenDrawer" class="w-full px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg">打开抽屉</button>
                        <button id="testCloseDrawer" class="w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg">关闭抽屉</button>
                        <button onclick="nodeSelection.clearFilters()" class="w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg">清除筛选</button>
                    </div>
                </div>
                <div>
                    <h4 class="font-medium mb-2">批量操作测试</h4>
                    <div class="space-y-2">
                        <button onclick="nodeSelection.selectAll()" class="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">全选节点</button>
                        <button onclick="nodeSelection.selectNone()" class="w-full px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg">清空选择</button>
                        <button onclick="nodeSelection.selectOnline()" class="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg">仅选在线</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 调试日志区域 -->
        <div class="mt-6 bg-gray-900 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-white mb-3">🔍 调试日志</h3>
            <div id="debugLog" class="bg-black text-green-400 p-3 rounded font-mono text-sm h-40 overflow-y-auto">
                [测试] 页面加载完成，等待初始化...\n
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 加载测试版本的JavaScript文件 -->
<script>
    // 添加调试日志函数
    function addDebugLog(message) {
        const debugLog = document.getElementById('debugLog');
        const timestamp = new Date().toLocaleTimeString();
        debugLog.textContent += `[${timestamp}] ${message}\n`;
        debugLog.scrollTop = debugLog.scrollHeight;
    }

    // 重写console方法来捕获日志
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;

    console.log = function(...args) {
        originalLog.apply(console, args);
        addDebugLog('LOG: ' + args.join(' '));
    };

    console.warn = function(...args) {
        originalWarn.apply(console, args);
        addDebugLog('WARN: ' + args.join(' '));
    };

    console.error = function(...args) {
        originalError.apply(console, args);
        addDebugLog('ERROR: ' + args.join(' '));
    };

    addDebugLog('调试系统已启动');
    
    // 绑定测试按钮事件
    document.addEventListener('DOMContentLoaded', () => {
        document.getElementById('testOpenDrawer').addEventListener('click', () => {
            if (window.nodeSelection) {
                window.nodeSelection.openDrawer();
                addDebugLog('通过测试按钮打开抽屉');
            }
        });
        
        document.getElementById('testCloseDrawer').addEventListener('click', () => {
            if (window.nodeSelection) {
                window.nodeSelection.closeDrawer();
                addDebugLog('通过测试按钮关闭抽屉');
            }
        });
    });
</script>

<!-- 动态加载测试文件 -->
<script>
    async function loadTestFiles() {
        try {
            addDebugLog('开始加载测试JavaScript文件...');
            
            // 动态创建script标签加载API文件
            const apiScript = document.createElement('script');
            apiScript.src = '/test/node-selection-debug/node-selection-api.js';
            apiScript.onload = () => {
                addDebugLog('✅ node-selection-api.js 加载成功');
                
                // 加载主要逻辑文件
                const mainScript = document.createElement('script');
                mainScript.src = '/test/node-selection-debug/node-selection-integrated.js';
                mainScript.onload = () => {
                    addDebugLog('✅ node-selection-integrated.js 加载成功');
                    addDebugLog('🚀 初始化节点选择系统...');
                };
                mainScript.onerror = () => {
                    addDebugLog('❌ node-selection-integrated.js 加载失败');
                };
                document.head.appendChild(mainScript);
            };
            apiScript.onerror = () => {
                addDebugLog('❌ node-selection-api.js 加载失败');
            };
            document.head.appendChild(apiScript);
            
        } catch (error) {
            addDebugLog('❌ 加载测试文件失败: ' + error.message);
        }
    }

<!-- 嵌入式节点选择API模块 -->
<script>
// 节点选择API模块
class NodeSelectionAPI {
    constructor() {
        this.baseURL = window.location.origin;
        this.cache = new Map();
        this.cacheTimeout = 30000; // 30秒缓存
        
        // 国家代码映射
        this.countryMap = {
            'CN': { name: '中国', flag: '🇨🇳' }, 'HK': { name: '香港', flag: '🇭🇰' },
            'TW': { name: '台湾', flag: '🇹🇼' }, 'JP': { name: '日本', flag: '🇯🇵' },
            'KR': { name: '韩国', flag: '🇰🇷' }, 'SG': { name: '新加坡', flag: '🇸🇬' },
            'US': { name: '美国', flag: '🇺🇸' }, 'CA': { name: '加拿大', flag: '🇨🇦' },
            'GB': { name: '英国', flag: '🇬🇧' }, 'UK': { name: '英国', flag: '🇬🇧' },
            'DE': { name: '德国', flag: '🇩🇪' }, 'FR': { name: '法国', flag: '🇫🇷' },
            'AU': { name: '澳大利亚', flag: '🇦🇺' }, 'VN': { name: '越南', flag: '🇻🇳' },
            'TH': { name: '泰国', flag: '🇹🇭' }, 'MY': { name: '马来西亚', flag: '🇲🇾' },
            'ID': { name: '印度尼西亚', flag: '🇮🇩' }, 'PH': { name: '菲律宾', flag: '🇵🇭' },
            'RU': { name: '俄罗斯', flag: '🇷🇺' }, 'UA': { name: '乌克兰', flag: '🇺🇦' },
            'BR': { name: '巴西', flag: '🇧🇷' }, 'IN': { name: '印度', flag: '🇮🇳' }
        };
        
        // 地区映射
        this.regionMap = {
            'CN': '亚洲', 'US': '北美洲', 'JP': '亚洲', 'KR': '亚洲', 'SG': '亚洲',
            'DE': '欧洲', 'GB': '欧洲', 'FR': '欧洲', 'CA': '北美洲', 'AU': '大洋洲',
            'RU': '欧洲', 'BR': '南美洲', 'IN': '亚洲', 'HK': '亚洲', 'TW': '亚洲'
        };
    }

    // 通用API请求方法
    async request(url, options = {}) {
        const cacheKey = `${url}_${JSON.stringify(options)}`;
        
        // 检查缓存
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        try {
            const response = await fetch(`${this.baseURL}${url}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            // 缓存成功的响应
            this.cache.set(cacheKey, {
                data,
                timestamp: Date.now()
            });

            return data;
        } catch (error) {
            console.error(`API请求失败: ${url}`, error);
            throw error;
        }
    }

    // 获取服务器列表
    async getServers() {
        try {
            const response = await this.request('/api/monitor/nodes');
            return response.success ? response.data : [];
        } catch (error) {
            console.error('获取服务器列表失败:', error);
            return [];
        }
    }

    // 获取节点状态
    async getNodeStatus() {
        try {
            const response = await this.request('/api/allnode_status');
            return response.success ? response.data : {};
        } catch (error) {
            console.error('获取节点状态失败:', error);
            return {};
        }
    }

    // 判断节点是否在线
    isNodeOnline(nodeId, statusData) {
        const status = statusData[nodeId];
        if (!status) return false;
        
        if (status.stat === -1 || status.stat === 0 || status.stat === false) {
            return false;
        }
        
        if (typeof status.stat === 'object' && status.stat.offline) {
            return false;
        }
        
        return true;
    }

    // 安全的JSON解析
    safeParseJSON(str, defaultValue = {}) {
        try {
            if (typeof str !== 'string') {
                return typeof str === 'object' ? str : defaultValue;
            }
            const cleaned = str.replace(/^\uFEFF/, '').trim();
            if (!cleaned) return defaultValue;
            return JSON.parse(cleaned);
        } catch (error) {
            console.warn('JSON解析失败:', error.message);
            return defaultValue;
        }
    }

    // 数据增强：扩展服务器数据
    enhanceServerData(server, status = {}) {
        const serverData = this.safeParseJSON(server.data);
        
        // 获取国家信息
        const countryCode = serverData?.location?.code || 'CN';
        const countryInfo = this.countryMap[countryCode] || { name: '未知', flag: '🏳️' };
        
        // 获取标签信息
        const tags = serverData?.tags || [];
        
        // 计算性能指标
        const performance = this.calculatePerformance(server.id, status);
        
        return {
            id: server.id,
            name: server.name,
            ip: server.ip || serverData?.ssh?.host || '',
            region: this.regionMap[countryCode] || '未知地区',
            online: this.isNodeOnline(server.id, status),
            score: performance.score,
            groupId: server.group_id || 'default',
            groupName: server.group_name || '默认分组',
            countryCode: countryCode,
            countryName: countryInfo.name,
            countryFlag: countryInfo.flag,
            tags: tags.map(tag => tag.name || tag),
            latency: performance.latency,
            uptime: performance.uptime,
            disabled: server.status === 0
        };
    }

    // 计算性能指标
    calculatePerformance(nodeId, statusData) {
        const status = statusData[nodeId];
        
        if (!status || !this.isNodeOnline(nodeId, statusData)) {
            return { score: 0, latency: 999, uptime: 0 };
        }

        let score = 60;
        let latency = 100;
        let uptime = 50;

        if (this.isNodeOnline(nodeId, statusData)) {
            score += 20;
            uptime = 85;
        }

        const randomFactor = Math.random();
        latency = Math.floor(20 + randomFactor * 180);
        uptime = Math.floor(80 + randomFactor * 19);
        
        const latencyScore = Math.max(0, (200 - latency) / 200 * 30);
        const uptimeScore = (uptime / 100) * 30;
        score = Math.min(100, score + latencyScore + uptimeScore);

        return {
            score: Math.floor(score),
            latency,
            uptime
        };
    }

    // 获取增强的节点数据
    async getEnhancedNodes() {
        try {
            const [servers, statusData] = await Promise.all([
                this.getServers(),
                this.getNodeStatus()
            ]);

            const enhancedNodes = servers
                .filter(server => server.status !== 0)
                .map(server => this.enhanceServerData(server, statusData));

            return enhancedNodes;
        } catch (error) {
            console.error('获取增强节点数据失败:', error);
            return [];
        }
    }

    // 获取分组统计信息
    getGroupStatsFromNodes(nodes) {
        const groupStats = {};

        nodes.forEach(node => {
            const groupId = node.groupId;
            if (!groupStats[groupId]) {
                groupStats[groupId] = {
                    id: groupId,
                    name: node.groupName,
                    totalCount: 0,
                    onlineCount: 0,
                    nodes: []
                };
            }

            groupStats[groupId].totalCount++;
            if (node.online) {
                groupStats[groupId].onlineCount++;
            }
            groupStats[groupId].nodes.push(node);
        });

        return Object.values(groupStats);
    }

    // 获取所有可用的标签
    async getAvailableTags() {
        try {
            const nodes = await this.getEnhancedNodes();
            const tagSet = new Set();

            nodes.forEach(node => {
                node.tags.forEach(tag => tagSet.add(tag));
            });

            return Array.from(tagSet).sort();
        } catch (error) {
            console.error('获取可用标签失败:', error);
            return [];
        }
    }

    // 获取所有可用的国家
    async getAvailableCountries() {
        try {
            const nodes = await this.getEnhancedNodes();
            const countrySet = new Set();

            nodes.forEach(node => {
                countrySet.add(JSON.stringify({
                    code: node.countryCode,
                    name: node.countryName,
                    flag: node.countryFlag
                }));
            });

            return Array.from(countrySet)
                .map(str => JSON.parse(str))
                .sort((a, b) => a.name.localeCompare(b.name));
        } catch (error) {
            console.error('获取可用国家失败:', error);
            return [];
        }
    }

    // 清除缓存
    clearCache() {
        this.cache.clear();
    }

    // 获取缓存状态
    getCacheStatus() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys()),
            timeout: this.cacheTimeout
        };
    }
}

// 筛选器模块
class NodeFilter {
    constructor() {
        this.filters = {
            search: '',
            group: '',
            tags: new Set(),
            country: '',
            onlineOnly: false,
            highPerformance: false,
            minScore: 0
        };
    }

    setFilter(key, value) {
        if (key === 'tags' && !value instanceof Set) {
            this.filters.tags = new Set(value);
        } else {
            this.filters[key] = value;
        }
    }

    getFilter(key) {
        return this.filters[key];
    }

    clearFilters() {
        this.filters = {
            search: '', group: '', tags: new Set(), country: '',
            onlineOnly: false, highPerformance: false, minScore: 0
        };
    }

    applyFilters(nodes) {
        return nodes.filter(node => {
            // 搜索筛选
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                const searchMatch = node.name.toLowerCase().includes(searchTerm) ||
                                  node.ip.includes(searchTerm) ||
                                  node.groupName.toLowerCase().includes(searchTerm) ||
                                  node.countryName.toLowerCase().includes(searchTerm);
                if (!searchMatch) return false;
            }

            // 分组筛选
            if (this.filters.group && node.groupId !== this.filters.group) {
                return false;
            }

            // 国家筛选
            if (this.filters.country && node.countryCode !== this.filters.country) {
                return false;
            }

            // 在线筛选
            if (this.filters.onlineOnly && !node.online) {
                return false;
            }

            // 高性能筛选
            if (this.filters.highPerformance && node.score < 80) {
                return false;
            }

            // 标签筛选
            if (this.filters.tags.size > 0) {
                const hasMatchingTag = [...this.filters.tags].some(tag => 
                    node.tags.includes(tag)
                );
                if (!hasMatchingTag) return false;
            }

            return true;
        });
    }

    getFilterStats(allNodes, filteredNodes) {
        return {
            total: allNodes.length,
            filtered: filteredNodes.length,
            online: filteredNodes.filter(n => n.online).length,
            highPerformance: filteredNodes.filter(n => n.score >= 80).length
        };
    }
}

addDebugLog('✅ NodeSelectionAPI和NodeFilter类已加载');
</script>

<!-- 嵌入式节点选择集成模块 -->
<script>
// 集成版节点选择系统
class NodeSelectionIntegrated {
    constructor() {
        this.api = new NodeSelectionAPI();
        this.filter = new NodeFilter();
        this.nodes = [];
        this.filteredNodes = [];
        this.selectedNodes = new Set();
        this.currentMode = 'include';
        this.isLoading = false;
        this.allCollapsed = false;
        this.maxRetries = 3;
        this.retryCount = 0;
        
        addDebugLog('NodeSelectionIntegrated 初始化完成');
        this.init();
    }

    async init() {
        this.showLoading(true);
        
        try {
            await this.loadData();
            this.setupEventListeners();
            this.setupDrawerEvents();
            this.populateFilterOptions();
            this.applyFilters();
            this.updateDisplay();
            addDebugLog('🚀 节点选择系统初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
            addDebugLog('❌ 初始化失败: ' + error.message);
            this.showError('初始化失败，请刷新页面重试');
        } finally {
            this.showLoading(false);
        }
    }

    // 加载数据
    async loadData() {
        try {
            addDebugLog('开始加载节点数据...');
            
            const enhancedNodes = await this.api.getEnhancedNodes();
            const groupStats = this.api.getGroupStatsFromNodes(enhancedNodes);

            this.nodes = enhancedNodes;
            this.groupStats = groupStats;
            
            addDebugLog(`成功加载 ${this.nodes.length} 个节点，${this.groupStats.length} 个分组`);
            
            if (this.nodes.length === 0) {
                addDebugLog('未获取到节点数据，尝试使用模拟数据');
                await this.loadMockData();
            }
            
            this.retryCount = 0;
        } catch (error) {
            console.error('加载数据失败:', error);
            addDebugLog(`❌ 加载数据失败: ${error.message}`);
            
            if (this.retryCount < this.maxRetries) {
                this.retryCount++;
                addDebugLog(`重试加载数据 (${this.retryCount}/${this.maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, 1000 * this.retryCount));
                return this.loadData();
            }
            
            addDebugLog('加载真实数据失败，使用模拟数据');
            await this.loadMockData();
        }
    }

    // 降级：加载模拟数据
    async loadMockData() {
        addDebugLog('使用模拟数据模式');
        
        const mockNodes = [];
        const groups = ['default', 'beijing', 'shanghai', 'guangzhou', 'overseas'];
        const countries = ['CN', 'US', 'JP', 'KR', 'SG'];
        const tags = ['高性能', '低延迟', '稳定', '测试'];

        for (let i = 1; i <= 34; i++) {
            const groupId = groups[Math.floor(Math.random() * groups.length)];
            const countryCode = countries[Math.floor(Math.random() * countries.length)];
            const countryInfo = this.api.countryMap[countryCode] || { name: '中国', flag: '🇨🇳' };
            
            mockNodes.push({
                id: `node_${i}`,
                name: `监控节点${i}`,
                ip: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
                region: this.api.regionMap[countryCode] || '亚洲',
                online: Math.random() > 0.2,
                score: Math.floor(Math.random() * 40) + 60,
                groupId: groupId,
                groupName: groupId === 'default' ? '默认分组' : `${groupId}机房`,
                countryCode: countryCode,
                countryName: countryInfo.name,
                countryFlag: countryInfo.flag,
                tags: this.getRandomTags(tags, Math.floor(Math.random() * 3) + 1),
                latency: Math.floor(Math.random() * 200) + 10,
                uptime: Math.floor(Math.random() * 20) + 80,
                disabled: false,
                mockData: true
            });
        }

        this.nodes = mockNodes;
        this.groupStats = this.calculateGroupStats(mockNodes);
        addDebugLog(`模拟数据生成完成: ${mockNodes.length} 个节点`);
    }

    getRandomTags(tags, count) {
        const shuffled = [...tags].sort(() => 0.5 - Math.random());
        return shuffled.slice(0, count);
    }

    calculateGroupStats(nodes) {
        const stats = {};
        nodes.forEach(node => {
            if (!stats[node.groupId]) {
                stats[node.groupId] = {
                    id: node.groupId,
                    name: node.groupName,
                    totalCount: 0,
                    onlineCount: 0,
                    nodes: []
                };
            }
            stats[node.groupId].totalCount++;
            if (node.online) {
                stats[node.groupId].onlineCount++;
            }
            stats[node.groupId].nodes.push(node);
        });
        return Object.values(stats);
    }

    showLoading(show) {
        this.isLoading = show;
        const nodeContainer = document.getElementById('nodeContainer');
        const drawerNodeList = document.getElementById('drawerNodeList');
        
        if (show) {
            const loadingHTML = `
                <div class="flex items-center justify-center p-8">
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <p class="mt-2 text-sm text-gray-600">正在加载节点数据...</p>
                    </div>
                </div>
            `;
            if (nodeContainer) nodeContainer.innerHTML = loadingHTML;
            if (drawerNodeList) drawerNodeList.innerHTML = loadingHTML;
        }
    }

    showError(message) {
        const errorHTML = `
            <div class="flex items-center justify-center p-8">
                <div class="text-center">
                    <div class="text-red-600 mb-2">⚠️</div>
                    <p class="text-sm text-red-600">${message}</p>
                    <button onclick="location.reload()" class="mt-2 px-4 py-2 bg-blue-500 text-white rounded text-sm">
                        重新加载
                    </button>
                </div>
            </div>
        `;
        document.getElementById('nodeContainer').innerHTML = errorHTML;
    }

    // 设置事件监听器
    setupEventListeners() {
        // 主界面事件
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filter.setFilter('search', e.target.value.toLowerCase());
                this.applyFilters();
            });
        }

        const groupFilter = document.getElementById('groupFilter');
        if (groupFilter) {
            groupFilter.addEventListener('change', (e) => {
                this.filter.setFilter('group', e.target.value);
                this.applyFilters();
            });
        }

        const countryFilter = document.getElementById('countryFilter');
        if (countryFilter) {
            countryFilter.addEventListener('change', (e) => {
                this.filter.setFilter('country', e.target.value);
                this.applyFilters();
            });
        }

        const tagFilter = document.getElementById('tagFilter');
        if (tagFilter) {
            tagFilter.addEventListener('change', (e) => {
                const tags = e.target.value ? new Set([e.target.value]) : new Set();
                this.filter.setFilter('tags', tags);
                this.applyFilters();
            });
        }

        const includeMode = document.getElementById('includeMode');
        if (includeMode) {
            includeMode.addEventListener('change', () => {
                if (includeMode.checked) {
                    this.setMode('include');
                }
            });
        }

        const excludeMode = document.getElementById('excludeMode');
        if (excludeMode) {
            excludeMode.addEventListener('change', () => {
                if (excludeMode.checked) {
                    this.setMode('exclude');
                }
            });
        }
    }

    // 设置抽屉事件
    setupDrawerEvents() {
        // 抽屉开关
        const openDrawer = document.getElementById('openDrawer');
        if (openDrawer) {
            openDrawer.addEventListener('click', () => {
                this.openDrawer();
            });
        }

        const closeDrawer = document.getElementById('closeDrawer');
        if (closeDrawer) {
            closeDrawer.addEventListener('click', () => {
                this.closeDrawer();
            });
        }

        const drawerBackdrop = document.getElementById('drawerBackdrop');
        if (drawerBackdrop) {
            drawerBackdrop.addEventListener('click', () => {
                this.closeDrawer();
            });
        }

        // 抽屉内筛选器
        const drawerSearchInput = document.getElementById('drawerSearchInput');
        if (drawerSearchInput) {
            drawerSearchInput.addEventListener('input', (e) => {
                this.filter.setFilter('search', e.target.value.toLowerCase());
                this.applyFilters();
            });
        }

        const drawerOnlineOnly = document.getElementById('drawerOnlineOnly');
        if (drawerOnlineOnly) {
            drawerOnlineOnly.addEventListener('change', (e) => {
                this.filter.setFilter('onlineOnly', e.target.checked);
                this.applyFilters();
            });
        }

        const drawerHighPerformance = document.getElementById('drawerHighPerformance');
        if (drawerHighPerformance) {
            drawerHighPerformance.addEventListener('change', (e) => {
                this.filter.setFilter('highPerformance', e.target.checked);
                this.applyFilters();
            });
        }

        const drawerGroupFilter = document.getElementById('drawerGroupFilter');
        if (drawerGroupFilter) {
            drawerGroupFilter.addEventListener('change', (e) => {
                this.filter.setFilter('group', e.target.value);
                this.applyFilters();
            });
        }

        const drawerCountryFilter = document.getElementById('drawerCountryFilter');
        if (drawerCountryFilter) {
            drawerCountryFilter.addEventListener('change', (e) => {
                this.filter.setFilter('country', e.target.value);
                this.applyFilters();
            });
        }

        // 抽屉模式切换
        const drawerIncludeMode = document.getElementById('drawerIncludeMode');
        if (drawerIncludeMode) {
            drawerIncludeMode.addEventListener('click', () => {
                this.setMode('include');
            });
        }

        const drawerExcludeMode = document.getElementById('drawerExcludeMode');
        if (drawerExcludeMode) {
            drawerExcludeMode.addEventListener('click', () => {
                this.setMode('exclude');
            });
        }

        // 抽屉批量操作
        const drawerSelectAll = document.getElementById('drawerSelectAll');
        if (drawerSelectAll) {
            drawerSelectAll.addEventListener('click', () => {
                this.selectAll();
            });
        }

        const drawerSelectNone = document.getElementById('drawerSelectNone');
        if (drawerSelectNone) {
            drawerSelectNone.addEventListener('click', () => {
                this.selectNone();
            });
        }

        const drawerSelectOnline = document.getElementById('drawerSelectOnline');
        if (drawerSelectOnline) {
            drawerSelectOnline.addEventListener('click', () => {
                this.selectOnline();
            });
        }

        const drawerClearFilters = document.getElementById('drawerClearFilters');
        if (drawerClearFilters) {
            drawerClearFilters.addEventListener('click', () => {
                this.clearFilters();
            });
        }

        const drawerToggleCollapse = document.getElementById('drawerToggleCollapse');
        if (drawerToggleCollapse) {
            drawerToggleCollapse.addEventListener('click', () => {
                this.toggleAllGroups();
            });
        }
    }

    // 抽屉控制
    openDrawer() {
        const backdrop = document.getElementById('drawerBackdrop');
        const drawer = document.getElementById('nodeSelectionDrawer');
        
        if (backdrop) backdrop.classList.add('open');
        if (drawer) drawer.classList.add('open');
        
        addDebugLog('抽屉已打开');
        this.updateDrawerDisplay();
    }

    closeDrawer() {
        const backdrop = document.getElementById('drawerBackdrop');
        const drawer = document.getElementById('nodeSelectionDrawer');
        
        if (backdrop) backdrop.classList.remove('open');
        if (drawer) drawer.classList.remove('open');
        
        addDebugLog('抽屉已关闭');
    }

    // 填充筛选选项
    async populateFilterOptions() {
        try {
            // 主界面分组选项
            const groupSelect = document.getElementById('groupFilter');
            const drawerGroupSelect = document.getElementById('drawerGroupFilter');
            const groupOptions = '<option value="">所有分组</option>' +
                this.groupStats.map(group => 
                    `<option value="${group.id}">${group.name} (${group.onlineCount}/${group.totalCount})</option>`
                ).join('');
            
            if (groupSelect) groupSelect.innerHTML = groupOptions;
            if (drawerGroupSelect) drawerGroupSelect.innerHTML = groupOptions;

            // 国家选项
            const countrySelect = document.getElementById('countryFilter');
            const drawerCountrySelect = document.getElementById('drawerCountryFilter');
            const countries = await this.api.getAvailableCountries();
            const countryOptions = '<option value="">所有国家</option>' +
                countries.map(country => 
                    `<option value="${country.code}">${country.flag} ${country.name}</option>`
                ).join('');
            
            if (countrySelect) countrySelect.innerHTML = countryOptions;
            if (drawerCountrySelect) drawerCountrySelect.innerHTML = countryOptions;

            // 标签选项（主界面简化为下拉框）
            const tagSelect = document.getElementById('tagFilter');
            const tags = await this.api.getAvailableTags();
            const tagOptions = '<option value="">所有标签</option>' +
                tags.map(tag => `<option value="${tag}">${tag}</option>`).join('');
            if (tagSelect) tagSelect.innerHTML = tagOptions;

            // 抽屉标签按钮
            const drawerTagContainer = document.getElementById('drawerTagFilters');
            if (drawerTagContainer) {
                drawerTagContainer.innerHTML = tags.map(tag => 
                    `<button class="tag-button" data-tag="${tag}">${tag}</button>`
                ).join('');

                // 绑定抽屉标签点击事件
                drawerTagContainer.addEventListener('click', (e) => {
                    if (e.target.classList.contains('tag-button')) {
                        this.toggleTag(e.target.dataset.tag);
                        e.target.classList.toggle('active');
                    }
                });
            }

        } catch (error) {
            console.error('填充筛选选项失败:', error);
            addDebugLog('❌ 填充筛选选项失败: ' + error.message);
        }
    }

    toggleTag(tag) {
        const currentTags = this.filter.getFilter('tags');
        if (currentTags.has(tag)) {
            currentTags.delete(tag);
        } else {
            currentTags.add(tag);
        }
        this.filter.setFilter('tags', currentTags);
        this.applyFilters();
    }

    applyFilters() {
        if (this.isLoading) return;
        
        this.filteredNodes = this.filter.applyFilters(this.nodes);
        this.updateDisplay();
        this.updateDrawerDisplay();
    }

    setMode(mode) {
        this.currentMode = mode;
        
        // 更新主界面按钮状态
        const includeMode = document.getElementById('includeMode');
        const excludeMode = document.getElementById('excludeMode');
        if (includeMode) includeMode.checked = (mode === 'include');
        if (excludeMode) excludeMode.checked = (mode === 'exclude');
        
        // 更新抽屉按钮状态
        const drawerIncludeMode = document.getElementById('drawerIncludeMode');
        const drawerExcludeMode = document.getElementById('drawerExcludeMode');
        if (drawerIncludeMode) drawerIncludeMode.classList.toggle('active', mode === 'include');
        if (drawerExcludeMode) drawerExcludeMode.classList.toggle('active', mode === 'exclude');
        
        this.updateDisplay();
        this.updateDrawerDisplay();
    }

    toggleNode(nodeId) {
        if (this.selectedNodes.has(nodeId)) {
            this.selectedNodes.delete(nodeId);
        } else {
            this.selectedNodes.add(nodeId);
        }
        this.updateDisplay();
        this.updateDrawerDisplay();
    }

    selectAll() {
        this.filteredNodes.forEach(node => {
            this.selectedNodes.add(node.id);
        });
        this.updateDisplay();
        this.updateDrawerDisplay();
    }

    selectNone() {
        this.selectedNodes.clear();
        this.updateDisplay();
        this.updateDrawerDisplay();
    }

    selectOnline() {
        this.filteredNodes.forEach(node => {
            if (node.online) {
                this.selectedNodes.add(node.id);
            }
        });
        this.updateDisplay();
        this.updateDrawerDisplay();
    }

    clearFilters() {
        this.filter.clearFilters();

        // 重置主界面UI
        const searchInput = document.getElementById('searchInput');
        const groupFilter = document.getElementById('groupFilter');
        const countryFilter = document.getElementById('countryFilter');
        const tagFilter = document.getElementById('tagFilter');
        
        if (searchInput) searchInput.value = '';
        if (groupFilter) groupFilter.value = '';
        if (countryFilter) countryFilter.value = '';
        if (tagFilter) tagFilter.value = '';

        // 重置抽屉UI
        const drawerSearchInput = document.getElementById('drawerSearchInput');
        const drawerOnlineOnly = document.getElementById('drawerOnlineOnly');
        const drawerHighPerformance = document.getElementById('drawerHighPerformance');
        const drawerGroupFilter = document.getElementById('drawerGroupFilter');
        const drawerCountryFilter = document.getElementById('drawerCountryFilter');
        
        if (drawerSearchInput) drawerSearchInput.value = '';
        if (drawerOnlineOnly) drawerOnlineOnly.checked = false;
        if (drawerHighPerformance) drawerHighPerformance.checked = false;
        if (drawerGroupFilter) drawerGroupFilter.value = '';
        if (drawerCountryFilter) drawerCountryFilter.value = '';
        
        document.querySelectorAll('#drawerTagFilters button').forEach(btn => {
            btn.classList.remove('active');
        });

        this.applyFilters();
    }

    toggleAllGroups() {
        this.allCollapsed = !this.allCollapsed;
        const toggleBtn = document.getElementById('drawerToggleCollapse');
        if (toggleBtn) {
            toggleBtn.textContent = this.allCollapsed ? '展开所有' : '收起所有';
        }
        
        document.querySelectorAll('.collapsible-content').forEach(content => {
            if (this.allCollapsed) {
                content.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
            }
        });
    }

    updateDisplay() {
        if (this.isLoading) return;
        
        this.updateNodeList();
        this.updateStats();
    }

    updateDrawerDisplay() {
        if (this.isLoading) return;
        
        this.updateDrawerNodeList();
        this.updateDrawerStats();
    }

    updateNodeList() {
        const container = document.getElementById('nodeContainer');
        if (!container) return;
        
        container.innerHTML = '';

        if (this.filteredNodes.length === 0) {
            container.innerHTML = `
                <div class="text-center p-8">
                    <p class="text-gray-500 text-sm">暂无符合条件的节点</p>
                </div>
            `;
            return;
        }

        // 按分组显示节点
        const groupedNodes = this.groupNodesByGroup(this.filteredNodes);

        Object.entries(groupedNodes).forEach(([groupName, nodes]) => {
            const groupCard = document.createElement('div');
            groupCard.className = 'bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-4';
            groupCard.innerHTML = `
                <h3 class="text-lg font-semibold mb-3">${groupName} (${nodes.length}个节点)</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    ${nodes.map(node => this.createSimpleNodeCard(node)).join('')}
                </div>
            `;
            container.appendChild(groupCard);
        });
    }

    updateDrawerNodeList() {
        const container = document.getElementById('drawerNodeList');
        if (!container) return;
        
        container.innerHTML = '';

        if (this.filteredNodes.length === 0) {
            container.innerHTML = `
                <div class="text-center p-8">
                    <p class="text-gray-500 text-sm">暂无符合条件的节点</p>
                </div>
            `;
            return;
        }

        // 按分组显示
        const groupedNodes = this.groupNodesByGroup(this.filteredNodes);

        Object.entries(groupedNodes).forEach(([groupName, nodes]) => {
            // 分组标题
            const groupHeader = document.createElement('div');
            groupHeader.className = 'group-header';
            groupHeader.innerHTML = `
                <div class="flex items-center">
                    <span class="text-sm font-medium">${groupName}</span>
                    <span class="ml-2 text-xs text-gray-500">(${nodes.length})</span>
                </div>
                <div class="flex gap-1">
                    <button class="text-xs px-2 py-1 bg-blue-100 hover:bg-blue-200 rounded" onclick="nodeSelection.selectGroupNodes('${groupName}')">全选</button>
                    <button class="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded" onclick="nodeSelection.deselectGroupNodes('${groupName}')">取消</button>
                </div>
            `;
            
            groupHeader.addEventListener('click', (e) => {
                if (!e.target.closest('button')) {
                    this.toggleGroupExpansion(groupName);
                }
            });
            
            container.appendChild(groupHeader);

            // 分组内容
            const groupContent = document.createElement('div');
            groupContent.className = 'collapsible-content expanded';
            groupContent.setAttribute('data-group', groupName);

            nodes.forEach(node => {
                const nodeCard = this.createDrawerNodeCard(node);
                groupContent.appendChild(nodeCard);
            });

            container.appendChild(groupContent);
        });
    }

    groupNodesByGroup(nodes) {
        const grouped = {};
        nodes.forEach(node => {
            const groupName = node.groupName || '默认分组';
            if (!grouped[groupName]) {
                grouped[groupName] = [];
            }
            grouped[groupName].push(node);
        });
        return grouped;
    }

    toggleGroupExpansion(groupName) {
        const groupContent = document.querySelector(`[data-group="${groupName}"]`);
        if (groupContent) {
            groupContent.classList.toggle('expanded');
        }
    }

    createSimpleNodeCard(node) {
        const isSelected = this.selectedNodes.has(node.id);
        const dataSource = node.mockData ? '📊' : '🔗';
        
        return `
            <div class="border rounded-lg p-3 cursor-pointer transition-colors ${
                isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'
            }" onclick="nodeSelection.toggleNode('${node.id}')">
                <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center">
                        <input type="checkbox" ${isSelected ? 'checked' : ''} class="mr-2" onclick="event.stopPropagation()">
                        <span class="text-sm font-medium">${node.name} ${dataSource}</span>
                    </div>
                    <div class="text-xs ${
                        node.online ? 'text-green-600' : 'text-red-600'
                    }">${node.online ? '在线' : '离线'}</div>
                </div>
                <div class="text-xs text-gray-600">
                    <div>${node.countryFlag} ${node.ip}</div>
                    <div>延迟: ${node.latency}ms | 评分: ${node.score}</div>
                </div>
            </div>
        `;
    }

    createDrawerNodeCard(node) {
        const isSelected = this.selectedNodes.has(node.id);
        const div = document.createElement('div');
        let cardClass = 'node-card';
        
        if (isSelected) {
            cardClass += ' selected';
        }
        if (this.currentMode === 'exclude' && isSelected) {
            cardClass += ' excluded';
        }
        
        div.className = cardClass;
        div.onclick = () => this.toggleNode(node.id);

        const tagsHtml = node.tags.slice(0, 2).map(tag => `<span class="tag-chip">${tag}</span>`).join('');
        const moreTagsText = node.tags.length > 2 ? `+${node.tags.length - 2}` : '';
        const dataSource = node.mockData ? '<span class="text-xs text-orange-500" title="模拟数据">📊</span>' : '<span class="text-xs text-green-500" title="真实数据">🔗</span>';
        
        div.innerHTML = `
            <div class="flex justify-between items-start">
                <div class="flex items-start">
                    <input type="checkbox" ${isSelected ? 'checked' : ''} class="mt-1 mr-2" onclick="event.stopPropagation()">
                    <div>
                        <div class="text-sm font-medium">${node.countryFlag} ${node.name} ${dataSource}</div>
                        <div class="text-xs text-gray-500">${node.ip}</div>
                        <div class="mt-1">
                            ${tagsHtml}
                            ${moreTagsText ? `<span class="text-xs text-gray-400">${moreTagsText}</span>` : ''}
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-xs ${
                        node.online ? 'text-green-600' : 'text-red-600'
                    } font-medium">${node.online ? '在线' : '离线'}</div>
                    <div class="text-xs text-gray-500">${node.score}分</div>
                    <div class="text-xs text-gray-500">${node.latency}ms</div>
                </div>
            </div>
        `;

        return div;
    }

    selectGroupNodes(groupName) {
        this.filteredNodes.forEach(node => {
            if (node.groupName === groupName) {
                this.selectedNodes.add(node.id);
            }
        });
        this.updateDisplay();
        this.updateDrawerDisplay();
    }

    deselectGroupNodes(groupName) {
        this.filteredNodes.forEach(node => {
            if (node.groupName === groupName) {
                this.selectedNodes.delete(node.id);
            }
        });
        this.updateDisplay();
        this.updateDrawerDisplay();
    }

    updateStats() {
        const total = this.nodes.length;
        const filtered = this.filteredNodes.length;
        const selected = this.selectedNodes.size;
        const online = this.filteredNodes.filter(n => n.online).length;

        let actualCount;
        if (this.currentMode === 'include') {
            actualCount = selected;
        } else {
            actualCount = filtered - selected;
        }

        const totalNodesEl = document.getElementById('totalNodes');
        const onlineNodesEl = document.getElementById('onlineNodes');
        const filteredNodesEl = document.getElementById('filteredNodes');
        const selectedNodesEl = document.getElementById('selectedNodes');
        
        if (totalNodesEl) totalNodesEl.textContent = total;
        if (onlineNodesEl) onlineNodesEl.textContent = online;
        if (filteredNodesEl) filteredNodesEl.textContent = filtered;
        if (selectedNodesEl) selectedNodesEl.textContent = selected;
    }

    updateDrawerStats() {
        const total = this.nodes.length;
        const filtered = this.filteredNodes.length;
        const selected = this.selectedNodes.size;

        let actualCount;
        if (this.currentMode === 'include') {
            actualCount = selected;
        } else {
            actualCount = filtered - selected;
        }

        const drawerFilterStatsEl = document.getElementById('drawerFilterStats');
        const drawerSelectedCountEl = document.getElementById('drawerSelectedCount');
        const drawerActualCountEl = document.getElementById('drawerActualCount');
        const drawerCurrentModeEl = document.getElementById('drawerCurrentMode');
        
        if (drawerFilterStatsEl) drawerFilterStatsEl.textContent = `${filtered}/${total}`;
        if (drawerSelectedCountEl) drawerSelectedCountEl.textContent = selected;
        if (drawerActualCountEl) drawerActualCountEl.textContent = actualCount;
        if (drawerCurrentModeEl) drawerCurrentModeEl.textContent = this.currentMode === 'include' ? '包含模式' : '排除模式';
    }

    // 获取当前选择的节点数据
    getSelectedNodesData() {
        const selectedData = [];
        this.selectedNodes.forEach(nodeId => {
            const node = this.nodes.find(n => n.id === nodeId);
            if (node) {
                selectedData.push(node);
            }
        });
        return selectedData;
    }

    // 获取实际监控的节点数据
    getMonitoringNodesData() {
        if (this.currentMode === 'include') {
            return this.getSelectedNodesData();
        } else {
            return this.filteredNodes.filter(node => !this.selectedNodes.has(node.id));
        }
    }
}

// 初始化节点选择系统
let nodeSelection;
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        nodeSelection = new NodeSelectionIntegrated();
        window.nodeSelection = nodeSelection;
    });
} else {
    nodeSelection = new NodeSelectionIntegrated();
    window.nodeSelection = nodeSelection;
}

addDebugLog('✅ 节点选择系统脚本加载完成');
</script>
{% endblock %}