
<nav id="main-navbar" class="fixed top-0 left-0 right-0 z-40 {% if admin %}admin-appbar{% endif %}">
    <!-- 背景层 -->
    <div class="absolute inset-0" style="padding-top: env(safe-area-inset-top);">
        <div class="absolute inset-0 bg-white/95 backdrop-blur-sm dark:bg-slate-900/95 transition-colors duration-200" style="margin-top: calc(-1 * env(safe-area-inset-top));"></div>
        <div class="absolute bottom-0 left-0 right-0 h-[1px] border-b border-slate-200/60 dark:border-slate-800/60"></div>
    </div>

    <!-- 内容容器 -->
    <div class="relative {% if not admin %}max-w-screen-2xl mx-auto{% endif %} h-16 {% if admin %}appbar-content{% endif %}">
        <div class="flex items-center justify-between h-full px-4 sm:px-6 {% if admin %}lg:px-8{% endif %}" style="padding-top: env(safe-area-inset-top);">
            <!-- Logo区域 -->
            <div class="flex items-center">
                <a href="/" class="group flex items-center space-x-2 sm:space-x-3">
                    <!-- Logo图标 -->
                    <div class="relative w-8 h-8 sm:w-9 sm:h-9 flex items-center justify-center">
                        <img src="/img/logo.png" class="w-6 h-6 sm:w-7 sm:h-7 transition-transform duration-300 group-hover:scale-110 object-contain" alt="Logo">
                        <div class="absolute inset-0 bg-indigo-500/10 rounded-xl blur-sm transition-opacity duration-300 opacity-0 group-hover:opacity-100"></div>
                    </div>
                    <!-- 站点名称 -->
                    <span class="text-base sm:text-lg font-medium bg-gradient-to-r from-indigo-600 to-indigo-800 dark:from-indigo-300 dark:to-indigo-500 bg-clip-text text-transparent">
                        {{setting.site.name}}
                    </span>
                </a>
            </div>

            <!-- 导航按钮组 - 调整间距 -->
            <div class="flex items-center space-x-0 lg:space-x-1">
                <!-- 主导航按钮 -->
                <a href="/" id="home-nav-link" class="nav-link group {%if title=='节点状态'%}nav-link-active{%endif%}" data-nav-link>
                    <div class="flex items-center space-x-1.5 sm:space-x-2">
                        <i class="ti ti-server transition-colors"></i>
                        <span class="hidden md:block text-xs sm:text-sm font-medium">节点状态</span>
                    </div>
                </a>

                <!-- 网络质量监控按钮 -->
                <a href="/network-quality" class="nav-link group {%if title=='网络质量监控'%}nav-link-active{%endif%}" data-feature="NETWORK_QUALITY" data-nav-link>
                    <div class="flex items-center space-x-1.5 sm:space-x-2">
                        <i class="ti ti-activity transition-colors"></i>
                        <span class="hidden md:block text-xs sm:text-sm font-medium">网络质量</span>
                    </div>
                </a>

                <!-- 主题切换按钮 -->
                <button id="theme-toggle" class="nav-link group">
                    <div class="flex items-center space-x-1.5 sm:space-x-2">
                        <i id="theme-toggle-icon" class="ti ti-moon transition-colors"></i>
                        <span class="hidden lg:block text-xs sm:text-sm font-medium">主题</span>
                    </div>
                </button>

                {%if admin%}
                <!-- 设置下拉菜单 -->
                <div class="relative" id="settings-dropdown">
                    <button class="nav-link group flex items-center" id="settings-dropdown-toggle">
                        <div class="flex items-center space-x-1.5 sm:space-x-2">
                            <i class="ti ti-settings transition-colors"></i>
                            <span class="hidden md:block text-xs sm:text-sm font-medium">设置</span>
                            <i class="ti ti-chevron-down transition-transform duration-200" id="dropdown-arrow"></i>
                        </div>
                    </button>
                    
                    <!-- 下拉菜单内容 -->
                    <div id="settings-dropdown-menu" class="absolute right-0 mt-1 w-40 bg-white dark:bg-slate-800 rounded-md shadow-lg border border-slate-200 dark:border-slate-700 opacity-0 invisible transform -translate-y-2 transition-transform transition-opacity duration-200">
                        <style>
                            /* 确保深色模式下的 hover 效果正常工作 - 使用明显不同的颜色 */
                            #settings-dropdown-menu a:hover {
                                background-color: #e0e7ff !important; /* 浅蓝色 */
                            }
                            :root.dark #settings-dropdown-menu a:hover {
                                background-color: #3b4764 !important; /* 深蓝灰色 */
                            }
                        </style>
                        <!-- 服务器管理组 -->
                        <div class="p-1.5">
                            <div class="text-xs font-semibold text-slate-500 dark:text-slate-400 px-2 py-1">服务器管理</div>
                            <a href="/admin/monitor" class="block px-3 py-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors {%if title=='网络监控配置'%}bg-slate-100 dark:bg-slate-700{%endif%}">
                                <div class="flex items-center">
                                    <i class="ti ti-activity text-base text-slate-600 dark:text-slate-400 mr-3"></i>
                                    <span class="text-sm text-slate-700 dark:text-slate-300">网络监控配置</span>
                                </div>
                            </a>
                            <a href="/admin/servers" class="block px-3 py-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors {%if title=='管理服务器'%}bg-slate-100 dark:bg-slate-700{%endif%}">
                                <div class="flex items-center">
                                    <i class="ti ti-server-2 text-base text-slate-600 dark:text-slate-400 mr-3"></i>
                                    <span class="text-sm text-slate-700 dark:text-slate-300">服务器列表</span>
                                </div>
                            </a>
                            <a href="/admin/groups" class="block px-3 py-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors">
                                <div class="flex items-center">
                                    <i class="ti ti-users-group text-base text-slate-600 dark:text-slate-400 mr-3"></i>
                                    <span class="text-sm text-slate-700 dark:text-slate-300">分组管理</span>
                                </div>
                            </a>
                            <a href="/admin/autodiscovery" class="block px-3 py-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors">
                                <div class="flex items-center">
                                    <i class="ti ti-device-tablet text-base text-slate-600 dark:text-slate-400 mr-3"></i>
                                    <span class="text-sm text-slate-700 dark:text-slate-300">自动发现</span>
                                </div>
                            </a>
                            <a href="/admin/ssh_scripts" class="block px-3 py-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors">
                                <div class="flex items-center">
                                    <i class="ti ti-terminal-2 text-base text-slate-600 dark:text-slate-400 mr-3"></i>
                                    <span class="text-sm text-slate-700 dark:text-slate-300">SSH脚本</span>
                                </div>
                            </a>
                        </div>
                        
                        <!-- 分隔线 -->
                        <div class="border-t border-slate-200 dark:border-slate-700"></div>
                        
                        <!-- 系统设置组 -->
                        <div class="p-1.5">
                            <div class="text-xs font-semibold text-slate-500 dark:text-slate-400 px-2 py-1">系统设置</div>
                            <a href="/admin/setting" class="block px-3 py-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors {%if title=='管理设置'%}bg-slate-100 dark:bg-slate-700{%endif%}">
                                <div class="flex items-center">
                                    <i class="ti ti-settings text-base text-slate-600 dark:text-slate-400 mr-3"></i>
                                    <span class="text-sm text-slate-700 dark:text-slate-300">系统设置</span>
                                </div>
                            </a>
                            <a href="/admin/notification" class="block px-3 py-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors {%if title=='通知设置'%}bg-slate-100 dark:bg-slate-700{%endif%}">
                                <div class="flex items-center">
                                    <i class="ti ti-bell text-base text-slate-600 dark:text-slate-400 mr-3"></i>
                                    <span class="text-sm text-slate-700 dark:text-slate-300">通知设置</span>
                                </div>
                            </a>
                            <a href="/admin/advanced-settings" class="block px-3 py-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors {%if title=='高级设置'%}bg-slate-100 dark:bg-slate-700{%endif%}">
                                <div class="flex items-center">
                                    <i class="ti ti-adjustments text-base text-slate-600 dark:text-slate-400 mr-3"></i>
                                    <span class="text-sm text-slate-700 dark:text-slate-300">高级设置</span>
                                </div>
                            </a>
                            <a href="/admin/personalization" class="block px-3 py-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors {%if title=='个性化设置'%}bg-slate-100 dark:bg-slate-700{%endif%}">
                                <div class="flex items-center">
                                    <i class="ti ti-palette text-base text-slate-600 dark:text-slate-400 mr-3"></i>
                                    <span class="text-sm text-slate-700 dark:text-slate-300">美化设置</span>
                                </div>
                            </a>
                            <a href="/admin/log-management" class="block px-3 py-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors">
                                <div class="flex items-center">
                                    <i class="ti ti-file-text text-base text-slate-600 dark:text-slate-400 mr-3"></i>
                                    <span class="text-sm text-slate-700 dark:text-slate-300">日志管理</span>
                                </div>
                            </a>
                            <a href="/admin/license-management" class="block px-3 py-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors">
                                <div class="flex items-center">
                                    <i class="ti ti-key text-base text-slate-600 dark:text-slate-400 mr-3"></i>
                                    <span class="text-sm text-slate-700 dark:text-slate-300">授权管理</span>
                                </div>
                            </a>
                        </div>
                        
                        <!-- 分隔线 -->
                        <div class="border-t border-slate-200 dark:border-slate-700"></div>
                        
                        <!-- 高级分析 -->
                        <div class="p-1.5">
                            <a href="/admin/analytics" data-feature="ADVANCED_ANALYTICS" class="block px-3 py-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors {%if title=='高级分析'%}bg-slate-100 dark:bg-slate-700{%endif%}">
                                <div class="flex items-center">
                                    <i class="ti ti-chart-line text-base text-slate-600 dark:text-slate-400 mr-3"></i>
                                    <span class="text-sm text-slate-700 dark:text-slate-300">高级分析</span>
                                </div>
                            </a>
                        </div>
                        
                    </div>
                </div>

                <!-- 分隔线 - 在大屏幕上显示 -->
                <div class="hidden sm:block w-px h-6 bg-slate-200 dark:bg-slate-800 mx-1 lg:mx-2"></div>

                <!-- 退出按钮 -->
                <a href="/logout" class="nav-link group" data-nav-link>
                    <div class="flex items-center space-x-1.5 sm:space-x-2">
                        <i class="ti ti-logout transition-colors"></i>
                        <span class="hidden md:block text-xs sm:text-sm font-medium">退出</span>
                    </div>
                </a>
                {%else%}
                <a href="/login" class="nav-link group" data-nav-link>
                    <div class="flex items-center space-x-1.5 sm:space-x-2">
                        <i class="ti ti-login transition-colors"></i>
                        <span class="hidden md:block text-xs sm:text-sm font-medium">登录</span>
                    </div>
                </a>
                {%endif%}
            </div>
        </div>
    </div>
</nav>

<!-- 为导航栏预留空间 -->
<div style="height: calc(4rem + env(safe-area-inset-top));"></div>

<script>
// 主题切换和下拉菜单功能
document.addEventListener('DOMContentLoaded', function() {
    // 下拉菜单功能
    const dropdown = document.getElementById('settings-dropdown');
    const dropdownToggle = document.getElementById('settings-dropdown-toggle');
    const dropdownMenu = document.getElementById('settings-dropdown-menu');
    const dropdownArrow = document.getElementById('dropdown-arrow');
    let isOpen = false;
    let closeTimeout;

    function toggleDropdown() {
        isOpen = !isOpen;
        if (isOpen) {
            dropdownMenu.classList.remove('opacity-0', 'invisible', '-translate-y-2');
            dropdownMenu.classList.add('opacity-100', 'visible', 'translate-y-0');
            dropdownArrow.style.transform = 'rotate(180deg)';
        } else {
            dropdownMenu.classList.add('opacity-0', 'invisible', '-translate-y-2');
            dropdownMenu.classList.remove('opacity-100', 'visible', 'translate-y-0');
            dropdownArrow.style.transform = 'rotate(0deg)';
        }
    }

    function closeDropdown() {
        if (isOpen) {
            isOpen = false;
            dropdownMenu.classList.add('opacity-0', 'invisible', '-translate-y-2');
            dropdownMenu.classList.remove('opacity-100', 'visible', 'translate-y-0');
            dropdownArrow.style.transform = 'rotate(0deg)';
        }
    }

    // 点击按钮切换下拉菜单
    if (dropdownToggle) {
        dropdownToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            clearTimeout(closeTimeout);
            toggleDropdown();
        });
    }

    // 鼠标进入下拉菜单区域时保持打开
    if (dropdown) {
        dropdown.addEventListener('mouseenter', function() {
            clearTimeout(closeTimeout);
        });

        // 鼠标离开下拉菜单区域时延迟关闭
        dropdown.addEventListener('mouseleave', function() {
            closeTimeout = setTimeout(function() {
                closeDropdown();
            }, 300);
        });
    }

    // 点击页面其他地方关闭下拉菜单
    document.addEventListener('click', function(e) {
        if (!dropdown || !dropdown.contains(e.target)) {
            closeDropdown();
        }
    });

    // 键盘导航支持
    if (dropdownToggle) {
        // Escape 键关闭下拉菜单
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && isOpen) {
                closeDropdown();
                dropdownToggle.focus();
            }
        });

        // 下拉菜单内的键盘导航
        if (dropdownMenu) {
            const menuItems = dropdownMenu.querySelectorAll('a, button');
            let currentIndex = -1;

            dropdownToggle.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowDown' && !isOpen) {
                    e.preventDefault();
                    toggleDropdown();
                    if (menuItems.length > 0) {
                        currentIndex = 0;
                        menuItems[0].focus();
                    }
                }
            });

            dropdownMenu.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    currentIndex = (currentIndex + 1) % menuItems.length;
                    menuItems[currentIndex].focus();
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    currentIndex = (currentIndex - 1 + menuItems.length) % menuItems.length;
                    menuItems[currentIndex].focus();
                }
            });
        }
    }

    // 主题切换功能
    const themeToggleSwitch = document.getElementById('theme-toggle-switch');
    const themeToggleMenu = document.getElementById('theme-toggle-menu');
    const themeIconMenu = document.getElementById('theme-icon-menu');
    const themeToggleButton = document.getElementById('theme-toggle');
    const themeToggleIcon = document.getElementById('theme-toggle-icon');

    // 初始化主题切换状态
    const isDark = document.documentElement.classList.contains('dark');
    
    // 更新主题切换图标
    if (themeToggleIcon) {
        if (isDark) {
            themeToggleIcon.className = themeToggleIcon.className.replace('ti-moon', 'ti-sun');
        } else {
            themeToggleIcon.className = themeToggleIcon.className.replace('ti-sun', 'ti-moon');
        }
    }
    
    // 初始化主题切换开关状态（如果存在）
    if (themeToggleSwitch) {
        themeToggleSwitch.checked = isDark;
    }

    // 主题切换按钮事件
    const themeToggleBtn = document.getElementById('theme-toggle');
    if (themeToggleBtn && window.themeManager) {
        themeToggleBtn.addEventListener('click', function(e) {
            e.preventDefault();
            window.themeManager.toggleTheme();
        });
    }
    
    // 主题切换菜单事件（如果存在）
    if (themeToggleMenu && window.themeManager) {
        themeToggleMenu.addEventListener('click', function(e) {
            e.preventDefault();
            window.themeManager.toggleTheme();
        });
    }

    // 监听主题变化事件
    document.addEventListener('theme:changed', function(e) {
        // 更新开关状态
        if (themeToggleSwitch) {
            themeToggleSwitch.checked = e.detail.isDark;
        }
        
        // 更新主题切换图标
        if (themeToggleIcon) {
            if (e.detail.isDark) {
                themeToggleIcon.className = themeToggleIcon.className.replace('ti-moon', 'ti-sun');
            } else {
                themeToggleIcon.className = themeToggleIcon.className.replace('ti-sun', 'ti-moon');
            }
        }
        
        // 更新菜单图标（如果存在）
        if (themeIconMenu) {
            if (e.detail.isDark) {
                themeIconMenu.className = themeIconMenu.className.replace('ti-moon', 'ti-sun');
            } else {
                themeIconMenu.className = themeIconMenu.className.replace('ti-sun', 'ti-moon');
            }
        }
    });

    // 注意：简化版主题切换已整合到上面的通用处理中

    // 为首页链接添加WebSocket连接保持功能
    const homeNavLink = document.getElementById('home-nav-link');
    if (homeNavLink) {
        homeNavLink.addEventListener('click', function() {
            // 在跳转到首页前保存WebSocket连接状态
            try {
                // 批量更新 localStorage，减少 I/O 操作
                const connectionState = {
                    active: 'true',
                    timestamp: Date.now().toString()
                };
                
                // 合并操作到一个存储键
                localStorage.setItem('stats_connection_state', JSON.stringify(connectionState));
                
                // 清除第一次数据标记
                localStorage.removeItem('stats_first_data_received');

                // 确保网络数据缓存不会被清除
                const networkDataCache = localStorage.getItem('network_data_cache');
                if (networkDataCache) {
                    // 更新缓存时间戳，确保缓存不会过期
                    try {
                        const data = JSON.parse(networkDataCache);
                        data.timestamp = Date.now();
                        localStorage.setItem('network_data_cache', JSON.stringify(data));
                        console.log('跳转到首页前已更新网络数据缓存时间戳');
                    } catch (e) {
                        console.error('更新网络数据缓存时间戳失败:', e);
                    }
                }

                console.log('跳转到首页前已保存WebSocket连接状态');
            } catch (e) {
                console.error('保存WebSocket连接状态失败:', e);
                // 存储满时的降级处理
                if (e.name === 'QuotaExceededError') {
                    try {
                        // 清理旧数据
                        const keysToRemove = [];
                        for (let i = 0; i < localStorage.length; i++) {
                            const key = localStorage.key(i);
                            if (key && (key.startsWith('temp_') || key.includes('_old'))) {
                                keysToRemove.push(key);
                            }
                        }
                        keysToRemove.forEach(key => localStorage.removeItem(key));
                        
                        // 重试保存
                        localStorage.setItem('stats_connection_state', JSON.stringify({
                            active: 'true',
                            timestamp: Date.now().toString()
                        }));
                    } catch (retryError) {
                        console.error('清理后重试仍失败:', retryError);
                    }
                }
            }
        });
    }
});
</script>
