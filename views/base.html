{%set Title=setting.site.name%}
<!DOCTYPE html>
<html lang="zh-CN" data-blur-enabled="{% if setting.personalization.blur and setting.personalization.blur.enabled %}true{% else %}false{% endif %}" data-blur-quality="{{setting.personalization.blur.quality|default("normal")}}">
<head>
    <script>
    // 必须放在<head>最前面，防止主题闪烁
    (function() {
      try {
        // 与 theme-manager.js 使用相同的本地存储键，防止初始化时主题闪烁
        var port = location.port || '80';
        var host = (location.hostname || '').replace(/\./g, '_');
        var key = 'theme_' + host + '_' + port;
        var theme = localStorage.getItem(key) || localStorage.getItem('theme');
        if (theme === 'light') {
          document.documentElement.classList.remove('dark');
        } else if (theme === 'dark') {
          document.documentElement.classList.add('dark');
        } else {
          // 默认与主题管理器保持一致，避免首次加载闪烁
          document.documentElement.classList.add('dark');
        }
      } catch(e) {}
    })();
    </script>
    
    <!-- 日志管理器（必须在所有脚本之前加载） -->
    <script src="/js/utils/logger.js"></script>
    <!-- 版本信息（提供 window.VERSION_INFO.version，避免额外网络请求） -->
    <script src="/js/version-info.js"></script>

    <!-- 提供站点范围给 SharedWorker 身份模块使用 -->
    <script>
      window.SITE_INFO = {
        name: "{{setting.site.name}}" || '',
        url: "{{setting.site.url}}" || ''
      };
    </script>

    <!-- SharedWorker 身份构建与补丁（专用目录，保持原有模块不变） -->
    <script src="/js/sharedworker/identity.js"></script>
    <script src="/js/sharedworker/patch-stats-shared-client.js"></script>
    
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, minimal-ui">
    <meta name="is-admin" content="{{admin|default(false)}}">

    <!-- PWA 支持 -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="{{Title}}">
    <meta name="theme-color" content="#0f172a" media="(prefers-color-scheme: dark)">
    <meta name="theme-color" content="#0f172a" media="(prefers-color-scheme: light)">

    <!-- 统一使用 PNG 图标 -->
    <link rel="icon" href="/img/logo.png" type="image/png">
    <link rel="apple-touch-icon" href="/img/logo.png">

    <!-- PWA manifest -->
    <link rel="manifest" href="/manifest.json">

    <title>{{Title}} - Dstatus</title>
    <link rel="shortcut icon" href="/img/logo.png">

    
    <!-- Tabler Icons Webfont 图标库 - 稳定版本 -->
    <link rel="stylesheet" href="/libs/tabler-icons/tabler-icons.min.css" id="tabler-icons-css">

    <!-- 主题文件 -->
    <link rel="stylesheet" href="/css/style.min.css">
    <link rel="stylesheet" href="/css/scrollbar.css">
    <link rel="stylesheet" href="/css/components/theme.css">
    <!-- 组件样式 - 临时修复 -->
    <!-- <link rel="stylesheet" href="/css/components/components.css"> -->
    <!-- 拆分后的组件样式文件 -->
    <link rel="stylesheet" href="/css/components/buttons.css">
    <link rel="stylesheet" href="/css/components/cards.css">
    <link rel="stylesheet" href="/css/components/forms.css">
    <link rel="stylesheet" href="/css/components/layout.css">
    <!-- 功能墙样式 -->
    <link rel="stylesheet" href="/css/components/feature-wall.css">
    <!-- 毛玻璃效果样式 -->
    <link rel="stylesheet" href="/css/components/glassmorphism.css">

    {% if admin %}
    <!-- 管理后台侧边栏样式 - 仅admin页面需要 -->
    <link rel="stylesheet" href="/css/components/admin-sidebar.css">
    {% endif %}

    <!-- OS Logos Pack helper (self-hosted SVGs) -->
    <script src="/js/icons/os-icons.js"></script>
    

    <!-- 个性化设置数据 -->
    <script id="personalization-data" type="application/json">
    {
      "wallpaper": {
        "enabled": {% if setting.personalization and setting.personalization.wallpaper and setting.personalization.wallpaper.enabled %}true{% else %}false{% endif %},
        "url": "{% if setting.personalization and setting.personalization.wallpaper and setting.personalization.wallpaper.url %}{{setting.personalization.wallpaper.url}}{% else %}{% endif %}",
        "brightness": {% if setting.personalization and setting.personalization.wallpaper %}{{setting.personalization.wallpaper.brightness|default(75)}}{% else %}75{% endif %},
        "fixed": {% if setting.personalization and setting.personalization.wallpaper and setting.personalization.wallpaper.fixed %}true{% else %}false{% endif %},
        "size": "{% if setting.personalization and setting.personalization.wallpaper %}{{setting.personalization.wallpaper.size|default('cover')}}{% else %}cover{% endif %}",
        "repeat": "{% if setting.personalization and setting.personalization.wallpaper %}{{setting.personalization.wallpaper.repeat|default('repeat')}}{% else %}repeat{% endif %}",
        "blur": {
          "enabled": {% if setting.personalization and setting.personalization.wallpaper and setting.personalization.wallpaper.blur and setting.personalization.wallpaper.blur.enabled %}true{% else %}false{% endif %},
          "amount": {% if setting.personalization and setting.personalization.wallpaper and setting.personalization.wallpaper.blur %}{{setting.personalization.wallpaper.blur.amount|default(5)}}{% else %}5{% endif %}
        }
      }
    }
    </script>

    {% if setting.personalization and setting.personalization.theme %}
   

   

    {% endif %}

    <!-- 背景样式处理 -->
    {% if setting.personalization and setting.personalization.wallpaper %}
    <!-- 预加载壁纸图片 -->
    {% if setting.personalization.wallpaper.enabled and setting.personalization.wallpaper.url %}
    <link rel="preload" href="{{setting.personalization.wallpaper.url}}" as="image">
    <script src="/js/wallpaper-loader.js"></script>
    {% endif %}
    {% endif %}




    <!-- 当前页面ID -->
    <meta name="page-type" content="{% if admin %}admin{% else %}{% if card %}card{% else %}list{% endif %}{% endif %}">
    
    <!-- 通知系统样式 -->
    <style>
        .notification-bubble {
            will-change: transform, opacity;
            transform-origin: right center;
        }
        
        .notification-bubble.translate-x-0 {
            transform: translateX(0);
        }
        
        .notification-bubble.translate-x-full {
            transform: translateX(calc(100% + 2rem));
        }
        
        /* 防止通知在切换主题时闪烁 */
        .notification-bubble {
            transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1),
                        opacity 300ms cubic-bezier(0.4, 0, 0.2, 1),
                        top 200ms cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* 确保通知始终在最上层 */
        .notification-bubble {
            z-index: 9999;
        }
        
        /* 优化模糊效果性能 */
        @supports (backdrop-filter: blur(12px)) {
            .notification-bubble {
                backdrop-filter: blur(12px);
                -webkit-backdrop-filter: blur(12px);
            }
        }
        
        /* 降级方案：不支持 backdrop-filter 的浏览器 */
        @supports not (backdrop-filter: blur(12px)) {
            .notification-bubble.dark\:bg-slate-800\/90 {
                background-color: rgba(30, 41, 59, 0.95);
            }
            .notification-bubble.bg-white\/95 {
                background-color: rgba(255, 255, 255, 0.98);
            }
        }
    </style>

    <!-- 性能优化脚本 -->
    <script>
        // 检测设备性能
        function checkDevicePerformance() {
            const performanceAPI = window.performance || window.mozPerformance || window.msPerformance || window.webkitPerformance || {};

            // 检查是否为低端设备
            const isLowEndDevice = () => {
                if ('deviceMemory' in navigator) {
                    return navigator.deviceMemory < 4; // 小于4GB内存视为低端设备
                }
                return false;
            };

            // 检查是否为移动设备
            const isMobileDevice = () => {
                return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            };

            return {
                isLowEnd: isLowEndDevice(),
                isMobile: isMobileDevice()
            };
        }

        // 应用性能优化
        function applyPerformanceOptimizations() {
            const performance = checkDevicePerformance();
            const root = document.documentElement;

            if (performance.isLowEnd || performance.isMobile) {
                // 降低模糊质量
                root.style.setProperty('--blur-quality', 'low');
                // 减少透明度计算
                root.style.setProperty('--blur-opacity', '0.9');

                // 添加低性能标记
                document.querySelectorAll('.blur-enabled').forEach(el => {
                    el.setAttribute('data-quality', 'low');
                });
            }
        }

        // 监听页面加载完成
        document.addEventListener('DOMContentLoaded', () => {
            applyPerformanceOptimizations();

            // 应用模糊效果
            const blurEnabled = document.documentElement.getAttribute('data-blur-enabled') === 'true';
            const blurQuality = document.documentElement.getAttribute('data-blur-quality') || 'normal';
            if (blurEnabled) {
                document.querySelectorAll('.card, .server-card').forEach(el => {
                    el.classList.add('blur-enabled');
                    el.setAttribute('data-quality', blurQuality);
                });
            }
        });

        // 监听设备方向变化，优化性能
        window.addEventListener('orientationchange', () => {
            requestAnimationFrame(() => {
                applyPerformanceOptimizations();
            });
        });
    </script>

    <!-- 加载延迟优化 -->

    <!-- 页面特定样式和脚本 -->
    {% block head %}{% endblock %}
</head>
<body class="min-h-screen bg-slate-50 dark:bg-slate-900 text-slate-800 dark:text-slate-100 transition-colors duration-300 {% if admin %}admin-page{% endif %}">
    <!-- Loading 指示器 -->
    <div id='loading' class="fixed inset-0 z-50 hidden">
        <div class="absolute inset-0 flex items-center justify-center">
            <div class="loading"></div>
        </div>
    </div>

    <!-- 主题切换遮罩层 -->
    <div id="theme-transition-overlay" class="fixed inset-0 z-40 bg-white dark:bg-slate-900 opacity-0 pointer-events-none transition-opacity duration-100 hidden"></div>

    <!-- 顶部导航栏 -->
    {%include 'appbar.html'%}

    <!-- 为导航栏预留空间 -->
    <div style="height: calc(0.1rem + env(safe-area-inset-top));"></div>

    <!-- 主容器 - 智能检测侧边栏 -->
    {%if admin and isAdminPage != false%}
    <!-- Admin布局 - 带侧边栏 -->
    <div id='container' class="admin-layout-container py-6 min-h-[calc(100vh-14rem)]">
        <div class="admin-content-wrapper px-4 sm:px-6 lg:px-8 mx-auto max-w-screen-2xl">
    {%else%}
    <!-- 普通布局 -->
    <div id='container' class="container mx-auto px-2 sm:px-3 md:px-4 lg:px-6 py-6 min-h-[calc(100vh-14rem)] {%if fluid%}w-full{%else%}max-w-screen-2xl{%endif%}">
    {%endif%}
        {%block content%}{%endblock%}
    {%if admin and isAdminPage != false%}
        </div>
    {%endif%}
    </div>


    <!-- 页脚 -->
    <div class="w-full {%if admin and isAdminPage != false%}lg:pl-64{%endif%}" id="footer-wrapper">
        {%include "./footer.html"%}
    </div>

    <script>
        // 页面布局优化 - 简化版
        document.addEventListener('DOMContentLoaded', function() {
            // 仅处理导航栏偏移（admin页面）
            const sidebar = document.getElementById('admin-sidebar');
            const navbar = document.getElementById('main-navbar');
            
            if (sidebar && navbar) {
                // 确保admin页面有正确的body类
                if (window.innerWidth >= 1024 && !document.body.classList.contains('sidebar-collapsed') && !document.body.classList.contains('sidebar-expanded')) {
                    // 如果没有设置侧边栏状态类，默认为展开
                    document.body.classList.add('sidebar-expanded');
                }
            }
            
            // 检测是否有背景图
            const hasBackgroundImage = document.body.classList.contains('has-background-image');
            const footer = document.querySelector('footer');

            if (hasBackgroundImage && footer) {
                // 如果有背景图，增强footer的背景模糊效果
                footer.classList.add('backdrop-blur-md');
                footer.classList.remove('backdrop-blur-sm');
            }
        });
    </script>

    <script>
        // 设置最小高度
        var container = document.querySelector("#container");
        container.style.minHeight = window.innerHeight - document.body.clientHeight + container.clientHeight + 'px';
    </script>
    {%block js%}
    {%endblock%}

    <!-- 移动端添加到主屏幕引导按钮 -->
    <div id="pwa-install-button" class="hidden fixed bottom-4 right-4 sm:hidden z-50">
        <button class="flex items-center space-x-2 px-4 py-2 bg-primary-500 text-white rounded-full shadow-lg backdrop-blur-sm bg-opacity-90 hover:bg-opacity-100 transition-all">
            <img src="/img/logo.png" class="w-5 h-5" alt="Logo">
            <span class="text-sm">添加到主屏幕</span>
        </button>
    </div>

    <!-- 添加到主屏幕的引导弹窗 -->
    <div id="pwa-install-modal" class="hidden fixed inset-0 z-50">
        <div class="absolute inset-0 bg-black/60 backdrop-blur-sm"></div>
        <div class="absolute left-4 right-4 bottom-4 p-4 bg-gray-900 rounded-lg border border-gray-700/50">
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <img src="/img/logo.png" class="w-12 h-12 text-primary-500" alt="Logo">
                </div>
                <div class="flex-1">
                    <h3 class="text-lg font-medium text-white mb-1">添加到主屏幕</h3>
                    <p class="text-sm text-gray-300 mb-4">添加后可以快速访问节点状态</p>
                    <div class="text-xs text-gray-400 space-y-2">
                        <p>1. 点击浏览器底部分享按钮</p>
                        <p>2. 选择"添加到主屏幕"</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 flex justify-end space-x-3">
                <button onclick="dismissPWAGuide()" class="px-4 py-2 text-sm text-gray-400 hover:text-gray-300">稍后再说</button>
                <button onclick="showAddToHomeScreen()" class="px-4 py-2 text-sm bg-primary-500 text-white rounded-md hover:bg-primary-600">
                    立即添加
                </button>
            </div>
        </div>
    </div>

    <!-- PWA 相关脚本 -->
    <script>
    // 检测是否为支持的设备
    function isSupportedDevice() {
        // 检查是否为移动设备
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        // 检查是否为支持的浏览器
        const isSupportedBrowser = !/Edge|Firefox/i.test(navigator.userAgent);

        // 检查是否已经安装为 PWA
        const isAlreadyInstalled = window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone;

        return isMobile && isSupportedBrowser && !isAlreadyInstalled;
    }

    // 检测是否为 iOS 设备
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

    // 检测是否已经安装 PWA
    let deferredPrompt;
    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        if (isSupportedDevice()) {
            showPWAButton();
        }
    });

    // 显示安装按钮
    function showPWAButton() {
        const pwaButton = document.getElementById('pwa-install-button');
        if (pwaButton) {
            // 检查是否已经显示过引导
            const hasShownGuide = localStorage.getItem('pwa-guide-shown');
            if (!hasShownGuide) {
                pwaButton.classList.remove('hidden');
                // 添加点击事件
                pwaButton.addEventListener('click', showPWAInstallModal);
            }
        }
    }

    // 显示安装引导弹窗
    function showPWAInstallModal() {
        const modal = document.getElementById('pwa-install-modal');
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    // 关闭引导弹窗
    function dismissPWAGuide() {
        const modal = document.getElementById('pwa-install-modal');
        const button = document.getElementById('pwa-install-button');
        if (modal) {
            modal.classList.add('hidden');
        }
        if (button) {
            button.classList.add('hidden');
        }
        // 记录已经显示过引导
        localStorage.setItem('pwa-guide-shown', 'true');
    }

    // 显示添加到主屏幕的引导
    function showAddToHomeScreen() {
        if (deferredPrompt) {
            // Android 设备
            deferredPrompt.prompt();
            deferredPrompt.userChoice.then((choiceResult) => {
                if (choiceResult.outcome === 'accepted') {
                    dismissPWAGuide();
                }
                deferredPrompt = null;
            });
        } else if (isIOS) {
            // iOS 设备显示 Safari 添加到主屏幕的引导
            const modal = document.getElementById('pwa-install-modal');
            if (modal) {
                modal.querySelector('.text-xs').innerHTML = `
                    <p>1. 点击底部分享按钮 <svg class="inline-block w-5 h-5" viewBox="0 0 24 24" fill="currentColor"><path d="M16 5l-1.42 1.42-1.59-1.59V16h-1.98V4.83L9.42 6.42 8 5l4-4 4 4zm4 5v11c0 1.1-.9 2-2 2H6c-1.11 0-2-.9-2-2V10c0-1.11.89-2 2-2h3v2H6v11h12V10h-3V8h3c1.1 0 2 .89 2 2z"/></svg></p>
                    <p>2. 滚动并选择"添加到主屏幕" <svg class="inline-block w-5 h-5" viewBox="0 0 24 24" fill="currentColor"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/></svg></p>
                `;
            }
        }
    }

    // 检查是否是从主屏幕启动
    if (window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone) {
        // 已经安装为 PWA，不显示引导
        localStorage.setItem('pwa-guide-shown', 'true');
    } else {
        // 延迟显示引导按钮
        setTimeout(() => {
            if (isSupportedDevice()) {
                showPWAButton();
            }
        }, 3000);
    }
    </script>

    <!-- 统一主题管理器 -->
    <script src="/js/theme-manager.js"></script>
    
    <!-- 毛玻璃效果控制器 -->
    <script src="/js/glassmorphism-toggle.js"></script>
    
    <!-- 设置同步脚本 -->
    <script src="/js/settings-sync.js"></script>

    <script>
    // 全局通知管理
    window.notificationManager = {
        notifications: [],
        spacing: 16, // 通知之间的间距
        
        updatePositions() {
            let currentTop = 16;
            this.notifications.forEach(notification => {
                if (notification && notification.parentNode) {
                    notification.style.top = `${currentTop}px`;
                    currentTop += notification.offsetHeight + this.spacing;
                }
            });
        },
        
        add(notification) {
            this.notifications.push(notification);
            this.updatePositions();
        },
        
        remove(notification) {
            const index = this.notifications.indexOf(notification);
            if (index > -1) {
                this.notifications.splice(index, 1);
                this.updatePositions();
            }
        }
    };
    
    function notice(msg,type='info'){
        var div=document.createElement('div');
        
        // 根据主题和类型设置样式
        const isDark = document.documentElement.classList.contains('dark');
        let bgClass, iconClass, textClass, borderClass;
        
        switch(type) {
            case 'success':
                bgClass = isDark 
                    ? 'bg-green-900/90 backdrop-blur-md border-green-700/50' 
                    : 'bg-green-50/95 backdrop-blur-md border-green-200';
                iconClass = 'text-green-400';
                textClass = isDark ? 'text-green-100' : 'text-green-900';
                break;
            case 'error':
                bgClass = isDark 
                    ? 'bg-red-900/90 backdrop-blur-md border-red-700/50' 
                    : 'bg-red-50/95 backdrop-blur-md border-red-200';
                iconClass = 'text-red-400';
                textClass = isDark ? 'text-red-100' : 'text-red-900';
                break;
            case 'warning':
                bgClass = isDark 
                    ? 'bg-amber-900/90 backdrop-blur-md border-amber-700/50' 
                    : 'bg-amber-50/95 backdrop-blur-md border-amber-200';
                iconClass = 'text-amber-400';
                textClass = isDark ? 'text-amber-100' : 'text-amber-900';
                break;
            default: // info
                bgClass = isDark 
                    ? 'bg-slate-800/90 backdrop-blur-md border-slate-600/50' 
                    : 'bg-white/95 backdrop-blur-md border-slate-200';
                iconClass = 'text-indigo-500';
                textClass = isDark ? 'text-slate-100' : 'text-slate-900';
        }
        
        // 创建通知元素
        div.className=`notification-bubble fixed top-4 right-4 max-w-sm rounded-xl shadow-2xl z-50 transition-all duration-300 transform translate-x-full border ${bgClass}`;
        div.dataset.type = type;
        
        // 创建内容容器
        const contentDiv = document.createElement('div');
        contentDiv.className = 'flex items-start gap-3 px-4 py-3';
        
        // 添加图标
        const iconContainer = document.createElement('div');
        iconContainer.className = 'flex-shrink-0';
        const icon = document.createElement('i');
        // 使用Tabler Icons替代Material Icons
        const tablerIconClass = type === 'success' ? 'ti-circle-check' : 
                               type === 'error' ? 'ti-circle-x' : 
                               type === 'warning' ? 'ti-alert-triangle' :
                               'ti-info-circle';
        icon.className = `ti ${tablerIconClass} ${iconClass} text-lg`;
        iconContainer.appendChild(icon);
        
        // 添加消息文本
        const textDiv = document.createElement('div');
        textDiv.className = `flex-1 text-sm font-medium ${textClass}`;
        textDiv.textContent = msg;
        
        // 添加关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.className = `flex-shrink-0 ml-2 ${textClass} opacity-60 hover:opacity-100 transition-opacity`;
        closeBtn.innerHTML = '<i class="ti ti-x text-base"></i>';
        
        // 组装元素
        contentDiv.appendChild(iconContainer);
        contentDiv.appendChild(textDiv);
        contentDiv.appendChild(closeBtn);
        div.appendChild(contentDiv);
        
        // 添加到页面
        document.body.appendChild(div);
        
        // 添加到通知管理器
        window.notificationManager.add(div);
        
        // 动画显示
        requestAnimationFrame(() => {
            div.classList.remove('translate-x-full');
            div.classList.add('translate-x-0');
        });
        
        // 移除通知的函数
        const removeNotification = () => {
            div.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => {
                window.notificationManager.remove(div);
                div.remove();
            }, 300);
        };
        
        // 自动隐藏
        let hideTimeout = setTimeout(removeNotification, 5000);
        
        // 鼠标悬停时暂停自动隐藏
        div.onmouseenter = () => {
            clearTimeout(hideTimeout);
            hideTimeout = null;
        };
        div.onmouseleave = () => {
            if (!hideTimeout) {
                hideTimeout = setTimeout(removeNotification, 1000);
            }
        };
        
        // 设置关闭按钮事件
        closeBtn.onclick = removeNotification;
    }
    
    // 更新通知主题的辅助函数
    function updateNotificationTheme(notification, type, isDark) {
        // 移除旧的样式类
        const classesToRemove = Array.from(notification.classList).filter(cls => 
            cls.includes('bg-') || cls.includes('border-') || cls.includes('backdrop-')
        );
        classesToRemove.forEach(cls => notification.classList.remove(cls));
        
        // 根据类型和主题添加新样式
        let bgClass;
        switch(type) {
            case 'success':
                bgClass = isDark 
                    ? 'bg-green-900/90 backdrop-blur-md border-green-700/50' 
                    : 'bg-green-50/95 backdrop-blur-md border-green-200';
                break;
            case 'error':
                bgClass = isDark 
                    ? 'bg-red-900/90 backdrop-blur-md border-red-700/50' 
                    : 'bg-red-50/95 backdrop-blur-md border-red-200';
                break;
            case 'warning':
                bgClass = isDark 
                    ? 'bg-amber-900/90 backdrop-blur-md border-amber-700/50' 
                    : 'bg-amber-50/95 backdrop-blur-md border-amber-200';
                break;
            default:
                bgClass = isDark 
                    ? 'bg-slate-800/90 backdrop-blur-md border-slate-600/50' 
                    : 'bg-white/95 backdrop-blur-md border-slate-200';
        }
        
        bgClass.split(' ').forEach(cls => notification.classList.add(cls));
        
        // 更新文本颜色
        const textElements = notification.querySelectorAll('[class*="text-"]');
        textElements.forEach(el => {
            const classesToRemove = Array.from(el.classList).filter(cls => cls.includes('text-'));
            classesToRemove.forEach(cls => el.classList.remove(cls));
            
            // 添加新的文本颜色
            let textClass;
            switch(type) {
                case 'success':
                    textClass = isDark ? 'text-green-100' : 'text-green-900';
                    break;
                case 'error':
                    textClass = isDark ? 'text-red-100' : 'text-red-900';
                    break;
                case 'warning':
                    textClass = isDark ? 'text-amber-100' : 'text-amber-900';
                    break;
                default:
                    textClass = isDark ? 'text-slate-100' : 'text-slate-900';
            }
            el.classList.add(textClass);
        });
    }
    function startloading(){
        // 先检查是否已经存在loading元素，如果存在则不重复创建
        if (document.querySelector('.loading-indicator')) {
            return;
        }
        var div=document.createElement('div');
        div.className='loading-indicator fixed top-0 right-0 p-4 z-50 flex items-center justify-center transition-opacity duration-300 opacity-0';
        div.innerHTML=`<div class="w-8 h-8 border-3 border-primary-500 border-t-transparent rounded-full animate-spin"></div>`;
        document.body.appendChild(div);
        // 使用setTimeout以确保过渡效果生效
        setTimeout(() => {
            div.classList.add('opacity-100');
        }, 10);
    }
    function endloading(){
        const loadingEl = document.querySelector('.loading-indicator');
        if (loadingEl) {
            // 添加淡出效果
            loadingEl.classList.remove('opacity-100');
            loadingEl.classList.add('opacity-0');
            // 等待过渡完成后再移除元素
            setTimeout(() => {
                loadingEl.remove();
            }, 300);
        }

        // 为兼容性，也尝试清除旧的loading类元素
        document.querySelector('.loading')?.remove();
    }
    async function postjson(url,data){
        startloading();
        try{
            // 增强JSON序列化错误处理
            let jsonBody;
            try {
                jsonBody = JSON.stringify(data);
            } catch (jsonError) {
                console.error('JSON序列化失败:', jsonError);
                console.error('尝试序列化的数据:', data);

                // 检查是否包含SSH私钥等敏感数据
                if (data && typeof data === 'object') {
                    const dataStr = JSON.stringify(data, (key, value) => {
                        if (key === 'privateKey' && typeof value === 'string' && value.length > 100) {
                            return '[SSH私钥内容已隐藏]';
                        }
                        if (key === 'password' && typeof value === 'string') {
                            return '[密码已隐藏]';
                        }
                        return value;
                    });
                    console.error('数据结构（敏感信息已隐藏）:', dataStr);
                }

                notice('数据格式错误，请检查输入的内容是否包含特殊字符', 'error');
                throw new Error('JSON序列化失败: ' + jsonError.message);
            }

            var res=await fetch(url,{
                method:'POST',
                headers:{'Content-Type':'application/json'},
                body:jsonBody
            });

            if (!res.ok) {
                const errorText = await res.text();
                console.error('HTTP请求失败:', res.status, res.statusText, errorText);
                throw new Error(`HTTP ${res.status}: ${res.statusText}`);
            }

            return await res.json();
        }catch(e){
            console.error('postjson错误详情:', e);
            notice(e.message,'error');
            throw e;
        }finally{
            endloading();
        }
    }
    function V(id){
        return document.getElementById(id)?.value;
    }
    </script>
    <script>
    // 统一监听主题切换，确保所有页面元素同步刷新
    document.addEventListener('theme:changed', function(e) {
      if (window.themeManager && typeof window.themeManager.updateThemeElements === 'function') {
        window.themeManager.updateThemeElements(e.detail.isDark);
      }
      
      // 更新现有通知的主题
      document.querySelectorAll('.notification-bubble').forEach(notification => {
        const type = notification.dataset.type || 'info';
        updateNotificationTheme(notification, type, e.detail.isDark);
      });
    });
    
    // 个性化设置初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 减少延迟以提升首屏体验（从200ms减少到50ms）
        setTimeout(() => {
            if (window.settingsSync && typeof window.settingsSync.loadAndApplySettings === 'function') {
                window.settingsSync.loadAndApplySettings();
                console.log('个性化设置已初始化');
            }
        }, 50);
    });
    </script>

    <!-- 标签页生命周期处理脚本 -->
    <script src="/js/tab-lifecycle-handler.js"></script>
    
    <!-- 功能墙脚本 -->
    <script src="/js/feature-wall.js"></script>

</body>
</html>
