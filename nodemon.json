{"verbose": true, "watch": ["dstatus.js", "modules/**/*.js", "database/**/*.js", "views/**/*.html", "views/**/*.njk", "static/js/**/*.js"], "ext": "js,html,njk,nunjucks", "ignore": ["node_modules/**/*", ".git/**/*", "data/**/*", "logs/**/*", "**/*.log", "**/*.db", "**/*.db-wal", "**/*.db-shm", "**/*.db-journal", "static/css/**/*", "static/img/**/*", "static/fonts/**/*", "static/svg/**/*", "static/bgsvg/**/*", "static/webfonts/**/*", "static/sh/**/*", "static/*.html", "static/*.txt", "static/manifest.json", "dist/**/*", "backup/**/*", "*.md", "package*.json", ".env*", "**/tokens.json", "scripts/**/*", "docs/**/*", "plan/**/*", ".database/**/*", "license-server/**/*", "**/*.tmp", "**/*.temp", "**/*.cache", "**/*.backup*", "**/*.bak", ".vscode/**/*", ".idea/**/*", "**/.DS_Store"], "env": {"NODE_ENV": "development"}, "delay": "1000", "legacyWatch": false, "events": {"restart": "echo '[Nodemon] App restarted due to file change'"}}