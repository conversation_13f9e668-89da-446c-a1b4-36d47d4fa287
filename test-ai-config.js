#!/usr/bin/env node
"use strict";

// 测试AI配置系统
const aiConfig = require('./modules/utils/ai-config');

async function testAIConfig() {
  console.log('===== 测试 AI 配置系统 =====\n');
  
  // 模拟数据库对象
  const mockDb = {
    get(table, field) {
      console.log(`[MockDB] 获取 ${table}.${field}`);
      return {
        ai: {
          enabled: true,
          name: 'Google Gemini',
          apiBase: 'https://generativelanguage.googleapis.com/v1beta/openai',
          apiKey: 'test-key-12345',
          model: 'gemini-1.5-flash'
        }
      };
    },
    set(table, field, value) {
      console.log(`[MockDB] 设置 ${table}.${field}:`, value);
      return true;
    }
  };
  
  try {
    // 测试1: 获取AI配置
    console.log('1. 测试获取AI配置...');
    const config = await aiConfig.getAiConfig(mockDb);
    console.log('配置内容:', JSON.stringify(config, null, 2));
    console.log('✅ 获取配置成功\n');
    
    // 测试2: 保存AI配置
    console.log('2. 测试保存AI配置...');
    const newConfig = {
      enabled: true,
      name: 'OpenAI',
      apiBase: 'https://api.openai.com/v1',
      apiKey: 'sk-test-new',
      model: 'gpt-4'
    };
    const saveResult = await aiConfig.saveAiConfig(mockDb, newConfig);
    console.log('保存结果:', saveResult);
    console.log('✅ 保存配置成功\n');
    
    // 测试3: 测试连接（模拟）
    console.log('3. 测试AI连接...');
    console.log('注意: 这会实际调用API，可能失败（如果没有有效的API密钥）');
    const testResult = await aiConfig.testAiConnection(mockDb);
    console.log('测试结果:', testResult);
    
    console.log('\n===== 测试完成 =====');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testAIConfig();