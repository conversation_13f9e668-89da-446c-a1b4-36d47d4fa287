name: ARM Build Test

permissions:
  contents: write

on:
  workflow_dispatch:

jobs:
  build-arm:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js 20
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        
    - name: Install dependencies
      run: npm install --legacy-peer-deps

    - name: Install build tools
      run: |
        npm install -g terser
        sudo apt-get update && sudo apt-get install -y jq
        # 安装 ARM64 交叉编译工具链
        sudo apt-get install -y gcc-aarch64-linux-gnu g++-aarch64-linux-gnu

    - name: Prepare ARM64 native modules
      run: |
        chmod +x arm-beta/scripts/*.sh
        ./arm-beta/scripts/prepare-native-modules.sh arm64 linux

    - name: Build ARM64 binary
      run: ./arm-beta/scripts/build-arm.sh "${{ github.run_number }}"
        
    - name: Package ARM64 build
      run: ./arm-beta/scripts/package-arm.sh "${{ github.run_number }}"

    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: arm64-build-artifacts
        path: |
          dstatus-linux-arm64
          dstatus-arm64-beta-*.tar.gz
        retention-days: 7

    - name: Create test release
      uses: ncipollo/release-action@v1
      with:
        artifacts: "dstatus-linux-arm64,dstatus-arm64-beta-*.tar.gz"
        tag: "arm-beta-${{ github.run_number }}"
        name: "ARM64 内部测试版本 #${{ github.run_number }}"
        body: |
          ## 🧪 ARM64 内部测试版本

          **⚠️ 内部测试版本 - 不建议生产使用**

          ### 版本信息
          - 构建号：#${{ github.run_number }}
          - 架构：Linux ARM64 (aarch64)
          - 类型：Beta 测试版本
          - 构建时间：${{ github.event.head_commit.timestamp }}

          ### 文件说明
          - `dstatus-linux-arm64` - ARM64 二进制文件（测试版）
          - `dstatus-arm64-beta-*.tar.gz` - 完整部署包（测试版）

          ### 测试目标
          - 验证 ARM64 架构兼容性
          - 测试原生模块功能
          - 性能基准测试

          ### 已知限制
          - 仅在 ARM64 Linux 系统上运行
          - 部分功能可能不稳定
          - 性能未经优化

          ### 测试方法
          在 ARM64 Linux 系统上运行：
          ```bash
          tar -xzf dstatus-arm64-beta-*.tar.gz
          cd dstatus-arm64-complete
          ./start.sh
          ```

          ### 反馈渠道
          发现问题请通过 Issue 反馈，标注 `[ARM-BETA]`
        allowUpdates: true
        makeLatest: false
        prerelease: true

    - name: Upload ARM files to down.vps.mom
      env:
        DSTATUS_API_KEY: ${{ secrets.DSTATUS_API_KEY }}
      run: |
        echo "📤 上传 ARM 文件到 down.vps.mom/downloads/arm/..."
        
        # 检查 API 密钥
        if [ -z "$DSTATUS_API_KEY" ]; then
          echo "❌ DSTATUS_API_KEY 未设置，跳过上传"
          exit 0
        fi
        
        # 安装依赖
        sudo apt-get update -qq && sudo apt-get install -y jq curl
        
        # 上传文件函数
        upload_file() {
          local file_path="$1"
          local target_name="$2"
          
          if [ ! -f "$file_path" ]; then
            echo "❌ 文件不存在: $file_path"
            return 1
          fi
          
          echo "📤 上传: $file_path -> $target_name"
          
          local response=$(curl -s -X POST \
            -H "X-API-Key: ${DSTATUS_API_KEY}" \
            -F "file=@${file_path}" \
            -F "target_path=${target_name}" \
            -F "overwrite=true" \
            "https://down.vps.mom/upload-to-vps-v2.php")
          
          if echo "$response" | jq -e '.success' >/dev/null 2>&1; then
            echo "✅ 上传成功: https://down.vps.mom/$target_name"
            return 0
          else
            echo "❌ 上传失败: $(echo "$response" | jq -r '.error // "Unknown"')"
            return 1
          fi
        }
        
        # 上传构建的压缩包
        TARBALL=$(ls dstatus-arm64-beta-*.tar.gz | head -n1)
        if [ -n "$TARBALL" ]; then
          if upload_file "$TARBALL" "downloads/arm/dstatus-arm64.tar.gz"; then
            echo "✅ ARM64 压缩包上传成功"
          else
            echo "⚠️ ARM64 压缩包上传失败"
          fi
        else
          echo "⚠️ 未找到构建的压缩包"
        fi
        
        # 上传简化安装脚本
        if [ -f "scripts/install-arm64-simple.sh" ]; then
          if upload_file "scripts/install-arm64-simple.sh" "downloads/arm/install-arm64-simple.sh"; then
            echo "✅ 简化安装脚本上传成功"
          else
            echo "⚠️ 简化安装脚本上传失败"
          fi
        else
          echo "⚠️ 未找到 scripts/install-arm64-simple.sh"
        fi
        
        # 上传原始 beta 脚本（保持兼容）
        if upload_file "arm-beta/scripts/install-arm-beta.sh" "downloads/install-arm-beta.sh"; then
          echo "✅ Beta 安装脚本上传成功"
        else
          echo "⚠️ Beta 安装脚本上传失败"
        fi
        
        echo ""
        echo "📋 安装说明："
        echo "# 简化版本（推荐）："
        echo "curl -fsSL https://down.vps.mom/downloads/arm/install-arm64-simple.sh | sudo bash"
        echo ""
        echo "# Beta 版本："
        echo "curl -fsSL https://down.vps.mom/downloads/install-arm-beta.sh | sudo bash"