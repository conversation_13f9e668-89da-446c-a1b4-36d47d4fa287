name: Beta Build

permissions:
  contents: write

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        # 确保获取最新的代码，禁用shallow clone
        fetch-depth: 0
        # 清理工作区，确保没有旧文件
        clean: true
        
    - name: Verify latest commit
      run: |
        echo "🔍 验证最新提交..."
        echo "当前分支: $(git branch --show-current)"
        echo "最新提交: $(git log --oneline -1)"
        echo "提交时间: $(git log -1 --format=%cd)"
        echo "提交作者: $(git log -1 --format=%an)"
        
        # 检查是否包含最新的许可证修复
        if git log --oneline -10 | grep -E "(许可证|license|binding|force-transfer)"; then
          echo "✅ 找到许可证相关修复"
        else
          echo "⚠️ 警告：可能未包含最新的许可证修复"
        fi
      
    - name: Setup Node.js 20
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        # 清理npm缓存
        cache: ''
        
    - name: Clean npm cache
      run: |
        echo "🧹 清理npm缓存..."
        npm cache clean --force
        rm -rf node_modules package-lock.json
        
    - name: Install dependencies
      run: |
        echo "📦 安装依赖..."
        npm install --legacy-peer-deps --no-cache

    - name: Install frontend minification tools
      run: npm install -g terser
      
    - name: Verify critical files
      run: |
        echo "🔍 验证关键文件内容..."
        
        # 检查bindingManager.js文件
        echo "检查 bindingManager.js..."
        if [ -f modules/license-enhanced/bindingManager.js ]; then
          echo "文件存在，检查关键修复..."
          # 检查是否包含当前的force-transfer实现
          if grep -q "统一使用force-transfer确保成功" modules/license-enhanced/bindingManager.js; then
            echo "✅ bindingManager.js包含最新修复（force-transfer统一实现）"
          else
            echo "❌ 错误：bindingManager.js缺少最新修复"
            exit 1
          fi
          
          # 检查API端点是否正确
          if grep -q "/api/license/force-transfer" modules/license-enhanced/bindingManager.js; then
            echo "✅ bindingManager.js包含正确的API端点"
          else
            echo "⚠️ 警告：bindingManager.js可能缺少正确的API端点"
          fi
        else
          echo "❌ 错误：找不到bindingManager.js文件"
          exit 1
        fi
        
        # 显示文件的最后修改时间
        echo ""
        echo "文件最后修改时间："
        ls -la modules/license-enhanced/bindingManager.js
        
    - name: Inject version information
      run: |
        # 生成版本号并注入到项目中
        echo "📝 注入版本信息..."
        node scripts/inject-version.js
        
        # 显示生成的版本信息
        if [ -f version.txt ]; then
          echo "✅ 生成的版本号: $(cat version.txt)"
        fi
        
    - name: Clean build cache
      run: |
        echo "🧹 清理构建缓存..."
        # 清理pkg缓存
        rm -rf ~/.pkg-cache
        # 清理其他可能的缓存
        rm -rf .cache
        rm -rf dist
        rm -rf build
      
    - name: Build Linux x64 binary
      run: |
        export NODE_ENV=production
        # 强制重新构建，不使用缓存
        npx @yao-pkg/pkg . --target node20-linux-x64 --output dstatus-linux-x64 --no-bytecode --public
        ls -lh dstatus-linux-x64
        
        # 验证二进制文件大小（应该大于50MB）
        FILE_SIZE=$(stat -c%s dstatus-linux-x64)
        if [ $FILE_SIZE -lt 52428800 ]; then
          echo "⚠️ 警告：二进制文件大小异常 ($(($FILE_SIZE / 1024 / 1024))MB)"
        else
          echo "✅ 二进制文件大小正常: $(($FILE_SIZE / 1024 / 1024))MB"
        fi
        
    - name: Test binary (basic check)
      run: |
        # 验证二进制文件存在
        if [ -f ./dstatus-linux-x64 ]; then
          echo "✓ Binary file exists"
          ls -lh ./dstatus-linux-x64
        else
          echo "✗ Binary file not found"
          exit 1
        fi
        
        # 创建所有需要的临时目录
        mkdir -p /tmp/dstatus-logs /tmp/offline-queue /tmp/dstatus-data
        
        # 设置所有环境变量
        export DB_PATH=/tmp/dstatus-data
        export DSTATUS_LOG_DIR=/tmp/dstatus-logs
        export DSTATUS_QUEUE_DIR=/tmp/offline-queue
        export TOKENS_PATH=/tmp/dstatus-tokens.json
        
        # 在后台启动服务器，等待5秒后杀死进程
        echo "Starting binary in background for quick test..."
        timeout 5s ./dstatus-linux-x64 || true
        
        echo "✓ Binary test completed successfully"
        
    - name: Verify binary contains latest code
      run: |
        echo "🔍 验证二进制文件包含最新代码..."
        
        # 使用strings命令检查二进制文件中是否包含关键字符串
        # 注意：这种方法不是100%可靠，但可以提供基本验证
        
        echo "检查二进制文件中的代码特征..."
        
        # 检查是否包含force-transfer相关代码
        if strings dstatus-linux-x64 | grep -q "force-transfer"; then
          echo "✅ 找到force-transfer相关代码"
        else
          echo "⚠️ 警告：未找到force-transfer相关代码"
        fi
        
        # 检查是否包含许可证管理相关代码
        if strings dstatus-linux-x64 | grep -q "license-enhanced"; then
          echo "✅ 找到许可证管理相关代码"
        else
          echo "⚠️ 警告：未找到许可证管理相关代码"
        fi
        
        # 显示二进制文件信息
        echo ""
        echo "二进制文件信息："
        file dstatus-linux-x64
        echo "MD5: $(md5sum dstatus-linux-x64 | cut -d' ' -f1)"
        echo "SHA256: $(sha256sum dstatus-linux-x64 | cut -d' ' -f1)"

    - name: Create complete deployment package
      run: |
        echo "📦 创建完整部署包..."

        # 创建部署目录
        DEPLOY_DIR="dstatus-complete"
        rm -rf "${DEPLOY_DIR}"
        mkdir -p "${DEPLOY_DIR}"

        # 复制二进制文件
        cp dstatus-linux-x64 "${DEPLOY_DIR}/"
        chmod +x "${DEPLOY_DIR}/dstatus-linux-x64"

        # 复制前端文件和安全的配置文件
        cp -r static "${DEPLOY_DIR}/"
        cp -r views "${DEPLOY_DIR}/"
        
        # 只复制配置模板，不复制实际配置文件
        mkdir -p "${DEPLOY_DIR}/config"
        if [ -f config/database.json.template ]; then
          cp config/database.json.template "${DEPLOY_DIR}/config/"
        fi

        # 混淆前端JavaScript
        echo "🔐 混淆前端代码..."
        find "${DEPLOY_DIR}/static/js" -name "*.js" -type f | while read file; do
            if [[ ! "$file" =~ \.min\.js$ ]]; then
                terser '$file' --compress --mangle -o '$file' 2>/dev/null || true
                echo "  ✓ 混淆: $(basename '$file')"
            fi
        done

        # 创建数据目录结构
        mkdir -p "${DEPLOY_DIR}/data/temp" "${DEPLOY_DIR}/data/backups"

        # 创建启动脚本
        cat > "${DEPLOY_DIR}/start.sh" << 'EOF'
        #!/bin/bash
        cd "$(dirname "$0")"
        echo "🚀 启动 DStatus Beta..."
        echo "🌐 Web 界面: http://localhost:5556"
        echo "⚙️ 管理面板：http://localhost:5556/login"
        echo "⚠️  这是测试版本，仅供内部测试使用"
        echo ""
        ./dstatus-linux-x64 --port=5556
        EOF
        chmod +x "${DEPLOY_DIR}/start.sh"

        # 创建停止脚本
        cat > "${DEPLOY_DIR}/stop.sh" << 'EOF'
        #!/bin/bash
        echo "🛑 停止 DStatus Beta..."
        pkill -f 'dstatus-linux-x64' || echo '没有运行的实例'
        echo "✅ 已停止"
        EOF
        chmod +x "${DEPLOY_DIR}/stop.sh"

        # 创建README
        cat > "${DEPLOY_DIR}/README.md" << 'EOF'
        # DStatus Beta 测试版本

        ⚠️ **注意：这是测试版本，仅供内部测试使用！**

        ## 快速启动
        ```bash
        ./start.sh
        ```

        ## 停止服务
        ```bash
        ./stop.sh
        ```

        ## 访问地址
        - Web界面: http://localhost:5556
        - 管理面板：http://localhost:5556/login

        ## 包含内容
        - ✅ 二进制文件 (dstatus-linux-x64)
        - ✅ 前端文件 (已混淆压缩)
        - ✅ 视图模板
        - ✅ 启动/停止脚本
        - ✅ 数据目录

        ## 系统要求
        - Linux x64 系统
        - 无需安装 Node.js (已打包)

        ## 构建信息
        - 构建时间: $(date)
        - 版本: v$(date +%y.%m.${{ github.run_number }})-beta
        - License Server: https://dstatus_api.vps.mom
        - 默认端口: 5556 (避免与生产版本冲突)
        EOF

        # 创建Beta版本压缩包
        VERSION=$(date +%y.%m.${{ github.run_number }})
        tar -czf dstatus-complete-v${VERSION}-beta.tar.gz "${DEPLOY_DIR}"
        echo "✅ Beta 完整部署包创建完成！"

    - name: Create Docker deployment package
      run: |
        echo "🐳 创建 Docker 部署包..."

        # 运行 Docker 部署包构建脚本
        VERSION=$(date +%y.%m.${{ github.run_number }})
        ./scripts/build-docker-package.sh v${VERSION}-beta

        # 创建Beta版本Docker包
        mv dist-docker/dstatus-docker.tar.gz dstatus-docker-v${VERSION}-beta.tar.gz
        echo "✅ Beta Docker 部署包创建完成！"

    - name: Run beta release script
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        DSTATUS_API_KEY: ${{ secrets.DSTATUS_API_KEY }}
      run: |
        # 安装依赖
        sudo apt-get update && sudo apt-get install -y jq
        
        # 运行Beta发布脚本
        ./scripts/build-release.sh \
          "beta" \
          "${{ github.run_number }}" \
          "${{ secrets.GITHUB_TOKEN }}" \
          "${{ secrets.DSTATUS_API_KEY }}"