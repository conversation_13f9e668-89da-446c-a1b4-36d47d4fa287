name: Production Build

permissions:
  contents: write

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js 20
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        
    - name: Install dependencies
      run: npm install --legacy-peer-deps

    - name: Install frontend minification tools
      run: npm install -g terser
      
    - name: Inject version information
      run: |
        # 生成版本号并注入到项目中
        echo "📝 注入版本信息..."
        node scripts/inject-version.js
        
        # 显示生成的版本信息
        if [ -f version.txt ]; then
          echo "✅ 生成的版本号: $(cat version.txt)"
        fi
      
    - name: Build Linux x64 binary
      run: |
        export NODE_ENV=production
        npx @yao-pkg/pkg . --target node20-linux-x64 --output dstatus-linux-x64
        ls -lh dstatus-linux-x64
        
    - name: Test binary (basic check)
      run: |
        # 验证二进制文件存在
        if [ -f ./dstatus-linux-x64 ]; then
          echo "✓ Binary file exists"
          ls -lh ./dstatus-linux-x64
        else
          echo "✗ Binary file not found"
          exit 1
        fi
        
        # 创建所有需要的临时目录
        mkdir -p /tmp/dstatus-logs /tmp/offline-queue /tmp/dstatus-data
        
        # 设置所有环境变量
        export DB_PATH=/tmp/dstatus-data
        export DSTATUS_LOG_DIR=/tmp/dstatus-logs
        export DSTATUS_QUEUE_DIR=/tmp/offline-queue
        export TOKENS_PATH=/tmp/dstatus-tokens.json
        
        # 在后台启动服务器，等待5秒后杀死进程
        echo "Starting binary in background for quick test..."
        timeout 5s ./dstatus-linux-x64 || true
        
        echo "✓ Binary test completed successfully"

    - name: Create complete deployment package
      run: |
        echo "📦 创建完整部署包..."

        # 创建部署目录
        DEPLOY_DIR="dstatus-complete"
        rm -rf "${DEPLOY_DIR}"
        mkdir -p "${DEPLOY_DIR}"

        # 复制二进制文件
        cp dstatus-linux-x64 "${DEPLOY_DIR}/"
        chmod +x "${DEPLOY_DIR}/dstatus-linux-x64"

        # 复制前端文件
        cp -r static "${DEPLOY_DIR}/"
        cp -r views "${DEPLOY_DIR}/"

        # 混淆前端JavaScript
        echo "🔐 混淆前端代码..."
        find "${DEPLOY_DIR}/static/js" -name "*.js" -type f | while read file; do
            if [[ ! "$file" =~ \.min\.js$ ]]; then
                terser "$file" --compress --mangle -o "$file" 2>/dev/null || true
                echo "  ✓ 混淆: $(basename "$file")"
            fi
        done

        # 创建数据目录
        mkdir -p "${DEPLOY_DIR}/data/temp"

        # 创建启动脚本
        {
          echo "#!/bin/bash"
          echo "cd \"\$(dirname \"\$0\")\""
          echo "echo \"🚀 启动 DStatus...\""
          echo "echo \"🌐 Web 界面: http://localhost:5555\""
          echo "echo \"⚙️ 管理面板：http://localhost:5555/login\""
          echo "echo \"\""
          echo "./dstatus-linux-x64"
        } > "${DEPLOY_DIR}/start.sh"
        chmod +x "${DEPLOY_DIR}/start.sh"

        # 创建停止脚本
        {
          echo "#!/bin/bash"
          echo "echo \"🛑 停止 DStatus...\""
          echo "pkill -f 'dstatus-linux-x64' || echo '没有运行的实例'"
          echo "echo \"✅ 已停止\""
        } > "${DEPLOY_DIR}/stop.sh"
        chmod +x "${DEPLOY_DIR}/stop.sh"

        # 创建README
        {
          echo "# DStatus 生产部署包"
          echo ""
          echo "## 快速启动"
          echo "\`\`\`bash"
          echo "./start.sh"
          echo "\`\`\`"
          echo ""
          echo "## 停止服务"
          echo "\`\`\`bash"
          echo "./stop.sh"
          echo "\`\`\`"
          echo ""
          echo "## 访问地址"
          echo "- Web界面: http://localhost:5555"
          echo "- 管理面板：http://localhost:5555/login"
          echo ""
          echo "## 包含内容"
          echo "- ✅ 二进制文件 (dstatus-linux-x64)"
          echo "- ✅ 前端文件 (已混淆压缩)"
          echo "- ✅ 视图模板"
          echo "- ✅ 启动/停止脚本"
          echo "- ✅ 数据目录"
          echo ""
          echo "## 系统要求"
          echo "- Linux x64 系统"
          echo "- 无需安装 Node.js (已打包)"
          echo ""
          echo "## 构建信息"
          echo "- 构建时间: \$(date)"
          echo "- 版本: v$(date +%y.%m.${{ github.run_number }})"
          echo "- License Server: https://dstatus_api.vps.mom"
        } > "${DEPLOY_DIR}/README.md"

        # 创建生产版本压缩包
        VERSION=$(date +%y.%m.${{ github.run_number }})
        tar -czf "dstatus-complete-v${VERSION}.tar.gz" "${DEPLOY_DIR}"
        echo "✅ 生产部署包创建完成！"

    - name: Create Docker deployment package
      run: |
        echo "🐳 创建 Docker 部署包..."

        # 运行 Docker 部署包构建脚本
        VERSION=$(date +%y.%m.${{ github.run_number }})
        ./scripts/build-docker-package.sh "v${VERSION}"

        # 创建生产版本Docker包
        mv dist-docker/dstatus-docker.tar.gz "dstatus-docker-v${VERSION}.tar.gz"
        echo "✅ 生产 Docker 部署包创建完成！"

    - name: Run production release script
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        DSTATUS_API_KEY: ${{ secrets.DSTATUS_API_KEY }}
      run: |
        # 安装依赖
        sudo apt-get update && sudo apt-get install -y jq
        
        # 运行生产发布脚本
        ./scripts/build-release.sh \
          "stable" \
          "${{ github.run_number }}" \
          "${{ secrets.GITHUB_TOKEN }}" \
          "${{ secrets.DSTATUS_API_KEY }}"
