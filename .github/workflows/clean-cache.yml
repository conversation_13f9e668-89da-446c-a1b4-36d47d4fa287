name: Clean Build Cache

on:
  workflow_dispatch:
  schedule:
    # 每周清理一次缓存
    - cron: '0 0 * * 0'

jobs:
  clean-cache:
    runs-on: ubuntu-latest
    permissions:
      actions: write
      
    steps:
    - name: Cleanup caches
      run: |
        echo "🧹 清理GitHub Actions缓存..."
        
    - name: Cleanup caches by branch
      uses: actions/github-script@v7
      with:
        script: |
          const owner = context.repo.owner;
          const repo = context.repo.repo;
          
          console.log("清理所有分支的缓存...");
          
          // 获取所有缓存
          const caches = await github.rest.actions.getActionsCacheList({
            owner,
            repo,
          });
          
          // 删除所有缓存
          for (const cache of caches.data.actions_caches) {
            console.log(`删除缓存: ${cache.key}`);
            try {
              await github.rest.actions.deleteActionsCacheById({
                owner,
                repo,
                cache_id: cache.id,
              });
            } catch (error) {
              console.log(`删除缓存失败: ${error.message}`);
            }
          }
          
          console.log("✅ 缓存清理完成");