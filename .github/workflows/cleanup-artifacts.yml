name: Cleanup Artifacts

on:
  schedule:
    # 每天凌晨 2 点运行
    - cron: '0 2 * * *'
  workflow_dispatch:  # 允许手动触发

jobs:
  cleanup:
    runs-on: ubuntu-latest
    permissions:
      actions: write
      
    steps:
    - name: Delete old artifacts
      uses: actions/github-script@v7
      with:
        script: |
          const owner = context.repo.owner;
          const repo = context.repo.repo;
          
          // 获取所有 artifacts
          const artifacts = await github.rest.actions.listArtifactsForRepo({
            owner,
            repo,
            per_page: 100
          });
          
          // 删除超过 1 天的 artifacts
          const oneDay = 24 * 60 * 60 * 1000;
          const now = Date.now();
          
          for (const artifact of artifacts.data.artifacts) {
            const createdAt = new Date(artifact.created_at).getTime();
            const age = now - createdAt;
            
            if (age > oneDay) {
              console.log(`Deleting artifact: ${artifact.name} (${artifact.id})`);
              await github.rest.actions.deleteArtifact({
                owner,
                repo,
                artifact_id: artifact.id
              });
            }
          }
          
          console.log('Cleanup completed');