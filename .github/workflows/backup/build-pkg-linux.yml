name: Build PKG Linux Binary

on:
  push:
    branches: [ main, master, feature/* ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-22.04  # 使用 Ubuntu 22.04 LTS
    permissions:
      contents: write
      actions: read
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential python3
    
    - name: Make build script executable
      run: |
        chmod +x build-pkg-github.sh
    
    - name: Run PKG build
      run: |
        ./build-pkg-github.sh
    
    - name: Upload artifact (Optional)
      uses: actions/upload-artifact@v4
      continue-on-error: true  # 即使失败也继续
      with:
        name: dstatus-linux-x64-pkg
        path: dstatus-linux-x64-pkg.tar.gz
        retention-days: 1  # 减少保留天数
    
    - name: Push to releases branch
      run: |
        # 保存构建产物
        cp dstatus-linux-x64-pkg.tar.gz /tmp/
        
        # 配置 git
        git config --global user.name "GitHub Actions"
        git config --global user.email "<EMAIL>"
        
        # 获取所有分支
        git fetch origin
        
        # 创建或切换到 releases-pkg 分支
        git checkout -B releases-pkg origin/releases-pkg || git checkout -B releases-pkg
        
        # 清理分支
        git rm -rf . || true
        git clean -fdx
        
        # 恢复构建产物
        cp /tmp/dstatus-linux-x64-pkg.tar.gz ./
        
        # 创建 README
        cat > README.md << 'EOF'
        # DStatus PKG Linux Binaries
        
        这个分支包含使用 pkg 打包的 Linux 二进制文件。
        
        ## 最新版本
        - 文件: `dstatus-linux-x64-pkg.tar.gz`
        - 构建时间: $(date)
        - 构建编号: ${{ github.run_number }}
        - 构建方式: pkg (Node.js 单文件二进制)
        
        ## 特点
        - 单个可执行文件，无需 Node.js 环境
        - 内置 License Server URL
        - 包含所有静态资源
        - 约 55MB 大小
        
        ## 下载方法
        ```bash
        wget https://github.com/${{ github.repository }}/raw/releases-pkg/dstatus-linux-x64-pkg.tar.gz
        ```
        
        ## 部署方法
        1. 解压: `tar -xzf dstatus-linux-x64-pkg.tar.gz`
        2. 赋权: `chmod +x start.sh`
        3. 运行: `./start.sh`
        
        ## 访问地址
        - Web界面: http://localhost:5555
        - 管理面板：http://localhost:5555/login
        EOF
        
        # 创建安装脚本
        cat > install.sh << 'EOF'
        #!/bin/bash
        echo "🚀 DStatus PKG 版本安装脚本"
        echo "=========================="
        
        # 下载
        echo "📥 下载最新版本..."
        wget -O dstatus-linux-x64-pkg.tar.gz https://github.com/${{ github.repository }}/raw/releases-pkg/dstatus-linux-x64-pkg.tar.gz
        
        # 解压
        echo "📦 解压文件..."
        tar -xzf dstatus-linux-x64-pkg.tar.gz
        
        # 赋权
        echo "🔧 设置权限..."
        chmod +x start.sh
        chmod +x dstatus-linux-x64
        
        echo "✅ 安装完成！"
        echo "运行 ./start.sh 启动服务"
        EOF
        chmod +x install.sh
        
        # 提交
        git add .
        git commit -m "PKG Build ${{ github.run_number }}: $(date +%Y%m%d-%H%M%S)" || echo "没有更改"
        
        # 推送
        git push -f origin releases-pkg || git push --set-upstream origin releases-pkg
    
    - name: Create Release (仅主分支)
      if: github.event_name == 'push' && contains(github.ref, 'main')
      uses: softprops/action-gh-release@v1
      with:
        tag_name: pkg-v1.0.${{ github.run_number }}
        name: DStatus PKG Build ${{ github.run_number }}
        body: |
          PKG 打包的 Linux 二进制版本
          
          ## 包含内容
          - 单文件二进制 (dstatus-linux-x64)
          - 静态资源 (views, static)
          - 启动脚本 (start.sh)
          
          ## 优势
          - 无需 Node.js 环境
          - 源代码已编译保护
          - 一键部署
          
          ## 快速安装
          ```bash
          wget https://github.com/${{ github.repository }}/releases/download/pkg-v1.0.${{ github.run_number }}/dstatus-linux-x64-pkg.tar.gz
          tar -xzf dstatus-linux-x64-pkg.tar.gz
          ./start.sh
          ```
        files: dstatus-linux-x64-pkg.tar.gz
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}