"use strict";

module.exports = (DB) => {
    async function saveReport(report, metadata = {}) {
        const reportId = `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const now = Math.floor(Date.now() / 1000);
        const title = `AI分析报告 - ${new Date().toLocaleString('zh-CN')}`;
        
        const sql = `
            INSERT INTO ai_reports (
                report_id, title, report_data, metadata,
                time_range_start, time_range_end, 
                servers_analyzed, overall_score
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        `;
        
        const params = [
            reportId,
            title,
            JSON.stringify(report),
            JSON.stringify(metadata),
            metadata.timeRange?.start || now - 86400,
            metadata.timeRange?.end || now,
            metadata.serversAnalyzed || 0,
            report.summary?.overallScore || 0
        ];

        const info = await DB.run(sql, params);
        
        return {
            id: info.lastInsertRowid,
            reportId,
            title,
            createdAt: now
        };
    }

    async function getReports(options = {}) {
        const { limit = 20, offset = 0, startTime, endTime } = options;
        
        let query = `
            SELECT id, report_id, title, created_at, 
                   time_range_start, time_range_end,
                   servers_analyzed, overall_score, status
            FROM ai_reports
            WHERE 1=1
        `;
        
        const params = [];
        
        if (startTime) {
            query += ` AND created_at >= $${params.length + 1}`;
            params.push(startTime);
        }
        
        if (endTime) {
            query += ` AND created_at <= $${params.length + 1}`;
            params.push(endTime);
        }
        
        query += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
        params.push(limit, offset);
        
        const reports = await DB.all(query, params);
        
        return reports.map(report => ({
            id: report.id,
            reportId: report.report_id,
            title: report.title,
            createdAt: report.created_at,
            timeRangeStart: report.time_range_start,
            timeRangeEnd: report.time_range_end,
            serversAnalyzed: report.servers_analyzed,
            overallScore: report.overall_score,
            status: report.status
        }));
    }

    async function getReport(reportId) {
        const sql = `SELECT * FROM ai_reports WHERE report_id = $1`;
        const report = await DB.get(sql, [reportId]);
        
        if (!report) {
            return null;
        }
        
        return {
            id: report.id,
            reportId: report.report_id,
            title: report.title,
            reportData: JSON.parse(report.report_data),
            metadata: report.metadata ? JSON.parse(report.metadata) : {},
            createdAt: report.created_at,
            timeRangeStart: report.time_range_start,
            timeRangeEnd: report.time_range_end,
            serversAnalyzed: report.servers_analyzed,
            overallScore: report.overall_score,
            status: report.status
        };
    }

    async function deleteReport(reportId) {
        const sql = `DELETE FROM ai_reports WHERE report_id = $1`;
        const info = await DB.run(sql, [reportId]);
        return info.changes > 0;
    }

    async function cleanupOldReports(daysToKeep = 30) {
        const cutoffTime = Math.floor(Date.now() / 1000) - (daysToKeep * 86400);
        const sql = `DELETE FROM ai_reports WHERE created_at < $1`;
        const info = await DB.run(sql, [cutoffTime]);
        return info.changes;
    }

    async function getStats() {
        const totalCountResult = await DB.get(`SELECT COUNT(*) as count FROM ai_reports`);
        const totalCount = totalCountResult.count;

        const last24HoursResult = await DB.get(`SELECT COUNT(*) as count FROM ai_reports WHERE created_at > $1`, [Math.floor(Date.now() / 1000) - 86400]);
        const last24Hours = last24HoursResult.count;

        const avgScoreResult = await DB.get(`SELECT AVG(overall_score) as avg FROM ai_reports`);
        const avgScore = avgScoreResult.avg || 0;
        
        return {
            totalReports: totalCount,
            reportsLast24Hours: last24Hours,
            averageScore: Math.round(avgScore)
        };
    }

    return {
        saveReport,
        getReports,
        getReport,
        deleteReport,
        cleanupOldReports,
        getStats
    };
};