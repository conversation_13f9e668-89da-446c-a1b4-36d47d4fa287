"use strict"
module.exports = (DB) => {
    const TimeFunctionUtils = require('./adapters/time-function-utils');
    const ssh_scripts = {
        async ins(id, name, content, options = {}) {
            try {
                // 对于DualAdapter，写操作使用SQLite，所以用SQLite时间函数
                const dbTypeForWriting = DB.type === 'dual' ? 'sqlite' : DB.type;
                const currentDateTime = TimeFunctionUtils.getCurrentDateTime(dbTypeForWriting);
                
                // 检查表结构，优雅降级到简化版本
                const hasExtendedFields = await this._checkExtendedFields();
                
                if (hasExtendedFields) {
                    const sql = `INSERT INTO ssh_scripts
                        (id,name,content,category,description,variables,examples,tags,created_at,updated_at)
                        VALUES ($1,$2,$3,$4,$5,$6,$7,$8,${currentDateTime},${currentDateTime})`;
                    const params = [
                        id, name, content,
                        options.category || 'custom',
                        options.description || '',
                        JSON.stringify(options.variables || []),
                        JSON.stringify(options.examples || []),
                        options.tags || ''
                    ];
                    await DB.run(sql, params);
                } else {
                    // 向后兼容：只使用基础字段
                    console.warn('[SSH Scripts] 使用简化表结构，部分功能可能受限');
                    const sql = `INSERT INTO ssh_scripts (id,name,content) VALUES ($1,$2,$3)`;
                    await DB.run(sql, [id, name, content]);
                }
            } catch (error) {
                console.error('[SSH Scripts] 插入脚本失败:', error);
                throw error;
            }
        },

        async get(id) {
            const script = await DB.get("SELECT * FROM ssh_scripts WHERE id=$1 LIMIT 1", [id]);
            return this._parseScript(script);
        },

        async upd(id, name, content, options = {}) {
            try {
                const hasExtendedFields = await this._checkExtendedFields();
                
                if (hasExtendedFields) {
                    // 对于DualAdapter，写操作使用SQLite，所以用SQLite时间函数
                    const dbTypeForWriting = DB.type === 'dual' ? 'sqlite' : DB.type;
                    const sql = `UPDATE ssh_scripts SET
                        name=$1,
                        content=$2,
                        category=COALESCE($3,category),
                        description=COALESCE($4,description),
                        variables=COALESCE($5,variables),
                        examples=COALESCE($6,examples),
                        tags=COALESCE($7,tags),
                        updated_at=${TimeFunctionUtils.getCurrentDateTime(dbTypeForWriting)}
                        WHERE id=$8`;
                    const params = [
                        name, content,
                        options.category,
                        options.description,
                        options.variables ? JSON.stringify(options.variables) : undefined,
                        options.examples ? JSON.stringify(options.examples) : undefined,
                        options.tags,
                        id
                    ];
                    await DB.run(sql, params);
                } else {
                    console.warn('[SSH Scripts] 使用简化表结构，只更新基础字段');
                    const sql = `UPDATE ssh_scripts SET name=$1, content=$2 WHERE id=$3`;
                    await DB.run(sql, [name, content, id]);
                }
            } catch (error) {
                console.error('[SSH Scripts] 更新脚本失败:', error);
                throw error;
            }
        },

        async del(id) {
            await DB.run("DELETE FROM ssh_scripts WHERE id=$1", [id]);
        },

        async all() {
            try {
                const hasExtendedFields = await this._checkExtendedFields();
                if (hasExtendedFields) {
                    const scripts = await DB.all("SELECT * FROM ssh_scripts ORDER BY category, name");
                    return scripts.map(s => this._parseScript(s));
                } else {
                    // 简化排序，只按name排序
                    const scripts = await DB.all("SELECT * FROM ssh_scripts ORDER BY name");
                    return scripts.map(s => this._parseScript(s));
                }
            } catch (error) {
                console.error('[SSH Scripts] 获取所有脚本失败:', error);
                return [];
            }
        },

        async byCategory(category) {
            try {
                const hasExtendedFields = await this._checkExtendedFields();
                if (hasExtendedFields) {
                    const scripts = await DB.all("SELECT * FROM ssh_scripts WHERE category=$1 ORDER BY name", [category]);
                    return scripts.map(s => this._parseScript(s));
                } else {
                    console.warn('[SSH Scripts] 简化表结构不支持分类功能');
                    return [];
                }
            } catch (error) {
                console.error('[SSH Scripts] 按分类查询失败:', error);
                return [];
            }
        },

        async search(query) {
            try {
                const hasExtendedFields = await this._checkExtendedFields();
                if (hasExtendedFields) {
                    const scripts = await DB.all("SELECT * FROM ssh_scripts WHERE name LIKE $1 OR content LIKE $2 OR description LIKE $3 OR tags LIKE $4 ORDER BY name", [`%${query}%`, `%${query}%`, `%${query}%`, `%${query}%`]);
                    return scripts.map(s => this._parseScript(s));
                } else {
                    // 简化搜索，只搜索name和content
                    const scripts = await DB.all("SELECT * FROM ssh_scripts WHERE name LIKE $1 OR content LIKE $2 ORDER BY name", [`%${query}%`, `%${query}%`]);
                    return scripts.map(s => this._parseScript(s));
                }
            } catch (error) {
                console.error('[SSH Scripts] 搜索脚本失败:', error);
                return [];
            }
        },

        async incrementUsage(id) {
            try {
                const hasExtendedFields = await this._checkExtendedFields();
                if (hasExtendedFields) {
                    await DB.run("UPDATE ssh_scripts SET usage_count = usage_count + 1 WHERE id=$1", [id]);
                } else {
                    console.warn('[SSH Scripts] 简化表结构不支持使用统计');
                }
            } catch (error) {
                console.error('[SSH Scripts] 更新使用统计失败:', error);
            }
        },

        async getCategories() {
            try {
                const hasExtendedFields = await this._checkExtendedFields();
                if (hasExtendedFields) {
                    return await DB.all("SELECT DISTINCT category, COUNT(*) as count FROM ssh_scripts GROUP BY category ORDER BY category");
                } else {
                    console.warn('[SSH Scripts] 简化表结构不支持分类功能');
                    return [];
                }
            } catch (error) {
                console.error('[SSH Scripts] 获取分类失败:', error);
                return [];
            }
        },

        _parseScript(script) {
            if (!script) return null;
            try {
                if (script.variables && typeof script.variables === 'string') {
                    script.variables = JSON.parse(script.variables);
                }
                if (script.examples && typeof script.examples === 'string') {
                    script.examples = JSON.parse(script.examples);
                }
                
                // 向后兼容：如果字段不存在，提供默认值
                script.category = script.category || 'custom';
                script.description = script.description || '';
                script.variables = script.variables || [];
                script.examples = script.examples || [];
                script.tags = script.tags || '';
                script.usage_count = script.usage_count || 0;
            } catch (e) {
                console.error('[SSH Scripts] 解析脚本数据失败:', e);
                script.variables = [];
                script.examples = [];
            }
            return script;
        },

        async _checkExtendedFields() {
            if (this._extendedFieldsCache !== undefined) {
                return this._extendedFieldsCache;
            }
            
            try {
                // 检查是否存在扩展字段
                // 对于DualAdapter，写操作使用SQLite，所以用SQLite语法检查
                const isPostgresOnly = DB.type === 'postgresql';
                const tableInfo = isPostgresOnly
                    ? await DB.all(`
                        SELECT column_name FROM information_schema.columns 
                        WHERE table_name = 'ssh_scripts'
                    `)
                    : await DB.all("PRAGMA table_info(ssh_scripts)");
                
                const columnNames = isPostgresOnly
                    ? tableInfo.map(col => col.column_name)
                    : tableInfo.map(col => col.name);
                    
                // 检查关键的扩展字段是否存在
                const requiredFields = ['category', 'description', 'variables', 'examples'];
                const hasAllFields = requiredFields.every(field => columnNames.includes(field));
                
                this._extendedFieldsCache = hasAllFields;
                console.log(`[SSH Scripts] 表结构检查: ${hasAllFields ? '完整' : '简化'}结构`);
                return hasAllFields;
            } catch (error) {
                console.error('[SSH Scripts] 检查表结构失败:', error);
                this._extendedFieldsCache = false;
                return false;
            }
        }
    };

    return {
        ssh_scripts
    };
}