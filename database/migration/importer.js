"use strict";

/**
 * 数据导入模块
 * 负责将数据导入到目标数据库（PostgreSQL）
 */

const migrationConfig = require('./config');

class DataImporter {
    constructor(targetAdapter) {
        this.targetAdapter = targetAdapter;
        this.batchSize = migrationConfig.migration.batchSize; // 批量插入的默认大小
    }

    /**
     * 批量导入数据到表
     * @param {string} tableName - 表名
     * @param {Array} columns - 列名数组
     * @param {AsyncGenerator} dataStream - 数据流
     * @returns {number} 导入的记录数量
     */
    async importTable(tableName, columns, dataStream) {
        console.log(`[导入] 开始导入表 ${tableName}`);
        
        const batch = [];
        let totalCount = 0;
        
        try {
            for await (const row of dataStream) {
                batch.push(row);
                
                // 当批次达到指定大小时，执行批量插入
                if (batch.length >= this.batchSize) {
                    const insertedCount = await this.batchInsert(tableName, columns, batch);
                    totalCount += insertedCount;
                    batch.length = 0; // 清空批次
                    
                    if (totalCount % migrationConfig.logging.progressInterval === 0) {
                        console.log(`[导入] 已导入 ${tableName} 表 ${totalCount} 条记录`);
                    }
                }
            }
            
            // 导入剩余的数据
            if (batch.length > 0) {
                const insertedCount = await this.batchInsert(tableName, columns, batch);
                totalCount += insertedCount;
            }
            
            console.log(`[导入] 表 ${tableName} 导入完成，共 ${totalCount} 条记录`);
            return totalCount;
        } catch (error) {
            console.error(`[导入] 表 ${tableName} 导入失败:`, error.message);
            throw error;
        }
    }

    /**
     * 批量插入数据
     * @param {string} tableName - 表名
     * @param {Array} columns - 列名数组
     * @param {Array} rows - 数据行数组
     * @returns {number} 插入的记录数量
     */
    async batchInsert(tableName, columns, rows) {
        if (rows.length === 0) {
            return 0;
        }

        if (this.targetAdapter.type === 'postgresql') {
            // 使用 PostgreSQL 的多行 INSERT 语句
            return await this.postgresqlBatchInsert(tableName, columns, rows);
        } else {
            // 对于其他数据库，使用逐行插入
            return await this.standardBatchInsert(tableName, columns, rows);
        }
    }

    /**
     * PostgreSQL 批量插入 - 性能优化版本
     * @param {string} tableName - 表名
     * @param {Array} columns - 列名数组
     * @param {Array} rows - 数据行数组
     * @param {Object} client - 可选的事务客户端
     * @returns {number} 插入的记录数量
     */
    async postgresqlBatchInsert(tableName, columns, rows, client = null) {
        if (rows.length === 0) return 0;
        
        // 预分配数组以提高性能
        const valuesClauses = new Array(rows.length);
        const allParams = new Array(rows.length * columns.length);
        const columnList = columns.join(', ');
        
        // 优化: 批量构建参数，减少数组操作
        let paramIndex = 0;
        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            const placeholders = new Array(columns.length);
            
            for (let j = 0; j < columns.length; j++) {
                placeholders[j] = `$${paramIndex + 1}`;
                
                // 获取并处理值
                let value = row[columns[j]];
                
                // 时间字段特殊处理 (ssh_scripts表优化)
                if (tableName === 'ssh_scripts' && (columns[j] === 'created_at' || columns[j] === 'updated_at')) {
                    if (typeof value === 'string' && value.length > 0 && value !== 'NULL') {
                        try {
                            value = Math.floor(new Date(value).getTime() / 1000);
                        } catch (error) {
                            console.warn(`[DataImporter] 时间转换失败 ${columns[j]}: ${value}, 使用当前时间`);
                            value = Math.floor(Date.now() / 1000);
                        }
                    } else if (value === null || value === '' || value === 'NULL') {
                        value = null;
                    }
                }
                
                allParams[paramIndex] = value;
                paramIndex++;
            }
            
            valuesClauses[i] = `(${placeholders.join(', ')})`;
        }
        
        // 主键冲突处理 (缓存查询结果)
        let conflictClause = '';
        if (columns.includes('id')) {
            conflictClause = await this.getConflictClause(tableName);
        }
        
        const sql = `INSERT INTO ${tableName} (${columnList}) VALUES ${valuesClauses.join(', ')}${conflictClause}`;
        
        try {
            // 使用提供的事务客户端或创建新连接
            const adapter = client || this.targetAdapter;
            const result = await adapter.run(sql, allParams);
            return result.changes || rows.length;
        } catch (error) {
            console.error(`[导入] PostgreSQL 批量插入失败:`, error.message);
            console.error(`[导入] 表: ${tableName}, 批量大小: ${rows.length}`);
            throw error;
        }
    }

    /**
     * 获取冲突处理子句 (缓存优化)
     * @param {string} tableName - 表名
     * @returns {string} ON CONFLICT子句
     */
    async getConflictClause(tableName) {
        // 静态缓存，避免重复查询
        if (!this._conflictCache) {
            this._conflictCache = new Map();
        }
        
        if (this._conflictCache.has(tableName)) {
            return this._conflictCache.get(tableName);
        }
        
        let conflictClause = '';
        try {
            const pkCheck = await this.targetAdapter.query(`
                SELECT kcu.column_name 
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name 
                AND tc.table_schema = kcu.table_schema
                WHERE tc.table_name = $1 AND tc.constraint_type = 'PRIMARY KEY'
            `, [tableName]);
            
            if (pkCheck.length > 0) {
                const pkColumns = pkCheck.map(row => row.column_name);
                if (pkColumns.includes('id')) {
                    conflictClause = ' ON CONFLICT (id) DO NOTHING';
                }
            }
        } catch (error) {
            console.log(`[导入] 无法检查表 ${tableName} 的主键约束，跳过 ON CONFLICT`);
        }
        
        this._conflictCache.set(tableName, conflictClause);
        return conflictClause;
    }

    /**
     * 标准批量插入（逐行）
     * @param {string} tableName - 表名
     * @param {Array} columns - 列名数组
     * @param {Array} rows - 数据行数组
     * @returns {number} 插入的记录数量
     */
    async standardBatchInsert(tableName, columns, rows) {
        const columnList = columns.join(', ');
        const placeholders = columns.map((_, index) => `$${index + 1}`).join(', ');
        const sql = `INSERT INTO ${tableName} (${columnList}) VALUES (${placeholders})`;
        
        let insertedCount = 0;
        
        for (const row of rows) {
            try {
                const params = columns.map(col => row[col]);
                await this.targetAdapter.run(sql, params);
                insertedCount++;
            } catch (error) {
                console.error(`[导入] 单行插入失败:`, error.message);
                console.error(`[导入] 数据:`, row);
                throw error;
            }
        }
        
        return insertedCount;
    }

    /**
     * 导入 groups 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importGroups(dataStream) {
        const columns = ['id', 'name', 'top'];
        return await this.importTable('groups', columns, dataStream);
    }

    /**
     * 导入 servers 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importServers(dataStream) {
        const columns = [
            'sid', 'name', 'data', 'top', 'status', 'expire_time', 'group_id',
            'traffic_limit', 'traffic_reset_day', 'traffic_alert_percent', 
            'traffic_last_reset', 'traffic_direction', 'traffic_calibration_date', 
            'traffic_calibration_value'
        ];
        return await this.importTable('servers', columns, dataStream);
    }

    /**
     * 导入 traffic 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importTraffic(dataStream) {
        const columns = ['sid', 'hs', 'ds', 'ms'];
        return await this.importTable('traffic', columns, dataStream);
    }

    /**
     * 导入 lt 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importLt(dataStream) {
        const columns = ['sid', 'traffic'];
        return await this.importTable('lt', columns, dataStream);
    }

    /**
     * 导入 traffic_calibration 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importTrafficCalibration(dataStream) {
        const columns = ['sid', 'calibration_date', 'calibration_value'];
        return await this.importTable('traffic_calibration', columns, dataStream);
    }

    /**
     * 导入 autodiscovery_servers 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importAutodiscoveryServers(dataStream) {
        const columns = ['id', 'hostname', 'ip', 'system', 'version', 'device', 'api_key', 'status', 'created_at', 'updated_at'];
        return await this.importTable('autodiscovery_servers', columns, dataStream);
    }

    /**
     * 导入 ai_reports 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importAiReports(dataStream) {
        const columns = ['id', 'report_id', 'title', 'report_data', 'metadata', 'created_at', 'time_range_start', 'time_range_end', 'servers_analyzed', 'overall_score', 'status'];
        return await this.importTable('ai_reports', columns, dataStream);
    }

    /**
     * 导入 load_archive 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importLoadArchive(dataStream) {
        const columns = ['id', 'sid', 'cpu', 'mem', 'swap', 'ibw', 'obw', 'expire_time', 'created_at'];
        return await this.importTable('load_archive', columns, dataStream);
    }

    /**
     * 导入 load_h 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importLoadH(dataStream) {
        const columns = ['id', 'sid', 'cpu', 'mem', 'swap', 'ibw', 'obw', 'expire_time', 'created_at'];
        return await this.importTable('load_h', columns, dataStream);
    }

    /**
     * 导入 load_m 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importLoadM(dataStream) {
        const columns = ['id', 'sid', 'cpu', 'mem', 'swap', 'ibw', 'obw', 'expire_time', 'created_at'];
        return await this.importTable('load_m', columns, dataStream);
    }

    /**
     * 导入 tcping_5m 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importTcping5m(dataStream) {
        const columns = ['id', 'target_id', 'success_rate', 'avg_time', 'min_time', 'max_time', 'created_at', 'expire_time', 'sid'];
        return await this.importTable('tcping_5m', columns, dataStream);
    }

    /**
     * 导入 tcping_archive 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importTcpingArchive(dataStream) {
        const columns = ['id', 'target_id', 'sid', 'success_rate', 'avg_time', 'min_time', 'max_time', 'created_at', 'year', 'month', 'day', 'hour', 'minute'];
        return await this.importTable('tcping_archive', columns, dataStream);
    }

    /**
     * 导入 tcping_d 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importTcpingD(dataStream) {
        const columns = ['id', 'target_id', 'success_rate', 'avg_time', 'min_time', 'max_time', 'created_at', 'expire_time', 'node_id', 'sid'];
        return await this.importTable('tcping_d', columns, dataStream);
    }

    /**
     * 导入 tcping_h 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importTcpingH(dataStream) {
        const columns = ['id', 'target_id', 'success_rate', 'avg_time', 'min_time', 'max_time', 'created_at', 'expire_time', 'node_id', 'sid'];
        return await this.importTable('tcping_h', columns, dataStream);
    }

    /**
     * 导入 tcping_m 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importTcpingM(dataStream) {
        const columns = ['id', 'target_id', 'success_rate', 'avg_time', 'min_time', 'max_time', 'created_at', 'expire_time', 'node_id', 'sid'];
        return await this.importTable('tcping_m', columns, dataStream);
    }

    /**
     * 导入 tcping_month 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importTcpingMonth(dataStream) {
        const columns = ['id', 'target_id', 'success_rate', 'avg_time', 'min_time', 'max_time', 'created_at', 'expire_time', 'node_id', 'sid'];
        return await this.importTable('tcping_month', columns, dataStream);
    }

    /**
     * 导入 setting 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importSetting(dataStream) {
        const columns = ['key', 'val'];
        return await this.importTable('setting', columns, dataStream);
    }

    /**
     * 导入 ssh_scripts 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importSshScripts(dataStream) {
        const columns = ['id', 'name', 'content', 'category', 'description', 'variables', 'examples', 'tags', 'usage_count', 'created_at', 'updated_at'];
        return await this.importTable('ssh_scripts', columns, dataStream);
    }

    /**
     * 导入 monitor_regions 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importMonitorRegions(dataStream) {
        const columns = ['id', 'name', 'description', 'created_at'];
        return await this.importTable('monitor_regions', columns, dataStream);
    }

    /**
     * 导入 monitor_targets 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importMonitorTargets(dataStream) {
        const columns = ['id', 'region_id', 'name', 'host', 'port', 'description', 'created_at', 'mode', 'node_id', 'test_type'];
        return await this.importTable('monitor_targets', columns, dataStream);
    }

    /**
     * 导入 db_migrations 表
     * @param {AsyncGenerator} dataStream - 数据流
     */
    async importDbMigrations(dataStream) {
        const columns = ['version', 'name', 'executed_at', 'status'];
        return await this.importTable('db_migrations', columns, dataStream);
    }

    /**
     * 清空目标表
     * @param {string} tableName - 表名
     */
    async truncateTable(tableName) {
        try {
            console.log(`[导入] 清空表 ${tableName}`);
            
            // 首先尝试使用 TRUNCATE，它会重置序列
            await this.targetAdapter.run(`TRUNCATE TABLE ${tableName} RESTART IDENTITY CASCADE`);
            console.log(`[导入] 使用 TRUNCATE 清空表 ${tableName}`);
            
        } catch (error) {
            console.warn(`[警告] 无法使用 TRUNCATE 清空表 ${tableName}:`, error.message);
            // 尝试使用 DELETE 语句
            try {
                await this.targetAdapter.run(`DELETE FROM ${tableName}`);
                console.log(`[导入] 使用 DELETE 清空表 ${tableName}`);
                
                // 如果表有序列，重置序列
                try {
                    await this.targetAdapter.run(`ALTER SEQUENCE ${tableName}_id_seq RESTART WITH 1`);
                    console.log(`[导入] 重置序列 ${tableName}_id_seq`);
                } catch (seqError) {
                    // 忽略序列重置错误，可能表没有序列
                }
                
            } catch (deleteError) {
                console.error(`[错误] 无法清空表 ${tableName}:`, deleteError.message);
                throw deleteError;
            }
        }
    }

    /**
     * 设置批量插入的大小
     * @param {number} size - 批量大小
     */
    setBatchSize(size) {
        const { minSize, maxSize } = migrationConfig.migration.adaptiveBatch;
        this.batchSize = Math.max(minSize, Math.min(maxSize, size)); // 使用配置的限制
        console.log(`[导入] 设置批量大小为 ${this.batchSize}`);
    }
}

module.exports = DataImporter;