#!/usr/bin/env node
"use strict";

const path = require('path');
const dbConfig = require('../config');
const SQLiteAdapter = require('../adapters/sqlite');
const PostgreSQLAdapter = require('../adapters/postgresql');
const DataExporter = require('./exporter');
const DataImporter = require('./importer');

class SingleTableMigration {
    constructor() {
        this.sourceAdapter = null;
        this.targetAdapter = null;
        this.exporter = null;
        this.importer = null;
    }

    async initialize() {
        console.log('[单表] 初始化数据库连接...');
        
        this.sourceAdapter = new SQLiteAdapter(dbConfig.sqlite);
        await this.sourceAdapter.connect();
        console.log('[数据库] SQLite 连接成功');
        
        this.targetAdapter = new PostgreSQLAdapter(dbConfig.postgresql);
        await this.targetAdapter.connect();
        console.log('[数据库] PostgreSQL 连接成功');
        
        this.exporter = new DataExporter(this.sourceAdapter);
        this.importer = new DataImporter(this.targetAdapter);
        this.importer.setBatchSize(10); // 极小批次
    }

    async migrateTable(tableName) {
        console.log(`[单表] 开始迁移表 ${tableName}...`);
        
        try {
            // 检查源表记录数
            const sourceCount = await this.exporter.getTableCount(tableName);
            if (sourceCount === 0) {
                console.log(`[单表] 表 ${tableName} 为空，跳过迁移`);
                return 0;
            }
            
            // 检查目标表记录数
            let targetCount = 0;
            try {
                const result = await this.targetAdapter.query(`SELECT COUNT(*) as count FROM ${tableName}`);
                targetCount = result[0].count;
            } catch (error) {
                console.log(`[单表] 表 ${tableName} 在目标数据库中不存在: ${error.message}`);
                return 0;
            }
            
            if (targetCount >= sourceCount) {
                console.log(`[单表] 表 ${tableName} 已完成迁移 (${targetCount}/${sourceCount})`);
                return targetCount;
            }
            
            console.log(`[单表] 表 ${tableName} 状态: ${targetCount}/${sourceCount}`);
            
            // 清空目标表重新开始
            await this.importer.truncateTable(tableName);
            
            // 获取方法名
            const exportMethod = this.getExportMethodName(tableName);
            const importMethod = this.getImportMethodName(tableName);
            
            if (!this.exporter[exportMethod]) {
                throw new Error(`导出方法 ${exportMethod} 不存在`);
            }
            if (!this.importer[importMethod]) {
                throw new Error(`导入方法 ${importMethod} 不存在`);
            }
            
            // 执行迁移
            const dataStream = this.exporter[exportMethod]();
            const migratedCount = await this.importer[importMethod](dataStream);
            
            console.log(`[单表] 表 ${tableName} 迁移完成，${migratedCount} 条记录`);
            return migratedCount;
            
        } catch (error) {
            console.error(`[单表] 表 ${tableName} 迁移失败:`, error.message);
            throw error;
        }
    }

    getExportMethodName(tableName) {
        let camelCase = tableName.replace(/_([a-z0-9])/g, (match, letter) => letter.toUpperCase());
        if (tableName === 'tcping_5m') {
            camelCase = 'tcping5m';
        }
        return 'export' + camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
    }

    getImportMethodName(tableName) {
        let camelCase = tableName.replace(/_([a-z0-9])/g, (match, letter) => letter.toUpperCase());
        if (tableName === 'tcping_5m') {
            camelCase = 'tcping5m';
        }
        return 'import' + camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
    }

    async cleanup() {
        console.log('[单表] 清理连接...');
        
        if (this.sourceAdapter) {
            await this.sourceAdapter.disconnect();
            console.log('[数据库] SQLite 连接已断开');
        }
        
        if (this.targetAdapter) {
            await this.targetAdapter.disconnect();
            console.log('[数据库] PostgreSQL 连接已断开');
        }
    }

    async run(tableName) {
        try {
            await this.initialize();
            const migratedCount = await this.migrateTable(tableName);
            
            console.log(`\n==========================================`);
            console.log(`✅ 表 ${tableName} 迁移完成！`);
            console.log(`迁移记录数: ${migratedCount.toLocaleString()}`);
            console.log(`==========================================\n`);
            
            return migratedCount;
            
        } catch (error) {
            console.error(`\n==========================================`);
            console.error(`❌ 表 ${tableName} 迁移失败: ${error.message}`);
            console.error(`==========================================\n`);
            throw error;
        } finally {
            await this.cleanup();
        }
    }
}

// 获取命令行参数
const tableName = process.argv[2];
if (!tableName) {
    console.error('请指定要迁移的表名');
    console.error('用法: node single_table_migration.js <table_name>');
    process.exit(1);
}

const migration = new SingleTableMigration();
migration.run(tableName)
    .then(count => {
        console.log(`表 ${tableName} 迁移成功完成，共 ${count} 条记录`);
        process.exit(0);
    })
    .catch(error => {
        console.error(`表 ${tableName} 迁移失败:`, error.message);
        process.exit(1);
    });

module.exports = SingleTableMigration;