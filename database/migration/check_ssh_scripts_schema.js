#!/usr/bin/env node
"use strict";

const dbConfig = require('../config');
const SQLiteAdapter = require('../adapters/sqlite');
const PostgreSQLAdapter = require('../adapters/postgresql');

async function checkSshScriptsSchema() {
    const sqliteAdapter = new SQLiteAdapter(dbConfig.sqlite);
    const pgAdapter = new PostgreSQLAdapter(dbConfig.postgresql);
    
    try {
        await sqliteAdapter.connect();
        await pgAdapter.connect();
        
        console.log('[检查] ssh_scripts表结构对比...\n');
        
        // SQLite表结构
        const sqliteSchema = await sqliteAdapter.query(`PRAGMA table_info(ssh_scripts)`);
        console.log('[SQLite] ssh_scripts表结构:');
        sqliteSchema.forEach(col => {
            console.log(`  ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.pk ? '(PRIMARY KEY)' : ''}`);
        });
        
        // PostgreSQL表结构
        const pgSchema = await pgAdapter.query(`
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'ssh_scripts'
            ORDER BY ordinal_position
        `);
        
        console.log('\n[PostgreSQL] ssh_scripts表结构:');
        pgSchema.forEach(col => {
            console.log(`  ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : ''} ${col.column_default ? `DEFAULT ${col.column_default}` : ''}`);
        });
        
        // 检查数据样本
        const sqliteData = await sqliteAdapter.query(`
            SELECT id, name, created_at, updated_at 
            FROM ssh_scripts 
            LIMIT 3
        `);
        
        console.log('\n[SQLite] ssh_scripts数据样本:');
        sqliteData.forEach(row => {
            console.log(`  ID: ${row.id}, Name: ${row.name}, Created: ${row.created_at}, Updated: ${row.updated_at}`);
        });
        
    } catch (error) {
        console.error('[检查] 失败:', error.message);
    } finally {
        await sqliteAdapter.disconnect();
        await pgAdapter.disconnect();
    }
}

checkSshScriptsSchema();