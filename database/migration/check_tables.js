#!/usr/bin/env node
"use strict";

const dbConfig = require('../config');
const PostgreSQLAdapter = require('../adapters/postgresql');

async function checkTables() {
    const adapter = new PostgreSQLAdapter(dbConfig.postgresql);
    
    try {
        await adapter.connect();
        console.log('[检查] PostgreSQL 连接成功');
        
        // 查询所有表
        const tables = await adapter.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        `);
        
        console.log('[检查] PostgreSQL 中的表:');
        tables.forEach((row, index) => {
            console.log(`  ${index + 1}. ${row.table_name}`);
        });
        
        console.log(`[检查] 总计 ${tables.length} 个表`);
        
    } catch (error) {
        console.error('[检查] 失败:', error.message);
    } finally {
        await adapter.disconnect();
    }
}

checkTables();