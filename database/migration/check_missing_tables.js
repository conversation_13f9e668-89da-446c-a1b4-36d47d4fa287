#!/usr/bin/env node
"use strict";

const dbConfig = require('../config');
const SQLiteAdapter = require('../adapters/sqlite');
const PostgreSQLAdapter = require('../adapters/postgresql');

async function checkMissingTables() {
    const sqliteAdapter = new SQLiteAdapter(dbConfig.sqlite);
    const postgresAdapter = new PostgreSQLAdapter(dbConfig.postgresql);
    
    try {
        await sqliteAdapter.connect();
        await postgresAdapter.connect();
        
        console.log('[检查] 数据库连接成功');
        
        // 获取SQLite中的表
        const sqliteTables = await sqliteAdapter.query(`
            SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        `);
        
        // 获取PostgreSQL中的表
        const postgresTables = await postgresAdapter.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name NOT LIKE 'failed_authentication_%'
            AND table_name NOT LIKE 'postgres_log%'
            AND table_name NOT LIKE 'pg_%'
            ORDER BY table_name
        `);
        
        console.log('[检查] SQLite 表:');
        const sqliteTableNames = sqliteTables.map(row => row.name);
        sqliteTableNames.forEach((name, index) => {
            console.log(`  ${index + 1}. ${name}`);
        });
        
        console.log('\n[检查] PostgreSQL 表:');
        const postgresTableNames = postgresTables.map(row => row.table_name);
        postgresTableNames.forEach((name, index) => {
            console.log(`  ${index + 1}. ${name}`);
        });
        
        // 找出缺失的表
        const missingTables = sqliteTableNames.filter(name => !postgresTableNames.includes(name));
        
        if (missingTables.length > 0) {
            console.log('\n[检查] PostgreSQL 中缺失的表:');
            missingTables.forEach((name, index) => {
                console.log(`  ${index + 1}. ${name}`);
            });
            
            // 检查 traffic_calibration 表结构
            if (missingTables.includes('traffic_calibration')) {
                console.log('\n[检查] traffic_calibration 表结构:');
                const tableInfo = await sqliteAdapter.query(`PRAGMA table_info(traffic_calibration)`);
                tableInfo.forEach(column => {
                    console.log(`  ${column.name}: ${column.type} ${column.notnull ? 'NOT NULL' : ''} ${column.pk ? 'PRIMARY KEY' : ''}`);
                });
            }
        } else {
            console.log('\n[检查] 所有表都存在');
        }
        
    } catch (error) {
        console.error('[检查] 失败:', error.message);
    } finally {
        await sqliteAdapter.disconnect();
        await postgresAdapter.disconnect();
    }
}

checkMissingTables();