#!/usr/bin/env node
"use strict";

const dbConfig = require('../config');
const PostgreSQLAdapter = require('../adapters/postgresql');
const SQLiteAdapter = require('../adapters/sqlite');

async function checkMigrationStatus() {
    const pgAdapter = new PostgreSQLAdapter(dbConfig.postgresql);
    const sqliteAdapter = new SQLiteAdapter(dbConfig.sqlite);
    
    try {
        await pgAdapter.connect();
        await sqliteAdapter.connect();
        
        console.log('[检查] 迁移状态检查...\n');
        
        // 定义所有需要迁移的表
        const tables = [
            'groups', 'servers', 'traffic', 'lt', 'traffic_calibration',
            'autodiscovery_servers', 'ai_reports', 'load_archive', 'load_h', 'load_m',
            'tcping_5m', 'tcping_archive', 'tcping_d', 'tcping_h', 'tcping_m', 'tcping_month',
            'setting', 'ssh_scripts', 'monitor_regions', 'monitor_targets', 'db_migrations'
        ];
        
        let totalSourceRecords = 0;
        let totalTargetRecords = 0;
        let completedTables = 0;
        let pendingTables = [];
        
        for (const table of tables) {
            try {
                // 获取SQLite源数据数量
                const sourceCount = await sqliteAdapter.query(`SELECT COUNT(*) as count FROM ${table}`);
                const sourceRecords = sourceCount[0].count;
                
                // 获取PostgreSQL目标数据数量
                let targetRecords = 0;
                try {
                    const targetCount = await pgAdapter.query(`SELECT COUNT(*) as count FROM ${table}`);
                    targetRecords = targetCount[0].count;
                } catch (error) {
                    console.log(`[检查] 表 ${table} 在PostgreSQL中不存在: ${error.message}`);
                    continue;
                }
                
                totalSourceRecords += sourceRecords;
                totalTargetRecords += targetRecords;
                
                const status = sourceRecords === targetRecords ? '✅ 完成' : 
                             targetRecords > 0 ? '🔄 部分' : '⏳ 待迁移';
                
                if (sourceRecords === targetRecords && sourceRecords > 0) {
                    completedTables++;
                } else if (sourceRecords > 0) {
                    pendingTables.push({
                        table,
                        source: sourceRecords,
                        target: targetRecords,
                        pending: sourceRecords - targetRecords
                    });
                }
                
                console.log(`[检查] ${table.padEnd(20)} | 源: ${sourceRecords.toString().padStart(8)} | 目标: ${targetRecords.toString().padStart(8)} | ${status}`);
                
            } catch (error) {
                console.log(`[检查] 表 ${table} 检查失败: ${error.message}`);
            }
        }
        
        console.log(`\n[检查] ========== 迁移状态汇总 ==========`);
        console.log(`[检查] 总源记录数: ${totalSourceRecords.toLocaleString()}`);
        console.log(`[检查] 已迁移记录数: ${totalTargetRecords.toLocaleString()}`);
        console.log(`[检查] 完成进度: ${((totalTargetRecords / totalSourceRecords) * 100).toFixed(1)}%`);
        console.log(`[检查] 完成表数: ${completedTables}/${tables.length}`);
        console.log(`[检查] 待迁移记录数: ${(totalSourceRecords - totalTargetRecords).toLocaleString()}`);
        
        if (pendingTables.length > 0) {
            console.log(`\n[检查] 待迁移表详情:`);
            pendingTables.forEach(item => {
                console.log(`  ${item.table}: ${item.pending.toLocaleString()} 条记录`);
            });
        }
        
        return {
            totalSourceRecords,
            totalTargetRecords,
            completedTables,
            pendingTables,
            progress: (totalTargetRecords / totalSourceRecords) * 100
        };
        
    } catch (error) {
        console.error('[检查] 失败:', error.message);
    } finally {
        await pgAdapter.disconnect();
        await sqliteAdapter.disconnect();
    }
}

checkMigrationStatus();