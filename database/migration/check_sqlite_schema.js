#!/usr/bin/env node
"use strict";

const dbConfig = require('../config');
const SQLiteAdapter = require('../adapters/sqlite');

async function checkSQLiteSchema() {
    const sqliteAdapter = new SQLiteAdapter(dbConfig.sqlite);
    
    try {
        await sqliteAdapter.connect();
        
        const tables = ['load_h', 'load_m', 'load_archive'];
        
        for (const table of tables) {
            console.log(`\n[检查] ========== SQLite ${table} 表结构 ==========`);
            
            // 获取表结构
            const schema = await sqliteAdapter.query(`PRAGMA table_info(${table})`);
            
            console.log(`[检查] ${table} 表列定义:`);
            schema.forEach(col => {
                const pk = col.pk ? ' (PRIMARY KEY)' : '';
                const notnull = col.notnull ? ' NOT NULL' : '';
                const defaultVal = col.dflt_value ? ` DEFAULT ${col.dflt_value}` : '';
                console.log(`  ${col.name}: ${col.type}${notnull}${defaultVal}${pk}`);
            });
            
            // 获取索引信息
            const indexes = await sqliteAdapter.query(`PRAGMA index_list(${table})`);
            
            if (indexes.length > 0) {
                console.log(`[检查] ${table} 表索引:`);
                for (const idx of indexes) {
                    const indexInfo = await sqliteAdapter.query(`PRAGMA index_info(${idx.name})`);
                    const columns = indexInfo.map(info => info.name).join(', ');
                    const unique = idx.unique ? ' (UNIQUE)' : '';
                    console.log(`  ${idx.name}: ${columns}${unique}`);
                }
            }
            
            // 获取外键信息
            const foreignKeys = await sqliteAdapter.query(`PRAGMA foreign_key_list(${table})`);
            
            if (foreignKeys.length > 0) {
                console.log(`[检查] ${table} 表外键:`);
                foreignKeys.forEach(fk => {
                    console.log(`  ${fk.from} -> ${fk.table}.${fk.to}`);
                });
            }
        }
        
    } catch (error) {
        console.error('[检查] 失败:', error.message);
    } finally {
        await sqliteAdapter.disconnect();
    }
}

checkSQLiteSchema();