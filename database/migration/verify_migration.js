"use strict";

/**
 * 数据迁移完整性验证工具
 * 验证SQLite和PostgreSQL数据库中各表的数据一致性
 */

const DatabaseAdapter = require('../adapters/sqlite');
const PostgreSQLAdapter = require('../adapters/postgresql');
const { DatabaseConfig } = require('../config');

class MigrationVerifier {
    constructor() {
        this.sourceAdapter = null;
        this.targetAdapter = null;
        this.verificationResults = {};
    }

    async initialize() {
        console.log('[验证] 初始化数据库连接...');
        
        // 初始化SQLite连接
        this.sourceAdapter = new DatabaseAdapter();
        await this.sourceAdapter.connect();
        console.log('[数据库] SQLite 连接成功');

        // 初始化PostgreSQL连接
        const config = new DatabaseConfig();
        this.targetAdapter = new PostgreSQLAdapter(config.postgresql);
        await this.targetAdapter.connect();
        console.log('[数据库] PostgreSQL 连接成功');
    }

    async cleanup() {
        console.log('[验证] 清理连接...');
        if (this.sourceAdapter) {
            await this.sourceAdapter.disconnect();
            console.log('[数据库] SQLite 连接已断开');
        }
        if (this.targetAdapter) {
            await this.targetAdapter.disconnect();
            console.log('[数据库] PostgreSQL 连接已断开');
        }
    }

    /**
     * 验证单个表的数据一致性
     * @param {string} tableName - 表名
     * @returns {Object} 验证结果
     */
    async verifyTable(tableName) {
        console.log(`[验证] 开始验证表 ${tableName}...`);
        
        try {
            // 获取源表记录数
            const sourceCount = await this.getTableCount(this.sourceAdapter, tableName);
            console.log(`[验证] ${tableName} SQLite记录数: ${sourceCount}`);

            // 获取目标表记录数
            const targetCount = await this.getTableCount(this.targetAdapter, tableName);
            console.log(`[验证] ${tableName} PostgreSQL记录数: ${targetCount}`);

            // 数据抽样验证
            const sampleVerification = await this.verifySampleData(tableName, sourceCount);

            const result = {
                table: tableName,
                sourceCount,
                targetCount,
                countMatch: sourceCount === targetCount,
                sampleVerification,
                status: sourceCount === targetCount && sampleVerification.passed ? 'PASS' : 'FAIL',
                timestamp: new Date().toISOString()
            };

            console.log(`[验证] ${tableName} 验证${result.status === 'PASS' ? '通过' : '失败'}`);
            return result;

        } catch (error) {
            console.error(`[验证] 验证表 ${tableName} 时出错:`, error.message);
            return {
                table: tableName,
                status: 'ERROR',
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 获取表记录数
     * @param {Object} adapter - 数据库适配器
     * @param {string} tableName - 表名
     * @returns {number} 记录数
     */
    async getTableCount(adapter, tableName) {
        try {
            const result = await adapter.get(`SELECT COUNT(*) as count FROM ${tableName}`);
            return parseInt(result.count);
        } catch (error) {
            console.warn(`[验证] 无法获取表 ${tableName} 的记录数:`, error.message);
            return 0;
        }
    }

    /**
     * 抽样验证数据一致性
     * @param {string} tableName - 表名
     * @param {number} totalCount - 总记录数
     * @returns {Object} 抽样验证结果
     */
    async verifySampleData(tableName, totalCount) {
        if (totalCount === 0) {
            return { passed: true, sampleSize: 0, message: '空表，跳过抽样验证' };
        }

        try {
            // 抽样大小：最多100条记录或10%，取较小值
            const sampleSize = Math.min(100, Math.ceil(totalCount * 0.1));
            
            if (sampleSize === 0) {
                return { passed: true, sampleSize: 0, message: '记录数太少，跳过抽样验证' };
            }

            // 获取抽样数据（随机选择）
            const sampleQuery = this.getSampleQuery(tableName, sampleSize);
            
            const sourceSample = await this.sourceAdapter.all(sampleQuery);
            const targetSample = await this.targetAdapter.all(sampleQuery);

            if (sourceSample.length !== targetSample.length) {
                return {
                    passed: false,
                    sampleSize,
                    message: `抽样数量不匹配: SQLite ${sourceSample.length}, PostgreSQL ${targetSample.length}`
                };
            }

            // 简单的数据比较（基于关键字段）
            const keyField = this.getKeyField(tableName);
            if (keyField) {
                const sourceKeys = new Set(sourceSample.map(row => row[keyField]));
                const targetKeys = new Set(targetSample.map(row => row[keyField]));
                
                const missingInTarget = [...sourceKeys].filter(key => !targetKeys.has(key));
                const extraInTarget = [...targetKeys].filter(key => !sourceKeys.has(key));

                if (missingInTarget.length > 0 || extraInTarget.length > 0) {
                    return {
                        passed: false,
                        sampleSize,
                        message: `关键字段不匹配: 缺失 ${missingInTarget.length}, 多余 ${extraInTarget.length}`
                    };
                }
            }

            return {
                passed: true,
                sampleSize,
                message: `抽样验证通过 (${sampleSize}条记录)`
            };

        } catch (error) {
            return {
                passed: false,
                sampleSize: 0,
                message: `抽样验证失败: ${error.message}`
            };
        }
    }

    /**
     * 获取抽样查询SQL
     * @param {string} tableName - 表名
     * @param {number} sampleSize - 抽样大小
     * @returns {string} SQL查询
     */
    getSampleQuery(tableName, sampleSize) {
        const keyField = this.getKeyField(tableName);
        if (keyField) {
            return `SELECT * FROM ${tableName} ORDER BY ${keyField} LIMIT ${sampleSize}`;
        }
        return `SELECT * FROM ${tableName} LIMIT ${sampleSize}`;
    }

    /**
     * 获取表的关键字段（通常是主键）
     * @param {string} tableName - 表名
     * @returns {string|null} 关键字段名
     */
    getKeyField(tableName) {
        const keyFields = {
            'groups': 'id',
            'servers': 'sid',
            'traffic': 'sid',
            'lt': 'sid',
            'autodiscovery_servers': 'id',
            'ai_reports': 'id',
            'load_archive': 'id',
            'load_h': 'id',
            'load_m': 'id',
            'tcping_5m': 'id',
            'tcping_archive': 'id',
            'tcping_d': 'id',
            'tcping_h': 'id',
            'tcping_m': 'id',
            'tcping_month': 'id',
            'setting': 'key',
            'ssh_scripts': 'id',
            'monitor_regions': 'id',
            'monitor_targets': 'id',
            'db_migrations': 'version'
        };
        return keyFields[tableName] || null;
    }

    /**
     * 验证所有表
     * @returns {Object} 全面验证结果
     */
    async verifyAllTables() {
        console.log('[验证] 开始全面验证...');
        
        const tables = [
            'groups', 'servers', 'traffic', 'lt', 'autodiscovery_servers',
            'ai_reports', 'load_archive', 'load_h', 'load_m', 'tcping_5m',
            'tcping_archive', 'tcping_d', 'tcping_h', 'tcping_m', 'tcping_month',
            'setting', 'ssh_scripts', 'monitor_regions', 'monitor_targets', 'db_migrations'
        ];

        const results = [];
        let totalSourceRecords = 0;
        let totalTargetRecords = 0;
        let passedTables = 0;
        let failedTables = 0;

        for (const table of tables) {
            const result = await this.verifyTable(table);
            results.push(result);

            if (result.status === 'PASS') {
                passedTables++;
                totalSourceRecords += result.sourceCount || 0;
                totalTargetRecords += result.targetCount || 0;
            } else if (result.status === 'FAIL') {
                failedTables++;
            }

            // 添加小延迟以避免过载
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        const summary = {
            totalTables: tables.length,
            passedTables,
            failedTables,
            errorTables: results.filter(r => r.status === 'ERROR').length,
            totalSourceRecords,
            totalTargetRecords,
            overallStatus: passedTables === tables.length ? 'PASS' : 'FAIL',
            completionTimestamp: new Date().toISOString()
        };

        console.log('[验证] ========== 验证结果汇总 ==========');
        console.log(`[验证] 总表数: ${summary.totalTables}`);
        console.log(`[验证] 通过: ${summary.passedTables}`);
        console.log(`[验证] 失败: ${summary.failedTables}`);
        console.log(`[验证] 错误: ${summary.errorTables}`);
        console.log(`[验证] 总源记录数: ${summary.totalSourceRecords.toLocaleString()}`);
        console.log(`[验证] 总目标记录数: ${summary.totalTargetRecords.toLocaleString()}`);
        console.log(`[验证] 整体状态: ${summary.overallStatus}`);

        return {
            summary,
            tableResults: results
        };
    }

    /**
     * 生成验证报告
     * @param {Object} verificationResults - 验证结果
     * @returns {string} 报告内容
     */
    generateReport(verificationResults) {
        const { summary, tableResults } = verificationResults;
        
        let report = '# 数据库迁移验证报告\n\n';
        report += `**验证时间**: ${summary.completionTimestamp}\n\n`;
        report += `## 汇总结果\n\n`;
        report += `- **总表数**: ${summary.totalTables}\n`;
        report += `- **通过验证**: ${summary.passedTables}\n`;
        report += `- **验证失败**: ${summary.failedTables}\n`;
        report += `- **验证错误**: ${summary.errorTables}\n`;
        report += `- **总源记录数**: ${summary.totalSourceRecords.toLocaleString()}\n`;
        report += `- **总目标记录数**: ${summary.totalTargetRecords.toLocaleString()}\n`;
        report += `- **整体状态**: ${summary.overallStatus}\n\n`;

        report += `## 详细结果\n\n`;
        report += `| 表名 | 状态 | SQLite记录数 | PostgreSQL记录数 | 抽样验证 |\n`;
        report += `|------|------|-------------|----------------|----------|\n`;

        tableResults.forEach(result => {
            const status = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
            const sourceCount = result.sourceCount?.toLocaleString() || 'N/A';
            const targetCount = result.targetCount?.toLocaleString() || 'N/A';
            const sampleInfo = result.sampleVerification?.message || result.error || 'N/A';
            
            report += `| ${result.table} | ${status} ${result.status} | ${sourceCount} | ${targetCount} | ${sampleInfo} |\n`;
        });

        if (summary.failedTables > 0) {
            report += `\n## 失败详情\n\n`;
            tableResults.filter(r => r.status === 'FAIL').forEach(result => {
                report += `### ${result.table}\n`;
                if (result.sourceCount !== result.targetCount) {
                    report += `- **记录数不匹配**: SQLite ${result.sourceCount}, PostgreSQL ${result.targetCount}\n`;
                }
                if (result.sampleVerification && !result.sampleVerification.passed) {
                    report += `- **抽样验证失败**: ${result.sampleVerification.message}\n`;
                }
                report += '\n';
            });
        }

        return report;
    }
}

// 主函数
async function main() {
    const verifier = new MigrationVerifier();
    
    try {
        await verifier.initialize();
        const results = await verifier.verifyAllTables();
        
        // 生成并保存报告
        const report = verifier.generateReport(results);
        const fs = require('fs');
        const path = require('path');
        
        const reportPath = path.join(__dirname, '../../../agent/migration_verification_report.md');
        fs.writeFileSync(reportPath, report);
        
        console.log(`[验证] 验证报告已保存至: ${reportPath}`);
        
        return results;
        
    } catch (error) {
        console.error('[验证] 验证过程出错:', error);
        throw error;
    } finally {
        await verifier.cleanup();
    }
}

// 导出模块
module.exports = MigrationVerifier;

// 如果直接运行此脚本
if (require.main === module) {
    main()
        .then(results => {
            console.log('[验证] 验证完成');
            process.exit(results.summary.overallStatus === 'PASS' ? 0 : 1);
        })
        .catch(error => {
            console.error('[验证] 验证失败:', error);
            process.exit(1);
        });
}