#!/usr/bin/env node
"use strict";

const dbConfig = require('../config');
const SQLiteAdapter = require('../adapters/sqlite');

async function checkSidDuplicates() {
    const sqliteAdapter = new SQLiteAdapter(dbConfig.sqlite);
    
    try {
        await sqliteAdapter.connect();
        
        console.log('[检查] 检查SQLite load_h表中的sid重复情况...');
        
        // 检查总记录数
        const totalCount = await sqliteAdapter.query(`SELECT COUNT(*) as count FROM load_h`);
        console.log(`[检查] 总记录数: ${totalCount[0].count}`);
        
        // 检查唯一sid数量
        const uniqueSidCount = await sqliteAdapter.query(`SELECT COUNT(DISTINCT sid) as count FROM load_h`);
        console.log(`[检查] 唯一sid数量: ${uniqueSidCount[0].count}`);
        
        // 如果数量不一致，说明有重复
        if (totalCount[0].count !== uniqueSidCount[0].count) {
            console.log(`[检查] ⚠️  发现重复的sid！`);
            console.log(`[检查] 重复数量: ${totalCount[0].count - uniqueSidCount[0].count}`);
            
            // 找出重复的sid
            const duplicates = await sqliteAdapter.query(`
                SELECT sid, COUNT(*) as count 
                FROM load_h 
                GROUP BY sid 
                HAVING COUNT(*) > 1 
                ORDER BY count DESC
                LIMIT 10
            `);
            
            console.log('[检查] 重复的sid (前10个):');
            duplicates.forEach(dup => {
                console.log(`  SID: ${dup.sid}, 出现次数: ${dup.count}`);
            });
            
            // 查看某个重复sid的详细信息
            if (duplicates.length > 0) {
                const sampleSid = duplicates[0].sid;
                const sampleRecords = await sqliteAdapter.query(`
                    SELECT id, sid, cpu, created_at 
                    FROM load_h 
                    WHERE sid = ? 
                    ORDER BY created_at 
                    LIMIT 5
                `, [sampleSid]);
                
                console.log(`[检查] SID ${sampleSid} 的详细记录:`);
                sampleRecords.forEach(record => {
                    console.log(`  ID: ${record.id}, 创建时间: ${record.created_at}, CPU: ${record.cpu}`);
                });
            }
        } else {
            console.log('[检查] ✅ 没有发现重复的sid');
        }
        
        // 检查是否有null或空的sid
        const nullSidCount = await sqliteAdapter.query(`
            SELECT COUNT(*) as count 
            FROM load_h 
            WHERE sid IS NULL OR sid = ''
        `);
        console.log(`[检查] NULL或空的sid数量: ${nullSidCount[0].count}`);
        
        // 检查id重复情况
        const uniqueIdCount = await sqliteAdapter.query(`SELECT COUNT(DISTINCT id) as count FROM load_h`);
        console.log(`[检查] 唯一id数量: ${uniqueIdCount[0].count}`);
        
        if (totalCount[0].count !== uniqueIdCount[0].count) {
            console.log(`[检查] ⚠️  发现重复的id！`);
            
            const duplicateIds = await sqliteAdapter.query(`
                SELECT id, COUNT(*) as count 
                FROM load_h 
                GROUP BY id 
                HAVING COUNT(*) > 1 
                ORDER BY count DESC
                LIMIT 5
            `);
            
            console.log('[检查] 重复的id (前5个):');
            duplicateIds.forEach(dup => {
                console.log(`  ID: ${dup.id}, 出现次数: ${dup.count}`);
            });
        } else {
            console.log('[检查] ✅ 没有发现重复的id');
        }
        
    } catch (error) {
        console.error('[检查] 失败:', error.message);
    } finally {
        await sqliteAdapter.disconnect();
    }
}

checkSidDuplicates();