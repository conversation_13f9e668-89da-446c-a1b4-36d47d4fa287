#!/usr/bin/env node
"use strict";

const dbConfig = require('../config');
const PostgreSQLAdapter = require('../adapters/postgresql');

async function fixLoadTableSchema() {
    const pgAdapter = new PostgreSQLAdapter(dbConfig.postgresql);
    
    try {
        await pgAdapter.connect();
        
        console.log('[修复] 开始修复PostgreSQL load表结构...');
        
        const tables = ['load_h', 'load_m'];
        
        for (const table of tables) {
            console.log(`\n[修复] ========== 修复 ${table} 表 ==========`);
            
            // 1. 检查当前表结构
            const currentStructure = await pgAdapter.query(`
                SELECT 
                    tc.constraint_name, 
                    tc.constraint_type, 
                    kcu.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu 
                    ON tc.constraint_name = kcu.constraint_name 
                    AND tc.table_schema = kcu.table_schema
                WHERE tc.table_name = $1 AND tc.constraint_type = 'PRIMARY KEY'
            `, [table]);
            
            console.log(`[修复] 当前 ${table} 主键:`);
            currentStructure.forEach(c => {
                console.log(`  列: ${c.column_name}, 约束: ${c.constraint_name}`);
            });
            
            // 2. 删除现有的主键约束
            if (currentStructure.length > 0) {
                const constraintName = currentStructure[0].constraint_name;
                console.log(`[修复] 删除现有主键约束: ${constraintName}`);
                await pgAdapter.run(`ALTER TABLE ${table} DROP CONSTRAINT ${constraintName}`);
            }
            
            // 3. 确保id列存在且类型正确
            const idColumn = await pgAdapter.query(`
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = $1 AND column_name = 'id'
            `, [table]);
            
            if (idColumn.length === 0) {
                console.log(`[修复] 添加id列到 ${table} 表`);
                await pgAdapter.run(`ALTER TABLE ${table} ADD COLUMN id BIGSERIAL`);
            } else {
                console.log(`[修复] id列已存在，类型: ${idColumn[0].data_type}`);
            }
            
            // 4. 添加新的主键约束
            console.log(`[修复] 添加新的主键约束到 ${table}.id`);
            await pgAdapter.run(`ALTER TABLE ${table} ADD CONSTRAINT ${table}_pkey PRIMARY KEY (id)`);
            
            // 5. 创建sid索引（如果不存在）
            console.log(`[修复] 创建 ${table}.sid 索引`);
            try {
                await pgAdapter.run(`CREATE INDEX IF NOT EXISTS idx_${table}_sid ON ${table} (sid)`);
            } catch (error) {
                console.log(`[修复] 索引可能已存在: ${error.message}`);
            }
            
            // 6. 创建复合索引（如果不存在）
            console.log(`[修复] 创建 ${table} 复合索引`);
            try {
                await pgAdapter.run(`CREATE INDEX IF NOT EXISTS idx_${table}_sid_time ON ${table} (sid, created_at)`);
                await pgAdapter.run(`CREATE INDEX IF NOT EXISTS idx_${table}_created_at ON ${table} (created_at)`);
            } catch (error) {
                console.log(`[修复] 索引可能已存在: ${error.message}`);
            }
            
            // 7. 验证修复结果
            const newStructure = await pgAdapter.query(`
                SELECT 
                    tc.constraint_name, 
                    tc.constraint_type, 
                    kcu.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu 
                    ON tc.constraint_name = kcu.constraint_name 
                    AND tc.table_schema = kcu.table_schema
                WHERE tc.table_name = $1 AND tc.constraint_type = 'PRIMARY KEY'
            `, [table]);
            
            console.log(`[修复] 修复后 ${table} 主键:`);
            newStructure.forEach(c => {
                console.log(`  列: ${c.column_name}, 约束: ${c.constraint_name}`);
            });
            
            console.log(`[修复] ✅ ${table} 表结构修复完成`);
        }
        
        console.log('\n[修复] ✅ 所有load表结构修复完成！');
        
    } catch (error) {
        console.error('[修复] 失败:', error.message);
        console.error('[修复] 详细错误:', error);
    } finally {
        await pgAdapter.disconnect();
    }
}

fixLoadTableSchema();