#!/usr/bin/env node
"use strict";

const dbConfig = require('../config');
const PostgreSQLAdapter = require('../adapters/postgresql');

async function checkConstraints() {
    const pgAdapter = new PostgreSQLAdapter(dbConfig.postgresql);
    
    try {
        await pgAdapter.connect();
        
        console.log('[检查] 查看 load_h 表的所有约束...');
        
        // 查看详细的约束信息
        const constraints = await pgAdapter.query(`
            SELECT 
                tc.constraint_name, 
                tc.constraint_type, 
                kcu.column_name,
                tc.table_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name 
                AND tc.table_schema = kcu.table_schema
            WHERE tc.table_name = 'load_h'
            ORDER BY tc.constraint_type, kcu.ordinal_position
        `);
        
        console.log('[检查] load_h 表约束详情:');
        constraints.forEach(c => {
            console.log(`  约束名: ${c.constraint_name}`);
            console.log(`  类型: ${c.constraint_type}`);
            console.log(`  列: ${c.column_name}`);
            console.log(`  ---`);
        });
        
        // 检查索引
        console.log('[检查] 查看 load_h 表的索引...');
        const indexes = await pgAdapter.query(`
            SELECT 
                indexname, 
                indexdef 
            FROM pg_indexes 
            WHERE tablename = 'load_h'
        `);
        
        console.log('[检查] load_h 表索引:');
        indexes.forEach(idx => {
            console.log(`  索引名: ${idx.indexname}`);
            console.log(`  定义: ${idx.indexdef}`);
            console.log(`  ---`);
        });
        
        // 尝试不同的ON CONFLICT语法
        console.log('[检查] 测试不同的ON CONFLICT语法...');
        
        // 测试1: 使用约束名
        if (constraints.length > 0) {
            const pkConstraint = constraints.find(c => c.constraint_type === 'PRIMARY KEY');
            if (pkConstraint) {
                console.log(`[测试] 尝试使用约束名: ${pkConstraint.constraint_name}`);
                try {
                    await pgAdapter.run(`
                        INSERT INTO load_h (id, sid, cpu, mem, swap, ibw, obw, expire_time, created_at) 
                        VALUES (99999, 'test', 0, 0, 0, 0, 0, 0, 0)
                        ON CONFLICT ON CONSTRAINT ${pkConstraint.constraint_name} DO NOTHING
                    `);
                    console.log('[测试] ✅ 使用约束名的语法成功');
                } catch (error) {
                    console.log(`[测试] ❌ 使用约束名失败: ${error.message}`);
                }
            }
        }
        
        // 测试2: 使用列名
        try {
            await pgAdapter.run(`
                INSERT INTO load_h (id, sid, cpu, mem, swap, ibw, obw, expire_time, created_at) 
                VALUES (99998, 'test2', 0, 0, 0, 0, 0, 0, 0)
                ON CONFLICT (id) DO NOTHING
            `);
            console.log('[测试] ✅ 使用列名的语法成功');
        } catch (error) {
            console.log(`[测试] ❌ 使用列名失败: ${error.message}`);
        }
        
        // 清理测试数据
        await pgAdapter.run(`DELETE FROM load_h WHERE id IN (99999, 99998)`);
        
    } catch (error) {
        console.error('[检查] 失败:', error.message);
    } finally {
        await pgAdapter.disconnect();
    }
}

checkConstraints();