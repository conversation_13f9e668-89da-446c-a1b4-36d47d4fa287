#!/usr/bin/env node
"use strict";

const dbConfig = require('../config');
const PostgreSQLAdapter = require('../adapters/postgresql');
const SQLiteAdapter = require('../adapters/sqlite');

async function debugLoadH() {
    const pgAdapter = new PostgreSQLAdapter(dbConfig.postgresql);
    const sqliteAdapter = new SQLiteAdapter(dbConfig.sqlite);
    
    try {
        await pgAdapter.connect();
        await sqliteAdapter.connect();
        
        console.log('[调试] 检查 load_h 表结构...');
        
        // 检查PostgreSQL中的load_h表结构
        const pgSchema = await pgAdapter.query(`
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'load_h'
            ORDER BY ordinal_position
        `);
        
        console.log('[调试] PostgreSQL load_h 表结构:');
        pgSchema.forEach(col => {
            console.log(`  ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : ''} ${col.column_default || ''}`);
        });
        
        // 检查约束
        const constraints = await pgAdapter.query(`
            SELECT constraint_name, constraint_type 
            FROM information_schema.table_constraints 
            WHERE table_name = 'load_h'
        `);
        
        console.log('[调试] PostgreSQL load_h 表约束:');
        constraints.forEach(c => {
            console.log(`  ${c.constraint_name}: ${c.constraint_type}`);
        });
        
        // 检查序列
        const sequences = await pgAdapter.query(`
            SELECT sequencename, last_value 
            FROM pg_sequences 
            WHERE sequencename LIKE 'load_h%'
        `);
        
        console.log('[调试] PostgreSQL load_h 相关序列:');
        sequences.forEach(s => {
            console.log(`  ${s.sequencename}: ${s.last_value}`);
        });
        
        // 检查表的完整定义
        const tableDefinition = await pgAdapter.query(`
            SELECT 
                column_name, 
                data_type, 
                is_nullable, 
                column_default,
                is_identity,
                identity_generation
            FROM information_schema.columns 
            WHERE table_name = 'load_h' AND column_name = 'id'
        `);
        
        console.log('[调试] PostgreSQL load_h 表 id 列详细信息:');
        tableDefinition.forEach(col => {
            console.log(`  ${col.column_name}: ${col.data_type}, default: ${col.column_default}, identity: ${col.is_identity}`);
        });
        
        // 检查表中的数据
        const count = await pgAdapter.query(`SELECT COUNT(*) as count FROM load_h`);
        console.log(`[调试] PostgreSQL load_h 表现有数据: ${count[0].count} 条`);
        
        if (count[0].count > 0) {
            const sample = await pgAdapter.query(`SELECT id FROM load_h ORDER BY id LIMIT 5`);
            console.log('[调试] PostgreSQL load_h 表前5个ID:');
            sample.forEach(row => {
                console.log(`  ID: ${row.id}`);
            });
        }
        
        // 检查SQLite中的load_h表数据
        const sqliteCount = await sqliteAdapter.query(`SELECT COUNT(*) as count FROM load_h`);
        console.log(`[调试] SQLite load_h 表数据: ${sqliteCount[0].count} 条`);
        
        if (sqliteCount[0].count > 0) {
            const sqliteSample = await sqliteAdapter.query(`SELECT id FROM load_h ORDER BY id LIMIT 5`);
            console.log('[调试] SQLite load_h 表前5个ID:');
            sqliteSample.forEach(row => {
                console.log(`  ID: ${row.id}`);
            });
        }
        
    } catch (error) {
        console.error('[调试] 失败:', error.message);
    } finally {
        await pgAdapter.disconnect();
        await sqliteAdapter.disconnect();
    }
}

debugLoadH();