'use strict';

/**
 * 数据库迁移配置
 * 支持环境变量覆盖默认值
 */

module.exports = {
  // 迁移核心配置
  migration: {
    // 批次大小 - 优化: 100 → 500 (减少网络往返次数)
    batchSize: parseInt(process.env.MIGRATION_BATCH_SIZE) || 500,
    
    // 重试配置
    retry: {
      maxAttempts: parseInt(process.env.MIGRATION_MAX_RETRIES) || 5,
      initialDelay: parseInt(process.env.MIGRATION_RETRY_DELAY) || 2000,
      maxDelay: parseInt(process.env.MIGRATION_MAX_DELAY) || 60000,
      backoffMultiplier: parseFloat(process.env.MIGRATION_BACKOFF_MULTIPLIER) || 2
    },
    
    // 连接配置
    connection: {
      timeout: parseInt(process.env.MIGRATION_CONNECT_TIMEOUT) || 30000,
      keepAlive: process.env.MIGRATION_KEEP_ALIVE !== 'false'
    },
    
    // 自适应批次大小
    adaptiveBatch: {
      enabled: process.env.MIGRATION_ADAPTIVE_BATCH !== 'false',
      minSize: parseInt(process.env.MIGRATION_MIN_BATCH) || 10,
      maxSize: parseInt(process.env.MIGRATION_MAX_BATCH) || 1000,
      errorThreshold: parseFloat(process.env.MIGRATION_ERROR_THRESHOLD) || 0.1,
      adjustmentFactor: parseFloat(process.env.MIGRATION_ADJUSTMENT_FACTOR) || 0.5
    },
    
    // 验证配置
    validation: {
      enabled: process.env.MIGRATION_VALIDATION !== 'false',
      sampleSize: parseInt(process.env.MIGRATION_SAMPLE_SIZE) || 100,
      checksumEnabled: process.env.MIGRATION_CHECKSUM === 'true'
    }
  },
  
  // 日志配置
  logging: {
    level: process.env.MIGRATION_LOG_LEVEL || 'info',
    showProgress: process.env.MIGRATION_SHOW_PROGRESS !== 'false',
    progressInterval: parseInt(process.env.MIGRATION_PROGRESS_INTERVAL) || 5000
  },
  
  // 获取动态调整后的批次大小
  getAdjustedBatchSize(currentSize, errorRate) {
    const config = this.migration.adaptiveBatch;
    if (!config.enabled) return currentSize;
    
    if (errorRate > config.errorThreshold) {
      // 错误率高，减小批次
      const newSize = Math.floor(currentSize * config.adjustmentFactor);
      return Math.max(config.minSize, newSize);
    } else if (errorRate < config.errorThreshold * 0.1) {
      // 错误率很低，增大批次
      const newSize = Math.floor(currentSize * (2 - config.adjustmentFactor));
      return Math.min(config.maxSize, newSize);
    }
    
    return currentSize;
  }
};