#!/usr/bin/env node
"use strict";

/**
 * 自动迁移管理器
 * 负责SQLite到PostgreSQL的无感自动迁移
 */

const CompleteMigration = require('./migrate');
const PostgreSQLAdapter = require('../adapters/postgresql');

class AutoMigrationManager {
    constructor(db) {
        this.db = db;
        this.migrationStatus = {
            isRunning: false,
            progress: 0,
            currentTable: '',
            error: null,
            startTime: null,
            endTime: null
        };
    }

    /**
     * 触发自动迁移
     * @param {Object} pgConfig PostgreSQL配置
     */
    async triggerAutoMigration(pgConfig) {
        if (this.migrationStatus.isRunning) {
            console.log('[自动迁移] 迁移已在进行中，跳过重复触发');
            return;
        }

        console.log('[自动迁移] 开始自动迁移任务...');
        
        try {
            // 1. 设置迁移状态
            await this.setMigrationStatus('preparing', '准备迁移...');
            
            // 2. 验证PostgreSQL连接
            await this.testPostgreSQLConnection(pgConfig);
            
            // 3. 后台执行迁移
            setImmediate(() => this.executeMigration(pgConfig));
            
            // 4. 启动进度监控
            this.startProgressReporting();
            
            console.log('[自动迁移] 迁移任务已启动');
            
        } catch (error) {
            console.error('[自动迁移] 启动迁移失败:', error.message);
            await this.setMigrationStatus('failed', `启动失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 执行迁移任务
     * @param {Object} pgConfig PostgreSQL配置
     */
    async executeMigration(pgConfig) {
        try {
            this.migrationStatus.isRunning = true;
            this.migrationStatus.startTime = new Date().toISOString();
            
            await this.setMigrationStatus('running', '正在迁移数据...');
            
            // 1. 创建迁移实例
            const migration = new CompleteMigration();
            
            // 2. 设置PostgreSQL配置
            migration.postgresConfig = {
                connection: this.buildConnectionString(pgConfig)
            };
            
            // 3. 执行完整迁移
            console.log('[自动迁移] 开始执行数据迁移...');
            const migratedCount = await migration.run();
            
            console.log(`[自动迁移] 迁移完成，共迁移 ${migratedCount} 条记录`);
            
            // 4. 设置完成状态
            await this.setMigrationStatus('completed', `迁移完成，共 ${migratedCount} 条记录`);
            
            // 5. 触发热切换
            await this.triggerHotSwitch(pgConfig);
            
            // 6. 通知前端
            this.notifyMigrationCompleted(migratedCount);
            
        } catch (error) {
            console.error('[自动迁移] 迁移执行失败:', error.message);
            await this.setMigrationStatus('failed', `迁移失败: ${error.message}`);
            this.notifyMigrationFailed(error);
        } finally {
            this.migrationStatus.isRunning = false;
            this.migrationStatus.endTime = new Date().toISOString();
        }
    }

    /**
     * 测试PostgreSQL连接
     * @param {Object} pgConfig PostgreSQL配置
     */
    async testPostgreSQLConnection(pgConfig) {
        const connectionString = this.buildConnectionString(pgConfig);
        const testAdapter = new PostgreSQLAdapter({ connection: connectionString });
        
        try {
            await testAdapter.connect();
            console.log('[自动迁移] PostgreSQL连接测试成功');
            await testAdapter.disconnect();
        } catch (error) {
            console.error('[自动迁移] PostgreSQL连接测试失败:', error.message);
            throw new Error(`PostgreSQL连接测试失败: ${error.message}`);
        }
    }

    /**
     * 构建PostgreSQL连接字符串
     * @param {Object} pgConfig PostgreSQL配置
     * @returns {string} 连接字符串
     */
    buildConnectionString(pgConfig) {
        const { host, port, database, username, password } = pgConfig;
        return `postgresql://${username}:${password}@${host}:${port}/${database}`;
    }

    /**
     * 设置迁移状态
     * @param {string} status 状态：preparing/running/completed/failed
     * @param {string} message 状态消息
     */
    async setMigrationStatus(status, message) {
        try {
            await this.db.setting.set('migration_status', status);
            await this.db.setting.set('migration_message', message);
            await this.db.setting.set('migration_last_update', new Date().toISOString());
            
            console.log(`[自动迁移] 状态更新: ${status} - ${message}`);
            
            // 实时推送状态到前端
            this.notifyStatusUpdate(status, message);
            
        } catch (error) {
            console.error('[自动迁移] 状态更新失败:', error.message);
        }
    }

    /**
     * 启动进度监控
     */
    startProgressReporting() {
        this.progressInterval = setInterval(async () => {
            if (!this.migrationStatus.isRunning) {
                clearInterval(this.progressInterval);
                return;
            }
            
            try {
                // 获取当前迁移进度（这里可以与CompleteMigration类集成）
                const status = await this.getMigrationProgress();
                this.notifyProgressUpdate(status);
                
            } catch (error) {
                console.error('[自动迁移] 进度报告失败:', error.message);
            }
        }, 2000); // 每2秒更新一次进度
    }

    /**
     * 获取迁移进度
     * @returns {Object} 进度信息
     */
    async getMigrationProgress() {
        // 从setting表获取进度信息
        const status = await this.db.setting.get('migration_status') || 'unknown';
        const message = await this.db.setting.get('migration_message') || '';
        const progress = await this.db.setting.get('migration_progress') || 0;
        
        return {
            status,
            message,
            progress: parseInt(progress),
            isRunning: this.migrationStatus.isRunning,
            startTime: this.migrationStatus.startTime
        };
    }

    /**
     * 触发热切换
     * @param {Object} pgConfig PostgreSQL配置
     */
    async triggerHotSwitch(pgConfig) {
        try {
            console.log('[自动迁移] 准备执行热切换...');
            
            // 调用数据库模块的热切换功能
            const dbIndex = require('../index');
            if (dbIndex.performHotSwitch) {
                await dbIndex.performHotSwitch(pgConfig);
                console.log('[自动迁移] 热切换完成');
            } else {
                console.log('[自动迁移] 热切换功能未就绪，需要重启应用');
                await this.db.setting.set('postgres_switch_pending', 'true');
            }
            
        } catch (error) {
            console.error('[自动迁移] 热切换失败:', error.message);
            await this.db.setting.set('postgres_switch_pending', 'true');
        }
    }

    /**
     * 通知状态更新 (简化版)
     * @param {string} status 状态
     * @param {string} message 消息
     */
    notifyStatusUpdate(status, message) {
        // 仅记录日志，状态通过数据库setting表共享
        console.log(`[自动迁移] 状态更新: ${status} - ${message}`);
    }

    /**
     * 通知进度更新 (简化版)
     * @param {Object} progressInfo 进度信息
     */
    notifyProgressUpdate(progressInfo) {
        // 仅记录日志
        console.log(`[自动迁移] 进度: ${progressInfo.progress || 0}%`);
    }

    /**
     * 通知迁移完成 (简化版)
     * @param {number} migratedCount 迁移记录数
     */
    notifyMigrationCompleted(migratedCount) {
        console.log(`[自动迁移] 迁移完成，共迁移 ${migratedCount} 条记录`);
    }

    /**
     * 通知迁移失败 (简化版)
     * @param {Error} error 错误信息
     */
    notifyMigrationFailed(error) {
        console.log(`[自动迁移] 迁移失败: ${error.message}`);
    }

    /**
     * 获取迁移状态
     * @returns {Object} 当前迁移状态
     */
    async getStatus() {
        const status = await this.db.setting.get('migration_status') || 'idle';
        const message = await this.db.setting.get('migration_message') || '';
        const progress = await this.db.setting.get('migration_progress') || 0;
        const lastUpdate = await this.db.setting.get('migration_last_update') || '';
        
        return {
            status,
            message,
            progress: parseInt(progress),
            isRunning: this.migrationStatus.isRunning,
            startTime: this.migrationStatus.startTime,
            endTime: this.migrationStatus.endTime,
            lastUpdate
        };
    }
}

module.exports = AutoMigrationManager;