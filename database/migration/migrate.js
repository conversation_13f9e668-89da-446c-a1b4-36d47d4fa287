#!/usr/bin/env node
"use strict";

/**
 * 完整数据迁移脚本
 * 从SQLite迁移所有104,510条记录到PostgreSQL
 */


// 引入数据库配置和适配器
const dbConfig = require('../config');
const migrationConfig = require('./config');
const SQLiteAdapter = require('../adapters/sqlite');
const PostgreSQLAdapter = require('../adapters/postgresql');
const fs = require('fs');
const path = require('path');

// 引入迁移工具
const DataExporter = require('./exporter');
const DataImporter = require('./importer');
const TableOptimizer = require('./table-optimizer');

class CompleteMigration {
    constructor() {
        this.sourceAdapter = null;
        this.targetAdapter = null;
        this.exporter = null;
        this.importer = null;
        this.migrationStats = {};
        this.tableOptimizer = new TableOptimizer(); // 性能优化器
        this.optimizationReport = null;
        this.progressLogPath = path.join(process.cwd(), 'logs', 'migration-progress.log');
    }

    /**
     * 写入迁移进度日志
     * @param {number} progress - 进度百分比 (0-100)
     * @param {string} message - 进度消息
     * @param {string} details - 详细信息 (可选)
     */
    writeProgressLog(progress, message, details = '') {
        try {
            // 确保logs目录存在
            const logsDir = path.dirname(this.progressLogPath);
            if (!fs.existsSync(logsDir)) {
                fs.mkdirSync(logsDir, { recursive: true });
            }

            const timestamp = new Date().toISOString();
            const logLine = `[${timestamp}] PROGRESS: ${progress}% - ${message}${details ? ' (' + details + ')' : ''}\n`;
            
            fs.appendFileSync(this.progressLogPath, logLine);
            console.log(`[迁移进度] ${progress}% - ${message}`);
        } catch (error) {
            console.error('[迁移进度] 写入进度日志失败:', error.message);
        }
    }

    /**
     * 清理旧的进度日志文件
     */
    clearProgressLog() {
        try {
            if (fs.existsSync(this.progressLogPath)) {
                fs.unlinkSync(this.progressLogPath);
            }
        } catch (error) {
            console.error('[迁移进度] 清理旧日志失败:', error.message);
        }
    }

    async initialize() {
        console.log('[迁移] 初始化数据库连接...');
        
        // 初始化SQLite（源）
        this.sourceAdapter = new SQLiteAdapter(dbConfig.sqlite);
        await this.sourceAdapter.connect();
        console.log('[数据库] SQLite 连接成功');
        
        // 初始化PostgreSQL（目标）
        // 优先使用自动迁移管理器注入的配置，否则使用默认配置
        const targetConfig = this.postgresConfig || dbConfig.postgresql;
        this.targetAdapter = new PostgreSQLAdapter(targetConfig);
        await this.connectWithRetry();
        console.log('[数据库] PostgreSQL 连接成功');
        
        // 初始化导出器和导入器
        this.exporter = new DataExporter(this.sourceAdapter);
        this.importer = new DataImporter(this.targetAdapter);
        this.importer.setBatchSize(migrationConfig.migration.batchSize); // 使用配置的批量大小
    }

    async connectWithRetry() {
        const { maxAttempts, initialDelay } = migrationConfig.migration.retry;
        for (let i = 0; i < maxAttempts; i++) {
            try {
                await this.targetAdapter.connect();
                return;
            } catch (error) {
                console.log(`[连接] PostgreSQL 连接失败 (尝试 ${i + 1}/${maxAttempts}): ${error.message}`);
                if (i === maxAttempts - 1) throw error;
                await this.sleep(initialDelay); // 使用配置的延迟
            }
        }
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async migrateWithRetry(table, importMethod, dataStream) {
        const { maxAttempts } = migrationConfig.migration.retry;
        for (let i = 0; i < maxAttempts; i++) {
            try {
                return await this.importer[importMethod](dataStream);
            } catch (error) {
                if (error.message.includes('Connection terminated') || 
                    error.message.includes('connection') || 
                    error.message.includes('ECONNRESET') ||
                    error.message.includes('ENOTFOUND') ||
                    error.message.includes('timeout')) {
                    console.log(`[重试] 表 ${table} 连接错误 (尝试 ${i + 1}/${maxAttempts}): ${error.message}`);
                    
                    if (i === maxAttempts - 1) throw error;
                    
                    // 渐进式重连等待时间
                    const { initialDelay, backoffMultiplier, maxDelay } = migrationConfig.migration.retry;
                    const waitTime = Math.min(initialDelay * Math.pow(backoffMultiplier, i), maxDelay);
                    console.log(`[重试] 等待 ${waitTime/1000} 秒后重新连接...`);
                    await this.sleep(waitTime);
                    
                    // 重新连接数据库
                    try {
                        await this.targetAdapter.disconnect();
                    } catch (e) {
                        // 忽略断开连接的错误
                    }
                    
                    await this.connectWithRetry();
                    
                    // 重新创建导入器
                    this.importer = new DataImporter(this.targetAdapter);
                    this.importer.setBatchSize(migrationConfig.migration.batchSize);
                    
                    // 重新创建数据流
                    const exportMethod = this.getExportMethodName(table);
                    dataStream = this.exporter[exportMethod]();
                    
                    continue;
                }
                throw error;
            }
        }
    }

    async migrateAllTables() {
        console.log('[迁移] 开始完整数据迁移...');
        
        // 清理旧的进度日志
        this.clearProgressLog();
        
        // 所有需要迁移的表
        const tables = [
            'groups', 'servers', 'traffic', 'lt', 'traffic_calibration',
            'autodiscovery_servers', 'ai_reports', 'load_archive', 'load_h', 'load_m',
            'tcping_5m', 'tcping_archive', 'tcping_d', 'tcping_h', 'tcping_m', 'tcping_month',
            'setting', 'ssh_scripts', 'monitor_regions', 'monitor_targets', 'db_migrations'
        ];

        const totalTables = tables.length;
        this.writeProgressLog(0, '开始数据迁移', `总共${totalTables}个表`);

        // 🚀 性能优化: 预分析所有表特征
        console.log('[迁移] 分析表特征，优化迁移策略...');
        const tableAnalyses = await this.analyzeAllTables(tables);
        this.optimizationReport = this.tableOptimizer.generateOptimizationReport(tableAnalyses);
        
        // 输出优化报告
        console.log(`[迁移] 优化分析完成:`);
        console.log(`  • 预计总时间: ${this.optimizationReport.summary.totalTimeFormatted}`);
        console.log(`  • 预计吞吐量: ${this.optimizationReport.summary.averageThroughput} 行/秒`);
        console.log(`  • 总批次数: ${this.optimizationReport.summary.totalBatches}`);
        console.log(`  • 总事务数: ${this.optimizationReport.summary.totalTransactions}`);

        let totalMigrated = 0;
        let completedTables = 0;
        
        for (const table of tables) {
            try {
                // 🚀 性能优化: 获取表的优化策略
                const tableAnalysis = tableAnalyses.find(t => t.tableName === table);
                if (tableAnalysis) {
                    console.log(`[迁移] 开始迁移表 ${table} (${tableAnalysis.category}表, ${tableAnalysis.rowCount}条记录)...`);
                    
                    // 动态调整批量大小
                    const originalBatchSize = this.importer.batchSize;
                    this.importer.setBatchSize(tableAnalysis.strategy.batchSize);
                    
                    console.log(`[迁移] 表 ${table} 策略: 批量=${tableAnalysis.strategy.batchSize}, 事务批次=${tableAnalysis.strategy.transactionBatches}`);
                } else {
                    console.log(`[迁移] 开始迁移表 ${table}...`);
                }
                
                // 获取导出方法名
                const exportMethod = this.getExportMethodName(table);
                const importMethod = this.getImportMethodName(table);
                
                // 检查方法是否存在
                if (!this.exporter[exportMethod]) {
                    throw new Error(`导出方法 ${exportMethod} 不存在`);
                }
                if (!this.importer[importMethod]) {
                    throw new Error(`导入方法 ${importMethod} 不存在`);
                }
                
                // 检查源表是否有数据
                const sourceCount = await this.exporter.getTableCount(table);
                if (sourceCount === 0) {
                    console.log(`[迁移] 表 ${table} 为空，跳过迁移`);
                    this.migrationStats[table] = 0;
                    continue;
                }
                
                // 清空目标表或检查表状态
                try {
                    await this.importer.truncateTable(table);
                } catch (error) {
                    if (error.message.includes('does not exist')) {
                        // ✅ 修复：创建表而不是跳过
                        console.log(`[迁移] 表 ${table} 不存在，这不应该发生 (表结构应已创建)`);
                        console.log(`[迁移] 跳过表 ${table}，请检查表结构创建过程`);
                        this.migrationStats[table] = 0;
                        continue;
                    } else {
                        // ✅ 新增：支持增量迁移 - 检查表是否有数据
                        console.warn(`[迁移] 无法清空表 ${table}: ${error.message}`);
                        const hasData = await this.checkTableHasData(table);
                        if (hasData) {
                            console.log(`[迁移] 表 ${table} 已有数据，尝试增量迁移（使用ON CONFLICT处理）...`);
                            // 继续执行迁移，依赖导入器的ON CONFLICT机制
                        } else {
                            console.error(`[迁移] 表 ${table} 清空失败且无法确定表状态`);
                            throw error;
                        }
                    }
                }
                
                // 执行迁移（带重试机制和智能优化）
                const dataStream = this.exporter[exportMethod]();
                const migratedCount = await this.migrateWithRetry(table, importMethod, dataStream);
                
                this.migrationStats[table] = migratedCount;
                totalMigrated += migratedCount;
                completedTables++;
                
                // 计算并写入进度
                const progress = Math.round((completedTables / totalTables) * 100);
                this.writeProgressLog(progress, `表 ${table} 迁移完成`, `${migratedCount} 条记录，总计 ${completedTables}/${totalTables}`);
                
                console.log(`[迁移] 表 ${table} 迁移完成，${migratedCount} 条记录`);
                
            } catch (error) {
                console.error(`[迁移] 表 ${table} 迁移失败:`, error.message);
                throw error;
            }
        }
        
        // 写入完成进度
        this.writeProgressLog(100, '数据迁移完成！', `总计 ${totalMigrated} 条记录`);
        
        console.log(`[迁移] 完整迁移完成，总计 ${totalMigrated} 条记录`);
        return totalMigrated;
    }

    /**
     * 分析所有表的特征 - 性能优化预处理
     * @param {Array} tables - 表名数组
     * @returns {Array} 表分析结果数组
     */
    async analyzeAllTables(tables) {
        const analyses = [];
        
        console.log('[优化] 开始表特征分析...');
        for (const table of tables) {
            try {
                // 获取表行数
                const rowCount = await this.exporter.getTableCount(table);
                
                // 使用优化器分析表特征
                const analysis = this.tableOptimizer.analyzeTable(table, rowCount);
                analyses.push(analysis);
                
                // 输出分析结果
                if (rowCount > 0) {
                    console.log(`[优化] 表 ${table}: ${rowCount}条记录 (${analysis.category}), 批量=${analysis.strategy.batchSize}`);
                }
            } catch (error) {
                console.warn(`[优化] 无法分析表 ${table}:`, error.message);
                // 使用默认策略
                analyses.push(this.tableOptimizer.analyzeTable(table, 0));
            }
        }
        
        return analyses;
    }

    getExportMethodName(tableName) {
        // 将表名转换为驼峰命名的导出方法名
        let camelCase = tableName.replace(/_([a-z0-9])/g, (match, letter) => letter.toUpperCase());
        // 处理特殊情况
        if (tableName === 'tcping_5m') {
            camelCase = 'tcping5m';
        }
        const methodName = 'export' + camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
        return methodName;
    }

    getImportMethodName(tableName) {
        // 将表名转换为驼峰命名的导入方法名
        let camelCase = tableName.replace(/_([a-z0-9])/g, (match, letter) => letter.toUpperCase());
        // 处理特殊情况
        if (tableName === 'tcping_5m') {
            camelCase = 'tcping5m';
        }
        const methodName = 'import' + camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
        return methodName;
    }


    /**
     * ✅ 新增：在PostgreSQL上创建表结构
     * 解决迁移脚本假设PostgreSQL已有表结构的问题
     */
    async createPostgreSQLSchema() {
        console.log('[迁移] 检查PostgreSQL表结构...');
        
        try {
            // 检查PostgreSQL迁移状态
            const migrationStatus = await this.checkPostgreSQLMigrationStatus();
            console.log(`[迁移] PostgreSQL当前迁移版本: ${migrationStatus}`);
            
            if (migrationStatus === 0) {
                console.log('[迁移] PostgreSQL是空数据库，开始创建表结构...');
                
                // 创建专用的PostgreSQL数据库实例用于执行migrations.js
                const migrations = require('../migrations')(this.targetAdapter);
                await migrations.migrate();
                
                console.log('[迁移] PostgreSQL表结构创建完成');
            } else {
                console.log(`[迁移] PostgreSQL已有表结构 (版本 ${migrationStatus})，跳过创建`);
                
                // 检查SQLite版本，如果更新则需要增量迁移
                const sqliteVersion = await this.sourceAdapter.get("SELECT MAX(version) as version FROM db_migrations WHERE status = 'success'");
                if (sqliteVersion && sqliteVersion.version > migrationStatus) {
                    console.log(`[迁移] SQLite版本更新 (${sqliteVersion.version} > ${migrationStatus})，需要表结构增量更新`);
                    const migrations = require('../migrations')(this.targetAdapter);
                    await migrations.migrate();
                    console.log('[迁移] 表结构增量更新完成');
                }
            }
        } catch (error) {
            console.error('[迁移] PostgreSQL表结构创建失败:', error.message);
            throw new Error(`表结构创建失败: ${error.message}`);
        }
    }

    /**
     * ✅ 新增：检查PostgreSQL迁移状态
     * 返回已完成的最高迁移版本号
     */
    async checkPostgreSQLMigrationStatus() {
        try {
            const result = await this.targetAdapter.get(
                "SELECT MAX(version) as version FROM db_migrations WHERE status = 'success'"
            );
            return result ? result.version : 0;
        } catch (error) {
            // db_migrations表不存在或查询失败，说明是全新数据库
            console.log('[迁移] db_migrations表不存在，PostgreSQL需要完整初始化');
            return 0;
        }
    }

    /**
     * ✅ 新增：检查表是否有数据（用于增量迁移判断）
     * @param {string} tableName - 表名
     * @returns {boolean} 表是否有数据
     */
    async checkTableHasData(tableName) {
        try {
            const result = await this.targetAdapter.get(
                `SELECT COUNT(*) as count FROM ${tableName} LIMIT 1`
            );
            const hasData = result && result.count > 0;
            console.log(`[迁移] 表 ${tableName} 数据状态: ${hasData ? '有数据' : '空表'} (${result?.count || 0}条记录)`);
            return hasData;
        } catch (error) {
            console.warn(`[迁移] 无法检查表 ${tableName} 的数据状态: ${error.message}`);
            return false; // 保守处理，假设没有数据
        }
    }

    async cleanup() {
        console.log('[迁移] 清理连接...');
        
        if (this.sourceAdapter) {
            await this.sourceAdapter.disconnect();
            console.log('[数据库] SQLite 连接已断开');
        }
        
        if (this.targetAdapter) {
            await this.targetAdapter.disconnect();
            console.log('[数据库] PostgreSQL 连接已断开');
        }
    }

    async run() {
        try {
            await this.initialize();
            
            // ✅ 新增：在PostgreSQL上运行migrations.js创建表结构
            await this.createPostgreSQLSchema();
            
            const totalMigrated = await this.migrateAllTables();
            
            console.log(`\n==========================================`);
            console.log(`✅ 完整数据迁移成功！`);
            console.log(`总计迁移记录数: ${totalMigrated.toLocaleString()}`);
            console.log(`==========================================\n`);
            
            return totalMigrated;
            
        } catch (error) {
            console.error(`\n==========================================`);
            console.error(`❌ 迁移失败: ${error.message}`);
            console.error(`==========================================\n`);
            throw error;
        } finally {
            await this.cleanup();
        }
    }
}

// 直接运行迁移（如果作为脚本执行）
if (require.main === module) {
    const migration = new CompleteMigration();
    migration.run()
        .then(count => {
            console.log(`迁移成功完成，共 ${count} 条记录`);
            process.exit(0);
        })
        .catch(error => {
            console.error('迁移失败:', error.message);
            process.exit(1);
        });
}

module.exports = CompleteMigration;