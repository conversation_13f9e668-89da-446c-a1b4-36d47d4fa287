#!/usr/bin/env node
"use strict";

const { Pool } = require("pg");
const { parse } = require("pg-connection-string");
const dbConfig = require('../config');
const SQLiteAdapter = require('../adapters/sqlite');

class ResumeStableMigration {
    constructor() {
        this.sourceAdapter = null;
        this.pgPool = null;
        this.batchSize = 5; // 非常小的批次
    }

    convertValue(columnName, value, tableName) {
        // 处理特殊的数据类型转换
        if (tableName === 'ssh_scripts' && (columnName === 'created_at' || columnName === 'updated_at')) {
            // 将日期字符串转换为时间戳
            if (typeof value === 'string' && value.includes('-')) {
                const timestamp = Math.floor(new Date(value).getTime() / 1000);
                return timestamp;
            }
        }
        
        // 处理其他可能的日期转换
        if ((columnName === 'created_at' || columnName === 'updated_at') && typeof value === 'string') {
            if (value.includes('-')) {
                const timestamp = Math.floor(new Date(value).getTime() / 1000);
                return timestamp;
            }
        }
        
        return value;
    }

    async initialize() {
        console.log('[恢复] 初始化数据库连接...');
        
        // SQLite连接
        this.sourceAdapter = new SQLiteAdapter(dbConfig.sqlite);
        await this.sourceAdapter.connect();
        console.log('[数据库] SQLite 连接成功');
        
        // PostgreSQL连接 - 使用更稳定的配置
        const connectionConfig = parse(dbConfig.postgresql.connection);
        
        // 优化连接配置
        const poolConfig = {
            ...connectionConfig,
            max: 1,                    // 最大连接数限制为1
            idleTimeoutMillis: 600000, // 10分钟空闲超时
            connectionTimeoutMillis: 30000, // 30秒连接超时
            keepAlive: true,           // 保持连接活跃
            keepAliveInitialDelayMillis: 0,
            statement_timeout: 300000, // 5分钟语句超时
            query_timeout: 300000,     // 5分钟查询超时
            application_name: 'dstatus_migration' // 应用名称
        };
        
        this.pgPool = new Pool(poolConfig);
        
        // 测试连接
        try {
            const client = await this.pgPool.connect();
            await client.query("SELECT 1");
            client.release();
            console.log('[数据库] PostgreSQL 连接成功');
        } catch (error) {
            console.error('[数据库] PostgreSQL 连接失败:', error.message);
            throw error;
        }
    }

    async resumeTable(tableName) {
        console.log(`[恢复] 开始恢复表 ${tableName}...`);
        
        try {
            // 检查源表记录数
            console.log(`[恢复] 查询表 ${tableName} 的记录数...`);
            const sourceData = await this.sourceAdapter.query(`SELECT COUNT(*) as count FROM ${tableName}`);
            console.log(`[恢复] 查询结果:`, sourceData);
            if (!sourceData || sourceData.length === 0) {
                console.log(`[恢复] 无法获取表 ${tableName} 的记录数`);
                return 0;
            }
            const sourceCount = sourceData[0].count;
            console.log(`[恢复] 表 ${tableName} 源记录数: ${sourceCount}`);
            
            if (sourceCount === 0) {
                console.log(`[恢复] 表 ${tableName} 为空，跳过迁移`);
                return 0;
            }
            
            // 检查目标表当前记录数
            console.log(`[恢复] 查询目标表 ${tableName} 的当前记录数...`);
            const client = await this.pgPool.connect();
            let currentCount = 0;
            try {
                const result = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
                console.log(`[恢复] PostgreSQL查询结果:`, result.rows);
                if (result && result.rows && result.rows.length > 0) {
                    currentCount = parseInt(result.rows[0].count);
                } else {
                    console.log(`[恢复] PostgreSQL查询结果为空`);
                }
            } catch (error) {
                console.log(`[恢复] PostgreSQL查询失败:`, error.message);
            } finally {
                client.release();
            }
            
            if (currentCount >= sourceCount) {
                console.log(`[恢复] 表 ${tableName} 已完成迁移 (${currentCount}/${sourceCount})`);
                return currentCount;
            }
            
            console.log(`[恢复] 表 ${tableName} 当前进度: ${currentCount}/${sourceCount}`);
            
            // 获取表结构
            const schema = await this.sourceAdapter.query(`PRAGMA table_info(${tableName})`);
            const columns = schema.map(col => col.name);
            
            // 从中断处继续迁移数据
            let migratedCount = currentCount;
            let offset = currentCount;
            
            while (offset < sourceCount) {
                try {
                    // 获取一批数据
                    const batchData = await this.sourceAdapter.query(
                        `SELECT ${columns.join(', ')} FROM ${tableName} LIMIT ${this.batchSize} OFFSET ${offset}`
                    );
                    
                    if (batchData.length === 0) break;
                    
                    // 插入数据
                    const client = await this.pgPool.connect();
                    try {
                        for (const row of batchData) {
                            const placeholders = columns.map((_, i) => `$${i + 1}`).join(', ');
                            const values = columns.map(col => this.convertValue(col, row[col], tableName));
                            
                            const insertSQL = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`;
                            await client.query(insertSQL, values);
                            migratedCount++;
                        }
                    } finally {
                        client.release();
                    }
                    
                    offset += batchData.length;
                    console.log(`[恢复] 已迁移 ${migratedCount}/${sourceCount} 条记录`);
                    
                    // 短暂暂停
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                } catch (error) {
                    console.error(`[恢复] 批次处理失败: ${error.message}`);
                    // 等待更长时间后重试
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    continue;
                }
            }
            
            console.log(`[恢复] 表 ${tableName} 迁移完成，共 ${migratedCount} 条记录`);
            return migratedCount;
            
        } catch (error) {
            console.error(`[恢复] 表 ${tableName} 迁移失败:`, error.message);
            throw error;
        }
    }

    async cleanup() {
        console.log('[恢复] 清理连接...');
        
        if (this.sourceAdapter) {
            await this.sourceAdapter.disconnect();
            console.log('[数据库] SQLite 连接已断开');
        }
        
        if (this.pgPool) {
            await this.pgPool.end();
            console.log('[数据库] PostgreSQL 连接已断开');
        }
    }

    async run(tableName) {
        try {
            await this.initialize();
            const migratedCount = await this.resumeTable(tableName);
            
            console.log(`\n==========================================`);
            console.log(`✅ 表 ${tableName} 恢复迁移完成！`);
            console.log(`迁移记录数: ${migratedCount.toLocaleString()}`);
            console.log(`==========================================\n`);
            
            return migratedCount;
            
        } catch (error) {
            console.error(`\n==========================================`);
            console.error(`❌ 表 ${tableName} 恢复迁移失败: ${error.message}`);
            console.error(`==========================================\n`);
            throw error;
        } finally {
            await this.cleanup();
        }
    }
}

// 获取命令行参数
const tableName = process.argv[2];
if (!tableName) {
    console.error('请指定要恢复迁移的表名');
    console.error('用法: node resume_stable_migration.js <table_name>');
    process.exit(1);
}

const migration = new ResumeStableMigration();
migration.run(tableName)
    .then(count => {
        console.log(`表 ${tableName} 恢复迁移成功完成，共 ${count} 条记录`);
        process.exit(0);
    })
    .catch(error => {
        console.error(`表 ${tableName} 恢复迁移失败:`, error.message);
        process.exit(1);
    });

module.exports = ResumeStableMigration;