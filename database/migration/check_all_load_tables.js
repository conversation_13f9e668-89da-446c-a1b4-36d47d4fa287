#!/usr/bin/env node
"use strict";

const dbConfig = require('../config');
const PostgreSQLAdapter = require('../adapters/postgresql');
const SQLiteAdapter = require('../adapters/sqlite');

async function checkAllLoadTables() {
    const pgAdapter = new PostgreSQLAdapter(dbConfig.postgresql);
    const sqliteAdapter = new SQLiteAdapter(dbConfig.sqlite);
    
    try {
        await pgAdapter.connect();
        await sqliteAdapter.connect();
        
        const tables = ['load_h', 'load_m', 'load_archive'];
        
        for (const table of tables) {
            console.log(`\n[检查] ========== ${table} 表 ==========`);
            
            // 检查PostgreSQL表结构
            const pgConstraints = await pgAdapter.query(`
                SELECT 
                    tc.constraint_name, 
                    tc.constraint_type, 
                    kcu.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu 
                    ON tc.constraint_name = kcu.constraint_name 
                    AND tc.table_schema = kcu.table_schema
                WHERE tc.table_name = $1 AND tc.constraint_type = 'PRIMARY KEY'
                ORDER BY kcu.ordinal_position
            `, [table]);
            
            console.log(`[检查] PostgreSQL ${table} 主键:`);
            pgConstraints.forEach(c => {
                console.log(`  列: ${c.column_name}, 约束: ${c.constraint_name}`);
            });
            
            // 检查SQLite表数据
            const sqliteCount = await sqliteAdapter.query(`SELECT COUNT(*) as count FROM ${table}`);
            console.log(`[检查] SQLite ${table} 总记录数: ${sqliteCount[0].count}`);
            
            if (sqliteCount[0].count > 0) {
                // 检查sid重复情况
                const uniqueSidCount = await sqliteAdapter.query(`SELECT COUNT(DISTINCT sid) as count FROM ${table}`);
                console.log(`[检查] SQLite ${table} 唯一sid数量: ${uniqueSidCount[0].count}`);
                
                if (sqliteCount[0].count !== uniqueSidCount[0].count) {
                    console.log(`[检查] ⚠️  ${table} 表有重复的sid！`);
                    console.log(`[检查] 重复数量: ${sqliteCount[0].count - uniqueSidCount[0].count}`);
                    
                    // 获取重复统计
                    const duplicates = await sqliteAdapter.query(`
                        SELECT sid, COUNT(*) as count 
                        FROM ${table} 
                        GROUP BY sid 
                        HAVING COUNT(*) > 1 
                        ORDER BY count DESC
                        LIMIT 3
                    `);
                    
                    console.log(`[检查] ${table} 重复的sid样本:`);
                    duplicates.forEach(dup => {
                        console.log(`  SID: ${dup.sid}, 出现次数: ${dup.count}`);
                    });
                } else {
                    console.log(`[检查] ✅ ${table} 表没有重复的sid`);
                }
                
                // 检查id重复情况
                const uniqueIdCount = await sqliteAdapter.query(`SELECT COUNT(DISTINCT id) as count FROM ${table}`);
                console.log(`[检查] SQLite ${table} 唯一id数量: ${uniqueIdCount[0].count}`);
                
                if (sqliteCount[0].count !== uniqueIdCount[0].count) {
                    console.log(`[检查] ⚠️  ${table} 表有重复的id！`);
                } else {
                    console.log(`[检查] ✅ ${table} 表没有重复的id`);
                }
            }
        }
        
    } catch (error) {
        console.error('[检查] 失败:', error.message);
    } finally {
        await pgAdapter.disconnect();
        await sqliteAdapter.disconnect();
    }
}

checkAllLoadTables();