"use strict";

/**
 * 表优化器 - 智能批量大小计算和性能优化
 * 根据表特征动态调整迁移策略
 */

class TableOptimizer {
    constructor() {
        // 表特征阈值
        this.thresholds = {
            large: 100000,   // 大表：>10万条
            medium: 10000,   // 中表：1万-10万条
            small: 1000      // 小表：<1千条
        };
        
        // 优化策略配置
        this.strategies = {
            large: {
                batchSize: 1000,
                transactionBatches: 5,     // 每5个批次提交一次事务
                progressInterval: 10000    // 每1万条记录报告进度
            },
            medium: {
                batchSize: 500,
                transactionBatches: 3,     // 每3个批次提交一次事务
                progressInterval: 5000     // 每5千条记录报告进度
            },
            small: {
                batchSize: 200,
                transactionBatches: 1,     // 单事务处理
                progressInterval: 1000     // 每1千条记录报告进度
            }
        };
    }

    /**
     * 分析表特征
     * @param {string} tableName - 表名
     * @param {number} rowCount - 行数
     * @returns {Object} 表特征信息
     */
    analyzeTable(tableName, rowCount) {
        let category;
        if (rowCount >= this.thresholds.large) {
            category = 'large';
        } else if (rowCount >= this.thresholds.medium) {
            category = 'medium';
        } else if (rowCount >= this.thresholds.small) {
            category = 'small';
        } else {
            category = 'tiny';
        }

        const strategy = this.strategies[category] || this.strategies.small;
        
        return {
            tableName,
            rowCount,
            category,
            strategy: {
                ...strategy,
                // 根据表名特殊调整
                ...this.getTableSpecificAdjustments(tableName, rowCount)
            }
        };
    }

    /**
     * 获取表特定的调整参数
     * @param {string} tableName - 表名
     * @param {number} rowCount - 行数
     * @returns {Object} 调整参数
     */
    getTableSpecificAdjustments(tableName, rowCount) {
        const adjustments = {};
        
        // 时序数据表(load_*, tcping_*)优化
        if (tableName.startsWith('load_') || tableName.startsWith('tcping_')) {
            adjustments.batchSize = Math.min(1500, Math.max(500, Math.floor(rowCount / 100)));
            adjustments.transactionBatches = rowCount > 50000 ? 10 : 5;
        }
        
        // 配置表(setting)特殊处理
        if (tableName === 'setting') {
            adjustments.batchSize = 50;
            adjustments.transactionBatches = 1;
        }
        
        // 流量数据表优化
        if (tableName === 'traffic') {
            adjustments.batchSize = Math.min(2000, Math.max(800, Math.floor(rowCount / 50)));
            adjustments.transactionBatches = 8;
        }
        
        return adjustments;
    }

    /**
     * 计算预估迁移时间
     * @param {string} tableName - 表名
     * @param {number} rowCount - 行数
     * @param {Object} strategy - 策略配置
     * @returns {Object} 时间预估
     */
    estimateMigrationTime(tableName, rowCount, strategy) {
        // 基础每行处理时间(毫秒)
        const baseTimePerRow = this.getBaseProcessingTime(tableName);
        
        // 批次数量
        const batchCount = Math.ceil(rowCount / strategy.batchSize);
        
        // 事务数量
        const transactionCount = Math.ceil(batchCount / strategy.transactionBatches);
        
        // 估算总时间
        const processingTime = rowCount * baseTimePerRow;
        const transactionOverhead = transactionCount * 50; // 每个事务50ms开销
        const networkOverhead = batchCount * 10;            // 每个批次10ms网络开销
        
        const totalTime = processingTime + transactionOverhead + networkOverhead;
        
        return {
            totalTimeMs: Math.round(totalTime),
            totalTimeSeconds: Math.round(totalTime / 1000),
            batchCount,
            transactionCount,
            estimatedThroughput: Math.round(rowCount / (totalTime / 1000)) // 行/秒
        };
    }

    /**
     * 获取表的基础处理时间
     * @param {string} tableName - 表名
     * @returns {number} 每行处理时间(毫秒)
     */
    getBaseProcessingTime(tableName) {
        // 不同类型表的处理复杂度
        const complexityMap = {
            'servers': 2.0,          // 复杂结构
            'ai_reports': 3.0,       // 大JSON字段
            'ssh_scripts': 2.5,      // 文本内容
            'setting': 1.0,          // 简单键值
            'groups': 0.8,           // 最简单
            'traffic': 0.9,          // 数值型
            'load_archive': 1.2,     // 时序数据
            'tcping_archive': 1.5    // 复杂时序
        };
        
        // 默认复杂度
        let complexity = 1.0;
        
        // 查找匹配的复杂度
        for (const [pattern, value] of Object.entries(complexityMap)) {
            if (tableName.includes(pattern)) {
                complexity = value;
                break;
            }
        }
        
        return complexity;
    }

    /**
     * 生成优化报告
     * @param {Array} tableAnalyses - 表分析结果数组
     * @returns {Object} 优化报告
     */
    generateOptimizationReport(tableAnalyses) {
        let totalRows = 0;
        let totalTimeMs = 0;
        let totalBatches = 0;
        let totalTransactions = 0;
        
        const report = {
            summary: {},
            tables: [],
            recommendations: []
        };
        
        for (const analysis of tableAnalyses) {
            const timeEstimate = this.estimateMigrationTime(
                analysis.tableName,
                analysis.rowCount,
                analysis.strategy
            );
            
            totalRows += analysis.rowCount;
            totalTimeMs += timeEstimate.totalTimeMs;
            totalBatches += timeEstimate.batchCount;
            totalTransactions += timeEstimate.transactionCount;
            
            report.tables.push({
                ...analysis,
                timeEstimate
            });
        }
        
        report.summary = {
            totalTables: tableAnalyses.length,
            totalRows,
            totalTimeSeconds: Math.round(totalTimeMs / 1000),
            totalTimeFormatted: this.formatTime(totalTimeMs),
            totalBatches,
            totalTransactions,
            averageThroughput: Math.round(totalRows / (totalTimeMs / 1000))
        };
        
        // 生成建议
        report.recommendations = this.generateRecommendations(report);
        
        return report;
    }

    /**
     * 格式化时间显示
     * @param {number} ms - 毫秒数
     * @returns {string} 格式化的时间字符串
     */
    formatTime(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours}时${minutes % 60}分${seconds % 60}秒`;
        } else if (minutes > 0) {
            return `${minutes}分${seconds % 60}秒`;
        } else {
            return `${seconds}秒`;
        }
    }

    /**
     * 生成优化建议
     * @param {Object} report - 分析报告
     * @returns {Array} 建议数组
     */
    generateRecommendations(report) {
        const recommendations = [];
        
        // 基于总时间的建议
        if (report.summary.totalTimeSeconds > 300) { // >5分钟
            recommendations.push({
                type: 'performance',
                message: '预计迁移时间较长，建议在低峰期执行',
                priority: 'medium'
            });
        }
        
        // 基于吞吐量的建议
        if (report.summary.averageThroughput < 1000) { // <1000行/秒
            recommendations.push({
                type: 'optimization',
                message: '吞吐量偏低，考虑增加批量大小或检查网络连接',
                priority: 'low'
            });
        }
        
        // 检查大表
        const largeTables = report.tables.filter(t => t.category === 'large');
        if (largeTables.length > 0) {
            recommendations.push({
                type: 'strategy',
                message: `发现${largeTables.length}个大表，将使用大批量+多事务策略`,
                priority: 'info'
            });
        }
        
        return recommendations;
    }
}

module.exports = TableOptimizer;