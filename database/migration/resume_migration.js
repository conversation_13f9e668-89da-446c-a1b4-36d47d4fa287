#!/usr/bin/env node
"use strict";

const path = require('path');
const fs = require('fs');
const dbConfig = require('../config');
const SQLiteAdapter = require('../adapters/sqlite');
const PostgreSQLAdapter = require('../adapters/postgresql');
const DataExporter = require('./exporter');
const DataImporter = require('./importer');

class ResumeMigration {
    constructor() {
        this.sourceAdapter = null;
        this.targetAdapter = null;
        this.exporter = null;
        this.importer = null;
        this.migrationStats = {};
        this.maxRetries = 10;
        this.initialWaitTime = 2000; // 2秒
        this.maxWaitTime = 60000; // 60秒
    }

    async initialize() {
        console.log('[恢复] 初始化数据库连接...');
        
        this.sourceAdapter = new SQLiteAdapter(dbConfig.sqlite);
        await this.sourceAdapter.connect();
        console.log('[数据库] SQLite 连接成功');
        
        this.targetAdapter = new PostgreSQLAdapter(dbConfig.postgresql);
        await this.connectWithRetry();
        console.log('[数据库] PostgreSQL 连接成功');
        
        this.exporter = new DataExporter(this.sourceAdapter);
        this.importer = new DataImporter(this.targetAdapter);
        this.importer.setBatchSize(50); // 减小批次大小提高稳定性
    }

    async connectWithRetry(maxRetries = 5) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                await this.targetAdapter.connect();
                return;
            } catch (error) {
                console.log(`[连接] PostgreSQL 连接失败 (尝试 ${i + 1}/${maxRetries}): ${error.message}`);
                if (i === maxRetries - 1) throw error;
                await this.sleep(3000); // 等待3秒后重试
            }
        }
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async migrateTableWithRetry(table, importMethod, maxRetries = 10) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                // 检查表是否存在
                const sourceCount = await this.exporter.getTableCount(table);
                if (sourceCount === 0) {
                    console.log(`[恢复] 表 ${table} 为空，跳过迁移`);
                    return 0;
                }

                const currentCount = await this.getCurrentCount(table);
                if (currentCount >= sourceCount) {
                    console.log(`[恢复] 表 ${table} 已完成迁移 (${currentCount}/${sourceCount})`);
                    return currentCount;
                }

                console.log(`[恢复] 开始迁移表 ${table}... (当前: ${currentCount}/${sourceCount})`);
                
                // 如果是部分迁移，不清空表
                if (currentCount === 0) {
                    await this.importer.truncateTable(table);
                }

                // 获取导出方法
                const exportMethod = this.getExportMethodName(table);
                if (!this.exporter[exportMethod]) {
                    throw new Error(`导出方法 ${exportMethod} 不存在`);
                }
                if (!this.importer[importMethod]) {
                    throw new Error(`导入方法 ${importMethod} 不存在`);
                }

                // 执行迁移
                const dataStream = this.exporter[exportMethod]();
                const migratedCount = await this.importer[importMethod](dataStream);
                
                console.log(`[恢复] 表 ${table} 迁移完成，${migratedCount} 条记录`);
                return migratedCount;

            } catch (error) {
                if (this.isConnectionError(error) && i < maxRetries - 1) {
                    const waitTime = Math.min(this.initialWaitTime * Math.pow(2, i), this.maxWaitTime);
                    console.log(`[重试] 表 ${table} 连接错误 (尝试 ${i + 1}/${maxRetries}): ${error.message}`);
                    console.log(`[重试] 等待 ${waitTime/1000} 秒后重新连接...`);
                    
                    await this.sleep(waitTime);
                    
                    // 重新连接
                    try {
                        await this.targetAdapter.disconnect();
                    } catch (e) {
                        // 忽略断开连接的错误
                    }
                    
                    await this.connectWithRetry();
                    
                    // 重新创建导入器
                    this.importer = new DataImporter(this.targetAdapter);
                    this.importer.setBatchSize(50);
                    
                    continue;
                }
                throw error;
            }
        }
        throw new Error(`表 ${table} 迁移失败，已达到最大重试次数`);
    }

    isConnectionError(error) {
        const connectionErrors = [
            'Connection terminated',
            'connection',
            'ECONNRESET',
            'ENOTFOUND',
            'timeout',
            'network',
            'socket'
        ];
        return connectionErrors.some(keyword => 
            error.message.toLowerCase().includes(keyword.toLowerCase())
        );
    }

    async getCurrentCount(table) {
        try {
            const result = await this.targetAdapter.query(`SELECT COUNT(*) as count FROM ${table}`);
            return result[0].count;
        } catch (error) {
            return 0;
        }
    }

    async resumeMigration() {
        console.log('[恢复] 开始恢复迁移...');
        
        // 需要迁移的表（按优先级排序）
        const pendingTables = [
            'load_archive',    // 12,216 条记录
            'load_h',          // 781 条记录
            'load_m',          // 48,246 条记录
            'tcping_5m',       // 6,661 条记录
            'tcping_archive',  // 1,461 条记录
            'tcping_d',        // 1,460 条记录
            'tcping_h',        // 1,440 条记录
            'tcping_m',        // 28,240 条记录
            'tcping_month',    // 86 条记录
            'ssh_scripts',     // 3 条记录
            'monitor_regions', // 2 条记录
            'monitor_targets'  // 2 条记录
        ];

        let totalMigrated = 0;
        
        for (const table of pendingTables) {
            try {
                const importMethod = this.getImportMethodName(table);
                const migratedCount = await this.migrateTableWithRetry(table, importMethod);
                
                this.migrationStats[table] = migratedCount;
                totalMigrated += migratedCount;
                
                // 每个表完成后暂停一下，让连接稳定
                await this.sleep(1000);
                
            } catch (error) {
                console.error(`[恢复] 表 ${table} 迁移失败:`, error.message);
                // 记录失败但继续其他表
                this.migrationStats[table] = `失败: ${error.message}`;
            }
        }
        
        console.log(`[恢复] 迁移完成，本次迁移 ${totalMigrated} 条记录`);
        return totalMigrated;
    }

    getExportMethodName(tableName) {
        let camelCase = tableName.replace(/_([a-z0-9])/g, (match, letter) => letter.toUpperCase());
        if (tableName === 'tcping_5m') {
            camelCase = 'tcping5m';
        }
        return 'export' + camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
    }

    getImportMethodName(tableName) {
        let camelCase = tableName.replace(/_([a-z0-9])/g, (match, letter) => letter.toUpperCase());
        if (tableName === 'tcping_5m') {
            camelCase = 'tcping5m';
        }
        return 'import' + camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
    }

    async cleanup() {
        console.log('[恢复] 清理连接...');
        
        if (this.sourceAdapter) {
            await this.sourceAdapter.disconnect();
            console.log('[数据库] SQLite 连接已断开');
        }
        
        if (this.targetAdapter) {
            await this.targetAdapter.disconnect();
            console.log('[数据库] PostgreSQL 连接已断开');
        }
    }

    async run() {
        try {
            await this.initialize();
            const totalMigrated = await this.resumeMigration();
            
            console.log(`\n==========================================`);
            console.log(`✅ 恢复迁移完成！`);
            console.log(`本次迁移记录数: ${totalMigrated.toLocaleString()}`);
            console.log(`==========================================\n`);
            
            return totalMigrated;
            
        } catch (error) {
            console.error(`\n==========================================`);
            console.error(`❌ 恢复迁移失败: ${error.message}`);
            console.error(`==========================================\n`);
            throw error;
        } finally {
            await this.cleanup();
        }
    }
}

if (require.main === module) {
    const migration = new ResumeMigration();
    migration.run()
        .then(count => {
            console.log(`恢复迁移成功完成，共 ${count} 条记录`);
            process.exit(0);
        })
        .catch(error => {
            console.error('恢复迁移失败:', error.message);
            process.exit(1);
        });
}

module.exports = ResumeMigration;