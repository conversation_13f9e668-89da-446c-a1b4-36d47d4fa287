"use strict";
const path = require('path');
const fs = require('fs');

/**
 * 数据库配置管理类
 * 统一管理数据库相关的路径和配置
 */
class DatabaseConfig {
    constructor() {
        // 读取数据库配置：优先配置文件，后备环境变量
        const dbConfig = this.loadDatabaseConfig();
        this.type = dbConfig.type;

        // SQLite 配置 - 修复pkg构建环境路径问题
        this.sqlite = {
            path: process.env.DB_PATH || this.getDefaultSQLitePath(),
        };

        // PostgreSQL 配置 - 优先使用环境变量，再使用配置文件
        this.postgresql = {
            connection: process.env.DATABASE_URL || (dbConfig.type === 'postgresql' ? dbConfig.postgresConnection : null)
        };

        // 基础配置
        this.BASE_PATH = path.dirname(this.sqlite.path);
        this.MAIN_DB = path.basename(this.sqlite.path);
        this.BACKUP_PREFIX = 'backup-';
        this.TEMP_PREFIX = 'temp-';

        // 初始化时验证SQLite路径（即使主要使用PostgreSQL也需要SQLite作为配置存储）
        this.validateAndCreatePaths();
    }

    /**
     * 获取默认SQLite路径，处理pkg构建环境
     * @returns {string} SQLite数据库文件路径
     */
    getDefaultSQLitePath() {
        if (process.pkg) {
            // 在pkg构建环境中，使用相对于当前工作目录的路径
            return path.join(process.cwd(), 'data', 'db.db');
        } else {
            // 在开发环境中，使用相对于项目根目录的路径
            return path.join(__dirname, '..', 'data', 'db.db');
        }
    }

    /**
     * 加载数据库配置（类型和连接信息）
     * @returns {Object} 包含数据库类型和连接配置的对象
     */
    loadDatabaseConfig() {
        try {
            // 优先从配置文件读取 - 处理pkg构建环境
            const configFile = process.pkg 
                ? path.join(process.cwd(), 'config', 'database.json')
                : path.join(__dirname, '../config/database.json');
            const templateConfigFile = process.pkg 
                ? path.join(process.cwd(), 'config', 'database.json.template')
                : path.join(__dirname, '../config/database.json.template');
            
            if (fs.existsSync(configFile)) {
                const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));
                if (config.activeDatabase && config.databases && config.databases[config.activeDatabase]) {
                    const activeDbConfig = config.databases[config.activeDatabase];
                    console.log(`[Config] 使用配置文件指定的数据库: ${config.activeDatabase}`);
                    
                    return {
                        type: config.activeDatabase,
                        postgresConnection: activeDbConfig.type === 'postgresql' ? activeDbConfig.connection : null
                    };
                }
            } else if (fs.existsSync(templateConfigFile)) {
                // 如果正式配置文件不存在，但模板存在，则从模板复制一份
                fs.copyFileSync(templateConfigFile, configFile);
                console.log(`[Config] 未找到配置文件，已从模板创建: ${configFile}`);
                // 重新加载配置
                return this.loadDatabaseConfig();
            }
        } catch (error) {
            console.warn(`[Config] 读取配置文件失败，使用环境变量: ${error.message}`);
        }
        
        // 后备方案：使用环境变量
        const envType = process.env.DB_TYPE || 'sqlite';
        console.log(`[Config] 使用环境变量数据库: ${envType}`);
        return {
            type: envType,
            postgresConnection: null // 环境变量模式下使用DATABASE_URL
        };
    }

    /**
     * 获取当前数据库配置
     * @returns {Object}
     */
    getConfig() {
        return this[this.type];
    }

    /**
     * 获取所有数据库相关路径
     * @returns {Object} 包含所有相关路径的对象
     */
    getPaths() {
        return {
            base: this.BASE_PATH,
            main: this.sqlite.path,
            backup: (timestamp) => path.join(this.BASE_PATH, 'backups', `${this.BACKUP_PREFIX}${timestamp}.db.db`),
            temp: (id) => path.join(this.BASE_PATH, 'temp', `${this.TEMP_PREFIX}${id}.db`),
            migration: path.join(this.BASE_PATH, 'migrations.json')
        };
    }

    /**
     * 验证并创建必要的目录
     */
    validateAndCreatePaths() {
        const paths = this.getPaths();
        const directories = [
            this.BASE_PATH,
            path.join(this.BASE_PATH, 'backups'),
            path.join(this.BASE_PATH, 'temp')
        ];

        directories.forEach(dir => {
            if (!fs.existsSync(dir)) {
                try {
                    fs.mkdirSync(dir, { recursive: true, mode: 0o777 });
                    console.log(`创建目录: ${dir}`);
                } catch (error) {
                    console.error(`❌ 创建数据目录失败 ${dir}: ${error.message}`);
                    console.error('🚨 数据持久化风险警告：');
                    console.error('   请确保当前用户有权限在以下位置创建目录：');
                    console.error(`   ${dir}`);
                    console.error('   或手动创建目录并设置适当权限：');
                    console.error(`   mkdir -p "${dir}" && chmod 755 "${dir}"`);
                    
                    // 只在开发环境使用临时目录，生产环境抛出错误
                    if (process.env.NODE_ENV !== 'production' && !process.pkg) {
                        try {
                            const tempDir = path.join(require('os').tmpdir(), 'dstatus', path.basename(dir));
                            fs.mkdirSync(tempDir, { recursive: true, mode: 0o777 });
                            console.warn(`⚠️  开发环境：使用临时目录 ${tempDir}`);
                            console.warn('⚠️  注意：临时目录中的数据在系统重启后会丢失！');

                            // 更新路径
                            if (dir === this.BASE_PATH) {
                                this.BASE_PATH = path.join(require('os').tmpdir(), 'dstatus');
                                this.sqlite.path = path.join(this.BASE_PATH, this.MAIN_DB);
                            }
                        } catch (tempError) {
                            console.error(`创建临时目录也失败: ${tempError.message}`);
                            throw new Error(`无法创建数据目录，请检查权限设置: ${dir}`);
                        }
                    } else {
                        // 生产环境或pkg环境不使用临时目录，直接抛出错误
                        throw new Error(`数据目录创建失败，请手动创建并设置权限: ${dir}`);
                    }
                }
            }
        });
    }

    /**
     * 验证数据库文件
     * @returns {boolean} 数据库文件是否存在且可访问
     */
    validateDatabase() {
        if (this.type !== 'sqlite') {
            return true;
        }
        const paths = this.getPaths();
        const exists = fs.existsSync(paths.main);
        if (!exists) {
            console.warn(`数据库文件不存在: ${paths.main}`);
        }
        return exists;
    }

    /**
     * 清理临时文件
     * @param {number} maxAge 最大保留时间（毫秒）
     */
    cleanupTempFiles(maxAge = 24 * 60 * 60 * 1000) {
        try {
            const files = fs.readdirSync(this.BASE_PATH);
            const now = Date.now();

            files.forEach(file => {
                if (file.startsWith(this.TEMP_PREFIX) ||
                    (file.startsWith(this.BACKUP_PREFIX) && file.endsWith('.db.db'))) {
                    const filePath = path.join(this.BASE_PATH, file);
                    const stats = fs.statSync(filePath);

                    if (now - stats.mtimeMs > maxAge) {
                        fs.unlinkSync(filePath);
                        console.log(`清理过期文件: ${file}`);
                    }
                }
            });
        } catch (error) {
            console.error(`清理临时文件失败: ${error.message}`);
        }
    }

    /**
     * 获取数据库大小信息
     * @returns {Object} 包含数据库大小信息的对象
     */
    getDatabaseStats() {
        if (this.type !== 'sqlite') {
            return null;
        }
        const paths = this.getPaths();
        try {
            const stats = fs.statSync(paths.main);
            return {
                size: stats.size,
                created: stats.birthtime,
                modified: stats.mtime
            };
        } catch (error) {
            console.error(`获取数据库状态失败: ${error.message}`);
            return null;
        }
    }
}

// 导出单例实例
module.exports = new DatabaseConfig();