"use strict";

module.exports = (DB) => {
    // 严格判断是否为纯PostgreSQL（Dual模式写操作走SQLite）
    const isPostgres = DB.type === 'postgresql';
    
    async function initMigrationTable() {
        const defaultTimestamp = isPostgres ? "NOW()" : "strftime('%s', 'now')";
        const executedAtType = isPostgres ? 'TIMESTAMP' : 'INTEGER';
        await DB.run(`
            CREATE TABLE IF NOT EXISTS db_migrations (
                version INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                executed_at ${executedAtType} DEFAULT (${defaultTimestamp}),
                status TEXT CHECK(status IN ('pending', 'success', 'failed')) DEFAULT 'pending'
            )
        `);
    }

    async function getCurrentVersion() {
        const result = await DB.get("SELECT MAX(version) as version FROM db_migrations WHERE status = 'success'");
        return result ? result.version : 0;
    }

    async function recordMigration(version, name, status) {
        const sql = isPostgres
            ? `INSERT INTO db_migrations (version, name, status) VALUES ($1, $2, $3)
               ON CONFLICT (version) DO UPDATE SET name = $2, status = $3`
            : `REPLACE INTO db_migrations (version, name, status) VALUES (?, ?, ?)`;
        await DB.run(sql, [version, name, status]);
    }

    async function safeExecute(sql) {
        try {
            await DB.run(sql);
            return true;
        } catch (err) {
            return false;
        }
    }

    async function columnExists(table, column) {
        if (isPostgres) {
            const result = await DB.get(`
                SELECT 1 FROM information_schema.columns
                WHERE table_name = $1 AND column_name = $2
            `, [table, column]);
            return !!result;
        } else {
            // SQLite或Dual模式下使用SQLite语法
            const columns = await DB.all(`PRAGMA table_info(${table})`);
            return columns.some(col => col.name === column);
        }
    }

    /**
     * 修复历史遗留的迁移问题
     * 这个函数会自动检测并修复各种客户环境中可能存在的迁移问题
     */
    async function fixLegacyMigrationIssues() {
        try {
            console.log('[Migration] 检查并修复历史遗留问题...');
            
            // 1. 修复版本15重复问题
            // 检查是否有两个版本15的记录
            const version15Records = await DB.all(
                "SELECT * FROM db_migrations WHERE version = 15"
            );
            
            if (version15Records.length > 1) {
                console.log('[Migration] 检测到版本15重复，正在修复...');
                // 保留SSH相关的，删除tcping相关的
                await DB.run(
                    "DELETE FROM db_migrations WHERE version = 15 AND name LIKE '%tcping%'"
                );
                console.log('[Migration] 版本15重复问题已修复');
            }
            
            // 2. 检查tcping索引是否已创建但未记录
            // 如果索引已存在但没有迁移记录，补充记录
            const indexExists = await DB.get(
                "SELECT name FROM sqlite_master WHERE type='index' AND name='idx_tcping_m_target_time'"
            );
            
            if (indexExists) {
                // 索引已存在，检查是否有对应的迁移记录
                const hasMigration22 = await DB.get(
                    "SELECT * FROM db_migrations WHERE version = 22"
                );
                
                if (!hasMigration22) {
                    // 索引存在但没有版本22的记录，说明是旧版本15创建的
                    console.log('[Migration] 检测到tcping索引已存在，补充版本22记录...');
                    await recordMigration(22, 'Add performance indexes for tcping tables (SQLite/PG)', 'success');
                }
            }
            
            // 3. 修复版本18失败状态
            // 如果版本18失败但索引实际已创建，更新状态
            const version18Failed = await DB.get(
                "SELECT * FROM db_migrations WHERE version = 18 AND status = 'failed'"
            );
            
            if (version18Failed) {
                // 检查索引是否实际已创建
                const indexActuallyExists = await DB.get(
                    "SELECT COUNT(*) as count FROM sqlite_master WHERE type='index' AND name LIKE 'idx_tcping%'"
                );
                
                if (indexActuallyExists && indexActuallyExists.count > 0) {
                    console.log('[Migration] 修复版本18错误状态...');
                    await DB.run(
                        "UPDATE db_migrations SET status = 'success' WHERE version = 18"
                    );
                }
            }
            
            // 4. 确保版本连续性
            // 检查是否有跳过的版本号（除了预期的版本15到版本16的跳跃）
            const allVersions = await DB.all(
                "SELECT version FROM db_migrations WHERE status = 'success' ORDER BY version"
            );
            
            if (allVersions.length > 0) {
                let lastVersion = 0;
                for (const record of allVersions) {
                    // 版本15后直接到16是正常的（因为重复的15被改为22）
                    if (lastVersion === 15 && record.version === 16) {
                        lastVersion = record.version;
                        continue;
                    }
                    
                    // 检查其他版本跳跃
                    if (record.version > lastVersion + 1 && lastVersion !== 0) {
                        console.log(`[Migration] 警告：版本${lastVersion}和${record.version}之间存在跳跃`);
                    }
                    lastVersion = record.version;
                }
            }
            
            console.log('[Migration] 历史遗留问题检查完成');
        } catch (error) {
            // 如果修复过程失败，记录错误但不中断迁移
            console.error('[Migration] 修复历史问题时出现非致命错误:', error.message);
            console.log('[Migration] 继续执行正常迁移流程...');
        }
    }

    const migrations = [
        {
            version: 1,
            name: 'Create initial tables',
            async up(client) {
                const strftime = isPostgres ? "EXTRACT(EPOCH FROM NOW())::INTEGER" : "strftime('%s', 'now')";
                const primaryKey = isPostgres ? 'SERIAL PRIMARY KEY' : 'INTEGER PRIMARY KEY AUTOINCREMENT';

                // servers表：top字段存储毫秒级时间戳，PostgreSQL需要BIGINT
                const serverTopFieldType = isPostgres ? 'BIGINT' : 'INTEGER';
                await client.query(`
                    CREATE TABLE IF NOT EXISTS servers (
                        sid TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        data TEXT,
                        top ${serverTopFieldType},
                        status INTEGER,
                        expire_time INTEGER,
                        group_id TEXT DEFAULT 'default',
                        traffic_limit BIGINT DEFAULT 0,
                        traffic_reset_day INTEGER DEFAULT 1,
                        traffic_alert_percent INTEGER DEFAULT 80,
                        traffic_last_reset INTEGER DEFAULT (${strftime}),
                        traffic_calibration_date INTEGER DEFAULT (${strftime}),
                        traffic_calibration_value BIGINT DEFAULT 0,
                        traffic_direction TEXT DEFAULT 'both',
                        last_online INTEGER
                    )
                `);

                // groups表：top字段存储毫秒级时间戳，PostgreSQL需要BIGINT
                const topFieldType = isPostgres ? 'BIGINT' : 'INTEGER';
                await client.query(`
                    CREATE TABLE IF NOT EXISTS groups (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        top ${topFieldType} DEFAULT 0
                    )
                `);

                await client.query(`
                    CREATE TABLE IF NOT EXISTS traffic (
                        sid TEXT PRIMARY KEY,
                        hs TEXT,
                        ds TEXT,
                        ms TEXT
                    )
                `);

                await client.query(`
                    CREATE TABLE IF NOT EXISTS lt (
                        sid TEXT PRIMARY KEY,
                        traffic TEXT
                    )
                `);

                await client.query(`
                    CREATE TABLE IF NOT EXISTS load_m (
                        sid TEXT,
                        cpu REAL,
                        mem REAL,
                        swap REAL,
                        ibw REAL,
                        obw REAL,
                        expire_time INTEGER,
                        created_at INTEGER DEFAULT (${strftime}),
                        PRIMARY KEY(sid)
                    )
                `);

                await client.query(`
                    CREATE TABLE IF NOT EXISTS load_h (
                        sid TEXT,
                        cpu REAL,
                        mem REAL,
                        swap REAL,
                        ibw REAL,
                        obw REAL,
                        expire_time INTEGER,
                        created_at INTEGER DEFAULT (${strftime}),
                        PRIMARY KEY(sid)
                    )
                `);

                await client.query(`
                    CREATE TABLE IF NOT EXISTS load_archive (
                        id ${primaryKey},
                        sid TEXT NOT NULL,
                        cpu REAL,
                        mem REAL,
                        swap REAL,
                        ibw REAL,
                        obw REAL,
                        expire_time INTEGER,
                        created_at INTEGER DEFAULT (${strftime})
                    )
                `);

                await client.query(`
                    CREATE TABLE IF NOT EXISTS ssh_scripts (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        content TEXT NOT NULL,
                        category TEXT DEFAULT 'custom',
                        description TEXT DEFAULT '',
                        variables TEXT DEFAULT '[]',
                        examples TEXT DEFAULT '[]',
                        tags TEXT DEFAULT '',
                        usage_count INTEGER DEFAULT 0,
                        created_at TEXT DEFAULT (${isPostgres ? "NOW()" : "datetime('now')"}),
                        updated_at TEXT DEFAULT (${isPostgres ? "NOW()" : "datetime('now')"})
                    )
                `);

                await client.query(`
                    CREATE TABLE IF NOT EXISTS setting (
                        key TEXT PRIMARY KEY,
                        val TEXT
                    )
                `);

                await client.query(`
                    CREATE TABLE IF NOT EXISTS autodiscovery_servers (
                        id TEXT PRIMARY KEY,
                        hostname TEXT NOT NULL,
                        ip TEXT NOT NULL,
                        system TEXT,
                        version TEXT,
                        device TEXT,
                        api_key TEXT NOT NULL,
                        status TEXT DEFAULT 'pending',
                        created_at INTEGER DEFAULT (${strftime}),
                        updated_at INTEGER DEFAULT (${strftime})
                    )
                `);

                await client.query(`
                    CREATE TABLE IF NOT EXISTS monitor_regions (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        description TEXT,
                        created_at INTEGER DEFAULT (${strftime})
                    )
                `);

                await client.query(`
                    CREATE TABLE IF NOT EXISTS monitor_targets (
                        id TEXT PRIMARY KEY,
                        region_id TEXT NOT NULL,
                        name TEXT NOT NULL,
                        host TEXT NOT NULL,
                        port INTEGER NOT NULL,
                        description TEXT,
                        mode TEXT DEFAULT 'auto',
                        node_id TEXT,
                        test_type TEXT DEFAULT 'tcping',
                        created_at INTEGER DEFAULT (${strftime})
                    )
                `);

                for (const table of ['tcping_m', 'tcping_5m', 'tcping_h', 'tcping_d', 'tcping_month']) {
                    await client.query(`
                        CREATE TABLE IF NOT EXISTS ${table} (
                            id ${primaryKey},
                            target_id TEXT NOT NULL,
                            sid TEXT,
                            node_id TEXT,
                            success_rate REAL,
                            avg_time REAL,
                            min_time REAL,
                            max_time REAL,
                            created_at INTEGER DEFAULT (${strftime}),
                            expire_time INTEGER
                        )
                    `);
                }

                await client.query(`
                    CREATE TABLE IF NOT EXISTS tcping_archive (
                        id ${primaryKey},
                        target_id TEXT NOT NULL,
                        sid TEXT NOT NULL,
                        success_rate REAL,
                        avg_time REAL,
                        min_time REAL,
                        max_time REAL,
                        created_at INTEGER DEFAULT (${strftime}),
                        year INTEGER,
                        month INTEGER,
                        day INTEGER,
                        hour INTEGER,
                        minute INTEGER
                    )
                `);

                await client.query(`
                    CREATE TABLE IF NOT EXISTS ai_reports (
                        id ${primaryKey},
                        report_id TEXT UNIQUE NOT NULL,
                        title TEXT NOT NULL,
                        report_data TEXT NOT NULL,
                        metadata TEXT,
                        created_at INTEGER DEFAULT (${strftime}),
                        time_range_start INTEGER,
                        time_range_end INTEGER,
                        servers_analyzed INTEGER,
                        overall_score INTEGER,
                        status TEXT DEFAULT 'completed'
                    )
                `);

                return true;
            }
        },
        {
            version: 13,
            name: 'Initialize PostgreSQL configuration (deprecated)',
            async up(client) {
                try {
                    // 检查是否已有专用表（v14迁移）
                    let tableExists = false;
                    try {
                        if (DB.type === 'sqlite') {
                            const result = await DB.get("SELECT name FROM sqlite_master WHERE type='table' AND name='postgres_config'");
                            tableExists = !!result;
                        } else {
                            const result = await DB.get("SELECT tablename FROM pg_tables WHERE tablename = 'postgres_config'");
                            tableExists = !!result;
                        }
                    } catch (checkError) {
                        // 表检查失败，继续执行v13迁移
                    }
                    
                    if (tableExists) {
                        console.log('[Migration] 专用PostgreSQL配置表已存在，跳过v2迁移');
                        return true;
                    }
                    
                    // 检查是否已存在postgresql_config配置
                    const existing = await DB.get("SELECT * FROM setting WHERE key = 'postgresql_config'");
                    
                    if (!existing) {
                        console.log('[Migration] 初始化PostgreSQL配置（JSON格式，将在v3迁移到专用表）...');
                        
                        // 插入默认的PostgreSQL配置
                        const defaultConfig = {
                            enabled: false,
                            host: "",
                            port: 5432,
                            database: "",
                            username: "",
                            password: "",
                            updated_at: new Date().toISOString(),
                            version: "1.0",
                            migration_created: true
                        };
                        
                        await DB.run(
                            "INSERT INTO setting (key, val) VALUES (?, ?)",
                            ['postgresql_config', JSON.stringify(defaultConfig)]
                        );
                        
                        console.log('[Migration] PostgreSQL配置初始化完成（将在v3迁移到专用表）');
                    } else {
                        console.log('[Migration] PostgreSQL配置已存在，跳过初始化');
                    }
                    
                    return true;
                } catch (error) {
                    console.error('[Migration] PostgreSQL配置初始化失败:', error);
                    return false;
                }
            }
        },
        {
            version: 14,
            name: 'Create PostgreSQL configuration table',
            async up(client) {
                try {
                    console.log('[Migration] 创建PostgreSQL专用配置表...');
                    
                    // 创建专用的PostgreSQL配置表
                    const strftime = isPostgres 
                        ? "EXTRACT(EPOCH FROM NOW())::INTEGER" 
                        : "strftime('%s', 'now')";
                    const booleanDefault = isPostgres ? 'FALSE' : '0';
                    const primaryKeyDef = isPostgres ? 'SERIAL PRIMARY KEY' : 'INTEGER PRIMARY KEY AUTOINCREMENT';
                    
                    await DB.run(`
                        CREATE TABLE IF NOT EXISTS postgres_config (
                            id ${primaryKeyDef},
                            enabled BOOLEAN NOT NULL DEFAULT ${booleanDefault},
                            host TEXT NOT NULL DEFAULT '',
                            port INTEGER NOT NULL DEFAULT 5432,
                            database_name TEXT NOT NULL DEFAULT '',
                            username TEXT NOT NULL DEFAULT '',
                            password TEXT NOT NULL DEFAULT '',
                            connection_timeout INTEGER DEFAULT 5000,
                            query_timeout INTEGER DEFAULT 30000,
                            max_connections INTEGER DEFAULT 20,
                            created_at INTEGER DEFAULT (${strftime}),
                            updated_at INTEGER DEFAULT (${strftime}),
                            last_test_at INTEGER,
                            last_test_success BOOLEAN DEFAULT ${booleanDefault},
                            last_error TEXT DEFAULT '',
                            config_version TEXT DEFAULT '1.0',
                            notes TEXT DEFAULT ''
                        )
                    `);
                    
                    // 检查并迁移现有JSON配置
                    const existing = await DB.get("SELECT id FROM postgres_config WHERE id = 1");
                    if (!existing) {
                        console.log('[Migration] 检查现有PostgreSQL配置...');
                        
                        // 尝试从setting表读取现有JSON配置
                        let migratedConfig = null;
                        try {
                            const jsonConfig = await DB.get("SELECT val FROM setting WHERE key = 'postgresql_config'");
                            if (jsonConfig && jsonConfig.val) {
                                const parsed = JSON.parse(jsonConfig.val);
                                if (parsed && typeof parsed === 'object') {
                                    migratedConfig = parsed;
                                    console.log('[Migration] 发现现有JSON配置，准备迁移');
                                }
                            }
                        } catch (parseError) {
                            console.warn('[Migration] JSON配置解析失败，使用默认配置:', parseError.message);
                        }
                        
                        const now = Math.floor(Date.now() / 1000);
                        
                        if (migratedConfig) {
                            // 迁移现有配置 - 处理BOOLEAN类型兼容性
                            const enabledValue = isPostgres 
                                ? (migratedConfig.enabled ? true : false)
                                : (migratedConfig.enabled ? 1 : 0);
                            
                            await DB.run(`
                                INSERT INTO postgres_config 
                                (id, enabled, host, port, database_name, username, password, 
                                 created_at, updated_at, notes, config_version)
                                VALUES (1, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            `, [
                                enabledValue,
                                migratedConfig.host || '',
                                migratedConfig.port || 5432,
                                migratedConfig.database || '',
                                migratedConfig.username || '',
                                migratedConfig.password || '',
                                now, now,
                                'Migrated from JSON configuration',
                                '1.0'
                            ]);
                            console.log('[Migration] JSON配置迁移完成');
                        } else {
                            // 创建默认配置 - 处理BOOLEAN类型兼容性
                            const defaultEnabled = isPostgres ? false : 0;
                            await DB.run(`
                                INSERT INTO postgres_config (id, enabled, notes, created_at, updated_at)
                                VALUES (1, ?, 'Initial default configuration', ?, ?)
                            `, [defaultEnabled, now, now]);
                            console.log('[Migration] 创建默认PostgreSQL配置');
                        }
                    } else {
                        console.log('[Migration] PostgreSQL配置表已存在，跳过初始化');
                    }
                    
                    console.log('[Migration] PostgreSQL配置表创建完成');
                    return true;
                } catch (error) {
                    console.error('[Migration] PostgreSQL配置表创建失败:', error);
                    return false;
                }
            }
        },
        {
            version: 15,
            name: 'Fix SSH scripts table structure compatibility',
            async up(client) {
                try {
                    console.log('[Migration] 修复SSH脚本表结构兼容性...');
                    
                    // 检查SSH脚本表是否存在，如果不存在则创建完整表结构
                    // 对于DualAdapter，写操作使用SQLite，所以用SQLite语法检查
                    const isPostgresOnly = DB.type === 'postgresql';
                    const timeDefault = isPostgresOnly ? "NOW()" : "datetime('now')";
                    
                    const tableExists = isPostgresOnly
                        ? await DB.get("SELECT table_name FROM information_schema.tables WHERE table_name = 'ssh_scripts'")
                        : await DB.get("SELECT name FROM sqlite_master WHERE type='table' AND name='ssh_scripts'");
                    
                    if (!tableExists) {
                        console.log('[Migration] SSH脚本表不存在，创建完整表结构...');
                        await DB.run(`
                            CREATE TABLE ssh_scripts (
                                id TEXT PRIMARY KEY,
                                name TEXT NOT NULL,
                                content TEXT NOT NULL,
                                category TEXT DEFAULT 'custom',
                                description TEXT DEFAULT '',
                                variables TEXT DEFAULT '[]',
                                examples TEXT DEFAULT '[]',
                                tags TEXT DEFAULT '',
                                usage_count INTEGER DEFAULT 0,
                                created_at TEXT DEFAULT (${timeDefault}),
                                updated_at TEXT DEFAULT (${timeDefault})
                            )
                        `);
                        console.log('[Migration] SSH脚本表创建完成');
                        return true;
                    }
                    
                    // 表存在，检查并添加缺失的字段
                    const requiredColumns = [
                        { name: 'category', sql: "ALTER TABLE ssh_scripts ADD COLUMN category TEXT DEFAULT 'custom'" },
                        { name: 'description', sql: "ALTER TABLE ssh_scripts ADD COLUMN description TEXT DEFAULT ''" },
                        { name: 'variables', sql: "ALTER TABLE ssh_scripts ADD COLUMN variables TEXT DEFAULT '[]'" },
                        { name: 'examples', sql: "ALTER TABLE ssh_scripts ADD COLUMN examples TEXT DEFAULT '[]'" },
                        { name: 'tags', sql: "ALTER TABLE ssh_scripts ADD COLUMN tags TEXT DEFAULT ''" },
                        { name: 'usage_count', sql: "ALTER TABLE ssh_scripts ADD COLUMN usage_count INTEGER DEFAULT 0" },
                        { name: 'created_at', sqlTemplate: 'ALTER TABLE ssh_scripts ADD COLUMN created_at TEXT DEFAULT ($TIME_DEFAULT$)' },
                        { name: 'updated_at', sqlTemplate: 'ALTER TABLE ssh_scripts ADD COLUMN updated_at TEXT DEFAULT ($TIME_DEFAULT$)' }
                    ];
                    
                    let addedColumns = 0;
                    for (const column of requiredColumns) {
                        const exists = await columnExists('ssh_scripts', column.name);
                        if (!exists) {
                            console.log(`[Migration] 添加缺失字段: ${column.name}`);
                            const sql = column.sql || column.sqlTemplate.replace('$TIME_DEFAULT$', timeDefault);
                            await DB.run(sql);
                            addedColumns++;
                        }
                    }
                    
                    if (addedColumns > 0) {
                        console.log(`[Migration] SSH脚本表结构升级完成，添加了 ${addedColumns} 个字段`);
                    } else {
                        console.log('[Migration] SSH脚本表结构已是最新版本');
                    }
                    
                    return true;
                } catch (error) {
                    console.error('[Migration] SSH脚本表结构修复失败:', error);
                    return false;
                }
            }
        },
        {
            version: 16,
            name: 'Fix missing tables and columns for code compatibility',
            async up(client) {
                try {
                    console.log('[Migration] 修复缺失的表和字段以保证代码兼容性...');
                    
                    const isPostgres = DB.type === 'postgresql';
                    const strftime = isPostgres ? "EXTRACT(EPOCH FROM NOW())::INTEGER" : "strftime('%s', 'now')";
                    const primaryKey = isPostgres ? 'SERIAL PRIMARY KEY' : 'INTEGER PRIMARY KEY AUTOINCREMENT';
                    
                    // 1. 创建 traffic_calibration 表（如果不存在）
                    console.log('[Migration] 检查并创建 traffic_calibration 表...');
                    const tableExists = isPostgres
                        ? await DB.get("SELECT table_name FROM information_schema.tables WHERE table_name = 'traffic_calibration'")
                        : await DB.get("SELECT name FROM sqlite_master WHERE type='table' AND name='traffic_calibration'");
                    
                    if (!tableExists) {
                        await client.query(`
                            CREATE TABLE traffic_calibration (
                                sid TEXT PRIMARY KEY,
                                calibration_date INTEGER NOT NULL,
                                calibration_value BIGINT NOT NULL DEFAULT 0,
                                created_at INTEGER DEFAULT (${strftime}),
                                updated_at INTEGER DEFAULT (${strftime})
                            )
                        `);
                        console.log('[Migration] traffic_calibration 表创建成功');
                    } else {
                        console.log('[Migration] traffic_calibration 表已存在，跳过创建');
                    }
                    
                    // 2. 修复 load_m 表结构（添加 id 列）
                    console.log('[Migration] 检查并修复 load_m 表结构...');
                    const loadMHasId = await columnExists('load_m', 'id');
                    
                    if (!loadMHasId) {
                        console.log('[Migration] load_m 表缺少 id 列，开始重建表结构...');
                        
                        // 备份数据
                        await client.query('CREATE TABLE load_m_backup AS SELECT * FROM load_m');
                        
                        // 删除原表
                        await client.query('DROP TABLE load_m');
                        
                        // 重新创建带 id 的表
                        await client.query(`
                            CREATE TABLE load_m (
                                id ${primaryKey},
                                sid TEXT,
                                cpu REAL,
                                mem REAL,
                                swap REAL,
                                ibw REAL,
                                obw REAL,
                                expire_time INTEGER,
                                created_at INTEGER DEFAULT (${strftime})
                            )
                        `);
                        
                        // 恢复数据
                        await client.query(`
                            INSERT INTO load_m (sid, cpu, mem, swap, ibw, obw, expire_time, created_at)
                            SELECT sid, cpu, mem, swap, ibw, obw, expire_time, created_at FROM load_m_backup
                        `);
                        
                        // 删除备份表
                        await client.query('DROP TABLE load_m_backup');
                        
                        console.log('[Migration] load_m 表结构修复完成');
                    } else {
                        console.log('[Migration] load_m 表已有 id 列，跳过修复');
                    }
                    
                    // 3. 修复 load_h 表结构（添加 id 列）
                    console.log('[Migration] 检查并修复 load_h 表结构...');
                    const loadHHasId = await columnExists('load_h', 'id');
                    
                    if (!loadHHasId) {
                        console.log('[Migration] load_h 表缺少 id 列，开始重建表结构...');
                        
                        // 备份数据
                        await client.query('CREATE TABLE load_h_backup AS SELECT * FROM load_h');
                        
                        // 删除原表
                        await client.query('DROP TABLE load_h');
                        
                        // 重新创建带 id 的表
                        await client.query(`
                            CREATE TABLE load_h (
                                id ${primaryKey},
                                sid TEXT,
                                cpu REAL,
                                mem REAL,
                                swap REAL,
                                ibw REAL,
                                obw REAL,
                                expire_time INTEGER,
                                created_at INTEGER DEFAULT (${strftime})
                            )
                        `);
                        
                        // 恢复数据
                        await client.query(`
                            INSERT INTO load_h (sid, cpu, mem, swap, ibw, obw, expire_time, created_at)
                            SELECT sid, cpu, mem, swap, ibw, obw, expire_time, created_at FROM load_h_backup
                        `);
                        
                        // 删除备份表
                        await client.query('DROP TABLE load_h_backup');
                        
                        console.log('[Migration] load_h 表结构修复完成');
                    } else {
                        console.log('[Migration] load_h 表已有 id 列，跳过修复');
                    }
                    
                    console.log('[Migration] 表结构兼容性修复完成');
                    return true;
                } catch (error) {
                    console.error('[Migration] 表结构兼容性修复失败:', error);
                    return false;
                }
            }
        },
        {
            version: 17,
            name: 'Add missing server table columns',
            async up(client) {
                try {
                    console.log('[Migration] 添加servers表缺失字段');
                    
                    // 检查并添加 display_index 字段
                    if (!await columnExists('servers', 'display_index')) {
                        await safeExecute('ALTER TABLE servers ADD COLUMN display_index INTEGER DEFAULT 0');
                        console.log('[Migration] 添加 display_index 字段成功');
                    }
                    
                    // 检查并添加 disabled 字段
                    if (!await columnExists('servers', 'disabled')) {
                        await safeExecute('ALTER TABLE servers ADD COLUMN disabled INTEGER DEFAULT 0');
                        console.log('[Migration] 添加 disabled 字段成功');
                    }
                    
                    // 检查并添加 deleted_at 字段
                    if (!await columnExists('servers', 'deleted_at')) {
                        await safeExecute('ALTER TABLE servers ADD COLUMN deleted_at INTEGER');
                        console.log('[Migration] 添加 deleted_at 字段成功');
                    }
                    
                    console.log('[Migration] servers表字段修复完成');
                    return true;
                } catch (error) {
                    console.error('[Migration] servers表字段修复失败:', error);
                    return false;
                }
            }
        },
        {
            version: 22,
            name: 'Add performance indexes for tcping tables (SQLite/PG)',
            async up(client) {
                try {
                    const tables = ['tcping_m', 'tcping_5m', 'tcping_h', 'tcping_d', 'tcping_month'];
                    for (const table of tables) {
                        // 按目标+时间
                        await DB.run(`CREATE INDEX IF NOT EXISTS idx_${table}_target_time ON ${table} (target_id, created_at)`);
                        // 按节点+时间
                        await DB.run(`CREATE INDEX IF NOT EXISTS idx_${table}_sid_time ON ${table} (sid, created_at)`);
                        // 组合索引：目标+节点+时间（常见过滤组合）
                        await DB.run(`CREATE INDEX IF NOT EXISTS idx_${table}_target_sid_time ON ${table} (target_id, sid, created_at)`);
                    }

                    // SQLite 优化提示（忽略PG）
                    if (DB.type === 'sqlite') {
                        try { await DB.run('ANALYZE'); } catch (e) {}
                        try { await DB.run('PRAGMA optimize'); } catch (e) {}
                    }
                    return true;
                } catch (error) {
                    console.error('[Migration] 创建 tcping 表索引失败:', error);
                    return false;
                }
            }
        },
        {
            version: 18,
            name: 'Re-apply tcping indexes for DB >= 17',
            async up(client) {
                try {
                    const tables = ['tcping_m', 'tcping_5m', 'tcping_h', 'tcping_d', 'tcping_month'];
                    for (const table of tables) {
                        await DB.run(`CREATE INDEX IF NOT EXISTS idx_${table}_target_time ON ${table} (target_id, created_at)`);
                        await DB.run(`CREATE INDEX IF NOT EXISTS idx_${table}_sid_time ON ${table} (sid, created_at)`);
                        await DB.run(`CREATE INDEX IF NOT EXISTS idx_${table}_target_sid_time ON ${table} (target_id, sid, created_at)`);
                    }
                    
                    // 只在纯SQLite模式下执行这些优化命令
                    if (DB.type === 'sqlite' || DB.type === 'sqlite3') {
                        try { await DB.run('ANALYZE'); } catch (e) {}
                        try { await DB.run('PRAGMA optimize'); } catch (e) {}
                    }
                    return true;
                } catch (error) {
                    console.error('[Migration] v18 索引创建失败:', error);
                    return false;
                }
            }
        },
        {
            version: 19,
            name: 'Fix groups table top field type for PostgreSQL compatibility',
            async up(client) {
                try {
                    console.log('[Migration] 修复groups表top字段类型以兼容PostgreSQL...');
                    
                    if (isPostgres) {
                        // PostgreSQL: 修改top字段类型从INTEGER到BIGINT
                        // 先检查字段当前类型
                        const columnInfo = await DB.get(`
                            SELECT data_type 
                            FROM information_schema.columns 
                            WHERE table_name = 'groups' AND column_name = 'top'
                        `);
                        
                        if (columnInfo && columnInfo.data_type === 'integer') {
                            console.log('[Migration] 将groups.top字段从INTEGER改为BIGINT...');
                            await DB.run('ALTER TABLE groups ALTER COLUMN top TYPE BIGINT');
                            console.log('[Migration] groups表字段类型修复完成');
                        } else {
                            console.log('[Migration] groups.top字段已是BIGINT类型，跳过修改');
                        }
                    } else {
                        // SQLite: INTEGER类型可以存储大数值，无需修改
                        console.log('[Migration] SQLite环境，groups.top字段无需修改');
                    }
                    
                    return true;
                } catch (error) {
                    console.error('[Migration] groups表字段类型修复失败:', error);
                    return false;
                }
            }
        },
        {
            version: 20,
            name: 'Fix servers table top field type for PostgreSQL compatibility',
            async up(client) {
                try {
                    console.log('[Migration] 修复servers表top字段类型以兼容PostgreSQL...');
                    
                    if (isPostgres) {
                        // PostgreSQL: 修改servers表top字段类型从INTEGER到BIGINT
                        // 先检查字段当前类型
                        const columnInfo = await DB.get(`
                            SELECT data_type 
                            FROM information_schema.columns 
                            WHERE table_name = 'servers' AND column_name = 'top'
                        `);
                        
                        if (columnInfo && columnInfo.data_type === 'integer') {
                            console.log('[Migration] 将servers.top字段从INTEGER改为BIGINT...');
                            await DB.run('ALTER TABLE servers ALTER COLUMN top TYPE BIGINT');
                            console.log('[Migration] servers表top字段类型修复完成');
                        } else {
                            console.log('[Migration] servers.top字段已是BIGINT类型，跳过修改');
                        }
                    } else {
                        // SQLite: INTEGER类型可以存储大数值，无需修改
                        console.log('[Migration] SQLite环境，servers.top字段无需修改');
                    }
                    
                    return true;
                } catch (error) {
                    console.error('[Migration] servers表字段类型修复失败:', error);
                    return false;
                }
            }
        },
        {
            version: 21,
            name: 'Add node_id column to tcping tables for PostgreSQL compatibility',
            async up(client) {
                try {
                    console.log('[Migration] 添加tcping表缺失的node_id字段...');
                    
                    // tcping表列表
                    const tcpingTables = ['tcping_m', 'tcping_5m', 'tcping_h', 'tcping_d', 'tcping_month'];
                    
                    for (const table of tcpingTables) {
                        // 检查node_id字段是否存在
                        const hasNodeId = await columnExists(table, 'node_id');
                        
                        if (!hasNodeId) {
                            console.log(`[Migration] 向${table}表添加node_id字段...`);
                            await DB.run(`ALTER TABLE ${table} ADD COLUMN node_id TEXT`);
                            console.log(`[Migration] ${table}表node_id字段添加成功`);
                        } else {
                            console.log(`[Migration] ${table}表已有node_id字段，跳过`);
                        }
                    }
                    
                    return true;
                } catch (error) {
                    console.error('[Migration] tcping表node_id字段添加失败:', error);
                    return false;
                }
            }
        },
        {
            version: 23,
            name: 'Mark unused online time fields for future cleanup',
            async up(client) {
                try {
                    console.log('[Migration] 标记未使用的在线时间字段...');
                    
                    // 这些字段未被代码使用：last_online_time, last_offline_time, total_online_time
                    // 实际使用的是 last_online 字段
                    // 为了保持向后兼容性，暂时保留这些字段，仅做标记
                    
                    // 可选：将来可以考虑以下操作
                    // 1. 将有效数据迁移到 last_online 字段
                    // 2. 在确认所有部署都更新后删除这些字段
                    
                    console.log('[Migration] 未使用字段标记完成，保留以维持兼容性');
                    console.log('[Migration] 受影响的字段：last_online_time, last_offline_time, total_online_time');
                    console.log('[Migration] 实际使用的字段：last_online');
                    
                    return true;
                } catch (error) {
                    console.error('[Migration] 未使用字段标记失败:', error);
                    return false;
                }
            }
        },
        {
            version: 24,
            name: 'Add is_default field to groups table',
            async up(client) {
                try {
                    console.log('[Migration] 为groups表添加is_default字段...');
                    
                    // 检查字段是否已存在
                    const hasIsDefault = await columnExists('groups', 'is_default');
                    if (hasIsDefault) {
                        console.log('[Migration] is_default字段已存在，跳过创建');
                        return true;
                    }
                    
                    // 添加is_default字段，默认为false
                    const defaultValue = isPostgres ? 'FALSE' : '0';
                    await client.query(`ALTER TABLE groups ADD COLUMN is_default ${isPostgres ? 'BOOLEAN' : 'INTEGER'} DEFAULT ${defaultValue}`);
                    
                    // 将原有的'default'分组设为默认分组
                    const trueValue = isPostgres ? 'TRUE' : '1';
                    await client.query(`UPDATE groups SET is_default = ${trueValue} WHERE id = 'default'`);
                    
                    console.log('[Migration] is_default字段添加成功，已将default分组设为默认');
                    return true;
                } catch (error) {
                    console.error('[Migration] 添加is_default字段失败:', error);
                    return false;
                }
            }
        }
        ,
        {
            version: 25,
            name: 'Create notification_tasks table for flexible per-node alerts',
            async up(client) {
                try {
                    console.log('[Migration] 创建 notification_tasks 表...');
                    const isPg = DB.type === 'postgresql';
                    const nowExpr = isPg ? "EXTRACT(EPOCH FROM NOW())::INTEGER" : "strftime('%s', 'now')";
                    await client.query(`
                        CREATE TABLE IF NOT EXISTS notification_tasks (
                            id TEXT PRIMARY KEY,
                            sid TEXT NOT NULL,
                            enabled ${isPg ? 'BOOLEAN' : 'INTEGER'} DEFAULT ${isPg ? 'TRUE' : 1},
                            type TEXT DEFAULT 'traffic',
                            period TEXT DEFAULT 'monthly',
                            traffic_limit BIGINT,
                            thresholds TEXT,
                            direction TEXT DEFAULT 'both',
                            reset_day INTEGER,
                            last_notified INTEGER DEFAULT 0,
                            last_period_start INTEGER DEFAULT 0,
                            created_at INTEGER DEFAULT (${nowExpr}),
                            updated_at INTEGER DEFAULT (${nowExpr})
                        )
                    `);
                    // 索引以便按服务器查询
                    await client.query(`CREATE INDEX IF NOT EXISTS idx_notification_tasks_sid ON notification_tasks (sid)`);
                    console.log('[Migration] notification_tasks 表检查/创建完成');
                    return true;
                } catch (error) {
                    console.error('[Migration] 创建 notification_tasks 表失败:', error);
                    return false;
                }
            }
        },
        {
            version: 26,
            name: 'Create email_config and email_logs tables',
            async up(client) {
                try {
                    const isPg = DB.type === 'postgresql';
                    const nowExpr = isPg ? "EXTRACT(EPOCH FROM NOW())::INTEGER" : "strftime('%s', 'now')";
                    await client.query(`
                        CREATE TABLE IF NOT EXISTS email_config (
                            id TEXT PRIMARY KEY,
                            enabled ${isPg ? 'BOOLEAN' : 'INTEGER'} DEFAULT ${isPg ? 'FALSE' : 0},
                            host TEXT,
                            port INTEGER,
                            secure ${isPg ? 'BOOLEAN' : 'INTEGER'} DEFAULT ${isPg ? 'FALSE' : 0},
                            auth_user TEXT,
                            auth_pass TEXT,
                            from_name TEXT,
                            from_address TEXT,
                            default_to TEXT,
                            default_cc TEXT,
                            default_bcc TEXT,
                            notification_types TEXT,
                            rate_limit_per_min INTEGER DEFAULT 60,
                            timezone TEXT DEFAULT 'Asia/Shanghai',
                            language TEXT DEFAULT 'zh-CN',
                            updated_at INTEGER DEFAULT (${nowExpr})
                        )
                    `);
                    await client.query(`
                        CREATE TABLE IF NOT EXISTS email_logs (
                            id TEXT PRIMARY KEY,
                            to TEXT,
                            cc TEXT,
                            bcc TEXT,
                            subject TEXT,
                            body_text TEXT,
                            body_html TEXT,
                            template TEXT,
                            payload TEXT,
                            type TEXT,
                            status TEXT,
                            provider_message_id TEXT,
                            error TEXT,
                            created_at INTEGER DEFAULT (${nowExpr}),
                            sent_at INTEGER
                        )
                    `);
                    await client.query(`CREATE INDEX IF NOT EXISTS idx_email_logs_created ON email_logs (created_at)`);
                    await client.query(`CREATE INDEX IF NOT EXISTS idx_email_logs_type ON email_logs (type)`);
                    return true;
                } catch (error) {
                    console.error('[Migration] 创建邮件表失败:', error);
                    return false;
                }
            }
        }
    ];

    async function migrate() {
        await initMigrationTable();
        
        // 修复历史遗留问题：处理版本号冲突
        await fixLegacyMigrationIssues();
        
        const currentVersion = await getCurrentVersion();
        console.log('当前数据库版本:', currentVersion);

        for (const migration of migrations) {
            if (migration.version > currentVersion) {
                console.log(`开始执行迁移: ${migration.name} (版本 ${migration.version})`);
                try {
                    const success = await migration.up({
                        query: async (sql, params) => await DB.run(sql, params)
                    });
                    if (success) {
                        await recordMigration(migration.version, migration.name, 'success');
                        console.log(`迁移成功: ${migration.name}`);
                    } else {
                        await recordMigration(migration.version, migration.name, 'failed');
                        console.error(`迁移失败: ${migration.name}`);
                    }
                } catch (err) {
                    console.error(`迁移出错: ${migration.name}`, err);
                    await recordMigration(migration.version, migration.name, 'failed');
                }
            }
        }
    }

    return {
        migrate,
        getCurrentVersion
    };
};
