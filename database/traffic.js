"use strict";
module.exports = (DB) => {
    function shift(a) { a.shift(); a.push([0, 0]); return a; }

    const traffic = {
        async ins(sid) {
            const hs = JSON.stringify(Array(24).fill().map(() => [0, 0]));
            const ds = JSON.stringify(Array(31).fill().map(() => [0, 0]));
            const ms = JSON.stringify(Array(12).fill().map(() => [0, 0]));
            
            if (DB.type === 'sqlite') {
                await DB.run("INSERT OR IGNORE INTO traffic (sid,hs,ds,ms) VALUES ($1,$2,$3,$4)", [sid, hs, ds, ms]);
            } else {
                await DB.run("INSERT INTO traffic (sid,hs,ds,ms) VALUES ($1,$2,$3,$4) ON CONFLICT (sid) DO NOTHING", [sid, hs, ds, ms]);
            }
        },
        async qry(sid) {
            return await DB.get("SELECT * FROM traffic WHERE sid=$1", [sid]);
        },
        async get(sid) {
            const t = await DB.get("SELECT hs,ds,ms FROM traffic WHERE sid=$1", [sid]);
            if (t) return { hs: JSON.parse(t.hs), ds: JSON.parse(t.ds), ms: JSON.parse(t.ms) };
            
            // 使用 INSERT OR IGNORE，不会抛出错误
            await this.ins(sid);
            
            // 再次查询
            const t2 = await DB.get("SELECT hs,ds,ms FROM traffic WHERE sid=$1", [sid]);
            if (t2) return { hs: JSON.parse(t2.hs), ds: JSON.parse(t2.ds), ms: JSON.parse(t2.ms) };
            
            // 如果还是没有，返回默认值
            return {
                hs: Array(24).fill().map(() => [0, 0]),
                ds: Array(31).fill().map(() => [0, 0]),
                ms: Array(12).fill().map(() => [0, 0])
            };
        },
        async UPD(sid, hs, ds, ms) {
            await DB.run("UPDATE traffic SET hs=$1,ds=$2,ms=$3 WHERE sid=$4", [JSON.stringify(hs), JSON.stringify(ds), JSON.stringify(ms), sid]);
        },
        async upd_sid(sid, newsid) {
            await DB.run("UPDATE traffic SET sid=$1 WHERE sid=$2", [newsid, sid]);
        },
        async get_hs(sid) {
            const result = await DB.get("SELECT hs FROM traffic WHERE sid=$1", [sid]);
            return JSON.parse(result.hs);
        },
        async upd_hs(sid, hs) {
            await DB.run("UPDATE traffic SET hs=$1 WHERE sid=$2", [JSON.stringify(hs), sid]);
        },
        async get_ds(sid) {
            const result = await DB.get("SELECT ds FROM traffic WHERE sid=$1", [sid]);
            return JSON.parse(result.ds);
        },
        async upd_ds(sid, ds) {
            await DB.run("UPDATE traffic SET ds=$1 WHERE sid=$2", [JSON.stringify(ds), sid]);
        },
        async get_ms(sid) {
            const result = await DB.get("SELECT ms FROM traffic WHERE sid=$1", [sid]);
            return JSON.parse(result.ms);
        },
        async upd_ms(sid, ms) {
            await DB.run("UPDATE traffic SET ms=$1 WHERE sid=$2", [JSON.stringify(ms), sid]);
        },
        async del(sid) {
            await DB.run("DELETE FROM traffic WHERE sid=$1", [sid]);
        },
        async all() {
            return await DB.all("SELECT * FROM traffic");
        },
        async add(sid, tf) {
            const { hs, ds, ms } = await this.get(sid);
            hs[23][0] += tf[0]; ds[30][0] += tf[0]; ms[11][0] += tf[0];
            hs[23][1] += tf[1]; ds[30][1] += tf[1]; ms[11][1] += tf[1];
            await this.UPD(sid, hs, ds, ms);
        },
        async shift_hs() {
            for (const { sid, hs } of await this.all()) {
                await this.upd_hs(sid, shift(JSON.parse(hs)));
            }
        },
        async shift_ds() {
            for (const { sid, ds } of await this.all()) {
                await this.upd_ds(sid, shift(JSON.parse(ds)));
            }
        },
        async shift_ms() {
            for (const { sid, ms } of await this.all()) {
                await this.upd_ms(sid, shift(JSON.parse(ms)));
            }
        },
        // 批量添加流量增量
        async batchAdd(additions) {
            if (!additions || additions.length === 0) return;
            
            // 1. 批量获取现有流量数据
            const sids = additions.map(item => item.sid);
            const placeholders = sids.map(() => '?').join(',');
            const existingData = await DB.all(`SELECT * FROM traffic WHERE sid IN (${placeholders})`, sids);
            
            // 2. 创建数据映射
            const dataMap = new Map();
            existingData.forEach(row => {
                dataMap.set(row.sid, {
                    hs: JSON.parse(row.hs),
                    ds: JSON.parse(row.ds), 
                    ms: JSON.parse(row.ms)
                });
            });
            
            // 3. 处理新增数据（对于不存在的sid，先创建默认数据）
            const updates = [];
            const inserts = [];
            
            for (const { sid, tf } of additions) {
                if (dataMap.has(sid)) {
                    // 更新现有数据
                    const data = dataMap.get(sid);
                    data.hs[23][0] += tf[0]; data.ds[30][0] += tf[0]; data.ms[11][0] += tf[0];
                    data.hs[23][1] += tf[1]; data.ds[30][1] += tf[1]; data.ms[11][1] += tf[1];
                    updates.push({ sid, ...data });
                } else {
                    // 创建新记录
                    const defaultData = {
                        hs: Array(24).fill().map(() => [0, 0]),
                        ds: Array(31).fill().map(() => [0, 0]),
                        ms: Array(12).fill().map(() => [0, 0])
                    };
                    defaultData.hs[23][0] = tf[0]; defaultData.ds[30][0] = tf[0]; defaultData.ms[11][0] = tf[0];
                    defaultData.hs[23][1] = tf[1]; defaultData.ds[30][1] = tf[1]; defaultData.ms[11][1] = tf[1];
                    inserts.push({ sid, ...defaultData });
                }
            }
            
            // 4. 批量执行数据库操作
            if (DB.type === 'sqlite') {
                await DB.run('BEGIN TRANSACTION');
                try {
                    // 批量插入新记录
                    for (const { sid, hs, ds, ms } of inserts) {
                        await DB.run("INSERT OR IGNORE INTO traffic (sid,hs,ds,ms) VALUES (?,?,?,?)", 
                                    [sid, JSON.stringify(hs), JSON.stringify(ds), JSON.stringify(ms)]);
                    }
                    // 批量更新现有记录
                    for (const { sid, hs, ds, ms } of updates) {
                        await DB.run("UPDATE traffic SET hs=?,ds=?,ms=? WHERE sid=?", 
                                    [JSON.stringify(hs), JSON.stringify(ds), JSON.stringify(ms), sid]);
                    }
                    await DB.run('COMMIT');
                } catch (error) {
                    await DB.run('ROLLBACK');
                    throw error;
                }
            } else {
                // PostgreSQL批量操作（暂时使用单条操作，后续可优化为UPSERT）
                for (const { sid, hs, ds, ms } of inserts) {
                    await DB.run("INSERT INTO traffic (sid,hs,ds,ms) VALUES ($1,$2,$3,$4) ON CONFLICT (sid) DO NOTHING", 
                                [sid, JSON.stringify(hs), JSON.stringify(ds), JSON.stringify(ms)]);
                }
                for (const { sid, hs, ds, ms } of updates) {
                    await DB.run("UPDATE traffic SET hs=$1,ds=$2,ms=$3 WHERE sid=$4", 
                                [JSON.stringify(hs), JSON.stringify(ds), JSON.stringify(ms), sid]);
                }
            }
        }
    };

    const lt = {
        async ins(sid, traffic = [0, 0]) {
            await DB.run(`INSERT INTO lt (sid,traffic) VALUES ($1,$2)`, [sid, JSON.stringify(traffic)]);
            return { sid, traffic };
        },
        async get(sid) {
            const x = await DB.get(`SELECT * FROM lt WHERE sid=$1`, [sid]);
            if (x) x.traffic = JSON.parse(x.traffic);
            return x;
        },
        async set(sid, traffic) {
            return await DB.run(`UPDATE lt SET traffic=$1 WHERE sid=$2`, [JSON.stringify(traffic), sid]);
        },
        async del(sid) {
            await DB.run(`DELETE FROM lt WHERE sid=$1`, [sid]);
        },
        // 批量查询lt记录
        async batchGet(sids) {
            if (!sids || sids.length === 0) return [];
            
            const placeholders = sids.map(() => '?').join(',');
            const query = `SELECT * FROM lt WHERE sid IN (${placeholders})`;
            const results = await DB.all(query, sids);
            
            // 解析traffic字段并创建Map便于快速查找
            const ltMap = new Map();
            results.forEach(row => {
                ltMap.set(row.sid, {
                    sid: row.sid,
                    traffic: JSON.parse(row.traffic)
                });
            });
            
            return ltMap;
        },
        // 批量更新lt记录
        async batchSet(updates) {
            if (!updates || updates.length === 0) return;
            
            // 使用事务确保原子性
            if (DB.type === 'sqlite' || DB.type === 'dual') {
                await DB.run('BEGIN TRANSACTION');
                try {
                    for (const { sid, traffic } of updates) {
                        await DB.run(`UPDATE lt SET traffic=? WHERE sid=?`, [JSON.stringify(traffic), sid]);
                    }
                    await DB.run('COMMIT');
                } catch (error) {
                    await DB.run('ROLLBACK');
                    throw error;
                }
            } else {
                // PostgreSQL使用批量更新
                const values = updates.map(({ sid, traffic }) => `('${sid}', '${JSON.stringify(traffic)}')`).join(',');
                const query = `
                    UPDATE lt SET traffic = tmp.traffic 
                    FROM (VALUES ${values}) AS tmp(sid, traffic) 
                    WHERE lt.sid = tmp.sid
                `;
                await DB.run(query);
            }
        },
    };
    return { traffic, lt };
};