"use strict";

module.exports = (DB) => {
  const TABLE = 'email_config';
  const DEFAULT_ID = 'default';

  function S(v) { return JSON.stringify(v); }

  const emailConfig = {
    async get(id = DEFAULT_ID) {
      const row = await DB.get(`SELECT * FROM ${TABLE} WHERE id=$1`, [id]);
      if (!row) return null;
      return {
        ...row,
        default_to: row.default_to ? JSON.parse(row.default_to) : [],
        default_cc: row.default_cc ? JSON.parse(row.default_cc) : [],
        default_bcc: row.default_bcc ? JSON.parse(row.default_bcc) : [],
        notification_types: row.notification_types ? JSON.parse(row.notification_types) : {}
      };
    },
    async set(config, id = DEFAULT_ID) {
      const sql = `
        INSERT INTO ${TABLE} (
          id, enabled, host, port, secure, auth_user, auth_pass, from_name, from_address,
          default_to, default_cc, default_bcc, notification_types, rate_limit_per_min, timezone, language, updated_at
        ) VALUES (
          $1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16, strftime('%s','now')
        ) ON CONFLICT(id) DO UPDATE SET
          enabled=$2, host=$3, port=$4, secure=$5, auth_user=$6, auth_pass=$7, from_name=$8, from_address=$9,
          default_to=$10, default_cc=$11, default_bcc=$12, notification_types=$13, rate_limit_per_min=$14,
          timezone=$15, language=$16, updated_at=strftime('%s','now')
      `;
      const params = [
        id,
        config.enabled ? 1 : 0,
        config.host || null,
        config.port || null,
        config.secure ? 1 : 0,
        config.auth_user || null,
        config.auth_pass || null,
        config.from_name || null,
        config.from_address || null,
        S(config.default_to || []),
        S(config.default_cc || []),
        S(config.default_bcc || []),
        S(config.notification_types || {}),
        config.rate_limit_per_min || 60,
        config.timezone || 'Asia/Shanghai',
        config.language || 'zh-CN'
      ];
      await DB.run(sql, params);
      return true;
    }
  };

  return { emailConfig };
};

