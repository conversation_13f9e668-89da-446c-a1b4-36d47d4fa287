"use strict";

/**
 * SQL参数处理工具类
 * 提供统一的参数格式转换和处理机制
 */
class SqlParameterUtils {
    /**
     * 解析SQL中的参数占位符
     * @param {string} sql - SQL语句
     * @returns {Object} - 解析结果
     */
    static parseParameters(sql) {
        // 匹配 $1, $2, $3... 格式的参数
        const pgParams = sql.match(/\$(\d+)/g) || [];
        const paramNumbers = pgParams.map(p => parseInt(p.substring(1)));
        
        return {
            originalSql: sql,
            pgParameters: pgParams,
            paramNumbers: paramNumbers,
            maxParamIndex: paramNumbers.length > 0 ? Math.max(...paramNumbers) : 0,
            hasParameters: pgParams.length > 0
        };
    }

    /**
     * 将PostgreSQL风格的SQL转换为SQLite风格
     * @param {string} sql - 原始SQL
     * @param {Array} params - 参数数组
     * @returns {Object} - 转换结果
     */
    static convertToSQLite(sql, params = []) {
        const parseResult = this.parseParameters(sql);
        
        if (!parseResult.hasParameters) {
            // 处理没有参数的UPSERT语法
            return { 
                sql: this._adaptUpsertSyntax(sql, 'sqlite'), 
                params 
            };
        }

        // 检查参数数量是否匹配
        if (parseResult.maxParamIndex > params.length) {
            throw new Error(
                `参数不足: SQL需要${parseResult.maxParamIndex}个参数，但只提供了${params.length}个`
            );
        }

        // 按出现顺序收集参数
        const orderedParams = [];
        const paramMap = new Map();
        let convertedSql = sql;
        
        // 按照在SQL中出现的顺序处理参数
        const matches = [...sql.matchAll(/\$(\d+)/g)];
        
        for (const match of matches) {
            const paramIndex = parseInt(match[1]);
            const paramValue = params[paramIndex - 1]; // $1对应数组索引0
            
            if (!paramMap.has(paramIndex)) {
                paramMap.set(paramIndex, orderedParams.length);
                orderedParams.push(paramValue);
            }
        }

        // 替换所有 $n 为 ?
        convertedSql = convertedSql.replace(/\$\d+/g, '?');
        
        // 处理UPSERT语法
        convertedSql = this._adaptUpsertSyntax(convertedSql, 'sqlite');

        return {
            sql: convertedSql,
            params: orderedParams
        };
    }

    /**
     * 将SQLite风格的SQL转换为PostgreSQL风格
     * @param {string} sql - 原始SQL (使用?占位符)
     * @param {Array} params - 参数数组
     * @returns {Object} - 转换结果
     */
    static convertToPostgreSQL(sql, params = []) {
        // 计算?占位符的数量
        const questionMarks = (sql.match(/\?/g) || []).length;
        
        if (questionMarks === 0) {
            // 处理没有参数的UPSERT语法
            return { 
                sql: this._adaptUpsertSyntax(sql, 'postgresql'), 
                params 
            };
        }

        // 检查参数数量是否匹配
        if (questionMarks > params.length) {
            throw new Error(
                `参数不足: SQL需要${questionMarks}个参数，但只提供了${params.length}个`
            );
        }

        // 将?依次替换为$1, $2, $3...
        let paramIndex = 1;
        const convertedSql = sql.replace(/\?/g, () => `$${paramIndex++}`);
        
        // 处理UPSERT语法
        const adaptedSql = this._adaptUpsertSyntax(convertedSql, 'postgresql');

        return {
            sql: adaptedSql,
            params: params.slice(0, questionMarks) // 只使用需要的参数
        };
    }

    /**
     * 验证参数完整性
     * @param {string} sql - SQL语句
     * @param {Array} params - 参数数组
     * @returns {Object} - 验证结果
     */
    static validateParameters(sql, params = []) {
        const parseResult = this.parseParameters(sql);
        
        const validation = {
            isValid: true,
            errors: [],
            warnings: [],
            paramCount: parseResult.maxParamIndex,
            providedCount: params.length
        };

        if (parseResult.hasParameters) {
            // 检查参数数量
            if (parseResult.maxParamIndex > params.length) {
                validation.isValid = false;
                validation.errors.push(
                    `参数不足: 需要${parseResult.maxParamIndex}个，提供${params.length}个`
                );
            }

            // 检查参数连续性
            const sortedNumbers = [...parseResult.paramNumbers].sort((a, b) => a - b);
            for (let i = 0; i < sortedNumbers.length - 1; i++) {
                if (sortedNumbers[i + 1] - sortedNumbers[i] > 1) {
                    validation.warnings.push(
                        `参数编号不连续: $${sortedNumbers[i]} 到 $${sortedNumbers[i + 1]}`
                    );
                }
            }

            // 检查是否从$1开始
            if (sortedNumbers.length > 0 && sortedNumbers[0] !== 1) {
                validation.warnings.push(
                    `参数编号不从$1开始: 第一个参数是$${sortedNumbers[0]}`
                );
            }
        }

        return validation;
    }

    /**
     * 为调试提供详细的参数信息
     * @param {string} sql - SQL语句
     * @param {Array} params - 参数数组
     * @returns {Object} - 调试信息
     */
    static getDebugInfo(sql, params = []) {
        const parseResult = this.parseParameters(sql);
        const validation = this.validateParameters(sql, params);
        const sqliteResult = parseResult.hasParameters ? 
            this.convertToSQLite(sql, params) : { sql, params };

        // 检测SQL使用的占位符类型
        const hasPgStyle = parseResult.hasParameters;
        const hasSqliteStyle = (sql.match(/\?/g) || []).length > 0;
        
        let postgresqlResult;
        if (hasSqliteStyle && !hasPgStyle) {
            // 如果是SQLite风格，转换为PostgreSQL
            postgresqlResult = this.convertToPostgreSQL(sql, params);
        } else {
            // 已经是PostgreSQL风格或无参数
            postgresqlResult = { sql, params };
        }

        return {
            original: {
                sql,
                params,
                paramCount: params.length
            },
            parsed: parseResult,
            validation,
            converted: {
                sqlite: sqliteResult,
                postgresql: postgresqlResult
            }
        };
    }

    /**
     * 适配UPSERT语法（INSERT ... ON CONFLICT）
     * 处理PostgreSQL和SQLite之间的UPSERT语法差异
     * 
     * @param {string} sql - 原始SQL语句
     * @param {string} targetDb - 目标数据库类型 ('sqlite' 或 'postgresql')
     * @returns {string} - 适配后的SQL语句
     */
    static _adaptUpsertSyntax(sql, targetDb) {
        // 检测是否包含ON CONFLICT语句
        if (!sql.match(/ON\s+CONFLICT/i)) {
            return sql;
        }

        if (targetDb === 'sqlite') {
            // PostgreSQL -> SQLite
            // 1. DO NOTHING -> DO NOTHING (兼容)
            // 2. DO UPDATE SET -> DO UPDATE SET (兼容)
            // 3. 处理EXCLUDED关键字：PostgreSQL的EXCLUDED在SQLite中也支持
            
            // 处理可能的PostgreSQL特有语法
            let adaptedSql = sql;
            
            // 处理RETURNING子句（SQLite 3.35.0+支持，但语法略有不同）
            adaptedSql = adaptedSql.replace(/\s+RETURNING\s+\*/gi, ' RETURNING *');
            
            // 处理复杂的UPDATE SET子句
            adaptedSql = this._processUpdateSetClause(adaptedSql, 'sqlite');
            
            return adaptedSql;
        } else if (targetDb === 'postgresql') {
            // SQLite -> PostgreSQL
            // 大部分语法是兼容的，但需要处理一些细节
            
            let adaptedSql = sql;
            
            // 处理UPDATE SET子句
            adaptedSql = this._processUpdateSetClause(adaptedSql, 'postgresql');
            
            // 确保CONFLICT目标格式正确
            adaptedSql = this._normalizeConflictTarget(adaptedSql);
            
            return adaptedSql;
        }

        return sql;
    }

    /**
     * 处理UPDATE SET子句中的特殊语法
     * 主要处理类型转换和EXCLUDED引用
     * 
     * @param {string} sql - SQL语句
     * @param {string} targetDb - 目标数据库
     * @returns {string} - 处理后的SQL
     */
    static _processUpdateSetClause(sql, targetDb) {
        // 匹配DO UPDATE SET子句
        const updateMatch = sql.match(/DO\s+UPDATE\s+SET\s+(.+?)(?:\s+WHERE|\s+RETURNING|\s*$)/is);
        
        if (!updateMatch) {
            return sql;
        }

        const setClause = updateMatch[1];
        let processedSetClause = setClause;

        if (targetDb === 'sqlite') {
            // PostgreSQL -> SQLite
            // 处理类型转换运算符 :: 
            // PostgreSQL使用 ::jsonb, ::json, ::text, ::integer等
            // SQLite不支持这种语法，需要移除
            processedSetClause = processedSetClause.replace(/::jsonb/gi, '');
            processedSetClause = processedSetClause.replace(/::json/gi, '');
            processedSetClause = processedSetClause.replace(/::text/gi, '');
            processedSetClause = processedSetClause.replace(/::integer/gi, '');
            processedSetClause = processedSetClause.replace(/::bigint/gi, '');
            processedSetClause = processedSetClause.replace(/::boolean/gi, '');
            processedSetClause = processedSetClause.replace(/::timestamp/gi, '');
            processedSetClause = processedSetClause.replace(/::date/gi, '');
            processedSetClause = processedSetClause.replace(/::numeric/gi, '');
            processedSetClause = processedSetClause.replace(/::decimal/gi, '');
            processedSetClause = processedSetClause.replace(/::real/gi, '');
            processedSetClause = processedSetClause.replace(/::double\s+precision/gi, '');
            
            // 处理EXCLUDED前缀（SQLite和PostgreSQL都支持，但确保格式一致）
            processedSetClause = this._normalizeExcludedReferences(processedSetClause);
        } else if (targetDb === 'postgresql') {
            // SQLite -> PostgreSQL
            // 处理json函数调用 -> ::jsonb
            // SQLite可能使用json()函数，PostgreSQL使用类型转换
            processedSetClause = processedSetClause.replace(/json\((EXCLUDED\.\w+)\)/gi, '$1::jsonb');
            
            // 确保EXCLUDED引用格式正确
            processedSetClause = this._normalizeExcludedReferences(processedSetClause);
        }

        // 重建SQL语句
        return sql.replace(setClause, processedSetClause);
    }

    /**
     * 规范化EXCLUDED引用
     * 确保EXCLUDED关键字的大小写一致性
     * 
     * @param {string} clause - SET子句
     * @returns {string} - 规范化后的子句
     */
    static _normalizeExcludedReferences(clause) {
        // 确保EXCLUDED引用的格式一致
        // 处理 excluded.column 和 EXCLUDED.column 的大小写差异
        // 统一使用大写EXCLUDED
        return clause.replace(/\bexcluded\./gi, 'EXCLUDED.');
    }

    /**
     * 规范化CONFLICT目标
     * 确保ON CONFLICT子句的格式正确
     * 
     * @param {string} sql - SQL语句
     * @returns {string} - 规范化后的SQL
     */
    static _normalizeConflictTarget(sql) {
        // 确保ON CONFLICT (column) 格式正确
        // 处理可能的空格问题
        sql = sql.replace(/ON\s+CONFLICT\s*\(\s*(\w+)\s*\)/gi, 'ON CONFLICT ($1)');
        
        // 处理复合唯一约束 ON CONFLICT (col1, col2)
        sql = sql.replace(/ON\s+CONFLICT\s*\(\s*([^)]+?)\s*\)/gi, (match, columns) => {
            // 移除多余的空格
            const cleanedColumns = columns.split(',').map(col => col.trim()).join(', ');
            return `ON CONFLICT (${cleanedColumns})`;
        });
        
        return sql;
    }
}

module.exports = SqlParameterUtils;