"use strict";

/**
 * 时间函数兼容性工具类
 * 提供SQLite和PostgreSQL之间的时间函数兼容性支持
 */
class TimeFunctionUtils {
    /**
     * 获取Unix时间戳的SQL表达式
     * @param {string} dbType - 数据库类型 ('sqlite' | 'postgresql')
     * @returns {string} SQL表达式
     */
    static getUnixTimestamp(dbType) {
        return dbType === 'sqlite' 
            ? "strftime('%s', 'now')" 
            : "EXTRACT(EPOCH FROM NOW())::INTEGER";
    }
    
    /**
     * 获取当前日期时间的SQL表达式
     * @param {string} dbType - 数据库类型 ('sqlite' | 'postgresql')
     * @returns {string} SQL表达式
     */
    static getCurrentDateTime(dbType) {
        return dbType === 'sqlite' 
            ? "datetime('now')" 
            : "NOW()";
    }
    
    /**
     * 获取Unix时间戳加偏移量的SQL表达式
     * @param {string} dbType - 数据库类型 ('sqlite' | 'postgresql')
     * @param {number} offsetSeconds - 偏移秒数
     * @returns {string} SQL表达式
     */
    static getUnixTimestampWithOffset(dbType, offsetSeconds) {
        return dbType === 'sqlite' 
            ? `strftime('%s', 'now') + ${offsetSeconds}`
            : `EXTRACT(EPOCH FROM NOW())::INTEGER + ${offsetSeconds}`;
    }
    
    /**
     * 获取当前时间加偏移量的SQL表达式
     * @param {string} dbType - 数据库类型 ('sqlite' | 'postgresql')
     * @param {number} offsetDays - 偏移天数
     * @returns {string} SQL表达式
     */
    static getDateTimeWithOffset(dbType, offsetDays) {
        if (offsetDays >= 0) {
            return dbType === 'sqlite' 
                ? `datetime('now', '+${offsetDays} days')`
                : `NOW() + INTERVAL '${offsetDays} days'`;
        } else {
            const positiveDays = Math.abs(offsetDays);
            return dbType === 'sqlite' 
                ? `datetime('now', '-${positiveDays} days')`
                : `NOW() - INTERVAL '${positiveDays} days'`;
        }
    }
    
    /**
     * 获取指定时间字段的Unix时间戳
     * @param {string} dbType - 数据库类型 ('sqlite' | 'postgresql')
     * @param {string} timeField - 时间字段名
     * @returns {string} SQL表达式
     */
    static getUnixTimestampFromField(dbType, timeField) {
        return dbType === 'sqlite' 
            ? `strftime('%s', ${timeField})`
            : `EXTRACT(EPOCH FROM ${timeField})::INTEGER`;
    }
    
    /**
     * 根据数据库类型格式化时间相关的SQL语句
     * @param {string} sql - 包含时间函数的SQL语句
     * @param {string} dbType - 数据库类型 ('sqlite' | 'postgresql')
     * @returns {string} 格式化后的SQL语句
     */
    static formatTimeSQL(sql, dbType) {
        let formattedSQL = sql;
        
        // 替换常见的时间函数模式
        const patterns = [
            // strftime('%s', 'now') + 数字
            {
                regex: /strftime\('%s',\s*'now'\)\s*\+\s*(\d+)/g,
                replacement: (match, offset) => this.getUnixTimestampWithOffset(dbType, parseInt(offset))
            },
            // strftime('%s', 'now')
            {
                regex: /strftime\('%s',\s*'now'\)/g,
                replacement: () => this.getUnixTimestamp(dbType)
            },
            // datetime('now')
            {
                regex: /datetime\('now'\)/g,
                replacement: () => this.getCurrentDateTime(dbType)
            },
            // datetime('now', '+N days') 或 datetime('now', '-N days')
            {
                regex: /datetime\('now',\s*'([+-])(\d+)\s+days?'\)/g,
                replacement: (match, sign, days) => {
                    const offsetDays = sign === '+' ? parseInt(days) : -parseInt(days);
                    return this.getDateTimeWithOffset(dbType, offsetDays);
                }
            }
        ];
        
        patterns.forEach(pattern => {
            formattedSQL = formattedSQL.replace(pattern.regex, pattern.replacement);
        });
        
        return formattedSQL;
    }
    
    /**
     * 获取适合当前环境的时间函数工具实例
     * @param {Object} dbConfig - 数据库配置对象，包含type属性
     * @returns {Object} 包含格式化方法的工具对象
     */
    static createAdapter(dbConfig) {
        const dbType = dbConfig.type || 'sqlite';
        
        return {
            dbType,
            getUnixTimestamp: () => this.getUnixTimestamp(dbType),
            getCurrentDateTime: () => this.getCurrentDateTime(dbType),
            getUnixTimestampWithOffset: (offsetSeconds) => this.getUnixTimestampWithOffset(dbType, offsetSeconds),
            getDateTimeWithOffset: (offsetDays) => this.getDateTimeWithOffset(dbType, offsetDays),
            formatSQL: (sql) => this.formatTimeSQL(sql, dbType)
        };
    }
}

module.exports = TimeFunctionUtils;