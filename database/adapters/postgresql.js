"use strict";

const { Pool } = require("pg");
const { parse } = require("pg-connection-string");
const BaseAdapter = require("./base");

class PostgresAdapter extends BaseAdapter {
    constructor(config) {
        super(config);
        this.type = 'postgresql';
        this.pool = null;
        this.activeTransactions = new Set(); // 跟踪活跃的事务客户端
    }

    async connect() {
        const connectionConfig = typeof this.config.connection === 'string'
            ? parse(this.config.connection)
            : this.config.connection;
        
        // 添加连接池配置参数
        const poolConfig = {
            ...connectionConfig,
            max: 10,                           // 降低到10个连接（避免超出服务器限制）
            idleTimeoutMillis: 10000,         // 空闲连接超时 10秒（加快连接回收）
            connectionTimeoutMillis: 5000,     // 连接超时 5秒
            query_timeout: 30000,              // 查询超时 30秒
            statement_timeout: 30000,          // 语句超时 30秒
            idle_in_transaction_session_timeout: 30000, // 事务中空闲超时 30秒
            // 新增：连接池监控
            log: (msg) => {
                if (msg.includes('connection') || msg.includes('pool')) {
                    console.log(`[DB Pool] ${msg}`);
                }
            }
        };
        
        this.pool = new Pool(poolConfig);
        try {
            await this.pool.query("SELECT 1");
            console.log("[数据库] PostgreSQL 连接成功");
            console.log(`[数据库] 连接池配置: max=${poolConfig.max}, connectionTimeout=${poolConfig.connectionTimeoutMillis}ms, queryTimeout=${poolConfig.query_timeout}ms`);
        } catch (error) {
            console.error("[数据库] PostgreSQL 连接失败:", error.message);
            throw error;
        }
    }

    async disconnect() {
        if (this.pool) {
            // 停止连接池监控
            this.stopPoolMonitoring();
            
            // 关闭所有活跃事务
            this.activeTransactions.clear();
            
            // 结束连接池
            await this.pool.end();
            this.pool = null;
            console.log("[数据库] PostgreSQL 连接已断开");
        }
    }

    /**
     * PostgreSQL特定的SQL适配方法
     * 处理SQLite语法到PostgreSQL的转换，包括参数格式和UPSERT语法
     * 
     * @param {string} sql - 原始SQL语句
     * @param {Array} params - 参数数组
     * @returns {Object} - 适配后的SQL和参数
     */
    _adaptSqlForDatabase(sql, params) {
        // 使用SqlParameterUtils将SQLite风格(?)转换为PostgreSQL风格($1,$2,$3)
        // 同时处理UPSERT语法的兼容性转换
        const SqlParameterUtils = require('./sql-parameter-utils');
        return SqlParameterUtils.convertToPostgreSQL(sql, params);
    }

    async query(sql, params = []) {
        if (!this.pool) {
            throw new Error("数据库未连接");
        }
        
        const { sql: adaptedSql, params: adaptedParams } = this._preprocessSqlAndParams(sql, params);
        
        const client = await this.pool.connect();
        try {
            // 设置查询超时（每个查询30秒）
            await client.query('SET statement_timeout = 30000');
            
            const result = await client.query(adaptedSql, adaptedParams);
            return result.rows;
        } catch (error) {
            if (this.debugMode) {
                console.error('[PostgreSQL] Query执行失败:', {
                    originalSql: sql,
                    adaptedSql,
                    originalParams: params,
                    adaptedParams,
                    error: error.message
                });
            }
            throw error;
        } finally {
            client.release();
        }
    }

    async get(sql, params = []) {
        const rows = await this.query(sql, params);
        return rows[0];
    }

    async all(sql, params = []) {
        return this.query(sql, params);
    }

    async run(sql, params = []) {
        if (!this.pool) {
            throw new Error("数据库未连接");
        }
        
        const { sql: adaptedSql, params: adaptedParams } = this._preprocessSqlAndParams(sql, params);
        
        const client = await this.pool.connect();
        try {
            const result = await client.query(adaptedSql, adaptedParams);
            return {
                changes: result.rowCount,
                lastInsertRowid: result.rows.length > 0 ? result.rows[0].id : null,
            };
        } catch (error) {
            if (this.debugMode) {
                console.error('[PostgreSQL] Run执行失败:', {
                    originalSql: sql,
                    adaptedSql,
                    originalParams: params,
                    adaptedParams,
                    error: error.message
                });
            }
            throw error;
        } finally {
            client.release();
        }
    }

    async beginTransaction() {
        if (!this.pool) {
            throw new Error("数据库未连接");
        }
        
        const client = await this.pool.connect();
        
        // 检测事务嵌套 - PostgreSQL使用客户端级别检测
        if (this.activeTransactions.has(client)) {
            client.release();
            throw new Error("事务嵌套检测：PostgreSQL客户端已存在活跃事务");
        }
        
        await client.query("BEGIN");
        this.activeTransactions.add(client);
        return client;
    }

    async commitTransaction(client) {
        try {
            await client.query("COMMIT");
            this.activeTransactions.delete(client); // 移除事务跟踪
        } finally {
            client.release();
        }
    }

    async rollbackTransaction(client) {
        try {
            await client.query("ROLLBACK");
            this.activeTransactions.delete(client); // 移除事务跟踪
        } finally {
            client.release();
        }
    }

    // SQLite兼容方法：pragma操作（PostgreSQL不支持，仅兼容性）
    pragma(pragma) {
        console.warn(`[PostgreSQL] pragma操作不支持: ${pragma}`);
        return null;
    }

    // SQLite兼容方法：exec操作（PostgreSQL使用query替代）
    async exec(sql) {
        if (!this.pool) {
            throw new Error("数据库未连接");
        }
        const client = await this.pool.connect();
        try {
            const result = await client.query(sql);
            return result;
        } finally {
            client.release();
        }
    }

    // SQLite兼容方法：prepare操作（PostgreSQL不支持prepared statements，返回模拟对象）
    prepare(sql) {
        console.warn(`[PostgreSQL] prepare操作不完全支持，使用模拟对象: ${sql}`);
        return {
            get: async (params = []) => {
                const rows = await this.query(sql, params);
                return rows[0];
            },
            all: async (params = []) => {
                return await this.query(sql, params);
            },
            run: async (params = []) => {
                const client = await this.pool.connect();
                try {
                    const result = await client.query(sql, params);
                    return {
                        changes: result.rowCount,
                        lastInsertRowid: result.rows.length > 0 ? result.rows[0].id : null,
                    };
                } finally {
                    client.release();
                }
            }
        };
    }

    // SQLite兼容方法：事务包装器（PostgreSQL用客户端实现）
    async transaction(fn) {
        const client = await this.beginTransaction();
        try {
            await fn(client);
            await this.commitTransaction(client);
        } catch (error) {
            await this.rollbackTransaction(client);
            throw error;
        }
    }
    
    /**
     * 获取连接池状态信息
     * @returns {Object} 连接池状态
     */
    getPoolStatus() {
        if (!this.pool) {
            return { status: 'disconnected' };
        }
        
        return {
            status: 'connected',
            totalCount: this.pool.totalCount,       // 总连接数
            idleCount: this.pool.idleCount,         // 空闲连接数
            waitingCount: this.pool.waitingCount,   // 等待连接的客户端数
            max: this.pool.options.max,             // 最大连接数配置
            // 计算使用率
            usage: {
                active: this.pool.totalCount - this.pool.idleCount,
                percentage: ((this.pool.totalCount - this.pool.idleCount) / this.pool.options.max * 100).toFixed(2) + '%'
            }
        };
    }
    
    /**
     * 开始连接池监控（定期输出状态）
     * @param {number} intervalMs - 监控间隔（毫秒）
     */
    startPoolMonitoring(intervalMs = 30000) {
        if (this.poolMonitorInterval) {
            clearInterval(this.poolMonitorInterval);
        }
        
        this.poolMonitorInterval = setInterval(() => {
            const status = this.getPoolStatus();
            if (status.status === 'connected') {
                console.log(`[DB Pool Monitor] 连接池状态 - 活跃: ${status.usage.active}/${status.max} (${status.usage.percentage}), 空闲: ${status.idleCount}, 等待: ${status.waitingCount}`);
                
                // 如果使用率超过80%，发出警告
                if (parseFloat(status.usage.percentage) > 80) {
                    console.warn(`[DB Pool Monitor] ⚠️ 连接池使用率较高: ${status.usage.percentage}`);
                }
            }
        }, intervalMs);
        
        console.log(`[DB Pool Monitor] 已启动连接池监控，间隔: ${intervalMs}ms`);
    }
    
    /**
     * 停止连接池监控
     */
    stopPoolMonitoring() {
        if (this.poolMonitorInterval) {
            clearInterval(this.poolMonitorInterval);
            this.poolMonitorInterval = null;
            console.log('[DB Pool Monitor] 已停止连接池监控');
        }
    }

    backup(backupPath) {
        const fs = require('fs');
        const path = require('path');
        const { spawn } = require('child_process');
        
        return new Promise((resolve, reject) => {
            try {
                const backupDir = path.dirname(backupPath);
                if (!fs.existsSync(backupDir)) {
                    fs.mkdirSync(backupDir, { recursive: true });
                }

                const connectionConfig = typeof this.config.connection === 'string'
                    ? require('pg-connection-string').parse(this.config.connection)
                    : this.config.connection;

                const env = {
                    ...process.env,
                    PGPASSWORD: connectionConfig.password
                };

                const args = [
                    '-h', connectionConfig.host || 'localhost',
                    '-p', connectionConfig.port || '5432',
                    '-U', connectionConfig.user || connectionConfig.username,
                    '-d', connectionConfig.database,
                    '-f', backupPath,
                    '--verbose'
                ];

                console.log('开始 PostgreSQL 备份...');
                const pgDump = spawn('pg_dump', args, { env });

                let stderr = '';
                pgDump.stderr.on('data', (data) => {
                    stderr += data.toString();
                    console.log('备份日志:', data.toString().trim());
                });

                pgDump.on('close', (code) => {
                    if (code === 0) {
                        console.log('PostgreSQL 备份完成');
                        resolve();
                    } else {
                        console.error('PostgreSQL 备份失败，退出码:', code);
                        console.error('错误信息:', stderr);
                        
                        if (stderr.includes('pg_dump: command not found') || stderr.includes('not found')) {
                            reject(new Error('pg_dump 命令未找到，请确保 PostgreSQL 客户端工具已安装'));
                        } else {
                            reject(new Error(`备份失败: ${stderr || `退出码 ${code}`}`));
                        }
                    }
                });

                pgDump.on('error', (error) => {
                    console.error('PostgreSQL 备份进程错误:', error);
                    if (error.code === 'ENOENT') {
                        reject(new Error('pg_dump 命令未找到，请确保 PostgreSQL 客户端工具已安装'));
                    } else {
                        reject(error);
                    }
                });

            } catch (error) {
                console.error('PostgreSQL 备份初始化失败:', error);
                reject(error);
            }
        });
    }
}

module.exports = PostgresAdapter;
