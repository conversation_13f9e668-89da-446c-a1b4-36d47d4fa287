"use strict";

const SqlParameterUtils = require('./sql-parameter-utils');

class BaseAdapter {
    constructor(config) {
        this.config = config;
        this.debugMode = config?.debug || false;
    }

    /**
     * 预处理SQL和参数，确保兼容性
     * @param {string} sql - 原始SQL语句
     * @param {Array} params - 参数数组
     * @returns {Object} - 处理后的SQL和参数
     */
    _preprocessSqlAndParams(sql, params = []) {
        // 验证参数
        const validation = SqlParameterUtils.validateParameters(sql, params);
        
        if (!validation.isValid) {
            const error = new Error(`SQL参数验证失败: ${validation.errors.join(', ')}`);
            if (this.debugMode) {
                console.error('[数据库] SQL参数错误:', SqlParameterUtils.getDebugInfo(sql, params));
            }
            throw error;
        }

        // 记录警告
        if (this.debugMode && validation.warnings.length > 0) {
            console.warn('[数据库] SQL参数警告:', validation.warnings.join(', '));
        }

        // 各适配器自己实现具体的转换逻辑
        return this._adaptSqlForDatabase(sql, params);
    }

    /**
     * 子类需要实现的SQL适配方法
     * @param {string} sql - 原始SQL语句
     * @param {Array} params - 参数数组
     * @returns {Object} - 适配后的SQL和参数
     */
    _adaptSqlForDatabase(sql, params) {
        // 默认实现：原样返回
        return { sql, params };
    }

    async connect() {
        throw new Error("connect() must be implemented by subclass");
    }

    async disconnect() {
        throw new Error("disconnect() must be implemented by subclass");
    }

    async query(sql, params = []) {
        throw new Error("query() must be implemented by subclass");
    }

    async get(sql, params = []) {
        throw new Error("get() must be implemented by subclass");
    }

    async all(sql, params = []) {
        throw new Error("all() must be be implemented by subclass");
    }

    async run(sql, params = []) {
        throw new Error("run() must be implemented by subclass");
    }

    async beginTransaction() {
        throw new Error("beginTransaction() must be implemented by subclass");
    }

    async commitTransaction() {
        throw new Error("commitTransaction() must be implemented by subclass");
    }

    async rollbackTransaction() {
        throw new Error("rollbackTransaction() must be implemented by subclass");
    }
}

module.exports = BaseAdapter;
