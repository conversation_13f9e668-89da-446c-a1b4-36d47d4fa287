"use strict";

const Database = require("better-sqlite3");
const BaseAdapter = require("./base");

class SQLiteAdapter extends BaseAdapter {
    constructor(config) {
        super(config);
        this.type = 'sqlite';
        this.db = null;
        this.transactionActive = false; // 事务状态跟踪
    }

    async connect() {
        this.db = new Database(this.config.path);
        this.db.pragma("journal_mode = WAL");
        this.db.pragma("synchronous = NORMAL");
        this.db.pragma("cache_size = -64000");
        this.db.pragma("temp_store = MEMORY");
        this.db.pragma("mmap_size = 268435456");
        this.db.pragma("busy_timeout = 10000"); // 🔥 增强：延长到10秒，减少BUSY错误
        this.db.pragma("wal_autocheckpoint = 1000");
        this.db.pragma("foreign_keys = ON"); // 🔥 增强：启用外键约束
        console.log("[数据库] SQLite 连接成功 (WAL模式已优化)");
    }

    async disconnect() {
        if (this.db) {
            this.db.close();
            this.db = null;
            console.log("[数据库] SQLite 连接已断开");
        }
    }

    /**
     * SQLite特定的SQL适配方法
     * 处理PostgreSQL语法到SQLite的转换，包括参数格式和UPSERT语法
     * 
     * @param {string} sql - 原始SQL语句
     * @param {Array} params - 参数数组
     * @returns {Object} - 适配后的SQL和参数
     */
    _adaptSqlForDatabase(sql, params) {
        const SqlParameterUtils = require('./sql-parameter-utils');
        // 使用增强的转换方法，已包含UPSERT语法处理
        let result = SqlParameterUtils.convertToSQLite(sql, params);
        
        // 添加OFFSET语法适配 - 修复事务中的OFFSET查询
        // SQLite要求OFFSET必须与LIMIT一起使用
        if (result.sql.includes('OFFSET') && !result.sql.includes('LIMIT')) {
            result.sql = result.sql.replace(/(\s+ORDER\s+BY[^O]+?)OFFSET\s+(\?)/gi, '$1LIMIT -1 OFFSET $2');
        }
        
        // 添加其他PostgreSQL语法适配
        // PostgreSQL的EXTRACT(EPOCH FROM NOW()) -> SQLite的strftime('%s', 'now')
        if (result.sql.includes('EXTRACT(EPOCH FROM NOW())')) {
            result.sql = result.sql.replace(/EXTRACT\(EPOCH FROM NOW\(\)\)::INTEGER/gi, "strftime('%s', 'now')");
        }
        
        return result;
    }

    async query(sql, params = []) {
        if (!this.db) {
            throw new Error("数据库未连接");
        }
        
        const { sql: adaptedSql, params: adaptedParams } = this._preprocessSqlAndParams(sql, params);
        
        try {
            return this.db.prepare(adaptedSql).all(adaptedParams);
        } catch (error) {
            if (this.debugMode) {
                console.error('[SQLite] Query执行失败:', {
                    originalSql: sql,
                    adaptedSql,
                    originalParams: params,
                    adaptedParams,
                    error: error.message
                });
            }
            throw error;
        }
    }

    async get(sql, params = []) {
        if (!this.db) {
            throw new Error("数据库未连接");
        }
        
        const { sql: adaptedSql, params: adaptedParams } = this._preprocessSqlAndParams(sql, params);
        
        try {
            return this.db.prepare(adaptedSql).get(adaptedParams);
        } catch (error) {
            if (this.debugMode) {
                console.error('[SQLite] Get执行失败:', {
                    originalSql: sql,
                    adaptedSql,
                    originalParams: params,
                    adaptedParams,
                    error: error.message
                });
            }
            throw error;
        }
    }

    async all(sql, params = []) {
        if (!this.db) {
            throw new Error("数据库未连接");
        }
        
        const { sql: adaptedSql, params: adaptedParams } = this._preprocessSqlAndParams(sql, params);
        
        try {
            return this.db.prepare(adaptedSql).all(adaptedParams);
        } catch (error) {
            if (this.debugMode) {
                console.error('[SQLite] All执行失败:', {
                    originalSql: sql,
                    adaptedSql,
                    originalParams: params,
                    adaptedParams,
                    error: error.message
                });
            }
            throw error;
        }
    }

    async run(sql, params = []) {
        if (!this.db) {
            throw new Error("数据库未连接");
        }
        
        const { sql: adaptedSql, params: adaptedParams } = this._preprocessSqlAndParams(sql, params);
        
        try {
            return this.db.prepare(adaptedSql).run(adaptedParams);
        } catch (error) {
            if (this.debugMode) {
                console.error('[SQLite] Run执行失败:', {
                    originalSql: sql,
                    adaptedSql,
                    originalParams: params,
                    adaptedParams,
                    error: error.message
                });
            }
            throw error;
        }
    }

    async beginTransaction() {
        // 🔥 强化事务管理 - 严格防止嵌套
        if (this.transactionActive) {
            // 等待现有事务完成（最多等待1秒）
            let retries = 0;
            while (this.transactionActive && retries < 10) {
                await new Promise(resolve => setTimeout(resolve, 100));
                retries++;
            }
            
            if (this.transactionActive) {
                console.warn('[SQLite] 事务等待超时，强制使用现有事务');
                return this._createTransactionClient();
            }
        }
        
        try {
            this.db.prepare("BEGIN IMMEDIATE").run(); // 使用 IMMEDIATE 获得立即锁
            this.transactionActive = true;
        } catch (error) {
            if (error.message.includes('already within a transaction')) {
                console.warn('[SQLite] 已在事务中，复用现有事务');
                this.transactionActive = true;
            } else {
                throw error;
            }
        }
        
        // 返回模拟的client对象，与PostgreSQL接口保持一致
        return this._createTransactionClient();
    }

    _createTransactionClient() {
        return {
            query: async (sql, params = []) => {
                const { sql: adaptedSql, params: adaptedParams } = this._preprocessSqlAndParams(sql, params);
                
                // 根据SQL类型选择合适的方法
                const sqlUpper = adaptedSql.trim().toUpperCase();
                if (sqlUpper.startsWith('SELECT') || sqlUpper.startsWith('WITH')) {
                    // 查询语句使用all()
                    const result = this.db.prepare(adaptedSql).all(adaptedParams);
                    return { rows: result }; // 模拟PostgreSQL的结果格式
                } else {
                    // 修改语句使用run()
                    const result = this.db.prepare(adaptedSql).run(adaptedParams);
                    return { 
                        rows: [], 
                        rowCount: result.changes,
                        insertId: result.lastInsertRowid
                    };
                }
            },
            release: () => {}, // SQLite不需要释放连接
            adapter: this, // 保持对原适配器的引用
            isTransaction: true
        };
    }

    async commitTransaction(client = null) {
        this.db.prepare("COMMIT").run();
        this.transactionActive = false; // 重置事务状态
        // 兼容旧接口和新接口
    }

    async rollbackTransaction(client = null) {
        this.db.prepare("ROLLBACK").run();
        this.transactionActive = false; // 重置事务状态
        // 兼容旧接口和新接口
    }

    // SQLite特有方法：pragma操作
    pragma(pragma) {
        if (!this.db) {
            throw new Error("数据库未连接");
        }
        return this.db.pragma(pragma);
    }

    // SQLite特有方法：exec操作（用于ATTACH/DETACH等）
    exec(sql) {
        if (!this.db) {
            throw new Error("数据库未连接");
        }
        return this.db.exec(sql);
    }

    // SQLite特有方法：prepare操作（用于复杂查询）
    prepare(sql) {
        if (!this.db) {
            throw new Error("数据库未连接");
        }
        return this.db.prepare(sql);
    }

    // SQLite特有方法：事务包装器（用于热恢复）
    transaction(fn) {
        if (!this.db) {
            throw new Error("数据库未连接");
        }
        return this.db.transaction(fn);
    }

    // SQLite特有方法：iterate操作（用于流式数据处理）
    iterate(sql, params = []) {
        if (!this.db) {
            throw new Error("数据库未连接");
        }
        return this.db.prepare(sql).iterate(params);
    }

    async backup(backupPath) {
        if (!this.db) {
            throw new Error("数据库未连接");
        }
        
        const fs = require('fs');
        const path = require('path');
        
        try {
            const backupDir = path.dirname(backupPath);
            if (!fs.existsSync(backupDir)) {
                fs.mkdirSync(backupDir, { recursive: true });
            }
            
            try {
                const progressCallback = (progress) => {
                    const { totalPages, remainingPages } = progress;
                    const completed = totalPages - remainingPages;
                    const percentage = Math.round((completed / totalPages) * 100);
                    console.log(`备份进度: ${completed}/${totalPages} 页 (${percentage}%)`);
                };
                
                console.log('开始 SQLite 数据库备份...');
                await this.db.backup(backupPath, {
                    progress: progressCallback
                });
                console.log('SQLite 备份完成');
            } catch (backupError) {
                console.warn('新版 API 备份失败，尝试文件复制方式:', backupError.message);
                
                // 在文件复制前执行checkpoint，确保WAL中的数据写入主文件
                try {
                    this.db.pragma('wal_checkpoint(TRUNCATE)');
                    console.log('执行WAL checkpoint成功');
                } catch (checkpointError) {
                    console.warn('WAL checkpoint失败，继续备份:', checkpointError.message);
                }
                
                const currentPath = this.config.path;
                fs.copyFileSync(currentPath, backupPath);
                console.log('使用文件复制方式备份完成');
            }
        } catch (error) {
            console.error('备份失败:', error);
            throw error;
        }
    }
}

module.exports = SQLiteAdapter;
