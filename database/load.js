'use strict'

function pad(arr, len) {
    for (var i = arr.length; i < len; ++i)
        arr.unshift({ cpu: 0, mem: 0, swap: 0, ibw: 0, obw: 0 });
    return arr;
}

module.exports = (DB) => {
    const TimeFunctionUtils = require('./adapters/time-function-utils');
    
    function gen(table, len) {
        return {
            len,
            async ins(sid) {
                const expireTimeSQL = TimeFunctionUtils.getUnixTimestampWithOffset(DB.type, 86400);
                const sql = `
                    INSERT INTO ${table} (sid, cpu, mem, swap, ibw, obw, expire_time)
                    VALUES ($1, 0, 0, 0, 0, 0, ${expireTimeSQL})
                `;
                await DB.run(sql, [sid]);
            },
            async select(sid) {
                const rows = await this.selectRaw(sid);
                return pad(rows, this.len);
            },
            async selectRaw(sid) {
                const sql = `
                    SELECT * FROM ${table}
                    WHERE sid = $1
                    ORDER BY created_at DESC
                    LIMIT $2
                `;
                return await DB.all(sql, [sid, this.len]);
            },
            async count(sid) {
                const sql = `SELECT COUNT(*) as count FROM ${table} WHERE sid = $1`;
                const result = await DB.get(sql, [sid]);
                return result.count;
            },
            async shift(sid, { cpu, mem, swap, ibw, obw }) {
                const client = await DB.beginTransaction();
                try {
                    // 确保LIMIT不会为负数，当len <= 1时跳过删除操作
                    if (this.len > 1) {
                        const delSql = `
                            DELETE FROM ${table}
                            WHERE id IN (
                                SELECT id FROM ${table}
                                WHERE sid = $1
                                ORDER BY created_at DESC
                                OFFSET $2
                            )
                        `;
                        await client.query(delSql, [sid, this.len - 1]);
                    }

                    const insSql = `
                        INSERT INTO ${table} (sid, cpu, mem, swap, ibw, obw, created_at)
                        VALUES ($1, $2, $3, $4, $5, $6, EXTRACT(EPOCH FROM NOW())::INTEGER)
                    `;
                    await client.query(insSql, [sid, cpu, mem, swap, ibw, obw]);

                    await DB.commitTransaction(client);
                } catch (err) {
                    await DB.rollbackTransaction(client);
                    console.error(`Error in shift operation for ${table}:`, err);
                    throw err;
                }
            },
            async del_sid(sid) {
                await DB.run(`DELETE FROM ${table} WHERE sid = $1`, [sid]);
            },
            async cleanup() {
                const now = Math.floor(Date.now() / 1000);
                await DB.run(`DELETE FROM ${table} WHERE expire_time < $1`, [now]);
            }
        };
    }

    async function cleanupArchiveData() {
        const now = Math.floor(Date.now() / 1000);
        let archiveHours = 3;
        try {
            const config = await DB.get('SELECT val FROM setting WHERE key = $1', ['data_retention_archive_hours']);
            if (config && config.val) {
                archiveHours = parseInt(config.val) || 3;
            }
        } catch (err) {
            console.error('获取归档数据保留配置失败，使用默认值3小时:', err);
        }
        const cutoffTime = now - (archiveHours * 3600);
        await DB.run(`DELETE FROM load_archive WHERE created_at < $1`, [cutoffTime]);
        await DB.run(`DELETE FROM load_archive WHERE cpu <= 0 AND mem <= 0 AND swap <= 0`);

        const duplicates = await DB.all(`
            SELECT sid, created_at, COUNT(*) as count
            FROM load_archive
            GROUP BY sid, created_at
            HAVING COUNT(*) > 1
        `);

        if (duplicates.length > 0) {
            const client = await DB.beginTransaction();
            try {
                for (const dup of duplicates) {
                    const records = await client.query(`
                        SELECT id FROM load_archive
                        WHERE sid = $1 AND created_at = $2
                        ORDER BY id DESC
                    `, [dup.sid, dup.created_at]);

                    for (let i = 1; i < records.rows.length; i++) {
                        await client.query(`DELETE FROM load_archive WHERE id = $1`, [records.rows[i].id]);
                    }
                }
                await DB.commitTransaction(client);
            } catch (error) {
                await DB.rollbackTransaction(client);
                console.error(`清理重复数据失败:`, error);
            }
        }
    }

    async function cleanupMinuteData() {
        const now = Math.floor(Date.now() / 1000);
        let minuteDays = 14;
        try {
            const config = await DB.get('SELECT val FROM setting WHERE key = $1', ['data_retention_minute_days']);
            if (config && config.val) {
                minuteDays = parseInt(config.val) || 14;
            }
        } catch (err) {
            console.error('获取分钟级数据保留配置失败，使用默认值14天:', err);
        }
        const cutoffTime = now - (minuteDays * 24 * 60 * 60);
        try {
            const { changes } = await DB.run(`DELETE FROM load_m WHERE created_at < $1`, [cutoffTime]);
            if (changes > 0) {
                console.log(`已清理 ${changes} 条过期的分钟级负载数据（超过14天）`);
            }
        } catch (error) {
            console.error('清理分钟级负载数据失败:', error);
        }
    }

    async function cleanupHourData() {
        const now = Math.floor(Date.now() / 1000);
        let hourDays = 90;
        try {
            const config = await DB.get('SELECT val FROM setting WHERE key = $1', ['data_retention_hour_days']);
            if (config && config.val) {
                hourDays = parseInt(config.val) || 90;
            }
        } catch (err) {
            console.error('获取小时级数据保留配置失败，使用默认值90天:', err);
        }
        const cutoffTime = now - (hourDays * 24 * 60 * 60);
        try {
            const { changes } = await DB.run(`DELETE FROM load_h WHERE created_at < $1`, [cutoffTime]);
            if (changes > 0) {
                console.log(`已清理 ${changes} 条过期的小时级负载数据（超过90天）`);
            }
        } catch (error) {
            console.error('清理小时级负载数据失败:', error);
        }
    }

    setInterval(async () => {
        const load_m = gen('load_m', 1440);
        const load_h = gen('load_h', 1440);
        await load_m.cleanup();
        await load_h.cleanup();
        await cleanupArchiveData();
        await cleanupMinuteData();
        await cleanupHourData();
    }, 3600000);

    setInterval(cleanupArchiveData, 10 * 60 * 1000);

    // This part of the code is problematic as it runs synchronously at module load.
    // It should be refactored to run asynchronously after the DB connection is established.
    // For now, I will comment it out to avoid blocking the event loop.
    /*
    let archiveHours = 3;
    let minuteDays = 14; 
    let hourDays = 90;
    let pollingInterval = 3000;
    
    try {
        const archiveConfig = DB.get('SELECT val FROM setting WHERE key = ?', ['data_retention_archive_hours']);
        if (archiveConfig && archiveConfig.val) {
            archiveHours = parseInt(archiveConfig.val) || 3;
        }
        
        const minuteConfig = DB.get('SELECT val FROM setting WHERE key = ?', ['data_retention_minute_days']);
        if (minuteConfig && minuteConfig.val) {
            minuteDays = parseInt(minuteConfig.val) || 14;
        }
        
        const hourConfig = DB.get('SELECT val FROM setting WHERE key = ?', ['data_retention_hour_days']);
        if (hourConfig && hourConfig.val) {
            hourDays = parseInt(hourConfig.val) || 90;
        }
        
        const pollingConfig = DB.get('SELECT val FROM setting WHERE key = ?', ['polling_interval']);
        if (pollingConfig && pollingConfig.val) {
            pollingInterval = parseInt(pollingConfig.val) || 3000;
        }
    } catch (err) {
        console.error('获取数据保留配置失败，使用默认值:', err);
    }
    
    const archiveMax = Math.ceil((archiveHours * 60 * 60 * 1000) / pollingInterval);
    const minuteMax = Math.ceil((minuteDays * 24 * 60 * 60 * 1000) / pollingInterval);
    const hourMax = hourDays * 24;
    
    console.log('[Load Database] 数据保留配置:', {
        archive: `${archiveHours}小时 (${archiveMax}条记录)`,
        minute: `${minuteDays}天 (${minuteMax}条记录)`,
        hour: `${hourDays}天 (${hourMax}条记录)`
    });
    
    const load_archive = gen('load_archive', archiveMax);
    */

    // We will return the gen function and let the caller create the instances.
    return {
        gen,
        load_m: gen('load_m', 1440),
        load_h: gen('load_h', 1440),
        load_archive: gen('load_archive', 240)
    };
}