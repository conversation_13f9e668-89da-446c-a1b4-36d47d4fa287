"use strict"
module.exports = (DB) => {
    const groups = {
        async ins(id, name, top = 0) {
            await DB.run("INSERT INTO groups (id, name, top) VALUES ($1, $2, $3)", [id, name, top]);
        },

        async get(id) {
            return await DB.get("SELECT * FROM groups WHERE id = $1", [id]);
        },

        async upd(id, name, top) {
            await DB.run("UPDATE groups SET name = $1, top = $2 WHERE id = $3", [name, top, id]);
        },

        async del(id) {
            if (id === 'default') {
                throw new Error('不能删除默认分组');
            }
            const client = await DB.beginTransaction();
            try {
                await client.query("UPDATE servers SET group_id = 'default' WHERE group_id = $1", [id]);
                await client.query("DELETE FROM groups WHERE id = $1", [id]);
                await DB.commitTransaction(client);
            } catch (err) {
                await DB.rollbackTransaction(client);
                throw err;
            }
        },

        async all() {
            return await DB.all("SELECT * FROM groups ORDER BY top DESC");
        },

        async getWithCount() {
            const sql = `
                SELECT g.*, COUNT(s.sid) as server_count
                FROM groups g
                LEFT JOIN servers s ON g.id = s.group_id
                GROUP BY g.id
                ORDER BY g.top DESC
            `;
            return await DB.all(sql);
        }
    };
    return { groups };
};