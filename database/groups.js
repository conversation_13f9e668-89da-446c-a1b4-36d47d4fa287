"use strict"
module.exports = (DB) => {
    const groups = {
        async ins(id, name, top = 0) {
            await DB.run("INSERT INTO groups (id, name, top) VALUES ($1, $2, $3)", [id, name, top]);
        },

        async get(id) {
            return await DB.get("SELECT * FROM groups WHERE id = $1", [id]);
        },

        async upd(id, name, top) {
            await DB.run("UPDATE groups SET name = $1, top = $2 WHERE id = $3", [name, top, id]);
        },

        async del(id) {
            // 检查是否为默认分组
            const group = await this.get(id);
            if (!group) {
                throw new Error('分组不存在');
            }
            if (group.is_default) {
                throw new Error('不能删除默认分组');
            }
            
            const client = await DB.beginTransaction();
            try {
                // 获取默认分组ID
                const defaultGroup = await this.getDefault();
                const defaultGroupId = defaultGroup ? defaultGroup.id : 'default';
                
                // 将该分组的服务器移动到默认分组
                await client.query("UPDATE servers SET group_id = ? WHERE group_id = ?", [defaultGroupId, id]);
                // 删除分组
                await client.query("DELETE FROM groups WHERE id = ?", [id]);
                await DB.commitTransaction(client);
            } catch (err) {
                await DB.rollbackTransaction(client);
                throw err;
            }
        },

        async all() {
            return await DB.all("SELECT * FROM groups ORDER BY top DESC");
        },

        async getWithCount() {
            const sql = `
                SELECT g.*, COUNT(s.sid) as server_count
                FROM groups g
                LEFT JOIN servers s ON g.id = s.group_id
                GROUP BY g.id
                ORDER BY g.is_default DESC, g.top DESC
            `;
            return await DB.all(sql);
        },

        async getDefault() {
            return await DB.get("SELECT * FROM groups WHERE is_default = ?", [1]);
        },

        async setDefault(id) {
            const client = await DB.beginTransaction();
            try {
                // 先清除所有默认标记
                await client.query("UPDATE groups SET is_default = ?", [0]);
                // 设置新的默认分组
                await client.query("UPDATE groups SET is_default = ? WHERE id = ?", [1, id]);
                await DB.commitTransaction(client);
            } catch (err) {
                await DB.rollbackTransaction(client);
                throw err;
            }
        }
    };
    return { groups };
};