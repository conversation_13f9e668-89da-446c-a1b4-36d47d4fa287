"use strict";

module.exports = (DB) => {
    const autodiscovery = {
        async addServer(server) {
            try {
                const { id, hostname, ip, system, version, device, api_key } = server;
                const sql = `
                    INSERT INTO autodiscovery_servers
                    (id, hostname, ip, system, version, device, api_key)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                `;
                await DB.run(sql, [id, hostname, ip, system, version, device, api_key]);
                return true;
            } catch (error) {
                console.error('[自动发现] 添加服务器记录失败:', error);
                return false;
            }
        },

        async findPendingServers() {
            try {
                const sql = `
                    SELECT * FROM autodiscovery_servers
                    WHERE status = 'pending'
                    ORDER BY created_at DESC
                `;
                return await DB.all(sql);
            } catch (error) {
                console.error('[自动发现] 查询待审核服务器失败:', error);
                return [];
            }
        },

        async findDiscoveredServers() {
            try {
                const sql = `
                    SELECT * FROM autodiscovery_servers
                    WHERE status = 'approved'
                    ORDER BY hostname ASC
                `;
                return await DB.all(sql);
            } catch (error) {
                console.error('[自动发现] 查询已发现服务器失败:', error);
                return [];
            }
        },

        async getServerById(id) {
            try {
                const sql = `
                    SELECT * FROM autodiscovery_servers
                    WHERE id = $1
                `;
                return await DB.get(sql, [id]);
            } catch (error) {
                console.error('[自动发现] 获取服务器详情失败:', error);
                return null;
            }
        },

        async updateServerStatus(id, status) {
            try {
                const strftime = DB.type === 'sqlite' ? "strftime('%s', 'now')" : "EXTRACT(EPOCH FROM NOW())::INTEGER";
                const sql = `
                    UPDATE autodiscovery_servers
                    SET status = $1, updated_at = ${strftime}
                    WHERE id = $2
                `;
                await DB.run(sql, [status, id]);
                return true;
            } catch (error) {
                console.error('[自动发现] 更新服务器状态失败:', error);
                return false;
            }
        },

        async deleteServer(id) {
            try {
                console.log(`[自动发现] 开始级联删除服务器: ${id}`);
                const serverExists = await DB.get("SELECT 1 FROM servers WHERE sid = $1", [id]);

                if (serverExists) {
                    console.log(`[自动发现] 服务器 ${id} 存在于servers表中，使用级联删除`);
                    const serversModule = require('./servers')(DB);
                    const result = await serversModule.servers.cascadeDelete(id);
                    if (result) {
                        console.log(`[自动发现] 服务器 ${id} 级联删除成功`);
                    } else {
                        console.error(`[自动发现] 服务器 ${id} 级联删除失败`);
                    }
                } else {
                    console.log(`[自动发现] 服务器 ${id} 不存在于servers表中，仅删除autodiscovery_servers表中的记录`);
                }

                await DB.run(`DELETE FROM autodiscovery_servers WHERE id = $1`, [id]);
                console.log(`[自动发现] 服务器 ${id} 已从autodiscovery_servers表中删除`);
                return true;
            } catch (error) {
                console.error('[自动发现] 删除服务器记录失败:', error);
                return false;
            }
        },

        async findPendingServerById(id) {
            try {
                console.log(`[自动发现] 查询待审核服务器: ${id}`);
                const sql = `
                    SELECT * FROM autodiscovery_servers
                    WHERE id = $1 AND status = 'pending'
                `;
                return await DB.get(sql, [id]);
            } catch (error) {
                console.error(`[自动发现] 查询待审核服务器失败: ${id}`, error);
                return null;
            }
        },

        async findDiscoveredServerById(id) {
            try {
                console.log(`[自动发现] 查询已发现服务器: ${id}`);
                const sql = `
                    SELECT * FROM autodiscovery_servers
                    WHERE id = $1 AND status = 'approved'
                `;
                return await DB.get(sql, [id]);
            } catch (error) {
                console.error(`[自动发现] 查询已发现服务器失败: ${id}`, error);
                return null;
            }
        },

        async approveServer(id, groupId = null) {
            try {
                console.log(`[自动发现] 批准服务器: ${id}`);
                const server = await this.findPendingServerById(id);
                if (!server) {
                    console.error(`[自动发现] 批准服务器失败: 服务器不存在 ${id}`);
                    return { success: false, message: '服务器不存在' };
                }

                const hasPublicIP = await this.isPublicIP(server.ip);
                const serverData = {
                    ssh: { host: server.ip, port: 22, username: "root" },
                    api: { mode: !hasPublicIP, key: server.api_key, port: 9999 },
                    device: server.device || "eth0",
                    system: server.system || "Unknown",
                    version: server.version || "Unknown",
                    autodiscovered: true,
                    discoveryTime: new Date(server.created_at * 1000).toISOString(),
                    approved: true,
                    publicIP: hasPublicIP
                };

                console.log(`[自动发现] 服务器 ${server.hostname} (${server.ip}) 使用${!hasPublicIP ? '主动' : '被动'}模式`);
                const serversModule = require('./servers')(DB);
                await serversModule.servers.ins(
                    server.id,
                    server.hostname,
                    serverData,
                    0,
                    1,
                    null,
                    groupId || "default"
                );

                await this.updateServerStatus(id, 'approved');
                console.log(`[自动发现] 服务器已批准: ${server.hostname} (${server.ip}), SID: ${id}, 分组ID: ${groupId || "default"}`);
                return { success: true, serverId: id, ip: server.ip, hostname: server.hostname };
            } catch (error) {
                console.error(`[自动发现] 批准服务器失败: ${id}`, error);
                return { success: false, message: error.message };
            }
        },

        async rejectServer(id) {
            try {
                console.log(`[自动发现] 拒绝服务器: ${id}`);
                const server = await this.findPendingServerById(id);
                if (!server) {
                    console.error(`[自动发现] 拒绝服务器失败: 服务器不存在 ${id}`);
                    return false;
                }
                await this.deleteServer(id);
                console.log(`[自动发现] 服务器已拒绝并从数据库中删除: ${server.hostname} (${server.ip}), SID: ${id}`);
                return true;
            } catch (error) {
                console.error(`[自动发现] 拒绝服务器失败: ${id}`, error);
                return false;
            }
        },

        async isPublicIP(ip) {
            if (ip.includes(':')) {
                if (ip === '::1') return false;
                const ipLower = ip.toLowerCase();
                if (ipLower.startsWith('fc') || ipLower.startsWith('fd') || ipLower.startsWith('fe80')) {
                    return false;
                }
                return true;
            }
            const parts = ip.split('.');
            if (parts.length !== 4) return false;
            const firstOctet = parseInt(parts[0], 10);
            const secondOctet = parseInt(parts[1], 10);
            if (firstOctet === 10) return false;
            if (firstOctet === 172 && secondOctet >= 16 && secondOctet <= 31) return false;
            if (firstOctet === 192 && secondOctet === 168) return false;
            if (firstOctet === 127) return false;
            if (firstOctet === 169 && secondOctet === 254) return false;
            if (firstOctet === 0) return false;
            if (firstOctet >= 224) return false;
            return true;
        },

        async checkServerStatus(server) {
            if (!server || !server.ip) {
                console.error('[自动发现] 检查服务器状态失败: 无效的服务器数据');
                return { offline: true };
            }

            try {
                const apiPort = 9999;
                const apiKey = server.api_key || '';
                console.log(`[自动发现] 正在检查服务器状态: ${server.hostname || server.ip} (${server.ip}:${apiPort})`);
                const fetch = require('node-fetch');
                const response = await fetch(`http://${server.ip}:${apiPort}/stat`, {
                    method: 'GET',
                    headers: { key: apiKey },
                    timeout: 1000,  // 减少超时时间到 1 秒
                });

                if (!response.ok) {
                    console.log(`[自动发现] 服务器响应错误: ${server.ip}:${apiPort}, 状态码: ${response.status}`);
                    return { offline: true };
                }

                const data = await response.json();

                if (data.success && data.data) {
                    console.log(`[自动发现] 服务器在线: ${server.hostname || server.ip} (${server.ip}:${apiPort})`);
                    return data.data;
                } else {
                    console.log(`[自动发现] 服务器离线或返回无效数据: ${server.ip}:${apiPort}`);
                    return { offline: true };
                }
            } catch (error) {
                console.log(`[自动发现] 检查服务器状态出错: ${server.ip}, 错误: ${error.message}`);
                return { offline: true };
            }
        }
    };

    return autodiscovery;
};
