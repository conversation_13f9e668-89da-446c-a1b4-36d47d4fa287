# 数据库状态调查报告

## 调查概要

调查时间：2025年7月21日

本次调查对DStatus系统的数据库进行了全面的只读检查，主要涉及两个数据库环境：
- 当前使用的SQLite数据库
- 已配置但未激活的PostgreSQL数据库

## 一、当前SQLite数据库状态

### 1.1 基本信息
- **数据库类型**: SQLite
- **文件路径**: `/Users/<USER>/code/dstatus/dzstatus/data/db.db`
- **文件大小**: 163.05 MB
- **创建时间**: 2025年3月25日
- **最后修改**: 2025年7月21日

### 1.2 表结构统计
发现25个表，主要包括：
- 核心业务表：servers, groups, traffic, setting
- 负载监控表：load_m, load_h, load_archive
- 网络监控表：tcping_m, tcping_5m, tcping_h, tcping_d, tcping_month, tcping_archive
- 辅助功能表：ai_reports, ssh_scripts, autodiscovery_servers
- 系统表：db_migrations, sqlite_sequence, sqlite_stat1, sqlite_stat4

### 1.3 数据量统计
主要表的记录数：
- **servers**: 33条（33台服务器）
- **load_m**: 43,634条（分钟级负载数据）
- **load_h**: 715条（小时级负载数据）
- **load_archive**: 13,020条（归档负载数据）
- **tcping_m**: 29,782条（分钟级网络监控）
- **traffic**: 33条（流量统计）
- **ai_reports**: 16条（AI分析报告）

### 1.4 服务器状态
- **总服务器数**: 33台
- **最近在线时间**: 2025年7月21日 05:07:22
- **服务器分组**:
  - 默认分组: 22台
  - 腾讯: 5台
  - 阿里云1: 3台
  - 主力1: 3台
  - akile: 0台

### 1.5 数据时间范围
- **load_m**: 2025/7/20 14:18 - 2025/7/21 14:18（24小时）
- **load_h**: 2025/7/20 16:00 - 2025/7/21 14:00（22小时）
- **load_archive**: 2025/7/21 13:17 - 2025/7/21 14:18（1小时）

### 1.6 数据完整性
- **孤立的流量记录**: 0条
- **孤立的负载记录**: 16条（load_m表中有16条记录对应的服务器已不存在）
- **无效的分组引用**: 0条

## 二、PostgreSQL数据库状态

### 2.1 基本信息
- **数据库类型**: PostgreSQL 14.10
- **连接地址**: dbprovider.ap-northeast-1.clawcloudrun.com:48032
- **数据库名**: postgres
- **数据库大小**: 34 MB
- **用户**: postgres

### 2.2 表结构对比
PostgreSQL数据库包含42个表，其中：
- **业务表**: 与SQLite相同的20个核心表
- **系统表**: 额外的PostgreSQL系统表（pg_stat_*, postgres_log_*, failed_authentication_*）

### 2.3 数据量对比
PostgreSQL中的数据量：
- **servers**: 34条（比SQLite多1条）
- **load_m**: 48,246条（比SQLite多约5,000条）
- **load_h**: 782条（比SQLite多67条）
- **load_archive**: 15,916条（比SQLite多约3,000条）
- **tcping_m**: 28,240条（比SQLite少约1,500条）
- **traffic**: 34条（与服务器数量一致）

### 2.4 数据时间范围对比
PostgreSQL中的数据时间范围：
- **load_m**: 2025/7/17 15:15 - 2025/7/18 15:44（约24小时，但是3天前的数据）
- **load_h**: 2025/7/17 16:00 - 2025/7/18 15:00（约23小时）
- **load_archive**: 2025/7/18 14:34 - 2025/7/18 15:45（约1小时）

### 2.5 服务器状态差异
- PostgreSQL中所有34台服务器状态均为"离线"（status=0）
- 没有last_online时间记录
- 这表明PostgreSQL数据库已经有几天没有接收新数据

## 三、数据库迁移状态

两个数据库都有相同的迁移记录，最新版本为v12：
1. v12: 确保test_type字段存在于monitor_targets表
2. v11: 添加test_type字段到monitor_targets表
3. v10: 添加tcping_archive表
4. v9: 添加最后在线时间字段
5. v8: 统一位置数据结构
6. v7-v1: 各种功能增强和字段添加

## 四、关键发现

### 4.1 数据同步状态
- SQLite是当前活跃使用的数据库，有最新的数据
- PostgreSQL数据库存在但数据陈旧（最后更新约3天前）
- 两个数据库的表结构基本一致，迁移版本相同

### 4.2 数据完整性
- SQLite中存在16条孤立的负载记录
- PostgreSQL中数据完整性较好，没有孤立记录
- 两个数据库的服务器数量略有差异（SQLite: 33台，PostgreSQL: 34台）

### 4.3 系统配置
当前系统配置显示：
- **DB_TYPE**: 未设置（默认使用sqlite）
- **数据库适配器**: 系统已实现SQLite和PostgreSQL两种适配器
- **连接配置**: PostgreSQL连接信息已硬编码在配置文件中

## 五、建议

1. **数据库选择**：系统当前使用SQLite，PostgreSQL已配置但未启用
2. **数据清理**：SQLite中的16条孤立负载记录可以考虑清理
3. **配置管理**：建议使用环境变量管理数据库配置，避免硬编码敏感信息
4. **数据迁移**：如需切换到PostgreSQL，需要进行完整的数据迁移
5. **监控连续性**：确保数据采集的连续性，避免数据断档