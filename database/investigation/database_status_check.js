#!/usr/bin/env node
"use strict";

const dbConfig = require("../config");
const SQLiteAdapter = require("../adapters/sqlite");
const PostgresAdapter = require("../adapters/postgresql");

async function investigateDatabase() {
    console.log("=".repeat(80));
    console.log("数据库状态调查报告");
    console.log("=".repeat(80));
    console.log(`调查时间: ${new Date().toISOString()}`);
    console.log();

    // 获取数据库配置
    const type = dbConfig.type;
    const config = dbConfig.getConfig();
    
    console.log("数据库连接信息:");
    console.log(`- 类型: ${type}`);
    if (type === 'postgresql') {
        console.log(`- 连接字符串: ${config.connection.replace(/:[^:@]+@/, ':****@')}`); // 隐藏密码
    } else if (type === 'sqlite') {
        console.log(`- SQLite路径: ${config.path}`);
    }
    console.log();

    // 创建数据库连接
    let DB;
    if (type === 'sqlite') {
        DB = new SQLiteAdapter(config);
    } else if (type === 'postgresql') {
        DB = new PostgresAdapter(config);
    } else {
        console.error(`不支持的数据库类型: ${type}`);
        return;
    }
    
    try {
        await DB.connect();
        console.log("✅ 数据库连接成功");
        console.log();

        // 1. 检查所有表
        console.log("【1. 数据库表结构】");
        console.log("-".repeat(60));
        
        let tables;
        if (type === 'sqlite') {
            tables = await DB.all(`
                SELECT name as table_name 
                FROM sqlite_master 
                WHERE type='table' 
                ORDER BY name
            `);
        } else {
            tables = await DB.all(`
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
            `);
        }
        
        console.log(`发现 ${tables.length} 个表:`);
        for (const table of tables) {
            console.log(`  - ${table.table_name}`);
        }
        console.log();

        // 2. 检查各表的记录数
        console.log("【2. 各表记录数统计】");
        console.log("-".repeat(60));
        
        const tableStats = {};
        for (const table of tables) {
            try {
                const countResult = await DB.get(`SELECT COUNT(*) as count FROM ${table.table_name}`);
                tableStats[table.table_name] = countResult.count;
                console.log(`${table.table_name.padEnd(30)} : ${countResult.count} 条记录`);
            } catch (e) {
                console.log(`${table.table_name.padEnd(30)} : 查询失败 (${e.message})`);
            }
        }
        console.log();

        // 3. 检查服务器数据状态
        if (tableStats.servers > 0) {
            console.log("【3. 服务器数据状态】");
            console.log("-".repeat(60));
            
            // 最近在线的服务器
            const recentOnline = await DB.all(`
                SELECT sid, name, last_online, status
                FROM servers 
                WHERE last_online IS NOT NULL
                ORDER BY last_online DESC 
                LIMIT 5
            `);
            
            console.log("最近在线的服务器:");
            for (const server of recentOnline) {
                const lastOnlineTime = server.last_online ? new Date(server.last_online * 1000).toLocaleString() : 'N/A';
                console.log(`  - ${server.name} (${server.sid}): ${lastOnlineTime} [状态: ${server.status}]`);
            }
            console.log();

            // 按分组统计服务器
            const groupStats = await DB.all(`
                SELECT g.name as group_name, COUNT(s.sid) as server_count
                FROM groups g
                LEFT JOIN servers s ON g.id = s.group_id
                GROUP BY g.id, g.name
                ORDER BY server_count DESC
            `);
            
            console.log("服务器分组统计:");
            for (const group of groupStats) {
                console.log(`  - ${group.group_name}: ${group.server_count} 台服务器`);
            }
            console.log();
        }

        // 4. 检查负载数据时间范围
        console.log("【4. 负载数据时间范围】");
        console.log("-".repeat(60));
        
        for (const loadTable of ['load_m', 'load_h', 'load_archive']) {
            if (tableStats[loadTable] > 0) {
                try {
                    const timeRange = await DB.get(`
                        SELECT 
                            MIN(created_at) as earliest,
                            MAX(created_at) as latest,
                            COUNT(DISTINCT sid) as server_count
                        FROM ${loadTable}
                    `);
                    
                    if (timeRange && timeRange.earliest && timeRange.latest) {
                        const earliest = new Date(timeRange.earliest * 1000).toLocaleString();
                        const latest = new Date(timeRange.latest * 1000).toLocaleString();
                        console.log(`${loadTable}:`);
                        console.log(`  - 最早记录: ${earliest}`);
                        console.log(`  - 最新记录: ${latest}`);
                        console.log(`  - 涉及服务器数: ${timeRange.server_count}`);
                        console.log();
                    }
                } catch (e) {
                    console.log(`${loadTable}: 查询失败 (可能缺少created_at字段)`);
                    console.log();
                }
            }
        }

        // 5. 检查数据库迁移状态
        console.log("【5. 数据库迁移状态】");
        console.log("-".repeat(60));
        
        if (tableStats.db_migrations > 0) {
            const migrations = await DB.all(`
                SELECT version, name, status, applied_at
                FROM db_migrations
                ORDER BY version DESC
            `);
            
            console.log(`已执行的迁移记录 (共 ${migrations.length} 条):`);
            for (const migration of migrations) {
                const appliedAt = migration.applied_at ? 
                    new Date(migration.applied_at * 1000).toLocaleString() : 'N/A';
                console.log(`  - v${migration.version}: ${migration.name} [${migration.status}] - ${appliedAt}`);
            }
        } else {
            console.log("❌ 未找到数据库迁移记录表 (db_migrations)");
        }
        console.log();

        // 6. 检查数据完整性
        console.log("【6. 数据完整性检查】");
        console.log("-".repeat(60));
        
        // 检查孤立的流量记录
        if (tableStats.traffic > 0 && tableStats.servers > 0) {
            const orphanTraffic = await DB.get(`
                SELECT COUNT(*) as count
                FROM traffic t
                WHERE NOT EXISTS (SELECT 1 FROM servers s WHERE s.sid = t.sid)
            `);
            console.log(`孤立的流量记录: ${orphanTraffic.count} 条`);
        }

        // 检查孤立的负载记录
        if (tableStats.load_m > 0 && tableStats.servers > 0) {
            const orphanLoad = await DB.get(`
                SELECT COUNT(*) as count
                FROM load_m l
                WHERE NOT EXISTS (SELECT 1 FROM servers s WHERE s.sid = l.sid)
            `);
            console.log(`孤立的负载记录 (load_m): ${orphanLoad.count} 条`);
        }

        // 检查无效的分组引用
        if (tableStats.servers > 0 && tableStats.groups > 0) {
            const invalidGroups = await DB.get(`
                SELECT COUNT(*) as count
                FROM servers s
                WHERE s.group_id NOT IN (SELECT id FROM groups)
                AND s.group_id IS NOT NULL
            `);
            console.log(`无效的分组引用: ${invalidGroups.count} 条`);
        }
        console.log();

        // 7. 系统设置
        console.log("【7. 系统设置】");
        console.log("-".repeat(60));
        
        if (tableStats.setting > 0) {
            const settings = await DB.all(`SELECT key, val FROM setting ORDER BY key`);
            console.log(`系统设置项 (共 ${settings.length} 项):`);
            for (const setting of settings) {
                let value = 'null';
                if (setting.val !== null) {
                    const valStr = String(setting.val);
                    value = valStr.substring(0, 50) + (valStr.length > 50 ? '...' : '');
                }
                console.log(`  - ${setting.key}: ${value}`);
            }
        } else {
            console.log("暂无系统设置项");
        }
        console.log();

        // 8. AI报告统计
        if (tableStats.ai_reports > 0) {
            console.log("【8. AI报告统计】");
            console.log("-".repeat(60));
            
            const aiReportStats = await DB.get(`
                SELECT 
                    COUNT(*) as total_reports,
                    MIN(created_at) as earliest_report,
                    MAX(created_at) as latest_report,
                    AVG(overall_score) as avg_score
                FROM ai_reports
                WHERE status = 'completed'
            `);
            
            console.log(`AI报告总数: ${aiReportStats.total_reports}`);
            if (aiReportStats.earliest_report) {
                const earliestDate = type === 'sqlite' ? 
                    new Date(aiReportStats.earliest_report * 1000).toLocaleString() :
                    new Date(aiReportStats.earliest_report).toLocaleString();
                const latestDate = type === 'sqlite' ? 
                    new Date(aiReportStats.latest_report * 1000).toLocaleString() :
                    new Date(aiReportStats.latest_report).toLocaleString();
                    
                console.log(`最早报告: ${earliestDate}`);
                console.log(`最新报告: ${latestDate}`);
                console.log(`平均得分: ${aiReportStats.avg_score ? aiReportStats.avg_score.toFixed(2) : 'N/A'}`);
            }
        }

        // 9. 数据库大小（仅SQLite）
        if (type === 'sqlite') {
            console.log();
            console.log("【9. 数据库文件信息】");
            console.log("-".repeat(60));
            
            const dbStats = dbConfig.getDatabaseStats();
            if (dbStats) {
                console.log(`文件大小: ${(dbStats.size / 1024 / 1024).toFixed(2)} MB`);
                console.log(`创建时间: ${dbStats.created.toLocaleString()}`);
                console.log(`修改时间: ${dbStats.modified.toLocaleString()}`);
            }
        }

        console.log();
        console.log("=".repeat(80));
        console.log("✅ 数据库调查完成");
        console.log("=".repeat(80));

    } catch (error) {
        console.error("❌ 调查过程中发生错误:", error.message);
        console.error(error.stack);
    } finally {
        await DB.disconnect();
    }
}

// 执行调查
investigateDatabase().catch(console.error);