#!/usr/bin/env node
"use strict";

const dbConfig = require("../config");
const SQLiteAdapter = require("../adapters/sqlite");

async function checkMigrationSchema() {
    console.log("检查数据库迁移表结构...");
    
    const config = dbConfig.getConfig();
    const DB = new SQLiteAdapter(config);
    
    try {
        await DB.connect();
        
        // 检查db_migrations表结构
        const columns = await DB.all(`PRAGMA table_info(db_migrations)`);
        
        console.log("\ndb_migrations表结构:");
        console.log("-".repeat(60));
        console.log("列名".padEnd(20), "类型".padEnd(15), "非空", "默认值");
        console.log("-".repeat(60));
        
        for (const col of columns) {
            console.log(
                col.name.padEnd(20),
                col.type.padEnd(15),
                col.notnull ? "是" : "否",
                col.dflt_value || "无"
            );
        }
        
        console.log("\n实际数据样例:");
        const samples = await DB.all(`SELECT * FROM db_migrations ORDER BY version DESC LIMIT 5`);
        console.log(samples);
        
    } catch (error) {
        console.error("错误:", error.message);
    } finally {
        await DB.disconnect();
    }
}

checkMigrationSchema().catch(console.error);