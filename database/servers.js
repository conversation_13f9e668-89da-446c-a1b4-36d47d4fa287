"use strict";
module.exports = (DB) => {
    const { encryptSSHData, decryptSSHData } = require('../modules/servers/simple-crypto');

    const servers = {
        async ins(sid, name, data, top, status = 1, expire_time = null, group_id = 'default', options = {}) {
            const {
                traffic_limit = 0,
                traffic_reset_day = 1,
                traffic_alert_percent = 80,
                traffic_last_reset = Math.floor(Date.now() / 1000),
                traffic_direction = 'both'
            } = options;

            const encryptedData = { ...data };
            if (encryptedData.ssh) {
                encryptedData.ssh = encryptSSHData(encryptedData.ssh);
            }

            const sql = `
                INSERT INTO servers (
                    sid, name, data, top, status, expire_time, group_id,
                    traffic_limit, traffic_reset_day, traffic_alert_percent, traffic_last_reset,
                    traffic_direction
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            `;
            const params = [
                sid, name, JSON.stringify(encryptedData), top, status, expire_time, group_id,
                traffic_limit, traffic_reset_day, traffic_alert_percent, traffic_last_reset,
                traffic_direction
            ];
            return await DB.run(sql, params);
        },

        async upd(sid, name, data, top, expire_time = null, group_id = 'default', traffic_limit = null, traffic_reset_day = null, traffic_alert_percent = null, traffic_calibration_date = null, traffic_calibration_value = null, traffic_direction = null) {
            const encryptedData = { ...data };
            if (encryptedData.ssh) {
                encryptedData.ssh = encryptSSHData(encryptedData.ssh);
            }

            const sql = `
                UPDATE servers SET
                    name=$1, data=$2, top=$3, expire_time=$4, group_id=$5,
                    traffic_limit=COALESCE($6, traffic_limit),
                    traffic_reset_day=COALESCE($7, traffic_reset_day),
                    traffic_alert_percent=COALESCE($8, traffic_alert_percent),
                    traffic_last_reset=COALESCE($9, traffic_last_reset),
                    traffic_calibration_date=COALESCE($10, traffic_calibration_date),
                    traffic_calibration_value=COALESCE($11, traffic_calibration_value),
                    traffic_direction=COALESCE($12, traffic_direction)
                WHERE sid=$13
            `;
            const params = [
                name, JSON.stringify(encryptedData), top, expire_time, group_id,
                traffic_limit, traffic_reset_day, traffic_alert_percent, null,
                traffic_calibration_date, traffic_calibration_value, traffic_direction,
                sid
            ];
            return await DB.run(sql, params);
        },

        async upd_status(sid, status) {
            return await DB.run("UPDATE servers SET status=$1 WHERE sid=$2", [status, sid]);
        },

        async upd_data(sid, data) {
            const encryptedData = { ...data };
            if (encryptedData.ssh) {
                encryptedData.ssh = encryptSSHData(encryptedData.ssh);
            }
            return await DB.run("UPDATE servers SET data=$1 WHERE sid=$2", [JSON.stringify(encryptedData), sid]);
        },

        async upd_top(sid, top) {
            return await DB.run("UPDATE servers SET top=$1 WHERE sid=$2", [top, sid]);
        },

        async upd_expire_time(sid, expire_time) {
            return await DB.run("UPDATE servers SET expire_time=$1 WHERE sid=$2", [expire_time, sid]);
        },

        async upd_group_id(sid, group_id = 'default') {
            return await DB.run("UPDATE servers SET group_id=$1 WHERE sid=$2", [group_id, sid]);
        },

        async upd_traffic_limit(sid, traffic_limit) {
            return await DB.run("UPDATE servers SET traffic_limit=$1 WHERE sid=$2", [traffic_limit, sid]);
        },

        async upd_traffic_reset_day(sid, traffic_reset_day) {
            return await DB.run("UPDATE servers SET traffic_reset_day=$1 WHERE sid=$2", [traffic_reset_day, sid]);
        },

        async upd_traffic_alert_percent(sid, traffic_alert_percent) {
            return await DB.run("UPDATE servers SET traffic_alert_percent=$1 WHERE sid=$2", [traffic_alert_percent, sid]);
        },

        async upd_traffic_last_reset(sid, traffic_last_reset) {
            return await DB.run("UPDATE servers SET traffic_last_reset=$1 WHERE sid=$2", [traffic_last_reset, sid]);
        },

        async upd_traffic_direction(sid, traffic_direction) {
            return await DB.run("UPDATE servers SET traffic_direction=$1 WHERE sid=$2", [traffic_direction, sid]);
        },

        async upd_last_online(sid, last_online) {
            return await DB.run("UPDATE servers SET last_online=$1 WHERE sid=$2", [last_online, sid]);
        },

        async setCalibration(sid, calibrationDate, calibrationValue) {
            try {
                const sql = `
                    INSERT INTO traffic_calibration (sid, calibration_date, calibration_value)
                    VALUES ($1, $2, $3)
                    ON CONFLICT(sid) DO UPDATE SET
                        calibration_date = excluded.calibration_date,
                        calibration_value = excluded.calibration_value
                `;
                await DB.run(sql, [sid, calibrationDate, calibrationValue]);
                return true;
            } catch (err) {
                console.error('设置流量校准信息时出错:', err);
                return false;
            }
        },

        async getCalibration(sid) {
            try {
                const sql = `
                    SELECT calibration_date, calibration_value
                    FROM traffic_calibration
                    WHERE sid = $1
                `;
                return await DB.get(sql, [sid]);
            } catch (err) {
                console.error('获取流量校准信息时出错:', err);
                return null;
            }
        },

        async deleteCalibration(sid) {
            try {
                await DB.run('DELETE FROM traffic_calibration WHERE sid = $1', [sid]);
                return true;
            } catch (err) {
                console.error('删除流量校准信息时出错:', err);
                return false;
            }
        },

        async get(sid) {
            const server = await DB.get("SELECT * FROM servers WHERE sid=$1", [sid]);
            if (server) {
                server.data = JSON.parse(server.data);
                if (server.data.ssh) {
                    server.data.ssh = decryptSSHData(server.data.ssh, sid);
                }
            }
            return server;
        },

        async del(sid) {
            return await DB.run("DELETE FROM servers WHERE sid=$1", [sid]);
        },

        async cascadeDelete(sid) {
            const client = await DB.beginTransaction();
            try {
                // 删除流量相关数据
                await client.query("DELETE FROM traffic WHERE sid = $1", [sid]);
                await client.query("DELETE FROM lt WHERE sid = $1", [sid]);

                // 删除负载相关数据
                await client.query("DELETE FROM load_m WHERE sid = $1", [sid]);
                await client.query("DELETE FROM load_h WHERE sid = $1", [sid]);
                await client.query("DELETE FROM load_archive WHERE sid = $1", [sid]);

                // 删除网络监控相关数据
                await client.query('DELETE FROM tcping_archive WHERE sid = $1', [sid]);
                for (const table of ['tcping_m', 'tcping_5m', 'tcping_h', 'tcping_d', 'tcping_month']) {
                    await client.query(`DELETE FROM ${table} WHERE sid = $1`, [sid]);
                }
                const targets = await client.query(`
                    SELECT id, node_id FROM monitor_targets
                    WHERE node_id LIKE $1 OR node_id LIKE $2 OR node_id = $3
                `, [`%"${sid}"%`, `%${sid}%`, sid]);

                for (const target of targets.rows) {
                    let nodeIds;
                    try {
                        nodeIds = JSON.parse(target.node_id);
                        if (Array.isArray(nodeIds)) {
                            nodeIds = nodeIds.filter(id => id !== sid);
                            await client.query('UPDATE monitor_targets SET node_id = $1 WHERE id = $2', [JSON.stringify(nodeIds), target.id]);
                        } else if (nodeIds === sid) {
                            await client.query('UPDATE monitor_targets SET node_id = NULL WHERE id = $1', [target.id]);
                        }
                    } catch (e) {
                        if (target.node_id === sid) {
                            await client.query('UPDATE monitor_targets SET node_id = NULL WHERE id = $1', [target.id]);
                        }
                    }
                }

                await client.query('DELETE FROM autodiscovery_servers WHERE id = $1', [sid]);
                await client.query('DELETE FROM traffic_calibration WHERE sid = $1', [sid]);
                await client.query("DELETE FROM servers WHERE sid=$1", [sid]);

                await DB.commitTransaction(client);
                console.log(`[${new Date().toISOString()}] 服务器 ${sid} 及其所有相关数据已成功删除`);
                return true;
            } catch (error) {
                await DB.rollbackTransaction(client);
                console.error(`[${new Date().toISOString()}] 删除服务器 ${sid} 失败:`, error);
                return false;
            }
        },

        async all() {
            const svrs = await DB.all("SELECT * FROM servers ORDER BY top DESC");
            svrs.forEach(svr => {
                svr.data = JSON.parse(svr.data);
                if (svr.data.ssh) {
                    svr.data.ssh = decryptSSHData(svr.data.ssh, svr.sid);
                }
            });
            return svrs;
        },

        async allPaged(page = 1, pageSize = 20) {
            const offset = (page - 1) * pageSize;
            
            const totalResult = await DB.get("SELECT COUNT(*) as total FROM servers");
            const total = totalResult.total;
            
            const svrs = await DB.all("SELECT * FROM servers ORDER BY top DESC LIMIT $1 OFFSET $2", [pageSize, offset]);
            svrs.forEach(svr => {
                svr.data = JSON.parse(svr.data);
                if (svr.data.ssh) {
                    svr.data.ssh = decryptSSHData(svr.data.ssh, svr.sid);
                }
            });
            
            const totalPages = Math.ceil(total / pageSize);
            
            return {
                data: svrs,
                pagination: {
                    page: page,
                    pageSize: pageSize,
                    total: total,
                    totalPages: totalPages,
                    hasNext: page < totalPages,
                    hasPrev: page > 1
                }
            };
        },
    };

    return { servers };
};