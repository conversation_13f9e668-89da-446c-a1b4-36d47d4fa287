/**
 * 数据库迁移脚本 - 添加tcping_archive表
 * 用于存储长期网络质量监控数据
 */

module.exports = {
    version: 10,
    name: '添加tcping_archive表',
    up: async (client) => {
        try {
            console.log('开始创建tcping_archive表...');
            
            // 事务由migrations.js管理，无需手动开始
            // 适配器兼容性：PostgreSQL使用client.query，SQLite使用全局DB适配器
            const query = async (sql, params = []) => {
                if (client && typeof client.query === 'function') {
                    // PostgreSQL client
                    return await client.query(sql, params);
                } else {
                    // SQLite - 使用全局DB实例
                    const db = require('../../index')();
                    return await db.DB.run(sql, params);
                }
            };
            
            // 创建tcping_archive表
            await query(`
                CREATE TABLE IF NOT EXISTS tcping_archive (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    target_id TEXT NOT NULL,
                    sid TEXT NOT NULL,
                    success_rate REAL,
                    avg_time REAL,
                    min_time REAL,
                    max_time REAL,
                    created_at INTEGER DEFAULT (strftime('%s', 'now')),
                    year INTEGER,
                    month INTEGER,
                    day INTEGER,
                    hour INTEGER,
                    minute INTEGER,
                    FOREIGN KEY (target_id) REFERENCES monitor_targets(id)
                )
            `);
            
            // 创建索引
            // 1. 节点索引
            await query(`
                CREATE INDEX IF NOT EXISTS idx_tcping_archive_sid
                ON tcping_archive(sid)
            `);
            
            // 2. 复合索引，加速节点特定目标的时间范围查询
            await query(`
                CREATE INDEX IF NOT EXISTS idx_tcping_archive_sid_target_time
                ON tcping_archive(sid, target_id, created_at)
            `);
            
            // 3. 时间索引，加速时间范围查询
            await query(`
                CREATE INDEX IF NOT EXISTS idx_tcping_archive_time
                ON tcping_archive(year, month, day, hour, minute)
            `);
            
            // 事务由migrations.js管理，无需手动提交
            
            console.log('tcping_archive表创建成功');
            return true;
        } catch (err) {
            // 错误处理，事务回滚由migrations.js管理
            console.error('创建tcping_archive表失败:', err);
            return false;
        }
    },
    
    down: async (client) => {
        try {
            console.log('开始删除tcping_archive表...');
            
            // 事务由migrations.js管理，无需手动开始
            // 适配器兼容性：PostgreSQL使用client.query，SQLite使用全局DB适配器
            const query = async (sql, params = []) => {
                if (client && typeof client.query === 'function') {
                    // PostgreSQL client
                    return await client.query(sql, params);
                } else {
                    // SQLite - 使用全局DB实例
                    const db = require('../../index')();
                    return await db.DB.run(sql, params);
                }
            };
            
            // 删除索引
            const indexes = [
                'idx_tcping_archive_sid',
                'idx_tcping_archive_sid_target_time',
                'idx_tcping_archive_time'
            ];
            
            for (const index of indexes) {
                try {
                    await query(`DROP INDEX IF EXISTS ${index}`);
                } catch (indexErr) {
                    console.warn(`删除索引 ${index} 失败:`, indexErr);
                    // 继续执行，不中断回滚过程
                }
            }
            
            // 删除表
            await query('DROP TABLE IF EXISTS tcping_archive');
            
            // 事务由migrations.js管理，无需手动提交
            
            console.log('tcping_archive表删除成功');
            return true;
        } catch (err) {
            // 错误处理，事务回滚由migrations.js管理
            console.error('删除tcping_archive表失败:', err);
            return false;
        }
    }
};
