#!/usr/bin/env node
"use strict";

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 引入数据库适配器
const SQLiteAdapter = require('./adapters/sqlite');
const PostgresAdapter = require('./adapters/postgresql');
const dbConfig = require('./config');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(query) {
    return new Promise(resolve => rl.question(query, resolve));
}

class DatabaseMigrator {
    constructor() {
        this.tables = [
            'servers',
            'groups',
            'traffic',
            'lt',
            'load_m',
            'load_h',
            'load_archive',
            'ssh_scripts',
            'setting',
            'autodiscovery_servers',
            'monitor_regions',
            'monitor_targets',
            'tcping_m',
            'tcping_5m',
            'tcping_h',
            'tcping_d',
            'tcping_month',
            'tcping_archive',
            'ai_reports',
            'db_migrations'
        ];
    }

    async exportFromPostgreSQL() {
        console.log('📤 开始从 PostgreSQL 导出数据...');
        
        // 创建 PostgreSQL 连接
        const pgConfig = {
            connection: dbConfig.postgresql.connection
        };
        const pgDB = new PostgresAdapter(pgConfig);
        
        try {
            await pgDB.connect();
            console.log('✅ 已连接到 PostgreSQL');

            const exportData = {
                metadata: {
                    exportTime: new Date().toISOString(),
                    sourceType: 'postgresql',
                    tables: {}
                },
                data: {}
            };

            // 导出每个表的数据
            for (const table of this.tables) {
                try {
                    console.log(`  导出表 ${table}...`);
                    const rows = await pgDB.all(`SELECT * FROM ${table}`);
                    exportData.data[table] = rows;
                    exportData.metadata.tables[table] = rows.length;
                    console.log(`  ✅ ${table}: ${rows.length} 条记录`);
                } catch (error) {
                    console.log(`  ⚠️ ${table}: 表不存在或导出失败 - ${error.message}`);
                    exportData.data[table] = [];
                    exportData.metadata.tables[table] = 0;
                }
            }

            // 保存导出数据
            const exportPath = path.join(__dirname, '../data/db-export.json');
            const exportDir = path.dirname(exportPath);
            if (!fs.existsSync(exportDir)) {
                fs.mkdirSync(exportDir, { recursive: true });
            }
            
            fs.writeFileSync(exportPath, JSON.stringify(exportData, null, 2));
            console.log(`\n✅ 数据已导出到: ${exportPath}`);
            
            await pgDB.disconnect();
            return exportPath;
            
        } catch (error) {
            console.error('❌ 导出失败:', error.message);
            await pgDB.disconnect();
            throw error;
        }
    }

    async importToSQLite(exportPath) {
        console.log('\n📥 开始导入数据到 SQLite...');
        
        // 读取导出的数据
        if (!fs.existsSync(exportPath)) {
            throw new Error(`导出文件不存在: ${exportPath}`);
        }
        
        const exportData = JSON.parse(fs.readFileSync(exportPath, 'utf8'));
        console.log(`导出时间: ${exportData.metadata.exportTime}`);
        
        // 创建 SQLite 连接
        const sqliteConfig = {
            path: dbConfig.sqlite.path
        };
        const sqliteDB = new SQLiteAdapter(sqliteConfig);
        
        try {
            await sqliteDB.connect();
            console.log('✅ 已连接到 SQLite');

            // 开始事务
            await sqliteDB.run('BEGIN TRANSACTION');

            // 导入每个表的数据
            for (const table of this.tables) {
                const rows = exportData.data[table] || [];
                if (rows.length === 0) {
                    console.log(`  ⏭️  ${table}: 无数据`);
                    continue;
                }

                console.log(`  导入表 ${table}...`);
                
                try {
                    // 清空目标表
                    await sqliteDB.run(`DELETE FROM ${table}`);
                    
                    // 批量插入数据
                    let imported = 0;
                    for (const row of rows) {
                        const columns = Object.keys(row);
                        const values = columns.map(col => row[col]);
                        const placeholders = columns.map(() => '?').join(', ');
                        
                        const sql = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`;
                        await sqliteDB.run(sql, values);
                        imported++;
                        
                        // 每100条记录显示进度
                        if (imported % 100 === 0) {
                            process.stdout.write(`\r  ✅ ${table}: ${imported}/${rows.length} 条记录`);
                        }
                    }
                    
                    console.log(`\r  ✅ ${table}: ${imported} 条记录已导入`);
                    
                } catch (error) {
                    console.error(`\n  ❌ ${table}: 导入失败 - ${error.message}`);
                    // 继续处理其他表
                }
            }

            // 提交事务
            await sqliteDB.run('COMMIT');
            console.log('\n✅ 所有数据导入完成');
            
            await sqliteDB.disconnect();
            
        } catch (error) {
            await sqliteDB.run('ROLLBACK');
            console.error('❌ 导入失败:', error.message);
            await sqliteDB.disconnect();
            throw error;
        }
    }

    async switchDatabase(targetType) {
        console.log(`\n🔄 切换数据库到 ${targetType}...`);
        
        // 更新配置文件
        const configPath = path.join(__dirname, '../config/database.json');
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        
        config.activeDatabase = targetType;
        config.lastUpdated = new Date().toISOString();
        config.updatedBy = 'migration-tool';
        
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
        console.log('✅ 配置文件已更新');
    }
}

async function main() {
    console.log('=== DzStatus 数据库迁移工具 ===\n');
    
    const migrator = new DatabaseMigrator();
    
    try {
        // 读取当前配置
        const configPath = path.join(__dirname, '../config/database.json');
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        const currentDB = config.activeDatabase;
        
        console.log(`当前数据库: ${currentDB}`);
        console.log('\n选择操作:');
        console.log('1. PostgreSQL -> SQLite (导出并切换)');
        console.log('2. SQLite -> PostgreSQL (导出并切换)');
        console.log('3. 仅导出当前数据库');
        console.log('4. 从文件导入数据');
        console.log('5. 退出');
        
        const choice = await question('\n请选择 (1-5): ');
        
        switch (choice) {
            case '1': {
                if (currentDB !== 'postgresql') {
                    console.log('\n⚠️  当前不是 PostgreSQL 数据库');
                    const confirm = await question('确定要继续吗? (y/n): ');
                    if (confirm.toLowerCase() !== 'y') {
                        break;
                    }
                }
                
                // 导出 PostgreSQL 数据
                const exportPath = await migrator.exportFromPostgreSQL();
                
                // 确认是否导入到 SQLite
                const doImport = await question('\n是否立即导入到 SQLite? (y/n): ');
                if (doImport.toLowerCase() === 'y') {
                    await migrator.importToSQLite(exportPath);
                    
                    // 切换配置
                    const doSwitch = await question('\n是否切换到 SQLite? (y/n): ');
                    if (doSwitch.toLowerCase() === 'y') {
                        await migrator.switchDatabase('sqlite');
                        console.log('\n🎉 迁移完成！请重启应用以使用 SQLite 数据库。');
                    }
                }
                break;
            }
            
            case '2': {
                console.log('\n⚠️  SQLite 到 PostgreSQL 的迁移暂未实现');
                console.log('请手动将 SQLite 数据导出，然后导入到 PostgreSQL');
                break;
            }
            
            case '3': {
                if (currentDB === 'postgresql') {
                    await migrator.exportFromPostgreSQL();
                } else {
                    console.log('⚠️  当前仅支持导出 PostgreSQL 数据');
                }
                break;
            }
            
            case '4': {
                const exportPath = await question('请输入导出文件路径: ');
                if (fs.existsSync(exportPath)) {
                    await migrator.importToSQLite(exportPath);
                } else {
                    console.log('❌ 文件不存在');
                }
                break;
            }
            
            case '5':
                console.log('👋 再见！');
                break;
                
            default:
                console.log('❌ 无效的选择');
        }
        
    } catch (error) {
        console.error('\n❌ 错误:', error.message);
        process.exit(1);
    } finally {
        rl.close();
    }
}

// 运行主程序
if (require.main === module) {
    main().catch(console.error);
}

module.exports = DatabaseMigrator;