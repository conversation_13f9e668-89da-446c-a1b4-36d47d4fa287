"use strict";

const { randomUUID } = require('crypto');

module.exports = (DB) => {
  function parseJSON(val, def) {
    if (!val) return def;
    try { return JSON.parse(val); } catch { return def; }
  }

  // 计算周期起点（秒）
  function getPeriodStart(period, resetDay = 1) {
    const now = new Date();
    if (period === 'daily') {
      const d = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      return Math.floor(d.getTime() / 1000);
    } else if (period === 'weekly') {
      // 以周一为周起点
      const day = now.getDay(); // 0=Sun..6=Sat
      const diffToMonday = (day === 0 ? -6 : 1 - day); // 周一=1
      const monday = new Date(now.getFullYear(), now.getMonth(), now.getDate() + diffToMonday);
      const m = new Date(monday.getFullYear(), monday.getMonth(), monday.getDate());
      return Math.floor(m.getTime() / 1000);
    } else { // monthly
      const day = now.getDate();
      let start;
      if (day >= resetDay) {
        start = new Date(now.getFullYear(), now.getMonth(), resetDay);
      } else {
        start = new Date(now.getFullYear(), now.getMonth() - 1, resetDay);
      }
      return Math.floor(start.getTime() / 1000);
    }
  }

  const notificationTasks = {
    async create({ sid, enabled = true, type = 'traffic', period = 'monthly', traffic_limit = null, thresholds = [], direction = 'both', reset_day = null }) {
      const id = randomUUID();
      const sql = `
        INSERT INTO notification_tasks (id, sid, enabled, type, period, traffic_limit, thresholds, direction, reset_day, last_notified, last_period_start)
        VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,0,0)
      `;
      await DB.run(sql, [id, sid, enabled ? 1 : 0, type, period, traffic_limit, JSON.stringify(thresholds || []), direction, reset_day]);
      return id;
    },

    async update(id, fields = {}) {
      const keys = Object.keys(fields);
      if (!keys.length) return false;
      const setClauses = [];
      const params = [];
      let idx = 1;
      for (const k of keys) {
        let v = fields[k];
        if (k === 'thresholds') v = JSON.stringify(v || []);
        setClauses.push(`${k}=$${idx++}`);
        params.push(v);
      }
      params.push(id);
      const sql = `UPDATE notification_tasks SET ${setClauses.join(', ')}, updated_at = (${DB.type === 'postgresql' ? "EXTRACT(EPOCH FROM NOW())::INTEGER" : "strftime('%s','now')"}) WHERE id=$${idx}`;
      await DB.run(sql, params);
      return true;
    },

    async remove(id) {
      await DB.run('DELETE FROM notification_tasks WHERE id=$1', [id]);
      return true;
    },

    async get(id) {
      const row = await DB.get('SELECT * FROM notification_tasks WHERE id=$1', [id]);
      if (!row) return null;
      row.thresholds = parseJSON(row.thresholds, []);
      return row;
    },

    async listAll() {
      const rows = await DB.all('SELECT * FROM notification_tasks ORDER BY created_at DESC');
      return rows.map(r => ({ ...r, thresholds: parseJSON(r.thresholds, []) }));
    },

    async listActiveByServer(sid) {
      const rows = await DB.all('SELECT * FROM notification_tasks WHERE sid=$1 AND enabled=1', [sid]);
      return rows.map(r => ({ ...r, thresholds: parseJSON(r.thresholds, []) }));
    },
    
    // 批量拉取所有启用的任务，供批量评估使用
    async listActiveAll() {
      const rows = await DB.all('SELECT * FROM notification_tasks WHERE enabled=1 ORDER BY created_at DESC');
      return rows.map(r => ({ ...r, thresholds: parseJSON(r.thresholds, []) }));
    },

    async markNotified(id, threshold) {
      await DB.run('UPDATE notification_tasks SET last_notified=$1, updated_at=updated_at WHERE id=$2', [threshold, id]);
      return true;
    },

    async ensurePeriodBoundary(task) {
      const start = getPeriodStart(task.period || 'monthly', task.reset_day || 1);
      if ((task.last_period_start || 0) !== start) {
        await DB.run('UPDATE notification_tasks SET last_period_start=$1, last_notified=0 WHERE id=$2', [start, task.id]);
        return { ...task, last_period_start: start, last_notified: 0 };
      }
      return task;
    }
  };

  return { notificationTasks };
};
