"use strict"
module.exports = (DB) => {
    function S(val) { return JSON.stringify(val); }
    
    // 安全的JSON解析函数，带错误处理和默认值
    function P(pair, defaultValue = null, key = 'unknown') {
        if (!pair || pair.val === null || pair.val === undefined) return defaultValue;
        
        try {
            // 如果已经是对象或数字，直接返回
            if (typeof pair.val === 'object' && pair.val !== null) {
                return pair.val;
            }
            if (typeof pair.val === 'number') {
                return pair.val;
            }
            if (typeof pair.val === 'boolean') {
                return pair.val;
            }
            
            // 确保是字符串再进行字符串操作
            if (typeof pair.val !== 'string') {
                console.warn(`[Setting] 意外的数据类型 [${key}]:`, typeof pair.val, '值:', pair.val);
                return pair.val; // 直接返回原值
            }
            
            // 先清理潜在的BOM和空白字符
            const cleaned = pair.val.replace(/^\uFEFF/, '').trim();
            if (!cleaned) return defaultValue;
            
            return JSON.parse(cleaned);
        } catch (error) {
            console.error(`[Setting] JSON解析失败 [${key}]:`, error.message);
            if (typeof pair.val === 'string') {
                console.error(`[Setting] 损坏的数据:`, pair.val.substring(0, 100) + '...');
            } else {
                console.error(`[Setting] 非字符串数据:`, typeof pair.val, pair.val);
            }
            
            // 记录损坏的配置以便修复
            console.warn(`[Setting] 使用默认值替代损坏的配置: ${key}`);
            return defaultValue;
        }
    }
    const setting = {
        async ins(key, val) {
            const importantKeys = ['listen', 'password', 'site', 'telegram', 'personalization'];
            if (importantKeys.includes(key)) {
                console.log(`[Setting] 初始化: ${key}`);
            }
            await DB.run("INSERT INTO setting (key,val) VALUES ($1,$2)", [key, S(val)]);
        },

        async set(key, val) {
            const importantKeys = ['licenseEnhancedMode', 'licenseKey', 'instanceId', 'telegram', 'personalization', 'polling_interval', 'websocket_interval'];
            const shouldLog = importantKeys.includes(key) || key.includes('Error') || key.includes('Warning');

            if (shouldLog) {
                console.log(`[Setting] 更新: ${key}`);
            }

            try {
                // 使用 UPSERT 语法避免UNIQUE约束冲突
                await DB.run(`
                    INSERT INTO setting (key, val) VALUES ($1, $2)
                    ON CONFLICT(key) DO UPDATE SET val = $3
                `, [key, S(val), S(val)]);
            } catch (err) {
                console.error(`保存设置失败: ${key}`, err);
                throw err;
            }
        },

        _loggedMissingKeys: new Set(),

        async get(key, defaultValue = null) {
            try {
                const pair = await DB.get("SELECT * FROM setting WHERE key=$1", [key]);
                const result = P(pair, defaultValue, key);
                
                if (!result && !this._loggedMissingKeys.has(key)) {
                    this._loggedMissingKeys.add(key);
                }
                
                return result;
            } catch (error) {
                console.error(`[Setting] 数据库查询失败 [${key}]:`, error.message);
                return defaultValue;
            }
        },
        
        // 带默认值的安全获取方法
        async getSafe(key, defaultValue) {
            const result = await this.get(key, defaultValue);
            return result !== null ? result : defaultValue;
        },

        async del(key) {
            if (key.includes('license') || key.includes('License')) {
                console.log(`[Setting] 删除: ${key}`);
            }
            await DB.run("DELETE FROM setting WHERE key=$1", [key]);
        },

        async all() {
            var s = {};
            const settings = await DB.all("SELECT * FROM setting");
            
            for (var { key, val } of settings) {
                try {
                    s[key] = JSON.parse(val);
                } catch (error) {
                    console.error(`[Setting] 跳过损坏的配置 [${key}]:`, error.message);
                    // 跳过损坏的配置，不影响其他配置的读取
                }
            }
            return s;
        },
        
        // 关键配置的安全访问方法
        async getCritical(key, defaultValue) {
            const criticalDefaults = {
                'listen': 5555,
                'password': 'dstatus',
                'instanceId': null,
                'telegram': {
                    enabled: false,
                    notificationTypes: {
                        serverOnline: true,
                        serverOffline: true,
                        trafficLimit: true,
                        testNotification: true
                    }
                },
                'personalization': {
                    wallpaper: { enabled: false }
                },
                // PostgreSQL配置已迁移到专用表postgres_config
            };
            
            const fallback = defaultValue || criticalDefaults[key];
            const result = await this.getSafe(key, fallback);
            
            // 如果是关键配置且读取失败，尝试自动修复
            if (!result && criticalDefaults[key]) {
                console.warn(`[Setting] 自动修复关键配置: ${key}`);
                try {
                    await this.set(key, criticalDefaults[key]);
                    return criticalDefaults[key];
                } catch (repairError) {
                    console.error(`[Setting] 修复配置失败 [${key}]:`, repairError.message);
                    return fallback;
                }
            }
            
            return result;
        },
        
        // 配置健康检查
        async healthCheck() {
            const criticalKeys = ['listen', 'password', 'instanceId'];
            const issues = [];
            
            for (const key of criticalKeys) {
                try {
                    const value = await this.get(key);
                    if (value === null) {
                        issues.push(`缺失关键配置: ${key}`);
                    }
                } catch (error) {
                    issues.push(`配置读取失败: ${key} - ${error.message}`);
                }
            }
            
            if (issues.length > 0) {
                console.warn('[Setting] 配置健康检查发现问题:', issues);
            }
            
            return {
                healthy: issues.length === 0,
                issues: issues
            };
        },
    };

    // 深度合并对象的辅助函数
    function deepMerge(existing, defaults) {
        if (typeof existing !== 'object' || existing === null) return defaults;
        if (typeof defaults !== 'object' || defaults === null) return existing;
        
        const result = {...existing};
        for (const key in defaults) {
            if (defaults.hasOwnProperty(key)) {
                if (typeof defaults[key] === 'object' && defaults[key] !== null && !Array.isArray(defaults[key])) {
                    result[key] = deepMerge(existing[key], defaults[key]);
                } else if (!(key in existing)) {
                    result[key] = defaults[key];
                }
            }
        }
        return result;
    }

    async function init(key, val) {
        const existing = await setting.get(key);
        if (existing == undefined) {
            await setting.ins(key, val);
        } else if (typeof val === 'object' && val !== null && typeof existing === 'object' && existing !== null) {
            // 智能合并：补充缺失的字段，保留现有的值
            const merged = deepMerge(existing, val);
            if (JSON.stringify(merged) !== JSON.stringify(existing)) {
                console.log(`[Setting] 补充缺失字段: ${key}`);
                await setting.set(key, merged);
            }
        }
    }

    async function initializeDefaults() {
        await init("listen", 5555);
        await init("password", "dstatus");
        await init("site", {
            name: "DStatus",
            url: "https://status.nekoneko.cloud",
        });
        await init("neko_status_url", "https://github.com/fev125/dstatus/releases/download/v1.1");
        await init("debug", true);
        await init("telegram", {
            enabled: false,
            token: "",
            chatIds: [],
            webhook: true,
            webhookPort: 443,
            baseApiUrl: "https://api.telegram.org",
            lastTestTime: 0,
            notificationTypes: {
                serverOnline: true,
                serverOffline: true,
                trafficLimit: true,
                testNotification: true
            }
        });
        await init("personalization", {
            wallpaper: {
                enabled: false,
                url: "",
                brightness: 75,
                fixed: false,
                size: "cover",
                repeat: "repeat",
                blur: {
                    enabled: false,
                    amount: 5
                }
            }
        });
        await init("polling_interval", 3000);
        await init("websocket_interval", 4000);
        
        // PostgreSQL配置已迁移到专用表postgres_config，不再需要JSON初始化
    }

    // Since the main export is an object with a `setting` property,
    // we need to return it after initializing the defaults.
    // The initialization needs to be handled by the caller.
    // We can expose an init function.
    return {
        setting,
        initializeDefaults
    };
}