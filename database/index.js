"use strict";
const dbConfig = require("./config");
const fs = require("fs");
const path = require("path");

const SQLiteAdapter = require("./adapters/sqlite");
const PostgresAdapter = require("./adapters/postgresql");
const DualAdapter = require("./adapters/dual");

module.exports = async (conf = {}) => {
    // 第一阶段：先连接SQLite读取动态配置
    const sqliteConfig = { path: dbConfig.sqlite.path };
    const tempSQLite = new SQLiteAdapter(sqliteConfig);
    await tempSQLite.connect();
    
    // 确保setting表存在
    await tempSQLite.run(`
        CREATE TABLE IF NOT EXISTS setting (
            key TEXT PRIMARY KEY,
            val TEXT
        )
    `);
    
    // 第二阶段：从专用配置表读取PostgreSQL配置
    let DB;
    let postgresConnectionFailed = false;
    try {
        // 创建临时的配置管理器
        const tempPostgresConfig = require('./postgres_config')(tempSQLite);
        const pgConfig = await tempPostgresConfig.postgresConfig.get();
        
        if (pgConfig.enabled && pgConfig.host && pgConfig.database && pgConfig.username) {
            console.log("[数据库] 发现启用的PostgreSQL配置");
            
            try {
                // 构建PostgreSQL连接字符串
                const encodedUsername = encodeURIComponent(pgConfig.username);
                const encodedPassword = encodeURIComponent(pgConfig.password);
                const connectionString = `postgresql://${encodedUsername}:${encodedPassword}@${pgConfig.host}:${pgConfig.port}/${pgConfig.database}`;
                
                // 使用动态PostgreSQL配置创建DualAdapter
                DB = new DualAdapter(sqliteConfig, { connection: connectionString });
                await tempSQLite.disconnect();
                await DB.connect();
                console.log("[数据库] 使用专用表存储的PostgreSQL配置");
            } catch (connectError) {
                // PostgreSQL连接失败，降级到SQLite
                postgresConnectionFailed = true;
                DB = tempSQLite;
                console.warn(`[数据库] PostgreSQL连接失败: ${connectError.message}`);
                console.log("[数据库] 降级到SQLite模式，程序将继续运行");
                
                // 更新配置表中的测试结果
                try {
                    await tempPostgresConfig.postgresConfig.updateTestResult(false, connectError.message);
                } catch (updateError) {
                    // 忽略更新错误
                }
            }
        } else {
            // PostgreSQL未启用或配置不完整，使用SQLite模式
            DB = tempSQLite;
            const reason = pgConfig.enabled ? '配置不完整' : 'PostgreSQL未启用';
            console.log(`[数据库] ${reason}，使用SQLite模式`);
        }
    } catch (error) {
        console.warn("[数据库] 读取PostgreSQL配置失败，降级到SQLite:", error.message);
        DB = tempSQLite;
    }
    
    // 如果是DualAdapter，启动连接池监控
    if (DB.type === 'dual' && DB.startPoolMonitoring) {
        DB.startPoolMonitoring(60000); // 每分钟输出一次连接池状态
    }

    // 初始化基础数据库表结构（最小化，主要表由迁移系统管理）
    async function initDatabase() {
        // 创建设置表（必须优先创建，其他模块依赖）
        await DB.run(`
            CREATE TABLE IF NOT EXISTS setting (
                key TEXT PRIMARY KEY,
                val TEXT
            )
        `);

        // 其他核心表现在由迁移系统统一管理，这里只保留业务数据初始化
        // 在迁移完成后检查并创建默认分组
    }

    // 执行初始化
    await initDatabase();

    // 在加载模型之前运行迁移
    const migrations = require('./migrations')(DB);
    try {
        await migrations.migrate();
        if (postgresConnectionFailed) {
            console.log("[数据库] 迁移在SQLite模式下完成（PostgreSQL不可用）");
        }
    } catch (migrationError) {
        // 如果是PostgreSQL连接失败导致的迁移问题，给出更友好的提示
        if (postgresConnectionFailed) {
            console.warn("[数据库] PostgreSQL不可用，迁移在SQLite模式下继续");
            console.warn("[数据库] 建议检查PostgreSQL配置或禁用PostgreSQL模式");
        } else {
            console.error("[数据库] 迁移失败:", migrationError.message);
            // 对于关键迁移失败，可能需要抛出错误
            // throw migrationError;
        }
    }

    // 迁移完成后，创建默认业务数据
    try {
        const defaultGroup = await DB.get("SELECT * FROM groups WHERE id = 'default'");
        if (!defaultGroup) {
            await DB.run("INSERT INTO groups (id, name, top) VALUES ('default', '默认分组', 0)");
            console.log("[数据库] 已创建默认分组");
        }
    } catch (error) {
        console.warn("[数据库] 创建默认分组失败:", error.message);
    }

    const servers = require("./servers")(DB).servers,
          autodiscovery = require("./autodiscovery")(DB),
          traffic = require("./traffic")(DB).traffic,
          lt = require("./traffic")(DB).lt,
          load_m = require("./load")(DB).load_m,
          load_h = require("./load")(DB).load_h,
          load_archive = require("./load")(DB).load_archive,
          ssh_scripts = require("./ssh_scripts")(DB).ssh_scripts,
          settingModule = require("./setting")(DB),
          setting = settingModule.setting,
          groups = require("./groups")(DB).groups,
          monitor = require("./monitor")(DB),
          aiReports = require("./ai_reports")(DB),
          postgresConfig = require("./postgres_config")(DB).postgresConfig;

    // 初始化默认配置
    await settingModule.initializeDefaults();

    async function getServers() { return await servers.all(); }

    // 全局设置存储函数
    async function set(key, value) {
        if (key === 'setting') {
            // 将整个设置对象保存，更新各个配置项
            for (const [settingKey, settingValue] of Object.entries(value)) {
                await setting.set(settingKey, settingValue);
            }
            return;
        }

        // 处理其他键值存储
        if (setting && typeof setting.set === 'function') {
            await setting.set(key, value);
        } else {
            console.error(`[数据库] 无法保存设置: ${key}, setting对象不可用`);
        }
    }

    // 检查是否有待执行的PostgreSQL切换
    try {
        const switchPending = await setting.get('postgres_switch_pending');
        if (switchPending === 'true') {
            console.log('[数据库] 检测到待执行的PostgreSQL切换');
            await setting.set('postgres_switch_pending', 'false');
        }
    } catch (error) {
        console.warn('[数据库] 检查切换状态失败:', error.message);
    }

    return {
        DB,
        type: DB.type || 'sqlite',  // 暴露数据库类型
        adapter: DB,    // 暴露适配器实例
        servers, getServers,
        traffic, lt,
        load_m, load_h, load_archive,
        ssh_scripts,
        autodiscovery,
        setting,
        groups,
        monitor,
        aiReports,
        postgresConfig,  // 新增：PostgreSQL专用配置管理器
        // 导出配置实例以供其他模块使用
        config: dbConfig,
        // 添加全局设置函数
        set,
        // 暴露常用适配器方法
        run: DB.run.bind(DB),
        get: DB.get.bind(DB),
        all: DB.all.bind(DB)
    };
}

/**
 * 热切换到PostgreSQL
 * @param {Object} pgConfig PostgreSQL配置
 */
async function performHotSwitch(pgConfig) {
    try {
        console.log('[数据库] 开始执行热切换到PostgreSQL...');
        
        const DualAdapter = require('./adapters/dual');
        const sqliteConfig = { path: dbConfig.sqlite.path };
        
        // 构建连接字符串
        const connectionString = `postgresql://${pgConfig.username}:${pgConfig.password}@${pgConfig.host}:${pgConfig.port}/${pgConfig.database}`;
        
        // 创建新的DualAdapter
        const newAdapter = new DualAdapter(sqliteConfig, { connection: connectionString });
        await newAdapter.connect();
        
        // 获取当前模块实例
        const currentDB = module.exports;
        if (currentDB && currentDB.adapter) {
            const oldAdapter = currentDB.adapter;
            
            // 原子性切换适配器
            currentDB.DB = newAdapter;
            currentDB.adapter = newAdapter;
            currentDB.type = 'dual';
            
            // 重新绑定方法
            currentDB.run = newAdapter.run.bind(newAdapter);
            currentDB.get = newAdapter.get.bind(newAdapter);
            currentDB.all = newAdapter.all.bind(newAdapter);
            
            console.log('[数据库] 热切换成功，现在使用PostgreSQL+SQLite双数据库模式');
            
            // 延迟清理旧连接
            setTimeout(async () => {
                try {
                    if (oldAdapter && oldAdapter.disconnect) {
                        await oldAdapter.disconnect();
                        console.log('[数据库] 旧连接已清理');
                    }
                } catch (error) {
                    console.warn('[数据库] 清理旧连接失败:', error.message);
                }
            }, 5000);
        }
        
    } catch (error) {
        console.error('[数据库] 热切换失败:', error.message);
        throw error;
    }
}

// 导出热切换函数
module.exports.performHotSwitch = performHotSwitch;