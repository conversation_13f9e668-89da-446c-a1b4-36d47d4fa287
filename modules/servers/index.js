"use strict";
const {initServer,removeServer}=require("./func"),
    ssh=require("../../ssh"),
    uuid = require('uuid');

// T056: 引入节点限制检查中间件
const { nodeLimitCheck } = require('./middleware/nodeLimitCheck');
const { initNodeStatsApi } = require('./nodeStatsApi');

module.exports=svr=>{
const {db,pr}=svr.locals;

// 加载测试API
require('./test-api')(svr);

// T056: 初始化节点统计API - 延迟初始化
function initNodeStatsDelayed() {
    const licenseEnhanced = svr.locals['license-enhanced'];
    if (licenseEnhanced) {
        try {
            initNodeStatsApi(svr, licenseEnhanced);
            console.log('[T056] 节点统计API初始化成功');
        } catch (error) {
            console.error('[T056] 初始化节点统计API失败:', error);
        }
    } else {
        // 如果license-enhanced还没有加载，延迟1秒后重试
        setTimeout(initNodeStatsDelayed, 1000);
    }
}

// 立即尝试初始化，如果失败会自动重试
initNodeStatsDelayed();

// 挂载手动续期功能（独立模块，最小侵入）
try {
    require('./renewal')(svr);
} catch (e) {
    console.error('[续期模块] 加载失败:', e.message);
}

// 添加获取服务器数量的API
svr.get("/admin/api/servers/count", async(req, res) => {
    try {
        const activeCount = await db.servers.get_active();
        const totalCount = await db.servers.get_all();
        
        res.json(pr(1, {
            data: activeCount.length,
            total: totalCount.length,
            active: activeCount.length
        }));
    } catch (error) {
        console.error('获取服务器数量失败:', error);
        res.json(pr(0, '获取服务器数量失败', { error: error.message }));
    }
});

svr.post("/admin/servers/add", nodeLimitCheck, async(req,res)=>{
    try {
        const {
            sid,
            name,
            data,
            top,
            status,
            expire_time,
            group_id,
            traffic_limit,
            traffic_reset_day,
            traffic_alert_percent,
            traffic_calibration_date,
            traffic_calibration_value,
            traffic_direction
        } = req.body;

        // 验证必要字段
        if (!name) {
            return res.json(pr(0, '服务器名称不能为空'));
        }

        // 生成服务器ID（如果未提供）
        const serverSid = sid || uuid.v1();

        // 添加服务器记录
        await db.servers.ins(
            serverSid,
            name,
            data,
            top || 0,
            status || 1,
            expire_time || null,
            group_id || 'default',
            {
                traffic_limit,
                traffic_reset_day,
                traffic_alert_percent,
                traffic_calibration_date,
                traffic_calibration_value,
                traffic_direction
            }
        );

        // 获取服务器IP地址 - 增强JSON解析错误处理
        let serverData;
        try {
            serverData = typeof data === 'string' ? JSON.parse(data) : data;
        } catch (parseError) {
            console.error('解析服务器数据失败:', parseError);
            console.error('原始数据:', data);
            return res.json(pr(0, '服务器配置数据格式错误，请检查SSH密钥等字段是否包含特殊字符'));
        }
        const ip = serverData.ip || serverData.ssh?.host;

        // 如果有IP地址，尝试获取IP国家信息
        if (ip && svr.locals.stats && svr.locals.stats.ipLocationService) {
            try {
                // 获取服务器完整信息
                const server = await db.servers.get(serverSid);

                // 调用IP位置服务更新位置信息
                await svr.locals.stats.ipLocationService.updateServerLocation(server, db);
            } catch (locationError) {
                // 获取位置信息失败不影响服务器添加
            }
        }

        // 更新服务器配置缓存
        try {
            const newServer = await db.servers.get(serverSid);
            if (newServer && svr.locals.stats?.updateServerConfigCache) {
                svr.locals.stats.updateServerConfigCache(serverSid, newServer);
            }
        } catch (cacheError) {
            console.warn('[缓存管理] 服务器添加后更新缓存失败:', cacheError.message);
        }

        // T056: 添加节点限制信息到成功响应
        const responseData = {
            serverId: serverSid,
            nodeLimit: req.nodeLimit || {
                current: 0,
                max: 5,
                remaining: 5,
                isGrandfathered: false,
                canAddMore: true
            }
        };

        res.json(pr(1, responseData));
    } catch (error) {
        res.json(pr(0, '添加失败: ' + error.message));
    }
});
svr.get("/admin/servers/add", async (req,res)=>{
    try {
        res.render(`admin/servers/add`,{
            groups: await db.groups.all() // 添加分组数据
        });
    } catch (error) {
        console.error('获取添加服务器页面失败:', error);
        res.status(500).render("error", { error: '获取页面失败' });
    }
});
/**
 * @description 修改服务器编辑API端点，在修改服务器时获取IP国家图标
 * 1. 为什么要修改：需要在修改服务器时自动获取IP国家图标
 * 2. 修改了什么：在更新服务器后，调用IP位置服务获取国家信息
 * 3. 可能的影响：服务器修改时会自动获取国家图标，提高用户体验
 * @modified 2023-11-15
 */
svr.post("/admin/servers/:sid/edit",async(req,res)=>{
    try {
        const {sid} = req.params;
        const {
            name, data, top, status, expire_time, group_id,
            traffic_limit, traffic_reset_day, traffic_alert_percent,
            traffic_calibration_date, traffic_calibration_value, traffic_direction
        } = req.body;
        const server = await db.servers.get(sid);

        if (!server) {
            return res.json(pr(0, '服务器不存在'));
        }

        // 准备更新的数据
        let updatedData = server.data;
        if (data) {
            // 解析前端传来的数据 - 增强JSON解析错误处理
            let newData;
            try {
                newData = typeof data === 'string' ? JSON.parse(data) : data;
            } catch (parseError) {
                console.error('解析服务器编辑数据失败:', parseError);
                console.error('原始数据:', data);
                return res.json(pr(0, '服务器配置数据格式错误，请检查SSH密钥等字段是否包含特殊字符'));
            }

            // 使用SecureDataHandler处理SSH密码数据
            const SecureDataHandler = require('./secure-data-handler');
            
            // 处理SSH密码字段，如果是占位符则保留原密码
            let processedData = { ...newData };
            if (processedData.ssh && server.data && server.data.ssh) {
                processedData.ssh = SecureDataHandler.processSubmittedSSHData(processedData.ssh, server.data.ssh);
            }

            // 处理位置信息
            if (processedData.location && processedData.location.preserveExisting) {
                // 如果前端标记了保留现有位置信息
                console.log(`[${new Date().toISOString()}] 编辑服务器 ${server.name}：保留现有位置信息`);
                
                if (updatedData.location) {
                    // 如果原服务器有位置信息，保留它并更新 manual 标记
                    console.log(`[${new Date().toISOString()}] 原位置信息:`, JSON.stringify(updatedData.location));
                    processedData.location = {
                        ...updatedData.location,
                        manual: processedData.location.manual !== undefined ? processedData.location.manual : (updatedData.location ? updatedData.location.manual : false)
                    };
                } else {
                    // 如果原服务器没有位置信息，删除 location 字段，让系统稍后自动获取（如果是自动模式）
                    console.log(`[${new Date().toISOString()}] 原服务器无位置信息`);
                    delete processedData.location;
                }
            } else if (!processedData.location && updatedData.location) {
                // 如果前端没有发送位置信息，保留原有的
                console.log(`[${new Date().toISOString()}] 保留服务器 ${server.name} 的原有位置信息`);
                console.log(`[${new Date().toISOString()}] 原位置信息:`, JSON.stringify(updatedData.location));
                processedData.location = updatedData.location;
            }

            updatedData = processedData;
        }

        // 更新服务器信息
        await db.servers.upd(
            sid,
            name || server.name,
            updatedData,
            top ?? server.top,
            expire_time || server.expire_time,
            group_id || server.group_id,
            traffic_limit,
            traffic_reset_day,
            traffic_alert_percent,
            traffic_calibration_date,
            traffic_calibration_value,
            traffic_direction
        );

        // 更新状态
        if (status != null) {
            await db.servers.upd_status(sid, status);
        }

        // 检查IP地址是否变更，如果变更则重新获取IP国家信息
        const oldIp = server.data.ip || server.data.ssh?.host;
        const newIp = updatedData.ip || updatedData.ssh?.host;
        
        // 检查是否应该自动获取位置信息
        // 条件：1) 有新IP 2) IP变更或无位置信息 3) 不是手动设置的位置 4) IPLocationService可用
        const shouldAutoFetchLocation = newIp && 
            (newIp !== oldIp || !updatedData.location) && 
            (!updatedData.location || !updatedData.location.manual) &&
            svr.locals.stats && 
            svr.locals.stats.ipLocationService;

        if (shouldAutoFetchLocation) {
            try {
                console.log(`[${new Date().toISOString()}] 修改服务器，正在获取IP位置信息: ${name || server.name} (${newIp})`);

                // 获取更新后的服务器完整信息
                const updatedServer = await db.servers.get(sid);

                // 清除缓存，强制重新获取
                if (svr.locals.stats.ipLocationService.ipCache && svr.locals.stats.ipLocationService.ipCache[newIp]) {
                    delete svr.locals.stats.ipLocationService.ipCache[newIp];
                }

                // 清除失败记录
                svr.locals.stats.ipLocationService.updateFailures.delete(sid);

                // 调用IP位置服务更新位置信息
                await svr.locals.stats.ipLocationService.updateServerLocation(updatedServer, db);

                console.log(`[${new Date().toISOString()}] 服务器IP位置信息获取完成: ${name || server.name} (${newIp})`);
            } catch (locationError) {
                console.error(`[${new Date().toISOString()}] 获取服务器IP位置信息失败: ${name || server.name} (${newIp})`, locationError);
                // 获取位置信息失败不影响服务器编辑
            }
        }

        // 更新服务器配置缓存
        try {
            const updatedServer = await db.servers.get(sid);
            if (updatedServer && svr.locals.stats?.updateServerConfigCache) {
                svr.locals.stats.updateServerConfigCache(sid, updatedServer);
            }
        } catch (cacheError) {
            console.warn('[缓存管理] 服务器编辑后更新缓存失败:', cacheError.message);
        }

        res.json(pr(1, '修改成功'));
    } catch (error) {
        console.error('更新服务器信息失败:', error);
        res.json(pr(0, '更新失败: ' + error.message));
    }
});
/**
 * 获取服务器相关数据的统计信息
 * @route POST /admin/servers/:sid/stats
 */
svr.post("/admin/servers/:sid/stats", async(req, res) => {
    var {sid} = req.params;

    // 获取服务器信息
    const server = await db.servers.get(sid);
    if (!server) {
        return res.json(pr(0, '服务器不存在'));
    }

    try {
        // 统计相关数据
        const stats = {};

        // 统计流量数据
        try {
            // 统计traffic表中的记录数
            const trafficCount = await db.DB.get("SELECT COUNT(*) as count FROM traffic WHERE sid = ?", [sid]);
            const trafficRecords = trafficCount ? trafficCount.count : 0;

            // 统计lt表中的记录数
            const ltCount = await db.DB.get("SELECT COUNT(*) as count FROM lt WHERE sid = ?", [sid]);
            const ltRecords = ltCount ? ltCount.count : 0;

            // 总流量记录数
            stats.traffic = trafficRecords + ltRecords;
        } catch (err) {
            console.error(`[统计] 查询流量数据失败:`, err);
            stats.traffic = 0;
        }

        // 统计负载数据
        try {
            let loadCount = 0;
            for (const table of ['load_m', 'load_h', 'load_archive']) {
                try {
                    const count = await db.DB.get(`SELECT COUNT(*) as count FROM ${table} WHERE sid = ?`, [sid]);
                    loadCount += count ? count.count : 0;
                } catch (err) {
                    console.error(`[统计] 查询 ${table} 表失败:`, err);
                }
            }
            stats.load = loadCount;
        } catch (err) {
            console.error(`[统计] 查询负载数据失败:`, err);
            stats.load = 0;
        }

        // 统计网络质量监控数据
        let tcpingCount = 0;
        for (const table of ['tcping_m', 'tcping_5m', 'tcping_h', 'tcping_d', 'tcping_month', 'tcping_archive']) {
            try {
                const count = await db.DB.get(`SELECT COUNT(*) as count FROM ${table} WHERE sid = ?`, [sid]);
                tcpingCount += count ? count.count : 0;
            } catch (err) {
                console.error(`[统计] 查询 ${table} 表失败:`, err);
            }
        }
        stats.tcping = tcpingCount;

        // 统计监控目标引用
        try {
            const targetsCount = await db.DB.get(`
                SELECT COUNT(*) as count FROM monitor_targets
                WHERE node_id LIKE ? OR node_id LIKE ? OR node_id = ?
            `, [`%"${sid}"%`, `%${sid}%`, sid]);
            stats.monitorTargets = targetsCount ? targetsCount.count : 0;
        } catch (err) {
            console.error(`[统计] 查询监控目标引用失败:`, err);
            stats.monitorTargets = 0;
        }

        res.json(pr(1, stats));
    } catch (error) {
        console.error(`[${new Date().toISOString()}] 获取服务器 ${server.name} (${sid}) 统计信息失败:`, error);
        res.json(pr(0, '获取统计信息失败'));
    }
});

/**
 * 获取孤立数据统计信息
 * @route POST /admin/servers/orphan-stats
 */
svr.post("/admin/servers/orphan-stats", async(req, res) => {
    try {
        const orphanStats = {};

        // 定义包含sid字段的表
        const sidTables = [
            'traffic', 'lt', 'load_m', 'load_h', 'load_archive',
            'tcping_m', 'tcping_5m', 'tcping_h', 'tcping_d', 'tcping_month', 'tcping_archive',
            'traffic_calibration'
        ];

        let totalOrphans = 0;

        for (const table of sidTables) {
            try {
                const count = await db.DB.get(`
                    SELECT COUNT(*) as count FROM ${table}
                    WHERE sid IS NOT NULL AND sid NOT IN (SELECT sid FROM servers)
                `, []);
                const orphanCount = count ? count.count : 0;
                orphanStats[table] = orphanCount;
                totalOrphans += orphanCount;
            } catch (err) {
                console.error(`[孤立数据统计] 查询 ${table} 表失败:`, err);
                orphanStats[table] = 0;
            }
        }

        orphanStats.total = totalOrphans;
        res.json(pr(1, orphanStats));
    } catch (error) {
        console.error(`[${new Date().toISOString()}] 获取孤立数据统计失败:`, error);
        res.json(pr(0, '获取孤立数据统计失败'));
    }
});

/**
 * 清理孤立数据
 * @route POST /admin/servers/cleanup-orphans
 */
svr.post("/admin/servers/cleanup-orphans", async(req, res) => {
    try {
        const client = await db.DB.beginTransaction();
        const cleanupResults = {};
        let totalCleaned = 0;

        // 定义包含sid字段的表
        const sidTables = [
            'traffic', 'lt', 'load_m', 'load_h', 'load_archive',
            'tcping_m', 'tcping_5m', 'tcping_h', 'tcping_d', 'tcping_month', 'tcping_archive',
            'traffic_calibration'
        ];

        try {
            for (const table of sidTables) {
                try {
                    const result = await client.query(`
                        DELETE FROM ${table}
                        WHERE sid IS NOT NULL AND sid NOT IN (SELECT sid FROM servers)
                    `);
                    const deletedCount = result.changes || result.rowCount || 0;
                    cleanupResults[table] = deletedCount;
                    totalCleaned += deletedCount;
                    console.log(`[孤立数据清理] ${table} 表清理了 ${deletedCount} 条记录`);
                } catch (err) {
                    console.error(`[孤立数据清理] 清理 ${table} 表失败:`, err);
                    cleanupResults[table] = 0;
                }
            }

            await db.DB.commitTransaction(client);
            cleanupResults.total = totalCleaned;

            console.log(`[${new Date().toISOString()}] 孤立数据清理完成，共清理 ${totalCleaned} 条记录`);
            res.json(pr(1, cleanupResults));
        } catch (error) {
            await db.DB.rollbackTransaction(client);
            throw error;
        }
    } catch (error) {
        console.error(`[${new Date().toISOString()}] 清理孤立数据失败:`, error);
        res.json(pr(0, '清理孤立数据失败: ' + error.message));
    }
});

/**
 * 删除服务器及其所有相关数据
 * @route POST /admin/servers/:sid/del
 */
svr.post("/admin/servers/:sid/del", async(req, res) => {
    var {sid} = req.params;

    // 获取服务器信息，用于日志记录
    const server = await db.servers.get(sid);
    if (!server) {
        return res.json(pr(0, '服务器不存在'));
    }

    // 使用级联删除函数
    const success = await db.servers.cascadeDelete(sid);

    if (success) {
        console.log(`[${new Date().toISOString()}] 管理员删除了服务器 ${server.name} (${sid}) 及其所有相关数据`);
        
        // 删除服务器配置缓存
        try {
            if (svr.locals.stats?.updateServerConfigCache) {
                svr.locals.stats.updateServerConfigCache(sid, null); // null表示删除
            }
        } catch (cacheError) {
            console.warn('[缓存管理] 服务器删除后清除缓存失败:', cacheError.message);
        }
        
        res.json(pr(1, '删除成功'));
    } else {
        console.error(`[${new Date().toISOString()}] 删除服务器 ${server.name} (${sid}) 失败`);
        res.json(pr(0, '删除失败，请查看日志'));
    }
});
svr.post("/admin/servers/:sid/init",async(req,res)=>{
    var {sid}=req.params,
        server=await db.servers.get(sid);
    // 获取客户端下载链接，并确保其有效
    let neko_status_url = await db.setting.get("neko_status_url");
    if (!neko_status_url) {
        console.warn('[初始化服务器] 从数据库获取的neko_status_url为空');
        neko_status_url = '"https://github.com/fev125/dstatus/releases/download/v1.1"/neko-status';
    }
    res.json(await initServer(server, neko_status_url));
});

svr.post("/admin/servers/:sid/remove",async(req,res)=>{
    var {sid}=req.params,
        server=await db.servers.get(sid);
    if (!server) {
        return res.json(pr(0, '服务器不存在'));
    }
    res.json(await removeServer(server));
});

/**
 * @description 重置服务器流量数据API端点
 * 1. 为什么要添加：解决新添加服务器历史流量被均匀填充为网卡流量导致数据不准确的问题
 * 2. 功能说明：清空指定服务器的所有历史流量记录，并重新初始化为空值
 * 3. 影响范围：仅影响指定服务器的流量统计数据，不影响其他配置
 * @added 2023-12-03
 */
svr.post("/admin/servers/:sid/reset-traffic", async(req, res) => {
    try {
        const { sid } = req.params;
        const server = await db.servers.get(sid);

        if (!server) {
            return res.json(pr(0, '服务器不存在'));
        }

        // 删除当前流量记录
        await db.traffic.del(sid);

        // 删除最后记录的流量值
        await db.lt.del(sid);

        // 重新初始化流量记录为空值
        await db.traffic.ins(sid);

        // 记录操作日志
        console.log(`[${new Date().toISOString()}] 管理员重置了服务器 ${server.name} (${sid}) 的流量数据`);

        res.json(pr(1, `已成功重置 ${server.name} 的流量数据`));
    } catch (error) {
        console.error('重置流量数据失败:', error);
        res.json(pr(0, '重置失败: ' + error.message));
    }
});

svr.get("/admin/servers", async (req,res)=>{
    try {
        // 获取所有服务器数据，让前端处理分页
        const servers = (await db.servers.all()).map(server => {
            // 解析服务器数据中的JSON字段
            let serverData = server;
            if (typeof server.data === 'string') {
                try {
                    serverData = { ...server, data: JSON.parse(server.data) };
                } catch (error) {
                    console.error(`解析服务器 ${server.sid} 数据失败:`, error);
                    serverData = { ...server, data: {} };
                }
            }
            return serverData;
        });
        
        // 服务器数据已准备好，前端会通过 API 获取在线状态
        res.render("admin/servers",{
            servers: servers
        });
    } catch (error) {
        console.error('获取服务器列表失败:', error);
        res.status(500).render("error", { error: '获取服务器列表失败' });
    }
});
svr.post("/admin/servers/ord", async (req,res)=>{
    try {
        const {servers} = req.body;
        if (!Array.isArray(servers)) {
            return res.json(pr(0, '无效的服务器列表'));
        }

        // 使用时间戳作为基数，确保顺序正确
        const baseOrder = Date.now();

        // 更新排序 - 使用数组索引确保顺序正确
        for (let index = 0; index < servers.length; index++) {
            const sid = servers[index];
            const server = await db.servers.get(sid);
            if (!server) {
                throw new Error(`服务器 ${sid} 不存在`);
            }
            // 使用 baseOrder - index 确保较新的排在前面
            await db.servers.upd_top(sid, baseOrder - index);
        }

        // 排序更新后刷新内存缓存，确保前端实时读取到最新的 top 顺序
        try {
            if (svr.locals.stats && typeof svr.locals.stats.reloadServerConfigCache === 'function') {
                await svr.locals.stats.reloadServerConfigCache();
            }
        } catch (cacheError) {
            console.warn('[缓存管理] 排序更新后刷新缓存失败:', cacheError.message);
            // 不中断主流程
        }

        res.json(pr(1, '排序更新成功'));
    } catch (error) {
        console.error('更新服务器排序失败:', error);
        res.json(pr(0, '排序更新失败: ' + error.message));
    }
});
svr.get("/admin/servers/:sid", async (req,res)=>{
    try {
        var {sid}=req.params,server=await db.servers.get(sid);

        if (!server) {
            return res.status(404).render("error", { error: '服务器不存在' });
        }

        // 使用SecureDataHandler处理敏感数据
        const SecureDataHandler = require('./secure-data-handler');

        // 对服务器数据进行安全处理
        const sanitizedServer = SecureDataHandler.sanitizeServerData(server);

        res.render(`admin/servers/edit`,{
            server: sanitizedServer,
            groups: await db.groups.all() // 添加分组数据
        });
    } catch (error) {
        console.error('获取服务器编辑页面失败:', error);
        res.status(500).render("error", { error: '获取服务器信息失败' });
    }
});

// ==================== 标签管理API ====================

/**
 * 获取服务器标签
 */
svr.get("/api/servers/:sid/tags", async (req, res) => {
    try {
        const { sid } = req.params;
        const server = await db.servers.get(sid);
        
        if (!server) {
            return res.json(pr(0, '服务器不存在'));
        }
        
        // server.data 已经在 db.servers.get() 中被解析过了
        const serverData = server.data || {};
        const tags = serverData.tags || [];
        res.json(pr(1, tags));
    } catch (error) {
        console.error('获取服务器标签失败:', error);
        res.json(pr(0, '获取标签失败: ' + error.message));
    }
});

/**
 * 更新服务器标签
 */
svr.put("/api/servers/:sid/tags", async (req, res) => {
    // 检查是否为管理员
    if (!req.admin) {
        return res.status(403).json(pr(0, '无权限执行此操作'));
    }
    
    try {
        const { sid } = req.params;
        const { tags } = req.body;
        
        const server = await db.servers.get(sid);
        if (!server) {
            return res.json(pr(0, '服务器不存在'));
        }
        
        // 验证标签数据
        if (!Array.isArray(tags)) {
            return res.json(pr(0, '标签数据格式错误'));
        }
        
        // 验证每个标签的结构
        for (const tag of tags) {
            if (!tag.name || typeof tag.name !== 'string') {
                return res.json(pr(0, '标签名称不能为空'));
            }
            if (tag.name.length > 20) {
                return res.json(pr(0, '标签名称不能超过20个字符'));
            }
            if (tag.color && !/^#[0-9A-Fa-f]{6}$/.test(tag.color)) {
                return res.json(pr(0, '标签颜色格式错误'));
            }
        }
        
        // 限制标签数量
        if (tags.length > 4) {
            return res.json(pr(0, '标签数量不能超过4个'));
        }
        
        // server.data 已经在 db.servers.get() 中被解析过了
        const serverData = server.data || {};
        
        // 归一化工具
        const normalizeIcon = (icon) => {
            if (!icon || typeof icon !== 'string') return 'tag';
            return icon.toLowerCase().replace(/^ti\s*-/, '').replace(/_/g, '-') || 'tag';
        };
        const normalizeColor = (color) => (/^#[0-9A-Fa-f]{6}$/.test(color || '') ? color : '#6b7280');

        // 更新标签（归一化icon/color）
        serverData.tags = tags.map((tag, index) => ({
            id: tag.id || `tag_${Date.now()}_${index}`,
            name: tag.name,
            color: normalizeColor(tag.color),
            icon: normalizeIcon(tag.icon)
        }));
        
        // 保存到数据库
        await db.servers.upd_data(sid, serverData);

        // 刷新服务器配置缓存，确保首页与其他会话及时获得最新标签
        try {
            const updatedServer = await db.servers.get(sid);
            if (updatedServer && svr.locals.stats?.updateServerConfigCache) {
                svr.locals.stats.updateServerConfigCache(sid, updatedServer);
            }
        } catch (cacheErr) {
            console.warn('[标签] 更新后刷新配置缓存失败:', cacheErr.message);
        }

        res.json(pr(1, '标签更新成功'));
    } catch (error) {
        console.error('更新服务器标签失败:', error);
        res.json(pr(0, '更新标签失败: ' + error.message));
    }
});

/**
 * 添加单个标签
 */
svr.post("/api/servers/:sid/tags", async (req, res) => {
    // 检查是否为管理员
    if (!req.admin) {
        return res.status(403).json(pr(0, '无权限执行此操作'));
    }
    
    try {
        const { sid } = req.params;
        const { name, color, icon } = req.body;
        
        const server = await db.servers.get(sid);
        if (!server) {
            return res.json(pr(0, '服务器不存在'));
        }
        
        // 验证参数
        if (!name || typeof name !== 'string') {
            return res.json(pr(0, '标签名称不能为空'));
        }
        if (name.length > 20) {
            return res.json(pr(0, '标签名称不能超过20个字符'));
        }
        if (color && !/^#[0-9A-Fa-f]{6}$/.test(color)) {
            return res.json(pr(0, '标签颜色格式错误'));
        }
        
        // server.data 已经在 db.servers.get() 中被解析过了
        const serverData = server.data || {};
        const tags = serverData.tags || [];
        
        // 检查标签数量限制
        if (tags.length >= 4) {
            return res.json(pr(0, '标签数量不能超过4个'));
        }
        
        // 检查重名
        if (tags.some(tag => tag.name === name)) {
            return res.json(pr(0, '标签名称已存在'));
        }
        
        // 添加新标签
        const normalizeIcon = (i) => (i && typeof i === 'string' ? i.toLowerCase().replace(/^ti\s*-/, '').replace(/_/g, '-') : 'tag') || 'tag';
        const normalizeColor = (c) => (/^#[0-9A-Fa-f]{6}$/.test(c || '') ? c : '#6b7280');
        const newTag = {
            id: `tag_${Date.now()}`,
            name,
            color: normalizeColor(color),
            icon: normalizeIcon(icon)
        };
        
        tags.push(newTag);
        serverData.tags = tags;
        
        // 保存到数据库
        await db.servers.upd_data(sid, serverData);

        // 刷新服务器配置缓存
        try {
            const updatedServer = await db.servers.get(sid);
            if (updatedServer && svr.locals.stats?.updateServerConfigCache) {
                svr.locals.stats.updateServerConfigCache(sid, updatedServer);
            }
        } catch (cacheErr) {
            console.warn('[标签] 添加后刷新配置缓存失败:', cacheErr.message);
        }

        res.json(pr(1, newTag));
    } catch (error) {
        console.error('添加服务器标签失败:', error);
        res.json(pr(0, '添加标签失败: ' + error.message));
    }
});

/**
 * 删除标签
 */
svr.delete("/api/servers/:sid/tags/:tagId", async (req, res) => {
    // 检查是否为管理员
    if (!req.admin) {
        return res.status(403).json(pr(0, '无权限执行此操作'));
    }
    
    try {
        const { sid, tagId } = req.params;
        
        const server = await db.servers.get(sid);
        if (!server) {
            return res.json(pr(0, '服务器不存在'));
        }
        
        // server.data 已经在 db.servers.get() 中被解析过了
        const serverData = server.data || {};
        const tags = serverData.tags || [];
        const tagIndex = tags.findIndex(tag => tag.id === tagId);
        
        if (tagIndex === -1) {
            return res.json(pr(0, '标签不存在'));
        }
        
        // 删除标签
        tags.splice(tagIndex, 1);
        serverData.tags = tags;
        
        // 保存到数据库
        await db.servers.upd_data(sid, serverData);

        // 刷新服务器配置缓存
        try {
            const updatedServer = await db.servers.get(sid);
            if (updatedServer && svr.locals.stats?.updateServerConfigCache) {
                svr.locals.stats.updateServerConfigCache(sid, updatedServer);
            }
        } catch (cacheErr) {
            console.warn('[标签] 删除后刷新配置缓存失败:', cacheErr.message);
        }

        res.json(pr(1, '标签删除成功'));
    } catch (error) {
        console.error('删除服务器标签失败:', error);
        res.json(pr(0, '删除标签失败: ' + error.message));
    }
});

// WebSSH 功能权限中间件
const checkWebSSHFeature = (req, res, next) => {
    try {
        const licenseEnhanced = svr.locals['license-enhanced'];
        if (!licenseEnhanced || !licenseEnhanced.featureChecker) {
            console.warn('[WebSSH] License Enhanced 或 FeatureChecker 未找到，允许访问');
            return next();
        }
        
        const featureChecker = licenseEnhanced.featureChecker;
        const check = featureChecker.checkFeature('WEBSSH');
        
        if (check.allowed) {
            console.log('[WebSSH] 功能检查通过，允许访问');
            next();
        } else {
            console.log('[WebSSH] 功能检查失败:', check.message);
            // 对于WebSocket连接，发送错误消息并关闭连接
            if (req.ws) {
                // WebSocket连接
                req.ws.send(JSON.stringify({
                    error: 'Feature not available',
                    message: check.message,
                    currentPlan: check.currentPlan,
                    requiredFeature: 'WebSSH'
                }));
                req.ws.close();
            } else {
                // HTTP请求
                res.status(403).json(check);
            }
        }
    } catch (error) {
        console.error('[WebSSH] 功能检查异常:', error);
        next(); // 出错时允许访问
    }
};

svr.ws("/admin/servers/:sid/ws-ssh/:data", checkWebSSHFeature, async (ws,req)=>{
    try {
        var {sid,data}=req.params,server=await db.servers.get(sid);
        if (!server) {
            ws.send(JSON.stringify({error: '服务器不存在'}));
            return ws.close();
        }
        if(data)data=JSON.parse(data);
        ssh.createSocket(server.data.ssh,ws,data);
    } catch (error) {
        console.error('WebSSH连接失败:', error);
        ws.send(JSON.stringify({error: 'WebSSH连接失败: ' + error.message}));
        ws.close();
    }
})

svr.get("/get-neko-status",async(req,res)=>{
    var path=__dirname+'/neko-status';
    // if(!fs.existsSync(path)){
    //     await fetch("文件url", {
    //         method: 'GET',
    //         headers: { 'Content-Type': 'application/octet-stream' },
    //     }).then(res=>res.buffer()).then(_=>{
    //         fs.writeFileSync(path,_,"binary");
    //     });
    // }
    res.sendFile(path);
})

svr.put("/api/server/:sid", async (req, res) => {
    const { sid } = req.params;
    const { group_id } = req.body;

    try {
        // 获取当前服务器信息
        const server = await db.servers.get(sid);
        if (!server) {
            return res.status(404).json({ success: false, message: '服务器不存在' });
        }

        // 使用专门的方法更新 group_id
        try {
            await db.servers.upd_group_id(sid, group_id);
            console.log(`Updated server ${sid} group_id to ${group_id}`);
            // 刷新服务器配置缓存
            try {
                const updatedServer = await db.servers.get(sid);
                if (updatedServer && svr.locals.stats?.updateServerConfigCache) {
                    svr.locals.stats.updateServerConfigCache(sid, updatedServer);
                }
            } catch (cacheErr) {
                console.warn('[分组变更] 刷新服务器配置缓存失败:', cacheErr.message);
            }

            // 广播服务器列表变更（仅管理员连接可见）
            try {
                if (svr.locals.broadcastServerListChange) {
                    const updatedServer = await db.servers.get(sid).catch(() => null);
                    svr.locals.broadcastServerListChange('updated', updatedServer ? {
                        sid: updatedServer.sid,
                        name: updatedServer.name,
                        group_id: updatedServer.group_id,
                        status: updatedServer.status
                    } : { sid, group_id });
                }
            } catch (broadcastErr) {
                console.warn('[分组变更] 广播列表变更失败:', broadcastErr.message);
            }

            res.json({ success: true });
        } catch (error) {
            console.error('Failed to update group_id:', error);
            res.status(500).json({ success: false, message: '更新分组失败' });
        }
    } catch (error) {
        console.error('更新服务器信息失败:', error);
        res.status(500).json({ success: false, message: '更新服务器信息失败' });
    }
});

/**
 * 手动获取服务器位置信息的API
 * POST /api/admin/servers/:sid/fetch-location
 */
svr.post("/api/admin/servers/:sid/fetch-location", async (req, res) => {
    // 检查是否为管理员
    if (!req.admin) {
        return res.status(403).json({ success: false, message: '无权限执行此操作' });
    }

    const { sid } = req.params;

    try {
        // 获取服务器信息
        const server = await db.servers.get(sid);
        if (!server) {
            return res.status(404).json({ success: false, message: '服务器不存在' });
        }

        // 获取服务器数据
        const serverData = server.data || {};

        // 使用与 IPLocationService 相同的逻辑获取IP地址
        const ip = serverData.ip || serverData.host || serverData.ssh?.host;
        if (!ip) {
            return res.status(400).json({ success: false, message: '服务器无有效IP地址' });
        }

        console.log(`[${new Date().toISOString()}] 手动触发获取服务器 ${server.name} (${ip}) 位置信息`);

        // 获取状态模块实例
        const statsModule = svr.locals.stats;
        if (!statsModule || !statsModule.ipLocationService) {
            return res.status(500).json({ success: false, message: 'IP位置服务不可用' });
        }

        // 保存当前位置信息用于比较
        const currentLocation = serverData.location || {};
        const currentCode = currentLocation.code || currentLocation.country?.code;

        // 清除缓存和失败计数，强制重新获取
        statsModule.ipLocationService.updateFailures.delete(sid);

        // 如果IP在缓存中，删除以强制刷新
        if (statsModule.ipLocationService.ipCache && statsModule.ipLocationService.ipCache[ip]) {
            delete statsModule.ipLocationService.ipCache[ip];
            console.log(`[${new Date().toISOString()}] 已清除 IP ${ip} 的缓存记录`);
        }

        // 调用IP位置服务更新位置信息
        const result = await statsModule.ipLocationService.updateServerLocation(server, db);
        console.log(`[${new Date().toISOString()}] 位置更新结果:`, JSON.stringify(result));

        // 检查更新结果
        if (result.success) {
            // 更新成功
            const locationData = result.data?.location || {};
            const responseData = {
                success: true,
                message: result.unchanged ? '位置信息未变化' : '位置信息更新成功',
                unchanged: result.unchanged || false,
                location: {
                    code: locationData.code || locationData.country?.code,
                    name: locationData.country?.name,
                    name_zh: locationData.country?.name_zh,
                    flag: locationData.country?.flag,
                    previous: currentCode ? {
                        code: currentCode,
                        name: currentLocation.country?.name,
                        name_zh: currentLocation.country?.name_zh,
                        flag: currentLocation.country?.flag
                    } : null,
                    updated_at: locationData.updated_at
                }
            };

            console.log(`[${new Date().toISOString()}] 服务器 ${server.name} 位置信息更新成功:`, JSON.stringify(responseData));
            return res.json(responseData);
        } else {
            // 更新失败
            const errorMessage = result.message || result.error || '位置信息更新失败';
            console.log(`[${new Date().toISOString()}] 服务器 ${server.name} 位置信息更新失败: ${errorMessage}`);

            return res.json({
                success: false,
                message: errorMessage,
                error: result.error || errorMessage,
                server_data: {
                    name: server.name,
                    location: result.data?.location || server.data?.location || null
                }
            });
        }
    } catch (error) {
        console.error(`[${new Date().toISOString()}] 手动获取位置信息失败:`, error);
        return res.status(500).json({
            success: false,
            message: `获取位置信息失败: ${error.message}`,
            error: error.message
        });
    }
});

// 测试SSH连接
svr.post("/admin/test-ssh", async (req, res) => {
    try {
        const { host, port, username, password, privateKey, passphrase, serverId } = req.body;

        // 验证必要参数
        if (!host || !username) {
            return res.json(pr(0, '缺少必要参数'));
        }

        // 处理密码：检测占位符并获取真实密码
        let actualPassword = password;
        if (serverId && password) {
            const SecureDataHandler = require('./secure-data-handler');
            if (SecureDataHandler.isPasswordPlaceholder(password)) {
                // 从数据库获取服务器信息
                const server = await db.servers.get(serverId);
                if (server && server.data && server.data.ssh && server.data.ssh.password) {
                    actualPassword = server.data.ssh.password;
                    console.log(`[SSH测试] 使用服务器 ${serverId} 的已保存密码进行测试`);
                } else {
                    return res.json(pr(0, '无法获取已保存的密码，请重新输入密码或检查服务器配置'));
                }
            }
        }

        // 创建SSH配置
        let sshConfig = {
            host,
            port: port || 22,
            username,
            password: actualPassword || undefined,
            privateKey: privateKey || undefined,
            passphrase: passphrase || undefined
        };

        console.log(`[SSH测试] 正在测试连接: ${host}:${port || 22} 用户名: ${username}`);

        // 测试连接
        const result = await ssh.testConnection(sshConfig);

        // 记录结果但不记录详细错误信息
        console.log(`[SSH测试] 连接结果: ${result.success ? '成功' : '失败'}`);

        if (result.success) {
            return res.json(pr(1, '连接成功'));
        } else {
            return res.json(pr(0, result.message || '连接失败', { details: result.details }));
        }
    } catch (err) {
        console.error(`[SSH测试] 处理请求时出错:`, err);
        return res.json(pr(0, '请求处理失败: ' + err.message));
    }
});
}
