/**
 * 节点数量限制检查中间件
 * 任务: T056 - 客户端节点数量控制功能
 * 功能: 根据用户套餐限制可添加的节点数量，统一以后端API为准
 */

const path = require('path');
const licensePlansCache = require('../../../config/license-plans-cache');

/**
 * 节点限制检查中间件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象  
 * @param {Function} next - 下一个中间件
 */
const nodeLimitCheck = async (req, res, next) => {
    try {
        // 获取license-enhanced模块实例
        const licenseEnhanced = req.app.locals['license-enhanced'];
        if (!licenseEnhanced) {
            throw new Error('license-enhanced模块未找到');
        }
        
        // 获取当前用户的许可证信息
        const license = await licenseEnhanced.getCurrentLicenseStatus();

        // 获取当前活跃节点数
        const db = req.app.locals.db || req.app.locals.database;
        const activeNodes = await getActiveNodesCount(db);

        // 统一使用后端API返回的许可证信息中的maxNodes
        let effectiveMaxNodes;
        if (license.maxNodes && typeof license.maxNodes === 'number' && license.maxNodes > 0) {
            effectiveMaxNodes = license.maxNodes;
            console.log(`[T056] 使用许可证API返回的maxNodes: ${effectiveMaxNodes} (套餐: ${license.planType || license.planName})`);
        } else {
            // 后备方案：如果API没有返回maxNodes，使用默认值（兜底10）
            effectiveMaxNodes = 10;
            console.log(`[T056] API未返回maxNodes，使用默认值: ${effectiveMaxNodes}`);
        }

        // 移除祖父条款逻辑，统一以后端API为准
        const isGrandfathered = false;
        
        // 检查是否允许添加节点
        const canAddMore = activeNodes < effectiveMaxNodes;
        
        if (!canAddMore && activeNodes >= effectiveMaxNodes) {
            // 构建错误信息
            let errorMessage = `已达到最大节点数限制 (${effectiveMaxNodes}个)`;
            let errorData = {
                currentNodes: activeNodes,
                maxNodes: effectiveMaxNodes,
                planName: license.planName || license.planDisplayName || 'Unknown Plan',
                upgradeUrl: '/upgrade',
                limitations: {
                    canAddMore: false,
                    canEdit: true,
                    canDelete: true,
                    reason: 'PLAN_LIMIT_REACHED'
                }
            };
            
            return res.status(403).json({
                success: false,
                error: 'NODE_LIMIT_EXCEEDED',
                message: errorMessage,
                data: errorData
            });
        }
        
        // 将限制信息附加到请求对象
        req.nodeLimit = {
            current: activeNodes,
            max: effectiveMaxNodes,
            remaining: Math.max(0, effectiveMaxNodes - activeNodes),
            canAddMore: canAddMore,
            canEdit: true,
            canDelete: true,
            license: {
                planId: license.planType,
                planName: license.planName
            }
        };
        
        next();
    } catch (error) {
        console.error('[T056] 节点限制检查失败:', error);
        // 检查失败时允许通过，避免阻塞正常操作
        req.nodeLimit = {
            current: 0,
            max: 10,
            remaining: 10,
            canAddMore: true,
            canEdit: true,
            canDelete: true,
            error: true
        };
        next();
    }
};

/**
 * 获取当前活跃节点数量
 * @param {Object} db - 数据库实例
 * @returns {Promise<number>} 活跃节点数
 */
async function getActiveNodesCount(db) {
    try {
        if (!db || !db.DB) {
            console.error('[T056] 数据库实例无效');
            return 0;
        }
        
        const result = await db.DB.get(`
            SELECT COUNT(*) as count 
            FROM servers 
            WHERE status = 1
        `);
        
        return result.count || 0;
    } catch (error) {
        console.error('[T056] 获取节点数量失败:', error);
        return 0;
    }
}

// 祖父条款相关函数已移除 - 统一以后端API为准

// 祖父条款相关的升级建议和迁移函数已移除

/**
 * 获取每个License的节点统计
 * @returns {Promise<Object>} License ID到节点数的映射
 */
async function getNodeStatsPerLicense() {
    try {
        // 简化实现：返回空对象，实际需要根据具体的数据库结构实现
        // TODO: 实现真正的统计逻辑
        return {};
    } catch (error) {
        console.error('[T056] 获取节点统计失败:', error);
        return {};
    }
}

module.exports = {
    nodeLimitCheck,
    getActiveNodesCount
};
