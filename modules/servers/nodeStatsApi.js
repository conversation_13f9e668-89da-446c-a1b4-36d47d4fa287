/**
 * 节点统计API模块
 * 任务: T056 - 客户端节点数量控制功能
 * 功能: 提供节点使用情况的统计信息API
 */

const path = require('path');
const { getActiveNodesCount } = require('./middleware/nodeLimitCheck');
const licensePlansCache = require('../../config/license-plans-cache');

/**
 * 初始化节点统计API路由
 * @param {Object} app - Express应用实例
 * @param {Object} licenseEnhanced - license-enhanced模块实例
 */
function initNodeStatsApi(app, licenseEnhanced) {
    
    /**
     * GET /api/admin/nodes/stats
     * 获取节点使用统计信息
     */
    app.get('/api/admin/nodes/stats', async (req, res) => {
        try {
            const license = await licenseEnhanced.getCurrentLicenseStatus();
            const db = req.app.locals.db || req.app.locals.database;
            const stats = await getNodeStats(db);
            
            // 统一使用许可证API返回的maxNodes（缓存失效兜底10）
            let effectiveMaxNodes = license.maxNodes || 10;
            
            // 计算使用情况
            const canAddMore = stats.active < effectiveMaxNodes;
            const usagePercentage = Math.min((stats.active / effectiveMaxNodes) * 100, 100);
            const remaining = Math.max(0, effectiveMaxNodes - stats.active);
            
            // 构建响应数据
            const responseData = {
                ...stats,
                limit: effectiveMaxNodes,
                planName: license.planName || '免费版',
                planId: license.planType || 'free',
                canAddMore: canAddMore,
                canEdit: true,
                canDelete: true,
                usage: {
                    percentage: usagePercentage,
                    remaining: remaining,
                    warningLevel: getWarningLevel(stats.active, effectiveMaxNodes)
                }
            };
            
            // 祖父条款逻辑已移除
            
            res.json({
                success: true,
                data: responseData
            });
        } catch (error) {
            console.error('[T056] 获取节点统计失败:', error);
            res.status(500).json({ 
                success: false, 
                error: error.message,
                data: {
                    total: 0,
                    active: 0,
                    disabled: 0,
                    limit: 5,
                    canAddMore: true,
                    canEdit: true,
                    canDelete: true,
                    error: true
                }
            });
        }
    });

    // 祖父条款用户迁移API已移除

    /**
     * GET /api/admin/nodes/details
     * 获取详细的节点列表信息
     */
    app.get('/api/admin/nodes/details', async (req, res) => {
        try {
            const db = req.app.locals.db || req.app.locals.database;
            const nodes = await getDetailedNodeList(db);
            const license = await licenseEnhanced.getCurrentLicenseStatus();
            
            // 统一使用许可证API返回的maxNodes
            let maxNodes = license.maxNodes || 10;
            
            res.json({
                success: true,
                data: {
                    nodes,
                    totalCount: nodes.length,
                    activeCount: nodes.filter(n => !n.disabled).length,
                    license: {
                        planId: license.planType,
                        planName: license.planName,
                        maxNodes: maxNodes
                    }
                }
            });
        } catch (error) {
            console.error('[T056] 获取节点详情失败:', error);
            res.status(500).json({ 
                success: false, 
                error: error.message 
            });
        }
    });
}

/**
 * 获取节点统计信息
 * @returns {Promise<Object>} 节点统计
 */
async function getNodeStats(db) {
    try {
        if (!db || !db.DB) {
            console.error('[T056] 数据库实例无效');
            return { total: 0, active: 0, disabled: 0 };
        }
        
        const result = await db.DB.get(`
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active,
                SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as disabled
            FROM servers
        `);
        
        return {
            total: result.total || 0,
            active: result.active || 0,
            disabled: result.disabled || 0
        };
    } catch (error) {
        console.error('[T056] 获取节点统计失败:', error);
        return { total: 0, active: 0, disabled: 0 };
    }
}

/**
 * 获取详细的节点列表
 * @param {Object} db - 数据库实例
 * @returns {Promise<Array>} 节点详情列表
 */
async function getDetailedNodeList(db) {
    try {
        if (!db || !db.DB) {
            console.error('[T056] 数据库实例无效');
            return [];
        }
        
        const nodes = await db.DB.all(`
            SELECT 
                sid,
                name,
                data,
                status,
                group_id,
                expire_time,
                last_online_time,
                total_online_time
            FROM servers
            ORDER BY top DESC, name ASC
        `);
        
        return nodes.map(node => {
            let parsedData = {};
            try {
                parsedData = JSON.parse(node.data || '{}');
            } catch (e) {
                console.warn(`[T056] 解析节点 ${node.sid} 数据失败:`, e);
            }
            
            return {
                id: node.sid,
            name: node.name,
                ip: parsedData.ip || parsedData.ssh?.host,
                location: parsedData.location,
                disabled: node.status === 0,
                status: node.status === 1 ? 'active' : 'disabled',
                groupId: node.group_id,
                expireTime: node.expire_time,
                lastOnlineTime: node.last_online_time,
                totalOnlineTime: node.total_online_time
            };
        });
    } catch (error) {
        console.error('[T056] 获取节点详情失败:', error);
        return [];
    }
}

/**
 * 获取警告级别
 * @param {number} activeNodes - 当前活跃节点数
 * @param {number} maxNodes - 最大节点数
 * @returns {string} 警告级别
 */
function getWarningLevel(activeNodes, maxNodes) {
    const percentage = (activeNodes / maxNodes) * 100;

    if (percentage >= 100) return 'critical';
    if (percentage >= 80) return 'warning';
    if (percentage >= 60) return 'notice';
    return 'normal';
}

/**
 * 获取下一个推荐套餐
 * @param {string} currentPlanId - 当前套餐ID
 * @returns {Promise<Object>} 下一个套餐信息
 */
async function getNextPlan(currentPlanId) {
    const plans = await licensePlansCache.getPlans();
    
    // 定义升级路径
    const upgradePath = {
        'free': 'standard',
        'standard': 'pro',
        'pro': 'enterprise',
        'enterprise': null
    };
    
    const nextPlanId = upgradePath[currentPlanId];
    if (!nextPlanId) return null;
    
    const nextPlan = plans[nextPlanId];
    if (!nextPlan) return null;
    
    return {
        id: nextPlanId,
        name: nextPlan.displayName,
        maxNodes: nextPlan.maxNodes,
        price: nextPlan.price ? `¥${nextPlan.price}/月` : '联系销售'
    };
}

/**
 * 获取升级收益
 * @param {string} currentPlanId - 当前套餐ID
 * @returns {Promise<Array>} 升级收益列表
 */
async function getUpgradeBenefits(currentPlanId) {
    const plans = await licensePlansCache.getPlans();
    const nextPlan = await getNextPlan(currentPlanId);
    
    if (!nextPlan) return [];
    
    const nextPlanInfo = plans[nextPlan.id];
    if (!nextPlanInfo) return [];
    
    const benefits = [];
    
    // 添加节点数量提升
    benefits.push(`支持最多${nextPlanInfo.maxNodes}个节点`);
    
    // 添加功能特性
    if (nextPlanInfo.features && nextPlanInfo.features.length > 0) {
        benefits.push(...nextPlanInfo.features);
    }
    
    // 根据套餐级别添加额外描述
    if (nextPlan.id === 'enterprise') {
        benefits.push('专属客户经理');
        benefits.push('优先技术支持');
    } else if (nextPlan.id === 'pro') {
        benefits.push('技术支持');
    }
    
    return benefits;
}

module.exports = {
    initNodeStatsApi,
    getNodeStats,
    getDetailedNodeList
}; 
