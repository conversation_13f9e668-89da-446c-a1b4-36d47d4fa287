/**
 * 服务器数据安全处理模块
 * 处理SSH密码的脱敏显示和安全传输
 */

const { isEncrypted } = require('./simple-crypto');

class SecureDataHandler {
    /**
     * 生成密码占位符
     * @param {string} password - 原始密码
     * @returns {string} 占位符
     */
    static generatePasswordPlaceholder(password) {
        if (!password) return '';
        
        // 根据密码长度生成相应长度的占位符
        const length = password.length;
        if (length <= 4) {
            return '••••';
        } else if (length <= 8) {
            return '••••••••';
        } else if (length <= 16) {
            return '••••••••••••••••';
        } else {
            return '••••••••••••••••••••••••';
        }
    }

    /**
     * 脱敏SSH数据（用于前端显示）
     * @param {object} sshData - SSH配置数据
     * @returns {object} 脱敏后的SSH数据
     */
    static sanitizeSSHData(sshData) {
        if (!sshData || typeof sshData !== 'object') {
            return sshData;
        }

        const sanitized = { ...sshData };

        // 脱敏密码字段
        if (sanitized.password) {
            sanitized.password = this.generatePasswordPlaceholder(sanitized.password);
            sanitized._hasPassword = true; // 标记存在密码
        }

        // 脱敏私钥密码字段  
        if (sanitized.passphrase) {
            sanitized.passphrase = this.generatePasswordPlaceholder(sanitized.passphrase);
            sanitized._hasPassphrase = true; // 标记存在私钥密码
        }

        // 保留私钥标记，但不脱敏内容（编辑页面需要显示真实私钥）
        if (sanitized.privateKey && sanitized.privateKey.length > 100) {
            sanitized._hasPrivateKey = true; // 仅标记存在私钥
        }

        return sanitized;
    }

    /**
     * 脱敏服务器数据（用于前端显示）
     * @param {object} serverData - 完整的服务器数据
     * @returns {object} 脱敏后的服务器数据
     */
    static sanitizeServerData(serverData) {
        if (!serverData) return serverData;

        const sanitized = { ...serverData };

        // 脱敏SSH数据
        if (sanitized.data && sanitized.data.ssh) {
            sanitized.data = { ...sanitized.data };
            sanitized.data.ssh = this.sanitizeSSHData(sanitized.data.ssh);
        }

        return sanitized;
    }

    /**
     * 检查密码是否为占位符
     * @param {string} password - 待检查的密码
     * @returns {boolean} 是否为占位符
     */
    static isPasswordPlaceholder(password) {
        if (!password || typeof password !== 'string') {
            return false;
        }
        
        // 检查是否全部为 • 字符
        return /^••+$/.test(password);
    }

    /**
     * 处理前端提交的SSH数据
     * @param {object} frontendSSHData - 前端提交的SSH数据
     * @param {object} originalSSHData - 原始的SSH数据（已解密）
     * @returns {object} 处理后的SSH数据
     */
    static processSubmittedSSHData(frontendSSHData, originalSSHData = {}) {
        if (!frontendSSHData || typeof frontendSSHData !== 'object') {
            return frontendSSHData;
        }

        const processed = { ...frontendSSHData };

        // 处理密码字段
        if (processed.password !== undefined) {
            if (this.isPasswordPlaceholder(processed.password)) {
                // 如果是占位符，保持原始密码
                processed.password = originalSSHData.password || '';
            }
            // 如果不是占位符，说明用户修改了密码，使用新密码
        }

        // 处理私钥密码字段
        if (processed.passphrase !== undefined) {
            if (this.isPasswordPlaceholder(processed.passphrase)) {
                // 如果是占位符，保持原始私钥密码
                processed.passphrase = originalSSHData.passphrase || '';
            }
            // 如果不是占位符，说明用户修改了私钥密码，使用新密码
        }

        // 处理私钥字段
        if (processed.privateKey !== undefined) {
            // 由于私钥不再脱敏，直接使用提交的私钥值
        }

        // 移除标记字段
        delete processed._hasPassword;
        delete processed._hasPassphrase;
        delete processed._hasPrivateKey;

        return processed;
    }

    /**
     * 创建安全的服务器编辑响应数据
     * @param {object} server - 服务器数据
     * @param {object} options - 选项
     * @returns {object} 安全的响应数据
     */
    static createSecureEditResponse(server, options = {}) {
        if (!server) return null;

        const response = {
            ...server,
            data: { ...server.data }
        };

        // 如果需要脱敏SSH数据
        if (options.sanitizeSSH !== false && response.data.ssh) {
            response.data.ssh = this.sanitizeSSHData(response.data.ssh);
        }

        return response;
    }

    /**
     * 验证SSH数据的完整性
     * @param {object} sshData - SSH数据
     * @returns {object} 验证结果
     */
    static validateSSHData(sshData) {
        const result = {
            valid: true,
            errors: [],
            warnings: []
        };

        if (!sshData || typeof sshData !== 'object') {
            result.valid = false;
            result.errors.push('SSH数据格式无效');
            return result;
        }

        // 检查必需字段
        if (!sshData.host) {
            result.errors.push('SSH主机地址不能为空');
            result.valid = false;
        }

        if (!sshData.username) {
            result.errors.push('SSH用户名不能为空');
            result.valid = false;
        }

        // 检查认证方式
        const hasPassword = sshData.password && !this.isPasswordPlaceholder(sshData.password);
        const hasPrivateKey = sshData.privateKey && sshData.privateKey !== '••••••••[已隐藏私钥内容]••••••••';

        if (!hasPassword && !hasPrivateKey) {
            result.warnings.push('未提供SSH密码或私钥，将无法进行SSH连接');
        }

        return result;
    }
}

module.exports = SecureDataHandler;