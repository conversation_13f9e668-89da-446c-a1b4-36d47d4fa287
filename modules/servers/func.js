const ssh=require("../../ssh");
const fs = require('fs');
const path = require('path');

async function initServer(server,neko_status_url){
    // 验证server参数
    if (!server || typeof server !== 'object') {
        return {
            status: 0,
            data: "服务器对象不存在或无效",
            log: "server参数为undefined或不是对象"
        };
    }

    // 如果neko_status_url为空或无效，使用备用链接
    if (!neko_status_url || typeof neko_status_url !== 'string' || neko_status_url.trim() === '') {
        neko_status_url = '"https://github.com/fev125/dstatus/releases/download/v1.1"/neko-status';
    }

    // 从static/sh目录加载安装脚本
    const scriptPath = path.join(__dirname, '../../static/sh/install-agent.sh');
    let scriptContent;
    try {
        scriptContent = fs.readFileSync(scriptPath, 'utf8');
    } catch (err) {
        return { 
            status: 0, 
            data: "无法读取安装脚本", 
            log: err.message 
        };
    }
    
    // 确保 server.data 是对象格式
    let serverData = server.data;
    if (typeof serverData === 'string') {
        try {
            serverData = JSON.parse(serverData);
        } catch (err) {
            return {
                status: 0,
                data: "服务器数据格式错误",
                log: err.message
            };
        }
    }

    // 检查并处理SSH密码占位符问题
    if (serverData && serverData.ssh && serverData.ssh.password) {
        const SecureDataHandler = require('./secure-data-handler');
        if (SecureDataHandler.isPasswordPlaceholder(serverData.ssh.password)) {
            // 如果检测到占位符密码，无法进行SSH连接
            console.log(`[探针安装] 检测到占位符密码，无法建立SSH连接`);
            return {
                status: 0,
                data: "SSH密码配置错误",
                log: "检测到脱敏密码，无法建立SSH连接。请重新编辑服务器并输入正确的SSH密码，或使用SSH私钥认证。"
            };
        }
    }

    // 检查必要的配置数据
    if (!serverData || !serverData.api || !serverData.api.key || !serverData.api.port) {
        return {
            status: 0,
            data: "服务器配置数据不完整",
            log: "缺少API密钥或端口配置"
        };
    }

    // 替换脚本中的变量
    scriptContent = scriptContent
        .replace(/##DOWNLOAD_URL##/g, neko_status_url)
        .replace(/##API_KEY##/g, serverData.api.key)
        .replace(/##API_PORT##/g, serverData.api.port);
    
    // 通过cat/bash的方式执行脚本内容，避免需要上传文件
    const command = `cat << 'EOFNEKOSTATUS' | bash -s\n${scriptContent}\nEOFNEKOSTATUS`;
    
    // 执行SSH命令
    var res = await ssh.Exec(serverData.ssh, command);

    // 处理结果
    if (res.success) {
        // 提取安装日志
        const installLog = res.data.trim();

        // 返回成功状态和安装日志
        return {
            status: 1,
            data: "安装/更新成功",
            log: installLog
        };
    } else {
        // 返回失败状态和错误信息
        return {
            status: 0,
            data: "安装/更新失败/SSH连接失败",
            log: res.data || res.error || "未知错误"
        };
    }
}

async function removeServer(server){
    // 确保 server.data 是对象格式
    let serverData = server.data;
    if (typeof serverData === 'string') {
        try {
            serverData = JSON.parse(serverData);
        } catch (err) {
            return {
                status: 0,
                data: "服务器数据格式错误",
                log: err.message
            };
        }
    }

    // 检查SSH配置
    if (!serverData || !serverData.ssh) {
        return {
            status: 0,
            data: "服务器SSH配置不完整",
            log: "缺少SSH配置信息"
        };
    }

    // 检查并处理SSH密码占位符问题
    if (serverData.ssh.password) {
        const SecureDataHandler = require('./secure-data-handler');
        if (SecureDataHandler.isPasswordPlaceholder(serverData.ssh.password)) {
            // 如果检测到占位符密码，无法进行SSH连接
            console.log(`[探针卸载] 检测到占位符密码，无法建立SSH连接`);
            return {
                status: 0,
                data: "SSH密码配置错误",
                log: "检测到脱敏密码，无法建立SSH连接。请重新编辑服务器并输入正确的SSH密码，或使用SSH私钥认证。"
            };
        }
    }

    // 从static/sh目录加载卸载脚本
    const scriptPath = path.join(__dirname, '../../static/sh/remove-agent.sh');
    let scriptContent;
    try {
        scriptContent = fs.readFileSync(scriptPath, 'utf8');
    } catch (err) {
        return {
            status: 0,
            data: "无法读取卸载脚本",
            log: err.message
        };
    }

    // 通过cat/bash的方式执行脚本内容
    const command = `cat << 'EOFNEKOSTATUS' | bash -s\n${scriptContent}\nEOFNEKOSTATUS`;

    // 执行SSH命令，并返回结果
    var res = await ssh.Exec(serverData.ssh, command);
    // 根据执行结果返回状态
    if (res.success) {
        return { status: 1, data: "删除成功", log: res.data };
    } else {
        return { status: 0, data: "删除失败", log: res.data || res.error };
    }
}

module.exports={
    initServer,removeServer,
}