/**
 * SSH数据简单加密模块
 * 提供SSH密码和私钥密码的基础加密保护
 */

const crypto = require('crypto');

class SimpleCrypto {
    constructor() {
        // 使用实例ID作为加密密钥，如果没有则使用默认值
        this.encryptionKey = this.getEncryptionKey();
        this.algorithm = 'aes-256-cbc';
    }

    /**
     * 获取加密密钥
     */
    getEncryptionKey() {
        // 尝试从不同来源获取实例ID
        const sources = [
            process.env.INSTANCE_ID,
            process.env.NODE_ENV,
            require('os').hostname(),
            'dstatus-default-key'
        ];

        for (const source of sources) {
            if (source && source.length > 0) {
                // 使用SHA-256生成32字节密钥
                return crypto.createHash('sha256').update(source).digest();
            }
        }

        // 兜底默认密钥
        return crypto.createHash('sha256').update('dstatus-ssh-encryption').digest();
    }

    /**
     * 加密文本
     * @param {string} text - 要加密的文本
     * @returns {string} 加密后的文本（格式：iv:encrypted）
     */
    encrypt(text) {
        if (!text || typeof text !== 'string') {
            return text; // 返回原值
        }

        try {
            // 生成随机IV
            const iv = crypto.randomBytes(16);
            const cipher = crypto.createCipheriv(this.algorithm, this.encryptionKey, iv);
            
            let encrypted = cipher.update(text, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            
            // 返回格式：iv:encrypted
            return iv.toString('hex') + ':' + encrypted;
        } catch (error) {
            console.error('[SimpleCrypto] 加密失败:', error.message);
            return text; // 加密失败返回原文
        }
    }

    /**
     * 解密文本
     * @param {string} encryptedText - 加密的文本（格式：iv:encrypted）
     * @param {string} context - 调用上下文（用于调试）
     * @returns {string} 解密后的文本
     */
    decrypt(encryptedText, context = '') {
        if (!encryptedText || typeof encryptedText !== 'string') {
            return encryptedText; // 返回原值
        }

        // 检查是否是加密格式
        if (!encryptedText.includes(':') || encryptedText.length < 32) {
            // console.log('[SimpleCrypto] 密码已经是明文，直接返回');
            return encryptedText; // 可能是明文，直接返回
        }

        try {
            const parts = encryptedText.split(':');
            if (parts.length !== 2) {
                return encryptedText; // 格式不正确，返回原文
            }

            const iv = Buffer.from(parts[0], 'hex');
            const encrypted = parts[1];
            
            const decipher = crypto.createDecipheriv(this.algorithm, this.encryptionKey, iv);
            
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            return decrypted;
        } catch (error) {
            // 只在调试模式下输出错误，避免日志污染
            if (process.env.DEBUG_CRYPTO === 'true') {
                console.error('[SimpleCrypto] 解密失败:', error.message, context ? `上下文: ${context}` : '');
            }
            // 记录解密失败的信息到专门的集合
            if (!this.decryptFailures) {
                this.decryptFailures = new Set();
            }
            if (context) {
                this.decryptFailures.add(context);
            }
            return encryptedText; // 解密失败返回原文
        }
    }

    /**
     * 检查文本是否已加密
     * @param {string} text - 要检查的文本
     * @returns {boolean} 是否已加密
     */
    isEncrypted(text) {
        if (!text || typeof text !== 'string') {
            return false;
        }
        
        // 简单判断：包含冒号且长度足够
        return text.includes(':') && text.length > 32;
    }

    /**
     * 加密SSH数据对象中的敏感字段
     * @param {object} sshData - SSH数据对象
     * @returns {object} 加密后的SSH数据对象
     */
    encryptSSHData(sshData) {
        if (!sshData || typeof sshData !== 'object') {
            return sshData;
        }

        const encrypted = { ...sshData };

        // 加密密码字段
        if (encrypted.password && !this.isEncrypted(encrypted.password)) {
            encrypted.password = this.encrypt(encrypted.password);
        }

        // 加密私钥密码字段
        if (encrypted.passphrase && !this.isEncrypted(encrypted.passphrase)) {
            encrypted.passphrase = this.encrypt(encrypted.passphrase);
        }

        return encrypted;
    }

    /**
     * 解密SSH数据对象中的敏感字段
     * @param {object} sshData - 加密的SSH数据对象
     * @param {string} serverId - 服务器ID（用于调试）
     * @returns {object} 解密后的SSH数据对象
     */
    decryptSSHData(sshData, serverId = '') {
        if (!sshData || typeof sshData !== 'object') {
            return sshData;
        }

        const decrypted = { ...sshData };

        // 解密密码字段
        if (decrypted.password) {
            decrypted.password = this.decrypt(decrypted.password, serverId ? `Server ${serverId} password` : 'password');
        }

        // 解密私钥密码字段
        if (decrypted.passphrase) {
            decrypted.passphrase = this.decrypt(decrypted.passphrase, serverId ? `Server ${serverId} passphrase` : 'passphrase');
        }

        return decrypted;
    }
    
    /**
     * 获取解密失败的服务器列表
     * @returns {Array} 解密失败的上下文列表
     */
    getDecryptFailures() {
        return this.decryptFailures ? Array.from(this.decryptFailures) : [];
    }
}

// 创建单例实例
const simpleCrypto = new SimpleCrypto();

module.exports = {
    encrypt: (text) => simpleCrypto.encrypt(text),
    decrypt: (text, context) => simpleCrypto.decrypt(text, context),
    isEncrypted: (text) => simpleCrypto.isEncrypted(text),
    encryptSSHData: (data) => simpleCrypto.encryptSSHData(data),
    decryptSSHData: (data, serverId) => simpleCrypto.decryptSSHData(data, serverId),
    getDecryptFailures: () => simpleCrypto.getDecryptFailures()
};