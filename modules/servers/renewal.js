"use strict";

const { logUtils, LOG_TYPES } = require('../utils/log-utils');

module.exports = (svr) => {
  const { db, pr } = svr.locals;

  // 仅实现手动续期 API；自动续期暂不启用

  /**
   * 解析续期期限
   * 支持: '1m' | '3m' | '1y'
   */
  function parsePeriod(period) {
    const map = {
      '1m': { months: 1 },
      '3m': { months: 3 },
      '1y': { months: 12 }
    };
    return map[period] || null;
  }

  function daysInMonthUTC(year, month /* 0-11 */) {
    return new Date(Date.UTC(year, month + 1, 0)).getUTCDate();
  }

  /**
   * 基于UTC的安全加月函数，处理月末溢出
   */
  function addMonthsUTC(baseSeconds, monthsToAdd) {
    const baseMs = (baseSeconds || 0) * 1000;
    const d = new Date(baseMs);
    const y = d.getUTCFullYear();
    const m = d.getUTCMonth();
    const day = d.getUTCDate();
    const hours = d.getUTCHours();
    const mins = d.getUTCMinutes();
    const secs = d.getUTCSeconds();

    const targetMonthIndex = m + monthsToAdd;
    const targetYear = y + Math.floor(targetMonthIndex / 12);
    const targetMonth = ((targetMonthIndex % 12) + 12) % 12;
    const dim = daysInMonthUTC(targetYear, targetMonth);
    const safeDay = Math.min(day, dim);

    const nd = new Date(Date.UTC(targetYear, targetMonth, safeDay, hours, mins, secs));
    return Math.floor(nd.getTime() / 1000);
  }

  function formatDateYYYYMMDD(seconds) {
    const d = new Date((seconds || 0) * 1000);
    const y = d.getUTCFullYear();
    const m = String(d.getUTCMonth() + 1).padStart(2, '0');
    const day = String(d.getUTCDate()).padStart(2, '0');
    return `${y}-${m}-${day}`;
  }

  /**
   * 手动续期
   * POST /api/admin/servers/:sid/renew
   * body: { period: '1m'|'3m'|'1y' }
   */
  svr.post('/api/admin/servers/:sid/renew', async (req, res) => {
    try {
      if (!req.admin) {
        return res.status(403).json(pr(0, '无权限执行此操作'));
      }

      const { sid } = req.params;
      const period = (req.body && req.body.period) || '1m';
      const parsed = parsePeriod(period);
      if (!parsed) {
        return res.json(pr(0, '无效的续期期限'));
      }

      const server = await db.servers.get(sid);
      if (!server) {
        return res.json(pr(0, '服务器不存在'));
      }

      const nowSec = Math.floor(Date.now() / 1000);
      const currentExpire = server.expire_time ? parseInt(server.expire_time, 10) : 0;
      const base = Math.max(nowSec, currentExpire);

      const newExpire = addMonthsUTC(base, parsed.months);
      await db.servers.upd_expire_time(sid, newExpire);

      // 刷新内存中的服务器配置缓存（若存在），确保前端无需进入编辑页也能读到最新数据
      try {
        const updatedServer = await db.servers.get(sid);
        if (updatedServer && svr.locals.stats && typeof svr.locals.stats.updateServerConfigCache === 'function') {
          svr.locals.stats.updateServerConfigCache(sid, updatedServer);
        }
      } catch (cacheErr) {
        console.warn('[续期] 更新服务器配置缓存失败:', cacheErr.message);
      }

      // 审计日志
      try {
        logUtils.writeLog(LOG_TYPES.SYSTEM, {
          action: 'manual-renew',
          sid,
          serverName: server.name,
          oldExpire: currentExpire,
          newExpire,
          by: req.ip || 'admin'
        });
      } catch (_) {}

      const data = {
        expire_time: newExpire,
        expire_date: formatDateYYYYMMDD(newExpire)
      };
      return res.json(pr(1, data));
    } catch (error) {
      console.error('[手动续期] 失败:', error);
      return res.json(pr(0, '续期失败: ' + error.message));
    }
  });

  /**
   * 手动设置到期日期
   * POST /api/admin/servers/:sid/set-expire
   * body: { date: 'YYYY-MM-DD' } 或 { expire_time: number(UNIX秒) }
   */
  svr.post('/api/admin/servers/:sid/set-expire', async (req, res) => {
    try {
      if (!req.admin) {
        return res.status(403).json(pr(0, '无权限执行此操作'));
      }
      const { sid } = req.params;
      const { date, expire_time } = req.body || {};

      const server = await db.servers.get(sid);
      if (!server) {
        return res.json(pr(0, '服务器不存在'));
      }

      let newExpire = 0;
      if (typeof expire_time === 'number' && expire_time > 0) {
        newExpire = Math.floor(expire_time);
      } else if (typeof date === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(date)) {
        const [y, m, d] = date.split('-').map(n => parseInt(n, 10));
        // 设为当天 UTC 23:59:59，用户视觉上与日期选择一致
        newExpire = Math.floor(Date.UTC(y, m - 1, d, 23, 59, 59) / 1000);
      } else {
        return res.json(pr(0, '无效的日期参数'));
      }

      await db.servers.upd_expire_time(sid, newExpire);

      // 刷新内存中的服务器配置缓存
      try {
        const updatedServer = await db.servers.get(sid);
        if (updatedServer && svr.locals.stats && typeof svr.locals.stats.updateServerConfigCache === 'function') {
          svr.locals.stats.updateServerConfigCache(sid, updatedServer);
        }
      } catch (cacheErr) {
        console.warn('[手动设置到期] 更新服务器配置缓存失败:', cacheErr.message);
      }

      try {
        logUtils.writeLog(LOG_TYPES.SYSTEM, {
          action: 'manual-set-expire',
          sid,
          serverName: server.name,
          newExpire,
          by: req.ip || 'admin'
        });
      } catch (_) {}

      return res.json(pr(1, {
        expire_time: newExpire,
        expire_date: formatDateYYYYMMDD(newExpire)
      }));
    } catch (error) {
      console.error('[手动设置到期] 失败:', error);
      return res.json(pr(0, '设置失败: ' + error.message));
    }
  });
};
