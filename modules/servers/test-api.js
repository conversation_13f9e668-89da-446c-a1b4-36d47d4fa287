// 测试API端点 - 用于验证服务器数据
module.exports = svr => {
    const { db } = svr.locals;
    
    // 获取服务器详细信息的API
    svr.get("/admin/api/servers/:sid", async (req, res) => {
        try {
            const { sid } = req.params;
            const server = await db.servers.get(sid);
            
            if (!server) {
                return res.status(404).json({
                    status: false,
                    data: '服务器不存在'
                });
            }
            
            res.json({
                status: true,
                data: server
            });
        } catch (error) {
            console.error('获取服务器信息失败:', error);
            res.status(500).json({
                status: false,
                data: '获取服务器信息失败: ' + error.message
            });
        }
    });
};