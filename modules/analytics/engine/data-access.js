// 核心数据访问层 - 负责从现有数据库表中查询数据
"use strict";

// 使用 console 替代 logUtils 避免模块加载问题

class DataAccess {
    constructor(db) {
        if (!db) {
            throw new Error('数据库实例未提供');
        }
        this.db = db;
        this.monitor = db.monitor;
        this.load_m = db.load_m;
        this.load_h = db.load_h;
        this.traffic = db.traffic;
        
        console.log('[Analytics DataAccess] 数据访问层已初始化');
    }
    
    /**
     * 获取TCPing监控数据
     * @param {number} startTime - 开始时间戳(秒)
     * @param {number} endTime - 结束时间戳(秒)
     * @param {string} serverId - 服务器ID (可选)
     * @param {string} granularity - 数据粒度: 'raw', 'minute', '5min', 'hourly', 'daily', 'monthly'
     * @returns {Array} 监控数据数组
     */
    async getTcpingData(startTime, endTime, serverId = null, granularity = 'raw') {
        try {
            let data = [];
            let tableName = '';
            
            // 根据粒度选择合适的表
            switch (granularity) {
                case 'raw':
                case 'minute':
                    tableName = 'tcping_m';
                    break;
                case '5min':
                    tableName = 'tcping_5m';
                    break;
                case 'hourly':
                    tableName = 'tcping_h';
                    break;
                case 'daily':
                    tableName = 'tcping_d';
                    break;
                case 'monthly':
                    tableName = 'tcping_month';
                    break;
                default:
                    throw new Error(`不支持的粒度: ${granularity}`);
            }
            
            // 构建查询
            let sql = `
                SELECT target_id, sid, success_rate, avg_time as value, min_time, max_time, created_at
                FROM ${tableName}
                WHERE created_at >= ? AND created_at <= ?
            `;
            const params = [startTime, endTime];
            
            if (serverId) {
                sql += ' AND sid = ?';
                params.push(serverId);
            }
            
            sql += ' ORDER BY created_at ASC';
            
            // 执行查询
            data = await this.db.DB.all(sql, params);
            
            // 标准化输出格式
            return data.map(row => ({
                timestamp: row.created_at,
                serverId: row.sid,
                targetId: row.target_id,
                value: row.value,
                min: row.min_time,
                max: row.max_time,
                successRate: row.success_rate,
                type: 'tcping'
            }));
            
        } catch (error) {
            console.error('[Analytics DataAccess] 获取TCPing数据失败:', error.message);
            throw error;
        }
    }
    
    /**
     * 获取负载数据
     * @param {number} startTime - 开始时间戳(秒)
     * @param {number} endTime - 结束时间戳(秒)
     * @param {string} serverId - 服务器ID (可选)
     * @param {string} granularity - 数据粒度: 'raw', 'minute', 'hourly'
     * @returns {Array} 负载数据数组
     */
    async getLoadData(startTime, endTime, serverId = null, granularity = 'raw') {
        try {
            let data = [];
            
            // 根据粒度选择合适的表
            let tableName;
            switch (granularity) {
                case 'raw':
                case 'minute':
                    tableName = 'load_archive';
                    break;
                case 'hourly':
                    tableName = 'load_h';
                    break;
                default:
                    throw new Error(`负载数据不支持的粒度: ${granularity}`);
            }
            
            // 构建查询 - 使用正确的字段名
            let sql = `SELECT sid, cpu, mem, swap, ibw, obw, created_at FROM ${tableName} WHERE created_at >= ? AND created_at <= ?`;
            const params = [startTime, endTime];
            
            if (serverId) {
                sql += ' AND sid = ?';
                params.push(serverId);
            }
            
            sql += ' ORDER BY created_at ASC';
            
            // 执行查询
            data = await this.db.DB.all(sql, params);
            
            // 标准化输出格式
            return data.map(row => ({
                timestamp: row.created_at,
                serverId: row.sid,
                load1: null, // load_archive表中没有load字段
                load5: null,
                load15: null,
                cpu: row.cpu,
                mem: row.mem,
                swap: row.swap,
                disk: null, // load_archive表中没有disk字段
                ibw: row.ibw,
                obw: row.obw,
                type: 'load'
            }));
            
        } catch (error) {
            console.error('[Analytics DataAccess] 获取负载数据失败:', error.message);
            throw error;
        }
    }
    
    /**
     * 获取流量数据
     * @param {number} startTime - 开始时间戳(秒)
     * @param {number} endTime - 结束时间戳(秒)
     * @param {string} serverId - 服务器ID (可选)
     * @param {string} granularity - 数据粒度: 'raw', 'hourly', 'daily', 'monthly'
     * @returns {Array} 流量数据数组
     */
    async getTrafficData(startTime, endTime, serverId = null, granularity = 'raw') {
        try {
            let data = [];
            
            // 流量数据目前只有traffic表，包含sid, hs, ds, ms字段
            // 从load_archive表获取带宽数据ibw, obw
            let sql = `
                SELECT sid, ibw, obw, created_at 
                FROM load_archive 
                WHERE created_at >= ? AND created_at <= ?
            `;
            const params = [startTime, endTime];
            
            if (serverId) {
                sql += ' AND sid = ?';
                params.push(serverId);
            }
            
            sql += ' ORDER BY created_at ASC';
            
            // 执行查询
            data = await this.db.DB.all(sql, params);
            
            // 标准化输出格式
            return data.map(row => ({
                timestamp: row.created_at,
                serverId: row.sid,
                inbound: row.ibw || 0,
                outbound: row.obw || 0,
                total: (row.ibw || 0) + (row.obw || 0),
                type: 'traffic'
            }));
            
        } catch (error) {
            console.error('[Analytics DataAccess] 获取流量数据失败:', error.message);
            throw error;
        }
    }
    
    /**
     * 统一的数据获取接口
     * @param {string} dataType - 数据类型: 'tcping', 'load', 'traffic'
     * @param {number} startTime - 开始时间戳(秒)
     * @param {number} endTime - 结束时间戳(秒)
     * @param {Object} options - 可选参数
     * @param {string} options.serverId - 服务器ID
     * @param {string} options.granularity - 数据粒度
     * @returns {Array} 标准化的数据数组
     */
    async getAnalyticsData(dataType, startTime, endTime, options = {}) {
        try {
            // 参数验证
            if (!dataType || !['tcping', 'load', 'traffic'].includes(dataType)) {
                throw new Error(`无效的数据类型: ${dataType}`);
            }
            
            if (!startTime || !endTime || startTime > endTime) {
                throw new Error('无效的时间范围');
            }
            
            const { serverId = null, granularity = 'raw' } = options;
            
            // 根据数据类型调用相应的方法
            switch (dataType) {
                case 'tcping':
                    return await this.getTcpingData(startTime, endTime, serverId, granularity);
                    
                case 'load':
                    return await this.getLoadData(startTime, endTime, serverId, granularity);
                    
                case 'traffic':
                    return await this.getTrafficData(startTime, endTime, serverId, granularity);
                    
                default:
                    throw new Error(`不支持的数据类型: ${dataType}`);
            }
            
        } catch (error) {
            console.error('[Analytics DataAccess] 获取分析数据失败:', error.message);
            throw error;
        }
    }
    
    /**
     * 获取可用的服务器列表
     * @returns {Array} 服务器列表
     */
    async getAvailableServers() {
        try {
            const servers = await this.db.getServers();
            return servers.map(server => ({
                id: server.sid,
                name: server.name,
                status: server.status
            }));
        } catch (error) {
            console.error('[Analytics DataAccess] 获取服务器列表失败:', error.message);
            throw error;
        }
    }
    
    /**
     * 获取服务器详细信息
     * @param {string} serverId - 服务器ID
     * @returns {Object} 服务器信息
     */
    async getServerInfo(serverId) {
        try {
            const servers = await this.db.getServers();
            const server = servers.find(s => s.sid === serverId);
            
            if (!server) {
                throw new Error(`服务器 ${serverId} 未找到`);
            }
            
            // 解析data字段中的JSON数据
            let serverData = {};
            try {
                if (server.data) {
                    // server.data可能已经是对象，也可能是JSON字符串
                    if (typeof server.data === 'string') {
                        serverData = JSON.parse(server.data);
                    } else if (typeof server.data === 'object') {
                        serverData = server.data;
                    }
                }
            } catch (e) {
                console.warn(`[DataAccess] 解析服务器 ${serverId} 的data字段失败:`, e);
            }
            
            return {
                id: server.sid,
                name: server.name,
                status: parseInt(server.status) || 0,
                location: serverData.location?.code || 'Unknown',
                region: serverData.location?.code || 'Unknown',
                type: serverData.device || 'Unknown',
                // 不返回敏感信息，只返回必要的非敏感数据
                // 移除了 host 和 port 信息，避免泄露服务器地址
            };
        } catch (error) {
            console.error('[Analytics DataAccess] 获取服务器信息失败:', error.message);
            throw error;
        }
    }
}

module.exports = DataAccess;