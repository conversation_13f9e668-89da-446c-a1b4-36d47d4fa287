// Core analytics processing engine logic
"use strict";

const DataAccess = require('./data-access');
const LocalAnalyzer = require('./local-analyzer');

let dataAccessInstance = null;
let localAnalyzerInstance = null;

/**
 * 初始化数据访问层
 * @param {Object} db - 数据库实例
 * @returns {DataAccess} 数据访问实例
 */
function initDataAccess(db) {
    if (!dataAccessInstance) {
        dataAccessInstance = new DataAccess(db);
        // 同时初始化本地分析器
        localAnalyzerInstance = new LocalAnalyzer(dataAccessInstance);
    }
    return dataAccessInstance;
}

/**
 * 获取数据访问实例
 * @returns {DataAccess} 数据访问实例
 */
function getDataAccess() {
    if (!dataAccessInstance) {
        throw new Error('数据访问层尚未初始化，请先调用initDataAccess');
    }
    return dataAccessInstance;
}

/**
 * 获取本地分析器实例
 * @returns {LocalAnalyzer} 本地分析器实例
 */
function getLocalAnalyzer() {
    if (!localAnalyzerInstance) {
        throw new Error('本地分析器尚未初始化，请先调用initDataAccess');
    }
    return localAnalyzerInstance;
}

module.exports = {
    initDataAccess,
    getDataAccess,
    getLocalAnalyzer,
    DataAccess,
    LocalAnalyzer
};