// 本地数据分析器 - 处理固定的数据分析任务
"use strict";

class LocalAnalyzer {
    constructor(dataAccess) {
        this.dataAccess = dataAccess;
        console.log('[LocalAnalyzer] 本地分析器已初始化');
    }

    /**
     * 分析服务器地理分布
     * @param {Array} servers - 服务器列表
     * @returns {Object} 地理分布数据
     */
    analyzeGeoDistribution(servers) {
        const geoDistribution = {};
        
        servers.forEach(server => {
            // 从服务器数据中提取地理位置
            let country = 'Unknown';
            
            if (server.data) {
                // 处理可能是字符串或对象的data字段
                let serverData = server.data;
                if (typeof serverData === 'string') {
                    try {
                        serverData = JSON.parse(serverData);
                    } catch (e) {
                        // 解析失败，使用默认值
                    }
                }
                
                // 提取国家信息 - 优先使用country，其次使用location.code
                if (serverData.country) {
                    country = serverData.country;
                } else if (serverData.location && serverData.location.code) {
                    country = serverData.location.code;
                }
            }
            
            // 统计国家分布
            geoDistribution[country] = (geoDistribution[country] || 0) + 1;
        });
        
        // 移除Unknown如果没有节点
        if (geoDistribution['Unknown'] === 0) {
            delete geoDistribution['Unknown'];
        }
        
        return geoDistribution;
    }

    /**
     * 分析流量占比分布
     * @param {Array} servers - 服务器列表
     * @param {Array} trafficData - 流量数据
     * @returns {Array} 流量分布数据
     */
    analyzeTrafficDistribution(servers, trafficData) {
        const trafficMap = new Map();
        
        // 按服务器ID聚合流量数据
        trafficData.forEach(data => {
            const current = trafficMap.get(data.serverId) || 0;
            trafficMap.set(data.serverId, current + data.total);
        });
        
        // 构建流量分布数组
        const trafficDistribution = [];
        servers.forEach(server => {
            // 兼容不同的ID字段名
            const serverId = server.sid || server.id;
            const totalTraffic = trafficMap.get(serverId) || 0;
            if (totalTraffic > 0) {
                trafficDistribution.push({
                    name: server.name || serverId,
                    value: totalTraffic
                });
            }
        });
        
        // 按流量降序排序
        trafficDistribution.sort((a, b) => b.value - a.value);
        
        return trafficDistribution;
    }

    /**
     * 分析性能占比（CPU、内存使用）
     * @param {Array} servers - 服务器列表
     * @param {Array} loadData - 负载数据
     * @returns {Object} 性能分析结果
     */
    analyzePerformanceDistribution(servers, loadData) {
        const performanceMap = new Map();
        
        // 按服务器ID计算平均性能指标
        const serverDataCount = new Map();
        
        loadData.forEach(data => {
            if (!performanceMap.has(data.serverId)) {
                performanceMap.set(data.serverId, {
                    totalCpu: 0,
                    totalMem: 0,
                    totalDisk: 0,
                    totalLoad: 0
                });
                serverDataCount.set(data.serverId, 0);
            }
            
            const perf = performanceMap.get(data.serverId);
            perf.totalCpu += data.cpu || 0;
            perf.totalMem += data.mem || 0;
            perf.totalDisk += data.disk || 0;
            perf.totalLoad += ((data.cpu || 0) + (data.mem || 0)) / 2;
            
            serverDataCount.set(data.serverId, serverDataCount.get(data.serverId) + 1);
        });
        
        // 计算平均值并构建分布数据
        const cpuDistribution = [];
        const memDistribution = [];
        const loadDistribution = [];
        
        servers.forEach(server => {
            // 兼容不同的ID字段名
            const serverId = server.sid || server.id;
            const perf = performanceMap.get(serverId);
            const count = serverDataCount.get(serverId) || 1;
            
            if (perf) {
                const serverName = server.name || serverId;
                
                cpuDistribution.push({
                    name: serverName,
                    value: Math.round(perf.totalCpu / count)
                });
                
                memDistribution.push({
                    name: serverName,
                    value: Math.round(perf.totalMem / count)
                });
                
                loadDistribution.push({
                    name: serverName,
                    value: Math.round(perf.totalLoad / count)
                });
            }
        });
        
        // 排序
        cpuDistribution.sort((a, b) => b.value - a.value);
        memDistribution.sort((a, b) => b.value - a.value);
        loadDistribution.sort((a, b) => b.value - a.value);
        
        return {
            cpuDistribution,
            memDistribution,
            loadDistribution
        };
    }

    /**
     * 计算服务器健康评分
     * @param {Object} server - 服务器信息
     * @param {Array} tcpingData - TCPing数据
     * @param {Array} loadData - 负载数据
     * @returns {number} 健康评分 (0-100)
     */
    calculateHealthScore(server, tcpingData, loadData) {
        let score = 100;
        
        // 基于TCPing成功率扣分
        if (tcpingData.length > 0) {
            const avgSuccessRate = tcpingData.reduce((sum, d) => sum + (d.successRate || 0), 0) / tcpingData.length;
            if (avgSuccessRate < 0.95) score -= 20;
            else if (avgSuccessRate < 0.99) score -= 10;
        }
        
        // 基于负载扣分
        if (loadData.length > 0) {
            const avgCpu = loadData.reduce((sum, d) => sum + (d.cpu || 0), 0) / loadData.length;
            const avgMem = loadData.reduce((sum, d) => sum + (d.mem || 0), 0) / loadData.length;
            
            if (avgCpu > 90) score -= 30;
            else if (avgCpu > 80) score -= 20;
            else if (avgCpu > 70) score -= 10;
            
            if (avgMem > 90) score -= 30;
            else if (avgMem > 80) score -= 20;
            else if (avgMem > 70) score -= 10;
        }
        
        // 基于在线状态 - 注意status字段可能是字符串
        const status = parseInt(server.status) || 0;
        if (status !== 1 && server.status !== 'online' && server.status !== 1) {
            score = Math.min(score, 20); // 离线服务器最高20分
        }
        
        return Math.max(0, score);
    }

    /**
     * 执行完整的本地分析
     * @param {number} startTime - 开始时间戳
     * @param {number} endTime - 结束时间戳
     * @returns {Object} 分析结果
     */
    async performLocalAnalysis(startTime, endTime) {
        try {
            console.log('[LocalAnalyzer] 开始本地数据分析...');
            
            // 获取服务器列表
            const servers = await this.dataAccess.getAvailableServers();
            
            // 获取各类监控数据
            const [tcpingData, loadData, trafficData] = await Promise.all([
                this.dataAccess.getTcpingData(startTime, endTime),
                this.dataAccess.getLoadData(startTime, endTime),
                this.dataAccess.getTrafficData(startTime, endTime)
            ]);
            
            console.log('[LocalAnalyzer] 数据获取完成:', {
                servers: servers.length,
                tcping: tcpingData.length,
                load: loadData.length,
                traffic: trafficData.length
            });
            
            // 获取详细的服务器信息
            const serversWithDetails = [];
            for (const server of servers) {
                try {
                    const serverInfo = await this.dataAccess.getServerInfo(server.id);
                    serversWithDetails.push({
                        id: serverInfo.id,
                        sid: serverInfo.id, // 添加sid别名
                        name: serverInfo.name,
                        status: serverInfo.status,
                        data: {
                            location: { code: serverInfo.location }
                        }
                    });
                } catch (e) {
                    // 如果获取详细信息失败，使用基本信息
                    serversWithDetails.push({
                        id: server.id,
                        sid: server.id,
                        name: server.name,
                        status: server.status || 0,
                        data: { location: { code: 'Unknown' } }
                    });
                }
            }
            
            // 执行各项分析
            const geoDistribution = this.analyzeGeoDistribution(serversWithDetails);
            const trafficDistribution = this.analyzeTrafficDistribution(serversWithDetails, trafficData);
            const performanceDistribution = this.analyzePerformanceDistribution(serversWithDetails, loadData);
            
            // 计算总体统计
            let healthyServers = 0;
            let warningServers = 0;
            let criticalServers = 0;
            
            serversWithDetails.forEach(server => {
                const serverTcping = tcpingData.filter(d => d.serverId === server.id);
                const serverLoad = loadData.filter(d => d.serverId === server.id);
                const healthScore = this.calculateHealthScore(server, serverTcping, serverLoad);
                
                if (healthScore >= 80) healthyServers++;
                else if (healthScore >= 60) warningServers++;
                else criticalServers++;
            });
            
            // 计算流量统计
            const totalTrafficBytes = trafficData.reduce((sum, d) => sum + d.total, 0);
            const avgTrafficBytesPerSec = trafficData.length > 0 ? totalTrafficBytes / trafficData.length : 0;
            const totalTrafficUsed = this.formatBytes(totalTrafficBytes * (endTime - startTime));
            const averageUsage = Math.round((avgTrafficBytesPerSec / (1024 * 1024)) * 100) + '%'; // 假设1MB/s为100%
            
            // 计算网络质量
            const avgLatency = tcpingData.length > 0 
                ? Math.round(tcpingData.reduce((sum, d) => sum + (d.value || 0), 0) / tcpingData.length)
                : 0;
            const qualityScore = this.calculateNetworkQualityScore(tcpingData);
            
            // 计算负载平均值
            const avgCpu = loadData.length > 0
                ? Math.round(loadData.reduce((sum, d) => sum + (d.cpu || 0), 0) / loadData.length)
                : 0;
            const avgMem = loadData.length > 0
                ? Math.round(loadData.reduce((sum, d) => sum + (d.mem || 0), 0) / loadData.length)
                : 0;
            
            return {
                // 本地计算的数据
                localAnalysis: true,
                summary: {
                    totalServers: servers.length,
                    healthyServers,
                    warningServers,
                    criticalServers,
                    overallScore: Math.round((healthyServers / serversWithDetails.length) * 100)
                },
                geoDistribution,
                serverTrafficDistribution: trafficDistribution,
                performanceDistribution,
                trafficAnalysis: {
                    totalTrafficUsed,
                    averageUsage,
                    warnings: [] // 可以基于阈值添加警告
                },
                networkQuality: {
                    averageLatency: avgLatency,
                    qualityScore: qualityScore
                },
                loadAnalysis: {
                    averageCpuUsage: avgCpu,
                    averageMemoryUsage: avgMem
                },
                // 原始数据供AI进一步分析
                rawData: {
                    servers: serversWithDetails,
                    tcpingData,
                    loadData,
                    trafficData
                }
            };
            
        } catch (error) {
            console.error('[LocalAnalyzer] 本地分析失败:', error);
            throw error;
        }
    }

    /**
     * 计算网络质量评分
     */
    calculateNetworkQualityScore(tcpingData) {
        if (tcpingData.length === 0) return 0;
        
        let score = 100;
        const avgLatency = tcpingData.reduce((sum, d) => sum + (d.value || 0), 0) / tcpingData.length;
        const avgSuccessRate = tcpingData.reduce((sum, d) => sum + (d.successRate || 0), 0) / tcpingData.length;
        
        // 基于延迟扣分
        if (avgLatency > 200) score -= 30;
        else if (avgLatency > 100) score -= 20;
        else if (avgLatency > 50) score -= 10;
        
        // 基于成功率扣分
        if (avgSuccessRate < 0.9) score -= 30;
        else if (avgSuccessRate < 0.95) score -= 20;
        else if (avgSuccessRate < 0.99) score -= 10;
        
        return Math.max(0, score);
    }

    /**
     * 格式化字节数
     */
    formatBytes(bytes) {
        if (bytes < 1024) return bytes + ' B';
        if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
        if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
        if (bytes < 1024 * 1024 * 1024 * 1024) return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
        return (bytes / (1024 * 1024 * 1024 * 1024)).toFixed(2) + ' TB';
    }
}

module.exports = LocalAnalyzer;