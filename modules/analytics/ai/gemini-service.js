// Gemini AI Service for Analytics Data Processing
"use strict";

const { GoogleGenerativeAI } = require('@google/generative-ai');
const aiConfig = require('../../utils/ai-config');

class GeminiAnalyticsService {
    constructor(db) {
        this.db = db;  // 保存数据库实例
        this.apiKey = null;
        this.genAI = null;
        this.model = null;
        this.initialized = false;
        this.config = null;
    }

    /**
     * 初始化Gemini服务
     */
    async init() {
        try {
            // 使用新的统一配置系统
            this.config = await aiConfig.getAiConfig(this.db);
            
            // 获取 API 密钥
            this.apiKey = this.config.apiKey;
            if (!this.apiKey) {
                // 兼容旧的环境变量（将被弃用）
                this.apiKey = process.env.GEMINI_API_KEY;
                if (this.apiKey) {
                    console.warn('[Gemini Analytics] 使用旧的 GEMINI_API_KEY 环境变量，建议使用 AI 设置配置');
                }
            }
            
            // 如果有API密钥且功能启用，初始化AI客户端
            if (this.apiKey && this.config.enabled) {
                // 根据配置选择合适的模型
                const modelName = this.config.model || 'gemini-1.5-flash';
                
                this.genAI = new GoogleGenerativeAI(this.apiKey);
                this.model = this.genAI.getGenerativeModel({ model: modelName });
                console.log('[Gemini Analytics] Service 初始化成功，使用模型:', modelName);
            } else {
                console.log('[Gemini Analytics] Service 已加载但未配置（需要API密钥并启用）');
            }
            
            // 标记为已初始化（即使未配置）
            this.initialized = true;
        } catch (error) {
            console.error('[Gemini Analytics] Service 初始化异常:', error.message);
            // 即使出错也标记为已初始化，避免重复初始化
            this.initialized = true;
        }
    }

    /**
     * 检查服务是否已配置并可用
     */
    checkInitialized() {
        if (!this.initialized) {
            throw new Error('Gemini Analytics Service 未初始化');
        }
        if (!this.apiKey) {
            throw new Error('AI 服务未配置 API 密钥，请在 AI 设置中配置');
        }
        if (!this.config?.enabled) {
            throw new Error('AI 功能未启用，请在 AI 设置中开启');
        }
        if (!this.model) {
            throw new Error('AI 模型未初始化，请检查配置');
        }
    }

    /**
     * 分析服务器监控数据并生成报告
     * @param {Object} monitoringData - 监控数据
     * @returns {Object} 分析报告JSON
     */
    async generateAnalyticsReport(monitoringData) {
        this.checkInitialized();

        const prompt = this._buildAnalyticsPrompt(monitoringData);
        
        try {
            console.log('[Gemini Analytics] 开始分析监控数据...');
            
            const result = await this.model.generateContent(prompt);
            const response = await result.response;
            const text = response.text();
            
            // 解析JSON响应
            const analysisReport = this._parseAnalysisResponse(text);
            
            console.log('[Gemini Analytics] 数据分析完成');
            return analysisReport;
            
        } catch (error) {
            console.error('[Gemini Analytics] 分析失败:', error.message);
            
            // 检查是否是地区限制错误
            if (error.message && error.message.includes('User location is not supported')) {
                throw new Error('AI服务地区限制：您所在的地区暂不支持 Google Gemini API。建议：1) 切换到其他AI服务提供商（如OpenAI）；2) 使用VPN或代理服务；3) 联系管理员配置可用的API服务');
            }
            
            throw new Error('AI分析服务暂时不可用: ' + error.message);
        }
    }

    /**
     * 创建数据摘要，减少发送给AI的数据量
     * @param {Object} data - 原始监控数据
     * @returns {Object} 数据摘要
     */
    _createDataSummary(data) {
        const summary = {
            metadata: data.metadata,
            serverCount: data.servers ? data.servers.length : 0,
            servers: []
        };
        
        // 只包含服务器基本信息和指标统计
        if (data.servers && Array.isArray(data.servers)) {
            summary.servers = data.servers.slice(0, 10).map(server => ({
                id: server.id,
                name: server.name,
                info: server.info ? {
                    status: server.info.status,
                    location: server.info.location || 'Unknown'
                } : null,
                metricsCount: {
                    tcping: server.metrics?.tcping?.count || 0,
                    load: server.metrics?.load?.count || 0,
                    traffic: server.metrics?.traffic?.count || 0
                },
                // 只包含最近的几个数据点作为样本
                sampleMetrics: {
                    tcping: Array.isArray(server.metrics?.tcping) ? 
                        server.metrics.tcping.slice(-3).map(d => d.value) : 
                        (server.metrics?.tcping?.count || 0),
                    load: Array.isArray(server.metrics?.load) ? 
                        server.metrics.load.slice(-3).map(d => ({ cpu: d.cpu, mem: d.mem })) : 
                        (server.metrics?.load?.count || 0),
                    traffic: Array.isArray(server.metrics?.traffic) ? 
                        server.metrics.traffic.slice(-3).map(d => d.total) : 
                        (server.metrics?.traffic?.count || 0)
                }
            }));
            
            // 如果服务器数量超过10个，添加摘要信息
            if (data.servers.length > 10) {
                summary.additionalServers = data.servers.length - 10;
                summary.note = `只显示前10个服务器的详细信息，共有${data.servers.length}个服务器`;
            }
        }
        
        // 如果有本地分析结果，直接使用
        if (data.localAnalysis) {
            summary.localAnalysis = data.localAnalysis;
        }
        
        return summary;
    }
    
    /**
     * 构建分析提示词
     * @param {Object} data - 监控数据
     * @returns {string} 提示词
     */
    _buildAnalyticsPrompt(data) {
        // 检查是否有本地分析数据
        const hasLocalAnalysis = data.localAnalysis && data.localAnalysis.geoDistribution;
        
        // 创建数据摘要，减少数据量
        const dataSummary = this._createDataSummary(data);
        
        return `
你是一位专业的服务器运维专家，请分析以下监控数据并生成运维报告。

**重要：请使用中文回复所有内容。**

# 监控数据说明
以下数据包含多台服务器的监控指标：
- **TCPing数据**: 网络延迟测试结果，value字段表示延迟毫秒数，0表示测试失败/超时
- **Load数据**: 系统负载，cpu为CPU使用率(%)，mem为内存使用率(%)，swap为交换空间使用率(%)
- **Traffic数据**: 流量监控，inbound/outbound为入站/出站带宽(字节/秒)，total为总带宽
- 每个服务器都有唯一的serverId和serverName，请在分析中明确引用
${hasLocalAnalysis ? '\n# 本地分析结果\n系统已经完成了基础数据分析，包括地理分布、流量分布、性能分布等。请使用这些已分析的数据，不要重新计算。重点关注深度分析和智能建议。' : ''}

# 监控数据摘要
${JSON.stringify(dataSummary, null, 2)}

# 分析要求
请使用中文回答，基于提供的数据分析以下几个方面：
在所有涉及具体服务器的问题和建议中，请务必明确指出服务器的名称（serverName）和ID（serverId）。这对于报告的可操作性至关重要。

${hasLocalAnalysis ? `
**重要说明**：数据中的localAnalysis字段包含了系统已经计算好的基础分析结果：
- geoDistribution: 地理分布数据（直接使用，不要重新计算）
- serverTrafficDistribution: 流量分布数据（直接使用，不要重新计算）
- performanceDistribution: 性能分布数据（直接使用）
- summary: 基础统计数据（直接使用）
- trafficAnalysis: 流量分析结果（直接使用）
- networkQuality: 网络质量数据（直接使用）
- loadAnalysis: 负载分析数据（直接使用）

请基于这些已分析的数据，重点提供：
1. **深度问题分析** - 基于已有数据，识别潜在问题和异常模式
2. **趋势预测** - 分析当前趋势，预测可能的问题
3. **优化建议** - 提供具体、可操作的优化方案
4. **服务器价值评估** - 基于性能数据评估服务器的使用效率
` : `
1. **整体健康状况** - 评估所有服务器的健康度，特别注意异常服务器。
2. **流量分析** - 基于traffic数据分析带宽使用情况。traffic数据中的inbound/outbound为字节/秒，total为总带宽。计算方法：
   - 总流量使用量 = 将带宽数据乘以时间间隔累加，转换为GB/TB单位。例如：8MB/s * 300秒 = 2.4GB
   - 平均使用率 = (平均inbound + 平均outbound) / 1MB/s * 100%，如10%表示平均1MB/s的使用
   - **强制要求**：必须基于现有数据进行计算，不允许返回"需要更多数据"，至少给出估算值
3. **硬盘容量** - 检查磁盘使用率和预警，指出磁盘空间不足的服务器。
4. **网络质量** - 基于TCPing数据评估网络状况，指出网络延迟高或丢包的服务器。
5. **负载分析** - CPU和内存使用情况，识别高负载或内存耗尽的服务器。
6. **服务器价值评估** - 识别高利用率（高效利用资源）和低利用率（可能资源浪费）服务器，并说明具体服务器名称和原因。
7. **流量占比分布** - 分析各服务器的流量使用占比，识别流量消耗最大的服务器，返回serverTrafficDistribution数组。
8. **地理分布统计** - 统计服务器的地理分布情况，按国家/地区分组统计节点数量，返回geoDistribution对象。
`}
9. **运维建议** - 提供具体、可操作的优化建议，并明确指出针对的服务器名称和ID。

# 输出格式
请使用中文填写所有字段内容，严格按照以下JSON格式返回分析结果，不要包含任何其他文字：
${hasLocalAnalysis ? '**注意**：如果localAnalysis中已经包含了某个字段的数据，请直接使用该数据，不要重新计算。' : ''}

{
  "summary": {
    "totalServers": ${hasLocalAnalysis ? '使用localAnalysis.summary.totalServers' : '数字'},
    "healthyServers": ${hasLocalAnalysis ? '使用localAnalysis.summary.healthyServers' : '数字'},
    "warningServers": ${hasLocalAnalysis ? '使用localAnalysis.summary.warningServers' : '数字'},
    "criticalServers": ${hasLocalAnalysis ? '使用localAnalysis.summary.criticalServers' : '数字'},
    "overallScore": ${hasLocalAnalysis ? '使用localAnalysis.summary.overallScore' : '数字（0-100）'},
    "lastAnalysisTime": "ISO时间字符串"
  },
  "trafficAnalysis": {
    "totalTrafficUsed": "基于traffic数据计算的总流量使用量，格式如：1.2TB，必须提供具体数值，不允许写'需要更多数据'",
    "averageUsage": "平均带宽使用率，格式如：85%，基于(inbound+outbound)/1MB/s计算，必须提供具体百分比",
    "warnings": [
      {
        "serverId": "服务器ID",
        "serverName": "服务器名称",
        "currentUsage": "使用率（如：95%）",
        "remainingTraffic": "剩余流量",
        "estimatedDaysLeft": 数字,
        "severity": "low|medium|high|critical"
      }
    ]
  },
  "diskWarnings": [
    {
      "serverId": "服务器ID",
      "serverName": "服务器名称",
      "currentUsage": "使用率（如：88%）",
      "remainingSpace": "剩余空间",
      "estimatedDaysToFull": 数字,
      "severity": "low|medium|high|critical"
    }
  ],
  "networkQuality": {
    "averageLatency": 数字,
    "qualityScore": 数字（0-100）,
    "issueServers": [
      {
        "serverId": "服务器ID",
        "serverName": "服务器名称",
        "avgLatency": 数字,
        "packetLoss": "百分比",
        "issue": "具体网络问题描述，使用中文 (请包含服务器名称和ID)"
      }
    ]
  },
  "loadAnalysis": {
    "averageCpuUsage": 数字,
    "averageMemoryUsage": 数字,
    "highLoadServers": [
      {
        "serverId": "服务器ID",
        "serverName": "服务器名称",
        "cpuUsage": 数字,
        "memoryUsage": 数字,
        "loadScore": 数字（0-100）
      }
    ]
  },
  "serverUtilization": {
    "highValueServers": [
      {
        "serverId": "服务器ID",
        "serverName": "服务器名称",
        "utilizationScore": 数字（0-100）,
        "reason": "高价值原因，使用中文描述 (请包含服务器名称和ID)",
        "recommendation": "建议内容，使用中文描述 (请包含服务器名称和ID)"
      }
    ],
    "underutilizedServers": [
      {
        "serverId": "服务器ID",
        "serverName": "服务器名称",
        "utilizationScore": 数字（0-100）,
        "reason": "低利用率原因，使用中文描述 (请包含服务器名称和ID)",
        "recommendation": "优化建议，使用中文描述 (请包含服务器名称和ID)"
      }
    ]
  },
  "serverTrafficDistribution": ${hasLocalAnalysis ? '使用localAnalysis.serverTrafficDistribution的数据' : `[
    {
      "name": "服务器名称",
      "value": 流量使用量（字节）
    }
  ]`},
  "geoDistribution": ${hasLocalAnalysis ? '使用localAnalysis.geoDistribution的数据' : `{
    "国家名称": 节点数量
  }`},
  "recommendations": [
    {
      "type": "optimization|warning|maintenance",
      "priority": "low|medium|high|critical",
      "title": "建议标题，使用中文 (请包含具体服务器名称或ID，如适用)",
      "description": "详细描述，使用中文 (请包含具体服务器名称或ID，如适用)",
      "action": "具体操作步骤，使用中文",
      "estimatedImpact": "预期效果，使用中文"
    }
  ]
}
`;
    }

    /**
     * 解析AI响应
     * @param {string} responseText - AI响应文本
     * @returns {Object} 解析后的JSON对象
     */
    _parseAnalysisResponse(responseText) {
        try {
            // 清理响应文本，移除可能的markdown格式
            let cleanText = responseText.trim();
            
            // 移除可能的```json标记
            if (cleanText.startsWith('```json')) {
                cleanText = cleanText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
            } else if (cleanText.startsWith('```')) {
                cleanText = cleanText.replace(/^```\s*/, '').replace(/\s*```$/, '');
            }
            
            // 查找JSON结束位置，移除JSON后面的额外文字
            const jsonStart = cleanText.indexOf('{');
            let jsonEnd = -1;
            let braceCount = 0;
            
            for (let i = jsonStart; i < cleanText.length; i++) {
                if (cleanText[i] === '{') braceCount++;
                if (cleanText[i] === '}') {
                    braceCount--;
                    if (braceCount === 0) {
                        jsonEnd = i;
                        break;
                    }
                }
            }
            
            if (jsonStart >= 0 && jsonEnd >= 0) {
                cleanText = cleanText.substring(jsonStart, jsonEnd + 1);
            }
            
            // 解析JSON
            const parsed = JSON.parse(cleanText);
            
            // 验证必要字段
            if (!parsed.summary || !parsed.recommendations) {
                throw new Error('AI响应格式不完整');
            }
            
            // 添加时间戳
            parsed.summary.lastAnalysisTime = new Date().toISOString();
            
            return parsed;
            
        } catch (error) {
            console.error('[Gemini Analytics] AI响应解析失败:', error.message);
            console.error('[Gemini Analytics] 原始响应:', responseText);
            
            // 返回默认报告结构
            return this._getDefaultReport();
        }
    }

    /**
     * 获取默认报告结构（当AI解析失败时使用）
     * @returns {Object} 默认报告
     */
    _getDefaultReport() {
        return {
            summary: {
                totalServers: 0,
                healthyServers: 0,
                warningServers: 0,
                criticalServers: 0,
                overallScore: 0,
                lastAnalysisTime: new Date().toISOString()
            },
            trafficAnalysis: {
                totalTrafficUsed: "数据不可用",
                averageUsage: "0%",
                warnings: []
            },
            diskWarnings: [],
            networkQuality: {
                averageLatency: 0,
                qualityScore: 0,
                issueServers: []
            },
            loadAnalysis: {
                averageCpuUsage: 0,
                averageMemoryUsage: 0,
                highLoadServers: []
            },
            serverUtilization: {
                highValueServers: [],
                underutilizedServers: []
            },
            recommendations: [
                {
                    type: "warning",
                    priority: "medium",
                    title: "AI分析服务异常",
                    description: "AI分析服务暂时不可用，请稍后重试",
                    action: "检查网络连接和AI服务状态",
                    estimatedImpact: "暂时无法获取智能分析结果"
                }
            ]
        };
    }

    /**
     * 测试Gemini连接
     * @returns {Object} 测试结果
     */
    async testConnection() {
        try {
            // 确保服务已初始化
            if (!this.initialized) {
                await this.init();
            }

            // 重新加载配置以获取最新状态
            this.config = await aiConfig.getAiConfig(this.db);
            
            // 检查基本配置
            const hasApiKey = !!this.apiKey || !!this.config.apiKey;
            const isEnabled = this.config.enabled;
            const hasModel = !!this.model;
            
            let message = 'AI服务状态: ';
            if (!hasApiKey) {
                message = 'AI服务未配置（需要设置API密钥）';
            } else if (!isEnabled) {
                message = 'AI服务已配置但未启用';
            } else if (!hasModel) {
                message = 'AI服务配置中（模型未初始化）';
            } else {
                message = 'AI服务配置正常';
            }

            return {
                success: hasApiKey && isEnabled && hasModel,
                message: message,
                initialized: this.initialized,
                hasApiKey: hasApiKey,
                isEnabled: isEnabled,
                hasModel: hasModel,
                modelName: this.config.model || 'gemini-1.5-flash',
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            return {
                success: false,
                message: '服务检查失败',
                error: error.message
            };
        }
    }
}

module.exports = GeminiAnalyticsService;