// AI module for analytics
"use strict";

const GeminiAnalyticsService = require('./gemini-service');

let geminiService = null;
let database = null;

/**
 * 初始化AI分析服务
 * @param {Object} db - 数据库实例（必需）
 */
async function init(db) {
    try {
        // 保存数据库实例
        if (db) {
            database = db;
        } else if (!database) {
            throw new Error('AI模块初始化需要数据库实例');
        }
        
        // 如果服务已经初始化且配置相同，跳过重复初始化
        if (geminiService && geminiService.initialized) {
            console.log('[Analytics AI] 模块已经初始化，跳过重复初始化');
            return;
        }
        
        geminiService = new GeminiAnalyticsService(database);
        await geminiService.init();
        console.log('[Analytics AI] 模块初始化完成');
    } catch (error) {
        console.error('[Analytics AI] 模块初始化失败:', error.message);
        throw error;
    }
}

/**
 * 获取Gemini服务实例
 * @returns {GeminiAnalyticsService|null}
 */
function getGeminiService() {
    return geminiService;
}

/**
 * 测试AI服务连接
 * @param {Object} db - 数据库实例（可选，如果服务未初始化则必需）
 * @returns {Object} 测试结果
 */
async function testConnection(db) {
    try {
        if (!geminiService) {
            if (!db && !database) {
                throw new Error('测试连接需要数据库实例');
            }
            await init(db || database);
        }
        return await geminiService.testConnection();
    } catch (error) {
        return {
            success: false,
            message: '测试失败',
            error: error.message
        };
    }
}

/**
 * 生成分析报告
 * @param {Object} monitoringData - 监控数据
 * @param {Object} db - 数据库实例（可选，如果服务未初始化则必需）
 * @returns {Object} 分析报告
 */
async function generateReport(monitoringData, db) {
    try {
        if (!geminiService) {
            if (!db && !database) {
                throw new Error('生成报告需要数据库实例');
            }
            await init(db || database);
        }
        
        console.log('[Analytics AI] 开始生成分析报告...');
        const report = await geminiService.generateAnalyticsReport(monitoringData);
        console.log('[Analytics AI] 分析报告生成完成');
        
        return report;
    } catch (error) {
        console.error('[Analytics AI] 报告生成失败:', error.message);
        throw error;
    }
}

console.log('[Analytics AI] 模块已加载');

module.exports = {
    init,
    getGeminiService,
    testConnection,
    generateReport
};