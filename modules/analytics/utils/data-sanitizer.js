// 数据安全过滤器 - 移除敏感信息
"use strict";

/**
 * 过滤监控数据中的敏感信息
 * @param {Object} data - 原始监控数据
 * @returns {Object} 过滤后的安全数据
 */
function sanitizeMonitoringData(data) {
    if (!data) return data;
    
    // 深拷贝数据以避免修改原始数据
    const sanitized = JSON.parse(JSON.stringify(data));
    
    // 遍历所有服务器数据
    if (sanitized.servers && Array.isArray(sanitized.servers)) {
        sanitized.servers.forEach(server => {
            // 移除服务器信息中的敏感字段
            if (server.info) {
                delete server.info.host;
                delete server.info.port;
                delete server.info.ip;
                delete server.info.ipAddress;
                delete server.info.hostname;
                delete server.info.address;
            }
            
            // 确保metrics中不包含敏感信息
            if (server.metrics) {
                // TCPing数据通常不包含敏感信息，但检查targetId
                if (server.metrics.tcping && Array.isArray(server.metrics.tcping)) {
                    server.metrics.tcping.forEach(item => {
                        // 如果targetId包含IP地址，替换为匿名标识
                        if (item.targetId && /\d+\.\d+\.\d+\.\d+/.test(item.targetId)) {
                            item.targetId = 'target_' + item.targetId.replace(/\d+\.\d+\.\d+\.\d+/g, 'xxx.xxx.xxx.xxx');
                        }
                    });
                }
            }
        });
    }
    
    // 移除可能包含的其他敏感字段
    const sensitiveKeys = [
        'password', 'pass', 'pwd', 'secret', 'key', 'token',
        'auth', 'credential', 'private', 'ssh', 'api_key',
        'access_token', 'refresh_token', 'bearer', 'authorization'
    ];
    
    function removeSensitiveFields(obj) {
        if (!obj || typeof obj !== 'object') return;
        
        Object.keys(obj).forEach(key => {
            const lowerKey = key.toLowerCase();
            
            // 检查是否是敏感字段
            if (sensitiveKeys.some(sensitive => lowerKey.includes(sensitive))) {
                delete obj[key];
            } else if (typeof obj[key] === 'object') {
                // 递归处理嵌套对象
                removeSensitiveFields(obj[key]);
            }
        });
    }
    
    removeSensitiveFields(sanitized);
    
    return sanitized;
}

/**
 * 验证数据是否包含敏感信息
 * @param {Object} data - 要验证的数据
 * @returns {Object} 验证结果 {safe: boolean, issues: string[]}
 */
function validateDataSafety(data) {
    const issues = [];
    const dataStr = JSON.stringify(data);
    
    // 检查常见的敏感模式
    const sensitivePatterns = [
        { pattern: /password\s*[:=]\s*["'][^"']+["']/i, desc: '明文密码' },
        { pattern: /\b(?:ssh|ftp|sftp):\/\/[^:]+:[^@]+@/i, desc: '包含凭据的URL' },
        { pattern: /api[_-]?key\s*[:=]\s*["'][^"']+["']/i, desc: 'API密钥' },
        { pattern: /secret\s*[:=]\s*["'][^"']+["']/i, desc: '密钥信息' },
        { pattern: /token\s*[:=]\s*["'][^"']+["']/i, desc: '令牌信息' },
        { pattern: /\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+/g, desc: 'IP地址和端口' }
    ];
    
    sensitivePatterns.forEach(({ pattern, desc }) => {
        if (pattern.test(dataStr)) {
            issues.push(desc);
        }
    });
    
    return {
        safe: issues.length === 0,
        issues
    };
}

module.exports = {
    sanitizeMonitoringData,
    validateDataSafety
};