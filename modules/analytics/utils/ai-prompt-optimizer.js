// 优化的 AI 提示词构建器
"use strict";

/**
 * AI 提示词优化器 - 生成结构化、高效的提示词
 */
class AIPromptOptimizer {
    constructor() {
        this.version = '2.0';
        this.maxPromptLength = 4000; // 字符限制
    }

    /**
     * 构建优化的分析提示词
     * @param {Object} preprocessedData - 预处理后的数据
     * @returns {string} 优化的提示词
     */
    buildOptimizedPrompt(preprocessedData) {
        const { metadata, serverSummaries, aggregatedMetrics, anomalies, localAnalysis } = preprocessedData;
        
        // 构建精炼的提示词
        const prompt = `
## 角色
你是专业的服务器运维分析专家。

## 任务
基于提供的监控数据摘要，生成智能分析报告。

## 数据概览
- 时间范围: ${this._formatTimeRange(metadata.timeRange)}
- 服务器数量: ${metadata.serverCount}
- 数据采样点: ${metadata.dataPoints}

## 核心指标
${this._formatCoreMetrics(aggregatedMetrics)}

## 异常发现
${this._formatAnomalies(anomalies)}

## 服务器状态摘要
${this._formatServerSummaries(serverSummaries)}

${localAnalysis ? `## 已计算数据\n${this._formatLocalAnalysis(localAnalysis)}` : ''}

## 输出要求
生成JSON格式的分析报告，包含：
1. summary - 总体评估（健康度评分0-100）
2. issues - 发现的问题（按严重程度排序）
3. recommendations - 具体建议（包含服务器ID和名称）
4. predictions - 趋势预测（基于当前数据）

JSON格式示例：
\`\`\`json
{
  "summary": {
    "overallScore": 85,
    "criticalCount": 2,
    "warningCount": 5,
    "status": "需要关注"
  },
  "issues": [{
    "serverId": "srv001",
    "serverName": "生产服务器1",
    "type": "high_latency",
    "severity": "high",
    "description": "网络延迟持续超过300ms",
    "metrics": { "avgLatency": 450, "maxLatency": 800 }
  }],
  "recommendations": [{
    "priority": "high",
    "title": "优化网络连接",
    "targets": ["srv001", "srv002"],
    "action": "检查网络路由，考虑更换线路",
    "impact": "预计降低50%延迟"
  }],
  "predictions": [{
    "type": "traffic",
    "trend": "increasing",
    "forecast": "未来7天流量预计增长20%",
    "risk": "可能触达带宽上限"
  }]
}
\`\`\`

注意：
- 所有建议必须包含具体的服务器ID和名称
- 优先关注严重问题
- 提供可执行的具体建议
`;

        return this._trimPrompt(prompt);
    }

    /**
     * 构建预测分析提示词
     */
    buildPredictivePrompt(historicalData, targetMetrics) {
        const prompt = `
## 任务
基于历史数据进行趋势预测。

## 历史数据
${this._formatHistoricalData(historicalData)}

## 预测目标
- 指标: ${targetMetrics.join(', ')}
- 时间跨度: ${historicalData.days}天历史 → 预测未来${targetMetrics.futureDays}天

## 输出格式
{
  "predictions": {
    "traffic": { "trend": "increasing", "confidence": 0.85, "values": [...] },
    "performance": { "trend": "stable", "confidence": 0.92, "risks": [...] }
  },
  "alerts": [{ "metric": "traffic", "threshold": "90%", "estimatedDate": "2025-08-05" }]
}
`;
        return this._trimPrompt(prompt);
    }

    /**
     * 构建实时分析提示词
     */
    buildRealtimePrompt(realtimeData, baseline) {
        const prompt = `
## 任务
实时异常检测和快速响应建议。

## 当前数据
${JSON.stringify(realtimeData, null, 2)}

## 基线数据
${JSON.stringify(baseline, null, 2)}

## 分析要求
1. 检测偏离基线的异常
2. 评估严重程度
3. 提供即时响应建议

## 输出格式
{
  "anomalies": [{ "metric": "cpu", "deviation": "+45%", "severity": "high" }],
  "immediateActions": ["重启服务", "增加实例"],
  "alertLevel": "warning|critical"
}
`;
        return this._trimPrompt(prompt);
    }

    // === 格式化辅助方法 ===

    _formatTimeRange(timeRange) {
        const start = new Date(timeRange.start * 1000).toISOString();
        const end = new Date(timeRange.end * 1000).toISOString();
        const duration = (timeRange.end - timeRange.start) / 3600;
        return `${start} 至 ${end} (${duration.toFixed(1)}小时)`;
    }

    _formatCoreMetrics(metrics) {
        if (!metrics?.overall) return '无数据';
        
        const { overall } = metrics;
        return `
- 总服务器: ${overall.totalServers}
- 健康状态: ${this._formatHealthStatus(overall.healthStatus)}
- 平均响应: ${overall.avgResponseTime}ms
- 总流量: ${overall.totalTraffic}
- CPU使用率: ${overall.avgCpuUsage}%
- 内存使用率: ${overall.avgMemUsage}%`;
    }

    _formatHealthStatus(status) {
        if (!status) return '未知';
        return `健康:${status.healthy || 0} 警告:${status.warning || 0} 严重:${status.critical || 0}`;
    }

    _formatAnomalies(anomalies) {
        if (!anomalies || anomalies.length === 0) {
            return '未检测到明显异常';
        }
        
        return anomalies.slice(0, 5).map(a => 
            `- [${a.severity}] ${a.serverName}(${a.serverId}): ${a.description}`
        ).join('\n');
    }

    _formatServerSummaries(summaries) {
        if (!summaries || summaries.length === 0) return '无服务器数据';
        
        // 只展示有问题的服务器
        const problematicServers = summaries.filter(s => s.status !== 'healthy');
        
        if (problematicServers.length === 0) {
            return `所有${summaries.length}台服务器运行正常`;
        }
        
        return problematicServers.slice(0, 10).map(s => {
            const perf = s.performance;
            return `
### ${s.name} (${s.id}) - ${s.status}
- 位置: ${s.location}
- 延迟: 平均${perf.tcping.avg}ms, 失败率${perf.tcping.failureRate}
- 负载: CPU ${perf.load.cpu.avg}%, 内存 ${perf.load.memory.avg}%
- 流量: ${perf.traffic.total}`;
        }).join('\n');
    }

    _formatLocalAnalysis(localAnalysis) {
        return `
### 已完成的本地分析
- 地理分布: ${localAnalysis.geoDistribution ? '已计算' : '未计算'}
- 流量分布: ${localAnalysis.serverTrafficDistribution ? '已计算' : '未计算'}
- 性能分布: ${localAnalysis.performanceDistribution ? '已计算' : '未计算'}

请直接使用这些已计算的数据，专注于：
1. 深度问题分析和模式识别
2. 预测性见解和趋势分析
3. 具体可操作的优化建议`;
    }

    _formatHistoricalData(data) {
        // 格式化历史数据的关键统计信息
        return `
- 数据点数: ${data.dataPoints}
- 平均值: ${JSON.stringify(data.averages)}
- 峰值: ${JSON.stringify(data.peaks)}
- 趋势: ${data.trend}`;
    }

    _trimPrompt(prompt) {
        // 确保提示词不超过长度限制
        if (prompt.length <= this.maxPromptLength) {
            return prompt.trim();
        }
        
        // 智能截断，保留关键信息
        console.warn(`[Prompt Optimizer] 提示词过长 (${prompt.length}), 进行智能截断`);
        return prompt.substring(0, this.maxPromptLength - 100) + '\n\n[数据已截断...]';
    }
}

module.exports = AIPromptOptimizer;