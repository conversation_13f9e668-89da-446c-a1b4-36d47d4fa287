// 优化的数据预处理器
"use strict";

/**
 * AI 数据预处理器 - 将原始监控数据转换为结构化的分析摘要
 * 目标：减少 token 使用，提高分析质量
 */
class AIDataPreprocessor {
    constructor() {
        this.aggregationRules = {
            tcping: { window: '5min', metrics: ['avg', 'min', 'max', 'p95', 'failureRate'] },
            load: { window: '5min', metrics: ['avg', 'max', 'trend'] },
            traffic: { window: 'hour', metrics: ['total', 'peak', 'avg'] }
        };
    }

    /**
     * 预处理监控数据，生成结构化摘要
     * @param {Object} rawData - 原始监控数据
     * @returns {Object} 预处理后的数据摘要
     */
    preprocessForAI(rawData) {
        console.log('[AI Preprocessor] 开始数据预处理...');
        
        const processed = {
            // 元数据
            metadata: {
                timeRange: rawData.metadata.timeRange,
                serverCount: rawData.servers.length,
                dataPoints: this._calculateDataPoints(rawData),
                analysisTime: new Date().toISOString()
            },
            
            // 服务器摘要（而非原始数据）
            serverSummaries: this._generateServerSummaries(rawData.servers),
            
            // 聚合指标
            aggregatedMetrics: this._aggregateMetrics(rawData),
            
            // 异常指标（只传递异常数据）
            anomalies: this._detectAnomalies(rawData),
            
            // 趋势分析
            trends: this._analyzeTrends(rawData),
            
            // 如果有本地分析结果，保留它
            localAnalysis: rawData.localAnalysis
        };
        
        // 计算数据压缩率
        const originalSize = JSON.stringify(rawData).length;
        const processedSize = JSON.stringify(processed).length;
        const compressionRate = ((originalSize - processedSize) / originalSize * 100).toFixed(1);
        
        console.log(`[AI Preprocessor] 数据压缩率: ${compressionRate}% (${originalSize} → ${processedSize} bytes)`);
        
        return processed;
    }

    /**
     * 生成服务器摘要信息
     */
    _generateServerSummaries(servers) {
        return servers.map(server => {
            const summary = {
                id: server.id,
                name: server.name,
                location: server.info?.location || 'Unknown',
                status: this._calculateServerStatus(server),
                
                // 性能摘要（而非原始数据点）
                performance: {
                    tcping: this._summarizeTcping(server.metrics.tcping),
                    load: this._summarizeLoad(server.metrics.load),
                    traffic: this._summarizeTraffic(server.metrics.traffic)
                },
                
                // 关键指标
                keyMetrics: this._extractKeyMetrics(server)
            };
            
            // 只在有问题时添加详细信息
            if (summary.status !== 'healthy') {
                summary.issues = this._identifyIssues(server);
            }
            
            return summary;
        });
    }

    /**
     * TCPing 数据摘要
     */
    _summarizeTcping(tcpingData) {
        if (!tcpingData || tcpingData.count !== undefined) {
            return { dataPoints: tcpingData?.count || 0 };
        }
        
        const values = tcpingData.map(d => d.value).filter(v => v > 0);
        const failures = tcpingData.filter(d => d.value === 0).length;
        
        return {
            avg: values.length > 0 ? Math.round(values.reduce((a, b) => a + b) / values.length) : 0,
            min: values.length > 0 ? Math.min(...values) : 0,
            max: values.length > 0 ? Math.max(...values) : 0,
            p95: this._calculatePercentile(values, 95),
            failureRate: (failures / tcpingData.length * 100).toFixed(1) + '%',
            trend: this._calculateTrend(values)
        };
    }

    /**
     * 负载数据摘要
     */
    _summarizeLoad(loadData) {
        if (!loadData || loadData.count !== undefined) {
            return { dataPoints: loadData?.count || 0 };
        }
        
        const cpuValues = loadData.map(d => d.cpu).filter(v => v !== null);
        const memValues = loadData.map(d => d.mem).filter(v => v !== null);
        
        return {
            cpu: {
                avg: Math.round(this._average(cpuValues)),
                max: Math.max(...cpuValues),
                trend: this._calculateTrend(cpuValues)
            },
            memory: {
                avg: Math.round(this._average(memValues)),
                max: Math.max(...memValues),
                trend: this._calculateTrend(memValues)
            }
        };
    }

    /**
     * 流量数据摘要
     */
    _summarizeTraffic(trafficData) {
        if (!trafficData || trafficData.count !== undefined) {
            return { dataPoints: trafficData?.count || 0 };
        }
        
        const totalTraffic = trafficData.reduce((sum, d) => sum + (d.total || 0), 0);
        const inboundTotal = trafficData.reduce((sum, d) => sum + (d.inbound || 0), 0);
        const outboundTotal = trafficData.reduce((sum, d) => sum + (d.outbound || 0), 0);
        
        return {
            total: this._formatBytes(totalTraffic),
            inbound: this._formatBytes(inboundTotal),
            outbound: this._formatBytes(outboundTotal),
            avgBandwidth: this._formatBandwidth(totalTraffic / trafficData.length / 3600), // 转换为每秒
            peakBandwidth: this._formatBandwidth(Math.max(...trafficData.map(d => d.total || 0)) / 3600)
        };
    }

    /**
     * 聚合所有服务器的指标
     */
    _aggregateMetrics(rawData) {
        return {
            overall: {
                totalServers: rawData.servers.length,
                healthStatus: this._calculateHealthDistribution(rawData.servers),
                avgResponseTime: this._calculateAvgResponseTime(rawData.servers),
                totalTraffic: this._calculateTotalTraffic(rawData.servers),
                avgCpuUsage: this._calculateAvgCpu(rawData.servers),
                avgMemUsage: this._calculateAvgMemory(rawData.servers)
            },
            
            // 按地区聚合（如果有）
            byRegion: this._aggregateByRegion(rawData.servers),
            
            // TOP 5 问题服务器
            topIssues: this._identifyTopIssues(rawData.servers)
        };
    }

    /**
     * 检测异常情况
     */
    _detectAnomalies(rawData) {
        const anomalies = [];
        
        rawData.servers.forEach(server => {
            // 网络异常
            const tcpingAnomalies = this._detectTcpingAnomalies(server);
            if (tcpingAnomalies) anomalies.push(tcpingAnomalies);
            
            // 性能异常
            const loadAnomalies = this._detectLoadAnomalies(server);
            if (loadAnomalies) anomalies.push(loadAnomalies);
            
            // 流量异常
            const trafficAnomalies = this._detectTrafficAnomalies(server);
            if (trafficAnomalies) anomalies.push(trafficAnomalies);
        });
        
        return anomalies;
    }

    /**
     * 分析趋势
     */
    _analyzeTrends(rawData) {
        return {
            network: this._analyzeNetworkTrends(rawData),
            performance: this._analyzePerformanceTrends(rawData),
            traffic: this._analyzeTrafficTrends(rawData)
        };
    }

    // === 辅助方法 ===

    _calculateDataPoints(rawData) {
        let total = 0;
        rawData.servers.forEach(server => {
            const metrics = server.metrics;
            total += (metrics.tcping?.length || metrics.tcping?.count || 0);
            total += (metrics.load?.length || metrics.load?.count || 0);
            total += (metrics.traffic?.length || metrics.traffic?.count || 0);
        });
        return total;
    }

    _calculateServerStatus(server) {
        // 基于各项指标计算服务器状态
        const metrics = server.metrics;
        let score = 100;
        
        // 根据各项指标扣分
        // ... 实现评分逻辑
        
        if (score >= 90) return 'healthy';
        if (score >= 70) return 'warning';
        return 'critical';
    }

    _calculatePercentile(values, percentile) {
        if (values.length === 0) return 0;
        const sorted = values.sort((a, b) => a - b);
        const index = Math.ceil((percentile / 100) * sorted.length) - 1;
        return sorted[index];
    }

    _calculateTrend(values) {
        if (values.length < 2) return 'stable';
        
        // 简单的趋势判断
        const firstHalf = values.slice(0, Math.floor(values.length / 2));
        const secondHalf = values.slice(Math.floor(values.length / 2));
        
        const firstAvg = this._average(firstHalf);
        const secondAvg = this._average(secondHalf);
        
        const change = ((secondAvg - firstAvg) / firstAvg) * 100;
        
        if (change > 10) return 'increasing';
        if (change < -10) return 'decreasing';
        return 'stable';
    }

    _average(values) {
        if (values.length === 0) return 0;
        return values.reduce((a, b) => a + b) / values.length;
    }

    _formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    _formatBandwidth(bytesPerSecond) {
        return this._formatBytes(bytesPerSecond) + '/s';
    }

    _extractKeyMetrics(server) {
        // 提取关键指标用于快速判断
        return {
            isOnline: server.info?.status === 1,
            hasHighLatency: false, // 基于tcping数据判断
            hasHighLoad: false,    // 基于load数据判断
            hasTrafficSpike: false // 基于traffic数据判断
        };
    }

    _identifyIssues(server) {
        const issues = [];
        // 识别具体问题
        return issues;
    }

    // ... 其他辅助方法实现
}

module.exports = AIDataPreprocessor;