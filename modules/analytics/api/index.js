// Backend API routes and handlers for analytics module
"use strict";

const express = require('express');
const router = express.Router();
const { getDataAccess, getLocalAnalyzer } = require('../engine');
const { MovingAverage } = require('../algorithms');
const { sanitizeMonitoringData, validateDataSafety } = require('../utils/data-sanitizer');

/**
 * AI Analytics 功能权限检查中间件
 * 检查用户是否拥有 AI_ANALYTICS 功能权限
 */
const checkAIAnalyticsFeature = (req, res, next) => {
    try {
        const licenseEnhanced = req.app.locals['license-enhanced'];
        if (!licenseEnhanced || !licenseEnhanced.featureChecker) {
            // 无许可证检查器时默认放行
            console.log('[AI Analytics] 许可证检查器不可用，默认允许访问');
            return next();
        }
        
        const check = licenseEnhanced.featureChecker.checkFeature('AI_ANALYTICS');
        if (check.allowed) {
            return next();
        } else {
            // 返回功能墙提示（保持API控制）
            return res.status(403).json({
                success: false,
                error: 'Feature not available',
                message: check.message,
                currentPlan: check.currentPlan,
                requiredFeature: 'AI_ANALYTICS'
            });
        }
    } catch (error) {
        console.error('[AI Analytics] 权限检查异常:', error);
        // 异常时默认放行
        return next();
    }
};

/**
 * GET /api/analytics/data
 * 获取分析数据的通用接口
 * 
 * Query参数:
 * - dataType: 数据类型 (tcping, load, traffic)
 * - startTime: 开始时间戳(秒)
 * - endTime: 结束时间戳(秒)
 * - serverId: 服务器ID (可选)
 * - granularity: 数据粒度 (可选)
 */
router.get('/data', async (req, res) => {
    try {
        const { dataType, startTime, endTime, serverId, granularity = 'raw' } = req.query;
        
        // 参数验证
        if (!dataType || !startTime || !endTime) {
            return res.status(400).json({
                success: false,
                error: '缺少必需参数: dataType, startTime, endTime'
            });
        }
        
        const dataAccess = getDataAccess();
        const data = await dataAccess.getAnalyticsData(
            dataType,
            parseInt(startTime),
            parseInt(endTime),
            { serverId, granularity }
        );
        
        res.json({
            success: true,
            data,
            meta: {
                dataType,
                startTime: parseInt(startTime),
                endTime: parseInt(endTime),
                granularity,
                count: data.length
            }
        });
        
    } catch (error) {
        console.error('[Analytics API] 错误:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/analytics/servers
 * 获取可用的服务器列表
 */
router.get('/servers', async (req, res) => {
    try {
        const dataAccess = getDataAccess();
        const servers = await dataAccess.getAvailableServers();
        
        res.json({
            success: true,
            data: servers
        });
        
    } catch (error) {
        console.error('[Analytics API] 获取服务器列表失败:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/analytics/status
 * 获取Analytics模块状态
 */
router.get('/status', (req, res) => {
    // 模块状态需要从engine获取
    const hasDataAccess = (() => {
        try {
            const { getDataAccess } = require('../engine');
            return !!getDataAccess();
        } catch {
            return false;
        }
    })();
    
    res.json({
        success: true,
        data: {
            initialized: hasDataAccess,
            hasDatabase: hasDataAccess,
            modules: {
                dataAccess: hasDataAccess,
                algorithms: true, // 已实现移动平均算法
                ai: false, // 待实现
                cache: false // 待实现
            }
        }
    });
});

/**
 * GET /api/analytics/trends/:serverId
 * 获取服务器的趋势分析数据
 * 
 * Query参数:
 * - startTime: 开始时间戳(秒)
 * - endTime: 结束时间戳(秒)
 * - granularity: 数据粒度 (raw, minute, 5min, hourly, daily)
 * - windowSize: 移动平均窗口大小 (默认5)
 * - algorithm: 算法类型 (simple, exponential, weighted) 默认simple
 * - dataType: 数据类型 (tcping, load, traffic) 默认tcping
 */
router.get('/trends/:serverId', async (req, res) => {
    try {
        const { serverId } = req.params;
        const { 
            startTime, 
            endTime, 
            granularity = 'raw',
            windowSize = 5,
            algorithm = 'simple',
            dataType = 'tcping'
        } = req.query;
        
        // 参数验证
        if (!serverId || !startTime || !endTime) {
            return res.status(400).json({
                success: false,
                error: '缺少必需参数: serverId, startTime, endTime'
            });
        }
        
        // 获取数据
        const dataAccess = getDataAccess();
        const rawData = await dataAccess.getAnalyticsData(
            dataType,
            parseInt(startTime),
            parseInt(endTime),
            { serverId, granularity }
        );
        
        if (!rawData || rawData.length === 0) {
            return res.json({
                success: true,
                data: {
                    rawData: [],
                    smoothedData: [],
                    predictions: [],
                    statistics: null
                },
                meta: {
                    serverId,
                    dataType,
                    algorithm,
                    windowSize: parseInt(windowSize),
                    startTime: parseInt(startTime),
                    endTime: parseInt(endTime),
                    granularity
                }
            });
        }
        
        // 准备数据格式
        const timeSeriesData = rawData.map(item => ({
            timestamp: item.timestamp,
            value: dataType === 'tcping' ? item.value : 
                   dataType === 'load' ? item.load1 : 
                   item.total || 0
        }));
        
        // 应用移动平均算法
        let result;
        const windowSizeInt = parseInt(windowSize);
        
        switch (algorithm) {
            case 'exponential':
                result = MovingAverage.exponential(timeSeriesData, windowSizeInt);
                break;
            case 'weighted':
                result = MovingAverage.weighted(timeSeriesData, windowSizeInt);
                break;
            case 'simple':
            default:
                result = MovingAverage.simple(timeSeriesData, windowSizeInt);
                break;
        }
        
        // 返回结果
        res.json({
            success: true,
            data: {
                rawData: timeSeriesData,
                smoothedData: result.smoothedData,
                predictions: result.predictions,
                statistics: result.statistics
            },
            meta: {
                serverId,
                dataType,
                algorithm,
                windowSize: result.windowSize,
                startTime: parseInt(startTime),
                endTime: parseInt(endTime),
                granularity,
                dataPoints: timeSeriesData.length,
                smoothingFactor: result.smoothingFactor || null
            }
        });
        
    } catch (error) {
        console.error('[Analytics API] 趋势分析错误:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});



/**
 * GET /api/analytics/ai/reports
 * 获取AI报告历史列表
 */
router.get('/ai/reports', checkAIAnalyticsFeature, async (req, res) => {
    try {
        const { limit = 20, offset = 0, startTime, endTime } = req.query;
        
        const db = req.app.locals.database;
        if (!db || !db.aiReports) {
            return res.status(503).json({
                success: false,
                error: '报告存储服务不可用'
            });
        }
        
        const reports = db.aiReports.getReports({
            limit: parseInt(limit),
            offset: parseInt(offset),
            startTime: startTime ? parseInt(startTime) : undefined,
            endTime: endTime ? parseInt(endTime) : undefined
        });
        
        const stats = db.aiReports.getStats();
        
        res.json({
            success: true,
            data: reports,
            stats,
            pagination: {
                limit: parseInt(limit),
                offset: parseInt(offset),
                total: stats.totalReports
            }
        });
        
    } catch (error) {
        console.error('[Analytics API] 获取报告列表失败:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/analytics/ai/reports/:reportId
 * 获取单个AI报告详情
 */
router.get('/ai/reports/:reportId', checkAIAnalyticsFeature, async (req, res) => {
    try {
        const { reportId } = req.params;
        
        const db = req.app.locals.database;
        if (!db || !db.aiReports) {
            return res.status(503).json({
                success: false,
                error: '报告存储服务不可用'
            });
        }
        
        const report = db.aiReports.getReport(reportId);
        
        if (!report) {
            return res.status(404).json({
                success: false,
                error: '报告不存在'
            });
        }
        
        res.json({
            success: true,
            data: report
        });
        
    } catch (error) {
        console.error('[Analytics API] 获取报告详情失败:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * DELETE /api/analytics/ai/reports/:reportId
 * 删除AI报告
 */
router.delete('/ai/reports/:reportId', checkAIAnalyticsFeature, async (req, res) => {
    try {
        const { reportId } = req.params;
        
        const db = req.app.locals.database;
        if (!db || !db.aiReports) {
            return res.status(503).json({
                success: false,
                error: '报告存储服务不可用'
            });
        }
        
        const deleted = db.aiReports.deleteReport(reportId);
        
        if (!deleted) {
            return res.status(404).json({
                success: false,
                error: '报告不存在'
            });
        }
        
        res.json({
            success: true,
            message: '报告已删除'
        });
        
    } catch (error) {
        console.error('[Analytics API] 删除报告失败:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/analytics/ai/test
 * 测试AI服务连接
 */
router.get('/ai/test', checkAIAnalyticsFeature, async (req, res) => {
    // 检查管理员权限
    if (!req.admin) {
        return res.status(403).json({
            success: false,
            error: '权限不足',
            message: '需要管理员权限才能测试AI服务'
        });
    }
    
    try {
        const aiModule = require('../ai');
        const db = req.app.locals.database;
        const result = await aiModule.testConnection(db);
        
        res.json({
            success: true,
            data: result,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        console.error('[Analytics API] AI测试连接失败:', error.message);
        res.status(500).json({
            success: false,
            error: error.message,
            message: 'AI服务连接测试失败'
        });
    }
});

/**
 * POST /api/analytics/ai/report
 * 生成AI分析报告
 */
router.post('/ai/report', checkAIAnalyticsFeature, async (req, res) => {
    // 检查管理员权限
    if (!req.admin) {
        return res.status(403).json({
            success: false,
            error: '权限不足',
            message: '需要管理员权限才能生成AI分析报告'
        });
    }
    
    try {
        const { timeRange, serverIds, includeData } = req.body;
        
        // 收集监控数据
        const dataAccess = getDataAccess();
        const now = Math.floor(Date.now() / 1000);
        const startTime = timeRange?.start || (now - 24 * 3600); // 默认24小时
        const endTime = timeRange?.end || now;
        
        // 获取服务器列表
        let serversToAnalyze;
        if (serverIds && serverIds.length > 0) {
            // 如果指定了服务器ID，则只分析这些服务器
            serversToAnalyze = serverIds;
        } else {
            // 否则，获取所有在线服务器
            const allServers = await dataAccess.getAvailableServers();
            serversToAnalyze = allServers
                .filter(s => s.status === 1) // 过滤掉离线服务器
                .map(s => s.id);
        }
        
        // 收集各类监控数据
        const monitoringData = {
            metadata: {
                timeRange: { start: startTime, end: endTime },
                serverCount: serversToAnalyze.length,
                analysisTime: new Date().toISOString()
            },
            servers: []
        };
        
        // 为每个服务器收集数据 (不再限制数量，只分析在线的)
        console.log(`[AI Analytics] 开始收集 ${serversToAnalyze.length} 个在线服务器的数据...`);
        
        for (const serverId of serversToAnalyze) {
            try {
                console.log(`[AI Analytics] 收集服务器 ${serverId} 的数据...`);
                
                const serverInfo = await dataAccess.getServerInfo(serverId);
                const tcpingData = await dataAccess.getAnalyticsData('tcping', startTime, endTime, { serverId, granularity: '5min' });
                const loadData = await dataAccess.getAnalyticsData('load', startTime, endTime, { serverId, granularity: 'minute' });
                const trafficData = await dataAccess.getAnalyticsData('traffic', startTime, endTime, { serverId, granularity: 'hourly' });
                
                console.log(`[AI Analytics] 服务器 ${serverId} 数据收集完成: tcping=${tcpingData.length}, load=${loadData.length}, traffic=${trafficData.length}`);
                
                monitoringData.servers.push({
                    id: serverId,
                    name: serverInfo?.name || 'Unknown',
                    info: serverInfo, // 包含完整服务器信息，AI分析时可利用
                    metrics: {
                        tcping: includeData ? tcpingData.slice(-50) : { count: tcpingData.length },
                        load: includeData ? loadData.slice(-50) : { count: loadData.length },
                        traffic: includeData ? trafficData.slice(-24) : { count: trafficData.length }
                    }
                });
            } catch (error) {
                console.error(`[AI Analytics] 收集服务器 ${serverId} 数据失败:`, error);
                console.warn(`[AI Analytics] 收集服务器 ${serverId} 数据失败:`, error.message);
            }
        }
        
        console.log(`[AI Analytics] 数据收集完成，成功收集 ${monitoringData.servers.length} 个服务器的数据`);
        
        // 执行本地数据分析
        console.log('[AI Analytics] 开始本地数据分析...');
        const localAnalyzer = getLocalAnalyzer();
        const localAnalysis = await localAnalyzer.performLocalAnalysis(startTime, endTime);
        console.log('[AI Analytics] 本地分析完成，包含地理分布和流量分析数据');
        
        // 将本地分析结果合并到监控数据中
        monitoringData.localAnalysis = localAnalysis;
        
        // 在发送给AI之前过滤敏感信息
        const sanitizedData = sanitizeMonitoringData(monitoringData);
        
        // 验证数据安全性
        const validation = validateDataSafety(sanitizedData);
        if (!validation.safe) {
            console.warn(`[AI Analytics] 数据安全验证失败，发现敏感信息: ${validation.issues.join(', ')}`);
            console.warn(`[AI Analytics] 数据包含敏感信息: ${validation.issues.join(', ')}`);
        }
        
        console.log(`[AI Analytics] 已过滤敏感信息，准备发送给AI`);
        console.log(`[AI Analytics] 过滤后的数据预览:`, JSON.stringify(sanitizedData, null, 2).substring(0, 500));
        
        // 调用AI生成报告
        const aiModule = require('../ai');
        const db = req.app.locals.database;
        
        // 确保AI模块已初始化（传入数据库实例）
        if (!aiModule.getGeminiService()) {
            console.log('[AI Analytics] AI服务未初始化，正在初始化...');
            await aiModule.init(db);
        }
        
        // 如果本地分析成功，优先使用本地分析的数据
        let report;
        if (localAnalysis.localAnalysis) {
            console.log('[AI Analytics] 使用本地分析结果作为基础数据');
            // 创建混合报告 - 使用本地分析的固定数据 + AI的智能建议
            const hybridData = {
                ...sanitizedData,
                localAnalysis: {
                    geoDistribution: localAnalysis.geoDistribution,
                    serverTrafficDistribution: localAnalysis.serverTrafficDistribution,
                    performanceDistribution: localAnalysis.performanceDistribution,
                    summary: localAnalysis.summary,
                    trafficAnalysis: localAnalysis.trafficAnalysis,
                    networkQuality: localAnalysis.networkQuality,
                    loadAnalysis: localAnalysis.loadAnalysis
                }
            };
            report = await aiModule.generateReport(hybridData, db);
            
            // 确保本地分析的数据覆盖AI可能生成的相同数据
            if (localAnalysis.geoDistribution) {
                report.geoDistribution = localAnalysis.geoDistribution;
            }
            if (localAnalysis.serverTrafficDistribution) {
                report.serverTrafficDistribution = localAnalysis.serverTrafficDistribution;
            }
            if (localAnalysis.performanceDistribution) {
                report.performanceDistribution = localAnalysis.performanceDistribution;
            }
            // 添加本地分析标志
            report.localAnalysis = true;
        } else {
            // 如果本地分析失败，回退到纯AI分析
            console.log('[AI Analytics] 本地分析失败，使用纯AI分析');
            report = await aiModule.generateReport(sanitizedData, db);
        }
        
        // 保存报告到数据库
        try {
            const db = req.app.locals.database;
            if (db && db.aiReports) {
                const savedReport = db.aiReports.saveReport(report, {
                    timeRange: { start: startTime, end: endTime },
                    serversAnalyzed: monitoringData.servers.length,
                    generatedAt: new Date().toISOString(),
                    serverIds: serversToAnalyze
                });
                
                console.log(`[AI Analytics] 报告已保存，ID: ${savedReport.reportId}`);
                
                res.json({
                    success: true,
                    data: report,
                    reportId: savedReport.reportId,
                    meta: {
                        serversAnalyzed: monitoringData.servers.length,
                        timeRange: { start: startTime, end: endTime },
                        generatedAt: new Date().toISOString()
                    }
                });
            } else {
                // 如果没有数据库，仍然返回报告
                res.json({
                    success: true,
                    data: report,
                    meta: {
                        serversAnalyzed: monitoringData.servers.length,
                        timeRange: { start: startTime, end: endTime },
                        generatedAt: new Date().toISOString()
                    }
                });
            }
        } catch (saveError) {
            console.error('[AI Analytics] 保存报告失败:', saveError.message);
            // 即使保存失败，仍然返回报告
            res.json({
                success: true,
                data: report,
                meta: {
                    serversAnalyzed: monitoringData.servers.length,
                    timeRange: { start: startTime, end: endTime },
                    generatedAt: new Date().toISOString()
                }
            });
        }
        
    } catch (error) {
        console.error('[Analytics API] AI报告生成失败:', error.message);
        res.status(500).json({
            success: false,
            error: error.message,
            message: 'AI分析报告生成失败，请稍后重试'
        });
    }
});

module.exports = router;