// Main entry point for the analytics backend module
"use strict";

const { initDataAccess, getDataAccess } = require('./engine');
const GeminiAnalyticsService = require('./ai/gemini-service'); // 直接引用AI服务
// 临时使用console.log代替logUtils，避免LOG_TYPES未定义问题

let initialized = false;
let db = null;
let geminiService = null; // 在这个作用域中定义geminiService

/**
 * 初始化Analytics模块
 * @param {Object} database - 数据库实例
 * @param {Object} app - Express应用实例
 */
async function init(database, app) {
    if (initialized) {
        console.log('[Analytics] 模块已经初始化');
        return;
    }
    
    if (!database) {
        throw new Error('Analytics模块初始化失败：未提供数据库实例');
    }
    
    db = database;
    
    // 初始化数据访问层
    initDataAccess(db);
    
    // 初始化AI模块
    try {
        // 直接在这里初始化GeminiAnalyticsService
        geminiService = new GeminiAnalyticsService(db); // 创建实例，传入数据库
        await geminiService.init(); // 初始化Gemini服务
        console.log('[Analytics] AI 模块初始化完成');
    } catch (error) {
        console.log(`[Analytics] AI 模块初始化失败: ${error.message}`);
    }
    
    // 注册API路由
    if (app) {
        const apiRoutes = require('./api/index');  // 明确指定使用 api/index.js

        app.use('/api/analytics', apiRoutes);
        console.log('[Analytics] API路由已注册: /api/analytics');
    }
    
    initialized = true;
    console.log('[Analytics] 模块初始化完成');
}

/**
 * 获取Analytics模块状态
 * @returns {Object} 模块状态信息
 */
function getStatus() {
    return {
        initialized,
        hasDatabase: !!db,
        modules: {
            dataAccess: !!getDataAccess,
            algorithms: false, // 异常检测功能已移除
            ai: !!geminiService?.initialized, // 根据geminiService的初始化状态更新AI模块状态
            cache: false // 待实现
        }
    };
}

// DStatus模块导出函数
module.exports = async function(app, database) {
    // 初始化模块
    await init(database, app);
    
    // 返回模块API
    const analyticsModule = {
        getStatus,
        // 导出子模块以供直接访问
        engine: require('./engine'),
        algorithms: require('./algorithms'),
        api: require('./api'),
        cache: require('./cache'),
        // 确保导出的AI模块有generateReport和testConnection方法
        ai: {
            generateReport: async (monitoringData) => {
                if (!geminiService) {
                    throw new Error('AI服务未初始化，无法生成报告');
                }
                return await geminiService.generateAnalyticsReport(monitoringData);
            },
            testConnection: async () => {
                if (!geminiService) {
                    return { success: false, message: 'AI服务未初始化，无法测试连接' };
                }
                return await geminiService.testConnection();
            }
        }
    };

    // 将模块本身挂载到svr.locals，以便其他模块访问
    if (app) {
        app.locals.analytics = analyticsModule;
    }

    return analyticsModule;
};