// 移动平均算法 - 用于平滑时间序列数据和趋势预测
"use strict";

const { logUtils, LOG_TYPES } = require('../../utils/log-utils');

/**
 * 简单移动平均(SMA)算法实现
 */
class MovingAverage {
    /**
     * 计算简单移动平均
     * @param {Array} data - 时间序列数据数组 [{timestamp, value}, ...]
     * @param {number} windowSize - 窗口大小(数据点数)
     * @returns {Object} 包含平滑数据和预测的结果
     */
    static simple(data, windowSize = 5) {
        try {
            // 参数验证
            if (!Array.isArray(data) || data.length === 0) {
                throw new Error('数据必须是非空数组');
            }
            
            if (windowSize < 1 || windowSize > data.length) {
                throw new Error(`窗口大小必须在1到${data.length}之间`);
            }
            
            // 排序数据(按时间戳升序)
            const sortedData = [...data].sort((a, b) => a.timestamp - b.timestamp);
            
            // 计算移动平均
            const smoothedData = [];
            const predictions = [];
            
            for (let i = 0; i < sortedData.length; i++) {
                // 计算当前窗口的起始位置
                const start = Math.max(0, i - windowSize + 1);
                const windowData = sortedData.slice(start, i + 1);
                
                // 计算平均值
                const sum = windowData.reduce((acc, item) => acc + item.value, 0);
                const avg = sum / windowData.length;
                
                smoothedData.push({
                    timestamp: sortedData[i].timestamp,
                    value: sortedData[i].value,
                    smoothed: avg,
                    windowSize: windowData.length
                });
            }
            
            // 趋势预测(基于最后几个数据点的趋势)
            if (smoothedData.length >= 2) {
                const trend = this._calculateTrend(smoothedData.slice(-Math.min(windowSize, smoothedData.length)));
                predictions.push(...this._generatePredictions(smoothedData, trend, 3)); // 预测3个数据点
            }
            
            return {
                smoothedData,
                predictions,
                statistics: this._calculateStatistics(smoothedData),
                windowSize
            };
            
        } catch (error) {
            logUtils.writeLog(LOG_TYPES.ERROR, `计算简单移动平均失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * 指数移动平均(EMA)算法实现
     * @param {Array} data - 时间序列数据数组
     * @param {number} windowSize - 窗口大小(用于计算平滑因子)
     * @param {number} smoothingFactor - 平滑因子(可选，默认根据窗口大小计算)
     * @returns {Object} 包含平滑数据和预测的结果
     */
    static exponential(data, windowSize = 5, smoothingFactor = null) {
        try {
            // 参数验证
            if (!Array.isArray(data) || data.length === 0) {
                throw new Error('数据必须是非空数组');
            }
            
            // 计算平滑因子(α)
            const alpha = smoothingFactor || (2 / (windowSize + 1));
            
            // 排序数据
            const sortedData = [...data].sort((a, b) => a.timestamp - b.timestamp);
            
            // 计算EMA
            const smoothedData = [];
            let ema = sortedData[0].value; // 初始EMA值为第一个数据点
            
            for (let i = 0; i < sortedData.length; i++) {
                if (i === 0) {
                    smoothedData.push({
                        timestamp: sortedData[i].timestamp,
                        value: sortedData[i].value,
                        smoothed: ema,
                        alpha
                    });
                } else {
                    // EMA = α × 当前值 + (1 - α) × 前一个EMA
                    ema = alpha * sortedData[i].value + (1 - alpha) * ema;
                    smoothedData.push({
                        timestamp: sortedData[i].timestamp,
                        value: sortedData[i].value,
                        smoothed: ema,
                        alpha
                    });
                }
            }
            
            // 趋势预测
            const predictions = [];
            if (smoothedData.length >= 2) {
                const trend = this._calculateEMATrend(smoothedData.slice(-Math.min(windowSize, smoothedData.length)));
                predictions.push(...this._generatePredictions(smoothedData, trend, 3));
            }
            
            return {
                smoothedData,
                predictions,
                statistics: this._calculateStatistics(smoothedData),
                windowSize,
                smoothingFactor: alpha
            };
            
        } catch (error) {
            logUtils.writeLog(LOG_TYPES.ERROR, `计算指数移动平均失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * 加权移动平均(WMA)算法实现
     * @param {Array} data - 时间序列数据数组
     * @param {number} windowSize - 窗口大小
     * @param {Array} weights - 权重数组(可选，默认线性递减)
     * @returns {Object} 包含平滑数据和预测的结果
     */
    static weighted(data, windowSize = 5, weights = null) {
        try {
            // 参数验证
            if (!Array.isArray(data) || data.length === 0) {
                throw new Error('数据必须是非空数组');
            }
            
            // 生成默认权重(线性递减)
            if (!weights) {
                weights = [];
                for (let i = windowSize; i > 0; i--) {
                    weights.push(i);
                }
            }
            
            // 标准化权重
            const sumWeights = weights.reduce((a, b) => a + b, 0);
            const normalizedWeights = weights.map(w => w / sumWeights);
            
            // 排序数据
            const sortedData = [...data].sort((a, b) => a.timestamp - b.timestamp);
            
            // 计算WMA
            const smoothedData = [];
            
            for (let i = 0; i < sortedData.length; i++) {
                const start = Math.max(0, i - windowSize + 1);
                const windowData = sortedData.slice(start, i + 1);
                
                // 计算加权平均
                let weightedSum = 0;
                let weightSum = 0;
                
                for (let j = 0; j < windowData.length; j++) {
                    const weightIndex = Math.min(j, normalizedWeights.length - 1);
                    weightedSum += windowData[j].value * normalizedWeights[weightIndex];
                    weightSum += normalizedWeights[weightIndex];
                }
                
                const wma = weightedSum / weightSum;
                
                smoothedData.push({
                    timestamp: sortedData[i].timestamp,
                    value: sortedData[i].value,
                    smoothed: wma,
                    windowSize: windowData.length
                });
            }
            
            // 趋势预测
            const predictions = [];
            if (smoothedData.length >= 2) {
                const trend = this._calculateTrend(smoothedData.slice(-Math.min(windowSize, smoothedData.length)));
                predictions.push(...this._generatePredictions(smoothedData, trend, 3));
            }
            
            return {
                smoothedData,
                predictions,
                statistics: this._calculateStatistics(smoothedData),
                windowSize,
                weights: normalizedWeights
            };
            
        } catch (error) {
            logUtils.writeLog(LOG_TYPES.ERROR, `计算加权移动平均失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * 计算趋势
     * @private
     */
    static _calculateTrend(data) {
        if (data.length < 2) return 0;
        
        // 使用最小二乘法计算趋势线斜率
        let sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;
        const n = data.length;
        
        for (let i = 0; i < n; i++) {
            sumX += i;
            sumY += data[i].smoothed;
            sumXY += i * data[i].smoothed;
            sumX2 += i * i;
        }
        
        const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        const intercept = (sumY - slope * sumX) / n;
        
        return { slope, intercept };
    }
    
    /**
     * 计算EMA趋势
     * @private
     */
    static _calculateEMATrend(data) {
        if (data.length < 2) return 0;
        
        // 计算平滑值的变化率
        const changes = [];
        for (let i = 1; i < data.length; i++) {
            changes.push(data[i].smoothed - data[i - 1].smoothed);
        }
        
        // 平均变化率
        const avgChange = changes.reduce((a, b) => a + b, 0) / changes.length;
        
        return {
            slope: avgChange,
            intercept: data[data.length - 1].smoothed
        };
    }
    
    /**
     * 生成预测数据
     * @private
     */
    static _generatePredictions(data, trend, steps = 3) {
        const predictions = [];
        const lastPoint = data[data.length - 1];
        const avgInterval = this._calculateAverageInterval(data);
        
        for (let i = 1; i <= steps; i++) {
            const timestamp = lastPoint.timestamp + (avgInterval * i);
            let predictedValue;
            
            if (trend.slope !== undefined) {
                // 线性预测
                predictedValue = trend.intercept + trend.slope * (data.length - 1 + i);
            } else {
                // 使用最后的平滑值
                predictedValue = lastPoint.smoothed;
            }
            
            predictions.push({
                timestamp,
                value: predictedValue,
                isPrediction: true,
                confidence: Math.max(0, 1 - (i * 0.2)) // 置信度随预测距离递减
            });
        }
        
        return predictions;
    }
    
    /**
     * 计算平均时间间隔
     * @private
     */
    static _calculateAverageInterval(data) {
        if (data.length < 2) return 60; // 默认60秒
        
        let totalInterval = 0;
        for (let i = 1; i < data.length; i++) {
            totalInterval += data[i].timestamp - data[i - 1].timestamp;
        }
        
        return totalInterval / (data.length - 1);
    }
    
    /**
     * 计算统计信息
     * @private
     */
    static _calculateStatistics(smoothedData) {
        const values = smoothedData.map(d => d.value);
        const smoothedValues = smoothedData.map(d => d.smoothed);
        
        // 计算原始数据统计
        const originalStats = {
            mean: values.reduce((a, b) => a + b, 0) / values.length,
            min: Math.min(...values),
            max: Math.max(...values),
            std: 0
        };
        
        // 计算标准差
        const variance = values.reduce((acc, val) => acc + Math.pow(val - originalStats.mean, 2), 0) / values.length;
        originalStats.std = Math.sqrt(variance);
        
        // 计算平滑数据统计
        const smoothedStats = {
            mean: smoothedValues.reduce((a, b) => a + b, 0) / smoothedValues.length,
            min: Math.min(...smoothedValues),
            max: Math.max(...smoothedValues),
            std: 0
        };
        
        // 计算平滑数据标准差
        const smoothedVariance = smoothedValues.reduce((acc, val) => acc + Math.pow(val - smoothedStats.mean, 2), 0) / smoothedValues.length;
        smoothedStats.std = Math.sqrt(smoothedVariance);
        
        // 计算平滑效果
        const smoothingEffect = {
            varianceReduction: ((originalStats.std - smoothedStats.std) / originalStats.std * 100).toFixed(2) + '%',
            rangeReduction: (((originalStats.max - originalStats.min) - (smoothedStats.max - smoothedStats.min)) / (originalStats.max - originalStats.min) * 100).toFixed(2) + '%'
        };
        
        return {
            original: originalStats,
            smoothed: smoothedStats,
            smoothingEffect
        };
    }
}

module.exports = MovingAverage;