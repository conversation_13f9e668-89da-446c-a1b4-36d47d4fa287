/**
 * AI 配置管理工具
 * 负责读取和合并环境变量与数据库中的 AI 配置
 * 支持热更新，每次调用都会实时读取最新配置
 */

"use strict";

/**
 * 获取 AI 配置
 * 优先级: 环境变量 > 数据库配置 > 默认值
 * @param {Object} db - 数据库实例
 * @returns {Promise<Object>} AI 配置对象
 */
async function getAiConfig(db) {
  // 默认配置
  const defaultConfig = {
    enabled: false,
    name: 'OpenAI',
    apiBase: 'https://api.openai.com/v1',
    apiKey: '',
    model: 'gpt-3.5-turbo'
  };

  // 从数据库读取配置
  let dbConfig = {};
  try {
    const savedConfig = await db.setting.get('ai');
    if (savedConfig && typeof savedConfig === 'object') {
      dbConfig = savedConfig;
    }
  } catch (error) {
    console.error('[AI Config] 读取数据库配置失败:', error);
  }

  // 合并默认值和数据库配置
  const config = { ...defaultConfig, ...dbConfig };

  // 环境变量覆盖（优先级最高）
  const envLocks = {};

  // OpenAI 环境变量
  if (process.env.OPENAI_API_KEY) {
    config.apiKey = process.env.OPENAI_API_KEY;
    envLocks.apiKey = true;
  }

  if (process.env.OPENAI_BASE_URL) {
    config.apiBase = process.env.OPENAI_BASE_URL;
    envLocks.apiBase = true;
  }

  if (process.env.OPENAI_MODEL) {
    config.model = process.env.OPENAI_MODEL;
    envLocks.model = true;
  }

  // GEMINI 兼容性映射（弃用警告）
  if (!process.env.OPENAI_API_KEY && process.env.GEMINI_API_KEY) {
    console.warn('[AI Config] GEMINI_API_KEY 已弃用，请使用 OPENAI_API_KEY');
    config.apiKey = process.env.GEMINI_API_KEY;
    config.apiBase = 'https://generativelanguage.googleapis.com/v1beta';
    config.name = 'Gemini';
    config.model = 'gemini-pro';
    envLocks.apiKey = true;
    envLocks.apiBase = true;
  }

  // 添加环境锁定信息
  config.envLocks = envLocks;

  return config;
}

/**
 * 保存 AI 配置到数据库
 * @param {Object} db - 数据库实例
 * @param {Object} config - 要保存的配置
 * @returns {Promise<void>}
 */
async function saveAiConfig(db, config) {
  try {
    // 获取当前配置
    const currentConfig = await db.setting.get('ai') || {};
    
    // 合并配置（空 apiKey 不覆盖）
    const mergedConfig = { ...currentConfig };
    
    if (config.enabled !== undefined) {
      mergedConfig.enabled = config.enabled;
    }
    
    if (config.name !== undefined && config.name !== '') {
      mergedConfig.name = config.name;
    }
    
    if (config.apiBase !== undefined && config.apiBase !== '') {
      mergedConfig.apiBase = config.apiBase;
    }
    
    if (config.model !== undefined && config.model !== '') {
      mergedConfig.model = config.model;
    }
    
    // 只有非空 apiKey 才更新
    if (config.apiKey && config.apiKey !== '') {
      mergedConfig.apiKey = config.apiKey;
    }
    
    // 保存到数据库
    await db.setting.set('ai', mergedConfig);
    console.log('[AI Config] 配置已保存，db.type =', db.type || 'unknown');
    
  } catch (error) {
    console.error('[AI Config] 保存配置失败:', error);
    throw error;
  }
}

/**
 * 测试 AI 服务连接
 * @param {Object} config - AI 配置
 * @returns {Promise<Object>} 测试结果
 */
async function testAiConnection(config) {
  // 检查必要配置
  if (!config.apiBase || !config.apiKey) {
    return {
      success: false,
      message: '缺少必要的配置：API 地址或密钥'
    };
  }

  try {
    const http = require('http');
    const https = require('https');
    const url = require('url');
    
    // 确保 apiBase 正确格式化
    let baseUrl = config.apiBase;
    
    // 移除尾部斜杠
    baseUrl = baseUrl.replace(/\/+$/, '');
    
    // 根据不同的 API 提供商选择合适的测试端点
    let testEndpoint = '/v1/models';
    
    // 检查各种 API 提供商
    if (baseUrl.includes('generativelanguage.googleapis.com')) {
      // Google Gemini OpenAI 兼容端点
      // 对于 Gemini，使用 chat completions 端点进行测试会更可靠
      // 因为 models 端点可能不存在
      console.log('[AI Config] 检测到 Google Gemini API');
      // 简单的连接测试 - 使用一个最小的请求
      testEndpoint = '/models';  // 尝试 models 端点
      // 注意：Gemini 可能没有 models 端点，我们接受 404 作为"连接成功但端点不存在"
    } else if (baseUrl.includes('azure.com')) {
      // Azure OpenAI 使用不同的端点格式
      testEndpoint = '/openai/models?api-version=2023-05-15';
    } else if (baseUrl.includes('/v1')) {
      // 如果 baseUrl 已经包含 /v1，则不需要再添加
      testEndpoint = '/models';
    }
    
    const apiUrl = new URL(baseUrl + testEndpoint);
    const isHttps = apiUrl.protocol === 'https:';
    const client = isHttps ? https : http;
    
    return new Promise((resolve) => {
      const options = {
        hostname: apiUrl.hostname,
        port: apiUrl.port || (isHttps ? 443 : 80),
        path: apiUrl.pathname,
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json',
          'User-Agent': 'DStatus/1.0'
        },
        timeout: 5000
      };
      
      const req = client.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          if (res.statusCode === 200) {
            // 尝试解析响应数据以验证是否真的成功
            try {
              const responseData = JSON.parse(data);
              resolve({
                success: true,
                message: '连接成功',
                details: {
                  statusCode: res.statusCode,
                  provider: config.name,
                  modelsCount: responseData.data ? responseData.data.length : 0
                }
              });
            } catch (parseError) {
              resolve({
                success: true,
                message: '连接成功',
                details: {
                  statusCode: res.statusCode,
                  provider: config.name
                }
              });
            }
          } else if (res.statusCode === 401) {
            // 解析错误响应以获取更详细的信息
            let errorMessage = 'API 密钥无效';
            try {
              const errorData = JSON.parse(data);
              if (errorData.error && errorData.error.message) {
                errorMessage = errorData.error.message;
              }
            } catch (e) {
              // 忽略解析错误
            }
            resolve({
              success: false,
              message: `鉴权失败：${errorMessage}`,
              details: { 
                statusCode: res.statusCode,
                error: errorMessage 
              }
            });
          } else if (res.statusCode === 403) {
            resolve({
              success: false,
              message: '权限不足：请检查 API 密钥权限',
              details: { statusCode: res.statusCode }
            });
          } else if (res.statusCode === 404) {
            // 对于某些 API（如 Gemini），models 端点可能不存在
            // 但这不代表配置错误
            if (baseUrl.includes('generativelanguage.googleapis.com')) {
              // Gemini API 可能没有 models 端点，但这是正常的
              // 我们可以认为连接是成功的，只是端点不存在
              resolve({
                success: true,
                message: '连接成功（Gemini API）',
                details: { 
                  statusCode: res.statusCode,
                  provider: 'Google Gemini',
                  note: 'Gemini API 不提供 models 端点，但连接正常'
                }
              });
            } else {
              resolve({
                success: false,
                message: 'API 端点不存在：请检查 API 地址',
                details: { statusCode: res.statusCode }
              });
            }
          } else if (res.statusCode === 429) {
            resolve({
              success: false,
              message: '请求频率限制：稍后再试',
              details: { statusCode: res.statusCode }
            });
          } else {
            resolve({
              success: false,
              message: `连接失败：HTTP ${res.statusCode}`,
              details: { 
                statusCode: res.statusCode,
                responseData: data.substring(0, 200) // 前200字符用于调试
              }
            });
          }
        });
      });
      
      req.on('error', (error) => {
        resolve({
          success: false,
          message: '网络连接失败：' + error.message,
          details: { error: error.message }
        });
      });
      
      req.on('timeout', () => {
        req.destroy();
        resolve({
          success: false,
          message: '连接超时（5秒）',
          details: { timeout: true }
        });
      });
      
      req.end();
    });
    
  } catch (error) {
    return {
      success: false,
      message: '测试失败：' + error.message,
      details: { error: error.message }
    };
  }
}

module.exports = {
  getAiConfig,
  saveAiConfig,
  testAiConnection
};