/**
 * 统一日志控制器
 * 集成现有的面板设置和日志管理功能
 * 一行代码激活，零侵入集成
 */

const { ConsoleProxy } = require('./console-proxy');
const { logUtils } = require('./log-utils');

class LogController {
    constructor() {
        this.proxy = null;
        this.isInitialized = false;
        this.originalLogLevel = null;
    }

    /**
     * 初始化日志控制器
     * @param {Object} db - 数据库实例
     * @param {Object} options - 配置选项
     */
    async initialize(db, options = {}) {
        if (this.isInitialized) return;

        try {
            // 创建代理实例
            this.proxy = new ConsoleProxy(db, logUtils);
            
            // 激活代理
            this.proxy.activate();
            
            // 绑定设置变更监听
            this.setupSettingsListener(db);
            
            // 输出初始化信息
            const debugMode = await db?.setting?.get('debug') || false;
            console.info(`[日志控制器] 已激活 - Debug模式: ${debugMode ? '启用' : '禁用'}`);
            
            this.isInitialized = true;
        } catch (error) {
            console.error('[日志控制器] 初始化失败:', error.message);
        }
    }

    /**
     * 设置变更监听
     */
    setupSettingsListener(db) {
        // 如果有事件发射器，监听设置变更
        const eventEmitter = global.eventEmitter;
        if (eventEmitter) {
            eventEmitter.on('settings:update', (settings) => {
                if (settings.debug !== undefined) {
                    const debugMode = !!settings.debug;
                    console.info(`[日志控制器] Debug模式已${debugMode ? '启用' : '禁用'}`);
                }
            });
        }
    }

    /**
     * 获取当前日志设置
     */
    async getSettings(db) {
        return {
            debug: await db?.setting?.get('debug') || false,
            logLevel: process.env.LOG_LEVEL || 'INFO',
            consoleOutput: await db?.setting?.get('log_console_output') !== false,
            fileOutput: await db?.setting?.get('log_file_output') !== false
        };
    }

    /**
     * 更新日志设置
     */
    async updateSettings(db, newSettings) {
        try {
            if (newSettings.debug !== undefined) {
                await db.setting.set('debug', newSettings.debug);
                global.debugMode = newSettings.debug;
            }
            
            if (newSettings.consoleOutput !== undefined) {
                await db.setting.set('log_console_output', newSettings.consoleOutput);
            }
            
            if (newSettings.fileOutput !== undefined) {
                await db.setting.set('log_file_output', newSettings.fileOutput);
            }
            
            console.info('[日志控制器] 设置已更新');
            return true;
        } catch (error) {
            console.error('[日志控制器] 更新设置失败:', error.message);
            return false;
        }
    }

    /**
     * 获取日志统计信息
     */
    getStats() {
        if (!this.proxy) return null;
        
        return {
            ...this.proxy.getStats(),
            logFiles: logUtils?.getStats?.() || {}
        };
    }

    /**
     * 清理高频日志（面板功能集成）
     */
    cleanHighFrequencyLogs() {
        try {
            // 重置频率控制器
            if (this.proxy) {
                this.proxy.throttleMap.clear();
                this.proxy.messageCount.clear();
            }
            
            console.info('[日志控制器] 高频日志限制已重置');
            return true;
        } catch (error) {
            console.error('[日志控制器] 清理失败:', error.message);
            return false;
        }
    }

    /**
     * 临时启用调试模式（用于故障排查）
     */
    enableDebugTemporarily(duration = 300000) { // 默认5分钟
        if (!this.originalLogLevel) {
            this.originalLogLevel = process.env.LOG_LEVEL;
        }
        
        process.env.LOG_LEVEL = 'DEBUG';
        console.info(`[日志控制器] 临时启用调试模式 ${duration/1000} 秒`);
        
        setTimeout(() => {
            process.env.LOG_LEVEL = this.originalLogLevel || 'INFO';
            console.info('[日志控制器] 调试模式已恢复');
        }, duration);
    }

    /**
     * 停用日志控制（用于调试）
     */
    deactivate() {
        if (this.proxy) {
            this.proxy.deactivate();
            console.info('[日志控制器] 已停用');
        }
    }
}

// 创建全局实例
const logController = new LogController();

/**
 * 快速激活函数 - 一行代码集成
 * @param {Object} db - 数据库实例
 * @param {Object} options - 配置选项
 */
async function activateLogControl(db, options = {}) {
    await logController.initialize(db, options);
    return logController;
}

module.exports = {
    LogController,
    logController,
    activateLogControl
};