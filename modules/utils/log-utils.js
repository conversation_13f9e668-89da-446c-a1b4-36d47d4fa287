'use strict';

/**
 * 统一日志工具模块
 * 提供轻量化的日志管理功能
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// 智能选择日志目录
let LOG_DIR;
// 1. 优先使用环境变量
if (process.env.DSTATUS_LOG_DIR) {
    LOG_DIR = process.env.DSTATUS_LOG_DIR;
} 
// 2. pkg环境使用系统临时目录
else if (process.pkg) {
    LOG_DIR = path.join(os.tmpdir(), 'dstatus-logs');
} 
// 3. 开发环境使用项目目录
else {
    LOG_DIR = path.join(__dirname, '../../data/logs');
}

// 日志类型配置
const LOG_TYPES = {
    PERFORMANCE: 'performance',
    NOTIFICATION: 'notification',
    SYSTEM: 'system',
    ERROR: 'error',
    ACCESS: 'access'
};

// 默认配置
const DEFAULT_CONFIG = {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    maxFiles: 3, // 保留3个历史版本
    dateFormat: 'YYYY-MM', // 按月分割
    enableRotation: true
};

class LogUtils {
    constructor() {
        this.ensureLogDirectory();
    }

    /**
     * 确保日志目录存在
     */
    ensureLogDirectory() {
        if (!fs.existsSync(LOG_DIR)) {
            fs.mkdirSync(LOG_DIR, { recursive: true });
            console.log(`[日志工具] 创建日志目录: ${LOG_DIR}`);
        }
    }

    /**
     * 获取日志文件路径
     * @param {string} type - 日志类型
     * @param {Date} date - 日期（可选）
     * @returns {string} 日志文件路径
     */
    getLogPath(type, date = new Date()) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        
        if (type === LOG_TYPES.PERFORMANCE) {
            return path.join(LOG_DIR, `${type}.log`);
        } else if (type.includes('-')) {
            // 处理自定义类型（如test-rotation）
            return path.join(LOG_DIR, `${type}.log`);
        } else {
            return path.join(LOG_DIR, `${type}-${year}-${month}.log`);
        }
    }

    /**
     * 写入日志
     * @param {string} type - 日志类型
     * @param {string|Object} content - 日志内容
     * @param {Object} options - 选项
     */
    writeLog(type, content, options = {}) {
        try {
            const config = { ...DEFAULT_CONFIG, ...options };
            const logPath = this.getLogPath(type);
            
            // 格式化日志内容
            const logEntry = this.formatLogEntry(content, type);
            
            // 检查文件大小并轮转
            if (config.enableRotation) {
                this.checkAndRotate(logPath, config);
            }
            
            // 写入日志
            fs.appendFileSync(logPath, logEntry);
        } catch (error) {
            console.error(`[日志工具] 写入日志失败 (${type}):`, error);
        }
    }

    /**
     * 格式化日志条目
     * @param {string|Object} content - 内容
     * @param {string} type - 类型
     * @returns {string} 格式化后的日志条目
     */
    formatLogEntry(content, type) {
        const timestamp = new Date().toISOString();
        
        if (typeof content === 'string') {
            return `${timestamp} [${type.toUpperCase()}] ${content}\n`;
        } else if (typeof content === 'object') {
            return `${JSON.stringify({ timestamp, type, ...content })}\n`;
        }
        
        return `${timestamp} [${type.toUpperCase()}] ${String(content)}\n`;
    }

    /**
     * 检查并轮转日志文件
     * @param {string} filePath - 文件路径
     * @param {Object} config - 配置
     */
    checkAndRotate(filePath, config) {
        if (!fs.existsSync(filePath)) return;
        
        const stats = fs.statSync(filePath);
        if (stats.size <= config.maxFileSize) return;
        
        try {
            const dir = path.dirname(filePath);
            const ext = path.extname(filePath);
            const baseName = path.basename(filePath, ext);
            
            // 移动现有的轮转文件
            for (let i = config.maxFiles - 1; i >= 1; i--) {
                const oldFile = path.join(dir, `${baseName}.${i}${ext}`);
                const newFile = path.join(dir, `${baseName}.${i + 1}${ext}`);
                
                if (fs.existsSync(oldFile)) {
                    if (i === config.maxFiles - 1) {
                        fs.unlinkSync(oldFile);
                    } else {
                        fs.renameSync(oldFile, newFile);
                    }
                }
            }
            
            // 轮转当前文件
            const rotatedFile = path.join(dir, `${baseName}.1${ext}`);
            fs.renameSync(filePath, rotatedFile);
            
            console.log(`[日志工具] 文件轮转: ${path.basename(filePath)}`);
        } catch (error) {
            console.error(`[日志工具] 轮转失败:`, error);
        }
    }

    /**
     * 获取日志文件列表
     * @param {string} type - 日志类型（可选）
     * @returns {Array} 日志文件信息
     */
    getLogFiles(type = null) {
        try {
            const files = fs.readdirSync(LOG_DIR);
            const logFiles = [];
            
            for (const file of files) {
                if (!file.endsWith('.log')) continue;
                if (type && !file.startsWith(type)) continue;
                
                const filePath = path.join(LOG_DIR, file);
                const stats = fs.statSync(filePath);
                
                logFiles.push({
                    name: file,
                    path: filePath,
                    size: stats.size,
                    sizeFormatted: this.formatFileSize(stats.size),
                    modified: stats.mtime,
                    type: this.getLogTypeFromFilename(file)
                });
            }
            
            return logFiles.sort((a, b) => b.modified - a.modified);
        } catch (error) {
            console.error('[日志工具] 获取文件列表失败:', error);
            return [];
        }
    }

    /**
     * 从文件名获取日志类型
     * @param {string} filename - 文件名
     * @returns {string} 日志类型
     */
    getLogTypeFromFilename(filename) {
        if (filename.startsWith('performance')) return LOG_TYPES.PERFORMANCE;
        if (filename.startsWith('notification')) return LOG_TYPES.NOTIFICATION;
        if (filename.startsWith('system')) return LOG_TYPES.SYSTEM;
        if (filename.startsWith('error')) return LOG_TYPES.ERROR;
        if (filename.startsWith('access')) return LOG_TYPES.ACCESS;
        return 'unknown';
    }

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i];
    }

    /**
     * 读取日志内容
     * @param {string} type - 日志类型
     * @param {Object} options - 选项
     * @returns {Array} 日志条目
     */
    readLogs(type, options = {}) {
        try {
            const { month, limit = 100, offset = 0 } = options;
            let logPath;
            
            if (month && type !== LOG_TYPES.PERFORMANCE) {
                const [year, monthNum] = month.split('-');
                const date = new Date(year, monthNum - 1);
                logPath = this.getLogPath(type, date);
            } else {
                logPath = this.getLogPath(type);
            }
            
            if (!fs.existsSync(logPath)) return [];
            
            const content = fs.readFileSync(logPath, 'utf8');
            const lines = content.split('\n').filter(line => line.trim());
            
            // 解析日志条目
            const logs = lines.map(line => {
                try {
                    // 尝试解析JSON格式
                    return JSON.parse(line);
                } catch {
                    // 解析文本格式
                    const match = line.match(/^(\S+)\s+\[(\w+)\]\s+(.+)$/);
                    if (match) {
                        return {
                            timestamp: match[1],
                            type: match[2].toLowerCase(),
                            message: match[3]
                        };
                    }
                    return { timestamp: new Date().toISOString(), message: line };
                }
            });
            
            // 排序并分页
            logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
            return logs.slice(offset, offset + limit);
        } catch (error) {
            console.error(`[日志工具] 读取日志失败 (${type}):`, error);
            return [];
        }
    }

    /**
     * 清理日志文件
     * @param {string} type - 日志类型（可选）
     */
    cleanLogs(type = null) {
        try {
            const files = this.getLogFiles(type);
            
            for (const file of files) {
                fs.writeFileSync(file.path, '');
                console.log(`[日志工具] 清理文件: ${file.name}`);
            }
        } catch (error) {
            console.error('[日志工具] 清理日志失败:', error);
        }
    }

    /**
     * 获取日志统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const files = this.getLogFiles();
        const stats = {
            totalFiles: files.length,
            totalSize: files.reduce((sum, file) => sum + file.size, 0),
            files: files,
            byType: {}
        };
        
        // 按类型分组
        for (const file of files) {
            if (!stats.byType[file.type]) {
                stats.byType[file.type] = { count: 0, size: 0, files: [] };
            }
            stats.byType[file.type].count++;
            stats.byType[file.type].size += file.size;
            stats.byType[file.type].files.push(file);
        }
        
        stats.totalSizeFormatted = this.formatFileSize(stats.totalSize);
        return stats;
    }
}

// 导出单例实例和类型常量
module.exports = {
    logUtils: new LogUtils(),
    LOG_TYPES,
    LOG_DIR
}; 