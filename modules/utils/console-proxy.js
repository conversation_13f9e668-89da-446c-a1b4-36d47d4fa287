/**
 * 统一日志控制代理
 * 拦截所有console输出，根据面板设置控制日志级别
 * 最小化修改，向后兼容所有现有代码
 */

const path = require('path');
const fs = require('fs');

class ConsoleProxy {
    constructor(db, logUtils) {
        this.db = db;
        this.logUtils = logUtils;
        this.originalConsole = {};
        this.isActive = false;
        this.isUpdatingCache = false; // 防止重复更新缓存
        
        // 设置缓存
        this.settingsCache = {
            debug: false,
            log_console_output: true,
            log_file_output: true,
            lastUpdate: 0
        };
        this.cacheExpiry = 30000; // 30秒缓存过期
        
        // 保存原始console方法
        this.originalConsole.log = console.log.bind(console);
        this.originalConsole.info = console.info.bind(console);
        this.originalConsole.warn = console.warn.bind(console);
        this.originalConsole.error = console.error.bind(console);
        this.originalConsole.debug = console.debug.bind(console);
        
        // 频率控制
        this.throttleMap = new Map();
        this.messageCount = new Map();
        
        // 初始化设置缓存
        this.updateSettingsCache();
    }

    /**
     * 更新设置缓存
     */
    async updateSettingsCache() {
        try {
            if (this.db?.setting) {
                this.settingsCache.debug = await this.db.setting.get('debug') || false;
                this.settingsCache.log_console_output = await this.db.setting.get('log_console_output') !== false;
                this.settingsCache.log_file_output = await this.db.setting.get('log_file_output') !== false;
                this.settingsCache.lastUpdate = Date.now();
            }
        } catch (error) {
            // 只在首次失败或调试模式下输出错误
            if (!this.hasLoggedCacheError || this.settingsCache.debug) {
                this.originalConsole.error('[日志代理] 更新设置缓存失败:', error.message);
                this.hasLoggedCacheError = true;
            }
            // 延长缓存时间，减少重试频率
            this.settingsCache.lastUpdate = Date.now() - (this.cacheExpiry / 2);
        }
    }

    /**
     * 检查并更新缓存
     */
    checkCacheExpiry() {
        // 如果正在更新，直接返回
        if (this.isUpdatingCache) return;
        
        const now = Date.now();
        if (now - this.settingsCache.lastUpdate > this.cacheExpiry) {
            // 标记正在更新，防止重复调用
            this.isUpdatingCache = true;
            
            // 异步更新缓存，不等待结果
            this.updateSettingsCache()
                .catch(() => {
                    // 静默处理错误
                })
                .finally(() => {
                    // 更新完成后重置标志
                    this.isUpdatingCache = false;
                });
        }
    }

    /**
     * 激活日志代理
     */
    activate() {
        if (this.isActive) return;
        
        console.log = this.createProxyMethod('info');
        console.info = this.createProxyMethod('info');
        console.warn = this.createProxyMethod('warn');
        console.error = this.createProxyMethod('error');
        console.debug = this.createProxyMethod('debug');
        
        this.isActive = true;
        this.originalConsole.info('[日志代理] 统一日志控制已激活');
    }

    /**
     * 创建代理方法
     */
    createProxyMethod(level) {
        return (...args) => {
            // 获取调用栈信息
            const stack = new Error().stack;
            const caller = this.getCallerInfo(stack);
            
            // 格式化消息
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            // 检查是否应该输出
            if (this.shouldLog(level, message, caller)) {
                // 控制台输出（如果启用）
                if (this.shouldOutputToConsole(level)) {
                    this.originalConsole[level === 'info' ? 'log' : level](...args);
                }
                
                // 文件输出（始终记录ERROR，其他根据设置）
                if (this.shouldOutputToFile(level)) {
                    this.writeToFile(level, message, caller);
                }
                
                // 更新计数器
                this.updateMessageCount(level, caller);
            }
        };
    }

    /**
     * 判断是否应该记录日志
     */
    shouldLog(level, message, caller) {
        // ERROR级别始终记录
        if (level === 'error') return true;
        
        // 检查缓存过期
        this.checkCacheExpiry();
        
        // 获取面板设置
        const debugMode = this.settingsCache.debug;
        const logLevel = process.env.LOG_LEVEL || (debugMode ? 'DEBUG' : 'WARN');
        
        // 根据级别判断 - 默认显示WARN及以上
        const levelPriority = { debug: 0, info: 1, warn: 2, error: 3 };
        const currentPriority = levelPriority[logLevel.toLowerCase()] || 2; // 默认WARN级别
        const messagePriority = levelPriority[level] || 1;
        
        // 检查频率限制
        if (this.isHighFrequencyMessage(message, caller)) {
            return this.checkThrottle(message, caller);
        }
        
        return messagePriority >= currentPriority;
    }

    /**
     * 是否输出到控制台
     */
    shouldOutputToConsole(level) {
        this.checkCacheExpiry();
        const consoleOutput = this.settingsCache.log_console_output;
        if (consoleOutput === false) return false;
        
        // 在生产环境可能需要减少控制台输出
        if (process.env.NODE_ENV === 'production' && level === 'debug') {
            return false;
        }
        
        return true;
    }

    /**
     * 是否输出到文件
     */
    shouldOutputToFile(level) {
        this.checkCacheExpiry();
        const fileOutput = this.settingsCache.log_file_output;
        if (fileOutput === false) return false;
        
        return true;
    }

    /**
     * 写入文件
     */
    writeToFile(level, message, caller) {
        try {
            let logType = 'SYSTEM';
            
            // 根据来源和内容智能分类
            if (caller.includes('reporting') || message.includes('上报')) {
                logType = 'NOTIFICATION';
            } else if (caller.includes('performance') || message.includes('性能')) {
                logType = 'PERFORMANCE';
            } else if (level === 'error') {
                logType = 'ERROR';
            } else if (caller.includes('api') || message.includes('API')) {
                logType = 'ACCESS';
            }
            
            // 格式化日志内容
            const logContent = {
                timestamp: new Date().toISOString(),
                level: level.toUpperCase(),
                source: caller,
                message: message
            };
            
            this.logUtils?.writeLog(logType, logContent);
        } catch (error) {
            // 避免日志写入错误导致无限循环
            this.originalConsole.error('[日志代理] 写入文件失败:', error.message);
        }
    }

    /**
     * 获取调用者信息
     */
    getCallerInfo(stack) {
        const lines = stack.split('\n');
        // 跳过代理方法本身，找到真实调用者
        for (let i = 3; i < lines.length; i++) {
            const line = lines[i];
            if (line && !line.includes('console-proxy.js')) {
                const match = line.match(/at\s+(.+?)(?:\s+\((.+?)\)|\s+(.+))$/);
                if (match) {
                    const location = match[2] || match[3];
                    if (location && location.includes('/dstatus/')) {
                        return location.split('/dstatus/')[1] || 'unknown';
                    }
                }
            }
        }
        return 'unknown';
    }

    /**
     * 检查是否为高频消息
     */
    isHighFrequencyMessage(message, caller) {
        // 识别可能的高频消息模式
        const highFreqPatterns = [
            /WebSocket.*连接/,
            /心跳.*检查/,
            /数据.*更新/,
            /状态.*上报/,
            /TCPing.*测试/,
            /性能.*监控/
        ];
        
        return highFreqPatterns.some(pattern => pattern.test(message)) ||
               caller.includes('heartbeat') ||
               caller.includes('scheduler') ||
               caller.includes('websocket');
    }

    /**
     * 频率限制检查
     */
    checkThrottle(message, caller) {
        const key = `${caller}:${message.substring(0, 50)}`;
        const now = Date.now();
        const throttleWindow = 60000; // 1分钟
        const maxCount = 5; // 每分钟最多5条相同消息
        
        const throttleData = this.throttleMap.get(key) || { count: 0, firstTime: now };
        
        // 重置时间窗口
        if (now - throttleData.firstTime > throttleWindow) {
            throttleData.count = 0;
            throttleData.firstTime = now;
        }
        
        throttleData.count++;
        this.throttleMap.set(key, throttleData);
        
        return throttleData.count <= maxCount;
    }

    /**
     * 更新消息计数
     */
    updateMessageCount(level, caller) {
        const key = `${level}:${caller}`;
        const count = this.messageCount.get(key) || 0;
        this.messageCount.set(key, count + 1);
    }

    /**
     * 获取统计信息
     */
    getStats() {
        this.checkCacheExpiry();
        return {
            isActive: this.isActive,
            messageCount: Object.fromEntries(this.messageCount),
            throttleActive: this.throttleMap.size,
            settings: {
                debugMode: this.settingsCache.debug,
                logLevel: process.env.LOG_LEVEL || 'INFO'
            }
        };
    }

    /**
     * 停用代理（用于调试）
     */
    deactivate() {
        if (!this.isActive) return;
        
        console.log = this.originalConsole.log;
        console.info = this.originalConsole.info;
        console.warn = this.originalConsole.warn;
        console.error = this.originalConsole.error;
        console.debug = this.originalConsole.debug;
        
        this.isActive = false;
        this.originalConsole.info('[日志代理] 统一日志控制已停用');
    }
}

module.exports = { ConsoleProxy };