'use strict';

/**
 * 统一日志管理器
 * 提供日志级别控制和日志聚合功能
 */

class Logger {
    constructor() {
        // 日志级别定义
        this.LEVELS = {
            DEBUG: 0,
            INFO: 1,
            WARN: 2,
            ERROR: 3
        };

        // 默认日志级别（从环境变量读取，默认为ERROR）
        this.currentLevel = this.LEVELS[process.env.LOG_LEVEL || 'ERROR'] || this.LEVELS.ERROR;

        // 日志聚合缓存
        this.aggregatedLogs = new Map();
        
        // 定期输出聚合日志
        this.startAggregationTimer();

        // 节点错误统计
        this.nodeErrors = new Map();
        
        // TCPing保存统计
        this.tcpingStats = {
            saveCount: 0,
            lastReportTime: Date.now(),
            targets: new Set()
        };
    }

    /**
     * 设置日志级别
     */
    setLevel(level) {
        if (this.LEVELS[level] !== undefined) {
            this.currentLevel = this.LEVELS[level];
            console.log(`[Logger] 日志级别已设置为: ${level}`);
        }
    }

    /**
     * 获取当前日志级别
     */
    getLevel() {
        return Object.keys(this.LEVELS).find(key => this.LEVELS[key] === this.currentLevel);
    }

    /**
     * 检查是否应该输出该级别的日志
     */
    shouldLog(level) {
        return this.LEVELS[level] >= this.currentLevel;
    }

    /**
     * DEBUG级别日志
     */
    debug(...args) {
        if (this.shouldLog('DEBUG')) {
            console.log('[DEBUG]', ...args);
        }
    }

    /**
     * INFO级别日志
     */
    info(...args) {
        if (this.shouldLog('INFO')) {
            console.log(...args);
        }
    }

    /**
     * WARN级别日志
     */
    warn(...args) {
        if (this.shouldLog('WARN')) {
            console.warn('[WARN]', ...args);
        }
    }

    /**
     * ERROR级别日志
     */
    error(...args) {
        if (this.shouldLog('ERROR')) {
            console.error('[ERROR]', ...args);
        }
    }

    /**
     * 记录TCPing数据保存（聚合输出）
     */
    logTcpingSave(targetId) {
        this.tcpingStats.saveCount++;
        this.tcpingStats.targets.add(targetId);
        
        // 每小时输出一次统计
        const now = Date.now();
        if (now - this.tcpingStats.lastReportTime > 3600000) { // 1小时
            this.info(`[TCPing数据] 过去1小时保存了 ${this.tcpingStats.saveCount} 条记录，涉及 ${this.tcpingStats.targets.size} 个目标`);
            this.tcpingStats.saveCount = 0;
            this.tcpingStats.targets.clear();
            this.tcpingStats.lastReportTime = now;
        }
    }

    /**
     * 记录节点错误（聚合输出）
     */
    logNodeError(nodeName, error) {
        const key = `${nodeName}_${error}`;
        
        if (!this.nodeErrors.has(key)) {
            this.nodeErrors.set(key, {
                nodeName,
                error,
                count: 0,
                firstTime: Date.now(),
                lastTime: Date.now()
            });
        }
        
        const errorInfo = this.nodeErrors.get(key);
        errorInfo.count++;
        errorInfo.lastTime = Date.now();
        
        // 只在第一次、第10次、第50次、第100次时输出
        const shouldLog = errorInfo.count === 1 || 
                          errorInfo.count === 10 || 
                          errorInfo.count === 50 || 
                          errorInfo.count === 100 ||
                          (errorInfo.count > 100 && errorInfo.count % 100 === 0);
        
        if (shouldLog) {
            this.warn(`[节点错误] ${nodeName} 请求失败: ${error} (第${errorInfo.count}次)`);
        }
    }

    /**
     * 记录性能监控（只在超过阈值时输出）
     */
    logPerformance(cpuUsage, memoryUsage) {
        const CPU_THRESHOLD = 80;  // CPU使用率阈值
        const MEMORY_THRESHOLD = 95; // 内存使用率阈值（提高到95%）
        const CRITICAL_MEMORY_THRESHOLD = 99; // 严重内存阈值
        
        const now = Date.now();
        const lastWarnKey = 'performanceWarning';
        
        // 检查上次警告时间
        if (!this.lastWarnings) {
            this.lastWarnings = new Map();
        }
        
        const lastWarnTime = this.lastWarnings.get(lastWarnKey) || 0;
        const shouldWarn = now - lastWarnTime > 300000; // 5分钟内只警告一次
        
        if (memoryUsage > CRITICAL_MEMORY_THRESHOLD && shouldWarn) {
            // 严重警告始终输出
            this.warn(`[性能监控严重警告] 内存使用率极高: ${memoryUsage.toFixed(2)}%`);
            this.lastWarnings.set(lastWarnKey, now);
        } else if ((cpuUsage > CPU_THRESHOLD || memoryUsage > MEMORY_THRESHOLD) && shouldWarn) {
            const warnings = [];
            if (cpuUsage > CPU_THRESHOLD) warnings.push(`CPU使用率过高: ${cpuUsage.toFixed(2)}%`);
            if (memoryUsage > MEMORY_THRESHOLD) warnings.push(`内存使用率过高: ${memoryUsage.toFixed(2)}%`);
            
            this.warn(`[性能监控警告] ${warnings.join(', ')}`);
            this.lastWarnings.set(lastWarnKey, now);
        } else {
            // 正常情况下只在DEBUG级别输出
            this.debug(`[性能监控] CPU使用率: ${cpuUsage.toFixed(2)}%, 内存使用率: ${memoryUsage.toFixed(2)}%`);
        }
    }

    /**
     * 聚合日志（用于相同消息的批量输出）
     */
    aggregate(category, message, data = {}) {
        const key = `${category}:${message}`;
        
        if (!this.aggregatedLogs.has(key)) {
            this.aggregatedLogs.set(key, {
                category,
                message,
                count: 0,
                firstTime: Date.now(),
                lastTime: Date.now(),
                data: []
            });
        }
        
        const log = this.aggregatedLogs.get(key);
        log.count++;
        log.lastTime = Date.now();
        if (data) log.data.push(data);
    }

    /**
     * 启动聚合日志定时器
     */
    startAggregationTimer() {
        // 每5分钟输出一次聚合日志
        setInterval(() => {
            this.flushAggregatedLogs();
            this.flushNodeErrors();
        }, 300000); // 5分钟
    }

    /**
     * 输出聚合日志
     */
    flushAggregatedLogs() {
        if (this.aggregatedLogs.size === 0) return;
        
        const now = Date.now();
        this.info('[日志汇总] 过去5分钟的聚合日志:');
        
        for (const [key, log] of this.aggregatedLogs) {
            const duration = (now - log.firstTime) / 1000;
            this.info(`  - [${log.category}] ${log.message}: 发生 ${log.count} 次 (${duration.toFixed(1)}秒内)`);
        }
        
        this.aggregatedLogs.clear();
    }

    /**
     * 输出节点错误汇总
     */
    flushNodeErrors() {
        if (this.nodeErrors.size === 0) return;
        
        const now = Date.now();
        const errorSummary = new Map();
        
        // 按节点汇总错误
        for (const [key, errorInfo] of this.nodeErrors) {
            if (!errorSummary.has(errorInfo.nodeName)) {
                errorSummary.set(errorInfo.nodeName, []);
            }
            errorSummary.get(errorInfo.nodeName).push(errorInfo);
        }
        
        this.info('[节点错误汇总] 过去5分钟的节点错误:');
        
        for (const [nodeName, errors] of errorSummary) {
            const totalErrors = errors.reduce((sum, e) => sum + e.count, 0);
            this.info(`  - ${nodeName}: 共 ${totalErrors} 次错误`);
            
            // 只显示前3个最常见的错误
            errors.sort((a, b) => b.count - a.count);
            errors.slice(0, 3).forEach(error => {
                this.info(`    · ${error.error} (${error.count}次)`);
            });
        }
        
        // 清理超过5分钟的错误记录
        for (const [key, errorInfo] of this.nodeErrors) {
            if (now - errorInfo.lastTime > 300000) {
                this.nodeErrors.delete(key);
            }
        }
    }
}

// 创建全局日志实例
const logger = new Logger();

// 导出日志实例和便捷方法
module.exports = {
    logger,
    debug: (...args) => logger.debug(...args),
    info: (...args) => logger.info(...args),
    warn: (...args) => logger.warn(...args),
    error: (...args) => logger.error(...args),
    setLogLevel: (level) => logger.setLevel(level),
    logTcpingSave: (targetId) => logger.logTcpingSave(targetId),
    logNodeError: (nodeName, error) => logger.logNodeError(nodeName, error),
    logPerformance: (cpuUsage, memoryUsage) => logger.logPerformance(cpuUsage, memoryUsage)
};