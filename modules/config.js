/**
 * 统一配置访问接口
 * 为现有代码提供简洁的配置访问方式，逐步替换硬编码配置
 * 
 * 使用方式:
 * const config = require('./modules/config');
 * const maxConnections = config.websocket.maxConnections;  // 替代 MAX_WEBSOCKET_CONNECTIONS
 * const updateInterval = config.websocket.updateInterval;   // 替代 UPDATE_INTERVAL
 */

const configManager = require('./config-manager');

// 配置代理对象 - 提供点语法访问
const createConfigProxy = () => {
    const cache = new Map();
    
    return new Proxy({}, {
        get(target, prop) {
            if (typeof prop === 'string') {
                // 如果是配置分组，返回分组代理
                if (configManager.manager.initialized || configManager.manager.initPromise) {
                    return new Proxy({}, {
                        get(groupTarget, groupProp) {
                            if (typeof groupProp === 'string') {
                                const configPath = `${prop}.${groupProp}`;
                                
                                // 返回 Promise 或同步值，根据初始化状态
                                if (configManager.manager.initialized) {
                                    // 已初始化，可以同步获取缓存值
                                    const cached = configManager.manager.configCache.get(configPath);
                                    if (cached !== undefined) {
                                        return cached;
                                    }
                                }
                                
                                // 未初始化或无缓存，返回 getter 函数
                                return configManager.get(configPath);
                            }
                            return undefined;
                        }
                    });
                }
                
                // 未初始化时返回空对象
                return new Proxy({}, {
                    get() { return undefined; }
                });
            }
            return undefined;
        }
    });
};

// 同步配置访问函数（用于已缓存的配置）
const getSync = (path) => {
    if (!configManager.manager.initialized) {
        console.warn(`[配置] 尚未初始化，无法同步获取配置: ${path}`);
        return undefined;
    }
    
    const cached = configManager.manager.configCache.get(path);
    return cached;
};

// 异步配置访问函数
const getAsync = (path) => {
    return configManager.get(path);
};

// 配置常量映射（向后兼容）
const LEGACY_CONSTANTS = {
    // WebSocket 常量
    MAX_WEBSOCKET_CONNECTIONS: () => getSync('websocket.maxConnections') || 100,
    UPDATE_INTERVAL: () => getSync('websocket.updateInterval') || 5000,
    MAX_CONNECTIONS_PER_IP: () => getSync('websocket.maxConnectionsPerIp') || 30,
    SERVER_CACHE_DURATION: () => getSync('websocket.serverCacheDuration') || 300000,
    REQUEST_LIMIT_WINDOW: () => getSync('websocket.requestLimitWindow') || 1000,
    ACTIVE_MODE_TIMEOUT: () => getSync('websocket.activeModeTimeout') || 300000,
    
    // 批处理常量
    BATCH_SIZE: () => getSync('batch.size') || 2,
    BATCH_DELAY: () => getSync('batch.delay') || 50,
    SAFETY_RATIO: () => getSync('batch.safetyRatio') || 0.6,
    
    // 监控常量
    POLLING_INTERVAL: () => getSync('monitoring.pollingInterval') || 3000,
    
    // 网络常量
    NETWORK_TIMEOUT: () => getSync('network.timeout') || 15000,
    
    // 缓存常量
    TRAFFIC_CACHE_TTL: () => getSync('cache.trafficCacheTtl') || 60000,
    DATA_RETENTION_TTL: () => getSync('cache.dataRetentionTtl') || 3600000,
    QUERY_MONITORING_INTERVAL: () => getSync('cache.queryMonitoringInterval') || 60000,
    
    // 安全冷却常量
    FAIL_THRESHOLD: () => getSync('security.failThreshold') || 3,
    INITIAL_COOLING_TIME: () => getSync('security.initialCoolingTime') || 5,
    MAX_COOLING_TIME: () => getSync('security.maxCoolingTime') || 60,
    COOLING_FACTOR: () => getSync('security.coolingFactor') || 2,
    
    // 服务器常量
    LICENSE_SERVER_URL: () => getSync('services.licenseServerUrl') || 'https://dstatus_api.vps.mom',
    USER_FRONTEND_URL: () => getSync('services.userFrontendUrl') || 'https://client.vps.mom/',
    
    // 数据保留常量
    ARCHIVE_HOURS: () => getSync('retention.archiveHours') || 3,
    MINUTE_DAYS: () => getSync('retention.minuteDays') || 14,
    HOUR_DAYS: () => getSync('retention.hourDays') || 90
};

// 创建配置代理实例
const configProxy = createConfigProxy();

// 主配置对象
const config = {
    // 配置代理 - 支持点语法访问
    ...configProxy,
    
    // 核心方法
    init: configManager.init,
    get: configManager.get,
    set: configManager.set,
    getAll: configManager.getAll,
    clearCache: configManager.clearCache,
    healthCheck: configManager.healthCheck,
    
    // 同步访问方法（用于性能关键场景）
    sync: {
        get: getSync,
        
        // 常用配置的快速访问
        websocket: {
            get maxConnections() { return getSync('websocket.maxConnections') || 100; },
            get updateInterval() { return getSync('websocket.updateInterval') || 5000; },
            get maxConnectionsPerIp() { return getSync('websocket.maxConnectionsPerIp') || 30; }
        },
        
        batch: {
            get size() { return getSync('batch.size') || 2; },
            get delay() { return getSync('batch.delay') || 50; },
            get safetyRatio() { return getSync('batch.safetyRatio') || 0.6; },
            get bulkInsertEnabled() { return getSync('batch.bulkInsertEnabled') === true; }
        },
        
        monitoring: {
            get pollingInterval() { return getSync('monitoring.pollingInterval') || 3000; },
            get archiveInterval() { return getSync('monitoring.archiveInterval') || 4000; },
            get enablePerformanceLog() { return getSync('monitoring.enablePerformanceLog') === true; }
        },
        
        logging: {
            get level() { return getSync('logging.level') || 'WARN'; },
            get console() { return getSync('logging.console') !== false; },
            get aggregation() { return getSync('logging.aggregation') !== false; }
        },
        
        system: {
            get debug() { return getSync('system.debug') === true; },
            get logLevel() { return getSync('system.logLevel') || 'WARN'; },
            get environment() { return getSync('system.environment') || 'production'; }
        },
        
        cache: {
            get dataRetentionTtl() { return getSync('cache.dataRetentionTtl') || 3600000; },
            get queryMonitoringInterval() { return getSync('cache.queryMonitoringInterval') || 60000; }
        },
        
        security: {
            get failThreshold() { return getSync('security.failThreshold') || 3; },
            get initialCoolingTime() { return getSync('security.initialCoolingTime') || 5; },
            get maxCoolingTime() { return getSync('security.maxCoolingTime') || 60; },
            get coolingFactor() { return getSync('security.coolingFactor') || 2; }
        },
        
        services: {
            get licenseServerUrl() { return getSync('services.licenseServerUrl') || 'https://dstatus_api.vps.mom'; },
            get userFrontendUrl() { return getSync('services.userFrontendUrl') || 'https://client.vps.mom/'; }
        },
        
        server: {
            get port() { return getSync('server.port') || 3001; },
            get host() { return getSync('server.host') || '0.0.0.0'; }
        }
    },
    
    // 异步访问方法
    async: {
        get: getAsync,
        
        websocket: {
            maxConnections: () => getAsync('websocket.maxConnections'),
            updateInterval: () => getAsync('websocket.updateInterval'),
            maxConnectionsPerIp: () => getAsync('websocket.maxConnectionsPerIp')
        },
        
        batch: {
            size: () => getAsync('batch.size'),
            delay: () => getAsync('batch.delay'),
            safetyRatio: () => getAsync('batch.safetyRatio'),
            bulkInsertEnabled: () => getAsync('batch.bulkInsertEnabled')
        },
        
        monitoring: {
            pollingInterval: () => getAsync('monitoring.pollingInterval'),
            archiveInterval: () => getAsync('monitoring.archiveInterval'),
            enablePerformanceLog: () => getAsync('monitoring.enablePerformanceLog')
        }
    },
    
    // 向后兼容的常量
    constants: new Proxy(LEGACY_CONSTANTS, {
        get(target, prop) {
            if (prop in target && typeof target[prop] === 'function') {
                return target[prop]();
            }
            return undefined;
        }
    }),
    
    // 工具方法
    utils: {
        // 等待配置初始化完成
        waitForInit: async (timeout = 5000) => {
            if (configManager.manager.initialized) {
                return true;
            }
            
            return new Promise((resolve, reject) => {
                const timer = setTimeout(() => {
                    reject(new Error('[配置] 初始化超时'));
                }, timeout);
                
                configManager.manager.once('initialized', () => {
                    clearTimeout(timer);
                    resolve(true);
                });
            });
        },
        
        // 检查配置是否可用
        isAvailable: (path) => {
            return getSync(path) !== undefined;
        },
        
        // 获取配置描述
        getDescription: (path) => {
            const [group, key] = path.split('.');
            const { CONFIG_SCHEMA } = require('../config/global-config');
            return CONFIG_SCHEMA[group]?.[key]?.description || '无描述';
        }
    }
};

// 配置变更事件转发
configManager.on('configChanged', (event) => {
    console.log(`[配置] 配置已更新: ${event.path} = ${event.value}`);
});

// 导出配置对象
module.exports = config;

/**
 * 使用示例:
 * 
 * // 异步方式 (推荐用于初始化阶段)
 * const maxConnections = await config.get('websocket.maxConnections');
 * 
 * // 同步方式 (推荐用于性能关键场景)
 * const maxConnections = config.sync.websocket.maxConnections;
 * 
 * // 点语法方式 (需要 await，适合少量使用)
 * const maxConnections = await config.websocket.maxConnections;
 * 
 * // 向后兼容方式
 * const maxConnections = config.constants.MAX_WEBSOCKET_CONNECTIONS;
 */