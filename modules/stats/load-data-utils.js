/**
 * 负载数据处理工具模块
 * 
 * 提供负载数据的提取、处理和批处理相关的工具函数
 * 
 * @module load-data-utils
 */

/**
 * 从统计数据中提取服务器负载数据
 * 
 * @param {string} sid - 服务器ID
 * @param {Object} stats - 统计数据对象
 * @returns {Object|null} 负载数据对象，如果数据无效则返回null
 */
function extractServerLoadData(sid, stats) {
    const stat = stats[sid];
    
    if (!stat || !stat.stat || stat.stat === -1) {
        return null;
    }
    
    const cpu = stat.stat.cpu.multi * 100;
    const mem = stat.stat.mem.virtual.usedPercent;
    const swap = stat.stat.mem.swap.usedPercent;
    const ibw = stat.stat.net.delta.in;
    const obw = stat.stat.net.delta.out;
    
    // 检查数据有效性 - 至少有一个指标大于0
    const hasValidData = cpu > 0 || mem > 0 || swap > 0;
    
    if (!hasValidData) {
        return null;
    }
    
    return { sid, cpu, mem, swap, ibw, obw };
}

/**
 * 处理单个服务器批次的负载数据保存
 * 
 * @param {Array} batch - 服务器批次数组
 * @param {Object} stats - 统计数据对象
 * @param {Function} saveLoadToArchive - 保存函数
 * @returns {Promise<Array>} 处理结果数组
 */
async function processBatch(batch, stats, saveLoadToArchive) {
    const batchPromises = batch.map(async ({sid}) => {
        const loadData = extractServerLoadData(sid, stats);
        
        if (!loadData) {
            return { success: false, sid };
        }
        
        try {
            await saveLoadToArchive(
                loadData.sid, 
                loadData.cpu, 
                loadData.mem, 
                loadData.swap, 
                loadData.ibw, 
                loadData.obw
            );
            return { success: true, sid };
        } catch (error) {
            console.error(`[监控] 保存服务器 ${sid} 数据失败:`, error.message);
            return { success: false, sid, error: error.message };
        }
    });
    
    return await Promise.all(batchPromises);
}

/**
 * 批处理服务器数据的通用逻辑
 * 
 * @param {Object} options - 批处理选项
 * @param {Array} options.servers - 服务器列表
 * @param {number} options.batchSize - 批大小
 * @param {number} options.batchDelay - 批延迟(ms)
 * @param {number} options.safeExecutionTime - 安全执行时间限制(ms)
 * @param {number} options.startTime - 开始时间戳
 * @param {Object} options.stats - 统计数据对象
 * @param {Function} options.saveLoadToArchive - 保存函数
 * @param {string} [options.mode=''] - 处理模式名称（用于日志）
 * @param {number} [options.errorThreshold=5] - 错误阈值
 * @returns {Promise<Object>} 处理结果统计
 */
async function processBatchesWithProgress(options) {
    const {
        servers,
        batchSize,
        batchDelay,
        safeExecutionTime,
        startTime,
        stats,
        saveLoadToArchive,
        mode = '',
        errorThreshold = 5
    } = options;
    
    let savedCount = 0;
    let processedCount = 0;
    let errorCount = 0;
    const errors = [];
    
    for (let i = 0; i < servers.length; i += batchSize) {
        // 检查是否接近执行时间限制
        const currentElapsed = Date.now() - startTime;
        if (currentElapsed > safeExecutionTime) {
            console.warn(`[监控] ⚠️ 已达到安全执行时间(${safeExecutionTime}ms)，提前结束。已处理 ${processedCount}/${servers.length} 个服务器`);
            break;
        }
        
        const batch = servers.slice(i, i + batchSize);
        const batchNumber = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(servers.length / batchSize);
        
        const modePrefix = mode ? `[${mode}] ` : '';
        console.log(`[监控] ${modePrefix}处理第 ${batchNumber}/${totalBatches} 批，包含 ${batch.length} 个服务器`);
        const batchStartTime = Date.now();
        
        // 处理当前批次
        const results = await processBatch(batch, stats, saveLoadToArchive);
        const successResults = results.filter(r => r.success);
        const failureResults = results.filter(r => !r.success);
        
        savedCount += successResults.length;
        processedCount += batch.length;
        errorCount += failureResults.length;
        
        // 收集错误信息
        failureResults.forEach(r => {
            if (r.error) {
                errors.push({ sid: r.sid, error: r.error });
            }
        });
        
        // 检查错误阈值
        if (errorCount >= errorThreshold && errorCount === errorThreshold) {
            console.error(`[监控] ⚠️ 错误数量达到阈值(${errorCount}/${errorThreshold})，可能存在系统性问题`);
        }
        
        const batchElapsed = Date.now() - batchStartTime;
        console.log(`[监控] ${modePrefix}第 ${batchNumber} 批处理完成，耗时 ${batchElapsed}ms`);
        
        // 批次处理完成后，让出事件循环
        if (i + batchSize < servers.length) {
            await new Promise(resolve => setImmediate(resolve));
            
            // 增加延迟以改善响应性
            if (batchDelay > 0) {
                await new Promise(resolve => setTimeout(resolve, batchDelay));
            }
        }
    }
    
    return { savedCount, processedCount, errorCount, errors };
}

module.exports = {
    extractServerLoadData,
    processBatch,
    processBatchesWithProgress
};