/**
 * 批量计算优化模块
 * 将原本的逐个服务器处理改为批量处理，减少数据库往返
 */

/**
 * 批量更新流量数据
 * @param {Array} updates - [{sid, traffic: [in, out]}]
 * @param {Object} db - 数据库实例
 */
async function batchUpdateLt(updates, db) {
    if (!updates.length) return;
    
    const client = await db.adapter.beginTransaction();
    try {
        // 使用 CASE WHEN 批量更新
        const cases = updates.map((u, i) => 
            `WHEN sid = $${i*2+1} THEN $${i*2+2}::jsonb`
        ).join(' ');
        
        const params = [];
        updates.forEach(u => {
            params.push(u.sid, JSON.stringify(u.traffic));
        });
        
        const sql = `
            UPDATE lt 
            SET traffic = CASE ${cases} END
            WHERE sid IN (${updates.map((_, i) => `$${i*2+1}`).join(',')})
        `;
        
        await client.query(sql, params);
        await db.adapter.commitTransaction(client);
    } catch (error) {
        await db.adapter.rollbackTransaction(client);
        throw error;
    }
}

/**
 * 批量添加流量增量
 * @param {Array} updates - [{sid, delta: [in, out]}]
 * @param {Object} db - 数据库实例
 */
async function batchAddTraffic(updates, db) {
    if (!updates.length) return;
    
    const client = await db.adapter.beginTransaction();
    try {
        // 批量更新流量数据
        for (const update of updates) {
            await client.query(`
                UPDATE traffic 
                SET hs = jsonb_set(
                    COALESCE(hs, '[]'::jsonb),
                    ARRAY[jsonb_array_length(COALESCE(hs, '[]'::jsonb))::text],
                    $2::jsonb
                )
                WHERE sid = $1
            `, [update.sid, JSON.stringify(update.delta)]);
        }
        
        await db.adapter.commitTransaction(client);
    } catch (error) {
        await db.adapter.rollbackTransaction(client);
        throw error;
    }
}

/**
 * 优化后的 calc 函数
 * 使用批量操作减少数据库访问
 */
async function optimizedCalc(db, stats) {
    try {
        const servers = await db.getServers();
        if (!servers.length) return;
        
        // 批量获取所有 lt 数据
        const sids = servers.map(s => s.sid);
        const ltData = await db.all(
            `SELECT sid, traffic FROM lt WHERE sid = ANY($1)`,
            [sids]
        );
        
        // 创建映射表
        const ltMap = new Map();
        ltData.forEach(item => {
            ltMap.set(item.sid, item.traffic || [0, 0]);
        });
        
        // 收集所有更新
        const ltUpdates = [];
        const trafficUpdates = [];
        
        for (const server of servers) {
            const { sid } = server;
            const stat = stats[sid];
            
            if (!stat || !stat.stat || stat.stat === -1) continue;
            
            // 验证网络数据
            if (!stat.stat.net?.total || typeof stat.stat.net.total !== 'object') {
                console.warn(`[Stats] 服务器 ${server.name} 网络数据异常，跳过`);
                continue;
            }
            
            const ni = Number(stat.stat.net.total.in) || 0;
            const no = Number(stat.stat.net.total.out) || 0;
            
            // 获取上次的流量数据
            const [li, lo] = ltMap.get(sid) || [0, 0];
            
            // 计算增量
            let ti = ni - li;
            let to = no - lo;
            
            // 处理计数器重置
            if (ti < 0) ti = ni;
            if (to < 0) to = no;
            
            // 收集更新
            ltUpdates.push({ sid, traffic: [ni, no] });
            
            if (ti > 0 || to > 0) {
                trafficUpdates.push({ sid, delta: [ti, to] });
            }
        }
        
        // 批量执行更新
        console.log(`[Stats] 批量更新: ${ltUpdates.length} 个lt记录, ${trafficUpdates.length} 个流量记录`);
        
        await Promise.all([
            batchUpdateLt(ltUpdates, db),
            batchAddTraffic(trafficUpdates, db)
        ]);
        
        console.log('[Stats] 批量更新完成');
        
    } catch (error) {
        console.error('[Stats] calc批量优化失败:', error);
        throw error;
    }
}

/**
 * 批量保存负载数据
 * @param {Array} loadDataArray - [{sid, loadData}]
 * @param {Object} db - 数据库实例
 */
async function batchSaveLoadData(loadDataArray, db) {
    if (!loadDataArray.length) return;
    
    const client = await db.adapter.beginTransaction();
    try {
        // 使用 INSERT ... ON CONFLICT UPDATE 批量插入/更新
        const values = loadDataArray.map((data, i) => 
            `($${i*2+1}, $${i*2+2}::jsonb)`
        ).join(',');
        
        const params = [];
        loadDataArray.forEach(data => {
            params.push(data.sid, JSON.stringify(data.loadData));
        });
        
        const sql = `
            INSERT INTO load_m (sid, load_data, updated_at)
            VALUES ${values.map((v, i) => `${v}, NOW()`).join(',')}
            ON CONFLICT (sid) DO UPDATE 
            SET load_data = EXCLUDED.load_data,
                updated_at = NOW()
        `;
        
        await client.query(sql, params);
        await db.adapter.commitTransaction(client);
        
        console.log(`[Stats] 批量保存 ${loadDataArray.length} 条负载数据`);
    } catch (error) {
        await db.adapter.rollbackTransaction(client);
        throw error;
    }
}

module.exports = {
    batchUpdateLt,
    batchAddTraffic,
    optimizedCalc,
    batchSaveLoadData
};