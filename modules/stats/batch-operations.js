/**
 * 批量操作工具模块
 * 
 * 提供数据库批量操作的通用功能，包括批量插入、错误处理和性能优化
 * 
 * @module batch-operations
 * @since 1.0.0
 */

const TimeFunctionUtils = require('../../database/adapters/time-function-utils');
const { logger } = require('../utils/logger');

/**
 * 批量插入负载数据到归档表
 * 
 * @param {Object} db - 数据库实例，必须包含DB.run和DB.type
 * @param {Array<Object>} loadDataArray - 负载数据数组
 * @param {string} loadDataArray[].sid - 服务器ID
 * @param {number} loadDataArray[].cpu - CPU使用率(%)
 * @param {number} loadDataArray[].mem - 内存使用率(%)
 * @param {number} loadDataArray[].swap - 交换空间使用率(%)
 * @param {number} loadDataArray[].ibw - 入站带宽(bytes/s)
 * @param {number} loadDataArray[].obw - 出站带宽(bytes/s)
 * @returns {Promise<number>} 成功插入的记录数量
 * @throws {Error} 当数据库操作失败时抛出错误
 */
async function batchInsertLoadArchive(db, loadDataArray) {
    // 参数验证
    if (!db || !db.DB || typeof db.DB.run !== 'function') {
        throw new Error('无效的数据库实例');
    }
    
    if (!Array.isArray(loadDataArray) || loadDataArray.length === 0) {
        return 0;
    }
    
    // 验证数据格式
    const requiredFields = ['sid', 'cpu', 'mem', 'swap', 'ibw', 'obw'];
    for (const data of loadDataArray) {
        for (const field of requiredFields) {
            if (data[field] === undefined || data[field] === null) {
                throw new Error(`数据缺少必需字段: ${field}`);
            }
        }
    }
    
    try {
        // 获取数据库特定的时间函数
        const expireTimeSQL = TimeFunctionUtils.getUnixTimestampWithOffset(
            db.DB.type, 
            86400 // 24小时过期
        );
        
        // 构建批量INSERT SQL语句
        const placeholders = loadDataArray
            .map(() => `(?, ?, ?, ?, ?, ?, ${expireTimeSQL})`)
            .join(', ');
            
        const sql = `INSERT INTO load_archive (sid, cpu, mem, swap, ibw, obw, expire_time) VALUES ${placeholders}`;
        
        // 展开参数数组（每条记录6个参数）
        const params = [];
        for (const item of loadDataArray) {
            params.push(
                item.sid,
                item.cpu,
                item.mem,
                item.swap,
                item.ibw,
                item.obw
            );
        }
        
        // 执行批量插入
        const result = await db.DB.run(sql, params);
        const insertedCount = result.changes || loadDataArray.length;
        
        // 记录成功信息
        logger.info(`[批量操作] 成功插入 ${insertedCount} 条负载数据记录`);
        
        return insertedCount;
    } catch (error) {
        // 增强错误信息
        const enhancedError = new Error(`批量插入负载数据失败: ${error.message}`);
        enhancedError.originalError = error;
        enhancedError.dataCount = loadDataArray.length;
        
        logger.error('[批量操作] 错误详情:', {
            message: error.message,
            dataCount: loadDataArray.length,
            dbType: db.DB.type
        });
        
        throw enhancedError;
    }
}

/**
 * 构建带降级支持的批量操作执行器
 * 
 * @param {Function} batchOperation - 批量操作函数
 * @param {Function} fallbackOperation - 降级操作函数
 * @param {string} operationName - 操作名称（用于日志）
 * @returns {Function} 带降级支持的执行函数
 */
function createBatchOperationWithFallback(batchOperation, fallbackOperation, operationName) {
    return async function executeWithFallback(...args) {
        try {
            // 尝试执行批量操作
            const result = await batchOperation(...args);
            return { success: true, result, usedFallback: false };
        } catch (batchError) {
            logger.error(`[批量操作] ${operationName}失败，尝试降级处理:`, batchError.message);
            
            try {
                // 执行降级操作
                const fallbackResult = await fallbackOperation(...args);
                logger.info(`[批量操作] ${operationName}降级处理成功`);
                return { success: true, result: fallbackResult, usedFallback: true };
            } catch (fallbackError) {
                logger.error(`[批量操作] ${operationName}降级处理也失败:`, fallbackError.message);
                throw fallbackError;
            }
        }
    };
}

/**
 * 性能监控装饰器
 * 
 * @param {Function} operation - 需要监控的操作函数
 * @param {string} operationName - 操作名称
 * @returns {Function} 带性能监控的函数
 */
function withPerformanceMonitoring(operation, operationName) {
    return async function monitoredOperation(...args) {
        const startTime = Date.now();
        let result;
        let error;
        
        try {
            result = await operation(...args);
        } catch (err) {
            error = err;
        }
        
        const duration = Date.now() - startTime;
        
        // 记录性能数据
        logger.debug(`[性能监控] ${operationName}:`, {
            duration: `${duration}ms`,
            success: !error,
            timestamp: new Date().toISOString()
        });
        
        if (error) {
            throw error;
        }
        
        return result;
    };
}

module.exports = {
    batchInsertLoadArchive,
    createBatchOperationWithFallback,
    withPerformanceMonitoring
};