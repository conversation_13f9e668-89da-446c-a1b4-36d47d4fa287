"use strict";

/**
 * 服务器状态管理器
 * 提供统一的状态数据管理接口
 */
class StatsManager {
    constructor(db, eventEmitter) {
        this.db = db;
        this.eventEmitter = eventEmitter;
        this.stats = {};
        this.fails = {};
        this.serverStatusCache = {};
        this.updating = new Set();
        this.initialStatusCollectionComplete = false;
        
        // 初始化事件监听
        if (this.eventEmitter) {
            this.initEventListeners();
        }
    }
    
    /**
     * 初始化事件监听
     */
    initEventListeners() {
        // 监听服务器状态更新事件
        this.eventEmitter.on('server:stats:update', ({ sid, data, server }) => {
            this.updateServerStats(sid, data);
        });
    }
    
    /**
     * 更新服务器状态
     * @param {string} sid - 服务器ID
     * @param {Object} data - 状态数据
     */
    updateServerStats(sid, data) {
        this.stats[sid] = data;
        
        // 如果是首次更新，初始化服务器状态缓存
        if (this.serverStatusCache[sid] === undefined) {
            if (data && data.stat !== false) {
                this.serverStatusCache[sid] = true;
                console.log(`[状态监控] 初始化服务器状态: ${data.name || sid} => 在线`);
            } else {
                // 假设初始状态未知，设置为null以避免错误的通知
                this.serverStatusCache[sid] = null;
                console.log(`[状态监控] 初始化服务器状态: ${data.name || sid} => 未知`);
            }
        }
        
        // 重置失败计数
        this.fails[sid] = 0;
        
        // 发出状态更新事件
        if (this.eventEmitter) {
            this.eventEmitter.emit('stats:updated', { sid, data });
        }
    }
    
    /**
     * 获取服务器状态
     * @param {string} sid - 服务器ID
     * @returns {Object|null} - 服务器状态
     */
    getServerStats(sid) {
        return this.stats[sid] || null;
    }
    
    /**
     * 获取所有服务器状态
     * @param {boolean} isAdmin - 是否为管理员
     * @returns {Object} - 所有服务器状态
     */
    async getAllStats(isAdmin = false) {
        let Stats = {};
        for(let server of await this.db.servers.all()) {
            if(server.status == 1 || (server.status == 2 && isAdmin)){
                const serverStats = this.stats[server.sid];
                // 状态判断逻辑：
                // - 如果没有stats记录，返回-1（初始状态）
                // - 如果stats.stat === false，说明连接失败
                // - 如果有具体数据，说明在线
                const stat = !serverStats ? -1 :
                            serverStats.stat === false ? 0 :
                            serverStats.stat;

                Stats[server.sid] = {
                    name: server.name,
                    stat: stat,
                    expire_time: server.expire_time,
                    group_id: server.group_id,
                    top: server.top,
                    traffic_used: serverStats?.traffic_used || 0,
                    traffic_limit: server.traffic_limit || 0,
                    traffic_reset_day: server.traffic_reset_day || 1,
                    traffic_calibration_date: server.traffic_calibration_date || 0,
                    traffic_calibration_value: server.traffic_calibration_value || 0,
                    calibration_base_traffic: serverStats?.calibration_base_traffic || null,
                    data: server.data
                };
            }
        }
        return Stats;
    }
    
    /**
     * 标记服务器离线
     * @param {string} sid - 服务器ID
     * @param {string} name - 服务器名称
     * @param {Object} expireTime - 过期时间
     */
    markServerOffline(sid, name, expireTime) {
        this.stats[sid] = {
            name: name,
            stat: false,
            expire_time: expireTime,
            traffic_used: this.stats[sid]?.traffic_used || 0
        };
        
        // 更新状态缓存
        this.serverStatusCache[sid] = false;
        
        // 发出状态更新事件
        if (this.eventEmitter) {
            this.eventEmitter.emit('stats:server:offline', { sid, name });
        }
    }
    
    /**
     * 增加服务器失败计数
     * @param {string} sid - 服务器ID
     * @returns {number} - 当前失败计数
     */
    incrementFailCount(sid) {
        this.fails[sid] = (this.fails[sid] || 0) + 1;
        return this.fails[sid];
    }
    
    /**
     * 重置服务器失败计数
     * @param {string} sid - 服务器ID
     */
    resetFailCount(sid) {
        this.fails[sid] = 0;
    }
    
    /**
     * 获取服务器失败计数
     * @param {string} sid - 服务器ID
     * @returns {number} - 当前失败计数
     */
    getFailCount(sid) {
        return this.fails[sid] || 0;
    }
    
    /**
     * 设置初始状态收集完成标志
     * @param {boolean} value - 是否完成
     */
    setInitialStatusCollectionComplete(value) {
        this.initialStatusCollectionComplete = value;
        
        // 发出初始化完成事件
        if (value && this.eventEmitter) {
            this.eventEmitter.emit('stats:initialization:complete');
        }
    }
    
    /**
     * 检查初始状态收集是否完成
     * @returns {boolean} - 是否完成
     */
    isInitialStatusCollectionComplete() {
        return this.initialStatusCollectionComplete;
    }
    
    /**
     * 获取服务器状态缓存
     * @param {string} sid - 服务器ID
     * @returns {boolean|null} - 服务器状态缓存
     */
    getServerStatusCache(sid) {
        return this.serverStatusCache[sid];
    }
    
    /**
     * 设置服务器状态缓存
     * @param {string} sid - 服务器ID
     * @param {boolean|null} status - 服务器状态
     */
    setServerStatusCache(sid, status) {
        this.serverStatusCache[sid] = status;
    }
    
    /**
     * 获取所有服务器状态缓存
     * @returns {Object} - 所有服务器状态缓存
     */
    getAllServerStatusCache() {
        return { ...this.serverStatusCache };
    }
    
    /**
     * 清除服务器状态
     * @param {string} sid - 服务器ID
     */
    clearServerStats(sid) {
        delete this.stats[sid];
        delete this.fails[sid];
        this.serverStatusCache[sid] = null;
    }
}

module.exports = StatsManager;
