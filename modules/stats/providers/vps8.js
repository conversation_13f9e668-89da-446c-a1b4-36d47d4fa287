"use strict";

const BaseIPProvider = require('./base');
const fetch = require('node-fetch');

/**
 * vps8.de IP地理位置查询Provider
 */
class VPS8Provider extends BaseIPProvider {
    constructor() {
        super({ name: 'vps8.de' });
    }
    
    /**
     * 查询IP地理位置信息
     * @param {string} ip - IP地址
     * @returns {Promise<Object>} 查询结果
     */
    async query(ip) {
        const url = `https://vps8.de/api.php?ip=${ip}`;
        
        try {
            const response = await fetch(url, { 
                timeout: this.timeout,
                headers: {
                    'User-Agent': 'dstatus/1.0'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            return this.parseResponse(data);
            
        } catch (error) {
            throw new Error(`${this.name}: ${error.message}`);
        }
    }
    
    /**
     * 解析API响应（从原有代码迁移）
     * @param {Object} data - API响应数据
     * @returns {Object} 统一格式的地理位置信息
     */
    parseResponse(data) {
        if (!data || typeof data !== 'object') {
            return {
                country: 'Unknown',
                countryCode: '--',
                flag: '',
                success: false,
                error: 'Invalid response format'
            };
        }
        
        // 首先检查是否有错误
        if (data.error) {
            console.log(`[VPS8 API] 错误响应: ${data.error}`);
            return {
                country: 'Unknown',
                countryCode: '--',
                flag: '',
                success: false,
                error: data.error
            };
        }
        
        // 检查是否返回有效的国家代码
        const countryCode = data.country_code;
        const isValid = countryCode && countryCode !== '--' && countryCode !== 'Unknown';
        
        // vps8.de API 不返回 country_name，使用代码作为名称
        const countryName = isValid ? countryCode : 'Unknown';
        
        return {
            country: countryName,
            countryCode: countryCode || '--',
            flag: data.flag_url || '',
            success: isValid,
            provider: this.name
        };
    }
}

module.exports = VPS8Provider;