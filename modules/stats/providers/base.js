"use strict";

/**
 * IP地理位置查询Provider基类
 */
class BaseIPProvider {
    /**
     * 初始化Provider
     * @param {Object} config - 配置选项
     * @param {string} config.name - Provider名称
     * @param {number} config.timeout - 请求超时时间（毫秒）
     */
    constructor(config = {}) {
        this.name = config.name || 'Unknown';
        this.timeout = config.timeout || 5000;
    }
    
    /**
     * 查询IP地理位置信息
     * @param {string} ip - IP地址
     * @returns {Promise<Object>} 查询结果
     */
    async query(ip) {
        throw new Error('Provider must implement query method');
    }
    
    /**
     * 解析API响应
     * @param {Object} data - API响应数据
     * @returns {Object} 统一格式的地理位置信息
     */
    parseResponse(data) {
        throw new Error('Provider must implement parseResponse method');
    }
}

module.exports = BaseIPProvider;