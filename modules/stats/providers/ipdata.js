"use strict";

const BaseIPProvider = require('./base');
const fetch = require('node-fetch');

/**
 * ipdata.co IP地理位置查询Provider
 */
class IPDataProvider extends BaseIPProvider {
    constructor() {
        super({ name: 'ipdata.co' });
        this.apiKey = 'cb9ad5f3d665d2827ec4139ea015ffcd940150d5daaef8202d001489';
    }
    
    /**
     * 查询IP地理位置信息
     * @param {string} ip - IP地址
     * @returns {Promise<Object>} 查询结果
     */
    async query(ip) {
        const url = `https://api.ipdata.co/${ip}?api-key=${this.apiKey}`;
        
        try {
            const response = await fetch(url, { 
                timeout: this.timeout,
                headers: {
                    'User-Agent': 'dstatus/1.0'
                }
            });
            
            if (!response.ok) {
                // 处理特殊情况：私有IP等
                if (response.status === 400) {
                    const errorData = await response.json();
                    console.log(`[${this.name}] 400错误: ${errorData.message}`);
                    return {
                        country: 'Unknown',
                        countryCode: '--',
                        flag: '',
                        success: false,
                        error: errorData.message
                    };
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            return this.parseResponse(data);
            
        } catch (error) {
            throw new Error(`${this.name}: ${error.message}`);
        }
    }
    
    /**
     * 解析API响应
     * @param {Object} data - API响应数据
     * @returns {Object} 统一格式的地理位置信息
     */
    parseResponse(data) {
        if (!data || typeof data !== 'object') {
            return {
                country: 'Unknown',
                countryCode: '--',
                flag: '',
                success: false,
                error: 'Invalid response format'
            };
        }
        
        // 特殊处理：ipdata.co 对某些IP（如*******）返回null国家代码
        const countryCode = data.country_code;
        const isValid = countryCode && countryCode !== null;
        
        return {
            country: data.country_name || 'Unknown',
            countryCode: countryCode || '--',
            flag: data.flag || '',
            success: isValid,
            // 扩展信息（可选）
            city: data.city,
            region: data.region,
            asn: data.asn?.name,
            provider: this.name
        };
    }
}

module.exports = IPDataProvider;