"use strict";

/**
 * IP地理位置查询模块
 * 提供IP地址的地理位置查询功能
 */

const fetch = require('node-fetch');
const dns = require('dns').promises;

// 引入Provider实现
const IPDataProvider = require('./providers/ipdata');
const VPS8Provider = require('./providers/vps8');

/**
 * IP地理位置查询类
 */
class IPLocation {
    /**
     * 初始化IP地理位置查询
     * @param {Object} options - 配置选项
     * @param {string} options.apiUrl - IP查询API地址
     * @param {number} options.timeout - 请求超时时间（毫秒）
     */
    constructor(options = {}) {
        // 保留原有配置以保持兼容性
        this.apiUrl = options.apiUrl || 'https://vps8.de/api.php?ip=';
        this.timeout = options.timeout || 5000;
        
        // 初始化providers，ipdata.co优先，vps8.de作为备用
        this.providers = [
            new IPDataProvider(),
            new VPS8Provider()
        ];
    }

    /**
     * 解析VPS8 API响应
     * @private
     * @deprecated 保留此方法以维持向后兼容性，新代码应使用VPS8Provider
     */
    parseVPS8Response(data) {
        // 使用VPS8Provider的解析逻辑以保持一致性
        const provider = new VPS8Provider();
        return provider.parseResponse(data);
    }


    /**
     * DNS解析域名为IP地址
     * @param {string} hostname - 域名或IP地址
     * @returns {Promise<string>} 解析后的IP地址
     */
    async resolveDNS(hostname) {
        // 检查是否已经是IP地址
        const ipv4Regex = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;
        const ipv6Regex = /^[0-9a-fA-F:]+$/;
        
        if (ipv4Regex.test(hostname) || ipv6Regex.test(hostname)) {
            return hostname;
        }
        
        // 首先尝试使用第三方 DNS-over-HTTPS 服务
        try {
            const ip = await this.resolveDNSViaDoH(hostname);
            if (ip) {
                console.log(`[DNS解析] DoH解析成功: ${hostname} -> ${ip}`);
                return ip;
            }
        } catch (dohError) {
            console.log(`[DNS解析] DoH解析失败，尝试本地DNS: ${dohError.message}`);
        }
        
        // 如果 DoH 失败，回退到本地 DNS
        try {
            const result = await dns.resolve4(hostname);
            const ip = result[0];
            console.log(`[DNS解析] 本地解析成功: ${hostname} -> ${ip}`);
            return ip;
        } catch (error) {
            throw new Error(`DNS解析失败: ${error.message}`);
        }
    }
    
    /**
     * 使用 DNS-over-HTTPS 解析域名
     * @private
     * @param {string} hostname - 域名
     * @returns {Promise<string|null>} IP地址或null
     */
    async resolveDNSViaDoH(hostname) {
        // 使用 Cloudflare DNS-over-HTTPS
        const dohUrl = `https://cloudflare-dns.com/dns-query?name=${encodeURIComponent(hostname)}&type=A`;
        
        try {
            const response = await fetch(dohUrl, {
                headers: {
                    'Accept': 'application/dns-json'
                },
                timeout: 3000 // 3秒超时
            });
            
            if (!response.ok) {
                throw new Error(`DoH请求失败: ${response.status}`);
            }
            
            const data = await response.json();
            
            // 检查是否有答案
            if (data.Answer && data.Answer.length > 0) {
                // 找到第一个 A 记录（type=1）
                const aRecord = data.Answer.find(answer => answer.type === 1);
                if (aRecord && aRecord.data) {
                    return aRecord.data;
                }
            }
            
            return null;
        } catch (error) {
            throw new Error(`DoH解析错误: ${error.message}`);
        }
    }

    /**
     * 查询IP地理位置信息（支持API轮询和DNS解析）
     * @param {string} ip - IP地址或域名
     * @returns {Promise<Object>} 地理位置信息
     */
    async query(ip) {
        let resolvedIP = ip;
        
        // 第一步：DNS解析（如果需要）
        try {
            resolvedIP = await this.resolveDNS(ip);
            if (ip !== resolvedIP) {
                console.log(`[IP位置查询] DNS解析成功: ${ip} -> ${resolvedIP}`);
            }
        } catch (dnsError) {
            console.error(`[IP位置查询] DNS解析失败: ${ip} - ${dnsError.message}`);
            return {
                country: 'Unknown',
                countryCode: '--',
                flag: '',
                success: false,
                error: `DNS解析失败: ${dnsError.message}`
            };
        }
        
        // 第二步：使用providers查询位置信息
        let lastError = null;
        
        for (let i = 0; i < this.providers.length; i++) {
            const provider = this.providers[i];
            
            try {
                console.log(`[IP位置查询] 使用${provider.name}查询: ${resolvedIP}`);
                const result = await provider.query(resolvedIP);
                
                if (result && result.success) {
                    console.log(`[IP位置查询] ${provider.name}查询成功: ${resolvedIP} -> ${result.countryCode}`);
                    return result;
                }
                
                // Provider返回了结果但标记为失败
                console.log(`[IP位置查询] ${provider.name}返回无效数据`);
                lastError = result.error || `${provider.name}返回无效数据`;
                
            } catch (error) {
                lastError = error.message;
                console.error(`[IP位置查询] ${provider.name}查询失败: ${error.message}`);
            }
            
            // 如果不是最后一个provider，尝试下一个
            if (i < this.providers.length - 1) {
                console.log(`[IP位置查询] 切换到备用服务...`);
            }
        }
        
        // 所有provider都失败
        const finalError = lastError || '所有查询服务失败';
        console.error(`[IP位置查询] 查询失败: ${finalError}`);
        
        return {
            country: 'Unknown',
            countryCode: '--',
            flag: '',
            success: false,
            error: finalError
        };
    }

    /**
     * 批量查询IP地理位置信息
     * @param {string[]} ips - IP地址数组
     * @returns {Promise<Object>} 批量查询结果
     */
    async batchQuery(ips) {
        const results = {};
        for (const ip of ips) {
            results[ip] = await this.query(ip);
        }
        return results;
    }
}

/**
 * IP地理位置服务类
 * 提供IP地理位置查询、缓存和服务器位置更新功能
 */
class IPLocationService {
    /**
     * 初始化IP地理位置服务
     * @param {Object} options - 配置选项
     * @param {IPLocation} options.ipLocator - IP地理位置查询实例
     * @param {number} options.cacheTTL - 缓存过期时间（毫秒）
     * @param {number} options.retryInterval - 重试间隔时间（毫秒）
     * @param {number} options.maxRetries - 最大重试次数
     */
    constructor(options = {}) {
        this.ipLocator = options.ipLocator || new IPLocation();
        this.cacheTTL = options.cacheTTL || 24 * 60 * 60 * 1000; // 24小时
        this.retryInterval = options.retryInterval || 30 * 60 * 1000; // 默认30分钟重试一次
        this.maxRetries = options.maxRetries || 5; // 默认最大重试5次

        // IP缓存
        this.ipCache = {};
        // 更新失败记录 - 改为Map以存储更多信息
        this.updateFailures = new Map(); // sid -> {retries, lastTry, error}

        // 初始化定时任务
        this.retryTimer = null;
        this.startRetryTimer();
    }

    /**
     * 启动定时重试任务
     * 定期尝试重新获取失败的IP位置信息
     */
    startRetryTimer() {
        if (this.retryTimer) {
            clearInterval(this.retryTimer);
        }

        this.retryTimer = setInterval(() => {
            this.retryFailedUpdates();
        }, this.retryInterval);

        console.log(`[${new Date().toISOString()}] IP位置服务定时重试任务已启动，间隔: ${this.retryInterval / 1000 / 60}分钟`);
    }

    /**
     * 重试失败的更新
     * @param {Object} db - 数据库对象，如果提供则会更新数据库
     */
    async retryFailedUpdates(db = null) {
        if (this.updateFailures.size === 0) {
            return;
        }

        console.log(`[${new Date().toISOString()}] 开始重试失败的IP位置更新，共${this.updateFailures.size}个`);

        // 复制失败记录，避免迭代过程中修改
        const failures = [...this.updateFailures.entries()];

        for (const [sid, failInfo] of failures) {
            // 检查重试次数是否超过最大值
            if (failInfo.retries >= this.maxRetries) {
                console.log(`[${new Date().toISOString()}] 服务器 ${sid} 的IP位置更新已达最大重试次数，不再重试`);
                continue;
            }

            // 检查上次尝试时间，避免频繁重试
            const now = Date.now();
            const timeSinceLastTry = now - failInfo.lastTry;
            if (timeSinceLastTry < this.retryInterval) {
                continue;
            }

            // 如果有数据库对象，尝试更新服务器位置
            if (db) {
                const server = db.servers.get(sid);
                if (server) {
                    console.log(`[${new Date().toISOString()}] 重试更新服务器 ${server.name} 的位置信息`);

                    // 更新重试计数
                    failInfo.retries++;
                    failInfo.lastTry = now;
                    this.updateFailures.set(sid, failInfo);

                    // 尝试更新
                    await this.updateServerLocation(server, db);
                }
            }
        }
    }

    /**
     * 检查是否为本地IP或局域网IP
     * @param {string} ip - IP地址
     * @returns {boolean} 是否为本地IP或局域网IP
     */
    isLocalOrPrivateIP(ip) {
        // 检查是否为本地IP
        if (ip === '127.0.0.1' || ip === 'localhost' || ip === '::1') {
            return true;
        }

        // 检查是否为局域网IP
        // 10.0.0.0/8
        if (ip.startsWith('10.')) {
            return true;
        }

        // **********/12
        if (ip.startsWith('172.')) {
            const secondPart = parseInt(ip.split('.')[1], 10);
            if (secondPart >= 16 && secondPart <= 31) {
                return true;
            }
        }

        // ***********/16
        if (ip.startsWith('192.168.')) {
            return true;
        }

        // fc00::/7 (IPv6 ULA)
        if (ip.toLowerCase().startsWith('fc') || ip.toLowerCase().startsWith('fd')) {
            return true;
        }

        // fe80::/10 (IPv6 link-local)
        if (ip.toLowerCase().startsWith('fe8') || ip.toLowerCase().startsWith('fe9') ||
            ip.toLowerCase().startsWith('fea') || ip.toLowerCase().startsWith('feb')) {
            return true;
        }

        return false;
    }

    /**
     * 获取IP地理位置信息
     * @param {string} ip - IP地址
     * @returns {Promise<Object>} 地理位置信息
     */
    async getIPLocation(ip) {
        // 检查是否为本地IP或局域网IP
        if (this.isLocalOrPrivateIP(ip)) {
            // 返回本地网络的位置信息
            const localNetworkData = {
                success: true,
                country: '本地网络',
                countryCode: 'LO',  // LO 代表 Local
                flag: ''
            };

            // 更新缓存
            this.ipCache[ip] = {
                timestamp: Date.now(),
                data: localNetworkData
            };

            return localNetworkData;
        }

        // 检查缓存
        if (this.ipCache[ip] && this.ipCache[ip].timestamp > Date.now() - this.cacheTTL) {
            return this.ipCache[ip].data;
        }

        try {
            // 查询IP位置
            const locationData = await this.ipLocator.query(ip);

            // 检查查询是否成功
            if (!locationData || !locationData.success) {
                const errorMsg = locationData?.error || '未知错误';

                // 返回带有错误信息的结果
                return {
                    success: false,
                    error: errorMsg,
                    country: 'Unknown',
                    countryCode: '--',
                    flag: ''
                };
            }

            // 更新缓存
            this.ipCache[ip] = {
                timestamp: Date.now(),
                data: locationData
            };

            return locationData;
        } catch (error) {
            // 返回带有错误信息的结果
            return {
                success: false,
                error: error.message || '未知错误',
                country: 'Unknown',
                countryCode: '--',
                flag: ''
            };
        }
    }

    /**
     * 更新服务器的位置信息
     * @param {Object} server - 服务器对象
     * @param {Object} db - 数据库对象
     * @returns {Promise<Object>} 更新后的服务器数据
     */
    async updateServerLocation(server, db) {
        const { sid } = server;
        const now = Date.now();
        // 使用 let 而不是 const，因为我们可能需要重新赋值
        let serverData = server.data || {};

        try {
            // 获取IP地址
            const ip = serverData.ip || serverData.host || serverData.ssh?.host;
            if (!ip) {
                const errorMsg = '无法获取服务器IP地址';

                // 记录失败信息
                this.updateFailures.set(sid, {
                    retries: 1,
                    lastTry: now,
                    error: errorMsg
                });

                // 更新服务器数据
                if (!serverData.location) serverData.location = {};
                serverData.location.error = errorMsg;
                serverData.location.updated_at = now;

                // 保存到数据库
                db.servers.upd_data(sid, serverData);

                return {
                    success: false,
                    data: serverData,
                    error: errorMsg,
                    message: errorMsg
                };
            }

            // 获取IP位置
            const locationData = await this.getIPLocation(ip);

            // 判断是否获取到了有效的国家代码
            if (locationData && locationData.countryCode && locationData.countryCode !== '--') {
                // 检查现有位置信息是否与新获取的一致
                const currentLocation = serverData.location || {};
                const currentCode = currentLocation.code || currentLocation.country?.code;

                if (currentCode === locationData.countryCode) {
                    console.log(`[${new Date().toISOString()}] 服务器 ${server.name} 位置信息未变化: ${locationData.countryCode}`);

                    // 更新时间戳
                    if (!serverData.location) serverData.location = {};
                    serverData.location.updated_at = now;

                    // 保存到数据库
                    db.servers.upd_data(sid, serverData);

                    // 从失败记录中移除
                    this.updateFailures.delete(sid);

                    return {
                        success: true,
                        data: serverData,
                        unchanged: true,
                        message: '位置信息未变化'
                    };
                }

                // 更新位置信息
                console.log(`[${new Date().toISOString()}] 获取到新的位置信息: ${server.name} (${ip}) -> ${locationData.countryCode}`);

                if (!serverData.location) {
                    serverData.location = {};
                }

                // 更新位置信息 - 使用新的数据结构
                serverData.location = {
                    code: locationData.countryCode,
                    flag: this.getCountryFlag(locationData.countryCode, locationData.flag),
                    country_name: locationData.country,
                    name_zh: this.getCountryNameZh(locationData.countryCode),
                    auto_detect: true,
                    manual: false,
                    updated_at: now
                };

                // 清除错误信息
                delete serverData.location.error;

                // 保存到数据库
                db.servers.upd_data(sid, serverData);

                // 从失败记录中移除
                this.updateFailures.delete(sid);

                console.log(`[${new Date().toISOString()}] 更新服务器位置成功: ${server.name} (${locationData.country || locationData.countryCode})`);
                return {
                    success: true,
                    data: serverData,
                    message: '位置信息更新成功'
                };
            } else {
                // 提供更详细的错误诊断信息
                let errorMsg = '无法获取有效的位置信息';
                let diagnosticInfo = '';
                
                if (locationData?.error) {
                    errorMsg = `获取位置信息失败: ${locationData.error}`;
                    
                    // 添加诊断信息
                    if (locationData.error.includes('DNS解析失败')) {
                        diagnosticInfo = ' [建议：检查域名格式或网络连接]';
                    } else if (locationData.error.includes('所有API提供商都失败')) {
                        diagnosticInfo = ' [建议：检查网络连接或API服务状态]';
                    } else if (locationData.error.includes('HTTP')) {
                        diagnosticInfo = ' [建议：API服务可能暂时不可用]';
                    }
                } else if (locationData) {
                    // 分析返回的数据
                    const dataInfo = [];
                    if (locationData.country) dataInfo.push(`country: ${locationData.country}`);
                    if (locationData.countryCode) dataInfo.push(`code: ${locationData.countryCode}`);
                    if (dataInfo.length > 0) {
                        errorMsg = `位置数据不完整: ${dataInfo.join(', ')}`;
                        diagnosticInfo = ' [数据可用但缺少关键字段]';
                    }
                }

                console.error(`[${new Date().toISOString()}] ${errorMsg}${diagnosticInfo}: ${server.name} (${ip})`);

                // 记录失败信息
                const failInfo = this.updateFailures.get(sid) || { retries: 0, lastTry: 0 };
                this.updateFailures.set(sid, {
                    retries: failInfo.retries + 1,
                    lastTry: now,
                    error: errorMsg + diagnosticInfo
                });

                // 更新服务器数据
                if (!serverData.location) serverData.location = {};
                serverData.location.error = errorMsg;
                serverData.location.updated_at = now;

                // 保存到数据库
                db.servers.upd_data(sid, serverData);

                return {
                    success: false,
                    data: serverData,
                    error: errorMsg,
                    message: errorMsg
                };
            }
        } catch (error) {
            const errorMsg = `更新位置信息时发生错误: ${error.message || '未知错误'}`;
            console.error(`[${new Date().toISOString()}] ${errorMsg}: ${server.name}`);

            // 记录失败信息
            this.updateFailures.set(sid, {
                retries: (this.updateFailures.get(sid)?.retries || 0) + 1,
                lastTry: now,
                error: errorMsg
            });

            // 更新服务器数据
            try {
                // 确保 serverData 是一个对象
                if (typeof serverData === 'string') {
                    try {
                        serverData = JSON.parse(serverData);
                    } catch (parseError) {
                        console.error(`[${new Date().toISOString()}] 无法解析服务器数据: ${parseError.message}`);
                        serverData = {};
                    }
                }

                if (!serverData) serverData = {};
                if (!serverData.location) serverData.location = {};
                serverData.location.error = errorMsg;
                serverData.location.updated_at = now;
            } catch (updateError) {
                console.error(`[${new Date().toISOString()}] 更新服务器位置数据时出错: ${updateError.message}`);
                serverData = { location: { error: errorMsg, updated_at: now } };
            }

            // 保存到数据库
            db.servers.upd_data(sid, serverData);

            return {
                success: false,
                error: errorMsg,
                message: errorMsg,
                data: serverData
            };
        }
    }

    /**
     * 获取国家中文名
     * @param {string} countryCode - 国家代码
     * @returns {string} 国家中文名
     */
    getCountryNameZh(countryCode) {
        const code = String(countryCode || '').toUpperCase();
        const countryMap = {
            // East Asia / Greater China
            'CN': '中国', 'HK': '香港', 'MO': '澳门', 'TW': '台湾', 'JP': '日本', 'KR': '韩国',
            // Southeast Asia
            'SG': '新加坡', 'MY': '马来西亚', 'TH': '泰国', 'VN': '越南', 'ID': '印度尼西亚', 'PH': '菲律宾', 'BN': '文莱', 'KH': '柬埔寨', 'LA': '老挝', 'MM': '缅甸', 'TL': '东帝汶',
            // South Asia
            'IN': '印度', 'PK': '巴基斯坦', 'BD': '孟加拉国', 'LK': '斯里兰卡', 'NP': '尼泊尔', 'BT': '不丹', 'MV': '马尔代夫',
            // Central Asia
            'KZ': '哈萨克斯坦', 'KG': '吉尔吉斯斯坦', 'TJ': '塔吉克斯坦', 'UZ': '乌兹别克斯坦', 'TM': '土库曼斯坦',
            // Middle East / West Asia
            'AE': '阿拉伯联合酋长国', 'SA': '沙特阿拉伯', 'QA': '卡塔尔', 'BH': '巴林', 'OM': '阿曼', 'KW': '科威特', 'IR': '伊朗', 'IQ': '伊拉克', 'IL': '以色列', 'JO': '约旦', 'LB': '黎巴嫩', 'YE': '也门', 'SY': '叙利亚', 'PS': '巴勒斯坦',
            // Europe
            'GB': '英国', 'UK': '英国', 'IE': '爱尔兰', 'FR': '法国', 'DE': '德国', 'NL': '荷兰', 'BE': '比利时', 'LU': '卢森堡', 'CH': '瑞士', 'AT': '奥地利', 'LI': '列支敦士登',
            'DK': '丹麦', 'NO': '挪威', 'SE': '瑞典', 'FI': '芬兰', 'IS': '冰岛', 'EE': '爱沙尼亚', 'LV': '拉脱维亚', 'LT': '立陶宛',
            'PL': '波兰', 'CZ': '捷克', 'SK': '斯洛伐克', 'HU': '匈牙利', 'RO': '罗马尼亚', 'BG': '保加利亚', 'HR': '克罗地亚', 'SI': '斯洛文尼亚', 'RS': '塞尔维亚', 'BA': '波黑', 'ME': '黑山', 'MK': '北马其顿', 'XK': '科索沃', 'AL': '阿尔巴尼亚', 'GR': '希腊', 'CY': '塞浦路斯', 'TR': '土耳其', 'IT': '意大利', 'ES': '西班牙', 'PT': '葡萄牙', 'SM': '圣马力诺', 'VA': '梵蒂冈', 'AD': '安道尔', 'MC': '摩纳哥', 'GI': '直布罗陀', 'MT': '马耳他',
            // North America & Caribbean
            'US': '美国', 'CA': '加拿大', 'MX': '墨西哥', 'GL': '格陵兰', 'BM': '百慕大',
            'PR': '波多黎各', 'VI': '美属维尔京群岛', 'VG': '英属维尔京群岛', 'KY': '开曼群岛', 'BS': '巴哈马', 'BB': '巴巴多斯', 'JM': '牙买加', 'TT': '特立尼达和多巴哥', 'GD': '格林纳达', 'LC': '圣卢西亚', 'VC': '圣文森特和格林纳丁斯', 'DM': '多米尼克', 'AG': '安提瓜和巴布达', 'KN': '圣基茨和尼维斯', 'HT': '海地', 'DO': '多米尼加', 'CU': '古巴', 'AW': '阿鲁巴', 'AI': '安圭拉', 'GP': '瓜德罗普', 'MQ': '马提尼克',
            // South America
            'AR': '阿根廷', 'BR': '巴西', 'CL': '智利', 'BO': '玻利维亚', 'PE': '秘鲁', 'EC': '厄瓜多尔', 'UY': '乌拉圭', 'PY': '巴拉圭', 'CO': '哥伦比亚', 'VE': '委内瑞拉', 'GY': '圭亚那', 'SR': '苏里南', 'GF': '法属圭亚那', 'FK': '福克兰群岛',
            // Africa
            'EG': '埃及', 'LY': '利比亚', 'TN': '突尼斯', 'DZ': '阿尔及利亚', 'MA': '摩洛哥', 'SD': '苏丹', 'SS': '南苏丹', 'ET': '埃塞俄比亚', 'ER': '厄立特里亚', 'SO': '索马里',
            'UG': '乌干达', 'KE': '肯尼亚', 'TZ': '坦桑尼亚', 'RW': '卢旺达', 'BI': '布隆迪', 'CD': '刚果（金）', 'CG': '刚果（布）', 'CM': '喀麦隆', 'CF': '中非共和国', 'TD': '乍得',
            'NG': '尼日利亚', 'NE': '尼日尔', 'BJ': '贝宁', 'TG': '多哥', 'GH': '加纳', 'CI': '科特迪瓦', 'BF': '布基纳法索', 'ML': '马里', 'MR': '毛里塔尼亚', 'SN': '塞内加尔', 'GM': '冈比亚', 'GN': '几内亚', 'GW': '几内亚比绍', 'SL': '塞拉利昂', 'LR': '利比里亚',
            'ZA': '南非', 'NA': '纳米比亚', 'BW': '博茨瓦纳', 'ZW': '津巴布韦', 'ZM': '赞比亚', 'MW': '马拉维', 'MZ': '莫桑比克', 'AO': '安哥拉', 'GA': '加蓬', 'GQ': '赤道几内亚',
            'ST': '圣多美和普林西比', 'CM': '喀麦隆', 'KM': '科摩罗', 'MG': '马达加斯加', 'SC': '塞舌尔', 'MU': '毛里求斯', 'YT': '马约特', 'RE': '留尼汪',
            // Oceania / Pacific
            'AU': '澳大利亚', 'NZ': '新西兰', 'PG': '巴布亚新几内亚', 'FJ': '斐济', 'TO': '汤加', 'WS': '萨摩亚', 'TV': '图瓦卢', 'VU': '瓦努阿图', 'SB': '所罗门群岛', 'KI': '基里巴斯', 'NR': '瑙鲁', 'MH': '马绍尔群岛', 'FM': '密克罗尼西亚', 'PW': '帕劳',
            'NC': '新喀里多尼亚', 'PF': '法属波利尼西亚', 'WF': '瓦利斯和富图纳', 'CK': '库克群岛', 'NU': '纽埃', 'TK': '托克劳', 'PN': '皮特凯恩群岛', 'NF': '诺福克岛', 'CC': '科科斯群岛', 'CX': '圣诞岛',
            // Others / Territories
            'GI': '直布罗陀', 'IM': '马恩岛', 'JE': '泽西岛', 'GG': '根西岛', 'FO': '法罗群岛', 'AX': '奥兰群岛', 'SJ': '斯瓦尔巴和扬马延', 'BV': '布韦岛', 'HM': '赫德岛和麦克唐纳群岛', 'TF': '法属南部领地', 'IO': '英属印度洋领地', 'GS': '南乔治亚和南桑威奇群岛', 'UM': '美国本土外小岛屿', 'AQ': '南极洲',
            // Americas minor
            'AS': '美属萨摩亚', 'MP': '北马里亚纳群岛', 'GU': '关岛',
            // Russia / CIS already above
            'RU': '俄罗斯', 'UA': '乌克兰', 'BY': '白俄罗斯', 'AM': '亚美尼亚', 'AZ': '阿塞拜疆', 'GE': '格鲁吉亚', 'MD': '摩尔多瓦', 'KZ': '哈萨克斯坦',
            // fallback specials
            '--': '未知地区', 'UNKNOWN': '未知地区', 'LO': '本地网络', 'OT': '其他地区'
        };
        return countryMap[code] || `未知(${code})`;
    }

    /**
     * 获取国家旗帜
     * @param {string} countryCode - 国家代码
     * @param {string} flagUrl - 旗帜图片URL
     * @returns {string} 国家旗帜表情
     */
    getCountryFlag(countryCode, flagUrl) {
        // 如果有国家代码，优先使用本地文件
        if (countryCode && countryCode !== '--') {
            // 特殊情况处理
            if (countryCode === 'UK') {
                return '/img/flags/GB.SVG'; // 英国使用GB代码
            } else if (countryCode === 'LO' || countryCode === 'OT' || countryCode === '--') {
                // 对于特殊代码，返回null表示使用图标字体
                return null;
            }

            // 返回本地文件路径（注意扩展名是大写的）
            return `/img/flags/${countryCode}.SVG`;
        }

        // 如果没有国家代码但有旗帜图片URL，使用API返回的URL
        if (flagUrl && flagUrl.startsWith('http')) {
            return flagUrl;
        }

        // 如果都没有，返回null表示使用图标字体
        return null;
    }

    /**
     * 清除IP缓存
     * @param {string} ip - 要清除的IP地址，如果不提供则清除所有缓存
     */
    clearCache(ip) {
        if (ip) {
            delete this.ipCache[ip];
            console.log(`[${new Date().toISOString()}] 已清除IP缓存: ${ip}`);
        } else {
            this.ipCache = {};
            console.log(`[${new Date().toISOString()}] 已清除所有IP缓存`);
        }
    }

    /**
     * 清除失败记录
     * @param {string} sid - 要清除的服务器ID，如果不提供则清除所有失败记录
     */
    clearFailures(sid) {
        if (sid) {
            this.updateFailures.delete(sid);
            console.log(`[${new Date().toISOString()}] 已清除服务器失败记录: ${sid}`);
        } else {
            this.updateFailures.clear();
            console.log(`[${new Date().toISOString()}] 已清除所有服务器失败记录`);
        }
    }

    /**
     * 检查并更新没有位置信息的服务器
     * @param {Object} db - 数据库对象
     * @returns {Promise<Object>} 处理结果，包含更新数量和成功数量
     */
    async checkAndUpdateMissingLocations(db) {
        try {
            console.log(`[${new Date().toISOString()}] 开始检查没有位置信息的服务器`);

            let totalChecked = 0;
            let totalUpdated = 0;
            let totalSuccess = 0;

            // 获取所有服务器
            const servers = await db.servers.all();

            for (const server of servers) {
                totalChecked++;

                // 检查服务器是否有位置信息
                const hasValidLocation = server.data &&
                                       server.data.location &&
                                       server.data.location.code &&
                                       server.data.location.code !== '--';

                if (!hasValidLocation) {
                    console.log(`[${new Date().toISOString()}] 服务器 ${server.name} 没有有效的位置信息，尝试更新`);

                    // 清除缓存和失败记录
                    if (server.data && server.data.ssh && server.data.ssh.host) {
                        this.clearCache(server.data.ssh.host);
                    }
                    this.clearFailures(server.sid);

                    // 更新位置信息
                    totalUpdated++;
                    const result = await this.updateServerLocation(server, db);

                    // 检查更新是否成功
                    if ((result && result.success) ||
                        (result && result.data && result.data.location && result.data.location.code)) {
                        totalSuccess++;
                        console.log(`[${new Date().toISOString()}] 服务器 ${server.name} 位置信息更新成功: ${result.data.location.code}`);
                    } else {
                        console.log(`[${new Date().toISOString()}] 服务器 ${server.name} 位置信息更新失败`);
                    }
                }
            }

            console.log(`[${new Date().toISOString()}] 检查完成: 共检查 ${totalChecked} 个服务器，更新 ${totalUpdated} 个，成功 ${totalSuccess} 个`);

            return {
                success: true,
                totalChecked,
                totalUpdated,
                totalSuccess
            };
        } catch (error) {
            console.error(`[${new Date().toISOString()}] 检查和更新位置信息失败:`, error);
            return {
                success: false,
                error: error.message,
                totalChecked: 0,
                totalUpdated: 0,
                totalSuccess: 0
            };
        }
    }

    /**
     * 刷新服务器位置信息（处理手动刷新请求）
     * @param {string} sid - 服务器ID
     * @param {Object} db - 数据库对象
     * @param {boolean} isAdmin - 是否为管理员
     * @returns {Promise<Object>} 处理结果
     */
    async refreshServerLocation(sid, db, isAdmin = true) {
        try {
            // 只允许管理员刷新IP
            if (!isAdmin) {
                return { success: false, message: '权限不足', status: 403 };
            }

            // 获取服务器
            const server = db.servers.get(sid);
            if (!server) {
                return { success: false, message: '服务器不存在', status: 404 };
            }

            // 清除缓存
            if (server.data && server.data.ssh && server.data.ssh.host) {
                this.clearCache(server.data.ssh.host);
                console.log(`[${new Date().toISOString()}] 手动触发获取服务器 ${server.name} (${server.data.ssh.host}) 位置信息`);
            }

            // 清除失败记录
            this.clearFailures(sid);

            // 更新位置
            const result = await this.updateServerLocation(server, db);

            // 判断是否成功获取了位置信息
            if (result && result.data && result.data.location && result.data.location.code) {
                // 如果有位置信息，则认为更新成功，即使 result.success 为 false
                console.log(`[${new Date().toISOString()}] 服务器 ${server.name} 位置信息更新成功: ${result.data.location.code}`);
                return { success: true, message: '刷新成功', data: result.data };
            } else if (result && result.success) {
                // 原来的成功判断逻辑
                return { success: true, message: '刷新成功', data: result.data };
            } else {
                // 使用返回的错误信息或默认错误信息
                let errorMessage = '位置信息更新失败';
                if (result && result.error) {
                    errorMessage = `${result.error}`;
                } else if (server.data && server.data.location && server.data.location.error) {
                    errorMessage = `${server.data.location.error}`;
                }

                console.log(`[${new Date().toISOString()}] 服务器 ${server.name} 位置信息更新失败: ${errorMessage}`);

                return {
                    success: false,
                    message: errorMessage,
                    server_data: {
                        name: server.name,
                        location: result ? result.data.location : (server.data?.location || null)
                    }
                };
            }
        } catch (error) {
            console.error('刷新IP位置失败:', error);
            return { success: false, message: '服务器错误', status: 500 };
        }
    }
}

// 导出IPLocation类、IPLocationService类和一些工具函数
module.exports = {
    IPLocation,
    IPLocationService,

    /**
     * 创建默认的IP地理位置查询实例
     * @returns {IPLocation} IP地理位置查询实例
     */
    createDefault() {
        return new IPLocation();
    },

    /**
     * 创建默认的IP地理位置服务实例
     * @returns {IPLocationService} IP地理位置服务实例
     */
    createService() {
        return new IPLocationService();
    }
};
