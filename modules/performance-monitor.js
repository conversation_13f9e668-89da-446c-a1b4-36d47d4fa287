'use strict';

/**
 * 性能监控模块
 * 用于监控服务器资源使用情况，确保系统稳定运行
 */

module.exports = (svr, db) => {
    const os = require('os');
const fs = require('fs');
const path = require('path');
const { logUtils, LOG_TYPES } = require('./utils/log-utils');

    // 性能数据存储
    const performanceData = {
        // CPU使用率历史数据（最近60分钟）
        cpuUsage: [],
        // 内存使用率历史数据（最近60分钟）
        memoryUsage: [],
        // API响应时间历史数据（最近60分钟）
        apiResponseTime: {},
        // 数据库查询时间历史数据（最近60分钟）
        dbQueryTime: {},
        // 时间戳
        timestamps: []
    };

    // 性能监控配置
    const config = {
        // 监控间隔（毫秒）
        interval: 60000, // 默认1分钟
        // 保存的数据点数量
        dataPoints: 60, // 默认保存60个数据点（1小时）
        // 日志文件路径（已迁移到统一日志工具）
        logFilePath: logUtils.getLogPath(LOG_TYPES.PERFORMANCE),
        // 是否启用日志
        enableLogging: true,
        // 是否启用控制台输出
        enableConsoleOutput: true,
        // CPU使用率警告阈值（百分比）
        cpuWarningThreshold: 80,
        // 内存使用率警告阈值（百分比）
        memoryWarningThreshold: 80,
        // API响应时间警告阈值（毫秒）
        apiResponseTimeWarningThreshold: 1000,
        // 数据库查询时间警告阈值（毫秒）
        dbQueryTimeWarningThreshold: 500
    };

    /**
     * 初始化性能监控模块
     * @param {Object} options - 配置选项
     */
    function init(options = {}) {
        // 合并配置
        Object.assign(config, options);

        // 确保日志目录存在
        const logDir = path.dirname(config.logFilePath);
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }

        // 开始监控
        startMonitoring();

        console.log('性能监控模块已初始化');
    }

    /**
     * 开始监控
     */
    function startMonitoring() {
        // 立即执行一次监控
        collectPerformanceData();

        // 设置定时任务，定期收集性能数据
        setInterval(collectPerformanceData, config.interval);

        console.log(`性能监控已启动，监控间隔: ${config.interval}ms`);
    }

    /**
     * 收集性能数据
     */
    function collectPerformanceData() {
        try {
            // 获取当前时间戳
            const timestamp = Date.now();

            // 收集CPU使用率
            const cpuUsage = getCpuUsage();

            // 收集内存使用率
            const memoryUsage = getMemoryUsage();

            // 添加数据
            performanceData.timestamps.push(timestamp);
            performanceData.cpuUsage.push(cpuUsage);
            performanceData.memoryUsage.push(memoryUsage);

            // 限制数据点数量
            if (performanceData.timestamps.length > config.dataPoints) {
                performanceData.timestamps.shift();
                performanceData.cpuUsage.shift();
                performanceData.memoryUsage.shift();
            }

            // 检查是否超过警告阈值
            checkWarningThresholds(cpuUsage, memoryUsage);

            // 记录日志
            if (config.enableLogging) {
                logPerformanceData(timestamp, cpuUsage, memoryUsage);
            }

            // 使用新的日志系统
            if (config.enableConsoleOutput) {
                const logger = require('./utils/logger');
                logger.logPerformance(cpuUsage, memoryUsage);
            }
        } catch (err) {
            console.error('收集性能数据失败:', err);
        }
    }

    /**
     * 获取CPU使用率
     * @returns {number} CPU使用率（百分比）
     */
    function getCpuUsage() {
        const cpus = os.cpus();
        let totalIdle = 0;
        let totalTick = 0;

        // 计算所有CPU核心的总时间和空闲时间
        for (const cpu of cpus) {
            for (const type in cpu.times) {
                totalTick += cpu.times[type];
            }
            totalIdle += cpu.times.idle;
        }

        // 计算CPU使用率
        const idle = totalIdle / cpus.length;
        const total = totalTick / cpus.length;
        const usage = 100 - (idle / total * 100);

        return usage;
    }

    /**
     * 获取内存使用率
     * @returns {number} 内存使用率（百分比）
     */
    function getMemoryUsage() {
        // 在开发环境或macOS上监控进程内存，避免系统内存缓存导致的虚高
        if (process.env.NODE_ENV === 'development' || process.platform === 'darwin') {
            // 监控Node.js进程内存使用
            const processMemory = process.memoryUsage();
            const heapUsed = processMemory.heapUsed;
            const heapTotal = processMemory.heapTotal;
            const memoryUsage = (heapUsed / heapTotal) * 100;
            return memoryUsage;
        } else {
            // VPS环境：监控系统内存
            const totalMemory = os.totalmem();
            const freeMemory = os.freemem();
            const usedMemory = totalMemory - freeMemory;
            const memoryUsage = (usedMemory / totalMemory) * 100;
            return memoryUsage;
        }
    }

    /**
     * 检查是否超过警告阈值
     * @param {number} cpuUsage - CPU使用率
     * @param {number} memoryUsage - 内存使用率
     */
    function checkWarningThresholds(cpuUsage, memoryUsage) {
        // 警告已经在logger.logPerformance中处理
        // 这里可以进行其他必要的处理
    }

    /**
     * 记录性能数据到日志文件
     * @param {number} timestamp - 时间戳
     * @param {number} cpuUsage - CPU使用率
     * @param {number} memoryUsage - 内存使用率
     */
    function logPerformanceData(timestamp, cpuUsage, memoryUsage) {
        try {
            const content = `CPU: ${cpuUsage.toFixed(2)}% MEM: ${memoryUsage.toFixed(2)}%`;
            logUtils.writeLog(LOG_TYPES.PERFORMANCE, content);
        } catch (err) {
            console.error('记录性能数据到日志文件失败:', err);
        }
    }

    /**
     * 记录API响应时间
     * @param {string} endpoint - API端点
     * @param {number} responseTime - 响应时间（毫秒）
     */
    function recordApiResponseTime(endpoint, responseTime) {
        try {
            // 如果没有该端点的数据，初始化数组
            if (!performanceData.apiResponseTime[endpoint]) {
                performanceData.apiResponseTime[endpoint] = [];
            }

            // 添加数据
            performanceData.apiResponseTime[endpoint].push(responseTime);

            // 限制数据点数量
            if (performanceData.apiResponseTime[endpoint].length > config.dataPoints) {
                performanceData.apiResponseTime[endpoint].shift();
            }

            // 检查是否超过警告阈值
            if (responseTime > config.apiResponseTimeWarningThreshold) {
                console.warn(`[性能监控警告] API响应时间过长: ${endpoint} ${responseTime}ms`);
            }

            // 控制台输出
            if (config.enableConsoleOutput) {
                console.log(`[性能监控] API响应时间: ${endpoint} ${responseTime}ms`);
            }
        } catch (err) {
            console.error('记录API响应时间失败:', err);
        }
    }

    /**
     * 记录数据库查询时间
     * @param {string} query - 查询语句或标识符
     * @param {number} queryTime - 查询时间（毫秒）
     */
    function recordDbQueryTime(query, queryTime) {
        try {
            // 使用查询的前20个字符作为标识符
            const queryId = query.substring(0, 20).replace(/\s+/g, ' ').trim();

            // 如果没有该查询的数据，初始化数组
            if (!performanceData.dbQueryTime[queryId]) {
                performanceData.dbQueryTime[queryId] = [];
            }

            // 添加数据
            performanceData.dbQueryTime[queryId].push(queryTime);

            // 限制数据点数量
            if (performanceData.dbQueryTime[queryId].length > config.dataPoints) {
                performanceData.dbQueryTime[queryId].shift();
            }

            // 检查是否超过警告阈值
            if (queryTime > config.dbQueryTimeWarningThreshold) {
                console.warn(`[性能监控警告] 数据库查询时间过长: ${queryId} ${queryTime}ms`);
            }

            // 控制台输出
            if (config.enableConsoleOutput) {
                console.log(`[性能监控] 数据库查询时间: ${queryId} ${queryTime}ms`);
            }
        } catch (err) {
            console.error('记录数据库查询时间失败:', err);
        }
    }

    /**
     * 获取性能数据
     * @returns {Object} 性能数据
     */
    function getPerformanceData() {
        return performanceData;
    }

    // 初始化模块
    init();

    // 将性能监控模块添加到svr.locals中
    const performanceMonitor = {
        recordApiResponseTime,
        recordDbQueryTime,
        getPerformanceData
    };

    svr.locals.performanceMonitor = performanceMonitor;

    console.log('[性能监控] 模块已加载');

    return performanceMonitor;
};
