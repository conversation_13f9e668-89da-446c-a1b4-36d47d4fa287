"use strict";

const nodemailer = require('nodemailer');
const BaseProvider = require('./base-provider');

class EmailProvider extends BaseProvider {
  constructor(db, renderer) {
    super();
    this.db = db;
    this.renderer = renderer;
    this.transporter = null;
    this.rate = { capacity: 60, tokens: 60, refillMs: 1000, last: Date.now() };
  }

  async init() {
    const cfg = await this.db.emailConfig.get();
    if (!cfg || !cfg.enabled) return { ok: false, reason: 'disabled' };
    this.rate.capacity = cfg.rate_limit_per_min || 60;
    this.rate.tokens = this.rate.capacity;
    // 创建transporter
    this.transporter = nodemailer.createTransport({
      host: cfg.host,
      port: cfg.port,
      secure: !!cfg.secure,
      auth: cfg.auth_user ? { user: cfg.auth_user, pass: cfg.auth_pass } : undefined,
      tls: { rejectUnauthorized: false }
    });
    try {
      await this.transporter.verify();
      return { ok: true };
    } catch (e) {
      return { ok: false, reason: e.message };
    }
  }

  _refill() {
    const now = Date.now();
    const dt = now - this.rate.last;
    if (dt > 0) {
      const add = Math.floor((dt / 60000) * this.rate.capacity);
      if (add > 0) {
        this.rate.tokens = Math.min(this.rate.capacity, this.rate.tokens + add);
        this.rate.last = now;
      }
    }
  }

  async send({ type, title, content, meta = {} }) {
    const cfg = await this.db.emailConfig.get();
    if (!cfg || !cfg.enabled || !this.transporter) return { ok: false, reason: 'disabled' };

    // 类型开关
    const typeKeyMap = {
      '服务器恢复': 'serverOnline',
      '服务器掉线': 'serverOffline',
      '流量超限': 'trafficLimit',
      '测试通知': 'testNotification',
      '状态汇总': 'statusSummary'
    };
    const typeKey = typeKeyMap[title] || 'custom';
    if (cfg.notification_types && cfg.notification_types[typeKey] === false) {
      return { ok: false, reason: 'type_disabled' };
    }

    // 限速
    this._refill();
    if (this.rate.tokens <= 0) {
      return { ok: false, reason: 'rate_limited' };
    }
    this.rate.tokens -= 1;

    // 渲染模板
    const render = await this.renderer.render(typeKey === 'testNotification' ? 'test' : 'alert', {
      site: meta.site || { name: 'DStatus', url: '' },
      title: title,
      content: content,
      data: meta
    });
    const subject = `[DStatus] ${title}`;
    const to = (cfg.default_to || []).join(',');
    const cc = (cfg.default_cc || []).join(',');
    const bcc = (cfg.default_bcc || []).join(',');

    // 写入日志（pending）
    const logId = await this.db.emailLogs.create({ to: cfg.default_to || [], cc: cfg.default_cc || [], bcc: cfg.default_bcc || [], subject, body_text: content, body_html: render.html, template: render.template, payload: meta, type });

    try {
      const info = await this.transporter.sendMail({
        from: { name: cfg.from_name || 'DStatus', address: cfg.from_address },
        to, cc, bcc,
        subject,
        text: content,
        html: render.html
      });
      await this.db.emailLogs.mark(logId, { status: 'sent', provider_message_id: info?.messageId || null });
      return { ok: true };
    } catch (e) {
      await this.db.emailLogs.mark(logId, { status: 'failed', error: e.message });
      return { ok: false, reason: e.message };
    }
  }
}

module.exports = EmailProvider;

