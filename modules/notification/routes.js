// 通知设置路由模块
"use strict";

const express = require('express');

module.exports = (svr) => {
    const { db, notification } = svr.locals;
    const router = express.Router();
    
    // 通知设置页面
    router.get('/admin/notification', async (req, res) => {
        try {
            if (!req.admin) {
                return res.redirect('/login');
            }

            const setting = await db.setting.all();
            res.render('admin/notification', { 
                setting: setting,
                currentPage: 'notification'
            });
        } catch (error) {
            console.error('加载通知设置页面失败:', error);
            res.status(500).send('服务器内部错误');
        }
    });
    
    // 保存通知设置接口 - 只操作telegram键
    router.post('/admin/notification/save', async (req, res) => {
        try {
            if (!req.admin) {
                return res.json({ code: 0, msg: '需要管理员权限' });
            }

            const { telegram } = req.body;
            if (!telegram) {
                return res.json({ code: 0, msg: '缺少telegram设置数据' });
            }

            // 只保存telegram设置到数据库
            await db.setting.set('telegram', telegram);
            
            console.log('[通知系统] Telegram设置已保存');
            return res.json({ code: 1, msg: '设置保存成功' });
        } catch (error) {
            console.error('[通知系统] 保存设置失败:', error);
            return res.json({ code: 0, msg: `保存失败: ${error.message}` });
        }
    });

    // 测试通知接口
    router.post('/admin/notification/test', async (req, res) => {
        try {
            if (!req.admin) {
                return res.json({ code: 0, msg: '需要管理员权限' });
            }

            // 获取通知管理器实例
            const notificationManager = notification;
            if (!notificationManager) {
                return res.json({ code: 0, msg: '通知系统未初始化' });
            }

            // 获取Telegram设置
            const telegramSetting = await db.setting.get('telegram');
            if (!telegramSetting?.enabled) {
                return res.json({ code: 0, msg: 'Telegram通知未启用' });
            }

            if (!telegramSetting?.chatIds?.length) {
                return res.json({ code: 0, msg: '未配置Chat ID' });
            }

            // 发送测试通知
            console.log('[通知系统] 发送测试通知...');
            const result = await notificationManager.sendNotification(
                '测试通知',
                '这是一条测试通知，如果您收到这条消息，说明通知系统工作正常。',
                telegramSetting.chatIds,
                { priority: 'normal' }
            );

            if (result.success) {
                console.log('[通知系统] 测试通知发送成功');
                return res.json({ code: 1, msg: '测试通知已发送' });
            } else {
                console.error('[通知系统] 测试通知发送失败:', result.error);
                return res.json({ code: 0, msg: `发送失败: ${result.error}`, details: result.details });
            }
        } catch (error) {
            console.error('[通知系统] 测试通知发送异常:', error);
            return res.json({ code: 0, msg: `发送异常: ${error.message}` });
        }
    });
    
    // 注册路由到应用
    svr.use(router);
};