// 通知设置路由模块
"use strict";

const express = require('express');

module.exports = (svr) => {
    const { db } = svr.locals;
    const router = express.Router();
    
    // 通知设置页面
    router.get('/admin/notification', async (req, res) => {
        try {
            if (!req.admin) {
                return res.redirect('/login');
            }

            const setting = await db.setting.all();
            // 加载服务器列表用于前端筛选
            let servers = [];
            try {
                servers = await db.getServers();
            } catch (e) {
                console.warn('[通知系统] 读取服务器列表失败:', e.message);
            }
            res.render('admin/notification', { 
                setting: setting,
                servers: servers,
                currentPage: 'notification'
            });
        } catch (error) {
            console.error('加载通知设置页面失败:', error);
            res.status(500).send('服务器内部错误');
        }
    });
    
    // 保存通知设置接口 - 只操作telegram键
    router.post('/admin/notification/save', async (req, res) => {
        try {
            if (!req.admin) {
                return res.json({ code: 0, msg: '需要管理员权限' });
            }

            const { telegram } = req.body;
            if (!telegram) {
                return res.json({ code: 0, msg: '缺少telegram设置数据' });
            }

            // 只保存telegram设置到数据库
            await db.setting.set('telegram', telegram);
            
            console.log('[通知系统] Telegram设置已保存');

            // 让配置立即生效：重建并注入Bot
            try {
                const latest = await db.setting.get('telegram');
                if (latest?.enabled && latest?.token) {
                    const botOptions = {
                        webhook: latest.webhook || false,
                        webhookPort: latest.webhookPort || 8443,
                        baseApiUrl: latest.baseApiUrl || 'https://api.telegram.org'
                    };
                    const botWrapper = require('../../bot')(latest.token, latest.chatIds || [], botOptions);
                    if (botWrapper && botWrapper.bot) {
                        svr.locals.bot = botWrapper.bot;
                        if (svr.locals.notification) {
                            svr.locals.notification.setBot(svr.locals.bot);
                        }
                        console.log('[通知系统] 新的Telegram Bot已加载并生效');
                    } else {
                        console.warn('[通知系统] Bot包装器返回无效实例，跳过注入');
                    }
                } else {
                    console.log('[通知系统] Telegram未启用或缺少token，跳过重建Bot');
                }
            } catch (reinitError) {
                console.error('[通知系统] 重新初始化Bot失败:', reinitError.message);
            }

            return res.json({ code: 1, msg: '设置保存成功' });
        } catch (error) {
            console.error('[通知系统] 保存设置失败:', error);
            return res.json({ code: 0, msg: `保存失败: ${error.message}` });
        }
    });

    // 获取 Telegram 配置（JSON）
    router.get('/admin/notification/telegram/config', async (req, res) => {
        try {
            if (!req.admin) return res.json({ code: 0, msg: '需要管理员权限' });
            const tele = await db.setting.get('telegram');
            res.json({ code: 1, data: tele || {} });
        } catch (e) { res.json({ code: 0, msg: e.message }); }
    });

    // 测试通知接口
    router.post('/admin/notification/test', async (req, res) => {
        try {
            if (!req.admin) {
                return res.json({ code: 0, msg: '需要管理员权限' });
            }

            // 获取通知管理器实例
            const notificationManager = svr.locals.notification;
            if (!notificationManager) {
                return res.json({ code: 0, msg: '通知系统未初始化' });
            }

            // 获取Telegram设置
            const telegramSetting = await db.setting.get('telegram');
            if (!telegramSetting?.enabled) {
                return res.json({ code: 0, msg: 'Telegram通知未启用' });
            }

            if (!telegramSetting?.chatIds?.length) {
                return res.json({ code: 0, msg: '未配置Chat ID' });
            }

            // 发送测试通知
            console.log('[通知系统] 发送测试通知...');
            const result = await notificationManager.sendNotification(
                '测试通知',
                '这是一条测试通知，如果您收到这条消息，说明通知系统工作正常。',
                telegramSetting.chatIds,
                { priority: 'normal' }
            );

            if (result.success) {
                console.log('[通知系统] 测试通知发送成功');
                return res.json({ code: 1, msg: '测试通知已发送' });
            } else {
                console.error('[通知系统] 测试通知发送失败:', result.error);
                return res.json({ code: 0, msg: `发送失败: ${result.error}`, details: result.details });
            }
        } catch (error) {
            console.error('[通知系统] 测试通知发送异常:', error);
            return res.json({ code: 0, msg: `发送异常: ${error.message}` });
        }
    });

    // 自定义通知任务 - 列表
    router.get('/admin/notification/tasks', async (req, res) => {
        try {
            if (!req.admin) return res.json({ code: 0, msg: '需要管理员权限' });
            const tasks = await db.notificationTasks.listAll();
            return res.json({ code: 1, data: tasks });
        } catch (e) {
            console.error('[通知系统] 加载任务失败:', e);
            return res.json({ code: 0, msg: e.message });
        }
    });

    function normalizeThresholds(input) {
        let arr = input;
        if (typeof arr === 'string') {
            arr = arr.split(',');
        }
        if (!Array.isArray(arr)) return [];
        const nums = arr.map(v => parseInt(v, 10)).filter(v => !isNaN(v) && v > 0 && v < 100);
        return Array.from(new Set(nums)).sort((a,b)=>a-b);
    }
    function isValidPeriod(p) { return ['daily','weekly','monthly'].includes(p); }
    function isValidDirection(d) { return ['both','in','out','max'].includes(d); }

    // 自定义通知任务 - 新增
    router.post('/admin/notification/tasks', async (req, res) => {
        try {
            if (!req.admin) return res.json({ code: 0, msg: '需要管理员权限' });
            const { sid, period = 'monthly', traffic_limit = null, thresholds = [], direction = 'both', reset_day = null, enabled = true } = req.body || {};
            if (!sid) return res.json({ code: 0, msg: '缺少sid' });

            // 校验服务器是否存在
            const server = await db.servers.get(sid);
            if (!server) return res.json({ code: 0, msg: '服务器不存在' });

            // 校验 period/direction
            if (!isValidPeriod(period)) return res.json({ code: 0, msg: '非法周期（仅支持 daily/weekly/monthly）' });
            if (!isValidDirection(direction)) return res.json({ code: 0, msg: '非法方向（仅支持 both/in/out/max）' });

            // 校验与归一化阈值
            const ths = normalizeThresholds(thresholds);
            if (ths.length === 0 && !server.traffic_alert_percent) return res.json({ code: 0, msg: '阈值为空且无服务器单阈值可用' });

            // 校验上限与重置日
            if (traffic_limit !== null && Number(traffic_limit) < 0) return res.json({ code: 0, msg: '流量上限必须为非负数' });
            if (reset_day !== null) {
                const rd = parseInt(reset_day, 10);
                if (isNaN(rd) || rd < 1 || rd > 31) return res.json({ code: 0, msg: '重置日范围应在1-31' });
            }

            const id = await db.notificationTasks.create({ sid, period, traffic_limit, thresholds: ths, direction, reset_day, enabled });
            return res.json({ code: 1, data: { id } });
        } catch (e) {
            console.error('[通知系统] 创建任务失败:', e);
            return res.json({ code: 0, msg: e.message });
        }
    });

    // 自定义通知任务 - 删除
    router.post('/admin/notification/tasks/:id/delete', async (req, res) => {
        try {
            if (!req.admin) return res.json({ code: 0, msg: '需要管理员权限' });
            await db.notificationTasks.remove(req.params.id);
            return res.json({ code: 1, msg: '删除成功' });
        } catch (e) {
            console.error('[通知系统] 删除任务失败:', e);
            return res.json({ code: 0, msg: e.message });
        }
    });

    // 自定义通知任务 - 启用/禁用
    router.post('/admin/notification/tasks/:id/toggle', async (req, res) => {
        try {
            if (!req.admin) return res.json({ code: 0, msg: '需要管理员权限' });
            const { enabled } = req.body || {};
            const t = await db.notificationTasks.get(req.params.id);
            if (!t) return res.json({ code: 0, msg: '任务不存在' });
            await db.notificationTasks.update(req.params.id, { enabled: enabled ? 1 : 0 });
            return res.json({ code: 1, msg: '更新成功' });
        } catch (e) {
            console.error('[通知系统] 更新任务状态失败:', e);
            return res.json({ code: 0, msg: e.message });
        }
    });

    // 注册路由到应用
    svr.use(router);
    
    // 邮件配置与日志 API（JSON）
    const emailRouter = express.Router();
    
    emailRouter.get('/admin/notification/email/config', async (req, res) => {
        if (!req.admin) return res.json({ code: 0, msg: '需要管理员权限' });
        try {
            const cfg = await db.emailConfig.get();
            res.json({ code: 1, data: cfg || {} });
        } catch (e) { res.json({ code: 0, msg: e.message }); }
    });
    
    emailRouter.post('/admin/notification/email/save', async (req, res) => {
        if (!req.admin) return res.json({ code: 0, msg: '需要管理员权限' });
        try {
            const cfg = req.body || {};
            // 基本校验
            if (!cfg.host || !cfg.port || !cfg.from_address) return res.json({ code: 0, msg: '缺少必要字段(host/port/from_address)' });
            // 保留旧密码（避免覆盖为空）
            try {
                const existing = await db.emailConfig.get();
                if (!cfg.auth_pass && existing && existing.auth_pass) {
                    cfg.auth_pass = existing.auth_pass;
                }
            } catch (e) {}
            await db.emailConfig.set(cfg);
            // 重新初始化邮件通道
            if (svr.locals.notification && svr.locals.notification.emailProvider) {
                try { await svr.locals.notification.emailProvider.init(); } catch (e) {}
            }
            res.json({ code: 1, msg: '保存成功' });
        } catch (e) { res.json({ code: 0, msg: e.message }); }
    });
    
    emailRouter.post('/admin/notification/test-email', async (req, res) => {
        if (!req.admin) return res.json({ code: 0, msg: '需要管理员权限' });
        try {
            if (!svr.locals.notification || !svr.locals.notification.emailProvider) return res.json({ code: 0, msg: '邮件通道未初始化' });
            const r = await svr.locals.notification.emailProvider.send({ type: 'test', title: '测试通知', content: '这是一封测试邮件', meta: { site: svr.locals.notification.siteConfig } });
            if (r.ok) return res.json({ code: 1, msg: '测试邮件已发送' });
            return res.json({ code: 0, msg: r.reason || '发送失败' });
        } catch (e) { res.json({ code: 0, msg: e.message }); }
    });
    
    emailRouter.get('/admin/notification/email/logs', async (req, res) => {
        if (!req.admin) return res.json({ code: 0, msg: '需要管理员权限' });
        try {
            const page = parseInt(req.query.page || '1', 10);
            const pageSize = parseInt(req.query.pageSize || '20', 10);
            const list = await db.emailLogs.list(page, pageSize);
            res.json({ code: 1, data: list });
        } catch (e) { res.json({ code: 0, msg: e.message }); }
    });
    
    emailRouter.post('/admin/notification/email/logs/cleanup', async (req, res) => {
        if (!req.admin) return res.json({ code: 0, msg: '需要管理员权限' });
        try {
            const days = parseInt((req.body?.days) || 30, 10);
            await db.emailLogs.cleanup(days);
            res.json({ code: 1, msg: '清理完成' });
        } catch (e) { res.json({ code: 0, msg: e.message }); }
    });
    
    svr.use(emailRouter);
};
