const path = require('path');
const fs = require('fs');
const { logUtils, LOG_TYPES } = require('../utils/log-utils');

class NotificationManager {
    constructor(db, bot) {
        if (!db) {
            const error = new Error('[通知系统] 致命错误: 数据库实例未提供');
            console.error(error);
            throw error;
        }
        this.bot = bot;
        this.db = db;
        // 使用统一日志工具，无需单独管理目录
        // 异步初始化通知类型配置
        this.initializeNotificationTypes().catch(error => {
            console.error('[通知系统] 初始化通知类型配置失败:', error);
        });
        
        // 错误计数器
        this.errorCounts = {
            initialization: 0,
            sending: 0,
            bot: 0,
            database: 0
        };
        
        // 最后一次错误时间记录
        this.lastErrorTime = {
            initialization: null,
            sending: null,
            bot: null,
            database: null
        };
        
        // 错误阈值设置
        this.errorThresholds = {
            initialization: 3,
            sending: 5,
            bot: 3,
            database: 3
        };

        // 统一的消息图标定义
        this.messageIcons = {
            '服务器恢复': '🟢',
            '服务器掉线': '🔴',
            '流量超限': '⚠️',
            '测试通知': '🔔',
            '状态汇总': '📊',
            '系统错误': '⚠️',
            '新服务器发现': '🔍',
            '服务器批准': '✅'
        };

        // 添加通知防重复机制
        this.notificationDeduplication = {
            errors: new Map(),  // 存储错误消息的哈希
            summaries: new Map(), // 存储汇总通知的记录
            deduplicationWindow: 300000,  // 5分钟内的相同错误只发送一次
            summaryDeduplicationWindow: 60000, // 1分钟内的汇总通知去重
            maxErrorsPerWindow: 3,  // 每个时间窗口内最多发送3次相同类型的错误
            maxSummariesPerWindow: 1 // 每个时间窗口内最多发送1次汇总
        };

        // 添加系统状态跟踪
        this.systemState = {
            isHealthy: true,
            lastHealthCheck: Date.now(),
            healthCheckInterval: 60000,  // 1分钟检查一次
            consecutiveFailures: 0,
            maxConsecutiveFailures: 5
        };

        // 启动健康检查
        this.startHealthCheck();
        
        // 初始化站点配置缓存
        this.siteConfig = null;
        this.initializeSiteConfig().catch(error => {
            console.error('[通知系统] 初始化站点配置失败:', error);
        });
    }



    // 初始化通知类型配置
    async initializeNotificationTypes() {
        try {
            const telegramSetting = await this.db.setting.get('telegram') || {};
        
        // 确保notificationTypes对象存在
        if (!telegramSetting.notificationTypes) {
            telegramSetting.notificationTypes = {
                serverOnline: true,
                serverOffline: true,
                trafficLimit: true,
                testNotification: true,
                statusSummary: true,  // 状态汇总通知类型
                newServerDiscovered: true, // 新增：新服务器发现通知
                serverApproved: true  // 新增：服务器批准通知
            };
            await this.db.setting.set('telegram', telegramSetting);
        } else {
            // 确保所有必要的通知类型都存在 
            let updated = false;
            const defaultTypes = {
                serverOnline: true,
                serverOffline: true,
                trafficLimit: true,
                testNotification: true,
                statusSummary: true,  // 状态汇总通知类型
                newServerDiscovered: true, // 新增：新服务器发现通知
                serverApproved: true  // 新增：服务器批准通知
            };
            
            // 遍历默认类型，添加缺失的类型
            for (const [type, enabled] of Object.entries(defaultTypes)) {
                if (telegramSetting.notificationTypes[type] === undefined) {
                    console.log(`[通知系统] 添加缺失的通知类型: ${type}`);
                    telegramSetting.notificationTypes[type] = enabled;
                    updated = true;
                }
            }
            
            // 如果有更新，保存设置
            if (updated) {
                console.log('[通知系统] 更新通知类型配置');
                await this.db.setting.set('telegram', telegramSetting);
            }
        }
        
        // 输出当前通知类型设置情况
        console.log('[通知系统] 当前通知类型配置:', JSON.stringify(telegramSetting.notificationTypes));
        
        /* 保证离线通知延迟配置存在且合法（5-300秒），默认30秒 */
        try {
            let telegramSetting = await this.db.setting.get('telegram') || {};
            let raw = telegramSetting.offlineNotificationDelay;
            let needUpdate = false;
            let val = 30;

            if (raw === undefined || raw === null || raw === '') {
                needUpdate = true;
            } else {
                let v = parseInt(raw, 10);
                if (isNaN(v)) { v = 30; needUpdate = true; }
                v = Math.max(5, Math.min(300, v));
                if (v !== raw) { needUpdate = true; }
                val = v;
            }

            if (needUpdate) {
                telegramSetting.offlineNotificationDelay = val;
                await this.db.setting.set('telegram', telegramSetting);
                console.log('[通知系统] 初始化/归一化离线通知延迟:', val, '秒');
            }
            // 初始化/归一化流量阈值配置（全局），默认 [80, 90, 95]
            try {
                let updated = false;
                if (!Array.isArray(telegramSetting.trafficThresholds)) {
                    telegramSetting.trafficThresholds = [80, 90, 95];
                    updated = true;
                } else {
                    // 归一化：去重、过滤1-99、升序排序
                    const parsed = telegramSetting.trafficThresholds
                        .map(v => parseInt(v, 10))
                        .filter(v => !isNaN(v) && v > 0 && v < 100);
                    const norm = Array.from(new Set(parsed)).sort((a, b) => a - b);
                    if (norm.length !== telegramSetting.trafficThresholds.length ||
                        norm.some((v, i) => v !== telegramSetting.trafficThresholds[i])) {
                        telegramSetting.trafficThresholds = norm.length ? norm : [80, 90, 95];
                        updated = true;
                    }
                }
                if (updated) {
                    await this.db.setting.set('telegram', telegramSetting);
                    console.log('[通知系统] 初始化/归一化流量阈值:', telegramSetting.trafficThresholds);
                }
            } catch (e) {
                console.error('[通知系统] 流量阈值配置初始化失败:', e);
            }
        } catch (e) {
            console.error('[通知系统] 离线通知延迟配置初始化失败:', e);
        }
        } catch (error) {
            console.error('[通知系统] 初始化通知类型配置失败:', error);
            // 设置默认配置
            const defaultSetting = {
                notificationTypes: {
                    serverOnline: true,
                    serverOffline: true,
                    trafficLimit: true,
                    testNotification: true,
                    statusSummary: true,
                    newServerDiscovered: true,
                    serverApproved: true
                }
            };
            try {
                await this.db.setting.set('telegram', defaultSetting);
            } catch (setError) {
                console.error('[通知系统] 设置默认配置失败:', setError);
            }
        }
    }

    // 初始化站点配置缓存
    async initializeSiteConfig() {
        try {
            const siteConfig = await this.db.setting.get('site');
            if (siteConfig && siteConfig.name && siteConfig.url) {
                this.siteConfig = {
                    name: siteConfig.name,
                    url: siteConfig.url,
                    logo: siteConfig.logo || null,
                    brandColor: siteConfig.brandColor || null
                };
                console.log('[通知系统] 站点配置加载成功:', this.siteConfig);
            } else {
                // 使用默认配置
                this.siteConfig = {
                    name: 'DStatus',
                    url: 'https://status.nekoneko.cloud',
                    logo: null,
                    brandColor: '#6366f1'
                };
                console.warn('[通知系统] 未找到站点配置，使用默认值');
            }
        } catch (error) {
            console.error('[通知系统] 获取站点配置失败:', error);
            // 设置默认配置作为容错
            this.siteConfig = {
                name: 'DStatus',
                url: 'https://status.nekoneko.cloud',
                logo: null,
                brandColor: '#6366f1'
            };
        }
    }

    setBot(bot) {
        console.log(`[通知系统] 设置 Bot: bot对象存在=${!!bot}`);
        if (!bot) {
            console.warn('[通知系统] 警告: 尝试设置空的bot对象');
            return;
        }
        this.bot = bot;
        console.log('[通知系统] Bot设置成功，尝试发送功能函数是否存在:', !!this.bot.sendMessage);
    }

    async sendNotification(type, content, chatIds, options = {}) {
        try {
            console.log(`[通知系统] 尝试发送 ${type} 通知: bot=${!!this.bot}, chatIds=${chatIds?.length || 0}`);

            // 汇总通知去重
            if (type === '状态汇总' && !options.bypassDeduplication) {
                const now = Date.now();
                const lastSummary = this.notificationDeduplication.summaries.get('lastSummary');
                if (lastSummary && (now - lastSummary.timestamp < this.notificationDeduplication.summaryDeduplicationWindow)) {
                    console.log(`[通知系统] 汇总通知在去重窗口期内，跳过发送。距离上次发送: ${now - lastSummary.timestamp}ms`);
                    return { success: false, error: '汇总通知在去重窗口期内', errorType: 'SUMMARY_DUPLICATE' };
                }
                this.notificationDeduplication.summaries.set('lastSummary', { timestamp: now, content });
            }

            // 类型映射
            const typeMap = {
                '服务器恢复': 'serverOnline',
                '服务器掉线': 'serverOffline',
                '流量超限': 'trafficLimit',
                '测试通知': 'testNotification',
                '状态汇总': 'statusSummary',
                '系统错误': 'systemError',
                '新服务器发现': 'newServerDiscovered',
                '服务器批准': 'serverApproved'
            };
            const notificationType = typeMap[type];
            if (!notificationType) {
                const error = new Error(`[通知系统] 错误: 未知的通知类型 "${type}"`);
                this.handleSystemError('sending', error);
                return { success: false, error: error.message, errorType: 'INVALID_TYPE' };
            }

            // 格式化消息
            const message = this.formatMessage(type, content, options);
            if (!message || message.trim().length === 0) {
                const error = new Error('[通知系统] 错误: 消息内容为空');
                this.handleSystemError('sending', error);
                return { success: false, error: error.message, errorType: 'EMPTY_MESSAGE' };
            }

            const results = [];

            // Telegram 通道（不阻断其他通道）
            try {
                const tg = await this.db.setting.get('telegram');
                if (tg?.enabled && tg?.notificationTypes?.[notificationType] && this.bot && chatIds && chatIds.length > 0) {
                    for (const chatId of chatIds) {
                        try {
                            const result = await (typeof this.bot.sendMessage === 'function' ? this.bot.sendMessage(chatId, message) : this.bot.funcs?.notice?.(message));
                            results.push({ channel: 'telegram', chatId, success: true, result });
                        } catch (e) {
                            results.push({ channel: 'telegram', chatId, success: false, error: e.message });
                            this.handleSystemError('sending', e);
                        }
                    }
                }
            } catch (e) {
                results.push({ channel: 'telegram', success: false, error: e.message });
            }

            // Email 通道
            try {
                if (this.emailProvider) {
                    const emailRes = await this.emailProvider.send({ type: notificationType, title: type, content: message, meta: { site: this.siteConfig } });
                    results.push({ channel: 'email', success: !!emailRes.ok, error: emailRes.ok ? undefined : emailRes.reason });
                }
            } catch (e) {
                results.push({ channel: 'email', success: false, error: e.message });
            }

            const ok = results.some(r => r.success);
            if (!ok) {
                const err = results.map(r => `${r.channel}:${r.error}`).join('; ');
                this.logError(type, err);
                return { success: false, error: err, details: results };
            }
            this.logSuccess(type, message, { results });
            return { success: true, results };
        } catch (error) {
            this.handleSystemError('sending', error);
            return { success: false, error: error.message, errorType: 'UNEXPECTED_ERROR', stack: error.stack };
        }
    }

    handleSystemError(type, error) {
        // 更新错误计数和时间
        this.errorCounts[type] = (this.errorCounts[type] || 0) + 1;
        this.lastErrorTime[type] = new Date();

        // 生成错误消息哈希
        const errorHash = this.generateErrorHash(type, error.message);
        
        // 检查是否在去重窗口内
        const now = Date.now();
        const errorRecord = this.notificationDeduplication.errors.get(errorHash);
        
        if (errorRecord) {
            if (now - errorRecord.firstSeen < this.notificationDeduplication.deduplicationWindow) {
                if (errorRecord.count >= this.notificationDeduplication.maxErrorsPerWindow) {
                    console.log(`[通知系统] 错误通知已达到窗口限制: ${type}`);
                    return;
                }
                errorRecord.count++;
            } else {
                // 重置计数
                errorRecord.firstSeen = now;
                errorRecord.count = 1;
            }
        } else {
            // 新错误记录
            this.notificationDeduplication.errors.set(errorHash, {
                firstSeen: now,
                count: 1
            });
        }

        // 检查是否达到错误阈值
        if (this.errorCounts[type] >= this.errorThresholds[type]) {
            const errorMessage = this.formatSystemErrorMessage(type, this.errorCounts[type], error);
            
            // 发送系统错误通知
            this.sendSystemErrorNotification(errorMessage).catch(err => {
                console.error('[通知系统] 发送系统错误通知失败:', err);
            });

            // 重置错误计数
            this.errorCounts[type] = 0;
        }
    }

    generateErrorHash(type, message) {
        // 简单的哈希生成方法
        return `${type}:${message}`.slice(0, 100);
    }

    async sendSystemErrorNotification(errorMessage) {
        try {
            const telegramSetting = await this.db.setting.get('telegram');
        if (telegramSetting?.enabled && telegramSetting?.chatIds?.length > 0) {
            try {
                // 添加特殊标记防止递归
                await this.sendNotification('系统错误', errorMessage, telegramSetting.chatIds, {
                    parse_mode: 'HTML',
                    priority: 'high',
                    isSystemErrorNotification: true  // 特殊标记
                });
            } catch (sendError) {
                // 只记录日志，不再尝试发送通知
                console.error('[通知系统] 发送系统错误通知时发生错误:', sendError);
                this.logError('notification', sendError);
            }
        }
        } catch (error) {
            console.error('[通知系统] 获取Telegram设置失败:', error);
        }
    }

    formatSystemErrorMessage(type, count, error) {
        const timestamp = new Date().toLocaleString();
        const errorTypes = {
            initialization: '初始化',
            sending: '消息发送',
            bot: 'Bot',
            database: '数据库'
        };

        let content = `错误类型: ${errorTypes[type] || type}\n`;
        content += `错误次数: ${count}\n`;
        content += `最后错误: ${error.message}`;

        if (error.stack) {
            content += `\n\n错误堆栈:\n${error.stack.split('\n').slice(0, 3).join('\n')}`;
        }

        return this.formatMessage('系统错误', content, {
            timestamp,
            priority: 'high'
        });
    }

    /**
     * 统一的消息格式化方法
     * @param {string} type - 消息类型
     * @param {string} content - 消息内容
     * @param {Object} options - 格式化选项
     * @param {string} [options.timestamp] - 自定义时间戳
     * @param {Object} [options.errorDetails] - 错误详情（用于系统错误）
     * @param {string} [options.priority] - 消息优先级
     * @returns {string} 格式化后的消息
     */
    formatMessage(type, content, options = {}) {
        const timestamp = options.timestamp || new Date().toLocaleString();
        const icon = this.messageIcons[type] || '📝';
        
        let message = `${icon} ${type}\n\n`;
        
        // 处理系统错误消息
        if (type === '系统错误') {
            message += `${content}\n`;
            if (options.errorDetails) {
                message += `\n详细信息:\n${options.errorDetails}\n`;
            }
        } else {
            message += `${content}\n`;
        }
        
        // 添加时间戳
        message += `\n发送时间: ${timestamp}`;
        
        // 添加来源信息
        if (this.siteConfig) {
            message += `\n来源: ${this.siteConfig.name} (${this.siteConfig.url})`;
        }
        
        // 添加优先级标记（如果有）
        if (options.priority === 'high') {
            message = `❗️ 优先级: 高\n${message}`;
        }
        
        return message;
    }

    logSuccess(type, message, result) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            type,
            status: 'success',
            message,
            result
        };
        this.writeLog(logEntry);
    }

    logError(type, error) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            type,
            status: 'error',
            error: typeof error === 'string' ? error : {
                message: error.message,
                stack: error.stack,
                type: error.constructor.name
            }
        };
        this.writeLog(logEntry);
    }

    writeLog(logEntry) {
        try {
            logUtils.writeLog(LOG_TYPES.NOTIFICATION, logEntry);
        } catch (error) {
            console.error('[通知系统] 日志写入失败:', error);
        }
    }

    /**
     * 系统健康检查
     */
    startHealthCheck() {
        setInterval(() => {
            this.performHealthCheck();
        }, this.systemState.healthCheckInterval);
    }

    async performHealthCheck() {
        try {
            // 检查数据库连接
            const dbCheck = await this.checkDatabaseConnection();
            
            // 检查 Bot 状态
            const botCheck = await this.checkBotStatus();
            
            // 更新系统状态
            const isCurrentlyHealthy = dbCheck && botCheck;
            
            // 如果状态发生变化，记录并通知
            if (this.systemState.isHealthy !== isCurrentlyHealthy) {
                this.systemState.isHealthy = isCurrentlyHealthy;
                
                if (!isCurrentlyHealthy) {
                    this.systemState.consecutiveFailures++;
                    if (this.systemState.consecutiveFailures >= this.systemState.maxConsecutiveFailures) {
                        // 发送系统不健康通知
                        this.handleSystemStateChange(false);
                    }
                } else {
                    // 系统恢复健康，重置失败计数
                    this.systemState.consecutiveFailures = 0;
                    this.handleSystemStateChange(true);
                }
            }
            
            this.systemState.lastHealthCheck = Date.now();
        } catch (error) {
            console.error('[通知系统] 健康检查失败:', error);
        }
    }

    async checkDatabaseConnection() {
        try {
            // 修改原因：findOne 方法不存在，改用正确的 get 方法
            // 修改内容：使用 setting.get 方法检查数据库连接
            // 注意事项：确保返回布尔值表示连接状态
            const result = await this.db.setting.get('telegram');
            return true;
        } catch (error) {
            console.error('[通知系统] 数据库连接检查失败:', error);
            return false;
        }
    }

    async checkBotStatus() {
        try {
            if (!this.bot) return false;
            
            // 检查基本方法是否存在
            const hasRequiredMethods = typeof this.bot.sendMessage === 'function' || 
                                    (this.bot.funcs && typeof this.bot.funcs.notice === 'function');
            
            if (!hasRequiredMethods) {
                console.error('[通知系统] Bot 缺少必要的方法');
                return false;
            }
            
            return true;
        } catch (error) {
            console.error('[通知系统] Bot 状态检查失败:', error);
            return false;
        }
    }

    async handleSystemStateChange(isHealthy) {
        const message = isHealthy ? 
            '系统已恢复正常运行' : 
            `系统状态异常\n连续失败次数: ${this.systemState.consecutiveFailures}\n上次正常检查: ${new Date(this.systemState.lastHealthCheck).toLocaleString()}`;
        
        // 使用特殊标记防止递归
        try {
            const telegramSetting = await this.db.setting.get('telegram');
            await this.sendNotification(
                '系统状态',
                message,
                telegramSetting?.chatIds || [],
                { 
                    priority: isHealthy ? 'normal' : 'high',
                    isSystemHealthNotification: true  // 特殊标记
                }
            );
        } catch (error) {
            console.error('[通知系统] 获取Telegram设置失败:', error);
        }
    }
}

// 修改导出方式
module.exports = function(svr) {
    const { db, bot } = svr.locals;
    if (!db) {
        throw new Error('NotificationManager requires a db instance');
    }
    
    // 创建通知管理器实例
    const notificationManager = new NotificationManager(db, bot);
    
    // 使用背板变量保存当前bot引用
    let currentBot = bot;
    
    // 如果bot实例存在，立即设置
    if (currentBot) {
        notificationManager.setBot(currentBot);
    }
    
    // 监听bot实例变化 - 使用正确的getter/setter模式
    Object.defineProperty(svr.locals, 'bot', {
        configurable: true,
        enumerable: true,
        set: function(newBot) {
            currentBot = newBot;
            if (newBot) {
                console.log('[通知系统] 检测到新的bot实例，正在更新...');
                notificationManager.setBot(newBot);
            }
        },
        get: function() {
            return currentBot;
        }
    });
    
    // 加载通知模块路由
    try {
        require('./routes')(svr);
        console.log('[通知系统] 路由模块已加载');
    } catch (error) {
        console.error('[通知系统] 加载路由模块失败:', error);
    }
    
    return notificationManager;
};
