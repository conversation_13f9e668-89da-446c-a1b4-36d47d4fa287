"use strict";

const { logger } = require('../utils/logger');

/**
 * 通知触发器类 - 统一管理所有通知条件检测
 * 职责：检测各种通知条件，但不负责具体的通知发送逻辑
 */
class NotificationTriggers {
    constructor(notificationManager, database) {
        this.notification = notificationManager;
        this.db = database;
        
        // 服务器状态缓存
        this.serverStatusCache = {};
        
        // 流量状态缓存 - 避免重复流量通知
        this.trafficStatusCache = {};
        
        // 延迟离线通知管理器
        this.offlineNotificationTimers = new Map();
        
        // 系统初始化阶段标志
        this.initialStatusCollectionComplete = false;
        
        // 系统启动时间（用于优雅期判断）
        this.SYSTEM_START_TIME = Date.now();
        
        // 常量配置
        this.INITIALIZATION_PERIOD = 30 * 1000; // 30秒初始化期
        this.STARTUP_GRACE_PERIOD = 10 * 60 * 1000; // 10分钟启动缓冲期
        
        // 初始化定时器
        this.setupInitializationTimer();
        
        logger.info('[通知触发器] 已初始化');
    }
    
    /**
     * 设置初始化定时器
     */
    setupInitializationTimer() {
        setTimeout(() => {
            this.initialStatusCollectionComplete = true;
            logger.info('[通知触发器] 初始化阶段完成，开始发送状态变化通知');
        }, this.INITIALIZATION_PERIOD);
    }
    
    /**
     * 初始化服务器状态
     */
    async initializeServerStates() {
        try {
            const servers = await this.db.getServers();
            
            for (const server of servers) {
                if (server.status > 0) {
                    // 根据服务器当前状态初始化缓存
                    // 这里不发送通知，只是建立初始状态
                    this.serverStatusCache[server.sid] = null; // 初始状态为未知
                }
            }
            
            logger.info(`[通知触发器] 已初始化 ${servers.length} 个服务器的状态缓存`);
        } catch (error) {
            logger.error('[通知触发器] 初始化服务器状态失败:', error);
        }
    }
    
    /**
     * 检查服务器状态变化并触发通知
     * @param {string} sid - 服务器ID
     * @param {Object} server - 服务器信息
     * @param {boolean} isOnline - 当前是否在线
     * @param {Object} stats - 统计数据（可选）
     */
    async checkServerStatusChange(sid, server, isOnline, stats = null) {
        try {
            const oldStatus = this.serverStatusCache[sid];
            const newStatus = isOnline;
            
            // 避免重复通知：如果状态没有变化，直接返回
            if (oldStatus === newStatus) {
                return;
            }
            
            // 更新状态缓存
            this.serverStatusCache[sid] = newStatus;
            
            // 判断是否需要发送通知
            const isInitialPeriod = !this.initialStatusCollectionComplete;
            
            if (isOnline) {
                // 服务器上线
                const shouldNotifyOnline = !isInitialPeriod && oldStatus === false;
                
                if (shouldNotifyOnline) {
                    await this.handleServerOnlineNotification(sid, server);
                }
            } else {
                // 服务器下线
                const timeSinceStartup = Date.now() - this.SYSTEM_START_TIME;
                const isAfterGracePeriod = timeSinceStartup > this.STARTUP_GRACE_PERIOD;
                const shouldNotifyOffline = !isInitialPeriod && oldStatus === true && isAfterGracePeriod;
                
                if (shouldNotifyOffline) {
                    await this.handleServerOfflineNotification(sid, server);
                }
            }
        } catch (error) {
            logger.error(`[通知触发器] 检查服务器状态变化失败 ${server.name}:`, error);
        }
    }
    
    /**
     * 处理服务器上线通知
     */
    async handleServerOnlineNotification(sid, server) {
        try {
            const telegramSetting = await this.db.setting.get('telegram');
            
            if (telegramSetting?.enabled && 
                telegramSetting?.chatIds?.length > 0 && 
                telegramSetting?.notificationTypes?.serverOnline) {
                
                const message = `#恢复 ${server.name} ${new Date().toLocaleString()}`;
                logger.info(`[通知触发器] 发送服务器恢复通知: ${server.name}`);
                
                // 清理待发送的离线通知
                if (this.offlineNotificationTimers.has(sid)) {
                    clearTimeout(this.offlineNotificationTimers.get(sid));
                    this.offlineNotificationTimers.delete(sid);
                    logger.info(`[通知触发器] 服务器恢复，已取消待发送离线通知: ${server.name}`);
                }
                
                // 发送恢复通知（包含重试机制）
                await this.sendNotificationWithRetry('服务器恢复', message, telegramSetting.chatIds, server.name);
            }
        } catch (error) {
            logger.error(`[通知触发器] 处理服务器上线通知失败 ${server.name}:`, error);
        }
    }
    
    /**
     * 处理服务器下线通知
     */
    async handleServerOfflineNotification(sid, server) {
        try {
            const telegramSetting = await this.db.setting.get('telegram');
            
            if (telegramSetting?.enabled && 
                telegramSetting?.chatIds?.length > 0 && 
                telegramSetting?.notificationTypes?.serverOffline) {
                
                const message = `#掉线 ${server.name} ${new Date().toLocaleString()}`;
                logger.info(`[通知触发器] 发送服务器掉线通知(状态从在线变为离线): ${server.name}`);
                
                // 使用延迟通知机制
                await this.sendDelayedOfflineNotification(sid, server.name, message, telegramSetting);
            }
        } catch (error) {
            logger.error(`[通知触发器] 处理服务器下线通知失败 ${server.name}:`, error);
        }
    }
    
    /**
     * 发送延迟的离线通知
     * @param {string} sid - 服务器ID
     * @param {string} serverName - 服务器名称
     * @param {string} message - 通知消息
     * @param {Object} telegramSetting - Telegram配置
     */
    async sendDelayedOfflineNotification(sid, serverName, message, telegramSetting) {
        // 如果有待发送的通知，先清除
        if (this.offlineNotificationTimers.has(sid)) {
            clearTimeout(this.offlineNotificationTimers.get(sid));
            this.offlineNotificationTimers.delete(sid);
        }
        
        // 读取延迟配置（5-300秒，默认30秒）
        const delay = telegramSetting.offlineNotificationDelay || 30;
        logger.info(`[通知触发器] 设置${delay}秒延迟通知: ${serverName}`);
        
        // 设置延迟通知
        const timer = setTimeout(async () => {
            // 延迟期满后，再次检查服务器状态
            if (this.serverStatusCache[sid] === false) {
                // 服务器仍然离线，发送通知
                logger.info(`[通知触发器] 延迟通知发送: ${serverName}`);
                await this.sendNotificationWithRetry('服务器掉线', message, telegramSetting.chatIds, serverName);
            } else {
                logger.info(`[通知触发器] 服务器已恢复，取消延迟通知: ${serverName}`);
            }
            this.offlineNotificationTimers.delete(sid);
        }, delay * 1000);
        
        this.offlineNotificationTimers.set(sid, timer);
    }
    
    /**
     * 检查流量超限并触发通知
     * @param {string} sid - 服务器ID
     * @param {Object} server - 服务器信息
     * @param {Object} trafficStats - 流量统计数据
     */
    async checkTrafficLimit(sid, server, trafficStats) {
        try {
            if (!server.traffic_limit || server.traffic_limit <= 0) {
                return;
            }
            
            const alertThreshold = server.traffic_alert_percent || 80;
            const currentStatus = trafficStats.status; // normal/warning/critical
            const previousStatus = this.trafficStatusCache[sid];
            
            // 检查是否需要发送通知（状态变化 + 达到阈值）
            if (currentStatus !== 'normal' && 
                trafficStats.ratio >= alertThreshold &&
                currentStatus !== previousStatus) {
                
                const telegramSetting = await this.db.setting.get('telegram');
                
                if (telegramSetting?.enabled && 
                    telegramSetting?.notificationTypes?.trafficLimit) {
                    
                    const usedGB = (trafficStats.used / 1024 / 1024 / 1024).toFixed(2);
                    const limitGB = (trafficStats.limit / 1024 / 1024 / 1024).toFixed(2);
                    const message = `#流量超限 ${server.name} 已使用${trafficStats.ratio.toFixed(1)}% (${usedGB}GB/${limitGB}GB)`;
                    
                    await this.sendNotificationWithRetry('流量超限', message, telegramSetting.chatIds, server.name);
                    logger.info(`[通知触发器] 流量超限通知已发送: ${server.name}`);
                }
            }
            
            // 更新缓存状态
            this.trafficStatusCache[sid] = currentStatus;
            
        } catch (error) {
            logger.error(`[通知触发器] 流量超限检测失败 ${server.name}:`, error);
        }
    }
    
    /**
     * 带重试机制的通知发送
     * @param {string} title - 通知标题
     * @param {string} message - 通知消息
     * @param {Array} chatIds - 接收者列表
     * @param {string} serverName - 服务器名称（用于日志）
     * @param {number} maxRetries - 最大重试次数
     */
    async sendNotificationWithRetry(title, message, chatIds, serverName, maxRetries = 3) {
        let success = false;
        let retryCount = 0;
        
        while (!success && retryCount < maxRetries) {
            try {
                const notificationResult = await this.notification.sendNotification(title, message, chatIds);
                
                if (notificationResult.success) {
                    success = true;
                    logger.info(`[通知触发器] 通知发送成功: ${serverName}`);
                } else {
                    throw new Error(notificationResult.error || '通知系统返回失败');
                }
            } catch (error) {
                retryCount++;
                if (retryCount < maxRetries) {
                    logger.warn(`[通知触发器] 通知发送失败(尝试 ${retryCount}/${maxRetries}): ${serverName}`, error.message);
                    await this.sleep(2000 * retryCount); // 递增延迟
                } else {
                    logger.error(`[通知触发器] 通知发送失败，已达到最大重试次数: ${serverName}`, error.message);
                }
            }
        }
        
        return success;
    }
    
    /**
     * 清理已删除服务器的缓存
     * @param {Set} activeServerIds - 当前活动的服务器ID集合
     */
    cleanupCache(activeServerIds) {
        // 清理状态缓存
        for (const sid in this.serverStatusCache) {
            if (!activeServerIds.has(sid)) {
                delete this.serverStatusCache[sid];
            }
        }
        
        // 清理流量状态缓存
        for (const sid in this.trafficStatusCache) {
            if (!activeServerIds.has(sid)) {
                delete this.trafficStatusCache[sid];
            }
        }
        
        // 清理待发送的离线通知
        for (const [sid, timer] of this.offlineNotificationTimers.entries()) {
            if (!activeServerIds.has(sid)) {
                clearTimeout(timer);
                this.offlineNotificationTimers.delete(sid);
            }
        }
    }
    
    /**
     * 获取服务器状态
     */
    getServerStatus(sid) {
        return this.serverStatusCache[sid];
    }
    
    /**
     * 工具方法：延迟函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 清理资源
     */
    cleanup() {
        // 清理所有待发送的通知定时器
        for (const timer of this.offlineNotificationTimers.values()) {
            clearTimeout(timer);
        }
        this.offlineNotificationTimers.clear();
        
        logger.info('[通知触发器] 资源清理完成');
    }
}

module.exports = NotificationTriggers;