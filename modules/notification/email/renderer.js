"use strict";

const nunjucks = require('nunjucks');
const path = require('path');

class EmailRenderer {
  constructor() {
    const templatesPath = path.join(__dirname, 'templates');
    this.env = nunjucks.configure(templatesPath, { autoescape: true, noCache: true });
  }

  async render(template, data) {
    const tpl = template === 'test' ? 'test.html' : 'alert.html';
    const html = this.env.render(tpl, data || {});
    return { html, template: tpl };
  }
}

module.exports = EmailRenderer;

