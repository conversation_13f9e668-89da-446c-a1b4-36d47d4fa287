/**
 * 统一配置管理器
 * 负责解析全局配置、类型转换、动态更新、缓存优化
 * 
 * 核心功能:
 * 1. 多源配置解析 (环境变量 > 配置文件 > 数据库 > 默认值)
 * 2. 类型安全和验证
 * 3. 动态配置更新
 * 4. 缓存优化
 * 5. 配置变更通知
 */

const { CONFIG_SCHEMA, CONFIG_META } = require('../config/global-config');
const EventEmitter = require('events');

class ConfigManager extends EventEmitter {
    constructor() {
        super();
        
        // 配置缓存
        this.configCache = new Map();
        this.dbInstance = null;
        this.initialized = false;
        
        // 缓存TTL (30秒)
        this.cacheTTL = 30000;
        this.cacheTimestamps = new Map();
        
        // 配置变更监听器
        this.changeListeners = new Map();
        
        // 初始化状态
        this.initPromise = null;
    }

    /**
     * 初始化配置管理器
     * @param {Object} db - 数据库实例
     */
    async initialize(db) {
        if (this.initialized) return;
        
        // 避免重复初始化
        if (this.initPromise) return this.initPromise;
        
        this.initPromise = this._doInitialize(db);
        return this.initPromise;
    }

    async _doInitialize(db) {
        try {
            this.dbInstance = db;
            
            // 设置初始化标志，允许配置读取
            this.initialized = true;
            
            // 预加载关键配置到缓存
            await this._preloadConfigs();
            
            // 设置定期清理缓存
            this._setupCacheCleanup();
            
            this.emit('initialized');
            
            console.log('[配置管理器] 初始化完成');
        } catch (error) {
            console.error('[配置管理器] 初始化失败:', error.message);
            this.initialized = false;
            throw error;
        }
    }

    /**
     * 预加载关键配置
     */
    async _preloadConfigs() {
        const criticalConfigs = [
            'system.debug',
            'system.logLevel', 
            'websocket.maxConnections',
            'websocket.updateInterval',
            'monitoring.pollingInterval'
        ];

        for (const configPath of criticalConfigs) {
            try {
                await this.get(configPath);
            } catch (error) {
                console.warn(`[配置管理器] 预加载配置失败: ${configPath}`, error.message);
            }
        }
    }

    /**
     * 获取配置值 - 主要API
     * @param {string} path - 配置路径 (如: 'websocket.maxConnections')
     * @returns {*} 配置值
     */
    async get(path) {
        // 等待初始化完成
        if (this.initPromise && !this.initialized) {
            await this.initPromise;
        }
        
        if (!this.initialized) {
            throw new Error('[配置管理器] 未初始化，请先调用 initialize()');
        }

        // 检查缓存
        const cacheKey = path;
        const cached = this.configCache.get(cacheKey);
        const cacheTime = this.cacheTimestamps.get(cacheKey);
        
        if (cached !== undefined && cacheTime && (Date.now() - cacheTime) < this.cacheTTL) {
            return cached;
        }

        // 解析配置路径
        const [group, key] = path.split('.');
        if (!group || !key) {
            throw new Error(`[配置管理器] 无效的配置路径: ${path}`);
        }

        const schema = CONFIG_SCHEMA[group]?.[key];
        if (!schema) {
            throw new Error(`[配置管理器] 配置不存在: ${path}`);
        }

        // 获取配置值
        let value;
        try {
            if (schema.calculated) {
                // 计算型配置
                value = await this._calculateValue(schema, path);
            } else {
                // 普通配置
                value = await this._resolveValue(schema, path);
            }

            // 类型转换和验证
            value = this._convertAndValidateValue(value, schema, path);

            // 缓存结果
            this.configCache.set(cacheKey, value);
            this.cacheTimestamps.set(cacheKey, Date.now());

            return value;
        } catch (error) {
            console.error(`[配置管理器] 获取配置失败: ${path}`, error.message);
            
            // 返回默认值作为降级处理
            if (schema.default !== undefined) {
                const defaultValue = this._convertAndValidateValue(schema.default, schema, path);
                console.warn(`[配置管理器] 使用默认值: ${path} = ${defaultValue}`);
                return defaultValue;
            }
            
            throw error;
        }
    }

    /**
     * 解析配置值（从多个源）
     */
    async _resolveValue(schema, path) {
        const sources = schema.sources || [];
        
        // 按优先级依次尝试各个配置源
        for (const source of sources) {
            let value;
            
            if (source.startsWith('env:')) {
                // 环境变量
                const envKey = source.substring(4);
                value = process.env[envKey];
            } else if (source.startsWith('db:')) {
                // 数据库设置
                const dbKey = source.substring(3);
                if (this.dbInstance && this.dbInstance.setting) {
                    try {
                        value = await this.dbInstance.setting.get(dbKey);
                    } catch (error) {
                        console.debug(`[配置管理器] 数据库配置读取失败: ${dbKey}`, error.message);
                        continue;
                    }
                }
            }
            
            // 如果找到值，返回
            if (value !== undefined && value !== null && value !== '') {
                return value;
            }
        }
        
        // 所有源都没有找到，使用默认值
        return schema.default;
    }

    /**
     * 计算型配置值
     */
    async _calculateValue(schema, path) {
        if (typeof schema.formula === 'function') {
            // 创建配置代理对象，用于公式计算
            const configProxy = this._createConfigProxy();
            return schema.formula(configProxy);
        }
        
        throw new Error(`[配置管理器] 无效的计算公式: ${path}`);
    }

    /**
     * 创建配置代理对象（用于计算型配置）
     */
    _createConfigProxy() {
        const self = this;
        
        return new Proxy({}, {
            get(target, prop) {
                if (typeof prop === 'string' && prop in CONFIG_SCHEMA) {
                    return new Proxy({}, {
                        get(groupTarget, groupProp) {
                            if (typeof groupProp === 'string') {
                                const path = `${prop}.${groupProp}`;
                                // 直接从缓存读取，避免递归
                                const cached = self.configCache.get(path);
                                if (cached !== undefined) {
                                    return cached;
                                }
                                // 如果缓存没有，返回默认值
                                const schema = CONFIG_SCHEMA[prop]?.[groupProp];
                                return schema?.default;
                            }
                            return undefined;
                        }
                    });
                }
                return undefined;
            }
        });
    }

    /**
     * 类型转换和验证
     */
    _convertAndValidateValue(value, schema, path) {
        if (value === undefined || value === null) {
            if (schema.default !== undefined) {
                return schema.default;
            }
            throw new Error(`[配置管理器] 配置值为空且无默认值: ${path}`);
        }

        // 类型转换
        let convertedValue = value;
        
        switch (schema.type) {
            case 'boolean':
                if (typeof value === 'string') {
                    convertedValue = value.toLowerCase() === 'true' || value === '1';
                } else {
                    convertedValue = Boolean(value);
                }
                break;
                
            case 'integer':
                convertedValue = parseInt(value, 10);
                if (isNaN(convertedValue)) {
                    throw new Error(`[配置管理器] 无法转换为整数: ${path} = ${value}`);
                }
                break;
                
            case 'float':
                convertedValue = parseFloat(value);
                if (isNaN(convertedValue)) {
                    throw new Error(`[配置管理器] 无法转换为浮点数: ${path} = ${value}`);
                }
                break;
                
            case 'string':
                convertedValue = String(value);
                break;
                
            default:
                // 保持原值
                break;
        }

        // 值验证
        this._validateValue(convertedValue, schema, path);
        
        return convertedValue;
    }

    /**
     * 值验证
     */
    _validateValue(value, schema, path) {
        // 枚举值验证
        if (schema.enum && !schema.enum.includes(value)) {
            throw new Error(`[配置管理器] 配置值不在允许范围内: ${path} = ${value}, 允许值: ${schema.enum.join(', ')}`);
        }

        // 数值范围验证
        if (schema.type === 'integer' || schema.type === 'float') {
            if (schema.min !== undefined && value < schema.min) {
                throw new Error(`[配置管理器] 配置值小于最小值: ${path} = ${value}, 最小值: ${schema.min}`);
            }
            if (schema.max !== undefined && value > schema.max) {
                throw new Error(`[配置管理器] 配置值大于最大值: ${path} = ${value}, 最大值: ${schema.max}`);
            }
        }
    }

    /**
     * 设置配置值（写入数据库）
     */
    async set(path, value) {
        if (!this.initialized) {
            throw new Error('[配置管理器] 未初始化');
        }

        const [group, key] = path.split('.');
        const schema = CONFIG_SCHEMA[group]?.[key];
        
        if (!schema) {
            throw new Error(`[配置管理器] 配置不存在: ${path}`);
        }

        if (schema.calculated) {
            throw new Error(`[配置管理器] 计算型配置不能修改: ${path}`);
        }

        // 类型转换和验证
        const validatedValue = this._convertAndValidateValue(value, schema, path);

        // 查找数据库配置源
        const dbSource = schema.sources?.find(s => s.startsWith('db:'));
        if (!dbSource) {
            throw new Error(`[配置管理器] 配置不支持动态修改: ${path}`);
        }

        const dbKey = dbSource.substring(3);
        
        try {
            // 写入数据库
            await this.dbInstance.setting.set(dbKey, validatedValue);
            
            // 清除缓存
            this.configCache.delete(path);
            this.cacheTimestamps.delete(path);
            
            // 发出配置变更事件
            this.emit('configChanged', { path, value: validatedValue, oldValue: await this.get(path) });
            
            console.log(`[配置管理器] 配置已更新: ${path} = ${validatedValue}`);
        } catch (error) {
            console.error(`[配置管理器] 配置更新失败: ${path}`, error.message);
            throw error;
        }
    }

    /**
     * 清除缓存
     */
    clearCache(path) {
        if (path) {
            this.configCache.delete(path);
            this.cacheTimestamps.delete(path);
        } else {
            this.configCache.clear();
            this.cacheTimestamps.clear();
        }
    }

    /**
     * 获取所有配置
     */
    async getAll() {
        const result = {};
        
        for (const [groupName, group] of Object.entries(CONFIG_SCHEMA)) {
            result[groupName] = {};
            
            for (const keyName of Object.keys(group)) {
                try {
                    result[groupName][keyName] = await this.get(`${groupName}.${keyName}`);
                } catch (error) {
                    console.warn(`[配置管理器] 获取配置失败: ${groupName}.${keyName}`, error.message);
                    result[groupName][keyName] = null;
                }
            }
        }
        
        return result;
    }

    /**
     * 获取配置元数据
     */
    getMeta() {
        return CONFIG_META;
    }

    /**
     * 设置定期缓存清理
     */
    _setupCacheCleanup() {
        setInterval(() => {
            const now = Date.now();
            
            for (const [key, timestamp] of this.cacheTimestamps.entries()) {
                if (now - timestamp > this.cacheTTL) {
                    this.configCache.delete(key);
                    this.cacheTimestamps.delete(key);
                }
            }
        }, 60000); // 每分钟清理一次
    }

    /**
     * 健康检查
     */
    async healthCheck() {
        try {
            // 检查关键配置是否可读取
            await this.get('system.logLevel');
            await this.get('websocket.maxConnections');
            
            return {
                status: 'healthy',
                initialized: this.initialized,
                cacheSize: this.configCache.size,
                lastUpdated: CONFIG_META.lastUpdated
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
                initialized: this.initialized
            };
        }
    }
}

// 创建全局单例
const configManager = new ConfigManager();

// 便捷的访问函数
const config = {
    // 初始化
    init: (db) => configManager.initialize(db),
    
    // 获取配置
    get: (path) => configManager.get(path),
    
    // 设置配置  
    set: (path, value) => configManager.set(path, value),
    
    // 清除缓存
    clearCache: (path) => configManager.clearCache(path),
    
    // 获取所有配置
    getAll: () => configManager.getAll(),
    
    // 元数据
    getMeta: () => configManager.getMeta(),
    
    // 健康检查
    healthCheck: () => configManager.healthCheck(),
    
    // 事件监听
    on: (event, listener) => configManager.on(event, listener),
    
    // 原始管理器实例
    manager: configManager
};

module.exports = config;