"use strict";
const EventEmitter = require('events');

/**
 * 系统事件管理模块
 * 提供全局事件发射器，用于模块间通信
 */
module.exports = (svr) => {
    // 创建全局事件发射器
    const eventEmitter = new EventEmitter();
    
    // 设置最大监听器数量，避免内存泄漏警告
    eventEmitter.setMaxListeners(30);
    
    // 添加调试日志 - 延迟设置以等待数据库初始化
    setTimeout(async () => {
        try {
            const debugMode = svr.locals.db?.setting ? 
                await svr.locals.db.setting.get('debug') : false;
            
            if (debugMode) {
                const originalEmit = eventEmitter.emit;
                eventEmitter.emit = function(type, ...args) {
                    console.log(`[事件系统] 发出事件: ${type}`);
                    return originalEmit.apply(this, [type, ...args]);
                };
                console.log('[事件系统] 调试模式已启用');
            }
        } catch (error) {
            // 静默处理错误，不影响事件系统正常工作
            console.warn('[事件系统] 无法检查调试模式设置:', error.message);
        }
    }, 1000); // 1秒后检查
    
    // 将事件发射器添加到svr.locals中，使其在整个应用中可用
    svr.locals.eventEmitter = eventEmitter;
    
    console.log('[事件系统] 事件模块已加载');
    
    return eventEmitter;
};
