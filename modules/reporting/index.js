"use strict";

/**
 * 服务器状态上报模块
 * 处理客户端主动上报的状态数据
 */
module.exports = async (svr) => {
    const { db, pr } = svr.locals;
    const eventEmitter = svr.locals.eventEmitter;

    // 配置常量
    const CONFIG = {
        CLEANUP_INTERVAL: 60 * 60 * 1000,    // 1小时清理间隔
        DEFAULT_WINDOW_MS: 60 * 60 * 1000,   // 1小时时间窗口
        DEFAULT_MAX_COUNT: 10                // 默认最大上报次数
    };

    // 获取设置
    const reportingSettings = await db.setting.get('reporting') || {
        enabled: true,                   // 是否启用上报功能
        integrateWithAutodiscovery: false, // 是否与自动发现集成（默认关闭，防止未授权注册）
        validateData: true,              // 是否验证上报数据
        logLevel: 'normal',              // 日志级别: minimal, normal, verbose
        requireApiKey: false,            // 是否要求API密钥验证，设为true时强制要求密钥验证
        unknownServerReportLimit: {
            maxCount: CONFIG.DEFAULT_MAX_COUNT,  // 每个IP每小时最多报告10次
            windowMs: CONFIG.DEFAULT_WINDOW_MS   // 1小时时间窗口
        },
        logUnknownServers: false         // 是否记录未知服务器上报（默认关闭以减少日志噪音）
    };

    // 缓存调试设置以避免重复查询
    let debugMode = null;
    const getDebugMode = async () => {
        if (debugMode === null) {
            debugMode = await db.setting.get('debug') || false;
        }
        return debugMode;
    };

    // 保存默认设置（如果不存在）
    if (!(await db.setting.get('reporting'))) {
        await db.setting.set('reporting', reportingSettings);
    }
    
    // 未知服务器上报频率限制器
    const unknownServerReports = new Map(); // key: IP, value: { count, firstReport }
    
    // 清理过期的记录
    setInterval(() => {
        const now = Date.now();
        const windowMs = reportingSettings.unknownServerReportLimit?.windowMs || CONFIG.DEFAULT_WINDOW_MS;
        for (const [ip, data] of unknownServerReports.entries()) {
            if (now - data.firstReport > windowMs) {
                unknownServerReports.delete(ip);
            }
        }
    }, CONFIG.CLEANUP_INTERVAL);

    // 辅助格式化函数已移除
    // 如需调试或数据处理，可以添加相关代码

    // 数据摘要功能已移除
    // 如需调试，可以添加相关代码

    /**
     * 日志工具函数
     * 优化后的同步日志函数，避免异步调用开销
     */
    const log = {
        debug: (message, obj) => {
            // 使用缓存的调试模式设置
            if (debugMode || reportingSettings.logLevel === 'verbose') {
                if (obj) {
                    console.log(`[上报服务] ${message}`, obj);
                } else {
                    console.log(`[上报服务] ${message}`);
                }
            }
        },
        info: (message) => {
            if (reportingSettings.logLevel !== 'minimal') {
                console.log(`[上报服务] ${message}`);
            }
        },
        error: (message, error) => {
            console.error(`[上报服务] ${message}`, error);
        }
    };

    // 初始化调试模式缓存
    await getDebugMode();

    /**
     * 验证上报数据
     * @param {Object} data - 上报的数据
     * @returns {boolean} - 数据是否有效
     */
    function validateReportData(data) {
        if (!data) return false;

        // 基本结构验证
        if (!data.hostname) {
            log.debug('数据验证失败: 缺少主机名');
            return false;
        }

        // CPU数据验证
        if (data.cpu && typeof data.cpu.multi !== 'number') {
            log.debug('数据验证失败: CPU数据格式错误');
            return false;
        }

        // 内存数据验证
        if (data.mem && (!data.mem.virtual || typeof data.mem.virtual.used !== 'number')) {
            log.debug('数据验证失败: 内存数据格式错误');
            return false;
        }

        // 网络数据验证
        if (data.net) {
            if (!data.net.delta || !data.net.total) {
                log.debug('数据验证失败: 网络数据结构不完整');
                return false;
            }

            if (typeof data.net.delta.in !== 'number' || typeof data.net.delta.out !== 'number') {
                log.debug('数据验证失败: 网络增量数据格式错误');
                return false;
            }

            if (typeof data.net.total.in !== 'number' || typeof data.net.total.out !== 'number') {
                log.debug('数据验证失败: 网络总量数据格式错误');
                return false;
            }
        }

        return true;
    }

    /**
     * 检查未知服务器上报频率限制
     * @param {string} clientIP - 客户端IP
     * @param {string} sid - 服务器ID
     * @returns {Object} - 检查结果
     */
    function checkUnknownServerRateLimit(clientIP, sid) {
        const now = Date.now();
        const reportData = unknownServerReports.get(clientIP) || { count: 0, firstReport: now };
        
        // 如果是新的时间窗口，重置计数
        if (now - reportData.firstReport > (reportingSettings.unknownServerReportLimit?.windowMs || CONFIG.DEFAULT_WINDOW_MS)) {
            reportData.count = 0;
            reportData.firstReport = now;
        }
        
        reportData.count++;
        unknownServerReports.set(clientIP, reportData);
        
        // 只在启用日志且未超过限制时记录
        const maxCount = reportingSettings.unknownServerReportLimit?.maxCount || CONFIG.DEFAULT_MAX_COUNT;
        if (reportingSettings.logUnknownServers) {
            if (reportData.count <= maxCount) {
                log.info(`未知服务器上报数据: ${sid}, IP: ${clientIP}`);
            } else if (reportData.count === maxCount + 1) {
                // 第一次超过限制时警告
                log.info(`未知服务器上报频率过高，已达限制: ${sid}, IP: ${clientIP} (后续上报将被忽略)`);
            }
        }
        
        return { success: false, message: '未知服务器' };
    }

    /**
     * 验证API密钥
     * @param {Object} server - 服务器信息
     * @param {Object} req - 请求对象
     * @param {string} sid - 服务器ID
     * @returns {Object} - 验证结果
     */
    function validateApiKey(server, req, sid) {
        // 尝试多种方式获取请求头中的key
        // Express会将所有请求头转为小写
        let requestKey = req.headers['key'] || 
                         req.header('key') || 
                         req.get('key') || 
                         '';
                         
        // 调试信息：输出所有请求头，查看实际收到的头部信息
        if (reportingSettings.logLevel === 'verbose') {
            log.debug(`[状态上报] 请求头信息: ${JSON.stringify(req.headers)}`);
            log.debug(`[状态上报] 尝试获取的密钥: ${requestKey}`);
        }
        
        // 主动模式（agent主动上报）和被动模式（服务器查询）有不同的验证要求
        const isActiveMode = server.data?.api?.mode === true;
    
        // 拒绝被动模式节点的主动上报（静默）
        if (!isActiveMode) {
            return { success: false, message: '被动模式节点不支持主动上报' };
        }
            
        // API密钥验证 - 仅针对stat上报
        if (server.data && server.data.api && server.data.api.key) {
            // 如果服务器已配置API密钥，则进行验证
            if (requestKey && requestKey !== server.data.api.key) {
                // 密钥不匹配时才返回错误
                log.info(`[状态上报] 服务器密钥不匹配: ${server.name} (${sid}), 请求密钥: ${requestKey}, 服务器密钥: ${server.data.api.key}`);
                return { success: false, message: '密钥验证失败' };
            } else if (!requestKey && reportingSettings.requireApiKey === true && !isActiveMode) {
                // 对于非主动模式，如果系统要求密钥但请求中没有提供，则返回错误
                log.info(`[状态上报] 缺少密钥: ${server.name} (${sid}), 系统要求验证密钥`);
                return { success: false, message: '请求中未提供密钥' };
            }
            
            if (requestKey === server.data.api.key) {
                log.debug(`[状态上报] 服务器密钥验证成功: ${server.name} (${sid})`);
            } else {
                log.debug(`[状态上报] 跳过密钥验证: ${server.name} (${sid}), 模式: ${isActiveMode ? '主动' : '被动'}`);
            }
        } else if (reportingSettings.requireApiKey === true && !isActiveMode) {
            // 如果设置了强制要求API密钥且不是主动模式，则拒绝没有密钥的请求
            log.info(`[状态上报] 服务器未配置API密钥，但系统要求验证密钥: ${server.name} (${sid})`);
            return { success: false, message: '服务器未配置API密钥' };
        }
        
        return { success: true, isActiveMode };
    }

    /**
     * 更新服务器统计状态
     * @param {string} sid - 服务器ID
     * @param {Object} server - 服务器信息
     * @param {Object} data - 上报数据
     * @param {boolean} isActiveMode - 是否为主动模式
     * @returns {Object} - 更新结果
     */
    function updateServerStats(sid, server, data, isActiveMode) {
        try {
            // 获取全局stats对象
            const statsModule = require('../stats');
            const stats = statsModule.getGlobalStats();

            // 更新stats对象
            if (stats && typeof stats === 'object') {
                // 注意: 必须保持正确的数据结构
                // stats[sid].stat 必须是一个对象，而不是直接将数据存储到 stats[sid]

                // 如果是主动模式节点，保存上一次有效的状态数据
                const currentStat = stats[sid];
                const isCurrentlyOffline = currentStat && currentStat.stat === false;

                // 构建新的状态对象
                const newStats = {
                    name: server.name,
                    stat: data,  // 将上报的数据存储到 stat 属性中
                    expire_time: server.expire_time,
                    traffic_used: stats[sid]?.traffic_used || 0,
                    traffic_limit: server.traffic_limit || 0,
                    traffic_reset_day: server.traffic_reset_day || 1,
                    traffic_calibration_date: server.traffic_calibration_date || 0,
                    traffic_calibration_value: server.traffic_calibration_value || 0,
                    calibration_base_traffic: stats[sid]?.calibration_base_traffic || null,
                    last_report_time: Date.now() // 添加最后上报时间
                };

                // 如果是主动模式节点，保存当前有效的状态数据
                if (isActiveMode) {
                    newStats.lastValidStat = data;

                    // 如果当前是离线状态，则更新状态缓存
                    if (isCurrentlyOffline) {
                        // 在下一次定时任务中处理恢复在线状态
                        log.debug(`主动模式节点 ${server.name} 收到上报数据，将在下一次定时任务中恢复在线状态`);
                    }
                }

                // 更新状态
                stats[sid] = newStats;
                log.debug(`更新服务器状态: ${server.name} (${sid})`);
                return { success: true };
            } else {
                log.error(`无法更新服务器状态: 全局stats对象不可用`);
                return { success: false, message: '内部错误: 无法更新服务器状态' };
            }
        } catch (error) {
            log.error(`更新服务器状态时出错: ${sid}`, error);
            return { success: false, message: '内部错误: ' + error.message };
        }
    }

    /**
     * 处理上报数据
     * 重构后的主函数，将复杂逻辑拆分为多个小函数
     * @param {string} sid - 服务器ID
     * @param {Object} data - 上报的数据
     * @param {Object} req - 请求对象
     * @returns {Object} - 处理结果
     */
    async function processReportData(sid, data, req) {
        try {
            // 获取客户端IP地址
            const clientIP = req.headers['x-forwarded-for'] || req.connection.remoteAddress;

            // 验证数据
            if (reportingSettings.validateData && !validateReportData(data)) {
                return { success: false, message: '数据验证失败' };
            }

            // 获取服务器信息
            const server = await db.servers.get(sid);
            if (!server) {
                return checkUnknownServerRateLimit(clientIP, sid);
            }

            // 检查服务器状态
            if (server.status <= 0) {
                log.info(`禁用服务器上报数据: ${server.name} (${sid})`);
                return { success: false, message: '服务器已禁用' };
            }

            // 验证API密钥
            const keyValidation = validateApiKey(server, req, sid);
            if (!keyValidation.success) {
                return keyValidation;
            }

            // 更新服务器统计状态
            const updateResult = updateServerStats(sid, server, data, keyValidation.isActiveMode);
            if (!updateResult.success) {
                return updateResult;
            }

            // 发出数据上报事件
            if (eventEmitter) {
                eventEmitter.emit('server:stats:update', {
                    sid,
                    data,
                    server,
                    request: {
                        ip: clientIP,
                        userAgent: req.headers['user-agent'],
                        timestamp: Date.now()
                    }
                });
                log.debug(`发出数据上报事件: ${server.name} (${sid})`);
            }

            return { success: true, message: '数据上报成功' };
        } catch (error) {
            log.error(`处理上报数据时出错: ${sid}`, error);
            return { success: false, message: '处理数据时出错', error: error.message };
        }
    }

    // 注册上报接口
    svr.post("/stats/update", async (req, res) => {
        // 检查上报功能是否启用
        if (!reportingSettings.enabled) {
            return res.json(pr(0, '上报功能已禁用'));
        }

        const { sid, data } = req.body;

        // 基本参数验证
        if (!sid || !data) {
            return res.json(pr(0, '缺少必要参数'));
        }

        // 处理上报数据
        const result = await processReportData(sid, data, req);

        // 返回处理结果
        if (result.success) {
            // 使用pr函数返回标准响应，并添加success字段以兼容客户端
            const response = pr(1, 'update success');
            response.success = 1;
            res.json(response);
        } else {
            const response = pr(0, result.message);
            response.success = 0;
            res.json(response);
        }
    });

    // 兼容旧版本的上报接口
    svr.post("/api/report", async (req, res) => {
        // 重定向到新接口
        req.url = "/stats/update";
        svr._router.handle(req, res);
    });

    // 添加配置API
    svr.get("/admin/api/reporting/settings", async (req, res) => {
        if (!req.admin) {
            return res.status(403).json({ success: false, message: '需要管理员权限' });
        }

        const settings = await db.setting.get('reporting') || reportingSettings;
        res.json({ success: true, data: settings });
    });

    svr.post("/admin/api/reporting/settings", async (req, res) => {
        if (!req.admin) {
            return res.status(403).json({ success: false, message: '需要管理员权限' });
        }

        try {
            const newSettings = req.body;

            // 验证设置
            if (typeof newSettings.enabled !== 'boolean') {
                return res.json({ success: false, message: '无效的启用状态' });
            }

            // 验证API密钥设置
            if (newSettings.requireApiKey !== undefined && typeof newSettings.requireApiKey !== 'boolean') {
                return res.json({ success: false, message: '无效的API密钥验证设置' });
            }

            // 更新设置
            const settings = {
                ...reportingSettings,
                ...newSettings
            };

            await db.setting.set('reporting', settings);

            // 发出设置更新事件
            if (eventEmitter) {
                eventEmitter.emit('settings:reporting:update', settings);
            }

            res.json({ success: true, message: '设置已更新', data: settings });
        } catch (error) {
            log.error('更新设置时出错:', error);
            res.status(500).json({ success: false, message: '更新设置时出错' });
        }
    });

    // 添加状态API
    svr.get("/admin/api/reporting/status", (req, res) => {
        if (!req.admin) {
            return res.status(403).json({ success: false, message: '需要管理员权限' });
        }

        try {
            // 获取上报统计信息
            const stats = {
                enabled: reportingSettings.enabled,
                integrateWithAutodiscovery: reportingSettings.integrateWithAutodiscovery,
                requireApiKey: reportingSettings.requireApiKey,
                activeServers: 0,
                totalReports: 0,
                lastReportTime: null
            };

            res.json({ success: true, data: stats });
        } catch (error) {
            log.error('获取状态时出错:', error);
            res.status(500).json({ success: false, message: '获取状态时出错' });
        }
    });

    // 初始化事件监听
    if (eventEmitter) {
        // 监听设置更新事件
        eventEmitter.on('settings:update', (settings) => {
            if (settings.reporting) {
                Object.assign(reportingSettings, settings.reporting);
                log.info('上报设置已更新');
            }
        });
    }

    // 不再打印初始化日志
    // log.info('上报模块已加载');

    // 返回模块接口
    return {
        processReportData,
        validateReportData,
        getSettings: () => ({ ...reportingSettings }),
        updateSettings: async (newSettings) => {
            Object.assign(reportingSettings, newSettings);
            await db.setting.set('reporting', reportingSettings);

            if (eventEmitter) {
                eventEmitter.emit('settings:reporting:update', reportingSettings);
            }

            return { ...reportingSettings };
        }
    };
};
