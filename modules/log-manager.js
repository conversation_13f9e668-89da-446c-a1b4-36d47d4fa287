'use strict';

/**
 * 日志管理模块
 * 用于管理系统日志文件，防止日志文件过大导致服务器异常
 */

const fs = require('fs');
const path = require('path');
const { logUtils, LOG_TYPES, LOG_DIR } = require('./utils/log-utils');

class LogManager {
    constructor(options = {}) {
        // 默认配置
        this.config = {
            // 单个日志文件最大大小（字节）
            maxFileSize: options.maxFileSize || 10 * 1024 * 1024, // 默认10MB
            // 保留的日志文件数量
            maxFiles: options.maxFiles || 5,
            // 检查间隔（毫秒）
            checkInterval: options.checkInterval || 60 * 60 * 1000, // 默认1小时
            // 是否启用自动清理
            autoCleanup: options.autoCleanup !== false,
            // 统一日志目录
            logDirectories: options.logDirectories || [LOG_DIR],
            // 需要管理的日志文件模式
            logPatterns: options.logPatterns || [
                '*.log',
                'notification-*.log',
                'performance.log'
            ]
        };

        // 初始化
        this.init();
    }

    /**
     * 初始化日志管理器
     */
    init() {
        console.log('[日志管理] 初始化日志管理器');
        console.log(`[日志管理] 最大文件大小: ${(this.config.maxFileSize / 1024 / 1024).toFixed(2)}MB`);
        console.log(`[日志管理] 保留文件数量: ${this.config.maxFiles}`);
        
        // 立即执行一次检查
        this.checkAndRotateLogs();
        
        // 设置定时检查
        if (this.config.autoCleanup) {
            setInterval(() => {
                this.checkAndRotateLogs();
            }, this.config.checkInterval);
            
            console.log(`[日志管理] 自动清理已启用，检查间隔: ${this.config.checkInterval / 1000 / 60}分钟`);
        }
    }

    /**
     * 检查并轮转日志文件
     */
    async checkAndRotateLogs() {
        try {
            console.log('[日志管理] 开始检查日志文件...');
            
            for (const logDir of this.config.logDirectories) {
                if (fs.existsSync(logDir)) {
                    await this.processDirectory(logDir);
                }
            }
            
            // 检查根目录的app.log
            const rootAppLog = path.join(__dirname, '../app.log');
            if (fs.existsSync(rootAppLog)) {
                await this.processLogFile(rootAppLog);
            }
            
            console.log('[日志管理] 日志文件检查完成');
        } catch (error) {
            console.error('[日志管理] 检查日志文件失败:', error);
        }
    }

    /**
     * 处理目录中的日志文件
     */
    async processDirectory(dirPath) {
        try {
            const files = fs.readdirSync(dirPath);
            
            for (const file of files) {
                const filePath = path.join(dirPath, file);
                const stat = fs.statSync(filePath);
                
                if (stat.isFile() && this.isLogFile(file)) {
                    await this.processLogFile(filePath);
                }
            }
        } catch (error) {
            console.error(`[日志管理] 处理目录 ${dirPath} 失败:`, error);
        }
    }

    /**
     * 判断是否为日志文件
     */
    isLogFile(filename) {
        return this.config.logPatterns.some(pattern => {
            const regex = new RegExp(pattern.replace(/\*/g, '.*'));
            return regex.test(filename);
        });
    }

    /**
     * 处理单个日志文件
     */
    async processLogFile(filePath) {
        try {
            const stat = fs.statSync(filePath);
            const fileSize = stat.size;
            const fileName = path.basename(filePath);
            
            if (fileSize > this.config.maxFileSize) {
                console.log(`[日志管理] 文件 ${fileName} 大小 ${(fileSize / 1024 / 1024).toFixed(2)}MB 超过限制，开始轮转`);
                await this.rotateLogFile(filePath);
            } else {
                console.log(`[日志管理] 文件 ${fileName} 大小 ${(fileSize / 1024 / 1024).toFixed(2)}MB 正常`);
            }
        } catch (error) {
            console.error(`[日志管理] 处理文件 ${filePath} 失败:`, error);
        }
    }

    /**
     * 轮转日志文件
     */
    async rotateLogFile(filePath) {
        try {
            const dir = path.dirname(filePath);
            const ext = path.extname(filePath);
            const baseName = path.basename(filePath, ext);
            
            // 移动现有的轮转文件
            for (let i = this.config.maxFiles - 1; i >= 1; i--) {
                const oldFile = path.join(dir, `${baseName}.${i}${ext}`);
                const newFile = path.join(dir, `${baseName}.${i + 1}${ext}`);
                
                if (fs.existsSync(oldFile)) {
                    if (i === this.config.maxFiles - 1) {
                        // 删除最老的文件
                        fs.unlinkSync(oldFile);
                        console.log(`[日志管理] 删除最老的日志文件: ${path.basename(oldFile)}`);
                    } else {
                        // 重命名文件
                        fs.renameSync(oldFile, newFile);
                        console.log(`[日志管理] 重命名: ${path.basename(oldFile)} -> ${path.basename(newFile)}`);
                    }
                }
            }
            
            // 将当前文件重命名为 .1
            const rotatedFile = path.join(dir, `${baseName}.1${ext}`);
            fs.renameSync(filePath, rotatedFile);
            console.log(`[日志管理] 轮转: ${path.basename(filePath)} -> ${path.basename(rotatedFile)}`);
            
            // 创建新的空日志文件
            fs.writeFileSync(filePath, '');
            console.log(`[日志管理] 创建新的日志文件: ${path.basename(filePath)}`);
            
        } catch (error) {
            console.error(`[日志管理] 轮转文件 ${filePath} 失败:`, error);
        }
    }

    /**
     * 手动轮转指定文件
     */
    async manualRotate(filePath) {
        if (fs.existsSync(filePath)) {
            console.log(`[日志管理] 手动轮转文件: ${filePath}`);
            await this.rotateLogFile(filePath);
        } else {
            console.log(`[日志管理] 文件不存在: ${filePath}`);
        }
    }

    /**
     * 获取日志文件统计信息
     */
    getLogStats() {
        try {
            // 使用统一日志工具获取统计信息
            const stats = logUtils.getStats();
            
            // 检查根目录的app.log（兼容性）
            const rootAppLog = path.join(__dirname, '../app.log');
            if (fs.existsSync(rootAppLog)) {
                const stat = fs.statSync(rootAppLog);
                stats.totalFiles++;
                stats.totalSize += stat.size;
                stats.files.push({
                    path: rootAppLog,
                    name: 'app.log',
                    size: stat.size,
                    sizeFormatted: logUtils.formatFileSize(stat.size),
                    modified: stat.mtime,
                    type: 'system'
                });
            }

            // 重新计算总大小
            stats.totalSizeFormatted = logUtils.formatFileSize(stats.totalSize);
            
            return stats;
        } catch (error) {
            console.error('[日志管理] 获取统计信息失败:', error);
            return { totalFiles: 0, totalSize: 0, files: [], byType: {} };
        }
    }

    /**
     * 清理所有日志文件
     */
    async cleanAllLogs() {
        try {
            console.log('[日志管理] 开始清理所有日志文件...');
            
            for (const logDir of this.config.logDirectories) {
                if (fs.existsSync(logDir)) {
                    const files = fs.readdirSync(logDir);
                    
                    for (const file of files) {
                        const filePath = path.join(logDir, file);
                        const stat = fs.statSync(filePath);
                        
                        if (stat.isFile() && this.isLogFile(file)) {
                            // 清空文件内容而不是删除文件
                            fs.writeFileSync(filePath, '');
                            console.log(`[日志管理] 清空文件: ${file}`);
                        }
                    }
                }
            }

            // 清空根目录的app.log
            const rootAppLog = path.join(__dirname, '../app.log');
            if (fs.existsSync(rootAppLog)) {
                fs.writeFileSync(rootAppLog, '');
                console.log('[日志管理] 清空文件: app.log');
            }
            
            console.log('[日志管理] 所有日志文件清理完成');
        } catch (error) {
            console.error('[日志管理] 清理日志文件失败:', error);
        }
    }
}

// 导出工厂函数，与其他模块保持一致
module.exports = function(svr, db) {
    // 创建日志管理器实例
    const logManager = new LogManager({
        maxFileSize: 10 * 1024 * 1024, // 10MB
        maxFiles: 5,
        checkInterval: 60 * 60 * 1000, // 1小时检查一次
        autoCleanup: true
    });
    
    console.log('[日志管理] 模块已加载');
    return logManager;
}; 