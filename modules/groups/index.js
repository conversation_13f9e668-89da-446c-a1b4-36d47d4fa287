/**
 * 分组管理路由模块
 * 提供分组管理的Web接口
 */
"use strict"
const uuid = require('uuid');

module.exports = svr => {
    const {db, pr} = svr.locals;
    
    // 分组列表页面
    svr.get("/admin/groups", async (req, res) => {
        try {
            res.render("admin/groups", {
                groups: await db.groups.getWithCount()
            });
        } catch (error) {
            console.error('获取分组列表失败:', error);
            res.render("admin/groups", {
                groups: []
            });
        }
    });
    
    // 添加分组
    svr.post("/admin/groups/add", async (req, res) => {
        const {name} = req.body;
        const id = uuid.v4();
        const top = Date.now();
        
        try {
            await db.groups.ins(id, name, top);
            res.json(pr(1, "添加成功"));
        } catch (error) {
            res.json(pr(0, "添加失败"));
        }
    });
    
    // 编辑分组
    svr.post("/admin/groups/:id/edit", async (req, res) => {
        const {id} = req.params;
        const {name} = req.body;
        
        try {
            const group = await db.groups.get(id);
            if (!group) {
                return res.json(pr(0, "分组不存在"));
            }
            
            await db.groups.upd(id, name, group.top);
            res.json(pr(1, "修改成功"));
        } catch (error) {
            res.json(pr(0, "修改失败"));
        }
    });
    
    // 删除分组
    svr.post("/admin/groups/:id/del", async (req, res) => {
        const {id} = req.params;
        
        try {
            // 检查是否为默认分组
            if (id === 'default') {
                return res.json(pr(0, "默认分组不能删除"));
            }
            
            // 将该分组的服务器移动到默认分组
            await db.run("UPDATE servers SET group_id = $1 WHERE group_id = $2", ['default', id]);
            
            // 删除分组
            await db.groups.del(id);
            // 批量变更，重载服务器配置缓存
            try {
                if (svr.locals.stats?.reloadServerConfigCache) {
                    await svr.locals.stats.reloadServerConfigCache();
                }
            } catch (cacheErr) {
                console.warn('[分组删除] 重载服务器配置缓存失败:', cacheErr.message);
            }

            // 广播服务器列表变更（批量更新，简化为一次 updated 通知）
            try {
                if (svr.locals.broadcastServerListChange) {
                    svr.locals.broadcastServerListChange('updated', {
                        reason: 'group_deleted',
                        group_id: id
                    });
                }
            } catch (broadcastErr) {
                console.warn('[分组删除] 广播列表变更失败:', broadcastErr.message);
            }

            res.json(pr(1, "删除成功"));
        } catch (error) {
            res.json(pr(0, "删除失败"));
        }
    });
    
    // 保存分组排序
    svr.post("/admin/groups/order", async (req, res) => {
        try {
            const {groups} = req.body;
            console.log('Received group order request:', groups); // 添加日志
            
            if (!Array.isArray(groups)) {
                console.error('Invalid groups data:', groups); // 添加日志
                return res.json(pr(0, '无效的分组列表'));
            }
            
            if (groups.length === 0) {
                console.error('Empty groups array'); // 添加日志
                return res.json(pr(0, '分组列表不能为空'));
            }
            
            // 验证所有分组是否存在
            for (const id of groups) {
                const group = await db.groups.get(id);
                if (!group) {
                    console.error(`Group not found: ${id}`); // 添加日志
                    return res.json(pr(0, `分组 ${id} 不存在`));
                }
            }
            
            // 更新排序 - 注意这里使用数组长度作为基数，保持降序排序
            const baseOrder = Date.now(); // 使用时间戳作为基数，确保顺序正确
            for (let index = 0; index < groups.length; index++) {
                const id = groups[index];
                const group = await db.groups.get(id);
                const newOrder = baseOrder - index;
                console.log(`Updating group ${id} order to ${newOrder}`); // 添加日志
                await db.groups.upd(id, group.name, newOrder);
            }
            
            console.log('Group order updated successfully'); // 添加日志
            res.json(pr(1, "排序已保存"));
        } catch (error) {
            console.error('更新分组排序失败:', error);
            res.json(pr(0, '排序保存失败: ' + error.message));
        }
    });
};
