'use strict';

/**
 * API文档模块
 * 集成Swagger UI，提供在线API文档
 */

const swaggerUi = require('swagger-ui-express');
const fs = require('fs');
const path = require('path');

module.exports = (svr, db) => {
    console.log('[API文档] 正在初始化API文档模块...');

    // Swagger文档路径
    const swaggerPath = path.join(__dirname, '../../docs/swagger-output.json');
    
    // API密钥缓存 - 解决同步调用异步方法的问题
    let cachedApiKey = null;
    
    // 预加载API密钥
    async function initializeApiKey() {
        try {
            const settings = await db.setting.all();
            cachedApiKey = settings.api_key;
            console.log('[API文档] API密钥缓存已初始化');
        } catch (error) {
            console.warn('[API文档] 无法加载API密钥:', error.message);
            cachedApiKey = null;
        }
    }
    
    // 立即初始化API密钥
    initializeApiKey();
    
    // 提供刷新缓存的方法
    const refreshApiKeyCache = async () => {
        await initializeApiKey();
    };
    
    let swaggerDocument = null;

    // 尝试加载Swagger文档
    try {
        if (fs.existsSync(swaggerPath)) {
            swaggerDocument = JSON.parse(fs.readFileSync(swaggerPath, 'utf8'));
            console.log('[API文档] Swagger文档加载成功');
        } else {
            console.warn('[API文档] Swagger文档文件不存在，请运行: npm run generate-docs');
            // 创建基础文档结构
            swaggerDocument = createBasicSwaggerDoc();
        }
    } catch (error) {
        console.error('[API文档] 加载Swagger文档失败:', error);
        swaggerDocument = createBasicSwaggerDoc();
    }

    // 自定义Swagger UI配置
    const swaggerOptions = {
        explorer: true,
        swaggerOptions: {
            docExpansion: 'list',
            filter: true,
            showRequestDuration: true,
            tryItOutEnabled: true,
            requestInterceptor: (req) => {
                // 使用缓存的API密钥，避免同步调用异步方法
                if (cachedApiKey && req.url.includes('/api/')) {
                    if (req.url.includes('?')) {
                        req.url += `&key=${cachedApiKey}`;
                    } else {
                        req.url += `?key=${cachedApiKey}`;
                    }
                }
                return req;
            }
        },
        customCss: `
            .swagger-ui .topbar { display: none; }
            .swagger-ui .info { margin: 20px 0; }
            .swagger-ui .info .title { color: #7c3aed; }
            .swagger-ui .scheme-container { background: #f8fafc; padding: 10px; border-radius: 8px; }
            .swagger-ui .btn.authorize { background-color: #7c3aed; border-color: #7c3aed; }
            .swagger-ui .btn.authorize:hover { background-color: #6d28d9; }
            .swagger-ui .opblock.opblock-get .opblock-summary-method { background: #10b981; }
            .swagger-ui .opblock.opblock-post .opblock-summary-method { background: #3b82f6; }
            .swagger-ui .opblock.opblock-put .opblock-summary-method { background: #f59e0b; }
            .swagger-ui .opblock.opblock-delete .opblock-summary-method { background: #ef4444; }
        `,
        customSiteTitle: 'DStatus API Documentation',
        customfavIcon: '/img/logo.png'
    };

    // 设置API文档路由
    svr.use('/api-docs', swaggerUi.serve);
    svr.get('/api-docs', swaggerUi.setup(swaggerDocument, swaggerOptions));

    // API文档管理页面
    svr.get('/admin/api-docs', (req, res) => {
        if (!req.admin) return res.redirect('/login');
        res.render('admin/api_docs', {
            hasSwaggerDoc: fs.existsSync(swaggerPath),
            swaggerPath: swaggerPath,
            apiBaseUrl: `${req.protocol}://${req.get('host')}`
        });
    });

    // 重新生成API文档的API
    svr.post('/admin/api/regenerate-docs', async (req, res) => {
        if (!req.admin) {
            return res.json({ status: 0, msg: '需要管理员权限' });
        }

        try {
            // 执行文档生成脚本
            const { spawn } = require('child_process');
            const generateProcess = spawn('node', ['scripts/generate-api-docs.js'], {
                cwd: process.cwd(),
                stdio: 'pipe'
            });

            let output = '';
            let errorOutput = '';

            generateProcess.stdout.on('data', (data) => {
                output += data.toString();
            });

            generateProcess.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });

            generateProcess.on('close', async (code) => {
                if (code === 0) {
                    // 重新加载文档并刷新API密钥缓存
                    try {
                        swaggerDocument = JSON.parse(fs.readFileSync(swaggerPath, 'utf8'));
                        await refreshApiKeyCache(); // 刷新API密钥缓存
                        res.json({ 
                            status: 1, 
                            msg: 'API文档重新生成成功，API密钥缓存已刷新',
                            output: output
                        });
                    } catch (error) {
                        res.json({ 
                            status: 0, 
                            msg: '文档生成成功但加载失败: ' + error.message 
                        });
                    }
                } else {
                    res.json({ 
                        status: 0, 
                        msg: '文档生成失败',
                        error: errorOutput || output
                    });
                }
            });

        } catch (error) {
            console.error('[API文档] 重新生成文档失败:', error);
            res.json({ status: 0, msg: '重新生成文档失败: ' + error.message });
        }
    });

    // 获取API统计信息
    svr.get('/admin/api/docs-stats', (req, res) => {
        if (!req.admin) {
            return res.json({ status: 0, msg: '需要管理员权限' });
        }

        try {
            const stats = {
                hasDocument: fs.existsSync(swaggerPath),
                documentSize: 0,
                lastModified: null,
                totalEndpoints: 0,
                endpointsByMethod: {},
                endpointsByTag: {}
            };

            if (stats.hasDocument) {
                const docStats = fs.statSync(swaggerPath);
                stats.documentSize = docStats.size;
                stats.lastModified = docStats.mtime;

                if (swaggerDocument && swaggerDocument.paths) {
                    const paths = swaggerDocument.paths;
                    stats.totalEndpoints = Object.keys(paths).length;

                    // 统计各种HTTP方法的端点数量
                    Object.values(paths).forEach(pathObj => {
                        Object.keys(pathObj).forEach(method => {
                            if (['get', 'post', 'put', 'delete', 'patch'].includes(method)) {
                                stats.endpointsByMethod[method] = (stats.endpointsByMethod[method] || 0) + 1;
                                
                                // 统计标签
                                const tags = pathObj[method].tags || ['未分类'];
                                tags.forEach(tag => {
                                    stats.endpointsByTag[tag] = (stats.endpointsByTag[tag] || 0) + 1;
                                });
                            }
                        });
                    });
                }
            }

            res.json({ status: 1, data: stats });
        } catch (error) {
            console.error('[API文档] 获取统计信息失败:', error);
            res.json({ status: 0, msg: '获取统计信息失败: ' + error.message });
        }
    });

    console.log('[API文档] API文档模块初始化完成');
    console.log('[API文档] 访问地址: /api-docs');
    console.log('[API文档] 管理页面: /admin/api-docs');
};

/**
 * 创建基础Swagger文档结构
 */
function createBasicSwaggerDoc() {
    return {
        swagger: '2.0',
        info: {
            version: '1.0.0',
            title: 'DStatus API Documentation',
            description: 'DStatus 服务器监控系统 API 文档\n\n⚠️ 文档尚未生成，请运行 `npm run generate-docs` 生成完整文档。',
            contact: {
                name: 'DStatus Team',
                url: 'https://github.com/fev125/dstatus'
            }
        },
        host: 'localhost:5555',
        basePath: '/',
        schemes: ['http', 'https'],
        consumes: ['application/json'],
        produces: ['application/json'],
        paths: {
            '/api/servers': {
                get: {
                    tags: ['Server Management'],
                    summary: '获取所有服务器节点',
                    description: '获取系统中所有可用的服务器节点信息',
                    responses: {
                        200: {
                            description: '成功获取服务器列表',
                            schema: {
                                type: 'object',
                                properties: {
                                    success: { type: 'boolean' },
                                    data: {
                                        type: 'array',
                                        items: { $ref: '#/definitions/ServerInfo' }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        definitions: {
            ServerInfo: {
                type: 'object',
                properties: {
                    id: { type: 'string', description: '服务器ID' },
                    name: { type: 'string', description: '服务器名称' },
                    ip: { type: 'string', description: '服务器IP地址' }
                }
            }
        }
    };
} 