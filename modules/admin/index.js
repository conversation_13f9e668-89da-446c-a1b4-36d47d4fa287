const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const Database = require('better-sqlite3');
const schedule = require('node-schedule');
const { performance } = require('perf_hooks');

// 格式化时间为人类可读格式
function formatDateTime(date = new Date()) {
    return date.toISOString().replace(/[T:]/g, '-').slice(0, 19);
}

module.exports = function(svr, db) {
    if (!db || !db.config) {
        console.error('[Admin模块] 数据库配置未正确初始化');
        return;
    }

    const { pr } = svr.locals;
    const dbConfig = db.config;  // 获取数据库配置实例
    
    // 操作锁，防止并发操作
    const operationLocks = {
        backup: false,
        restore: false
    };
    
    // 操作锁超时自动释放机制
    const lockTimeouts = {};
    
    function setLock(operation, timeout = 30 * 60 * 1000) { // 默认30分钟
        operationLocks[operation] = true;
        // 清除已存在的超时
        if (lockTimeouts[operation]) {
            clearTimeout(lockTimeouts[operation]);
        }
        // 设置新的超时自动释放
        lockTimeouts[operation] = setTimeout(() => {
            console.warn(`[Admin] ${operation}操作超时，自动释放锁`);
            operationLocks[operation] = false;
            delete lockTimeouts[operation];
        }, timeout);
    }
    
    function clearLock(operation) {
        operationLocks[operation] = false;
        if (lockTimeouts[operation]) {
            clearTimeout(lockTimeouts[operation]);
            delete lockTimeouts[operation];
        }
    }

    // 数据库备份
    router.get('/db/backup', async (req, res) => {
        // 检查操作锁
        if (operationLocks.backup) {
            return res.status(423).json(pr(0, '备份操作正在进行中，请稍后再试'));
        }
        
        operationLocks.backup = true;
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const paths = dbConfig.getPaths();
        const backupPath = paths.backup(timestamp);

        console.log('===== 数据库备份开始 =====');
        console.log('时间:', new Date().toLocaleString());
        console.log('用户IP:', req.ip);
        console.log('备份路径:', backupPath);

        try {
            // 获取原始数据库大小
            const dbStats = dbConfig.getDatabaseStats();
            console.log('原始数据库大小:', (dbStats.size / 1024 / 1024).toFixed(2) + 'MB');

            // 创建备份
            console.log('开始创建备份文件...');
            await db.DB.backup(backupPath);

            // 获取备份文件大小
            const backupSize = fs.statSync(backupPath).size;
            console.log('备份文件创建成功');
            console.log('备份文件大小:', (backupSize / 1024 / 1024).toFixed(2) + 'MB');

            // 发送文件并在发送后删除
            console.log('开始发送备份文件...');
            res.download(backupPath, `dstatus-backup-${timestamp}.db.db`, (err) => {
                if (err) {
                    console.error('下载过程出错:', err);
                    console.log('===== 数据库备份失败 =====\n');
                } else {
                    console.log('文件发送成功');
                    console.log('===== 数据库备份完成 =====\n');
                }
                // 删除临时文件
                fs.unlink(backupPath, (unlinkErr) => {
                    if (unlinkErr) {
                        console.error('清理临时文件失败:', unlinkErr);
                    } else {
                        console.log('临时文件已清理:', backupPath);
                    }
                });
            });
        } catch (error) {
            console.error('备份过程出错:', error);
            console.log('===== 数据库备份失败 =====\n');
            res.status(500).json(pr(0, '备份失败: ' + error.message));
        } finally {
            // 释放操作锁
            operationLocks.backup = false;
        }
    });

    // 数据库恢复
    router.post('/db/restore', async (req, res) => {
        // 检查操作锁
        if (operationLocks.restore) {
            return res.status(423).json(pr(0, '恢复操作正在进行中，请稍后再试'));
        }
        
        setLock('restore', 30 * 60 * 1000); // 30分钟超时
        
        const paths = dbConfig.getPaths();

        try {
            // 检查文件上传
            if (!req.files || !req.files.database) {
                return res.json(pr(0, "请选择数据库文件"));
            }

            const file = req.files.database;
            if (!file.name.endsWith('.db')) {
                return res.json(pr(0, "请上传.db格式的数据库文件"));
            }

            if (file.size === 0) {
                return res.json(pr(0, "上传的文件为空"));
            }
            
            // 文件大小限制 - 最大500MB
            const MAX_FILE_SIZE = 500 * 1024 * 1024;
            if (file.size > MAX_FILE_SIZE) {
                return res.json(pr(0, `文件太大，最大支持 ${MAX_FILE_SIZE / 1024 / 1024}MB`));
            }

            const backupPath = paths.backup(formatDateTime());

            // 确保临时文件存在
            if (!file.tempFilePath || !fs.existsSync(file.tempFilePath)) {
                const tempPath = paths.temp(Date.now());
                await file.mv(tempPath);
                file.tempFilePath = tempPath;
            }

            // 验证SQLite文件魔数
            const validateSQLiteFile = (filePath) => {
                const fd = fs.openSync(filePath, 'r');
                const buffer = Buffer.alloc(16);
                fs.readSync(fd, buffer, 0, 16, 0);
                fs.closeSync(fd);
                
                const magic = buffer.toString('utf8', 0, 15);
                return magic === 'SQLite format 3';
            };
            
            if (!validateSQLiteFile(file.tempFilePath)) {
                return res.json(pr(0, "文件格式错误，不是有效的SQLite数据库"));
            }
            
            // 验证上传的文件
            let testDb;
            try {
                testDb = new Database(file.tempFilePath, { verbose: console.log });
                // 验证表结构等...
                testDb.close();
            } catch (error) {
                if (testDb) testDb.close();
                throw new Error('数据库验证失败: ' + error.message);
            }

            // 备份当前数据库
            fs.copyFileSync(paths.main, backupPath);
            console.log('已创建当前数据库备份:', backupPath);

            try {
                // 执行热恢复
                console.log("开始执行数据库热恢复...");
                const restoreStartTime = Date.now();
                await performHotRestore(file.tempFilePath, db, svr);
                const restoreTime = Date.now() - restoreStartTime;
                
                console.log(`数据库热恢复完成！耗时: ${restoreTime}ms`);
                res.json(pr(1, `数据库热恢复成功！耗时 ${restoreTime}ms，无需重启即可生效`));
                
            } catch (error) {
                console.error("热恢复失败，回滚到备份:", error.message);
                
                // 回滚：使用文件替换的备用方案
                try {
                    // 清理WAL文件，避免与新数据库文件不匹配
                    const walPath = paths.main + '-wal';
                    const shmPath = paths.main + '-shm';
                    if (fs.existsSync(walPath)) {
                        fs.unlinkSync(walPath);
                        console.log("已清理WAL文件");
                    }
                    if (fs.existsSync(shmPath)) {
                        fs.unlinkSync(shmPath);
                        console.log("已清理SHM文件");
                    }
                    
                    fs.copyFileSync(backupPath, paths.main);
                    console.log("已回滚到恢复前状态");
                } catch (rollbackError) {
                    console.error("回滚失败:", rollbackError.message);
                    throw new Error("恢复和回滚都失败，请手动检查数据库状态");
                }
                
                throw error;
            }

        } catch (error) {
            console.error('恢复过程出错:', error);
            // 如果出错，尝试恢复备份
            const backupPath = paths.backup(formatDateTime());
            if (fs.existsSync(backupPath)) {
                try {
                    // 清理WAL文件，避免与新数据库文件不匹配
                    const walPath = paths.main + '-wal';
                    const shmPath = paths.main + '-shm';
                    if (fs.existsSync(walPath)) {
                        fs.unlinkSync(walPath);
                        console.log("清理WAL文件");
                    }
                    if (fs.existsSync(shmPath)) {
                        fs.unlinkSync(shmPath);
                        console.log("清理SHM文件");
                    }
                    
                    fs.copyFileSync(backupPath, paths.main);
                    console.log('已恢复到备份数据库');
                } catch (restoreError) {
                    console.error('恢复备份失败:', restoreError);
                }
            }
            res.json(pr(0, error.message));
        } finally {
            // 释放操作锁
            clearLock('restore');
        }
    });

    // 定期清理临时文件（每天凌晨执行）
    schedule.scheduleJob('0 0 * * *', () => {
        dbConfig.cleanupTempFiles();
    });

    // 管理页面路由

    // 自动发现管理页面
    router.get('/autodiscovery', async (req, res) => {
        // 先检查登录状态
        if (!req.admin) return res.redirect('/login');
        
        // 临时注释掉功能权限检查
        /*
        // 再检查功能权限
        const licenseEnhanced = svr.locals['license-enhanced'];
        if (licenseEnhanced && licenseEnhanced.featureChecker) {
            const check = licenseEnhanced.featureChecker.checkFeature('AUTO_DISCOVERY');
            console.log('[自动发现] 权限检查结果:', check);
            
            if (!check.allowed) {
                const url = `/admin/license-management?upgrade=auto-discovery&message=${encodeURIComponent(check.message || '需要自动发现功能权限')}`;
                console.log('[自动发现] 无权限，重定向到:', url);
                return res.redirect(url);
            }
        }
        */
        
        try {
            res.render('admin/autodiscovery', {
                groups: await svr.locals.db.groups.all()
            });
        } catch (error) {
            console.error('[Admin] 获取分组失败:', error);
            res.status(500).send('获取分组失败');
        }
    });

    // SSH脚本管理页面 - 已移动到 modules/ssh_scripts/index.js
    // router.get('/ssh_scripts', (req, res) => {
    //     if (!req.admin) return res.redirect('/login');
    //     try {
    //         const scripts = svr.locals.db.ssh_scripts.all();
    //         res.render('admin/ssh_scripts', { ssh_scripts: scripts });
    //     } catch (error) {
    //         console.error('[SSH脚本] 获取脚本列表失败:', error);
    //         res.status(500).send('获取脚本列表失败');
    //     }
    // });

    // 通知日志路由（已整合到统一日志管理）
    router.get('/notification-logs', async (req, res) => {
        if (!req.admin) return res.json({ code: 0, msg: '未授权的访问' });

        try {
            const { logUtils, LOG_TYPES } = require('../utils/log-utils');
            const month = req.query.month;
            const type = req.query.type || 'all';

            const logs = logUtils.readLogs(LOG_TYPES.NOTIFICATION, { month });

            // 过滤状态
            const filteredLogs = type === 'all' ? logs : logs.filter(log => log.status === type);

            res.json(filteredLogs);
        } catch (error) {
            console.error('读取通知日志失败:', error);
            res.status(500).json({ error: '读取日志失败' });
        }
    });

    // 通知日志页面路由
    router.get('/notification-logs-page', (req, res) => {
        if (!req.admin) return res.redirect('/login');
        res.render('admin/notification_logs');
    });



    // 显示网络监控页面
    router.get('/monitor', (req, res) => {
        res.render('admin/monitor', {
            curr_path: '/admin/monitor',
            title: '网络监控'
        });
    });

    // 确保路由正确注册到Express应用
    svr.use('/admin', router);

    // 直接添加路由，以防router中间件不生效
    svr.get('/admin/notification-logs-page', (req, res) => {
        if (!req.admin) return res.redirect('/login');
        res.render('admin/notification_logs');
    });

    // 备用路由（保持兼容性）
    svr.get('/admin/autodiscovery', async (req, res) => {
        // 先检查登录状态
        if (!req.admin) return res.redirect('/login');
        
        // 临时注释掉功能权限检查
        /*
        // 再检查功能权限
        const licenseEnhanced = svr.locals['license-enhanced'];
        if (licenseEnhanced && licenseEnhanced.featureChecker) {
            const check = licenseEnhanced.featureChecker.checkFeature('AUTO_DISCOVERY');
            console.log('[自动发现备用路由] 权限检查结果:', check);
            
            if (!check.allowed) {
                const url = `/admin/license-management?upgrade=auto-discovery&message=${encodeURIComponent(check.message || '需要自动发现功能权限')}`;
                console.log('[自动发现备用路由] 无权限，重定向到:', url);
                return res.redirect(url);
            }
        }
        */
        
        try {
            res.render('admin/autodiscovery', {
                groups: await svr.locals.db.groups.all()
            });
        } catch (error) {
            console.error('[Admin] 获取分组失败:', error);
            res.status(500).send('获取分组失败');
        }
    });

    // 日志管理页面
    svr.get('/admin/log-management', (req, res) => {
        if (!req.admin) return res.redirect('/login');
        res.render('admin/log_management');
    });

    // 授权管理页面
    router.get('/license-management', (req, res) => {
        if (!req.admin) return res.redirect('/login');
        res.render('admin/license-management', {
            title: '授权管理',
            curr_path: '/admin/license-management'
        });
    });


    // 高级分析页面（增加功能检查）
    router.get('/analytics', (req, res) => {
        // 先检查登录状态
        if (!req.admin) return res.redirect('/login');
        
        // 临时注释掉功能权限检查
        /*
        // 再检查功能权限
        const licenseEnhanced = svr.locals['license-enhanced'];
        if (licenseEnhanced && licenseEnhanced.featureChecker) {
            const check = licenseEnhanced.featureChecker.checkFeature('ADVANCED_ANALYTICS');
            console.log('[高级分析] 权限检查结果:', check);
            
            if (!check.allowed) {
                const url = `/admin/license-management?upgrade=advanced-analytics&message=${encodeURIComponent(check.message || '需要高级分析功能权限')}`;
                console.log('[高级分析] 无权限，重定向到:', url);
                return res.redirect(url);
            }
        }
        */
        
        res.render('admin/analytics', {
            title: '高级分析',
            curr_path: '/admin/analytics'
        });
    });

    // 网络质量监控页面
    router.get('/network-quality', (req, res) => {
        if (!req.admin) return res.redirect('/login');
        res.render('admin/network-quality', {
            title: '网络质量监控',
            curr_path: '/admin/network-quality'
        });
    });

    // AI分析页面
    router.get('/ai-analytics', (req, res) => {
        if (!req.admin) return res.redirect('/login');
        res.render('admin/ai-analytics', {
            title: 'AI智能分析',
            curr_path: '/admin/ai-analytics'
        });
    });
    
    // ==================== AI 配置 API ====================
    const aiConfig = require('../utils/ai-config');
    
    // AI API 响应格式转换函数
    const aiResponse = (code, data) => {
        return { code, msg: data };
    };
    
    // 获取 AI 配置
    router.get('/api/ai/config', async (req, res) => {
        if (!req.admin) {
            return res.json(aiResponse(0, '需要管理员权限'));
        }
        
        try {
            const config = await aiConfig.getAiConfig(db);
            
            // 脱敏处理
            const safeConfig = {
                enabled: config.enabled,
                name: config.name,
                apiBase: config.apiBase,
                model: config.model,
                hasKey: !!config.apiKey,
                apiKey: config.apiKey ? '***MASKED***' : '',
                envLocks: config.envLocks || {}
            };
            
            res.json(aiResponse(1, safeConfig));
        } catch (error) {
            console.error('[AI Config API] 获取配置失败:', error);
            res.json(aiResponse(0, '获取配置失败'));
        }
    });
    
    // 保存 AI 配置
    router.post('/api/ai/config', async (req, res) => {
        if (!req.admin) {
            return res.json(aiResponse(0, '需要管理员权限'));
        }
        
        try {
            const { enabled, name, apiBase, apiKey, model } = req.body;
            
            await aiConfig.saveAiConfig(db, {
                enabled,
                name,
                apiBase,
                apiKey,
                model
            });
            
            res.json(aiResponse(1, '配置保存成功'));
        } catch (error) {
            console.error('[AI Config API] 保存配置失败:', error);
            res.json(aiResponse(0, '保存配置失败：' + error.message));
        }
    });
    
    // 测试 AI 连接
    router.get('/api/ai/test', async (req, res) => {
        if (!req.admin) {
            return res.json(aiResponse(0, '需要管理员权限'));
        }
        
        try {
            const config = await aiConfig.getAiConfig(db);
            const result = await aiConfig.testAiConnection(config);
            
            if (result.success) {
                res.json(aiResponse(1, result));
            } else {
                res.json(aiResponse(0, result));
            }
        } catch (error) {
            console.error('[AI Config API] 测试连接失败:', error);
            res.json(aiResponse(0, { 
                success: false, 
                message: '测试失败：' + error.message 
            }));
        }
    });
    // ==================== AI 配置 API 结束 ====================

    // 获取日志统计信息API
    svr.get('/admin/api/log-stats', (req, res) => {
        if (!req.admin) {
            return res.json(pr(0, '需要管理员权限'));
        }

        try {
            const { logUtils } = require('../utils/log-utils');
            const stats = logUtils.getStats();

            // 检查根目录的app.log（兼容性）
            const rootAppLog = path.join(__dirname, '../app.log');
            if (fs.existsSync(rootAppLog)) {
                const stat = fs.statSync(rootAppLog);
                stats.totalFiles++;
                stats.totalSize += stat.size;
                stats.files.push({
                    path: rootAppLog,
                    name: 'app.log',
                    size: stat.size,
                    sizeFormatted: logUtils.formatFileSize(stat.size),
                    modified: stat.mtime,
                    type: 'system'
                });
                stats.totalSizeFormatted = logUtils.formatFileSize(stats.totalSize);
            }

            res.json(pr(1, stats));
        } catch (error) {
            console.error('获取日志统计失败:', error);
            res.json(pr(0, '获取日志统计失败'));
        }
    });

    // 手动轮转日志文件API
    svr.post('/admin/api/rotate-log', async (req, res) => {
        if (!req.admin) {
            return res.json(pr(0, '需要管理员权限'));
        }

        try {
            const { filePath } = req.body;
            if (!filePath) {
                return res.json(pr(0, '缺少文件路径参数'));
            }

            const { logUtils } = require('../utils/log-utils');

            // 手动轮转文件
            if (fs.existsSync(filePath)) {
                const config = { maxFileSize: 0, maxFiles: 3, enableRotation: true };
                logUtils.checkAndRotate(filePath, config);
                res.json(pr(1, '日志文件轮转成功'));
            } else {
                res.json(pr(0, '文件不存在'));
            }
        } catch (error) {
            console.error('轮转日志文件失败:', error);
            res.json(pr(0, '轮转日志文件失败'));
        }
    });

    // 清理所有日志文件API
    svr.post('/admin/api/clean-logs', async (req, res) => {
        if (!req.admin) {
            return res.json(pr(0, '需要管理员权限'));
        }

        try {
            const { logUtils } = require('../utils/log-utils');

            // 清理所有日志文件
            logUtils.cleanLogs();

            // 清理根目录的app.log（兼容性）
            const rootAppLog = path.join(__dirname, '../app.log');
            if (fs.existsSync(rootAppLog)) {
                fs.writeFileSync(rootAppLog, '');
                console.log('[日志管理] 清空文件: app.log');
            }

            res.json(pr(1, '所有日志文件清理成功'));
        } catch (error) {
            console.error('清理日志文件失败:', error);
            res.json(pr(0, '清理日志文件失败'));
        }
    });

    // 获取日志内容API
    svr.get('/admin/api/log-content', async (req, res) => {
        if (!req.admin) return res.json({ error: '未授权的访问' });

        try {
            const { logUtils, LOG_TYPES } = require('../utils/log-utils');
            const type = req.query.type;
            const month = req.query.month;

            // 映射前端类型到后端类型
            const typeMap = {
                'performance': LOG_TYPES.PERFORMANCE,
                'system': LOG_TYPES.SYSTEM,
                'error': LOG_TYPES.ERROR,
                'access': LOG_TYPES.ACCESS
            };

            const logType = typeMap[type];
            if (!logType) {
                return res.status(400).json({ error: '不支持的日志类型' });
            }

            const logs = logUtils.readLogs(logType, { month, limit: 100 });
            res.json(logs);
        } catch (error) {
            console.error('读取日志内容失败:', error);
            res.status(500).json({ error: '读取日志失败' });
        }
    });

    /**
     * 执行数据库热恢复
     * @param {string} backupPath - 备份数据库文件路径
     * @param {Object} db - 数据库实例
     * @param {Object} svr - 服务器实例
     */
    async function performHotRestore(backupPath, db, svr) {
        console.log('[热恢复] 开始执行热恢复...');
        
        // 检查数据库类型，热恢复功能仅在SQLite模式下可用
        if (db.DB.type !== 'sqlite') {
            throw new Error('热恢复功能仅在SQLite模式下可用，PostgreSQL请使用标准备份恢复方式');
        }
        
        // 1. 执行 WAL checkpoint 确保数据一致性（仅SQLite）
        try {
            db.DB.pragma('wal_checkpoint(TRUNCATE)');
            console.log('[热恢复] WAL checkpoint 已执行');
        } catch (error) {
            console.warn('[热恢复] WAL checkpoint 失败，继续恢复:', error.message);
        }

        // 保存并临时禁用外键约束
        const originalFKStatus = db.DB.pragma('foreign_keys');
        db.DB.pragma('foreign_keys = OFF');
        console.log('[热恢复] 已临时禁用外键约束');

        // 2. 使用 ATTACH DATABASE 热恢复（SQLite特有）
        try {
            // 验证备份文件路径安全性
            const resolvedBackupPath = path.resolve(backupPath);
            if (!fs.existsSync(resolvedBackupPath)) {
                throw new Error('备份文件不存在');
            }
            
            // 转义单引号防止SQL注入
            const escapedPath = resolvedBackupPath.replace(/'/g, "''");
            
            // 附加备份数据库
            db.DB.exec(`ATTACH DATABASE '${escapedPath}' AS restore_source`);
            console.log('[热恢复] 成功附加备份数据库');
            
            // 从备份数据库获取表名（不是当前数据库）
            const backupTables = db.DB.prepare("SELECT name FROM restore_source.sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'").all();
            console.log(`[热恢复] 备份数据库包含 ${backupTables.length} 个表`);
            
            // 开始事务，确保原子性
            const restoreTransaction = db.DB.transaction(() => {
                // 逐表恢复数据
                for (const table of backupTables) {
                    const tableName = table.name;
                    
                    // 跳过SQLite系统表和迁移表（迁移表会单独处理）
                    if (tableName.startsWith('sqlite_') || tableName === 'db_migrations') {
                        continue;
                    }
                    
                    try {
                        // 检查当前数据库是否有这个表
                        const currentTableExists = db.DB.prepare(
                            "SELECT 1 FROM sqlite_master WHERE type='table' AND name=?"
                        ).get(tableName);
                        
                        if (!currentTableExists) {
                            // 表不存在，从备份创建表结构
                            console.log(`[热恢复] 创建缺失的表: ${tableName}`);
                            const createTableSQL = db.DB.prepare(
                                "SELECT sql FROM restore_source.sqlite_master WHERE type='table' AND name=?"
                            ).get(tableName);
                            
                            if (createTableSQL && createTableSQL.sql) {
                                db.DB.exec(createTableSQL.sql);
                                // 直接复制所有数据
                                db.DB.prepare(`INSERT INTO ${tableName} SELECT * FROM restore_source.${tableName}`).run();
                            }
                        } else {
                            // 表存在，使用智能列映射
                            const backupColumns = db.DB.pragma(`restore_source.table_info(${tableName})`);
                            const currentColumns = db.DB.pragma(`table_info(${tableName})`);
                            
                            // 找出两边都有的列（列交集）
                            const commonColumns = backupColumns
                                .filter(bc => currentColumns.some(cc => cc.name === bc.name))
                                .map(c => c.name);
                            
                            if (commonColumns.length > 0) {
                                const columnList = commonColumns.join(',');
                                
                                // 清空目标表
                                db.DB.prepare(`DELETE FROM ${tableName}`).run();
                                
                                // 只复制共同的列
                                db.DB.prepare(
                                    `INSERT INTO ${tableName} (${columnList}) 
                                     SELECT ${columnList} FROM restore_source.${tableName}`
                                ).run();
                                
                                console.log(`[热恢复] 恢复表 ${tableName} 完成 (${commonColumns.length}列)`);
                            } else {
                                console.warn(`[热恢复] 表 ${tableName} 没有共同列，跳过`);
                            }
                        }
                    } catch (tableError) {
                        console.error(`[热恢复] 表 ${tableName} 恢复失败: ${tableError.message}`);
                        // 不抛出错误，继续恢复其他表
                    }
                }
                
                // 特殊处理：恢复迁移记录（保留较低的版本号以触发迁移）
                try {
                    const backupVersion = db.DB.prepare(
                        "SELECT MAX(version) as version FROM restore_source.db_migrations WHERE status='success'"
                    ).get();
                    const currentVersion = db.DB.prepare(
                        "SELECT MAX(version) as version FROM db_migrations WHERE status='success'"
                    ).get();
                    
                    if (backupVersion && currentVersion && backupVersion.version < currentVersion.version) {
                        console.log(`[热恢复] 保留较低的迁移版本 ${backupVersion.version} 以触发升级`);
                        db.DB.prepare("DELETE FROM db_migrations").run();
                        db.DB.prepare("INSERT INTO db_migrations SELECT * FROM restore_source.db_migrations").run();
                    }
                } catch (migrationError) {
                    console.warn('[热恢复] 迁移记录处理失败，将重新运行所有迁移:', migrationError.message);
                }
            });
            
            // 执行事务
            try {
                restoreTransaction();
            } catch (transError) {
                console.error('[热恢复] 事务执行失败，自动回滚:', transError.message);
                throw new Error(`恢复失败: ${transError.message}`);
            }
            
            console.log('[热恢复] 所有表恢复完成');
            
            // 分离备份数据库
            db.DB.exec('DETACH DATABASE restore_source');
            console.log('[热恢复] 成功分离备份数据库');
            
        } catch (error) {
            // 确保分离数据库连接
            try {
                db.DB.exec('DETACH DATABASE restore_source');
            } catch {}
            
            // 恢复外键约束状态
            db.DB.pragma(`foreign_keys = ${originalFKStatus ? 'ON' : 'OFF'}`);
            console.log(`[热恢复] 外键约束已恢复为: ${originalFKStatus ? 'ON' : 'OFF'}`);
            
            throw new Error(`热恢复失败: ${error.message}`);
        }

        // 恢复外键约束状态
        db.DB.pragma(`foreign_keys = ${originalFKStatus ? 'ON' : 'OFF'}`);
        console.log(`[热恢复] 外键约束已恢复为: ${originalFKStatus ? 'ON' : 'OFF'}`);

        // 3. 运行数据库迁移（确保表结构最新）
        console.log('[热恢复] 开始运行数据库迁移...');
        try {
            const migrations = require('../../database/migrations');
            await migrations(db.DB).migrate();
            console.log('[热恢复] 数据库迁移完成');
        } catch (migrationError) {
            console.error('[热恢复] 迁移失败:', migrationError.message);
            // 迁移失败不应该阻止恢复，继续执行
        }

        // 4. 验证恢复结果
        console.log('[热恢复] 验证恢复结果...');
        try {
            // 验证关键表的数据
            const validation = {
                servers: db.DB.prepare("SELECT COUNT(*) as cnt FROM servers").get(),
                groups: db.DB.prepare("SELECT COUNT(*) as cnt FROM groups").get(),
                settings: db.DB.prepare("SELECT COUNT(*) as cnt FROM setting").get()
            };
            
            console.log('[热恢复] 数据验证结果:');
            console.log(`  - servers表: ${validation.servers.cnt} 条记录`);
            console.log(`  - groups表: ${validation.groups.cnt} 条记录`);
            console.log(`  - settings表: ${validation.settings.cnt} 条记录`);
            
            if (validation.servers.cnt === 0) {
                console.warn('[热恢复] ⚠️ 警告：servers表为空，可能影响系统功能');
            }
            
            // 验证关键表是否存在
            const trafficTableExists = db.DB.prepare(
                "SELECT COUNT(*) as cnt FROM sqlite_master WHERE type='table' AND name='traffic'"
            ).get();
            
            if (trafficTableExists.cnt === 0) {
                console.warn('[热恢复] ⚠️ 警告：traffic表不存在，流量统计功能可能受影响');
            }
        } catch (validationError) {
            console.error('[热恢复] 验证失败:', validationError.message);
        }

        // 5. 刷新所有缓存
        await refreshCachesAfterRestore(svr, db);
        console.log('[热恢复] 缓存刷新完成');
    }

    /**
     * 刷新数据库恢复后的所有缓存
     * @param {Object} svr - 服务器实例
     * @param {Object} db - 数据库实例
     */
    async function refreshCachesAfterRestore(svr, db) {
        console.log('[缓存刷新] 开始刷新缓存...');
        
        try {
            // 1. 刷新应用层缓存
            if (svr.locals.setting) {
                svr.locals.setting = await db.setting.all();
                console.log('[缓存刷新] 应用层设置缓存已刷新');
            }
            
            // 2. 清除许可证缓存
            if (svr.locals['license-enhanced']) {
                try {
                    // 清除许可证套餐缓存
                    const licensePlansCache = require('../../config/license-plans-cache');
                    licensePlansCache.clearCache();
                    
                    // 删除本地许可证缓存
                    await db.setting.del('license_cache_data');
                    await db.setting.del('license_cache_time');
                    
                    console.log('[缓存刷新] 许可证缓存已清除');
                } catch (error) {
                    console.warn('[缓存刷新] 许可证缓存清除失败:', error.message);
                }
            }
            
            // 3. 清除监控数据缓存
            if (global.trafficCache) {
                global.trafficCache.clear();
                console.log('[缓存刷新] 流量缓存已清除');
            }
            
            // 4. 重新加载 WebSocket 配置
            const newWebSocketInterval = parseInt(await db.setting.get('websocket_interval')) || 4000;
            if (global.UPDATE_INTERVAL !== newWebSocketInterval) {
                global.UPDATE_INTERVAL = newWebSocketInterval;
                console.log(`[缓存刷新] WebSocket间隔已更新: ${newWebSocketInterval}ms`);
            }
            
            // 5. 重新加载轮询配置
            const newPollingInterval = parseInt(await db.setting.get('polling_interval')) || 1500;
            if (svr.locals.updatePollingInterval) {
                svr.locals.updatePollingInterval(newPollingInterval);
                console.log(`[缓存刷新] 轮询间隔已更新: ${newPollingInterval}ms`);
            }
            
            // 6. 重新初始化性能监控
            if (svr.locals.performanceMonitor && svr.locals.performanceMonitor.reset) {
                svr.locals.performanceMonitor.reset();
                console.log('[缓存刷新] 性能监控已重置');
            }
            
            console.log('[缓存刷新] 所有缓存刷新完成');
            
        } catch (error) {
            console.error('[缓存刷新] 缓存刷新过程出错:', error);
            // 不抛出错误，避免影响恢复成功状态
        }
    }

};
