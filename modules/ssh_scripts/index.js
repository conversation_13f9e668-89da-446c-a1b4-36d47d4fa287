"use strict"
module.exports = (svr) => {
    const { db } = svr.locals;

    // 脚本分类定义
    const CATEGORIES = {
        system: { name: '系统维护', icon: '🔧', color: 'blue' },
        monitor: { name: '监控检查', icon: '📊', color: 'green' },
        deploy: { name: '部署发布', icon: '🚀', color: 'purple' },
        security: { name: '安全加固', icon: '🔒', color: 'red' },
        network: { name: '网络管理', icon: '🌐', color: 'cyan' },
        backup: { name: '备份恢复', icon: '📦', color: 'orange' },
        custom: { name: '自定义', icon: '🎯', color: 'gray' }
    };

    // 获取所有脚本（支持搜索和筛选）
    svr.get('/admin/ssh_scripts', async (req, res) => {
        console.log('[SSH脚本] 收到GET请求:', req.url, req.query);

        // 检查管理员权限
        if (!req.admin) {
            console.log('[SSH脚本] 未授权访问，重定向到登录页');
            return res.redirect('/login');
        }

        try {
            console.log('[SSH脚本] 开始获取脚本数据...');
            const { search, category } = req.query;
            let scripts;

            if (search) {
                console.log('[SSH脚本] 执行搜索:', search);
                scripts = await db.ssh_scripts.search(search);
            } else if (category && category !== 'all') {
                console.log('[SSH脚本] 按分类筛选:', category);
                scripts = await db.ssh_scripts.byCategory(category);
            } else {
                console.log('[SSH脚本] 获取所有脚本');
                scripts = await db.ssh_scripts.all();
            }

            console.log('[SSH脚本] 获取到脚本数量:', scripts ? scripts.length : 0);

            // 获取分类统计
            const categories = await db.ssh_scripts.getCategories();
            console.log('[SSH脚本] 获取到分类数量:', categories ? categories.length : 0);

            console.log('[SSH脚本] 准备渲染模板...');
            const templateData = {
                ssh_scripts: scripts,
                categories: categories,
                CATEGORIES: CATEGORIES,
                search: search || '',
                selectedCategory: category || 'all'
            };
            console.log('[SSH脚本] 模板数据准备完成，开始渲染');

            res.render('admin/ssh_scripts.html', templateData);
        } catch (error) {
            console.error('[SSH脚本] 获取脚本列表失败:', error);
            console.error('[SSH脚本] 错误堆栈:', error.stack);
            res.status(500).send('获取脚本列表失败: ' + error.message);
        }
    });

    // 获取单个脚本
    svr.post('/admin/ssh_scripts/get', async (req, res) => {
        try {
            const script = await db.ssh_scripts.get(req.body.id);
            if (!script) {
                res.json({ status: false, data: '脚本不存在' });
                return;
            }
            res.json({ status: true, data: script });
        } catch (error) {
            console.error('[SSH脚本] 获取脚本详情失败:', error);
            res.json({ status: false, data: '获取脚本详情失败' });
        }
    });

    // 添加脚本
    svr.post('/admin/ssh_scripts/add', async (req, res) => {
        try {
            const { name, content, category, description, variables, examples, tags } = req.body;

            // 参数验证
            if (!name || !content) {
                res.json({ status: false, data: '脚本名称和内容不能为空' });
                return;
            }


            // 生成唯一ID
            const id = Date.now().toString();

            // 保存到数据库（修复：添加await关键字）
            await db.ssh_scripts.ins(id, name.trim(), content.trim(), {
                category: category || 'custom',
                description: description || '',
                variables: variables || [],
                examples: examples || [],
                tags: tags || '',
            });

            console.log('[SSH脚本] 添加成功:', { id, name, category });
            res.json({ status: true, data: '添加成功' });

        } catch (error) {
            console.error('[SSH脚本] 添加失败:', error);
            res.json({ status: false, data: '添加脚本失败' });
        }
    });

    // 更新脚本
    svr.post('/admin/ssh_scripts/upd', async (req, res) => {
        try {
            const { id, name, content, category, description, variables, examples, tags } = req.body;

            // 参数验证
            if (!id || !name || !content) {
                res.json({ status: false, data: '参数不完整' });
                return;
            }

            // 检查脚本是否存在（修复：添加await关键字）
            const existing = await db.ssh_scripts.get(id);
            if (!existing) {
                res.json({ status: false, data: '脚本不存在' });
                return;
            }


            // 更新数据库（修复：添加await关键字）
            await db.ssh_scripts.upd(id, name.trim(), content.trim(), {
                category: category,
                description: description,
                variables: variables,
                examples: examples,
                tags: tags,
            });

            console.log('[SSH脚本] 更新成功:', { id, name });
            res.json({ status: true, data: '更新成功' });

        } catch (error) {
            console.error('[SSH脚本] 更新失败:', error);
            res.json({ status: false, data: '更新脚本失败' });
        }
    });

    // 删除脚本
    svr.post('/admin/ssh_scripts/del', async (req, res) => {
        try {
            const { id } = req.body;
            if (!id) {
                res.json({ status: false, data: '参数不完整' });
                return;
            }
            await db.ssh_scripts.del(id);
            res.json({ status: true, data: '删除成功' });
        } catch (error) {
            console.error('[SSH脚本] 删除脚本失败:', error);
            res.json({ status: false, data: '删除脚本失败' });
        }
    });

    // 记录脚本使用
    svr.post('/admin/ssh_scripts/use', async (req, res) => {
        try {
            const { id } = req.body;
            if (!id) {
                res.json({ status: false, data: '参数不完整' });
                return;
            }
            await db.ssh_scripts.incrementUsage(id);
            res.json({ status: true, data: '使用记录已更新' });
        } catch (error) {
            console.error('[SSH脚本] 记录使用失败:', error);
            res.json({ status: false, data: '记录使用失败' });
        }
    });

    // 获取分类列表
    svr.get('/admin/ssh_scripts/categories', async (req, res) => {
        try {
            const categories = await db.ssh_scripts.getCategories();
            res.json({ status: true, data: { categories: categories, definitions: CATEGORIES } });
        } catch (error) {
            console.error('[SSH脚本] 获取分类失败:', error);
            res.json({ status: false, data: '获取分类失败' });
        }
    });
};