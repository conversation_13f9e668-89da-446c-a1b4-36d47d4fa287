'use strict';

const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');
const fetch = require('node-fetch');
const { performTcping } = require('./tcping_core');
const {
    isNodeCooling,
    updateNodeCoolingStatus,
    getNodeCoolingStatus
} = require('./cooling_logic');
const { startScheduler } = require('./scheduler');

/**
 * TCPing模块 - 提供TCP连接测试功能
 */

module.exports = (app, database) => {
    // 初始化监控数据库模块
    const monitor = require('../../database/monitor')(database.DB);

    // 获取Express实例，用于访问服务器对象
    const svr = app.locals.svr || app;

    // 添加到数据库导出
    database.monitor = monitor;

    /**
     * 验证API密钥
     */
    function validateApiKey(req, res) {
        const key = req.query.key;
        if (!key) {
            res.status(401).json({ success: false, message: '缺少API密钥' });
            return false;
        }

        // 获取系统全局API密钥
        const setting = database.setting.all();
        const validApiKey = setting.api_key || 'dstatus';  // 默认API密钥

        // 验证密钥
        if (key !== validApiKey) {
            // 检查是否为服务器的API密钥
            const servers = database.getServers();
            const validServer = servers.find(server =>
                server.data && server.data.api && server.data.api.key === key
            );

            if (!validServer) {
                res.status(401).json({ success: false, message: 'API密钥无效' });
                return false;
            }
        }

        return true;
    }

    // ========== API路由 ==========
    const tcpingRoutes = require('./api_routes/tcping_routes')(database, validateApiKey);
    app.use('/', tcpingRoutes); // Mount the router

    const serverInfoRoutes = require('./api_routes/server_info_routes')(database);
    app.use('/', serverInfoRoutes); // Mount the router

    // ========== 管理API路由 ==========
    const monitorCrudRoutes = require('./api_routes/monitor_crud_routes')(database, monitor);
    app.use('/', monitorCrudRoutes); // Mount the router

    const monitorDataRoutes = require('./api_routes/monitor_data_routes')(database, monitor);
    app.use('/', monitorDataRoutes); // Mount the router

    // ========== 网络质量聚合API路由 ==========
    // 注意：网络质量路由已在 API 模块中加载，此处不再重复加载

    // ========== 调度任务 ==========
    startScheduler(app, database, monitor);

    // 导出模块
    return {
        performTcping, // Still needed by API routes that might remain or be called directly
        // scheduleTcpingCheck is now managed by scheduler.js
        getNodeCoolingStatus, // Still needed by API routes
        isNodeCooling,        // Still needed by API routes or scheduler
        updateNodeCoolingStatus // Still needed by API routes or scheduler
    };
};
