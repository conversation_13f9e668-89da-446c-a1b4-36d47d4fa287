'use strict';

/**
 * 负载归档数据API模块
 * 提供获取详细2秒级别负载历史数据的API接口
 * 以及特定时间范围的带宽历史数据接口
 */

module.exports = (svr, db) => {
    const app = svr.app || svr; // 兼容不同的Express实例传递方式

    /**
     * 获取详细的负载归档数据
     * GET /api/stats/:nodeId/load/archive
     *
     * 参数：
     * - startTime: 必填，开始时间戳（Unix时间戳，秒）
     * - endTime: 必填，结束时间戳（Unix时间戳，秒）
     * - interval: 可选，采样间隔（秒），默认为2（原始间隔）
     * - fields: 可选，需要返回的字段，默认全部 (cpu,mem,swap,ibw,obw)
     */
    app.get('/api/stats/:nodeId/load/archive', async (req, res) => {
        try {
            const nodeId = req.params.nodeId;

            // 获取请求参数
            const startTime = parseInt(req.query.startTime);
            const endTime = parseInt(req.query.endTime);
            const interval = parseInt(req.query.interval) || 2; // 默认2秒
            const fields = req.query.fields ? req.query.fields.split(',') : ['cpu', 'mem', 'swap', 'ibw', 'obw'];

            // 验证参数
            if (isNaN(startTime) || isNaN(endTime)) {
                return res.status(400).json({
                    status: 'error',
                    message: '开始时间和结束时间必须是有效的Unix时间戳（秒）'
                });
            }

            // 动态时间范围限制，平衡数据访问和系统性能
            // 根据数据密度和系统负载动态调整最大时间范围
            const maxTimeRange = 3600 * 12; // 最大12小时，适合深度分析
            if (endTime - startTime > maxTimeRange) {
                return res.status(400).json({
                    status: 'error',
                    message: `时间范围过大，最大支持${maxTimeRange / 3600}小时`
                });
            }

            // 构建查询
            let query = `
                SELECT id, sid, cpu, mem, swap, ibw, obw, created_at
                FROM load_archive
                WHERE sid = ?
                AND created_at >= ?
                AND created_at <= ?
                ORDER BY created_at ASC
            `;

            // 执行查询
            const rows = await db.DB.all(query, [nodeId, startTime, endTime]);

            // 初始化结果对象
            const result = {
                timestamps: []
            };

            // 根据请求的字段初始化数据数组
            fields.forEach(field => {
                if (['cpu', 'mem', 'swap', 'ibw', 'obw'].includes(field)) {
                    result[field] = [];
                }
            });

            // 处理采样
            if (rows.length > 0) {
                // 计算时间范围
                const firstTime = rows[0].created_at;
                const lastTime = rows[rows.length - 1].created_at;
                const timeRange = lastTime - firstTime;

                // 如果时间范围有效，创建均匀分布的时间桶
                if (timeRange > 0 && interval > 2) { // Only sample if interval is greater than raw data interval
                    // 确定使用的采样间隔
                    const dataInterval = 2; // 数据库中数据的实际间隔
                    const requestedInterval = interval;
                    const bucketSize = Math.max(dataInterval, requestedInterval);

                    // 计算需要的桶数量
                    const bucketCount = Math.ceil(timeRange / bucketSize);

                    // 动态数据点数量限制，基于时间范围优化性能
                    // 短时间范围保持高精度，长时间范围适度采样
                    const timeRangeHours = (endTime - startTime) / 3600;
                    const maxDataPoints = timeRangeHours <= 3 ? 3600 :     // ≤3小时: 3600点
                                         timeRangeHours <= 6 ? 5400 :     // 3-6小时: 5400点
                                         7200;                             // >6小时: 7200点
                    const finalBucketCount = Math.min(bucketCount, maxDataPoints);
                    // Ensure finalBucketSize is at least 1 to avoid division by zero if finalBucketCount is 0
                    const finalBucketSize = finalBucketCount > 0 ? timeRange / finalBucketCount : 1;


                    // 初始化桶
                    const buckets = Array(finalBucketCount).fill(null).map(() => { // Changed fill() to fill(null)
                        const bucket = {
                            timestamp: 0,
                            count: 0
                        };

                        // 为每个请求的字段初始化数组
                        fields.forEach(field => {
                            if (['cpu', 'mem', 'swap', 'ibw', 'obw'].includes(field)) {
                                bucket[field] = [];
                            }
                        });

                        return bucket;
                    });

                    // 将数据分配到桶中
                    for (const row of rows) {
                        if (row) {
                             // Ensure bucketIndex is within bounds
                            const bucketIndex = Math.min(
                                Math.max(0, finalBucketCount - 1), // Ensure index is not negative and within bounds
                                Math.floor((row.created_at - firstTime) / finalBucketSize)
                            );

                            if (buckets[bucketIndex]) { // Check if bucket exists
                                buckets[bucketIndex].timestamp += row.created_at;
                                buckets[bucketIndex].count++;

                                fields.forEach(field => {
                                    if (['cpu', 'mem', 'swap', 'ibw', 'obw'].includes(field) && row[field] !== undefined) {
                                        buckets[bucketIndex][field].push(row[field]);
                                    }
                                });
                            }
                        }
                    }

                    // 计算每个桶的平均值
                    for (const bucket of buckets) {
                        if (bucket && bucket.count > 0) { // Check if bucket is not null and has data
                            const avgTimestamp = Math.round(bucket.timestamp / bucket.count);
                            result.timestamps.push(avgTimestamp * 1000);

                            fields.forEach(field => {
                                if (['cpu', 'mem', 'swap', 'ibw', 'obw'].includes(field) && bucket[field] && bucket[field].length > 0) {
                                    const avg = bucket[field].reduce((sum, val) => sum + val, 0) / bucket[field].length;
                                    result[field].push(avg);
                                } else if (['cpu', 'mem', 'swap', 'ibw', 'obw'].includes(field)) {
                                    result[field].push(0); // Push 0 if no data for this field in the bucket
                                }
                            });
                        }
                    }
                } else {
                    // 如果时间范围无效或不需要采样，直接使用原始数据
                    rows.forEach(row => {
                        result.timestamps.push(row.created_at * 1000);

                        fields.forEach(field => {
                            if (['cpu', 'mem', 'swap', 'ibw', 'obw'].includes(field) && row[field] !== undefined) {
                                result[field].push(row[field]);
                            } else if (['cpu', 'mem', 'swap', 'ibw', 'obw'].includes(field)) {
                                result[field].push(0); // Push 0 if field is missing
                            }
                        });
                    });
                }
            }

            // 添加统计信息
            const statsInfo = { // Renamed from stats to statsInfo to avoid conflict
                totalRecords: rows.length,
                timeRange: {
                    start: startTime,
                    end: endTime,
                    duration: endTime - startTime
                },
                dataPoints: result.timestamps.length,
                samplingInterval: interval
            };

            // 返回结果
            res.json({
                status: 'success',
                data: result,
                stats: statsInfo // Use renamed statsInfo
            });
        } catch (error) {
            console.error('获取负载归档数据失败:', error);
            res.status(500).json({
                status: 'error',
                message: '获取负载归档数据失败: ' + error.message
            });
        }
    });

    /**
     * 获取特定时间范围的带宽历史数据
     * GET /api/stats/:nodeId/bandwidth/history
     *
     * Query Params:
     * - range: '10min', '1h', '60min', '24h' (default: '10min')
     * - fields: comma-separated string, e.g., 'ibw,obw' (default: 'ibw,obw')
     */
    app.get('/api/stats/:nodeId/bandwidth/history', async (req, res) => {
        try {
            const { nodeId } = req.params;
            const range = typeof req.query.range === 'string' ? req.query.range : '10min';

            const requestedFieldsString = typeof req.query.fields === 'string' ? req.query.fields : '';
            const requestedFields = requestedFieldsString ? requestedFieldsString.split(',') : [];
            const validLoadFields = ['cpu', 'mem', 'swap', 'ibw', 'obw'];
            // 如果未指定，则默认返回带宽字段；否则严格按请求的字段返回
            const fields = requestedFields.length > 0
                ? [...new Set(requestedFields.filter(f => validLoadFields.includes(f)))]
                : ['ibw', 'obw'];
            const needBandwidth = fields.includes('ibw') || fields.includes('obw');
            const needLoad = fields.includes('cpu') || fields.includes('mem') || fields.includes('swap');

            let dataRows = [];
            const now = Math.floor(Date.now() / 1000);

            const result = { timestamps: [] };
            fields.forEach(field => {
                if (validLoadFields.includes(field)) {
                    result[field] = [];
                }
            });

            switch (range) {
                case '10min': {
                    const startTime10min = now - (10 * 60);
                    // 条件拼接：仅当需要带宽字段时才限制 ibw/obw
                    const bwFilter = needBandwidth ? 'AND ibw > -1 AND obw > -1' : '';
                    dataRows = await db.DB.all(`
                        SELECT created_at, ${fields.join(', ')}
                        FROM load_archive
                        WHERE sid = ? AND created_at >= ? AND created_at <= ?
                        ${bwFilter}
                        ORDER BY created_at ASC
                    `, [nodeId, startTime10min, now]);
                    }
                    break;
                case '1h': {
                    const startTime1h = now - (60 * 60);
                    const bwFilter = needBandwidth ? 'AND ibw > -1 AND obw > -1' : '';
                    dataRows = await db.DB.all(`
                        SELECT created_at, ${fields.join(', ')}
                        FROM load_archive
                        WHERE sid = ? AND created_at >= ? AND created_at <= ?
                        ${bwFilter}
                        ORDER BY created_at ASC
                    `, [nodeId, startTime1h, now]);
                    }
                    break;
                case '60min':
                    const loadMData = await db.load_m.select(nodeId);
                    dataRows = loadMData.filter(r => r.ibw !== -1 && r.obw !== -1)
                                        .map(r => {
                                            const rowData = { created_at: r.created_at };
                                            fields.forEach(f => rowData[f] = r[f]); // Map all fields present in load_m
                                            return rowData;
                                        })
                                        .sort((a, b) => a.created_at - b.created_at);
                    break;
                case '24h': {
                    // 24小时，从load_m获取分钟级数据
                    const now24h = now;
                    const startTime24h = now24h - (24 * 60 * 60);
                    const loadMData24h = await db.load_m.select(nodeId);
                    dataRows = loadMData24h
                        .filter(r => r.created_at >= startTime24h && r.created_at <= now24h)
                        .filter(r => (needBandwidth ? (r.ibw !== -1 && r.obw !== -1) : true))
                        .filter(r => (needLoad ? (r.cpu !== -1 && r.mem !== -1 && r.swap !== -1) : true))
                        .map(r => {
                            const rowData = { created_at: r.created_at };
                            fields.forEach(f => rowData[f] = r[f]);
                            return rowData;
                        })
                        .sort((a, b) => a.created_at - b.created_at);
                    }
                    break;
                case '1month':
                    // 1个月，从load_h获取小时级数据
                    const now1m = now;
                    const startTime1m = now1m - (30 * 24 * 60 * 60);
                    const loadHData1m = await db.load_h.select(nodeId);
                    dataRows = loadHData1m
                        .filter(r => r.created_at >= startTime1m && r.created_at <= now1m && r.ibw !== -1 && r.obw !== -1)
                        .map(r => {
                            const rowData = { created_at: r.created_at };
                            fields.forEach(f => rowData[f] = r[f]);
                            return rowData;
                        })
                        .sort((a, b) => a.created_at - b.created_at);
                    break;
                case '7d': {
                    // 7天：从load_h获取小时级数据
                    const now7d = now;
                    const startTime7d = now7d - (7 * 24 * 60 * 60);
                    const loadHData7d = await db.load_h.select(nodeId);
                    dataRows = loadHData7d
                        .filter(r => r.created_at >= startTime7d && r.created_at <= now7d)
                        .filter(r => {
                            if (needLoad && needBandwidth) {
                                return (r.cpu > -1 || r.mem > -1 || r.swap > -1) && (r.ibw > -1 || r.obw > -1);
                            } else if (needLoad) {
                                return r.cpu > -1 || r.mem > -1 || r.swap > -1;
                            } else if (needBandwidth) {
                                return r.ibw > -1 || r.obw > -1;
                            }
                            return true;
                        })
                        .map(r => {
                            const rowData = { created_at: r.created_at };
                            fields.forEach(f => rowData[f] = r[f]);
                            return rowData;
                        })
                        .sort((a, b) => a.created_at - b.created_at);
                    }
                    break;
                case '30d':
                    // 30天，从load_h获取小时级数据
                    {
                        const now30d = now;
                        const startTime30d = now30d - (30 * 24 * 60 * 60);
                        const loadHData30d = await db.load_h.select(nodeId);
                        dataRows = loadHData30d
                            .filter(r => r.created_at >= startTime30d && r.created_at <= now30d)
                            .filter(r => {
                                if (needLoad && needBandwidth) {
                                    return (r.cpu > -1 || r.mem > -1 || r.swap > -1) && (r.ibw > -1 || r.obw > -1);
                                } else if (needLoad) {
                                    return r.cpu > -1 || r.mem > -1 || r.swap > -1;
                                } else if (needBandwidth) {
                                    return r.ibw > -1 || r.obw > -1;
                                }
                                return true;
                            })
                            .map(r => {
                                const rowData = { created_at: r.created_at };
                                fields.forEach(f => rowData[f] = r[f]);
                                return rowData;
                            })
                            .sort((a, b) => a.created_at - b.created_at);
                    }
                    break;
                case '60d': {
                    // 60天：从load_h获取小时级数据
                    const now60d = now;
                    const startTime60d = now60d - (60 * 24 * 60 * 60);
                    const loadHData60d = await db.load_h.select(nodeId);
                    dataRows = loadHData60d
                        .filter(r => r.created_at >= startTime60d && r.created_at <= now60d)
                        .filter(r => {
                            if (needLoad && needBandwidth) {
                                return (r.cpu > -1 || r.mem > -1 || r.swap > -1) && (r.ibw > -1 || r.obw > -1);
                            } else if (needLoad) {
                                return r.cpu > -1 || r.mem > -1 || r.swap > -1;
                            } else if (needBandwidth) {
                                return r.ibw > -1 || r.obw > -1;
                            }
                            return true;
                        })
                        .map(r => {
                            const rowData = { created_at: r.created_at };
                            fields.forEach(f => rowData[f] = r[f]);
                            return rowData;
                        })
                        .sort((a, b) => a.created_at - b.created_at);
                    }
                    break;
                default:
                    return res.status(400).json({ status: 'error', message: 'Invalid range parameter. Use 10min, 1h, 60min, 24h, 7d, 30d, 60d, or 1month.' });
            }

            if (dataRows.length > 0) {
                dataRows.forEach(row => {
                    result.timestamps.push(row.created_at * 1000);
                    fields.forEach(field => {
                        if (result.hasOwnProperty(field)) {
                            result[field].push(row[field] !== undefined ? row[field] : ((field === 'ibw' || field === 'obw') ? -1 : 0));
                        }
                    });
                });
            }

            res.json({ status: 'success', data: result });

        } catch (error) {
            console.error(`获取带宽历史数据失败 (${req.params.nodeId}, range: ${req.query.range}):`, error);
            res.status(500).json({ status: 'error', message: '获取带宽历史数据失败: ' + error.message });
        }
    });

    // The existing module might return an object, or nothing.
    // If it returns an object, ensure this structure is maintained.
    // return {
    //     // any previously exported methods
    // };
};
