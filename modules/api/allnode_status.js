'use strict';

/**
 * 所有节点状态API模块
 * 提供获取所有节点最新状态数据的API接口
 */

module.exports = (svr, db) => {
    const app = svr.app || svr; // 兼容不同的Express实例传递方式
    
    /**
     * 获取所有节点的状态数据
     * GET /api/allnode_status
     */
    app.get('/api/allnode_status', async (req, res) => {
        try {
            const isAdmin = req.admin;

            // 获取状态管理器
            const statsModule = svr.locals.stats;
            if (!statsModule) {
                return res.status(500).json({
                    success: false,
                    message: '状态模块未初始化'
                });
            }

            // 稳健获取所有节点数据：优先纯内存 → 再到缓存API → 最后同步版 → 空对象
            let statsData = {};
            if (statsModule) {
                if (typeof statsModule.getStatsFromMemory === 'function') {
                    statsData = await Promise.resolve(statsModule.getStatsFromMemory(isAdmin));
                } else if (typeof statsModule.getStatsData === 'function') {
                    statsData = await statsModule.getStatsData(isAdmin);
                } else if (typeof statsModule.getStats === 'function') {
                    statsData = await statsModule.getStats(isAdmin);
                }
            }

            // 获取当前时间戳
            const timestamp = Math.floor(Date.now() / 1000);

            // 构建响应数据
            const responseData = {
                success: true,
                timestamp,
                data: statsData
            };

            // 返回JSON响应
            res.json(responseData);
        } catch (error) {
            console.error('获取所有节点状态数据失败:', error);
            res.status(500).json({
                success: false,
                message: '获取所有节点状态数据失败: ' + error.message
            });
        }
    });
    
    console.log('[API] 所有节点状态API模块已加载');
};
