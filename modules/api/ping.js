'use strict';

const { exec } = require('child_process');
const crypto = require('crypto');

/**
 * Ping模块 - 提供ICMP Ping测试功能
 */

module.exports = (app, database) => {
    // 初始化监控数据库模块
    const monitor = require('../../database/monitor')(database.DB);

    // 获取Express实例，用于访问服务器对象
    const svr = app.locals.svr || app;

    /**
     * 执行Ping测试
     * @param {string} host - 目标主机
     * @param {number} count - 测试次数
     * @param {number} timeout - 超时时间(秒)
     * @returns {Promise<Object>} 测试结果
     */
    async function performPing(host, count = 8, timeout = 5) {
        // 获取调试模式
        const setting = await database.setting.all();
        const debugMode = setting.debug === true;

        if (debugMode) {
            console.log(`[Ping调试] 开始测试: ${host}, 测试次数: ${count}, 超时: ${timeout}秒`);
        }

        // 参数验证和限制
        count = Math.min(Math.max(1, count), 20); // 限制在1-20次
        timeout = Math.min(Math.max(1, timeout), 10); // 限制在1-10秒

        // 根据操作系统构建ping命令
        const platform = process.platform;
        let pingCmd;

        if (platform === 'win32') {
            // Windows
            pingCmd = `ping -n ${count} -w ${timeout * 1000} ${host}`;
        } else {
            // Linux/macOS
            pingCmd = `ping -c ${count} -W ${timeout} ${host}`;
        }

        return new Promise((resolve) => {
            exec(pingCmd, (error, stdout, stderr) => {
                if (debugMode) {
                    console.log(`[Ping调试] 命令: ${pingCmd}`);
                    console.log(`[Ping调试] 输出: ${stdout}`);
                    if (stderr) console.error(`[Ping调试] 错误: ${stderr}`);
                    if (error) console.error(`[Ping调试] 执行错误: ${error.message}`);
                }

                // 解析ping结果
                const results = parsePingOutput(stdout, platform, count, timeout, debugMode);

                resolve(results);
            });
        });
    }

    /**
     * 解析ping命令输出
     * @param {string} output - ping命令输出
     * @param {string} platform - 操作系统平台
     * @param {number} count - 测试次数
     * @param {number} timeout - 超时时间(秒)
     * @param {boolean} debugMode - 是否开启调试模式
     * @returns {Object} 解析结果
     */
    function parsePingOutput(output, platform, count, timeout, debugMode = false) {
        // 初始化结果
        const results = [];
        let successful = 0;
        let avgTime = 0;
        let minTime = 0;
        let maxTime = 0;
        const timeoutMs = timeout * 1000;

        try {
            if (platform === 'win32') {
                // Windows解析
                const lines = output.split('\n');

                // 提取每次ping的结果
                for (let i = 2; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (line.includes('Reply from') || line.includes('来自')) {
                        successful++;
                        // 提取时间
                        const timeMatch = line.match(/time[=<]([\d\.]+)ms/i) || line.match(/时间[=<]([\d\.]+)ms/i);
                        if (timeMatch) {
                            const time = parseFloat(timeMatch[1]);
                            results.push({
                                seq: results.length + 1,
                                success: true,
                                time: time
                            });
                        }
                    } else if (line.includes('Request timed out') || line.includes('请求超时')) {
                        results.push({
                            seq: results.length + 1,
                            success: false,
                            time: timeoutMs
                        });
                    }
                }

                // 提取统计信息
                const statsLine = output.match(/Minimum = ([\d\.]+)ms, Maximum = ([\d\.]+)ms, Average = ([\d\.]+)ms/i) ||
                                 output.match(/最小 = ([\d\.]+)ms，最大 = ([\d\.]+)ms，平均 = ([\d\.]+)ms/i);

                if (statsLine) {
                    minTime = parseFloat(statsLine[1]);
                    maxTime = parseFloat(statsLine[2]);
                    avgTime = parseFloat(statsLine[3]);
                }
            } else {
                // Linux/macOS解析
                const lines = output.split('\n');

                // 提取每次ping的结果
                for (let i = 1; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (line.includes('bytes from')) {
                        successful++;
                        // 提取时间
                        const timeMatch = line.match(/time=([\d\.]+) ms/i);
                        if (timeMatch) {
                            const time = parseFloat(timeMatch[1]);
                            results.push({
                                seq: results.length + 1,
                                success: true,
                                time: time
                            });
                        }
                    } else if (line.includes('Request timeout') || line.includes('100% packet loss')) {
                        results.push({
                            seq: results.length + 1,
                            success: false,
                            time: timeoutMs
                        });
                    }
                }

                // 提取统计信息
                const statsLine = output.match(/min\/avg\/max\/mdev = ([\d\.]+)\/([\d\.]+)\/([\d\.]+)\/([\d\.]+) ms/i);

                if (statsLine) {
                    minTime = parseFloat(statsLine[1]);
                    avgTime = parseFloat(statsLine[2]);
                    maxTime = parseFloat(statsLine[3]);
                }
            }
        } catch (err) {
            if (debugMode) {
                console.error(`[Ping调试] 解析输出失败:`, err);
            }
        }

        // 计算成功率
        const successRate = successful / count;

        // 如果有多个成功的测试，剔除最高值后计算平均值
        const successTimes = results.filter(r => r.success).map(r => r.time);

        if (successTimes.length > 1) {
            // 有多个成功的测试，剔除最高值后计算平均值
            const maxValue = Math.max(...successTimes);
            const sumWithoutMax = successTimes.reduce((sum, time) => sum + time, 0) - maxValue;
            // 剔除最高延迟值后的平均值
            avgTime = sumWithoutMax / (successTimes.length - 1);
            minTime = Math.min(...successTimes);
            maxTime = maxValue; // 仍然记录最高值，只是不计入平均值
        } else if (successTimes.length === 1) {
            // 只有一个成功的测试，使用该值
            avgTime = successTimes[0];
            minTime = successTimes[0];
            maxTime = successTimes[0];
        } else {
            // 全部失败，使用超时时间
            avgTime = timeoutMs;
            minTime = timeoutMs;
            maxTime = timeoutMs;
        }

        const stats = {
            host,
            count,
            successful,
            failed: count - successful,
            success_rate: successRate,
            min_time: minTime,
            max_time: maxTime,
            avg_time: avgTime,
            results
        };

        if (debugMode) {
            console.log(`[Ping调试] 测试结果统计:`, JSON.stringify(stats, null, 2));
        }

        return stats;
    }

    /**
     * 验证API密钥
     */
    async function validateApiKey(req, res) {
        const key = req.query.key;
        if (!key) {
            res.status(401).json({ success: false, message: '缺少API密钥' });
            return false;
        }

        // 获取系统全局API密钥
        const setting = await database.setting.all();
        const validApiKey = setting.api_key || 'dstatus';  // 默认API密钥

        // 验证密钥
        if (key !== validApiKey) {
            // 检查是否为服务器的API密钥
            const servers = database.getServers();
            const validServer = servers.find(server =>
                server.data && server.data.api && server.data.api.key === key
            );

            if (!validServer) {
                res.status(401).json({ success: false, message: 'API密钥无效' });
                return false;
            }
        }

        return true;
    }

    // ========== API路由 ==========

    /**
     * 执行Ping测试
     * GET /ping?key=<your_api_key>&host=<target_host>&count=<count>&timeout=<timeout>
     */
    app.get('/ping', async (req, res) => {
        if (!(await validateApiKey(req, res))) return;

        const { host, count, timeout } = req.query;

        // 参数验证
        if (!host) {
            return res.status(400).json({
                success: false,
                message: '缺少必需参数: host是必需的'
            });
        }

        try {
            const result = await performPing(
                host,
                count ? parseInt(count, 10) : 8,
                timeout ? parseFloat(timeout) : 5
            );

            res.json({ success: true, data: result });
        } catch (err) {
            console.error('Ping测试失败:', err);
            res.status(500).json({
                success: false,
                message: '执行Ping测试失败: ' + err.message
            });
        }
    });

    /**
     * 执行Ping测试并保存结果 (服务器调度接口)
     * GET /exec/ping?key=<your_api_key>&host=<target_host>&count=<count>&timeout=<timeout>
     */
    app.get('/exec/ping', async (req, res) => {
        if (!(await validateApiKey(req, res))) return;

        const { host, count, timeout } = req.query;

        // 参数验证
        if (!host) {
            return res.status(400).json({
                success: false,
                message: '缺少必需参数: host是必需的'
            });
        }

        try {
            const result = await performPing(
                host,
                count ? parseInt(count, 10) : 8,
                timeout ? parseFloat(timeout) : 5
            );

            res.json({ success: true, data: result });
        } catch (err) {
            console.error('Ping测试失败:', err);
            res.status(500).json({
                success: false,
                message: '执行Ping测试失败: ' + err.message
            });
        }
    });

    // 导出模块
    return {
        performPing
    };
};
