'use strict';

/**
 * 性能监控API模块
 * 提供性能监控相关的API接口
 */

module.exports = (svr, db) => {
    const app = svr.app || svr; // 兼容不同的Express实例传递方式

    // 验证API密钥
    async function validateApiKey(req, res) {
        const apiKey = req.query.key || req.headers['x-api-key'];
        const setting = await db.setting.all();

        if (!apiKey || apiKey !== setting.api_key) {
            res.status(401).json({
                success: false,
                message: 'API密钥无效'
            });
            return false;
        }

        return true;
    }

    /**
     * 获取性能数据
     * GET /api/performance/data?key=<your_api_key>
     */
    app.get('/api/performance/data', async (req, res) => {
        if (!(await validateApiKey(req, res))) return;

        try {
            // 检查性能监控模块是否可用
            if (!svr.locals.performanceMonitor) {
                return res.status(503).json({
                    success: false,
                    message: '性能监控模块未加载'
                });
            }

            // 获取性能数据
            const data = svr.locals.performanceMonitor.getPerformanceData();

            res.json({
                success: true,
                data
            });
        } catch (err) {
            console.error('获取性能数据失败:', err);
            res.status(500).json({
                success: false,
                message: '获取性能数据失败: ' + err.message
            });
        }
    });

    /**
     * 获取当前性能快照
     * GET /api/performance/snapshot?key=<your_api_key>
     */
    app.get('/api/performance/snapshot', async (req, res) => {
        if (!(await validateApiKey(req, res))) return;

        try {
            // 检查性能监控模块是否可用
            if (!svr.locals.performanceMonitor) {
                return res.status(503).json({
                    success: false,
                    message: '性能监控模块未加载'
                });
            }

            // 获取当前性能数据
            const os = require('os');

            // 计算CPU使用率
            const cpus = os.cpus();
            let totalIdle = 0;
            let totalTick = 0;

            for (const cpu of cpus) {
                for (const type in cpu.times) {
                    totalTick += cpu.times[type];
                }
                totalIdle += cpu.times.idle;
            }

            const idle = totalIdle / cpus.length;
            const total = totalTick / cpus.length;
            const cpuUsage = 100 - (idle / total * 100);

            // 计算内存使用率
            const totalMemory = os.totalmem();
            const freeMemory = os.freemem();
            const usedMemory = totalMemory - freeMemory;
            const memoryUsage = (usedMemory / totalMemory) * 100;

            // 获取系统负载
            const loadAvg = os.loadavg();

            // 获取进程信息
            const processMemoryUsage = process.memoryUsage();
            const processUptime = process.uptime();

            // 构建快照数据
            const snapshot = {
                timestamp: Date.now(),
                cpu: {
                    usage: cpuUsage.toFixed(2),
                    cores: cpus.length,
                    loadAvg: {
                        '1m': loadAvg[0].toFixed(2),
                        '5m': loadAvg[1].toFixed(2),
                        '15m': loadAvg[2].toFixed(2)
                    }
                },
                memory: {
                    total: totalMemory,
                    free: freeMemory,
                    used: usedMemory,
                    usage: memoryUsage.toFixed(2)
                },
                process: {
                    uptime: processUptime,
                    memory: {
                        rss: processMemoryUsage.rss,
                        heapTotal: processMemoryUsage.heapTotal,
                        heapUsed: processMemoryUsage.heapUsed,
                        external: processMemoryUsage.external
                    }
                },
                os: {
                    platform: os.platform(),
                    release: os.release(),
                    uptime: os.uptime()
                }
            };

            res.json({
                success: true,
                data: snapshot
            });
        } catch (err) {
            console.error('获取性能快照失败:', err);
            res.status(500).json({
                success: false,
                message: '获取性能快照失败: ' + err.message
            });
        }
    });

    console.log('性能监控API模块已加载');
};
