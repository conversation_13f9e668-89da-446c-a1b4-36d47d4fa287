'use strict';

/**
 * 冷却机制配置
 * 支持环境变量覆盖默认值
 */

module.exports = {
  // 冷却核心配置
  cooling: {
    // 失败阈值：连续失败多少次后开始冷却 (优化: 3→8次，延长部署窗口)
    failThreshold: parseInt(process.env.COOLING_FAIL_THRESHOLD) || 8,
    
    // 初始冷却时间（分钟）(优化: 5→2分钟，快速恢复机会)
    initialCoolingTime: parseInt(process.env.COOLING_INITIAL_TIME) || 2,
    
    // 最大冷却时间（分钟）(优化: 60→15分钟，合理上限)
    maxCoolingTime: parseInt(process.env.COOLING_MAX_TIME) || 15,
    
    // 冷却时间增长因子 (优化: 2→1.2，温和递增)
    coolingFactor: parseFloat(process.env.COOLING_FACTOR) || 1.2,
    
    // 失败计数重置时间（分钟）(优化: 30→60分钟，更长重置窗口)
    failCountResetTime: parseInt(process.env.COOLING_RESET_TIME) || 60
  },
  
  // 获取动态计算的冷却时间
  getCoolingTime(failCount) {
    const config = this.cooling;
    if (failCount < config.failThreshold) return 0;
    
    const coolingMinutes = Math.min(
      config.initialCoolingTime * Math.pow(config.coolingFactor, failCount - config.failThreshold),
      config.maxCoolingTime
    );
    
    return coolingMinutes;
  }
};