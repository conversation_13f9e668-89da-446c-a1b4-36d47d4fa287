'use strict';

const coolingConfig = require('./cooling.config');

// 节点冷却状态跟踪
const nodeCoolingStatus = {
    // 格式: { sid: { failCount: 0, coolUntil: 0, lastFailTime: 0 } }
};

/**
 * 检查节点是否处于冷却状态
 * @param {string} sid - 节点ID
 * @returns {boolean} - 是否处于冷却状态
 */
function isNodeCooling(sid) {
    const now = Date.now();
    const status = nodeCoolingStatus[sid];

    if (!status || !status.coolUntil) {
        return false;
    }

    return now < status.coolUntil;
}

/**
 * 更新节点冷却状态
 * @param {string} sid - 节点ID
 * @param {boolean} success - 测试是否成功
 * @param {boolean} debugMode - 是否开启调试模式
 */
function updateNodeCoolingStatus(sid, success, debugMode = false) {
    const now = Date.now();

    // 初始化状态
    if (!nodeCoolingStatus[sid]) {
        nodeCoolingStatus[sid] = { failCount: 0, coolUntil: 0, lastFailTime: 0 };
    }

    const status = nodeCoolingStatus[sid];

    if (success) {
        // 成功时，重置失败计数
        status.failCount = 0;
        status.coolUntil = 0;
    } else {
        // 检查是否需要重置失败计数（长时间未失败）
        const failCountResetTime = coolingConfig.cooling.failCountResetTime * 60 * 1000;
        if (status.lastFailTime > 0 && now - status.lastFailTime > failCountResetTime) {
            status.failCount = 0;
        }

        // 增加失败计数
        status.failCount++;
        status.lastFailTime = now;

        // 计算冷却时间
        if (status.failCount > coolingConfig.cooling.failThreshold) {
            // 动态计算冷却时间，失败越多冷却越长
            const coolingMinutes = coolingConfig.getCoolingTime(status.failCount);

            status.coolUntil = now + coolingMinutes * 60 * 1000;

            if (debugMode) {
                console.log(`[TCPing冷却] 节点 ${sid} 失败次数: ${status.failCount}, 冷却时间: ${coolingMinutes}分钟, 冷却结束时间: ${new Date(status.coolUntil).toLocaleString()}`);
            }
        }
    }
}

/**
 * 获取节点冷却状态信息
 * @returns {Object} - 所有节点的冷却状态
 */
function getNodeCoolingStatus() {
    const now = Date.now();
    const result = {};

    for (const sid in nodeCoolingStatus) {
        const status = nodeCoolingStatus[sid];
        result[sid] = {
            failCount: status.failCount,
            cooling: now < status.coolUntil,
            coolUntil: status.coolUntil > 0 ? new Date(status.coolUntil).toISOString() : null,
            remainingTime: status.coolUntil > now ? Math.ceil((status.coolUntil - now) / 60000) : 0
        };
    }

    return result;
}

module.exports = {
    isNodeCooling,
    updateNodeCoolingStatus,
    getNodeCoolingStatus,
    nodeCoolingStatus, // Export for scheduler to check
    coolingConfig // Export for scheduler if needed
};
