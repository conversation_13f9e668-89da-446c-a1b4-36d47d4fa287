'use strict';

/**
 * 最新数据API模块
 * 提供获取节点最新状态数据的API接口
 */

module.exports = (svr, db) => {
    const app = svr.app || svr; // 兼容不同的Express实例传递方式
    
    // 获取节点最新状态数据
    app.get('/stats/:sid/latest', async (req, res) => {
        try {
            const { sid } = req.params;
            const isAdmin = req.admin;
            
            // 获取状态管理器
            const statsModule = svr.locals.stats;
            if (!statsModule) {
                return res.status(500).json({
                    status: 'error',
                    message: '状态模块未初始化'
                });
            }
            
            // 获取节点数据（稳健回退：内存 → 数据 → 同步 → 空对象）
            let statsData = {};
            if (typeof statsModule.getStatsFromMemory === 'function') {
                statsData = await Promise.resolve(statsModule.getStatsFromMemory(isAdmin));
            } else if (typeof statsModule.getStatsData === 'function') {
                statsData = await statsModule.getStatsData(isAdmin);
            } else if (typeof statsModule.getStats === 'function') {
                statsData = await statsModule.getStats(isAdmin);
            }
            const node = statsData[sid];
            
            if (!node) {
                return res.status(404).json({
                    status: 'error',
                    message: '节点不存在'
                });
            }
            
            // 获取当前时间戳
            const timestamp = Math.floor(Date.now() / 1000);
            
            // 构建响应数据
            const responseData = {
                status: 'success',
                timestamp,
                data: {
                    [sid]: node
                }
            };
            
            // 返回数据
            res.json(responseData);
        } catch (error) {
            console.error('获取节点最新状态数据失败:', error);
            res.status(500).json({
                status: 'error',
                message: '获取节点最新状态数据失败',
                error: error.message
            });
        }
    });
    
    console.log('[API] 最新数据API模块已加载');
};
