/**
 * 版本信息 API 模块
 * 提供版本号查询和更新接口
 */

const express = require('express');
const fs = require('fs');
const path = require('path');
const router = express.Router();

/**
 * 获取版本信息
 * GET /api/version
 */
router.get('/version', async (req, res) => {
    try {
        // 读取 package.json 获取版本信息
        const packagePath = path.join(__dirname, '../../package.json');
        const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        
        // 尝试读取构建时生成的版本信息
        const versionInfoPath = path.join(__dirname, '../../static/version.json');
        let buildInfo = null;
        
        try {
            if (fs.existsSync(versionInfoPath)) {
                buildInfo = JSON.parse(fs.readFileSync(versionInfoPath, 'utf8'));
            }
        } catch (error) {
            console.warn('无法读取构建版本信息:', error.message);
        }
        
        // 组装版本信息（简化）
        const versionInfo = {
            version: packageData.version,
            success: true
        };
        
        res.json(versionInfo);
    } catch (error) {
        console.error('获取版本信息失败:', error);
        res.status(500).json({
            success: false,
            error: '获取版本信息失败',
            message: error.message
        });
    }
});

/**
 * 获取版本历史记录
 * GET /api/version/history
 */
router.get('/version/history', async (req, res) => {
    try {
        // 这里可以从数据库或文件系统读取版本历史
        // 暂时返回模拟数据
        const history = [
            {
                version: '25.07.1',
                releaseDate: '2025-07-17',
                type: 'stable',
                description: '正式版本发布'
            },
            {
                version: '25.07.1-beta',
                releaseDate: '2025-07-17',
                type: 'beta',
                description: '测试版本'
            }
        ];
        
        res.json({
            success: true,
            data: history
        });
    } catch (error) {
        console.error('获取版本历史失败:', error);
        res.status(500).json({
            success: false,
            error: '获取版本历史失败',
            message: error.message
        });
    }
});

/**
 * 检查版本更新
 * GET /api/version/check-update
 */
router.get('/version/check-update', async (req, res) => {
    try {
        // 这里可以实现与 GitHub API 的版本检查
        // 暂时返回模拟数据
        const currentVersion = require('../../package.json').version;
        
        res.json({
            success: true,
            currentVersion,
            hasUpdate: false,
            latestVersion: currentVersion,
            updateUrl: 'https://github.com/your-repo/releases/latest'
        });
    } catch (error) {
        console.error('检查版本更新失败:', error);
        res.status(500).json({
            success: false,
            error: '检查版本更新失败',
            message: error.message
        });
    }
});

module.exports = router;