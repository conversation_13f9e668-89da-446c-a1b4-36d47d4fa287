'use strict';

const { performance } = require('perf_hooks');
const net = require('net');

/**
 * Perform single TCPing test
 * @param {string} host - Target host
 * @param {number} port - Target port
 * @param {number} timeout - Timeout (ms)
 * @param {boolean} debugMode - Debug mode
 * @returns {Promise<Object>} Single test result
 */
function tcpingOnce(host, port, timeout, debugMode = false) {
    return new Promise((resolve) => {
        const startTime = performance.now();
        let isDone = false;
        let socket; // Declare socket here to ensure it's in scope for the timer

        if (debugMode) {
            console.log(`[TCPing调试] 开始连接 ${host}:${port}，开始时间: ${new Date().toISOString()}, 性能计时器: ${startTime}`);
            const dnsStart = Date.now();
            require('dns').lookup(host, (err, address) => {
                const dnsTime = Date.now() - dnsStart;
                if (err) {
                    console.error(`[TCPing调试] DNS解析失败: ${host} -> ${err.message}`);
                } else {
                    console.log(`[TCPing调试] DNS解析: ${host} -> ${address}, 耗时: ${dnsTime}ms`);
                }
            });
        }

        const timer = setTimeout(() => {
            if (!isDone) {
                isDone = true;
                if (socket) socket.destroy();
                if (debugMode) {
                    console.error(`[TCPing调试] 连接超时: ${host}:${port}, 超时时间: ${timeout}ms`);
                }
                resolve({ success: false, time: timeout });
            }
        }, timeout);

        socket = net.createConnection({ host, port }, () => {
            if (!isDone) {
                isDone = true;
                const endTime = performance.now();
                const elapsed = endTime - startTime;
                const formattedTime = parseFloat(elapsed.toFixed(2));

                if (debugMode) {
                    console.log(`[TCPing调试] 连接成功: ${host}:${port}, 结束时间: ${new Date().toISOString()}, 性能计时器: ${endTime}`);
                    console.log(`[TCPing调试] 连接耗时: ${formattedTime}ms (开始: ${startTime}, 结束: ${endTime})`);
                    try {
                        const localAddress = socket.localAddress || 'unknown';
                        const localPort = socket.localPort || 'unknown';
                        const remoteAddress = socket.remoteAddress || 'unknown';
                        const remotePort = socket.remotePort || 'unknown';
                        console.log(`[TCPing调试] 连接详情: 本地=${localAddress}:${localPort}, 远程=${remoteAddress}:${remotePort}`);
                    } catch (err) {
                        console.error(`[TCPing调试] 获取连接详情失败:`, err);
                    }
                }

                clearTimeout(timer);
                socket.destroy();
                resolve({ success: true, time: formattedTime });
            }
        });

        socket.on('error', (err) => {
            if (!isDone) {
                isDone = true;
                clearTimeout(timer);
                socket.destroy();
                if (debugMode) {
                    console.error(`[TCPing调试] 连接失败: ${host}:${port}, 错误: ${err.message}`);
                }
                resolve({ success: false, time: timeout, error: err.message });
            }
        });
    });
}

/**
 * Execute TCPing test
 * @param {string} host - Target host
 * @param {number} port - Target port
 * @param {number} count - Test count
 * @param {number} timeout - Timeout (seconds)
 * @param {Object} database - Database instance (for settings)
 * @returns {Promise<Object>} Test result
 */
async function performTcping(host, port, count = 8, timeout = 5, database) {
    const setting = database.setting.all();
    const debugMode = setting.debug === true;

    if (debugMode) {
        console.log(`[TCPing调试] 开始测试: ${host}:${port}, 测试次数: ${count}, 超时: ${timeout}秒`);
    }

    count = Math.min(Math.max(1, count), 20);
    timeout = Math.min(Math.max(1, timeout), 10);

    const timeoutMs = timeout * 1000;
    const results = [];

    for (let i = 0; i < count; i++) {
        try {
            if (debugMode) {
                console.log(`[TCPing调试] 第${i + 1}次测试 ${host}:${port}`);
            }
            const result = await tcpingOnce(host, port, timeoutMs, debugMode);
            results.push({
                seq: i + 1,
                success: result.success,
                time: result.success ? result.time : timeoutMs
            });
            if (debugMode) {
                console.log(`[TCPing调试] 第${i + 1}次测试结果: 成功=${result.success}, 时间=${result.success ? result.time : timeoutMs}ms`);
            }
        } catch (err) {
            if (debugMode) {
                console.error(`[TCPing调试] 第${i + 1}次测试出错:`, err);
            }
            results.push({
                seq: i + 1,
                success: false,
                time: timeoutMs,
                error: err.message
            });
        }
    }

    const successful = results.filter(r => r.success).length;
    const successTimes = results.filter(r => r.success).map(r => r.time);

    let successRate = successful / count;
    let avgTime, minTime, maxTime;

    if (successTimes.length > 1) {
        const maxValue = Math.max(...successTimes);
        const sumWithoutMax = successTimes.reduce((sum, time) => sum + time, 0) - maxValue;
        avgTime = sumWithoutMax / (successTimes.length - 1);
        minTime = Math.min(...successTimes);
        maxTime = maxValue;
    } else if (successTimes.length === 1) {
        avgTime = successTimes[0];
        minTime = successTimes[0];
        maxTime = successTimes[0];
    } else {
        avgTime = timeoutMs;
        minTime = timeoutMs;
        maxTime = timeoutMs;
    }

    const stats = {
        host,
        port,
        count,
        successful,
        failed: count - successful,
        success_rate: successRate,
        min_time: minTime,
        max_time: maxTime,
        avg_time: avgTime,
        results
    };

    if (debugMode) {
        console.log(`[TCPing调试] 测试结果统计:`, JSON.stringify(stats, null, 2));
    }
    return stats;
}

module.exports = {
    tcpingOnce,
    performTcping
};
