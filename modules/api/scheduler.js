'use strict';

const fetch = require('node-fetch');
const { performTcping } = require('./tcping_core');
const { logger } = require('../utils/logger');
const { isNodeCooling, updateNodeCoolingStatus, nodeCoolingStatus: coolingStatusData } = require('./cooling_logic'); // Renamed import

/**
 * 为测试添加超时控制
 * @param {Function} testFunction - 测试函数
 * @param {number} timeout - 超时时间(毫秒)
 * @returns {Promise<Object>} 测试结果或超时结果
 */
async function executeTestWithTimeout(testFunction, timeout = 10000, context = {}) {
    const testPromise = testFunction();
    const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Test timeout')), timeout)
    );

    try {
        return await Promise.race([testPromise, timeoutPromise]);
    } catch (error) {
        // 构建详细的日志信息
        const testType = context.testType || '未知测试';
        const targetInfo = context.targetInfo || '未知目标';
        const nodeInfo = context.nodeInfo || '未知节点';
        
        console.error(`[${testType}] 测试超时或失败: 目标=${targetInfo}, 节点=${nodeInfo}, 错误=${error.message}`);
        
        // 返回一个表示失败的结果
        return {
            success_rate: 0,
            avg_time: 0,
            min_time: 0,
            max_time: 0
        };
    }
}

/**
 * 保存测试结果到各种统计数据中
 * @param {string} targetId - 目标ID
 * @param {Object} result - 测试结果
 * @param {Object} database - Database instance
 * @param {Object} monitor - Monitor instance
 */
async function saveTestResult(targetId, result, database, monitor) {
    // 获取调试模式
    const setting = database.setting.all();
    const debugMode = setting.debug === true;

    // 准备保存的数据
    const data = {
        success_rate: result.success_rate,
        avg_time: result.avg_time,
        min_time: result.min_time,
        max_time: result.max_time,
        sid: result.sid  // 保存节点ID
    };

    // 创建复合键，用于防止同一目标和节点在短时间内重复写入
    const compositeKey = `${targetId}_${data.sid || 'null'}`;
    const now = new Date();
    const currentTime = now.getTime();
    const currentMinuteKey = Math.floor(currentTime / 60000); // 当前分钟的时间戳（毫秒除以60000）
    
    // 检查是否已经在当前分钟内写入过数据
    if (!global.tcpingLastWriteTime) {
        global.tcpingLastWriteTime = {};
    }

    const lastWrite = global.tcpingLastWriteTime[compositeKey];
    const lastMinuteKey = lastWrite ? Math.floor(lastWrite / 60000) : 0;

    // 如果在当前分钟内已经写入过数据，则跳过
    if (lastWrite && lastMinuteKey === currentMinuteKey) {
        if (debugMode) {
            const lastTime = new Date(lastWrite).toISOString();
            const currentTimeStr = now.toISOString();
            console.log(`[TCPing调试] 跳过在同一分钟内的重复写入: ` +
                `targetId=${targetId}, sid=${data.sid}, ` +
                `当前时间=${currentTimeStr}, 上次写入=${lastTime}, ` +
                `间隔=${Math.floor((currentTime - lastWrite) / 1000)}秒`);
        }
        return data;
    }

    // 更新最后写入时间
    global.tcpingLastWriteTime[compositeKey] = currentTime;

    // 保存到分钟级数据
    try {
        const shiftResult = await monitor.tcping_m.shift(targetId, data);
        if (debugMode) {
            console.log(`[TCPing调试] 保存到tcping_m表${shiftResult ? '成功' : '失败'}: ` +
                `targetId=${targetId}, sid=${data.sid}, ` +
                `success_rate=${data.success_rate}, avg_time=${data.avg_time}ms, ` +
                `min_time=${data.min_time}ms, max_time=${data.max_time}ms, ` +
                `时间=${now.toISOString()}`);
        }
    } catch (err) {
        console.error(`保存数据到tcping_m表失败: targetId=${targetId}, sid=${data.sid}, 错误:`, err);
    }

    // 保存到归档表（双轨保存）
    try {
        const archiveResult = await monitor.tcping_archive.insert(targetId, data);
        if (debugMode && archiveResult) {
            console.log(`[TCPing调试] 成功保存数据到归档表: targetId=${targetId}, sid=${data.sid}`);
        } else if (debugMode) {
            console.warn(`[TCPing调试] 保存数据到归档表失败: targetId=${targetId}, sid=${data.sid}`);
        }
    } catch (err) {
        console.error('保存数据到归档表失败:', err);
        // 继续执行，不影响现有功能
    }

    return data;
}


/**
 * 在远程节点上执行测试
 * @param {Object} server - 服务器节点
 * @param {Object} target - 监控目标
 * @param {string} testType - 测试类型 ('tcping' 或 'ping')
 * @param {Object} database - Database instance
 * @param {Object} monitor - Monitor instance
 */
async function executeRemoteTest(server, target, testType = 'tcping', database, monitor) {
    const setting = database.setting.all();
    const debugMode = setting.debug === true;

    if (isNodeCooling(server.sid)) {
        if (debugMode) {
            console.log(`[TCPing调试] 节点 ${server.name} (${server.sid}) 处于冷却状态，跳过测试`);
        }
        const coolingResult = {
            success_rate: 0, avg_time: 0, min_time: 0, max_time: 0,
            sid: server.sid, cooling: true
        };
        await saveTestResult(target.id, coolingResult, database, monitor);
        return;
    }

    const MAX_RETRY_COUNT = 3;
    const RETRY_DELAY = 1000;
    let retryCount = 0;

    try {
        const serverHost = server.data?.host || server.data?.api?.host || server.data?.ssh?.host || server.ip;
        const serverPort = server.data?.api?.port || 8080;
        const apiKey = server.data?.api?.key || '';

        if (!serverHost) throw new Error(`无法获取服务器 ${server.name} 的主机地址`);

        let url;
        if (testType === 'ping') {
            url = `http://${serverHost}:${serverPort}/ping?key=${apiKey}&host=${target.host}&count=8&timeout=5`;
        } else {
            url = `http://${serverHost}:${serverPort}/tcping?key=${apiKey}&host=${target.host}&port=${target.port}&count=8&timeout=5`;
        }

        if (debugMode) {
            console.log(`[TCPing调试] 通过远程节点执行${testType}测试:`);
            console.log(`[TCPing调试] - 节点: ${server.name} (${server.sid})`);
            console.log(`[TCPing调试] - 目标: ${target.name} (${target.host}${testType === 'tcping' ? ':' + target.port : ''})`);
            console.log(`[TCPing调试] - 请求URL: ${url}`);
        }

        while (retryCount < MAX_RETRY_COUNT) {
            try {
                const requestStart = Date.now();
                const response = await fetch(url, { timeout: 15000 });
                const data = await response.json();
                const requestTime = Date.now() - requestStart;

                if (debugMode) {
                    console.log(`[TCPing调试] 远程测试请求耗时: ${requestTime}ms`);
                    console.log(`[TCPing调试] 远程测试结果:`, JSON.stringify(data, null, 2));
                }

                if (data.success) {
                    let resultDataToSave;
                    if (testType === 'ping') {
                        if (data.data) {
                            const pingData = data.data;
                            let sr = pingData.success_rate;
                            if (typeof sr === 'undefined') {
                                if (typeof pingData.loss_rate !== 'undefined') sr = 1 - pingData.loss_rate;
                                else if (typeof pingData.successful !== 'undefined' && typeof pingData.count !== 'undefined' && pingData.count > 0) sr = pingData.successful / pingData.count;
                                else sr = 1;
                            }
                            resultDataToSave = { ...pingData, success_rate: sr, sid: server.sid };
                        }
                    } else { // tcping
                        if (data.data && typeof data.data.success_rate !== 'undefined') {
                            if (data.data.success_rate === 0 && debugMode) {
                                console.log(`[TCPing调试] 从节点 ${server.name} 获取的数据显示成功率为0，可能表示连接失败`);
                            }
                            resultDataToSave = { ...data.data, sid: server.sid };
                            // If success_rate is 0, ensure times are 0 for consistency
                            if (resultDataToSave.success_rate === 0) {
                                resultDataToSave.avg_time = 0;
                                resultDataToSave.min_time = 0;
                                resultDataToSave.max_time = 0;
                            }
                        }
                    }

                    if (resultDataToSave) {
                        await saveTestResult(target.id, resultDataToSave, database, monitor);
                        updateNodeCoolingStatus(server.sid, true, debugMode);
                        if (debugMode) console.log(`[TCPing调试] 从节点 ${server.name} 获取 ${target.name} 的监控数据成功`);
                        return;
                    }
                    console.error(`节点 ${server.name} 返回数据格式异常`);
                    if (debugMode) console.log(`[调试] 节点 ${server.name} 返回的数据:`, JSON.stringify(data, null, 2));
                } else {
                    console.error(`节点 ${server.name} 测试失败: ${data.message}`);
                }
            } catch (error) {
                const logger = require('../utils/logger');
                logger.logNodeError(server.name, error.message);
            }
            retryCount++;
            if (retryCount < MAX_RETRY_COUNT) {
                if (debugMode) console.log(`[TCPing调试] 第${retryCount}次重试...`);
                await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
            }
        }

        if (debugMode) console.log(`[TCPing调试] 在${MAX_RETRY_COUNT}次尝试后远程测试仍然失败，记录为超时`);
        const timeoutResult = { success_rate: 0, avg_time: 0, min_time: 0, max_time: 0, sid: server.sid };
        await saveTestResult(target.id, timeoutResult, database, monitor);
        updateNodeCoolingStatus(server.sid, false, debugMode);

    } catch (err) {
        console.error(`执行远程测试失败:`, err);
        if (debugMode) console.log(`[TCPing调试] 执行远程测试出错: ${err.message}, 记录为超时`);
        const errorResult = { success_rate: 0, avg_time: 0, min_time: 0, max_time: 0, sid: server.sid };
        await saveTestResult(target.id, errorResult, database, monitor);
        updateNodeCoolingStatus(server.sid, false, debugMode);
    }
}

/**
 * 执行监控目标的TCPing测试并保存结果
 * 此函数应由定时任务调用
 * @param {Object} app - Express app instance
 * @param {Object} database - Database instance
 * @param {Object} monitor - Monitor instance
 */
async function scheduleTcpingCheck(app, database, monitor) {
    try {
        const startTime = Date.now();
        const targets = await monitor.targets.getAll();
        const allServers = (await database.getServers()).filter(server => !server.disabled);
        const svr = app.locals.svr || app;
        const statsModule = svr.locals.stats;
        let statsData = {};
        if (statsModule) {
            if (typeof statsModule.getStatsFromMemory === 'function') {
                statsData = await Promise.resolve(statsModule.getStatsFromMemory(true));
            } else if (typeof statsModule.getStatsData === 'function') {
                statsData = await statsModule.getStatsData(true);
            } else if (typeof statsModule.getStats === 'function') {
                statsData = await statsModule.getStats(true);
            }
        }

        // 预防措施：检查statsData是否为Promise（错误情况）
        if (statsData instanceof Promise) {
            console.error('[监控错误] statsData是Promise对象，可能缺少await关键字，降级处理');
            statsData = {}; // 降级处理
        }

        // 改进的API配置检查函数：考虑api.key存在且不为空
        const hasValidApiConfig = (server) => {
            return server.data &&
                   server.data.api &&
                   server.data.api.key &&
                   server.data.api.key.trim() !== '';
        };

        const onlineServers = allServers.filter(server => {
            const isOnline = statsData[server.sid] && statsData[server.sid].stat !== false && !(statsData[server.sid].stat && statsData[server.sid].stat.offline);
            const cooling = isNodeCooling(server.sid);
            return isOnline && !cooling;
        });

        const setting = database.setting.all();
        const debugMode = setting.debug === true;

        if (debugMode) {
            // 检查statsData状态
            console.log(`[监控调试] statsData状态检查:`);
            console.log(`  - statsData类型: ${typeof statsData}`);
            console.log(`  - 是否为Promise: ${statsData instanceof Promise}`);
            console.log(`  - statsData记录数: ${Object.keys(statsData).length}`);
            console.log(`  - statsModule可用: ${!!statsModule}`);

            console.log(`[监控调试] 开始并行测试 ${targets.length} 个监控目标，可用节点: ${onlineServers.length}/${allServers.length}`);

            // 详细的节点过滤分析
            console.log(`[监控调试] 节点过滤详细分析:`);
            allServers.forEach(server => {
                const serverStats = statsData[server.sid];
                const hasStatsData = !!serverStats;
                const statValue = serverStats ? serverStats.stat : 'N/A';
                const isStatFalse = serverStats && serverStats.stat === false;
                const isOffline = serverStats && serverStats.stat && serverStats.stat.offline;
                const isOnline = hasStatsData && statValue !== false && !isOffline;
                const cooling = isNodeCooling(server.sid);
                const hasApiKey = !!(server.data && server.data.api && server.data.api.key);
                const apiMode = server.data && server.data.api ? server.data.api.mode : 'undefined';

                const finalAvailable = isOnline && !cooling;
                const status = finalAvailable ? '✅' : '❌';

                console.log(`  ${status} ${server.name} (${server.sid.substring(0, 8)}...)`);
                console.log(`    - statsData存在: ${hasStatsData}`);
                console.log(`    - stat值: ${statValue}`);
                console.log(`    - 在线状态: ${isOnline} (stat!==false: ${!isStatFalse}, !offline: ${!isOffline})`);
                console.log(`    - 冷却状态: ${cooling ? '冷却中' : '正常'}`);
                console.log(`    - API配置: key=${hasApiKey}, mode=${apiMode}`);
                console.log(`    - 最终可用: ${finalAvailable}`);
            });

            const coolingNodes = allServers.filter(server => isNodeCooling(server.sid));
            if (coolingNodes.length > 0) {
                console.log(`[TCPing调试] ${coolingNodes.length} 个节点处于冷却状态:`);
                coolingNodes.forEach(server => {
                    const status = coolingStatusData[server.sid]; // Use imported coolingStatusData
                    if (status) { // Check if status exists
                        console.log(`  - ${server.name} (${server.sid}): 失败次数=${status.failCount}, 冷却结束时间=${new Date(status.coolUntil).toLocaleString()}`);
                    }
                });
            }
        }

        const testPromises = targets.map(async (target) => {
            try {
                const testType = target.test_type || 'tcping';
                switch (target.mode) {
                    case 'local':
                        let localResult;
                        const targetInfo = `${target.name || target.host}:${target.port || 'N/A'}`;
                        const context = {
                            testType: testType === 'ping' ? 'Ping测试' : 'TCPing测试',
                            targetInfo: targetInfo,
                            nodeInfo: '本地节点'
                        };
                        if (testType === 'ping') {
                            const pingModule = require('./ping')(svr, database); // Assuming ping module is in the same directory or adjust path
                            localResult = await executeTestWithTimeout(() => pingModule.performPing(target.host, 8, 5), 20000, context);
                        } else {
                            localResult = await executeTestWithTimeout(() => performTcping(target.host, target.port, 8, 5, database), 20000, context);
                        }
                        await saveTestResult(target.id, localResult, database, monitor);
                        break;
                    case 'specific':
                        if (target.node_id) {
                            let nodeIds = [];
                            try {
                                if (typeof target.node_id === 'string' && target.node_id.startsWith('[')) nodeIds = JSON.parse(target.node_id);
                                else nodeIds = [target.node_id];
                            } catch (e) {
                                console.error(`解析节点ID失败: ${e.message}，使用单个节点ID`);
                                nodeIds = [target.node_id];
                            }
                            if (debugMode) console.log(`[TCPing调试] 监控目标 ${target.id} 有 ${nodeIds.length} 个节点进行测试`);

                            let validNodeFound = false;
                            const nodeTestPromises = nodeIds.map(async (nodeId) => {
                                const specificServer = onlineServers.find(s => s.sid === nodeId); // Use onlineServers
                                if (specificServer && hasValidApiConfig(specificServer)) {
                                    const remoteContext = {
                                        testType: testType === 'ping' ? 'Ping测试' : 'TCPing测试',
                                        targetInfo: `${target.name || target.host}:${target.port || 'N/A'}`,
                                        nodeInfo: `远程节点[${specificServer.name || specificServer.sid}]`
                                    };
                                    await executeTestWithTimeout(() => executeRemoteTest(specificServer, target, testType, database, monitor), 20000, remoteContext);
                                    return true;
                                } else {
                                    if (debugMode && !onlineServers.find(s => s.sid === nodeId)) console.error(`监控目标 ${target.id} 指定的节点 ${nodeId} 不在线或冷却中`);
                                    else if (debugMode) console.error(`监控目标 ${target.id} 指定的节点 ${nodeId} 无API配置或已禁用`);
                                    return false;
                                }
                            });
                            const nodeResults = await Promise.all(nodeTestPromises);
                            validNodeFound = nodeResults.some(r => r === true);

                            if (!validNodeFound) {
                                console.error(`监控目标 ${target.id} 所有指定节点都不可用，记录为不可用状态`);
                                const unavailableResult = { success_rate: 0, avg_time: 0, min_time: 0, max_time: 0, sid: 'unavailable' };
                                await saveTestResult(target.id, unavailableResult, database, monitor);
                            }
                        } else {
                            console.error(`监控目标 ${target.id} 模式为specific但未指定节点ID`);
                            const configErrorResult = { success_rate: 0, avg_time: 0, min_time: 0, max_time: 0, sid: 'config_error' };
                            await saveTestResult(target.id, configErrorResult, database, monitor);
                        }
                        break;
                    case 'auto':
                    default:
                        const region = monitor.regions.get(target.region_id);
                        if (!region) {
                            console.error(`监控目标 ${target.id} (${target.name}) 无法找到所属地区`);
                            const regionErrorResult = { success_rate: 0, avg_time: 0, min_time: 0, max_time: 0, sid: 'region_error' };
                            await saveTestResult(target.id, regionErrorResult, database, monitor);
                            return;
                        }
                        const regionServers = onlineServers.filter(s => s.data?.metadata?.region === region.name && hasValidApiConfig(s));
                        const availableServers = regionServers.length > 0 ? regionServers : onlineServers.filter(s => hasValidApiConfig(s));

                        if (debugMode) {
                            console.log(`[监控调试] 目标 ${target.id} (${target.name}) 节点选择详情:`);
                            console.log(`  - 在线节点数: ${onlineServers.length}`);
                            console.log(`  - 地区节点数: ${regionServers.length} (地区: ${region.name})`);
                            console.log(`  - 最终可用节点数: ${availableServers.length}`);
                            if (availableServers.length > 0) {
                                console.log(`  - 可用节点列表: ${availableServers.map(s => s.name).join(', ')}`);
                            }
                        }

                        if (availableServers.length === 0) {
                            console.error(`监控目标 ${target.id} (${target.name}) 没有可用的监控节点`);
                            if (debugMode) {
                                console.error(`[监控调试] 无可用节点详细分析:`);
                                console.error(`  - 总服务器数: ${allServers.length}`);
                                console.error(`  - 在线服务器数: ${onlineServers.length}`);
                                console.error(`  - 有API配置的在线服务器数: ${onlineServers.filter(s => hasValidApiConfig(s)).length}`);
                                console.error(`  - 目标地区: ${region.name}`);
                                console.error(`  - 地区匹配的服务器数: ${regionServers.length}`);
                            }
                            const noServerResult = { success_rate: 0, avg_time: 0, min_time: 0, max_time: 0, sid: 'no_servers' };
                            await saveTestResult(target.id, noServerResult, database, monitor);
                        } else {
                            const randomServer = availableServers[Math.floor(Math.random() * availableServers.length)];
                            const autoContext = {
                                testType: testType === 'ping' ? 'Ping测试' : 'TCPing测试',
                                targetInfo: `${target.name || target.host}:${target.port || 'N/A'}`,
                                nodeInfo: `自动模式节点[${randomServer.name || randomServer.sid}]`
                            };
                            await executeTestWithTimeout(() => executeRemoteTest(randomServer, target, testType, database, monitor), 20000, autoContext);
                        }
                        break;
                }
            } catch (err) {
                console.error(`监控目标 ${target.id} (${target.name}) ${testType}测试失败:`, err);
            }
        });
        await Promise.all(testPromises);
        const endTime = Date.now();
        if (debugMode) console.log(`[TCPing调试] 完成所有 ${targets.length} 个监控目标的测试，总耗时: ${endTime - startTime}ms`);
    } catch (err) {
        console.error('调度TCPing检查任务失败:', err);
    }
}

/**
 * 时间戳对齐工具函数
 */
function alignTimestamp(timestamp, interval) {
    return Math.floor(timestamp / interval) * interval;
}

function alignTo5MinBoundary(timestamp) {
    return alignTimestamp(timestamp, 300); // 300秒 = 5分钟
}

function alignToHourBoundary(timestamp) {
    return alignTimestamp(timestamp, 3600); // 3600秒 = 1小时
}

function alignToDayBoundary(timestamp) {
    return alignTimestamp(timestamp, 86400); // 86400秒 = 1天
}

function startScheduler(app, database, monitor) {
    // 设置定时任务，每分钟执行一次
    setInterval(() => scheduleTcpingCheck(app, database, monitor), 60000);

    // ========== 修复：多级聚合与清理入口 ==========
    // 正确的聚合任务（每分钟检查一次，内部判断是否到聚合点）
    setInterval(async () => {
        const now = new Date();
        const minute = now.getMinutes();
        const hour = now.getHours();
        const day = now.getDate();
        const month = now.getMonth() + 1;

        // 🔧 新增：5分钟聚合（每5分钟执行一次，从分钟级数据聚合到5分钟级）
        if (minute % 5 === 0) {
            try {
                const setting = database.setting.all();
                const debugMode = setting.debug === true;
                
                const targets = await monitor.targets.getAll();
                for (const target of targets) {
                    try {
                        // 按节点分组进行5分钟聚合
                        const monitoringNodes = await monitor.tcping_m.getMonitoringNodes(target.id, 300); // 5分钟 = 300秒
                    
                        if (monitoringNodes.length === 0) {
                            if (debugMode) console.log(`[TCPing 5分钟聚合] 目标 ${target.name} 没有找到监控节点`);
                            continue;
                        }

                        if (debugMode) console.log(`[TCPing 5分钟聚合] 目标 ${target.name} 有 ${monitoringNodes.length} 个监控节点: ${monitoringNodes.join(', ')}`);

                        // 对每个监控节点分别进行5分钟聚合
                        for (const nodeId of monitoringNodes) {
                            try {
                                // 获取该节点对该目标的过去5分钟分钟级数据
                                const fiveMinData = await monitor.tcping_m.selectByTimeRangeAndNode(target.id, nodeId, 300);
                        
                                if (fiveMinData.length === 0) {
                                    if (debugMode) console.log(`[TCPing 5分钟聚合] 节点 ${nodeId} 对目标 ${target.name} 没有5分钟内的分钟级数据`);
                                    continue;
                                }
                                
                                // 过滤有效数据
                                const valid = fiveMinData.filter(x => {
                                    if (typeof x.success_rate !== 'number') return false;
                                    return true; // 5分钟聚合保留更多数据，提供更细致的监控
                                });
                                
                                if (valid.length === 0) {
                                    if (debugMode) console.log(`[TCPing 5分钟聚合] 节点 ${nodeId} 对目标 ${target.name} 没有有效数据`);
                                    continue;
                                }
                                
                                // 计算该节点的5分钟级聚合
                                const agg = {
                                    success_rate: valid.reduce((a, b) => a + b.success_rate, 0) / valid.length,
                                    avg_time: Math.round(valid.reduce((a, b) => a + b.avg_time, 0) / valid.length),
                                    min_time: Math.min(...valid.map(x => x.min_time || Infinity).filter(x => x !== Infinity)),
                                    max_time: Math.max(...valid.map(x => x.max_time || 0)),
                                    sid: nodeId // 使用实际的节点ID
                                };
                                
                                // 🔧 使用对齐到5分钟边界的时间戳
                                const alignedTimestamp = alignTo5MinBoundary(Math.floor(Date.now() / 1000));
                                await monitor.tcping_5m.shift(target.id, agg, alignedTimestamp);
                                
                                if (debugMode) {
                                    console.log(`[TCPing 5分钟聚合] 完成节点 ${nodeId} 对目标 ${target.name} 的5分钟聚合: 有效数据=${valid.length}/${fiveMinData.length}, 平均成功率=${(agg.success_rate * 100).toFixed(1)}%, 平均延迟=${agg.avg_time}ms`);
                                }
                            } catch (nodeError) {
                                console.error(`[TCPing聚合] 节点 ${nodeId} 5分钟聚合失败:`, nodeError);
                            }
                        }
                    } catch (targetError) {
                        console.error(`[TCPing聚合] 目标 ${target.name} 5分钟聚合失败:`, targetError);
                    }
                }
            } catch (e) {
                console.error('[TCPing聚合] 5分钟聚合异常:', e);
            }
        }

        // 🔧 修复：小时聚合（每小时执行一次，从分钟级数据聚合到小时级）
        if (minute === 0) {
            try {
                const targets = await monitor.targets.getAll();
                for (const target of targets) {
                    try {
                        // 🚨 重要修复：按节点分组进行聚合，避免混合所有节点的数据
                        // 首先获取监控该目标的所有节点
                        const monitoringNodes = await monitor.tcping_m.getMonitoringNodes(target.id, 3600);
                    
                        if (monitoringNodes.length === 0) {
                            console.log(`[TCPing聚合] 目标 ${target.name} 没有找到监控节点`);
                            continue;
                        }

                        console.log(`[TCPing聚合] 目标 ${target.name} 有 ${monitoringNodes.length} 个监控节点: ${monitoringNodes.join(', ')}`);

                        // 对每个监控节点分别进行聚合
                        for (const nodeId of monitoringNodes) {
                            try {
                                // 获取该节点对该目标的过去1小时分钟级数据
                                const hourData = await monitor.tcping_m.selectByTimeRangeAndNode(target.id, nodeId, 3600);
                        
                                if (hourData.length === 0) {
                                    console.log(`[TCPing聚合] 节点 ${nodeId} 对目标 ${target.name} 没有分钟级数据`);
                                    continue;
                                }
                                
                                // 🔧 优化：过滤异常数据
                                const valid = hourData.filter(x => {
                                    // 基本数据类型检查
                                    if (typeof x.success_rate !== 'number') return false;
                                    
                                    // 过滤异常情况：
                                    // 1. 延迟为0且成功率低于50%的数据（通常是超时或网络异常）
                                    // 2. 延迟为0且成功率为0的数据（完全无响应）
                                    if (x.avg_time === 0 && x.success_rate < 0.5) {
                                        return false;
                                    }
                                    
                                    // 保留有效数据：
                                    // 1. 有正常延迟的数据
                                    // 2. 延迟为0但成功率较高的数据（可能是本地回环或极快响应）
                                    return true;
                                });
                                
                                if (valid.length === 0) {
                                    // 如果没有有效数据，检查是否所有数据都是异常的
                                    const totalData = hourData.filter(x => typeof x.success_rate === 'number');
                                    if (totalData.length > 0) {
                                        // 有数据但都是异常的，记录一个表示网络问题的状态
                                        const agg = {
                                            success_rate: 0,
                                            avg_time: 0,
                                            min_time: 0,
                                            max_time: 0,
                                            sid: nodeId // 🚨 关键修复：使用实际的节点ID，而不是混合ID
                                        };
                                        await monitor.tcping_h.shift(target.id, agg);
                                        
                                        const setting = database.setting.all();
                                        const debugMode = setting.debug === true;
                                        if (debugMode) {
                                            console.log(`[TCPing聚合] 节点 ${nodeId} 对目标 ${target.name}: 所有${totalData.length}个样本均为异常数据，记录为网络问题`);
                                        }
                                    }
                                    continue;
                                }
                                
                                // 计算该节点的小时级聚合（基于有效数据）
                                const agg = {
                                    success_rate: valid.reduce((a, b) => a + b.success_rate, 0) / valid.length,
                                    avg_time: Math.round(valid.reduce((a, b) => a + b.avg_time, 0) / valid.length),
                                    min_time: Math.min(...valid.map(x => x.min_time || Infinity).filter(x => x !== Infinity)),
                                    max_time: Math.max(...valid.map(x => x.max_time || 0)),
                                    sid: nodeId // 🚨 关键修复：使用实际的节点ID
                                };
                                
                                // 🔧 使用对齐到小时边界的时间戳
                                const alignedTimestamp = alignToHourBoundary(Math.floor(Date.now() / 1000));
                                await monitor.tcping_h.shift(target.id, agg, alignedTimestamp);
                                
                                const setting = database.setting.all();
                                const debugMode = setting.debug === true;
                                if (debugMode) {
                                    const filteredCount = hourData.length - valid.length;
                                    console.log(`[TCPing聚合] 完成节点 ${nodeId} 对目标 ${target.name} 的小时聚合: 有效数据=${valid.length}/${hourData.length} (过滤${filteredCount}个异常), 平均成功率=${agg.success_rate.toFixed(2)}, 平均延迟=${agg.avg_time}ms`);
                                }
                            } catch (nodeError) {
                                console.error(`[TCPing聚合] 节点 ${nodeId} 小时聚合失败:`, nodeError);
                            }
                        }
                    } catch (targetError) {
                        console.error(`[TCPing聚合] 目标 ${target.name} 小时聚合失败:`, targetError);
                    }
                }
            } catch (e) {
                console.error('[TCPing聚合] 小时聚合异常:', e);
            }
            
            // 🔧 修复：天级聚合（每天00:00执行一次，从小时级数据聚合到天级）
            if (hour === 0) {
                try {
                    const targets = await monitor.targets.getAll();
                    for (const target of targets) {
                        try {
                            // 🚨 重要修复：按节点分组进行天级聚合，避免混合不同节点的小时级数据
                            // 首先获取监控该目标的所有节点（从小时级数据中获取）
                            const monitoringNodes = await monitor.tcping_h.getMonitoringNodes(target.id, 24 * 3600);
                        
                            if (monitoringNodes.length === 0) {
                                console.log(`[TCPing聚合] 目标 ${target.name} 没有找到小时级监控节点`);
                                continue;
                            }

                            console.log(`[TCPing聚合] 目标 ${target.name} 的天级聚合有 ${monitoringNodes.length} 个监控节点: ${monitoringNodes.join(', ')}`);

                            // 对每个监控节点分别进行天级聚合
                            for (const nodeId of monitoringNodes) {
                                try {
                                    // 获取该节点对该目标的最近24条小时级数据
                                    const dayData = await monitor.tcping_h.selectByNode(target.id, nodeId, 24);
                            
                                    if (dayData.length === 0) {
                                        console.log(`[TCPing聚合] 节点 ${nodeId} 对目标 ${target.name} 没有小时级数据`);
                                        continue;
                                    }
                                    
                                    // 计算该节点的天级聚合
                                    const valid = dayData.filter(x => typeof x.success_rate === 'number');
                                    if (valid.length === 0) {
                                        console.log(`[TCPing聚合] 节点 ${nodeId} 对目标 ${target.name} 没有有效的小时级数据`);
                                        continue;
                                    }
                                    
                                    const agg = {
                                        success_rate: valid.reduce((a, b) => a + b.success_rate, 0) / valid.length,
                                        avg_time: Math.round(valid.reduce((a, b) => a + b.avg_time, 0) / valid.length),
                                        min_time: Math.min(...valid.map(x => x.min_time)),
                                        max_time: Math.max(...valid.map(x => x.max_time)),
                                        sid: nodeId // 🚨 关键修复：使用实际的节点ID
                                    };
                                    
                                    // 🔧 使用对齐到天边界的时间戳
                                    const alignedTimestamp = alignToDayBoundary(Math.floor(Date.now() / 1000));
                                    await monitor.tcping_d.shift(target.id, agg, alignedTimestamp);
                                    
                                    const setting = database.setting.all();
                                    const debugMode = setting.debug === true;
                                    if (debugMode) {
                                        console.log(`[TCPing聚合] 完成节点 ${nodeId} 对目标 ${target.name} 的天级聚合: 数据点=${valid.length}, 平均成功率=${agg.success_rate.toFixed(2)}, 平均延迟=${agg.avg_time}ms`);
                                    }
                                } catch (nodeError) {
                                    console.error(`[TCPing聚合] 节点 ${nodeId} 天级聚合失败:`, nodeError);
                                }
                            }
                        
                        // 月度聚合（每月1日00:00执行一次，保存10年）
                        if (day === 1) {
                            try {
                                // 🚨 重要修复：按节点分组进行月度聚合
                                const monthlyNodes = await monitor.tcping_d.getMonitoringNodes(target.id, 30 * 24 * 3600);
                                
                                for (const nodeId of monthlyNodes) {
                                    try {
                                        // 获取该节点的最近30天的天级数据
                                        const monthData = await monitor.tcping_d.selectByNode(target.id, nodeId, 30);
                                        if (monthData.length === 0) continue;
                                        
                                        const validMonth = monthData.filter(x => typeof x.success_rate === 'number');
                                        if (validMonth.length === 0) continue;
                                        
                                        const monthAgg = {
                                            success_rate: validMonth.reduce((a, b) => a + b.success_rate, 0) / validMonth.length,
                                            avg_time: Math.round(validMonth.reduce((a, b) => a + b.avg_time, 0) / validMonth.length),
                                            min_time: Math.min(...validMonth.map(x => x.min_time)),
                                            max_time: Math.max(...validMonth.map(x => x.max_time)),
                                            sid: nodeId // 🚨 关键修复：使用实际的节点ID
                                        };
                                        
                                        await monitor.tcping_month.shift(target.id, monthAgg);
                                        
                                        const setting = database.setting.all();
                                        const debugMode = setting.debug === true;
                                        if (debugMode) {
                                            console.log(`[TCPing聚合] 完成节点 ${nodeId} 对目标 ${target.name} 的月度聚合: 数据点=${validMonth.length}, 平均成功率=${monthAgg.success_rate.toFixed(2)}, 平均延迟=${monthAgg.avg_time}ms`);
                                        }
                                    } catch (nodeError) {
                                        console.error(`[TCPing聚合] 节点 ${nodeId} 月度聚合失败:`, nodeError);
                                    }
                                }
                            } catch (monthlyError) {
                                console.error(`[TCPing聚合] 目标 ${target.name} 月度聚合失败:`, monthlyError);
                            }
                        }
                    } catch (targetError) {
                        console.error(`[TCPing聚合] 目标 ${target.name} 聚合失败:`, targetError);
                    }
                }
                } catch (e) {
                    console.error('[TCPing聚合] 天级聚合异常:', e);
                }
            }
        }
    }, 60000);

    // 定时清理归档数据（每10分钟）
    setInterval(async () => {
        try {
            const setting = database.setting.all();
            const debugMode = setting.debug === true;
            const now = Math.floor(Date.now() / 1000);
            const archiveHours = Number.parseInt(setting['data_retention_archive_hours']) || 3;
            const cutoffArchive = now - archiveHours * 3600;

            if (debugMode) logger.debug(`[TCPing调试] 开始清理归档数据（<=${archiveHours}小时）`);
            const archiveChanges = await monitor.tcping_archive.cleanup({ before_time: cutoffArchive });
            if (debugMode) {
                logger.debug(`[TCPing调试] 归档清理完成，删除 ${archiveChanges} 条`);
            } else if (archiveChanges > 0) {
                logger.info(`[数据清理] tcping_archive 删除 ${archiveChanges} 条 (<=${archiveHours}小时)`);
            }
        } catch (err) {
            logger.error('定时清理归档数据失败:', err);
        }
    }, 600000);

    // 定时清理分钟/5分钟/小时表（每60分钟）
    setInterval(async () => {
        try {
            const setting = database.setting.all();
            const debugMode = setting.debug === true;
            const now = Math.floor(Date.now() / 1000);
            const minuteDays = Number.parseInt(setting['data_retention_minute_days']) || 14;
            const hourDays = Number.parseInt(setting['data_retention_hour_days']) || 90;
            const cutoffMinute = now - minuteDays * 86400;
            const cutoffHour = now - hourDays * 86400;

            if (debugMode) logger.debug(`[TCPing调试] 开始清理分钟/小时数据（minute<=${minuteDays}天, hour<=${hourDays}天）`);
            const minuteChangesM = await monitor.tcping_m.cleanup({ before_time: cutoffMinute });
            const minuteChanges5M = await monitor.tcping_5m.cleanup({ before_time: cutoffMinute });
            const hourChanges = await monitor.tcping_h.cleanup({ before_time: cutoffHour });
            if (debugMode) {
                logger.debug(`[TCPing调试] 分钟/小时清理完成：tcping_m=${minuteChangesM}, tcping_5m=${minuteChanges5M}, tcping_h=${hourChanges}`);
            } else {
                const total = (minuteChangesM || 0) + (minuteChanges5M || 0) + (hourChanges || 0);
                if (total > 0) {
                    logger.info(`[数据清理] tcping_m=${minuteChangesM}, tcping_5m=${minuteChanges5M}, tcping_h=${hourChanges} (minute<=${minuteDays}天, hour<=${hourDays}天)`);
                }
            }
        } catch (err) {
            logger.error('定时清理分钟/小时数据失败:', err);
        }
    }, 3600000);
}

module.exports = {
    startScheduler,
    scheduleTcpingCheck, // Export for direct call if needed elsewhere
    // executeRemoteTest and saveTestResult are internal to this module now
};
