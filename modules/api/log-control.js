/**
 * 日志控制API模块
 * 为面板提供统一的日志管理接口
 */

module.exports = async (svr) => {
    const { db } = svr.locals;
    const { logController } = require('../utils/log-controller');

    // 获取日志控制状态
    svr.get("/admin/api/log-control/status", (req, res) => {
        if (!req.admin) {
            return res.status(403).json({ success: false, message: '需要管理员权限' });
        }

        try {
            const stats = logController.getStats();
            const settings = logController.getSettings(db);
            
            res.json({
                success: true,
                data: {
                    ...stats,
                    settings
                }
            });
        } catch (error) {
            console.error('[日志控制API] 获取状态失败:', error);
            res.status(500).json({ success: false, message: '获取状态失败' });
        }
    });

    // 更新日志控制设置
    svr.post("/admin/api/log-control/settings", (req, res) => {
        if (!req.admin) {
            return res.status(403).json({ success: false, message: '需要管理员权限' });
        }

        try {
            const newSettings = req.body;
            const success = logController.updateSettings(db, newSettings);
            
            if (success) {
                res.json({ 
                    success: true, 
                    message: '设置已更新',
                    data: logController.getSettings(db)
                });
            } else {
                res.json({ success: false, message: '设置更新失败' });
            }
        } catch (error) {
            console.error('[日志控制API] 更新设置失败:', error);
            res.status(500).json({ success: false, message: '更新设置失败' });
        }
    });

    // 清理高频日志
    svr.post("/admin/api/log-control/clean-throttle", (req, res) => {
        if (!req.admin) {
            return res.status(403).json({ success: false, message: '需要管理员权限' });
        }

        try {
            const success = logController.cleanHighFrequencyLogs();
            
            if (success) {
                res.json({ success: true, message: '高频日志限制已重置' });
            } else {
                res.json({ success: false, message: '清理失败' });
            }
        } catch (error) {
            console.error('[日志控制API] 清理失败:', error);
            res.status(500).json({ success: false, message: '清理失败' });
        }
    });

    // 临时启用调试模式
    svr.post("/admin/api/log-control/debug-temporary", (req, res) => {
        if (!req.admin) {
            return res.status(403).json({ success: false, message: '需要管理员权限' });
        }

        try {
            const { duration = 300000 } = req.body; // 默认5分钟
            logController.enableDebugTemporarily(duration);
            
            res.json({ 
                success: true, 
                message: `临时调试模式已启用 ${duration/1000} 秒` 
            });
        } catch (error) {
            console.error('[日志控制API] 临时调试模式失败:', error);
            res.status(500).json({ success: false, message: '启用失败' });
        }
    });

    // 获取日志消息统计
    svr.get("/admin/api/log-control/message-stats", (req, res) => {
        if (!req.admin) {
            return res.status(403).json({ success: false, message: '需要管理员权限' });
        }

        try {
            const stats = logController.getStats();
            const messageStats = stats?.messageCount || {};
            
            // 按模块分组统计
            const groupedStats = {};
            Object.entries(messageStats).forEach(([key, count]) => {
                const [level, module] = key.split(':');
                if (!groupedStats[module]) {
                    groupedStats[module] = { total: 0, byLevel: {} };
                }
                groupedStats[module].total += count;
                groupedStats[module].byLevel[level] = count;
            });

            res.json({
                success: true,
                data: {
                    raw: messageStats,
                    grouped: groupedStats,
                    summary: {
                        totalModules: Object.keys(groupedStats).length,
                        totalMessages: Object.values(messageStats).reduce((sum, count) => sum + count, 0),
                        throttleActive: stats?.throttleActive || 0
                    }
                }
            });
        } catch (error) {
            console.error('[日志控制API] 获取消息统计失败:', error);
            res.status(500).json({ success: false, message: '获取统计失败' });
        }
    });

    console.info('[日志控制API] 模块已加载');
};