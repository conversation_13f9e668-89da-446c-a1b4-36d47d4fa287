'use strict';

const express = require('express');
const router = express.Router();

module.exports = (database) => {

    /**
     * 获取所有服务器节点
     * GET /api/servers
     */
    router.get('/api/servers', async (req, res) => {
        try {
            // 获取所有服务器
            const servers = await database.getServers();

            // 筛选有效的服务器（未禁用的并且配置了API）
            const validServers = servers
                .filter(server => !server.disabled)
                .map(server => {
                    const hasApi = !!(server.data && server.data.api && (server.data.api.key || server.data.api.port));

                    // 只返回需要的字段，避免读取或暴露任何 IP 信息
                    return {
                        id: server.sid, // 确保使用服务器的唯一标识符sid
                        sid: server.sid, // 额外提供sid字段以便兼容
                        name: server.name,
                        disabled: !!server.disabled,
                        group_id: server.group_id || 'default', // 添加分组ID
                        data: {
                            api: server.data && server.data.api ? {
                                mode: !!server.data.api.mode,
                                hasKey: !!server.data.api.key
                                // 不返回 key、port、host 等敏感或可侧推信息
                            } : { mode: false, hasKey: false },
                            metadata: server.data && server.data.metadata ? {
                                region: server.data.metadata.region || ''
                            } : null
                        }
                    };
                });

            res.json({ success: true, data: validServers });
        } catch (err) {
            console.error('获取服务器节点失败:', err);
            res.status(500).json({ success: false, message: err.message });
        }
    });

    /**
     * 获取所有分组
     * GET /api/groups
     */
    router.get('/api/groups', async (req, res) => {
        try {
            const groups = await database.groups.all();
            res.json({ success: true, data: groups });
        } catch (err) {
            console.error('获取分组列表失败:', err);
            res.status(500).json({ success: false, message: err.message });
        }
    });

    /**
     * 获取所有监控节点
     * GET /api/monitor/nodes
     */
    router.get('/api/monitor/nodes', async (req, res) => {
        try {
            // 获取所有服务器
            const servers = await database.getServers();

            // 筛选有效的服务器（未禁用的并且配置了API）
            const validServers = servers
                .filter(server => !server.disabled && server.data && server.data.api && server.data.api.key)
                .map(server => ({
                    id: server.sid,
                    name: server.name,
                    region: server.data?.metadata?.region || '未知地区',
                    group_id: server.group_id || 'default'
                }));

            res.json({ success: true, data: validServers });
        } catch (err) {
            console.error('获取监控节点列表失败:', err);
            res.status(500).json({ success: false, message: err.message });
        }
    });

    return router;
};
