'use strict';

const express = require('express');
const router = express.Router();
const { performTcping } = require('../tcping_core'); // Adjusted path

module.exports = (database, validateApiKey) => { // Pass validateApiKey as a parameter

    // 获取 featureChecker 实例
    function getFeatureChecker() {
        try {
            // 尝试从全局应用实例获取
            if (global.app && global.app.locals && global.app.locals['license-enhanced']) {
                return global.app.locals['license-enhanced'].featureChecker;
            }

            // 后备方案：直接require模块
            const licenseEnhanced = require('../../license-enhanced');
            return licenseEnhanced.featureChecker;
        } catch (error) {
            console.error('[TCPing API] 无法获取FeatureChecker:', error);
            return null;
        }
    }

    /**
     * 执行TCPing测试
     * GET /tcping?key=<your_api_key>&host=<target_host>&port=<port>&count=<count>&timeout=<timeout>
     */
    router.get('/tcping', async (req, res) => {
        if (!validateApiKey(req, res)) return;

        const { host, port, count, timeout } = req.query;

        // 参数验证
        if (!host || !port) {
            return res.status(400).json({
                success: false,
                message: '缺少必需参数: host和port是必需的'
            });
        }

        try {
            const result = await performTcping(
                host,
                parseInt(port, 10),
                count ? parseInt(count, 10) : 8,
                timeout ? parseFloat(timeout) : 5,
                database // Pass database for settings
            );

            res.json({ success: true, data: result });
        } catch (err) {
            console.error('TCPing测试失败:', err);
            res.status(500).json({
                success: false,
                message: '执行TCPing测试失败: ' + err.message
            });
        }
    });

    /**
     * 执行TCPing测试并保存结果 (服务器调度接口)
     * GET /exec/tcping?key=<your_api_key>&host=<target_host>&port=<port>&count=<count>&timeout=<timeout>
     */
    router.get('/exec/tcping', async (req, res) => {
        if (!validateApiKey(req, res)) return;

        const { host, port, count, timeout } = req.query;

        // 参数验证
        if (!host || !port) {
            return res.status(400).json({
                success: false,
                message: '缺少必需参数: host和port是必需的'
            });
        }

        try {
            const result = await performTcping(
                host,
                parseInt(port, 10),
                count ? parseInt(count, 10) : 8,
                timeout ? parseFloat(timeout) : 5,
                database // Pass database for settings
            );

            res.json({ success: true, data: result });
        } catch (err) {
            console.error('TCPing测试失败:', err);
            res.status(500).json({
                success: false,
                message: '执行TCPing测试失败: ' + err.message
            });
        }
    });

    return router;
};
