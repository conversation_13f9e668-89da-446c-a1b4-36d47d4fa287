'use strict';

const express = require('express');
const router = express.Router();

module.exports = (svr, monitor) => {
    const database = svr.locals.db;

    // 获取 featureChecker 实例
    function getFeatureChecker() {
        try {
            // 尝试从服务器实例获取
            if (svr && svr.locals && svr.locals['license-enhanced']) {
                return svr.locals['license-enhanced'].featureChecker;
            }

            // 后备方案：从全局应用实例获取
            if (global.app && global.app.locals && global.app.locals['license-enhanced']) {
                return global.app.locals['license-enhanced'].featureChecker;
            }

            console.error('[NetworkQuality API] 无法获取FeatureChecker实例');
            return null;
        } catch (error) {
            console.error('[NetworkQuality API] 无法获取FeatureChecker:', error);
            return null;
        }
    }

    // 获取 ipLocationService 实例
    function getIPLocationService() {
        try {
            if (svr && svr.locals) {
                if (svr.locals.ipLocationService) return svr.locals.ipLocationService;
                if (svr.locals.stats && svr.locals.stats.ipLocationService) return svr.locals.stats.ipLocationService;
            }
            if (global.app && global.app.locals) {
                if (global.app.locals.ipLocationService) return global.app.locals.ipLocationService;
                if (global.app.locals.stats && global.app.locals.stats.ipLocationService) return global.app.locals.stats.ipLocationService;
            }
            console.warn('[NetworkQuality API] 未找到 ipLocationService 实例');
            return null;
        } catch (e) {
            console.error('[NetworkQuality API] 获取 ipLocationService 失败:', e);
            return null;
        }
    }

    // 获取 unifiedConfigService 实例
    function getUnifiedConfigService() {
        try {
            // 尝试从服务器实例获取
            if (svr && svr.locals && svr.locals['license-enhanced']) {
                return svr.locals['license-enhanced'].unifiedConfigService;
            }

            // 后备方案：从全局应用实例获取
            if (global.app && global.app.locals && global.app.locals['license-enhanced']) {
                return global.app.locals['license-enhanced'].unifiedConfigService;
            }

            console.error('[NetworkQuality API] 无法获取UnifiedConfigService实例');
            return null;
        } catch (error) {
            console.error('[NetworkQuality API] 无法获取UnifiedConfigService:', error);
            return null;
        }
    }

    // 时间范围映射（转换为秒）
    function getTimeRangeInSeconds(timeRange) {
        switch (timeRange) {
            case '1h':
                return 3600;
            case '6h':
                return 6 * 3600;
            case '24h':
                return 24 * 3600;
            case '7d':
                return 7 * 24 * 3600;
            case '30d':
                return 30 * 24 * 3600;
            default:
                return 24 * 3600; // 默认24小时
        }
    }

    // 格式化时间范围为可读格式
    function formatTimeRange(seconds) {
        if (seconds < 3600) {
            return `${Math.round(seconds / 60)}分钟`;
        } else if (seconds < 24 * 3600) {
            return `${Math.round(seconds / 3600)}小时`;
        } else {
            return `${Math.round(seconds / (24 * 3600))}天`;
        }
    }

    /**
     * 检查时间范围限制的中间件
     * @param {string} featureName - 功能名称
     * @returns {function} Express中间件函数
     */
    function checkTimeRangeLimit(featureName = 'NETWORK_QUALITY') {
        return async (req, res, next) => {
            try {
                // 从请求参数中获取时间范围
                const timeRange = req.query.timeRange || req.body.timeRange || '24h';
                const requestedTimeInSeconds = getTimeRangeInSeconds(timeRange);

                // 获取 unifiedConfigService 实例
                const unifiedConfigService = getUnifiedConfigService();
                if (!unifiedConfigService) {
                    console.error('[NetworkQuality API] UnifiedConfigService不可用，跳过时间范围检查');
                    return next(); // 服务不可用时不阻止访问
                }

                // 获取当前用户的套餐信息
                const featureChecker = getFeatureChecker();
                if (!featureChecker) {
                    console.error('[NetworkQuality API] FeatureChecker不可用，跳过时间范围检查');
                    return next(); // 服务不可用时不阻止访问
                }

                // 获取当前许可证信息以确定套餐名称
                const licenseInfo = featureChecker.getCurrentLicenseInfo();
                const planName = licenseInfo.planName || licenseInfo.planDisplayName || licenseInfo.planType;

                if (!planName) {
                    console.warn('[NetworkQuality API] 无法确定套餐名称，跳过时间范围检查');
                    return next(); // 无法确定套餐时不阻止访问
                }

                // 获取功能的时间范围限制
                const timeLimit = await unifiedConfigService.getFeatureTimeLimit(planName, featureName);
                
                // 如果没有配置限制，允许访问
                if (!timeLimit) {
                    return next();
                }

                // 检查请求的时间范围是否超出限制
                if (requestedTimeInSeconds > timeLimit) {
                    return res.status(403).json({
                        success: false,
                        error: 'TIME_RANGE_EXCEEDED',
                        errorType: 'PERMISSION_DENIED',
                        message: `当前套餐最多支持 ${formatTimeRange(timeLimit)} 的历史数据`,
                        data: {
                            requestedRange: timeRange,
                            requestedSeconds: requestedTimeInSeconds,
                            maxAllowedSeconds: timeLimit,
                            maxAllowedRange: formatTimeRange(timeLimit),
                            currentPlan: planName,
                            upgradeUrl: '/admin/license-management'
                        }
                    });
                }

                // 检查通过，继续处理请求
                next();
            } catch (error) {
                console.error('[NetworkQuality API] 时间范围检查失败:', error);
                // 错误时不阻止访问，但记录日志
                next();
            }
        };
    }

    /**
     * 获取所有监控目标的网络质量概览数据
     * GET /api/network-quality/overview?timeRange=<1h|6h|24h|7d|30d>
     */
    router.get('/api/network-quality/overview', async (req, res) => {
        try {
            const { timeRange = '24h' } = req.query;
            
            // 验证monitor对象
            if (!monitor || !monitor.targets || typeof monitor.targets.getAll !== 'function') {
                console.error('[Network Quality API] monitor对象不可用', {
                    monitor: !!monitor,
                    targets: !!monitor?.targets,
                    getAll: typeof monitor?.targets?.getAll
                });
                return res.status(500).json({
                    success: false,
                    message: 'Monitor服务不可用',
                    error: 'MONITOR_UNAVAILABLE'
                });
            }
            
            // 获取所有监控目标
            const targets = await monitor.targets.getAll();
            
            // 计算时间范围（秒）
            let durationSeconds;
            switch (timeRange) {
                case '1h':
                    durationSeconds = 60 * 60;
                    break;
                case '6h':
                    durationSeconds = 6 * 60 * 60;
                    break;
                case '24h':
                    durationSeconds = 24 * 60 * 60;
                    break;
                case '7d':
                    durationSeconds = 7 * 24 * 60 * 60;
                    break;
                case '30d':
                    durationSeconds = 30 * 24 * 60 * 60;
                    break;
                default:
                    durationSeconds = 24 * 60 * 60;
            }

            // 获取聚合网络质量数据
            const networkQualityData = {
                timeRange: timeRange,
                totalTargets: targets.length,
                summary: {
                    avgLatency: 0,
                    avgPacketLoss: 0,
                    avgAvailability: 0
                },
                targets: []
            };

            // 为所有监控目标获取网络质量数据
            for (const target of targets) {
                try {
                    // 获取监控目标的TCPing数据
                    const tcpingData = await getTargetTcpingData(target.id, durationSeconds, monitor);
                    
                    const targetQuality = {
                        id: target.id,
                        name: target.name,
                        region: target.region_name,
                        test_type: target.test_type,
                        metrics: calculateNetworkMetrics(tcpingData),
                        chartData: formatChartData(tcpingData, timeRange)
                    };
                    
                    networkQualityData.targets.push(targetQuality);
                } catch (error) {
                    console.error(`获取监控目标 ${target.id} 网络质量数据失败:`, error);
                    // 添加默认数据
                    networkQualityData.targets.push({
                        id: target.id,
                        name: target.name,
                        region: target.region_name,
                        test_type: target.test_type,
                        metrics: { avgLatency: 0, packetLoss: 0, availability: 0 },
                        chartData: { times: [], latencies: [] }
                    });
                }
            }

            // 计算总体统计
            if (networkQualityData.targets.length > 0) {
                const validTargets = networkQualityData.targets.filter(t => t.metrics.avgLatency > 0);
                
                if (validTargets.length > 0) {
                    const totalLatency = validTargets.reduce((sum, target) => 
                        sum + (target.metrics.avgLatency || 0), 0);
                    const totalPacketLoss = validTargets.reduce((sum, target) => 
                        sum + (target.metrics.packetLoss || 0), 0);
                    const totalAvailability = validTargets.reduce((sum, target) => 
                        sum + (target.metrics.availability || 0), 0);
                    
                    networkQualityData.summary.avgLatency = Math.round(totalLatency / validTargets.length);
                    networkQualityData.summary.avgPacketLoss = Math.round((totalPacketLoss / validTargets.length) * 100) / 100;
                    networkQualityData.summary.avgAvailability = Math.round((totalAvailability / validTargets.length) * 100) / 100;
                }
            }

            res.json({
                success: true,
                data: networkQualityData
            });
        } catch (error) {
            // 增强错误日志记录
            console.error('[Network Quality API] 获取网络质量概览数据失败');
            console.error('错误类型:', typeof error);
            console.error('错误对象:', error);
            console.error('错误消息:', error?.message || '无错误消息');
            console.error('错误堆栈:', error?.stack || '无堆栈信息');
            
            res.status(500).json({
                success: false,
                message: '获取网络质量数据失败: ' + (error?.message || '未知错误'),
                errorType: typeof error,
                errorDetails: error ? Object.keys(error).join(', ') : 'null or undefined'
            });
        }
    });

    /**
     * 获取特定监控目标的详细网络质量数据
     * GET /api/network-quality/target/:id?timeRange=<1h|6h|24h|7d|30d>&granularity=<m|5m|h|d|month>
     */
    router.get('/api/network-quality/target/:id', async (req, res) => {
        try {
            const { id } = req.params;
            const { timeRange = '24h', granularity } = req.query;
            
            // 验证监控目标是否存在
            if (!monitor || !monitor.targets || typeof monitor.targets.get !== 'function') {
                return res.status(500).json({
                    success: false,
                    message: 'Monitor服务不可用'
                });
            }
            
            const target = await monitor.targets.get(id);
            
            if (!target) {
                return res.status(404).json({
                    success: false,
                    message: '监控目标不存在'
                });
            }

            // 计算时间范围（秒）
            let durationSeconds;
            switch (timeRange) {
                case '1h':
                    durationSeconds = 60 * 60;
                    break;
                case '6h':
                    durationSeconds = 6 * 60 * 60;
                    break;
                case '24h':
                    durationSeconds = 24 * 60 * 60;
                    break;
                case '7d':
                    durationSeconds = 7 * 24 * 60 * 60;
                    break;
                case '30d':
                    durationSeconds = 30 * 24 * 60 * 60;
                    break;
                default:
                    durationSeconds = 24 * 60 * 60;
            }

            // 获取TCPing数据，支持颗粒度参数
            const tcpingData = await getTargetTcpingDataWithGranularity(id, durationSeconds, granularity, timeRange, monitor);
            const metrics = calculateNetworkMetrics(tcpingData.data);
            const chartData = formatChartData(tcpingData.data, timeRange);

            res.json({
                success: true,
                data: {
                    target: {
                        id: target.id,
                        name: target.name,
                        region: target.region_name,
                        test_type: target.test_type
                    },
                    timeRange: timeRange,
                    granularity: tcpingData.actualGranularity,
                    dataSource: tcpingData.dataSource,
                    metrics: metrics,
                    chartData: chartData,
                    rawData: tcpingData.data.slice(-100), // 限制返回最近100条数据
                    metadata: {
                        dataPoints: tcpingData.data.length,
                        requestedGranularity: granularity || 'auto',
                        actualGranularity: tcpingData.actualGranularity,
                        dataSource: tcpingData.dataSource
                    }
                }
            });
        } catch (error) {
            // 增强错误日志记录
            console.error('[Network Quality API] 获取监控目标网络质量数据失败');
            console.error('错误类型:', typeof error);
            console.error('错误对象:', error);
            console.error('错误消息:', error?.message || '无错误消息');
            console.error('错误堆栈:', error?.stack || '无堆栈信息');
            
            res.status(500).json({
                success: false,
                message: '获取监控目标网络质量数据失败: ' + (error?.message || '未知错误'),
                errorType: typeof error,
                errorDetails: error ? Object.keys(error).join(', ') : 'null or undefined'
            });
        }
    });

    /**
     * 获取按节点分组的网络质量监控数据
     * GET /api/network-quality/nodes-overview?timeRange=<1h|6h|24h|7d|30d>
     */
    router.get('/api/network-quality/nodes-overview', async (req, res) => {
        try {
            const { timeRange = '24h' } = req.query;
            // 新增：可选参数以减载与前端懒加载配合
            const seriesParam = (req.query.series || '').toLowerCase();
            const chartsParam = (req.query.charts || 'all').toLowerCase();
            const maxPointsParam = parseInt(req.query.maxPoints, 10);
            const includePacketLossSeries = seriesParam.includes('packetloss');
            const chartsMode = chartsParam.startsWith('top:') ? 'top' : (chartsParam === 'none' ? 'none' : 'all');
            const chartsTopN = chartsMode === 'top' ? Math.max(1, parseInt(chartsParam.split(':')[1], 10) || 5) : 0;
            const maxPoints = Number.isFinite(maxPointsParam) ? Math.max(20, Math.min(maxPointsParam, 500)) : undefined;
            // 新增：控制返回内容与facets作用域
            const facetsScope = String(req.query.facetsScope || 'filtered').toLowerCase(); // filtered | both
            const includeParam = String(req.query.include || '').toLowerCase();
            const includeSet = new Set((includeParam ? includeParam.split(',') : []).map(s => s.trim()).filter(Boolean));
            // 已移除分页限制 - 返回所有节点
            // 筛选参数
            const groupIdsParam = req.query.groupIds || req.query.groupId || '';
            const regionsParam = req.query.regions || req.query.region || '';
            const statusParam = (req.query.status || '').toLowerCase(); // online|offline|all
            const q = (req.query.q || '').toLowerCase();
            const groupIds = String(groupIdsParam).split(',').map(s => s.trim()).filter(Boolean);
            const regions = String(regionsParam).split(',').map(s => s.trim().toUpperCase()).filter(Boolean);

            // 计算时间范围（秒）
            const durationSeconds = getTimeRangeDuration(timeRange);

            // 获取所有服务器并按筛选过滤
            const allServers = await database.servers.all();
            let filteredServers = allServers;
            // 状态筛选（与 /stats 语义对齐：仅在 online/offline 时过滤，all/空=不筛选）
            if (statusParam === 'online') {
                filteredServers = filteredServers.filter(s => s.status === 1);
            } else if (statusParam === 'offline') {
                filteredServers = filteredServers.filter(s => s.status !== 1);
            } // 其他情况（all/空/未知）不做状态过滤
            // 分组筛选
            if (groupIds.length > 0) {
                const set = new Set(groupIds);
                filteredServers = filteredServers.filter(s => set.has(s.group_id || 'default'));
            }
            // 地区筛选
            if (regions.length > 0) {
                const set = new Set(regions);
                filteredServers = filteredServers.filter(s => {
                    const loc = (s.data && s.data.location) || {};
                    const code = (loc.code || '').toUpperCase();
                    const nameZh = (loc.name_zh || '').toUpperCase();
                    return (code && set.has(code)) || (nameZh && set.has(nameZh));
                });
            }
            // 搜索
            if (q) filteredServers = filteredServers.filter(s => (s.name || '').toLowerCase().includes(q));

            // 为 facetsAll 计算准备：
            // 1) 忽略 groupId 的服务器过滤（其余条件一致）用于 groups 计数基线
            let filteredServersNoGroup = allServers;
            if (statusParam === 'online') {
                filteredServersNoGroup = filteredServersNoGroup.filter(s => s.status === 1);
            } else if (statusParam === 'offline') {
                filteredServersNoGroup = filteredServersNoGroup.filter(s => s.status !== 1);
            } // 其他情况不做状态过滤
            if (regions.length > 0) {
                const set = new Set(regions);
                filteredServersNoGroup = filteredServersNoGroup.filter(s => {
                    const loc = (s.data && s.data.location) || {};
                    const code = (loc.code || '').toUpperCase();
                    const nameZh = (loc.name_zh || '').toUpperCase();
                    return (code && set.has(code)) || (nameZh && set.has(nameZh));
                });
            }
            if (q) filteredServersNoGroup = filteredServersNoGroup.filter(s => (s.name || '').toLowerCase().includes(q));

            // 2) 忽略 region 的服务器过滤（其余条件一致）用于 regions 计数基线
            let filteredServersNoRegion = allServers;
            if (statusParam === 'online') {
                filteredServersNoRegion = filteredServersNoRegion.filter(s => s.status === 1);
            } else if (statusParam === 'offline') {
                filteredServersNoRegion = filteredServersNoRegion.filter(s => s.status !== 1);
            } // 其他情况不做状态过滤
            // 注意：不应用 regions 过滤
            if (groupIds.length > 0) {
                const set = new Set(groupIds);
                filteredServersNoRegion = filteredServersNoRegion.filter(s => set.has(s.group_id || 'default'));
            }
            if (q) filteredServersNoRegion = filteredServersNoRegion.filter(s => (s.name || '').toLowerCase().includes(q));
            
            // 获取所有分组信息
            const allGroups = await database.groups.all();
            const groupsMap = new Map(allGroups.map(group => [group.id, group.name]));
            
            // 获取所有监控目标
            let allTargets = [];
            if (monitor && monitor.targets && typeof monitor.targets.getAll === 'function') {
                try {
                    allTargets = await monitor.targets.getAll();
                    console.log('[Network Quality API] 获取监控目标成功，数量:', allTargets.length);
                } catch (targetsError) {
                    console.error('[Network Quality API] 获取监控目标失败:', targetsError);
                    throw targetsError;
                }
            } else {
                console.warn('[NetworkQuality API] Monitor.targets不可用，返回空数据');
                return res.json({
                    success: true,
                    data: {
                        summary: {
                            totalNodes: 0,
                            totalTargets: 0,
                            avgLatency: 0,
                            healthScore: 0
                        },
                        nodes: []
                    }
                });
            }

            // 允许的节点集合
            const allowedSidSet = new Set(filteredServers.map(s => s.sid));
            const allowedSidSetNoGroup = new Set(filteredServersNoGroup.map(s => s.sid));
            const allowedSidSetNoRegion = new Set(filteredServersNoRegion.map(s => s.sid));
            // 先根据监控目标筛选真正“有目标”的节点（且在允许集合内）
            const nodeIdsWithTargets = new Set();
            const nodeIdsWithTargetsNoGroup = new Set();
            for (const t of allTargets) {
                if (!t.node_id) continue;
                try {
                    if (typeof t.node_id === 'string' && (t.node_id.startsWith('[') || t.node_id.startsWith('{'))) {
                        const js = JSON.parse(t.node_id);
                        if (Array.isArray(js)) js.forEach(id => {
                            if (id && allowedSidSet.has(id)) nodeIdsWithTargets.add(id);
                            if (id && allowedSidSetNoGroup.has(id)) nodeIdsWithTargetsNoGroup.add(id);
                        });
                        else if (js) {
                            if (allowedSidSet.has(js)) nodeIdsWithTargets.add(js);
                            if (allowedSidSetNoGroup.has(js)) nodeIdsWithTargetsNoGroup.add(js);
                        }
                    } else {
                        if (allowedSidSet.has(t.node_id)) nodeIdsWithTargets.add(t.node_id);
                        if (allowedSidSetNoGroup.has(t.node_id)) nodeIdsWithTargetsNoGroup.add(t.node_id);
                    }
                } catch (_) {
                    if (allowedSidSet.has(t.node_id)) nodeIdsWithTargets.add(t.node_id);
                    if (allowedSidSetNoGroup.has(t.node_id)) nodeIdsWithTargetsNoGroup.add(t.node_id);
                }
            }

            const serversWithTargets = filteredServers.filter(s => nodeIdsWithTargets.has(s.sid));
            const serversWithTargetsNoGroup = filteredServersNoGroup.filter(s => nodeIdsWithTargetsNoGroup.has(s.sid));
            // 计算不带 region 过滤的允许节点集合
            const nodeIdsWithTargetsNoRegion = new Set();
            for (const t of allTargets) {
                if (!t.node_id) continue;
                try {
                    if (typeof t.node_id === 'string' && (t.node_id.startsWith('[') || t.node_id.startsWith('{'))) {
                        const js = JSON.parse(t.node_id);
                        if (Array.isArray(js)) js.forEach(id => { if (id && allowedSidSetNoRegion.has(id)) nodeIdsWithTargetsNoRegion.add(id); });
                        else if (js) { if (allowedSidSetNoRegion.has(js)) nodeIdsWithTargetsNoRegion.add(js); }
                    } else {
                        if (allowedSidSetNoRegion.has(t.node_id)) nodeIdsWithTargetsNoRegion.add(t.node_id);
                    }
                } catch (_) {
                    if (allowedSidSetNoRegion.has(t.node_id)) nodeIdsWithTargetsNoRegion.add(t.node_id);
                }
            }
            const serversWithTargetsNoRegion = filteredServersNoRegion.filter(s => nodeIdsWithTargetsNoRegion.has(s.sid));
            const totalNodesAll = serversWithTargets.length;
            const pageServers = serversWithTargets; // 返回所有节点
            
            // 按节点分组构建数据
            const { createConcurrencyLimiter } = require('../../../utils/concurrency');
            const limitTargetQueries = createConcurrencyLimiter(8);
            const nodesData = [];

            for (const server of pageServers) {
                const nodeId = server.sid;
                const nodeName = server.name;
                
                // 找到该节点监控的所有目标
                const nodeTargets = allTargets.filter(target => {
                    if (!target.node_id) return false;
                    
                    try {
                        // 尝试解析JSON格式的node_id
                        if (target.node_id.startsWith('[') || target.node_id.startsWith('{')) {
                            const nodeIds = JSON.parse(target.node_id);
                            return Array.isArray(nodeIds) ? nodeIds.includes(nodeId) : nodeIds === nodeId;
                        } else {
                            // 直接字符串比较
                            return target.node_id === nodeId;
                        }
                    } catch (e) {
                        // 如果解析失败，进行直接比较
                        return target.node_id === nodeId;
                    }
                });
                
                if (nodeTargets.length === 0) {
                    continue; // 跳过没有监控目标的节点
                }
                
                // 获取每个目标的数据（受限并发）
                const rawTargetResults = await Promise.all(nodeTargets.map(t =>
                    limitTargetQueries(async () => {
                        try {
                            const tcpingData = await getTargetTcpingDataByNode(t.id, nodeId, durationSeconds, monitor);
                            const metrics = calculateNetworkMetrics(tcpingData);
                            return { target: t, tcpingData, metrics };
                        } catch (error) {
                            console.error(`获取节点 ${nodeId} 目标 ${t.id} 数据失败:`, error);
                            return { target: t, tcpingData: [], metrics: { avgLatency: 0, packetLoss: 100, availability: 0, minLatency: 0, maxLatency: 0 } };
                        }
                    })
                ));

                // 计算节点级别指标
                let totalLatency = 0;
                let totalAvailability = 0;
                let validTargetsCount = 0;
                rawTargetResults.forEach(({ metrics }) => {
                    if (metrics && metrics.avgLatency > 0) {
                        totalLatency += metrics.avgLatency;
                        totalAvailability += metrics.availability;
                        validTargetsCount++;
                    }
                });

                // 选择需要生成图表的目标
                let selectedIds = new Set();
                if (chartsMode === 'all') {
                    selectedIds = new Set(rawTargetResults.map(r => r.target.id));
                } else if (chartsMode === 'top') {
                    const sorted = rawTargetResults.slice().sort((a, b) => (b.metrics.avgLatency || 0) - (a.metrics.avgLatency || 0));
                    sorted.slice(0, chartsTopN).forEach(r => selectedIds.add(r.target.id));
                } // 'none' -> 空集合

                // 组装返回的 targets 数据
                const targetsData = rawTargetResults.map(({ target, tcpingData, metrics }) => {
                    const includeChart = selectedIds.has(target.id);
                    const chartData = includeChart
                        ? formatChartData(tcpingData, timeRange, { maxPoints, includePacketLoss: includePacketLossSeries })
                        : { times: [], latencies: [], packetLoss: [] };

                    return {
                        targetId: target.id,
                        targetName: target.name,
                        host: target.host,
                        port: target.port,
                        region: target.region_name,
                        testType: target.test_type || 'tcping',
                        metrics,
                        chartData,
                        dataPoints: tcpingData.length
                    };
                });
                
                // 计算节点级别的整体指标
                const nodeMetrics = {
                    avgLatency: validTargetsCount > 0 ? Math.round(totalLatency / validTargetsCount) : 0,
                    avgAvailability: validTargetsCount > 0 ? Math.round(totalAvailability / validTargetsCount) : 0,
                    totalTargets: nodeTargets.length,
                    activeTargets: validTargetsCount,
                    healthScore: validTargetsCount > 0 ? Math.round(totalAvailability / validTargetsCount) : 0
                };
                
                // 提取分组和地区信息
                const groupId = server.group_id || 'default';
                let groupName = groupsMap.get(groupId);
                
                // 如果在groups表中找不到对应的分组，使用友好的默认名称
                if (!groupName) {
                    groupName = groupId === 'default' ? '默认分组' : '未分类分组';
                }
                
                const serverData = server.data || {};
                const location = serverData.location || {};
                const ipls = getIPLocationService();

                // 严格从存储信息获取，缺失时回退到 ipLocationService
                const codeUpper = (location.code || location.country?.code || 'UNKNOWN').toUpperCase();
                const nameZhStored = location.name_zh || location.country_name || location.country?.name_zh || '';
                const regionName = nameZhStored || (ipls ? ipls.getCountryNameZh(codeUpper) : getCountryNameZhLocal(codeUpper)) || '未知地区';
                const flagStored = location.flag || location.country?.flag || '';
                const flagPath = flagStored || (ipls ? ipls.getCountryFlag(codeUpper, null) : null) || '';

                const countryData = {
                    code: codeUpper,
                    name_zh: regionName,
                    name: nameZhStored || regionName,
                    flag: flagPath
                };

                nodesData.push({
                    nodeId: nodeId,
                    nodeName: nodeName,
                    nodeStatus: server.status,
                    groupId: groupId,
                    groupName: groupName,
                    location: {
                        country: countryData,
                        region: regionName
                    },
                    targets: targetsData,
                    nodeMetrics: nodeMetrics
                });
            }
            
            // 计算统计（基于本页数据）
            let globalTotalLatency = 0;
            let globalTotalAvailability = 0;
            let globalValidNodes = 0;
            let globalTotalTargets = 0;
            
            nodesData.forEach(node => {
                if (node.nodeMetrics.activeTargets > 0) {
                    globalTotalLatency += node.nodeMetrics.avgLatency;
                    globalTotalAvailability += node.nodeMetrics.avgAvailability;
                    globalValidNodes++;
                }
                globalTotalTargets += node.nodeMetrics.totalTargets;
            });
            
            const summary = {
                totalNodes: totalNodesAll,
                totalTargets: globalTotalTargets,
                avgLatency: globalValidNodes > 0 ? Math.round(globalTotalLatency / globalValidNodes) : 0,
                avgAvailability: globalValidNodes > 0 ? Math.round(globalTotalAvailability / globalValidNodes) : 0,
                healthScore: globalValidNodes > 0 ? Math.round(globalTotalAvailability / globalValidNodes) : 0
            };

            // 计算 facets（filtered）
            const groupCounts = new Map();
            const regionCounts = new Map();
            for (const server of serversWithTargets) {
                const gid = server.group_id || 'default';
                groupCounts.set(gid, (groupCounts.get(gid) || 0) + 1);
                const location = server.data?.location || {};
                const code = (location.code || 'UNKNOWN').toUpperCase();
                const ipls = getIPLocationService();
                const name = location.name_zh || (ipls ? ipls.getCountryNameZh(code) : getCountryNameZhLocal?.(code)) || '未知地区';
                if (!regionCounts.has(code)) regionCounts.set(code, { name, count: 0 });
                regionCounts.get(code).count++;
            }
            const facets = {
                totalCount: serversWithTargets.length,
                groups: Array.from(groupCounts, ([id, count]) => ({
                    id,
                    name: groupsMap.get(id) || (id === 'default' ? '默认分组' : '未分类分组'),
                    count
                })).sort((a, b) => b.count - a.count),
                regions: Array.from(regionCounts, ([code, data]) => ({
                    code,
                    name: data.name,
                    count: data.count
                })).sort((a, b) => b.count - a.count)
            };

            // 计算 facetsAll：groups 忽略 group 过滤，regions 忽略 region 过滤
            let facetsAll = null;
            if (facetsScope === 'both') {
                const groupCountsAll = new Map();
                const regionCountsAll = new Map();
                // groups：使用不带 group 过滤的集合
                for (const server of serversWithTargetsNoGroup) {
                    const gid = server.group_id || 'default';
                    groupCountsAll.set(gid, (groupCountsAll.get(gid) || 0) + 1);
                }
                // regions：使用不带 region 过滤的集合
                for (const server of serversWithTargetsNoRegion) {
                    const location = server.data?.location || {};
                    const code = (location.code || 'UNKNOWN').toUpperCase();
                    const ipls = getIPLocationService();
                    const name = location.name_zh || (ipls ? ipls.getCountryNameZh(code) : getCountryNameZhLocal?.(code)) || '未知地区';
                    if (!regionCountsAll.has(code)) regionCountsAll.set(code, { name, count: 0 });
                    regionCountsAll.get(code).count++;
                }
                facetsAll = {
                    // totalCount 仅作兼容占位，前端应以具体维度合计为准
                    totalCount: serversWithTargetsNoGroup.length,
                    groups: Array.from(groupCountsAll, ([id, count]) => ({
                        id,
                        name: groupsMap.get(id) || (id === 'default' ? '默认分组' : '未分类分组'),
                        count
                    })).sort((a, b) => b.count - a.count),
                    regions: Array.from(regionCountsAll, ([code, data]) => ({
                        code,
                        name: data.name,
                        count: data.count
                    })).sort((a, b) => b.count - a.count)
                };
            }

            // maps（分组与地区的名称映射）
            const maps = { groups: {}, regions: {} };
            try {
                for (const [gid, gname] of groupsMap.entries()) {
                    maps.groups[gid] = gname || (gid === 'default' ? '默认分组' : '未分类分组');
                }
                // 汇总地区名称与flag作为映射（合并不带group与不带region的集合，更全面）
                const unionForRegions = new Map();
                const collect = (list) => {
                    for (const server of list) {
                        const location = server.data?.location || {};
                        const code = (location.code || 'UNKNOWN').toUpperCase();
                        if (!unionForRegions.has(code)) {
                            const ipls = getIPLocationService();
                            const name = location.name_zh || (ipls ? ipls.getCountryNameZh(code) : getCountryNameZhLocal?.(code)) || '未知地区';
                            const flag = location.flag || (ipls ? ipls.getCountryFlag(code, null) : null) || '';
                            unionForRegions.set(code, { name, flag });
                        }
                    }
                };
                collect(serversWithTargetsNoGroup);
                collect(serversWithTargetsNoRegion);
                for (const [code, info] of unionForRegions.entries()) {
                    maps.regions[code] = info;
                }
            } catch (_) {}

            const respData = {
                timeRange: timeRange,
                lastUpdated: Date.now(),
                pagination: {
                    totalNodes: totalNodesAll,
                    hasMore: false
                },
                // 明确返回当前筛选，便于前端标注“汇总（按筛选）”
                scope: {
                    appliedFilters: {
                        groupId: groupIds.join(',') || null,
                        region: regions.join(',') || null,
                        status: statusParam || null,
                        q: q || ''
                    }
                }
            };

            // 按include控制返回内容（默认兼容：若未指定include则返回全部）
            const includeAll = includeSet.size === 0;
            if (includeAll || includeSet.has('summary')) respData.summary = summary;
            if (includeAll || includeSet.has('nodes')) respData.nodes = nodesData;
            if (includeAll || includeSet.has('facets')) respData.facets = facets;
            if ((includeAll || includeSet.has('facets')) && facetsScope === 'both') respData.facetsAll = facetsAll;
            if (includeAll || includeSet.has('maps')) respData.maps = maps;

            res.json({ success: true, data: respData });
        } catch (error) {
            // 增强错误日志记录
            console.error('[Network Quality API] 获取节点网络质量概览数据失败');
            console.error('错误类型:', typeof error);
            console.error('错误对象:', error);
            console.error('错误消息:', error?.message || '无错误消息');
            console.error('错误堆栈:', error?.stack || '无堆栈信息');
            console.error('错误字符串:', String(error));
            
            // 如果error是字符串，直接使用
            const errorMessage = typeof error === 'string' ? error : (error?.message || '未知错误');
            
            res.status(500).json({
                success: false,
                message: '获取节点网络质量数据失败: ' + errorMessage,
                errorType: typeof error,
                errorDetails: error ? Object.keys(error).join(', ') : 'null or undefined'
            });
        }
    });

    /**
     * 获取网络质量统计概要
     * GET /api/network-quality/stats
     */
    router.get('/api/network-quality/stats', async (req, res) => {
        try {
            const { timeRange = '1h' } = req.query;
            // 解析筛选参数（与 nodes-overview 对齐）
            const groupIdsParam = req.query.groupIds || req.query.groupId || '';
            const regionsParam = req.query.regions || req.query.region || '';
            const statusParam = (req.query.status || '').toLowerCase(); // online|offline|all
            const q = (req.query.q || '').toLowerCase();
            const groupIds = String(groupIdsParam).split(',').map(s => s.trim()).filter(Boolean);
            const regions = String(regionsParam).split(',').map(s => s.trim().toUpperCase()).filter(Boolean);
            // 验证monitor对象
            if (!monitor || !monitor.targets || typeof monitor.targets.getAll !== 'function') {
                console.error('[Network Quality API] monitor对象不可用 (统计API)');
                return res.status(500).json({
                    success: false,
                    message: 'Monitor服务不可用',
                    error: 'MONITOR_UNAVAILABLE'
                });
            }
            
            // 获取所有服务器并按筛选过滤
            const allServers = await database.servers.all();
            let filteredServers = allServers;

            // 状态筛选
            if (statusParam === 'online') {
                filteredServers = filteredServers.filter(s => s.status === 1);
            } else if (statusParam === 'offline') {
                filteredServers = filteredServers.filter(s => s.status !== 1);
            }

            // 分组筛选
            if (groupIds.length > 0) {
                const set = new Set(groupIds);
                filteredServers = filteredServers.filter(s => set.has(s.group_id || 'default'));
            }

            // 地区筛选（优先 code）
            if (regions.length > 0) {
                const set = new Set(regions.map(r => r.toUpperCase()));
                filteredServers = filteredServers.filter(s => {
                    const loc = (s.data && s.data.location) || {};
                    const code = (loc.code || '').toUpperCase();
                    const nameZh = (loc.name_zh || '').toUpperCase();
                    return (code && set.has(code)) || (nameZh && set.has(nameZh));
                });
            }

            // 搜索（名称模糊）
            if (q) {
                filteredServers = filteredServers.filter(s => (s.name || '').toLowerCase().includes(q));
            }

            // 仅统计拥有监控目标的节点
            const allTargets = await monitor.targets.getAll();
            const allowedSidSet = new Set(filteredServers.map(s => s.sid));
            const targets = allTargets.filter(t => {
                if (!t.node_id) return false;
                try {
                    const nid = t.node_id;
                    if (typeof nid === 'string' && (nid.startsWith('[') || nid.startsWith('{'))) {
                        const js = JSON.parse(nid);
                        if (Array.isArray(js)) return js.some(id => allowedSidSet.has(id));
                        return allowedSidSet.has(js);
                    }
                    return allowedSidSet.has(nid);
                } catch (_) {
                    return allowedSidSet.has(t.node_id);
                }
            });
            
            // 根据 timeRange 获取持续时长（默认1小时）
            const durationSeconds = getTimeRangeDuration(timeRange || '1h');
            
            let totalLatency = 0;
            let totalPacketLoss = 0;
            let totalAvailability = 0;
            let validTargetCount = 0;

            for (const target of targets) {
                try {
                    const tcpingData = await getTargetTcpingData(target.id, durationSeconds, monitor);
                    if (tcpingData.length > 0) {
                        const metrics = calculateNetworkMetrics(tcpingData);
                        totalLatency += metrics.avgLatency || 0;
                        totalPacketLoss += metrics.packetLoss || 0;
                        totalAvailability += metrics.availability || 0;
                        validTargetCount++;
                    }
                } catch (error) {
                    console.error(`统计监控目标 ${target.id} 网络质量失败:`, error);
                }
            }

            const stats = {
                totalTargets: targets.length,
                nodesCount: filteredServers.filter(s => allowedSidSet.has(s.sid)).length,
                activeTargets: validTargetCount,
                avgLatency: validTargetCount > 0 ? Math.round(totalLatency / validTargetCount) : 0,
                avgPacketLoss: validTargetCount > 0 ? Math.round((totalPacketLoss / validTargetCount) * 100) / 100 : 0,
                avgAvailability: validTargetCount > 0 ? Math.round((totalAvailability / validTargetCount) * 100) / 100 : 0,
                lastUpdated: Date.now()
            };

            // 轻量HTTP缓存：15s + stale-while-revalidate 30s
            try { res.set('Cache-Control', 'public, max-age=15, stale-while-revalidate=30'); } catch (_) {}

            res.json({
                success: true,
                data: {
                    ...stats,
                    timeRange
                }
            });
        } catch (error) {
            // 增强错误日志记录
            console.error('[Network Quality API] 获取网络质量统计失败');
            console.error('错误类型:', typeof error);
            console.error('错误对象:', error);
            console.error('错误消息:', error?.message || '无错误消息');
            console.error('错误堆栈:', error?.stack || '无堆栈信息');
            
            res.status(500).json({
                success: false,
                message: '获取网络质量统计失败: ' + (error?.message || '未知错误'),
                errorType: typeof error,
                errorDetails: error ? Object.keys(error).join(', ') : 'null or undefined'
            });
        }
    });

    return router;
};

/**
 * 获取监控目标的TCPing数据
 */
async function getTargetTcpingData(targetId, durationSeconds, monitor) {
    try {
        let data = [];

        // 验证monitor对象
        if (!monitor) {
            console.error('[getTargetTcpingData] monitor对象为null或undefined');
            return [];
        }

        // 根据时间范围选择合适的数据粒度
        if (durationSeconds <= 2 * 60 * 60) { // 2小时内使用分钟级数据
            if (monitor.tcping_m && typeof monitor.tcping_m.selectByTimeRange === 'function') {
                data = await monitor.tcping_m.selectByTimeRange(targetId, durationSeconds);
            } else {
                console.warn('[getTargetTcpingData] tcping_m.selectByTimeRange不可用');
            }
        } else if (durationSeconds <= 7 * 24 * 60 * 60) { // 7天内使用小时级数据
            if (monitor.tcping_h && typeof monitor.tcping_h.selectByTimeRange === 'function') {
                data = await monitor.tcping_h.selectByTimeRange(targetId, durationSeconds);
            } else {
                console.warn('[getTargetTcpingData] tcping_h.selectByTimeRange不可用');
            }
        } else { // 更长时间使用天级数据
            if (monitor.tcping_d && typeof monitor.tcping_d.selectByTimeRange === 'function') {
                data = await monitor.tcping_d.selectByTimeRange(targetId, durationSeconds);
            } else {
                console.warn('[getTargetTcpingData] tcping_d.selectByTimeRange不可用');
            }
        }

        return Array.isArray(data) ? data : [];
    } catch (error) {
        console.error('[getTargetTcpingData] 获取TCPing数据失败:', error);
        console.error('错误类型:', typeof error);
        console.error('错误消息:', error?.message);
        console.error('错误堆栈:', error?.stack);
        return [];
    }
}

/**
 * 获取监控目标的TCPing数据（支持颗粒度参数）
 */
async function getTargetTcpingDataWithGranularity(targetId, durationSeconds, granularity, timeRange, monitor) {
    try {
        let data = [];
        let actualGranularity = granularity;
        let dataSource = '';

        // 如果未指定颗粒度，根据时间范围自动选择
        if (!granularity) {
            if (timeRange === '1h' || timeRange === '6h') {
                actualGranularity = 'm';
            } else if (timeRange === '24h') {
                actualGranularity = '5m';
            } else if (timeRange === '7d') {
                actualGranularity = 'h';
            } else if (timeRange === '30d') {
                actualGranularity = 'd';
            } else {
                actualGranularity = 'h';
            }
        }

        // 验证monitor对象
        if (!monitor) {
            console.error('[getTargetTcpingDataWithGranularity] monitor对象为null或undefined');
            return {
                data: [],
                actualGranularity: actualGranularity,
                dataSource: 'error'
            };
        }
        
        // 根据颗粒度选择对应的数据表
        switch (actualGranularity) {
            case 'm':
                if (monitor.tcping_m && typeof monitor.tcping_m.selectByTimeRange === 'function') {
                    data = await monitor.tcping_m.selectByTimeRange(targetId, durationSeconds) || [];
                }
                dataSource = 'tcping_m';
                break;
            case '5m':
                // 如果有5分钟表就用5分钟表，否则用分钟表
                if (monitor.tcping_5m && typeof monitor.tcping_5m.selectByTimeRange === 'function') {
                    data = await monitor.tcping_5m.selectByTimeRange(targetId, durationSeconds) || [];
                    dataSource = 'tcping_5m';
                } else if (monitor.tcping_m && typeof monitor.tcping_m.selectByTimeRange === 'function') {
                    data = await monitor.tcping_m.selectByTimeRange(targetId, durationSeconds) || [];
                    dataSource = 'tcping_m';
                    actualGranularity = 'm'; // 实际使用的是分钟级
                }
                break;
            case 'h':
                if (monitor.tcping_h && typeof monitor.tcping_h.selectByTimeRange === 'function') {
                    data = await monitor.tcping_h.selectByTimeRange(targetId, durationSeconds) || [];
                }
                dataSource = 'tcping_h';
                break;
            case 'd':
                if (monitor.tcping_d && typeof monitor.tcping_d.selectByTimeRange === 'function') {
                    data = await monitor.tcping_d.selectByTimeRange(targetId, durationSeconds) || [];
                }
                dataSource = 'tcping_d';
                break;
            case 'month':
                if (monitor.tcping_month && typeof monitor.tcping_month.selectByTimeRange === 'function') {
                    data = await monitor.tcping_month.selectByTimeRange(targetId, durationSeconds) || [];
                }
                dataSource = 'tcping_month';
                break;
            default:
                // 回退到自动选择
                if (durationSeconds <= 2 * 60 * 60) {
                    if (monitor.tcping_m && typeof monitor.tcping_m.selectByTimeRange === 'function') {
                        data = await monitor.tcping_m.selectByTimeRange(targetId, durationSeconds) || [];
                    }
                    dataSource = 'tcping_m';
                    actualGranularity = 'm';
                } else if (durationSeconds <= 7 * 24 * 60 * 60) {
                    if (monitor.tcping_h && typeof monitor.tcping_h.selectByTimeRange === 'function') {
                        data = await monitor.tcping_h.selectByTimeRange(targetId, durationSeconds) || [];
                    }
                    dataSource = 'tcping_h';
                    actualGranularity = 'h';
                } else {
                    if (monitor.tcping_d && typeof monitor.tcping_d.selectByTimeRange === 'function') {
                        data = await monitor.tcping_d.selectByTimeRange(targetId, durationSeconds) || [];
                    }
                    dataSource = 'tcping_d';
                    actualGranularity = 'd';
                }
        }

        console.log(`[Network Quality] 获取目标 ${targetId} 数据: ${dataSource} (${actualGranularity}), ${data.length} 条记录`);

        return {
            data: Array.isArray(data) ? data : [],
            actualGranularity: actualGranularity,
            dataSource: dataSource
        };
    } catch (error) {
        console.error('获取TCPing数据失败:', error);
        return {
            data: [],
            actualGranularity: granularity || 'h',
            dataSource: 'error'
        };
    }
}

/**
 * 计算网络质量指标
 */
function calculateNetworkMetrics(tcpingData) {
    if (!Array.isArray(tcpingData) || tcpingData.length === 0) {
        return {
            avgLatency: 0,
            packetLoss: 0,
            availability: 0,
            minLatency: 0,
            maxLatency: 0
        };
    }

    let totalLatency = 0;
    let totalMinLatency = 0;
    let totalMaxLatency = 0;
    let totalSuccessRate = 0;
    let validRecords = 0;

    tcpingData.forEach(record => {
        if (record && typeof record.avg_time === 'number') {
            totalLatency += record.avg_time;
            totalMinLatency += record.min_time || 0;
            totalMaxLatency += record.max_time || 0;
            totalSuccessRate += record.success_rate || 0;
            validRecords++;
        }
    });

    if (validRecords === 0) {
        return {
            avgLatency: 0,
            packetLoss: 100,
            availability: 0,
            minLatency: 0,
            maxLatency: 0
        };
    }

    const avgLatency = Math.round(totalLatency / validRecords);
    const avgSuccessRate = totalSuccessRate / validRecords;
    const packetLoss = Math.round((1 - avgSuccessRate) * 10000) / 100; // 转换为百分比
    const availability = Math.round(avgSuccessRate * 10000) / 100; // 转换为百分比

    return {
        avgLatency: avgLatency,
        packetLoss: packetLoss,
        availability: availability,
        minLatency: Math.round(totalMinLatency / validRecords),
        maxLatency: Math.round(totalMaxLatency / validRecords)
    };
}

/**
 * 获取时间范围对应的秒数
 */
function getTimeRangeDuration(timeRange) {
    switch (timeRange) {
        case '1h':
            return 60 * 60;
        case '6h':
            return 6 * 60 * 60;
        case '24h':
            return 24 * 60 * 60;
        case '7d':
            return 7 * 24 * 60 * 60;
        case '30d':
            return 30 * 24 * 60 * 60;
        default:
            return 24 * 60 * 60;
    }
}

/**
 * 获取特定节点对特定监控目标的TCPing数据
 */
async function getTargetTcpingDataByNode(targetId, nodeId, durationSeconds, monitor) {
    try {
        let data = [];

        // 验证monitor对象
        if (!monitor) {
            console.error('[getTargetTcpingDataByNode] monitor对象为null或undefined');
            return [];
        }

        // 根据时间范围选择合适的数据粒度
        if (durationSeconds <= 2 * 60 * 60) { // 2小时内使用分钟级数据
            if (monitor.tcping_m && typeof monitor.tcping_m.selectByTimeRangeAndNode === 'function') {
                data = await monitor.tcping_m.selectByTimeRangeAndNode(targetId, nodeId, durationSeconds);
            } else {
                console.warn('[getTargetTcpingDataByNode] tcping_m.selectByTimeRangeAndNode不可用');
            }
        } else if (durationSeconds <= 7 * 24 * 60 * 60) { // 7天内使用小时级数据
            if (monitor.tcping_h && typeof monitor.tcping_h.selectByTimeRangeAndNode === 'function') {
                data = await monitor.tcping_h.selectByTimeRangeAndNode(targetId, nodeId, durationSeconds);
            } else {
                console.warn('[getTargetTcpingDataByNode] tcping_h.selectByTimeRangeAndNode不可用');
            }
        } else { // 更长时间使用天级数据
            if (monitor.tcping_d && typeof monitor.tcping_d.selectByTimeRangeAndNode === 'function') {
                data = await monitor.tcping_d.selectByTimeRangeAndNode(targetId, nodeId, durationSeconds);
            } else {
                console.warn('[getTargetTcpingDataByNode] tcping_d.selectByTimeRangeAndNode不可用');
            }
        }

        return Array.isArray(data) ? data : [];
    } catch (error) {
        console.error(`[getTargetTcpingDataByNode] 获取节点 ${nodeId} 对目标 ${targetId} 的TCPing数据失败:`, error);
        console.error('错误类型:', typeof error);
        console.error('错误消息:', error?.message);
        console.error('错误堆栈:', error?.stack);
        return [];
    }
}

/**
 * 格式化图表数据 - 优化版本，智能采样减少数据点
 */
function formatChartData(tcpingData, timeRange, options = {}) {
    if (!Array.isArray(tcpingData) || tcpingData.length === 0) {
        return {
            times: [],
            latencies: [],
            packetLoss: []
        };
    }

    const maxPoints = typeof options.maxPoints === 'number' && options.maxPoints > 0 ? options.maxPoints : null;
    const includePacketLoss = !!options.includePacketLoss;

    // 根据时间范围决定最大数据点数量（考虑渲染性能）
    let maxDataPoints;
    switch (timeRange) {
        case '1h':
            maxDataPoints = 60;  // 1分钟一个点
            break;
        case '6h':
            maxDataPoints = 72;  // 5分钟一个点
            break;
        case '24h':
            maxDataPoints = 96;  // 15分钟一个点
            break;
        case '7d':
            maxDataPoints = 168; // 1小时一个点
            break;
        case '30d':
            maxDataPoints = 120; // 6小时一个点
            break;
        default:
            maxDataPoints = 100;
    }
    if (maxPoints && maxPoints < maxDataPoints) {
        maxDataPoints = maxPoints;
    }

    // 如果数据点少于阈值，直接返回
    if (tcpingData.length <= maxDataPoints) {
        const times = [];
        const latencies = [];
        const packetLoss = [];

        tcpingData.forEach(record => {
            if (record && record.created_at) {
                times.push(new Date(record.created_at * 1000).toISOString());
                latencies.push(record.avg_time > 0 ? record.avg_time : null);
                if (includePacketLoss) {
                    packetLoss.push(Math.round((1 - (record.success_rate || 0)) * 100));
                }
            }
        });

        return { times, latencies, packetLoss: includePacketLoss ? packetLoss : [] };
    }

    // 智能采样：使用平均值聚合而不是简单跳过
    const chunkSize = Math.ceil(tcpingData.length / maxDataPoints);
    const sampledData = [];

    for (let i = 0; i < tcpingData.length; i += chunkSize) {
        const chunk = tcpingData.slice(i, i + chunkSize);
        if (chunk.length === 0) continue;

        // 计算该时间段的平均值
        let totalLatency = 0;
        let totalSuccessRate = 0;
        let validCount = 0;
        let latestTimestamp = 0;

        chunk.forEach(record => {
            if (record && record.created_at) {
                totalLatency += record.avg_time || 0;
                totalSuccessRate += record.success_rate || 0;
                validCount++;
                latestTimestamp = Math.max(latestTimestamp, record.created_at);
            }
        });

        if (validCount > 0) {
            sampledData.push({
                created_at: latestTimestamp,
                avg_time: Math.round(totalLatency / validCount),
                success_rate: totalSuccessRate / validCount
            });
        }
    }

    const times = [];
    const latencies = [];
    const packetLoss = [];

    sampledData.forEach(record => {
        times.push(new Date(record.created_at * 1000).toISOString());
        latencies.push(record.avg_time > 0 ? record.avg_time : null);
        if (includePacketLoss) {
            packetLoss.push(Math.round((1 - record.success_rate) * 100));
        }
    });

    console.log(`[数据采样] ${timeRange}: 原始${tcpingData.length}点 -> 采样${sampledData.length}点`);

    return {
        times: times,
        latencies: latencies,
        packetLoss: includePacketLoss ? packetLoss : []
    };
}



/**
 * 获取国家中文名（本地版本）
 */
function getCountryNameZhLocal(countryCode) {
    const countryMap = {
        'CN': '中国',
        'HK': '香港',
        'TW': '台湾',
        'JP': '日本',
        'KR': '韩国',
        'SG': '新加坡',
        'US': '美国',
        'CA': '加拿大',
        'GB': '英国',
        'UK': '英国',
        'DE': '德国',
        'FR': '法国',
        'AU': '澳大利亚',
        'VN': '越南',
        'TH': '泰国',
        'MY': '马来西亚',
        'ID': '印度尼西亚',
        'PH': '菲律宾',
        'RU': '俄罗斯',
        'UA': '乌克兰',
        'BR': '巴西',
        'IN': '印度',
        'ZA': '南非',
        'NL': '荷兰',
        'IT': '意大利',
        'ES': '西班牙',
        'CH': '瑞士',
        'SE': '瑞典',
        'NO': '挪威',
        'DK': '丹麦',
        'FI': '芬兰',
        'LO': '本地网络',
        'OT': '其他地区'
    };

    return countryMap[countryCode] || `未知(${countryCode})`;
}

/**
 * 获取国家中文名
 */
function getCountryNameZh(countryCode) {
    return getCountryNameZhLocal(countryCode);
}
