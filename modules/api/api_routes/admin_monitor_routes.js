"use strict";

const express = require('express');

module.exports = (svr, database, monitor) => {
  const router = express.Router();
  const requireAdmin = (req, res, next) => {
    if (!req.admin) {
      return res.status(403).json({ success: false, message: '需要管理员权限' });
    }
    next();
  };

  // 读取：完整字段（含 host/port）
  router.get('/api/admin/monitor/targets', requireAdmin, async (req, res) => {
    try {
      const targets = await monitor.targets.getAll();
      res.json({ success: true, data: targets });
    } catch (err) {
      res.status(500).json({ success: false, message: err.message });
    }
  });

  router.get('/api/admin/monitor/targets/:id', requireAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const target = await monitor.targets.get(id);
      if (!target) return res.status(404).json({ success: false, message: '监控目标不存在' });
      res.json({ success: true, data: target });
    } catch (err) {
      res.status(500).json({ success: false, message: err.message });
    }
  });

  router.get('/api/admin/monitor/regions', requireAdmin, async (req, res) => {
    try {
      const regions = await monitor.regions.getAll();
      res.json({ success: true, data: regions });
    } catch (err) {
      res.status(500).json({ success: false, message: err.message });
    }
  });

  router.get('/api/admin/monitor/regions/:region_id/targets', requireAdmin, async (req, res) => {
    try {
      const { region_id } = req.params;
      const region = await monitor.regions.get(region_id);
      if (!region) return res.status(404).json({ success: false, message: '监控地区不存在' });
      const targets = await monitor.targets.getByRegion(region_id);
      res.json({ success: true, data: targets });
    } catch (err) {
      res.status(500).json({ success: false, message: err.message });
    }
  });

  // 写入：必须 admin
  router.post('/api/admin/monitor/regions', requireAdmin, async (req, res) => {
    try {
      const { name, description } = req.body;
      if (!name) return res.status(400).json({ success: false, message: '地区名称不能为空' });
      const crypto = require('crypto');
      const id = 'region_' + crypto.randomBytes(8).toString('hex');
      const result = await monitor.regions.add(id, name, description || '');
      if (result) return res.json({ success: true, data: { id, name, description } });
      return res.status(500).json({ success: false, message: '添加监控地区失败' });
    } catch (err) {
      res.status(500).json({ success: false, message: err.message });
    }
  });

  router.put('/api/admin/monitor/regions/:id', requireAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const { name, description } = req.body;
      if (!name) return res.status(400).json({ success: false, message: '地区名称不能为空' });
      const region = await monitor.regions.get(id);
      if (!region) return res.status(404).json({ success: false, message: '监控地区不存在' });
      const result = await monitor.regions.update(id, name, description || '');
      if (result) return res.json({ success: true, data: { id, name, description } });
      return res.status(500).json({ success: false, message: '更新监控地区失败' });
    } catch (err) {
      res.status(500).json({ success: false, message: err.message });
    }
  });

  router.delete('/api/admin/monitor/regions/:id', requireAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const region = await monitor.regions.get(id);
      if (!region) return res.status(404).json({ success: false, message: '监控地区不存在' });
      const targets = await monitor.targets.getByRegion(id);
      if (targets && targets.length > 0) {
        return res.status(409).json({
          success: false,
          message: `该地区下存在 ${targets.length} 个监控目标，请先迁移或删除它们后再重试`,
          code: 'REGION_HAS_TARGETS',
          targetsCount: targets.length
        });
      }
      const result = await monitor.regions.delete(id);
      if (result.success) return res.json({ success: true });
      return res.status(400).json({ success: false, message: result.message });
    } catch (err) {
      res.status(500).json({ success: false, message: err.message });
    }
  });

  router.post('/api/admin/monitor/targets', requireAdmin, async (req, res) => {
    try {
      const { region_id, name, host, port, description, mode } = req.body;
      const node_id = req.body.node_id || null;
      const node_ids = req.body.node_ids || [];
      const finalNodeIds = node_ids.length > 0 ? node_ids : (node_id ? [node_id] : []);
      const test_type = req.body.test_type || 'tcping';

      if (!region_id || !name || !host || (test_type !== 'ping' && !port)) {
        return res.status(400).json({ success: false, message: '缺少必需参数: region_id, name, host(必需)，tcping 模式下 port 也是必需的' });
      }
      const region = await monitor.regions.get(region_id);
      if (!region) return res.status(404).json({ success: false, message: '监控地区不存在' });

      if (mode === 'specific' && finalNodeIds.length > 0) {
        const servers = await database.getServers();
        for (const nodeId of finalNodeIds) {
          const server = servers.find(s => s.sid === nodeId);
          if (!server) return res.status(404).json({ success: false, message: `指定的节点 ${nodeId} 不存在` });
          if (!server.data || !server.data.api || !server.data.api.key) {
            console.warn(`节点 ${nodeId} 没有配置API，但仍将继续处理`);
          }
        }
      }

      const crypto = require('crypto');
      const id = 'target_' + crypto.randomBytes(8).toString('hex');
      const success = await monitor.targets.add(id, region_id, name, host, port, description, mode, finalNodeIds.length > 0 ? finalNodeIds : null, test_type);
      if (success) return res.json({ success: true, message: '添加监控目标成功', data: { id } });
      return res.status(500).json({ success: false, message: '添加监控目标失败' });
    } catch (err) {
      res.status(500).json({ success: false, message: err.message });
    }
  });

  router.put('/api/admin/monitor/targets/:id', requireAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const { region_id, name, host, port, description, mode } = req.body;
      const node_id = req.body.node_id || null;
      const node_ids = req.body.node_ids || [];
      const finalNodeIds = node_ids.length > 0 ? node_ids : (node_id ? [node_id] : []);
      const test_type = req.body.test_type || 'tcping';

      if (!region_id || !name || !host || (test_type !== 'ping' && !port)) {
        return res.status(400).json({ success: false, message: '缺少必需参数: region_id, name, host(必需)，tcping 模式下 port 也是必需的' });
      }
      const region = await monitor.regions.get(region_id);
      if (!region) return res.status(404).json({ success: false, message: '监控地区不存在' });

      if (mode === 'specific' && finalNodeIds.length > 0) {
        const servers = await database.getServers();
        for (const nodeId of finalNodeIds) {
          const server = servers.find(s => s.sid === nodeId);
          if (!server) return res.status(404).json({ success: false, message: `指定的节点 ${nodeId} 不存在` });
          if (!server.data || !server.data.api || !server.data.api.key) {
            console.warn(`节点 ${nodeId} 没有配置API，但仍将继续处理`);
          }
        }
      }

      const success = await monitor.targets.update(id, region_id, name, host, port, description, mode, finalNodeIds.length > 0 ? finalNodeIds : null, test_type);
      if (success) return res.json({ success: true, message: '更新监控目标成功' });
      return res.status(500).json({ success: false, message: '更新监控目标失败' });
    } catch (err) {
      res.status(500).json({ success: false, message: err.message });
    }
  });

  router.delete('/api/admin/monitor/targets/:id', requireAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const target = await monitor.targets.get(id);
      if (!target) return res.status(404).json({ success: false, message: '监控目标不存在' });
      const result = await monitor.targets.delete(id);
      if (result) return res.json({ success: true });
      return res.status(500).json({ success: false, message: '删除监控目标失败' });
    } catch (err) {
      res.status(500).json({ success: false, message: err.message });
    }
  });

  return router;
};

