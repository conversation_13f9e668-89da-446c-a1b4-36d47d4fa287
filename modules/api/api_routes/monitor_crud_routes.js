'use strict';

const express = require('express');
const router = express.Router();
const crypto = require('crypto');

module.exports = (database, monitor) => { // Pass monitor instance

    // 公共路由写入禁用：仅允许 GET，其他方法统一403，提示使用 /api/admin
    router.use(['/api/monitor/targets', '/api/monitor/targets/:id', '/api/monitor/regions', '/api/monitor/regions/:id'], (req, res, next) => {
        if (req.method && req.method.toUpperCase() !== 'GET') {
            return res.status(403).json({ success: false, message: '禁止公共写入，请使用 /api/admin/monitor 路由' });
        }
        next();
    });

    // ========== Monitor Regions API Routes ==========

    /**
     * 获取所有监控地区
     * GET /api/monitor/regions
     */
    router.get('/api/monitor/regions', async (req, res) => {
        try {
            const regions = await monitor.regions.getAll();
        res.json({ success: true, data: regions });
        } catch (err) {
            console.error('获取监控地区失败:', err);
            res.status(500).json({ success: false, message: err.message });
        }
    });

    /**
     * 添加监控地区
     * POST /api/monitor/regions
     * Body: { name: "地区名称", description: "描述" }
     */
    router.post('/api/monitor/regions', (req, res) => {
        return res.status(403).json({ success: false, message: '禁止公共写入，请使用 /api/admin/monitor/regions' });
    });

    /**
     * 更新监控地区
     * PUT /api/monitor/regions/:id
     * Body: { name: "地区名称", description: "描述" }
     */
    router.put('/api/monitor/regions/:id', (req, res) => {
        return res.status(403).json({ success: false, message: '禁止公共写入，请使用 /api/admin/monitor/regions/:id' });
    });

    /**
     * 删除监控地区
     * DELETE /api/monitor/regions/:id
     */
    router.delete('/api/monitor/regions/:id', (req, res) => {
        return res.status(403).json({ success: false, message: '禁止公共写入，请使用 /api/admin/monitor/regions/:id' });
    });

    // ========== Monitor Targets API Routes ==========

    /**
     * 获取所有监控目标
     * GET /api/monitor/targets
     */
    router.get('/api/monitor/targets', async (req, res) => {
        try {
            console.log('[API] 获取所有监控目标');
            const targets = await monitor.targets.getAll();
            console.log('[API] 查询到目标数量:', targets.length);
            if (targets.length > 0) {
                console.log('[API] 第一个目标:', targets[0]);
            }
            // 去除敏感字段（host/port）
            const sanitized = targets.map(t => ({
                id: t.id,
                region_id: t.region_id,
                name: t.name,
                description: t.description,
                mode: t.mode,
                node_id: t.node_id,
                test_type: t.test_type,
                region_name: t.region_name,
                created_at: t.created_at
            }));
            res.json({ success: true, data: sanitized });
        } catch (err) {
            console.error('获取监控目标失败:', err);
            res.status(500).json({ success: false, message: err.message });
        }
    });

    /**
     * 获取单个监控目标
     * GET /api/monitor/targets/:id
     */
    router.get('/api/monitor/targets/:id', async (req, res) => {
        try {
            const { id } = req.params;

            const target = await monitor.targets.get(id);
            
            if (!target) {
                return res.status(404).json({ success: false, message: '监控目标不存在' });
            }

            // 去除敏感字段（host/port）
            const sanitized = {
                id: target.id,
                region_id: target.region_id,
                name: target.name,
                description: target.description,
                mode: target.mode,
                node_id: target.node_id,
                test_type: target.test_type,
                region_name: target.region_name,
                created_at: target.created_at
            };

            res.json({ success: true, data: sanitized });
        } catch (err) {
            console.error('获取单个监控目标失败:', err);
            res.status(500).json({ success: false, message: err.message });
        }
    });

    /**
     * 获取指定地区的监控目标
     * GET /api/monitor/regions/:region_id/targets
     */
    router.get('/api/monitor/regions/:region_id/targets', async (req, res) => {
        try {
            const { region_id } = req.params;

            const region = await monitor.regions.get(region_id);
            if (!region) {
                return res.status(404).json({ success: false, message: '监控地区不存在' });
            }

            const targets = await monitor.targets.getByRegion(region_id);
            // 去除敏感字段（host/port）
            const sanitized = targets.map(t => ({
                id: t.id,
                region_id: t.region_id,
                name: t.name,
                description: t.description,
                mode: t.mode,
                node_id: t.node_id,
                test_type: t.test_type,
                region_name: t.region_name,
                created_at: t.created_at
            }));
            res.json({ success: true, data: sanitized });
        } catch (err) {
            console.error('获取地区监控目标失败:', err);
            res.status(500).json({ success: false, message: err.message });
        }
    });

    /**
     * 添加监控目标
     * POST /api/monitor/targets
     * Body: { region_id: "地区ID", name: "名称", host: "主机/IP", port: 80, description: "描述", mode: "监控模式", node_ids: ["节点ID1", "节点ID2"] }
     */
    router.post('/api/monitor/targets', async (req, res) => {
        try {
            const { region_id, name, host, port, description, mode } = req.body;
            const node_id = req.body.node_id || null;
            const node_ids = req.body.node_ids || [];
            const finalNodeIds = node_ids.length > 0 ? node_ids : (node_id ? [node_id] : []);
            const test_type = req.body.test_type || 'tcping';

            if (!region_id || !name || !host || (test_type !== 'ping' && !port)) {
                return res.status(400).json({
                    success: false,
                    message: '缺少必需参数: region_id, name, host 都是必需的，tcping 模式下 port 也是必需的'
                });
            }

            const region = await monitor.regions.get(region_id);
            if (!region) {
                return res.status(404).json({ success: false, message: '监控地区不存在' });
            }

            if (mode === 'specific' && finalNodeIds.length > 0) {
                const servers = await database.getServers(); // Assuming database is accessible
                for (const nodeId of finalNodeIds) {
                    const server = servers.find(s => s.sid === nodeId);
                    if (!server) {
                        return res.status(404).json({ success: false, message: `指定的节点 ${nodeId} 不存在` });
                    }
                    if (!server.data || !server.data.api || !server.data.api.key) {
                        console.warn(`节点 ${nodeId} 没有配置API，但仍将继续处理`);
                    }
                }
            }

            const id = 'target_' + crypto.randomBytes(8).toString('hex');
            const success = await monitor.targets.add(id, region_id, name, host, port, description, mode, finalNodeIds.length > 0 ? finalNodeIds : null, test_type);

            if (success) {
                res.json({ success: true, message: '添加监控目标成功', data: { id } });
            } else {
                res.status(500).json({ success: false, message: '添加监控目标失败' });
            }
        } catch (err) {
            console.error('添加监控目标失败:', err);
            res.status(500).json({ success: false, message: err.message });
        }
    });

    /**
     * 更新监控目标
     * PUT /api/monitor/targets/:id
     * Body: { region_id: "地区ID", name: "名称", host: "主机/IP", port: 80, description: "描述", mode: "监控模式", node_ids: ["节点ID1", "节点ID2"] }
     */
    router.put('/api/monitor/targets/:id', async (req, res) => {
        try {
            const { id } = req.params;
            const { region_id, name, host, port, description, mode } = req.body;
            const node_id = req.body.node_id || null;
            const node_ids = req.body.node_ids || [];
            const finalNodeIds = node_ids.length > 0 ? node_ids : (node_id ? [node_id] : []);
            const test_type = req.body.test_type || 'tcping';

            if (!region_id || !name || !host || (test_type !== 'ping' && !port)) {
                return res.status(400).json({
                    success: false,
                    message: '缺少必需参数: region_id, name, host 都是必需的，tcping 模式下 port 也是必需的'
                });
            }

            const target = await monitor.targets.get(id);
            if (!target) {
                return res.status(404).json({ success: false, message: '监控目标不存在' });
            }

            const region = await monitor.regions.get(region_id);
            if (!region) {
                return res.status(404).json({ success: false, message: '监控地区不存在' });
            }

            if (mode === 'specific' && finalNodeIds.length > 0) {
                const servers = await database.getServers(); // Assuming database is accessible
                for (const nodeId of finalNodeIds) {
                    const server = servers.find(s => s.sid === nodeId);
                    if (!server) {
                        return res.status(404).json({ success: false, message: `指定的节点 ${nodeId} 不存在` });
                    }
                    if (!server.data || !server.data.api || !server.data.api.key) {
                        console.warn(`节点 ${nodeId} 没有配置API，但仍将继续处理`);
                    }
                }
            }

            const success = await monitor.targets.update(id, region_id, name, host, port, description, mode, finalNodeIds.length > 0 ? finalNodeIds : null, test_type);

            if (success) {
                res.json({ success: true, message: '更新监控目标成功' });
            } else {
                res.status(500).json({ success: false, message: '更新监控目标失败' });
            }
        } catch (err) {
            console.error('更新监控目标失败:', err);
            res.status(500).json({ success: false, message: err.message });
        }
    });

    /**
     * 删除监控目标
     * DELETE /api/monitor/targets/:id
     */
    router.delete('/api/monitor/targets/:id', async (req, res) => {
        try {
            const { id } = req.params;

            const target = await monitor.targets.get(id);
            if (!target) {
                return res.status(404).json({ success: false, message: '监控目标不存在' });
            }

            const result = await monitor.targets.delete(id);

            if (result) {
                res.json({ success: true });
            } else {
                res.status(500).json({ success: false, message: '删除监控目标失败' });
            }
        } catch (err) {
            console.error('删除监控目标失败:', err);
            res.status(500).json({ success: false, message: err.message });
        }
    });

    return router;
};
