'use strict';

const express = require('express');
const router = express.Router();
const { getNodeCoolingStatus } = require('../cooling_logic'); // Adjusted path

// 批量网络数据缓存管理器
const networkDataCache = new Map();
const NETWORK_DATA_CACHE_TTL = 300000; // 5分钟缓存，与前端缓存时间一致

/**
 * 获取缓存的网络数据
 * @param {string} cacheKey - 缓存键
 * @returns {Object|null} 缓存的数据或null
 */
function getCachedNetworkData(cacheKey) {
    const cached = networkDataCache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < NETWORK_DATA_CACHE_TTL) {
        return cached.data;
    }
    
    // 缓存过期或不存在
    if (cached) {
        networkDataCache.delete(cacheKey);
    }
    
    return null;
}

/**
 * 设置网络数据缓存
 * @param {string} cacheKey - 缓存键
 * @param {Object} data - 要缓存的数据
 */
function setCachedNetworkData(cacheKey, data) {
    networkDataCache.set(cacheKey, {
        data: data,
        timestamp: Date.now()
    });
    
    // 定期清理过期缓存
    if (networkDataCache.size > 500) {
        const now = Date.now();
        for (const [key, value] of networkDataCache.entries()) {
            if (now - value.timestamp > NETWORK_DATA_CACHE_TTL) {
                networkDataCache.delete(key);
            }
        }
    }
}

module.exports = (database, monitor) => { // Pass monitor instance

    /**
     * 获取监控目标的TCPing数据
     * GET /api/monitor/targets/:id/data?type=<m|h|d|month>
     */
    router.get('/api/monitor/targets/:id/data', async (req, res) => {
        try {
            const { id } = req.params;
            const { type = 'm' } = req.query; // 默认查询分钟级数据

            // 检查目标是否存在
            const target = await monitor.targets.get(id);
            if (!target) {
                return res.status(404).json({ success: false, message: '监控目标不存在' });
            }

            let data;
            switch (type) {
                case 'm':
                    data = monitor.tcping_m.select(id);
                    break;
                case 'h':
                    data = monitor.tcping_h.select(id);
                    break;
                case 'd':
                    data = monitor.tcping_d.select(id);
                    break;
                case 'month':
                    data = monitor.tcping_month.select(id);
                    break;
                default:
                    return res.status(400).json({
                        success: false,
                        message: '无效的数据类型，有效值: m, h, d, month'
                    });
            }

            const nodes = {};
            const servers = await database.getServers();
            let targetNodeIds = [];
            if (target.node_id) {
                try {
                    if (typeof target.node_id === 'string' && target.node_id.startsWith('[')) {
                        targetNodeIds = JSON.parse(target.node_id);
                    } else {
                        targetNodeIds = [target.node_id];
                    }
                } catch (e) {
                    console.error(`解析目标 ${target.id} 节点ID失败:`, e);
                    targetNodeIds = [target.node_id];
                }
            }

            const sidSet = new Set();
            data.forEach(item => {
                if (item.sid) sidSet.add(item.sid);
            });

            if (sidSet.size > 0) {
                sidSet.forEach(sid => {
                    const server = servers.find(s => s.sid === sid);
                    nodes[sid] = server ? { name: server.name, sid: server.sid, metadata: server.data?.metadata || {} } : { name: `节点 ${sid}`, sid: sid };
                });
            } else if (targetNodeIds.length > 0) {
                targetNodeIds.forEach(sid => {
                    const server = servers.find(s => s.sid === sid);
                    nodes[sid] = server ? { name: server.name, sid: server.sid, metadata: server.data?.metadata || {} } : { name: `节点 ${sid}`, sid: sid };
                });
            }

            // 去除目标敏感字段（host/port）
            const targetSanitized = {
                id: target.id,
                region_id: target.region_id,
                name: target.name,
                description: target.description,
                mode: target.mode,
                node_id: target.node_id,
                test_type: target.test_type,
                region_name: target.region_name,
                created_at: target.created_at
            };

            res.json({
                success: true,
                data: {
                    target: targetSanitized,
                    stats: data,
                    nodes: nodes
                }
            });
        } catch (err) {
            console.error('获取TCPing数据失败:', err);
            res.status(500).json({ success: false, message: err.message });
        }
    });

    /**
     * 获取监控数据
     * GET /api/monitor/data?type=<m|h|d|month|archive>&region_id=<region_id>&target_id=<target_id>&detail_level=<normal|detailed>
     */
    router.get('/api/monitor/data', async (req, res) => {
        try {
            const {
                type = 'm', region_id, target_id, detail_level = 'normal',
                node_id, sampling_rate = 1, limit, start_time, end_time // Added start_time, end_time for archive
            } = req.query;

            const setting = await database.setting.all();
            const debugMode = setting.debug === true;

            if (debugMode) {
                console.log(`[TCPing调试] 获取监控数据请求: type=${type}, region_id=${region_id}, target_id=${target_id}, detail_level=${detail_level}, node_id=${node_id}, sampling_rate=${sampling_rate}, limit=${limit}, start_time=${start_time}, end_time=${end_time}`);
            }

            let table;
            let dataLimit = 0;
            const customLimit = limit ? parseInt(limit, 10) : 0;
            let records;
            let duration_seconds; // 声明 duration_seconds

            if (type === 'archive') {
                try {
                    // 验证target_id是否存在
                    if (target_id && target_id !== 'all') {
                        const allTargets = await monitor.targets.getAll();
                        const targetExists = allTargets.some(t => t.id === target_id);
                        if (!targetExists) {
                            return res.status(404).json({
                                success: false,
                                message: `目标ID ${target_id} 不存在。可用的目标ID: ${allTargets.map(t => t.id).join(', ')}`
                            });
                        }
                    }

                    const queryOptions = {};
                    if (target_id) queryOptions.target_id = target_id;
                    if (node_id) queryOptions.sid = node_id;

                    const now = Math.floor(Date.now() / 1000);
                    const time_range = req.query.time_range || '1h'; // Default to 1h if not specified

                    // Define time ranges for archive queries
                    const timeRanges = {
                        '1h': { start_time: now - 1 * 60 * 60, end_time: now, aggregation: 'minute' },
                        '4h': { start_time: now - 4 * 60 * 60, end_time: now, aggregation: 'minute' },
                        '24h': { start_time: now - 24 * 60 * 60, end_time: now, aggregation: 'minute' },
                        '7d': { start_time: now - 7 * 24 * 60 * 60, end_time: now, aggregation: '5min' },
                        '31d': { start_time: now - 31 * 24 * 60 * 60, end_time: now, aggregation: 'day' }
                    };

                    const selectedRange = timeRanges[time_range];
                    if (!selectedRange) {
                         // Fallback to 1h if time_range is invalid or not in predefined list
                        Object.assign(queryOptions, timeRanges['1h']);
                        console.warn(`[TCPing调试] 无效的 time_range: ${time_range}, 回退到 1h`);
                    } else {
                        Object.assign(queryOptions, selectedRange);
                    }

                    // 明确设置詳細的分钟级别数据类型处理
                    if (detail_level === 'detailed' && ['1h', '4h', '24h', '7d'].includes(time_range)) {
                        // 🔧 修改：移除数量限制，改为严格按时间范围过滤
                        // 对于详细级别查询，确保返回指定时间范围内的所有数据
                        console.log(`[TCPing调试] ${time_range}时间范围使用详细数据，按时间范围过滤，不限制记录数量`);
                        
                        // 不设置limit，让selectByTimeRange和query函数根据时间范围自然过滤
                        delete queryOptions.limit;
                    } else {
                        // 对于非详细级别的查询，可以保留适当的限制
                        queryOptions.limit = customLimit > 0 ? Math.min(customLimit, 10000) : 10000;
                    }
                    queryOptions.offset = 0;
                    queryOptions.sort_by = 'created_at';
                    queryOptions.sort_order = 'asc';

                    if (debugMode) console.log(`[TCPing调试] 查询归档数据，参数:`, queryOptions);

                    // 关键修改：为4h、24h时间范围从tcping_m表获取数据，7d从tcping_5m表获取数据
                    let archiveData = [];
                    let dataSource = ''; // 记录数据来源

                    if (['4h', '24h'].includes(time_range)) {
                        // 使用tcping_m表获取短时间范围的分钟级数据
                        dataSource = 'tcping_m';
                        console.log(`[TCPing调试] ${time_range}时间范围使用${dataSource}表获取分钟级数据`);

                        // 计算开始时间（秒级时间戳）
                        const startTime = queryOptions.start_time;
                        const duration_seconds = queryOptions.end_time - queryOptions.start_time;

                        if (!target_id) {
                            // 获取所有目标的数据
                            console.log(`[TCPing调试] 未指定target_id参数，获取所有目标数据`);
                            const allTargets = await monitor.targets.getAll();
                            let allData = [];

                            // 对每个目标进行查询并合并结果
                            for (const target of allTargets) {
                                const targetData = await monitor.tcping_m.selectByTimeRange(target.id, duration_seconds, 0);
                                // 只保留与指定节点相关的数据（如果提供了node_id）
                                if (node_id) {
                                    const filteredData = targetData.filter(item => item.sid === node_id);
                                    if (filteredData.length > 0) {
                                        // 添加target_id和target_name字段，便于前端处理
                                        filteredData.forEach(item => {
                                            item.target_id = target.id;
                                            item.target_name = target.name || target.id;
                                        });
                                        allData = [...allData, ...filteredData];
                                    }
                                } else {
                                    // 添加target_id和target_name字段，便于前端处理
                                    targetData.forEach(item => {
                                        item.target_id = target.id;
                                        item.target_name = target.name || target.id;
                                    });
                                    allData = [...allData, ...targetData];
                                }
                            }

                            archiveData = allData;
                            console.log(`[TCPing调试] 获取到 ${archiveData.length} 条跨目标数据`);
                        } else {
                            // 使用selectByTimeRange函数查询tcping_m表
                            archiveData = await monitor.tcping_m.selectByTimeRange(target_id, duration_seconds, 0);

                            // 确保每条记录都有target_id字段
                            archiveData.forEach(item => {
                                if (!item.target_id) {
                                    item.target_id = target_id;
                                }
                            });

                            // 过滤特定节点的数据（如果提供了node_id）
                            if (node_id) {
                                archiveData = archiveData.filter(item => item.sid === node_id);
                                console.log(`[TCPing调试] 已过滤节点ID为 ${node_id} 的数据，过滤后有 ${archiveData.length} 条记录`);
                            }
                        }
                    } else if (time_range === '7d') {
                        // 🔧 新增：7天时间范围支持颗粒度选择，默认使用小时级数据
                        const granularity = req.query.granularity || 'hour'; // 默认小时级
                        let actualGranularity = granularity;
                        
                        console.log(`[TCPing调试] ${time_range}时间范围，请求颗粒度=${granularity}`);

                        // 计算开始时间（秒级时间戳）
                        const startTime = queryOptions.start_time;
                        const duration_seconds = queryOptions.end_time - queryOptions.start_time;

                        // 根据颗粒度选择数据源
                        switch(granularity) {
                            case 'hour':
                                dataSource = 'tcping_h';
                                console.log(`[TCPing调试] 使用小时级数据(${dataSource}表)，快速加载`);
                                break;
                            case '5min':
                                dataSource = 'tcping_5m';
                                console.log(`[TCPing调试] 使用5分钟级数据(${dataSource}表)，详细监控`);
                                break;
                            case 'minute':
                                dataSource = 'tcping_m';
                                console.log(`[TCPing调试] 使用分钟级数据(${dataSource}表)，最详细分析`);
                                break;
                            default:
                                // 智能选择：默认小时级，大数据量时强制小时级
                                const allTargets = await monitor.targets.getAll();
                                const totalTargets = allTargets.length * (node_id ? 1 : 3); // 估算总数据量
                                if (totalTargets > 5) {
                                    dataSource = 'tcping_h';
                                    actualGranularity = 'hour';
                                    console.log(`[TCPing调试] 智能选择：数据量较大(${totalTargets}个目标)，使用小时级数据`);
                                } else {
                                    dataSource = 'tcping_5m';
                                    actualGranularity = '5min';
                                    console.log(`[TCPing调试] 智能选择：数据量适中(${totalTargets}个目标)，使用5分钟级数据`);
                                }
                        }

                        if (!target_id) {
                            // 获取所有目标的数据
                            console.log(`[TCPing调试] 未指定target_id参数，获取所有目标的${actualGranularity}级数据`);
                            const allTargets = await monitor.targets.getAll();
                            let allData = [];

                            // 对每个目标进行查询并合并结果
                            for (const target of allTargets) {
                                let targetData;
                                
                                // 根据数据源获取数据
                                if (dataSource === 'tcping_h') {
                                    targetData = await monitor.tcping_h.selectByTimeRange(target.id, duration_seconds, 0);
                                } else if (dataSource === 'tcping_5m') {
                                    targetData = await monitor.tcping_5m.selectByTimeRange(target.id, duration_seconds, 0);
                                } else {
                                    targetData = await monitor.tcping_m.selectByTimeRange(target.id, duration_seconds, 0);
                                }
                                
                                // 只保留与指定节点相关的数据（如果提供了node_id）
                                if (node_id) {
                                    const filteredData = targetData.filter(item => item.sid === node_id);
                                    if (filteredData.length > 0) {
                                        // 添加target_id和target_name字段，便于前端处理
                                        filteredData.forEach(item => {
                                            item.target_id = target.id;
                                            item.target_name = target.name || target.id;
                                        });
                                        allData = [...allData, ...filteredData];
                                    }
                                } else {
                                    // 添加target_id和target_name字段，便于前端处理
                                    targetData.forEach(item => {
                                        item.target_id = target.id;
                                        item.target_name = target.name || target.id;
                                    });
                                    allData = [...allData, ...targetData];
                                }
                            }

                            archiveData = allData;
                            console.log(`[TCPing调试] 从${dataSource}表获取到 ${archiveData.length} 条跨目标${actualGranularity}级数据`);
                        } else {
                            // 获取指定目标的数据
                            let targetData;
                            
                            if (dataSource === 'tcping_h') {
                                targetData = await monitor.tcping_h.selectByTimeRange(target_id, duration_seconds, 0);
                            } else if (dataSource === 'tcping_5m') {
                                targetData = await monitor.tcping_5m.selectByTimeRange(target_id, duration_seconds, 0);
                            } else {
                                targetData = await monitor.tcping_m.selectByTimeRange(target_id, duration_seconds, 0);
                            }

                            // 确保每条记录都有target_id字段
                            targetData.forEach(item => {
                                if (!item.target_id) {
                                    item.target_id = target_id;
                                }
                            });

                            // 过滤特定节点的数据（如果提供了node_id）
                            if (node_id) {
                                archiveData = targetData.filter(item => item.sid === node_id);
                                console.log(`[TCPing调试] 已过滤节点ID为 ${node_id} 的数据，过滤后有 ${archiveData.length} 条${actualGranularity}级记录`);
                            } else {
                                archiveData = targetData;
                            }

                            console.log(`[TCPing调试] 从${dataSource}表获取到 ${archiveData.length} 条${actualGranularity}级数据`);
                        }
                    } else if (time_range === '31d') {
                        // 使用天级聚合数据而不是归档数据
                        dataSource = 'tcping_d';
                        console.log(`[TCPing调试] ${time_range}时间范围使用${dataSource}表获取天级聚合数据`);
                        
                        // 计算31天前的时间戳
                        const duration_seconds = queryOptions.end_time - queryOptions.start_time;
                        
                        if (!target_id) {
                            // 获取所有目标的天级数据
                            const allTargets = await monitor.targets.getAll();
                            let allData = [];
                            
                            for (const target of allTargets) {
                                const targetData = await monitor.tcping_d.selectByTimeRange(target.id, duration_seconds, 0);
                                // 过滤节点数据
                                if (node_id) {
                                    const filteredData = targetData.filter(item => item.sid === node_id);
                                    filteredData.forEach(item => {
                                        item.target_id = target.id;
                                        item.target_name = target.name || target.id;
                                    });
                                    allData = [...allData, ...filteredData];
                                } else {
                                    targetData.forEach(item => {
                                        item.target_id = target.id;
                                        item.target_name = target.name || target.id;
                                    });
                                    allData = [...allData, ...targetData];
                                }
                            }
                            
                            archiveData = allData;
                        } else {
                            // 获取指定目标的天级数据
                            archiveData = await monitor.tcping_d.selectByTimeRange(target_id, duration_seconds, 0);
                            
                            // 确保每条记录都有target_id字段
                            archiveData.forEach(item => {
                                if (!item.target_id) {
                                    item.target_id = target_id;
                                }
                            });
                            
                            // 过滤特定节点的数据
                            if (node_id) {
                                archiveData = archiveData.filter(item => item.sid === node_id);
                            }
                        }
                    } else {
                        // 对于1h时间范围继续使用tcping_archive表
                        dataSource = 'tcping_archive';
                        console.log(`[TCPing调试] ${time_range}时间范围使用${dataSource}表获取数据`);
                        archiveData = await monitor.tcping_archive.query(queryOptions);
                    }

                    const stats = await monitor.tcping_archive.getStats(queryOptions);
                    if (debugMode) console.log(`[TCPing调试] 从${dataSource}表查询到 ${archiveData.length} 条归档数据，总记录数: ${stats.count}`);

                    // 分析数据的时间范围
                    if (debugMode || ['4h', '24h', '7d'].includes(time_range)) {
                        // 检查和记录原始数据的时间范围
                        if (archiveData.length > 0) {
                            let oldestRecord = archiveData[0];
                            let newestRecord = archiveData[0];

                            archiveData.forEach(record => {
                                if (record.created_at < oldestRecord.created_at) {
                                    oldestRecord = record;
                                }
                                if (record.created_at > newestRecord.created_at) {
                                    newestRecord = record;
                                }
                            });

                            const oldestTime = new Date(oldestRecord.created_at * 1000);
                            const newestTime = new Date(newestRecord.created_at * 1000);
                            const spanHours = (newestRecord.created_at - oldestRecord.created_at) / 3600;

                            console.log(`[TCPing调试] 数据时间范围: type=archive, 数据来源=${dataSource}, time_range=${time_range}, 记录数=${archiveData.length}, 最早=${oldestTime.toISOString()}, 最新=${newestTime.toISOString()}, 跨度=${spanHours.toFixed(2)}小时`);
                        } else {
                            console.log(`[TCPing调试] 数据时间范围: type=archive, 数据来源=${dataSource}, time_range=${time_range}, 记录数=0`);
                        }
                    }

                    let processedData = archiveData;
                    if (queryOptions.aggregation) {
                        const aggregatedData = {};
                        const aggUnit = queryOptions.aggregation === 'minute' ? 60 : (queryOptions.aggregation === 'hour' ? 3600 : 86400);

                        archiveData.forEach(item => {
                            const timestamp = Math.floor(item.created_at / aggUnit) * aggUnit;
                            const key = `${item.target_id}_${item.sid || 'null'}_${timestamp}`;
                            if (!aggregatedData[key]) {
                                aggregatedData[key] = {
                                    ...item, created_at: timestamp,
                                    success_rates: [item.success_rate], avg_times: [item.avg_time],
                                    min_times: [item.min_time], max_times: [item.max_time], count: 1
                                };
                            } else {
                                aggregatedData[key].success_rates.push(item.success_rate);
                                aggregatedData[key].avg_times.push(item.avg_time);
                                aggregatedData[key].min_times.push(item.min_time);
                                aggregatedData[key].max_times.push(item.max_time);
                                aggregatedData[key].count++;
                            }
                        });
                        processedData = Object.values(aggregatedData).map(item => {
                            // 如果数据包含success_rates、avg_times等数组属性，这意味着多个时间点被合并到一个记录中
                            // 需要将其展开为多个独立的时间点数据
                            if (item.success_rates && item.avg_times && item.min_times && item.max_times && item.count > 1) {
                                const baseTimestamp = item.created_at;
                                const timeInterval = aggUnit / item.count; // 估算时间间隔

                                // 创建多个独立的时间点数据记录
                                return Array.from({length: item.count}, (_, i) => ({
                                    id: item.id ? `${item.id}_${i}` : undefined,
                                    target_id: item.target_id,
                                    target_name: item.target_name,
                                    sid: item.sid,
                                    region_id: item.region_id,
                                    region_name: item.region_name,
                                    success_rate: item.success_rates[i] || item.success_rates[0],
                                    avg_time: item.avg_times[i] || item.avg_times[0],
                                    min_time: item.min_times[i] || item.min_times[0],
                                    max_time: item.max_times[i] || item.max_times[0],
                                    created_at: Math.floor(baseTimestamp + (i * timeInterval)),
                                    year: item.year,
                                    month: item.month,
                                    day: item.day,
                                    hour: item.hour,
                                    minute: (item.minute + i) % 60 // 估算分钟值
                                }));
                            } else {
                                // 对于正常数据或只有一个时间点的情况，返回原始对象
                                return {
                                    ...item,
                                    success_rate: item.success_rates ? (item.success_rates.reduce((s, v) => s + v, 0) / item.count) : item.success_rate,
                                    avg_time: item.avg_times ? Math.round(item.avg_times.reduce((s, v) => s + v, 0) / item.count) : item.avg_time,
                                    min_time: item.min_times ? Math.round(Math.min(...item.min_times)) : item.min_time,
                                    max_time: item.max_times ? Math.round(Math.max(...item.max_times)) : item.max_time,
                                };
                            }
                        });

                        // 展平数组（因为map可能返回嵌套数组）
                        processedData = processedData.flat();

                        if (debugMode) console.log(`[TCPing调试] 对${time_range}数据进行${queryOptions.aggregation}级聚合，原始: ${archiveData.length}，处理后: ${processedData.length}`);
                    }

                    let effectiveSamplingRate = parseInt(sampling_rate);
                    // 对于24小时查询，如果是分钟级别聚合，减少自动采样的可能性
                    if (time_range === '24h' && queryOptions.aggregation === 'minute') {
                        // 只有在数据量非常大的情况下才进行采样
                        if (processedData.length > 1440) { // 24小时 * 60分钟 = 1440个数据点
                            // 将数据量控制在1440左右，不要过度采样
                            effectiveSamplingRate = Math.max(effectiveSamplingRate, Math.ceil(processedData.length / 1440));
                            if (sampling_rate === '1' && effectiveSamplingRate > 1) {
                                // 只有在用户未明确指定采样率（使用默认值）的情况下才输出此警告
                                console.log(`[TCPing调试] 24小时数据量过大(${processedData.length})，自动调整采样率为: ${effectiveSamplingRate}`);
                            }
                        } else {
                            // 如果数据不足1440点，则不采样
                            effectiveSamplingRate = 1;
                            console.log(`[TCPing调试] 24小时数据量(${processedData.length})不足1440点，不进行采样`);
                        }
                    } else if (processedData.length > 1000) {
                        effectiveSamplingRate = Math.max(effectiveSamplingRate, Math.ceil(processedData.length / 1000));
                        if (debugMode) console.log(`[TCPing调试] 数据量过大(${processedData.length})，自动调整采样率为: ${effectiveSamplingRate}`);
                    }

                    let sampledData = processedData;
                    if (effectiveSamplingRate > 1) {
                        sampledData = processedData.filter((_, index) => index % effectiveSamplingRate === 0);
                        if (debugMode) console.log(`[TCPing调试] 对归档数据进行采样，采样率: ${effectiveSamplingRate}，采样前: ${processedData.length}，采样后: ${sampledData.length}`);
                    }

                    const allTargets = await monitor.targets.getAll();
                    const targetMap = {};
                    allTargets.forEach(t => targetMap[t.id] = t);
                    sampledData = sampledData.map(item => {
                        const t = targetMap[item.target_id] || {};
                        return {
                            ...item, 
                            target_name: t.name || item.target_name || item.target_id, 
                            region_id: t.region_id || null, 
                            region_name: t.region_name || null,
                            success_rate: item.success_rate !== null && item.success_rate !== undefined ? item.success_rate : 0
                        };
                    });

                    // 处理完成后记录最终数据的时间范围
                    if (debugMode || ['24h', '7d'].includes(time_range)) {
                        if (sampledData.length > 0) {
                            let oldestRecord = sampledData[0];
                            let newestRecord = sampledData[0];

                            sampledData.forEach(record => {
                                if (record.created_at < oldestRecord.created_at) {
                                    oldestRecord = record;
                                }
                                if (record.created_at > newestRecord.created_at) {
                                    newestRecord = record;
                                }
                            });

                            const oldestTime = new Date(oldestRecord.created_at * 1000);
                            const newestTime = new Date(newestRecord.created_at * 1000);
                            const spanHours = (newestRecord.created_at - oldestRecord.created_at) / 3600;

                            console.log(`[TCPing调试] 最终返回数据时间范围: type=archive, time_range=${time_range}, 采样后记录数=${sampledData.length}, 最早=${oldestTime.toISOString()}, 最新=${newestTime.toISOString()}, 跨度=${spanHours.toFixed(2)}小时`);
                        }
                    }

                    // 发送前记录数据分布情况
                    if (debugMode || ['4h', '24h', '7d'].includes(time_range)) {
                        if (processedData.length > 0) {
                            // 按created_at分组统计数据数量
                            const timeDistribution = {};
                            const targetDistribution = {};

                            processedData.forEach(item => {
                                // 记录时间点分布
                                const timeKey = new Date(item.created_at * 1000).toISOString().slice(0, 16);
                                timeDistribution[timeKey] = (timeDistribution[timeKey] || 0) + 1;

                                // 记录目标分布
                                if (item.target_id) {
                                    targetDistribution[item.target_id] = (targetDistribution[item.target_id] || 0) + 1;
                                }
                            });

                            // 统计不同时间点的数量
                            const uniqueTimePoints = Object.keys(timeDistribution).length;
                            const uniqueTargets = Object.keys(targetDistribution).length;

                            console.log(`[TCPing调试] 数据分布情况: 总记录数=${processedData.length}, 不同时间点数=${uniqueTimePoints}, 目标数=${uniqueTargets}`);

                            // 只在开发模式下打印详细时间点分布
                            if (debugMode) {
                                console.log(`[TCPing调试] 时间点分布:`, Object.keys(timeDistribution).slice(0, 10).map(t => `${t}(${timeDistribution[t]})`).join(', ') + (Object.keys(timeDistribution).length > 10 ? '...' : ''));
                            }
                        }
                    }

                    // 如果指定了target_id，确保只返回该target_id的数据
                    let finalData = processedData;
                    if (target_id && target_id !== 'all') {
                        // 过滤出指定target_id的数据
                        const targetData = processedData.filter(item => item.target_id === target_id);

                        // 如果过滤后的数据不为空，则使用过滤后的数据
                        if (targetData.length > 0) {
                            console.log(`[TCPing调试] 过滤后的target_id=${target_id}数据量: ${targetData.length}条记录`);
                            finalData = targetData;
                        } else {
                            console.log(`[TCPing调试] 警告: 过滤后的target_id=${target_id}数据为空，使用原始数据`);
                        }
                    }

                    return res.json({
                        success: true,
                        data: finalData,
                        stats: {
                            total: stats.count,
                            avg_latency: Math.round(stats.avg_latency || 0),
                            min_latency: Math.round(stats.min_latency || 0),
                            max_latency: Math.round(stats.max_latency || 0),
                            avg_success_rate: stats.avg_success_rate,
                            oldest_record: stats.oldest_record,
                            newest_record: stats.newest_record
                        },
                        metadata: {
                            granularity: time_range === '7d' ? (req.query.granularity || 'hour') : 'auto',
                            dataSource: dataSource,
                            actualDataPoints: finalData.length,
                            performanceLevel: finalData.length > 3000 ? 'heavy' : finalData.length > 1000 ? 'medium' : 'light'
                        },
                        time_range: time_range,
                        sampling_rate: effectiveSamplingRate
                    });

                } catch (err) {
                    console.error('查询归档数据失败:', err);
                    return res.status(500).json({ success: false, message: '查询归档数据失败: ' + err.message });
                }
            } else {
                // Handle non-archive types
                switch (type) {
                    case 'm':
                        table = monitor.tcping_m;
                        dataLimit = customLimit > 0 ? Math.min(customLimit, 1440) : 0; // 1天分钟数据
                        duration_seconds = 24 * 60 * 60; // 默认查询1天
                        if (customLimit > 0 && customLimit <= 240) { // 如果limit是4小时内的分钟数
                            duration_seconds = customLimit * 60; // 暂时这样处理，后续可优化
                        } else if (req.query.time_range === '4h') { // 兼容旧的 echarts time_range 参数
                            duration_seconds = 4 * 60 * 60;
                        }
                        break;
                    case 'h':
                        if (detail_level === 'detailed') {
                            table = monitor.tcping_m;
                            duration_seconds = 4 * 3600; // 固定为4小时的秒数
                            dataLimit = 0; // 移除基于 req.query.limit 的数量限制，因为时长已固定
                            if (debugMode) {
                                console.log(`[TCPing调试] type=h, detail_level=detailed. duration_seconds forced to 4 hours (${duration_seconds}s). dataLimit set to 0 (unlimited for the duration).`);
                            }
                        } else {
                            table = monitor.tcping_h;
                            dataLimit = customLimit > 0 ? Math.min(customLimit, 720) : 0; // 30天小时数据
                            duration_seconds = 30 * 24 * 60 * 60; // 默认查询30天
                        }
                        break;
                    case 'd':
                        if (detail_level === 'detailed') {
                            table = monitor.tcping_h; // 天的详细数据来自小时表
                            duration_seconds = 7 * 24 * 3600; // 例如，查询最近7天的小时数据
                            dataLimit = 0; // 移除数量限制
                             if (debugMode) {
                                console.log(`[TCPing调试] type=d, detail_level=detailed. duration_seconds set to 7 days (${duration_seconds}s). dataLimit set to 0 (unlimited for the duration).`);
                            }
                        } else {
                            table = monitor.tcping_d;
                            dataLimit = customLimit > 0 ? Math.min(customLimit, 365) : 0; // 1年天数据
                            duration_seconds = 365 * 24 * 60 * 60; // 默认查询1年
                        }
                        break;
                    case 'month':
                        table = monitor.tcping_month;
                        dataLimit = customLimit > 0 ? Math.min(customLimit, 120) : 0; // 10年月数据
                        duration_seconds = 10 * 365 * 24 * 60 * 60; // 默认查询10年
                        break;
                    default:
                        return res.status(400).json({ success: false, message: '无效的数据类型' });
                }

                if (target_id) {
                    // 确保 duration_seconds 有值，如果前面分支没有设置，则给一个默认值
                    if (duration_seconds === undefined) {
                         duration_seconds = 24 * 60 * 60; // 默认查询24小时
                         if (debugMode) console.log(`[TCPing调试] duration_seconds was undefined, set to default 24 hours.`);
                    }
                    records = await table.selectByTimeRange(target_id, duration_seconds, dataLimit);
                    if (debugMode) {
                        console.log(`[TCPing调试] Fetched ${records ? records.length : 0} records from ${table === monitor.tcping_m ? 'tcping_m' : (table === monitor.tcping_h ? 'tcping_h' : (table === monitor.tcping_d ? 'tcping_d' : 'tcping_month'))} for target_id: ${target_id}, duration: ${duration_seconds}s, dataLimit: ${dataLimit}`);
                    }
                } else if (region_id) {
                    // Region-wide data fetching might need a different approach or be unsupported for non-archive
                    console.warn('[TCPing调试] Region-wide data request for non-archive type is not fully implemented here.');
                    records = [];
                } else {
                    // Neither target_id nor region_id provided for non-archive type
                     console.warn('[TCPing调试] Neither target_id nor region_id provided for non-archive type.');
                    records = [];
                }
            }

            // 如果数据为空或者长度为0，不需要额外处理
            if (!records || records.length === 0) {
                console.log(`[TCPing调试] 未找到符合条件的数据: type=${type}, detail_level=${detail_level}, target_id=${target_id}`);
                res.json({ success: true, data: [] });
                return;
            }

            // 分析数据的时间范围
            if (debugMode || (type === 'h' && detail_level === 'detailed')) {
                const oldestRecord = records[records.length - 1]; // ASC排序后，最后一条是最旧的
                const newestRecord = records[0]; // ASC排序后，第一条是最新的

                if (oldestRecord && newestRecord) {
                    const oldestTime = new Date(oldestRecord.created_at * 1000);
                    const newestTime = new Date(newestRecord.created_at * 1000);
                    const spanHours = (newestRecord.created_at - oldestRecord.created_at) / 3600;

                    console.log(`[TCPing调试] 返回数据时间范围统计: type=${type}, detail_level=${detail_level}, 记录数=${records.length}, 最早=${oldestTime.toISOString()}, 最新=${newestTime.toISOString()}, 跨度=${spanHours.toFixed(2)}小时`);
                }
            }

            if (debugMode) {
                console.log(`[TCPing调试] 返回监控数据: ${records ? records.length : 0}条记录, detail_level=${detail_level}, type=${type}`);
                if (records && records.length > 0) console.log(`[TCPing调试] 数据样例:`, JSON.stringify(records[0], null, 2));
            }
            res.json({ success: true, data: records });
        } catch (err) {
            console.error('获取监控数据失败:', err);
            res.status(500).json({ success: false, message: err.message });
        }
    });

    /**
     * 批量获取多个服务器的网络质量历史数据
     * POST /api/monitor/batch-network-data
     * Body: { serverIds: string[], timeRange: string }
     */
    router.post('/api/monitor/batch-network-data', async (req, res) => {
        try {
            const { serverIds = [], timeRange = '24h' } = req.body;
            const setting = await database.setting.all();
            const debugMode = setting.debug === true;

            if (!Array.isArray(serverIds) || serverIds.length === 0) {
                return res.status(400).json({ 
                    success: false, 
                    message: '请提供有效的服务器ID列表' 
                });
            }

            // 生成缓存键（使用排序后的serverIds，提升缓存命中率）
            const cacheKey = `batch_network_${[...serverIds].sort().join('_')}_${timeRange}`;
            
            // 检查缓存
            const cachedData = getCachedNetworkData(cacheKey);
            if (cachedData) {
                if (debugMode) {
                    console.log(`[批量网络数据] 缓存命中：key=${cacheKey}`);
                }
                return res.json(cachedData);
            }

            if (debugMode) {
                console.log(`[批量网络数据] 缓存未命中，查询数据库：服务器数量=${serverIds.length}, 时间范围=${timeRange}`);
            }

            // 获取所有监控目标
            const allTargets = await monitor.targets.getAll();
            const result = {};

            // 分批并发处理，避免长时间占用事件循环，影响其他连接/WS
            const CONCURRENCY = 5;
            const duration_seconds = timeRange === '24h' ? 24 * 3600 :
                                       timeRange === '7d' ? 7 * 24 * 3600 :
                                       timeRange === '30d' ? 30 * 24 * 3600 : 24 * 3600;

            for (let i = 0; i < serverIds.length; i += CONCURRENCY) {
                const batch = serverIds.slice(i, i + CONCURRENCY);
                await Promise.all(batch.map(async (sid) => {
                    try {
                        // 查找该服务器监控的所有目标（使用与请求时间范围一致的查询）
                        const serverTargets = [];
                        for (const target of allTargets) {
                            const tcpingData = await monitor.tcping_h.selectByTimeRangeAndNode(target.id, sid, duration_seconds);
                            if (tcpingData && tcpingData.length > 0) {
                                serverTargets.push(target);
                            }
                        }

                        if (serverTargets.length === 0) {
                            return; // 该服务器没有监控数据，跳过
                        }

                        // 收集该服务器的所有监控数据
                        const serverData = [];
                        for (const target of serverTargets) {
                            let targetData;
                            // 根据时间范围选择合适的数据源（24h 使用小时级）
                            if (timeRange === '24h') {
                                targetData = await monitor.tcping_h.selectByTimeRangeAndNode(target.id, sid, duration_seconds);
                            } else if (timeRange === '7d') {
                                targetData = await monitor.tcping_h.selectByTimeRangeAndNode(target.id, sid, duration_seconds);
                            } else {
                                // 更长时间（30天）使用天级数据
                                targetData = await monitor.tcping_d.selectByTimeRangeAndNode(target.id, sid, duration_seconds);
                            }

                            if (targetData && targetData.length > 0) {
                                // 添加必要的目标信息（去除敏感字段）
                                targetData.forEach(item => {
                                    item.target_id = target.id;
                                    item.target_name = target.name;
                                });
                                serverData.push(...targetData);
                            }
                        }

                        if (serverData.length > 0) {
                            // 按时间排序
                            serverData.sort((a, b) => a.created_at - b.created_at);
                            result[sid] = serverData;
                        }
                    } catch (error) {
                        console.error(`获取服务器 ${sid} 网络数据失败:`, error);
                        // 继续处理其他服务器
                    }
                }));

                // 主动让出事件循环，避免阻塞其他连接/WS握手
                await new Promise(resolve => setImmediate(resolve));
            }

            if (debugMode) {
                const serverCount = Object.keys(result).length;
                const totalRecords = Object.values(result).reduce((sum, data) => sum + data.length, 0);
                console.log(`[批量网络数据] 返回数据：有效服务器=${serverCount}, 总记录数=${totalRecords}`);
            }

            // 构建响应数据
            const responseData = { 
                success: true, 
                data: result,
                summary: {
                    requestedServers: serverIds.length,
                    serversWithData: Object.keys(result).length,
                    timeRange: timeRange
                }
            };

            // 存入缓存
            setCachedNetworkData(cacheKey, responseData);
            
            if (debugMode) {
                console.log(`[批量网络数据] 数据已缓存：key=${cacheKey}`);
            }

            res.json(responseData);
        } catch (err) {
            console.error('批量获取网络数据失败:', err);
            res.status(500).json({ 
                success: false, 
                message: '批量获取网络数据失败: ' + err.message 
            });
        }
    });

    /**
     * 获取节点冷却状态
     * GET /api/monitor/cooling_status
     */
    router.get('/api/monitor/cooling_status', async (req, res) => {
        try {
            const coolingStatus = getNodeCoolingStatus();
            const servers = await database.getServers();
            const serversMap = {};
            servers.forEach(server => {
                serversMap[server.sid] = { name: server.name };
            });

            const responseData = {};
            for (const sid in coolingStatus) {
                responseData[sid] = {
                    ...coolingStatus[sid],
                    name: serversMap[sid] ? serversMap[sid].name : 'Unknown'
                };
            }
            res.json({ success: true, data: responseData });
        } catch (err) {
            console.error('获取节点冷却状态失败:', err);
            res.status(500).json({ success: false, message: '获取节点冷却状态失败: ' + err.message });
        }
    });

    /**
     * 清理归档数据
     * DELETE /api/monitor/archive?node_id=<node_id>&before_time=<timestamp>
     */
    router.delete('/api/monitor/archive', async (req, res) => {
        try {
            const { node_id, before_time } = req.query;
            if (!node_id && !before_time) {
                return res.status(400).json({ success: false, message: '必须提供node_id或before_time参数' });
            }
            const setting = await database.setting.all();
            const debugMode = setting.debug === true;
            const cleanupOptions = {};
            if (node_id) cleanupOptions.sid = node_id;
            if (before_time) cleanupOptions.before_time = parseInt(before_time);

            if (debugMode) console.log(`[TCPing调试] 清理归档数据，参数:`, cleanupOptions);
            const changes = monitor.tcping_archive.cleanup(cleanupOptions);
            if (debugMode) console.log(`[TCPing调试] 清理归档数据完成，删除了 ${changes} 条记录`);
            res.json({ success: true, message: `已清理 ${changes} 条归档数据` });
        } catch (err) {
            console.error('清理归档数据失败:', err);
            res.status(500).json({ success: false, message: err.message });
        }
    });

    /**
     * 获取归档数据统计信息
     * GET /api/monitor/archive/stats?node_id=<node_id>&target_id=<target_id>&start_time=<timestamp>&end_time=<timestamp>
     */
    router.get('/api/monitor/archive/stats', async (req, res) => {
        try {
            const { node_id, target_id, start_time, end_time } = req.query;
            const setting = await database.setting.all();
            const debugMode = setting.debug === true;
            const queryOptions = {};
            if (node_id) queryOptions.sid = node_id;
            if (target_id) queryOptions.target_id = target_id;
            if (start_time) queryOptions.start_time = parseInt(start_time);
            if (end_time) queryOptions.end_time = parseInt(end_time);

            if (debugMode) console.log(`[TCPing调试] 获取归档数据统计信息，参数:`, queryOptions);
            const stats = monitor.tcping_archive.getStats(queryOptions);
            if (debugMode) console.log(`[TCPing调试] 获取归档数据统计信息完成:`, stats);
            res.json({ success: true, data: stats });
        } catch (err) {
            console.error('获取归档数据统计信息失败:', err);
            res.status(500).json({ success: false, message: err.message });
        }
    });

    return router;
};
