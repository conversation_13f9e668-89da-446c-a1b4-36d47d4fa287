"use strict";
const express = require("express");

module.exports = (svr) => {
    const { db, pr } = svr.locals;
    const router = express.Router();

    // 版本信息API
    router.get('/api/version', (req, res) => {
        try {
            const fs = require('fs');
            const path = require('path');
            
            // 读取 package.json 获取版本信息
            const packagePath = path.join(__dirname, '../../package.json');
            const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            
            // 尝试读取构建时生成的版本信息
            const versionInfoPath = path.join(__dirname, '../../static/version.json');
            let buildInfo = null;
            
            try {
                if (fs.existsSync(versionInfoPath)) {
                    buildInfo = JSON.parse(fs.readFileSync(versionInfoPath, 'utf8'));
                }
            } catch (error) {
                console.warn('无法读取构建版本信息:', error.message);
            }
            
            // 组装版本信息
            const versionInfo = {
                version: packageData.version,
                name: packageData.name,
                description: packageData.description,
                buildTime: buildInfo?.buildTime || new Date().toISOString(),
                buildDate: buildInfo?.buildDate || new Date().toLocaleDateString('zh-CN'),
                format: buildInfo?.format || 'YY.MM.BUILD_NUMBER',
                success: true
            };
            
            res.json(versionInfo);
        } catch (error) {
            console.error('[API] 获取版本信息失败:', error);
            res.status(500).json({
                success: false,
                error: '获取版本信息失败',
                message: error.message
            });
        }
    });

    // 客户端下载链接前缀API
    router.get('/api/client/download-prefix', async (req, res) => {
        try {
            // 获取设置中的neko_status_url
            const setting = await db.setting.all();
            const nekoStatusUrl = setting.neko_status_url || '"https://github.com/fev125/dstatus/releases/download/v1.1"';

            // 返回下载链接前缀
            res.json({
                success: true,
                url: nekoStatusUrl
            });
        } catch (error) {
            console.error('[API] 获取客户端下载链接前缀失败:', error);
            res.status(500).json({
                success: false,
                message: '获取客户端下载链接前缀失败'
            });
        }
    });
    
    // 数据库连接池状态API
    router.get('/api/db-pool-status', async (req, res) => {
        try {
            const dbConfig = require('../../database/config');
            const type = dbConfig.type;
            
            if (type !== 'postgresql') {
                return res.json({
                    success: true,
                    type: type,
                    message: 'SQLite不使用连接池',
                    status: 'N/A'
                });
            }
            
            // 获取PostgreSQL连接池状态
            if (db.adapter && db.adapter.getPoolStatus) {
                const poolStatus = db.adapter.getPoolStatus();
                res.json({
                    success: true,
                    type: 'postgresql',
                    pool: poolStatus,
                    timestamp: new Date().toISOString()
                });
            } else {
                res.json({
                    success: false,
                    message: '无法获取连接池状态'
                });
            }
        } catch (error) {
            console.error('[API] 获取连接池状态失败:', error);
            res.status(500).json({
                success: false,
                error: '获取连接池状态失败',
                message: error.message
            });
        }
    });



    // 注册路由
    svr.use(router);

    // 加载TCPing模块
    require('./tcping')(svr, db);

    // 加载Ping模块
    require('./ping')(svr, db);

    // 加载性能监控API模块
    require('./performance')(svr, db);

    // 加载最新数据API模块
    require('./latest')(svr, db);

    // 加载所有节点状态API模块
    require('./allnode_status')(svr, db);

    // 加载负载数据API模块
    require('./load')(svr, db);

    // 加载负载归档数据API模块
    require('./load-archive')(svr, db);

    // 加载日志控制API模块
    require('./log-control')(svr);

    // 加载数据保留设置API模块
    require('./data-retention')(svr, db);

    // 加载网络质量监控API模块
    try {
        const networkQualityRoutes = require('./api_routes/network_quality_routes')(svr, db.monitor);
        svr.use(networkQualityRoutes);
        console.log('[API] 网络质量监控API模块已加载');
    } catch (error) {
        console.error('[API] 加载网络质量监控API模块失败:', error);
    }

    // 加载Admin监控配置API（完整字段，需鉴权）
    try {
        const adminMonitorRoutes = require('./api_routes/admin_monitor_routes')(svr, db, db.monitor);
        svr.use(adminMonitorRoutes);
        console.log('[API] Admin监控配置API模块已加载');
    } catch (error) {
        console.error('[API] 加载Admin监控配置API模块失败:', error);
    }

    console.log('[API] API模块已加载');
};
