'use strict';

const fetch = require('node-fetch');
const { performTcping } = require('./tcping_core');
const { isNodeCooling, updateNodeCoolingStatus, nodeCoolingStatus: coolingStatusData } = require('./cooling_logic'); // Renamed import

/**
 * 为测试添加超时控制
 * @param {Function} testFunction - 测试函数
 * @param {number} timeout - 超时时间(毫秒)
 * @returns {Promise<Object>} 测试结果或超时结果
 */
async function executeTestWithTimeout(testFunction, timeout = 10000) {
    const testPromise = testFunction();
    const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Test timeout')), timeout)
    );

    try {
        return await Promise.race([testPromise, timeoutPromise]);
    } catch (error) {
        console.error(`测试超时或失败: ${error.message}`);
        // 返回一个表示失败的结果
        return {
            success_rate: 0,
            avg_time: 0,
            min_time: 0,
            max_time: 0
        };
    }
}

/**
 * 保存测试结果到各种统计数据中
 * @param {string} targetId - 目标ID
 * @param {Object} result - 测试结果
 * @param {Object} database - Database instance
 * @param {Object} monitor - Monitor instance
 */
async function saveTestResult(targetId, result, database, monitor) {
    // 获取调试模式
    const setting = database.setting.all();
    const debugMode = setting.debug === true;

    // 准备保存的数据
    const data = {
        success_rate: result.success_rate,
        avg_time: result.avg_time,
        min_time: result.min_time,
        max_time: result.max_time,
        sid: result.sid  // 保存节点ID
    };

    // 创建复合键，用于防止同一目标和节点在短时间内重复写入
    const compositeKey = `${targetId}_${data.sid || 'null'}`;
    const now = new Date();
    const currentTime = now.getTime();
    const currentMinuteKey = Math.floor(currentTime / 60000); // 当前分钟的时间戳（毫秒除以60000）
    
    // 检查是否已经在当前分钟内写入过数据
    if (!global.tcpingLastWriteTime) {
        global.tcpingLastWriteTime = {};
    }

    const lastWrite = global.tcpingLastWriteTime[compositeKey];
    const lastMinuteKey = lastWrite ? Math.floor(lastWrite / 60000) : 0;

    // 如果在当前分钟内已经写入过数据，则跳过
    if (lastWrite && lastMinuteKey === currentMinuteKey) {
        if (debugMode) {
            const lastTime = new Date(lastWrite).toISOString();
            const currentTimeStr = now.toISOString();
            console.log(`[TCPing调试] 跳过在同一分钟内的重复写入: ` +
                `targetId=${targetId}, sid=${data.sid}, ` +
                `当前时间=${currentTimeStr}, 上次写入=${lastTime}, ` +
                `间隔=${Math.floor((currentTime - lastWrite) / 1000)}秒`);
        }
        return data;
    }

    // 更新最后写入时间
    global.tcpingLastWriteTime[compositeKey] = currentTime;

    // 保存到分钟级数据
    try {
        const shiftResult = monitor.tcping_m.shift(targetId, data);
        if (debugMode) {
            console.log(`[TCPing调试] 保存到tcping_m表${shiftResult ? '成功' : '失败'}: ` +
                `targetId=${targetId}, sid=${data.sid}, ` +
                `success_rate=${data.success_rate}, avg_time=${data.avg_time}ms, ` +
                `min_time=${data.min_time}ms, max_time=${data.max_time}ms, ` +
                `时间=${now.toISOString()}`);
        }
    } catch (err) {
        console.error(`保存数据到tcping_m表失败: targetId=${targetId}, sid=${data.sid}, 错误:`, err);
    }

    // 保存到归档表（双轨保存）
    try {
        const archiveResult = monitor.tcping_archive.insert(targetId, data);
        if (debugMode && archiveResult) {
            console.log(`[TCPing调试] 成功保存数据到归档表: targetId=${targetId}, sid=${data.sid}`);
        } else if (debugMode) {
            console.warn(`[TCPing调试] 保存数据到归档表失败: targetId=${targetId}, sid=${data.sid}`);
        }
    } catch (err) {
        console.error('保存数据到归档表失败:', err);
        // 继续执行，不影响现有功能
    }

    return data;
}


/**
 * 在远程节点上执行测试
 * @param {Object} server - 服务器节点
 * @param {Object} target - 监控目标
 * @param {string} testType - 测试类型 ('tcping' 或 'ping')
 * @param {Object} database - Database instance
 * @param {Object} monitor - Monitor instance
 */
async function executeRemoteTest(server, target, testType = 'tcping', database, monitor) {
    const setting = database.setting.all();
    const debugMode = setting.debug === true;

    if (isNodeCooling(server.sid)) {
        if (debugMode) {
            console.log(`[TCPing调试] 节点 ${server.name} (${server.sid}) 处于冷却状态，跳过测试`);
        }
        const coolingResult = {
            success_rate: 0, avg_time: 0, min_time: 0, max_time: 0,
            sid: server.sid, cooling: true
        };
        await saveTestResult(target.id, coolingResult, database, monitor);
        return;
    }

    const MAX_RETRY_COUNT = 3;
    const RETRY_DELAY = 1000;
    let retryCount = 0;

    try {
        const serverHost = server.data?.host || server.data?.api?.host || server.data?.ssh?.host || server.ip;
        const serverPort = server.data?.api?.port || 8080;
        const apiKey = server.data?.api?.key || '';

        if (!serverHost) throw new Error(`无法获取服务器 ${server.name} 的主机地址`);

        let url;
        if (testType === 'ping') {
            url = `http://${serverHost}:${serverPort}/ping?key=${apiKey}&host=${target.host}&count=8&timeout=5`;
        } else {
            url = `http://${serverHost}:${serverPort}/tcping?key=${apiKey}&host=${target.host}&port=${target.port}&count=8&timeout=5`;
        }

        if (debugMode) {
            console.log(`[TCPing调试] 通过远程节点执行${testType}测试:`);
            console.log(`[TCPing调试] - 节点: ${server.name} (${server.sid})`);
            console.log(`[TCPing调试] - 目标: ${target.name} (${target.host}${testType === 'tcping' ? ':' + target.port : ''})`);
            console.log(`[TCPing调试] - 请求URL: ${url}`);
        }

        while (retryCount < MAX_RETRY_COUNT) {
            try {
                const requestStart = Date.now();
                const response = await fetch(url, { timeout: 15000 });
                const data = await response.json();
                const requestTime = Date.now() - requestStart;

                if (debugMode) {
                    console.log(`[TCPing调试] 远程测试请求耗时: ${requestTime}ms`);
                    console.log(`[TCPing调试] 远程测试结果:`, JSON.stringify(data, null, 2));
                }

                if (data.success) {
                    let resultDataToSave;
                    if (testType === 'ping') {
                        if (data.data) {
                            const pingData = data.data;
                            let sr = pingData.success_rate;
                            if (typeof sr === 'undefined') {
                                if (typeof pingData.loss_rate !== 'undefined') sr = 1 - pingData.loss_rate;
                                else if (typeof pingData.successful !== 'undefined' && typeof pingData.count !== 'undefined' && pingData.count > 0) sr = pingData.successful / pingData.count;
                                else sr = 1;
                            }
                            resultDataToSave = { ...pingData, success_rate: sr, sid: server.sid };
                        }
                    } else { // tcping
                        if (data.data && typeof data.data.success_rate !== 'undefined') {
                            if (data.data.success_rate === 0 && debugMode) {
                                console.log(`[TCPing调试] 从节点 ${server.name} 获取的数据显示成功率为0，可能表示连接失败`);
                            }
                            resultDataToSave = { ...data.data, sid: server.sid };
                            // If success_rate is 0, ensure times are 0 for consistency
                            if (resultDataToSave.success_rate === 0) {
                                resultDataToSave.avg_time = 0;
                                resultDataToSave.min_time = 0;
                                resultDataToSave.max_time = 0;
                            }
                        }
                    }

                    if (resultDataToSave) {
                        await saveTestResult(target.id, resultDataToSave, database, monitor);
                        updateNodeCoolingStatus(server.sid, true);
                        if (debugMode) console.log(`[TCPing调试] 从节点 ${server.name} 获取 ${target.name} 的监控数据成功`);
                        return;
                    }
                    console.error(`节点 ${server.name} 返回数据格式异常`);
                    if (debugMode) console.log(`[调试] 节点 ${server.name} 返回的数据:`, JSON.stringify(data, null, 2));
                } else {
                    console.error(`节点 ${server.name} 测试失败: ${data.message}`);
                }
            } catch (error) {
                console.error(`从节点 ${server.name} 请求测试失败: ${error.message}`);
            }
            retryCount++;
            if (retryCount < MAX_RETRY_COUNT) {
                if (debugMode) console.log(`[TCPing调试] 第${retryCount}次重试...`);
                await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
            }
        }

        if (debugMode) console.log(`[TCPing调试] 在${MAX_RETRY_COUNT}次尝试后远程测试仍然失败，记录为超时`);
        const timeoutResult = { success_rate: 0, avg_time: 0, min_time: 0, max_time: 0, sid: server.sid };
        await saveTestResult(target.id, timeoutResult, database, monitor);
        updateNodeCoolingStatus(server.sid, false);

    } catch (err) {
        console.error(`执行远程测试失败:`, err);
        if (debugMode) console.log(`[TCPing调试] 执行远程测试出错: ${err.message}, 记录为超时`);
        const errorResult = { success_rate: 0, avg_time: 0, min_time: 0, max_time: 0, sid: server.sid };
        await saveTestResult(target.id, errorResult, database, monitor);
        updateNodeCoolingStatus(server.sid, false);
    }
}

/**
 * 执行监控目标的TCPing测试并保存结果
 * 此函数应由定时任务调用
 * @param {Object} app - Express app instance
 * @param {Object} database - Database instance
 * @param {Object} monitor - Monitor instance
 */
async function scheduleTcpingCheck(app, database, monitor) {
    try {
        const startTime = Date.now();
        const targets = monitor.targets.getAll();
        const allServers = database.getServers().filter(server => !server.disabled);
        const svr = app.locals.svr || app;
        const statsModule = svr.locals.stats;
        const statsData = statsModule ? statsModule.getStatsData(true) : {};

        const onlineServers = allServers.filter(server => {
            const isOnline = statsData[server.sid] && statsData[server.sid].stat !== false && !(statsData[server.sid].stat && statsData[server.sid].stat.offline);
            const cooling = isNodeCooling(server.sid);
            return isOnline && !cooling;
        });

        const setting = database.setting.all();
        const debugMode = setting.debug === true;

        if (debugMode) {
            console.log(`[监控调试] 开始并行测试 ${targets.length} 个监控目标，可用节点: ${onlineServers.length}/${allServers.length}`);
            const coolingNodes = allServers.filter(server => isNodeCooling(server.sid));
            if (coolingNodes.length > 0) {
                console.log(`[TCPing调试] ${coolingNodes.length} 个节点处于冷却状态:`);
                coolingNodes.forEach(server => {
                    const status = coolingStatusData[server.sid]; // Use imported coolingStatusData
                    if (status) { // Check if status exists
                        console.log(`  - ${server.name} (${server.sid}): 失败次数=${status.failCount}, 冷却结束时间=${new Date(status.coolUntil).toLocaleString()}`);
                    }
                });
            }
        }

        const testPromises = targets.map(async (target) => {
            try {
                const testType = target.test_type || 'tcping';
                switch (target.mode) {
                    case 'local':
                        let localResult;
                        if (testType === 'ping') {
                            const pingModule = require('./ping')(svr, database); // Assuming ping module is in the same directory or adjust path
                            localResult = await executeTestWithTimeout(() => pingModule.performPing(target.host, 8, 5), 20000);
                        } else {
                            localResult = await executeTestWithTimeout(() => performTcping(target.host, target.port, 8, 5, database), 20000);
                        }
                        await saveTestResult(target.id, localResult, database, monitor);
                        break;
                    case 'specific':
                        if (target.node_id) {
                            let nodeIds = [];
                            try {
                                if (typeof target.node_id === 'string' && target.node_id.startsWith('[')) nodeIds = JSON.parse(target.node_id);
                                else nodeIds = [target.node_id];
                            } catch (e) {
                                console.error(`解析节点ID失败: ${e.message}，使用单个节点ID`);
                                nodeIds = [target.node_id];
                            }
                            if (debugMode) console.log(`[TCPing调试] 监控目标 ${target.id} 有 ${nodeIds.length} 个节点进行测试`);

                            let validNodeFound = false;
                            const nodeTestPromises = nodeIds.map(async (nodeId) => {
                                const specificServer = onlineServers.find(s => s.sid === nodeId); // Use onlineServers
                                if (specificServer && specificServer.data?.api?.key) {
                                    await executeTestWithTimeout(() => executeRemoteTest(specificServer, target, testType, database, monitor), 20000);
                                    return true;
                                } else {
                                    if (debugMode && !onlineServers.find(s => s.sid === nodeId)) console.error(`监控目标 ${target.id} 指定的节点 ${nodeId} 不在线或冷却中`);
                                    else if (debugMode) console.error(`监控目标 ${target.id} 指定的节点 ${nodeId} 无API配置或已禁用`);
                                    return false;
                                }
                            });
                            const nodeResults = await Promise.all(nodeTestPromises);
                            validNodeFound = nodeResults.some(r => r === true);

                            if (!validNodeFound) {
                                console.error(`监控目标 ${target.id} 所有指定节点都不可用，记录为不可用状态`);
                                const unavailableResult = { success_rate: 0, avg_time: 0, min_time: 0, max_time: 0, sid: 'unavailable' };
                                await saveTestResult(target.id, unavailableResult, database, monitor);
                            }
                        } else {
                            console.error(`监控目标 ${target.id} 模式为specific但未指定节点ID`);
                            const configErrorResult = { success_rate: 0, avg_time: 0, min_time: 0, max_time: 0, sid: 'config_error' };
                            await saveTestResult(target.id, configErrorResult, database, monitor);
                        }
                        break;
                    case 'auto':
                    default:
                        const region = monitor.regions.get(target.region_id);
                        if (!region) {
                            console.error(`监控目标 ${target.id} (${target.name}) 无法找到所属地区`);
                            const regionErrorResult = { success_rate: 0, avg_time: 0, min_time: 0, max_time: 0, sid: 'region_error' };
                            await saveTestResult(target.id, regionErrorResult, database, monitor);
                            return;
                        }
                        const regionServers = onlineServers.filter(s => s.data?.metadata?.region === region.name && s.data?.api?.key);
                        const availableServers = regionServers.length > 0 ? regionServers : onlineServers.filter(s => s.data?.api?.key);

                        if (availableServers.length === 0) {
                            console.error(`监控目标 ${target.id} (${target.name}) 没有可用的监控节点`);
                            const noServerResult = { success_rate: 0, avg_time: 0, min_time: 0, max_time: 0, sid: 'no_servers' };
                            await saveTestResult(target.id, noServerResult, database, monitor);
                        } else {
                            const randomServer = availableServers[Math.floor(Math.random() * availableServers.length)];
                            await executeTestWithTimeout(() => executeRemoteTest(randomServer, target, testType, database, monitor), 20000);
                        }
                        break;
                }
            } catch (err) {
                console.error(`监控目标 ${target.id} (${target.name}) ${testType}测试失败:`, err);
            }
        });
        await Promise.all(testPromises);
        const endTime = Date.now();
        if (debugMode) console.log(`[TCPing调试] 完成所有 ${targets.length} 个监控目标的测试，总耗时: ${endTime - startTime}ms`);
    } catch (err) {
        console.error('调度TCPing检查任务失败:', err);
    }
}

function startScheduler(app, database, monitor) {
    // 设置定时任务，每分钟执行一次
    setInterval(() => scheduleTcpingCheck(app, database, monitor), 60000);

    // ========== 修复：多级聚合与清理入口 ==========
    // 正确的聚合任务（每分钟检查一次，内部判断是否到聚合点）
    setInterval(() => {
        const now = new Date();
        const minute = now.getMinutes();
        const hour = now.getHours();
        const day = now.getDate();
        const month = now.getMonth() + 1;

        // 🔧 修复：小时聚合（每小时执行一次，从分钟级数据聚合到小时级）
        if (minute === 0) {
            try {
                const targets = monitor.targets.getAll();
                targets.forEach(target => {
                    // 获取过去1小时的分钟级数据（60分钟）
                    const hourData = monitor.tcping_m.selectByTimeRange(target.id, 3600); // 3600秒=1小时
                    if (hourData.length === 0) return;
                    
                    // 🔧 优化：过滤异常数据
                    const valid = hourData.filter(x => {
                        // 基本数据类型检查
                        if (typeof x.success_rate !== 'number') return false;
                        
                        // 过滤异常情况：
                        // 1. 延迟为0且成功率低于50%的数据（通常是超时或网络异常）
                        // 2. 延迟为0且成功率为0的数据（完全无响应）
                        if (x.avg_time === 0 && x.success_rate < 0.5) {
                            return false;
                        }
                        
                        // 保留有效数据：
                        // 1. 有正常延迟的数据
                        // 2. 延迟为0但成功率较高的数据（可能是本地回环或极快响应）
                        return true;
                    });
                    
                    if (valid.length === 0) {
                        // 如果没有有效数据，检查是否所有数据都是异常的
                        const totalData = hourData.filter(x => typeof x.success_rate === 'number');
                        if (totalData.length > 0) {
                            // 有数据但都是异常的，记录一个表示网络问题的状态
                            const agg = {
                                success_rate: 0,
                                avg_time: 0,
                                min_time: 0,
                                max_time: 0,
                                sid: `network_issue_${totalData.length}_samples`
                            };
                            monitor.tcping_h.shift(target.id, agg);
                            
                            if (debugMode) {
                                console.log(`[TCPing聚合] ${target.name}: 所有${totalData.length}个样本均为异常数据，记录为网络问题`);
                            }
                        }
                        return;
                    }
                    
                    // 计算小时级聚合（基于有效数据）
                    const agg = {
                        success_rate: valid.reduce((a, b) => a + b.success_rate, 0) / valid.length,
                        avg_time: Math.round(valid.reduce((a, b) => a + b.avg_time, 0) / valid.length),
                        min_time: Math.min(...valid.map(x => x.min_time || Infinity).filter(x => x !== Infinity)),
                        max_time: Math.max(...valid.map(x => x.max_time || 0)),
                        sid: valid[0].sid
                    };
                    
                    monitor.tcping_h.shift(target.id, agg);
                    
                    if (debugMode) {
                        const filteredCount = hourData.length - valid.length;
                        console.log(`[TCPing聚合] 完成小时聚合：${target.name}, 有效数据=${valid.length}/${hourData.length} (过滤${filteredCount}个异常), 平均成功率=${agg.success_rate.toFixed(2)}, 平均延迟=${agg.avg_time}ms`);
                    }
                });
            } catch (e) {
                console.error('[TCPing聚合] 小时聚合异常:', e);
            }
            
            // 🔧 修复：天级聚合（每天00:00执行一次，从小时级数据聚合到天级）
            if (hour === 0) {
                try {
                    const targets = monitor.targets.getAll();
                    targets.forEach(target => {
                        // 获取最近24条小时级数据（真正的小时级数据）
                        const dayData = monitor.tcping_h.select(target.id).slice(0, 24);
                        if (dayData.length === 0) return;
                        
                        // 计算天级聚合
                        const valid = dayData.filter(x => typeof x.success_rate === 'number');
                        if (valid.length === 0) return;
                        
                        const agg = {
                            success_rate: valid.reduce((a, b) => a + b.success_rate, 0) / valid.length,
                            avg_time: Math.round(valid.reduce((a, b) => a + b.avg_time, 0) / valid.length),
                            min_time: Math.min(...valid.map(x => x.min_time)),
                            max_time: Math.max(...valid.map(x => x.max_time)),
                            sid: valid[0].sid
                        };
                        
                        monitor.tcping_d.shift(target.id, agg);
                        
                        if (debugMode) {
                            console.log(`[TCPing聚合] 完成天级聚合：${target.name}, 数据点=${valid.length}, 平均成功率=${agg.success_rate.toFixed(2)}, 平均延迟=${agg.avg_time}ms`);
                        }
                        
                        // 月度聚合（每月1日00:00执行一次，保存10年）
                        if (day === 1) {
                            try {
                                // 获取最近30天的天级数据
                                const monthData = monitor.tcping_d.select(target.id).slice(0, 30);
                                if (monthData.length === 0) return;
                                
                                const validMonth = monthData.filter(x => typeof x.success_rate === 'number');
                                if (validMonth.length === 0) return;
                                
                                const monthAgg = {
                                    success_rate: validMonth.reduce((a, b) => a + b.success_rate, 0) / validMonth.length,
                                    avg_time: Math.round(validMonth.reduce((a, b) => a + b.avg_time, 0) / validMonth.length),
                                    min_time: Math.min(...validMonth.map(x => x.min_time)),
                                    max_time: Math.max(...validMonth.map(x => x.max_time)),
                                    sid: validMonth[0].sid
                                };
                                
                                monitor.tcping_month.shift(target.id, monthAgg);
                                
                                if (debugMode) {
                                    console.log(`[TCPing聚合] 完成月度聚合：${target.name}, 数据点=${validMonth.length}, 平均成功率=${monthAgg.success_rate.toFixed(2)}, 平均延迟=${monthAgg.avg_time}ms`);
                                }
                            } catch (e) {
                                console.error('[TCPing聚合] 月度聚合异常:', e);
                            }
                        }
                    });
                } catch (e) {
                    console.error('[TCPing聚合] 天级聚合异常:', e);
                }
            }
        }
    }, 60000);

    // 定时清理归档数据，每10分钟执行一次
    setInterval(() => {
        try {
            const setting = database.setting.all();
            const debugMode = setting.debug === true;
            const now = Math.floor(Date.now() / 1000);
            const oneHourAgo = now - 3600;

            if (debugMode) {
                console.log(`[TCPing调试] 开始清理1小时前的归档数据，时间戳: ${oneHourAgo}`);
            }
            const cleanupOptions = { before_time: oneHourAgo };
            const changes = monitor.tcping_archive.cleanup(cleanupOptions);
            if (debugMode) {
                console.log(`[TCPing调试] 清理归档数据完成，删除了 ${changes} 条记录`);
            }
        } catch (err) {
            console.error('定时清理归档数据失败:', err);
        }
    }, 600000); // 每10分钟执行一次
}

module.exports = {
    startScheduler,
    scheduleTcpingCheck, // Export for direct call if needed elsewhere
    // executeRemoteTest and saveTestResult are internal to this module now
};
