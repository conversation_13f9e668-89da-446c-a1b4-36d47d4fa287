'use strict';

/**
 * 负载数据API模块
 * 提供获取服务器负载历史数据的API接口
 */

module.exports = (svr, db) => {
    const app = svr.app || svr; // 兼容不同的Express实例传递方式

    /**
     * 获取最近的负载数据
     * GET /api/stats/:nodeId/load/recent
     *
     * 参数：
     * - duration: 可选，获取的时间范围（分钟），默认为60
     * - interval: 可选，数据点之间的时间间隔（秒），默认为2
     */
    app.get('/api/stats/:nodeId/load/recent', async (req, res) => {
        try {
            const { nodeId } = req.params;
            const duration = parseInt(req.query.duration) || 60; // 默认60分钟
            const interval = parseInt(req.query.interval) || 2;  // 默认2秒

            // 计算开始时间
            const endTime = Math.floor(Date.now() / 1000);
            const startTime = endTime - (duration * 60);

            // 查询数据 - 过滤掉无效数据（CPU、内存和SWAP都为0或负数的记录）
            const query = `
                SELECT cpu, mem, swap, created_at
                FROM load_archive
                WHERE sid = ?
                AND created_at >= ?
                AND created_at <= ?
                AND (cpu > 0 OR mem > 0 OR swap > 0)
                ORDER BY created_at ASC
            `;

            // 使用适配器方法执行查询
            const rows = await db.DB.all(query, [nodeId, startTime, endTime]);



            // 处理数据 - 使用固定时间间隔采样
            const result = {
                cpu: [],
                mem: [],
                swap: [],
                timestamps: []
            };

            // 根据请求的间隔参数确定采样间隔
            // 如果请求的间隔是2秒，但数据实际上是每2秒保存的，那么我们应该每1个数据点采样一次
            // 如果请求的间隔是10秒，但数据是每2秒保存的，那么我们应该每5个数据点采样一次
            // 动态获取实际的数据间隔（轮询间隔）
            const pollingInterval = parseInt(await db.setting.get('polling_interval')) || 3000;
            const dataInterval = pollingInterval / 1000; // 转换为秒
            const requestedInterval = interval; // 请求的间隔（秒）
            const samplingRatio = Math.max(1, Math.round(requestedInterval / dataInterval));



            // 动态调整数据点限制，基于时间范围平衡数据完整性和性能
            // 短时间范围(≤1h): 保持原始数据密度，限制1200点
            // 中时间范围(1-4h): 适度采样，限制2400点
            // 长时间范围(>4h): 较强采样，限制3000点
            const maxPoints = duration <= 60 ? 1200 :
                             duration <= 240 ? 2400 : 3000;
            let sampleInterval = samplingRatio;

            // 如果即使按照采样比例采样后，数据点仍然过多，则进一步增加采样间隔
            if (rows.length / sampleInterval > maxPoints) {
                sampleInterval = Math.ceil(rows.length / maxPoints);

            }

            // 使用时间桶进行均匀采样，避免数据不连贯
            if (rows.length > 0) {
                // 计算时间范围
                const firstTime = rows[0].created_at;
                const lastTime = rows[rows.length - 1].created_at;
                const timeRange = lastTime - firstTime;

                // 如果时间范围有效，创建均匀分布的时间桶
                if (timeRange > 0) {
                    const bucketCount = Math.min(maxPoints, Math.ceil(timeRange / (requestedInterval)));
                    const bucketSize = timeRange / bucketCount;



                    // 初始化桶
                    const buckets = Array(bucketCount).fill().map(() => ({
                        cpu: [],
                        mem: [],
                        swap: [],
                        timestamp: 0,
                        count: 0
                    }));

                    // 将数据分配到桶中
                    for (const row of rows) {
                        if (row) {
                            const bucketIndex = Math.min(
                                bucketCount - 1,
                                Math.floor((row.created_at - firstTime) / bucketSize)
                            );
                            buckets[bucketIndex].cpu.push(row.cpu);
                            buckets[bucketIndex].mem.push(row.mem);
                            buckets[bucketIndex].swap.push(row.swap);
                            buckets[bucketIndex].timestamp += row.created_at;
                            buckets[bucketIndex].count++;
                        }
                    }

                    // 计算每个桶的平均值
                    for (const bucket of buckets) {
                        if (bucket.count > 0) {
                            // 计算平均值
                            const avgCpu = bucket.cpu.reduce((sum, val) => sum + val, 0) / bucket.count;
                            const avgMem = bucket.mem.reduce((sum, val) => sum + val, 0) / bucket.count;
                            const avgSwap = bucket.swap.reduce((sum, val) => sum + val, 0) / bucket.count;
                            const avgTimestamp = Math.round(bucket.timestamp / bucket.count);

                            // 添加到结果
                            result.cpu.push(avgCpu);
                            result.mem.push(avgMem);
                            result.swap.push(avgSwap);
                            result.timestamps.push(avgTimestamp * 1000); // 转换为毫秒
                        }
                    }


                } else {
                    // 如果时间范围无效，直接使用原始数据

                    for (let i = 0; i < rows.length; i += sampleInterval) {
                        const row = rows[i];
                        if (row) {
                            result.cpu.push(row.cpu);
                            result.mem.push(row.mem);
                            result.swap.push(row.swap);
                            result.timestamps.push(row.created_at * 1000); // 转换为毫秒
                        }
                    }
                }
            } else {

            }

            res.json({
                status: 'success',
                data: result
            });
        } catch (error) {
            console.error('获取最近负载数据失败:', error);
            res.status(500).json({
                status: 'error',
                message: '获取最近负载数据失败: ' + error.message
            });
        }
    });

    /**
     * 获取分钟级负载数据
     * GET /api/stats/:nodeId/load_m
     */
    app.get('/api/stats/:nodeId/load_m', async (req, res) => {
        try {
            const { nodeId } = req.params;

            // 获取分钟级数据（使用无填充版本获取真实数据）
            const rows = await db.load_m.selectRaw(nodeId);

            // 过滤无效数据（离线状态的-1值）并处理数据
            const result = rows
                .filter(row => row.cpu !== -1 && row.mem !== -1 && row.swap !== -1)
                .map(row => ({
                    cpu: row.cpu,
                    mem: row.mem,
                    swap: row.swap,
                    ibw: row.ibw || 0,    // 入站带宽
                    obw: row.obw || 0,    // 出站带宽
                    created_at: row.created_at
                }));

            res.json({
                status: 'success',
                data: result
            });
        } catch (error) {
            console.error('获取分钟级负载数据失败:', error);
            res.status(500).json({
                status: 'error',
                message: '获取分钟级负载数据失败: ' + error.message
            });
        }
    });

    /**
     * 获取小时级负载数据
     * GET /api/stats/:nodeId/load_h
     */
    app.get('/api/stats/:nodeId/load_h', async (req, res) => {
        try {
            const { nodeId } = req.params;

            // 获取小时级数据（使用无填充版本获取真实数据）
            const rows = await db.load_h.selectRaw(nodeId);

            // 过滤无效数据（离线状态的-1值）并处理数据
            const result = rows
                .filter(row => row.cpu !== -1 && row.mem !== -1 && row.swap !== -1)
                .map(row => ({
                    cpu: row.cpu,
                    mem: row.mem,
                    swap: row.swap,
                    ibw: row.ibw || 0,    // 入站带宽
                    obw: row.obw || 0,    // 出站带宽
                    created_at: row.created_at
                }));

            res.json({
                status: 'success',
                data: result
            });
        } catch (error) {
            console.error('获取小时级负载数据失败:', error);
            res.status(500).json({
                status: 'error',
                message: '获取小时级负载数据失败: ' + error.message
            });
        }
    });



    // 返回模块
    return {
        // 可以导出一些方法供其他模块使用
    };
};
