'use strict';

/**
 * 心跳服务 - 定期向许可证服务器报告客户端状态
 * 
 * 设计原则：
 * 1. 隐私保护：只上报许可证相关的必要信息
 * 2. 动态频率：根据系统状态调整心跳间隔
 * 3. 容错机制：网络失败不影响本地功能
 */

const fetch = require('node-fetch');
const crypto = require('crypto');
const OfflineQueue = require('./offlineQueue');

// 心跳间隔配置（毫秒）
const HeartbeatInterval = {
  NORMAL: 60 * 60 * 1000,      // 正常状态：60分钟
  WARNING: 30 * 60 * 1000,      // 警告状态：30分钟（接近限制时）
  CRITICAL: 10 * 60 * 1000,     // 关键状态：10分钟（超限或即将过期）
  STARTUP: 5 * 60 * 1000,       // 启动阶段：5分钟（前3次心跳）
  ERROR: 2 * 60 * 60 * 1000     // 错误状态：2小时（连续失败后）
};

class HeartbeatService {
  constructor(config, db, licenseModule) {
    this.config = config;
    this.db = db;
    this.license = licenseModule;
    
    // 心跳状态
    this.state = {
      interval: HeartbeatInterval.STARTUP,
      heartbeatCount: 0,
      consecutiveFailures: 0,
      lastHeartbeatTime: 0,
      lastSuccessTime: 0,
      isRunning: false,
      isOnline: true, // 网络连接状态
      secretSyncAttempts: 0 // 密钥同步尝试次数
    };
    
    // 定时器
    this.timer = null;
    
    // 实例密钥（延迟初始化）
    this.instanceSecret = null;
    
    // 初始化离线队列
    this.offlineQueue = new OfflineQueue({
      queueDir: require('path').join(__dirname, '../../data/heartbeat-queue'),
      maxQueueSize: 100,
      maxRetries: 3,
      retryInterval: 5 * 60 * 1000, // 5分钟重试一次
      maxAge: 7 * 24 * 60 * 60 * 1000 // 保存7天
    });
    
    // 初始化密钥同步工具
    const InstanceSecretSync = require('./syncInstanceSecret');
    this.secretSync = new InstanceSecretSync(config, db, licenseModule);
  }

  /**
   * 获取或创建实例密钥
   */
  async getOrCreateInstanceSecret() {
    // 优先使用服务端提供的密钥
    let secret = await this.db.setting.get('serverInstanceSecret');
    if (secret) {
      console.log('[HeartbeatService] 使用服务端提供的实例密钥，长度:', secret.length);
      return secret;
    }
    
    // 如果没有服务端密钥，使用本地生成的（向后兼容）
    secret = await this.db.setting.get('instanceSecret');
    if (!secret) {
      secret = crypto.randomBytes(32).toString('hex');
      await this.db.setting.set('instanceSecret', secret);
      console.log('[HeartbeatService] 生成本地实例密钥（向后兼容），长度:', secret.length);
    } else {
      console.log('[HeartbeatService] 使用本地实例密钥（向后兼容），长度:', secret.length);
    }
    return secret;
  }

  /**
   * 启动心跳服务
   */
  async start() {
    if (this.state.isRunning) {
      console.log('[HeartbeatService] 服务已在运行中');
      return;
    }

    console.log('[HeartbeatService] 启动心跳服务');
    this.state.isRunning = true;
    
    // 初始化实例密钥
    if (!this.instanceSecret) {
      this.instanceSecret = await this.getOrCreateInstanceSecret();
    }
    
    // 从磁盘加载队列数据
    await this.offlineQueue.loadFromDisk();
    
    // 启动离线队列自动重试
    this.offlineQueue.startAutoRetry(
      (data) => this.sendHeartbeatData(data),
      5 * 60 * 1000 // 5分钟检查一次
    );
    
    // 立即发送第一次心跳
    this.sendHeartbeat();
    
    // 调度后续心跳
    this.scheduleNextHeartbeat();
  }

  /**
   * 停止心跳服务
   */
  async stop() {
    if (!this.state.isRunning) {
      return;
    }

    console.log('[HeartbeatService] 停止心跳服务');
    this.state.isRunning = false;
    
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    
    // 停止离线队列
    this.offlineQueue.stopAutoRetry();
    await this.offlineQueue.shutdown();
  }

  /**
   * 发送心跳
   * @param {boolean} isManual - 是否为手动触发（事件驱动）
   */
  async sendHeartbeat(isManual = false) {
    if (!this.state.isRunning && !isManual) {
      return;
    }

    try {
      const heartbeatData = await this.prepareHeartbeatData();
      
      // 如果是手动触发，添加触发原因
      if (isManual) {
        heartbeatData.data.triggerType = 'manual';
      }
      
      console.log('[HeartbeatService] 发送心跳:', {
        instanceId: heartbeatData.instanceId,
        nodeCount: heartbeatData.data.nodeCount,
        interval: this.state.interval,
        isManual: isManual
      });

      const response = await this.sendHeartbeatData(heartbeatData);
      
      if (response) {
        await this.handleHeartbeatSuccess(response);
        // 网络恢复，处理离线队列
        if (!this.state.isOnline) {
          this.state.isOnline = true;
          console.log('[HeartbeatService] 网络恢复，处理离线队列');
          await this.offlineQueue.processQueue((data) => this.sendHeartbeatData(data));
        }
      }
    } catch (error) {
      this.handleHeartbeatError(error);
      
      // 网络错误时加入离线队列
      if (this.isNetworkError(error)) {
        this.state.isOnline = false;
        console.log('[HeartbeatService] 网络异常，数据加入离线队列');
        
        try {
          const heartbeatData = await this.prepareHeartbeatData();
          if (isManual) {
            heartbeatData.data.triggerType = 'manual';
          }
          
          await this.offlineQueue.enqueue(heartbeatData, {
            priority: isManual ? 'high' : 'normal',
            reason: error.message
          });
        } catch (prepareError) {
          console.error('[HeartbeatService] 准备心跳数据失败:', prepareError.message);
          // 如果无法准备心跳数据，不加入队列，避免服务崩溃
        }
      }
    }
  }

  /**
   * 准备心跳数据
   */
  async prepareHeartbeatData() {
    const licenseStatus = await this.license.getCurrentLicenseStatus();
    const nodeCount = await this.getActiveNodeCount();
    
    // 确保有许可证密钥
    if (!licenseStatus.licenseKey) {
      console.warn('[HeartbeatService] 许可证密钥缺失，尝试恢复...');
      const licenseInfo = await this.db.setting.get('license_info');
      if (licenseInfo && (licenseInfo.licenseKey || licenseInfo.key)) {
        licenseStatus.licenseKey = licenseInfo.licenseKey || licenseInfo.key;
        // 同时修复 currentLicenseKey
        await this.db.setting.set('currentLicenseKey', licenseStatus.licenseKey);
        console.log('[HeartbeatService] 已从 license_info 恢复许可证密钥');
      } else {
        console.warn('[HeartbeatService] 无法获取许可证密钥，使用空密钥继续');
        licenseStatus.licenseKey = '';
      }
    }
    
    // 确保有实例密钥
    if (!this.instanceSecret) {
      console.warn('[HeartbeatService] 实例密钥缺失，尝试恢复...');
      this.instanceSecret = await this.getOrCreateInstanceSecret();
      
      // 如果仍然没有密钥，尝试从 license_info 恢复
      if (!this.instanceSecret) {
        const licenseInfo = await this.db.setting.get('license_info');
        if (licenseInfo && licenseInfo.instanceSecret) {
          this.instanceSecret = licenseInfo.instanceSecret;
          await this.db.setting.set('serverInstanceSecret', this.instanceSecret);
          console.log('[HeartbeatService] 已从 license_info 恢复实例密钥');
        }
      }
    }
    
    const data = {
      instanceId: this.license.getInstanceId(),
      licenseKey: licenseStatus.licenseKey,
      timestamp: Date.now(),
      data: {
        nodeCount: nodeCount,
        version: require('../../package.json').version,
        uptime: Math.floor(process.uptime() * 1000),
        lastVerifyTime: licenseStatus.lastCheck || 0,
        planType: licenseStatus.planType,
        status: this.calculateSystemStatus(licenseStatus, nodeCount)
      }
    };

    // 添加签名
    data.signature = this.generateSignature(data);

    return data;
  }

  /**
   * 计算系统状态
   */
  calculateSystemStatus(licenseStatus, nodeCount) {
    const maxNodes = licenseStatus.maxNodes || 10;
    const usage = nodeCount / maxNodes;
    
    if (usage >= 1) {
      return 'CRITICAL'; // 已超限
    } else if (usage >= 0.9) {
      return 'WARNING';  // 接近限制（90%）
    } else {
      return 'NORMAL';
    }
  }

  /**
   * 生成数据签名
   */
  generateSignature(data) {
    // 支持两种签名格式
    let payload;
    
    if (data.method && data.path) {
      // 新格式：用于 X-Signature 头部
      payload = JSON.stringify({
        method: data.method,
        path: data.path,
        timestamp: data.timestamp,
        body: data.body
      });
    } else {
      // 旧格式：用于请求体中的 signature 字段（向后兼容）
      payload = JSON.stringify({
        instanceId: data.instanceId,
        licenseKey: data.licenseKey,
        timestamp: data.timestamp,
        data: data.data
      });
    }

    return crypto
      .createHmac('sha256', this.instanceSecret)
      .update(payload)
      .digest('hex');
  }

  /**
   * 处理心跳成功
   */
  async handleHeartbeatSuccess(response) {
    console.log('[HeartbeatService] 心跳成功');
    
    // 更新状态
    this.state.heartbeatCount++;
    this.state.consecutiveFailures = 0;
    this.state.secretSyncAttempts = 0; // 重置密钥同步尝试次数
    this.state.lastHeartbeatTime = Date.now();
    this.state.lastSuccessTime = Date.now();

    // 处理服务端响应
    if (response.nextInterval) {
      this.state.interval = response.nextInterval;
      console.log('[HeartbeatService] 更新心跳间隔:', this.state.interval);
    } else {
      // 根据心跳次数调整间隔
      this.adjustInterval();
    }

    // 处理服务端命令
    if (response.commands && Array.isArray(response.commands)) {
      this.executeCommands(response.commands);
    }

    // 保存心跳状态
    await this.saveHeartbeatState();
  }

  /**
   * 处理心跳错误
   */
  async handleHeartbeatError(error) {
    console.error('[HeartbeatService] 心跳失败:', error.message);
    
    // 更新失败计数
    this.state.consecutiveFailures++;
    this.state.lastHeartbeatTime = Date.now();

    // 如果是签名验证失败，尝试同步密钥
    if (error.message && (error.message.includes('signature verification failed') || 
        error.message.includes('Request signature verification failed'))) {
      console.log('[HeartbeatService] 检测到签名验证失败，尝试同步实例密钥...');
      
      // 限制同步尝试次数，避免无限循环
      if (this.state.secretSyncAttempts < 3) {
        this.state.secretSyncAttempts++;
        
        try {
          const syncResult = await this.secretSync.syncFromServer();
          
          if (syncResult.success) {
            console.log('[HeartbeatService] 实例密钥同步成功，重新加载密钥');
            // 重新加载密钥
            this.instanceSecret = await this.getOrCreateInstanceSecret();
            // 重置失败计数，立即重试
            this.state.consecutiveFailures = 0;
            this.state.secretSyncAttempts = 0;
            
            // 立即尝试发送心跳
            setTimeout(() => {
              console.log('[HeartbeatService] 密钥同步后立即重试心跳');
              this.sendHeartbeat();
            }, 1000);
            
            return;
          } else {
            console.error('[HeartbeatService] 实例密钥同步失败:', syncResult.error);
          }
        } catch (syncError) {
          console.error('[HeartbeatService] 同步过程出错:', syncError);
        }
      } else {
        console.warn('[HeartbeatService] 已达到最大同步尝试次数');
      }
    }

    // 连续失败超过3次，降低频率
    if (this.state.consecutiveFailures >= 3) {
      this.state.interval = HeartbeatInterval.ERROR;
      console.log('[HeartbeatService] 连续失败，降低心跳频率');
    }

    // 保存心跳状态
    await this.saveHeartbeatState();
  }

  /**
   * 调整心跳间隔
   */
  async adjustInterval() {
    const licenseStatus = await this.license.getCurrentLicenseStatus();
    const nodeCount = await this.getActiveNodeCount();
    const systemStatus = this.calculateSystemStatus(licenseStatus, nodeCount);

    // 前3次心跳使用启动间隔
    if (this.state.heartbeatCount <= 3) {
      this.state.interval = HeartbeatInterval.STARTUP;
      return;
    }

    // 根据系统状态调整间隔
    switch (systemStatus) {
      case 'CRITICAL':
        this.state.interval = HeartbeatInterval.CRITICAL;
        break;
      case 'WARNING':
        this.state.interval = HeartbeatInterval.WARNING;
        break;
      default:
        this.state.interval = HeartbeatInterval.NORMAL;
    }
  }

  /**
   * 执行服务端命令
   */
  async executeCommands(commands) {
    for (const command of commands) {
      try {
        console.log('[HeartbeatService] 执行命令:', command.type);
        
        switch (command.type) {
          case 'FORCE_VERIFY':
            // 强制重新验证许可证
            const licenseStatus = await this.license.getCurrentLicenseStatus();
            await this.license.verifyLicense(
              licenseStatus.licenseKey,
              this.license.getInstanceId()
            );
            break;
            
          case 'UPDATE_INTERVAL':
            // 更新心跳间隔
            if (command.interval) {
              this.state.interval = command.interval;
            }
            break;
            
          default:
            console.warn('[HeartbeatService] 未知命令类型:', command.type);
        }
      } catch (error) {
        console.error('[HeartbeatService] 命令执行失败:', error);
      }
    }
  }

  /**
   * 调度下次心跳
   */
  scheduleNextHeartbeat() {
    if (!this.state.isRunning) {
      return;
    }

    // 清除现有定时器
    if (this.timer) {
      clearTimeout(this.timer);
    }

    // 设置新的定时器
    this.timer = setTimeout(() => {
      this.sendHeartbeat();
      this.scheduleNextHeartbeat();
    }, this.state.interval);

    console.log('[HeartbeatService] 下次心跳时间:', new Date(Date.now() + this.state.interval).toLocaleString());
  }

  /**
   * 获取活跃节点数
   */
  async getActiveNodeCount() {
    try {
      const servers = await this.db.servers.all();
      const activeServers = servers.filter(s => s.status === 1);
      
      // 调试日志
      if (this.debugNodeCount !== undefined && this.debugNodeCount !== activeServers.length) {
        console.log('[HeartbeatService] 节点数变化:', {
          总数: servers.length,
          活跃数: activeServers.length,
          之前: this.debugNodeCount
        });
      }
      this.debugNodeCount = activeServers.length;
      
      return activeServers.length;
    } catch (error) {
      console.error('[HeartbeatService] 获取节点数失败:', error);
      console.error('[HeartbeatService] db对象状态:', {
        dbExists: !!this.db,
        serversExists: !!this.db?.servers,
        allIsFunction: typeof this.db?.servers?.all === 'function'
      });
      return 0;
    }
  }

  /**
   * 带超时的fetch请求
   */
  async fetchWithTimeout(url, options = {}, timeout = 30000) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        throw new Error('请求超时');
      }
      throw error;
    }
  }

  /**
   * 保存心跳状态
   */
  async saveHeartbeatState() {
    try {
      await this.db.setting.set('heartbeatState', {
        lastHeartbeatTime: this.state.lastHeartbeatTime,
        lastSuccessTime: this.state.lastSuccessTime,
        heartbeatCount: this.state.heartbeatCount,
        consecutiveFailures: this.state.consecutiveFailures,
        currentInterval: this.state.interval
      });
    } catch (error) {
      console.error('[HeartbeatService] 保存状态失败:', error);
    }
  }

  /**
   * 恢复心跳状态
   */
  async restoreHeartbeatState() {
    try {
      const savedState = await this.db.setting.get('heartbeatState');
      if (savedState) {
        Object.assign(this.state, savedState);
        console.log('[HeartbeatService] 恢复心跳状态:', savedState);
      }
    } catch (error) {
      console.error('[HeartbeatService] 恢复状态失败:', error);
    }
  }

  /**
   * 获取心跳服务状态
   */
  getStatus() {
    const queueStatus = this.offlineQueue.getStatus();
    
    return {
      isRunning: this.state.isRunning,
      isOnline: this.state.isOnline,
      lastHeartbeat: this.state.lastHeartbeatTime,
      lastSuccess: this.state.lastSuccessTime,
      totalCount: this.state.heartbeatCount,
      failures: this.state.consecutiveFailures,
      currentInterval: this.state.interval,
      nextHeartbeat: this.state.isRunning ? Date.now() + this.state.interval : null,
      offlineQueue: {
        size: queueStatus.size,
        stats: queueStatus.stats
      }
    };
  }
  
  /**
   * 发送心跳数据（核心发送逻辑）
   */
  async sendHeartbeatData(heartbeatData) {
    // 为新的签名验证机制生成签名头部
    // 注意：后端路由器使用 /heartbeat 而不是完整路径 /client/heartbeat
    const signaturePayload = {
      method: 'POST',
      path: '/heartbeat',
      timestamp: heartbeatData.timestamp,
      body: heartbeatData
    };
    
    const signature = this.generateSignature(signaturePayload);
    
    // 调试日志
    console.log('[HeartbeatService] 签名调试信息:', {
      secretLength: this.instanceSecret ? this.instanceSecret.length : 0,
      signature: signature.substring(0, 20) + '...',
      timestamp: heartbeatData.timestamp,
      licenseKey: heartbeatData.licenseKey.substring(0, 20) + '...',
      instanceId: heartbeatData.instanceId
    });
    
    const response = await this.fetchWithTimeout(
      `${this.config.serverUrl}/api/client/heartbeat`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Signature': signature,
          'X-License-Key': heartbeatData.licenseKey,
          'X-Instance-Id': heartbeatData.instanceId,
          'X-Timestamp': heartbeatData.timestamp.toString()
        },
        body: JSON.stringify(heartbeatData)
      }
    );

    const result = await response.json();

    if (response.ok && result.success) {
      return result;
    } else {
      throw new Error(result.message || '心跳请求失败');
    }
  }
  
  /**
   * 判断是否为网络错误
   */
  isNetworkError(error) {
    const networkErrors = [
      'ECONNREFUSED',
      'ECONNRESET',
      'ETIMEDOUT',
      'ENOTFOUND',
      'ENETUNREACH',
      'EHOSTUNREACH',
      'EPIPE',
      'ECONNABORTED'
    ];
    
    return networkErrors.some(code => error.code === code) ||
           error.message.includes('network') ||
           error.message.includes('timeout') ||
           error.message.includes('请求超时') ||
           error.message.includes('fetch failed');
  }

  /**
   * 手动触发心跳（用于事件驱动）
   */
  async triggerHeartbeat(reason) {
    console.log('[HeartbeatService] 手动触发心跳，原因:', reason);
    
    // 发送心跳（标记为手动触发）
    await this.sendHeartbeat(true);
    
    // 重新调度定时心跳
    if (this.state.isRunning) {
      this.scheduleNextHeartbeat();
    }
  }
  
  /**
   * 节点状态变化处理器
   * @param {string} event - 事件类型 ('add', 'remove', 'status_change')
   * @param {Object} nodeInfo - 节点信息
   */
  async onNodeStateChanged(event, nodeInfo) {
    console.log('[HeartbeatService] 节点状态变化:', event, nodeInfo);
    
    // 检查是否需要立即同步
    const shouldSync = this.shouldSyncImmediately(event);
    
    if (shouldSync) {
      // 等待一小段时间，避免批量操作时频繁触发
      if (this.nodeChangeTimer) {
        clearTimeout(this.nodeChangeTimer);
      }
      
      this.nodeChangeTimer = setTimeout(() => {
        this.triggerHeartbeat(`节点${event}: ${nodeInfo.name || nodeInfo.sid}`);
      }, 2000); // 2秒延迟，聚合批量操作
    }
  }
  
  /**
   * 判断是否需要立即同步
   */
  async shouldSyncImmediately(event) {
    const licenseStatus = await this.license.getCurrentLicenseStatus();
    const nodeCount = await this.getActiveNodeCount();
    const maxNodes = licenseStatus.maxNodes || 10;
    const usage = nodeCount / maxNodes;
    
    // 以下情况立即同步：
    // 1. 接近或超过限制（使用率 >= 80%）
    // 2. 从超限状态恢复（移除节点后低于限制）
    // 3. 系统启动后的前10分钟内
    const isNearLimit = usage >= 0.8;
    const wasOverLimit = this.lastNodeCount > maxNodes && nodeCount <= maxNodes;
    const isStartupPeriod = Date.now() - this.state.lastSuccessTime < 10 * 60 * 1000;
    
    // 记录上次节点数
    this.lastNodeCount = nodeCount;
    
    return isNearLimit || wasOverLimit || isStartupPeriod;
  }
}

module.exports = HeartbeatService;