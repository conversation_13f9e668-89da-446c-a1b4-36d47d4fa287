'use strict';

/**
 * License Enhanced Security Module
 * 许可证安全管理模块
 */

const crypto = require('crypto');

class LicenseSecurityManager {
  constructor(db) {
    this.db = db;
    
    // 安全配置
    this.config = {
      // 实例指纹有效期（小时）
      fingerprintValidityHours: 24,
      // 最大设备数量（防止一个用户注册太多设备）
      maxDevicesPerUser: 5
    };
    
    // 缓存installTime - 解决同步调用异步方法的问题
    this.cachedInstallTime = Date.now(); // 默认值
    this.initialize();
  }
  
  /**
   * 异步初始化 - 预加载必要的配置数据
   */
  async initialize() {
    try {
      this.cachedInstallTime = await this.db.setting.get('installTime') || Date.now();
      console.log('[Security] 安全管理器初始化完成，installTime已缓存');
    } catch (error) {
      console.warn('[Security] 无法加载installTime，使用默认值:', error.message);
      this.cachedInstallTime = Date.now();
    }
  }

  /**
   * 防滥用机制1：设备指纹验证
   * 生成并验证设备唯一指纹，防止复制系统钻漏洞
   */
  generateDeviceFingerprint() {
    try {
      const os = require('os');
      
      // 收集系统特征
      const systemInfo = {
        hostname: os.hostname(),
        platform: os.platform(),
        arch: os.arch(),
        cpus: os.cpus().length,
        totalmem: os.totalmem(),
        networkInterfaces: Object.keys(os.networkInterfaces()),
        // 使用缓存的安装时间戳，避免同步调用异步方法
        installTime: this.cachedInstallTime
      };
      
      // 生成指纹哈希
      const fingerprint = crypto
        .createHash('sha256')
        .update(JSON.stringify(systemInfo))
        .digest('hex')
        .substring(0, 32);
      
      // 保存指纹和生成时间
      this.db.setting.set('deviceFingerprint', fingerprint);
      this.db.setting.set('fingerprintGeneratedAt', Date.now());
      
      console.log('[Security] 设备指纹已生成:', fingerprint.substring(0, 8) + '...');
      return fingerprint;
    } catch (error) {
      console.error('[Security] 生成设备指纹失败:', error);
      return null;
    }
  }

  /**
   * 验证设备指纹一致性
   */
  verifyDeviceFingerprint() {
    const storedFingerprint = this.db.setting.get('deviceFingerprint');
    const generatedAt = this.db.setting.get('fingerprintGeneratedAt');
    
    if (!storedFingerprint || !generatedAt) {
      console.log('[Security] 首次运行，生成设备指纹');
      return this.generateDeviceFingerprint();
    }
    
    // 检查指纹是否过期
    const hoursElapsed = (Date.now() - generatedAt) / (1000 * 60 * 60);
    if (hoursElapsed > this.config.fingerprintValidityHours) {
      console.log('[Security] 设备指纹已过期，重新生成');
      return this.generateDeviceFingerprint();
    }
    
    // 重新生成当前指纹进行比较
    const currentFingerprint = this.generateDeviceFingerprint();
    
    if (storedFingerprint !== currentFingerprint) {
      console.warn('[Security] 设备指纹不匹配，可能存在系统复制风险');
      this.logSecurityEvent('FINGERPRINT_MISMATCH', {
        stored: storedFingerprint.substring(0, 8),
        current: currentFingerprint.substring(0, 8)
      });
      return false;
    }
    
    return true;
  }

  // 祖父条款相关的安全检查已移除

  /**
   * 防滥用机制3：优惠码使用追踪
   * 确保优惠码不被重复使用
   */
  validateCouponUsage(couponCode, licenseKey) {
    try {
      const usageKey = `coupon_usage_${couponCode}`;
      const usageRecord = this.db.setting.get(usageKey);
      
      if (usageRecord) {
        const usage = JSON.parse(usageRecord);
        
        // 检查是否已被当前License使用
        if (usage.licenseKey === licenseKey) {
          return { valid: true, reason: 'already_used_by_same_license' };
        }
        
        // 检查是否超过使用限制
        if (usage.usageCount >= this.config.couponUsageLimit) {
          console.warn('[Security] 优惠码已达使用限制:', couponCode);
          this.logSecurityEvent('COUPON_LIMIT_EXCEEDED', { couponCode, usage });
          return { valid: false, reason: 'usage_limit_exceeded' };
        }
      }
      
      return { valid: true, reason: 'available' };
    } catch (error) {
      console.error('[Security] 验证优惠码使用失败:', error);
      return { valid: false, reason: 'validation_error' };
    }
  }

  /**
   * 记录优惠码使用
   */
  recordCouponUsage(couponCode, licenseKey, deviceFingerprint) {
    try {
      const usageKey = `coupon_usage_${couponCode}`;
      const existingUsage = this.db.setting.get(usageKey);
      
      let usage = {
        couponCode,
        licenseKey,
        deviceFingerprint,
        usageCount: 1,
        firstUsedAt: Date.now(),
        lastUsedAt: Date.now()
      };
      
      if (existingUsage) {
        const parsed = JSON.parse(existingUsage);
        usage = {
          ...parsed,
          usageCount: parsed.usageCount + 1,
          lastUsedAt: Date.now()
        };
      }
      
      this.db.setting.set(usageKey, JSON.stringify(usage));
      this.logSecurityEvent('COUPON_USED', { couponCode, licenseKey });
      
      console.log('[Security] 优惠码使用已记录:', couponCode);
      return true;
    } catch (error) {
      console.error('[Security] 记录优惠码使用失败:', error);
      return false;
    }
  }

  /**
   * 综合安全检查
   * 在授权操作前进行全面的安全验证
   */
  performSecurityCheck(licenseKey, operation = 'license_check') {
    const results = {
      passed: true,
      warnings: [],
      errors: [],
      deviceFingerprint: null
    };
    
    try {
      // 1. 设备指纹验证
      const fingerprintValid = this.verifyDeviceFingerprint();
      if (!fingerprintValid) {
        results.warnings.push('设备指纹验证失败，可能存在系统复制风险');
        // 不阻止操作，但记录警告
      }
      results.deviceFingerprint = this.db.setting.get('deviceFingerprint');
      
      // 2. 操作频率限制（祖父条款相关检查已移除）
      
      // 3. 记录安全事件
      this.logSecurityEvent('SECURITY_CHECK', {
        licenseKey,
        operation,
        fingerprintValid,
        passed: results.passed
      });
      
    } catch (error) {
      console.error('[Security] 安全检查失败:', error);
      results.passed = false;
      results.errors.push('安全检查异常');
    }
    
    return results;
  }

  /**
   * 记录安全事件
   */
  logSecurityEvent(eventType, data) {
    try {
      const event = {
        type: eventType,
        timestamp: Date.now(),
        data: data,
        instanceId: this.db.setting.get('instanceId')
      };
      
      // 获取现有日志
      const securityLog = this.db.setting.get('securityLog') || '[]';
      const logs = JSON.parse(securityLog);
      
      // 添加新事件
      logs.push(event);
      
      // 保留最近100条记录
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }
      
      // 保存日志
      this.db.setting.set('securityLog', JSON.stringify(logs));
      
      console.log(`[Security] 安全事件已记录: ${eventType}`);
    } catch (error) {
      console.error('[Security] 记录安全事件失败:', error);
    }
  }

  /**
   * 获取安全报告
   */
  getSecurityReport() {
    try {
      const securityLog = this.db.setting.get('securityLog') || '[]';
      const logs = JSON.parse(securityLog);
      
      // 统计最近24小时的事件
      const last24Hours = Date.now() - (24 * 60 * 60 * 1000);
      const recentLogs = logs.filter(log => log.timestamp > last24Hours);
      
      // 按类型分组统计
      const eventStats = {};
      recentLogs.forEach(log => {
        eventStats[log.type] = (eventStats[log.type] || 0) + 1;
      });
      
      return {
        totalEvents: recentLogs.length,
        eventStats,
        deviceFingerprint: this.db.setting.get('deviceFingerprint'),
        lastSecurityCheck: Math.max(...recentLogs.map(log => log.timestamp), 0),
        warningEvents: recentLogs.filter(log => 
          log.type.includes('MISMATCH') || log.type.includes('LIMIT')
        ).length
      };
    } catch (error) {
      console.error('[Security] 生成安全报告失败:', error);
      return { error: error.message };
    }
  }
}

module.exports = LicenseSecurityManager; 