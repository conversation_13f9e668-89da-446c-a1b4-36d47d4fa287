'use strict';

/**
 * 持久化授权管理器
 * 实现类似OAuth的长期登录状态保存
 */

const crypto = require('crypto');

class PersistentAuth {
    constructor(db, config) {
        this.db = db;
        this.config = config;
        this.REFRESH_TOKEN_EXPIRY = 90 * 24 * 60 * 60 * 1000; // 90天
        this.ACCESS_TOKEN_EXPIRY = 7 * 24 * 60 * 60 * 1000; // 7天
        this.ENCRYPTION_KEY = null; // 将在init()中异步初始化
    }

    /**
     * 异步初始化
     */
    async init() {
        this.ENCRYPTION_KEY = await this.getOrCreateEncryptionKey();
    }

    /**
     * 获取或创建加密密钥
     */
    async getOrCreateEncryptionKey() {
        let key = await this.db.setting.get('oauth_encryption_key');
        if (!key) {
            key = crypto.randomBytes(32).toString('hex');
            await this.db.setting.set('oauth_encryption_key', key);
        }
        return key;
    }

    /**
     * 加密令牌
     */
    encryptToken(token) {
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipheriv(
            'aes-256-cbc', 
            Buffer.from(this.ENCRYPTION_KEY, 'hex'), 
            iv
        );
        
        let encrypted = cipher.update(token, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        
        return iv.toString('hex') + ':' + encrypted;
    }

    /**
     * 解密令牌
     */
    decryptToken(encryptedToken) {
        try {
            // 🔥 热修复：添加类型检查，防止非字符串值导致的错误
            if (!encryptedToken || typeof encryptedToken !== 'string') {
                console.warn('[PersistentAuth] decryptToken: 无效的加密令牌类型:', typeof encryptedToken);
                return null;
            }
            
            const parts = encryptedToken.split(':');
            const iv = Buffer.from(parts[0], 'hex');
            const encrypted = parts[1];
            
            const decipher = crypto.createDecipheriv(
                'aes-256-cbc',
                Buffer.from(this.ENCRYPTION_KEY, 'hex'),
                iv
            );
            
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            return decrypted;
        } catch (error) {
            console.error('[PersistentAuth] 解密失败:', error);
            return null;
        }
    }

    /**
     * 保存授权信息
     */
    async saveAuthTokens(authData) {
        const { accessToken, refreshToken, userInfo } = authData;

        // 生成设备ID（如果不存在）
        let deviceId = await this.db.setting.get('oauth_device_id');
        if (!deviceId) {
            deviceId = crypto.randomBytes(16).toString('hex');
            await this.db.setting.set('oauth_device_id', deviceId);
        }

        // 加密并保存refresh token
        const encryptedRefreshToken = this.encryptToken(refreshToken);
        await this.db.setting.set('oauth_refresh_token', encryptedRefreshToken);

        // 保存授权时间和用户信息
        await this.db.setting.set('oauth_authorized_at', Date.now());
        await this.db.setting.set('oauth_user_info', JSON.stringify(userInfo));
        await this.db.setting.set('oauth_access_token_expires', Date.now() + this.ACCESS_TOKEN_EXPIRY);
        
        // 内存中保存access token（不持久化）
        this.currentAccessToken = accessToken;
        
        console.log('[PersistentAuth] 授权信息已保存');
        
        return {
            success: true,
            deviceId
        };
    }

    /**
     * 获取当前访问令牌
     */
    async getAccessToken() {
        // 检查内存中的token是否有效
        const expiresAt = await this.db.setting.get('oauth_access_token_expires');
        if (this.currentAccessToken && expiresAt && Date.now() < expiresAt) {
            return this.currentAccessToken;
        }
        
        // 尝试使用refresh token获取新的access token
        const refreshToken = this.getRefreshToken();
        if (!refreshToken) {
            return null;
        }
        
        try {
            const result = await this.refreshAccessToken(refreshToken);
            if (result.success) {
                return result.accessToken;
            }
        } catch (error) {
            console.error('[PersistentAuth] 刷新令牌失败:', error);
        }
        
        return null;
    }

    /**
     * 获取刷新令牌
     */
    async getRefreshToken() {
        const encryptedToken = await this.db.setting.get('oauth_refresh_token');
        if (!encryptedToken) {
            return null;
        }

        return this.decryptToken(encryptedToken);
    }

    /**
     * 刷新访问令牌
     */
    async refreshAccessToken(refreshToken) {
        try {
            const response = await fetch(`${this.config.serverUrl}/api/auth/refresh`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    refresh_token: refreshToken,
                    device_id: await this.db.setting.get('oauth_device_id')
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // 更新access token
                this.currentAccessToken = result.access_token;
                await this.db.setting.set('oauth_access_token_expires', Date.now() + this.ACCESS_TOKEN_EXPIRY);

                // 如果返回了新的refresh token，也更新它
                if (result.refresh_token) {
                    const encryptedRefreshToken = this.encryptToken(result.refresh_token);
                    await this.db.setting.set('oauth_refresh_token', encryptedRefreshToken);
                }
                
                console.log('[PersistentAuth] 访问令牌已刷新');
                
                return {
                    success: true,
                    accessToken: result.access_token
                };
            } else {
                // 刷新失败，清除授权信息
                this.clearAuth();
                return {
                    success: false,
                    error: result.message
                };
            }
        } catch (error) {
            console.error('[PersistentAuth] 刷新令牌请求失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 检查是否已授权
     */
    async isAuthorized() {
        const refreshToken = await this.getRefreshToken();
        const authorizedAt = await this.db.setting.get('oauth_authorized_at');

        return !!(refreshToken && authorizedAt);
    }

    /**
     * 获取已保存的用户信息
     */
    async getSavedUserInfo() {
        const userInfoStr = await this.db.setting.get('oauth_user_info');
        if (!userInfoStr) {
            return null;
        }
        
        try {
            return JSON.parse(userInfoStr);
        } catch (error) {
            console.error('[PersistentAuth] 解析用户信息失败:', error);
            return null;
        }
    }

    /**
     * 清除授权信息
     */
    async clearAuth() {
        await this.db.setting.del('oauth_refresh_token');
        await this.db.setting.del('oauth_authorized_at');
        await this.db.setting.del('oauth_user_info');
        await this.db.setting.del('oauth_access_token_expires');
        this.currentAccessToken = null;

        console.log('[PersistentAuth] 授权信息已清除');
    }

    /**
     * 获取设备信息
     */
    async getDeviceInfo() {
        const os = require('os');

        return {
            deviceId: await this.db.setting.get('oauth_device_id'),
            deviceName: os.hostname(),
            platform: os.platform(),
            instanceId: await this.db.setting.get('instance_id')
        };
    }
}

module.exports = PersistentAuth;