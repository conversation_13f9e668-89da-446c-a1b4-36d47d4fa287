'use strict';

/**
 * DStatus客户端授权管理模块 - 主入口
 * 基于《DStatus客户端授权系统集成任务计划.md》v3.0
 * 
 * 功能：
 * - 云端授权验证
 * - 实例绑定管理
 * - 授权转移处理
 * - 简单离线缓存
 */

const LicenseBindingManager = require('./bindingManager');
const SimpleLicenseManager = require('./statusChecker');
const LicenseUIManager = require('./userInterface');
const LicenseServerSession = require('./userSession');
const LicenseHealthCheck = require('./healthCheck');
const LicenseSecurityManager = require('./security');
const HeartbeatService = require('./heartbeatService');
const FeatureChecker = require('./featureChecker');
const licensePlansCache = require('../../config/license-plans-cache');

class LicenseEnhancedModule {
  constructor(app, db) {
    this.app = app;
    this.db = db;

    this.config = {
      // 增强授权模式开关 - 暂时设为默认值，稍后异步初始化
      enabled: true, // 默认启用，将在initializeAsync中设置实际值
      // License Server URL - 从环境变量读取
      serverUrl: process.env.LICENSE_SERVER_URL || 'https://dstatus_api.vps.mom',
      // 测试模式 - 暂时设为默认值，稍后异步初始化
      testMode: false, // 默认禁用，将在initializeAsync中设置实际值
      // API超时设置
      apiTimeout: 10000,
      // 缓存有效期 (7天 = 168小时)
      cacheValidityHours: 168,
      // 是否启用扩展缓冲期
      enableExtendedBuffer: true
    };

    // 立即初始化子模块（同步初始化，确保路由注册前可用）
    this.bindingManager = new LicenseBindingManager(this.config, db);
    this.statusChecker = new SimpleLicenseManager(this.config, db);
    this.uiManager = new LicenseUIManager(this.config, db);
    this.userSession = new LicenseServerSession(this.config, db);
    this.healthCheck = new LicenseHealthCheck(this);
    this.securityManager = new LicenseSecurityManager(db);
    this.heartbeatService = new HeartbeatService(this.config, db, this);
    this.featureChecker = new FeatureChecker(db);
    this.unifiedConfigService = require('./unifiedConfigService');

    // 缓存实例ID，避免频繁数据库调用
    this._cachedInstanceId = null;
    
    // 实例ID初始化Promise，确保只初始化一次
    this._instanceIdInitPromise = null;
    
    // 标记需要异步初始化（仅用于数据库配置加载）
    this._needsAsyncInit = true;
    
    // 立即启动实例ID初始化（不等待）
    this._initializeInstanceId().catch(err => {
      console.error('[LicenseEnhanced] 构造函数中初始化实例ID失败:', err);
    });
  }

  /**
   * 异步初始化方法 - 处理需要数据库访问的初始化逻辑
   */
  async initializeAsync() {
    if (!this._needsAsyncInit) return;

    try {
      // 从数据库读取实际配置
      const enhancedMode = await this.db.setting.get('licenseEnhancedMode');
      const testMode = await this.db.setting.get('licenseTestMode');

      this.config.enabled = enhancedMode !== false; // 默认true，除非明确设置为false
      this.config.testMode = testMode || false;

      // 如果是首次运行，设置默认启用
      if (enhancedMode === undefined) {
        await this.db.setting.set('licenseEnhancedMode', true);
        this.config.enabled = true;
        console.log('[LicenseEnhanced] 首次运行，默认启用增强授权模式');
      }

      this._needsAsyncInit = false;
      console.log('[LicenseEnhanced] 异步配置加载完成，增强模式:', this.config.enabled ? '已启用' : '已禁用');
    } catch (error) {
      console.error('[LicenseEnhanced] 异步配置加载失败:', error);
      // 使用默认配置继续运行
      this.config.enabled = true;
      this.config.testMode = false;
      this._needsAsyncInit = false;
    }

    // 🆕 检查安装时自动许可证激活
    const autoLicenseKey = process.env.DSTATUS_AUTO_LICENSE;
    if (autoLicenseKey && autoLicenseKey.trim()) {
      try {
        const currentStatus = await this.getCurrentLicenseStatus();

        // 只在无有效许可证时执行自动激活
        if (!currentStatus || !currentStatus.licenseKey || currentStatus.status !== 'active') {
          console.log('[LicenseEnhanced] 检测到安装时许可证，开始自动激活:', autoLicenseKey.substring(0, 8) + '...');

          // 🔧 使用前端验证逻辑，确保格式一致
          const validation = this.uiManager.validateLicenseKey(autoLicenseKey);
          if (!validation.valid) {
            console.error('[LicenseEnhanced] 安装时许可证格式验证失败:', validation.message);
            // 清除无效的环境变量
            delete process.env.DSTATUS_AUTO_LICENSE;
          } else {
            console.log('[LicenseEnhanced] 许可证格式验证通过，格式:', validation.format);

            const result = await this.activateLicense(validation.licenseKey, true); // 使用验证后的许可证密钥

            if (result && result.success) {
              console.log('[LicenseEnhanced] 安装时许可证激活成功');

              // 仅记录密钥与状态，不在此处推断套餐信息
              await this.setCurrentLicense(validation.licenseKey, 'active');

              // 在线校验并由 StatusChecker 写入权威 license_info
              try {
                const instanceId = this.getInstanceId();
                const online = await this.statusChecker.verifyOnline(validation.licenseKey, instanceId);
                if (online && online.success) {
                  this.statusChecker.updateCache(validation.licenseKey, { ...online.data, planInfo: online.planInfo });
                } else {
                  console.warn('[LicenseEnhanced] 安装后在线验证失败，等待网络恢复再更新套餐信息');
                }
              } catch (verifyErr) {
                console.warn('[LicenseEnhanced] 安装后在线验证异常:', verifyErr.message);
              }

              // 清除环境变量避免重复激活
              delete process.env.DSTATUS_AUTO_LICENSE;
            } else {
              console.error('[LicenseEnhanced] 安装时许可证激活失败:', result?.error || '未知错误');
            }
          }
        } else {
          console.log('[LicenseEnhanced] 已有有效许可证，跳过自动激活');
          // 清除环境变量
          delete process.env.DSTATUS_AUTO_LICENSE;
        }
      } catch (error) {
        console.error('[LicenseEnhanced] 安装时许可证激活异常:', error.message);
        // 不阻塞系统启动，继续运行
      }
    }

    console.log('[LicenseEnhanced] 授权管理模块异步初始化完成');
    console.log('[LicenseEnhanced] 增强模式:', this.config.enabled ? '已启用' : '未启用');
    
    // 注册节点状态监听器
    this.registerNodeListeners();
    
    // 确保实例ID初始化完成（构造函数中已启动）
    await this._initializeInstanceId();
    
    // 🔧 优化：将许可证验证改为完全异步，不阻塞页面加载
    // 原来的同步阻塞检查改为后台异步执行
    this.performBackgroundLicenseCheck();
  }

  /**
   * 后台异步许可证检查 - 不阻塞界面渲染
   */
  performBackgroundLicenseCheck() {
    // 立即返回，在后台异步执行许可证检查
    setTimeout(async () => {
      try {
        console.log('[LicenseEnhanced] 开始后台许可证验证...');
        
        // 强制在线验证，确保获取最新的许可证信息
        const licenseKey = await this.db.setting.get('currentLicenseKey');
        if (licenseKey) {
          const instanceId = this.getInstanceId();
          console.log('[LicenseEnhanced] 强制执行在线验证...');
          
          // 直接调用statusChecker的在线验证
          const onlineResult = await this.statusChecker.verifyOnline(licenseKey, instanceId);
          if (onlineResult.success) {
            console.log('[LicenseEnhanced] 在线验证成功，更新本地数据');
            // 更新缓存
            this.statusChecker.updateCache(licenseKey, onlineResult.data);
            // 触发前端更新
            this.notifyLicenseStatusUpdate(onlineResult);
          } else {
            console.log('[LicenseEnhanced] 在线验证失败，使用缓存数据');
          }
        }
        
        // 检查授权状态（包含离线缓存逻辑）
        const result = await this.checkLicenseOnStartup();
        
        if (result.success) {
          console.log('[LicenseEnhanced] 后台许可证验证成功');
          // 触发前端更新事件
          this.notifyLicenseStatusUpdate(result);
        } else {
          console.log('[LicenseEnhanced] 后台许可证验证失败:', result.message);
        }
        
        // 自动检测和修复许可证状态
        await this.autoDetectAndRepairLicense();
        
      } catch (error) {
        console.error('[LicenseEnhanced] 后台许可证验证异常:', error);
      }
    }, 100); // 100ms后开始后台检查，给页面渲染让路
  }

  /**
   * 通知前端许可证状态更新
   */
  notifyLicenseStatusUpdate(licenseResult) {
    // 可以通过 WebSocket 或事件机制通知前端更新 sidebar
    // 这里先记录日志，后续可扩展为实时通知机制
    console.log('[LicenseEnhanced] 许可证状态已更新，可通知前端刷新菜单');
  }

  /**
   * 异步初始化实例ID缓存
   */
  async _initializeInstanceId() {
    // 如果已经在初始化中，返回现有的Promise
    if (this._instanceIdInitPromise) {
      return this._instanceIdInitPromise;
    }
    
    // 创建初始化Promise
    this._instanceIdInitPromise = (async () => {
      try {
        let instanceId = await this.db.setting.get('instanceId');
        if (!instanceId) {
          const { uuid } = require('../../core');
          instanceId = uuid.v4();
          await this.db.setting.set('instanceId', instanceId);
          console.log('[LicenseEnhanced] 生成新的实例ID:', instanceId);
        }
        this._cachedInstanceId = instanceId;
        console.log('[LicenseEnhanced] 实例ID缓存已初始化:', instanceId);
        return instanceId;
      } catch (error) {
        console.error('[LicenseEnhanced] 初始化实例ID失败:', error);
        // 生成固定的fallback ID（基于错误时间戳，保证相对稳定）
        const { uuid } = require('../../core');
        const fallbackId = uuid.v5(`fallback-${Date.now()}`, uuid.v5.DNS);
        this._cachedInstanceId = fallbackId;
        console.warn('[LicenseEnhanced] 使用fallback实例ID:', fallbackId);
        return fallbackId;
      }
    })();
    
    return this._instanceIdInitPromise;
  }

  /**
   * 获取实例ID（同步，使用缓存）
   */
  getInstanceId() {
    if (!this._cachedInstanceId) {
      // 如果缓存未准备好，使用基于机器特征的稳定ID
      if (!this._temporaryInstanceId) {
        const { uuid } = require('../../core');
        const os = require('os');
        // 基于主机名和平台生成相对稳定的临时ID
        const machineId = `${os.hostname()}-${os.platform()}-${os.arch()}`;
        this._temporaryInstanceId = uuid.v5(machineId, uuid.v5.DNS);
        console.warn('[LicenseEnhanced] 实例ID缓存未准备好，使用基于机器特征的临时ID:', this._temporaryInstanceId);
      }
      return this._temporaryInstanceId;
    }
    return this._cachedInstanceId;
  }

  /**
   * 启用/禁用增强授权模式
   */
  async setEnhancedMode(enabled) {
    this.config.enabled = enabled;
    await this.db.setting.set('licenseEnhancedMode', enabled);
    console.log('[LicenseEnhanced] 增强授权模式:', enabled ? '已启用' : '已禁用');
  }

  /**
   * 检查License状态
   */
  async checkLicenseStatus(licenseKey) {
    if (!this.config.enabled) {
      return { success: false, message: '增强授权模式未启用' };
    }

    const instanceId = this.getInstanceId();
    return await this.bindingManager.checkLicenseStatus(licenseKey, instanceId);
  }



  /**
   * 激活License
   */
  async activateLicense(licenseKey, forceTransfer = false) {
    if (!this.config.enabled) {
      return { success: false, message: '增强授权模式未启用' };
    }

    const instanceId = this.getInstanceId();
    return await this.bindingManager.activateLicense(licenseKey, instanceId, forceTransfer);
  }

  /**
   * 解绑License
   */
  async unbindLicense(licenseKey, instanceId) {
    if (!this.config.enabled) {
      return { success: false, message: '增强授权模式未启用' };
    }

    return await this.bindingManager.unbindLicense(licenseKey, instanceId);
  }

  /**
   * 智能解绑License - 查找实际绑定的instanceId并解绑
   */
  async smartUnbindLicense(licenseKey) {
    if (!this.config.enabled) {
      return { success: false, message: '增强授权模式未启用' };
    }

    try {
      // 先查询该license的绑定状态
      const statusResult = await this.bindingManager.checkLicenseStatus(licenseKey, 'dummy-id');
      
      if (statusResult.success && statusResult.binding && statusResult.binding.instanceId) {
        // 找到了实际绑定的instanceId，使用它进行解绑
        const actualInstanceId = statusResult.binding.instanceId;
        console.log('[LicenseEnhanced] 找到实际绑定的instanceId:', actualInstanceId);
        
        return await this.bindingManager.unbindLicense(licenseKey, actualInstanceId);
      } else {
        return { success: false, error: 'No active binding found for this license' };
      }
    } catch (error) {
      console.error('[LicenseEnhanced] 智能解绑失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 验证License (包含离线缓存)
   */
  async verifyLicense(licenseKey) {
    if (!this.config.enabled) {
      return { success: false, message: '增强授权模式未启用' };
    }

    const instanceId = this.getInstanceId();
    return await this.statusChecker.verifyLicense(licenseKey, instanceId);
  }

  /**
   * 获取套餐显示名称
   * 临时恢复此方法以确保激活过程中显示正确的套餐名称
   */
  getPlanDisplayName(planType) {
    // 最小兜底：不再本地映射，直接回显类型或占位
    return planType || 'Unknown Plan';
  }

  /**
   * 获取当前License状态 - 简化版本
   * 原则：统一数据源，只使用API返回的授权信息
   */
  async getCurrentLicenseStatus() {
    // 唯一数据源：license_info（来自在线API验证）
    const licenseInfo = await this.db.setting.get('license_info');
    
    // 🔧 调试日志：详细记录缓存读取过程
    console.log('[DEBUG] getCurrentLicenseStatus 缓存读取结果:', {
      hasLicenseInfo: !!licenseInfo,
      isValid: licenseInfo?.isValid,
      planName: licenseInfo?.planName,
      planType: licenseInfo?.planType,
      maxNodes: licenseInfo?.maxNodes,
      source: 'database_cache'
    });

    if (licenseInfo && licenseInfo.isValid) {
      // 直接使用API返回的权威数据，不做任何客户端推断或修改
      return {
        licenseKey: licenseInfo.key || '',
        status: 'active',
        lastCheck: licenseInfo.lastVerifiedTime || Date.now(),
        instanceId: this.getInstanceId(),
        enhancedMode: this.config.enabled,

        // 套餐信息：完全使用API返回的数据
        planName: licenseInfo.planDisplayName || licenseInfo.planName || 'Unknown Plan',
        planDisplayName: licenseInfo.planDisplayName || licenseInfo.planName || 'Unknown Plan',
        planType: licenseInfo.planType || licenseInfo.planIdentifier || 'unknown',

        // 限制信息：完全使用API返回的数据
        maxNodes: licenseInfo.maxNodes || 0,

        // 功能信息：完全使用API返回的数据
        features: licenseInfo.features || [],
        featuresMask: licenseInfo.featuresMask || 0,
        featureDetails: licenseInfo.featureDetails,
        permissions: licenseInfo.permissions || {},

        // 缓存信息
        isCached: true,
        cacheExpiry: new Date(licenseInfo.lastVerifiedTime + (7 * 24 * 60 * 60 * 1000)).toISOString()
      };
    }

    // 如果没有有效的授权信息，返回未授权状态
    // 不再进行任何客户端推断或fallback逻辑
    const licenseKey = await this.db.setting.get('currentLicenseKey') || '';
    const licenseStatus = await this.db.setting.get('licenseStatus') || 'inactive';

    return {
      licenseKey,
      status: licenseStatus,
      lastCheck: await this.db.setting.get('lastLicenseCheck') || 0,
      instanceId: this.getInstanceId(),
      enhancedMode: this.config.enabled,

      // 未授权状态：不提供任何功能或限制信息
      planName: 'Unauthorized',
      planDisplayName: 'Unauthorized',
      planType: 'unauthorized',
      maxNodes: 0,
      features: [],
      featuresMask: 0,
      featureDetails: null,
      permissions: {},

      isCached: false,
      message: '请激活许可证以获取授权信息'
    };
  }

  /**
   * 设置当前License
   */
  async setCurrentLicense(licenseKey, status = 'active', planInfo = null) {
    // 仅记录关键状态，不在此处构造/推断任何套餐信息
    const licKey = typeof licenseKey === 'string' ? licenseKey : (licenseKey?.key || licenseKey?.licenseKey || '');
    if (!licKey || typeof licKey !== 'string') {
      console.error('[LicenseEnhanced] setCurrentLicense: 无效的许可证密钥类型:', typeof licenseKey, licenseKey);
      return;
    }
    await this.db.setting.set('currentLicenseKey', licKey);
    await this.db.setting.set('licenseStatus', status);
    await this.db.setting.set('lastLicenseCheck', Date.now());

    if (status !== 'active') {
      // 清除 license_info，等待后续在线验证重新写入
      await this.db.setting.set('license_info', null);
      try {
        await this.db.setting.set('license_info_meta', {
          lastWriter: 'licenseEnhanced.setCurrentLicense(clear)',
          lastTimestamp: Date.now(),
          reason: 'setCurrentLicense:inactive'
        });
        let log = await this.db.setting.get('license_info_write_log');
        if (!Array.isArray(log)) log = [];
        log.push({ ts: Date.now(), writer: 'licenseEnhanced.setCurrentLicense(clear)' });
        if (log.length > 10) log = log.slice(log.length - 10);
        await this.db.setting.set('license_info_write_log', log);
      } catch (_) {}
      if (this.featureChecker && this.featureChecker.updateLicenseCache) {
        await this.featureChecker.updateLicenseCache();
      }
      console.log('[LicenseEnhanced] License状态非活跃，已清除license_info');
    }

    console.log('[LicenseEnhanced] 当前License已更新:', licKey);
  }

  /**
   * 验证maxNodes值 - 简化版本
   * 原则：完全信任服务器返回的数据，不做客户端修正
   */
  validateAndCorrectMaxNodes(maxNodes, planType) {
    // 只记录日志，不做任何修正
    console.log(`[LicenseEnhanced] 服务器返回maxNodes: ${maxNodes}, planType: ${planType}`);

    // 完全信任服务器返回的值
    return maxNodes;
  }

  /**
   * 从License Key推断套餐信息 - 简化版本
   * 原则：不再进行客户端推断，只返回基本的未知状态
   */
  async inferPlanFromLicenseKey(licenseKey) {
    console.warn('[LicenseEnhanced] inferPlanFromLicenseKey 已废弃：套餐信息必须来源于 License API');
    return null;
  }

  /**
   * 清除当前License
   */
  async clearCurrentLicense() {
    await this.db.setting.set('currentLicenseKey', '');
    await this.db.setting.set('licenseStatus', 'inactive');
    await this.db.setting.set('lastLicenseCheck', 0);
    await this.db.setting.set('license_info', null); // 同时清除license_info
    
    // 更新FeatureChecker缓存
    if (this.featureChecker && this.featureChecker.updateLicenseCache) {
      await this.featureChecker.updateLicenseCache();
    }
    
    console.log('[LicenseEnhanced] 当前License已清除');
  }

  /**
   * 启动时检查授权状态
   */
  async checkLicenseOnStartup() {
    if (!this.config.enabled) {
      console.log('[LicenseEnhanced] 增强授权模式未启用，跳过启动检查');
      return { success: true, mode: 'disabled' };
    }

    let licenseKey = await this.db.setting.get('currentLicenseKey');

    // 如果没有currentLicenseKey，尝试从license_info恢复
    if (!licenseKey) {
      const licenseInfo = await this.db.setting.get('license_info');
      if (licenseInfo && (licenseInfo.key || licenseInfo.licenseKey)) {
        licenseKey = licenseInfo.licenseKey || licenseInfo.key;
        console.log('[LicenseEnhanced] 从license_info恢复许可证密钥');
        await this.db.setting.set('currentLicenseKey', licenseKey);
      }
    }

    // 许可证通过Web管理面板设置，不从环境变量读取

    if (!licenseKey) {
      console.log('[LicenseEnhanced] 无授权信息，检查是否有用户会话和绑定的许可证');

      // 检查是否有用户登录会话
      if (this.isUserLoggedIn()) {
        console.log('[LicenseEnhanced] 发现用户登录会话，尝试自动恢复许可证绑定');
        try {
          await this.autoRecoverLicenseBinding();
          // 重新获取恢复后的许可证密钥
          licenseKey = await this.db.setting.get('currentLicenseKey');
        } catch (error) {
          console.warn('[LicenseEnhanced] 自动恢复许可证绑定失败:', error.message);
        }
      }

      if (!licenseKey) {
        return { success: false, message: '无授权信息' };
      }
    }

    const instanceId = this.getInstanceId();
    const result = await this.statusChecker.verifyLicense(licenseKey, instanceId);

    if (result.success) {
      const mode = result.mode || 'unknown';
      console.log(`[LicenseEnhanced] 授权验证成功 (${mode}模式)`);

      // 如果是离线模式，记录缓存信息
      if (mode === 'offline') {
        const cacheStatus = this.statusChecker.getCacheStatus(licenseKey);
        if (cacheStatus.exists && cacheStatus.valid) {
          const remainingHours = Math.round(cacheStatus.remainingTime / (60 * 60 * 1000));
          console.log(`[LicenseEnhanced] 使用离线缓存，剩余有效期: ${remainingHours}小时`);
        }
      }

      {
        // 🔧 修复：优先从数据库读取现有的正确许可证信息作为备用
        const existingLicenseInfo = await this.db.setting.get('license_info');
        
        // 🔧 调试日志：详细记录API验证结果
        console.log('[DEBUG] checkLicenseOnStartup API验证结果:', {
          hasResultPlanInfo: !!result.planInfo,
          hasResultData: !!result.data,
          resultDataKeys: result.data ? Object.keys(result.data) : [],
          existingPlanName: existingLicenseInfo?.planName,
          existingPlanType: existingLicenseInfo?.planType,
          existingMaxNodes: existingLicenseInfo?.maxNodes
        });
        
        // 从验证结果中提取套餐信息
        let planInfo = result.planInfo;
        
        // 如果没有planInfo，尝试从result.data构建
        if (!planInfo && result.data) {
          // 🔧 修复：使用现有数据作为默认值，防止错误覆盖
          const fallbackPlanType = existingLicenseInfo?.planType || existingLicenseInfo?.planIdentifier || 'unknown';
          const fallbackPlanName = existingLicenseInfo?.planName || 'Unknown Plan';
          const fallbackMaxNodes = existingLicenseInfo?.maxNodes || 10;
          
          console.log(`[DEBUG] API数据不完整，使用现有备用数据: ${fallbackPlanName} (${fallbackPlanType})`);
          console.log('[DEBUG] result.data内容:', {
            planDisplayName: result.data.planDisplayName,
            planName: result.data.planName,
            plan: result.data.plan,
            planType: result.data.planType,
            maxNodes: result.data.maxNodes
          });
          
          planInfo = {
            planName: result.data.planDisplayName || result.data.planName || result.data.plan || fallbackPlanName,
            planType: result.data.plan || result.data.planType || fallbackPlanType,
            maxNodes: result.data.maxNodes || fallbackMaxNodes,
            expiresAt: result.data.expiresAt || existingLicenseInfo?.expiresAt,
            features: result.data.features || existingLicenseInfo?.features || []
          };
          
          console.log('[DEBUG] 构建的planInfo:', planInfo);
        }
        
        // 不再本地构造套餐信息，直接记录激活状态
        await this.setCurrentLicense(licenseKey, 'active');
      }
      
      // 启动心跳服务
      await this.startHeartbeat();
      
      // 注释：老许可证系统已废弃，不再需要同步
    } else {
      console.warn('[LicenseEnhanced] 授权验证失败:', result.error);
      this.db.setting.set('licenseStatus', 'invalid');
    }

    return result;
  }

  /**
   * 自动恢复许可证绑定（当发现用户会话但无许可证时）
   */
  async autoRecoverLicenseBinding() {
    const instanceId = this.getInstanceId();
    console.log('[LicenseEnhanced] 开始自动恢复许可证绑定流程');

    // 获取用户许可证列表
    const licenseListResult = await this.getUserLicenseList();
    if (!licenseListResult.success) {
      console.warn('[LicenseEnhanced] 获取许可证列表失败:', licenseListResult.message);
      return;
    }

    const licenses = licenseListResult.licenses || [];
    console.log(`[LicenseEnhanced] 获取到 ${licenses.length} 个许可证`);

    // 寻找已绑定到当前实例的许可证
    const boundLicense = licenses.find(license => {
      const boundInstanceId = license.boundInstanceId || license.bound_instance_id || license.instanceId;
      const planName = (license.planName || license.plan_name || '').toLowerCase();
      const planType = (license.planType || license.plan_type || '').toLowerCase();
      const isFree = planName.includes('免费') || planName.includes('free') || planType === 'free';
      
      return boundInstanceId === instanceId && !isFree;
    });

    if (boundLicense) {
      console.log('[LicenseEnhanced] 发现已绑定的许可证，恢复状态');
      const licenseStatus = boundLicense.licenseStatus || boundLicense.status || 'active';
      const planInfo = {
        planName: boundLicense.planName || boundLicense.plan_name || boundLicense.planDisplayName || '',
        planType: boundLicense.planType || boundLicense.plan_type || 'standard',
        maxNodes: boundLicense.maxNodes || boundLicense.max_nodes || 10
      };
      await this.setCurrentLicense(boundLicense.licenseKey, licenseStatus, planInfo);

      // 注释：老许可证系统已废弃，不再需要同步

      console.log('[LicenseEnhanced] 许可证状态已恢复:', {
        licenseKey: boundLicense.licenseKey?.substring(0, 20) + '...',
        planName: boundLicense.planName || boundLicense.plan_name
      });
    } else {
      console.log('[LicenseEnhanced] 未找到已绑定到当前实例的许可证');
    }
  }

  /**
   * License Server用户登录
   */
  async loginLicenseServer(email, password) {
    return await this.userSession.login(email, password);
  }

  /**
   * License Server用户登出
   */
  async logoutLicenseServer() {
    return await this.userSession.logout();
  }

  /**
   * 获取用户License列表
   */
  async getUserLicenseList() {
    return await this.userSession.getLicenseList();
  }

  /**
   * 快速激活License
   */
  async quickActivateLicense(licenseKey) {
    const instanceId = this.getInstanceId();
    console.log(`[LicenseEnhanced] 开始快速激活License: ${licenseKey.substring(0, 8)}...`);

    try {
      // 调用License Server的快速激活API
      const result = await this.userSession.quickActivate(licenseKey, instanceId);

      if (result.success) {
        console.log('[LicenseEnhanced] 快速激活成功，更新本地状态');

        // 仅记录密钥与状态，不在此处推断套餐信息
        await this.setCurrentLicense(licenseKey, 'active');

        // 在线验证并由 StatusChecker 更新 license_info
        console.log('[LicenseEnhanced] 触发状态验证更新缓存');
        try {
          const online = await this.statusChecker.verifyOnline(licenseKey, instanceId);
          if (online && online.success) {
            this.statusChecker.updateCache(licenseKey, { ...online.data, planInfo: online.planInfo });
          }
        } catch (verifyError) {
          console.warn('[LicenseEnhanced] 状态验证失败，但激活已成功:', verifyError.message);
        }

        // 启动心跳服务
        try {
          await this.startHeartbeat();
        } catch (heartbeatError) {
          console.warn('[LicenseEnhanced] 启动心跳服务失败:', heartbeatError.message);
        }

        console.log('[LicenseEnhanced] 快速激活完成，本地状态已更新');
      } else {
        console.warn('[LicenseEnhanced] 快速激活失败:', result.error || result.message);
      }

      return result;

    } catch (error) {
      console.error('[LicenseEnhanced] 快速激活异常:', error);
      return {
        success: false,
        error: 'ACTIVATION_ERROR',
        message: '激活过程中发生异常: ' + error.message
      };
    }
  }

  /**
   * 检查用户登录状态
   */
  isUserLoggedIn() {
    if (!this.userSession || typeof this.userSession.isLoggedIn !== 'function') {
      console.warn('[LicenseEnhanced] userSession未初始化或方法不可用，返回默认值false');
      return false;
    }
    return this.userSession.isLoggedIn();
  }

  /**
   * 获取当前登录用户信息
   */
  getCurrentUser() {
    if (!this.userSession || typeof this.userSession.getCurrentUser !== 'function') {
      console.warn('[LicenseEnhanced] userSession未初始化或方法不可用，返回默认值null');
      return null;
    }
    return this.userSession.getCurrentUser();
  }

  /**
   * 获取模块状态信息
   */
  async getModuleStatus() {
    return {
      enabled: this.config.enabled,
      testMode: this.config.testMode,
      serverUrl: this.config.serverUrl,
      instanceId: this.getInstanceId(),
      currentLicense: await this.getCurrentLicenseStatus(),
      userSession: {
        isLoggedIn: this.isUserLoggedIn(),
        user: this.getCurrentUser()
      }
    };
  }

  /**
   * 执行健康检查
   */
  async performHealthCheck() {
    return await this.healthCheck.performHealthCheck();
  }

  /**
   * 启动心跳服务
   */
  async startHeartbeat() {
    if (!this.config.enabled) {
      console.log('[LicenseEnhanced] 增强模式未启用，不启动心跳服务');
      return;
    }

    const licenseStatus = await this.getCurrentLicenseStatus();
    if (!licenseStatus.licenseKey || licenseStatus.status !== 'active') {
      console.log('[LicenseEnhanced] 无有效许可证，不启动心跳服务');
      return;
    }

    console.log('[LicenseEnhanced] 启动心跳服务');
    await this.heartbeatService.start();
  }

  /**
   * 停止心跳服务
   */
  stopHeartbeat() {
    console.log('[LicenseEnhanced] 停止心跳服务');
    this.heartbeatService.stop();
  }

  /**
   * 获取心跳服务状态
   */
  getHeartbeatStatus() {
    return this.heartbeatService.getStatus();
  }

  /**
   * 手动触发心跳
   */
  async triggerHeartbeat(reason) {
    return await this.heartbeatService.triggerHeartbeat(reason);
  }

  /**
   * 节点变化事件处理
   */
  async onNodeCountChanged(event, nodeInfo) {
    console.log(`[LicenseEnhanced] 节点状态变化:`, event, nodeInfo);
    
    // 委托给心跳服务处理
    if (this.heartbeatService) {
      await this.heartbeatService.onNodeStateChanged(event, nodeInfo);
    }
  }
  
  /**
   * 注册节点状态监听器
   */
  registerNodeListeners() {
    console.log('[LicenseEnhanced] 注册节点状态监听器');
    
    // 保存原始方法
    const originalInsert = this.db.servers.ins.bind(this.db.servers);
    const originalUpdateStatus = this.db.servers.upd_status.bind(this.db.servers);
    const originalDelete = this.db.servers.del.bind(this.db.servers);
    
    // 包装插入方法
    this.db.servers.ins = (...args) => {
      const result = originalInsert(...args);
      // 异步触发，不阻塞主流程
      setImmediate(() => {
        this.onNodeCountChanged('add', { sid: args[0], name: args[1] });
      });
      return result;
    };
    
    // 包装状态更新方法
    this.db.servers.upd_status = (sid, status) => {
      const server = this.db.servers.get(sid);
      const oldStatus = server ? server.status : null;
      const result = originalUpdateStatus(sid, status);
      
      if (oldStatus !== status) {
        setImmediate(() => {
          this.onNodeCountChanged('status_change', { 
            sid, 
            name: server ? server.name : sid,
            oldStatus,
            newStatus: status
          });
        });
      }
      
      return result;
    };
    
    // 包装删除方法
    this.db.servers.del = (sid) => {
      const server = this.db.servers.get(sid);
      const result = originalDelete(sid);
      setImmediate(() => {
        this.onNodeCountChanged('remove', { sid, name: server ? server.name : sid });
      });
      return result;
    };
  }

  /**
   * 自动检测和修复许可证状态
   * 在启动时检查本地许可证信息与License Server的一致性
   */
  async autoDetectAndRepairLicense() {
    try {
      console.log('[AUTO-DETECT] 开始自动检测许可证状态...');
      
      const instanceId = this.getInstanceId();
      const currentLicenseKey = await this.db.setting.get('currentLicenseKey');
      const licenseInfo = await this.db.setting.get('license_info');
      
      console.log(`[AUTO-DETECT] 实例ID: ${instanceId}`);
      console.log(`[AUTO-DETECT] 当前许可证: ${currentLicenseKey || '无'}`);
      console.log(`[AUTO-DETECT] 本地许可证信息: ${licenseInfo ? '存在' : '无'}`);
      
      // 情况1：有许可证密钥但没有本地信息 - 可能需要重新同步
      if (currentLicenseKey && !licenseInfo) {
        console.log('[AUTO-DETECT] 发现许可证密钥但缺少本地信息，尝试从License Server同步...');
        const syncResult = await this.syncLicenseFromServer(currentLicenseKey, instanceId);
        if (syncResult && syncResult.success) {
          console.log('[AUTO-DETECT] 许可证信息同步成功');
        } else {
          console.warn('[AUTO-DETECT] 许可证信息同步失败，保持最小兜底，等待网络恢复');
        }
        return;
      }
      
      // 情况2：没有任何许可证信息 - 检查License Server是否有绑定
      if (!currentLicenseKey && !licenseInfo) {
        console.log('[AUTO-DETECT] 本地无许可证信息，检查License Server是否有该实例的绑定...');
        await this.checkServerForExistingBinding(instanceId);
        return;
      }
      
      // 情况3：license_info存在但currentLicenseKey缺失 - 修复一致性
      if (!currentLicenseKey && licenseInfo && (licenseInfo.key || licenseInfo.licenseKey)) {
        const recoveredKey = licenseInfo.licenseKey || licenseInfo.key;
        console.log('[AUTO-DETECT] 检测到currentLicenseKey缺失，从license_info恢复...');
        this.db.setting.set('currentLicenseKey', recoveredKey);
        console.log('[AUTO-DETECT] 已恢复currentLicenseKey');
        
        // 重新同步确保数据完整
        await this.syncLicenseFromServer(recoveredKey, instanceId);
        return;
      }
      
      // 情况4：有许可证密钥但信息不完整 - 重新同步
      if (currentLicenseKey && licenseInfo && (!licenseInfo.features || !licenseInfo.planType || (!licenseInfo.licenseKey && !licenseInfo.key))) {
        console.log('[AUTO-DETECT] 检测到许可证信息不完整，重新同步...');
        const syncResult = await this.syncLicenseFromServer(currentLicenseKey, instanceId);
        if (!syncResult || !syncResult.success) {
          console.warn('[AUTO-DETECT] 同步失败，保持最小兜底，等待网络恢复');
        }
        return;
      }
      
      // 情况5：有完整的许可证信息 - 验证一致性
      if (currentLicenseKey && licenseInfo) {
        console.log('[AUTO-DETECT] 检测到完整许可证信息，验证一致性...');
        await this.validateLicenseConsistency(currentLicenseKey, instanceId);
        return;
      }
      
      console.log('[AUTO-DETECT] 自动检测完成');
      
    } catch (error) {
      console.error('[AUTO-DETECT] 自动检测过程中出错:', error);
    }
  }

  /**
   * 从License Server同步许可证信息
   */
  async syncLicenseFromServer(licenseKey, instanceId) {
    try {
      console.log(`[SYNC] 尝试从License Server同步许可证信息: ${licenseKey}`);
      console.log(`[SYNC] License Server URL: ${this.config.serverUrl}`);

      // 调用License Server验证API
      const verifyUrl = `${this.config.serverUrl}/api/license/status`;
      console.log(`[SYNC] 验证URL: ${verifyUrl}`);
      const verifyData = {
        licenseKey: licenseKey,
        instanceId: instanceId,
        nodeCount: await this.getActiveNodeCount(),
        features: []
      };
      
      const fetch = require('node-fetch');
      const response = await fetch(verifyUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(verifyData),
        timeout: 10000
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          console.log('[SYNC] License Server验证成功，保存许可证信息');
          
          // 获取现有的license_info以保留重要字段
          const existingInfo = this.db.setting.get('license_info') || {};
          
          // 适配license/status API的扁平化结构
          const responseData = result.data || {};
          const binding = responseData.binding || {};
          
          // 保存完整的许可证信息到本地（不做本地推断，仅信任服务端数据；缺失则从严置空/0）
          const licenseInfo = {
            key: licenseKey, // 关键字段：许可证密钥
            licenseKey: licenseKey, // 添加 licenseKey 字段确保兼容性
            // 适配license/status API：使用status字段判断有效状态
            isValid: responseData.status === 'active', // 关键字段：有效状态
            lastVerifiedTime: Date.now(),
            machineId: instanceId,
            // 从responseData获取套餐类型
            planType: responseData.plan || responseData.planType || existingInfo.planType || 'unknown',
            // 优先使用服务器返回的显示名称
            planName: responseData.planDisplayName || responseData.planName || existingInfo.planName || '',
            planIdentifier: responseData.plan || responseData.planType || responseData.planIdentifier || 'unknown',
            // 从responseData获取节点限制
            maxNodes: (typeof responseData.maxNodes === 'number') ? responseData.maxNodes : (typeof existingInfo.maxNodes === 'number' ? existingInfo.maxNodes : 0),
            features: Array.isArray(responseData.features) ? responseData.features : (Array.isArray(existingInfo.features) ? existingInfo.features : []),
            featuresMask: (typeof responseData.featuresMask === 'number') ? responseData.featuresMask : (typeof existingInfo.featuresMask === 'number' ? existingInfo.featuresMask : 0),
            featureList: Array.isArray(responseData.featureList) ? responseData.featureList : (Array.isArray(existingInfo.featureList) ? existingInfo.featureList : []),
            featureDetails: responseData.featureDetails || existingInfo.featureDetails, // 保留现有的 featureDetails
            permissions: responseData.permissions || existingInfo.permissions || {},
            // 从responseData获取过期时间
            expiresAt: responseData.expiresAt || existingInfo.expiresAt,
            serverExpirationTime: responseData.expiresAt || existingInfo.serverExpirationTime,
            // 绑定相关信息
            boundAt: binding.boundAt,
            lastSeen: binding.lastSeen,
            isBound: responseData.status === 'active',
            instanceSecret: responseData.instanceSecret || existingInfo.instanceSecret, // 优先使用新返回的实例密钥
            syncedAt: new Date().toISOString(),
            source: 'auto_sync',
            // 消息中使用服务器返回的显示名称
            message: `当前套餐: ${responseData.planDisplayName || responseData.planName || existingInfo.planName || ''}`
          };
          
          // 保存所有相关字段
          this.db.setting.set('license_info', licenseInfo);
          try {
            await this.db.setting.set('license_info_meta', {
              lastWriter: 'licenseEnhanced.syncLicenseFromServer',
              lastTimestamp: Date.now(),
              keyPrefix: (licenseInfo?.licenseKey || licenseInfo?.key || '').toString().slice(0,8) + ((licenseInfo?.licenseKey || licenseInfo?.key) ? '...' : ''),
              plan: licenseInfo?.planName || licenseInfo?.planDisplayName || licenseInfo?.planType || 'unknown',
              maxNodes: licenseInfo?.maxNodes || 0,
              featuresCount: Array.isArray(licenseInfo?.features) ? licenseInfo.features.length : 0
            });
            let log = await this.db.setting.get('license_info_write_log');
            if (!Array.isArray(log)) log = [];
            log.push({ ts: Date.now(), writer: 'licenseEnhanced.syncLicenseFromServer' });
            if (log.length > 10) log = log.slice(log.length - 10);
            await this.db.setting.set('license_info_write_log', log);
          } catch (_) {}
          this.db.setting.set('licenseStatus', 'active');
          this.db.setting.set('currentLicenseKey', licenseKey);
          
          // 如果服务器返回了实例密钥，保存它
          if (result.data.instanceSecret) {
            this.db.setting.set('serverInstanceSecret', result.data.instanceSecret);
          }
          
          // 同时更新缓存
          if (this.statusChecker) {
            this.statusChecker.updateCache(licenseKey, result.data);
          }
          
          console.log('[SYNC] 许可证信息同步成功:', {
            licenseKey: licenseKey.substring(0, 20) + '...',
            planType: licenseInfo.planType,
            planName: licenseInfo.planName,
            maxNodes: licenseInfo.maxNodes,
            features: licenseInfo.features.length,
            hasFeatureDetails: !!licenseInfo.featureDetails,
            hasInstanceSecret: !!result.data.instanceSecret
          });
          
          return { success: true, licenseInfo };
        } else {
          console.log(`[SYNC] License Server验证失败: ${result.message}`);
          if (result.data && result.data.autoRepairAttempted) {
            console.log('[SYNC] License Server已尝试自动修复，但仍失败');
          }
          return { success: false, error: result.message };
        }
      } else {
        console.log(`[SYNC] License Server请求失败: ${response.status}`);
        return { success: false, error: `HTTP ${response.status}` };
      }
      
    } catch (error) {
      console.error('[SYNC] 同步许可证信息失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 检查License Server是否有该实例的绑定
   */
  async checkServerForExistingBinding(instanceId) {
    try {
      console.log(`[CHECK-BINDING] 检查实例 ${instanceId} 在License Server的绑定状态`);
      
      // 1. 通过instanceId获取客户端状态（包含licenseKey）
      const fetch = require('node-fetch');
      const statusUrl = `${this.config.serverUrl}/api/client/status/${instanceId}`;
      
      console.log(`[CHECK-BINDING] 请求客户端状态: ${statusUrl}`);
      const statusResponse = await fetch(statusUrl, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        timeout: this.config.apiTimeout
      });

      if (statusResponse.ok) {
        const statusResult = await statusResponse.json();
        console.log(`[CHECK-BINDING] 客户端状态响应:`, statusResult);
        
        if (statusResult.success && statusResult.data && statusResult.data.client && statusResult.data.client.licenseKey) {
          const licenseKey = statusResult.data.client.licenseKey;
          console.log(`[CHECK-BINDING] 发现绑定的许可证: ${licenseKey.substring(0, 8)}...`);
          
          // 🔧 修复：使用正常工作的 /api/license/status 端点检查绑定状态
          const bindingUrl = `${this.config.serverUrl}/api/license/status`;
          const bindingData = {
            licenseKey: licenseKey,
            instanceId: instanceId,
            nodeCount: 0,
            features: [],
            version: '1.0.0'
          };

          console.log(`[CHECK-BINDING] 检查绑定状态: ${bindingUrl}`);
          const bindingResponse = await fetch(bindingUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(bindingData),
            timeout: this.config.apiTimeout
          });
          
          if (bindingResponse.ok) {
            const bindingResult = await bindingResponse.json();
            console.log(`[CHECK-BINDING] 绑定状态结果:`, bindingResult);
            
            // 🔧 修复：适配 /api/license/status 的响应格式
            if (bindingResult.success && bindingResult.data && bindingResult.data.status === 'active') {
              const verifyData = bindingResult.data;

              // 检查绑定一致性（status端点通过status字段确认绑定状态）
              if (verifyData.status === 'active') {
                console.log(`[CHECK-BINDING] ✅ 数据一致，许可证已正确绑定`);

                // 仅记录密钥与状态
                await this.setCurrentLicense(licenseKey, 'active');

                // 在线校验，统一由 StatusChecker 写入权威 license_info
                try {
                  const online = await this.statusChecker.verifyOnline(licenseKey, instanceId);
                  if (online && online.success) {
                    this.statusChecker.updateCache(licenseKey, { ...online.data, planInfo: online.planInfo });
                  }
                } catch (e) {
                  console.warn('[CHECK-BINDING] 在线同步套餐信息失败:', e.message);
                }
                
                console.log(`[CHECK-BINDING] ✅ 自动恢复许可证成功: ${planInfo.planName}`);
                return { success: true, licenseKey, data: binding };
              } else {
                console.log(`[CHECK-BINDING] ⚠️ 绑定不一致 - 客户端状态: ${instanceId}`);
                console.log(`[CHECK-BINDING] 这可能需要数据同步修复`);
              }
            } else {
              console.log(`[CHECK-BINDING] 绑定状态查询失败: ${bindingResult.error || '未知错误'}`);
            }
          } else {
            const errorText = await bindingResponse.text();
            console.log(`[CHECK-BINDING] 绑定状态请求失败: ${bindingResponse.status}`);
            console.log(`[CHECK-BINDING] 错误响应: ${errorText}`);
          }
        } else {
          console.log('[CHECK-BINDING] 该实例没有绑定的许可证');
        }
      } else {
        console.log(`[CHECK-BINDING] 获取客户端状态失败: ${statusResponse.status}`);
      }
      
      return { success: false, error: 'NO_EXISTING_BINDING' };
      
    } catch (error) {
      console.error('[CHECK-BINDING] 检查绑定状态失败:', error);
      return { success: false, error: 'CHECK_FAILED' };
    }
  }

  /**
   * 验证许可证一致性
   */
  async validateLicenseConsistency(licenseKey, instanceId) {
    try {
      console.log(`[VALIDATE] 验证许可证一致性: ${licenseKey}`);
      
      // 尝试验证当前许可证
      await this.syncLicenseFromServer(licenseKey, instanceId);
      
    } catch (error) {
      console.error('[VALIDATE] 验证许可证一致性失败:', error);
    }
  }

  /**
   * 获取当前活跃节点数量
   */
  async getActiveNodeCount() {
    try {
      const servers = await this.db.servers.all();
      const activeServers = servers.filter(server => 
        server && server.status && server.status !== 'offline'
      );
      return activeServers.length;
    } catch (error) {
      console.error('获取活跃节点数量失败:', error);
      return 0;
    }
  }
}

module.exports = (app, db) => {
  const licenseEnhanced = new LicenseEnhancedModule(app, db);

  // 注册路由
  require('./routes')(app, licenseEnhanced);

  // 启动时检查授权状态
  process.nextTick(async () => {
    try {
      // 等待一小段时间确保数据库已完全初始化
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 调用异步初始化方法 - 这是关键步骤
      await licenseEnhanced.initializeAsync();
      
      // 初始化FeatureChecker
      if (licenseEnhanced.featureChecker && licenseEnhanced.featureChecker.initializeAsync) {
        await licenseEnhanced.featureChecker.initializeAsync();
      }
      
      // 初始化Feature同步
      await licenseEnhanced.featureChecker.initializeFeatureSync();
      
      // 检查授权状态
      await licenseEnhanced.checkLicenseOnStartup();
    } catch (error) {
      console.error('[LicenseEnhanced] 启动检查失败:', error);
    }
  });

  return licenseEnhanced;
};

/**
 * 为 LicenseEnhanced 原型添加修复方法
 */
const LicenseEnhancedPrototype = LicenseEnhancedModule.prototype;

/**
 * 修复本地许可证数据
 */
LicenseEnhancedPrototype.repairLocalLicenseData = async function(licenseKey, existingInfo) {
  try {
    console.log('[REPAIR] 开始修复本地许可证数据...');
    if (!licenseKey || typeof licenseKey !== 'string') {
      console.error('[REPAIR] 无效的许可证密钥类型:', typeof licenseKey);
      throw new Error('无效的许可证密钥');
    }
    const cacheStatus = this.statusChecker.getCacheStatus(licenseKey);
    if (cacheStatus.exists && cacheStatus.valid) {
      const cached = cacheStatus.data || {};
      await this.db.setting.set('license_info', {
        ...cached,
        key: licenseKey,
        licenseKey: licenseKey,
        isValid: cached.isValid !== undefined ? cached.isValid : true,
        lastVerifiedTime: Date.now()
      });
      try {
        await this.db.setting.set('license_info_meta', {
          lastWriter: 'licenseEnhanced.repairLocalLicenseData',
          lastTimestamp: Date.now(),
          keyPrefix: String(licenseKey).slice(0,8) + '...'
        });
        let log = await this.db.setting.get('license_info_write_log');
        if (!Array.isArray(log)) log = [];
        log.push({ ts: Date.now(), writer: 'licenseEnhanced.repairLocalLicenseData' });
        if (log.length > 10) log = log.slice(log.length - 10);
        await this.db.setting.set('license_info_write_log', log);
      } catch (_) {}
      await this.db.setting.set('currentLicenseKey', licenseKey);
      await this.db.setting.set('licenseStatus', 'active');
      if (this.featureChecker && this.featureChecker.updateLicenseCache) {
        await this.featureChecker.updateLicenseCache();
      }
      console.log('[REPAIR] 已从离线缓存恢复许可证信息');
    } else {
      console.warn('[REPAIR] 无有效离线缓存，跳过本地修复');
    }
  } catch (error) {
    console.error('[REPAIR] 修复本地许可证数据失败:', error);
  }
};

/**
 * 根据套餐类型获取默认节点数
 */
LicenseEnhancedPrototype.getDefaultMaxNodes = function(planType) {
  const defaultNodes = {
    free: 5,
    standard: 10,
    pro: 100,
    enterprise: 200,
    unknown: 10
  };
  return defaultNodes[planType] || 10;
};

/**
 * 从许可证密钥推断套餐类型
 */
LicenseEnhancedPrototype.inferPlanTypeFromKey = async function(licenseKey) {
  if (!licenseKey || typeof licenseKey !== 'string') {
    console.warn('[LicenseEnhanced] inferPlanTypeFromKey: 无效的许可证密钥类型:', typeof licenseKey);
    return 'unknown';
  }

  // 根据密钥格式推断
  if (licenseKey.includes('DSTATUSFRE')) return 'free';
  if (licenseKey.includes('DSTATUSPRO')) return 'pro';
  if (licenseKey.includes('DSTATUSENT')) return 'enterprise';

  return 'standard';
};

/**
 * 从许可证密钥推断套餐名称
 * 注意：此方法已废弃，应该通过在线验证获取权威数据
 */
LicenseEnhancedPrototype.inferPlanNameFromKey = async function(licenseKey) {
  console.warn('[LicenseEnhanced] inferPlanNameFromKey 已废弃，请通过在线验证获取权威数据');

  // 不再进行客户端推断，返回未知状态强制在线验证
  return 'Unknown Plan';
};
