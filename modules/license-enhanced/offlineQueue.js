/**
 * 离线队列服务
 * 用于在网络异常时缓存心跳数据，并在网络恢复后自动重试
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

class OfflineQueue {
  constructor(options = {}) {
    // 智能选择队列目录
    let defaultQueueDir;
    // 1. 优先使用环境变量
    if (process.env.DSTATUS_QUEUE_DIR) {
      defaultQueueDir = process.env.DSTATUS_QUEUE_DIR;
    }
    // 2. pkg环境使用系统临时目录
    else if (process.pkg) {
      const os = require('os');
      defaultQueueDir = path.join(os.tmpdir(), 'dstatus-offline-queue');
    }
    // 3. 开发环境使用项目目录
    else {
      defaultQueueDir = path.join(__dirname, '../../data/offline-queue');
    }
    
    // 配置选项
    this.options = {
      queueDir: options.queueDir || defaultQueueDir,
      maxQueueSize: options.maxQueueSize || 100,        // 最大队列大小
      maxRetries: options.maxRetries || 3,              // 最大重试次数
      retryInterval: options.retryInterval || 60000,    // 重试间隔（毫秒）
      maxAge: options.maxAge || 7 * 24 * 60 * 60 * 1000, // 数据最大保存时间（7天）
      ...options
    };

    // 内存队列（用于快速访问）
    this.queue = new Map();
    this.retryTimer = null;
    this.isProcessing = false;

    // 确保队列目录存在
    this.ensureQueueDir();
  }

  /**
   * 确保队列目录存在
   */
  async ensureQueueDir() {
    try {
      await fs.access(this.options.queueDir);
    } catch (error) {
      try {
        await fs.mkdir(this.options.queueDir, { recursive: true });
        console.log('[OfflineQueue] 创建队列目录:', this.options.queueDir);
      } catch (mkdirError) {
        // 如果权限被拒绝，fallback到/tmp
        if (mkdirError.code === 'EACCES' || mkdirError.code === 'EROFS') {
          const os = require('os');
          this.options.queueDir = path.join(os.tmpdir(), 'dstatus-offline-queue');
          console.warn('[OfflineQueue] 原目录不可写，使用临时目录:', this.options.queueDir);
          await fs.mkdir(this.options.queueDir, { recursive: true });
        } else {
          throw mkdirError;
        }
      }
    }
  }

  /**
   * 生成唯一的队列项ID
   */
  generateQueueId() {
    return `${Date.now()}-${crypto.randomBytes(8).toString('hex')}`;
  }

  /**
   * 将心跳数据添加到队列
   */
  async enqueue(data, options = {}) {
    try {
      // 检查队列大小限制
      if (this.queue.size >= this.options.maxQueueSize) {
        // 移除最旧的项
        const oldestKey = this.queue.keys().next().value;
        await this.remove(oldestKey);
        console.log('[OfflineQueue] 队列已满，移除最旧的项:', oldestKey);
      }

      const queueId = this.generateQueueId();
      const queueItem = {
        id: queueId,
        data,
        timestamp: Date.now(),
        retries: 0,
        priority: options.priority || 'normal',
        error: null,
        ...options
      };

      // 保存到内存
      this.queue.set(queueId, queueItem);

      // 持久化到文件
      await this.persistItem(queueItem);

      console.log('[OfflineQueue] 数据已加入队列:', {
        id: queueId,
        type: data.triggerType || 'scheduled',
        nodeCount: data.data?.nodeCount
      });

      return queueId;
    } catch (error) {
      console.error('[OfflineQueue] 入队失败:', error);
      throw error;
    }
  }

  /**
   * 持久化队列项到文件
   */
  async persistItem(item) {
    const filename = path.join(this.options.queueDir, `${item.id}.json`);
    await fs.writeFile(filename, JSON.stringify(item, null, 2));
  }

  /**
   * 从文件加载队列项
   */
  async loadItem(filename) {
    try {
      const filepath = path.join(this.options.queueDir, filename);
      const content = await fs.readFile(filepath, 'utf8');
      return JSON.parse(content);
    } catch (error) {
      console.error('[OfflineQueue] 加载队列项失败:', filename, error);
      return null;
    }
  }

  /**
   * 从磁盘加载所有队列项
   */
  async loadFromDisk() {
    try {
      const files = await fs.readdir(this.options.queueDir);
      const jsonFiles = files.filter(f => f.endsWith('.json'));

      console.log(`[OfflineQueue] 从磁盘加载 ${jsonFiles.length} 个队列项`);

      for (const file of jsonFiles) {
        const item = await this.loadItem(file);
        if (item) {
          // 检查是否过期
          if (Date.now() - item.timestamp > this.options.maxAge) {
            console.log('[OfflineQueue] 队列项已过期，删除:', item.id);
            await this.remove(item.id);
          } else {
            this.queue.set(item.id, item);
          }
        }
      }

      console.log(`[OfflineQueue] 成功加载 ${this.queue.size} 个有效队列项`);
    } catch (error) {
      console.error('[OfflineQueue] 加载队列失败:', error);
    }
  }

  /**
   * 处理队列中的所有项
   */
  async processQueue(sendFunction) {
    if (this.isProcessing) {
      console.log('[OfflineQueue] 队列正在处理中，跳过');
      return;
    }

    this.isProcessing = true;
    const results = { success: 0, failed: 0, retry: 0 };

    try {
      // 按优先级和时间戳排序
      const sortedItems = Array.from(this.queue.values()).sort((a, b) => {
        if (a.priority === 'high' && b.priority !== 'high') return -1;
        if (a.priority !== 'high' && b.priority === 'high') return 1;
        return a.timestamp - b.timestamp;
      });

      console.log(`[OfflineQueue] 开始处理 ${sortedItems.length} 个队列项`);

      for (const item of sortedItems) {
        try {
          // 调用发送函数
          await sendFunction(item.data);
          
          // 发送成功，从队列中移除
          await this.remove(item.id);
          results.success++;
          
          console.log('[OfflineQueue] 发送成功:', item.id);
        } catch (error) {
          item.retries++;
          item.error = error.message;

          if (item.retries >= this.options.maxRetries) {
            // 超过最大重试次数，移除
            console.error('[OfflineQueue] 超过最大重试次数，丢弃:', item.id);
            await this.remove(item.id);
            results.failed++;
          } else {
            // 更新重试信息并保存
            await this.persistItem(item);
            results.retry++;
            console.log(`[OfflineQueue] 发送失败，等待重试 (${item.retries}/${this.options.maxRetries}):`, item.id);
          }
        }
      }

      console.log('[OfflineQueue] 处理完成:', results);
    } finally {
      this.isProcessing = false;
    }

    return results;
  }

  /**
   * 从队列中移除项
   */
  async remove(queueId) {
    this.queue.delete(queueId);
    
    try {
      const filename = path.join(this.options.queueDir, `${queueId}.json`);
      await fs.unlink(filename);
    } catch (error) {
      // 文件可能不存在，忽略错误
    }
  }

  /**
   * 启动自动重试
   */
  startAutoRetry(sendFunction, interval) {
    const retryInterval = interval || this.options.retryInterval;
    
    if (this.retryTimer) {
      clearInterval(this.retryTimer);
    }

    this.retryTimer = setInterval(async () => {
      if (this.queue.size > 0) {
        console.log(`[OfflineQueue] 自动重试: ${this.queue.size} 个待处理项`);
        await this.processQueue(sendFunction);
      }
    }, retryInterval);

    console.log(`[OfflineQueue] 启动自动重试，间隔: ${retryInterval}ms`);
  }

  /**
   * 停止自动重试
   */
  stopAutoRetry() {
    if (this.retryTimer) {
      clearInterval(this.retryTimer);
      this.retryTimer = null;
      console.log('[OfflineQueue] 停止自动重试');
    }
  }

  /**
   * 获取队列状态
   */
  getStatus() {
    const items = Array.from(this.queue.values());
    
    return {
      size: this.queue.size,
      items: items.map(item => ({
        id: item.id,
        timestamp: item.timestamp,
        retries: item.retries,
        priority: item.priority,
        age: Date.now() - item.timestamp,
        error: item.error
      })),
      stats: {
        total: items.length,
        high: items.filter(i => i.priority === 'high').length,
        normal: items.filter(i => i.priority === 'normal').length,
        failed: items.filter(i => i.retries > 0).length
      }
    };
  }

  /**
   * 清空队列
   */
  async clear() {
    // 清空内存队列
    this.queue.clear();

    // 删除所有文件
    try {
      const files = await fs.readdir(this.options.queueDir);
      const jsonFiles = files.filter(f => f.endsWith('.json'));
      
      for (const file of jsonFiles) {
        await fs.unlink(path.join(this.options.queueDir, file));
      }

      console.log(`[OfflineQueue] 清空队列，删除 ${jsonFiles.length} 个文件`);
    } catch (error) {
      console.error('[OfflineQueue] 清空队列失败:', error);
    }
  }

  /**
   * 关闭队列服务
   */
  async shutdown() {
    this.stopAutoRetry();
    // 确保所有数据都已持久化
    for (const item of this.queue.values()) {
      await this.persistItem(item);
    }
    console.log('[OfflineQueue] 队列服务已关闭');
  }
}

module.exports = OfflineQueue;