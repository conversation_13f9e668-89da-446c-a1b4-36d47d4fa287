'use strict';

/**
 * DStatus客户端 - License Server用户会话管理
 * 基于T039和T040任务需求
 * 
 * 功能：
 * - License Server登录/登出
 * - 获取用户License列表
 * - 快速激活License
 * - 会话状态管理
 * - 持久化授权（类OAuth机制）
 */

const fetch = require('node-fetch');
const PersistentAuth = require('./persistentAuth');

class LicenseServerSession {
  constructor(config, db) {
    this.config = config;
    this.db = db;
    this.token = null;
    this.userInfo = null;
    this.licenseList = [];
    
    // 初始化持久化授权管理器
    this.persistentAuth = new PersistentAuth(db, config);
    
    // 从本地存储恢复会话
    this.restoreSession();
    
    console.log('[UserSession] License Server用户会话管理器初始化完成');
  }

  /**
   * 获取当前有效的访问令牌
   */
  async getCurrentToken() {
    // 优先使用持久化授权的token
    if (this.persistentAuth.isAuthorized()) {
      const token = await this.persistentAuth.getAccessToken();
      if (token) {
        this.token = token; // 同步更新内存中的token
        return token;
      }
    }
    
    // 使用内存中的token
    return this.token;
  }

  /**
   * 通用的fetch包装器，添加超时和错误处理
   */
  async fetchWithTimeout(url, options = {}) {
    const timeout = options.timeout || this.config.apiTimeout;
    
    const fetchPromise = fetch(url, options);
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error('请求超时')), timeout)
    );
    
    try {
      const response = await Promise.race([fetchPromise, timeoutPromise]);
      return response;
    } catch (error) {
      if (error.message === '请求超时') {
        throw new Error(`请求超时 (${timeout}ms)`);
      }
      throw error;
    }
  }

  /**
   * 用户登录License Server
   */
  async login(emailOrUsername, password) {
    try {
      console.log('[UserSession] 尝试登录License Server:', emailOrUsername);

      // 支持邮箱或用户名登录
      const loginData = {
        password: password
      };

      // 判断是邮箱还是用户名
      if (emailOrUsername.includes('@')) {
        loginData.email = emailOrUsername;
      } else {
        loginData.username = emailOrUsername;
      }

      const response = await this.fetchWithTimeout(`${this.config.serverUrl}/api/user-session/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(loginData)
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // 根据T034 API响应格式更新
        this.token = result.data.accessToken;
        this.userInfo = result.data.user;
        
        // 保存会话到本地
        this.saveSession();
        
        // 如果服务器返回了refreshToken，保存持久化授权
        if (result.data.refreshToken) {
          this.persistentAuth.saveAuthTokens({
            accessToken: result.data.accessToken,
            refreshToken: result.data.refreshToken,
            userInfo: this.userInfo
          });
        }

        const userIdentifier = this.userInfo.email || this.userInfo.username || emailOrUsername;
        console.log('[UserSession] 登录成功:', userIdentifier);
        return {
          success: true,
          message: '登录成功',
          user: this.userInfo
        };
      } else {
        console.error('[UserSession] 登录失败:', result.message);
        return {
          success: false,
          message: result.message || '登录失败'
        };
      }
    } catch (error) {
      console.error('[UserSession] 登录异常:', error);
      return {
        success: false,
        message: '网络连接失败，请检查License Server是否可访问'
      };
    }
  }

  /**
   * 用户登出
   */
  async logout() {
    try {
      if (this.token) {
        // 调用服务器登出API
        await this.fetchWithTimeout(`${this.config.serverUrl}/api/user-session/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
          }
        });
      }
    } catch (error) {
      console.warn('[UserSession] 登出API调用失败:', error);
    } finally {
      // 清除本地会话
      this.clearSession();
      console.log('[UserSession] 用户已登出');
    }

    return { success: true, message: '已登出' };
  }

  /**
   * 获取用户License列表
   */
  async getLicenseList() {
    if (!this.token) {
      return {
        success: false,
        message: '请先登录License Server'
      };
    }

    try {
      console.log('[UserSession] 获取用户License列表');

      const response = await this.fetchWithTimeout(`${this.config.serverUrl}/api/user-session/licenses`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // 根据T034 API响应格式更新
        this.licenseList = result.data.licenses || [];
        console.log('[UserSession] 获取到', this.licenseList.length, '个License');
        
        return {
          success: true,
          licenses: this.licenseList
        };
      } else {
        // 检查是否为token相关错误
        if (response.status === 401 || response.status === 403) {
          console.log('[UserSession] Token验证失败，清除会话');
          this.clearSession();
          return {
            success: false,
            message: 'Token verification failed: jwt expired'
          };
        }
        
        const errorMessage = result.message || '获取License列表失败';
        console.error('[UserSession] 获取License列表失败:', errorMessage);
        
        // 如果错误信息包含token相关字样，也清除会话
        if (errorMessage.toLowerCase().includes('jwt') || 
            errorMessage.toLowerCase().includes('token') || 
            errorMessage.toLowerCase().includes('expired') ||
            errorMessage.toLowerCase().includes('verification failed')) {
          console.log('[UserSession] 检测到JWT/Token错误，清除会话');
          this.clearSession();
        }
        
        return {
          success: false,
          message: errorMessage
        };
      }
    } catch (error) {
      console.error('[UserSession] 获取License列表异常:', error);
      return {
        success: false,
        message: '网络连接失败'
      };
    }
  }

  /**
   * 快速激活License
   * 注意：快速激活不需要用户登录，直接调用License Server的公开API
   */
  async quickActivate(licenseKey, instanceId) {
    try {
      console.log('[UserSession] 快速激活License:', licenseKey.substring(0, 8) + '...');

      // 直接调用License Server的快速激活API（不需要认证）
      const response = await this.fetchWithTimeout(`${this.config.serverUrl}/admin/api/license-enhanced/user/quick-activate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          licenseKey,
          instanceId,
          instanceInfo: {
            hostname: require('os').hostname(),
            platform: require('os').platform(),
            dstatusVersion: require('../../package.json').version || '1.0.0'
          }
        })
      });

      const result = await response.json();

      if (response.ok && result.status === 1) {
        console.log('[UserSession] 快速激活成功:', result.data);
        return {
          success: true,
          message: result.data.message || 'License激活成功',
          data: result.data
        };
      } else {
        console.error('[UserSession] 快速激活失败:', result.data || result.message);
        return {
          success: false,
          message: result.data || result.message || '快速激活失败'
        };
      }
    } catch (error) {
      console.error('[UserSession] 快速激活异常:', error);
      return {
        success: false,
        message: '激活失败: ' + error.message
      };
    }
  }

  /**
   * 解绑License (通过用户API)
   */
  async unbindLicense(licenseKey) {
    if (!this.token) {
      return {
        success: false,
        message: '请先登录License Server'
      };
    }

    try {
      console.log('[UserSession] 通过用户API解绑License:', licenseKey.substring(0, 20) + '...');

      const response = await this.fetchWithTimeout(`${this.config.serverUrl}/api/user/licenses/unbind`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          licenseKey
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        console.log('[UserSession] 用户API解绑成功');
        return {
          success: true,
          message: result.message || '解绑成功',
          data: result.data
        };
      } else {
        console.error('[UserSession] 用户API解绑失败:', result.message);
        return {
          success: false,
          error: result.message || result.error || '解绑失败'
        };
      }
    } catch (error) {
      console.error('[UserSession] 用户API解绑异常:', error);
      return {
        success: false,
        error: '解绑异常: ' + error.message
      };
    }
  }

  /**
   * 检查会话状态
   */
  isLoggedIn() {
    return !!this.token && !!this.userInfo;
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return this.userInfo;
  }

  /**
   * 获取缓存的License列表
   */
  getCachedLicenseList() {
    return this.licenseList;
  }

  /**
   * 保存会话到本地存储
   */
  saveSession() {
    const sessionData = {
      token: this.token,
      userInfo: this.userInfo,
      loginTime: Date.now()
    };

    this.db.setting.set('licenseServerSession', sessionData);
    console.log('[UserSession] 会话已保存到本地');
  }

  /**
   * 从本地存储恢复会话
   */
  async restoreSession() {
    // 首先尝试使用持久化授权
    if (this.persistentAuth.isAuthorized()) {
      console.log('[UserSession] 检测到持久化授权，尝试恢复...');
      
      const accessToken = await this.persistentAuth.getAccessToken();
      const userInfo = this.persistentAuth.getSavedUserInfo();
      
      if (accessToken && userInfo) {
        this.token = accessToken;
        this.userInfo = userInfo;
        console.log('[UserSession] 通过持久化授权恢复会话成功:', userInfo.email);
        return;
      }
    }
    
    // 如果持久化授权失败，尝试传统方式
    const sessionData = this.db.setting.get('licenseServerSession');
    
    if (sessionData) {
      const { token, userInfo, loginTime } = sessionData;
      
      // 检查会话是否过期 (24小时)
      const sessionAge = Date.now() - loginTime;
      const maxAge = 24 * 60 * 60 * 1000; // 24小时
      
      if (sessionAge < maxAge) {
        this.token = token;
        this.userInfo = userInfo;
        console.log('[UserSession] 会话已从本地恢复:', userInfo.email);
      } else {
        console.log('[UserSession] 本地会话已过期，已清除');
        this.clearSession();
      }
    }
  }

  /**
   * 清除本地会话
   */
  clearSession() {
    this.token = null;
    this.userInfo = null;
    this.licenseList = [];
    this.db.setting.set('licenseServerSession', null);
  }

  /**
   * 验证Token有效性
   */
  async validateToken() {
    if (!this.token) {
      return false;
    }

    try {
      const response = await this.fetchWithTimeout(`${this.config.serverUrl}/api/user-session/profile`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          this.userInfo = result.user;
          return true;
        }
      }
      
      // Token无效，清除会话
      this.clearSession();
      return false;
    } catch (error) {
      console.warn('[UserSession] Token验证失败:', error);
      return false;
    }
  }
}

module.exports = LicenseServerSession;
