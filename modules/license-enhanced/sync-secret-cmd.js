#!/usr/bin/env node

/**
 * 手动同步实例密钥的命令行工具
 * 用于调试和修复密钥不同步问题
 */

'use strict';

const path = require('path');
const Database = require('../../database');
const LicenseEnhanced = require('./index');
const InstanceSecretSync = require('./syncInstanceSecret');

async function main() {
  console.log('=== DStatus 实例密钥同步工具 ===\n');
  
  try {
    // 初始化数据库
    const dbPath = path.join(__dirname, '../../data/dstatus.db');
    const db = new Database(dbPath);
    
    // 初始化许可证模块
    const config = {
      serverUrl: await db.setting.get('licenseServerUrl') || 'https://license.example.com',
      apiTimeout: 30000
    };
    
    const licenseModule = new LicenseEnhanced(config, db);
    
    // 显示当前状态
    console.log('当前配置:');
    console.log('- 服务器URL:', config.serverUrl);
    console.log('- 实例ID:', licenseModule.getInstanceId());
    
    const licenseStatus = await licenseModule.getCurrentLicenseStatus();
    console.log('- 许可证密钥:', licenseStatus.licenseKey ? licenseStatus.licenseKey.substring(0, 20) + '...' : '未设置');
    
    // 检查当前密钥
    const currentServerSecret = db.setting.get('serverInstanceSecret');
    const currentLocalSecret = db.setting.get('instanceSecret');
    
    console.log('\n当前密钥状态:');
    console.log('- 服务端密钥:', currentServerSecret ? `已设置 (长度: ${currentServerSecret.length})` : '未设置');
    console.log('- 本地密钥:', currentLocalSecret ? `已设置 (长度: ${currentLocalSecret.length})` : '未设置');
    
    if (!licenseStatus.licenseKey) {
      console.error('\n❌ 错误: 未找到许可证密钥，请先激活许可证');
      process.exit(1);
    }
    
    // 创建同步工具
    const secretSync = new InstanceSecretSync(config, db, licenseModule);
    
    // 询问用户操作
    console.log('\n请选择操作:');
    console.log('1. 从服务器同步密钥');
    console.log('2. 验证当前密钥');
    console.log('3. 清除本地密钥缓存');
    console.log('4. 显示详细信息并退出');
    
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    rl.question('\n请输入选项 (1-4): ', async (answer) => {
      console.log('');
      
      switch (answer.trim()) {
        case '1':
          console.log('正在从服务器同步密钥...');
          const syncResult = await secretSync.syncFromServer();
          
          if (syncResult.success) {
            console.log(`✅ ${syncResult.message}`);
            if (syncResult.reactivated) {
              console.log('   (通过重新激活获取)');
            }
          } else {
            console.error(`❌ 同步失败: ${syncResult.error}`);
          }
          break;
          
        case '2':
          console.log('正在验证当前密钥...');
          const verifyResult = await secretSync.verifyCurrentSecret();
          
          if (verifyResult.valid) {
            console.log(`✅ 密钥有效`);
            console.log(`   类型: ${verifyResult.secretType}`);
            console.log(`   长度: ${verifyResult.secretLength}`);
          } else {
            console.error(`❌ 密钥无效: ${verifyResult.reason}`);
          }
          break;
          
        case '3':
          console.log('正在清除本地密钥缓存...');
          secretSync.clearLocalSecrets();
          console.log('✅ 本地密钥缓存已清除');
          console.log('   下次心跳时将重新生成或同步密钥');
          break;
          
        case '4':
          console.log('详细信息:');
          console.log(JSON.stringify({
            config: config,
            instanceId: licenseModule.getInstanceId(),
            licenseKey: licenseStatus.licenseKey ? licenseStatus.licenseKey.substring(0, 20) + '...' : null,
            serverSecret: currentServerSecret ? currentServerSecret.substring(0, 20) + '...' : null,
            localSecret: currentLocalSecret ? currentLocalSecret.substring(0, 20) + '...' : null,
            licenseStatus: {
              isValid: licenseStatus.isValid,
              expiresAt: licenseStatus.expiresAt,
              maxNodes: licenseStatus.maxNodes
            }
          }, null, 2));
          break;
          
        default:
          console.log('无效的选项');
      }
      
      rl.close();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// 运行主函数
main().catch(error => {
  console.error('未处理的错误:', error);
  process.exit(1);
});