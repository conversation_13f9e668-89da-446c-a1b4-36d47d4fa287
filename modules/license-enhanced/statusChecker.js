'use strict';

/**
 * 简单License状态检查器
 * 包含基本的离线缓存功能
 */

const fetch = require('node-fetch');
const crypto = require('crypto');

class SimpleLicenseManager {
  constructor(config, db) {
    this.config = config;
    this.db = db;
  }

  /**
   * 通用的fetch包装器，添加超时和错误处理
   */
  async fetchWithTimeout(url, options = {}) {
    const timeout = options.timeout || this.config.apiTimeout;
    
    const fetchPromise = fetch(url, options);
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error('请求超时')), timeout)
    );
    
    try {
      const response = await Promise.race([fetchPromise, timeoutPromise]);
      return response;
    } catch (error) {
      if (error.message === '请求超时') {
        throw new Error(`请求超时 (${timeout}ms)`);
      }
      throw error;
    }
  }

  /**
   * 验证License (包含离线缓存)
   * 🔧 优化：增强缓存策略，优先使用缓存减少验证时间
   */
  async verifyLicense(licenseKey, instanceId) {
    console.log('[StatusChecker] verifyLicense被调用');
    try {
      // 检查缓存状态，但启动时始终尝试在线验证
      const cacheResult = this.checkOfflineCache(licenseKey);
      
      // 尝试在线验证（启动时必须进行）
      const onlineResult = await this.verifyOnline(licenseKey, instanceId);
      
      if (onlineResult.success) {
        // 在线验证成功，更新缓存（保存完整的数据结构）
        this.updateCache(licenseKey, {
          ...onlineResult.data,
          planInfo: onlineResult.planInfo
        });
        return {
          success: true,
          mode: 'online',
          data: onlineResult.data,
          planInfo: onlineResult.planInfo
        };
      } else {
        // 在线验证失败，优先从数据库读取license_info，然后尝试离线缓存
        if (cacheResult.valid) {
          const cachedData = cacheResult.data || {};
          
          // 🔧 修复：优先从数据库获取license_info，避免使用空缓存
          let planInfo = cachedData.planInfo;
          if (!planInfo || planInfo.planName === 'unknown' || planInfo.maxNodes === 0) {
            const dbLicenseInfo = await this.db.setting.get('license_info');
            if (dbLicenseInfo && dbLicenseInfo.planName && dbLicenseInfo.planName !== 'unknown') {
              console.log('[StatusChecker] 使用数据库license_info修正缓存数据');
              planInfo = {
                planName: dbLicenseInfo.planName,
                planType: dbLicenseInfo.planType || dbLicenseInfo.planIdentifier,
                maxNodes: dbLicenseInfo.maxNodes || 0,
                expiresAt: dbLicenseInfo.serverExpirationTime || dbLicenseInfo.expiresAt,
                features: dbLicenseInfo.features || [],
                featuresMask: dbLicenseInfo.featuresMask || dbLicenseInfo.features || 1,
                featureDetails: dbLicenseInfo.featureDetails || null,
                permissions: dbLicenseInfo.permissions || {}
              };
            } else {
              planInfo = await this.buildPlanInfoFromCache(cachedData);
            }
          }
          
          // 🔧 调试日志：记录在线验证失败后的offline回退
          console.log('[DEBUG] 在线验证失败，使用offline缓存:', {
            onlineError: onlineResult.error,
            hasCachedData: !!cachedData,
            cachedDataKeys: Object.keys(cachedData),
            hasPlanInfo: !!planInfo,
            planInfoContent: planInfo
          });
          
          return {
            success: true,
            mode: 'offline',
            data: cachedData,
            cacheExpiry: cacheResult.expiry,
            planInfo: planInfo
          };
        } else {
          return {
            success: false,
            error: onlineResult.error || '验证失败且无有效缓存'
          };
        }
      }
    } catch (error) {
      // 异常情况下静默尝试离线缓存
      const cacheResult = this.checkOfflineCache(licenseKey);
      
      if (cacheResult.valid) {
        const cachedData = cacheResult.data || {};
        
        // 🔧 修复：优先从数据库获取license_info，避免使用空缓存
        let planInfo = cachedData.planInfo;
        if (!planInfo || planInfo.planName === 'unknown' || planInfo.maxNodes === 0) {
          const dbLicenseInfo = await this.db.setting.get('license_info');
          if (dbLicenseInfo && dbLicenseInfo.planName && dbLicenseInfo.planName !== 'unknown') {
            console.log('[StatusChecker] 异常处理：使用数据库license_info修正缓存数据');
            planInfo = {
              planName: dbLicenseInfo.planName,
              planType: dbLicenseInfo.planType || dbLicenseInfo.planIdentifier,
              maxNodes: dbLicenseInfo.maxNodes || 0,
              expiresAt: dbLicenseInfo.serverExpirationTime || dbLicenseInfo.expiresAt,
              features: dbLicenseInfo.features || [],
              featuresMask: dbLicenseInfo.featuresMask || dbLicenseInfo.features || 1,
              featureDetails: dbLicenseInfo.featureDetails || null,
              permissions: dbLicenseInfo.permissions || {}
            };
          } else {
            planInfo = await this.buildPlanInfoFromCache(cachedData);
          }
        }
        
        // 🔧 调试日志：详细记录offline模式的返回数据
        console.log('[DEBUG] statusChecker offline模式返回数据:', {
          hasCachedData: !!cachedData,
          cachedDataKeys: Object.keys(cachedData),
          hasPlanInfo: !!planInfo,
          planInfoContent: planInfo,
          cacheExpiry: cacheResult.expiry
        });
        
        return {
          success: true,
          mode: 'offline',
          data: cachedData,
          cacheExpiry: cacheResult.expiry,
          planInfo: planInfo
        };
      } else {
        return {
          success: false,
          error: `验证异常: ${error.message}`
        };
      }
    }
  }

  /**
   * 🔧 新增：从缓存数据构建planInfo
   */
  async buildPlanInfoFromCache(cachedData) {
    // 🔧 修复：优先从数据库读取license_info，避免默认到unknown
    // 首先尝试从数据库获取正确的license_info
    const dbLicenseInfo = await this.db.setting.get('license_info');
    if (dbLicenseInfo && dbLicenseInfo.planName && dbLicenseInfo.planName !== 'unknown') {
      console.log('[StatusChecker] buildPlanInfoFromCache: 使用数据库license_info');
      return {
        planName: dbLicenseInfo.planName,
        planType: dbLicenseInfo.planType || dbLicenseInfo.planIdentifier,
        maxNodes: dbLicenseInfo.maxNodes || 0,
        expiresAt: dbLicenseInfo.serverExpirationTime || dbLicenseInfo.expiresAt,
        features: dbLicenseInfo.features || [],
        featuresMask: dbLicenseInfo.featuresMask || dbLicenseInfo.features || 1,
        featureDetails: dbLicenseInfo.featureDetails || null,
        permissions: dbLicenseInfo.permissions || {}
      };
    }
    
    // 如果数据库没有数据，才使用缓存数据构建
    const planType = cachedData.plan || cachedData.planType || cachedData.planIdentifier || 'unknown';

    // 数据优先级：缓存中的planDisplayName > 缓存中的planName > 本地映射getPlanDisplayName(planType)
    const cachedDisplayName = cachedData.planDisplayName || cachedData.planName;
    const finalPlanName = cachedDisplayName || this.getPlanDisplayName(planType);

    console.log('[DEBUG] buildPlanInfoFromCache 完整缓存数据:', JSON.stringify(cachedData, null, 2));
    console.log('[DEBUG] buildPlanInfoFromCache 数据分析:', {
      plan: cachedData.plan,
      planName: cachedData.planName,
      planDisplayName: cachedData.planDisplayName,
      planType: cachedData.planType,
      planIdentifier: cachedData.planIdentifier,
      convertedPlanType: planType,
      cachedDisplayName: cachedDisplayName,
      finalPlanName: finalPlanName,
      dataSource: cachedDisplayName ? (cachedData.planDisplayName ? 'cache_planDisplayName' : 'cache_planName') : 'local_mapping',
      maxNodes: cachedData.maxNodes
    });

    return {
      planName: finalPlanName,
      planType: planType,
      maxNodes: cachedData.maxNodes || 0,
      expiresAt: cachedData.expiresAt,
      features: cachedData.features || [],
      featuresMask: cachedData.featuresMask || 1,
      featureDetails: cachedData.featureDetails || null,
      permissions: cachedData.permissions || {}
    };
  }

  /**
   * 在线验证License
   */
  async verifyOnline(licenseKey, instanceId) {
    try {
      const requestData = {
        licenseKey,
        instanceId,
        nodeCount: await this.getNodeCount(),
        features: [],
        version: require('../../package.json').version
      };

      // 发起在线验证请求

      const response = await this.fetchWithTimeout(`${this.config.serverUrl}/api/license/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      // 检查响应是否为JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error(`服务器返回非JSON响应: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // 适配license/status API：使用status字段判断许可证是否有效
      const isValidBinding = data.success && data.data && data.data.status === 'active';
      
      if (response.ok && isValidBinding) {
        // 简化日志输出
        console.log('[StatusChecker] 在线验证成功');
        console.log('[StatusChecker] featureDetails数据:', data.data.featureDetails ? `存在(${data.data.featureDetails.length}个)` : '不存在');
        
        // 适配license/status API的扁平化结构
        const responseData = data.data;
        
        // 强制输出调试日志查看实际数据
        console.log('[StatusChecker] 服务端原始返回数据:');
        console.log('  - plan:', responseData.plan);
        console.log('  - planType:', responseData.planType);
        console.log('  - planDisplayName:', responseData.planDisplayName);
        console.log('  - planName:', responseData.planName);
        console.log('  - maxNodes:', responseData.maxNodes);
        console.log('  - features:', JSON.stringify(responseData.features));
        console.log('  - featuresMask:', responseData.featuresMask);
        
        // 调试日志 - 仅在开发环境或debug模式下输出
        if (process.env.NODE_ENV === 'development' || await this.db.setting.get('debug_license')) {
          console.log('[StatusChecker] 服务端返回数据详情:');
          console.log('  - planType:', responseData.plan || responseData.planType);
          console.log('  - planName:', responseData.planDisplayName || responseData.planName);
          console.log('  - maxNodes:', responseData.maxNodes);
          console.log('  - features:', responseData.features);
          console.log('  - featuresMask:', responseData.featuresMask);
        }
        
        // 记录最后在线检查时间
        this.db.setting.set('lastLicenseOnlineCheck', Date.now());
        const binding = responseData.binding || {};
        
        // 直接从响应数据提取信息
        const planType = responseData.plan || responseData.planType || 'unknown';
        const features = responseData.features || [];
        const featuresMask = responseData.featuresMask || 1;
        // 使用服务器返回的显示名称
        const finalPlanName = responseData.planDisplayName || this.getPlanDisplayName(planType);

        const result = {
          success: true,
          data: {
            // 兼容旧格式，使用license/status API数据
            isValid: true,
            valid: true,
            licenseKey: responseData.licenseKey || licenseKey,
            planType: planType,
            plan: planType,
            planName: finalPlanName,
            planDisplayName: finalPlanName,
            maxNodes: responseData.maxNodes || 0,
            expiresAt: responseData.expiresAt,
            boundAt: binding.boundAt || responseData.boundAt,
            lastSeen: binding.lastSeen || responseData.lastSeen,
            isBound: true, // license/status返回的都是已绑定的
            instanceId: binding.instanceId || responseData.instanceId,
            // 直接使用API返回的功能信息
            features: features,
            featuresMask: featuresMask,
            featureDetails: [] // license/status不返回详细描述，保持空数组
          },
          planInfo: {
            planName: finalPlanName,
            planType: planType,
            maxNodes: responseData.maxNodes || 0,
            expiresAt: responseData.expiresAt,
            features: features,
            featuresMask: featuresMask,
            featureDetails: [],
            permissions: {}
          }
        };
        
        console.log('[StatusChecker] 返回的planInfo.featureDetails:', result.planInfo.featureDetails ? `存在(${result.planInfo.featureDetails.length}个)` : '不存在');
        return result;
      } else {
        console.warn('[StatusChecker] 在线验证失败:', data.message || data.error || '未知错误');
        return {
          success: false,
          error: data.message || data.error || '在线验证失败'
        };
      }
    } catch (error) {
      console.error('[StatusChecker] 在线验证异常:', error);
      return {
        success: false,
        error: `网络错误: ${error.message}`
      };
    }
  }

  /**
   * 获取套餐显示名称
   * 临时恢复此方法以确保激活过程中显示正确的套餐名称
   */
  getPlanDisplayName(planType) {
    // 最小兜底：不再做本地映射，直接返回服务器的类型或占位
    return planType || 'Unknown Plan';
  }

  /**
   * 检查离线缓存
   */
  checkOfflineCache(licenseKey) {
    try {
      // 统一使用 license_info 作为唯一本地真相源
      const licenseInfo = this.db.setting.get('license_info');
      if (!licenseInfo || !licenseInfo.isValid) {
        return { valid: false };
      }
      // 仅当密钥匹配当前缓存的许可证时才视为有效
      const cachedKey = licenseInfo.licenseKey || licenseInfo.key;
      if (!cachedKey || (licenseKey && cachedKey !== licenseKey)) {
        return { valid: false };
      }

      const now = Date.now();
      const timestamp = licenseInfo.lastVerifiedTime || 0;
      const maxAge = this.config.cacheValidityHours * 60 * 60 * 1000;
      const age = now - timestamp;
      const valid = age <= maxAge;

      return {
        valid,
        data: licenseInfo,
        expiry: timestamp + maxAge,
        timestamp,
        age,
        remainingTime: Math.max(0, maxAge - age)
      };
    } catch (error) {
      console.error('[StatusChecker] 离线缓存检查异常:', error);
      return { valid: false };
    }
  }

  /**
   * 更新缓存
   */
  updateCache(licenseKey, data) {
    try {
      // 统一仅更新 license_info 作为唯一缓存
      if (data && data.isValid) {
        // 获取套餐类型和显示名称 - 优先使用API返回的数据
        const planType = data.planType || data.plan || data.type || 'standard';
        // 🔧 修复：优先使用服务器返回的显示名称，移除硬编码依赖
        const serverPlanName = data.planDisplayName || data.planName;
        const finalPlanName = serverPlanName || this.getPlanDisplayName(planType);

        // 调试日志 - 记录原始maxNodes值
        if (process.env.NODE_ENV === 'development' || this.db.setting.get('debug_license')) {
          console.log('[StatusChecker] updateCache写入前 - maxNodes原始值:', data.maxNodes);
        }

        const licenseInfo = {
          key: licenseKey,
          isValid: data.isValid !== undefined ? data.isValid : true,
          lastVerifiedTime: Date.now(),
          machineId: data.machineId || this.db.setting.get('instanceId'),
          planName: finalPlanName,
          planIdentifier: data.planIdentifier || planType,
          planType: planType,
          maxNodes: this.validateMaxNodes(data.maxNodes, planType) || 0,

          // 双格式功能数据支持 - 优先使用API返回的数据
          features: Array.isArray(data.features) ? data.features : [],
          featuresMask: typeof data.featuresMask === 'number' ? data.featuresMask : 0,
          // 仅在具备映射时再生成显示名称，否则置空，避免本地推断
          featureList: Array.isArray(data.featureList) ? data.featureList : [],
          featureDetails: data.featureDetails || null, // 增强格式：完整功能详情
          permissions: data.permissions || {}, // 权限对象

          serverExpirationTime: this.convertExpirationTime(data.serverExpirationTime || data.expiresAt),
          message: data.message || `当前套餐: ${finalPlanName}`
        };
        
        // 确保数据格式完整性
        this.ensureDataFormatCompleteness(licenseInfo);

        // 调试日志 - 记录写入后的maxNodes值
        if (process.env.NODE_ENV === 'development' || this.db.setting.get('debug_license')) {
          console.log('[StatusChecker] updateCache写入后 - license_info.maxNodes:', licenseInfo.maxNodes);
        }

        this.db.setting.set('license_info', licenseInfo);
        try { this.recordLicenseWriteMeta('statusChecker.updateCache', licenseInfo).catch(() => {}); } catch (_) {}
        // 移除详细的日志输出
      }
    } catch (error) {
      console.error('[StatusChecker] 缓存更新失败:', error);
    }
  }

  /**
   * 清除缓存
   */
  clearCache(licenseKey) {
    try {
      // 统一清除本地唯一缓存
      this.db.setting.set('license_info', null);
      console.log('[StatusChecker] 本地许可证缓存已清除 (license_info)');
      // 标记元信息
      this.db.setting.set('license_info_meta', {
        lastWriter: 'statusChecker.clearCache',
        lastTimestamp: Date.now(),
        reason: 'manual-clear',
        keyPrefix: licenseKey ? String(licenseKey).slice(0, 8) + '...' : ''
      });
    } catch (error) {
      console.error('[StatusChecker] 缓存清除失败:', error);
    }
  }

  /**
   * 验证和修正maxNodes值
   */
  validateMaxNodes(maxNodes, planType) {
    // 仅校验明显异常值，信任服务器数据
    if (maxNodes === 999) {
      console.warn(`[StatusChecker] 检测到异常maxNodes值999，标记为无效`);
      return 0;
    }

    // 校验超出合理范围的异常值
    if (maxNodes > 1000) {
      console.warn(`[StatusChecker] maxNodes值异常过大(${maxNodes})，标记为无效`);
      return 0;
    }

    // 信任服务器返回的数据
    return maxNodes;
  }

  /**
   * 转换到期时间格式
   */
  convertExpirationTime(expiresAt) {
    if (!expiresAt) return null;
    
    // 如果是数字
    if (typeof expiresAt === 'number') {
      // 如果小于10亿，认为是秒级时间戳，需要转换为毫秒
      if (expiresAt < 1000000000000) {
        // 处理小数点情况（如1780009.453）
        const seconds = Math.floor(expiresAt);
        return seconds * 1000;
      }
      // 否则认为已经是毫秒
      return expiresAt;
    }
    
    // 如果是字符串，尝试解析
    if (typeof expiresAt === 'string') {
      const timestamp = parseInt(expiresAt);
      if (!isNaN(timestamp)) {
        return this.convertExpirationTime(timestamp);
      }
    }
    
    return null;
  }

  /**
   * 获取节点数量
   */
  async getNodeCount() {
    try {
      const servers = await this.db.servers.all();
      return servers.filter(s => s.status === 1).length;
    } catch (error) {
      console.error('[StatusChecker] 获取节点数量失败:', error);
      return 0;
    }
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus(licenseKey) {
    try {
      const licenseInfo = this.db.setting.get('license_info');
      if (!licenseInfo || !licenseInfo.isValid) {
        return { exists: false };
      }
      const cachedKey = licenseInfo.licenseKey || licenseInfo.key;
      if (licenseKey && cachedKey !== licenseKey) {
        return { exists: false };
      }
      const now = Date.now();
      const timestamp = licenseInfo.lastVerifiedTime || 0;
      const maxAge = this.config.cacheValidityHours * 60 * 60 * 1000;
      const age = now - timestamp;
      const remainingTime = maxAge - age;
      return {
        exists: true,
        valid: remainingTime > 0,
        timestamp,
        age,
        remainingTime: Math.max(0, remainingTime),
        data: licenseInfo
      };
    } catch (error) {
      console.error('[StatusChecker] 获取缓存状态失败:', error);
      return { exists: false };
    }
  }

  /**
   * 强制刷新License状态
   */
  async refreshLicense(licenseKey, instanceId) {
    console.log('[StatusChecker] 强制刷新License状态');
    try {
      // 仅尝试在线验证；成功后再覆盖写入，不成功则保持现有缓存不变
      const online = await this.verifyOnline(licenseKey, instanceId);
      if (online && online.success) {
        this.updateCache(licenseKey, { ...online.data, planInfo: online.planInfo });
        return { success: true, mode: 'online', data: online.data, planInfo: online.planInfo };
      }
      return { success: false, error: online && online.error ? online.error : 'ONLINE_VERIFY_FAILED' };
    } catch (error) {
      return { success: false, error: `REFRESH_EXCEPTION: ${error.message}` };
    }
  }

  /**
   * 记录 license_info 写入元数据与简易写入日志
   */
  async recordLicenseWriteMeta(writer, licenseInfo) {
    try {
      const keyPrefix = (licenseInfo?.licenseKey || licenseInfo?.key || '').toString().slice(0, 8) + (licenseInfo?.licenseKey || licenseInfo?.key ? '...' : '');
      const plan = licenseInfo?.planName || licenseInfo?.planDisplayName || licenseInfo?.planType || 'unknown';
      const maxNodes = licenseInfo?.maxNodes || 0;
      const featuresCount = Array.isArray(licenseInfo?.features) ? licenseInfo.features.length : 0;
      const hash = crypto.createHash('sha1').update(JSON.stringify(licenseInfo || {})).digest('hex').slice(0, 12);

      const meta = {
        lastWriter: writer,
        lastTimestamp: Date.now(),
        keyPrefix,
        plan,
        maxNodes,
        featuresCount,
        hash
      };
      await this.db.setting.set('license_info_meta', meta);

      // 写入简易日志（最多保留10条）
      let log = await this.db.setting.get('license_info_write_log');
      if (!Array.isArray(log)) log = [];
      log.push({ ts: meta.lastTimestamp, writer, keyPrefix, plan, maxNodes, featuresCount, hash });
      if (log.length > 10) log = log.slice(log.length - 10);
      await this.db.setting.set('license_info_write_log', log);
    } catch (e) {
      console.warn('[StatusChecker] 记录license写入元数据失败:', e.message);
    }
  }

  /**
   * 确保许可证数据格式的完整性
   * @param {Object} licenseInfo - 许可证信息
   */
  ensureDataFormatCompleteness(licenseInfo) {
    try {
      // 如果有features数组但没有featuresMask，生成featuresMask
      if (licenseInfo.features && Array.isArray(licenseInfo.features) && !licenseInfo.featuresMask) {
        licenseInfo.featuresMask = this.convertFeaturesToMask(licenseInfo.features);
        console.log('[StatusChecker] 已从features数组生成featuresMask:', licenseInfo.featuresMask);
      }

      // 如果有featuresMask但没有features数组，生成features数组
      if (licenseInfo.featuresMask && (!licenseInfo.features || !Array.isArray(licenseInfo.features))) {
        licenseInfo.features = this.convertMaskToFeatures(licenseInfo.featuresMask);
        console.log('[StatusChecker] 已从featuresMask生成features数组:', licenseInfo.features.join(', '));
      }

      // 如果有features数组但没有featureList，尽量使用featureDetails映射生成；
      // 若无映射则保持为空，避免使用本地静态映射引入不一致
      if (licenseInfo.features && Array.isArray(licenseInfo.features) && (!licenseInfo.featureList || !Array.isArray(licenseInfo.featureList))) {
        if (licenseInfo.featureDetails && Array.isArray(licenseInfo.featureDetails) && licenseInfo.featureDetails.length > 0) {
          const nameToDisplay = new Map();
          licenseInfo.featureDetails.forEach(f => {
            if (f && f.name) nameToDisplay.set(f.name, f.displayName || f.name);
          });
          licenseInfo.featureList = licenseInfo.features.map(n => nameToDisplay.get(n) || n);
          console.log('[StatusChecker] 已基于featureDetails生成featureList');
        } else {
          // 无可靠映射源，保持为空，交由 FeatureChecker 统一生成并回写
          licenseInfo.featureList = [];
          if (process.env.NODE_ENV === 'development' || this.db.setting.get('debug_license')) {
            console.log('[StatusChecker] 跳过本地静态映射生成featureList，等待FeatureChecker回写');
          }
        }
      }

    } catch (error) {
      console.error('[StatusChecker] 确保数据格式完整性时出错:', error);
    }
  }

  /**
   * 位掩码转换为功能数组
   * @param {number} mask - 位掩码
   * @returns {Array<string>} 功能名称数组
   */
  convertMaskToFeatures(mask) {
    const features = [];
    const featureMap = {
      1: 'BASIC_MONITORING',
      2: 'WEBSSH',
      4: 'AUTO_DISCOVERY',
      8: 'ADVANCED_ANALYTICS',
      16: 'API_ACCESS',
      32: 'CUSTOM_ALERTS',
      64: 'AI_ANALYTICS',
      128: 'NETWORK_QUALITY'
    };

    for (const [bit, featureName] of Object.entries(featureMap)) {
      if (mask & parseInt(bit)) {
        features.push(featureName);
      }
    }

    return features.length > 0 ? features : ['BASIC_MONITORING'];
  }

  /**
   * 功能数组转换为位掩码
   * @param {Array<string>} features - 功能名称数组
   * @returns {number} 位掩码
   */
  convertFeaturesToMask(features) {
    const featureMap = {
      'BASIC_MONITORING': 1,
      'WEBSSH': 2,
      'AUTO_DISCOVERY': 4,
      'ADVANCED_ANALYTICS': 8,
      'API_ACCESS': 16,
      'CUSTOM_ALERTS': 32,
      'AI_ANALYTICS': 64,
      'NETWORK_QUALITY': 128
    };

    let mask = 0;
    for (const featureName of features) {
      const bit = featureMap[featureName];
      if (bit) {
        mask |= bit;
      }
    }

    return mask > 0 ? mask : 1;
  }

  /**
   * 功能数组转换为显示名称
   * @param {Array<string>} features - 功能名称数组
   * @returns {Array<string>} 显示名称数组
   */
  convertFeaturesToDisplayNames(features) {
    // 此方法仅用于回退；优先由 featureDetails 映射或 FeatureChecker 统一生成
    const displayMap = {
      'BASIC_MONITORING': '基础监控',
      'WEBSSH': 'WebSSH终端',
      'AUTO_DISCOVERY': '自动发现',
      'ADVANCED_ANALYTICS': '高级分析',
      'API_ACCESS': 'API访问',
      'CUSTOM_ALERTS': '自定义告警',
      'AI_ANALYTICS': 'AI分析',
      'NETWORK_QUALITY': '网络质量监控'
    };
    return features.map(featureName => displayMap[featureName] || featureName);
  }

  /**
   * 从featureDetails数组提取功能信息
   * @param {Array} featureDetails - 功能详情数组
   * @returns {Object} 包含features数组和featuresMask的对象
   */
  extractFeaturesFromDetails(featureDetails, planType = 'unknown') {
    if (!Array.isArray(featureDetails) || featureDetails.length === 0) {
      // 不做任何本地推断；无详细信息时仅保留基础监控
      console.log('[StatusChecker] featureDetails缺失，跳过本地推断，返回最小功能集');
      return { features: ['BASIC_MONITORING'], featuresMask: 1 };
    }

    // 原有逻辑：从 featureDetails 中提取可用功能
    const availableFeatures = [];
    let featuresMask = 0;

    featureDetails.forEach(feature => {
      if (feature.available) {
        availableFeatures.push(feature.name);
        featuresMask |= feature.bit;
      }
    });

    return {
      features: availableFeatures.length > 0 ? availableFeatures : ['BASIC_MONITORING'],
      featuresMask: featuresMask > 0 ? featuresMask : 1
    };
  }
}

module.exports = SimpleLicenseManager;
