'use strict';

/**
 * License Enhanced 路由
 * 提供授权管理的API接口
 */

module.exports = (app, licenseEnhanced) => {
  const { pr } = require('../../core');
  
  // 添加调试日志
  console.log('[LicenseEnhanced] 开始注册路由...');
  console.log('[LicenseEnhanced] app对象是否存在:', !!app);
  console.log('[LicenseEnhanced] licenseEnhanced对象是否存在:', !!licenseEnhanced);

  // 中间件：检查管理员权限
  const requireAdmin = (req, res, next) => {
    if (!req.admin) {
      return res.json(pr(0, '需要管理员权限'));
    }
    next();
  };

  /**
   * 诊断：读取 license_info 与写入元信息
   */
  app.get('/admin/api/license-enhanced/license-info', requireAdmin, async (req, res) => {
    try {
      const db = req.app.locals.db || req.app.locals.database;
      const info = await db.setting.get('license_info');
      const meta = await db.setting.get('license_info_meta');
      const log = await db.setting.get('license_info_write_log');
      const redact = (v) => (typeof v === 'string' && v.length > 12) ? (v.slice(0, 8) + '...') : v;
      const sanitized = info ? { ...info } : null;
      if (sanitized) {
        if (sanitized.licenseKey) sanitized.licenseKey = redact(sanitized.licenseKey);
        if (sanitized.key) sanitized.key = redact(sanitized.key);
      }
      return res.json(pr(1, { license_info: sanitized, meta: meta || null, log: Array.isArray(log) ? log : [] }));
    } catch (e) {
      console.error('[LicenseEnhanced] 读取license-info诊断失败:', e);
      return res.json(pr(0, '读取失败', { error: e.message }));
    }
  });

  /**
   * 诊断：触发一次在线刷新并返回写入前后元数据
   */
  app.post('/admin/api/license-enhanced/test-refresh', requireAdmin, async (req, res) => {
    try {
      const db = req.app.locals.db || req.app.locals.database;
      const before = await db.setting.get('license_info_meta');
      const current = await licenseEnhanced.getCurrentLicenseStatus();
      if (!current.licenseKey) {
        return res.json(pr(0, '无当前License，无法刷新'));
      }
      const instanceId = licenseEnhanced.getInstanceId();
      const result = await licenseEnhanced.statusChecker.refreshLicense(current.licenseKey, instanceId);
      const after = await db.setting.get('license_info_meta');
      return res.json(pr(result.success ? 1 : 0, {
        refresh: result,
        metaBefore: before || null,
        metaAfter: after || null
      }));
    } catch (e) {
      console.error('[LicenseEnhanced] 测试刷新失败:', e);
      return res.json(pr(0, '刷新失败', { error: e.message }));
    }
  });

  /**
   * 获取License状态
   */
  app.get('/admin/api/license-enhanced/status', requireAdmin, async (req, res) => {
    try {
      const db = req.app.locals.db || req.app.locals.database;
      const instanceId = licenseEnhanced.getInstanceId();
      
      // 获取当前节点数（用于UI展示）
      const { getNodeStats } = require('../servers/nodeStatsApi');
      let nodeStats = { active: 0 };
      try {
        nodeStats = await getNodeStats(db);
      } catch (nodeError) {
        console.warn('[LicenseEnhanced] 获取节点统计失败:', nodeError);
      }

      // 在线优先 → 缓存 → 兜底(10个节点)
      let licenseResp;
      let licenseKey = await db.setting.get('currentLicenseKey');
      if (!licenseKey) {
        const cachedInfo = await db.setting.get('license_info');
        if (cachedInfo && (cachedInfo.licenseKey || cachedInfo.key)) {
          licenseKey = cachedInfo.licenseKey || cachedInfo.key;
        }
      }

      // 1) 在线优先
      if (licenseKey) {
        try {
          const online = await licenseEnhanced.statusChecker.verifyOnline(licenseKey, instanceId);
          if (online && online.success) {
            // 成功则更新唯一缓存
            licenseEnhanced.statusChecker.updateCache(licenseKey, { ...online.data, planInfo: online.planInfo });
            licenseResp = await licenseEnhanced.getCurrentLicenseStatus();
          }
        } catch (e) {
          console.warn('[LicenseEnhanced] 在线状态获取失败，准备使用缓存:', e.message);
        }
      }

      // 2) 缓存
      if (!licenseResp || !licenseResp.licenseKey || licenseResp.planType === 'unauthorized') {
        const cachedInfo = await db.setting.get('license_info');
        if (cachedInfo && cachedInfo.isValid) {
          licenseResp = await licenseEnhanced.getCurrentLicenseStatus();
        }
      }

      // 3) 兜底（仅用于UI显示，不写入缓存）
      if (!licenseResp || !licenseResp.licenseKey || licenseResp.planType === 'unauthorized') {
        licenseResp = {
          licenseKey: licenseKey || '',
          status: 'active',
          lastCheck: Date.now(),
          instanceId,
          enhancedMode: licenseEnhanced.config.enabled,
          planName: '应急模式',
          planDisplayName: '应急模式',
          planType: 'emergency',
          maxNodes: 10, // 兜底：10个节点上限（仅UI显示）
          features: [],
          featuresMask: 0,
          featureDetails: [],
          permissions: {},
          isCached: false,
          message: '应急模式：网络异常或无有效缓存，节点上限为10'
        };
      }

      // 填充 featureDetails（如果为空）
      if ((!licenseResp.featureDetails || licenseResp.featureDetails.length === 0) && 
          licenseResp.features && licenseResp.features.length > 0) {
        
        // 功能定义映射（使用 Tabler Icons）
        const featureDefinitions = {
          'BASIC_MONITORING': {
            bit: 1,
            name: 'BASIC_MONITORING',
            displayName: '基础监控',
            description: '服务器状态实时监控',
            icon: 'device-desktop',
            category: 'monitoring'
          },
          'WEBSSH': {
            bit: 2,
            name: 'WEBSSH',
            displayName: 'WebSSH终端',
            description: '浏览器内SSH终端访问',
            icon: 'terminal',
            category: 'remote'
          },
          'AUTO_DISCOVERY': {
            bit: 4,
            name: 'AUTO_DISCOVERY',
            displayName: '自动发现',
            description: '自动发现网络中的服务器',
            icon: 'search',
            category: 'network'
          },
          'ADVANCED_ANALYTICS': {
            bit: 8,
            name: 'ADVANCED_ANALYTICS',
            displayName: '高级分析',
            description: '深度数据分析和报表',
            icon: 'chart-line',
            category: 'analytics'
          },
          'API_ACCESS': {
            bit: 16,
            name: 'API_ACCESS',
            displayName: 'API访问',
            description: '完整的REST API访问权限',
            icon: 'code',
            category: 'developer'
          },
          'CUSTOM_ALERTS': {
            bit: 32,
            name: 'CUSTOM_ALERTS',
            displayName: '自定义告警',
            description: '配置个性化告警规则',
            icon: 'bell',
            category: 'notification'
          },
          'AI_ANALYTICS': {
            bit: 64,
            name: 'AI_ANALYTICS',
            displayName: 'AI分析',
            description: 'AI驱动的智能分析和预测',
            icon: 'brain',
            category: 'ai'
          },
          'NETWORK_QUALITY': {
            bit: 128,
            name: 'NETWORK_QUALITY',
            displayName: '网络质量监控',
            description: '全面的网络质量检测',
            icon: 'wifi',
            category: 'network'
          }
        };

        // 构建 featureDetails
        licenseResp.featureDetails = licenseResp.features.map(featureName => {
          const definition = featureDefinitions[featureName];
          if (definition) {
            return {
              ...definition,
              available: true  // 既然在 features 数组中，就是可用的
            };
          }
          // 未知功能的默认值
          return {
            bit: 0,
            name: featureName,
            displayName: featureName,
            description: featureName,
            icon: 'puzzle',
            category: 'other',
            available: true
          };
        });
        
        console.log('[LicenseEnhanced] 已填充 featureDetails，共', licenseResp.featureDetails.length, '个功能');
      }

      // 重新获取 moduleStatus（在可能的在线验证之后）
      const moduleStatus = await licenseEnhanced.getModuleStatus();

      // 补充当前节点数
      licenseResp.currentNodes = nodeStats.active || 0;

      const heartbeatStatus = licenseEnhanced.getHeartbeatStatus();

      res.json(pr(1, {
        message: '获取状态成功',
        license: licenseResp,
        module: moduleStatus,
        heartbeat: heartbeatStatus
      }));
    } catch (error) {
      console.error('[LicenseEnhanced] 获取状态失败:', error);
      res.json(pr(0, '获取状态失败', { error: error.message }));
    }
  });

  /**
   * 检查License状态
   */
  app.post('/admin/api/license-enhanced/check', requireAdmin, async (req, res) => {
    try {
      const { licenseKey } = req.body;
      
      if (!licenseKey) {
        return res.json(pr(0, 'License Key不能为空'));
      }

      const result = await licenseEnhanced.checkLicenseStatus(licenseKey);
      
      if (result.success) {
        res.json(pr(1, 'License状态检查成功', result));
      } else {
        res.json(pr(0, result.error || 'License状态检查失败', result));
      }
    } catch (error) {
      console.error('[LicenseEnhanced] License状态检查异常:', error);
      res.json(pr(0, '检查异常', { error: error.message }));
    }
  });

  /**
   * 激活License
   */
  app.post('/admin/api/license-enhanced/activate', requireAdmin, async (req, res) => {
    try {
      const { licenseKey, forceTransfer = true } = req.body; // 默认启用自动转移
      
      // 验证License Key格式
      const validation = licenseEnhanced.uiManager.validateLicenseKey(licenseKey);
      if (!validation.valid) {
        return res.json(pr(0, validation.message));
      }

      const result = await licenseEnhanced.activateLicense(validation.licenseKey, forceTransfer);
      
      if (result.success) {
        // 记录当前密钥与状态，不进行本地套餐推断
        await licenseEnhanced.setCurrentLicense(validation.licenseKey, 'active');

        // 激活成功后立即进行在线验证以获取完整功能信息
        try {
          console.log('[LicenseEnhanced] 激活成功，正在获取完整功能信息...');
          const instanceId = await licenseEnhanced._initializeInstanceId();
          const online = await licenseEnhanced.statusChecker.verifyOnline(
            validation.licenseKey,
            instanceId
          );
          if (online && online.success) {
            licenseEnhanced.statusChecker.updateCache(validation.licenseKey, { ...online.data, planInfo: online.planInfo });
          }
        } catch (error) {
          console.warn('[LicenseEnhanced] 获取功能信息失败:', error.message);
        }

        // 触发功能墙刷新
        if (licenseEnhanced.featureChecker) {
          try {
            await licenseEnhanced.featureChecker.fetchFeatureDefinitions();
            console.log('[LicenseEnhanced] 激活成功后已刷新功能权限');
          } catch (error) {
            console.warn('[LicenseEnhanced] 刷新功能权限失败:', error.message);
          }
        }

        res.json(pr(1, result.message || 'License激活成功', {
          ...result,
          shouldRefreshPermissions: true // 标记需要刷新前端权限
        }));
      } else {
        res.json(pr(0, result.error || 'License激活失败', result));
      }
    } catch (error) {
      console.error('[LicenseEnhanced] License激活异常:', error);
      res.json(pr(0, '激活异常', { error: error.message }));
    }
  });

  /**
   * 解绑License
   */
  app.post('/admin/api/license-enhanced/unbind', requireAdmin, async (req, res) => {
    try {
      const { licenseKey } = req.body;
      
      if (!licenseKey) {
        return res.json(pr(0, 'License Key不能为空'));
      }

      // 获取当前实例ID
      const currentInstanceId = licenseEnhanced.getInstanceId();
      
      console.log('[LicenseEnhanced] 开始解绑License:', { licenseKey: licenseKey.substring(0, 20) + '...', currentInstanceId });

      // 先尝试使用当前instanceId解绑
      let result = await licenseEnhanced.unbindLicense(licenseKey, currentInstanceId);
      
      // 如果因为绑定未找到而失败，尝试智能解绑
      if (!result.success && result.error && result.error.includes('No active binding found')) {
        console.log('[LicenseEnhanced] 当前instanceId解绑失败，尝试智能解绑...');
        result = await licenseEnhanced.smartUnbindLicense(licenseKey);
      }
      
      if (result.success) {
        // 清除当前License
        licenseEnhanced.clearCurrentLicense();

        // 触发功能墙刷新（清除权限）
        if (licenseEnhanced.featureChecker) {
          try {
            await licenseEnhanced.featureChecker.fetchFeatureDefinitions();
            console.log('[LicenseEnhanced] 解绑成功后已刷新功能权限');
          } catch (error) {
            console.warn('[LicenseEnhanced] 刷新功能权限失败:', error.message);
          }
        }

        console.log('[LicenseEnhanced] License解绑成功');
        res.json(pr(1, result.message || 'License解绑成功'));
      } else {
        console.log('[LicenseEnhanced] License解绑失败:', result.error);
        res.json(pr(0, result.error || 'License解绑失败'));
      }
    } catch (error) {
      console.error('[LicenseEnhanced] License解绑异常:', error);
      res.json(pr(0, '解绑异常: ' + error.message));
    }
  });

  /**
   * 验证License
   */
  app.post('/admin/api/license-enhanced/verify', requireAdmin, async (req, res) => {
    try {
      const { licenseKey } = req.body;
      
      if (!licenseKey) {
        return res.json(pr(0, 'License Key不能为空'));
      }

      const result = await licenseEnhanced.verifyLicense(licenseKey);
      
      if (result.success) {
        res.json(pr(1, 'License验证成功', result));
      } else {
        res.json(pr(0, result.error || 'License验证失败', result));
      }
    } catch (error) {
      console.error('[LicenseEnhanced] License验证异常:', error);
      res.json(pr(0, '验证异常', { error: error.message }));
    }
  });

  /**
   * 刷新License状态
   */
  app.post('/admin/api/license-enhanced/refresh', requireAdmin, async (req, res) => {
    try {
      const currentLicense = await licenseEnhanced.getCurrentLicenseStatus();
      if (!currentLicense.licenseKey) {
        return res.json(pr(0, '无当前License'));
      }

      const instanceId = licenseEnhanced.getInstanceId();

      // 使用 StatusChecker 的强制刷新：先清理缓存，再在线验证并写入权威 license_info
      const refreshResult = await licenseEnhanced.statusChecker.refreshLicense(
        currentLicense.licenseKey,
        instanceId
      );

      if (refreshResult && refreshResult.success) {
        await licenseEnhanced.setCurrentLicense(currentLicense.licenseKey, 'active');

        // 刷新功能定义（非阻塞）
        if (licenseEnhanced.featureChecker && licenseEnhanced.featureChecker.fetchFeatureDefinitions) {
          try { await licenseEnhanced.featureChecker.fetchFeatureDefinitions(); } catch (_) {}
        }

        return res.json(pr(1, '状态刷新成功', refreshResult));
      }

      return res.json(pr(0, (refreshResult && refreshResult.error) || '状态刷新失败', refreshResult || {}));
    } catch (error) {
      console.error('[LicenseEnhanced] 状态刷新异常:', error);
      return res.json(pr(0, '刷新异常', { error: error.message }));
    }
  });

  /**
   * 获取绑定历史
   */
  app.get('/admin/api/license-enhanced/history/:licenseKey', requireAdmin, async (req, res) => {
    try {
      const { licenseKey } = req.params;
      
      if (!licenseKey) {
        return res.json(pr(0, 'License Key不能为空'));
      }

      const result = await licenseEnhanced.bindingManager.getBindingHistory(licenseKey);
      
      if (result.success) {
        const formattedHistory = licenseEnhanced.uiManager.formatBindingHistory(result.data);
        res.json(pr(1, '获取历史成功', formattedHistory));
      } else {
        res.json(pr(0, result.error || '获取历史失败'));
      }
    } catch (error) {
      console.error('[LicenseEnhanced] 获取历史异常:', error);
      res.json(pr(0, '获取历史异常', { error: error.message }));
    }
  });

  /**
   * 设置增强模式
   */
  app.post('/admin/api/license-enhanced/config', requireAdmin, async (req, res) => {
    try {
      const { enhancedMode, testMode } = req.body;
      
      if (typeof enhancedMode === 'boolean') {
        licenseEnhanced.setEnhancedMode(enhancedMode);
      }
      
      if (typeof testMode === 'boolean') {
        licenseEnhanced.config.testMode = testMode;
        await licenseEnhanced.db.setting.set('licenseTestMode', testMode);
      }
      
      res.json(pr(1, '配置更新成功', await licenseEnhanced.getModuleStatus()));
    } catch (error) {
      console.error('[LicenseEnhanced] 配置更新异常:', error);
      res.json(pr(0, '配置更新异常', { error: error.message }));
    }
  });

  /**
   * 获取缓存状态
   */
  app.get('/admin/api/license-enhanced/cache/:licenseKey', requireAdmin, async (req, res) => {
    try {
      const { licenseKey } = req.params;

      if (!licenseKey) {
        return res.json(pr(0, 'License Key不能为空'));
      }

      const cacheStatus = licenseEnhanced.statusChecker.getCacheStatus(licenseKey);
      res.json(pr(1, '获取缓存状态成功', cacheStatus));
    } catch (error) {
      console.error('[LicenseEnhanced] 获取缓存状态异常:', error);
      res.json(pr(0, '获取缓存状态异常', { error: error.message }));
    }
  });

  /**
   * 获取本地缓存的许可证状态（支持离线模式）
   */
  app.get('/admin/api/license-enhanced/cache-status', requireAdmin, async (req, res) => {
    try {
      console.log('[LicenseEnhanced] 获取本地缓存状态...');
      
      // 获取当前许可证信息
      const currentLicense = await licenseEnhanced.getCurrentLicenseStatus();
      
      if (!currentLicense || !currentLicense.licenseKey) {
        // 尝试从本地缓存恢复许可证信息
        const cachedLicense = await licenseEnhanced.db.setting.get('license_info');
        if (cachedLicense) {
          console.log('[LicenseEnhanced] 从本地缓存恢复许可证信息');
          return res.json(pr(1, {
            message: '从缓存获取状态成功',
            license: cachedLicense,
            module: await licenseEnhanced.getModuleStatus(),
            heartbeat: licenseEnhanced.getHeartbeatStatus(),
            connectionMode: 'cached',
            cacheTimestamp: cachedLicense.lastVerifiedTime || Date.now()
          }));
        } else {
          return res.json(pr(0, '无缓存的许可证信息'));
        }
      }

      // 检查缓存有效性
      const cacheStatus = licenseEnhanced.statusChecker.getCacheStatus(currentLicense.licenseKey);
      
      // 获取节点统计
      let currentNodes = 0;
      try {
        const { getNodeStats } = require('../servers/nodeStatsApi');
        const nodeStats = await getNodeStats();
        currentNodes = nodeStats.active || 0;
      } catch (nodeError) {
        console.warn('[LicenseEnhanced] 获取节点统计失败:', nodeError);
      }

      // 添加缓存标识和过期信息
      const responseData = {
        message: '获取缓存状态成功',
        license: currentLicense,
        module: await licenseEnhanced.getModuleStatus(),
        heartbeat: licenseEnhanced.getHeartbeatStatus(),
        connectionMode: cacheStatus.valid ? 'cached' : 'expired',
        cacheInfo: {
          valid: cacheStatus.valid,
          remainingTime: cacheStatus.remainingTime,
          expiry: cacheStatus.expiry,
          timestamp: cacheStatus.timestamp
        }
      };

      // 添加节点信息
      if (responseData.license) {
        responseData.license.currentNodes = currentNodes;
      }

      res.json(pr(1, responseData));
    } catch (error) {
      console.error('[LicenseEnhanced] 获取缓存状态异常:', error);
      res.json(pr(0, '获取缓存状态异常', { error: error.message }));
    }
  });

  /**
   * 获取应急缓存数据（最基础的许可证信息）
   */
  app.get('/admin/api/license-enhanced/emergency-cache', requireAdmin, async (req, res) => {
    try {
      console.log('[LicenseEnhanced] 获取应急缓存数据...');
      
      // 检查是否有基础许可证信息
      const currentLicenseKey = await licenseEnhanced.db.setting.get('currentLicenseKey');
      const licenseStatus = await licenseEnhanced.db.setting.get('licenseStatus');
      const instanceId = licenseEnhanced.getInstanceId();
      
      if (!currentLicenseKey || licenseStatus !== 'active') {
        return res.json(pr(0, '无应急缓存数据'));
      }

      // 构造最小可用的许可证信息
      const emergencyData = {
        message: '应急模式 - 功能受限',
        license: {
          licenseKey: currentLicenseKey,
          status: 'active',
          planName: '应急模式',
          planType: 'emergency',
          maxNodes: 5, // 应急模式限制
          instanceId: instanceId,
          featureDetails: [
            {
              name: 'BASIC_MONITORING',
              displayName: '基础监控',
              available: true,
              description: '应急模式可用'
            }
          ]
        },
        module: await licenseEnhanced.getModuleStatus(),
        connectionMode: 'emergency',
        warning: '当前处于应急模式，部分功能受限。请检查网络连接。'
      };

      res.json(pr(1, emergencyData));
    } catch (error) {
      console.error('[LicenseEnhanced] 获取应急缓存异常:', error);
      res.json(pr(0, '获取应急缓存异常', { error: error.message }));
    }
  });

  /**
   * 获取套餐配置（从License Server获取权威数据）
   */
  app.get('/admin/api/license-enhanced/plans', requireAdmin, async (req, res) => {
    try {
      const licensePlansCache = require('../../config/license-plans-cache');
      const plans = await licensePlansCache.getPlans();

      if (plans) {
        res.json(pr(1, '获取套餐配置成功', plans));
      } else {
        res.json(pr(0, '获取套餐配置失败'));
      }
    } catch (error) {
      console.error('[LicenseEnhanced] 获取套餐配置异常:', error);
      res.json(pr(0, '获取套餐配置异常', { error: error.message }));
    }
  });


  /**
   * 刷新配置缓存API
   */
  app.post('/admin/api/license-enhanced/refresh-config', requireAdmin, async (req, res) => {
    try {
      console.log('[License-Enhanced] 开始刷新配置缓存...');
      
      // 1. 刷新统一配置服务
      const unifiedConfigService = require('./unifiedConfigService');
      const configRefreshed = await unifiedConfigService.refreshConfig();
      
      // 2. 清除license-plans-cache缓存
      const licensePlansCache = require('../../config/license-plans-cache');
      licensePlansCache.clearCache();
      
      // 3. 清除订阅管理器缓存
      if (licenseEnhanced.subscriptionManager) {
        licenseEnhanced.subscriptionManager.clearCache();
      }
      
      // 4. 清除本地许可证缓存
      if (req.db && req.db.setting) {
        // 清除缓存的许可证信息，强制下次请求时重新获取
        const cacheKeys = [
          'license_cache_data',
          'license_cache_time',
          'subscription_info'
        ];
        
        cacheKeys.forEach(key => {
          try {
            req.db.setting.delete(key);
          } catch (e) {
            // 忽略删除错误
          }
        });
      }
      
      console.log('[License-Enhanced] 配置缓存刷新完成');
      
      res.json({
        status: 1,
        data: {
          message: '配置已刷新',
          configRefreshed: configRefreshed,
          cacheCleared: true,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('[License-Enhanced] 刷新配置失败:', error);
      res.json({
        status: 0,
        data: '刷新配置失败: ' + error.message
      });
    }
  });

  /**
   * 调试API：获取详细的用户会话和许可证信息
   */
  app.get('/admin/api/license-enhanced/debug/user-info', requireAdmin, async (req, res) => {
    try {
      const isLoggedIn = licenseEnhanced.isUserLoggedIn();
      const user = licenseEnhanced.getCurrentUser();
      const instanceId = licenseEnhanced.getInstanceId();
      
      let licenseListResult = null;
      if (isLoggedIn) {
        licenseListResult = await licenseEnhanced.getUserLicenseList();
      }
      
      const currentLicense = await licenseEnhanced.getCurrentLicenseStatus();
      
      const debugInfo = {
        serverStatus: {
          isLoggedIn,
          user,
          instanceId
        },
        licenseServer: {
          serverUrl: licenseEnhanced.config.serverUrl,
          connected: isLoggedIn
        },
        currentLicense: currentLicense,
        userLicenses: licenseListResult?.success ? {
          count: licenseListResult.licenses?.length || 0,
          licenses: licenseListResult.licenses || []
        } : {
          error: licenseListResult?.message || '未获取',
          count: 0,
          licenses: []
        }
      };
      
      console.log('[LicenseEnhanced] 调试信息:', JSON.stringify(debugInfo, null, 2));
      
      res.json(pr(1, '调试信息获取成功', debugInfo));
    } catch (error) {
      console.error('[LicenseEnhanced] 获取调试信息异常:', error);
      res.json(pr(0, '获取调试信息异常', { error: error.message }));
    }
  });

  /**
   * 快速激活License
   * 注意：这是二级登录系统，不需要requireAdmin中间件
   */
  app.post('/admin/api/license-enhanced/user/quick-activate', async (req, res) => {
    try {
      const { licenseKey } = req.body;

      if (!licenseKey) {
        return res.json(pr(0, 'License Key不能为空'));
      }

      const result = await licenseEnhanced.quickActivateLicense(licenseKey);

      if (result.success) {
        res.json(pr(1, {
          message: result.message,
          data: {
            ...result.data,
            shouldRefreshPermissions: true // 标记需要刷新前端权限
          }
        }));
      } else {
        res.json(pr(0, result.message));
      }
    } catch (error) {
      console.error('[LicenseEnhanced] 快速激活异常:', error);
      res.json(pr(0, '激活失败，请稍后重试'));
    }
  });

  /**
   * 执行健康检查
   */
  app.get('/admin/api/license-enhanced/health', requireAdmin, async (req, res) => {
    try {
      const healthResult = await licenseEnhanced.performHealthCheck();
      const report = licenseEnhanced.healthCheck.generateReport(healthResult);
      
      res.json(pr(1, {
        health: healthResult,
        report: report
      }));
    } catch (error) {
      console.error('[LicenseEnhanced] 健康检查异常:', error);
      res.json(pr(0, '健康检查异常', { error: error.message }));
    }
  });

  /**
   * 执行安全检查
   */
  app.get('/admin/api/license-enhanced/security', requireAdmin, async (req, res) => {
    try {
      const { operation, licenseKey } = req.query;
      
      const securityResult = licenseEnhanced.securityManager.performSecurityCheck(
        licenseKey || 'unknown',
        operation || 'general_check'
      );
      
      const securityReport = licenseEnhanced.securityManager.getSecurityReport();
      
      res.json(pr(1, {
        security: securityResult,
        report: securityReport
      }));
    } catch (error) {
      console.error('[LicenseEnhanced] 安全检查异常:', error);
      res.json(pr(0, '安全检查异常', { error: error.message }));
    }
  });

  /**
   * 验证优惠码使用资格（祖父条款专用）
   */
  app.post('/admin/api/license-enhanced/validate-coupon', requireAdmin, async (req, res) => {
    try {
      const { couponCode, licenseKey } = req.body;
      
      if (!couponCode || !licenseKey) {
        return res.json(pr(0, '优惠码和License Key不能为空'));
      }
      
      // 执行安全检查
      const securityCheck = licenseEnhanced.securityManager.performSecurityCheck(
        licenseKey,
        'coupon_validation'
      );
      
      if (!securityCheck.passed) {
        return res.json(pr(0, '安全检查未通过: ' + securityCheck.errors.join(', ')));
      }
      
      // 验证优惠码使用资格
      const couponValidation = licenseEnhanced.securityManager.validateCouponUsage(couponCode, licenseKey);
      
      if (!couponValidation.valid) {
        return res.json(pr(0, `优惠码验证失败: ${couponValidation.reason}`, {
          reason: couponValidation.reason,
          securityWarnings: securityCheck.warnings
        }));
      }
      
      res.json(pr(1, '优惠码验证通过', {
        couponCode,
        licenseKey,
        deviceFingerprint: securityCheck.deviceFingerprint,
        securityWarnings: securityCheck.warnings
      }));
    } catch (error) {
      console.error('[LicenseEnhanced] 优惠码验证异常:', error);
      res.json(pr(0, '优惠码验证异常', { error: error.message }));
    }
  });

  /**
   * 记录优惠码使用（仅管理员）
   */
  app.post('/admin/api/license-enhanced/record-coupon-usage', requireAdmin, async (req, res) => {
    try {
      const { couponCode, licenseKey } = req.body;
      
      if (!couponCode || !licenseKey) {
        return res.json(pr(0, '优惠码和License Key不能为空'));
      }
      
      const deviceFingerprint = await licenseEnhanced.securityManager.db.setting.get('deviceFingerprint');
      const success = licenseEnhanced.securityManager.recordCouponUsage(
        couponCode,
        licenseKey,
        deviceFingerprint
      );
      
      if (success) {
        res.json(pr(1, '优惠码使用已记录'));
      } else {
        res.json(pr(0, '记录优惠码使用失败'));
      }
    } catch (error) {
      console.error('[LicenseEnhanced] 记录优惠码使用异常:', error);
      res.json(pr(0, '记录优惠码使用异常', { error: error.message }));
    }
  });


  /**
   * 手动触发心跳
   */
  app.post('/admin/api/license-enhanced/heartbeat/trigger', requireAdmin, async (req, res) => {
    try {
      const { reason } = req.body;
      
      const heartbeatStatus = licenseEnhanced.getHeartbeatStatus();
      if (!heartbeatStatus.isRunning) {
        return res.json(pr(0, '心跳服务未运行'));
      }

      await licenseEnhanced.triggerHeartbeat(reason || 'manual_trigger');
      
      res.json(pr(1, '心跳已触发', {
        status: licenseEnhanced.getHeartbeatStatus()
      }));
    } catch (error) {
      console.error('[LicenseEnhanced] 触发心跳异常:', error);
      res.json(pr(0, '触发心跳异常', { error: error.message }));
    }
  });

  /**
   * 启动/停止心跳服务
   */
  app.post('/admin/api/license-enhanced/heartbeat/control', requireAdmin, async (req, res) => {
    try {
      const { action } = req.body;
      
      if (action === 'start') {
        licenseEnhanced.startHeartbeat();
        res.json(pr(1, '心跳服务已启动', {
          status: licenseEnhanced.getHeartbeatStatus()
        }));
      } else if (action === 'stop') {
        licenseEnhanced.stopHeartbeat();
        res.json(pr(1, '心跳服务已停止', {
          status: licenseEnhanced.getHeartbeatStatus()
        }));
      } else {
        res.json(pr(0, '无效的操作'));
      }
    } catch (error) {
      console.error('[LicenseEnhanced] 控制心跳服务异常:', error);
      res.json(pr(0, '控制心跳服务异常', { error: error.message }));
    }
  });


  // Redirect handlers for incorrect endpoints
  app.get('/api/license-enhanced/status', (req, res) => {
    res.redirect(301, '/admin/api/license-enhanced/status');
  });

  app.post('/api/license-enhanced/activate', (req, res) => {
    res.redirect(307, '/admin/api/license-enhanced/activate');
  });

  /**
   * 获取许可证基本状态（公共API，不需要管理员权限）
   */
  app.get('/api/license/status', async (req, res) => {
    try {
      const status = await licenseEnhanced.getCurrentLicenseStatus();
      
      // 返回基本状态信息，隐藏敏感信息
      const publicStatus = {
        status: status.status || 'inactive',
        planName: status.planName || '未知',
        planType: status.planType || 'unknown',
        maxNodes: status.maxNodes || 0,
        enhancedMode: status.enhancedMode || false,
        instanceId: status.instanceId || 'unknown'
      };
      
      // 只显示许可证密钥的前16位
      if (status.licenseKey) {
        publicStatus.licenseKey = status.licenseKey;
      }
      
      res.json(pr(1, publicStatus));
    } catch (error) {
      console.error('[LicenseEnhanced] 获取许可证状态失败:', error);
      res.json(pr(0, { 
        message: '获取许可证状态失败', 
        error: error.message 
      }));
    }
  });

  /**
   * 获取功能权限信息（功能墙）
   * 使用统一的FeatureChecker数据源
   */
  app.get('/api/license/feature-wall', async (req, res) => {
    try {
      const featureChecker = licenseEnhanced.featureChecker;
      
      // 检查featureChecker是否存在
      if (!featureChecker) {
        return res.status(503).json({
          error: '功能检查器未初始化',
          message: 'Feature checker not initialized'
        });
      }
      
      // 直接使用统一的功能墙信息（从License Server数据库获取）
      const featureWallInfo = await featureChecker.getFeatureWallInfo();
      
      // 调试级别日志
      if (await licenseEnhanced.db.setting.get('debug')) {
        console.log('[LicenseEnhanced] 功能墙信息获取成功，isDynamic:', featureWallInfo.isDynamic);
      }
      
      res.json(featureWallInfo);
    } catch (error) {
      console.error('[LicenseEnhanced] 获取功能权限信息失败:', error);
      res.status(500).json({
        error: '获取功能权限信息失败',
        message: error.message
      });
    }
  });

  /**
   * 功能权限检查API - 前端功能墙专用
   * 这个端点被网络质量监控页面的JavaScript调用
   */
  app.post('/api/license-enhanced/feature-check', async (req, res) => {
    try {
      const { feature } = req.body;
      
      if (!feature) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_FEATURE',
          message: '功能名称不能为空'
        });
      }

      const featureChecker = licenseEnhanced.featureChecker;
      
      if (!featureChecker) {
        console.warn('[LicenseEnhanced] 功能检查器未初始化');
        return res.status(503).json({
          success: false,
          error: 'FEATURE_CHECKER_UNAVAILABLE',
          message: '功能检查器未初始化'
        });
      }
      
      // 检查功能权限
      const checkResult = featureChecker.checkFeature(feature);
      
      // 获取当前许可证信息
      const currentLicense = await licenseEnhanced.getCurrentLicenseStatus();
      
      // 获取功能限制信息（如时间范围限制）
      let limits = null;
      if (checkResult.allowed && feature === 'NETWORK_QUALITY') {
        try {
          const unifiedConfigService = licenseEnhanced.unifiedConfigService;
          if (unifiedConfigService && currentLicense.planName) {
            // 首先尝试使用套餐名称获取限制
            let planNameToUse = currentLicense.planName;
            
            // 套餐名称映射表（支持中英文）
            const planNameMapping = {
              // 中文名称映射
              '免费版': 'free',
              '基础版': 'free',  // API 返回的是"基础版"
              '专业版': 'pro',
              '企业版': 'enterprise',
              '标准版': 'standard',
              // 英文名称映射
              'Free Plan': 'free',
              'Basic Plan': 'free',
              'Professional Plan': 'pro',
              'Enterprise Plan': 'enterprise',
              'Standard Plan': 'standard'
            };
            
            if (planNameMapping[currentLicense.planName]) {
              planNameToUse = planNameMapping[currentLicense.planName];
            }
            
            const timeLimit = await unifiedConfigService.getFeatureTimeLimit(planNameToUse, feature);
            
            if (timeLimit) {
              limits = {
                networkQuality: {
                  maxTimeRange: timeLimit
                }
              };
            }
          }
        } catch (error) {
          console.error('[LicenseEnhanced] 获取功能限制失败:', error);
        }
      }
      
      const response = {
        success: true,
        data: {
          allowed: checkResult.allowed,
          reason: checkResult.reason || null,
          message: checkResult.message || null,
          planInfo: currentLicense ? {
            name: currentLicense.planName || '未知套餐',
            type: currentLicense.planType || 'unknown',
            maxNodes: currentLicense.maxNodes || 0
          } : null,
          limits: limits
        }
      };
      
      res.json(response);
    } catch (error) {
      console.error('[LicenseEnhanced] 功能权限检查失败:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '功能权限检查失败: ' + error.message
      });
    }
  });

  /**
   * WebSSH 功能权限中间件示例
   */
  app.use('/webssh', (req, res, next) => {
    const featureChecker = licenseEnhanced.featureChecker;
    const check = featureChecker.checkFeature('WEBSSH');
    
    if (check.allowed) {
      next();
    } else {
      // 如果是页面请求，重定向到许可证管理页面
      if (req.accepts('html')) {
        return res.redirect(`/admin/license-management?upgrade=webssh&message=${encodeURIComponent(check.message)}`);
      }
      // API请求返回JSON
      res.status(403).json(check);
    }
  });

  console.log('[LicenseEnhanced] 路由注册完成，已注册路由：');
  console.log('  - GET /api/license/status');
  console.log('  - GET /api/license/feature-wall');
  console.log('  - POST /api/license-enhanced/feature-check (功能墙专用)');
  console.log('  - GET /admin/api/license-enhanced/license-info (诊断)');
  console.log('  - POST /admin/api/license-enhanced/test-refresh (诊断)');
  console.log('  - GET /admin/api/license-enhanced/status');
  console.log('  - POST /admin/api/license-enhanced/activate');
  console.log('  - POST /admin/api/license-enhanced/unbind');
  console.log('  - POST /admin/api/license-enhanced/verify');
  console.log('  - POST /admin/api/license-enhanced/refresh');
  console.log('  - POST /admin/api/license-enhanced/user/login');
  console.log('  - POST /admin/api/license-enhanced/user/logout');
  console.log('  - GET /admin/api/license-enhanced/user/session');
  console.log('  - GET /admin/api/license-enhanced/user/licenses');
  console.log('  - POST /admin/api/license-enhanced/user/quick-activate');
  console.log('  - POST /admin/api/license-enhanced/user/unbind');
  console.log('  - GET /admin/api/license-enhanced/health');
  console.log('  - GET /admin/api/license-enhanced/security');
};
