'use strict';

/**
 * License用户界面管理器
 * 负责处理用户交互和界面逻辑
 */

class LicenseUIManager {
  constructor(config, db) {
    this.config = config;
    this.db = db;
  }

  /**
   * 格式化License状态显示
   */
  formatLicenseStatus(statusData) {
    if (!statusData) {
      return {
        status: 'inactive',
        statusText: '未激活',
        statusClass: 'text-gray-500',
        message: '请输入License Key进行激活'
      };
    }

    const { success, mode, data, error, cacheExpiry } = statusData;

    if (success) {
      let statusText = '已激活';
      let statusClass = 'text-green-600';
      let message = `License有效 (${mode === 'online' ? '在线' : '离线缓存'})`;

      if (mode === 'offline' && cacheExpiry) {
        const remainingHours = Math.round((cacheExpiry - Date.now()) / (1000 * 60 * 60));
        message += ` - 缓存剩余 ${remainingHours} 小时`;
        
        if (remainingHours < 24) {
          statusClass = 'text-yellow-600';
          statusText = '缓存即将过期';
        }
      }

      return {
        status: 'active',
        statusText,
        statusClass,
        message,
        data
      };
    } else {
      return {
        status: 'error',
        statusText: '验证失败',
        statusClass: 'text-red-600',
        message: error || '未知错误'
      };
    }
  }

  /**
   * 格式化绑定冲突信息
   */
  formatBindingConflict(conflictData) {
    if (!conflictData || !conflictData.requiresTransfer) {
      return null;
    }

    const { currentBinding } = conflictData;
    
    return {
      hasConflict: true,
      message: 'License已绑定到其他实例',
      currentInstance: currentBinding?.instanceId || '未知',
      bindingTime: currentBinding?.bindingTime ? new Date(currentBinding.bindingTime).toLocaleString() : '未知',
      canTransfer: true
    };
  }

  /**
   * 生成操作按钮配置
   */
  getActionButtons(licenseStatus, hasConflict = false) {
    const buttons = [];

    if (licenseStatus.status === 'inactive') {
      buttons.push({
        id: 'activate',
        text: '激活License',
        class: 'btn btn-primary',
        action: 'activate'
      });
    } else if (licenseStatus.status === 'active') {
      buttons.push({
        id: 'refresh',
        text: '刷新状态',
        class: 'btn btn-secondary',
        action: 'refresh'
      });
      
      buttons.push({
        id: 'unbind',
        text: '解绑License',
        class: 'btn btn-danger',
        action: 'unbind'
      });
    } else if (licenseStatus.status === 'error') {
      buttons.push({
        id: 'retry',
        text: '重试验证',
        class: 'btn btn-warning',
        action: 'retry'
      });
    }

    if (hasConflict) {
      buttons.push({
        id: 'force-transfer',
        text: '强制转移',
        class: 'btn btn-warning',
        action: 'force-transfer'
      });
    }

    return buttons;
  }

  /**
   * 生成状态面板数据
   */
  generateStatusPanel(licenseKey, statusResult, bindingResult = null) {
    const licenseStatus = this.formatLicenseStatus(statusResult);
    const conflictInfo = this.formatBindingConflict(bindingResult);
    const actionButtons = this.getActionButtons(licenseStatus, conflictInfo?.hasConflict);

    return {
      licenseKey: licenseKey || '',
      status: licenseStatus,
      conflict: conflictInfo,
      buttons: actionButtons,
      instanceId: this.getInstanceId(),
      enhancedMode: this.config.enabled,
      lastUpdate: new Date().toLocaleString()
    };
  }

  /**
   * 生成历史记录显示数据
   */
  formatBindingHistory(historyData) {
    if (!historyData || !Array.isArray(historyData)) {
      return [];
    }

    return historyData.map(record => ({
      id: record.id,
      action: this.getActionText(record.action),
      instanceId: record.instanceId || '未知',
      timestamp: new Date(record.timestamp).toLocaleString(),
      details: record.details || ''
    }));
  }

  /**
   * 获取操作文本
   */
  getActionText(action) {
    const actionMap = {
      'bind': '绑定',
      'unbind': '解绑',
      'transfer': '转移',
      'verify': '验证'
    };
    
    return actionMap[action] || action;
  }

  /**
   * 生成错误提示
   */
  formatErrorMessage(error) {
    if (typeof error === 'string') {
      return error;
    }

    if (error && error.message) {
      return error.message;
    }

    return '未知错误';
  }

  /**
   * 生成成功提示
   */
  formatSuccessMessage(action, data = null) {
    const messages = {
      'activate': 'License激活成功',
      'unbind': 'License解绑成功',
      'transfer': 'License转移成功',
      'refresh': '状态刷新成功'
    };

    let message = messages[action] || '操作成功';
    
    if (data && data.plan) {
      message += ` (${data.plan})`;
    }

    return message;
  }

  /**
   * 获取实例ID
   */
  getInstanceId() {
    let instanceId = this.db.setting.get('instanceId');
    if (!instanceId) {
      const { uuid } = require('../../core');
      instanceId = uuid.v4();
      this.db.setting.set('instanceId', instanceId);
    }
    return instanceId;
  }

  /**
   * 验证License Key格式
   */
  validateLicenseKey(licenseKey) {
    if (!licenseKey || typeof licenseKey !== 'string') {
      return { valid: false, message: 'License Key不能为空' };
    }

    const trimmed = licenseKey.trim();

    if (trimmed.length < 10) {
      return { valid: false, message: 'License Key长度不足' };
    }

    // 标准格式：XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXX
    const standardPattern = /^[A-F0-9]{8}-[A-F0-9]{8}-[A-F0-9]{8}-[A-F0-9]{8}$/i;

    // 兼容格式
    const compatiblePatterns = [
      /^lic_[a-f0-9]{32}$/i,  // frontend格式
      /^LS-[A-F0-9]{32}$/i,   // payment格式
      /^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$/i  // enhanced格式
    ];

    if (standardPattern.test(trimmed)) {
      return { valid: true, licenseKey: trimmed, format: 'standard' };
    }

    for (const pattern of compatiblePatterns) {
      if (pattern.test(trimmed)) {
        return { valid: true, licenseKey: trimmed, format: 'compatible' };
      }
    }

    return {
      valid: false,
      message: '许可证格式不正确。请检查格式：XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXX'
    };
  }

  /**
   * 生成配置面板数据
   */
  generateConfigPanel() {
    return {
      enhancedMode: this.config.enabled,
      testMode: this.config.testMode,
      serverUrl: this.config.serverUrl,
      cacheValidityHours: this.config.cacheValidityHours,
      apiTimeout: this.config.apiTimeout
    };
  }
}

module.exports = LicenseUIManager;
