/**
 * 功能权限检查模块
 * 用于检查当前许可证是否拥有特定功能的权限
 * 支持动态Feature定义（从License Server获取）
 */

const licensePlansCache = require('../../config/license-plans-cache');
const fetch = require('node-fetch');

class FeatureChecker {
  constructor(db) {
    this.db = db;
    
    // 移除硬编码功能定义，完全依赖License Server动态获取
    // 所有功能定义统一存储在License Server数据库中
    
    // 动态Feature定义缓存
    this.dynamicFeatures = null;
    this.lastFeatureUpdate = 0;
    this.FEATURE_CACHE_TTL = 3600000; // 1小时缓存
    this.syncInterval = null; // 同步定时器
    
    // 许可证信息缓存 - 解决同步/异步调用问题
    this.cachedLicenseInfo = null;
    this.cachedSettings = new Map(); // 设置缓存
    this.pendingUpdates = new Map(); // 待处理的更新队列
    this.isUpdating = false; // 更新状态标志
    
    // 配置选项
    this.config = {
      showInactiveFeatures: false  // 是否显示未激活的功能，默认false
    };
    
    // 初始化缓存
    this.initializeAsync();
    
    // 配置将在初始化时异步加载
    // 从数据库加载配置的代码移到 initializeAsync 方法
    
    // License Server URL
    this.licenseServerUrl = process.env.LICENSE_SERVER_URL || 'https://dstatus_api.vps.mom';
    
    // 缓存文件路径
    this.cacheFilePath = require('path').join(__dirname, '../../data/features_cache.json');
  }
  
  /**
   * 异步初始化 - 预加载必要的缓存数据
   */
  async initializeAsync() {
    try {
      // 预加载许可证信息
      const rawLicenseInfo = await this.db.setting.get('license_info');
      if (rawLicenseInfo) {
        this.cachedLicenseInfo = typeof rawLicenseInfo === 'string' 
          ? JSON.parse(rawLicenseInfo) 
          : rawLicenseInfo;
      }
      
      // 预加载配置设置
      const showInactive = await this.db.setting.get('showInactiveFeatures');
      if (showInactive !== null) {
        this.config.showInactiveFeatures = showInactive === 'true';
        this.cachedSettings.set('showInactiveFeatures', showInactive);
      }
      
      console.log('[FeatureChecker] 异步初始化完成，缓存已预加载');
    } catch (error) {
      console.warn('[FeatureChecker] 异步初始化失败，使用默认值:', error.message);
    }
  }
  
  /**
   * 调度延迟更新（避免频繁数据库写入）
   */
  scheduleUpdate(key, data) {
    this.pendingUpdates.set(key, data);
    if (!this.isUpdating) {
      process.nextTick(() => this.processPendingUpdates());
    }
  }
  
  /**
   * 处理待更新的数据
   */
  async processPendingUpdates() {
    if (this.isUpdating) return;
    this.isUpdating = true;
    
    try {
      for (const [key, data] of this.pendingUpdates) {
        if (key === 'license_info') {
          await this.updateLicenseInfoAsync(data);
        } else {
          await this.db.setting.set(key, data);
          this.cachedSettings.set(key, data);
        }
      }
      this.pendingUpdates.clear();
    } catch (error) {
      console.error('[FeatureChecker] 处理待更新数据失败:', error);
    } finally {
      this.isUpdating = false;
    }
  }
  
  /**
   * 异步更新许可证信息
   */
  async updateLicenseInfoAsync(licenseInfo) {
    try {
      // 合并写入，避免覆盖更完整的有效数据（API优先→缓存次之→兜底）
      const existing = await this.db.setting.get('license_info');
      let merged = { ...(existing || {}), ...(licenseInfo || {}) };

      // 避免用未知/空值覆盖已知值
      const keepIfBetter = (field, isBetterFn) => {
        const oldVal = existing && existing[field];
        const newVal = merged[field];
        if (isBetterFn && oldVal !== undefined && !isBetterFn(newVal, oldVal)) {
          merged[field] = oldVal;
        }
      };

      // 计划名称/类型：避免用未知覆盖
      keepIfBetter('planName', (nv, ov) => nv && nv !== 'unknown');
      keepIfBetter('planType', (nv, ov) => nv && nv !== 'unknown');
      keepIfBetter('planDisplayName', (nv, ov) => nv && nv !== 'unknown');

      // 节点数：避免用0/无效覆盖有效值
      keepIfBetter('maxNodes', (nv, ov) => typeof nv === 'number' && nv > 0);

      // features：避免用空数组覆盖非空数组
      keepIfBetter('features', (nv, ov) => Array.isArray(nv) && nv.length > 0);

      // featuresMask：避免用0覆盖非零
      keepIfBetter('featuresMask', (nv, ov) => typeof nv === 'number' && nv > 0);

      // featureDetails：避免用空/缺失覆盖非空数组
      keepIfBetter('featureDetails', (nv, ov) => Array.isArray(nv) && nv.length > 0);

      await this.db.setting.set('license_info', merged);
      this.cachedLicenseInfo = merged;
      console.log('[FeatureChecker] 许可证信息已异步更新(合并写入)');
      // 标记写入元信息
      try {
        await this.db.setting.set('license_info_meta', {
          lastWriter: 'featureChecker.updateLicenseInfoAsync',
          lastTimestamp: Date.now(),
          keyPrefix: (merged?.licenseKey || merged?.key || '').toString().slice(0,8) + ((merged?.licenseKey || merged?.key) ? '...' : ''),
          plan: merged?.planName || merged?.planDisplayName || merged?.planType || 'unknown',
          maxNodes: merged?.maxNodes || 0,
          featuresCount: Array.isArray(merged?.features) ? merged.features.length : 0
        });
        let log = await this.db.setting.get('license_info_write_log');
        if (!Array.isArray(log)) log = [];
        log.push({ ts: Date.now(), writer: 'featureChecker.updateLicenseInfoAsync' });
        if (log.length > 10) log = log.slice(log.length - 10);
        await this.db.setting.set('license_info_write_log', log);
      } catch (_) {}
    } catch (error) {
      console.error('[FeatureChecker] 更新许可证信息失败:', error);
    }
  }
  
  /**
   * 获取动态Feature定义
   * 完全依赖License Server数据库，包含故障恢复机制
   * 现在利用StatusChecker的168小时离线缓存机制
   */
  getDynamicFeatures() {
    // 先检查内存缓存
    if (this.dynamicFeatures && Date.now() - this.lastFeatureUpdate < this.FEATURE_CACHE_TTL) {
      return this.dynamicFeatures;
    }

    // 尝试从License验证信息中获取最新的功能定义
    const rawLicenseInfo = this.cachedLicenseInfo;

    // 如果License信息中包含featureDetails，使用它（这是最新的）
    if (rawLicenseInfo && rawLicenseInfo.featureDetails && Array.isArray(rawLicenseInfo.featureDetails)) {
      const features = {};
      const names = {};

      rawLicenseInfo.featureDetails.forEach(feature => {
        features[feature.name] = feature.bit;
        names[feature.bit] = feature.displayName || feature.name;
      });

      this.dynamicFeatures = { features, names, details: rawLicenseInfo.featureDetails };
      this.lastFeatureUpdate = Date.now();
      this.saveFeaturesCache(this.dynamicFeatures); // 持久化缓存
      return this.dynamicFeatures;
    }

    // 新增：尝试利用StatusChecker的168小时离线缓存
    const offlineCacheResult = this.tryOfflineCache();
    if (offlineCacheResult) {
      this.dynamicFeatures = offlineCacheResult;
      this.lastFeatureUpdate = Date.now();
      this.saveFeaturesCache(this.dynamicFeatures); // 持久化缓存
      return this.dynamicFeatures;
    }

    // 尝试从本地缓存文件恢复
    const cachedFeatures = this.loadFeaturesCache();
    if (cachedFeatures) {
      this.dynamicFeatures = cachedFeatures;
      return this.dynamicFeatures;
    }

    // 最终后备方案：最小功能集
    console.warn('[FeatureChecker] 无法获取功能定义，使用最小功能集');
    return this.getMinimalFeatures();
  }
  
  /**
   * 初始化Feature同步
   * 在模块启动时调用
   */
  async initializeFeatureSync() {
    try {
      // 立即获取一次Feature定义
      await this.fetchFeatureDefinitions();
      
      // 设置定期同步（每小时）
      this.syncInterval = setInterval(async () => {
        try {
          await this.fetchFeatureDefinitions();
        } catch (error) {
          console.error('[FeatureChecker] 定期同步Feature失败:', error);
        }
      }, this.FEATURE_CACHE_TTL);
      
      console.log('[FeatureChecker] Feature同步已启动');
    } catch (error) {
      console.error('[FeatureChecker] 初始化Feature同步失败:', error);
    }
  }
  
  /**
   * 停止Feature同步
   * 在模块卸载时调用
   */
  stopFeatureSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log('[FeatureChecker] Feature同步已停止');
    }
  }
  
  /**
   * 位掩码转换为功能数组
   * @param {number} mask - 位掩码
   * @returns {Array<string>} 功能名称数组
   */
  convertMaskToFeatures(mask) {
    // 使用标准映射，不依赖动态功能
    const standardFeatureMap = {
      'BASIC_MONITORING': 1,
      'WEBSSH': 2,
      'AUTO_DISCOVERY': 4,
      'ADVANCED_ANALYTICS': 8,
      'API_ACCESS': 16,
      'CUSTOM_ALERTS': 32,
      'AI_ANALYTICS': 64,
      'NETWORK_QUALITY': 128
    };
    
    const result = [];
    for (const [featureName, bit] of Object.entries(standardFeatureMap)) {
      if (mask & bit) {
        result.push(featureName);
      }
    }

    return result.length > 0 ? result : ['BASIC_MONITORING'];
  }

  /**
   * 功能数组转换为显示名称
   * @param {Array<string>} features - 功能名称数组
   * @returns {Array<string>} 显示名称数组
   */
  convertFeaturesToDisplayNames(features) {
    // 优先使用动态映射（featureDetails 或服务端同步）
    try {
      const nameToDisplay = new Map();

      // 1) 尝试使用缓存的 license_info.featureDetails
      const cached = this.cachedLicenseInfo;
      if (cached && Array.isArray(cached.featureDetails) && cached.featureDetails.length > 0) {
        cached.featureDetails.forEach(f => {
          if (f && f.name) nameToDisplay.set(f.name, f.displayName || f.name);
        });
      }

      // 2) 若缺失，使用动态特征定义（离线缓存/服务端拉取）
      if (nameToDisplay.size === 0) {
        const dyn = this.getDynamicFeatures();
        if (dyn && Array.isArray(dyn.details) && dyn.details.length > 0) {
          dyn.details.forEach(f => {
            if (f && f.name) nameToDisplay.set(f.name, f.displayName || f.name);
          });
        }
      }

      // 3) 无可靠映射时，回退到静态映射；再不行直接使用英文名
      const fallbackMap = {
        'BASIC_MONITORING': '基础监控',
        'WEBSSH': 'WebSSH终端',
        'AUTO_DISCOVERY': '自动发现',
        'ADVANCED_ANALYTICS': '高级分析',
        'API_ACCESS': 'API访问',
        'CUSTOM_ALERTS': '自定义告警',
        'AI_ANALYTICS': 'AI分析',
        'NETWORK_QUALITY': '网络质量监控'
      };

      return features.map(name => nameToDisplay.get(name) || fallbackMap[name] || name);
    } catch (_) {
      // 防御性回退
      return Array.isArray(features) ? features.slice() : [];
    }
  }

  /**
   * 功能数组转换为位掩码
   * @param {Array<string>} features - 功能名称数组
   * @returns {number} 位掩码
   */
  convertFeaturesToMask(features) {
    // 使用标准映射，不依赖动态功能
    const standardFeatureMap = {
      'BASIC_MONITORING': 1,
      'WEBSSH': 2,
      'AUTO_DISCOVERY': 4,
      'ADVANCED_ANALYTICS': 8,
      'API_ACCESS': 16,
      'CUSTOM_ALERTS': 32,
      'AI_ANALYTICS': 64,
      'NETWORK_QUALITY': 128
    };
    
    let mask = 0;
    for (const featureName of features) {
      const bit = standardFeatureMap[featureName];
      if (bit) {
        mask |= bit;
      }
    }

    return mask > 0 ? mask : 1;
  }

  /**
   * 获取当前许可证信息
   * 支持新的features数组格式，同时保持向后兼容
   * 自动检测和升级数据格式
   */
  getCurrentLicenseInfo() {
    // 优先从数据库读取最新的 license_info，避免内存副本滞后
    let rawLicenseInfo;
    try {
      // 🔧 修复：确保数据库调用是同步的，先尝试同步获取
      let fromDb;
      if (this.db?.setting?.get) {
        try {
          fromDb = this.db.setting.get('license_info');
          // 如果返回Promise，回退到缓存
          if (fromDb && typeof fromDb.then === 'function') {
            console.log('[FeatureChecker] 检测到异步数据库调用，使用缓存数据');
            fromDb = null; // 强制使用缓存
          }
        } catch (dbError) {
          console.warn('[FeatureChecker] 数据库读取失败，使用缓存:', dbError.message);
          fromDb = null;
        }
      }
      
      // 兼容字符串存储：必要时进行 JSON 解析
      let parsed = fromDb;
      if (typeof fromDb === 'string') {
        try { parsed = JSON.parse(fromDb); } catch (_) { /* ignore */ }
      }
      rawLicenseInfo = parsed || this.cachedLicenseInfo;
      if (parsed) this.cachedLicenseInfo = parsed; // 同步内存副本
      
      // 调试：输出当前的license_info
      console.log('[FeatureChecker] getCurrentLicenseInfo - rawLicenseInfo:', {
        planType: rawLicenseInfo?.planType,
        features: rawLicenseInfo?.features,
        featuresMask: rawLicenseInfo?.featuresMask,
        maxNodes: rawLicenseInfo?.maxNodes
      });
    } catch (_) {
      rawLicenseInfo = this.cachedLicenseInfo;
    }
    if (!rawLicenseInfo) {
      // 如果没有许可证信息，返回 free 默认值
      return {
        planType: 'free',
        features: ['BASIC_MONITORING'], // 使用新格式的功能名称
        featuresMask: 1,
        featureList: ['基础监控'] // 保留旧格式用于显示
      };
    }

    // 检测数据格式
    const format = this.detectDataFormat(rawLicenseInfo);

    // 如果不是最新格式，进行升级
    let licenseInfo = rawLicenseInfo;
    if (format !== 'enhanced' && format !== 'array') {
      licenseInfo = this.upgradeDataFormat(rawLicenseInfo);

      // 保存升级后的数据 - 使用延迟异步更新机制
      if (licenseInfo && licenseInfo !== rawLicenseInfo) {
        // 立即更新缓存，确保同步调用能获取到最新数据
        this.cachedLicenseInfo = licenseInfo;
        // 调度异步更新，避免同步调用异步方法
        this.scheduleUpdate('license_info', licenseInfo);
        console.log('[FeatureChecker] 许可证信息已升级，调度异步保存');
      }
    } else {
      // 即使是新格式，也要确保数据完整性
      const upgraded = { ...licenseInfo };
      let needsUpdate = false;

      // 确保有features数组
      if (!upgraded.features && upgraded.featuresMask) {
        upgraded.features = this.convertMaskToFeatures(upgraded.featuresMask);
        needsUpdate = true;
      }

      // 确保有featuresMask
      if (!upgraded.featuresMask && upgraded.features) {
        upgraded.featuresMask = this.convertFeaturesToMask(upgraded.features);
        needsUpdate = true;
      }

      // 确保有featureList
      if (!upgraded.featureList && upgraded.features) {
        upgraded.featureList = this.convertFeaturesToDisplayNames(upgraded.features);
        needsUpdate = true;
      }

      if (needsUpdate) {
        licenseInfo = upgraded;
        // 立即更新缓存，调度异步保存
        this.cachedLicenseInfo = licenseInfo;
        this.scheduleUpdate('license_info', licenseInfo);
        console.log('[FeatureChecker] 许可证信息已补全，调度异步保存');
      }
    }

    // 验证数据一致性
    if (!this.validateDataConsistency(licenseInfo)) {
      console.info('[FeatureChecker] 检测到数据不一致，尝试修复');
      // 以features数组为准进行修复
      if (licenseInfo.features) {
        licenseInfo.featuresMask = this.convertFeaturesToMask(licenseInfo.features);
        licenseInfo.featureList = this.convertFeaturesToDisplayNames(licenseInfo.features);
        // 立即更新缓存，调度异步保存
        this.cachedLicenseInfo = licenseInfo;
        this.scheduleUpdate('license_info', licenseInfo);
        console.log('[FeatureChecker] 数据不一致已修复，调度异步保存');
      }
    }

    return licenseInfo;
  }
  
  /**
   * 检查是否拥有指定功能
   * @param {string} featureName - 功能名称（如 'WEBSSH', 'AUTO_DISCOVERY'）
   * @returns {boolean} 是否拥有该功能
   */
  hasFeature(featureName) {
    const licenseInfo = this.getCurrentLicenseInfo();

    // 如果是全功能许可证
    if (licenseInfo.features && licenseInfo.features.includes('全部功能')) {
      return true;
    }

    // 获取动态Feature定义
    const { features, details } = this.getDynamicFeatures();

    // 规范化功能名称，支持多种格式
    const normalizedFeatureName = this.normalizeFeatureName(featureName);
    const possibleNames = this.getFeatureNameVariations(featureName);

    // 如果有featureDetails，优先使用它进行检查
    if (licenseInfo.featureDetails && Array.isArray(licenseInfo.featureDetails)) {
      // 先尝试精确匹配
      let feature = licenseInfo.featureDetails.find(f => f.name === featureName);
      
      // 如果精确匹配失败，尝试所有可能的名称变体
      if (!feature) {
        for (const name of possibleNames) {
          feature = licenseInfo.featureDetails.find(f => f.name === name);
          if (feature) break;
        }
      }
      
      return feature ? feature.available === true : false;
    }

    // 优先使用新格式的features数组进行检查
    if (licenseInfo.features && Array.isArray(licenseInfo.features)) {
      // 先尝试精确匹配
      if (licenseInfo.features.includes(featureName)) {
        return true;
      }
      
      // 尝试所有可能的名称变体
      for (const name of possibleNames) {
        if (licenseInfo.features.includes(name)) {
          return true;
        }
      }
      
      return false;
    }

    // 后备方案：使用传统的位运算检查
    let featureBit = features[featureName];
    
    // 如果精确匹配失败，尝试所有可能的名称变体
    if (!featureBit) {
      for (const name of possibleNames) {
        featureBit = features[name];
        if (featureBit) break;
      }
    }
    
    if (!featureBit) {
      console.warn(`[FeatureChecker] 未知的功能名称: ${featureName} (已尝试变体: ${possibleNames.join(', ')})`);
      return false;
    }

    // 如果有featuresMask，使用位运算检查
    if (licenseInfo.featuresMask !== undefined) {
      return (licenseInfo.featuresMask & featureBit) !== 0;
    }

    // 最后的后备方案：检查旧格式的features数组（显示名称）
    const { names } = this.getDynamicFeatures();
    const featureDisplayName = names[featureBit];
    return licenseInfo.featureList && licenseInfo.featureList.includes(featureDisplayName);
  }

  /**
   * 规范化功能名称
   * @param {string} featureName - 原始功能名称
   * @returns {string} 规范化后的功能名称
   */
  normalizeFeatureName(featureName) {
    return featureName.toLowerCase().replace(/[-_]/g, '');
  }

  /**
   * 获取功能名称的所有可能变体
   * @param {string} featureName - 原始功能名称
   * @returns {Array<string>} 功能名称变体数组
   */
  getFeatureNameVariations(featureName) {
    const variations = [featureName]; // 包含原始名称
    
    // 常见的功能名称映射
    const nameMap = {
      // WebSSH的各种变体
      'webssh': ['WEBSSH', 'WEB_SSH', 'web_ssh', 'web-ssh', 'WebSSH'],
      'WEBSSH': ['webssh', 'WEB_SSH', 'web_ssh', 'web-ssh', 'WebSSH'],
      'web_ssh': ['WEBSSH', 'webssh', 'web-ssh', 'WebSSH'],
      'web-ssh': ['WEBSSH', 'webssh', 'WEB_SSH', 'web_ssh', 'WebSSH'],
      'WebSSH': ['WEBSSH', 'webssh', 'WEB_SSH', 'web_ssh', 'web-ssh'],
      
      // AUTO_DISCOVERY的各种变体
      'autodiscovery': ['AUTO_DISCOVERY', 'auto_discovery', 'auto-discovery', 'autoDiscovery'],
      'AUTO_DISCOVERY': ['autodiscovery', 'auto_discovery', 'auto-discovery', 'autoDiscovery'],
      'auto_discovery': ['AUTO_DISCOVERY', 'autodiscovery', 'auto-discovery', 'autoDiscovery'],
      'auto-discovery': ['AUTO_DISCOVERY', 'autodiscovery', 'auto_discovery', 'autoDiscovery'],
      'autoDiscovery': ['AUTO_DISCOVERY', 'autodiscovery', 'auto_discovery', 'auto-discovery'],
      
      // ADVANCED_ANALYTICS的各种变体
      'advancedanalytics': ['ADVANCED_ANALYTICS', 'advanced_analytics', 'advanced-analytics', 'advancedAnalytics'],
      'ADVANCED_ANALYTICS': ['advancedanalytics', 'advanced_analytics', 'advanced-analytics', 'advancedAnalytics'],
      'advanced_analytics': ['ADVANCED_ANALYTICS', 'advancedanalytics', 'advanced-analytics', 'advancedAnalytics'],
      'advanced-analytics': ['ADVANCED_ANALYTICS', 'advancedanalytics', 'advanced_analytics', 'advancedAnalytics'],
      'advancedAnalytics': ['ADVANCED_ANALYTICS', 'advancedanalytics', 'advanced_analytics', 'advanced-analytics'],
      
      // API_ACCESS的各种变体
      'apiaccess': ['API_ACCESS', 'api_access', 'api-access', 'apiAccess'],
      'API_ACCESS': ['apiaccess', 'api_access', 'api-access', 'apiAccess'],
      'api_access': ['API_ACCESS', 'apiaccess', 'api-access', 'apiAccess'],
      'api-access': ['API_ACCESS', 'apiaccess', 'api_access', 'apiAccess'],
      'apiAccess': ['API_ACCESS', 'apiaccess', 'api_access', 'api-access'],
      
      // CUSTOM_ALERTS的各种变体
      'customalerts': ['CUSTOM_ALERTS', 'custom_alerts', 'custom-alerts', 'customAlerts'],
      'CUSTOM_ALERTS': ['customalerts', 'custom_alerts', 'custom-alerts', 'customAlerts'],
      'custom_alerts': ['CUSTOM_ALERTS', 'customalerts', 'custom-alerts', 'customAlerts'],
      'custom-alerts': ['CUSTOM_ALERTS', 'customalerts', 'custom_alerts', 'customAlerts'],
      'customAlerts': ['CUSTOM_ALERTS', 'customalerts', 'custom_alerts', 'custom-alerts'],
      
      // BASIC_MONITORING的各种变体
      'basicmonitoring': ['BASIC_MONITORING', 'basic_monitoring', 'basic-monitoring', 'basicMonitoring'],
      'BASIC_MONITORING': ['basicmonitoring', 'basic_monitoring', 'basic-monitoring', 'basicMonitoring'],
      'basic_monitoring': ['BASIC_MONITORING', 'basicmonitoring', 'basic-monitoring', 'basicMonitoring'],
      'basic-monitoring': ['BASIC_MONITORING', 'basicmonitoring', 'basic_monitoring', 'basicMonitoring'],
      'basicMonitoring': ['BASIC_MONITORING', 'basicmonitoring', 'basic_monitoring', 'basic-monitoring'],
      
      // NETWORK_QUALITY的各种变体
      'networkquality': ['NETWORK_QUALITY', 'network_quality', 'network-quality', 'networkQuality'],
      'NETWORK_QUALITY': ['networkquality', 'network_quality', 'network-quality', 'networkQuality'],
      'network_quality': ['NETWORK_QUALITY', 'networkquality', 'network-quality', 'networkQuality'],
      'network-quality': ['NETWORK_QUALITY', 'networkquality', 'network_quality', 'networkQuality'],
      'networkQuality': ['NETWORK_QUALITY', 'networkquality', 'network_quality', 'network-quality']
    };
    
    // 添加映射的变体
    if (nameMap[featureName]) {
      variations.push(...nameMap[featureName]);
    }
    
    // 去重并返回
    return [...new Set(variations)];
  }
  
  /**
   * 从License Server获取Feature定义
   * @returns {Promise<Object>} Feature定义
   */
  async fetchFeatureDefinitions() {
    try {
      // 检查缓存
      if (this.dynamicFeatures && Date.now() - this.lastFeatureUpdate < this.FEATURE_CACHE_TTL) {
        return this.dynamicFeatures;
      }
      
      // 获取所有激活的功能
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const response = await fetch(`${this.licenseServerUrl}/api/admin/features?active=true`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success && result.data) {
        // 构建Feature映射
        const features = {};
        const names = {};
        const details = [];
        
        result.data.forEach(feature => {
          features[feature.name] = feature.bit;
          names[feature.bit] = feature.displayName;
          details.push({
            bit: feature.bit,
            name: feature.name,
            displayName: feature.displayName,
            description: feature.description,
            icon: feature.icon,
            category: feature.category,
            available: null // 不在这里判断权限，由hasFeature根据licenseInfo判断
          });
        });
        
        // 更新缓存
        this.dynamicFeatures = { features, names, details };
        this.lastFeatureUpdate = Date.now();
        
        // 只在数量变化时输出日志
        if (!this.lastFeatureCount || details.length !== this.lastFeatureCount) {
          console.log('[FeatureChecker] Feature定义已更新，共', details.length, '个功能');
          this.lastFeatureCount = details.length;
        }
        return this.dynamicFeatures;
      }
      
      throw new Error('Invalid response format');
    } catch (error) {
      console.error('[FeatureChecker] 获取Feature定义失败:', error);
      // 返回默认值
      // 返回最小功能集作为最终后备
      return this.getMinimalFeatures();
    }
  }
  
  /**
   * 尝试利用StatusChecker的168小时离线缓存
   * @returns {Object|null} 功能定义或null
   */
  tryOfflineCache() {
    try {
      // 从缓存的许可证信息中获取licenseKey
      const licenseKey = this.cachedLicenseInfo?.key || this.cachedLicenseInfo?.licenseKey;
      if (!licenseKey) {
        return null;
      }

      const statusChecker = this.getStatusChecker();
      if (!statusChecker) {
        return null;
      }

      const cacheResult = statusChecker.checkOfflineCache(licenseKey);
      if (cacheResult.valid && cacheResult.data && cacheResult.data.featureDetails) {
        console.log('[FeatureChecker] 使用StatusChecker的168小时离线缓存');

        const features = {};
        const names = {};

        cacheResult.data.featureDetails.forEach(feature => {
          features[feature.name] = feature.bit;
          names[feature.bit] = feature.displayName || feature.name;
        });

        return { features, names, details: cacheResult.data.featureDetails };
      }
    } catch (error) {
      console.warn('[FeatureChecker] 尝试离线缓存失败:', error);
    }

    return null;
  }

  /**
   * 获取StatusChecker实例
   * @returns {Object|null} StatusChecker实例或null
   */
  getStatusChecker() {
    try {
      // 尝试从全局应用实例获取
      if (global.app && global.app.locals && global.app.locals['license-enhanced']) {
        return global.app.locals['license-enhanced'].statusChecker;
      }

      // 后备方案：直接require模块
      const licenseEnhanced = require('./index');
      return licenseEnhanced.statusChecker;
    } catch (error) {
      console.warn('[FeatureChecker] 无法获取StatusChecker实例:', error);
      return null;
    }
  }

  /**
   * 获取最小功能集（紧急后备方案）
   * 返回标准功能映射，支持所有已知功能
   */
  getMinimalFeatures() {
    // 使用完整的标准功能映射作为后备
    return {
      features: {
        'BASIC_MONITORING': 1,
        'WEBSSH': 2,
        'AUTO_DISCOVERY': 4,
        'ADVANCED_ANALYTICS': 8,
        'API_ACCESS': 16,
        'CUSTOM_ALERTS': 32,
        'AI_ANALYTICS': 64,
        'NETWORK_QUALITY': 128
      },
      names: {
        1: '基础监控',
        2: 'WebSSH终端',
        4: '自动发现',
        8: '高级分析',
        16: 'API访问',
        32: '自定义告警',
        64: 'AI分析',
        128: '网络质量监控'
      },
      details: [
        { bit: 1, name: 'BASIC_MONITORING', displayName: '基础监控', description: '实时监控服务器状态', available: true },
        { bit: 2, name: 'WEBSSH', displayName: 'WebSSH终端', description: 'Web端SSH终端访问', available: true },
        { bit: 4, name: 'AUTO_DISCOVERY', displayName: '自动发现', description: '自动发现网络中的服务器', available: true },
        { bit: 8, name: 'ADVANCED_ANALYTICS', displayName: '高级分析', description: '高级数据分析和报表', available: true },
        { bit: 16, name: 'API_ACCESS', displayName: 'API访问', description: 'REST API访问权限', available: true },
        { bit: 32, name: 'CUSTOM_ALERTS', displayName: '自定义告警', description: '自定义告警规则', available: true },
        { bit: 64, name: 'AI_ANALYTICS', displayName: 'AI分析', description: 'AI驱动的智能分析', available: true },
        { bit: 128, name: 'NETWORK_QUALITY', displayName: '网络质量监控', description: '网络质量和延迟监控', available: true }
      ]
    };
  }
  
  /**
   * 保存功能定义到本地缓存文件
   * @param {Object} features - 功能定义对象
   */
  saveFeaturesCache(features) {
    try {
      const fs = require('fs');
      const cacheData = {
        features: features,
        timestamp: Date.now(),
        version: '1.0'
      };
      
      // 确保目录存在
      const dir = require('path').dirname(this.cacheFilePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      fs.writeFileSync(this.cacheFilePath, JSON.stringify(cacheData, null, 2));
    } catch (error) {
      console.warn('[FeatureChecker] 保存功能缓存失败:', error);
    }
  }
  
  /**
   * 从本地缓存文件加载功能定义
   * @returns {Object|null} 缓存的功能定义或null
   */
  loadFeaturesCache() {
    try {
      const fs = require('fs');
      if (!fs.existsSync(this.cacheFilePath)) {
        return null;
      }
      
      const cacheData = JSON.parse(fs.readFileSync(this.cacheFilePath, 'utf8'));
      
      // 检查缓存是否过期（7天）
      if (Date.now() - cacheData.timestamp > 7 * 24 * 3600000) {
        console.warn('[FeatureChecker] 功能缓存已过期，忽略');
        return null;
      }
      
      console.log('[FeatureChecker] 从本地缓存恢复功能定义');
      return cacheData.features;
    } catch (error) {
      console.warn('[FeatureChecker] 加载功能缓存失败:', error);
      return null;
    }
  }
  
  /**
   * 获取当前拥有的所有功能列表
   * @param {string} format - 返回格式：'names'(功能名称), 'display'(显示名称), 'both'(两者)
   * @returns {Array<string>|Object} 功能列表
   */
  getAvailableFeatures(format = 'display') {
    const licenseInfo = this.getCurrentLicenseInfo();
    const { details, names, features: featureMap } = this.getDynamicFeatures();

    // 如果licenseInfo中有featureDetails（从服务器返回的），使用它
    if (licenseInfo.featureDetails && Array.isArray(licenseInfo.featureDetails)) {
      const availableFeatures = licenseInfo.featureDetails.filter(f => f.available === true);

      switch (format) {
        case 'names':
          return availableFeatures.map(f => f.name);
        case 'display':
          return availableFeatures.map(f => f.displayName || f.name);
        case 'both':
          return {
            names: availableFeatures.map(f => f.name),
            display: availableFeatures.map(f => f.displayName || f.name)
          };
        default:
          return availableFeatures.map(f => f.displayName || f.name);
      }
    }

    // 优先使用新格式的features数组
    if (licenseInfo.features && Array.isArray(licenseInfo.features)) {
      switch (format) {
        case 'names':
          return licenseInfo.features;
        case 'display':
          return this.convertFeaturesToDisplayNames(licenseInfo.features);
        case 'both':
          return {
            names: licenseInfo.features,
            display: this.convertFeaturesToDisplayNames(licenseInfo.features)
          };
        default:
          return this.convertFeaturesToDisplayNames(licenseInfo.features);
      }
    }

    // 后备方案：使用旧格式的featureList
    if (licenseInfo.featureList && Array.isArray(licenseInfo.featureList)) {
      switch (format) {
        case 'names':
          // 尝试从显示名称反向查找功能名称
          return licenseInfo.featureList.map(displayName => {
            for (const [name, bit] of Object.entries(featureMap)) {
              if (names[bit] === displayName) {
                return name;
              }
            }
            return displayName;
          });
        case 'display':
          return licenseInfo.featureList;
        case 'both':
          const namesList = licenseInfo.featureList.map(displayName => {
            for (const [name, bit] of Object.entries(featureMap)) {
              if (names[bit] === displayName) {
                return name;
              }
            }
            return displayName;
          });
          return {
            names: namesList,
            display: licenseInfo.featureList
          };
        default:
          return licenseInfo.featureList;
      }
    }

    // 最后的后备方案：如果只有featuresMask，解析它
    const features = [];
    const displayNames = [];
    if (licenseInfo.featuresMask) {
      for (let bit in names) {
        if (licenseInfo.featuresMask & parseInt(bit)) {
          // 查找对应的功能名称
          for (const [name, featureBit] of Object.entries(featureMap)) {
            if (featureBit === parseInt(bit)) {
              features.push(name);
              break;
            }
          }
          displayNames.push(names[bit]);
        }
      }
    }

    // 如果没有任何功能，返回默认值
    if (features.length === 0) {
      switch (format) {
        case 'names':
          return ['BASIC_MONITORING'];
        case 'display':
          return ['基础监控'];
        case 'both':
          return {
            names: ['BASIC_MONITORING'],
            display: ['基础监控']
          };
        default:
          return ['基础监控'];
      }
    }

    switch (format) {
      case 'names':
        return features;
      case 'display':
        return displayNames;
      case 'both':
        return {
          names: features,
          display: displayNames
        };
      default:
        return displayNames;
    }
  }
  
  /**
   * 检查功能并返回提示信息（简化版 - 支持API和数据库缓存）
   * @param {string} featureName - 功能名称
   * @returns {object} { allowed: boolean, message: string }
   */
  checkFeature(featureName) {
    try {
      // 获取许可证信息（从数据库缓存或内存）
      const licenseInfo = this.getCurrentLicenseInfo();
      
      // 标准化功能名称为大写下划线格式
      const normalizedName = featureName.toUpperCase().replace(/-/g, '_');
      
      // 优先级1：使用featureDetails（最准确，包含available状态）
      if (licenseInfo.featureDetails && Array.isArray(licenseInfo.featureDetails)) {
        const feature = licenseInfo.featureDetails.find(f => {
          const fname = f.name.toUpperCase().replace(/-/g, '_');
          return fname === normalizedName || fname === featureName.toUpperCase();
        });
        
        if (feature) {
          return {
            allowed: feature.available === true,
            reason: feature.available ? null : 'FEATURE_NOT_IN_PLAN',
            message: feature.available ? '功能可用' : `当前套餐不包含"${feature.displayName}"功能，请升级套餐`,
            currentPlan: licenseInfo.planName || licenseInfo.planDisplayName || licenseInfo.planType || ''
          };
        }
      }
      
      // 优先级2：检查features数组（通常包含已授权的功能列表）
      if (licenseInfo.features && Array.isArray(licenseInfo.features) && licenseInfo.features.length > 0) {
        // 支持多种格式匹配
        const hasFeature = licenseInfo.features.some(f => {
          const fname = f.toUpperCase().replace(/-/g, '_');
          return fname === normalizedName || fname === featureName.toUpperCase();
        });
        
        return {
          allowed: hasFeature,
          reason: hasFeature ? null : 'FEATURE_NOT_IN_PLAN',
          message: hasFeature ? '功能可用' : `当前套餐不包含此功能，请升级套餐`,
          currentPlan: licenseInfo.planName || licenseInfo.planDisplayName || licenseInfo.planType || ''
        };
      }
      
      // 优先级3：使用位掩码（传统方式）
      if (licenseInfo.featuresMask !== undefined && licenseInfo.featuresMask !== null) {
        // 标准功能位定义
        const featureBits = {
          'BASIC_MONITORING': 1,
          'WEBSSH': 2,
          'AUTO_DISCOVERY': 4,
          'ADVANCED_ANALYTICS': 8,
          'API_ACCESS': 16,
          'CUSTOM_ALERTS': 32,
          'AI_ANALYTICS': 64,
          'NETWORK_QUALITY': 128
        };
        
        const bit = featureBits[normalizedName];
        if (bit) {
          const hasFeature = (licenseInfo.featuresMask & bit) !== 0;
          return {
            allowed: hasFeature,
            reason: hasFeature ? null : 'FEATURE_NOT_IN_PLAN',
            message: hasFeature ? '功能可用' : `当前套餐不包含此功能，请升级套餐`,
            currentPlan: licenseInfo.planName || licenseInfo.planDisplayName || licenseInfo.planType || ''
          };
        }
      }
      
      // 优先级4：检查计划类型（某些计划默认包含所有功能）
      if (licenseInfo.planType === 'enterprise' || licenseInfo.planType === 'unlimited') {
        console.log(`[FeatureChecker] 企业版/无限版默认允许功能: ${featureName}`);
        return {
          allowed: true,
          reason: null,
          message: '功能可用'
        };
      }
      
      // 默认策略：基础功能允许，高级功能根据配置
      const basicFeatures = ['BASIC_MONITORING', 'NETWORK_QUALITY'];
      if (basicFeatures.includes(normalizedName)) {
        return {
          allowed: true,
          reason: null,
          message: '功能可用'
        };
      }
      
      // 对于无法确定的功能，根据环境决定
      const defaultAllow = process.env.NODE_ENV === 'development';
      console.warn(`[FeatureChecker] 无法确定功能 ${featureName} 的权限，${defaultAllow ? '默认允许' : '默认拒绝'}`);
      
      return {
        allowed: defaultAllow,
        reason: defaultAllow ? null : 'FEATURE_UNKNOWN',
        message: defaultAllow ? '功能可用' : '功能权限未定义，请联系管理员',
        currentPlan: licenseInfo.planName || licenseInfo.planDisplayName || licenseInfo.planType || ''
      };
    } catch (error) {
      console.error('[FeatureChecker] checkFeature异常:', error);
      // 异常时在开发环境允许，生产环境拒绝
      const defaultAllow = process.env.NODE_ENV === 'development';
      return {
        allowed: defaultAllow,
        reason: defaultAllow ? null : 'CHECK_ERROR',
        message: defaultAllow ? '功能可用' : '权限检查失败，请稍后重试'
      };
    }
  }

  /**
   * 获取功能限制值
   * @param {string} featureName - 功能名称
   * @param {string} limitType - 限制类型 (如 'TimeRange', 'NodeCount' 等)
   * @returns {number|null} 限制值，如果没有限制返回null
   */
  getFeatureLimit(featureName, limitType) {
    try {
      const licenseInfo = this.getCurrentLicenseInfo();

      // 检查是否有limits配置
      if (licenseInfo.limits && typeof licenseInfo.limits === 'object') {
        const featureLimits = licenseInfo.limits[featureName];
        if (featureLimits && typeof featureLimits === 'object') {
          return featureLimits[limitType] || null;
        }
      }

      // 后备方案：使用默认限制
      const defaultLimits = this.getDefaultFeatureLimits();
      if (defaultLimits[featureName] && defaultLimits[featureName][limitType]) {
        return defaultLimits[featureName][limitType];
      }

      return null;
    } catch (error) {
      console.error('[FeatureChecker] 获取功能限制失败:', error);
      return null;
    }
  }

  /**
   * 获取默认功能限制配置
   * @returns {object} 默认限制配置
   */
  getDefaultFeatureLimits() {
    return {
      'NETWORK_QUALITY': {
        'TimeRange': 24 * 60 * 60 * 1000, // 24小时，单位毫秒
        'MaxTargets': 10
      },
      'BASIC_MONITORING': {
        'NodeCount': 5,
        'DataRetention': 30 * 24 * 60 * 60 * 1000 // 30天
      }
    };
  }

  /**
   * 网络质量权限综合检查
   * @param {number} requestedTimeRange - 请求的时间范围(毫秒)
   * @returns {Object} 检查结果
   */
  checkNetworkQualityAccess(requestedTimeRange = 0) {
    try {
      // 第一步：检查基础功能权限
      if (!this.hasFeature('NETWORK_QUALITY')) {
        return {
          allowed: false,
          reason: 'FEATURE_NOT_AVAILABLE',
          message: '当前套餐不支持网络质量监控功能',
          upgradeUrl: '/admin/license-management'
        };
      }

      // 第二步：检查时间范围限制（如果指定了时间范围）
      if (requestedTimeRange > 0) {
        const maxTimeRange = this.getFeatureLimit('NETWORK_QUALITY', 'TimeRange');
        if (maxTimeRange && requestedTimeRange > maxTimeRange) {
          return {
            allowed: false,
            reason: 'TIME_RANGE_EXCEEDED',
            maxRange: maxTimeRange,
            message: `当前套餐最多支持${this.formatTimeRange(maxTimeRange)}的历史数据`,
            upgradeUrl: '/admin/license-management'
          };
        }
      }

      return { allowed: true };
    } catch (error) {
      console.error('[FeatureChecker] 网络质量权限检查失败:', error);
      return {
        allowed: false,
        reason: 'CHECK_FAILED',
        message: '权限检查失败，请稍后重试'
      };
    }
  }

  /**
   * 格式化时间范围显示
   * @param {number} timeRangeMs - 时间范围（毫秒）
   * @returns {string} 格式化的时间范围字符串
   */
  formatTimeRange(timeRangeMs) {
    const hours = Math.floor(timeRangeMs / (60 * 60 * 1000));
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天`;
    } else if (hours > 0) {
      return `${hours}小时`;
    } else {
      const minutes = Math.floor(timeRangeMs / (60 * 1000));
      return `${minutes}分钟`;
    }
  }
  
  /**
   * 创建功能检查中间件（用于Express路由）
   * @param {string} featureName - 需要检查的功能名称
   * @returns {Function} Express中间件
   */
  requireFeature(featureName) {
    return (req, res, next) => {
      const check = this.checkFeature(featureName);
      
      if (check.allowed) {
        next();
      } else {
        const { names, features } = this.getDynamicFeatures();
        res.status(403).json({
          error: 'Feature not available',
          message: check.message,
          currentPlan: check.currentPlan,
          requiredFeature: names[features[featureName]] || featureName,
          featureDescription: check.featureDescription
        });
      }
    };
  }
  
  /**
   * 更新配置选项
   * @param {Object} newConfig - 新的配置选项
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    // 使用延迟异步更新机制
    this.scheduleUpdate('featureChecker_config', this.config);
    console.log('[FeatureChecker] 配置已更新，调度异步保存:', this.config);
  }

  /**
   * 获取功能墙信息（用于前端显示）
   * @param {boolean} showOnlyActive - 是否只显示活跃的功能（使用配置中的值）
   * @returns {object} 功能墙配置
   */
  async getFeatureWallInfo(showOnlyActive = null) {
    // 如果没有指定，使用配置中的值
    if (showOnlyActive === null) {
      showOnlyActive = !this.config.showInactiveFeatures;
    }
    let licenseInfo = this.getCurrentLicenseInfo();

    // 在线优先修复：当本地缓存缺失或为未知状态时，尝试用 currentLicenseKey 进行一次在线校验并更新缓存
    try {
      const needsOnlineSync = !licenseInfo || !licenseInfo.isValid ||
        licenseInfo.planType === 'unknown' ||
        (Array.isArray(licenseInfo.features) && licenseInfo.features.length === 0) ||
        (!Array.isArray(licenseInfo.features) && !licenseInfo.featuresMask) ||
        !licenseInfo.maxNodes || licenseInfo.maxNodes === 0;

      if (needsOnlineSync) {
        const keyFromInfo = licenseInfo?.key || licenseInfo?.licenseKey;
        const keyFromDb = await this.db.setting.get('currentLicenseKey');
        const licenseKey = keyFromInfo || keyFromDb;

        if (licenseKey) {
          try {
            const instanceId = await this.db.setting.get('instanceId');
            const statusChecker = this.getStatusChecker();
            if (statusChecker && typeof statusChecker.refreshLicense === 'function') {
              const refreshed = await statusChecker.refreshLicense(licenseKey, instanceId);
              if (refreshed && refreshed.success) {
                // 重新读取最新的 license_info
                const updated = await this.db.setting.get('license_info');
                if (updated) {
                  this.cachedLicenseInfo = updated;
                  licenseInfo = updated;
                }
              }
            }
          } catch (syncErr) {
            console.warn('[FeatureChecker] 在线同步授权信息失败:', syncErr.message);
          }
        }
      }
    } catch (e) {
      console.warn('[FeatureChecker] 尝试在线优先修复时异常:', e.message);
    }
    
    // 🔧 修复：如果没有 featureDetails 或为空数组，尝试从服务器获取
    if ((!Array.isArray(licenseInfo.featureDetails) || licenseInfo.featureDetails.length === 0) && licenseInfo.key && licenseInfo.isValid) {
      console.log('[FeatureChecker] featureDetails缺失，尝试从服务器同步');
      try {
        const instanceId = await this.db.setting.get('instanceId');
        const statusChecker = require('./statusChecker');
        const checker = new statusChecker(
          { serverUrl: this.licenseServerUrl, apiTimeout: 30000, cacheValidityHours: 168 },
          this.db
        );
        
        const verifyResult = await checker.verifyLicense(licenseInfo.key, instanceId);
        
        if (verifyResult.success && verifyResult.planInfo?.featureDetails) {
          console.log('[FeatureChecker] 成功获取 featureDetails，更新 license_info');
          licenseInfo.featureDetails = verifyResult.planInfo.featureDetails;
          await this.db.setting.set('license_info', licenseInfo);
          try {
            await this.db.setting.set('license_info_meta', {
              lastWriter: 'featureChecker.getFeatureWallInfo.syncFeatureDetails',
              lastTimestamp: Date.now(),
              keyPrefix: (licenseInfo?.licenseKey || licenseInfo?.key || '').toString().slice(0,8) + ((licenseInfo?.licenseKey || licenseInfo?.key) ? '...' : ''),
              plan: licenseInfo?.planName || licenseInfo?.planDisplayName || licenseInfo?.planType || 'unknown',
              maxNodes: licenseInfo?.maxNodes || 0,
              featuresCount: Array.isArray(licenseInfo?.features) ? licenseInfo.features.length : 0
            });
            let log = await this.db.setting.get('license_info_write_log');
            if (!Array.isArray(log)) log = [];
            log.push({ ts: Date.now(), writer: 'featureChecker.syncFeatureDetails' });
            if (log.length > 10) log = log.slice(log.length - 10);
            await this.db.setting.set('license_info_write_log', log);
          } catch (_) {}
          
          // 更新缓存
          this.cachedLicenseInfo = licenseInfo;
        }
      } catch (error) {
        console.warn('[FeatureChecker] 获取 featureDetails 失败:', error.message);
      }
    }
    
    const availableFeaturesData = this.getAvailableFeatures('both');
    const { details, features: featureMap, names: featureNames } = this.getDynamicFeatures();

    // 调试日志
    console.log('[FeatureChecker] getFeatureWallInfo - licenseInfo.featureDetails:', 
      licenseInfo.featureDetails ? `有 ${licenseInfo.featureDetails.length} 个功能` : '无');

    // 如果有非空的featureDetails，使用它（避免循环调用）
    if (Array.isArray(licenseInfo.featureDetails) && licenseInfo.featureDetails.length > 0) {
      const features = {};

      // 获取活跃功能列表（从配置或环境变量）
      const inactiveFeatures = await this.db.setting.get('inactive_features') || [];
      
      // 获取调试标志
      const debug = await this.db.setting.get('debug');
      
      // 按分类组织功能
      licenseInfo.featureDetails.forEach(feature => {
        // 如果功能在不活跃列表中，跳过
        if (showOnlyActive && inactiveFeatures.includes(feature.name)) {
          // 调试级别日志
          if (debug) {
            console.log(`[FeatureChecker] 过滤掉已关闭的功能: ${feature.name}`);
          }
          return;
        }
        
        const key = feature.name.toLowerCase();  // 保留下划线，不替换
        features[key] = {
          name: feature.displayName,
          description: feature.description,
          available: feature.available === true,
          icon: feature.icon,
          category: feature.category,
          requiredPlan: feature.available ? '当前套餐' : '更高级套餐',
          featureName: feature.name // 添加功能名称用于检查
        };
      });

      console.log('[FeatureChecker] 返回动态功能，功能数量:', Object.keys(features).length);
      console.log('[FeatureChecker] 功能列表:', Object.keys(features));

      return {
        currentPlan: {
          name: licenseInfo.planName || licenseInfo.planDisplayName || licenseInfo.planType || '',
          type: licenseInfo.planType || 'free',
          features: availableFeaturesData.display || availableFeaturesData,
          featureNames: availableFeaturesData.names || [], // 新增：功能名称数组
          maxNodes: licenseInfo.maxNodes || 10
        },
        features: features,
        isDynamic: true, // 标记为动态功能墙
        dataFormat: 'enhanced' // 标记为增强格式
      };
    }

    // 使用动态功能定义（从getDynamicFeatures获取）
    console.log('[FeatureChecker] 使用动态功能墙（基于功能掩码）');
    
    // 检查features数组中是否包含对应功能 - 直接检查，避免循环调用
    const hasFeatureInArray = (featureName) => {
      // 名称标准化与多变体匹配，避免大小写/连字符差异
      const norm = (s) => (s || '').toString().toLowerCase().replace(/[-_]/g, '');
      if (licenseInfo.features && Array.isArray(licenseInfo.features)) {
        const set = new Set(licenseInfo.features.map(norm));
        const variations = [featureName, ...(this.getFeatureNameVariations ? this.getFeatureNameVariations(featureName) : [])];
        return variations.some(v => set.has(norm(v)));
      }
      // 后备方案：位掩码判断（动态定义返回的bit位为准）
      const bitCandidate = featureMap[featureName] || featureMap[featureName.toUpperCase()] || featureMap[featureName.toLowerCase()];
      if (bitCandidate && licenseInfo.featuresMask !== undefined) {
        return (licenseInfo.featuresMask & bitCandidate) !== 0;
      }
      return false;
    };
    
    // 动态生成功能列表
    const dynamicFeatures = {};
    
    // 获取活跃功能列表（从配置或环境变量）
    const inactiveFeatures = await this.db.setting.get('inactive_features') || [];
    
    // 🔧 修复：直接基于许可证中的features数组生成功能墙
    console.log('[FeatureChecker] 准备生成动态功能墙，licenseInfo.features:', licenseInfo.features);
    console.log('[FeatureChecker] featureMap:', featureMap);
    
    if (licenseInfo.features && Array.isArray(licenseInfo.features) && licenseInfo.features.length > 0) {
      console.log('[FeatureChecker] 进入features数组分支，功能数量:', licenseInfo.features.length);
      // 从许可证的features数组构建功能列表
      for (const featureName of licenseInfo.features) {
        // 如果功能在不活跃列表中，跳过
        if (showOnlyActive && inactiveFeatures.includes(featureName)) {
          continue;
        }
        
        const key = featureName.toLowerCase();  // 保留下划线
        
        // 使用标准映射，不依赖可能为空的featureMap
        const standardNames = {
          'BASIC_MONITORING': '基础监控',
          'WEBSSH': 'WebSSH终端',
          'AUTO_DISCOVERY': '自动发现',
          'ADVANCED_ANALYTICS': '高级分析',
          'API_ACCESS': 'API访问',
          'CUSTOM_ALERTS': '自定义告警',
          'AI_ANALYTICS': 'AI分析',
          'NETWORK_QUALITY': '网络质量监控'
        };
        
        const displayName = standardNames[featureName] || featureName;
        const description = `${displayName}功能`;
        const icon = 'extension';
        
        dynamicFeatures[key] = {
          name: displayName,
          description: description,
          available: true, // 如果在许可证features数组中，就是可用的
          icon: icon,
          category: 'general',
          requiredPlan: '当前套餐',
          featureName: featureName
        };
      }
    } else {
      // 后备方案：从featureMap构建（保持原有逻辑作为后备）
      for (const [featureName, featureBit] of Object.entries(featureMap)) {
        // 如果功能在不活跃列表中，跳过
        if (showOnlyActive && inactiveFeatures.includes(featureName)) {
          continue;
        }
        
        const key = featureName.toLowerCase();  // 保留下划线
        const featureDetail = details.find(d => d.name === featureName);
        const displayName = featureNames[featureBit] || featureDetail?.displayName || featureName;
        const description = featureDetail?.description || `${displayName}功能`;
        const icon = featureDetail?.icon || 'extension';
        
        dynamicFeatures[key] = {
          name: displayName,
          description: description,
          available: hasFeatureInArray(featureName),
          icon: icon,
          category: featureDetail?.category || 'general',
          requiredPlan: hasFeatureInArray(featureName) ? '当前套餐' : '更高级套餐',
          featureName: featureName
        };
      }
    }

    // 兜底保障：如果动态构建结果为空，提供基础功能可见（仅基础监控可用）
    if (Object.keys(dynamicFeatures).length === 0) {
      const standardNames = {
        'BASIC_MONITORING': '基础监控',
        'WEBSSH': 'WebSSH终端',
        'AUTO_DISCOVERY': '自动发现',
        'ADVANCED_ANALYTICS': '高级分析',
        'API_ACCESS': 'API访问',
        'CUSTOM_ALERTS': '自定义告警',
        'AI_ANALYTICS': 'AI分析',
        'NETWORK_QUALITY': '网络质量监控'
      };
      for (const [fname, dname] of Object.entries(standardNames)) {
        const key = fname.toLowerCase();  // 保留下划线
        dynamicFeatures[key] = {
          name: dname,
          description: `${dname}功能`,
          available: fname === 'BASIC_MONITORING',
          icon: 'extension',
          category: 'general',
          requiredPlan: fname === 'BASIC_MONITORING' ? '当前套餐' : '更高级套餐',
          featureName: fname
        };
      }
    }

    return {
      currentPlan: {
        name: licenseInfo.planName || licenseInfo.planDisplayName || licenseInfo.planType || '',
        type: licenseInfo.planType || 'free',
        features: availableFeaturesData.display || availableFeaturesData,
        featureNames: availableFeaturesData.names || [], // 新增：功能名称数组
        maxNodes: licenseInfo.maxNodes || 10
      },
      features: dynamicFeatures,
      isDynamic: false,
      dataFormat: 'enhanced' // 标记为增强格式
    };
  }

  /**
   * 检测许可证信息的数据格式
   * @param {Object} licenseInfo - 许可证信息
   * @returns {string} 数据格式类型
   */
  detectDataFormat(licenseInfo) {
    if (!licenseInfo) return 'none';

    // 检查是否有新格式的features数组
    if (licenseInfo.features && Array.isArray(licenseInfo.features)) {
      // 检查是否还有featureDetails（最新格式）
      if (licenseInfo.featureDetails && Array.isArray(licenseInfo.featureDetails)) {
        return 'enhanced'; // 增强格式（包含详细信息）
      }
      return 'array'; // 数组格式
    }

    // 检查是否只有位掩码
    if (licenseInfo.featuresMask !== undefined) {
      return 'mask'; // 位掩码格式
    }

    // 检查是否只有旧格式的features
    if (licenseInfo.featureList && Array.isArray(licenseInfo.featureList)) {
      return 'legacy'; // 旧格式
    }

    return 'unknown';
  }

  /**
   * 自动升级许可证信息格式
   * @param {Object} licenseInfo - 原始许可证信息
   * @returns {Object} 升级后的许可证信息
   */
  upgradeDataFormat(licenseInfo) {
    if (!licenseInfo) return null;

    const format = this.detectDataFormat(licenseInfo);
    const upgraded = { ...licenseInfo };

    switch (format) {
      case 'mask':
        // 从位掩码生成features数组
        upgraded.features = this.convertMaskToFeatures(licenseInfo.featuresMask);
        upgraded.featureList = this.convertFeaturesToDisplayNames(upgraded.features);
        console.log('[FeatureChecker] 已从位掩码格式升级到数组格式');
        break;

      case 'legacy':
        // 从旧格式转换到新格式
        const { features: featureMap } = this.getDynamicFeatures();
        upgraded.features = [];
        upgraded.featuresMask = 0;

        licenseInfo.featureList.forEach(displayName => {
          for (const [name, bit] of Object.entries(featureMap)) {
            const { names } = this.getDynamicFeatures();
            if (names[bit] === displayName) {
              upgraded.features.push(name);
              upgraded.featuresMask |= bit;
              break;
            }
          }
        });

        console.log('[FeatureChecker] 已从旧格式升级到数组格式');
        break;

      case 'array':
        // 确保有featuresMask
        if (!upgraded.featuresMask) {
          upgraded.featuresMask = this.convertFeaturesToMask(licenseInfo.features);
        }
        // 确保有featureList
        if (!upgraded.featureList) {
          upgraded.featureList = this.convertFeaturesToDisplayNames(licenseInfo.features);
        }
        break;

      case 'enhanced':
        // 已经是最新格式，无需升级
        break;

      default:
        console.warn('[FeatureChecker] 未知的数据格式，保留原始数据');
        // 保留原始数据中的关键字段，避免破坏性覆盖
        return {
          ...licenseInfo,  // 保留所有原始字段
          planType: licenseInfo.planType || 'unknown',
          features: licenseInfo.features || ['BASIC_MONITORING'],
          featuresMask: licenseInfo.featuresMask || 1,
          featureList: licenseInfo.featureList || ['基础监控']
        };
    }

    return upgraded;
  }

  /**
   * 验证数据格式一致性
   * @param {Object} licenseInfo - 许可证信息
   * @returns {boolean} 是否一致
   */
  validateDataConsistency(licenseInfo) {
    if (!licenseInfo) return false;

    try {
      // 检查features数组和featuresMask是否一致
      if (licenseInfo.features && licenseInfo.featuresMask !== undefined) {
        const calculatedMask = this.convertFeaturesToMask(licenseInfo.features);
        if (calculatedMask !== licenseInfo.featuresMask) {
          console.warn('[FeatureChecker] 数据不一致: features数组与featuresMask不匹配');
          return false;
        }
      }

      // 检查features数组和featureList是否一致
      // 仅在存在可靠映射源（featureDetails或动态定义）时进行严格对比
      if (licenseInfo.features && licenseInfo.featureList) {
        let hasReliableMapping = false;
        if (Array.isArray(licenseInfo.featureDetails) && licenseInfo.featureDetails.length > 0) {
          hasReliableMapping = true;
        } else if (this.dynamicFeatures && this.dynamicFeatures.details && this.dynamicFeatures.details.length > 0) {
          hasReliableMapping = true;
        }

        if (hasReliableMapping) {
          const calculatedDisplayNames = this.convertFeaturesToDisplayNames(licenseInfo.features);
          if (JSON.stringify(calculatedDisplayNames.slice().sort()) !== JSON.stringify(licenseInfo.featureList.slice().sort())) {
          console.info('[FeatureChecker] 数据不一致: features数组与featureList不匹配');
          return false;
        }
        } else {
          // 无可靠映射源，不做严格告警，视为一致，待后续由动态映射回写
          if (process.env.NODE_ENV === 'development' || this.cachedSettings.get('debug_license')) {
            console.log('[FeatureChecker] 暂无可靠映射源，跳过featureList一致性严格校验');
          }
        }
      }

      return true;
    } catch (error) {
      console.error('[FeatureChecker] 验证数据一致性时出错:', error);
      return false;
    }
  }

  // 注意：getMinimalFeatures() 已在前文定义（包含完整标准映射），避免重复定义导致覆盖
  
  /**
   * 初始化异步配置
   * 在模块初始化时调用
   */
  async initializeAsync() {
    try {
      // 加载保存的配置
      const savedConfig = await this.db.setting.get('featureChecker_config');
      if (savedConfig) {
        this.config = { ...this.config, ...savedConfig };
      }
      
      // 初始化许可证缓存
      await this.updateLicenseCache();
    } catch (error) {
      console.error('[FeatureChecker] 初始化失败:', error);
    }
  }
  
  /**
   * 更新许可证信息缓存
   * 当license_info更新时调用
   */
  async updateLicenseCache() {
    try {
      const licenseInfo = await this.db.setting.get('license_info');
      this.cachedLicenseInfo = licenseInfo;
      console.log('[FeatureChecker] 许可证缓存已更新');
    } catch (error) {
      console.error('[FeatureChecker] 更新许可证缓存失败:', error);
    }
  }
}

module.exports = FeatureChecker;
