'use strict';

/**
 * 授权绑定管理器
 * 负责与License Server的授权绑定API交互
 */

const fetch = require('node-fetch');

class LicenseBindingManager {
  constructor(config, db) {
    this.config = config;
    this.db = db;
  }

  /**
   * 通用的fetch包装器，添加超时和错误处理
   */
  async fetchWithTimeout(url, options = {}) {
    const timeout = options.timeout || this.config.apiTimeout;
    
    const fetchPromise = fetch(url, options);
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error('请求超时')), timeout)
    );
    
    try {
      const response = await Promise.race([fetchPromise, timeoutPromise]);
      return response;
    } catch (error) {
      if (error.message === '请求超时') {
        throw new Error(`请求超时 (${timeout}ms)`);
      }
      throw error;
    }
  }

  /**
   * 检查License绑定状态
   */
  async checkLicenseStatus(licenseKey, instanceId) {
    try {
      console.log('[BindingManager] 检查License状态:', licenseKey);

      // 🔧 修复：使用正常工作的 /api/license/status 端点
      const response = await this.fetchWithTimeout(`${this.config.serverUrl}/api/license/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          licenseKey,
          instanceId
        })
      });

      const data = await response.json();

      // 适配 /api/license/status 的响应格式
      if (response.ok && data.success) {
        console.log('[BindingManager] License状态查询成功:', data.data);
        return {
          success: true,
          data: data.data,
          binding: data.data.binding || null,
          conflicts: [] // status端点不返回冲突信息
        };
      } else {
        const errorMsg = data.message || data.error || 'License状态查询失败';
        console.warn('[BindingManager] License状态查询失败:', errorMsg);
        return {
          success: false,
          error: errorMsg
        };
      }
    } catch (error) {
      console.error('[BindingManager] License状态查询异常:', error);
      return {
        success: false,
        error: `网络错误: ${error.message}`
      };
    }
  }

  /**
   * 激活License (绑定到当前实例)
   */
  async activateLicense(licenseKey, instanceId, forceTransfer = false, isRetry = false) {
    try {
      console.log('[BindingManager] 激活License:', licenseKey, '实例ID:', instanceId, '强制转移:', forceTransfer);

      // 🔧 最小化修复：由于activate端点无法处理已绑定许可证，统一使用force-transfer
      // 这避免了服务器端activate API的设计缺陷
      console.log('[BindingManager] 跳过有问题的activate端点，统一使用force-transfer确保成功');
      forceTransfer = true;

      // 执行绑定或转移
      const endpoint = '/api/license/force-transfer'; // 统一使用force-transfer端点
      
      // 统一使用force-transfer请求格式
      const requestBody = {
        licenseKey,
        newInstanceId: instanceId,
        timestamp: Date.now()
      };
      
      const response = await this.fetchWithTimeout(`${this.config.serverUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();

      if (response.ok && data.success) {
        console.log('[BindingManager] License激活成功:', data.data);
        
        // 保存绑定信息到本地
        this.saveBindingInfo(licenseKey, instanceId, data.data);
        
        // 如果服务端返回了 instanceSecret，保存它
        if (data.data && data.data.instanceSecret) {
          this.db.setting.set('serverInstanceSecret', data.data.instanceSecret);
          console.log('[BindingManager] 保存服务端实例密钥');
        }
        
        return {
          success: true,
          message: 'License激活成功', // 统一消息，实际使用force-transfer实现
          data: data.data
        };
      } else {
        console.warn('[BindingManager] License激活失败:', data.message);
        
        // 由于统一使用force-transfer，不再需要重试逻辑
        
        return {
          success: false,
          error: data.message || 'License激活失败'
        };
      }
    } catch (error) {
      console.error('[BindingManager] License激活异常:', error);
      return {
        success: false,
        error: `网络错误: ${error.message}`
      };
    }
  }

  /**
   * 解绑License
   */
  async unbindLicense(licenseKey, instanceId) {
    try {
      console.log('[BindingManager] 解绑License:', licenseKey, '实例ID:', instanceId);

      const response = await this.fetchWithTimeout(`${this.config.serverUrl}/api/license/unbind`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          licenseKey,
          instanceId
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        console.log('[BindingManager] License解绑成功');
        
        // 清除本地绑定信息
        this.clearBindingInfo(licenseKey);
        
        return {
          success: true,
          message: 'License解绑成功'
        };
      } else {
        console.warn('[BindingManager] License解绑失败:', data.message);
        return {
          success: false,
          error: data.message || 'License解绑失败'
        };
      }
    } catch (error) {
      console.error('[BindingManager] License解绑异常:', error);
      return {
        success: false,
        error: `网络错误: ${error.message}`
      };
    }
  }

  /**
   * 获取绑定历史
   */
  async getBindingHistory(licenseKey) {
    try {
      console.log('[BindingManager] 获取绑定历史:', licenseKey);

      const response = await this.fetchWithTimeout(`${this.config.serverUrl}/api/license/binding-history/${licenseKey}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (response.ok && data.success) {
        console.log('[BindingManager] 绑定历史获取成功');
        return {
          success: true,
          data: data.data || []
        };
      } else {
        console.warn('[BindingManager] 绑定历史获取失败:', data.message);
        return {
          success: false,
          error: data.message || '绑定历史获取失败'
        };
      }
    } catch (error) {
      console.error('[BindingManager] 绑定历史获取异常:', error);
      return {
        success: false,
        error: `网络错误: ${error.message}`
      };
    }
  }

  /**
   * 保存绑定信息到本地
   */
  saveBindingInfo(licenseKey, instanceId, bindingData) {
    const bindingInfo = {
      licenseKey,
      instanceId,
      bindingTime: Date.now(),
      serverData: bindingData
    };

    this.db.setting.set('licenseBindingInfo', bindingInfo);
    console.log('[BindingManager] 绑定信息已保存到本地');
  }

  /**
   * 清除本地绑定信息
   */
  clearBindingInfo(licenseKey) {
    const currentBinding = this.db.setting.get('licenseBindingInfo');
    if (currentBinding && currentBinding.licenseKey === licenseKey) {
      this.db.setting.set('licenseBindingInfo', null);
      console.log('[BindingManager] 本地绑定信息已清除');
    }
  }

  /**
   * 获取本地绑定信息
   */
  getLocalBindingInfo() {
    return this.db.setting.get('licenseBindingInfo') || null;
  }
}

module.exports = LicenseBindingManager;
