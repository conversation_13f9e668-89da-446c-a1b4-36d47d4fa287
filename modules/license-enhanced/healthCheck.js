'use strict';

/**
 * License Enhanced 模块健康检查
 * 用于诊断和验证模块配置
 */

class LicenseHealthCheck {
  constructor(licenseEnhanced) {
    this.licenseEnhanced = licenseEnhanced;
  }

  /**
   * 执行完整的健康检查
   */
  async performHealthCheck() {
    const results = {
      timestamp: new Date().toISOString(),
      checks: []
    };

    // 1. 检查模块配置
    results.checks.push(await this.checkModuleConfig());

    // 2. 检查数据库连接
    results.checks.push(await this.checkDatabase());

    // 3. 检查License Server连接
    results.checks.push(await this.checkLicenseServer());

    // 4. 检查实例ID
    results.checks.push(await this.checkInstanceId());

    // 5. 检查缓存系统
    results.checks.push(await this.checkCacheSystem());

    // 计算总体健康状态
    results.overallHealth = this.calculateOverallHealth(results.checks);

    return results;
  }

  /**
   * 检查模块配置
   */
  async checkModuleConfig() {
    try {
      const config = this.licenseEnhanced.config;
      const checks = [];

      // 检查必要配置项
      checks.push({
        name: '增强模式状态',
        passed: typeof config.enabled === 'boolean',
        value: config.enabled,
        message: config.enabled ? '增强模式已启用' : '增强模式已禁用'
      });

      checks.push({
        name: 'License Server URL',
        passed: !!config.serverUrl,
        value: config.serverUrl,
        message: config.serverUrl ? 'Server URL已配置' : 'Server URL未配置'
      });

      checks.push({
        name: 'API超时设置',
        passed: config.apiTimeout > 0,
        value: `${config.apiTimeout}ms`,
        message: config.apiTimeout > 0 ? '超时设置正常' : '超时设置异常'
      });

      checks.push({
        name: '缓存有效期',
        passed: config.cacheValidityHours > 0,
        value: `${config.cacheValidityHours}小时`,
        message: config.cacheValidityHours > 0 ? '缓存设置正常' : '缓存设置异常'
      });

      return {
        component: '模块配置',
        status: checks.every(c => c.passed) ? 'healthy' : 'warning',
        details: checks
      };
    } catch (error) {
      return {
        component: '模块配置',
        status: 'error',
        error: error.message
      };
    }
  }

  /**
   * 检查数据库连接
   */
  async checkDatabase() {
    try {
      const db = this.licenseEnhanced.db;
      const checks = [];

      // 测试读取设置
      const testKey = '_healthcheck_test_' + Date.now();
      db.setting.set(testKey, 'test');
      const testValue = db.setting.get(testKey);
      db.setting.set(testKey, null); // 清理

      checks.push({
        name: '数据库读写',
        passed: testValue === 'test',
        message: testValue === 'test' ? '数据库读写正常' : '数据库读写异常'
      });

      // 检查实例ID
      const instanceId = db.setting.get('instanceId');
      checks.push({
        name: '实例ID存储',
        passed: !!instanceId,
        value: instanceId ? instanceId.substring(0, 8) + '...' : 'null',
        message: instanceId ? '实例ID已保存' : '实例ID未保存'
      });

      return {
        component: '数据库',
        status: checks.every(c => c.passed) ? 'healthy' : 'warning',
        details: checks
      };
    } catch (error) {
      return {
        component: '数据库',
        status: 'error',
        error: error.message
      };
    }
  }

  /**
   * 检查License Server连接
   */
  async checkLicenseServer() {
    try {
      const serverUrl = this.licenseEnhanced.config.serverUrl;
      
      // 尝试访问健康检查端点
      const fetch = require('node-fetch');
      const response = await fetch(`${serverUrl}/api/health`, {
        method: 'GET',
        timeout: 5000
      }).catch(err => null);

      if (response && response.ok) {
        return {
          component: 'License Server连接',
          status: 'healthy',
          details: [{
            name: 'Server可达性',
            passed: true,
            value: serverUrl,
            message: 'License Server连接正常'
          }]
        };
      } else {
        return {
          component: 'License Server连接',
          status: 'warning',
          details: [{
            name: 'Server可达性',
            passed: false,
            value: serverUrl,
            message: 'License Server无法连接'
          }]
        };
      }
    } catch (error) {
      return {
        component: 'License Server连接',
        status: 'error',
        error: error.message
      };
    }
  }

  /**
   * 检查实例ID
   */
  async checkInstanceId() {
    try {
      const instanceId = this.licenseEnhanced.getInstanceId();
      const checks = [];

      checks.push({
        name: '实例ID格式',
        passed: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(instanceId),
        value: instanceId,
        message: '实例ID格式正确'
      });

      return {
        component: '实例标识',
        status: checks.every(c => c.passed) ? 'healthy' : 'warning',
        details: checks
      };
    } catch (error) {
      return {
        component: '实例标识',
        status: 'error',
        error: error.message
      };
    }
  }

  /**
   * 检查缓存系统
   */
  async checkCacheSystem() {
    try {
      const currentLicense = await this.licenseEnhanced.getCurrentLicenseStatus();
      const checks = [];

      if (currentLicense.licenseKey) {
        const cacheStatus = this.licenseEnhanced.statusChecker.getCacheStatus(currentLicense.licenseKey);
        
        checks.push({
          name: '缓存存在性',
          passed: cacheStatus.exists,
          message: cacheStatus.exists ? '缓存数据存在' : '无缓存数据'
        });

        if (cacheStatus.exists) {
          checks.push({
            name: '缓存有效性',
            passed: cacheStatus.valid,
            value: cacheStatus.remainingTime > 0 ? 
              `剩余${Math.round(cacheStatus.remainingTime / (1000 * 60 * 60))}小时` : 
              '已过期',
            message: cacheStatus.valid ? '缓存有效' : '缓存已过期'
          });
        }
      } else {
        checks.push({
          name: '缓存状态',
          passed: true,
          message: '无激活的License，缓存检查跳过'
        });
      }

      return {
        component: '缓存系统',
        status: checks.every(c => c.passed) ? 'healthy' : 'warning',
        details: checks
      };
    } catch (error) {
      return {
        component: '缓存系统',
        status: 'error',
        error: error.message
      };
    }
  }

  /**
   * 计算总体健康状态
   */
  calculateOverallHealth(checks) {
    const statuses = checks.map(c => c.status);
    
    if (statuses.includes('error')) {
      return 'error';
    } else if (statuses.includes('warning')) {
      return 'warning';
    } else {
      return 'healthy';
    }
  }

  /**
   * 生成健康报告
   */
  generateReport(healthCheckResult) {
    const { timestamp, checks, overallHealth } = healthCheckResult;
    
    let report = `License Enhanced 模块健康检查报告\n`;
    report += `================================\n`;
    report += `检查时间: ${timestamp}\n`;
    report += `总体状态: ${this.getHealthStatusText(overallHealth)}\n\n`;

    for (const check of checks) {
      report += `【${check.component}】${this.getHealthStatusText(check.status)}\n`;
      
      if (check.error) {
        report += `  错误: ${check.error}\n`;
      } else if (check.details) {
        for (const detail of check.details) {
          const status = detail.passed ? '✓' : '✗';
          report += `  ${status} ${detail.name}: ${detail.message}`;
          if (detail.value) {
            report += ` (${detail.value})`;
          }
          report += '\n';
        }
      }
      report += '\n';
    }

    return report;
  }

  /**
   * 获取健康状态文本
   */
  getHealthStatusText(status) {
    const statusMap = {
      'healthy': '健康',
      'warning': '警告',
      'error': '错误'
    };
    return statusMap[status] || status;
  }
}

module.exports = LicenseHealthCheck;