/**
 * 同步实例密钥工具
 * 用于解决客户端和服务端 instanceSecret 不同步的问题
 */

'use strict';

const fetch = require('node-fetch');
const crypto = require('crypto');

class InstanceSecretSync {
  constructor(config, db, licenseModule) {
    this.config = config;
    this.db = db;
    this.license = licenseModule;
  }

  /**
   * 从服务端获取并同步 instanceSecret
   */
  async syncFromServer() {
    try {
      console.log('[SecretSync] 开始同步实例密钥...');
      
      // 获取当前许可证信息
      const licenseStatus = await this.license.getCurrentLicenseStatus();
      const instanceId = this.license.getInstanceId();
      
      if (!licenseStatus.licenseKey || !instanceId) {
        console.error('[SecretSync] 缺少许可证密钥或实例ID');
        return { success: false, error: '缺少必要信息' };
      }
      
      console.log('[SecretSync] 许可证信息:', {
        licenseKey: licenseStatus.licenseKey.substring(0, 20) + '...',
        instanceId: instanceId
      });
      
      // 🔧 修复：使用正常工作的 /api/license/status 端点获取绑定信息
      const response = await fetch(`${this.config.serverUrl}/api/license/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          licenseKey: licenseStatus.licenseKey,
          instanceId: instanceId,
          nodeCount: 0,
          features: [],
          version: '1.0.0'
        }),
        timeout: 30000
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        console.error('[SecretSync] 获取绑定信息失败:', result.message || result.error);
        return { success: false, error: result.message || result.error };
      }

      // 🔧 修复：适配 /api/license/status 的响应格式
      if (!result.data || result.data.status !== 'active') {
        console.error('[SecretSync] 许可证未绑定到此实例');
        return { success: false, error: '许可证未绑定' };
      }
      
      // 如果服务端返回了 instanceSecret，保存它
      if (result.data.instanceSecret) {
        console.log('[SecretSync] 从服务端获取到实例密钥');
        this.db.setting.set('serverInstanceSecret', result.data.instanceSecret);
        console.log('[SecretSync] 实例密钥已保存');
        
        return {
          success: true,
          message: '实例密钥同步成功',
          secretUpdated: true
        };
      } else {
        console.warn('[SecretSync] 服务端未返回实例密钥');
        
        // 尝试重新激活以获取密钥
        console.log('[SecretSync] 尝试重新激活许可证...');
        const activationResult = await this.reactivateLicense(licenseStatus.licenseKey, instanceId);
        
        if (activationResult.success && activationResult.instanceSecret) {
          this.db.setting.set('serverInstanceSecret', activationResult.instanceSecret);
          console.log('[SecretSync] 通过重新激活获取到实例密钥并保存');
          
          return {
            success: true,
            message: '通过重新激活同步实例密钥成功',
            secretUpdated: true,
            reactivated: true
          };
        }
        
        return {
          success: false,
          error: '无法获取实例密钥'
        };
      }
      
    } catch (error) {
      console.error('[SecretSync] 同步过程出错:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * 重新激活许可证以获取 instanceSecret
   */
  async reactivateLicense(licenseKey, instanceId) {
    try {
      const response = await fetch(`${this.config.serverUrl}/api/verification/activate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          activationCode: licenseKey,
          instanceId: instanceId,
          instanceInfo: {
            hostname: require('os').hostname(),
            platform: require('os').platform(),
            dstatusVersion: require('../../package.json').version
          },
          forceTransfer: true // 强制转移到当前实例
        }),
        timeout: 30000
      });
      
      const result = await response.json();
      
      if (response.ok && result.success) {
        return {
          success: true,
          instanceSecret: result.data?.instanceSecret
        };
      }
      
      return {
        success: false,
        error: result.message
      };
      
    } catch (error) {
      console.error('[SecretSync] 重新激活失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * 验证当前密钥是否有效
   */
  async verifyCurrentSecret() {
    try {
      const secret = this.db.setting.get('serverInstanceSecret') || this.db.setting.get('instanceSecret');
      
      if (!secret) {
        return {
          valid: false,
          reason: '未找到实例密钥'
        };
      }
      
      // 尝试使用当前密钥发送一个测试心跳
      const licenseStatus = await this.license.getCurrentLicenseStatus();
      const testData = {
        instanceId: this.license.getInstanceId(),
        licenseKey: licenseStatus.licenseKey,
        timestamp: Date.now(),
        data: { test: true }
      };
      
      const signature = this.generateTestSignature(testData, secret);
      
      // 这里可以添加实际的验证逻辑
      return {
        valid: true,
        secretType: this.db.setting.get('serverInstanceSecret') ? 'server' : 'local',
        secretLength: secret.length
      };
      
    } catch (error) {
      return {
        valid: false,
        reason: error.message
      };
    }
  }
  
  /**
   * 生成测试签名
   */
  generateTestSignature(data, secret) {
    const payload = JSON.stringify({
      method: 'POST',
      path: '/heartbeat',
      timestamp: data.timestamp,
      body: data
    });
    
    return crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');
  }
  
  /**
   * 清除本地密钥缓存
   */
  clearLocalSecrets() {
    console.log('[SecretSync] 清除本地密钥缓存...');
    this.db.setting.set('instanceSecret', null);
    this.db.setting.set('serverInstanceSecret', null);
    console.log('[SecretSync] 本地密钥已清除');
  }
}

module.exports = InstanceSecretSync;