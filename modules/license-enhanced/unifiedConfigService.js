/**
 * 统一配置服务
 * 提供一个中心化的配置获取服务，避免硬编码套餐值
 * 
 * 设计原则：
 * 1. 所有套餐配置必须从License Server动态获取
 * 2. 仅在完全无法连接时使用最小化的紧急fallback
 * 3. fallback值必须与数据库默认值保持一致
 */

const fetch = require('node-fetch');

class UnifiedConfigService {
    constructor() {
        this.serverUrl = process.env.LICENSE_SERVER_URL || 'https://dstatus_api.vps.mom';
        this.cache = null;
        this.cacheExpiry = 0;
        this.cacheTimeout = 60 * 60 * 1000; // 1小时缓存
        this.apiTimeout = 5000; // 5秒超时
        
        // 紧急fallback配置 - 仅在完全无法连接License Server时使用
        // 这些值必须与数据库中的默认值保持一致
        this.emergencyFallback = {
            plans: {
                'Free Plan': {
                    id: 1,
                    name: 'Free Plan',
                    displayName: '免费版',  
                    maxNodes: 5,
                    featuresMask: 0,
                    features: [],
                    price: 0
                }
            },
            features: {
                BASIC_MONITORING: { bitmask: 1, name: '基础监控' },
                WEBSSH: { bitmask: 2, name: 'WebSSH' },
                AUTO_DISCOVERY: { bitmask: 4, name: '自动发现' },
                ADVANCED_ANALYTICS: { bitmask: 8, name: '高级分析' },
                API_ACCESS: { bitmask: 16, name: 'API访问' },
                CUSTOM_ALERTS: { bitmask: 32, name: '自定义告警' }
            }
        };
    }

    /**
     * 获取所有套餐配置
     * @param {boolean} forceRefresh - 是否强制刷新缓存
     * @returns {Promise<Object>} 套餐配置对象
     */
    async getPlans(forceRefresh = false) {
        // 检查缓存
        if (!forceRefresh && this.cache && Date.now() < this.cacheExpiry) {
            return this.cache.plans;
        }

        try {
            // 从License Server获取配置（使用admin端点获取完整信息包括limits）
            const response = await this.fetchWithTimeout(`${this.serverUrl}/api/admin/plans`);
            
            if (response.ok) {
                const data = await response.json();
                
                if (data.success && data.data) {
                    // 转换为易用格式
                    const plans = {};
                    data.data.forEach(plan => {
                        plans[plan.name] = {
                            id: plan.id,
                            name: plan.name,
                            displayName: plan.displayName,
                            maxNodes: plan.maxNodes,
                            featuresMask: plan.featuresMask,
                            features: this.parseFeatures(plan.featuresMask),
                            price: plan.price,
                            description: plan.description,
                            limits: plan.limits || null
                        };
                    });
                    
                    // 更新缓存
                    this.cache = { plans, features: await this.getFeatures(true) };
                    this.cacheExpiry = Date.now() + this.cacheTimeout;
                    
                    console.log('[UnifiedConfig] 套餐配置已更新:', Object.keys(plans));
                    return plans;
                }
            }
        } catch (error) {
            console.error('[UnifiedConfig] 获取套餐配置失败:', error.message);
        }

        // 使用紧急fallback
        console.warn('[UnifiedConfig] 使用紧急fallback配置');
        return this.emergencyFallback.plans;
    }

    /**
     * 获取单个套餐配置
     * @param {string} planName - 套餐名称
     * @returns {Promise<Object|null>} 套餐配置
     */
    async getPlan(planName) {
        const plans = await this.getPlans();
        
        // 尝试多种匹配方式
        const plan = plans[planName] || 
               plans[planName.toLowerCase()] || 
               plans[planName.toUpperCase()] ||
               Object.values(plans).find(p => 
                   p.name.toLowerCase() === planName.toLowerCase() ||
                   p.displayName === planName ||  // 精确匹配中文显示名称
                   p.displayName.toLowerCase() === planName.toLowerCase()
               ) || null;
               
        return plan;
    }

    /**
     * 获取功能定义
     * @param {boolean} forceRefresh - 是否强制刷新
     * @returns {Promise<Object>} 功能定义
     */
    async getFeatures(forceRefresh = false) {
        if (!forceRefresh && this.cache && this.cache.features) {
            return this.cache.features;
        }

        try {
            const response = await this.fetchWithTimeout(`${this.serverUrl}/api/admin/features?active=true`);
            
            if (response.ok) {
                const data = await response.json();
                
                if (data.success && Array.isArray(data.data)) {
                    const features = {};
                    data.data.forEach(feature => {
                        features[feature.name] = {
                            bitmask: feature.bitmask,
                            name: feature.displayName || feature.name,
                            description: feature.description
                        };
                    });
                    return features;
                }
            }
        } catch (error) {
            console.error('[UnifiedConfig] 获取功能定义失败:', error.message);
        }

        return this.emergencyFallback.features;
    }

    /**
     * 解析功能掩码
     * @param {number} featuresMask - 功能掩码
     * @returns {Array<string>} 功能列表
     */
    async parseFeatures(featuresMask) {
        const features = await this.getFeatures();
        const result = [];
        
        for (const [key, feature] of Object.entries(features)) {
            if (featuresMask & feature.bitmask) {
                result.push(feature.name);
            }
        }
        
        return result.length > 0 ? result : ['基础监控'];
    }

    /**
     * 获取套餐的限制配置
     * @param {string} planName - 套餐名称
     * @returns {Promise<Object|null>} 套餐限制配置
     */
    async getPlanLimits(planName) {
        const plan = await this.getPlan(planName);
        return plan ? plan.limits : null;
    }

    /**
     * 获取特定功能的限制配置
     * @param {string} planName - 套餐名称
     * @param {string} featureName - 功能名称
     * @returns {Promise<Object|null>} 功能限制配置
     */
    async getFeatureLimit(planName, featureName) {
        const limits = await this.getPlanLimits(planName);
        if (!limits || typeof limits !== 'object') {
            return null;
        }
        return limits[featureName] || null;
    }

    /**
     * 检查功能是否有时间范围限制
     * @param {string} planName - 套餐名称
     * @param {string} featureName - 功能名称
     * @returns {Promise<number|null>} 时间范围限制(秒)，null表示无限制
     */
    async getFeatureTimeLimit(planName, featureName) {
        const featureLimit = await this.getFeatureLimit(planName, featureName);
        if (!featureLimit || typeof featureLimit !== 'object') {
            return null;
        }
        return featureLimit.timeRange || null;
    }

    /**
     * 获取升级建议
     * @param {string} currentPlan - 当前套餐
     * @returns {Promise<string>} 升级建议
     */
    async getUpgradeSuggestion(currentPlan) {
        const plans = await this.getPlans();
        
        // 按价格排序
        const sortedPlans = Object.values(plans).sort((a, b) => (a.price || 0) - (b.price || 0));
        const current = await this.getPlan(currentPlan);
        
        if (!current) {
            return '无法获取当前套餐信息';
        }
        
        // 找到下一个更高级的套餐
        const nextPlan = sortedPlans.find(p => (p.price || 0) > (current.price || 0));
        
        if (!nextPlan) {
            return '您已是最高级别套餐用户';
        }
        
        return `升级至${nextPlan.displayName}可支持最多${nextPlan.maxNodes}个节点`;
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache = null;
        this.cacheExpiry = 0;
        console.log('[UnifiedConfig] 缓存已清除');
    }

    /**
     * 带超时的fetch请求
     * @private
     */
    async fetchWithTimeout(url, options = {}) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.apiTimeout);
        
        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error(`请求超时 (${this.apiTimeout}ms)`);
            }
            throw error;
        }
    }

    /**
     * 主动刷新配置（管理员修改后调用）
     * @returns {Promise<boolean>} 是否刷新成功
     */
    async refreshConfig() {
        try {
            await this.getPlans(true);
            await this.getFeatures(true);
            console.log('[UnifiedConfig] 配置已主动刷新');
            return true;
        } catch (error) {
            console.error('[UnifiedConfig] 刷新配置失败:', error);
            return false;
        }
    }
}

// 导出单例
module.exports = new UnifiedConfigService();