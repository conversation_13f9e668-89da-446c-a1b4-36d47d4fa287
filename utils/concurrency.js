/**
 * 并发控制工具模块
 * 用于限制同时执行的异步操作数量，避免资源耗尽
 */

/**
 * 创建一个并发限制执行器
 * @param {number} concurrency - 最大并发数
 * @returns {Function} 并发控制函数
 */
function createConcurrencyLimiter(concurrency = 20) {
    let running = 0;
    const queue = [];
    
    /**
     * 执行任务队列中的下一个任务
     */
    const runNext = () => {
        // 当 concurrency = 0 时表示无限制，当 concurrency > 0 时才进行限制
        if ((concurrency > 0 && running >= concurrency) || queue.length === 0) {
            return;
        }
        
        running++;
        const { task, resolve, reject } = queue.shift();
        
        task()
            .then(resolve)
            .catch(reject)
            .finally(() => {
                running--;
                runNext();
            });
    };
    
    /**
     * 添加任务到队列并执行
     * @param {Function} task - 返回Promise的异步任务函数
     * @returns {Promise} 任务执行结果
     */
    return function limitConcurrency(task) {
        return new Promise((resolve, reject) => {
            queue.push({ task, resolve, reject });
            runNext();
        });
    };
}

/**
 * 批量执行任务，限制并发数
 * @param {Array<Function>} tasks - 任务函数数组
 * @param {number} concurrency - 最大并发数
 * @returns {Promise<Array>} 所有任务的执行结果
 */
async function batchExecute(tasks, concurrency = 20) {
    const limiter = createConcurrencyLimiter(concurrency);
    return Promise.all(tasks.map(task => limiter(task)));
}

/**
 * 将数组分批处理
 * @param {Array} array - 需要处理的数组
 * @param {Function} processor - 处理函数 (item) => Promise
 * @param {Object} options - 配置选项
 * @param {number} options.concurrency - 并发数限制
 * @param {number} options.batchSize - 每批处理的数量
 * @param {number} options.batchDelay - 批次之间的延迟(ms)
 * @returns {Promise<Array>} 处理结果
 */
async function processBatches(array, processor, options = {}) {
    const {
        concurrency = 20,
        batchSize = 10,
        batchDelay = 0
    } = options;
    
    const results = [];
    const limiter = createConcurrencyLimiter(concurrency);
    
    // 将数组分批
    for (let i = 0; i < array.length; i += batchSize) {
        const batch = array.slice(i, i + batchSize);
        
        // 处理当前批次
        const batchResults = await Promise.all(
            batch.map(item => limiter(() => processor(item)))
        );
        
        results.push(...batchResults);
        
        // 批次间延迟
        if (batchDelay > 0 && i + batchSize < array.length) {
            await new Promise(resolve => setTimeout(resolve, batchDelay));
        }
    }
    
    return results;
}

/**
 * 监控并发执行情况
 * @param {Array<Function>} tasks - 任务函数数组
 * @param {number} concurrency - 最大并发数
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise<Array>} 所有任务的执行结果
 */
async function executeWithProgress(tasks, concurrency = 20, onProgress = null) {
    const limiter = createConcurrencyLimiter(concurrency);
    let completed = 0;
    const total = tasks.length;
    
    const wrappedTasks = tasks.map((task, index) => 
        limiter(async () => {
            try {
                const result = await task();
                completed++;
                if (onProgress) {
                    onProgress({
                        completed,
                        total,
                        percentage: Math.round((completed / total) * 100),
                        index
                    });
                }
                return result;
            } catch (error) {
                completed++;
                if (onProgress) {
                    onProgress({
                        completed,
                        total,
                        percentage: Math.round((completed / total) * 100),
                        index,
                        error
                    });
                }
                throw error;
            }
        })
    );
    
    return Promise.all(wrappedTasks);
}

module.exports = {
    createConcurrencyLimiter,
    batchExecute,
    processBatches,
    executeWithProgress
};