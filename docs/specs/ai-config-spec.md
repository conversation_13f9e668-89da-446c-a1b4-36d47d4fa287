# AI 配置功能规范文档

## 概览
在 Admin/Analytics 页面添加 AI 配置功能，支持 OpenAI 兼容接口的 AI 服务集成，包括配置管理、热更新和连接测试。

## 技术架构

### 数据存储
- **位置**: `setting` 表的 `ai` 字段（JSON 对象）
- **结构**: 
  ```json
  {
    "enabled": boolean,
    "name": string,
    "apiBase": string,
    "apiKey": string,
    "model": string
  }
  ```

### 优先级规则
```
环境变量 (OPENAI_API_KEY, OPENAI_BASE_URL, OPENAI_MODEL) 
  > 数据库配置 (setting.ai) 
    > 默认值 (apiBase: https://api.openai.com/v1)
```

### 兼容性
- 支持 GEMINI_API_KEY 环境变量映射到 OpenAI 兼容格式（弃用警告）

## 模块结构

### 后端组件
1. **配置读取工具** (`modules/utils/ai-config.js`)
   - 导出: `async getAiConfig(db)`
   - 功能: 合并环境变量与数据库配置
   - 特性: 热更新（每次调用实时读取）

2. **Admin API** (`modules/admin/index.js`)
   - `GET /api/admin/ai/config`: 获取配置（密钥脱敏）
   - `POST /api/admin/ai/config`: 更新配置
   - `GET /api/admin/ai/test`: 测试连接

### 前端组件
- **位置**: `/admin/analytics` 页面
- **UI**: 内嵌配置面板（非跳转）
- **功能**: 配置编辑、保存、测试连接

## API 契约

### GET /api/admin/ai/config
**响应**:
```json
{
  "enabled": boolean,
  "name": string,
  "apiBase": string,
  "model": string,
  "hasKey": boolean,
  "apiKey": "***MASKED***",
  "envLocks": {
    "apiKey": boolean,
    "apiBase": boolean,
    "model": boolean
  }
}
```

### POST /api/admin/ai/config
**请求体**:
```json
{
  "enabled": boolean,
  "name": string,
  "apiBase": string,
  "apiKey": string,  // 空值不覆盖
  "model": string
}
```

### GET /api/admin/ai/test
**响应**:
```json
{
  "success": boolean,
  "message": string,
  "details": any
}
```

## 实现要求

### 必须满足
1. **热更新**: 配置变更立即生效，无需重启
2. **密钥安全**: API 不回显明文密钥
3. **环境优先**: 环境变量存在时锁定对应字段
4. **兼容双库**: 支持 SQLite/PostgreSQL 双适配器

### 代码规范
- CommonJS 模块
- 2 空格缩进
- 保留分号
- 文件名 kebab-case
- 配置字段 camelCase
- 环境变量 UPPER_SNAKE_CASE

## 验收标准

### 功能验收
- [ ] 配置保存后立即生效
- [ ] 密钥字段不回显明文
- [ ] 环境变量优先级正确
- [ ] 测试连接功能正常
- [ ] 界面不刷新即可操作

### 技术验收
- [ ] 无 Schema 变更
- [ ] 支持双数据库模式
- [ ] 代码符合项目规范
- [ ] 错误处理完善

## 测试用例

### 基础流程
1. 打开 Analytics 页面
2. 点击"AI 设置"按钮
3. 输入配置信息
4. 点击"测试连接"验证
5. 保存配置
6. 刷新页面验证持久化

### 边界情况
- 空密钥不覆盖旧值
- 环境变量锁定字段
- 无效 API 地址处理
- 网络超时处理

## 风险评估
- **低风险**: 仅新增功能，不改现有逻辑
- **数据安全**: 密钥加密存储，API 脱敏
- **兼容性**: 完全向后兼容

## 后续扩展
- 支持多模型切换
- 添加请求日志
- 集成更多 AI 服务
- 添加使用统计