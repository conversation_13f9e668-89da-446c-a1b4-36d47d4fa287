# 标签同步问题修复规范

## 问题描述
快速编辑器添加/删除标签后，数据已经保存到数据库（通过 `db.servers.upd_data` 更新），但编辑页面打开时显示的是更新后的标签。点击保存按钮才真正生效。

## 现状分析

### 数据流程
1. **快速编辑器** -> `POST /api/servers/:sid/tags` -> `db.servers.upd_data(sid, serverData)` -> 更新 `data` 字段
2. **编辑页面** -> `GET /admin/servers/:sid` -> `db.servers.get(sid)` -> 渲染页面

### 核心代码位置
- 快速编辑 API: `/modules/servers/index.js:739-798` (POST /api/servers/:sid/tags)
- 数据库更新: `/database/servers.js:66-72` (upd_data 方法)
- 编辑页面路由: `/modules/servers/index.js:626-648` (GET /admin/servers/:sid)

## 问题根因
1. `upd_data` 方法只更新了 `data` 字段（JSON 格式的配置数据）
2. 标签存储在 `data.tags` 中，是服务器配置的一部分
3. 快速编辑的标签更新实际已经保存到数据库
4. 问题可能是：
   - 缓存问题（但经检查没有缓存机制）
   - 数据结构问题（标签保存的位置不正确）
   - 或者快速编辑 API 本身有问题

## 验证步骤
1. 检查快速编辑后数据库中的实际数据
2. 确认编辑页面读取的是最新数据
3. 查看是否有其他字段影响标签显示

## 解决方案
待深入调查后确定

## 测试要求
1. 快速编辑添加标签 -> 刷新页面 -> 标签应该显示
2. 快速编辑删除标签 -> 刷新页面 -> 标签应该消失
3. 编辑页面不需要点击保存就应该看到最新标签