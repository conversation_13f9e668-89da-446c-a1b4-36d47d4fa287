# 实时排序系统重构规格文档

## 1. 问题诊断

### 1.1 当前架构问题
目前系统存在**两套独立且不同步的实时排序系统**：

#### SortManager系统 (`SortManager.js`)
- 内部状态：`state.realtimeSortEnabled`
- 检查机制：只有 `applyCurrentSort()` 检查开关
- 职责定位：专门的排序管理器，单一职责

#### TabMenuSystem系统 (`tab-menu.js`)
- 全局状态：`window.realtimeSortEnabled`
- 独立定时器：每12秒触发一次排序
- 职责混乱：标签菜单系统不应管理排序逻辑

### 1.2 核心问题
1. **绕过机制**：tab-menu.js 直接调用 `window.applySort()`，绕过了 SortManager 的开关检查
2. **状态不同步**：两个系统各自维护 realtimeSortEnabled，互不通信
3. **重复实现**：两套定时器机制，造成性能浪费和逻辑混乱
4. **职责不清**：TabMenuSystem 越权管理排序逻辑

### 1.3 影响分析
- 关闭实时排序开关**部分有效**
- 能阻止数据更新触发的排序（stats.js 通过 applyCurrentSort）
- 无法阻止 tab-menu 的定时器和用户点击触发的排序

## 2. 重构目标

### 2.1 架构目标
- **单一真相源**：只保留 SortManager 作为排序系统
- **职责分离**：TabMenuSystem 只负责 UI 交互
- **状态统一**：一个开关控制所有排序行为

### 2.2 性能目标
- 移除重复的定时器
- 减少不必要的 DOM 操作
- 保持现有的防抖和动画优化

## 3. 重构方案

### 3.1 修改 SortManager.js
```javascript
// 在 applySort 函数开头添加实时排序检查
function applySort(type, direction) {
    // 新增：检查实时排序开关（用户点击时也生效）
    if (!state.realtimeSortEnabled && !forceSort) {
        log('实时排序已禁用，跳过排序');
        return;
    }
    // 原有逻辑...
}

// 添加强制排序方法（用于用户主动点击）
function forceApplySort(type, direction) {
    const originalState = state.realtimeSortEnabled;
    state.realtimeSortEnabled = true;
    applySort(type, direction);
    state.realtimeSortEnabled = originalState;
}
```

### 3.2 修改 stats.js
```javascript
// 暴露 applySort 到全局
window.applySort = function(type, direction, isUserAction = false) {
    if (!window.SortManager) {
        console.error('SortManager 未初始化');
        return;
    }
    
    if (isUserAction) {
        // 用户主动操作，强制排序
        window.SortManager.forceApplySort(type, direction);
    } else {
        // 自动排序，受开关控制
        window.SortManager.applySort(type, direction);
    }
};

// 同步到 SortManager
window.applyCurrentSort = function() {
    if (!window.SortManager) {
        console.error('SortManager 未初始化');
        return;
    }
    window.SortManager.applyCurrentSort();
};
```

### 3.3 修改 tab-menu.js
```javascript
// 移除所有排序相关的定时器和状态管理
const SortMenu = {
    // 移除：_realtimeSortTimer
    // 移除：_realtimeSortInterval
    // 移除：_startRealtimeSortTimer()
    // 移除：_stopRealtimeSortTimer()
    
    _initRealtimeSortToggle() {
        const realtimeSortToggle = document.getElementById('realtime-sort');
        if (!realtimeSortToggle) return;

        // 恢复保存的设置
        const savedRealtimeSort = Utils.storage.get(this.REALTIME_SORT_KEY, true);
        realtimeSortToggle.checked = savedRealtimeSort;

        // 同步到 SortManager
        if (window.SortManager) {
            window.SortManager.setRealtimeSort(savedRealtimeSort);
        }

        // 监听开关变化
        realtimeSortToggle.addEventListener('change', (e) => {
            const enabled = e.target.checked;
            Utils.storage.save(this.REALTIME_SORT_KEY, enabled);
            
            // 同步到 SortManager
            if (window.SortManager) {
                window.SortManager.setRealtimeSort(enabled);
            }
            
            // 显示提示
            if (typeof notice === 'function') {
                notice(`实时排序已${enabled ? '启用' : '禁用'}`, 'success');
            }
        });
    },
    
    _handleSortOptionClick(item) {
        // ... 原有逻辑 ...
        
        // 修改：用户点击触发，标记为用户操作
        if (typeof window.applySort === 'function') {
            window.applySort(type, direction, true); // 第三个参数表示用户操作
        }
    }
}
```

### 3.4 修改 StatsInitializer.js
```javascript
async function initSortManager() {
    // ... 原有逻辑 ...
    
    if (window.SortManager) {
        // 从 localStorage 读取用户偏好
        const savedRealtimeSort = localStorage.getItem('realtimeSortEnabled');
        const realtimeSort = savedRealtimeSort !== null 
            ? JSON.parse(savedRealtimeSort) 
            : true; // 默认启用
        
        window.SortManager.init({
            initialSort: GLOBAL_CONFIG.sort,
            realtimeSort: realtimeSort, // 使用用户保存的设置
            callbacks: {
                // ... 原有回调 ...
            }
        });
    }
}
```

## 4. 实施步骤

### 第一阶段：修改 SortManager（核心）
1. 在 `applySort` 添加开关检查
2. 实现 `forceApplySort` 方法
3. 确保所有排序路径都经过检查

### 第二阶段：修改 stats.js（桥接）
1. 修改 `window.applySort` 支持用户操作参数
2. 确保暴露到全局
3. 添加适当的错误处理

### 第三阶段：清理 tab-menu.js（瘦身）
1. 移除定时器相关代码
2. 移除 `window.realtimeSortEnabled`
3. 修改开关同步逻辑
4. 标记用户点击操作

### 第四阶段：优化初始化（持久化）
1. 修改 StatsInitializer 读取用户偏好
2. 确保初始化顺序正确
3. 测试各种场景

## 5. 测试计划

### 5.1 功能测试
- [ ] 实时排序开关关闭后，WebSocket 更新不触发排序
- [ ] 实时排序开关关闭后，定时器不触发排序
- [ ] 用户点击排序按钮始终有效
- [ ] 开关状态在页面刷新后保持
- [ ] 切换开关时立即生效

### 5.2 兼容性测试
- [ ] 现有排序功能正常工作
- [ ] 动画效果保持流畅
- [ ] 防抖机制正常工作
- [ ] 多维度排序正常切换

### 5.3 性能测试
- [ ] 移除重复定时器后 CPU 占用降低
- [ ] DOM 操作次数减少
- [ ] 内存占用稳定

## 6. 风险评估

### 6.1 低风险
- 修改都是增量性的，不破坏现有功能
- 保留了用户主动操作的能力
- 有清晰的回退路径

### 6.2 需要注意
- 确保加载顺序不影响功能
- 测试各种边界情况
- 保证用户体验一致性

## 7. 预期收益

1. **代码质量提升**
   - 消除重复代码
   - 职责分离更清晰
   - 维护性提高

2. **性能优化**
   - 减少不必要的定时器
   - 降低 CPU 占用
   - 优化内存使用

3. **用户体验改善**
   - 开关真正生效
   - 行为更可预测
   - 保持用户控制权

## 8. 实施时间线

- **第一阶段**：30分钟（核心修改）
- **第二阶段**：15分钟（桥接层）
- **第三阶段**：30分钟（清理代码）
- **第四阶段**：15分钟（优化初始化）
- **测试验证**：30分钟
- **总计**：约2小时

## 9. 回滚方案

如果出现问题，可以：
1. 恢复原始文件（Git）
2. 重新添加 tab-menu 的定时器
3. 恢复双系统架构

## 10. 成功标准

- 实时排序开关完全控制所有自动排序
- 用户点击始终有效
- 性能指标改善
- 代码复杂度降低