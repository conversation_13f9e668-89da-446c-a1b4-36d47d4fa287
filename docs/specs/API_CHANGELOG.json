{"version": "***********.beta", "changes": [{"date": "2025-08-14", "version": "***********.beta", "type": "development", "breaking": false, "changes": [{"category": "development-environment", "type": "enhancement", "description": "修复热重载机制", "details": {"file": "package.json", "change": "dev:raw命令从node改为nodemon", "impact": "开发环境现在支持JS文件热重载", "breaking": false}}, {"category": "development-environment", "type": "addition", "description": "添加独立测试服务器", "details": {"file": "package.json", "change": "新增test:server命令", "command": "NODE_ENV=test PORT=5556 DB_PATH=data/test.db node dstatus.js", "purpose": "提供与开发环境隔离的测试环境", "breaking": false}}, {"category": "development-environment", "type": "optimization", "description": "优化nodemon配置", "details": {"file": "package.json", "changes": ["添加1秒防抖延迟减少频繁重启", "扩展ignore规则避免编辑器临时文件干扰", "启用现代文件监听API"], "added_ignores": [".vscode/", ".idea/", "*.swp", "*.swo", "*~", ".DS_Store", "*.tmp", "*.temp"], "breaking": false}}, {"category": "ui-controls", "type": "enhancement", "description": "扩展玻璃效果统一控制", "details": {"files": ["static/js/glassmorphism-toggle.js", "static/css/components/glassmorphism.css"], "change": "扩展控制范围到非卡片元素", "new_selectors": [".backdrop-blur-sm", ".globe-controls", "footer[class*=\"backdrop\"]", "[id*=\"tooltip\"][class*=\"backdrop\"]"], "css_addition": ".no-glass-effect规则禁用backdrop-filter", "breaking": false}}, {"category": "documentation", "type": "simplification", "description": "精简CLAUDE.md中的npm命令文档", "details": {"file": "CLAUDE.md", "change": "从16个命令精简到8个核心命令", "removed_commands": ["dev:raw", "watch:css", "restart", "kill:services", "test:watch", "test:coverage", "等详细构建命令"], "breaking": false}}], "rollback": {"available": true, "plan_file": "ROLLBACK_PLAN.md", "commands": ["npm run kill:services", "git checkout HEAD -- package.json static/js/glassmorphism-toggle.js static/css/components/glassmorphism.css CLAUDE.md", "rm -f ROLLBACK_PLAN.md data/test.db*", "npm install && npm test"]}, "testing": {"verified": true, "tests_passed": "npm test - 8/8 通过", "manual_verification": ["热重载功能正常（nodemon监听文件变化）", "双服务器独立运行（5555开发，5556测试）", "玻璃效果控制扩展到更多元素"]}}]}