# 配置变更回滚方案

## 变更记录（2025-08-14）

### 1. package.json 变更

**修改项：**
- `"dev:raw"`: 从 `"concurrently \"npm run watch:css\" \"node dstatus.js\""` 改为 `"concurrently \"npm run watch:css\" \"nodemon dstatus.js\""`
- 新增 `"test:server": "NODE_ENV=test PORT=5556 DB_PATH=data/test.db node dstatus.js"`
- nodemonConfig 新增 ignore 规则和 delay 配置

**回滚命令：**
```bash
# 恢复 dev:raw 命令
sed -i '' 's/"dev:raw": "concurrently \\"npm run watch:css\\" \\"nodemon dstatus.js\\""/"dev:raw": "concurrently \\"npm run watch:css\\" \\"node dstatus.js\\""/g' package.json

# 移除 test:server 命令
sed -i '' '/"test:server":/d' package.json

# 恢复 nodemonConfig（需手动编辑删除新增的 ignore 项和 delay、legacyWatch 配置）
```

### 2. glassmorphism-toggle.js 变更

**修改项：**
- 扩展选择器包含 `.backdrop-blur-sm` 等元素
- 新增 `.no-glass-effect` 类控制

**回滚命令：**
```bash
git checkout HEAD -- static/js/glassmorphism-toggle.js
```

### 3. glassmorphism.css 变更

**修改项：**
- 新增 `.no-glass-effect` CSS 规则

**回滚命令：**
```bash
git checkout HEAD -- static/css/components/glassmorphism.css
```

### 4. CLAUDE.md 变更

**修改项：**
- 精简 npm 命令列表
- 更新描述为"CSS/JS热重载"

**回滚命令：**
```bash
git checkout HEAD -- CLAUDE.md
```

## 完整回滚步骤

```bash
# 1. 停止所有运行中的服务
npm run kill:services

# 2. 恢复所有修改的文件
git checkout HEAD -- package.json
git checkout HEAD -- static/js/glassmorphism-toggle.js
git checkout HEAD -- static/css/components/glassmorphism.css
git checkout HEAD -- CLAUDE.md

# 3. 删除创建的文件
rm -f ROLLBACK_PLAN.md
rm -f data/test.db*

# 4. 重新安装依赖（如有必要）
npm install

# 5. 验证回滚
npm test
npm run dev
```

## 验证回滚成功

- [ ] `npm run dev` 使用 node 而非 nodemon
- [ ] 没有 `npm run test:server` 命令
- [ ] nodemonConfig 恢复原始配置
- [ ] 玻璃效果控制恢复原始范围
- [ ] CLAUDE.md 恢复完整命令列表

## 数据影响

- **主数据库**：无影响（data/db.db 未修改）
- **测试数据库**：data/test.db 可安全删除
- **配置数据**：localStorage 中的 glassmorphism_enabled 设置保持不变