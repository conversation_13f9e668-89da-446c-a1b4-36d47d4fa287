# 授权系统精简方案（Spec）

本文档用于定义授权系统的简化目标、范围、数据模型、API、流程与验收标准，作为后续改造与实施的唯一规范基础（Spec）。本方案遵循“License Server 为唯一真相源、客户端只做落盘与读取”的原则，去除本地推断与冗余逻辑，优先保证稳定性与可诊断性。

## 1. 目标与非目标

### 1.1 目标
- 单一真相源：以 License Server 为唯一真相；客户端仅持久化 `license_info`，不做任何本地套餐/功能推断。
- 在线优先：授权状态、功能与限额在线校验后写入 `license_info`；在线失败再读缓存；无缓存进入应急展示（10 节点），不写入推断数据。
- 权限一致：UI、菜单、页面与 API 的功能可用性统一由 `featureChecker`（读取 `license_info`）判定。
- 功能墙正确：`/api/license/feature-wall` 即使无 `featureDetails` 也能基于 `features/featuresMask` 正确生成非空 `features` 列表。
- 限额一致：节点上限、时间范围等限制均读取 `license_info`，与 UI 展示保持一致。
- 防竞争写入：建立 `license_info` 写入来源优先级与基本写入日志，避免高优写入被低优写入覆盖。
- 简单可测：API 语义收敛、边界清晰，保留最小必要的诊断接口与日志，便于快速定位问题。

### 1.2 非目标
- 不在客户端侧实现任何“套餐 → 功能”本地映射或推断。
- 不扩大功能范围；仅保留当前产品真正需要的核心功能位。
- 不改动 License Server 协议（如必须改动，将在“外部依赖”章节注明）。

## 2. 术语
- License Server：授权服务器。
- license_info：客户端本地持久化的授权缓存（权威镜像）。
- featureDetails：服务端返回的功能列表（含 displayName/bit/available 等）。
- features/featuresMask：功能名数组/位图（至少其一）。

## 3. 设计原则
- 只信任服务端：客户端只负责落盘与读取，不补齐、不推断。
- 在线优先、离线保守：能连网就覆盖写入；连不上读缓存；无缓存只展示基础监控与 10 节点应急上限。
- 接口幂等：刷新/状态 API 多次调用结果一致，不产生副作用冲突。
- 可诊断：关键写入带来源标记与简易写入日志。

## 4. 数据模型（license_info）

```json
{
  "licenseKey": "string",        // 服务端返回或激活写入
  "key": "string",               // 兼容字段
  "isValid": true,                // 授权有效标记
  "planType": "pro|enterprise|standard|free|unknown",
  "planName": "string",          // 显示名称（优先服务端）
  "planDisplayName": "string",   // 可选
  "maxNodes": 100,                // 节点上限（仅使用服务端值）
  "features": ["WEBSSH", "AUTO_DISCOVERY", "ADVANCED_ANALYTICS"],
  "featuresMask": 255,            // 二选一或同时具备
  "featureDetails": [             // 可选，存在时可直接驱动 Feature Wall
    { "bit": 2, "name": "WEBSSH", "displayName": "WebSSH", "available": true }
  ],
  "permissions": {},              // 可选
  "lastVerifiedTime": 1710000000000,
  "serverExpirationTime": 1711000000000
}
```

约束：
- 仅使用服务端返回填充上述字段；无值不推断，保持空或 0。
- 兼容字符串存储（读取时自动 JSON.parse）。

## 5. 功能列表（精简版）

只保留当前必要核心功能位（可根据业务再勾选）：
- BASIC_MONITORING（基础监控，默认）
- WEBSSH
- AUTO_DISCOVERY
- ADVANCED_ANALYTICS
- NETWORK_QUALITY（如本期需要）

服务器返回的任何其他功能位一律忽略或按扩展保留，但不在本期参与门控。

## 6. API 规范（客户端）

最小集合：
- GET `/admin/api/license-enhanced/status`
  - 在线优先：若持有 licenseKey → 调用 License Server → 成功则写入 license_info → 返回当前状态；失败则读缓存；无缓存返回应急展示（10 节点）。
- POST `/admin/api/license-enhanced/refresh`
  - 强制在线校验 → 成功覆盖写入 license_info；失败不更改缓存。
- GET `/api/license/feature-wall`
  - 若有 `featureDetails`：直接根据详情构造 features；
  - 无 `featureDetails`：基于 `features/featuresMask` 构造 features；
  - 保证 features 非空（至少有 BASIC_MONITORING）。
- POST `/admin/api/license-enhanced/activate`、`/unbind`
  - 激活/解绑；激活后建议立即 `refresh`。

诊断接口（仅开发/运维可用）：
- GET `/admin/api/license-enhanced/license-info` → 返回 `license_info`、`license_info_meta`、`license_info_write_log`（已打码）。
- POST `/admin/api/license-enhanced/test-refresh` → 触发一次在线刷新并返回“写入前/写入后”的 meta 对比。

## 7. Feature 判定规则（简化）

优先级：
1) `featureDetails` 存在时：根据 `available` 判定。
2) 否则：从 `features`（数组）或 `featuresMask`（位图）判定功能可用。

名称规范：
- 统一使用服务端的功能名（如 `WEBSSH`、`AUTO_DISCOVERY`）。
- 仅做最小程度标准化（大小写、连字符/下划线归一），不再维护大表映射。

## 8. 流程概述

1) 激活：前端提交 key → `/activate` → 成功后调用 `/refresh` → `license_info` 落盘。
2) 启动/状态：前端调用 `/status` → 在线优先写入 → 返回状态。
3) 功能墙：前端调用 `/api/license/feature-wall` → 基于 `license_info` 生成 features（详情优先、否则回退）。
4) 门控：服务端路由/前端菜单统一通过 `featureChecker` 读取 `license_info` 判定。

## 9. 并发与一致性（精简版）

写入来源优先级（仅当字段冲突时生效）：
1) `statusChecker.updateCache`（在线校验）
2) `syncLicenseFromServer`（二次同步）
3) `featureChecker.updateLicenseInfoAsync`（仅补齐 `featureDetails`，不得覆盖 `features/mask/maxNodes`）
4) `setCurrentLicense(clear)`（清空）

实现要点：
- 对于“仅补齐 detail”的路径，只更新 `featureDetails` 字段，禁止整体覆盖。
- 保留 `license_info_meta`（最后写入者/时间）与 `license_info_write_log`（最近 10 条）用于诊断。

## 10. 简化清单（做减法）

- 去除/关闭动态 Feature 定义的周期性拉取与本地缓存（非必须）。
- 移除本地“套餐→功能”的默认映射与任何推断逻辑。
- 缩减功能名称映射表，仅保留最小标准化。
- 合并/下线冗余端点，统一通过上述最小集合。

## 11. 迁移计划（里程碑）

M1 读写闭环稳定
- 校正 `/status`、`/refresh` 的在线优先写入逻辑与错误处理。
- `license_info` 写入仅信任服务端字段；补齐 detail 时只更新该字段。
- 保留诊断接口与写入日志。

M2 Feature Wall 回退
- 当 `featureDetails` 为空或缺失时，回退到 `features/mask` 生成 features，保证非空。
- 前端菜单与页面路由只读 `featureChecker` 的判定。

M3 限额与统计
- 节点上限读取 `license_info.maxNodes`；应急模式固定 10 节点（仅展示，不写盘）。
- 与网络质量等模块的时间范围限制对齐授权。

M4 清理与文档
- 移除无用映射/冗余代码路径；补充 README/管理员操作手册。

## 12. 测试计划（端到端）

- 正常授权：激活→刷新→`/feature-wall` 返回正确 plan 与 features。
- 无 featureDetails：`/feature-wall` 通过 `features/mask` 生成非空 features。
- 离线场景：在线失败→读缓存→返回正确 plan 与 features；无缓存→应急展示（10 节点）。
- 并发覆盖：`refresh` 后立即补齐 detail，不得覆盖 features/mask/maxNodes。
- 路由门控：访问受限功能返回 403，菜单隐藏。

## 13. 验收标准

- `/api/license/feature-wall` 在任何服务端返回形态下均可给出正确、非空的 features。
- 菜单/路由与授权保持一致，误判率为 0。
- `/refresh` 后 `license_info` 的最后写入者为 `statusChecker.updateCache`，features/mask/maxNodes 按服务端覆盖；补齐 detail 不会覆盖它们。
- 离线应急可用、无本地推断写入。

## 14. 外部依赖与开放问题

- 服务端 `/api/license/status` 应包含 `features` 或 `featuresMask`（至少其一）；若缺失，需要服务端补齐或增加二次获取端点。
- 功能位的标准名称需在服务端与客户端统一（建议固定大写+下划线）。

---

如对以上 Spec 没有异议，建议按“迁移计划”从 M1 开始实施，并先补充必要的端到端用例保障改造稳定性。

