# 子任务：修复 Feature Wall API 的权限补充与回退逻辑（含 maxNodes 一致性）

- 状态：提案（等待评审）
- 关联规范：docs/license-auth-simplification-spec.md（M2：Feature Wall 回退）
- 负责人：待定
- 预估工期：0.5–1 天

## 背景与问题

当前 `/api/license/feature-wall` 使用 `featureChecker.getFeatureWallInfo()` 生成特性清单。当 `license_info.featureDetails` 存在但为空数组时，API 仍走“详情分支”，导致 `features` 返回空对象；同时 `currentPlan` 正确（例如“专业版/100 节点”），但 UI 无法展示功能入口。

这与精简 Spec 的目标不符：当缺少 `featureDetails` 时，应回退到 `features/featuresMask` 推导，保证 `features` 永不为空。

## 目标

- 修复 `getFeatureWallInfo`：当 `featureDetails` 缺失或为空数组（length === 0）时，回退到 `features/featuresMask` 生成 `features` 列表。
- 确保从缓存（license_info）读取的字段（features 或 featuresMask）被正确识别，名称规范化后生成稳定的 `features` 键与 `available` 标记。
- 返回结构稳定：`features` 必不为空（至少包含 BASIC_MONITORING），`currentPlan` 与服务器授权一致。
- 节点上限一致：`currentPlan.maxNodes` 必须与 License Server 返回的 maxNodes 一致（例如 专业版 应为 200），不得被旧默认值/本地推断（如 100）覆盖。

## 非目标

- 不引入本地“套餐→功能”推断映射；仅使用 `license_info` 与服务端动态功能定义（如有）。
- 不修改 License Server 协议；若服务器未返回 `features` 与 `featuresMask`，则保持保守（只基础监控）。

## 数据源与兼容性

- 读取：`db.setting.get('license_info')`
  - 关键字段：`featureDetails`（可选）、`features`（数组，标准功能名）、`featuresMask`（位图）、`planName/planType/maxNodes`。
  - 兼容字符串存储（需要 JSON.parse）。
- 动态功能定义（可选）：`featureChecker.getDynamicFeatures()`，用于提供 displayName/icon/category 与 bit 映射；若不可用，退回最小集合显示名。

节点上限（maxNodes）来源：
- 仅信任 License Server `/api/license/status` 的 `data.maxNodes`；
- `statusChecker.updateCache` 必须原样写入到 `license_info.maxNodes`；
- `getFeatureWallInfo` 的 `currentPlan.maxNodes` 必须读取 `license_info.maxNodes`，不得使用旧的本地默认映射（如 pro=100）。

## 当前 API 行为简述（待修复点）

- 路由：`modules/license-enhanced/routes.js` → GET `/api/license/feature-wall` → `featureChecker.getFeatureWallInfo()`
- 辅助：`modules/license-enhanced/featureChecker.js#getFeatureWallInfo`
  - 分支1（详情）：当 `licenseInfo.featureDetails && Array.isArray()` 时，使用详情生成 features；若数组为空，当前实现会返回空 features（问题点）。
  - 分支2（回退）：无详情时，从动态定义与 `features/featuresMask` 推导 `available`，返回非空 features（期望保留/强化）。

## 方案与实现要点

1) 分支条件修复
- 将“详情分支”的进入条件改为：`licenseInfo.featureDetails && Array.isArray(...) && licenseInfo.featureDetails.length > 0`。
- 否则，进入回退分支（基于 `features/featuresMask`）。

2) 回退分支生成逻辑
- 规范化功能名（大小写、`-`/`_` 去除）作为 `features` 对象的 key。
- `available` 计算：
  - 若 `license_info.features` 存在：判断规范化集合是否包含当前功能名或变体；
  - 否则若 `featuresMask` 存在：根据动态定义的 bit 映射判断；
  - 二者皆无：仅返回 BASIC_MONITORING 可用，其余标记为 false 或不返回（至少保证非空）。
- 显示信息：优先动态定义 `details` 的 displayName/description/icon，缺失时回退为“功能名 + 默认描述”。

3) 返回结构保持不变
- `currentPlan`、`features`、`isDynamic`（回退分支可标记为 `false`）、`dataFormat`（`enhanced`）。
- 保证 `features` 非空。

4) maxNodes 一致性修复（新增）
- 验证 `/api/license/status` 实际返回的 `data.maxNodes`（应为 200，例）；
- 确认 `statusChecker.updateCache` 将 `responseData.maxNodes` 写入 `license_info.maxNodes`；
- `getFeatureWallInfo` 必须使用 `license_info.maxNodes` 设置 `currentPlan.maxNodes`；
- 消除旧代码路径中的本地默认值（如 `pro=100`）对 `feature-wall` 的影响（不改动节点限额逻辑，除非存在展示不一致）。

## 输入/输出定义（不变）

- 输入：无（从本地缓存与动态定义读取）。
- 输出：JSON
```json
{
  "currentPlan": { "name": "string", "type": "string", "features": ["显示名..."], "featureNames": ["标准名..."], "maxNodes": 100 },
  "features": {
    "webssh": { "name": "WebSSH", "description": "...", "available": true, "icon": "...", "category": "...", "requiredPlan": "当前套餐|更高级套餐", "featureName": "WEBSSH" },
    "autodiscovery": { ... }
  },
  "isDynamic": false,
  "dataFormat": "enhanced"
}
```

## 验收标准

- 情况A（仅 features 数组）：返回 `features` 非空；`available` 与 `features` 一致。
- 情况B（仅 featuresMask 位图）：返回 `features` 非空；`available` 与位图一致。
- 情况C（featureDetails=[] 空数组）：返回 `features` 非空；`isDynamic=false`；`available` 取自 A/B。
- 情况D（有完整 featureDetails）：返回 `features` 非空；`isDynamic=true`；`available` 按 `featureDetails.available`。
- 名称兼容：`AUTO_DISCOVERY/auto-discovery/auto_discovery/autodiscovery` 等变体均能被识别。
- 保证 `currentPlan` 与 `license_info` 一致；`maxNodes` 不变。
- maxNodes 一致性：
  - 当 License Server 返回 200 时：`license_info.maxNodes=200`，`/api/license/feature-wall.currentPlan.maxNodes=200`；
  - 刷新后不应因任何“本地默认”回落到 100；
  - 写入日志（`license_info_meta`）显示最近一次写入来自 `statusChecker.updateCache`。

## 测试用例（建议）

- 单元：
  - `getFeatureWallInfo` 输入构造四种场景（A/B/C/D），断言输出结构与 `available`。
  - 规范化名称识别测试（`AUTO_DISCOVERY` 多变体）。
- 集成：
  - 写入不同形态的 `license_info` 后，调用 `/api/license/feature-wall` 断言响应。
  - 模拟服务端 `/api/license/status` 返回 `maxNodes=200`：
    1) 调用 `/admin/api/license-enhanced/test-refresh`；
    2) 断言 `/admin/api/license-enhanced/license-info` 中 `license_info.maxNodes=200` 且 `meta.lastWriter=statusChecker.updateCache`；
    3) 断言 `/api/license/feature-wall.currentPlan.maxNodes=200`。

## 风险与回退

- 风险低：仅改变分支条件与回退路径，不涉及写库或协议变更。
- 出现异常时可立即回退到旧分支（但建议保留回退逻辑以避免空 features）。

## 实施步骤（可执行）

1) 修改 `modules/license-enhanced/featureChecker.js#getFeatureWallInfo`：
- 条件 `if (licenseInfo.featureDetails && Array.isArray(...))` 改为 `if (Array.isArray(featureDetails) && featureDetails.length > 0)`。
- 否则进入现有“动态定义 + features/mask 回退分支”。

2) 优化回退分支的 `available` 计算：
- 优先使用 `license_info.features`；无则使用 `featuresMask + 动态 bit 映射`。
- 缺省情况下仍保证返回 BASIC_MONITORING。

3) maxNodes 一致性校验：
- 在 `getFeatureWallInfo` 中统一从 `license_info.maxNodes` 读取，并在 `debug` 模式下打印 `plan/type/maxNodes`；
- 对照 License Server 返回值（通过 `/admin/api/license-enhanced/test-refresh` 验证）。

4) 新增/更新测试：
- 针对 A/B/C/D 场景的单测或轻量集成测试。

5) 可选：增加 DEBUG 日志开关，打印“进入详情/回退分支”的判定与关键计数（仅在 `debug` 配置开启时）。

---

审批通过后可按以上步骤实施。实施仅位于 `featureChecker.getFeatureWallInfo`，不涉及其他模块，影响面小、可快速交付。
