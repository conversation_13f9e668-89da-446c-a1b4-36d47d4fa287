# 许可证服务器 Bug 报告

## 问题描述
专业版（pro）套餐的 `maxNodes` 值在不同 API 接口中返回不一致。

## 影响
- **影响用户**：所有专业版用户
- **问题表现**：用户界面显示节点限制为 100，而非正确的 200
- **许可证Key示例**：12E02DCE-9008393C-5B7E44B9-471A5AA4

## API 不一致详情

### 1. 套餐定义接口 `/api/admin/plans`
```json
GET https://dstatus_api.vps.mom/api/admin/plans

返回数据（摘录）：
{
  "id": 2,
  "name": "pro",
  "displayName": "专业版",
  "maxNodes": 200,  // ✅ 正确值
  "featuresMask": 239,
  "features": [
    "BASIC_MONITORING",
    "WEBSSH", 
    "AUTO_DISCOVERY",
    "ADVANCED_ANALYTICS",
    "CUSTOM_ALERTS",
    "AI_ANALYTICS",
    "NETWORK_QUALITY"
  ]
}
```

### 2. 许可证状态接口 `/api/license/status`
```json
POST https://dstatus_api.vps.mom/api/license/status
Body: {
  "licenseKey": "12E02DCE-9008393C-5B7E44B9-471A5AA4",
  "instanceId": "c2dfdea5-67bd-408e-abd6-b5e3a0279b97",
  ...
}

返回数据（摘录）：
{
  "data": {
    "plan": "pro",
    "planDisplayName": "专业版",
    "maxNodes": 100,  // ❌ 错误值，应该是 200
    "featuresMask": 7,  // ❌ 错误值，应该是 239
    "features": [
      "BASIC_MONITORING",
      "WEBSSH",
      "AUTO_DISCOVERY"
    ],  // ❌ 缺少功能
    "_debug": {
      "license": {
        "type": "pro",
        "maxNodes": 100  // ❌ 数据库中存储的也是错误值
      }
    }
  }
}
```

## 问题分析

1. **数据不一致**：
   - 套餐定义：pro = 200 节点，featuresMask = 239（7个功能）
   - 许可证记录：pro = 100 节点，featuresMask = 7（3个功能）

2. **可能原因**：
   - 许可证数据库记录是旧版本数据（未同步更新）
   - 许可证创建时使用了错误的套餐配置
   - 套餐定义更新后，现有许可证未同步更新

3. **影响范围**：
   - 所有 pro 套餐的许可证都可能存在此问题
   - features 和 featuresMask 也不正确，影响功能权限

## 建议修复方案

### 方案1：数据迁移（推荐）
运行数据库更新脚本，将所有 pro 套餐许可证的配置同步到最新：
```sql
UPDATE licenses 
SET maxNodes = 200, 
    featuresMask = 239 
WHERE type = 'pro';
```

### 方案2：API 层面修正
在 `/api/license/status` 接口中，根据套餐类型动态获取正确的配置：
```javascript
// 伪代码
const planConfig = await getPlansConfig(license.type);
response.maxNodes = planConfig.maxNodes;
response.featuresMask = planConfig.featuresMask;
```

### 方案3：提供刷新接口
提供一个管理接口，允许刷新特定许可证的套餐配置：
```
POST /api/admin/license/refresh-plan-config
{
  "licenseKey": "xxx"
}
```

## 测试用例

验证修复是否成功：
1. 调用 `/api/admin/plans` 确认 pro 套餐定义为 200 节点
2. 调用 `/api/license/status` 验证返回 200 节点
3. 确认 featuresMask 为 239，包含全部 7 个功能

## 临时解决方案

在修复前，客户端可以：
1. 使用 `/api/admin/plans` 获取正确的套餐配置
2. 基于 planType 覆盖错误的 maxNodes 值

但这不是长期解决方案，应该在服务端修复数据一致性问题。

---

报告日期：2025-08-23
报告人：DStatus 开发团队