# 日志控制说明

## 当前配置
- 默认模式：显示WARN和ERROR级别
- console.log、console.info 被屏蔽
- console.warn、console.error 显示

## 开启DEBUG模式

### 方法1：环境变量
```bash
LOG_LEVEL=DEBUG npm run dev
```

### 方法2：面板设置
登录管理面板 → 系统设置 → 开启"调试模式"

### 方法3：数据库设置
```sql
UPDATE setting SET value = 'true' WHERE key = 'debug';
```

### 方法4：临时开启（5分钟）
```javascript
// 在代码中调用
logController.enableDebugTemporarily(300000);
```

## 日志级别说明
- DEBUG (0): 显示所有日志
- INFO (1): 显示info、warn、error
- WARN (2): 显示warn、error（当前默认）
- ERROR (3): 仅显示error

## 恢复原状
将 `modules/utils/console-proxy.js` 第148行改回：
```javascript
const logLevel = process.env.LOG_LEVEL || (debugMode ? 'DEBUG' : 'INFO');
```