# AI 服务优化指南

## 问题诊断记录（2025-08-24）

### 问题现象
- 错误信息：`fetch failed` 
- 模型：`gemini-2.5-flash`
- 数据量：33个服务器，提示词达到475,641字符

### 根本原因
1. **数据量过大** - 直接发送完整的监控数据JSON
2. **模型响应慢** - gemini-2.5-flash 处理大数据时响应时间长（14秒+）
3. **可能超时** - 大数据请求可能导致网络超时

### 解决方案

#### 1. 代码优化（已实施）
- 添加 `_createDataSummary()` 函数创建数据摘要
- 只发送前10个服务器的详细信息
- 每个服务器只包含最近3个数据点
- 数据量从 475K 减少到约 6K 字符

#### 2. 模型选择建议
```javascript
// 推荐配置
{
  "model": "gemini-1.5-flash"  // 响应更快，更稳定
  // 不推荐: "gemini-2.5-flash" - 响应慢，处理大数据不稳定
}
```

#### 3. 测试结果
| 模型 | 小数据响应时间 | 大数据响应时间 | 稳定性 |
|-----|-------------|-------------|-------|
| gemini-1.5-flash | 2.4秒 | 稳定 | ✅ 优秀 |
| gemini-2.5-flash | 14秒 | 可能失败 | ⚠️ 一般 |
| gemini-1.5-pro | 7秒 | 稳定 | ✅ 良好 |

## 故障排查步骤

1. **检查API密钥**
   ```bash
   node 工作区/get-ai-config.js
   ```

2. **测试网络连接**
   ```bash
   curl -I https://generativelanguage.googleapis.com
   ```

3. **测试模型可用性**
   - 创建简单测试脚本
   - 发送小数据量请求
   - 验证响应

4. **优化数据量**
   - 使用数据摘要而非完整数据
   - 限制服务器数量
   - 减少历史数据点

## 配置建议

### 推荐配置
```json
{
  "enabled": true,
  "name": "Google Gemini",
  "apiBase": "https://generativelanguage.googleapis.com/v1beta/openai",
  "apiKey": "your-api-key",
  "model": "gemini-1.5-flash"
}
```

### 注意事项
1. 避免使用 `gemini-2.5-flash`（响应慢）
2. 确保数据摘要功能启用
3. 监控请求大小，保持在10K字符以内
4. 考虑添加请求超时配置（30秒）