# 节点离线检测优化记录

## 问题描述
节点离线检测过于敏感，容易因短暂网络波动产生误报。

## 根本原因
1. 失败阈值过低（5次）
2. 轮询间隔1.5秒时，仅7.5秒就判定离线
3. HTTP超时2秒与轮询间隔冲突

## 修改方案（已实施）
**文件**：`modules/stats/index.js`  
**行号**：1244  
**修改内容**：将失败阈值从5次提高到20次

```javascript
// 原值
if(fails[sid] > 5) {

// 新值  
if(fails[sid] > 20) {
```

## 效果评估
- **1.5秒轮询**：30秒后判定离线
- **3秒轮询**（默认）：60秒后判定离线
- **10秒轮询**：200秒后判定离线

## 配套建议
1. 在`/admin/advanced-settings`将轮询间隔设为3秒或以上
2. 在`/admin/notification`保持离线通知延迟30秒

## 实施时间
2025-08-27

## 测试要点
1. 模拟网络抖动10-20秒，确认不会误报
2. 真实断网，确认能在合理时间内检测到
3. 监控系统负载，确认无异常

## 后续优化建议
如仍有问题，可考虑：
1. 降低HTTP超时到1200ms
2. 实施滑动窗口算法替代连续失败计数
3. 添加自适应阈值调整机制