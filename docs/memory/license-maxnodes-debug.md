# 许可证 maxNodes 调试经验

## 问题描述
专业版（pro）套餐显示 maxNodes: 100 而不是预期的 200

## 调试过程

### 1. 数据流分析
```
服务端 /api/license/status → statusChecker.verifyOnline() → updateCache() → license_info → featureChecker → feature-wall API
```

### 2. 关键发现
- 服务端 `/api/license/status` 返回 maxNodes: 100
- 本地 `getDefaultMaxNodes()` 定义 pro: 100
- licenseCache 中存储的也是 100

### 3. 调试日志位置
已在以下位置添加条件调试日志：
- `statusChecker.js:260-267` - 服务端返回数据详情
- `statusChecker.js:394-396` - updateCache写入前原始值
- `statusChecker.js:423-426` - updateCache写入后实际值

### 4. 启用调试模式
```sql
-- 启用许可证调试
INSERT OR REPLACE INTO setting (key, val) VALUES ('debug_license', 'true');
```

### 5. 触发许可证刷新方法
```bash
# 方法1：调用feature-wall（不需要认证）
curl http://localhost:5555/api/license/feature-wall

# 方法2：功能检查API
curl -X POST http://localhost:5555/api/license-enhanced/feature-check \
  -H "Content-Type: application/json" \
  -d '{"feature": "BASIC_MONITORING"}'

# 方法3：通过前端页面访问触发
```

### 6. 查看调试输出
开发环境会在控制台输出：
```
[StatusChecker] 服务端返回数据详情:
  - planType: pro
  - planName: 专业版  
  - maxNodes: 100  // <- 关键：服务端实际返回值
```

## 问题根源（已确认）

**服务端数据不一致**：
- 套餐定义 `/api/admin/plans`：pro = 200节点 ✅
- 许可证API `/api/license/status`：返回100节点 ❌  
- 原因：许可证数据库记录未更新，存储的是旧版本配置

## 解决方案

### 服务端修复（必需）
1. 更新数据库中所有 pro 套餐许可证的 maxNodes 为 200
2. 同步更新 featuresMask 从 7 到 239
3. 详见：`docs/license-server-bug-report.md`

### 客户端临时方案（不推荐）
不应在客户端硬编码修正，等待服务端修复

## 相关文件
- `modules/license-enhanced/statusChecker.js` - 在线验证和缓存
- `modules/license-enhanced/featureChecker.js` - 功能墙数据
- `modules/license-enhanced/unifiedConfigService.js` - 套餐配置
- `modules/license-enhanced/index.js:1342` - 默认节点数定义