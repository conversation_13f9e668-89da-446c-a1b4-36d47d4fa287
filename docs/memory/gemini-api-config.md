# Google Gemini OpenAI 兼容 API 配置指南

## 配置说明

Google Gemini 提供了 OpenAI 兼容的 API 端点，可以在 DStatus 中使用。

## 配置示例

### API 基础地址
```
https://generativelanguage.googleapis.com/v1beta/openai
```

### API 密钥
使用你的 Google AI Studio API 密钥（不是 OAuth）

### 模型
```
gemini-1.5-flash
gemini-1.5-pro
gemini-1.0-pro
```

## 完整配置

1. **在 Analytics 页面点击"AI 设置"**

2. **填写配置：**
   - AI 服务名称：`Google Gemini`
   - API 地址：`https://generativelanguage.googleapis.com/v1beta/openai`
   - 模型：`gemini-1.5-flash` 或其他支持的模型
   - API 密钥：你的 Google AI Studio API 密钥

3. **测试连接**
   - 点击"测试连接"按钮
   - 如果显示"连接成功（Gemini API）"，说明配置正确

## 注意事项

1. **端点差异**
   - Gemini API 不提供 `/models` 端点
   - 主要使用 `/chat/completions` 端点
   - 系统已对此进行了适配

2. **API 密钥获取**
   - 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
   - 创建 API 密钥
   - 复制密钥到配置中

3. **模型选择**
   - `gemini-1.5-flash`：快速，适合一般用途
   - `gemini-1.5-pro`：更强大，适合复杂分析
   - `gemini-1.0-pro`：稳定版本

4. **区域限制**
   - 某些地区可能无法访问 Google AI 服务
   - 可能需要使用代理或 VPN

## 环境变量配置（可选）

也可以通过环境变量配置：

```bash
export OPENAI_API_KEY="your-google-api-key"
export OPENAI_BASE_URL="https://generativelanguage.googleapis.com/v1beta/openai"
export OPENAI_MODEL="gemini-1.5-flash"
```

## 故障排除

### 401 鉴权失败
- 检查 API 密钥是否正确
- 确认 API 密钥已启用

### 404 端点不存在
- 这是正常的，Gemini 不提供 models 端点
- 如果测试显示"连接成功（Gemini API）"，可以忽略

### 429 频率限制
- 降低请求频率
- 检查 API 配额限制

## 验证配置

使用调试工具验证：

```bash
cd 工作区/测试
node debug-api.js "https://generativelanguage.googleapis.com/v1beta/openai" "your-api-key"
```

这将测试各个端点并显示哪些可用。