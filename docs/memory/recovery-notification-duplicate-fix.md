# 恢复通知重复问题修复

## 问题分析
恢复通知重复的根本原因：
1. **多个触发源**：主动模式数据上报、被动检测、定时检查都可能触发恢复通知
2. **状态缓存竞态条件**：多个调用同时检查到`oldStatus === false`
3. **缺乏原子性**：状态更新和通知判断不是原子操作

## 具体触发路径
1. `modules/stats/index.js:1235` - 被动模式检测成功
2. `modules/stats/index.js:1940` - 主动模式定时检查恢复  
3. 主动模式数据上报路由处理
4. 事件系统触发

## 修复方案
在`modules/notification/triggers.js`的`checkServerStatusChange`方法中添加去重逻辑：

```javascript
// 在第83行之前添加去重检查
if (isOnline && oldStatus === true) {
    // 已经是在线状态，跳过通知
    return;
}
```

## 影响评估
- 仅影响重复通知，不影响正常的上线/下线检测
- 保持离线通知的正常逻辑不变