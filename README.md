---

## 📖 使用说明

### 基本配置

#### 首次访问

1. 系统启动后，访问 `http://localhost:5555`
2. 按照引导向导完成基本配置：
   - 设置管理员账户
   - 配置系统基本信息
   - 选择监控模式

#### 管理员登录

- **URL**: `http://localhost:5555/login`
- **默认账户**: `admin` / `admin123` (首次启动时显示)

### 添加服务器

#### 方式一：Web 界面添加

1. 登录管理后台
2. 进入 "服务器管理" → "添加服务器"
3. 填写服务器信息：

```yaml
基本信息:
  名称: 生产服务器-01
  IP地址: *************
  端口: 22
  标签: production,web-server

SSH连接:
  用户名: root
  认证方式: 密码/密钥
  密码: your_password
  
监控配置:
  监控间隔: 5分钟
  告警阈值: CPU>80%, 内存>90%
```

4. 点击 "测试连接" 验证配置
5. 保存并开始监控

#### 方式二：批量导入

创建 `servers.json` 文件：

```json
{
  "servers": [
    {
      "name": "Web Server 01",
      "host": "*************",
      "port": 22,
      "username": "root",
      "password": "your_password",
      "tags": ["production", "web"],
      "location": "Beijing",
      "group": "web-servers"
    },
    {
      "name": "Database Server",
      "host": "*************",
      "port": 22,
      "username": "admin",
      "privateKey": "/path/to/private/key",
      "tags": ["production", "database"],
      "location": "Shanghai",
      "group": "db-servers"
    }
  ]
}
```

批量导入：

```bash
# 使用 API 导入
curl -X POST http://localhost:5555/api/servers/import \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_api_token" \
  -d @servers.json

# 或使用管理界面上传 JSON 文件
```

### 监控面板

#### 主要监控界面

1. **实时概览**: 服务器状态总览、关键指标摘要
2. **性能监控**: CPU、内存、磁盘、网络实时图表
3. **网络质量**: 延迟监控、丢包率、连通性测试
4. **历史数据**: 长期趋势分析、容量规划参考

#### 自定义监控面板

```javascript
// 示例：自定义监控指标
{
  "dashboard": {
    "name": "生产环境监控",
    "widgets": [
      {
        "type": "metric",
        "title": "CPU 使用率",
        "servers": ["server-01", "server-02"],
        "metric": "cpu_usage",
        "timeRange": "1h"
      },
      {
        "type": "chart",
        "title": "网络流量趋势",
        "chartType": "line",
        "metrics": ["network_in", "network_out"],
        "timeRange": "24h"
      }
    ]
  }
}
```

### AI智能分析

#### 启用 AI 功能

1. **获取 API 密钥**: 
   - 访问 [Google AI Studio](https://aistudio.google.com/app/apikey)
   - 创建新的 API 密钥

2. **配置环境变量**:
   ```bash
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

3. **升级许可证**: AI 功能需要专业版或企业版许可证

> 📖 **详细配置指南**: 查看 [Gemini AI 配置指南](./docs/Gemini-AI-配置指南.md) 了解完整的配置步骤、故障排除和安全最佳实践。

#### 使用 AI 分析

1. **智能报告生成**:
   - 进入 "AI 分析" 页面
   - 选择时间范围和服务器
   - 点击 "生成智能报告"

2. **异常检测**:
   ```bash
   # API 调用示例
   curl -X POST http://localhost:5555/api/analytics/ai/report \
     -H "Content-Type: application/json" \
     -d '{
       "timeRange": {
         "start": 1640995200,
         "end": 1641081600
       },
       "includeData": true
     }'
   ```

3. **优化建议**:
   - 自动识别性能瓶颈
   - 提供针对性优化建议
   - 预测资源使用趋势

---

## ⚙️ 配置说明

### 数据库配置

#### SQLite 配置 (默认)

```javascript
// config/database.js
module.exports = {
  type: 'sqlite',
  database: './data/db.db',
  options: {
    // WAL 模式以提升性能
    pragma: {
      journal_mode: 'WAL',
      synchronous: 'NORMAL',
      cache_size: -64000,  // 64MB 缓存
      temp_store: 'MEMORY'
    }
  }
};
```

#### PostgreSQL 配置

```javascript
// config/database.js
module.exports = {
  type: 'postgresql',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'dstatus',
  username: process.env.DB_USER || 'dstatus',
  password: process.env.DB_PASSWORD,
  pool: {
    min: 2,
    max: 20,
    acquire: 30000,
    idle: 10000
  },
  dialectOptions: {
    ssl: process.env.DB_SSL === 'true' ? {
      require: true,
      rejectUnauthorized: false
    } : false
  }
};
```

### 许可证配置

#### 许可证层级说明

| 版本 | 最大节点数 | 核心功能 | 高级功能 | AI 分析 | 技术支持 |
|------|------------|----------|----------|---------|----------|
| **免费版** | 5 | ✅ 基础监控 | ❌ | ❌ | 社区支持 |
| **标准版** | 20 | ✅ 全功能监控 | ✅ WebSSH | ❌ | 邮件支持 |
| **专业版** | 50 | ✅ 全功能监控 | ✅ 高级分析 | ✅ AI 分析 | 优先支持 |
| **企业版** | 无限 | ✅ 全功能监控 | ✅ 全部功能 | ✅ AI 分析 | 专属支持 |

#### 许可证激活

```bash
# 方式一：环境变量
export LICENSE_KEY="your_license_key_here"

# 方式二：配置文件
echo "your_license_key_here" > config/license.key

# 方式三：Web 界面
# 访问 http://localhost:5555/admin/license 输入许可证密钥
```

### 环境变量

#### 核心配置

```bash
# 基本设置
NODE_ENV=production                    # 运行环境 (development/production)
DSTATUS_PORT=5555                     # 服务端口
DSTATUS_HOST=0.0.0.0                  # 绑定地址

# 数据库设置
DB_TYPE=sqlite                        # 数据库类型 (sqlite/postgresql)
DB_PATH=./data/db.db                  # SQLite 数据库文件路径
DB_HOST=localhost                     # PostgreSQL 主机地址
DB_PORT=5432                          # PostgreSQL 端口
DB_USER=dstatus                       # PostgreSQL 用户名
DB_PASSWORD=your_password             # PostgreSQL 密码
DB_NAME=dstatus                       # PostgreSQL 数据库名
DB_SSL=false                          # PostgreSQL 是否启用 SSL

# 许可证设置
LICENSE_SERVER_URL=https://dstatus_api.vps.mom  # 许可证服务器地址
LICENSE_KEY=your_license_key          # 许可证密钥

# AI 功能设置
GEMINI_API_KEY=your_gemini_api_key    # Google Gemini API 密钥

# 安全设置
JWT_SECRET=your_jwt_secret_key        # JWT 签名密钥
ADMIN_PASSWORD=your_admin_password    # 默认管理员密码
SESSION_SECRET=your_session_secret    # 会话密钥

# 通知设置
SMTP_HOST=smtp.gmail.com              # SMTP 服务器
SMTP_PORT=587                         # SMTP 端口
SMTP_USER=<EMAIL>        # SMTP 用户名
SMTP_PASS=your_app_password           # SMTP 密码
WEBHOOK_URL=https://hooks.slack.com   # Webhook 地址
TELEGRAM_BOT_TOKEN=your_bot_token     # Telegram Bot Token
TELEGRAM_CHAT_ID=your_chat_id         # Telegram 聊天 ID

# 性能调优
MAX_MEMORY_RESTART=1G                 # PM2 内存限制重启
UPDATE_INTERVAL=5000                  # WebSocket 更新间隔 (毫秒)
CACHE_TTL=300                         # 缓存过期时间 (秒)
```

#### 高级配置

```bash
# 日志设置
LOG_LEVEL=info                        # 日志级别 (error/warn/info/debug)
LOG_FILE=./logs/dstatus.log          # 日志文件路径
LOG_MAX_SIZE=10MB                     # 日志文件最大大小
LOG_MAX_FILES=5                       # 日志文件保留数量

# 监控设置
MONITOR_INTERVAL=300                  # 监控数据收集间隔 (秒)
DATA_RETENTION_DAYS=90                # 数据保留天数
ALERT_COOLDOWN=300                    # 告警冷却时间 (秒)

# 性能设置
WORKER_THREADS=4                      # 工作线程数
COMPRESSION_LEVEL=6                   # Gzip 压缩级别
RATE_LIMIT_WINDOW=900000             # 请求限制窗口 (毫秒)
RATE_LIMIT_MAX=100                    # 请求限制最大次数