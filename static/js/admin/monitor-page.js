/* Admin Monitor Page wiring: switch all CRUD calls to /api/admin/monitor */
(function() {
  'use strict';

  function qs(sel, root=document){ return root.querySelector(sel); }
  function qsa(sel, root=document){ return Array.from(root.querySelectorAll(sel)); }

  function toast(msg){ try{ console.log('[Admin]', msg); }catch(_){} }

  // Admin 页面兜底：拦截任何仍指向 /api/monitor/* 的写入请求，改写到 /api/admin/monitor/*
  (function installAdminFetchRewrite(){
    try {
      const origFetch = window.fetch;
      window.fetch = async function(input, init){
        try {
          const urlStr = (typeof input === 'string') ? input : (input && input.url) || '';
          const method = ((init && init.method) || 'GET').toUpperCase();
          if (location.pathname.includes('/admin') && method !== 'GET' && urlStr.startsWith('/api/monitor/')) {
            let newUrl = urlStr.replace('/api/monitor/', '/api/admin/monitor/');
            const headers = new Headers((init && init.headers) || {});
            // Token是可选的 - 如果存在则添加
            const token = (window.AdminMonitorAPI && window.AdminMonitorAPI.getToken && window.AdminMonitorAPI.getToken()) || '';
            if (token && !headers.get('X-Admin-Token')) headers.set('X-Admin-Token', token);
            // 确保发送Cookie
            const newInit = Object.assign({ credentials: 'same-origin' }, init, { headers, method });
            console.warn('[Admin] 重写写入请求 ->', newUrl);
            return origFetch(newUrl, newInit);
          }
        } catch(e) { /* 静默失败，回落原始 fetch */ }
        return origFetch(input, init);
      };
    } catch(e) { /* ignore */ }
  })();

  async function loadRegionsIntoFilters() {
    try {
      const resp = await window.AdminMonitorAPI.getRegions();
      const list = (resp && resp.data) || [];
      const regionFilter = qs('#region-filter');
      const regionSelect = qs('#target-region-id');
      if (regionFilter) {
        regionFilter.innerHTML = '<option value="">全部地区</option>' + list.map(r => `<option value="${r.id}">${r.name}</option>`).join('');
      }
      if (regionSelect) {
        regionSelect.innerHTML = '<option value="">请选择地区</option>' + list.map(r => `<option value="${r.id}">${r.name}</option>`).join('');
      }
    } catch(e){ toast('加载地区失败: ' + e.message); }
  }

  function showModal(id){ const el=qs(id); if(el) el.classList.remove('hidden'); }
  function hideModal(id){ const el=qs(id); if(el) el.classList.add('hidden'); }

  function bindRegionModal() {
    const addBtn = qs('#btn-add-region');
    const addBtnMini = qs('#btn-add-region-mini'); // 新增的mini按钮
    const modalId = '#region-modal';
    const form = qs('#region-form');
    const cancel = qs('#region-cancel');

    // 绑定主要添加按钮
    if (addBtn) addBtn.addEventListener('click', () => { qs('#region-id').value=''; qs('#region-name').value=''; qs('#region-description').value=''; showModal(modalId); });

    // 绑定mini添加按钮
    if (addBtnMini) addBtnMini.addEventListener('click', (e) => {
      e.stopPropagation(); // 防止触发折叠面板
      qs('#region-id').value=''; qs('#region-name').value=''; qs('#region-description').value=''; showModal(modalId);
    });

    if (cancel) cancel.addEventListener('click', () => hideModal(modalId));
    if (form) form.addEventListener('submit', async (ev) => {
      ev.preventDefault();
      const id = qs('#region-id').value.trim();
      const payload = { name: qs('#region-name').value.trim(), description: qs('#region-description').value.trim() };
      try {
        if (id) {
          await AdminMonitorAPI.updateRegion(id, payload);
          toast('地区更新成功');
        } else {
          await AdminMonitorAPI.createRegion(payload);
          toast('地区创建成功');
        }
        hideModal(modalId);
        await loadRegionsIntoFilters();
        // 刷新整个页面数据，包括地区卡片
        if (window.loadData) {
          await window.loadData();
        }
      } catch(e){ toast('保存地区失败: ' + e.message); }
    });
    const backdrop = qs('#region-modal-backdrop'); if (backdrop) backdrop.addEventListener('click', () => hideModal(modalId));
  }

  function bindTargetModal() {
    const addBtn = qs('#btn-add-target');
    const modalId = '#target-modal';
    const form = qs('#target-form');
    const backdrop = qs('#target-modal-backdrop');
    const cancelBtn = qs('#target-cancel');
    if (addBtn) addBtn.addEventListener('click', () => {
      qsa('#target-form input, #target-form textarea').forEach(i=>{ if(i.type!=='hidden') i.value=''; });
      state.currentTargetNodes = [];
      showModal(modalId);
    });
    if (backdrop) backdrop.addEventListener('click', () => hideModal(modalId));
    if (cancelBtn) cancelBtn.addEventListener('click', () => hideModal(modalId));
    if (form) form.addEventListener('submit', async (ev) => {
      ev.preventDefault();
      const id = qs('#target-id').value.trim();
      const payload = {
        name: qs('#target-name').value.trim(),
        region_id: qs('#target-region-id').value.trim(),
        host: qs('#target-host').value.trim(),
        port: parseInt(qs('#target-port').value || '0', 10) || 0,
        description: (qs('#target-description')||{}).value || '',
        mode: (qs('#target-mode')||{}).value || 'auto',
        test_type: (qs('input[name="target-test-type"]:checked')||{}).value || 'tcping',
        node_ids: state.currentTargetNodes || []
      };
      try {
        if (id) {
          await AdminMonitorAPI.updateTarget(id, payload);
          toast('目标更新成功');
        } else {
          await AdminMonitorAPI.createTarget(payload);
          toast('目标创建成功');
        }
        hideModal(modalId);
        await reload();
        // 刷新整个页面数据，包括地区卡片
        if (window.loadData) {
          await window.loadData();
        }
      } catch(e){ toast('保存目标失败: ' + e.message); }
    });
  }

  // 事件委托：编辑/删除目标（要求行元素包含 data-id & data-action）
  function bindTableActions() {
    const table = document.getElementById('monitor-targets');
    if (!table) return;
    table.addEventListener('click', async (ev) => {
      const btn = ev.target.closest('[data-action]');
      if (!btn) return;
      const action = btn.getAttribute('data-action');
      const id = btn.getAttribute('data-id');
      if (!id) return;
      if (action === 'delete-target') {
        if (!confirm('确定删除该监控目标？')) return;
        try { await AdminMonitorAPI.deleteTarget(id); toast('已删除'); } catch(e){ toast('删除失败: ' + e.message); }
        return;
      }
      if (action === 'edit-target') {
        try {
          const resp = await AdminMonitorAPI.getTarget(id);
          const t = (resp && resp.data) || {};
          qs('#target-id').value = t.id || '';
          qs('#target-name').value = t.name || '';
          qs('#target-region-id').value = t.region_id || '';
          qs('#target-host').value = t.host || '';
          qs('#target-port').value = t.port || '';
          if (qs('#target-description')) qs('#target-description').value = t.description || '';
          if (qs('#target-mode')) qs('#target-mode').value = t.mode || 'auto';
          const testType = t.test_type || 'tcping';
          const radio = qs(`input[name="target-test-type"][value="${testType}"]`);
          if (radio) radio.checked = true;
          // 解析 node_ids
          try {
            if (typeof t.node_id === 'string') {
              if (t.node_id.startsWith('[')) {
                state.currentTargetNodes = JSON.parse(t.node_id);
              } else {
                state.currentTargetNodes = [t.node_id];
              }
            } else if (Array.isArray(t.node_id)) {
              state.currentTargetNodes = t.node_id;
            } else {
              state.currentTargetNodes = [];
            }
          } catch(_e){
            state.currentTargetNodes = [];
          }
          showModal('#target-modal');
        } catch(e){ toast('加载目标失败: ' + e.message); }
        return;
      }
    });
  }

  document.addEventListener('DOMContentLoaded', async () => {
    await loadRegionsIntoFilters();
    bindRegionModal();
    bindTargetModal();
    bindTableActions();

    // Table rendering + pagination + filters
    const state = {
      all: [],
      filtered: [],
      page: 1,
      pageSize: 20,
      total: 0,
      selected: new Set(),
      currentTargetNodes: []  // 添加节点选择状态
    };

    // 暴露给 monitor-table.js 使用的状态
    window.state = state;

    function getFilters() {
      return {
        q: (document.getElementById('search-input')?.value || '').trim().toLowerCase(),
        region: (document.getElementById('region-filter')?.value || '').trim()
      };
    }

    function normalizeTargets(list) {
      return list.map(t => ({
        id: t.id,
        name: t.name || '-',
        host: t.host || '-',
        port: t.port || 0,
        region: t.region_name || t.region_id || '-',
        node: (() => {
          if (!t.node_id) return '自动';
          try { const v = typeof t.node_id === 'string' ? JSON.parse(t.node_id) : t.node_id; return Array.isArray(v) ? `指定(${v.length})` : '指定(1)'; } catch(_) { return '指定'; }
        })(),
        status: '-',
        description: t.description || ''
      }));
    }

    function applyFilters() {
      const { q, region } = getFilters();
      let rows = state.all;
      if (region) rows = rows.filter(r => (r.region || '').toString() === region);
      if (q) rows = rows.filter(r =>
        r.name.toLowerCase().includes(q) ||
        (r.host||'').toLowerCase().includes(q) ||
        (r.description||'').toLowerCase().includes(q)
      );
      state.filtered = rows;
      state.total = rows.length;
      state.page = 1;
      renderPage();
    }

    function renderPage() {
      const start = (state.page - 1) * state.pageSize;
      const end = Math.min(start + state.pageSize, state.total);
      const slice = state.filtered.slice(start, end);
      const tbody = document.getElementById('monitor-targets-tbody');
      const mobile = document.getElementById('monitor-targets-mobile-cards');
      if (tbody) tbody.innerHTML = slice.map(rowToTr).join('');
      if (mobile) mobile.innerHTML = slice.map(rowToMobileCard).join('');
      // pagination text
      const sid = 'monitor-targets';
      const setText = (id, val) => { const el = document.getElementById(id); if (el) el.textContent = val; };
      setText(`start-index-${sid}`, state.total === 0 ? 0 : (start + 1));
      setText(`end-index-${sid}`, end);
      setText(`total-records-${sid}`, state.total);
      bindRowEvents();
    }

    function rowToTr(r) {
      const checkbox = `<td class=\"px-4 py-3\"><input type=\"checkbox\" class=\"row-select\" data-id=\"${r.id}\"></td>`;
      const name = `<td class=\"px-4 py-3 text-slate-800 dark:text-slate-200\">${escapeHtml(r.name)}</td>`;
      const address = `<td class=\"px-4 py-3 text-slate-600 dark:text-slate-300\">${escapeHtml(r.host)}:${r.port||0}</td>`;
      const region = `<td class=\"px-4 py-3\">${escapeHtml(r.region||'-')}</td>`;
      const node = `<td class=\"px-4 py-3\">${escapeHtml(r.node)}</td>`;
      const status = `<td class=\"px-4 py-3\">${escapeHtml(r.status)}</td>`;
      const actions = `<td class=\"px-4 py-3\">
        <button class=\"inline-flex items-center gap-1 px-2 py-1 text-xs text-blue-600 hover:text-blue-700\" data-action=\"edit-target\" data-id=\"${r.id}\"><i class=\"ti ti-edit\"></i>编辑</button>
        <button class=\"inline-flex items-center gap-1 px-2 py-1 text-xs text-red-600 hover:text-red-700\" data-action=\"delete-target\" data-id=\"${r.id}\"><i class=\"ti ti-trash\"></i>删除</button>
      </td>`;
      return `<tr data-id=\"${r.id}\">${checkbox}${name}${address}${region}${node}${status}${actions}</tr>`;
    }

    function rowToMobileCard(r) {
      return `<div class=\"p-3 rounded-lg border border-slate-200 dark:border-slate-700\">
        <div class=\"flex items-center justify-between\">
          <div class=\"font-semibold\">${escapeHtml(r.name)}</div>
          <div>
            <button class=\"text-xs text-blue-600 mr-2\" data-action=\"edit-target\" data-id=\"${r.id}\">编辑</button>
            <button class=\"text-xs text-red-600\" data-action=\"delete-target\" data-id=\"${r.id}\">删除</button>
          </div>
        </div>
        <div class=\"text-xs mt-1 text-slate-600 dark:text-slate-300\">${escapeHtml(r.host)}:${r.port||0}</div>
        <div class=\"text-xs mt-1\">地区：${escapeHtml(r.region||'-')}｜节点：${escapeHtml(r.node)}｜状态：${escapeHtml(r.status)}</div>
      </div>`;
    }

    function escapeHtml(s){ return (s||'').toString().replace(/[&<>"']/g,m=>({"&":"&amp;","<":"&lt;",">":"&gt;","\"":"&quot;","'":"&#39;"}[m])); }

    function bindRowEvents() {
      // select all
      const selectAll = document.getElementById('select-all-monitor-targets');
      if (selectAll) selectAll.onchange = (e) => {
        state.selected.clear();
        qsa('.row-select').forEach(chk => { chk.checked = e.target.checked; if (chk.checked) state.selected.add(chk.getAttribute('data-id')); });
      };
      // row selects
      qsa('.row-select').forEach(chk => {
        chk.addEventListener('change', (ev) => {
          const id = chk.getAttribute('data-id');
          if (chk.checked) state.selected.add(id); else state.selected.delete(id);
        });
      });
      // pagination buttons
      const prev = document.getElementById('prev-page-monitor-targets');
      const next = document.getElementById('next-page-monitor-targets');
      const prevM = document.getElementById('prev-page-mobile-monitor-targets');
      const nextM = document.getElementById('next-page-mobile-monitor-targets');
      const goPrev = () => { if (state.page > 1){ state.page--; renderPage(); } };
      const goNext = () => { const maxPage = Math.max(1, Math.ceil(state.total/state.pageSize)); if (state.page < maxPage){ state.page++; renderPage(); } };
      if (prev) prev.onclick = goPrev; if (next) next.onclick = goNext; if (prevM) prevM.onclick = goPrev; if (nextM) nextM.onclick = goNext;
      // search/filter
      const search = document.getElementById('search-input');
      const regionFilter = document.getElementById('region-filter');
      if (search) search.oninput = () => applyFilters();
      if (regionFilter) regionFilter.onchange = () => applyFilters();
      // batch delete
      const batchDel = document.getElementById('btn-batch-delete');
      if (batchDel) batchDel.onclick = async () => {
        if (state.selected.size === 0) return;
        if (!confirm(`确定删除选中的 ${state.selected.size} 个目标？`)) return;
        for (const id of Array.from(state.selected)) {
          try { await AdminMonitorAPI.deleteTarget(id); } catch(e){ toast(`删除 ${id} 失败: ` + e.message); }
        }
        await reload();
      };
    }

    async function reload() {
      try {
        const resp = await AdminMonitorAPI.getTargets();
        const targets = (resp && resp.data) || [];
        state.all = normalizeTargets(targets);
        state.selected.clear();
        applyFilters();
      } catch(e){ toast('加载监控目标失败: ' + e.message); }
    }

    // 节点抽屉逻辑

    // 复用 monitor-table.js 的节点选择系统
    const openNodeBtn = qs('#open-node-drawer');
    if (openNodeBtn) openNodeBtn.addEventListener('click', async () => {
      const targetId = qs('#target-id')?.value || 'new-target';
      const targetName = qs('#target-name')?.value || '新建目标';

      // 同步当前选择到全局状态
      if (window.syncTargetNodesToGlobal) {
        window.syncTargetNodesToGlobal(targetId, state.currentTargetNodes || []);
      }

      // 使用统一的节点选择系统
      if (window.openNodeSelection) {
        window.openNodeSelection(targetId, targetName);
      } else {
        toast('节点选择功能未加载，请刷新页面重试');
      }
    });

    // 保存节点选择（由统一系统处理）
    const saveNodeBtn = qs('#save-node-selection');
    if (saveNodeBtn) saveNodeBtn.addEventListener('click', () => {
      // 从统一系统同步当前选择的节点
      const targetId = qs('#target-id')?.value || 'new-target';
      if (window.syncNodesToEditModal) {
        window.syncNodesToEditModal(targetId);
      }
      // 关闭抽屉
      if (window.closeNodeDrawer) {
        window.closeNodeDrawer();
      }
    });

    await reload();
  });

})();
