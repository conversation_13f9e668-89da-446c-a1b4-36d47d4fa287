/**
 * 服务器列表分页管理器 - 优化版本
 * @description 处理服务器列表的分页功能，保持原有拖拽排序功能
 * @created 2025-01-26
 * @updated 2025-01-26 - 使用CSS类，优化代码结构，添加移动端支持
 */

class ServersPagination {
    constructor() {
        this.allServers = [];
        this.currentPage = 1;
        this.pageSize = 50;  // 默认改为50条
        this.totalItems = 0;
        this.totalPages = 1;
        this.sortableInstance = null;
        this.wsConnection = null;  // WebSocket 连接实例
        this.lastKnownServerCount = 0;  // 用于检测服务器数量变化
        
        // 状态映射（添加状态指示灯）
        this.statusMap = {
            0: '<span class="inline-flex items-center gap-1.5 px-2.5 py-1.5 text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 rounded-full"><span class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></span>停用</span>',
            1: '<span class="inline-flex items-center gap-1.5 px-2.5 py-1.5 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full"><span class="w-2 h-2 bg-green-500 rounded-full"></span>公开</span>',
            2: '<span class="inline-flex items-center gap-1.5 px-2.5 py-1.5 text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400 rounded-full"><span class="w-2 h-2 bg-yellow-500 rounded-full"></span>私有</span>'
        };
        
        this.init();
        this.initWebSocket();  // 初始化 WebSocket 连接
        this.initDropdownHandlers();  // 初始化下拉菜单处理
    }

    init() {
        this.loadServersData();
        this.bindEvents();
        
        // 设置初始服务器数量
        this.lastKnownServerCount = this.totalItems;
        
        // 强制设置下拉框的值为50
        const pageSizeSelector = document.getElementById('page-size-selector');
        if (pageSizeSelector) {
            pageSizeSelector.value = '50';
            console.log(`设置下拉框默认值为: 50 条/页`);
        }
        
        // 先加载一次在线状态（作为初始数据）
        this.loadOnlineStatus();
        
        this.renderPage();
    }
    
    initWebSocket() {
        if (typeof ConnectionManager === 'undefined') {
            console.warn('ConnectionManager not found, falling back to polling');
            return;
        }
        
        // 构建 WebSocket URL
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/stats`;
        
        console.log('初始化 WebSocket 连接:', wsUrl);
        
        // 使用正确的 ConnectionManager API
        this.wsConnection = ConnectionManager.init({
            config: {
                reconnectInterval: 3000,
                maxReconnectAttempts: 10,
                healthCheckInterval: 30000,
                debug: false
            },
            callbacks: {
                onMessage: (message, fromCache) => {
                    try {
                        // message 已经是解析后的对象
                        if (message.type === 'stats') {
                            const data = message.data;
                            this.updateServersStatus(data);
                            
                            // 检测服务器总数变化
                            if (message.totalServers !== undefined) {
                                this.checkServerCountChange(message.totalServers);
                            }
                        }
                    } catch (error) {
                        console.error('处理 WebSocket 消息失败:', error);
                    }
                },
                onConnected: () => {
                    console.log('WebSocket 连接已建立');
                },
                onDisconnected: () => {
                    console.log('WebSocket 连接已关闭');
                },
                onError: (error) => {
                    console.error('WebSocket 错误:', error);
                }
            }
        });
    }
    
    updateServersStatus(statusData) {
        if (!statusData || typeof statusData !== 'object') {
            return;
        }
        
        // 创建状态映射，方便快速查找
        const statusMap = new Map();
        
        // 遍历 WebSocket 推送的数据
        Object.entries(statusData).forEach(([sid, serverData]) => {
            const isOnline = serverData && serverData.stat && typeof serverData.stat === 'object' && !serverData.stat.offline;
            statusMap.set(sid, isOnline);
        });
        
        // 更新服务器的在线状态
        let hasChanges = false;
        this.allServers.forEach(server => {
            const newStatus = statusMap.get(server.sid) || false;
            if (server.isOnline !== newStatus) {
                server.isOnline = newStatus;
                hasChanges = true;
            }
        });
        
        // 如果有状态变化，更新UI
        if (hasChanges) {
            this.updateOnlineStatusUI();
        }
    }
    
    // 检测服务器数量变化
    checkServerCountChange(currentCount) {
        if (this.lastKnownServerCount > 0 && currentCount !== this.lastKnownServerCount) {
            console.log(`检测到服务器数量变化: ${this.lastKnownServerCount} -> ${currentCount}`);
            
            // 显示友好提示
            const changeType = currentCount > this.lastKnownServerCount ? '新增' : '移除';
            const changeAmount = Math.abs(currentCount - this.lastKnownServerCount);
            
            // 创建提示消息
            const message = `检测到${changeType}了 ${changeAmount} 个服务器，正在更新列表...`;
            
            // 显示临时提示
            this.showUpdateNotification(message);
            
            // 延迟刷新页面，让用户看到提示
            setTimeout(() => {
                location.reload();
            }, 1500);
        }
        
        // 更新已知数量
        this.lastKnownServerCount = currentCount;
    }
    
    // 显示更新通知
    showUpdateNotification(message) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-blue-600 text-white px-4 py-3 rounded-lg shadow-lg z-50 transition-all duration-300';
        notification.innerHTML = `
            <div class="flex items-center gap-2">
                <div class="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                <span class="text-sm font-medium">${message}</span>
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 添加进入动画
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        }, 100);
        
        // 自动移除
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    updateOnlineStatusUI() {
        // 获取当前页的服务器
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = Math.min(startIndex + this.pageSize, this.totalItems);
        const currentServers = this.allServers.slice(startIndex, endIndex);
        
        // 更新桌面端表格的在线状态指示灯
        currentServers.forEach(server => {
            // 桌面端表格
            const row = document.querySelector(`#servers tr[sid="${server.sid}"]`);
            if (row) {
                const indicator = row.querySelector('td:nth-child(2) .w-2.h-2.rounded-full');
                if (indicator) {
                    indicator.className = `w-2 h-2 rounded-full ${server.isOnline ? 'bg-green-500' : 'bg-red-500'}`;
                    indicator.title = server.isOnline ? '在线' : '离线';
                }
            }
            
            // 移动端卡片
            const card = document.querySelector(`#mobile-servers-list div[sid="${server.sid}"]`);
            if (card) {
                const indicator = card.querySelector('.space-y-2 .w-2.h-2.rounded-full');
                if (indicator) {
                    indicator.className = `w-2 h-2 rounded-full ${server.isOnline ? 'bg-green-500' : 'bg-red-500'}`;
                    indicator.title = server.isOnline ? '在线' : '离线';
                }
            }
        });
    }

    loadServersData() {
        try {
            const dataScript = document.getElementById('servers-data');
            if (dataScript) {
                this.allServers = JSON.parse(dataScript.textContent);
                this.totalItems = this.allServers.length;
                this.updateTotalPages();
                
                // 调试信息
                console.log(`加载服务器数据: ${this.totalItems} 个服务器`);
                console.log(`每页显示: ${this.pageSize} 条`);
                console.log(`总页数: ${this.totalPages} 页`);
            }
        } catch (error) {
            console.error('Failed to load servers data:', error);
            this.allServers = [];
            this.totalItems = 0;
        }
    }
    
    async loadOnlineStatus() {
        try {
            const response = await fetch('/api/allnode_status');
            const result = await response.json();
            
            if (result.success && result.data) {
                // 创建一个映射，方便快速查找
                const statusMap = new Map();
                
                // 遍历API返回的数据
                Object.entries(result.data).forEach(([sid, serverData]) => {
                    const isOnline = serverData.stat && typeof serverData.stat === 'object' && !serverData.stat.offline;
                    statusMap.set(sid, isOnline);
                });
                
                // 更新服务器的在线状态
                this.allServers.forEach(server => {
                    server.isOnline = statusMap.get(server.sid) || false;
                });
                
                console.log('在线状态加载完成');
                
                // 重新渲染页面
                this.renderPage();
            }
        } catch (error) {
            console.error('加载在线状态失败:', error);
        }
    }

    updateTotalPages() {
        this.totalPages = Math.ceil(this.totalItems / this.pageSize);
        if (this.currentPage > this.totalPages && this.totalPages > 0) {
            this.currentPage = this.totalPages;
        }
        console.log(`更新总页数: 总数据 ${this.totalItems}, 每页 ${this.pageSize}, 总页数 ${this.totalPages}`);
    }

    renderServerList() {
        const tbody = document.getElementById('servers');
        const mobileList = document.getElementById('mobile-servers-list');
        const noDataMessage = document.getElementById('no-data-message');
        
        console.log(`渲染第 ${this.currentPage} 页, 总共 ${this.totalPages} 页, 总数据 ${this.totalItems} 条`);
        
        if (this.totalItems === 0) {
            if (tbody) tbody.innerHTML = '';
            if (mobileList) mobileList.innerHTML = '';
            if (noDataMessage) noDataMessage.classList.remove('hidden');
            return;
        }
        
        if (noDataMessage) noDataMessage.classList.add('hidden');
        
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = Math.min(startIndex + this.pageSize, this.totalItems);
        const currentServers = this.allServers.slice(startIndex, endIndex);
        
        console.log(`显示索引 ${startIndex} 到 ${endIndex-1}, 实际显示 ${currentServers.length} 条数据`);
        
        // 渲染桌面端表格
        if (tbody) {
            tbody.innerHTML = currentServers.map((server, index) => this.createServerRow(server, index)).join('');
            this.initSortable();
        }
        
        // 渲染移动端卡片
        if (mobileList) {
            mobileList.innerHTML = currentServers.map(server => this.createMobileServerCard(server)).join('');
            this.initMobileSortable();
        }
    }

    createServerRow(server, index) {
        // 完全使用Tailwind实现所有功能：透明背景+边框分隔+响应式+拖拽支持
        const baseClass = [
            // 基础样式
            'bg-transparent transition-all duration-200',
            // 边框分隔 (替代divide-y)
            'border-b border-slate-200/50 dark:border-slate-700/40',
            // hover效果
            'hover:bg-slate-50/30 dark:hover:bg-slate-800/20',
            // 拖拽排序样式支持
            'sortable-item relative',
            // 响应式padding
            'md:text-sm sm:text-xs'
        ].join(' ');
        
        return `
            <tr sid="${server.sid}" class="${baseClass}">
                <td class="p-2 sm:p-3 md:p-4 w-12 sm:w-16 text-center">
                    <div class="w-6 h-6 bg-transparent rounded-lg flex items-center justify-center border border-slate-200/50 dark:border-slate-600/50 cursor-move handle transition-transform duration-150 hover:scale-110">
                        <i class="ti ti-grip-horizontal"></i>
                    </div>
                </td>
                <td class="p-2 sm:p-3 md:p-4 min-w-[200px] max-w-[300px]">
                    <div class="space-y-1.5">
                        <!-- 第一行：服务器名称（主要信息） -->
                        <div class="flex items-center gap-2">
                            <!-- 在线状态指示灯 -->
                            <div class="w-2 h-2 rounded-full ${server.isOnline ? 'bg-green-500' : 'bg-red-500'}" 
                                 title="${server.isOnline ? '在线' : '离线'}"></div>
                            <div class="w-6 h-6 bg-transparent rounded-lg flex items-center justify-center border border-blue-200/50 dark:border-blue-700/30">
                                <i class="ti ti-device-desktop"></i>
                            </div>
                            <span class="text-sm font-semibold text-slate-800 dark:text-slate-200 cursor-pointer select-all">${this.escapeHtml(server.name)}</span>
                        </div>
                        <!-- 第二行：主机地址（次要信息） -->
                        <div class="pl-10">
                            <span class="text-xs text-slate-500 dark:text-slate-400 font-mono cursor-pointer select-all">${this.escapeHtml(server.host)}</span>
                        </div>
                        <!-- 第三行：标签（独立一行） -->
                        ${server.tags && server.tags.length > 0 ? `
                        <div class="pl-10 mt-1">
                            ${this.createCompactTagsDisplay(server.tags)}
                        </div>` : ''}
                    </div>
                </td>
                <td class="p-2 sm:p-3 md:p-4 text-center">${this.statusMap[server.status] || this.statusMap[0]}</td>
                <td class="p-2 sm:p-3 md:p-4">
                    ${this.createActionButtons(server)}
                </td>
            </tr>
        `;
    }

    createMobileServerCard(server) {
        // 移动端卡片完全使用Tailwind：响应式+交互效果+拖拽支持
        const cardClasses = [
            // 基础样式
            'bg-white/90 dark:bg-slate-800/70 rounded-xl border border-slate-200 dark:border-slate-700',
            'p-4 transition-all duration-200',
            // 交互效果
            'hover:shadow-lg hover:bg-blue-50/80 dark:hover:bg-slate-700/90',
            // 拖拽排序支持
            'mobile-server-card sortable-item',
            // 响应式优化
            'relative cursor-pointer',
            // 变换效果
            'hover:translate-y-[-1px] active:scale-[0.98]'
        ].join(' ');
        
        return `
            <div sid="${server.sid}" class="${cardClasses}">
                <!-- 卡片头部 -->
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-transparent rounded-lg flex items-center justify-center border border-slate-200/50 dark:border-slate-600/50 cursor-move handle">
                            <i class="ti ti-grip-horizontal"></i>
                        </div>
                        <div class="w-8 h-8 bg-transparent rounded-lg flex items-center justify-center border border-blue-200/50 dark:border-blue-700/30">
                            <i class="ti ti-device-desktop"></i>
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        ${this.statusMap[server.status] || this.statusMap[0]}
                    </div>
                </div>
                
                <!-- 服务器信息 -->
                <div class="space-y-1.5 mb-4">
                    <!-- 第一行：服务器名称（主要信息） -->
                    <div class="flex items-center gap-2">
                        <!-- 在线状态指示灯 -->
                        <div class="w-2 h-2 rounded-full ${server.isOnline ? 'bg-green-500' : 'bg-red-500'}" 
                             title="${server.isOnline ? '在线' : '离线'}"></div>
                        <h3 class="text-base font-semibold text-slate-800 dark:text-slate-200 cursor-pointer select-all">${this.escapeHtml(server.name)}</h3>
                    </div>
                    <!-- 第二行：主机地址（次要信息） -->
                    <div class="text-sm">
                        <p class="text-sm text-slate-500 dark:text-slate-400 font-mono cursor-pointer select-all">${this.escapeHtml(server.host)}</p>
                    </div>
                    <!-- 第三行：标签（独立一行） -->
                    ${server.tags && server.tags.length > 0 ? `
                    <div class="mt-1">
                        ${this.createCompactTagsDisplay(server.tags)}
                    </div>` : ''}
                </div>
                
                <!-- 操作按钮 - 移动端优化 -->
                <div class="flex items-center gap-2 pt-3 border-t border-slate-200/60 dark:border-slate-700/40">
                    ${this.createMobileActionButtons(server)}
                </div>
            </div>
        `;
    }

    createActionButtons(server) {
        const btnBaseClass = "min-w-[68px] h-8 px-2 bg-white/90 dark:bg-slate-700/70 text-slate-600 dark:text-slate-300 rounded-lg focus:outline-none focus:ring-2 transition-all duration-200 backdrop-blur-sm border border-slate-200/60 dark:border-slate-600/60 active:scale-95 flex items-center justify-center gap-1 hover:shadow-sm text-xs font-medium";
        
        return `
            <div class="flex items-center gap-1">
                <!-- 编辑按钮（主要操作） -->
                <a href="/admin/servers/${server.sid}/" 
                   class="${btnBaseClass} hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-600 dark:hover:text-green-400 focus:ring-green-500/50 hover:border-green-300 dark:hover:border-green-600" 
                   title="编辑服务器信息">
                    <i class="ti ti-edit"></i>
                    <span>编辑</span>
                </a>
                
                <!-- 更多操作下拉菜单 -->
                <div class="relative server-dropdown" data-sid="${server.sid}">
                    <button onclick="serversPagination.toggleDropdown('${server.sid}')" 
                            class="${btnBaseClass} hover:bg-slate-50 dark:hover:bg-slate-600 focus:ring-slate-500/50" 
                            title="更多操作">
                        <i class="ti ti-dots-vertical"></i>
                    </button>
                    
                    <!-- 下拉菜单内容 -->
                    <div id="dropdown-${server.sid}" class="absolute right-0 top-full mt-1 w-40 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700 py-1 z-[9999] hidden">
                        <button onclick="init('${server.sid}'); serversPagination.closeAllDropdowns();" 
                                class="w-full px-3 py-2 text-left text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 flex items-center gap-2 transition-colors duration-150">
                            <i class="ti ti-download"></i>
                            <span>安装脚本</span>
                        </button>
                        <button onclick="resetTraffic('${server.sid}', '${this.escapeHtml(server.name)}'); serversPagination.closeAllDropdowns();" 
                                class="w-full px-3 py-2 text-left text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 flex items-center gap-2 transition-colors duration-150">
                            <i class="ti ti-refresh"></i>
                            <span>重置流量</span>
                        </button>
                        <div class="border-t border-slate-200 dark:border-slate-700 my-1"></div>
                        <button onclick="del('${server.sid}', '${this.escapeHtml(server.name)}'); serversPagination.closeAllDropdowns();" 
                                class="w-full px-3 py-2 text-left text-sm text-slate-700 dark:text-slate-300 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400 flex items-center gap-2 transition-colors duration-150">
                            <i class="ti ti-trash"></i>
                            <span>删除服务器</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    createMobileActionButtons(server) {
        const editBtnClass = "flex-1 h-11 bg-white/90 dark:bg-slate-700/70 text-slate-600 dark:text-slate-300 rounded-lg focus:outline-none focus:ring-2 transition-all duration-200 backdrop-blur-sm border border-slate-200/60 dark:border-slate-600/60 active:scale-95 flex items-center justify-center hover:shadow-sm text-sm font-medium gap-2 hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-600 dark:hover:text-green-400 focus:ring-green-500/50 hover:border-green-300 dark:hover:border-green-600";
        const moreBtnClass = "h-11 bg-white/90 dark:bg-slate-700/70 text-slate-600 dark:text-slate-300 rounded-lg focus:outline-none focus:ring-2 transition-all duration-200 backdrop-blur-sm border border-slate-200/60 dark:border-slate-600/60 active:scale-95 flex items-center justify-center hover:shadow-sm text-sm font-medium gap-2 hover:bg-slate-50 dark:hover:bg-slate-600 focus:ring-slate-500/50";
        
        return `
            <!-- 编辑按钮（主要操作） - 占用60%宽度 -->
            <a href="/admin/servers/${server.sid}/" 
               class="${editBtnClass}" 
               title="编辑服务器信息">
                <i class="ti ti-edit"></i>
                <span>编辑</span>
            </a>
            
            <!-- 更多操作下拉菜单 - 占用40%宽度 -->
            <div class="relative server-dropdown flex-1" data-sid="${server.sid}">
                <button onclick="serversPagination.toggleDropdown('mobile-${server.sid}')" 
                        class="${moreBtnClass} w-full" 
                        title="更多操作">
                    <i class="ti ti-dots-vertical"></i>
                    <span>更多</span>
                </button>
                
                <!-- 下拉菜单内容 -->
                <div id="dropdown-mobile-${server.sid}" class="absolute right-0 bottom-full mb-1 w-44 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700 py-1 z-[9999] hidden">
                    <button onclick="init('${server.sid}'); serversPagination.closeAllDropdowns();" 
                            class="w-full px-3 py-3 text-left text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 flex items-center gap-2 transition-colors duration-150">
                        <i class="ti ti-download"></i>
                        <span>安装脚本</span>
                    </button>
                    <button onclick="resetTraffic('${server.sid}', '${this.escapeHtml(server.name)}'); serversPagination.closeAllDropdowns();" 
                            class="w-full px-3 py-3 text-left text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 flex items-center gap-2 transition-colors duration-150">
                        <i class="ti ti-refresh"></i>
                        <span>重置流量</span>
                    </button>
                    <div class="border-t border-slate-200 dark:border-slate-700 my-1"></div>
                    <button onclick="del('${server.sid}', '${this.escapeHtml(server.name)}'); serversPagination.closeAllDropdowns();" 
                            class="w-full px-3 py-3 text-left text-sm text-slate-700 dark:text-slate-300 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400 flex items-center gap-2 transition-colors duration-150">
                        <i class="ti ti-trash"></i>
                        <span>删除服务器</span>
                    </button>
                </div>
            </div>
        `;
    }

    createTagsDisplay(tags) {
        if (!Array.isArray(tags) || tags.length === 0) {
            return `
                <div class="flex items-center justify-center text-xs px-1.5 py-0.5 rounded-md bg-slate-100 dark:bg-slate-800/60 text-slate-400 dark:text-slate-500" title="暂无标签">
                    <span class="text-xs">无标签</span>
                </div>
            `;
        }
        
        let tagsHtml = '';
        const maxTags = 4;
        
        // 显示前4个标签
        for (let i = 0; i < Math.min(maxTags, tags.length); i++) {
            const tag = tags[i];
            const tagStyle = tag.color ? 
                `background-color: ${tag.color}20; border: 1px solid ${tag.color}40; color: ${tag.color};` :
                'background-color: #6b728080; border: 1px solid #6b728040; color: #6b7280;';
            
            tagsHtml += `
                <span class="inline-flex items-center text-xs px-1.5 py-0.5 rounded-md font-medium"
                      title="${this.escapeHtml(tag.name)}"
                      style="${tagStyle}">
                    ${this.escapeHtml(tag.name)}
                </span>
            `;
        }
        
        return `<div class="flex flex-wrap gap-1">${tagsHtml}</div>`;
    }

    createCompactTagsDisplay(tags) {
        if (!Array.isArray(tags) || tags.length === 0) {
            return '';
        }
        
        let tagsHtml = '';
        const maxTags = 3; // 紧凑模式下最多显示3个标签
        
        // 显示前3个标签
        for (let i = 0; i < Math.min(maxTags, tags.length); i++) {
            const tag = tags[i];
            const tagStyle = tag.color ? 
                `background-color: ${tag.color}15; border: 1px solid ${tag.color}30; color: ${tag.color};` :
                'background-color: #6b728060; border: 1px solid #6b728030; color: #6b7280;';
            
            tagsHtml += `
                <span class="inline-flex items-center text-xs px-1.5 py-0.5 rounded font-medium"
                      title="${this.escapeHtml(tag.name)}"
                      style="${tagStyle}">
                    ${this.escapeHtml(tag.name)}
                </span>
            `;
        }
        
        // 如果有更多标签，显示 +N
        if (tags.length > maxTags) {
            const remainingCount = tags.length - maxTags;
            tagsHtml += `
                <span class="inline-flex items-center text-xs px-1.5 py-0.5 rounded bg-slate-100 dark:bg-slate-700 text-slate-500 dark:text-slate-400 font-medium"
                      title="还有 ${remainingCount} 个标签">
                    +${remainingCount}
                </span>
            `;
        }
        
        return `<div class="inline-flex items-center gap-1">${tagsHtml}</div>`;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    initSortable() {
        if (this.sortableInstance) {
            this.sortableInstance.destroy();
        }
        
        if (typeof Sortable !== 'undefined') {
            const serversElement = document.getElementById('servers');
            if (serversElement) {
                this.sortableInstance = new Sortable(serversElement, {
                    handle: '.handle',
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    onUpdate: () => this.updateServerOrder()
                });
            }
        }
    }

    initMobileSortable() {
        if (this.mobileSortableInstance) {
            this.mobileSortableInstance.destroy();
        }
        
        if (typeof Sortable !== 'undefined') {
            const mobileServersElement = document.getElementById('mobile-servers-list');
            if (mobileServersElement) {
                this.mobileSortableInstance = new Sortable(mobileServersElement, {
                    handle: '.handle',
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    onUpdate: () => this.updateMobileServerOrder()
                });
            }
        }
    }

    updateServerOrder() {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const rows = document.querySelectorAll("#servers > tr");
        
        rows.forEach((row, index) => {
            const sid = row.getAttribute('sid');
            const globalIndex = startIndex + index;
            
            const serverIndex = this.allServers.findIndex(s => s.sid === sid);
            if (serverIndex !== -1) {
                const server = this.allServers[serverIndex];
                this.allServers.splice(serverIndex, 1);
                this.allServers.splice(globalIndex, 0, server);
            }
        });
    }

    updateMobileServerOrder() {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const cards = document.querySelectorAll("#mobile-servers-list > div");
        
        cards.forEach((card, index) => {
            const sid = card.getAttribute('sid');
            const globalIndex = startIndex + index;
            
            const serverIndex = this.allServers.findIndex(s => s.sid === sid);
            if (serverIndex !== -1) {
                const server = this.allServers[serverIndex];
                this.allServers.splice(serverIndex, 1);
                this.allServers.splice(globalIndex, 0, server);
            }
        });
    }

    updatePaginationInfo() {
        const startIndex = (this.currentPage - 1) * this.pageSize + 1;
        const endIndex = Math.min(this.currentPage * this.pageSize, this.totalItems);
        
        const paginationInfo = document.getElementById('pagination-info');
        const paginationSummary = document.getElementById('pagination-summary');
        
        if (paginationInfo) {
            paginationInfo.textContent = `显示 ${startIndex}-${endIndex} 共 ${this.totalItems} 条`;
        }
        
        if (paginationSummary) {
            paginationSummary.textContent = `第 ${this.currentPage} 页，共 ${this.totalPages} 页`;
        }
    }

    renderPageNumbers() {
        const container = document.getElementById('page-numbers');
        if (!container) {
            console.log('找不到分页按钮容器');
            return;
        }

        const maxVisiblePages = window.innerWidth < 640 ? 3 : 5; // 移动端显示更少页码
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);
        
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }
        
        console.log(`生成分页按钮: 第 ${startPage} 到 ${endPage} 页, 当前第 ${this.currentPage} 页`);
        
        const pages = [];
        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === this.currentPage;
            const className = isActive ? 
                'inline-flex items-center justify-center w-10 h-10 sm:w-9 sm:h-9 bg-blue-500 text-white border border-blue-500 rounded-lg shadow-md font-medium focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-200' : 
                'inline-flex items-center justify-center w-10 h-10 sm:w-9 sm:h-9 bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-200 active:scale-95';
            
            pages.push(`
                <button class="${className}" 
                        onclick="serversPagination.goToPage(${i})" 
                        ${isActive ? 'disabled' : ''}>
                    ${i}
                </button>
            `);
        }
        
        container.innerHTML = pages.join('');
        console.log(`分页按钮生成完成, 共 ${pages.length} 个按钮`);
    }

    updatePaginationControls() {
        const controls = ['first-page', 'prev-page', 'next-page', 'last-page'];
        const states = [
            this.currentPage === 1,
            this.currentPage === 1, 
            this.currentPage === this.totalPages,
            this.currentPage === this.totalPages
        ];

        controls.forEach((id, index) => {
            const btn = document.getElementById(id);
            if (btn) btn.disabled = states[index];
        });
        
        const jumpInput = document.getElementById('jump-page');
        if (jumpInput) {
            jumpInput.max = this.totalPages;
            jumpInput.value = this.currentPage;
        }
    }

    goToPage(page) {
        if (page < 1 || page > this.totalPages) return;
        this.currentPage = page;
        this.renderPage();
    }

    changePageSize(newSize) {
        this.pageSize = parseInt(newSize);
        this.updateTotalPages();
        this.renderPage();
    }

    renderPage() {
        this.renderServerList();
        this.updatePaginationInfo();
        this.renderPageNumbers();
        this.updatePaginationControls();
    }

    bindEvents() {
        // 分页控制按钮
        const controls = [
            { id: 'first-page', action: () => this.goToPage(1) },
            { id: 'prev-page', action: () => this.goToPage(this.currentPage - 1) },
            { id: 'next-page', action: () => this.goToPage(this.currentPage + 1) },
            { id: 'last-page', action: () => this.goToPage(this.totalPages) }
        ];

        controls.forEach(({ id, action }) => {
            const btn = document.getElementById(id);
            if (btn) btn.addEventListener('click', action);
        });
        
        // 每页数量选择器
        const pageSizeSelector = document.getElementById('page-size-selector');
        if (pageSizeSelector) {
            pageSizeSelector.addEventListener('change', (e) => {
                this.changePageSize(e.target.value);
            });
        }
        
        // 快速跳转
        const jumpBtn = document.getElementById('jump-btn');
        const jumpInput = document.getElementById('jump-page');
        
        if (jumpBtn && jumpInput) {
            const handleJump = () => {
                const jumpPage = parseInt(jumpInput.value);
                if (jumpPage >= 1 && jumpPage <= this.totalPages) {
                    this.goToPage(jumpPage);
                }
            };

            jumpBtn.addEventListener('click', handleJump);
            jumpInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') handleJump();
            });
        }

        // 响应式页码按钮更新
        window.addEventListener('resize', () => {
            this.renderPageNumbers();
        });
    }

    // 初始化下拉菜单处理
    initDropdownHandlers() {
        // 点击页面其他地方关闭下拉菜单
        document.addEventListener('click', (e) => {
            // 如果点击的不是下拉菜单按钮或菜单内容，关闭所有下拉菜单
            if (!e.target.closest('.server-dropdown')) {
                this.closeAllDropdowns();
            }
        });
    }
    
    // 切换下拉菜单
    toggleDropdown(dropdownId) {
        const dropdown = document.getElementById(`dropdown-${dropdownId}`);
        if (!dropdown) return;
        
        const isHidden = dropdown.classList.contains('hidden');
        
        // 先关闭所有其他下拉菜单
        this.closeAllDropdowns();
        
        // 切换当前下拉菜单
        if (isHidden) {
            dropdown.classList.remove('hidden');
            
            // 检查下拉菜单是否超出视口
            setTimeout(() => {
                const rect = dropdown.getBoundingClientRect();
                const viewportHeight = window.innerHeight;
                
                // 如果下拉菜单底部超出视口，调整位置
                if (rect.bottom > viewportHeight - 10) {
                    dropdown.style.bottom = '100%';
                    dropdown.style.top = 'auto';
                    dropdown.style.marginBottom = '0.25rem';
                    dropdown.style.marginTop = '0';
                }
            }, 0);
        }
        
        // 阻止事件冒泡，防止立即关闭
        event.stopPropagation();
    }
    
    // 关闭所有下拉菜单
    closeAllDropdowns() {
        const dropdowns = document.querySelectorAll('[id^="dropdown-"]');
        dropdowns.forEach(dropdown => {
            dropdown.classList.add('hidden');
            // 重置位置样式
            dropdown.style.bottom = '';
            dropdown.style.top = '';
            dropdown.style.marginBottom = '';
            dropdown.style.marginTop = '';
        });
    }

    // 保存排序
    async saveOrder() {
        const servers = this.allServers.map(server => server.sid);
        
        startloading();
        try {
            const res = await postjson("/admin/servers/ord", {servers});
            endloading();
            notice(res.data);
        } catch (error) {
            endloading();
            notice('保存排序失败: ' + error.message, 'error');
        }
    }
    
    // 清理资源
    destroy() {
        // 关闭 WebSocket 连接
        if (this.wsConnection) {
            console.log('关闭 WebSocket 连接');
            this.wsConnection.close();
            this.wsConnection = null;
        }
        
        // 销毁 Sortable 实例
        if (this.sortableInstance) {
            this.sortableInstance.destroy();
            this.sortableInstance = null;
        }
        
        if (this.mobileSortableInstance) {
            this.mobileSortableInstance.destroy();
            this.mobileSortableInstance = null;
        }
    }
}

// 全局实例
let serversPagination = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    serversPagination = new ServersPagination();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', function() {
    if (serversPagination) {
        serversPagination.destroy();
    }
});

// 重写全局saveOrder函数
window.saveOrder = function() {
    if (serversPagination) {
        serversPagination.saveOrder();
    }
};
