/*
 * Admin Monitor API helper
 * - Uses /api/admin/monitor/* endpoints (requires Admin权限)
 * - 鉴权方式：
 *   1) <PERSON>ie鉴权 (自动) - 与系统统一
 *   2) Token鉴权 (可选) - 支持API独立调用
 * - Exposes window.AdminMonitorAPI with read/write methods
 */
(function() {
  'use strict';

  function getAdminToken() {
    try {
      const meta = document.querySelector('meta[name="x-admin-token"]');
      if (meta && meta.content) return meta.content.trim();
    } catch (_) {}
    try {
      const ls = localStorage.getItem('admin_token');
      if (ls && ls.trim()) return ls.trim();
    } catch (_) {}
    return '';
  }

  async function apiFetch(path, options = {}) {
    const token = getAdminToken();
    const headers = Object.assign({ 'Content-Type': 'application/json' }, options.headers || {});
    // Token是可选的 - 优先使用Cookie鉴权，Token作为备选
    if (token) headers['X-Admin-Token'] = token;
    // 确保发送Cookie
    const fetchOptions = Object.assign({ credentials: 'same-origin' }, options, { headers });
    const res = await fetch(path, fetchOptions);
    if (!res.ok) {
      let msg = `HTTP ${res.status}`;
      try { const j = await res.json(); msg = j.message || msg; } catch (_e) {}
      throw new Error(msg);
    }
    return res.json();
  }

  const AdminMonitorAPI = {
    // Regions (read)
    getRegions: async () => {
      return apiFetch('/api/admin/monitor/regions', { method: 'GET' });
    },
    // Regions (write)
    createRegion: async (payload) => {
      return apiFetch('/api/admin/monitor/regions', { method: 'POST', body: JSON.stringify(payload) });
    },
    updateRegion: async (id, payload) => {
      return apiFetch(`/api/admin/monitor/regions/${encodeURIComponent(id)}`, { method: 'PUT', body: JSON.stringify(payload) });
    },
    deleteRegion: async (id) => {
      return apiFetch(`/api/admin/monitor/regions/${encodeURIComponent(id)}`, { method: 'DELETE' });
    },
    getRegionTargets: async (regionId) => {
      return apiFetch(`/api/admin/monitor/regions/${encodeURIComponent(regionId)}/targets`, { method: 'GET' });
    },

    // Targets (read)
    getTargets: async () => {
      return apiFetch('/api/admin/monitor/targets', { method: 'GET' });
    },
    getTarget: async (id) => {
      return apiFetch(`/api/admin/monitor/targets/${encodeURIComponent(id)}`, { method: 'GET' });
    },
    // Targets (write)
    createTarget: async (payload) => {
      return apiFetch('/api/admin/monitor/targets', { method: 'POST', body: JSON.stringify(payload) });
    },
    updateTarget: async (id, payload) => {
      return apiFetch(`/api/admin/monitor/targets/${encodeURIComponent(id)}`, { method: 'PUT', body: JSON.stringify(payload) });
    },
    deleteTarget: async (id) => {
      return apiFetch(`/api/admin/monitor/targets/${encodeURIComponent(id)}`, { method: 'DELETE' });
    },

    // Token helpers
    setToken: (token) => { try { localStorage.setItem('admin_token', token || ''); } catch (_) {} },
    getToken: () => getAdminToken()
  };

  if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminMonitorAPI;
  } else {
    window.AdminMonitorAPI = AdminMonitorAPI;
  }
})();

