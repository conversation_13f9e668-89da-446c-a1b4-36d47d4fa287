/**
 * License管理页面 JavaScript
 * 

 */

class LicenseManagement {
    constructor() {
        this.currentUser = null;
        this.licenseList = [];
        this.filteredLicenseList = [];
        this.currentLicenseStatus = null;
        this.instanceId = null;

        // 添加配置对象
        this.config = {
            licenseServerUrl: 'https://dstatus_api.vps.mom/',
            apiTimeout: 10000
        };

        // 分页配置
        this.pagination = {
            currentPage: 1,
            pageSize: 20,
            totalPages: 1,
            totalItems: 0
        };

        // 搜索和筛选
        this.filters = {
            search: '',
            status: 'all',
            planType: 'all'
        };

        // 套餐配置缓存
        this.plansConfig = null;
        this.plansConfigLoadTime = 0;
        this.plansConfigCacheTime = 3600000; // 1小时缓存

        this.init();
    }

    /**
     * 检查是否为免费许可证
     * @param {object} license - 许可证对象
     * @returns {boolean} 是否为免费许可证
     */
    isFreeLicense(license) {
        const planName = (license.planName || license.plan_name || '').toLowerCase();
        const planType = (license.planType || license.plan_type || '').toLowerCase();
        return planName.includes('免费') || planName.includes('free') || planType === 'free';
    }



    /**
     * 统一的API调用包装器
     * @param {string} url - API URL
     * @param {object} options - fetch选项
     * @param {string} context - 错误上下文描述
     * @returns {Promise<object>} API响应结果
     */
    async apiCall(url, options = {}, context = 'API调用') {
        try {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            };
            
            const response = await fetch(url, { ...defaultOptions, ...options });
            
            // 检查认证错误
            if (response.status === 401 || response.status === 403) {
                console.log('[License] 检测到认证失败，清除用户会话状态');
                this.currentUser = null;
                this.updateLoginStatus(false);
                return { status: 0, data: '认证失败，请重新登录' };
            }
            
            const result = await response.json();
            
            // 检查token相关错误
            if (result.status !== 1) {
                const errorMessage = result.data || result.message || '';
                if (errorMessage.toLowerCase().includes('jwt') || 
                    errorMessage.toLowerCase().includes('token') || 
                    errorMessage.toLowerCase().includes('expired')) {
                    console.log('[License] 检测到JWT/Token错误，清除用户会话');
                    this.currentUser = null;
                    this.updateLoginStatus(false);
                }
            }
            
            return result;
        } catch (error) {
            console.error(`[License] ${context}失败:`, error);
            throw error;
        }
    }

    init() {
        console.log('🚀 License管理页面初始化...');
        
        // 绑定事件
        this.bindEvents();
        
        // 初始化页面
        this.initializePage();
    }

    bindEvents() {
        // Tab切换
        const tabServerBtn = document.getElementById('tabServerBtn');
        const tabManualBtn = document.getElementById('tabManualBtn');
        
        if (tabServerBtn) {
            tabServerBtn.addEventListener('click', () => this.switchTab('server'));
        }
        if (tabManualBtn) {
            tabManualBtn.addEventListener('click', () => this.switchTab('manual'));
        }
        
        // 移除了节点使用容器相关事件（已从界面删除）

        // Server登录
        const serverLoginBtn = document.getElementById('serverLoginBtn');
        if (serverLoginBtn) {
            serverLoginBtn.addEventListener('click', () => this.handleServerLogin());
        }

        // Server登出
        const serverLogoutBtn = document.getElementById('serverLogoutBtn');
        if (serverLogoutBtn) {
            serverLogoutBtn.addEventListener('click', () => this.handleServerLogout());
        }

        // 刷新License列表
        const refreshBtn = document.getElementById('refreshLicenseListBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshLicenseList());
        }

        // 全局刷新状态按钮
        const refreshAllBtn = document.getElementById('refreshAllBtn');
        if (refreshAllBtn) {
            refreshAllBtn.addEventListener('click', () => this.handleGlobalRefresh());
        }

        // 手动激活
        const checkBtn = document.getElementById('checkLicenseBtn');
        const activateBtn = document.getElementById('activateLicenseBtn');
        
        if (checkBtn) {
            checkBtn.addEventListener('click', () => this.checkManualLicense());
        }
        if (activateBtn) {
            activateBtn.addEventListener('click', () => this.activateManualLicense());
        }

        // 输入框回车键处理
        const emailInput = document.getElementById('serverEmailInput');
        const passwordInput = document.getElementById('serverPasswordInput');
        const licenseKeyInput = document.getElementById('licenseKeyInput');

        if (emailInput && passwordInput) {
            [emailInput, passwordInput].forEach(input => {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.handleServerLogin();
                    }
                });
            });
        }

        if (licenseKeyInput) {
            licenseKeyInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.checkManualLicense();
                }
            });
        }

        // 搜索和筛选事件
        this.bindSearchAndFilterEvents();
    }

    async loadPlansConfig() {
        // 检查缓存
        const now = Date.now();
        if (this.plansConfig && (now - this.plansConfigLoadTime) < this.plansConfigCacheTime) {
            return this.plansConfig;
        }

        try {
            const response = await fetch('/admin/api/license-enhanced/plans');
            const result = await response.json();
            
            if (result.status === 1 && result.data && result.data.plans) {
                this.plansConfig = result.data.plans;
                this.plansConfigLoadTime = now;
                console.log('套餐配置加载成功:', this.plansConfig);
                return this.plansConfig;
            } else {
                console.warn('获取套餐配置失败，将使用服务器端的动态配置');
                // 不使用硬编码的默认值，而是依赖服务器端的动态配置
                // 服务器端会从unifiedConfigService获取配置
                return null;
            }
        } catch (error) {
            console.error('加载套餐配置失败:', error);
            // 不使用硬编码的默认值，返回null让调用方处理
            return null;
        }
    }

    async getMaxNodesForPlan(planType) {
        // 优先从已加载的配置获取
        if (this.plansConfig && this.plansConfig[planType]) {
            return this.plansConfig[planType].maxNodes;
        }

        // 如果配置未加载，尝试从License Server获取
        try {
            await this.loadPlansConfig();
            if (this.plansConfig && this.plansConfig[planType]) {
                return this.plansConfig[planType].maxNodes;
            }
        } catch (error) {
            console.warn('[License] 无法从License Server获取套餐配置:', error.message);
        }

        // 最后的fallback：从License Plans Cache获取
        try {
            const response = await fetch('/admin/api/license-enhanced/plans');
            if (response.ok) {
                const result = await response.json();
                if (result.status === 1 && result.data && result.data[planType]) {
                    return result.data[planType].maxNodes;
                }
            }
        } catch (error) {
            console.warn('[License] 无法获取套餐配置fallback:', error.message);
        }

        // 处理professional别名
        if (planType === 'professional') {
            return await this.getMaxNodesForPlan('pro');
        }

        // 如果所有方法都失败，记录警告并返回null
        console.warn(`[License] 无法获取套餐 ${planType} 的节点限制`);
        return null;
    }

    bindSearchAndFilterEvents() {
        // 搜索框事件
        const searchInput = document.getElementById('licenseSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value.trim();
                this.applyFiltersAndRender();
            });
        }

        // 状态筛选
        const statusFilter = document.getElementById('licenseStatusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.applyFiltersAndRender();
            });
        }

        // 套餐类型筛选
        const planFilter = document.getElementById('licensePlanFilter');
        if (planFilter) {
            planFilter.addEventListener('change', (e) => {
                this.filters.planType = e.target.value;
                this.applyFiltersAndRender();
            });
        }

        // 分页事件
        this.bindPaginationEvents();
    }

    bindPaginationEvents() {
        // 页码按钮事件委托
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('page-btn')) {
                const page = parseInt(e.target.dataset.page);
                if (page && page !== this.pagination.currentPage) {
                    this.pagination.currentPage = page;
                    this.renderCurrentPage();
                }
            }
        });

        // 页面大小选择
        const pageSizeSelect = document.getElementById('licensePageSize');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                this.pagination.pageSize = parseInt(e.target.value);
                this.pagination.currentPage = 1;
                this.applyFiltersAndRender();
            });
        }
    }

    async initializePage() {
        try {
            console.log('[License] 开始初始化页面...');

            // 加载套餐配置
            console.log('[License] 1. 加载套餐配置...');
            await this.loadPlansConfig();
            
            // 加载模块状态
            console.log('[License] 2. 加载模块状态...');
            await this.loadModuleStatus();
            
            // 加载当前License状态
            console.log('[License] 3. 加载当前License状态...');
            await this.loadCurrentLicenseStatus();
            
            // 检查用户登录状态
            console.log('[License] 4. 检查用户登录状态...');
            await this.checkUserSession();
            
            console.log('[License] 页面初始化完成');
            
        } catch (error) {
            console.error('[License] 页面初始化失败:', error);
            
            // 使用类的 showMessage 方法
            if (typeof this.showMessage === 'function') {
                this.showMessage('页面初始化失败: ' + error.message, 'error');
            } else {
                showMessage('页面初始化失败: ' + error.message, 'error');
            }
        }
    }

    async loadModuleStatus() {
        try {
            const result = await this.apiCall('/admin/api/license-enhanced/status', {}, '获取模块状态');

            if (result.status === 1) {
                const moduleData = result.data.module;
                const licenseData = result.data.license;

                // 优先使用模块数据中的实例ID，如果没有则从许可证数据中获取
                this.instanceId = moduleData.instanceId || licenseData?.instanceId || 'N/A';

                console.log('[License] 实例ID获取结果:', {
                    moduleInstanceId: moduleData.instanceId,
                    licenseInstanceId: licenseData?.instanceId,
                    finalInstanceId: this.instanceId
                });

                // 更新实例ID显示
                const instanceIdElement = document.getElementById('instanceId');
                if (instanceIdElement) {
                    instanceIdElement.textContent = this.instanceId;
                }

                // 更新服务器连接状态
                this.updateServerStatus(moduleData.enabled);

            } else {
                showMessage('获取模块状态失败: ' + result.data, 'error');
            }
        } catch (error) {
            console.error('[License] 加载模块状态失败:', error);
            this.updateServerStatus(false);
        }
    }

    async loadCurrentLicenseStatus() {
        try {
            const result = await this.apiCall('/admin/api/license-enhanced/status', {}, '获取License状态');
            
            if (result.status === 1) {
                const licenseData = result.data.license;
                this.currentLicenseStatus = licenseData;
                
                // API已经包含了currentNodes数据，不需要额外调用节点API
                console.log('获取到许可证状态:', {
                    planName: licenseData.planName,
                    maxNodes: licenseData.maxNodes,
                    currentNodes: licenseData.currentNodes,
                    status: licenseData.status,
                    isCached: licenseData.isCached || false,
                    cacheExpiry: licenseData.cacheExpiry || null
                });
                
                // 更新当前License状态显示
                        this.updateLicenseStatusDisplay(licenseData);
                
            } else {
                showMessage('获取License状态失败: ' + result.data, 'error');
            }
        } catch (error) {
            // 错误已在apiCall中处理
        }
    }

    async checkUserSession() {
        try {
            // 首先检查localStorage中是否有保存的用户信息
            const savedUserInfo = localStorage.getItem('licenseUserInfo');
            const savedEmail = localStorage.getItem('licenseUserEmail');
            const rememberMe = localStorage.getItem('licenseServerRememberMe');
            
            if (savedUserInfo && rememberMe === 'true') {
                console.log('[License] 检测到本地保存的用户信息，尝试恢复登录状态');
                
                try {
                    // 先恢复UI显示，避免闪烁
                    const userInfo = JSON.parse(savedUserInfo);
                    this.currentUser = userInfo;
                    this.updateLoginStatus(true);
                    console.log('[License] 已恢复UI显示，用户:', userInfo.email || userInfo.username);
                } catch (parseError) {
                    console.error('[License] 解析本地用户信息失败:', parseError);
                    // 清除无效数据
                    localStorage.removeItem('licenseUserInfo');
                    localStorage.removeItem('licenseUserEmail');
                }
            }
            
            // 调用API验证会话有效性
            const response = await fetch('/admin/api/license-enhanced/user/session');
            const result = await response.json();
            
            if (result.status === 1 && result.data.user) {
                // API确认已登录，更新用户信息
                this.currentUser = result.data.user;
                this.updateLoginStatus(true);
                console.log('[License] API确认用户已登录:', result.data.user.email || result.data.user.username);
                
                // 确保用户信息设置完成后再加载License列表
                try {
                    await this.loadLicenseList();
                } catch (loadError) {
                    console.warn('[License] 加载许可证列表失败:', loadError);
                    // 如果是token相关错误，清除用户状态
                    if (loadError.message && (loadError.message.includes('jwt') || loadError.message.includes('token'))) {
                        console.log('[License] 检测到token问题，清除用户会话');
                        this.currentUser = null;
                        this.updateLoginStatus(false);
                        // 清除localStorage
                        localStorage.removeItem('licenseUserInfo');
                        localStorage.removeItem('licenseUserEmail');
                        localStorage.removeItem('licenseServerRememberMe');
                    }
                }
            } else {
                // API返回未登录
                console.log('[License] API返回用户未登录');
                
                // 如果本地有保存的信息但API返回未登录，说明会话已过期
                if (savedUserInfo && rememberMe === 'true') {
                    console.log('[License] 本地有保存信息但会话已过期，需要重新登录');
                    showMessage('登录会话已过期，请重新登录', 'warning');
                    
                    // 清除本地保存的信息
                    localStorage.removeItem('licenseUserInfo');
                    localStorage.removeItem('licenseUserEmail');
                    localStorage.removeItem('licenseServerRememberMe');
                }
                
                this.currentUser = null;
                this.updateLoginStatus(false);
            }
        } catch (error) {
            console.error('检查用户会话失败:', error);
            
            // 清除可能无效的本地数据
            localStorage.removeItem('licenseUserInfo');
            localStorage.removeItem('licenseUserEmail');
            
            this.currentUser = null;
            this.updateLoginStatus(false);
        }
    }

    updateServerStatus(connected) {
        const statusElement = document.getElementById('serverStatus');
        const indicatorElement = document.getElementById('serverStatusIndicator');
        
        if (statusElement) {
            if (connected) {
                statusElement.textContent = '连接正常';
            } else {
                statusElement.textContent = '连接失败';
            }
        }
        
        // 更新指示器状态
        if (indicatorElement) {
            if (connected) {
                indicatorElement.classList.add('online');
            } else {
                indicatorElement.classList.remove('online');
            }
        }
    }

    updateLicenseStatusDisplay(licenseData) {
        const contentElement = document.getElementById('licenseStatusContent');
        if (!contentElement) return;

        // 更新缓存状态指示器
        this.updateCacheStatusIndicator(licenseData);

        if (!licenseData) {
            // 无License信息
            contentElement.innerHTML = `
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <i class="ti ti-help-circle text-2xl text-slate-400 dark:text-slate-500"></i>
                    </div>
                    <p class="text-slate-500 dark:text-slate-400">暂无License信息</p>
                </div>
            `;
            return;
        }
        
        // 如果是免费版（没有licenseKey或planType为free），也正常显示
        const isFreeTier = !licenseData.licenseKey || licenseData.licenseKey === '' || licenseData.planType === 'free';

        // 格式化过期时间
        let expiryText = '永久';
        if (licenseData.expiresAt) {
            // 处理Unix时间戳（秒）转换为毫秒
            const timestamp = licenseData.expiresAt.toString().length === 10 
                ? licenseData.expiresAt * 1000 
                : licenseData.expiresAt;
            const expiryDate = new Date(timestamp);
            if (!isNaN(expiryDate.getTime())) {
                expiryText = expiryDate.toLocaleDateString('zh-CN');
            }
        }

        // 获取状态文本和图标
        const statusInfo = this.getLicenseStatusInfo(licenseData.status);
        
        // 获取套餐信息 - 优先使用planDisplayName（中文名称）
        const planName = licenseData.planDisplayName || licenseData.planName || licenseData.plan_name || 'Standard Plan';
        const displayLicenseKey = (licenseData.planType === 'free' || !licenseData.licenseKey)
            ? 'Free Plan (No License Required)'
            : (licenseData.licenseKey || licenseData.license_key || 'N/A');
        
        // 获取节点信息
        const maxNodes = licenseData.maxNodes || licenseData.max_nodes || 10;
        const currentNodes = licenseData.currentNodes || licenseData.current_nodes || 0;

        contentElement.innerHTML = `
            <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 p-4 rounded-xl border border-slate-200/60 dark:border-slate-700/40">
                        <div class="text-sm text-slate-500 dark:text-slate-400 mb-2">套餐类型</div>
                        <div class="text-lg font-semibold text-slate-700 dark:text-slate-300">${planName}</div>
                    </div>
                    <div class="bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 p-4 rounded-xl border border-slate-200/60 dark:border-slate-700/40">
                        <div class="text-sm text-slate-500 dark:text-slate-400 mb-2">授权状态</div>
                        <div class="flex items-center">
                            <div class="login-status-indicator ${statusInfo.isValid ? 'online' : ''}"></div>
                            <span class="text-lg font-semibold text-slate-700 dark:text-slate-300">${statusInfo.text}</span>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-blue-50/80 to-blue-100/60 dark:from-blue-900/40 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200/60 dark:border-blue-700/40">
                        <div class="text-sm text-blue-600 dark:text-blue-400 mb-2">最大节点数</div>
                        <div class="text-lg font-semibold text-blue-700 dark:text-blue-300">${maxNodes}</div>
                    </div>
                    <div class="bg-gradient-to-r from-green-50/80 to-green-100/60 dark:from-green-900/40 dark:to-green-800/20 p-4 rounded-xl border border-green-200/60 dark:border-green-700/40">
                        <div class="text-sm text-green-600 dark:text-green-400 mb-2">当前节点数</div>
                        <div class="text-lg font-semibold text-green-700 dark:text-green-300">${currentNodes}</div>
                    </div>
                </div>
                
                <div class="space-y-3">
                    <div class="bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 p-4 rounded-xl border border-slate-200/60 dark:border-slate-700/40">
                        <div class="text-sm text-slate-500 dark:text-slate-400 mb-2">授权Key</div>
                        <div class="license-key-display break-all font-mono text-sm ${licenseData.planType === 'free' ? '' : 'cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800/50'} p-2 rounded transition-colors"
                             ${licenseData.planType === 'free' ? '' : `onclick="licenseManager.copyToClipboard('${displayLicenseKey}', 'License Key')" title="点击复制"`}>
                            ${displayLicenseKey}
                            ${licenseData.planType === 'free' ? '' : '<i class="ti ti-copy text-xs ml-2 text-slate-400"></i>'}
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 p-4 rounded-xl border border-slate-200/60 dark:border-slate-700/40">
                            <div class="text-sm text-slate-500 dark:text-slate-400 mb-2">到期时间</div>
                            <div class="text-base font-medium text-slate-700 dark:text-slate-300">${expiryText}</div>
                        </div>
                        <div class="bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 p-4 rounded-xl border border-slate-200/60 dark:border-slate-700/40">
                            <div class="text-sm text-slate-500 dark:text-slate-400 mb-2">实例ID</div>
                            <div class="text-sm font-mono text-slate-600 dark:text-slate-400 break-all cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800/50 p-1 rounded transition-colors"
                                 onclick="licenseManager.copyToClipboard('${this.instanceId || licenseData.instanceId || 'N/A'}', '实例ID')"
                                 title="点击复制">
                                ${this.instanceId || licenseData.instanceId || 'N/A'}
                                <i class="ti ti-copy text-xs ml-1 text-slate-400"></i>
                            </div>
                        </div>
                        <div class="bg-gradient-to-r from-indigo-50/80 to-indigo-100/60 dark:from-indigo-900/40 dark:to-indigo-800/20 p-4 rounded-xl border border-indigo-200/60 dark:border-indigo-700/40">
                            <div class="text-sm text-indigo-600 dark:text-indigo-400 mb-2">权限掩码</div>
                            <div class="text-base font-mono text-indigo-700 dark:text-indigo-300">${licenseData.featuresMask || 'N/A'}</div>
                        </div>
                    </div>
                </div>
                
                <div class="flex flex-wrap gap-3 pt-4 border-t border-slate-200/60 dark:border-slate-700/40">
                    <button onclick="licenseManager.refreshCurrentStatus()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200">
                        <i class="ti ti-refresh text-sm"></i>
                        刷新状态
                    </button>
                    ${(licenseData.licenseKey && licenseData.planType !== 'free' && (licenseData.status === 'active' || licenseData.status === 'invalid' || licenseData.status === 'expired')) ? `
                        <button onclick="licenseManager.unbindCurrentLicense()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200">
                            <i class="ti ti-unlink text-sm"></i>
                            解绑授权
                        </button>
                    ` : ''}
                    ${licenseData.planType !== 'enterprise' ? this.generateUpgradeButton(licenseData.planType, licenseData.specialOffer) : ''}
                </div>
            </div>
        `;
        
        // 显示功能列表
        this.displayFeatureList(licenseData);
    }

    displayFeatureList(licenseData) {
        const featureListContent = document.getElementById('featureListSection');
        const featuresList = document.getElementById('featureList');
        
        if (!featureListContent || !featuresList) return;
        
        // 提前定义图标映射，供所有渲染路径使用
        const featureIcons = {
            'analytics': 'chart-line',
            'monitoring': 'device-desktop',
            'network_check': 'wifi',
            'notifications': 'bell',
            'search': 'search',
            'brain': 'brain',
            'terminal': 'terminal-2',
            // 兼容映射
            'webssh': 'terminal-2',
            'auto-discovery': 'search',
            'advanced-analytics': 'chart-line',
            'api-access': 'api',
            'custom-alerts': 'bell',
            'BASIC_MONITORING': 'device-desktop',
            'WEBSSH': 'terminal-2',
            'AUTO_DISCOVERY': 'search',
            'ADVANCED_ANALYTICS': 'chart-line',
            'API_ACCESS': 'api',
            'CUSTOM_ALERTS': 'bell',
            // 变体
            'network-quality': 'wifi',
            'alerts': 'bell',
            'discovery': 'search',
            'ai-analysis': 'brain',
            'webssh-terminal': 'terminal-2'
        };

        // 若缺少功能数据，回退到 FeatureWall 动态定义
        if (!licenseData || !Array.isArray(licenseData.featureDetails) || licenseData.featureDetails.length === 0) {
            (async () => {
                try {
                    if (window.FeatureWall && typeof window.FeatureWall.refresh === 'function') {
                        await window.FeatureWall.refresh();
                        const fw = window.FeatureWall.getFeatureWallInfo ? window.FeatureWall.getFeatureWallInfo() : null;
                        if (fw && fw.features) {
                            const details = Object.values(fw.features).map(f => ({
                                name: f.featureName || f.name || '',
                                displayName: f.name || f.displayName || f.featureName || '',
                                description: f.description || '',
                                available: !!f.available
                            }));
                            // 渲染动态功能
                            renderFeatureDetails(details);
                            return;
                        }
                    }
                    featureListContent.style.display = 'none';
                } catch (e) {
                    console.warn('[License] 加载可用功能失败:', e);
                    featureListContent.style.display = 'none';
                }
            })();
            return;
        }

        // 继续使用 licenseData.featureDetails 渲染
        
        // 对功能进行排序：可用的功能在前，不可用的在后
        const sortedFeatures = [...licenseData.featureDetails].sort((a, b) => {
            // 可用的功能排在前面
            if (a.available && !b.available) return -1;
            if (!a.available && b.available) return 1;
            // 如果可用性相同，按名称排序
            return (a.displayName || a.name).localeCompare(b.displayName || b.name);
        });

        renderFeatureDetails(sortedFeatures);

        function renderFeatureDetails(details) {
            if (!Array.isArray(details) || details.length === 0) {
                featureListContent.style.display = 'none';
                return;
            }
            featureListContent.style.display = 'block';

            let html = '';
            details.forEach((feature) => {
                const isAvailable = !!feature.available;
                const iconKey = (feature.name || '').toLowerCase();
                const icon = featureIcons[iconKey] || featureIcons[feature.name] || 'puzzle';
                html += `
                <div class="bg-gradient-to-r ${isAvailable 
                    ? 'from-green-50/80 to-green-100/60 dark:from-green-900/20 dark:to-green-800/10 border-green-200/60 dark:border-green-700/40' 
                    : 'from-slate-50/80 to-slate-100/60 dark:from-slate-800/40 dark:to-slate-700/20 border-slate-200/60 dark:border-slate-700/40'
                } p-3 rounded-lg border flex items-center gap-3">
                    <div class="w-8 h-8 ${isAvailable 
                        ? 'bg-green-100 dark:bg-green-800' 
                        : 'bg-slate-100 dark:bg-slate-700'
                    } rounded-lg flex items-center justify-center">
                        <i class="ti ti-${icon} text-sm ${isAvailable 
                            ? 'text-green-600 dark:text-green-400' 
                            : 'text-slate-400 dark:text-slate-500'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <div class="text-sm font-medium ${isAvailable 
                            ? 'text-green-700 dark:text-green-300' 
                            : 'text-slate-600 dark:text-slate-400'
                        }">${feature.displayName || feature.name}</div>
                        ${feature.description ? `
                            <div class="text-xs ${isAvailable 
                                ? 'text-green-600/70 dark:text-green-400/70' 
                                : 'text-slate-500 dark:text-slate-500'
                            }">${feature.description}</div>
                        ` : ''}
                    </div>
                    <div>
                        ${isAvailable 
                            ? '<i class="ti ti-circle-check text-green-500 dark:text-green-400 text-sm"></i>'
                            : '<i class="ti ti-lock text-slate-400 dark:text-slate-500 text-sm"></i>'
                        }
                    </div>
                </div>`;
            });
            featuresList.innerHTML = html;
        }
    }

    updateCacheStatusIndicator(licenseData) {
        const indicatorElement = document.getElementById('cacheStatusIndicator');
        if (!indicatorElement) return;

        if (licenseData && licenseData.isCached) {
            indicatorElement.style.display = 'flex';
            indicatorElement.className = 'text-xs flex items-center gap-2';
            
            let cacheExpiryText = '';
            if (licenseData.cacheExpiry) {
                const expiryTime = new Date(licenseData.cacheExpiry);
                const remainingHours = Math.round((expiryTime - new Date()) / 3600000);
                cacheExpiryText = `（剩余${remainingHours}小时）`;
            }
            
            indicatorElement.innerHTML = `
                <div class="flex items-center gap-1 text-green-600 dark:text-green-400">
                    <i class="ti ti-refresh text-sm"></i>
                    <span>使用缓存数据</span>
                    <span class="text-slate-500 dark:text-slate-400">${cacheExpiryText}</span>
                </div>
            `;
        } else {
            indicatorElement.style.display = 'none';
        }
    }

    getLicenseStatusInfo(status) {
        const statusMap = {
            'active': { text: '有效', isValid: true, class: 'success' },
            'bound': { text: '已绑定', isValid: true, class: 'info' },
            'available': { text: '可用', isValid: true, class: 'success' },
            'valid': { text: '有效', isValid: true, class: 'success' },
            'inactive': { text: '未激活', isValid: false, class: 'warning' },
            'expired': { text: '已过期', isValid: false, class: 'error' },
            'invalid': { text: '无效', isValid: false, class: 'error' },
            'suspended': { text: '已暂停', isValid: false, class: 'warning' },
            'pending': { text: '待处理', isValid: false, class: 'info' },
            'unknown': { text: '未知', isValid: false, class: 'default' }
        };
        
        return statusMap[status.toLowerCase()] || { text: '未知', isValid: false, class: 'default' };
    }

    // 已废弃：权限显示功能已由更详细的功能列表(displayFeatureList)替代
    /* generatePermissionsDisplay(licenseData) {
        if (!licenseData || (!licenseData.permissions && !licenseData.features)) {
            return '';
        }

        // 定义功能映射
        const featureMap = {
            'BASIC_MONITORING': { name: '基础监控', icon: 'monitor', color: 'blue' },
            'WEBSSH': { name: 'WebSSH终端', icon: 'terminal', color: 'green' },
            'AUTO_DISCOVERY': { name: '自动发现', icon: 'search', color: 'purple' },
            'ADVANCED_ANALYTICS': { name: '高级分析', icon: 'analytics', color: 'orange' },
            'API_ACCESS': { name: 'API访问', icon: 'api', color: 'red' },
            'CUSTOM_ALERTS': { name: '自定义告警', icon: 'notifications', color: 'yellow' }
        };

        let permissionsHtml = '';
        
        // 如果有permissions对象，使用permissions
        if (licenseData.permissions) {
            const permissions = licenseData.permissions;
            permissionsHtml = `
                <div class="bg-gradient-to-r from-purple-50/80 to-purple-100/60 dark:from-purple-900/40 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200/60 dark:border-purple-700/40">
                    <div class="flex items-center gap-2 mb-3">
                        <i class="ti ti-shield-check text-purple-600 dark:text-purple-400 text-lg"></i>
                        <div class="text-sm font-medium text-purple-700 dark:text-purple-300">当前可用权限</div>
                    </div>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                        ${Object.entries(permissions).map(([key, value]) => {
                            if (!key.startsWith('canUse')) return '';
                            
                            const featureName = key.replace('canUse', '').replace(/([A-Z])/g, '_$1').toUpperCase().substring(1);
                            const feature = featureMap[featureName] || { name: featureName, icon: 'extension', color: 'gray' };
                            const isEnabled = Boolean(value);
                            
                            return `
                                <div class="flex items-center gap-2 p-2 rounded-lg ${isEnabled 
                                    ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300' 
                                    : 'bg-slate-100 dark:bg-slate-800/50 text-slate-500 dark:text-slate-400'}">
                                    <i class="ti ${isEnabled ? 'ti-circle-check' : 'ti-x'} text-sm"></i>
                                    <span class="text-xs font-medium">${feature.name}</span>
                                </div>
                            `;
                        }).filter(html => html).join('')}
                    </div>
                </div>
            `;
        }
        // 如果有features数组，使用features
        else if (licenseData.features && Array.isArray(licenseData.features)) {
            permissionsHtml = `
                <div class="bg-gradient-to-r from-purple-50/80 to-purple-100/60 dark:from-purple-900/40 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200/60 dark:border-purple-700/40">
                    <div class="flex items-center gap-2 mb-3">
                        <i class="ti ti-shield-check text-purple-600 dark:text-purple-400 text-lg"></i>
                        <div class="text-sm font-medium text-purple-700 dark:text-purple-300">当前可用功能</div>
                    </div>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                        ${Object.entries(featureMap).map(([key, feature]) => {
                            const isEnabled = licenseData.features.includes(key);
                            
                            return `
                                <div class="flex items-center gap-2 p-2 rounded-lg ${isEnabled 
                                    ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300' 
                                    : 'bg-slate-100 dark:bg-slate-800/50 text-slate-500 dark:text-slate-400'}">
                                    <i class="ti ${isEnabled ? 'ti-circle-check' : 'ti-x'} text-sm"></i>
                                    <span class="text-xs font-medium">${feature.name}</span>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>
            `;
        }

        return permissionsHtml;
    } */


    switchTab(tab) {
        // 切换Tab按钮状态
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
            content.style.display = 'none';
        });

        if (tab === 'server') {
            document.getElementById('tabServerBtn').classList.add('active');
            document.getElementById('tabServerContent').classList.add('active');
            document.getElementById('tabServerContent').style.display = 'block';
        } else if (tab === 'manual') {
            document.getElementById('tabManualBtn').classList.add('active');
            document.getElementById('tabManualContent').classList.add('active');
            document.getElementById('tabManualContent').style.display = 'block';
        }
    }

    updateLoginStatus(isLoggedIn) {
        const formElement = document.getElementById('serverLoginForm');
        const loggedInElement = document.getElementById('serverLoggedInStatus');
        const listElement = document.getElementById('serverLicenseList');
        const userNameElement = document.getElementById('currentUserName');

        if (isLoggedIn && this.currentUser) {
            // 已登录状态：隐藏登录表单，显示用户信息
            if (formElement) formElement.style.display = 'none';
            if (loggedInElement) {
                loggedInElement.style.display = 'block';
                // 更新用户名显示
                if (userNameElement) {
                    userNameElement.textContent = this.currentUser.username || this.currentUser.email || '用户';
                }
            }
            if (listElement) listElement.style.display = 'block';
        } else {
            // 未登录状态：显示登录表单，隐藏用户信息
            if (formElement) formElement.style.display = 'block';
            if (loggedInElement) loggedInElement.style.display = 'none';
            if (listElement) listElement.style.display = 'none';
        }
    }

    async handleServerLogin() {
        const emailInput = document.getElementById('serverEmailInput');
        const passwordInput = document.getElementById('serverPasswordInput');
        const loginBtn = document.getElementById('serverLoginBtn');

        if (!emailInput || !passwordInput) return;

        const email = emailInput.value.trim();
        const password = passwordInput.value.trim();

        if (!email || !password) {
            showMessage('请输入邮箱/用户名和密码', 'error');
            return;
        }

        // 禁用按钮
        if (loginBtn) {
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<i class="ti ti-refresh text-sm animate-spin"></i><span>登录中...</span>';
        }

        try {
            const response = await fetch('/admin/api/license-enhanced/user/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    emailOrUsername: email,
                    password: password
                })
            });

            const result = await response.json();

            if (result.status === 1) {
                this.currentUser = result.data.user;
                showMessage('登录成功', 'success');
                this.updateLoginStatus(true);
                
                // 保存用户信息到localStorage（如果勾选了"记住我"）
                const rememberMe = document.getElementById('rememberMe');
                if (rememberMe && rememberMe.checked) {
                    localStorage.setItem('licenseUserInfo', JSON.stringify(result.data.user));
                    localStorage.setItem('licenseUserEmail', email);
                    // 注意：出于安全考虑，不保存密码
                    console.log('[License] 已保存用户信息到本地');
                }
                
                // 清空输入框
                emailInput.value = '';
                passwordInput.value = '';
                
                // 加载当前License状态
                try {
                    await this.loadCurrentLicenseStatus();
                } catch (statusError) {
                    console.warn('[License] 登录后加载许可证状态失败:', statusError);
                }
                
                // 加载License列表
                try {
                    await this.loadLicenseList();
                } catch (loadError) {
                    console.warn('[License] 登录后加载许可证列表失败:', loadError);
                    // 如果是token错误，用户状态已经在loadLicenseList中处理
                }
                
                // 尝试自动绑定最佳许可证
                try {
                    await this.autoBindBestLicense();
                } catch (bindError) {
                    console.warn('[License] 自动绑定失败:', bindError);
                }
            } else {
                showMessage('登录失败: ' + result.data, 'error');
            }
        } catch (error) {
            console.error('登录失败:', error);
            showMessage('登录失败: ' + error.message, 'error');
        } finally {
            // 恢复按钮
            if (loginBtn) {
                loginBtn.disabled = false;
                loginBtn.innerHTML = '<i class="ti ti-login text-sm"></i><span>登录许可证系统</span>';
            }
        }
    }

    /**
     * 自动绑定最佳许可证
     */
    async autoBindBestLicense() {
        try {
            showMessage('正在检查可用许可证...', 'info');
            
            const response = await fetch('/admin/api/license-enhanced/user/auto-bind', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    preferredPlan: 'standard' // Default preferred plan
                })
            });

            const result = await response.json();

            if (result.status === 1) {
                const data = result.data;
                
                if (data.action === 'already_bound') {
                    showMessage(`许可证已绑定: ${data.license.planName}`, 'success');
                } else if (data.action === 'bound') {
                    showMessage(`自动绑定成功: ${data.license.planName}`, 'success');
                    
                    // 刷新当前License状态
                    await this.loadCurrentLicenseStatus();
                }
            } else {
                // 如果自动绑定失败，不显示错误（因为这是后台操作）
                console.log('自动绑定失败:', result.data);
            }
        } catch (error) {
            console.error('自动绑定异常:', error);
            // 自动绑定失败不影响用户体验，只记录日志
        }
    }

    /**
     * 手动触发自动绑定（给用户主动调用）
     */
    async triggerAutoBind() {
        try {
            showMessage('正在自动选择最佳许可证...', 'info');
            
            const response = await fetch('/admin/api/license-enhanced/user/auto-bind', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    preferredPlan: 'standard'
                })
            });

            const result = await response.json();

            if (result.status === 1) {
                const data = result.data;
                
                if (data.action === 'already_bound') {
                    showMessage(`许可证已绑定到当前实例: ${data.license.planName}`, 'success');
                    
                    // 即使已绑定，也刷新状态以确保显示最新信息
                    await this.loadCurrentLicenseStatus();
                    await this.loadModuleStatus();
                } else if (data.action === 'bound') {
                    showMessage(`自动绑定成功: ${data.license.planName}！`, 'success');
                    
                    // 1. 刷新当前License状态
                    await this.loadCurrentLicenseStatus();
                    
                    // 2. 刷新模块状态（确保节点数等信息更新）
                    await this.loadModuleStatus();
                    
                    // 3. 刷新License列表
                    try {
                        await this.loadLicenseList();
                    } catch (loadError) {
                        console.warn('[License] 自动绑定后刷新列表失败:', loadError);
                    }
                    
                    // 4. 触发全局刷新事件（通知其他模块）
                    window.dispatchEvent(new CustomEvent('licenseActivated', {
                        detail: { 
                            licenseKey: data.license.licenseKey,
                            planName: data.license.planName
                        }
                    }));
                    
                    // 5. 如果有功能墙，触发功能墙刷新
                    if (window.featureWallManager) {
                        window.featureWallManager.refresh();
                    }
                }
            } else {
                showMessage('自动绑定失败: ' + result.data, 'error');
            }
        } catch (error) {
            console.error('自动绑定异常:', error);
            showMessage('自动绑定异常: ' + error.message, 'error');
        }
    }

    async handleServerLogout() {
        try {
            const response = await fetch('/admin/api/license-enhanced/user/logout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.status === 1) {
                this.currentUser = null;
                this.licenseList = [];
                showMessage('已退出登录', 'success');
                this.updateLoginStatus(false);
            } else {
                showMessage('退出登录失败: ' + result.data, 'error');
            }
        } catch (error) {
            console.error('退出登录失败:', error);
            showMessage('退出登录失败: ' + error.message, 'error');
        }
    }

    async loadLicenseList() {
        // 如果用户未登录，直接返回
        if (!this.currentUser) {
            console.log('[License] 用户未登录，跳过加载许可证列表');
            this.licenseList = [];
            this.renderLicenseList([]);
            return;
        }

        try {
            const response = await fetch('/admin/api/license-enhanced/user/licenses');
            
            // 检查HTTP状态码
            if (response.status === 401 || response.status === 403) {
                console.log('[License] 检测到认证失败(401/403)，清除用户会话状态');
                this.currentUser = null;
                this.updateLoginStatus(false);
                this.licenseList = [];
                this.renderLicenseList([]);
                return;
            }
            
            const result = await response.json();

            if (result.status === 1) {
                const allLicenses = result.data.licenses || [];
                
                // 过滤掉免费许可证
                this.licenseList = allLicenses.filter(license => {
                    if (this.isFreeLicense(license)) {
                        console.log('[License] 过滤免费许可证:', {
                            licenseKey: license.licenseKey?.substring(0, 20) + '...',
                            planName: license.planName || license.plan_name,
                            planType: license.planType || license.plan_type
                        });
                        return false;
                    }
                    return true;
                });
                
                // 如果当前实例有绑定的许可证，获取当前节点数
                const currentInstanceBoundLicense = this.licenseList.find(license => {
                    const boundId = license.boundInstanceId || license.bound_instance_id || license.instance_id;
                    return boundId === this.instanceId;
                });
                
                if (currentInstanceBoundLicense) {
                    try {
                        // 使用正确的节点统计API
                        const nodeResponse = await fetch('/api/admin/nodes/stats');
                        const nodeResult = await nodeResponse.json();
                        if (nodeResult.success && nodeResult.data) {
                            currentInstanceBoundLicense.currentNodes = nodeResult.data.active || 0;
                            // 保留许可证原始的maxNodes，不使用祖父协议调整后的limit
                            // currentInstanceBoundLicense.maxNodes = nodeResult.data.limit || 5;
                            currentInstanceBoundLicense.effectiveMaxNodes = nodeResult.data.limit || 5; // 祖父协议调整后的有效值
                            currentInstanceBoundLicense.isGrandfathered = nodeResult.data.isGrandfathered || false;
                        }
                    } catch (nodeError) {
                        console.warn('获取当前节点数失败:', nodeError);
                    }
                }
                
                console.log(`[License] 许可证列表加载完成，总数: ${allLicenses.length}，过滤后: ${this.licenseList.length}`);
                
                // 修正许可证绑定显示问题
                // 确保只有当前激活的许可证显示为绑定到当前实例
                this.licenseList = this.fixLicenseBindingDisplay(this.licenseList);
                
                // 直接渲染过滤后的许可证
                this.renderLicenseList(this.licenseList);
            } else {
                // 检查是否为token相关错误
                const errorMessage = result.data || result.message || '';
                if (errorMessage.toLowerCase().includes('jwt') || 
                    errorMessage.toLowerCase().includes('token') || 
                    errorMessage.toLowerCase().includes('expired') ||
                    errorMessage.toLowerCase().includes('verification failed')) {
                    console.log('[License] 检测到JWT/Token错误，清除用户会话:', errorMessage);
                    this.currentUser = null;
                    this.updateLoginStatus(false);
                    // 清空列表并直接返回，不抛出错误
                    this.licenseList = [];
                    this.renderLicenseList([]);
                    return;
                }
                
                // 401/403错误不显示给用户
                if (response.status !== 401 && response.status !== 403) {
                    showMessage('获取License列表失败: ' + errorMessage, 'error');
                }
                this.licenseList = [];
                this.renderLicenseList([]);
            }
        } catch (error) {
            console.error('获取License列表失败:', error);
            
            // 检查是否为token/jwt相关错误
            if (error.message && (
                error.message.toLowerCase().includes('jwt') || 
                error.message.toLowerCase().includes('token') || 
                error.message.toLowerCase().includes('expired') ||
                error.message.toLowerCase().includes('verification failed')
            )) {
                console.log('[License] 检测到JWT/Token错误，清除用户会话');
                this.currentUser = null;
                this.updateLoginStatus(false);
                // token错误不显示给用户，也不重新抛出，静默处理
                this.licenseList = [];
                this.renderLicenseList([]);
                return; // 直接返回，不继续执行下面的逻辑
            }
            
            // 只在确实是网络错误等异常情况下显示错误
            if (error.name !== 'AbortError') {
                showMessage('获取License列表失败: ' + error.message, 'error');
            }
            // 清空列表避免显示旧数据
            this.licenseList = [];
            this.renderLicenseList([]);
        }
    }

    applyFiltersAndRender() {
        // 应用搜索和筛选
        this.filteredLicenseList = this.licenseList.filter(license => {
            // 搜索筛选
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                const licenseKey = (license.licenseKey || '').toLowerCase();
                const planName = (license.planName || license.plan_name || '').toLowerCase();

                if (!licenseKey.includes(searchTerm) && !planName.includes(searchTerm)) {
                    return false;
                }
            }

            // 状态筛选
            if (this.filters.status !== 'all') {
                const status = license.licenseStatus || license.status;
                if (status !== this.filters.status) {
                    return false;
                }
            }

            // 套餐类型筛选
            if (this.filters.planType !== 'all') {
                const planName = license.planName || license.plan_name || 'Standard Plan';
                if (planName !== this.filters.planType) {
                    return false;
                }
            }

            // 再次确保不显示免费许可证（双重保险）
            if (this.isFreeLicense(license)) {
                return false;
            }

            return true;
        });

        // 更新分页信息
        this.pagination.totalItems = this.filteredLicenseList.length;
        this.pagination.totalPages = Math.ceil(this.pagination.totalItems / this.pagination.pageSize);

        // 确保当前页面在有效范围内
        if (this.pagination.currentPage > this.pagination.totalPages) {
            this.pagination.currentPage = Math.max(1, this.pagination.totalPages);
        }

        // 渲染当前页面
        this.renderCurrentPage();
        this.renderPagination();
        this.renderFilterSummary();
    }

    renderCurrentPage() {
        const startIndex = (this.pagination.currentPage - 1) * this.pagination.pageSize;
        const endIndex = startIndex + this.pagination.pageSize;
        const currentPageLicenses = this.filteredLicenseList.slice(startIndex, endIndex);

        this.renderLicenseList(currentPageLicenses);
    }

    renderLicenseList(licenses = null) {
        const listContent = document.getElementById('licenseListContent');
        if (!listContent) return;

        // 使用传入的licenses或默认使用全部列表
        const licensesToRender = licenses || this.licenseList;

        if (!licensesToRender || licensesToRender.length === 0) {
            listContent.innerHTML = `
                <div class="col-span-full text-center py-12">
                    <div class="w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                        <i class="ti ti-inbox text-2xl text-slate-400 dark:text-slate-500"></i>
                    </div>
                    <p class="text-slate-500 dark:text-slate-400">暂无可用许可证</p>
                    <p class="text-xs text-slate-400 dark:text-slate-500 mt-1">请登录License Server查看您的许可证</p>
                </div>
            `;
            return;
        }

        const html = licensesToRender.map(license => {
            // 增强状态处理逻辑，支持更多状态类型
            const status = license.licenseStatus || license.status || 'unknown';
            const statusInfo = this.getLicenseStatusInfo(status);
            const planName = license.planName || license.plan_name || 'Standard Plan';
            const isCurrentInstance = license.boundInstanceId === this.instanceId || license.bound_instance_id === this.instanceId;
            const canActivate = (status === 'active' || status === 'bound' || status === 'available' || status === 'valid') && !isCurrentInstance;
            
            // 提取节点信息
            let maxNodes = license.maxNodes || license.max_nodes || license.plan_max_nodes;

            // 如果没有maxNodes，从套餐配置获取
            if (!maxNodes) {
                const planType = (license.planType || license.plan_type || 'standard').toLowerCase();
                // 优先从已加载的配置获取，避免异步调用
                if (this.plansConfig && this.plansConfig[planType]) {
                    maxNodes = this.plansConfig[planType].maxNodes;
                } else {
                    // 如果配置未加载，显示为"未知"
                    console.warn(`[License] 无法获取套餐 ${planType} 的节点限制`);
                    maxNodes = '?';
                }
            }
            
            const currentNodes = license.currentNodes || license.current_nodes || license.plan_current_nodes || 0;
            const boundInstanceId = license.boundInstanceId || license.bound_instance_id || license.instance_id;
            
            // 处理到期时间显示
            let expiryDisplay = '永久有效';
            if (license.expiresAt || license.expires_at) {
                const expiryDate = new Date(license.expiresAt || license.expires_at);
                if (!isNaN(expiryDate.getTime())) {
                    const now = new Date();
                    const diffDays = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
                    
                    if (diffDays < 0) {
                        expiryDisplay = `已过期 ${Math.abs(diffDays)} 天`;
                    } else if (diffDays <= 30) {
                        expiryDisplay = `${diffDays} 天后到期`;
                    } else {
                        expiryDisplay = expiryDate.toLocaleDateString('zh-CN');
                    }
                }
            }
            
            return `
                <div class="compact-card p-5 hover:shadow-lg transition-all duration-300 cursor-pointer group" onclick="licenseManager.selectLicense('${license.licenseKey}')">
                    <!-- 头部：套餐名和状态 -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                                <i class="ti ti-credit-card text-white text-lg"></i>
                            </div>
                            <div>
                                <div class="font-semibold text-slate-700 dark:text-slate-300">${planName}</div>
                                ${isCurrentInstance ? '<div class="text-xs text-blue-600 dark:text-blue-400 font-medium">当前实例</div>' : ''}
                            </div>
                        </div>
                        <span class="status-badge ${this.getStatusBadgeClass(statusInfo.class)}">${statusInfo.text}</span>
                    </div>
                    
                    <!-- 许可证Key显示 - 优化为居中单行显示 -->
                    <div class="mb-4">
                        <div class="text-xs font-medium text-slate-500 dark:text-slate-400 mb-2 text-center">License Key</div>
                        <div class="license-key-container">
                            <div class="license-key-display bg-slate-50 dark:bg-slate-900/50 px-3 py-3 rounded-lg border border-slate-200/60 dark:border-slate-700/40 font-mono text-sm text-center break-all cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800/50 transition-colors" 
                                 onclick="event.stopPropagation(); licenseManager.copyToClipboard('${license.licenseKey}', 'License Key')" 
                                 title="点击复制License Key">
                                ${license.licenseKey}
                                <i class="ti ti-copy text-xs ml-2 text-slate-400"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 实例ID显示 - 可复制 -->
                    ${boundInstanceId ? `
                        <div class="mb-4 p-3 bg-gradient-to-r from-slate-50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-700/30 rounded-lg border border-slate-200/60 dark:border-slate-700/40">
                            <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                                    <i class="ti ti-link text-sm text-slate-500 dark:text-slate-400"></i>
                                    <span class="text-xs text-slate-600 dark:text-slate-400">绑定实例:</span>
                        </div>
                                <code class="text-xs font-mono text-slate-700 dark:text-slate-300 cursor-pointer hover:bg-slate-200 dark:hover:bg-slate-600 px-2 py-1 rounded" 
                                      onclick="event.stopPropagation(); licenseManager.copyToClipboard('${boundInstanceId}', '实例ID')" 
                                      title="点击复制实例ID">
                                    ${boundInstanceId.slice(0, 8)}...${boundInstanceId.slice(-8)}
                                    <i class="ti ti-copy text-xs ml-1 text-slate-400"></i>
                                </code>
                                ${isCurrentInstance ? '<span class="text-xs text-green-600 dark:text-green-400 font-medium ml-2">(当前)</span>' : ''}
                    </div>
                        </div>
                    ` : `
                        <div class="mb-4 p-3 bg-gradient-to-r from-orange-50 to-orange-100/50 dark:from-orange-900/20 dark:to-orange-800/10 rounded-lg border border-orange-200/60 dark:border-orange-700/40">
                            <div class="flex items-center gap-2">
                                <i class="ti ti-unlink text-sm text-orange-500 dark:text-orange-400"></i>
                                <span class="text-xs text-orange-600 dark:text-orange-400">未绑定到任何实例</span>
                            </div>
                        </div>
                    `}
                    
                    <!-- 详细信息网格 -->
                    <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                        <div class="text-center p-3 bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/10 rounded-lg">
                            <div class="text-lg font-bold text-blue-600 dark:text-blue-400">${maxNodes}</div>
                            <div class="text-xs text-blue-500 dark:text-blue-400">最大节点</div>
                        </div>
                        <div class="text-center p-3 bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-900/20 dark:to-green-800/10 rounded-lg">
                            <div class="text-lg font-bold text-green-600 dark:text-green-400">${currentNodes}</div>
                            <div class="text-xs text-green-500 dark:text-green-400">当前节点</div>
                        </div>
                        <div class="col-span-2 text-center p-3 bg-gradient-to-br from-purple-50 to-purple-100/50 dark:from-purple-900/20 dark:to-purple-800/10 rounded-lg">
                            <div class="text-sm font-semibold text-purple-600 dark:text-purple-400">${expiryDisplay}</div>
                            <div class="text-xs text-purple-500 dark:text-purple-400">到期时间</div>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="flex items-center justify-between pt-4 border-t border-slate-200/60 dark:border-slate-700/40">
                        <div class="text-xs text-slate-500 dark:text-slate-400">
                            <i class="ti ti-clock text-sm mr-1"></i>
                            ${license.updatedAt ? this.formatRelativeTime(license.updatedAt) : '未知时间'}
                        </div>
                        ${canActivate ? `
                            <button onclick="event.stopPropagation(); licenseManager.quickActivate('${license.licenseKey}')" 
                                    class="modern-btn modern-btn-primary opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                <i class="ti ti-player-play text-sm"></i>
                                绑定到此实例
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }).join('');

        listContent.innerHTML = html;
        
        // 智能显示自动绑定按钮：只在有多个可用许可证时显示
        this.updateAutoBindButtonVisibility();
    }

    /**
     * 修正许可证绑定显示问题
     * 确保只有当前激活的许可证显示为绑定到当前实例
     */
    fixLicenseBindingDisplay(licenses) {
        // 如果没有当前许可证，则所有许可证都应该显示为未绑定到当前实例
        if (!this.currentLicenseStatus || !this.currentLicenseStatus.licenseKey) {
            return licenses.map(license => {
                const boundTo = license.boundInstanceId || license.bound_instance_id || license.instanceId;
                if (boundTo === this.instanceId) {
                    // 如果显示绑定到当前实例，但实际上没有激活的许可证，则修正为未绑定
                    console.log(`[License] 修正许可证 ${license.licenseKey?.substring(0, 20)}... 的绑定状态（当前无激活许可证）`);
                    return {
                        ...license,
                        boundInstanceId: null,
                        bound_instance_id: null,
                        instanceId: null,
                        isBound: false,
                        canActivate: license.status === 'active' && !license.isExpired,
                        licenseStatus: 'available',
                        displayStatus: '可用'
                    };
                }
                return license;
            });
        }
        
        const currentLicenseKey = this.currentLicenseStatus.licenseKey;
        
        // 如果有当前许可证，确保只有它显示为绑定到当前实例
        return licenses.map(license => {
            const isCurrentLicense = license.licenseKey === currentLicenseKey;
            const boundTo = license.boundInstanceId || license.bound_instance_id || license.instanceId;
            
            if (isCurrentLicense) {
                // 当前激活的许可证应该显示为绑定到当前实例
                if (boundTo !== this.instanceId) {
                    console.log(`[License] 修正当前许可证 ${license.licenseKey?.substring(0, 20)}... 的绑定状态为已绑定`);
                }
                return {
                    ...license,
                    boundInstanceId: this.instanceId,
                    bound_instance_id: this.instanceId,
                    instanceId: this.instanceId,
                    isBound: true,
                    canActivate: false,
                    licenseStatus: 'bound',
                    displayStatus: '已绑定'
                };
            } else if (boundTo === this.instanceId) {
                // 其他显示绑定到当前实例的许可证应该修正为未绑定
                console.log(`[License] 修正许可证 ${license.licenseKey?.substring(0, 20)}... 的绑定状态为未绑定（不是当前激活的许可证）`);
                return {
                    ...license,
                    boundInstanceId: null,
                    bound_instance_id: null,
                    instanceId: null,
                    isBound: false,
                    canActivate: license.status === 'active' && !license.isExpired,
                    licenseStatus: 'available',
                    displayStatus: '可用'
                };
            }
            
            // 保持其他许可证的状态不变
            return license;
        });
    }

    /**
     * 更新自动绑定按钮的显示状态
     */
    updateAutoBindButtonVisibility() {
        const autoBindBtn = document.getElementById('autoBindBtn');
        if (!autoBindBtn) return;
        
        // 计算可绑定的许可证数量
        const availableLicenses = this.licenseList.filter(license => {
            const status = license.licenseStatus || license.status || 'unknown';
            const isCurrentInstance = license.boundInstanceId === this.instanceId || license.bound_instance_id === this.instanceId;
            return (status === 'active' || status === 'bound' || status === 'available' || status === 'valid') && !isCurrentInstance;
        });
        
        // 只在有多个可用许可证时显示按钮
        if (availableLicenses.length > 1) {
            autoBindBtn.style.display = 'inline-flex';
        } else {
            autoBindBtn.style.display = 'none';
        }
    }

    getStatusBadgeClass(statusClass) {
        const classMap = {
            'status-active': 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800',
            'status-bound': 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-800',
            'status-inactive': 'bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400 border-slate-200 dark:border-slate-700',
            'status-error': 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800',
            'status-warning': 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800'
        };
        return classMap[statusClass] || 'bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400 border-slate-200 dark:border-slate-700';
    }

    // 添加相对时间格式化方法
    formatRelativeTime(timestamp) {
        if (!timestamp) return '未知时间';
        
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now - date;
        
        const diffSecs = Math.floor(diffMs / 1000);
        const diffMins = Math.floor(diffSecs / 60);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);
        
        if (diffDays > 7) {
            return date.toLocaleDateString('zh-CN');
        } else if (diffDays > 0) {
            return `${diffDays}天前`;
        } else if (diffHours > 0) {
            return `${diffHours}小时前`;
        } else if (diffMins > 0) {
            return `${diffMins}分钟前`;
        } else {
            return '刚刚更新';
        }
    }

    async quickActivate(licenseKey) {
        try {
            showMessage('正在激活License...', 'info');
            
            const response = await fetch('/admin/api/license-enhanced/user/quick-activate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    licenseKey: licenseKey
                })
            });

            const result = await response.json();

            if (result.status === 1) {
                showMessage('License激活成功!', 'success');

                // 检查是否需要立即刷新权限
                const shouldRefreshPermissions = result.data?.shouldRefreshPermissions || result.shouldRefreshPermissions;
                
                if (shouldRefreshPermissions) {
                    console.log('[License] 检测到权限刷新标记，立即刷新功能墙权限...');
                    // 立即刷新功能墙权限，无延时
                    if (window.FeatureWall) {
                        try {
                            await window.FeatureWall.manualRefresh();
                            console.log('[License] 功能墙权限已立即刷新');
                        } catch (error) {
                            console.warn('[License] 立即刷新功能墙权限失败:', error);
                        }
                    }
                }

                // 等待后端状态同步完成，然后刷新其他状态
                console.log('[License] 快速激活成功，等待后端状态同步...');
                setTimeout(async () => {
                    try {
                        // 1. 刷新当前License状态
                        await this.loadCurrentLicenseStatus();

                        // 2. 刷新模块状态（确保节点数等信息更新）
                        await this.loadModuleStatus();

                        // 3. 刷新License列表
                        try {
                            await this.loadLicenseList();
                        } catch (loadError) {
                            console.warn('[License] 快速激活后刷新列表失败:', loadError);
                        }

                        // 4. 如果功能墙还没有刷新过，再次触发刷新（保险机制）
                        if (!shouldRefreshPermissions && window.FeatureWall) {
                            await window.FeatureWall.manualRefresh();
                        }

                        // 5. 触发全局许可证激活事件
                        window.dispatchEvent(new CustomEvent('licenseActivated', {
                            detail: { licenseKey: licenseKey }
                        }));

                        console.log('[License] 快速激活后状态刷新完成');
                    } catch (error) {
                        console.error('[License] 快速激活后状态刷新失败:', error);
                    }
                }, 1000); // 等待1秒让后端完成状态同步
            } else {
                showMessage('License激活失败: ' + result.data, 'error');
            }
        } catch (error) {
            console.error('License激活失败:', error);
            showMessage('License激活失败: ' + error.message, 'error');
        }
    }

    async refreshLicenseList() {
        const refreshBtn = document.getElementById('refreshLicenseListBtn');
        if (refreshBtn) {
            refreshBtn.innerHTML = '<i class="ti ti-refresh text-sm animate-spin"></i>';
        }
        
        try {
            await this.loadLicenseList();
        } catch (loadError) {
            console.warn('[License] 刷新许可证列表失败:', loadError);
            // 如果是token错误，用户状态已经在loadLicenseList中处理
        }
        
        if (refreshBtn) {
            refreshBtn.innerHTML = '<i class="ti ti-refresh text-sm"></i>';
        }
    }

    async checkManualLicense() {
        const licenseKeyInput = document.getElementById('licenseKeyInput');
        if (!licenseKeyInput) return;

        const licenseKey = licenseKeyInput.value.trim();
        if (!licenseKey) {
            showMessage('请输入License Key', 'error');
            return;
        }

        try {
            const response = await fetch('/admin/api/license-enhanced/check', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ licenseKey })
            });

            const result = await response.json();

            if (result.status === 1) {
                showMessage('License状态检查成功', 'success');
                console.log('License状态:', result.data);
            } else {
                showMessage('License检查失败: ' + result.data, 'error');
            }
        } catch (error) {
            console.error('License检查失败:', error);
            showMessage('License检查失败: ' + error.message, 'error');
        }
    }

    async activateManualLicense() {
        const licenseKeyInput = document.getElementById('licenseKeyInput');
        if (!licenseKeyInput) return;

        const licenseKey = licenseKeyInput.value.trim();
        if (!licenseKey) {
            showMessage('请输入License Key', 'error');
            return;
        }

        try {
            const response = await fetch('/admin/api/license-enhanced/activate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    licenseKey,
                    forceTransfer: true // 默认启用自动转移，支持已绑定许可证的重新激活
                })
            });

            const result = await response.json();

            if (result.status === 1) {
                showMessage('License激活成功!', 'success');

                // 清空输入框
                licenseKeyInput.value = '';

                // 检查是否需要立即刷新权限
                const shouldRefreshPermissions = result.data?.shouldRefreshPermissions || result.shouldRefreshPermissions;
                
                if (shouldRefreshPermissions) {
                    console.log('[License] 检测到权限刷新标记，立即刷新功能墙权限...');
                    // 立即刷新功能墙权限，无延时
                    if (window.FeatureWall) {
                        try {
                            await window.FeatureWall.manualRefresh();
                            console.log('[License] 功能墙权限已立即刷新');
                        } catch (error) {
                            console.warn('[License] 立即刷新功能墙权限失败:', error);
                        }
                    }
                }

                // 等待后端状态同步完成，然后刷新其他状态
                console.log('[License] 等待后端状态同步...');
                setTimeout(async () => {
                    try {
                        // 刷新当前License状态
                        await this.loadCurrentLicenseStatus();

                        // 如果功能墙还没有刷新过，再次触发刷新（保险机制）
                        if (!shouldRefreshPermissions && window.FeatureWall) {
                            await window.FeatureWall.manualRefresh();
                        }

                        // 触发全局许可证激活事件
                        window.dispatchEvent(new CustomEvent('licenseActivated', {
                            detail: { licenseKey: licenseKey }
                        }));

                        console.log('[License] 激活后状态刷新完成');
                    } catch (error) {
                        console.error('[License] 激活后状态刷新失败:', error);
                    }
                }, 1000); // 等待1秒让后端完成状态同步
            } else {
                showMessage('License激活失败: ' + result.data, 'error');
            }
        } catch (error) {
            console.error('License激活失败:', error);
            showMessage('License激活失败: ' + error.message, 'error');
        }
    }

    /**
     * 全局刷新处理函数
     * 统一的刷新逻辑，包含加载指示器和完整的状态更新
     */
    async handleGlobalRefresh() {
        try {
            console.log('[License] 开始全局刷新...');
            
            // 显示加载指示器
            if (typeof startloading === 'function') {
                startloading();
            }
            
            this.showMessage('正在刷新状态...', 'info');
            
            // 重新初始化整个页面状态
            await this.initializePage();
            
            // 更新最后更新时间
            const timeElement = document.getElementById('lastUpdateTime');
            if (timeElement) {
                timeElement.textContent = new Date().toLocaleTimeString();
            }
            
            this.showMessage('状态已刷新', 'success');
            console.log('[License] 全局刷新完成');
            
        } catch (error) {
            console.error('[License] 全局刷新失败:', error);
            this.showMessage('刷新失败: ' + error.message, 'error');
        } finally {
            // 隐藏加载指示器
            if (typeof endloading === 'function') {
                endloading();
            }
        }
    }

    async refreshCurrentStatus() {
        try {
            console.log('[License] 开始刷新当前许可证状态...');
            showMessage('正在刷新状态...', 'info');

            const response = await fetch('/admin/api/license-enhanced/refresh', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.status === 1) {
                showMessage('状态刷新成功', 'success');

                // 刷新许可证状态
                await this.loadCurrentLicenseStatus();

                // 刷新模块状态（包括节点限制信息）
                await this.loadModuleStatus();

                // 触发功能墙刷新
                if (window.FeatureWall) {
                    await window.FeatureWall.manualRefresh();
                }

                // 触发节点状态刷新事件
                window.dispatchEvent(new CustomEvent('licenseActivated', {
                    detail: { action: 'manual_refresh' }
                }));

                // 更新最后更新时间
                const timeElement = document.getElementById('lastUpdateTime');
                if (timeElement) {
                    timeElement.textContent = new Date().toLocaleTimeString();
                }

                console.log('[License] 许可证状态刷新完成');
            } else {
                console.warn('[License] 状态刷新失败:', result.data);
                showMessage('状态刷新失败: ' + result.data, 'error');
            }
        } catch (error) {
            console.error('[License] 状态刷新失败:', error);
            showMessage('状态刷新失败: ' + error.message, 'error');
        }
    }

    async refreshPlansConfig() {
        try {
            showMessage('正在刷新套餐配置...', 'info');
            
            // 调用新的配置刷新API
            const response = await fetch('/admin/api/license-enhanced/refresh-config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.status === 1) {
                showMessage('套餐配置已刷新', 'success');
                
                // 清除本地缓存
                this.plansConfig = null;
                this.plansConfigLoadTime = 0;
                
                // 重新加载配置
                await this.loadPlansConfig();
                await this.loadCurrentLicenseStatus();
                
                // 如果有许可证列表，也刷新它
                if (this.currentUser) {
                    await this.loadLicenseList();
                }
            } else {
                showMessage('配置刷新失败: ' + result.data, 'error');
            }
        } catch (error) {
            console.error('配置刷新失败:', error);
            showMessage('配置刷新失败: ' + error.message, 'error');
        }
    }

    async unbindCurrentLicense() {
        if (!this.currentLicenseStatus || !this.currentLicenseStatus.licenseKey) {
            showMessage('无当前License', 'error');
            return;
        }

        if (!confirm('确定要解绑当前License吗？解绑后需要重新激活。')) {
            return;
        }

        try {
            showMessage('正在解绑License...', 'info');

            const response = await fetch('/admin/api/license-enhanced/unbind', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    licenseKey: this.currentLicenseStatus.licenseKey
                })
            });

            const result = await response.json();

            if (result.status === 1) {
                // 根据不同的解绑方式显示不同的成功消息
                let successMessage = 'License解绑成功';
                if (result.data && typeof result.data === 'string') {
                    if (result.data.includes('智能解绑')) {
                        successMessage = '智能解绑成功 - 已自动处理绑定状态不一致问题';
                    } else if (result.data.includes('本地状态已同步')) {
                        successMessage = '解绑成功 - 许可证状态已同步';
                    } else if (result.data.includes('用户API')) {
                        successMessage = '解绑成功 - 已通过用户API完成解绑';
                    }
                }

                showMessage(successMessage, 'success');

                // 刷新当前License状态
                await this.loadCurrentLicenseStatus();

                // 如果用户已登录，也刷新License列表
                if (this.currentUser) {
                    try {
                        await this.loadLicenseList();
                    } catch (loadError) {
                        console.warn('[License] 解绑后刷新列表失败:', loadError);
                    }
                }
            } else {
                showMessage('License解绑失败: ' + result.data, 'error');
            }
        } catch (error) {
            console.error('License解绑失败:', error);
            showMessage('License解绑失败: ' + error.message, 'error');
        }
    }

    selectLicense(licenseKey) {
        console.log('选择License:', licenseKey);
        // 可以添加选择License的逻辑
    }

    showMessage(message, type = 'info') {
        // 使用全局通知函数
        if (typeof notice === 'function') {
            const typeMap = {
                'success': 1,
                'error': 0,
                'warning': 2,
                'info': 3
            };
            notice(message, typeMap[type] || 3);
        }
        
        // 输出到控制台
        console.log(`[${type.toUpperCase()}] ${message}`);
    }

    /**
     * 生成升级按钮HTML
     */
    generateUpgradeButton(planType, specialOffer = null) {
        const upgradeUrl = '/upgrade';
        const params = new URLSearchParams();
        
        if (planType && planType !== 'enterprise') {
            const nextPlans = {
                'free': 'standard',
                'standard': 'pro', 
                'pro': 'enterprise'
            };
            params.append('plan', nextPlans[planType] || 'standard');
        }
        
        params.append('source', 'license_management');
        
        if (specialOffer && specialOffer.code) {
            params.append('code', specialOffer.code);
        }
        
        const fullUrl = upgradeUrl + (params.toString() ? '?' + params.toString() : '');
        const discountText = specialOffer ? ` (${specialOffer.discount}%折扣)` : '';
        
        return `
            <a href="${fullUrl}" 
               class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
               target="_blank">
                <i class="ti ti-arrow-up text-sm"></i>
                升级套餐${discountText}
            </a>
        `;
    }

    renderPagination() {
        const paginationContainer = document.getElementById('licensePagination');
        if (!paginationContainer) return;

        if (this.pagination.totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        const currentPage = this.pagination.currentPage;
        const totalPages = this.pagination.totalPages;

        let paginationHtml = '<div class="flex items-center justify-center gap-2">';

        // 上一页按钮
        if (currentPage > 1) {
            paginationHtml += `
                <button class="page-btn px-3 py-2 text-sm bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700" data-page="${currentPage - 1}">
                    <i class="ti ti-chevron-left text-sm"></i>
                </button>
            `;
        }

        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            paginationHtml += `<button class="page-btn px-3 py-2 text-sm bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700" data-page="1">1</button>`;
            if (startPage > 2) {
                paginationHtml += '<span class="px-2 text-slate-500">...</span>';
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === currentPage;
            paginationHtml += `
                <button class="page-btn px-3 py-2 text-sm ${isActive ? 'bg-blue-600 text-white' : 'bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700'} rounded-lg" data-page="${i}">
                    ${i}
                </button>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHtml += '<span class="px-2 text-slate-500">...</span>';
            }
            paginationHtml += `<button class="page-btn px-3 py-2 text-sm bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700" data-page="${totalPages}">${totalPages}</button>`;
        }

        // 下一页按钮
        if (currentPage < totalPages) {
            paginationHtml += `
                <button class="page-btn px-3 py-2 text-sm bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700" data-page="${currentPage + 1}">
                    <i class="ti ti-chevron-right text-sm"></i>
                </button>
            `;
        }

        paginationHtml += '</div>';
        paginationContainer.innerHTML = paginationHtml;
    }

    renderFilterSummary() {
        const summaryContainer = document.getElementById('licenseFilterSummary');
        if (!summaryContainer) return;

        const totalLicenses = this.licenseList.length;
        const filteredLicenses = this.filteredLicenseList.length;
        const currentPageStart = (this.pagination.currentPage - 1) * this.pagination.pageSize + 1;
        const currentPageEnd = Math.min(currentPageStart + this.pagination.pageSize - 1, filteredLicenses);

        let summaryText = `显示 ${currentPageStart}-${currentPageEnd} 项，共 ${filteredLicenses} 项`;

        if (filteredLicenses !== totalLicenses) {
            summaryText += ` (从 ${totalLicenses} 项中筛选)`;
        }

        summaryContainer.innerHTML = `
            <div class="text-sm text-slate-600 dark:text-slate-400">
                ${summaryText}
            </div>
        `;
    }

    async copyToClipboard(text, label) {
        try {
            // 尝试使用现代API
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(text);
                showMessage(`${label}已复制到剪贴板`, 'success');
            } else {
                // 回退到传统方法
                const tempInput = document.createElement('input');
                tempInput.value = text;
                document.body.appendChild(tempInput);
                tempInput.select();
                tempInput.setSelectionRange(0, 99999); // 对移动设备友好
                document.execCommand('copy');
                document.body.removeChild(tempInput);
                showMessage(`${label}已复制到剪贴板`, 'success');
            }
            
            // 添加视觉反馈 - 为触发复制的元素添加动画
            const triggerElement = event && event.target ? event.target.closest('.license-key-display, code') : null;
            if (triggerElement) {
                triggerElement.classList.add('copy-success');
                setTimeout(() => triggerElement.classList.remove('copy-success'), 300);
            }
            
        } catch (error) {
            console.error('复制失败:', error);
            showMessage('复制失败，请手动选择复制', 'error');
        }
    }




    
    // 调试用户信息的方法
    async debugUserInfo() {
        try {
            const response = await fetch('/admin/api/license-enhanced/debug/user-info');
            const result = await response.json();
            
            if (result.status === 1) {
                console.log('=== 调试信息 ===');
                console.log('服务器状态:', result.data.serverStatus);
                console.log('当前License:', result.data.currentLicense);
                console.log('用户Licenses:', result.data.userLicenses);
                console.log('================');
                
                // 显示调试信息
                showMessage('调试信息已输出到控制台', 'info');
            } else {
                console.error('获取调试信息失败:', result.data);
                showMessage('获取调试信息失败', 'error');
            }
        } catch (error) {
            console.error('调试信息获取异常:', error);
            showMessage('调试信息获取异常', 'error');
        }
    }

    // 移除了updateNodeStats方法 - 节点数据现在通过license-enhanced status API统一获取
    
    // 节点统计相关的方法已被移除 - 功能已迁移到 license-enhanced 模块统一处理

    /**
     * 记录用户操作（空实现，防止调用错误）
     * @param {string} operation - 操作类型
     * @param {object} data - 操作数据
     */
    recordUserOperation(operation, data) {
        // 简单的操作记录，可以在控制台看到
        console.log('[License] 用户操作:', operation, data);
        
        // 如果需要持久化记录，可以在这里添加代码
        // 例如发送到服务器或存储到本地存储
    }

    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (info, success, error, warning)
     */
    showMessage(message, type = 'info') {
        // 使用全局的 showMessage 函数
        if (typeof showMessage === 'function') {
            showMessage(message, type);
        } else {
            // 备用方案：直接在控制台输出
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
    
}

// DOM ready event
document.addEventListener('DOMContentLoaded', function() {
    window.licenseManager = new LicenseManagement();
    console.log('[License] 页面已加载，许可证管理器已初始化');
}); 
