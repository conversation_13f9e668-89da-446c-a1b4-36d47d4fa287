/**
 * AutoDiscovery管理器 - iPhone风格优化版本
 * 负责自动发现功能的前端交互逻辑
 */
class AutoDiscoveryManager {
    constructor() {
        this.currentServerId = null;
        this.refreshTimer = null;
        this.countdownTimer = null;
        
        // 分页相关
        this.allDiscoveredServers = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.searchKeyword = '';
        
        // 分组缓存
        this.groups = [];
        
        // 配置
        this.config = {
            refreshInterval: 30000, // 30秒刷新间隔
            apiTimeout: 10000, // API超时时间
            maxRetries: 3 // 最大重试次数
        };
        
        this.init();
    }

    /**
     * 初始化管理器
     */
    async init() {
        try {
            this.setupEventListeners();
            this.loadUserPreferences();
            await this.loadInitialData();
            await this.loadGroups();
            this.startAutoRefresh();
            this.updateRefreshIndicator();
        } catch (error) {
            console.error('AutoDiscovery管理器初始化失败:', error);
            this.showNotice('初始化失败', 'error');
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 搜索框回车事件
        const searchInput = document.getElementById('serverSearchInput');
        if (searchInput) {
            searchInput.addEventListener('keyup', (event) => {
                if (event.key === 'Enter') {
                    this.searchServers();
                }
            });
        }

        // 页面离开时清理
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });

        // 快捷键保存
        document.addEventListener("keydown", (e) => {
            if ((window.navigator.platform.match("Mac") ? e.metaKey : e.ctrlKey) && e.keyCode == 83) {
                e.preventDefault();
                this.saveSettings();
            }
        });
    }

    /**
     * 加载用户偏好设置
     */
    loadUserPreferences() {
        const savedPageSize = localStorage.getItem('autodiscovery_page_size');
        if (savedPageSize) {
            this.pageSize = parseInt(savedPageSize);
            const pageSizeSelect = document.getElementById('pageSize');
            if (pageSizeSelect) {
                pageSizeSelect.value = this.pageSize;
            }
        }
    }

    /**
     * 加载初始数据
     */
    async loadInitialData() {
        await Promise.all([
            this.refreshPendingServers(),
            this.refreshDiscoveredServers()
        ]);
    }

    /**
     * 加载分组数据
     */
    async loadGroups() {
        try {
            const response = await this.apiRequest('/api/groups');
            const result = await response.json();
            if (this.isSuccessResponse(result)) {
                this.groups = result.data || [];
                console.log('加载分组数据成功:', this.groups.length, '个分组');
            }
        } catch (error) {
            console.error('加载分组数据失败:', error);
            this.groups = [];
        }
    }

    /**
     * 启动自动刷新
     */
    startAutoRefresh() {
        this.refreshTimer = setInterval(async () => {
            try {
                await Promise.all([
                    this.refreshPendingServers(),
                    this.refreshDiscoveredServers()
                ]);
                console.log('自动刷新完成 -', new Date().toLocaleTimeString());
                this.updateRefreshIndicator();
            } catch (error) {
                console.error('自动刷新失败:', error);
            }
        }, this.config.refreshInterval);
    }

    /**
     * 更新刷新状态指示器
     */
    updateRefreshIndicator() {
        const lastRefreshTime = document.getElementById('lastRefreshTime');
        const nextRefreshTime = document.getElementById('nextRefreshTime');

        if (lastRefreshTime) {
            lastRefreshTime.textContent = new Date().toLocaleTimeString();
        }

        // 更新倒计时
        let countdown = 30;
        if (nextRefreshTime) {
            const updateCountdown = () => {
                countdown--;
                if (countdown <= 0) {
                    countdown = 30;
                }
                nextRefreshTime.textContent = `${countdown}秒后`;
            };

            // 清除之前的倒计时
            if (this.countdownTimer) {
                clearInterval(this.countdownTimer);
            }

            updateCountdown();
            this.countdownTimer = setInterval(updateCountdown, 1000);
        }
    }

    /**
     * 刷新待审核服务器列表
     */
    async refreshPendingServers() {
        const container = document.getElementById('pendingServersContainer');
        const noDataMessage = document.getElementById('noPendingMessage');
        
        if (!container) return;

        try {
            // 显示加载状态
            this.showLoadingState(container, 'orange');

            const response = await this.apiRequest('/admin/autodiscovery/pending');
            const result = await response.json();

            if (this.isSuccessResponse(result) && result.data?.length > 0) {
                container.innerHTML = '';
                noDataMessage?.classList.add('hidden');

                result.data.forEach(server => {
                    container.innerHTML += this.createServerCard(server, true);
                });

                // 显示待审核卡片
                document.getElementById('pendingServersCard').style.display = 'block';
            } else {
                container.innerHTML = '';
                noDataMessage?.classList.remove('hidden');
            }
        } catch (error) {
            console.error('获取待审核服务器失败:', error);
            this.showNotice('获取待审核服务器失败', 'error');
            this.showErrorState(container, '加载失败');
        }
    }

    /**
     * 刷新已发现服务器列表
     */
    async refreshDiscoveredServers() {
        const container = document.getElementById('discoveredServersContainer');
        const noDataMessage = document.getElementById('noDiscoveredMessage');
        
        if (!container) return;

        try {
            // 显示加载状态
            this.showLoadingState(container, 'green');

            const response = await this.apiRequest('/admin/autodiscovery/discovered');
            const result = await response.json();

            if (this.isSuccessResponse(result) && result.data?.length > 0) {
                this.allDiscoveredServers = result.data;
                document.getElementById('paginationControls')?.classList.remove('hidden');
                this.renderDiscoveredServers();
            } else {
                this.allDiscoveredServers = [];
                container.innerHTML = '';
                noDataMessage?.classList.remove('hidden');
                document.getElementById('paginationControls')?.classList.add('hidden');
            }
        } catch (error) {
            console.error('获取已发现服务器失败:', error);
            this.showNotice('获取已发现服务器失败', 'error');
            this.showErrorState(container, '加载失败');
        }
    }

    /**
     * 渲染已发现服务器列表
     */
    renderDiscoveredServers() {
        const container = document.getElementById('discoveredServersContainer');
        const noDataMessage = document.getElementById('noDiscoveredMessage');
        
        if (!container) return;

        // 过滤数据
        let filteredServers = this.allDiscoveredServers;
        if (this.searchKeyword) {
            const keyword = this.searchKeyword.toLowerCase();
            filteredServers = this.allDiscoveredServers.filter(server =>
                this.matchesSearchKeyword(server, keyword)
            );
        }

        // 分页计算
        const totalPages = Math.ceil(filteredServers.length / this.pageSize);
        if (this.currentPage > totalPages) {
            this.currentPage = totalPages || 1;
        }

        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = Math.min(startIndex + this.pageSize, filteredServers.length);
        const currentPageServers = filteredServers.slice(startIndex, endIndex);

        // 更新分页信息
        this.updatePaginationInfo(filteredServers.length, startIndex, endIndex, totalPages);

        // 渲染服务器列表
        if (currentPageServers.length > 0) {
            container.innerHTML = '';
            noDataMessage?.classList.add('hidden');

            currentPageServers.forEach(server => {
                container.innerHTML += this.createServerCard(server, false);
            });
        } else {
            container.innerHTML = '';
            noDataMessage?.classList.remove('hidden');
        }
    }

    /**
     * 检查服务器是否匹配搜索关键词
     */
    matchesSearchKeyword(server, keyword) {
        const searchFields = [
            server.name,
            server.hostname,
            server.ip,
            server.system,
            server.data?.ssh?.host
        ];
        
        return searchFields.some(field => 
            field && field.toLowerCase().includes(keyword)
        );
    }

    /**
     * 更新分页信息
     */
    updatePaginationInfo(totalCount, startIndex, endIndex, totalPages) {
        const elements = {
            currentPage: document.getElementById('currentPage'),
            currentRange: document.getElementById('currentRange'),
            totalServers: document.getElementById('totalServers'),
            prevPageBtn: document.getElementById('prevPageBtn'),
            nextPageBtn: document.getElementById('nextPageBtn'),
            paginationControls: document.getElementById('paginationControls')
        };

        if (elements.currentPage) elements.currentPage.textContent = this.currentPage;
        if (elements.currentRange) elements.currentRange.textContent = totalCount > 0 ? `${startIndex + 1}-${endIndex}` : '0-0';
        if (elements.totalServers) elements.totalServers.textContent = totalCount;

        if (elements.prevPageBtn) elements.prevPageBtn.disabled = this.currentPage <= 1;
        if (elements.nextPageBtn) elements.nextPageBtn.disabled = this.currentPage >= totalPages;

        if (elements.paginationControls) {
            elements.paginationControls.classList.toggle('hidden', totalCount === 0);
        }
    }

    /**
     * 创建服务器卡片HTML
     */
    createServerCard(server, isPending) {
        if (!server) return '';

        const data = server.data || {};
        const ssh = data.ssh || {};
        const discoveryTime = data.discoveryTime ? new Date(data.discoveryTime).toLocaleString() : '未知';
        const isOnline = server.stat && !server.stat.offline;
        const serverId = server._id || server.id || '';

        const statusHtml = isOnline ?
            '<span class="text-green-500 flex items-center"><i class="ti ti-circle-check"></i>在线</span>' :
            '<span class="text-red-500 flex items-center"><i class="ti ti-alert-circle"></i>离线</span>';

        const deleteButton = !isPending ?
            `<button onclick="autodiscoveryManager.deleteServer('${serverId}')" class="ml-2 text-red-500 hover:text-red-400 p-1 rounded transition-colors" title="删除服务器">
                <i class="ti ti-trash"></i>
            </button>` : '';

        return `
            <div onclick="autodiscoveryManager.viewServerDetail('${serverId}', ${isPending})" 
                 class="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg p-4 hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors cursor-pointer shadow-sm">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-10 h-10 bg-slate-100 dark:bg-slate-700 rounded-lg flex items-center justify-center">
                            <i class="ti ti-device-desktop"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-base font-medium text-slate-900 dark:text-white">${server.name || server.hostname || '未命名服务器'}</h4>
                            <p class="text-sm text-slate-500 dark:text-slate-400">${ssh.host || server.ip || '未知IP'}</p>
                        </div>
                    </div>
                    <div class="flex items-center">
                        ${statusHtml}
                        <button onclick="event.stopPropagation(); autodiscoveryManager.viewServerDetail('${serverId}', ${isPending})" 
                                class="ml-3 text-blue-500 hover:text-blue-700 dark:hover:text-blue-400 p-1 rounded transition-colors" title="查看详情">
                            <i class="ti ti-eye"></i>
                        </button>
                        ${deleteButton}
                    </div>
                </div>
                <div class="mt-3 grid grid-cols-2 gap-2 text-sm">
                    <div class="text-slate-500 dark:text-slate-400">系统: <span class="text-slate-700 dark:text-slate-300">${data.system || server.system || '未知'}</span></div>
                    <div class="text-slate-500 dark:text-slate-400">发现时间: <span class="text-slate-700 dark:text-slate-300">${discoveryTime}</span></div>
                </div>
            </div>
        `;
    }

    /**
     * 显示加载状态
     */
    showLoadingState(container, color = 'blue') {
        container.innerHTML = `
            <div class="flex items-center justify-center py-8">
                <div class="w-8 h-8 border-4 border-${color}-500 border-t-transparent rounded-full animate-spin"></div>
                <span class="ml-3 text-slate-500 dark:text-slate-400">加载中...</span>
            </div>
        `;
    }

    /**
     * 显示错误状态
     */
    showErrorState(container, message) {
        container.innerHTML = `
            <div class="text-center py-12">
                <div class="flex flex-col items-center gap-4">
                    <div class="p-4 bg-gradient-to-br from-red-100/80 to-red-200/60 dark:from-red-900/60 dark:to-red-800/40 rounded-2xl border border-red-200/50 dark:border-red-700/30">
                        <i class="ti ti-alert-circle"></i>
                    </div>
                    <div>
                        <h4 class="text-lg font-medium text-slate-600 dark:text-slate-300">${message}</h4>
                        <p class="text-sm text-slate-500 dark:text-slate-400 mt-1">请稍后重试</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * API请求封装
     */
    async apiRequest(url, options = {}) {
        const timestamp = new Date().getTime();
        const urlWithTimestamp = url.includes('?') ? `${url}&t=${timestamp}` : `${url}?t=${timestamp}`;
        
        const defaultOptions = {
            timeout: this.config.apiTimeout,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        return fetch(urlWithTimestamp, { ...defaultOptions, ...options });
    }

    /**
     * 检查响应是否成功
     */
    isSuccessResponse(result) {
        return result.success === true || result.code === 1;
    }

    /**
     * 显示通知
     */
    showNotice(message, type = 'info') {
        if (typeof notice === 'function') {
            notice(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    /**
     * 搜索服务器
     */
    searchServers() {
        const searchInput = document.getElementById('serverSearchInput');
        if (searchInput) {
            this.searchKeyword = searchInput.value.trim();
            this.currentPage = 1;
            this.renderDiscoveredServers();
        }
    }

    /**
     * 更改每页显示数量
     */
    changePageSize() {
        const pageSizeSelect = document.getElementById('pageSize');
        if (pageSizeSelect) {
            this.pageSize = parseInt(pageSizeSelect.value);
            this.currentPage = 1;
            localStorage.setItem('autodiscovery_page_size', this.pageSize);
            this.renderDiscoveredServers();
        }
    }

    /**
     * 前往上一页
     */
    goToPrevPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.renderDiscoveredServers();
        }
    }

    /**
     * 前往下一页
     */
    goToNextPage() {
        const filteredServers = this.searchKeyword ?
            this.allDiscoveredServers.filter(server =>
                this.matchesSearchKeyword(server, this.searchKeyword.toLowerCase())
            ) : this.allDiscoveredServers;

        const totalPages = Math.ceil(filteredServers.length / this.pageSize);
        
        if (this.currentPage < totalPages) {
            this.currentPage++;
            this.renderDiscoveredServers();
        }
    }

    /**
     * 查看服务器详情
     */
    async viewServerDetail(serverId, isPending = false) {
        try {
            window.isPendingServer = isPending;
            
            const endpoint = isPending ? 
                `/admin/autodiscovery/pending/${serverId}` : 
                `/admin/autodiscovery/server/${serverId}`;
            
            const response = await this.apiRequest(endpoint);
            
            if (!response.ok) {
                throw new Error(`服务器返回错误: ${response.status}`);
            }

            const result = await response.json();

            if (this.isSuccessResponse(result) && result.data) {
                this.showServerDetailModal(result.data, isPending);
            } else {
                this.showNotice('获取服务器详情失败', 'error');
            }
        } catch (error) {
            console.error('查看服务器详情失败:', error);
            this.showNotice('查看服务器详情失败', 'error');
        }
    }

    /**
     * 显示服务器详情模态框
     */
    showServerDetailModal(server, isPending) {
        const modal = document.getElementById('serverDetailModal');
        const content = document.getElementById('serverDetailContent');
        const modalTitle = document.getElementById('modalTitle');
        const approveButton = document.getElementById('approveButton');
        const rejectButton = document.getElementById('rejectButton');

        if (!modal || !content) return;

        // 设置标题
        if (modalTitle) {
            modalTitle.textContent = server.name || server.hostname || '服务器详情';
        }

        // 生成详情内容
        content.innerHTML = this.generateServerDetailHtml(server);

        // 显示/隐藏审核按钮
        if (isPending) {
            if (approveButton) {
                approveButton.classList.remove('hidden');
                approveButton.onclick = () => this.approveServer(server._id || server.id);
            }
            if (rejectButton) {
                rejectButton.classList.remove('hidden');
                rejectButton.onclick = () => this.rejectServer(server._id || server.id);
            }
        } else {
            approveButton?.classList.add('hidden');
            rejectButton?.classList.add('hidden');
        }

        // 显示弹窗
        modal.classList.remove('hidden');
        modal.classList.add('flex');
    }

    /**
     * 生成服务器详情HTML
     */
    generateServerDetailHtml(server) {
        if (!server) return '<div class="text-red-500">无法加载服务器详情</div>';

        const data = server.data || {};
        const ssh = data.ssh || {};
        const api = data.api || {};
        const discoveryTime = data.discoveryTime ? new Date(data.discoveryTime).toLocaleString() : '未知';
        const lastOnline = server.status?.last_online ? new Date(server.status.last_online).toLocaleString() : '未知';
        const isOnline = server.stat && !server.stat.offline;

        // 生成分组选择器HTML
        let groupSelectHtml = '';
        if (window.isPendingServer) {
            // 生成分组选项
            let groupOptions = '';
            if (this.groups && this.groups.length > 0) {
                // 有分组数据，动态生成选项
                this.groups.forEach(group => {
                    const groupId = group.id || group._id || group.name;
                    const groupName = group.name || groupId;
                    const selected = groupId === 'default' ? 'selected' : '';
                    groupOptions += `<option value="${groupId}" ${selected}>${groupName}</option>`;
                });
            } else {
                // 没有分组数据，使用默认选项
                groupOptions = '<option value="default">默认分组</option>';
            }
            
            groupSelectHtml = `
                <div class="mt-4 p-4 bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 rounded-xl border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm">
                    <h4 class="text-sm font-medium text-slate-600 dark:text-slate-300 mb-2 flex items-center gap-2">
                        <i class="ti ti-users"></i>
                        选择分组
                    </h4>
                    <p class="text-xs text-slate-500 dark:text-slate-400 mb-3">选择要将此服务器添加到的分组，这将影响服务器在仪表板中的分类和管理方式。</p>
                    <select id="serverGroupSelect" class="w-full px-3 py-2 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 cursor-pointer backdrop-blur-sm shadow-sm">
                        ${groupOptions}
                    </select>
                </div>
            `;
        }

        return `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-3">
                    <div class="text-slate-500 dark:text-slate-400">主机名: <span class="text-slate-800 dark:text-white font-medium">${server.name || server.hostname || '未知'}</span></div>
                    <div class="text-slate-500 dark:text-slate-400">IP地址: <span class="text-slate-800 dark:text-white font-medium">${ssh.host || server.ip || '未知'}</span></div>
                    <div class="text-slate-500 dark:text-slate-400">系统: <span class="text-slate-800 dark:text-white font-medium">${data.system || server.system || '未知'}</span></div>
                    <div class="text-slate-500 dark:text-slate-400">版本: <span class="text-slate-800 dark:text-white font-medium">${data.version || server.version || '未知'}</span></div>
                    <div class="text-slate-500 dark:text-slate-400">状态: <span class="${isOnline ? 'text-green-500' : 'text-red-500'} font-medium">${isOnline ? '在线' : '离线'}</span></div>
                </div>
                <div class="space-y-3">
                    <div class="text-slate-500 dark:text-slate-400">分组: <span class="text-slate-800 dark:text-white font-medium">${server.group_id || server.group || '默认'}</span></div>
                    <div class="text-slate-500 dark:text-slate-400">API端口: <span class="text-slate-800 dark:text-white font-medium">${api.port || '未知'}</span></div>
                    <div class="text-slate-500 dark:text-slate-400">网络设备: <span class="text-slate-800 dark:text-white font-medium">${data.device || server.device || '未知'}</span></div>
                    <div class="text-slate-500 dark:text-slate-400">发现时间: <span class="text-slate-800 dark:text-white font-medium">${discoveryTime}</span></div>
                    <div class="text-slate-500 dark:text-slate-400">最后在线: <span class="text-slate-800 dark:text-white font-medium">${lastOnline}</span></div>
                </div>
            </div>
            ${groupSelectHtml}
            <div class="mt-6 p-4 bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 rounded-xl border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm">
                <h4 class="text-sm font-medium text-slate-600 dark:text-slate-300 mb-3 flex items-center gap-2">
                    <i class="ti ti-key"></i>
                    API密钥
                </h4>
                <div class="text-xs font-mono bg-slate-900 dark:bg-slate-950 p-3 rounded-lg overflow-x-auto text-green-400 border border-slate-700/60 shadow-inner">${api.key || server.api_key || '未知'}</div>
            </div>
        `;
    }

    /**
     * 批准服务器
     */
    async approveServer(serverId) {
        try {
            const groupSelect = document.getElementById('serverGroupSelect');
            const groupId = groupSelect?.value || 'default';
            const groupName = groupSelect?.options[groupSelect.selectedIndex]?.text || '默认分组';

            if (!confirm(`确定要批准此服务器吗？\n服务器将被添加到「${groupName}」分组。`)) {
                return;
            }

            const response = await this.apiRequest(`/admin/autodiscovery/approve/${serverId}`, {
                method: 'POST',
                body: JSON.stringify({
                    approved: true,
                    group_id: groupId
                })
            });

            if (!response.ok) {
                throw new Error(`服务器返回错误: ${response.status}`);
            }

            const result = await response.json();

            if (this.isSuccessResponse(result)) {
                this.showNotice('服务器已批准', 'success');
                this.closeModal();
                await Promise.all([
                    this.refreshPendingServers(),
                    this.refreshDiscoveredServers()
                ]);
            } else {
                this.showNotice(result.message || '批准服务器失败', 'error');
            }
        } catch (error) {
            console.error('批准服务器失败:', error);
            this.showNotice('批准服务器失败', 'error');
        }
    }

    /**
     * 拒绝服务器
     */
    async rejectServer(serverId) {
        try {
            if (!confirm('确定要拒绝此服务器吗？此操作不可恢复。')) {
                return;
            }

            const response = await this.apiRequest(`/admin/autodiscovery/reject/${serverId}`, {
                method: 'POST'
            });

            if (!response.ok) {
                throw new Error(`服务器返回错误: ${response.status}`);
            }

            const result = await response.json();

            if (this.isSuccessResponse(result)) {
                this.showNotice('服务器已拒绝', 'success');
                this.closeModal();
                await Promise.all([
                    this.refreshPendingServers(),
                    this.refreshDiscoveredServers()
                ]);
            } else {
                this.showNotice(result.message || '拒绝服务器失败', 'error');
            }
        } catch (error) {
            console.error('拒绝服务器失败:', error);
            this.showNotice('拒绝服务器失败', 'error');
        }
    }

    /**
     * 删除服务器
     */
    async deleteServer(serverId) {
        if (!confirm('确定要删除此服务器吗？此操作不可恢复。')) {
            return;
        }

        try {
            if (typeof startloading === 'function') startloading();

            const response = await this.apiRequest(`/admin/autodiscovery/delete/${serverId}`, {
                method: 'POST'
            });

            if (!response.ok) {
                throw new Error(`服务器返回错误: ${response.status}`);
            }

            const result = await response.json();

            if (this.isSuccessResponse(result)) {
                this.showNotice('服务器已删除', 'success');
                
                // 从本地数据中移除
                this.allDiscoveredServers = this.allDiscoveredServers.filter(server =>
                    (server._id !== serverId) && (server.id !== serverId)
                );

                this.renderDiscoveredServers();
            } else {
                this.showNotice(result.message || '删除服务器失败', 'error');
            }
        } catch (error) {
            console.error('删除服务器失败:', error);
            this.showNotice('删除服务器失败: ' + error.message, 'error');
        } finally {
            if (typeof endloading === 'function') endloading();
        }
    }

    /**
     * 关闭模态框
     */
    closeModal() {
        const modal = document.getElementById('serverDetailModal');
        if (modal) {
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }
        this.currentServerId = null;
    }

    /**
     * 清空操作
     */
    async clearAllPendingServers() {
        if (!confirm('确定要清空所有待审核服务器吗？\n该操作将从数据库中删除这些服务器。')) {
            return;
        }

        try {
            this.showNotice('正在清空所有待审核节点...', 'info');

            const response = await this.apiRequest('/admin/autodiscovery/clear-all-pending', {
                method: 'POST'
            });

            if (!response.ok) {
                throw new Error(`服务器返回错误: ${response.status}`);
            }

            const result = await response.json();

            if (this.isSuccessResponse(result)) {
                this.showNotice(result.message || `已清空 ${result.count} 个待审核节点`, 'success');
                await this.refreshPendingServers();
            } else {
                this.showNotice(result.message || '清空待审核节点失败', 'error');
            }
        } catch (error) {
            console.error('清空待审核节点时出错:', error);
            this.showNotice(`清空待审核节点失败: ${error.message}`, 'error');
        }
    }

    /**
     * 清空离线已发现服务器
     */
    async clearOfflineDiscoveredServers() {
        if (!confirm('确定要清空所有离线的已发现服务器吗？\n该操作将从数据库中删除这些服务器。')) {
            return;
        }

        try {
            this.showNotice('正在清空离线的已发现节点...', 'info');

            const response = await this.apiRequest('/admin/autodiscovery/clear-offline-discovered', {
                method: 'POST'
            });

            if (!response.ok) {
                throw new Error(`服务器返回错误: ${response.status}`);
            }

            const result = await response.json();

            if (this.isSuccessResponse(result)) {
                this.showNotice(result.message || `已清空 ${result.count} 个离线的已发现节点`, 'success');
                await this.refreshDiscoveredServers();
            } else {
                this.showNotice(result.message || '清空离线的已发现节点失败', 'error');
            }
        } catch (error) {
            console.error('清空离线的已发现节点时出错:', error);
            this.showNotice(`清空离线的已发现节点失败: ${error.message}`, 'error');
        }
    }

    /**
     * 保存设置
     */
    async saveSettings() {
        try {
            if (typeof startloading === 'function') startloading();

            const settings = {
                enabled: document.querySelector('[key="autodiscovery.enabled"]')?.checked || false,
                registrationKey: document.querySelector('[key="autodiscovery.registrationKey"]')?.value || '',
                defaultGroup: document.querySelector('[key="autodiscovery.defaultGroup"]')?.value || 'default',
                requireKey: document.querySelector('[key="autodiscovery.requireKey"]')?.checked || false,
                requireApproval: document.querySelector('[key="autodiscovery.requireApproval"]')?.checked || false
            };

            const response = await this.apiRequest('/api/admin/autodiscovery/config', {
                method: 'POST',
                body: JSON.stringify(settings)
            });

            if (!response.ok) {
                throw new Error(`服务器返回错误: ${response.status}`);
            }

            const result = await response.json();

            if (this.isSuccessResponse(result)) {
                this.showNotice('保存成功', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                this.showNotice(result.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('保存设置失败:', error);
            this.showNotice('保存失败: ' + error.message, 'error');
        } finally {
            if (typeof endloading === 'function') endloading();
        }
    }

    /**
     * 生成注册密钥
     */
    generateRegistrationKey() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let key = '';
        for (let i = 0; i < 16; i++) {
            key += chars.charAt(Math.floor(Math.random() * chars.length));
        }

        const input = document.querySelector('[key="autodiscovery.registrationKey"]');
        if (input) {
            input.value = key;
            this.showNotice('已生成新的注册密钥', 'success');
        }
    }

    /**
     * 显示安装命令弹窗
     */
    copyInstallCommand() {
        const modal = document.getElementById('copyCommandModal');
        const commandElement = document.getElementById('installCommand');

        if (!modal || !commandElement) return;

        const registrationKey = document.querySelector('[key="autodiscovery.registrationKey"]')?.value || '';
        const serverUrl = window.location.origin;
        
        // 先存储命令到全局变量，供标签页切换使用
        window.dstatusCommands = {
            registrationKey,
            serverUrl
        };
        
        // 设置默认显示安装命令
        this.showCommandTab('install');

        modal.classList.remove('hidden');
        modal.classList.add('flex');
    }

    /**
     * 显示指定的命令标签页
     */
    showCommandTab(tab) {
        const commandElement = document.getElementById('installCommand');
        if (!commandElement || !window.dstatusCommands) return;

        const { registrationKey, serverUrl } = window.dstatusCommands;
        
        // 获取模式和端口选择状态
        const activeReportCheckbox = document.getElementById('enable-active-report');
        const agentPortInput = document.getElementById('agent-port');
        const isActiveMode = activeReportCheckbox && activeReportCheckbox.checked;
        const agentPort = agentPortInput ? agentPortInput.value : '9999';
        
        let reportMode = '';
        if (isActiveMode) {
            reportMode = 'active-report';
        } else if (agentPort && agentPort !== '9999') {
            reportMode = 'passive';
        }
        
        // 更新标签页状态
        document.querySelectorAll('[data-tab]').forEach(btn => {
            const isActive = btn.dataset.tab === tab;
            btn.classList.toggle('bg-blue-600', isActive);
            btn.classList.toggle('text-white', isActive);
            btn.classList.toggle('bg-transparent', !isActive);
            btn.classList.toggle('text-slate-600', !isActive);
            btn.classList.toggle('dark:text-slate-400', !isActive);
        });

        // 更新说明内容
        const titleElement = document.getElementById('commandTitle');
        const descElement = document.getElementById('commandDescription');

        let command = '';
        let title = '';
        let description = '';

        switch (tab) {
            case 'install':
                title = '安装命令';
                description = '全新安装 DStatus Agent（如果已有配置会保留）';
                if (isActiveMode) {
                    command = `curl -fsSL https://down.vps.mom/scripts/install-dstatus-agent.sh | bash -s -- "${registrationKey}" "${serverUrl}" "active-report"`;
                } else if (agentPort && agentPort !== '9999') {
                    command = `curl -fsSL https://down.vps.mom/scripts/install-dstatus-agent.sh | bash -s -- "${registrationKey}" "${serverUrl}" "passive" "${agentPort}"`;
                } else {
                    command = `curl -fsSL https://down.vps.mom/scripts/install-dstatus-agent.sh | bash -s -- "${registrationKey}" "${serverUrl}"`;
                }
                break;
            
            case 'update':
                title = '更新命令';
                description = '更新 DStatus Agent 到最新版本（保留现有配置）';
                if (isActiveMode) {
                    command = `curl -fsSL https://down.vps.mom/scripts/install-dstatus-agent.sh | bash -s -- "${registrationKey}" "${serverUrl}" "active-report"`;
                } else if (agentPort && agentPort !== '9999') {
                    command = `curl -fsSL https://down.vps.mom/scripts/install-dstatus-agent.sh | bash -s -- "${registrationKey}" "${serverUrl}" "passive" "${agentPort}"`;
                } else {
                    command = `curl -fsSL https://down.vps.mom/scripts/install-dstatus-agent.sh | bash -s -- "${registrationKey}" "${serverUrl}"`;
                }
                break;
            
            case 'uninstall':
                title = '卸载命令';
                description = '完全卸载 DStatus Agent 及其配置';
                command = `sudo bash -c "systemctl stop dstatus 2>/dev/null; systemctl disable dstatus 2>/dev/null; rc-service dstatus stop 2>/dev/null; rc-update delete dstatus default 2>/dev/null; /etc/init.d/dstatus stop 2>/dev/null; update-rc.d -f dstatus remove 2>/dev/null; rm -f /usr/bin/dstatus-agent /etc/systemd/system/dstatus.service /etc/init.d/dstatus /etc/dstatus-agent/config.yaml 2>/dev/null; rmdir /etc/dstatus-agent 2>/dev/null || true; echo 'DStatus Agent 已卸载。防火墙规则可能需要手动清理。'"`;
                break;
        }

        if (titleElement) titleElement.textContent = title;
        if (descElement) descElement.textContent = description;
        commandElement.textContent = command;
    }

    /**
     * 关闭安装命令弹窗
     */
    closeCopyCommandModal() {
        const modal = document.getElementById('copyCommandModal');
        if (modal) {
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }
    }

    /**
     * 复制文本到剪贴板
     */
    copyToClipboard(elementId) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const text = element.textContent;
        const textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style.position = 'fixed';
        textarea.style.opacity = '0';
        document.body.appendChild(textarea);

        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);

        this.showNotice('安装命令已复制到剪贴板', 'success');
    }

    /**
     * 清理资源
     */
    cleanup() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }
    }
}

// 全局函数绑定 - 保持向后兼容
let autodiscoveryManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    autodiscoveryManager = new AutoDiscoveryManager();
});

/**
 * 切换上报模式（主动/被动）
 */
window.toggleReportMode = function() {
    const activeReportCheckbox = document.getElementById('enable-active-report');
    const passivePortOption = document.getElementById('passive-port-option');
    
    if (activeReportCheckbox && passivePortOption) {
        if (activeReportCheckbox.checked) {
            // 主动模式：隐藏端口选择
            passivePortOption.style.display = 'none';
        } else {
            // 被动模式：显示端口选择
            passivePortOption.style.display = 'block';
        }
    }
    
    // 更新命令显示
    updateCommandDisplay();
};

/**
 * 更新命令显示（当主动上报选项或端口改变时调用）
 */
window.updateCommandDisplay = function() {
    const activeTab = document.querySelector('[data-tab].bg-blue-600');
    if (activeTab && autodiscoveryManager) {
        autodiscoveryManager.showCommandTab(activeTab.dataset.tab);
    }
};

// 全局函数导出 - 供HTML onclick使用
window.saveSettings = () => autodiscoveryManager?.saveSettings();
window.generateRegistrationKey = () => autodiscoveryManager?.generateRegistrationKey();
window.copyInstallCommand = () => autodiscoveryManager?.copyInstallCommand();
window.closeCopyCommandModal = () => autodiscoveryManager?.closeCopyCommandModal();
window.copyToClipboard = (elementId) => autodiscoveryManager?.copyToClipboard(elementId);
window.closeModal = () => autodiscoveryManager?.closeModal();
window.refreshPendingServers = () => autodiscoveryManager?.refreshPendingServers();
window.refreshDiscoveredServers = () => autodiscoveryManager?.refreshDiscoveredServers();
window.clearAllPendingServers = () => autodiscoveryManager?.clearAllPendingServers();
window.clearOfflineDiscoveredServers = () => autodiscoveryManager?.clearOfflineDiscoveredServers();
window.searchServers = () => autodiscoveryManager?.searchServers();
window.changePageSize = () => autodiscoveryManager?.changePageSize();
window.goToPrevPage = () => autodiscoveryManager?.goToPrevPage();
window.goToNextPage = () => autodiscoveryManager?.goToNextPage(); 