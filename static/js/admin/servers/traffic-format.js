/**
 * 流量格式化工具函数
 */
window.TrafficFormat = {
    /**
     * 将字节数转换为GB
     * @param {number} bytes 字节数
     * @returns {number} GB值,保留2位小数
     */
    bytesToGB(bytes) {
        if (!bytes) return 0;

        // 使用安全的方式处理大数值
        try {
            const value = Number(bytes);
            if (isNaN(value) || !isFinite(value)) return 0;

            // 1GB = 1024^3 bytes
            const result = value / (1024 * 1024 * 1024);
            return parseFloat(result.toFixed(2));
        } catch (e) {
            console.error('[TrafficFormat] 字节转GB失败:', e);
            return 0;
        }
    },

    /**
     * 将GB转换为字节数
     * @param {number} gb GB值
     * @returns {number} 字节数
     */
    gbToBytes(gb) {
        if (!gb) return 0;
        try {
            const value = Number(gb);
            if (isNaN(value) || !isFinite(value)) return 0;

            // 处理非常大的值
            if (value > Number.MAX_SAFE_INTEGER / (1024 * 1024 * 1024)) {
                console.warn('[TrafficFormat] GB值过大，可能导致精度丢失');
            }

            // 1GB = 1024^3 bytes
            return Math.floor(value * 1024 * 1024 * 1024);
        } catch (e) {
            console.error('[TrafficFormat] GB转字节失败:', e);
            return 0;
        }
    },

    /**
     * 格式化流量数值为可读字符串
     * @param {number} bytes 字节数
     * @returns {string} 格式化后的字符串
     */
    formatBytes(bytes) {
        if (bytes === null || bytes === undefined || isNaN(bytes)) {
            return '0 B';
        }

        try {
            let value = Number(bytes);
            if (!isFinite(value)) {
                return '0 B';
            }

            const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB'];
            const k = 1024;

            if (value === 0) return '0 B';

            // 使用对数计算来确定合适的单位级别
            const i = Math.floor(Math.log(Math.abs(value)) / Math.log(k));
            const unitIndex = Math.min(i, units.length - 1);

            value = value / Math.pow(k, unitIndex);

            // 根据数值大小和单位级别决定小数位数
            let decimals = 2;
            if (unitIndex >= 4) decimals = 2; // TB及以上保留2位小数
            else if (value >= 100) decimals = 1;
            else if (value >= 10) decimals = 2;
            else decimals = 3;

            return value.toFixed(decimals) + ' ' + units[unitIndex];
        } catch (e) {
            console.error('[TrafficFormat] 格式化字节失败:', e);
            return '0 B';
        }
    },

    /**
     * 格式化GB值为可读字符串
     * @param {number} gb GB值
     * @returns {string} 格式化后的字符串
     */
    formatGB(gb) {
        return this.formatBytes(this.gbToBytes(gb));
    }
};
