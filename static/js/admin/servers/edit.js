/**
 * 服务器编辑页面主文件
 * 整合所有功能模块
 */

// 防止重复初始化的标志
let pageInitialized = false;

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 防止重复初始化
    if (pageInitialized) {
        console.log('页面已初始化，跳过重复初始化');
        return;
    }
    pageInitialized = true;
    
    console.log('服务器编辑页面初始化');

    // 初始化表单数据
    if (typeof server !== 'undefined' && server) {
        initForm(server);
        
        // 初始化标签 - 确保总是初始化，即使为空
        const tags = (server.data && server.data.tags) ? server.data.tags : [];
        console.log('准备初始化标签数据:', tags);
        
        // 检查是否已经初始化过
        if (window.tagsInitialized) {
            console.log('标签已经初始化过，跳过');
        } else if (typeof window.initTags === 'function') {
            console.log('调用 initTags 初始化标签');
            window.initTags(tags);
        } else {
            console.error('initTags 函数未定义，请确保 tag-manager.js 已加载');
            // 延迟重试一次
            setTimeout(() => {
                if (typeof window.initTags === 'function' && !window.tagsInitialized) {
                    console.log('延迟调用 initTags');
                    window.initTags(tags);
                }
            }, 100);
        }
    } else {
        console.warn('server 变量未定义，无法初始化表单和标签');
    }

    // 绑定标签输入框回车事件
    const tagInput = document.getElementById('tag-input');
    if (tagInput) {
        tagInput.addEventListener('keypress', handleTagInput);
    }

    // 初始化位置设置状态
    initLocationSettings();
    
    // 初始化文件上传
    if (typeof initFileUpload === 'function') {
        initFileUpload();
    }
    
    // 初始化API模式切换
    if (typeof initApiModeToggles === 'function') {
        initApiModeToggles();
    }
    
    // 初始化国家列表
    if (typeof initCountryList === 'function') {
        initCountryList();
    }
    
    // 初始化密码切换功能
    if (typeof initPasswordToggle === 'function') {
        initPasswordToggle();
    }
    
    // 初始化键盘快捷键
    if (typeof initKeyboardShortcuts === 'function') {
        initKeyboardShortcuts({
            saveCallback: () => {
                if (typeof edit === 'function') {
                    edit();
                }
            },
            escCallback: () => {
                window.location.href = '/admin/servers';
            },
            tipKey: 'editServerKeyboardTipShown'
        });
    }

    console.log('页面初始化完成');
});

// 页面加载完成后初始化悬浮操作栏
window.addEventListener('load', function() {
    if (typeof initFloatingActionBar === 'function') {
        initFloatingActionBar();
    }
});

// 初始化位置设置
function initLocationSettings() {
    const checkbox = document.getElementById('edit_location_manual');
    const toggle = document.getElementById('location-manual-toggle');
    const container = document.getElementById('country-select-container');
    const fetchContainer = document.getElementById('fetch-location-container');

    if (!checkbox || !toggle || !container || !fetchContainer) {
        console.warn('位置设置元素未找到');
        return;
    }

    // 根据复选框状态设置UI
    if (checkbox.checked) {
        toggle.classList.add('translate-x-5', 'bg-blue-500');
        toggle.classList.remove('bg-slate-300', 'dark:bg-slate-600');
        container.classList.remove('hidden');
        fetchContainer.classList.add('hidden');
    } else {
        toggle.classList.remove('translate-x-5', 'bg-blue-500');
        toggle.classList.add('bg-slate-300', 'dark:bg-slate-600');
        container.classList.add('hidden');
        fetchContainer.classList.remove('hidden');
    }
}

// 国家选择相关函数
function selectCountry(code, name) {
    // 更新隐藏字段
    document.getElementById('edit_location_country_code').value = code;
    
    // 更新显示的国家名称
    const nameElement = document.getElementById('selected-country-name');
    if (nameElement) {
        nameElement.textContent = name;
    }
    
    // 更新国旗显示
    const flagElement = document.getElementById('selected-country-flag');
    if (flagElement) {
        if (code === 'LO' || code === 'OT') {
            // 本地网络和其他网络使用图标
            flagElement.innerHTML = '<i class="ti ti-world text-lg"></i>';
        } else {
            // 其他国家使用国旗图片
            const flagUrl = code === 'UK' ? '/img/flags/GB.SVG' : `/img/flags/${code}.SVG`;
            flagElement.innerHTML = `<img src="${flagUrl}" alt="${name}" class="w-5 h-4 object-cover rounded">`;
        }
    }
    
    // 关闭下拉菜单
    const dropdown = document.getElementById('country-dropdown');
    if (dropdown) {
        dropdown.classList.add('hidden');
    }
    
    console.log('选择国家:', code, name);
}

// 切换国家下拉菜单
function toggleCountryDropdown() {
    const dropdown = document.getElementById('country-dropdown');
    if (dropdown) {
        dropdown.classList.toggle('hidden');
    }
}

// 点击外部关闭下拉菜单
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('country-dropdown');
    const button = document.getElementById('country-select-button');
    
    if (dropdown && button && !button.contains(event.target) && !dropdown.contains(event.target)) {
        dropdown.classList.add('hidden');
    }
});

// 工具函数：获取表单值
function V(id) {
    const element = document.getElementById(id);
    return element ? element.value : '';
}

// 全局错误处理
window.addEventListener('error', function(event) {
    console.error('页面错误:', event.error);
});

// 全局未处理的Promise拒绝处理
window.addEventListener('unhandledrejection', function(event) {
    console.error('未处理的Promise拒绝:', event.reason);
});

// 页面卸载前的清理
window.addEventListener('beforeunload', function() {
    // 清理定时器等资源
    console.log('页面卸载清理');
});

// 适配器函数，使用通用模块

// 测试SSH连接函数已在 ssh-test-common.js 中定义
// edit页面使用 testSSHConnectionForEdit 函数

// 切换位置手动设置功能已在 location-common.js 中定义
// HTML 直接调用全局函数，无需本地适配器

// 获取位置信息功能已在 location-common.js 中定义
// HTML 中的按钮直接调用全局 fetchLocationInfo 函数，无需本地包装器
