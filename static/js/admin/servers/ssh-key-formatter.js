/**
 * SSH 私钥格式化工具
 * 自动修复粘贴时丢失换行符的问题
 */

class SSHKeyFormatter {
    /**
     * 格式化私钥 - 主要方法
     * @param {string} privateKey - 原始私钥字符串
     * @returns {string} 格式化后的私钥
     */
    static format(privateKey) {
        if (!privateKey || typeof privateKey !== 'string') {
            return privateKey;
        }

        // 常见的私钥类型
        const keyTypes = [
            'RSA PRIVATE KEY',
            'OPENSSH PRIVATE KEY',
            'EC PRIVATE KEY',
            'PRIVATE KEY',
            'DSA PRIVATE KEY',
            'ENCRYPTED PRIVATE KEY'
        ];

        // 查找私钥类型
        let keyType = null;
        for (const type of keyTypes) {
            if (privateKey.includes(`BEGIN ${type}`)) {
                keyType = type;
                break;
            }
        }

        if (!keyType) {
            // 如果找不到标准格式，返回原始值
            return privateKey;
        }

        try {
            // 提取标记
            const beginMarker = `-----BEGIN ${keyType}-----`;
            const endMarker = `-----END ${keyType}-----`;

            // 查找开始和结束位置
            const startIndex = privateKey.indexOf(beginMarker);
            const endIndex = privateKey.indexOf(endMarker);

            if (startIndex === -1 || endIndex === -1) {
                return privateKey;
            }

            // 提取私钥内容部分
            const contentStart = startIndex + beginMarker.length;
            const keyContent = privateKey
                .substring(contentStart, endIndex)
                .replace(/\s+/g, ''); // 移除所有空白字符

            // 按照 PEM 标准，每行 64 个字符
            const formattedContent = this.splitIntoLines(keyContent, 64);

            // 重新组装私钥
            return `${beginMarker}\n${formattedContent}\n${endMarker}`;
        } catch (error) {
            console.error('格式化私钥失败:', error);
            return privateKey;
        }
    }

    /**
     * 将字符串分割成指定长度的行
     * @param {string} str - 要分割的字符串
     * @param {number} lineLength - 每行长度（默认64）
     * @returns {string} 分割后的字符串
     */
    static splitIntoLines(str, lineLength = 64) {
        const lines = [];
        for (let i = 0; i < str.length; i += lineLength) {
            lines.push(str.substring(i, i + lineLength));
        }
        return lines.join('\n');
    }

    /**
     * 验证私钥格式是否正确
     * @param {string} privateKey - 私钥字符串
     * @returns {object} 验证结果 {valid: boolean, message: string}
     */
    static validate(privateKey) {
        if (!privateKey || typeof privateKey !== 'string') {
            return { valid: false, message: '私钥不能为空' };
        }

        // 检查是否包含开始和结束标记
        const hasBeginMarker = /-----BEGIN .+?-----/.test(privateKey);
        const hasEndMarker = /-----END .+?-----/.test(privateKey);

        if (!hasBeginMarker || !hasEndMarker) {
            return { valid: false, message: '私钥格式不正确，缺少开始或结束标记' };
        }

        // 检查内容是否为有效的 Base64
        const content = privateKey
            .replace(/-----BEGIN .+?-----/, '')
            .replace(/-----END .+?-----/, '')
            .replace(/\s+/g, '');

        // Base64 字符集
        const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
        
        if (!base64Regex.test(content)) {
            return { valid: false, message: '私钥内容包含无效字符' };
        }

        return { valid: true, message: '私钥格式正确' };
    }

    /**
     * 自动检测并格式化私钥
     * @param {string} privateKey - 私钥字符串
     * @returns {object} {formatted: string, changed: boolean}
     */
    static autoFormat(privateKey) {
        const original = privateKey;
        const formatted = this.format(privateKey);
        
        return {
            formatted: formatted,
            changed: original !== formatted
        };
    }

    /**
     * 从文件内容提取私钥
     * @param {string} fileContent - 文件内容
     * @returns {string} 提取的私钥
     */
    static extractFromFile(fileContent) {
        // 匹配私钥的正则表达式
        const keyRegex = /-----BEGIN .+?-----[\s\S]+?-----END .+?-----/;
        const match = fileContent.match(keyRegex);
        
        if (match) {
            return this.format(match[0]);
        }
        
        return fileContent;
    }

    /**
     * 处理 textarea 输入事件
     * @param {HTMLTextAreaElement} textarea - 文本域元素
     */
    static bindToTextarea(textarea) {
        if (!textarea) return;

        // 失去焦点时自动格式化
        textarea.addEventListener('blur', function() {
            const result = SSHKeyFormatter.autoFormat(this.value);
            if (result.changed) {
                this.value = result.formatted;
                console.log('私钥已自动格式化');
            }
        });

        // 粘贴事件处理
        textarea.addEventListener('paste', function(e) {
            e.preventDefault();
            
            const pastedText = (e.clipboardData || window.clipboardData).getData('text');
            const result = SSHKeyFormatter.autoFormat(pastedText);
            
            // 插入格式化后的文本
            const start = this.selectionStart;
            const end = this.selectionEnd;
            const text = this.value;
            
            this.value = text.substring(0, start) + result.formatted + text.substring(end);
            
            // 设置光标位置
            const newCursorPos = start + result.formatted.length;
            this.setSelectionRange(newCursorPos, newCursorPos);
            
            if (result.changed) {
                console.log('粘贴的私钥已自动格式化');
            }
        });
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SSHKeyFormatter;
}