/**
 * 标签管理相关函数
 */

// 全局变量存储当前标签 - 确保挂载到 window 对象上
let currentTags = [];
window.currentTags = currentTags;  // 确保其他脚本可以访问

// 初始化标签数据 - 确保全局可访问
function initTags(serverTags) {
    console.log('initTags 被调用，参数:', serverTags);
    
    // 确保 serverTags 是一个数组
    if (!Array.isArray(serverTags)) {
        console.warn('initTags: serverTags 不是数组，使用空数组初始化');
        serverTags = [];
    }
    
    currentTags = serverTags;
    window.currentTags = currentTags;  // 同步更新 window 对象
    console.log('标签初始化完成，当前标签数:', currentTags.length);
    
    // 立即更新显示
    updateTagsDisplay();
    
    // 标记已初始化
    window.tagsInitialized = true;
}

// 确保函数全局可访问
window.initTags = initTags;

// 添加标签（从模态框）
function addTag() {
    const nameInput = document.getElementById('tag-name-input');
    const colorInput = document.getElementById('tag-color-input');
    const iconInput = document.getElementById('selected-icon');
    
    const tagName = nameInput?.value?.trim();
    const tagColor = colorInput?.value || '#6b7280';
    const tagIcon = iconInput?.value || 'tag';

    if (!tagName) {
        notice('请输入标签名称', 'error');
        return;
    }

    // 检查标签长度
    if (tagName.length > 20) {
        notice('标签长度不能超过20个字符', 'error');
        return;
    }

    // 检查是否已存在
    const exists = currentTags.some(tag => {
        const name = typeof tag === 'string' ? tag : tag.name;
        return name === tagName;
    });
    
    if (exists) {
        notice('标签已存在', 'error');
        return;
    }

    // 检查标签数量限制
    if (currentTags.length >= 10) {
        notice('最多只能添加10个标签', 'error');
        return;
    }

    // 创建标签对象
    const tag = {
        name: tagName,
        color: tagColor,
        icon: tagIcon
    };

    // 添加标签
    currentTags.push(tag);
    window.currentTags = currentTags;  // 同步更新 window 对象
    
    // 清空输入
    if (nameInput) nameInput.value = '';
    if (colorInput) colorInput.value = '#6b7280';
    if (iconInput) iconInput.value = 'label';
    
    // 关闭模态框
    closeAddTagModal();
    
    updateTagsDisplay();
    notice('标签添加成功', 'success');
}

// 删除标签
function removeTag(tagText) {
    let index = -1;
    
    // 查找匹配的标签索引（支持字符串和对象格式）
    for (let i = 0; i < currentTags.length; i++) {
        const tag = currentTags[i];
        let tagName;
        
        if (typeof tag === 'string') {
            tagName = tag;
        } else if (typeof tag === 'object' && tag !== null) {
            tagName = tag.name || tag.text || String(tag);
        } else {
            tagName = String(tag);
        }
        
        if (tagName === tagText) {
            index = i;
            break;
        }
    }
    
    if (index > -1) {
        currentTags.splice(index, 1);
        window.currentTags = currentTags;  // 同步更新 window 对象
        updateTagsDisplay();
        notice('标签删除成功', 'success');
    }
}

// 更新标签显示
function updateTagsDisplay() {
    const container = document.getElementById('tags-container');
    const countElement = document.getElementById('tags-count');

    console.log('updateTagsDisplay 被调用, currentTags:', currentTags);
    
    if (!container) {
        console.error('tags-container 元素未找到');
        return;
    }

    // 更新标签计数
    if (countElement) {
        countElement.textContent = currentTags.length;
    }

    // 清空容器
    container.innerHTML = '';

    if (currentTags.length === 0) {
        container.innerHTML = '<p class="text-slate-500 dark:text-slate-400 text-sm">暂无标签</p>';
        console.log('没有标签，显示"暂无标签"');
        return;
    }
    
    console.log('开始渲染', currentTags.length, '个标签');

    // 生成标签HTML
    currentTags.forEach((tag, index) => {
        // 处理不同格式的标签数据
        let tagName, tagColor, tagIcon;

        if (typeof tag === 'string') {
            // 简单字符串标签
            tagName = tag;
            tagColor = '#6b7280';
            tagIcon = 'tag';
        } else if (typeof tag === 'object' && tag !== null) {
            // 对象格式标签
            tagName = tag.name || tag.text || String(tag);
            tagColor = tag.color || '#6b7280';
            tagIcon = tag.icon || 'tag';
        } else {
            // 其他格式，转为字符串
            tagName = String(tag);
            tagColor = '#6b7280';
            tagIcon = 'tag';
        }
        
        console.log(`渲染标签 ${index + 1}: ${tagName}`);

        const tagElement = document.createElement('span');
        tagElement.className = 'inline-flex items-center gap-1 px-2 py-1 bg-pink-100 dark:bg-pink-900/30 text-pink-700 dark:text-pink-300 rounded-md text-sm mr-2 mb-2';
        
        // 获取图标类名
        const iconClass = getIconClass(tagIcon);
        
        tagElement.innerHTML = `
            <i class="ti ${iconClass} text-xs"></i>
            <span>${escapeHtml(tagName)}</span>
            <button type="button" onclick="removeTag('${escapeHtml(tagName)}')"
                    class="text-pink-500 hover:text-pink-700 dark:text-pink-400 dark:hover:text-pink-200">
                <i class="ti ti-x text-xs"></i>
            </button>
        `;
        container.appendChild(tagElement);
    });
    
    console.log('标签渲染完成');
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 图标映射函数 - 将存储的key映射到Tabler Icons类名
function getIconClass(iconKey) {
    const iconMap = {
        'tag': 'ti-tag',
        'label': 'ti-tag', // 兼容旧数据
        'star': 'ti-star',
        'warning': 'ti-alert-triangle',
        'info': 'ti-info-circle',
        'security': 'ti-shield-check',
        'storage': 'ti-database',
        'cloud': 'ti-cloud',
        'web': 'ti-world',
        'server': 'ti-server',
        'network': 'ti-network',
        'network_check': 'ti-network', // 兼容旧数据
        'analytics': 'ti-chart-line',
        'monitor': 'ti-heartbeat',
        'monitor_heart': 'ti-heartbeat', // 兼容旧数据
        'verified': 'ti-circle-check',
        'bug': 'ti-bug',
        'bug_report': 'ti-bug', // 兼容旧数据
        'code': 'ti-code',
        'settings': 'ti-settings',
        'brain': 'ti-brain',
        'psychology': 'ti-brain', // 兼容旧数据
        'rocket': 'ti-rocket',
        'rocket_launch': 'ti-rocket', // 兼容旧数据
        'science': 'ti-flask', // 对应测试环境图标
        'database': 'ti-database' // 数据库图标
    };
    
    return iconMap[iconKey] || 'ti-tag'; // 默认返回标签图标
}

// 处理标签输入框的回车事件
function handleTagInput(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        addTag();
    }
}

// 预设标签快速添加（统一版本）
function addPresetTag(name, color, icon) {
    // 兼容旧的单参数调用
    if (typeof name === 'string' && !color && !icon) {
        color = '#6b7280';
        icon = 'tag';
    }
    
    if (!name) return;

    // 检查是否已存在
    const exists = currentTags.some(tag => {
        const tagName = typeof tag === 'string' ? tag : tag.name;
        return tagName === name;
    });
    
    if (exists) {
        notice('标签已存在', 'error');
        return;
    }

    // 检查标签数量限制
    if (currentTags.length >= 10) {
        notice('最多只能添加10个标签', 'error');
        return;
    }

    // 创建标签对象
    const tag = {
        name: name,
        color: color || '#6b7280',
        icon: icon || 'tag'
    };

    // 添加标签
    currentTags.push(tag);
    window.currentTags = currentTags;  // 同步更新 window 对象
    updateTagsDisplay();
    notice('标签添加成功', 'success');
}

// 清空所有标签
function clearAllTags() {
    if (currentTags.length === 0) {
        notice('没有标签需要清空', 'info');
        return;
    }

    if (confirm(`确认清空所有 ${currentTags.length} 个标签？`)) {
        currentTags = [];
        window.currentTags = currentTags;  // 同步更新 window 对象
        updateTagsDisplay();
        notice('所有标签已清空', 'success');
    }
}

// 导入标签（从文本）
function importTags() {
    const text = prompt('请输入标签，用逗号分隔：');
    if (!text) return;

    const tags = text.split(',').map(tag => tag.trim()).filter(tag => tag);
    let addedCount = 0;
    let skippedCount = 0;

    tags.forEach(tag => {
        if (tag.length > 20) {
            console.warn(`标签 "${tag}" 长度超过20个字符，已跳过`);
            skippedCount++;
            return;
        }

        if (currentTags.includes(tag)) {
            console.warn(`标签 "${tag}" 已存在，已跳过`);
            skippedCount++;
            return;
        }

        if (currentTags.length >= 10) {
            console.warn('标签数量已达上限，停止添加');
            return;
        }

        currentTags.push(tag);
        addedCount++;
    });

    window.currentTags = currentTags;  // 同步更新 window 对象
    updateTagsDisplay();

    if (addedCount > 0) {
        notice(`成功添加 ${addedCount} 个标签${skippedCount > 0 ? `，跳过 ${skippedCount} 个` : ''}`, 'success');
    } else {
        notice('没有添加任何标签', 'info');
    }
}

// 模态框管理函数 - 合并重复定义
function showAddTagModal() {
    const modal = document.getElementById('add-tag-modal');
    if (modal) {
        modal.classList.remove('hidden');
        modal.classList.add('flex');
        // 初始化图标选择器
        populateIconSelector();
        // 重置表单
        const nameInput = document.getElementById('tag-name-input');
        const colorInput = document.getElementById('tag-color-input');
        const iconInput = document.getElementById('selected-icon');
        if (nameInput) nameInput.value = '';
        if (colorInput) colorInput.value = '#6b7280';
        if (iconInput) iconInput.value = 'tag';
        updateColorDisplay();
        updateIconSelection('tag');
    }
}

function closeAddTagModal() {
    const modal = document.getElementById('add-tag-modal');
    if (modal) {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
    }
}

// 图标选择器功能
function populateIconSelector() {
    const iconSelector = document.getElementById('icon-selector');
    if (!iconSelector) return;
    
    // 定义图标映射：键是内部名称，值是Tabler Icons的类名
    const iconOptions = [
        { key: 'tag', icon: 'ti-tag', label: '标签' },
        { key: 'star', icon: 'ti-star', label: '星标' },
        { key: 'warning', icon: 'ti-alert-triangle', label: '警告' },
        { key: 'info', icon: 'ti-info-circle', label: '信息' },
        { key: 'security', icon: 'ti-shield-check', label: '安全' },
        { key: 'storage', icon: 'ti-database', label: '存储' },
        { key: 'cloud', icon: 'ti-cloud', label: '云端' },
        { key: 'web', icon: 'ti-world', label: '网络' },
        { key: 'server', icon: 'ti-server', label: '服务器' },
        { key: 'network', icon: 'ti-network', label: '网络检查' },
        { key: 'analytics', icon: 'ti-chart-line', label: '分析' },
        { key: 'monitor', icon: 'ti-heartbeat', label: '监控' },
        { key: 'verified', icon: 'ti-circle-check', label: '验证' },
        { key: 'bug', icon: 'ti-bug', label: '缺陷' },
        { key: 'code', icon: 'ti-code', label: '代码' },
        { key: 'settings', icon: 'ti-settings', label: '设置' },
        { key: 'brain', icon: 'ti-brain', label: 'AI' },
        { key: 'rocket', icon: 'ti-rocket', label: '性能' }
    ];
    
    iconSelector.innerHTML = iconOptions.map(item => `
        <div class="icon-option cursor-pointer p-2 rounded border hover:bg-blue-100 dark:hover:bg-blue-900 text-center transition-colors"
             data-icon="${item.key}"
             onclick="selectIcon('${item.key}')"
             title="${item.label}">
            <i class="ti ${item.icon} text-lg"></i>
        </div>
    `).join('');
}

function selectIcon(iconName) {
    const iconInput = document.getElementById('selected-icon');
    if (iconInput) {
        iconInput.value = iconName;
    }
    updateIconSelection(iconName);
}

function updateIconSelection(selectedIcon) {
    // 更新图标选择状态
    document.querySelectorAll('.icon-option').forEach(option => {
        option.classList.remove('bg-blue-200', 'dark:bg-blue-800', 'border-blue-500');
        if (option.dataset.icon === selectedIcon) {
            option.classList.add('bg-blue-200', 'dark:bg-blue-800', 'border-blue-500');
        }
    });
}

// 颜色选择器功能
function updateColorDisplay() {
    const colorInput = document.getElementById('tag-color-input');
    const colorDisplay = document.getElementById('tag-color-display');
    if (colorInput && colorDisplay) {
        colorDisplay.textContent = colorInput.value;
        colorDisplay.style.color = colorInput.value;
    }
}

// 颜色输入事件监听 - 只处理颜色输入，不干扰标签初始化
document.addEventListener('DOMContentLoaded', function() {
    const colorInput = document.getElementById('tag-color-input');
    if (colorInput) {
        colorInput.addEventListener('input', updateColorDisplay);
    }
    
    // 添加点击背景关闭模态框的功能
    const modal = document.getElementById('add-tag-modal');
    if (modal) {
        modal.addEventListener('click', function(event) {
            // 如果点击的是模态框背景（不是内容区域）
            if (event.target === modal) {
                closeAddTagModal();
            }
        });
    }
    
    // 添加ESC键关闭模态框
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const modal = document.getElementById('add-tag-modal');
            if (modal && !modal.classList.contains('hidden')) {
                closeAddTagModal();
            }
        }
    });
    
    // 调试信息：检查标签是否已经初始化
    console.log('tag-manager.js DOMContentLoaded - currentTags:', window.currentTags);
    
    // 如果标签还没有显示，尝试刷新显示
    const container = document.getElementById('tags-container');
    if (container && container.innerHTML === '' && window.currentTags && window.currentTags.length > 0) {
        console.log('检测到标签未显示，刷新显示');
        updateTagsDisplay();
    }
});

// 确保所有必要的函数都暴露到全局作用域（供HTML直接调用）
window.addTag = addTag;
window.removeTag = removeTag;
window.addPresetTag = addPresetTag;
window.showAddTagModal = showAddTagModal;
window.closeAddTagModal = closeAddTagModal;
window.handleTagInput = handleTagInput;
window.updateTagsDisplay = updateTagsDisplay;
window.selectIcon = selectIcon;
window.clearAllTags = clearAllTags;
window.importTags = importTags;
window.updateColorDisplay = updateColorDisplay;
window.updateIconSelection = updateIconSelection;
