/**
 * 通用位置管理函数
 * 支持 add 和 edit 页面
 */

// 切换手动设置国家的UI显示
function toggleLocationManual(prefix = 'edit') {
    const checkbox = document.getElementById(`${prefix}_location_manual`);
    const toggle = document.getElementById('location-manual-toggle');
    const container = document.getElementById('country-select-container');
    const fetchContainer = document.getElementById('fetch-location-container');

    if (!checkbox || !toggle || !container || !fetchContainer) {
        console.warn('位置设置元素未找到');
        return;
    }

    // 切换复选框状态
    checkbox.checked = !checkbox.checked;

    // 更新开关样式
    const toggleContainer = toggle.parentElement; // 获取开关的父容器
    
    if (checkbox.checked) {
        toggle.classList.add('translate-x-5');
        toggleContainer.classList.add('!bg-blue-500', 'dark:!bg-blue-600');
        toggleContainer.classList.remove('bg-slate-300', 'dark:bg-slate-600');
        container.classList.remove('hidden');
        fetchContainer.classList.add('hidden');
    } else {
        toggle.classList.remove('translate-x-5');
        toggleContainer.classList.remove('!bg-blue-500', 'dark:!bg-blue-600');
        toggleContainer.classList.add('bg-slate-300', 'dark:bg-slate-600');
        container.classList.add('hidden');
        fetchContainer.classList.remove('hidden');
    }
}

/**
 * 手动获取服务器位置信息
 * @param {string} serverId - 服务器ID（edit页面用）
 */
async function fetchLocationInfo(serverId) {
    try {
        const button = document.getElementById('fetch-location-button');
        const statusDiv = document.getElementById('location-status');

        // 显示加载状态
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="ti ti-refresh"></i> <span>正在获取...</span>';
        }
        
        if (statusDiv) {
            statusDiv.textContent = '正在获取位置信息...';
            statusDiv.classList.remove('hidden', 'text-red-500', 'text-green-500');
            statusDiv.classList.add('text-purple-400');
        }

        // 如果没有传入serverId，尝试从URL获取（edit页面）
        if (!serverId) {
            serverId = window.location.pathname.split('/').filter(Boolean).pop();
        }

        // 发送请求到服务器
        const response = await fetch(`/api/admin/servers/${serverId}/fetch-location`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success && result.location) {
            const locationData = result.location;
            
            // 更新显示
            if (statusDiv) {
                const displayName = locationData.name_zh || locationData.name || locationData.code;
                statusDiv.textContent = `成功获取: ${displayName} (${locationData.code})`;
                statusDiv.classList.remove('text-purple-400', 'text-red-500');
                statusDiv.classList.add('text-green-500');
            }

            // 可以选择自动切换到手动模式并设置国家
            const displayName = locationData.name_zh || locationData.name || locationData.code;
            if (confirm(`检测到服务器位于 ${displayName}，是否设置为该位置？`)) {
                // 设置手动模式
                const manualCheckbox = document.querySelector('[id$="_location_manual"]');
                if (manualCheckbox && !manualCheckbox.checked) {
                    toggleLocationManual();
                }
                
                // 设置国家代码
                const countryCodeInput = document.querySelector('[id$="_location_country_code"]');
                if (countryCodeInput) {
                    countryCodeInput.value = locationData.code;
                    // 触发国家选择更新
                    if (typeof selectCountry === 'function') {
                        selectCountry(locationData.code, displayName);
                    }
                }
                
                console.log('位置信息已设置:', locationData);
                
                // 提示用户位置信息已自动更新，无需手动保存
                notice(`位置信息已自动更新为 ${displayName}`, 'success');
            } else {
                // 用户选择不使用检测到的位置，但仍显示检测结果
                notice(`检测到位置: ${displayName}，未应用`, 'info');
            }
        } else {
            throw new Error(result.message || result.error || '获取位置信息失败');
        }
    } catch (error) {
        console.error('获取位置信息失败:', error);
        
        const statusDiv = document.getElementById('location-status');
        if (statusDiv) {
            statusDiv.textContent = `获取失败: ${error.message}`;
            statusDiv.classList.remove('text-purple-400', 'text-green-500');
            statusDiv.classList.add('text-red-500');
        }
        
        notice(error.message || '获取位置信息失败', 'error');
    } finally {
        // 恢复按钮状态
        const button = document.getElementById('fetch-location-button');
        if (button) {
            button.disabled = false;
            button.innerHTML = '<i class="ti ti-map-pin"></i> <span>手动获取位置</span>';
        }
    }
}

// 初始化国家列表（可用于 add 和 edit 页面）
function initCountryList() {
    // 国家列表数据
    const countries = [
        { code: 'US', name: '美国' },
        { code: 'GB', name: '英国' },
        { code: 'JP', name: '日本' },
        { code: 'SG', name: '新加坡' },
        { code: 'HK', name: '香港' },
        { code: 'TW', name: '台湾' },
        { code: 'CN', name: '中国大陆' },
        { code: 'KR', name: '韩国' },
        { code: 'DE', name: '德国' },
        { code: 'FR', name: '法国' },
        { code: 'NL', name: '荷兰' },
        { code: 'CA', name: '加拿大' },
        { code: 'AU', name: '澳大利亚' },
        { code: 'IN', name: '印度' },
        { code: 'BR', name: '巴西' },
        { code: 'RU', name: '俄罗斯' }
    ];

    const countryList = document.getElementById('country-list');
    if (!countryList) return;

    // 生成国家选项
    countries.forEach(country => {
        const option = document.createElement('div');
        option.className = 'country-option cursor-pointer flex items-center gap-2 p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded transition-colors';
        option.setAttribute('data-code', country.code);
        option.innerHTML = `
            <img src="/img/flags/${country.code}.SVG" 
                 alt="${country.name}" 
                 class="w-6 h-4 object-cover rounded-sm border border-slate-200 dark:border-slate-700">
            <span class="text-sm text-slate-700 dark:text-slate-300">${country.name}</span>
            <span class="text-xs text-slate-500 dark:text-slate-400 ml-auto">${country.code}</span>
        `;
        
        option.addEventListener('click', function() {
            if (typeof selectCountry === 'function') {
                selectCountry(country.code, country.name);
            }
            
            // 隐藏列表
            const container = document.getElementById('country-list-container');
            if (container) {
                container.classList.add('hidden');
            }
        });
        
        countryList.appendChild(option);
    });

    // 搜索功能
    const searchInput = document.getElementById('country-search');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const container = document.getElementById('country-list-container');
            
            if (searchTerm) {
                container.classList.remove('hidden');
            } else {
                container.classList.add('hidden');
            }
            
            document.querySelectorAll('.country-option').forEach(option => {
                const name = option.textContent.toLowerCase();
                const code = option.getAttribute('data-code').toLowerCase();
                
                if (name.includes(searchTerm) || code.includes(searchTerm)) {
                    option.style.display = '';
                } else {
                    option.style.display = 'none';
                }
            });
        });

        // 聚焦时显示列表
        searchInput.addEventListener('focus', function() {
            if (this.value) {
                document.getElementById('country-list-container').classList.remove('hidden');
            }
        });
    }

    // 点击外部关闭
    document.addEventListener('click', function(e) {
        const container = document.getElementById('country-list-container');
        const searchInput = document.getElementById('country-search');
        
        if (container && searchInput && 
            !container.contains(e.target) && 
            !searchInput.contains(e.target)) {
            container.classList.add('hidden');
        }
    });
}

// 初始化位置设置开关的显示状态
function initLocationToggle(prefix = 'edit') {
    const checkbox = document.getElementById(`${prefix}_location_manual`);
    const toggle = document.getElementById('location-manual-toggle');
    
    if (!checkbox || !toggle) {
        return;
    }
    
    const toggleContainer = toggle.parentElement;
    
    // 根据复选框的初始状态设置样式
    if (checkbox.checked) {
        toggle.classList.add('translate-x-5');
        toggleContainer.classList.add('!bg-blue-500', 'dark:!bg-blue-600');
        toggleContainer.classList.remove('bg-slate-300', 'dark:bg-slate-600');
    } else {
        toggle.classList.remove('translate-x-5');
        toggleContainer.classList.remove('!bg-blue-500', 'dark:!bg-blue-600');
        toggleContainer.classList.add('bg-slate-300', 'dark:bg-slate-600');
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initLocationToggle();
});