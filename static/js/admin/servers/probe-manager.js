/**
 * 探针管理相关函数
 */

// 安装探针
async function installProbe() {
    try {
        // 显示安装进度模态框
        showInstallModal('正在安装探针', '准备安装...');

        const sid = V('sid');
        if (!sid) {
            hideInstallModal();
            notice('服务器ID不能为空', 'error');
            return;
        }

        // 发送安装请求
        const res = await postjson(`/admin/servers/${sid}/init`);

        // 如果有日志，显示安装日志
        if (res.log) {
            const logs = res.log.split('\n').filter(line => line.trim());
            streamInstallLog(logs);
        }

        if (res.status) {
            updateInstallStatus('安装完成！');
            console.log('探针安装结果:', res.data);

            // 显示安装详情
            if (res.data && res.data.details) {
                const details = res.data.details;
                let detailsLog = [];
                if (details.version) detailsLog.push(`版本: ${details.version}`);
                if (details.path) detailsLog.push(`安装路径: ${details.path}`);
                if (details.service) detailsLog.push(`服务状态: ${details.service}`);
                
                if (detailsLog.length > 0) {
                    updateInstallLog('\n\n安装详情：\n' + detailsLog.join('\n'));
                }
            }

            // 延迟关闭模态框，让用户看到成功信息
            setTimeout(() => {
                hideInstallModal();
                notice('探针安装成功', 'success');
                // 刷新页面以更新探针状态
                setTimeout(() => {
                    location.reload();
                }, 1500);
            }, 2000);
        } else {
            const errorMsg = res.data || '探针安装失败';
            updateInstallStatus('安装失败：' + errorMsg);
            console.error('探针安装失败:', errorMsg);

            // 延迟关闭模态框，让用户看到错误信息
            setTimeout(() => {
                hideInstallModal();
                notice(errorMsg, 'error');
                
                // 提供常见问题的解决建议
                if (errorMsg.includes('SSH密码配置错误') || errorMsg.includes('脱敏密码')) {
                    setTimeout(() => {
                        notice('SSH密码配置问题：请重新编辑服务器并输入正确的SSH密码', 'warning');
                    }, 1000);
                    setTimeout(() => {
                        notice('建议：您也可以使用SSH私钥认证代替密码认证', 'info');
                    }, 2000);
                } else if (errorMsg.includes('SSH')) {
                    setTimeout(() => {
                        notice('SSH连接失败，请先确保SSH配置正确', 'info');
                    }, 1000);
                } else if (errorMsg.includes('permission') || errorMsg.includes('权限')) {
                    setTimeout(() => {
                        notice('权限不足，请确保SSH用户有sudo权限', 'info');
                    }, 1000);
                } else if (errorMsg.includes('network') || errorMsg.includes('网络')) {
                    setTimeout(() => {
                        notice('网络连接失败，请检查服务器网络连接', 'info');
                    }, 1000);
                }
            }, 1500);
        }
    } catch (error) {
        console.error('探针安装异常:', error);
        updateInstallStatus('安装异常：' + error.message);
        setTimeout(() => {
            hideInstallModal();
            notice('探针安装失败: ' + (error.message || '发生未知错误'), 'error');
        }, 1500);
    }
}

// 卸载探针
async function uninstallProbe() {
    try {
        const sid = V('sid');
        if (!sid) {
            notice('服务器ID不能为空', 'error');
            return;
        }

        // 确认卸载
        if (!confirm('确认卸载探针？\\n\\n此操作将：\\n1. 停止探针服务\\n2. 删除探针文件\\n3. 清理相关配置\\n\\n此操作不可恢复！')) {
            return;
        }

        startloading();

        // 发送卸载请求
        const res = await postjson(`/admin/servers/${sid}/remove`);

        if (res.status) {
            notice('探针卸载成功', 'success');
            console.log('探针卸载结果:', res.data);

            // 显示卸载详情
            if (res.data && res.data.details) {
                setTimeout(() => {
                    const details = res.data.details;
                    let message = '探针卸载成功！\\n\\n卸载详情：\\n';
                    if (details.removed) message += `删除文件: ${details.removed.join(', ')}\\n`;
                    if (details.stopped) message += `停止服务: ${details.stopped}\\n`;

                    console.log('卸载详情:', details);
                    notice(message, 'info');
                }, 1000);
            }

            // 刷新页面以更新探针状态
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            const errorMsg = res.data || '探针卸载失败';
            notice(errorMsg, 'error');
            console.error('探针卸载失败:', errorMsg);

            // 提供常见问题的解决建议
            if (errorMsg.includes('SSH密码配置错误') || errorMsg.includes('脱敏密码')) {
                setTimeout(() => {
                    notice('SSH密码配置问题：请重新编辑服务器并输入正确的SSH密码', 'warning');
                }, 1000);
            } else if (errorMsg.includes('not found') || errorMsg.includes('未找到')) {
                setTimeout(() => {
                    notice('探针未安装或已被删除', 'info');
                }, 1000);
            } else if (errorMsg.includes('permission') || errorMsg.includes('权限')) {
                setTimeout(() => {
                    notice('权限不足，请确保SSH用户有sudo权限', 'info');
                }, 1000);
            }
        }
    } catch (error) {
        console.error('探针卸载异常:', error);
        notice('探针卸载失败: ' + (error.message || '发生未知错误'), 'error');
    } finally {
        endloading();
    }
}

// 更新探针
async function updateProbe() {
    // 更新操作与安装相同
    installProbe();
}

// 检查探针状态
async function checkProbeStatus() {
    try {
        startloading();

        const sid = V('sid');
        if (!sid) {
            notice('服务器ID不能为空', 'error');
            return;
        }

        // 发送状态检查请求
        const res = await postjson(`/admin/servers/${sid}/probe-status`);

        if (res.status) {
            const status = res.data || {};
            console.log('探针状态:', status);

            // 显示状态信息
            let message = '探针状态：\\n';
            message += `安装状态: ${status.installed ? '已安装' : '未安装'}\\n`;
            if (status.version) message += `版本: ${status.version}\\n`;
            if (status.running !== undefined) message += `运行状态: ${status.running ? '运行中' : '已停止'}\\n`;
            if (status.lastUpdate) message += `最后更新: ${new Date(status.lastUpdate).toLocaleString()}\\n`;

            notice(message, 'info');

            // 更新页面状态显示
            updateProbeStatusUI(status);
        } else {
            const errorMsg = res.data || '探针状态检查失败';
            notice(errorMsg, 'error');
            console.error('探针状态检查失败:', errorMsg);
        }
    } catch (error) {
        console.error('探针状态检查异常:', error);
        notice('探针状态检查失败: ' + (error.message || '发生未知错误'), 'error');
    } finally {
        endloading();
    }
}

// 更新探针状态UI
function updateProbeStatusUI(status) {
    // 查找状态显示元素
    const statusElement = document.getElementById('probe-status');
    if (!statusElement) return;

    // 根据状态更新UI
    if (status.installed) {
        statusElement.innerHTML = `
            <span class="text-green-600">
                <i class="ti ti-circle-check"></i>
                已安装 v${status.version || '未知'}
            </span>
        `;
    } else {
        statusElement.innerHTML = `
            <span class="text-gray-500">
                <i class="ti ti-x"></i>
                未安装
            </span>
        `;
    }
}

// 函数别名，保持与HTML调用一致
const init = installProbe;
const uninstall = uninstallProbe;