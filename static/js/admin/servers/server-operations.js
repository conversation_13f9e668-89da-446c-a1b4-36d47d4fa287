/**
 * 服务器操作相关函数
 */

// 编辑服务器
async function edit() {
    try {
        startloading();

        // 基础数据验证
        const name = V('edit_name');
        const host = V('edit_ssh_host');

        if (!name || !host) {
            notice('服务器名称和IP地址不能为空', 'error');
            return;
        }

        // 验证SSH端口号
        const sshPort = parseInt(V('edit_ssh_port')) || 22;
        if (sshPort < 1 || sshPort > 65535) {
            notice('SSH端口号必须在1-65535之间', 'error');
            return;
        }

        // 验证API端口号
        const apiPort = parseInt(V('edit_api_port')) || 8080;
        if (apiPort < 1 || apiPort > 65535) {
            notice('API端口号必须在1-65535之间', 'error');
            return;
        }

        // 构造服务器配置数据 - 不检查SSH凭据，允许无凭据保存
        const serverData = {
            ssh: {
                host: host,
                port: sshPort,
                username: V('edit_ssh_username'),
                password: V('edit_ssh_password'),
                privateKey: (function() {
                    const key = V('edit_ssh_privateKey');
                    // 如果有 SSHKeyFormatter，使用它格式化私钥
                    if (typeof SSHKeyFormatter !== 'undefined' && key) {
                        return SSHKeyFormatter.format(key);
                    }
                    return key;
                })()
            },
            api: {
                mode: document.getElementById('edit_api_mode').checked,
                key: V('edit_api_key') || '',
                port: apiPort
            },
            device: V('edit_device') || ''
        };

        // 添加位置信息 - 智能合并而非覆盖
        const isManualLocation = document.getElementById('edit_location_manual').checked;

        if (isManualLocation) {
            // 手动设置模式
            const countryCode = V('edit_location_country_code');
            
            if (countryCode && countryCode !== '') {
                // 用户选择了新地区 - 构造完整位置信息
                const countryNameElement = document.getElementById('selected-country-name');
                const countryName = countryNameElement ? countryNameElement.textContent.trim() : countryCode;
                
                let flagUrl = null;
                if (countryCode === 'LO' || countryCode === 'OT') {
                    flagUrl = null;
                } else if (countryCode === 'UK') {
                    flagUrl = '/img/flags/GB.SVG';
                } else {
                    flagUrl = `/img/flags/${countryCode}.SVG`;
                }
                
                serverData.location = {
                    code: countryCode,
                    flag: flagUrl,
                    country_name: countryName,
                    name_zh: countryName,
                    manual: true,
                    updated_at: Date.now()
                };
            } else {
                // 手动模式但没有选择新地区 - 保留原有位置信息
                serverData.location = {
                    manual: true,
                    preserveExisting: true  // 标记保留现有位置信息
                };
            }
        } else {
            // 自动模式 - 仅发送模式标记，不覆盖现有位置信息
            serverData.location = {
                manual: false,
                preserveExisting: true  // 标记保留现有位置信息
            };
        }

        // 添加标签数据到服务器数据中 - 始终包含标签字段，即使为空
        // 确保 currentTags 是一个数组
        if (typeof currentTags === 'undefined' || !Array.isArray(currentTags)) {
            console.warn('currentTags 未定义或不是数组，使用空数组');
            serverData.tags = [];
        } else {
            serverData.tags = currentTags;
            console.log('提交标签数据:', currentTags.length, '个标签');
        }

        // 构造请求数据 - 修复双重JSON序列化问题
        const requestData = {
            name,
            data: serverData, // 移除JSON.stringify，避免双重序列化
            status: parseInt(V('edit_status')),
            top: parseInt(V('edit_top')) || 0,
            expire_time: V('edit_expire_time') ?
                Math.floor(new Date(V('edit_expire_time') + ' 23:59:59').getTime() / 1000) :
                null,
            group_id: V('edit_group_id') || 'default',
            traffic_limit: TrafficFormat.gbToBytes(V('edit_traffic_limit')),
            traffic_reset_day: parseInt(V('edit_traffic_reset_day')) || 1,
            traffic_alert_percent: parseInt(V('edit_traffic_alert_percent')) || 80,
            traffic_calibration_date: V('edit_traffic_calibration_date') ?
                Math.floor(new Date(V('edit_traffic_calibration_date')).getTime() / 1000) :
                null,
            traffic_calibration_value: TrafficFormat.gbToBytes(V('edit_traffic_calibration_value')),
            traffic_direction: V('edit_traffic_direction') || 'both'
        };

        console.log('提交的数据:', requestData);

        const sid = V('sid');
        const res = await postjson(`/admin/servers/${sid}/edit`, requestData);

        if (res.status) {
            notice('保存成功', 'success');
            // 保存成功后刷新服务器数据和页面显示
            await refreshServerData();
        } else {
            notice(res.data || '保存失败，请检查必填项', 'error');
        }
    } catch (error) {
        console.error('保存失败:', error);
        // 优化错误提示
        if (error.message && error.message.includes('handleInstallation')) {
            notice('保存成功，但自动安装失败。您可以稍后手动安装。', 'info');
        } else {
            notice('保存失败: ' + (error.message || '发生未知错误'), 'error');
        }
    } finally {
        endloading();
    }
}

// 删除服务器
async function del() {
    try {
        startloading();
        const sid = V('sid');
        const serverName = V('edit_name');

        // 获取相关数据统计
        const res = await postjson(`/admin/servers/${sid}/stats`);
        endloading();

        if (!res.status) {
            notice(res.data || '获取服务器统计信息失败', 'error');
            return;
        }

        // 获取统计数据
        const stats = res.data;
        console.log('服务器统计信息:', stats);

        // 构建确认消息
        let confirmMessage = `确认删除服务器 "${serverName}" 及其所有相关数据?\n\n`;
        confirmMessage += "此操作将同时删除以下数据:\n";
        confirmMessage += `- 服务器基本信息\n`;

        // 流量统计数据
        if (stats.traffic > 0) {
            confirmMessage += `- 流量统计数据: ${stats.traffic} 条记录\n`;
        }

        // 负载统计数据
        if (stats.load > 0) {
            confirmMessage += `- 负载统计数据: ${stats.load} 条记录\n`;
        }

        // 网络质量监控数据
        if (stats.tcping > 0) {
            confirmMessage += `- 网络质量监控数据: ${stats.tcping} 条记录\n`;
        }

        if (stats.monitorTargets > 0) {
            confirmMessage += `- 监控目标引用: ${stats.monitorTargets} 个目标将被更新\n`;
        }

        confirmMessage += "\n此操作不可恢复，请确认!";

        if (!confirm(confirmMessage)) return;

        // 执行删除操作
        startloading();
        const delRes = await postjson(`/admin/servers/${sid}/del`);
        endloading();

        if (delRes.status) {
            notice('删除成功', 'success');

            // 检查当前页面是否已经是服务器列表页面
            const currentPath = window.location.pathname;
            if (currentPath !== '/admin/servers') {
                // 如果不是列表页面，则跳转到列表页面
                setTimeout(() => location.href = '/admin/servers', 1000);
            } else {
                // 如果已经在列表页面，则刷新页面
                setTimeout(() => location.reload(), 1000);
            }
        } else {
            notice(delRes.data || '删除失败', 'error');
        }
    } catch (error) {
        console.error('删除失败:', error);
        notice('删除失败: ' + (error.message || '发生未知错误'), 'error');
    } finally {
        endloading();
    }
}

// 刷新服务器数据 - 简化版本，重新加载页面以获取最新数据
async function refreshServerData() {
    try {
        console.log('编辑成功，重新加载页面以获取最新数据...');
        // 延迟1秒后重新加载页面，确保后端数据已更新
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    } catch (error) {
        console.warn('页面重新加载失败:', error);
    }
}
