/**
 * 安装进度模态框模块
 */

// 显示安装进度模态框
function showInstallModal(title = '系统安装中', status = '正在安装系统组件...') {
    const modal = document.getElementById('install-progress-modal');
    const spinner = document.getElementById('install-spinner');
    if (modal) {
        modal.querySelector('h3').textContent = title;
        document.getElementById('install-status').textContent = status;
        document.getElementById('install-log').textContent = ''; // 清空日志
        if (spinner) spinner.style.display = 'block'; // 确保旋转图标显示
        modal.classList.remove('hidden');
        modal.classList.add('flex');

        // 设置20秒后自动关闭
        window.installModalTimer = setTimeout(() => {
            hideInstallModal();
        }, 20000);
    }
}

// 隐藏安装进度模态框
function hideInstallModal() {
    const modal = document.getElementById('install-progress-modal');
    if (modal) {
        modal.classList.add('hidden');
        modal.classList.remove('flex');

        // 清除自动关闭计时器
        if (window.installModalTimer) {
            clearTimeout(window.installModalTimer);
            window.installModalTimer = null;
        }
    }
}

// 更新安装状态
function updateInstallStatus(status) {
    const statusElement = document.getElementById('install-status');
    if (statusElement) {
        statusElement.textContent = status;
    }
}

// 更新安装日志
function updateInstallLog(log) {
    const logElement = document.getElementById('install-log');
    if (logElement) {
        logElement.textContent = log;
        // 滚动到日志底部
        const container = document.getElementById('install-log-container');
        if (container) {
            container.scrollTop = container.scrollHeight;
        }
    }
}

// 流式更新安装日志
function streamInstallLog(logs) {
    if (!Array.isArray(logs) || logs.length === 0) return;

    const logElement = document.getElementById('install-log');
    const container = document.getElementById('install-log-container');
    if (!logElement || !container) return;

    let index = 0;
    const interval = 100; // 每100毫秒更新一行

    // 清除之前的流式输出计时器
    if (window.streamLogTimer) {
        clearInterval(window.streamLogTimer);
    }

    // 开始流式输出
    window.streamLogTimer = setInterval(() => {
        if (index < logs.length) {
            // 追加新的日志行
            logElement.textContent += logs[index] + '\n';
            // 滚动到底部
            container.scrollTop = container.scrollHeight;
            index++;
        } else {
            // 所有日志输出完毕，清除计时器
            clearInterval(window.streamLogTimer);
            window.streamLogTimer = null;
        }
    }, interval);
}