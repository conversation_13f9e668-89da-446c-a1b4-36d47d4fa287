/**
 * 表单相关工具函数
 */

// 工具函数
function timestampToDate(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp * 1000);
    return date.toISOString().split('T')[0];
}

function dateStringToTimestamp(dateString) {
    if (!dateString) return 0;
    const date = new Date(dateString);
    return Math.floor(date.getTime() / 1000);
}

// 初始化表单数据
function initForm(server) {
    console.log('初始化服务器数据:', server);

    // 基本信息
    document.getElementById('sid').value = server.sid;
    document.getElementById('edit_name').value = server.name;
    document.getElementById('edit_status').value = server.status;
    document.getElementById('edit_top').value = server.top;
    if (server.expire_time) {
        document.getElementById('edit_expire_time').value = timestampToDate(server.expire_time);
    }

    // SSH设置
    document.getElementById('edit_ssh_host').value = server.data.ssh.host;
    document.getElementById('edit_ssh_port').value = server.data.ssh.port || '';
    document.getElementById('edit_ssh_username').value = server.data.ssh.username || '';
    document.getElementById('edit_ssh_password').value = server.data.ssh.password || '';
    document.getElementById('edit_ssh_privateKey').value = server.data.ssh.privateKey || '';

    // 设备和API设置
    document.getElementById('edit_device').value = server.data.device || '';
    document.getElementById('edit_api_key').value = server.data.api?.key || '';
    document.getElementById('edit_api_port').value = server.data.api?.port || '';

    // API模式设置
    const apiMode = server.data.api?.mode;
    const apiModeCheckbox = document.getElementById('edit_api_mode');
    const apiModeToggle = document.getElementById('edit-api-mode-toggle');
    const apiModeText = document.getElementById('edit-api-mode-text');

    if (apiModeCheckbox) {
        apiModeCheckbox.checked = apiMode === true; // 仅当明确为 true 时才是主动模式

        if (apiModeCheckbox.checked) {
            // 主动模式
            apiModeToggle.classList.add('translate-x-5', 'bg-blue-500');
            apiModeToggle.classList.remove('bg-slate-400');
            apiModeText.textContent = '主动模式';
        } else {
            // 被动模式
            apiModeToggle.classList.remove('translate-x-5', 'bg-blue-500');
            apiModeToggle.classList.add('bg-slate-400');
            apiModeText.textContent = '被动模式';
        }
    }

    // 流量设置
    document.getElementById('edit_traffic_limit').value = TrafficFormat.bytesToGB(server.traffic_limit);
    document.getElementById('edit_traffic_reset_day').value = server.traffic_reset_day || 1;
    document.getElementById('edit_traffic_alert_percent').value = server.traffic_alert_percent || 80;
    document.getElementById('edit_traffic_calibration_date').value = timestampToDate(server.traffic_calibration_date);
    document.getElementById('edit_traffic_calibration_value').value = TrafficFormat.bytesToGB(server.traffic_calibration_value);
    
    // 流量方向设置
    const trafficDirection = document.getElementById('edit_traffic_direction');
    if (trafficDirection && server.traffic_direction) {
        trafficDirection.value = server.traffic_direction;
    }
    
    // 分组设置
    const groupId = document.getElementById('edit_group_id');
    if (groupId && server.group_id) {
        groupId.value = server.group_id;
    }

    // 初始化位置信息显示
    initLocationDisplay(server);
}

// 初始化位置信息显示
function initLocationDisplay(server) {
    console.log('初始化位置信息显示:', server.data?.location);
    
    // 设置手动模式状态
    const manualCheckbox = document.getElementById('edit_location_manual');
    const countryCodeInput = document.getElementById('edit_location_country_code');
    
    if (manualCheckbox && server.data?.location) {
        manualCheckbox.checked = server.data.location.manual || false;
        
        // 设置国家代码
        if (countryCodeInput && server.data.location.code) {
            countryCodeInput.value = server.data.location.code;
        }
        
        // 显示当前位置信息
        updateLocationDisplay(server.data.location);
    }
}

// 更新位置信息显示
function updateLocationDisplay(locationData) {
    if (!locationData) return;
    
    const flagElement = document.getElementById('selected-country-flag');
    const nameElement = document.getElementById('selected-country-name');
    const codeElement = document.getElementById('selected-country-code');
    
    if (flagElement && nameElement && codeElement) {
        // 更新国旗显示
        if (locationData.code === 'LO' || locationData.code === 'OT') {
            // 本地网络和其他网络使用图标
            flagElement.innerHTML = '<i class="ti ti-world text-lg"></i>';
        } else if (locationData.flag) {
            // 使用已保存的国旗路径
            flagElement.innerHTML = `<img src="${locationData.flag}" alt="${locationData.name_zh || locationData.country_name || locationData.code}" class="w-5 h-4 object-cover rounded">`;
        } else {
            // 根据国家代码生成国旗路径
            const flagUrl = locationData.code === 'UK' ? '/img/flags/GB.SVG' : `/img/flags/${locationData.code}.SVG`;
            flagElement.innerHTML = `<img src="${flagUrl}" alt="${locationData.name_zh || locationData.country_name || locationData.code}" class="w-5 h-4 object-cover rounded">`;
        }
        
        // 更新国家名称
        const displayName = locationData.name_zh || locationData.country_name || locationData.code;
        nameElement.textContent = displayName;
        
        // 更新国家代码
        codeElement.textContent = locationData.code;
        
        console.log('位置信息显示已更新:', {
            code: locationData.code,
            name: displayName,
            flag: locationData.flag
        });
    }
}

// 实时端口验证函数
function validatePortInput(inputId, errorId) {
    const input = document.getElementById(inputId);
    const errorElement = document.getElementById(errorId);
    
    if (!input || !errorElement) return;
    
    input.addEventListener('input', function() {
        const port = parseInt(this.value);
        
        if (!this.value) {
            errorElement.classList.add('hidden');
            this.classList.remove('border-red-500', 'dark:border-red-500');
            return;
        }
        
        if (isNaN(port) || port < 1 || port > 65535) {
            errorElement.textContent = '端口号必须在1-65535之间';
            errorElement.classList.remove('hidden');
            this.classList.add('border-red-500', 'dark:border-red-500');
        } else {
            errorElement.classList.add('hidden');
            this.classList.remove('border-red-500', 'dark:border-red-500');
        }
    });
}
