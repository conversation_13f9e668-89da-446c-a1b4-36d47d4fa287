/**
 * 服务器添加核心功能
 */

// 添加服务器主函数
async function add() {
    try {
        // 基本验证
        const name = V('add_name');
        if (!name || name.trim() === '') {
            notice('请输入服务器名称', 'error');
            return;
        }

        const sshHost = V('add_ssh_host');
        if (!sshHost || sshHost.trim() === '') {
            notice('请输入服务器地址', 'error');
            return;
        }

        // 验证IP地址格式
        // IPv4 正则表达式：支持标准的点分十进制格式
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        
        // IPv6 验证函数：支持所有标准格式
        function isValidIPv6(ip) {
            // 移除可能的方括号（用于带端口的格式，如 [::1]:8080）
            const cleanIp = ip.replace(/^\[|\]$/g, '');
            
            // IPv6 正则表达式，支持：
            // 1. 完整格式：2001:0db8:0000:0000:0000:ff00:0042:8329
            // 2. 压缩格式：2001:db8::ff00:42:8329
            // 3. 本地回环：::1
            // 4. 全零地址：::
            // 5. IPv4映射地址：::ffff:*********
            
            // 基础的IPv6段匹配（1到4位十六进制）
            const hex = '[0-9a-fA-F]{1,4}';
            
            // IPv4地址部分（用于IPv4映射地址）
            const ipv4 = '(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)';
            
            // 完整的IPv6正则表达式
            const ipv6Regex = new RegExp('^(' +
                // 1. 完整格式（8组）
                `(?:${hex}:){7}${hex}|` +
                // 2. 压缩格式（使用::）
                `(?:${hex}:){1,7}:|` +
                `(?:${hex}:){1,6}:${hex}|` +
                `(?:${hex}:){1,5}(?::${hex}){1,2}|` +
                `(?:${hex}:){1,4}(?::${hex}){1,3}|` +
                `(?:${hex}:){1,3}(?::${hex}){1,4}|` +
                `(?:${hex}:){1,2}(?::${hex}){1,5}|` +
                `${hex}:(?::${hex}){1,6}|` +
                ':(?:(?::' + hex + '){1,7}|:)|' +
                // 3. IPv4映射格式
                `(?:${hex}:){6}${ipv4}|` +
                `::(?:${hex}:){0,5}${ipv4}|` +
                `(?:${hex}:){1}(?::${hex}){0,4}:${ipv4}|` +
                `(?:${hex}:){2}(?::${hex}){0,3}:${ipv4}|` +
                `(?:${hex}:){3}(?::${hex}){0,2}:${ipv4}|` +
                `(?:${hex}:){4}(?::${hex})?:${ipv4}|` +
                `(?:${hex}:){5}:${ipv4}` +
                ')$', 'i');
            
            return ipv6Regex.test(cleanIp);
        }
        
        // 检查是否为域名（简单验证）
        const domainRegex = /^([a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]\.)*[a-zA-Z]{2,}$/;
        
        // 验证输入的地址
        if (!ipv4Regex.test(sshHost) && !isValidIPv6(sshHost) && !domainRegex.test(sshHost)) {
            notice('请输入有效的IP地址（IPv4、IPv6）或域名。支持的IPv6格式包括：完整格式、压缩格式(::)、IPv4映射格式等', 'error');
            return;
        }

        // 验证端口号
        const sshPort = parseInt(V('add_ssh_port')) || 22;
        if (sshPort < 1 || sshPort > 65535) {
            notice('端口号必须在1-65535之间', 'error');
            return;
        }

        // 验证用户名
        const sshUsername = V('add_ssh_username');
        if (!sshUsername || sshUsername.trim() === '') {
            notice('请输入SSH用户名', 'error');
            return;
        }

        // 验证密码或私钥（改为非必填，但仍显示警告）
        const sshPassword = V('add_ssh_password');
        const sshPrivateKey = V('add_ssh_privateKey');
        const hasCredentials = Boolean(sshPassword || sshPrivateKey);
        if (!hasCredentials) {
            notice('未提供SSH密码或私钥，此服务器将只能记录信息，无法安装探针或执行远程操作', 'warning');
            // 继续执行，不返回
        }

        startloading();

        // 使用已生成的 API key 作为服务器 ID
        const sid = V('add_api_key');
        const status = parseInt(E('server_status').value);

        // 处理到期时间
        const expireTime = V('add_expire_time') ?
            Math.floor(new Date(V('add_expire_time') + ' 23:59:59').getTime() / 1000) :
            null;

        // 构建SSH配置数据
        const sshConfig = {
            host: sshHost.trim(),
            port: sshPort,
            username: sshUsername.trim(),
            password: sshPassword
        };

        // 添加私钥（如果有）
        if (sshPrivateKey && sshPrivateKey.trim() !== '') {
            // 安全处理SSH私钥，确保特殊字符不会导致JSON序列化失败
            try {
                // 验证私钥格式并清理
                const cleanedPrivateKey = sshPrivateKey.trim();

                // 基本的私钥格式验证
                if (cleanedPrivateKey.includes('-----BEGIN') && cleanedPrivateKey.includes('-----END')) {
                    sshConfig.privateKey = cleanedPrivateKey;
                } else {
                    // 如果不是标准格式，仍然保存但添加警告
                    console.warn('SSH私钥格式可能不正确，但仍将保存');
                    sshConfig.privateKey = cleanedPrivateKey;
                }

                // 添加私钥密码（如果有）
                const passphrase = V('add_ssh_passphrase');
                if (passphrase && passphrase.trim() !== '') {
                    sshConfig.passphrase = passphrase.trim();
                }
            } catch (error) {
                console.error('处理SSH私钥时出错:', error);
                notice('SSH私钥格式可能有问题，请检查', 'warning');
                // 仍然尝试保存原始内容
                sshConfig.privateKey = sshPrivateKey.trim();
            }
        }

        // 验证API端口号
        const apiPort = parseInt(V('add_api_port')) || 9999;
        if (apiPort < 1 || apiPort > 65535) {
            notice('API端口号必须在1-65535之间', 'error');
            return;
        }

        const data = {
            ssh: sshConfig,
            api: {
                mode: document.getElementById('add_api_mode').checked,
                key: sid,
                port: apiPort,
            },
            device: V('add_device') || 'eth0',
            recordOnly: !hasCredentials
        };

        // 添加位置信息（如果手动设置）
        const locationManual = document.getElementById('add_location_manual');
        if (locationManual && locationManual.checked) {
            const countryCode = V('add_location_country_code');
            if (countryCode) {
                data.location = {
                    manual: true,
                    code: countryCode
                };
            }
        }

        // 添加流量相关数据
        const trafficLimit = parseInt(V('add_traffic_limit')) || 0;
        const trafficResetDay = parseInt(V('add_traffic_reset_day')) || 1;
        const trafficAlertPercent = parseInt(V('add_traffic_alert_percent')) || 80;
        const trafficDirection = V('add_traffic_direction') || 'both';

        // 处理流量校准数据
        const calibrationDate = V('add_traffic_calibration_date');
        const calibrationValue = parseFloat(V('add_traffic_calibration_value')) || 0;
        const calibrationTime = calibrationDate ?
            Math.floor(new Date(calibrationDate + ' 00:00:00').getTime() / 1000) :
            null;

        // 获取分组ID
        const groupId = V('add_group_id') || 'default';

        // 修复双重JSON序列化问题：直接传递data对象，让postjson函数处理序列化
        const res = await postjson('/admin/servers/add', {
            sid,
            name: name.trim(),
            data: data, // 移除JSON.stringify，避免双重序列化
            status,
            expire_time: expireTime,
            group_id: groupId,
            traffic_limit: trafficLimit ? trafficLimit * 1024 * 1024 * 1024 : 0,
            traffic_reset_day: trafficResetDay,
            traffic_alert_percent: trafficAlertPercent,
            traffic_calibration_date: calibrationTime,
            traffic_calibration_value: calibrationValue ? calibrationValue * 1024 * 1024 * 1024 : 0,
            traffic_direction: trafficDirection
        });

        if (res.status) {
            if (hasCredentials) {
                // 有凭据，弹窗询问是否安装探针
                if (confirm('服务器添加成功！是否立即安装探针？')) {
                    await handleInstallation(res.data.serverId);
                } else {
                    notice('服务器已添加，您可以稍后手动安装探针', 'success');
                    setTimeout(() => location.href = '/admin/servers', 1000);
                }
            } else {
                // 没有凭据，直接提示
                notice('服务器已添加（仅记录模式）', 'success');
                setTimeout(() => location.href = '/admin/servers', 1000);
            }
        } else {
            // T056: 检查是否为节点限制错误
            if (res.error === 'NODE_LIMIT_EXCEEDED' || res.error === 'GRANDFATHERED_LIMIT') {
                showNodeLimitDialog(res.data);
            } else {
                notice(res.msg || res.data || '添加失败', 'error');
            }
        }
    } catch (error) {
        console.error('添加服务器失败:', error);
        notice(error.message || '添加服务器失败', 'error');
    } finally {
        endloading();
    }
}

// 自动安装处理函数
async function handleInstallation(sid) {
    try {
        showInstallModal('正在安装探针', '准备安装...');
        const res = await postjson(`/admin/servers/${sid}/init`);
        
        if (res.status) {
            hideInstallModal();
            notice('探针安装成功', 'success');
            setTimeout(() => location.href = '/admin/servers', 1500);
        } else {
            hideInstallModal();
            notice(res.msg || res.data || '探针安装失败', 'error');
            setTimeout(() => location.href = '/admin/servers', 2000);
        }
    } catch (error) {
        hideInstallModal();
        console.error('安装探针失败:', error);
        notice('探针安装失败: ' + error.message, 'error');
        setTimeout(() => location.href = '/admin/servers', 2000);
    }
}

// 页面加载后绑定实时验证（使用form-utils.js中的validatePortInput函数）
document.addEventListener('DOMContentLoaded', function() {
    validatePortInput('add_ssh_port', 'ssh_port_error');
    validatePortInput('add_api_port', 'api_port_error');
});