/**
 * 智能悬浮操作栏模块
 * 用于在页面滚动时显示悬浮的操作按钮栏
 */

function initFloatingActionBar() {
    console.log('[FloatingActionBar] 初始化开始');
    const actionBar = document.getElementById('action-bar');
    if (!actionBar) {
        console.error('[FloatingActionBar] 错误：找不到 action-bar 元素');
        return;
    }
    console.log('[FloatingActionBar] 找到 action-bar 元素', actionBar);
    
    // 创建悬浮容器
    const floatingContainer = document.createElement('div');
    floatingContainer.className = 'fixed bottom-0 left-0 right-0 z-50';
    floatingContainer.style.paddingBottom = 'env(safe-area-inset-bottom)';
    floatingContainer.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
    floatingContainer.style.transform = 'translateY(100%)';
    floatingContainer.style.opacity = '0';
    floatingContainer.innerHTML = `
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row gap-6">
                <div class="hidden md:block" style="width: 16rem;"></div>
                <div class="flex-1">
                    <div class="bg-white/95 dark:bg-slate-800/95 backdrop-blur-md rounded-xl shadow-2xl border border-slate-200/60 dark:border-slate-700/40" id="floating-action-bar">
                        ${actionBar.innerHTML}
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(floatingContainer);
    console.log('[FloatingActionBar] 悬浮容器已创建并添加到页面');
    
    // 获取悬浮栏元素
    const floatingBar = document.getElementById('floating-action-bar');
    
    // 同步事件处理器
    floatingBar.querySelectorAll('button').forEach((btn, index) => {
        const originalBtn = actionBar.querySelectorAll('button')[index];
        if (originalBtn && originalBtn.onclick) {
            btn.onclick = originalBtn.onclick;
        }
    });
    
    // 同步链接点击
    floatingBar.querySelectorAll('a').forEach((link, index) => {
        const originalLink = actionBar.querySelectorAll('a')[index];
        if (originalLink) {
            link.href = originalLink.href;
        }
    });
    
    let isFloatingVisible = false;
    
    function checkScroll() {
        const rect = actionBar.getBoundingClientRect();
        const scrollY = window.scrollY;
        const windowHeight = window.innerHeight;
        
        // 计算操作栏是否在视口内完全可见
        const isFullyVisible = rect.top >= 0 && rect.bottom <= windowHeight;
        // 计算操作栏是否部分或完全不可见
        const isPartiallyHidden = rect.top < 0 || rect.bottom > windowHeight;
        
        console.log('[FloatingActionBar] 滚动检查:', {
            scrollY,
            rectTop: rect.top,
            rectBottom: rect.bottom,
            windowHeight,
            isFullyVisible,
            isPartiallyHidden,
            shouldShow: isPartiallyHidden && scrollY > 100  // 避免页面顶部时显示
        });
        
        // 当操作栏不完全可见时显示悬浮栏
        // 但要避免在页面顶部附近显示（scrollY > 100）
        if (isPartiallyHidden && scrollY > 100) {
            if (!isFloatingVisible) {
                console.log('[FloatingActionBar] 显示悬浮栏');
                floatingContainer.style.transform = 'translateY(0)';
                floatingContainer.style.opacity = '1';
                isFloatingVisible = true;
            }
        } else {
            if (isFloatingVisible) {
                console.log('[FloatingActionBar] 隐藏悬浮栏');
                floatingContainer.style.transform = 'translateY(100%)';
                floatingContainer.style.opacity = '0';
                isFloatingVisible = false;
            }
        }
    }
    
    // 优化滚动性能
    let ticking = false;
    function requestTick() {
        if (!ticking) {
            window.requestAnimationFrame(checkScroll);
            ticking = true;
            setTimeout(() => { ticking = false; }, 50);
        }
    }
    
    window.addEventListener('scroll', requestTick);
    window.addEventListener('resize', checkScroll);
    
    // 初始检查
    console.log('[FloatingActionBar] 设置初始检查');
    setTimeout(() => {
        console.log('[FloatingActionBar] 执行初始检查');
        checkScroll();
    }, 100);
    
    console.log('[FloatingActionBar] 初始化完成');
}

// 导出函数供外部使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { initFloatingActionBar };
}