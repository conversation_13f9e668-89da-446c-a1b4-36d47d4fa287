# 服务器管理页面共用 JavaScript 模块

## 概述
本文档记录了服务器添加（add.html）和编辑（edit.html）页面共用的 JavaScript 模块。

## 2024-01-15 更新
已完成 add.html 页面的 JavaScript 代码完全模块化，所有内联代码已分离到独立模块中。

## 共用模块列表

### 1. floating-action-bar.js
**功能**: 智能悬浮操作栏
- 当用户滚动页面时，自动显示悬浮的操作按钮栏
- 保持操作按钮始终可见，提升用户体验
- 自动同步原始操作栏的事件处理器

**使用方法**:
```javascript
window.addEventListener('load', function() {
    if (typeof initFloatingActionBar === 'function') {
        initFloatingActionBar();
    }
});
```

### 2. password-toggle.js
**功能**: 密码显示/隐藏切换
- 为所有带有 `.toggle-password` 类的按钮添加切换功能
- 点击时切换关联的密码输入框的显示状态

**使用方法**:
```javascript
if (typeof initPasswordToggle === 'function') {
    initPasswordToggle();
}
```

### 3. keyboard-shortcuts.js
**功能**: 键盘快捷键支持
- Ctrl+S / Cmd+S: 快速保存
- ESC: 返回上一页
- 首次使用时显示提示信息

**使用方法**:
```javascript
if (typeof initKeyboardShortcuts === 'function') {
    initKeyboardShortcuts({
        saveCallback: () => { /* 保存操作 */ },
        escCallback: () => { /* 返回操作 */ },
        showTip: true,
        tipKey: 'uniqueKeyForTip'
    });
}
```

## 新增的共用模块（从 add.html 分离）

### 4. ssh-test-common.js
**功能**: 通用SSH连接测试
- 支持 add 和 edit 页面，通过前缀参数区分
- `testSSHConnectionCommon(prefix)` - 核心函数
- `testSSHConnectionForAdd()` - add 页面专用
- `testSSHConnectionForEdit()` - edit 页面专用

### 5. location-common.js
**功能**: 位置管理功能
- `toggleLocationManual(prefix)` - 切换手动位置设置
- `fetchLocationInfo(serverId)` - 获取服务器位置信息
- `initCountryList()` - 初始化国家列表

### 6. file-upload.js
**功能**: 文件上传处理
- `initFileUpload()` - 初始化所有文件上传元素
- `handlePrivateKeyUpload(event)` - 处理私钥文件上传

### 7. api-mode-toggle.js
**功能**: API模式切换
- `toggleApiMode(prefix)` - 切换主动/被动模式
- `initApiModeToggles()` - 初始化所有API模式开关

### 8. node-limit-dialog.js
**功能**: 节点限制弹窗
- `showNodeLimitDialog(limitInfo)` - 显示节点限制信息
- `hideNodeLimitModal()` - 隐藏弹窗

### 9. install-progress.js
**功能**: 安装进度模态框
- `showInstallModal(title, status)` - 显示安装进度
- `hideInstallModal()` - 隐藏模态框
- `updateInstallStatus(status)` - 更新状态文本
- `updateInstallLog(log)` - 更新日志内容
- `streamInstallLog(logs)` - 流式显示日志

### 10. server-add.js
**功能**: 服务器添加核心逻辑
- `add()` - 添加服务器主函数
- `handleInstallation(sid)` - 处理探针安装

### 11. add.js / edit.js
**功能**: 页面主文件
- 初始化所有模块
- 页面特定的初始化逻辑

## 建议的进一步优化

1. **提取节点限制弹窗功能**
   - 创建 node-limit-dialog.js 模块
   - 在需要的页面引入

2. **提取文件上传功能**
   - 创建 file-upload.js 模块
   - 支持私钥文件和其他文件类型

3. **提取通用表单验证**
   - 创建 form-validation.js 模块
   - 包含 IP 地址、端口号等验证

4. **统一的安装进度弹窗**
   - 创建 install-progress.js 模块
   - 在 add 和其他需要的页面使用

## 使用示例

在 add.html 或 edit.html 中引入所需模块：

```html
<!-- 引入共用的JavaScript模块 -->
<script src="/js/admin/servers/floating-action-bar.js"></script>
<script src="/js/admin/servers/password-toggle.js"></script>
<script src="/js/admin/servers/keyboard-shortcuts.js"></script>
```

然后在页面的主 JavaScript 中初始化：

```javascript
// 页面加载完成后
document.addEventListener('DOMContentLoaded', function() {
    // 初始化密码切换
    if (typeof initPasswordToggle === 'function') {
        initPasswordToggle();
    }
    
    // 初始化键盘快捷键
    if (typeof initKeyboardShortcuts === 'function') {
        initKeyboardShortcuts({
            saveCallback: saveFunction,
            escCallback: () => window.location.href = '/admin/servers'
        });
    }
});

// 窗口加载完成后
window.addEventListener('load', function() {
    // 初始化悬浮操作栏
    if (typeof initFloatingActionBar === 'function') {
        initFloatingActionBar();
    }
});
```