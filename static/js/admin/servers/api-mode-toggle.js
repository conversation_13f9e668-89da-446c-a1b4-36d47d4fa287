/**
 * API模式切换功能模块
 * 支持主动模式和被动模式的切换
 */

function toggleApiMode(prefix) {
    const checkbox = document.getElementById(`${prefix}_api_mode`);
    const toggle = document.getElementById(`${prefix}-api-mode-toggle`);
    const text = document.getElementById(`${prefix}-api-mode-text`);
    
    if (!checkbox || !toggle || !text) {
        console.warn('API模式切换元素未找到');
        return;
    }

    // 切换复选框状态
    checkbox.checked = !checkbox.checked;
    
    // 更新UI
    if (checkbox.checked) {
        // 主动模式
        toggle.classList.add('translate-x-5');
        toggle.parentElement.classList.add('bg-blue-500');
        toggle.parentElement.classList.remove('bg-slate-300', 'dark:bg-slate-600');
        text.textContent = '主动模式';
    } else {
        // 被动模式
        toggle.classList.remove('translate-x-5');
        toggle.parentElement.classList.remove('bg-blue-500');
        toggle.parentElement.classList.add('bg-slate-300', 'dark:bg-slate-600');
        text.textContent = '被动模式';
    }
}

// 初始化所有API模式开关
function initApiModeToggles() {
    // 查找所有API模式切换元素
    const prefixes = ['add', 'edit'];
    
    prefixes.forEach(prefix => {
        const checkbox = document.getElementById(`${prefix}_api_mode`);
        const toggle = document.getElementById(`${prefix}-api-mode-toggle`);
        const text = document.getElementById(`${prefix}-api-mode-text`);
        
        if (checkbox && toggle && text) {
            // 设置初始状态
            if (checkbox.checked) {
                toggle.classList.add('translate-x-5');
                toggle.parentElement.classList.add('bg-blue-500');
                toggle.parentElement.classList.remove('bg-slate-300', 'dark:bg-slate-600');
                text.textContent = '主动模式';
            } else {
                toggle.classList.remove('translate-x-5');
                toggle.parentElement.classList.remove('bg-blue-500');
                toggle.parentElement.classList.add('bg-slate-300', 'dark:bg-slate-600');
                text.textContent = '被动模式';
            }
        }
    });
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { toggleApiMode, initApiModeToggles };
}