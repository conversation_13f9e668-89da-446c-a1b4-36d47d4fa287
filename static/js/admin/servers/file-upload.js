/**
 * 文件上传处理模块
 * 支持私钥文件等的上传和读取
 */

function initFileUpload() {
    // 处理所有私钥文件上传
    const fileInputs = [
        'add_ssh_privateKey_file',
        'edit_ssh_privateKey_file'
    ];

    fileInputs.forEach(inputId => {
        const fileInput = document.getElementById(inputId);
        if (fileInput) {
            fileInput.addEventListener('change', handlePrivateKeyUpload);
        }
    });
}

/**
 * 处理私钥文件上传
 * @param {Event} event - 文件选择事件
 */
function handlePrivateKeyUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // 确定目标输入框ID
    const inputId = event.target.id;
    let targetId;
    
    if (inputId.includes('add')) {
        targetId = 'add_ssh_privateKey';
    } else if (inputId.includes('edit')) {
        targetId = 'edit_ssh_privateKey';
    }

    if (!targetId) {
        console.error('无法确定目标输入框');
        return;
    }

    // 显示加载状态
    startloading();

    const reader = new FileReader();
    reader.onload = function(e) {
        const content = e.target.result;
        const targetInput = document.getElementById(targetId);
        
        if (targetInput) {
            targetInput.value = content;
            endloading();
            notice('私钥文件已加载', 'success');
        } else {
            endloading();
            notice('找不到目标输入框', 'error');
        }
    };

    reader.onerror = function(e) {
        console.error('读取文件失败:', e);
        endloading();
        notice('读取文件失败', 'error');
    };

    reader.readAsText(file);
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { initFileUpload, handlePrivateKeyUpload };
}