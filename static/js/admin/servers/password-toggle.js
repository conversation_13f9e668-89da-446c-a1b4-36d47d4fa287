/**
 * 密码显示/隐藏切换功能模块
 */

function initPasswordToggle() {
    // 查找所有密码切换按钮
    document.querySelectorAll('.toggle-password').forEach(toggle => {
        toggle.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                // 使用Tabler Icons替换Material Icons
                icon.className = 'ti ti-eye-off';
            } else {
                input.type = 'password';
                // 使用Tabler Icons替换Material Icons
                icon.className = 'ti ti-eye';
            }
        });
    });
}

// 导出函数供外部使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { initPasswordToggle };
}