/**
 * 键盘快捷键功能模块
 * 提供通用的键盘快捷键支持
 */

function initKeyboardShortcuts(config = {}) {
    const {
        saveCallback,
        escCallback,
        showTip = true,
        tipKey = 'serverPageKeyboardTipShown'
    } = config;
    
    // 注册快捷键
    document.addEventListener('keydown', function(e) {
        // Ctrl+S 或 Cmd+S 保存
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            if (saveCallback && typeof saveCallback === 'function') {
                saveCallback();
            }
        }
        
        // ESC 键返回
        if (e.key === 'Escape' && escCallback && typeof escCallback === 'function') {
            e.preventDefault();
            escCallback();
        }
    });
    
    // 首次进入页面时显示提示
    if (showTip && tipKey) {
        const hasShownTip = localStorage.getItem(tipKey);
        if (!hasShownTip) {
            setTimeout(() => {
                if (typeof notice === 'function') {
                    notice('提示：按 Ctrl+S 可快速保存', 'info');
                }
                localStorage.setItem(tipKey, 'true');
            }, 1000);
        }
    }
}

// 导出函数供外部使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { initKeyboardShortcuts };
}