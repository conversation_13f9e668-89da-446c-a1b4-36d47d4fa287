/**
 * 服务器添加页面主文件
 * 整合所有功能模块
 */

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('服务器添加页面初始化');

    // 初始化密码切换功能
    if (typeof initPasswordToggle === 'function') {
        initPasswordToggle();
    }
    
    // 初始化键盘快捷键
    if (typeof initKeyboardShortcuts === 'function') {
        initKeyboardShortcuts({
            saveCallback: add,
            escCallback: () => {
                if (confirm('确定要返回服务器列表吗？未保存的更改将丢失。')) {
                    window.location.href = '/admin/servers';
                }
            },
            tipKey: 'addServerKeyboardTipShown'
        });
    }
    
    // 初始化文件上传
    if (typeof initFileUpload === 'function') {
        initFileUpload();
    }
    
    // 初始化API模式切换
    if (typeof initApiModeToggles === 'function') {
        initApiModeToggles();
    }
    
    // 初始化国家列表
    if (typeof initCountryList === 'function') {
        initCountryList();
    }
    
    // 初始化位置设置
    initLocationSettings();

    console.log('页面初始化完成');
});

// 页面加载完成后初始化悬浮操作栏
window.addEventListener('load', function() {
    if (typeof initFloatingActionBar === 'function') {
        initFloatingActionBar();
    }
});

// 初始化位置设置状态
function initLocationSettings() {
    const checkbox = document.getElementById('add_location_manual');
    const toggle = document.getElementById('location-manual-toggle');
    const container = document.getElementById('country-select-container');
    const fetchContainer = document.getElementById('fetch-location-container');

    if (!checkbox || !toggle || !container || !fetchContainer) {
        console.warn('位置设置元素未找到');
        return;
    }

    // 根据复选框状态设置UI
    if (checkbox.checked) {
        toggle.classList.add('translate-x-5', 'bg-blue-500');
        toggle.classList.remove('bg-slate-300', 'dark:bg-slate-600');
        container.classList.remove('hidden');
        fetchContainer.classList.add('hidden');
    } else {
        toggle.classList.remove('translate-x-5', 'bg-blue-500');
        toggle.classList.add('bg-slate-300', 'dark:bg-slate-600');
        container.classList.add('hidden');
        fetchContainer.classList.remove('hidden');
    }
}

// 选择国家
function selectCountry(code, name) {
    // 更新隐藏字段
    document.getElementById('add_location_country_code').value = code;
    
    // 更新显示的国家名称
    const nameElement = document.getElementById('selected-country-name');
    if (nameElement) {
        nameElement.textContent = name;
    }
    
    // 更新国旗显示
    const flagElement = document.getElementById('selected-country-flag');
    if (flagElement) {
        if (code === 'LO' || code === 'OT') {
            // 本地网络和其他网络使用图标
            flagElement.innerHTML = '<i class="ti ti-world"></i>';
        } else {
            // 其他国家使用国旗图片
            const flagUrl = code === 'UK' ? '/img/flags/GB.SVG' : `/img/flags/${code}.SVG`;
            flagElement.innerHTML = `<img src="${flagUrl}" alt="${name}" class="w-5 h-4 object-cover rounded">`;
        }
    }
    
    console.log('选择国家:', code, name);
}

// 测试SSH连接已在 ssh-test-common.js 中定义
// add页面使用 testSSHConnectionForAdd 函数

// 切换位置手动设置功能已在 location-common.js 中定义
// HTML 调用时需要传递 'add' 参数：onclick="toggleLocationManual('add')"

// 获取位置信息
async function fetchLocationInfo() {
    // add 页面暂时不支持自动获取位置
    notice('新增服务器时无法自动获取位置，请在添加后编辑服务器获取', 'info');
}

// 工具函数
function E(id) {
    return document.getElementById(id);
}

function V(id) {
    const element = document.getElementById(id);
    return element ? element.value : '';
}