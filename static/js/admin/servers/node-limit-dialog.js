/**
 * 节点限制弹窗模块
 */

// 显示节点限制弹窗
function showNodeLimitDialog(limitInfo) {
    const modal = document.getElementById('node-limit-modal');
    const content = document.getElementById('node-limit-content');
    const upgradeLink = document.getElementById('upgrade-link');
    
    if (!modal || !content || !upgradeLink) {
        console.error('节点限制弹窗元素未找到');
        notice('已达到节点数量限制，请升级您的套餐', 'error');
        return;
    }

    // 构建内容HTML
    let contentHtml = `
        <div class="space-y-4">
            <p class="text-slate-700 dark:text-slate-300">
                您当前的套餐（<span class="font-medium">${limitInfo.currentPlan || '免费版'}</span>）
                最多支持 <span class="font-bold text-orange-600 dark:text-orange-400">${limitInfo.maxNodes}</span> 个节点。
            </p>
            
            <div class="bg-slate-50 dark:bg-slate-800/50 rounded-lg p-3 mb-4">
                <div class="text-sm text-slate-500 dark:text-slate-400">当前已使用</div>
                <div class="text-2xl font-bold text-slate-800 dark:text-slate-200">${limitInfo.currentNodes} / ${limitInfo.maxNodes}</div>
                <div class="text-sm text-slate-500 dark:text-slate-400">个节点</div>
            </div>
        </div>
    `;

    // 祖父条款显示逻辑已移除 - 统一显示升级建议
    upgradeLink.href = limitInfo.upgradeUrl || '/upgrade';

    content.innerHTML = contentHtml;
    modal.classList.remove('hidden');
    modal.classList.add('flex');
}

// 隐藏节点限制弹窗
function hideNodeLimitModal() {
    const modal = document.getElementById('node-limit-modal');
    if (modal) {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
    }
}