// SharedWorker identity builder: builds unique URL and name using build version and site scope
// Dedicated module; does not modify existing worker/client implementations.

(function () {
  if (window.SharedWorkerIdentity) return;

  function getFooterSiteHref() {
    try {
      // Prefer the first link in footer (site link), ignoring external repo links
      var footer = document.querySelector('footer');
      if (!footer) return null;
      var links = footer.querySelectorAll('a[href]');
      if (!links || links.length === 0) return null;
      // Heuristic: the first link is site link in current templates
      return links[0].href || null;
    } catch (e) {
      return null;
    }
  }

  async function getVersion() {
    try {
      if (window.VERSION_INFO && window.VERSION_INFO.version) {
        return String(window.VERSION_INFO.version);
      }
    } catch (_) {}

    // Try static version.json (served at "/version.json")
    try {
      var res = await fetch('/version.json', { cache: 'no-store' });
      if (res && res.ok) {
        var data = await res.json();
        if (data && data.version) return String(data.version);
      }
    } catch (_) {}

    // Fallback to API (still a single source, not a complex retry strategy)
    try {
      var res2 = await fetch('/api/version', { cache: 'no-store' });
      if (res2 && res2.ok) {
        var d2 = await res2.json();
        if (d2 && d2.version) return String(d2.version);
      }
    } catch (_) {}

    return 'dev';
  }

  function getScope() {
    try {
      if (window.SITE_INFO && (window.SITE_INFO.url || window.SITE_INFO.name)) {
        return String(window.SITE_INFO.url || window.SITE_INFO.name);
      }
    } catch (_) {}

    var href = getFooterSiteHref();
    if (href) return href;
    try {
      return window.location.origin;
    } catch (_) {
      return 'default';
    }
  }

  async function buildWorkerIdentity() {
    var version = await getVersion();
    var scope = getScope();
    var url = '/js/stats-shared-worker-minimal.js' +
      '?v=' + encodeURIComponent(version) +
      '&scope=' + encodeURIComponent(scope);
    var name = 'dstatus-stats@' + version + ':' + scope;
    return { url: url, name: name };
  }

  window.SharedWorkerIdentity = {
    getVersion: getVersion,
    getScope: getScope,
    buildWorkerIdentity: buildWorkerIdentity
  };
})();

