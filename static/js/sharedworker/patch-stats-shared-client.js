// Patch: ensure Shared<PERSON>or<PERSON> is instantiated with unique URL and name
// This file monkey-patches StatsSharedClient._initWorker to use identity.js
// No behavior changes besides the identity of the SharedWorker instance.

(function () {
  function patchWhenReady() {
    if (typeof window === 'undefined') return;
    if (!('SharedWorker' in window)) return; // browser not supported
    if (!window.StatsSharedClient || !window.StatsSharedClient.prototype) {
      setTimeout(patchWhenReady, 50);
      return;
    }
    if (!window.SharedWorkerIdentity || typeof window.SharedWorkerIdentity.buildWorkerIdentity !== 'function') {
      setTimeout(patchWhenReady, 50);
      return;
    }

    var proto = window.StatsSharedClient.prototype;
    if (!proto._initWorker || proto._initWorker.__patchedByIdentity) return;

    var originalInit = proto._initWorker;
    proto._initWorker = function () {
      var self = this;
      try {
        window.SharedWorkerIdentity.buildWorkerIdentity().then(function (ident) {
          try {
            var worker;
            try {
              worker = new SharedWorker(ident.url, { name: ident.name });
            } catch (_) {
              // Safari compatibility (second arg string)
              worker = new SharedWorker(ident.url, ident.name);
            }
            self.worker = worker;
            self.port = worker.port;

            // Bind message listener
            self.port.addEventListener('message', function (e) { self._onWorkerMessage(e); });

            // Start port
            self.port.start();

            // Send init with page info
            self._sendToWorker('init', {
              nodeId: self.options.nodeId,
              protocol: window.location.protocol,
              host: window.location.host
            });

            // Request latest data shortly after
            setTimeout(function () { self.requestLastData(); }, 50);

            // Cleanup on unload
            window.addEventListener('beforeunload', function () {
              if (self.port) {
                self._sendToWorker('page_unload');
              }
            });

            // Periodic connection check
            self.connectionCheckTimer = setInterval(function () {
              if (self.port) {
                self._sendToWorker('check_connection');
              }
            }, 30000);

            if (self.options.debug) {
              console.log('SharedWorker初始化成功 (patched identity)');
            }
          } catch (error) {
            console.error('初始化SharedWorker失败:', error);
            self.isSupported = false;
          }
        });
      } catch (err) {
        console.error('SharedWorker identity patch error:', err);
        // Fallback to original behavior if identity building somehow fails synchronously
        try { originalInit.call(self); } catch (e) {}
      }
    };

    proto._initWorker.__patchedByIdentity = true;
  }

  // Kick off
  patchWhenReady();
})();

