/**
 * @file latency-tooltip.js
 * @description 延迟和丢包率详细信息气泡提示功能
 * @updated 2024-12-19 增强主题支持和样式优化
 */

// 避免重复声明
if (typeof window.LatencyTooltip === 'undefined') {
    window.LatencyTooltip = (() => {
        // 内部状态
        const state = {
            initialized: false,
            tooltip: null,
            tooltipCache: new Map(), // 缓存已处理的详情数据
            cacheExpiry: 5 * 60 * 1000, // 缓存过期时间：5分钟
            lastCacheCleanup: Date.now()
        };

        /**
         * 获取当前主题状态
         */
        function getCurrentTheme() {
            return document.documentElement.classList.contains('dark');
        }

        /**
         * 获取主题适配的样式类
         */
        function getThemeClasses(isDark) {
            return isDark
                ? 'bg-gray-900/95 text-white border border-gray-600/50 shadow-xl backdrop-blur-sm'
                : 'bg-white/95 text-gray-900 border border-gray-200/60 shadow-lg backdrop-blur-sm';
        }

        /**
         * 更新气泡提示主题样式
         */
        function updateTooltipTheme() {
            if (!state.tooltip) return;
            
            const isDark = getCurrentTheme();
            const baseClasses = 'fixed z-50 text-xs rounded-lg py-2.5 px-3.5 pointer-events-none opacity-0 transition-all duration-200 max-w-xs';
            const themeClasses = getThemeClasses(isDark);
            
            state.tooltip.className = `${baseClasses} ${themeClasses}`;
        }

        /**
         * 初始化气泡提示
         */
        function init() {
            if (state.initialized) return;

            // 创建气泡提示元素
            state.tooltip = document.createElement('div');
            state.tooltip.id = 'latency-tooltip';
            
            // 应用初始主题样式
            updateTooltipTheme();
            
            document.body.appendChild(state.tooltip);
            
            // 添加事件监听
            document.addEventListener('mouseover', handleMouseOver);
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseout', handleMouseOut);
            
            // 监听主题变化
            setupThemeListener();
            
            state.initialized = true;
            console.log('延迟气泡提示功能已初始化 - 支持主题切换');
        }

        /**
         * 设置主题变化监听器
         */
        function setupThemeListener() {
            // 监听主题变化事件
            document.addEventListener('theme:changed', (e) => {
                updateTooltipTheme();
                // 清除缓存，因为主题变化会影响样式
                state.tooltipCache.clear();
            });

            // 备用方案：监听HTML类变化
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.attributeName === 'class' && mutation.target === document.documentElement) {
                        updateTooltipTheme();
                    }
                });
            });

            observer.observe(document.documentElement, { 
                attributes: true, 
                attributeFilter: ['class'] 
            });
        }

        /**
         * 处理鼠标悬停事件
         */
        function handleMouseOver(e) {
            const target = e.target.closest('[data-latency-details]');
            if (!target) return;
            
            try {
                // 获取详情数据
                const detailsStr = target.dataset.latencyDetails;
                if (!detailsStr) return;
                
                // 检查缓存
                if (state.tooltipCache.has(detailsStr)) {
                    const cached = state.tooltipCache.get(detailsStr);
                    if (Date.now() - cached.timestamp < state.cacheExpiry) {
                        state.tooltip.innerHTML = cached.content;
                        state.tooltip.style.opacity = '1';
                        updatePosition(e);
                        return;
                    }
                }
                
                // 解析详情数据
                const details = JSON.parse(detailsStr);
                if (!details || details.length === 0) return;
                
                // 构建气泡内容
                const isDark = getCurrentTheme();
                const borderClass = isDark ? 'border-gray-600' : 'border-gray-200';
                const avgSectionBorderClass = isDark ? 'border-gray-500' : 'border-gray-300';
                const avgTextClass = isDark ? 'text-amber-400' : 'text-amber-600';
                
                let content = '<div class="font-semibold mb-2 text-sm">监控目标详情</div>';
                details.forEach((detail, index) => {
                    content += `
                        <div class="${index > 0 ? `mt-2 pt-2 border-t ${borderClass}` : ''}">
                            <div class="font-medium mb-1 text-sm">${detail.name}</div>
                            <div class="flex justify-between items-center text-xs space-x-3">
                                <span class="flex items-center">
                                    <span class="inline-block w-2 h-2 bg-blue-500 rounded-full mr-1.5"></span>
                                    延迟: <span class="font-semibold ml-1">${detail.latency}ms</span>
                                </span>
                                <span class="flex items-center">
                                    <span class="inline-block w-2 h-2 bg-red-500 rounded-full mr-1.5"></span>
                                    丢包: <span class="font-semibold ml-1">${detail.packet_loss}%</span>
                                </span>
                            </div>
                        </div>
                    `;
                });
                
                // 添加平均值
                if (details.length > 1) {
                    const avgLatency = Math.round(details.reduce((sum, d) => sum + d.latency, 0) / details.length);
                    const avgLoss = Math.round(details.reduce((sum, d) => sum + d.packet_loss, 0) / details.length * 10) / 10;
                    content += `
                        <div class="mt-3 pt-2 border-t ${avgSectionBorderClass}">
                            <div class="font-semibold ${avgTextClass} text-sm flex items-center">
                                <span class="inline-block w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                                平均值: ${avgLatency}ms, ${avgLoss}%
                            </div>
                        </div>
                    `;
                }
                
                // 更新气泡内容
                state.tooltip.innerHTML = content;
                state.tooltip.style.opacity = '1';
                
                // 更新位置
                updatePosition(e);
                
                // 缓存处理结果
                state.tooltipCache.set(detailsStr, {
                    content,
                    timestamp: Date.now()
                });
                
                // 定期清理缓存
                cleanupCache();
                
            } catch (error) {
                console.error('显示延迟气泡提示失败:', error);
            }
        }

        /**
         * 处理鼠标移动事件
         */
        function handleMouseMove(e) {
            if (state.tooltip.style.opacity === '1') {
                updatePosition(e);
            }
        }

        /**
         * 处理鼠标移出事件
         */
        function handleMouseOut(e) {
            if (!e.target.closest('[data-latency-details]')) {
                state.tooltip.style.opacity = '0';
            }
        }

        /**
         * 更新气泡位置
         */
        function updatePosition(e) {
            const margin = 10;
            const rect = state.tooltip.getBoundingClientRect();
            
            let left = e.clientX + margin;
            let top = e.clientY + margin;
            
            // 防止气泡超出屏幕右边界
            if (left + rect.width > window.innerWidth) {
                left = e.clientX - rect.width - margin;
            }
            
            // 防止气泡超出屏幕下边界
            if (top + rect.height > window.innerHeight) {
                top = e.clientY - rect.height - margin;
            }
            
            state.tooltip.style.left = left + 'px';
            state.tooltip.style.top = top + 'px';
        }

        /**
         * 清理过期缓存
         */
        function cleanupCache() {
            const now = Date.now();
            
            // 每分钟最多清理一次
            if (now - state.lastCacheCleanup < 60000) return;
            
            state.lastCacheCleanup = now;
            
            // 清理过期缓存
            for (const [key, value] of state.tooltipCache.entries()) {
                if (now - value.timestamp > state.cacheExpiry) {
                    state.tooltipCache.delete(key);
                }
            }
        }

        // 返回公共API
        return {
            init,
            // 允许外部调用清理缓存
            cleanupCache
        };
    })();
}

// 在页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化，确保其他DOM元素已加载
    setTimeout(() => {
        if (window.LatencyTooltip) {
            window.LatencyTooltip.init();
        }
    }, 1000);
});
