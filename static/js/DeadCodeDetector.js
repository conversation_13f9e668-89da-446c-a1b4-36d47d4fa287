/**
 * DeadCodeDetector.js
 * 废弃代码检测器
 * 用于分析和识别项目中未使用的函数和变量
 */

window.DeadCodeDetector = (function() {
    'use strict';

    // 配置
    const CONFIG = {
        // 要分析的文件模式
        filePatterns: [
            '/js/stats.js',
            '/js/stats-simplified.js',
            '/js/*.js'
        ],
        
        // 忽略的函数名模式
        ignorePatterns: [
            /^on[A-Z]/, // 事件处理函数 (onClick, onLoad等)
            /^_/, // 私有函数
            /^handle/, // 处理函数
            /^init/, // 初始化函数
            /^debug/, // 调试函数
            /^log/ // 日志函数
        ],
        
        // 已知的全局函数（框架提供的等）
        knownGlobals: [
            'document', 'window', 'console', 'localStorage', 'sessionStorage',
            'fetch', 'XMLHttpRequest', 'WebSocket', 'setTimeout', 'setInterval',
            'clearTimeout', 'clearInterval', 'addEventListener', 'removeEventListener'
        ]
    };

    // 分析结果存储
    const analysisResult = {
        functions: new Map(), // 函数定义
        variables: new Map(), // 变量定义
        references: new Map(), // 引用关系
        deadCode: [], // 死代码列表
        warnings: [] // 警告信息
    };

    /**
     * 调试日志函数
     */
    function log(...args) {
        if (window.DEBUG_MODE) {
            console.log('[DeadCodeDetector]', ...args);
        }
    }

    /**
     * 从代码中提取函数定义
     * @param {string} code - 源代码
     * @param {string} filename - 文件名
     * @returns {Array} 函数定义列表
     */
    function extractFunctions(code, filename) {
        const functions = [];
        
        // 匹配函数声明
        const functionDeclarations = code.match(/function\s+(\w+)\s*\(/g);
        if (functionDeclarations) {
            functionDeclarations.forEach(match => {
                const name = match.match(/function\s+(\w+)/)[1];
                functions.push({
                    name,
                    type: 'declaration',
                    filename,
                    line: getLineNumber(code, match)
                });
            });
        }

        // 匹配函数表达式
        const functionExpressions = code.match(/(?:const|let|var)\s+(\w+)\s*=\s*function/g);
        if (functionExpressions) {
            functionExpressions.forEach(match => {
                const name = match.match(/(?:const|let|var)\s+(\w+)/)[1];
                functions.push({
                    name,
                    type: 'expression',
                    filename,
                    line: getLineNumber(code, match)
                });
            });
        }

        // 匹配箭头函数
        const arrowFunctions = code.match(/(?:const|let|var)\s+(\w+)\s*=\s*(?:\([^)]*\)|[^=>\s]+)\s*=>/g);
        if (arrowFunctions) {
            arrowFunctions.forEach(match => {
                const name = match.match(/(?:const|let|var)\s+(\w+)/)[1];
                functions.push({
                    name,
                    type: 'arrow',
                    filename,
                    line: getLineNumber(code, match)
                });
            });
        }

        // 匹配对象方法
        const objectMethods = code.match(/(\w+)\s*:\s*function/g);
        if (objectMethods) {
            objectMethods.forEach(match => {
                const name = match.match(/(\w+)\s*:/)[1];
                functions.push({
                    name,
                    type: 'method',
                    filename,
                    line: getLineNumber(code, match)
                });
            });
        }

        return functions;
    }

    /**
     * 从代码中提取变量定义
     * @param {string} code - 源代码
     * @param {string} filename - 文件名
     * @returns {Array} 变量定义列表
     */
    function extractVariables(code, filename) {
        const variables = [];
        
        // 匹配变量声明
        const variableDeclarations = code.match(/(?:const|let|var)\s+(\w+)/g);
        if (variableDeclarations) {
            variableDeclarations.forEach(match => {
                const name = match.match(/(?:const|let|var)\s+(\w+)/)[1];
                
                // 排除函数表达式和箭头函数
                const context = getContext(code, match, 50);
                if (!context.includes('function') && !context.includes('=>')) {
                    variables.push({
                        name,
                        type: 'variable',
                        filename,
                        line: getLineNumber(code, match)
                    });
                }
            });
        }

        return variables;
    }

    /**
     * 提取代码中的引用
     * @param {string} code - 源代码
     * @param {string} filename - 文件名
     * @returns {Array} 引用列表
     */
    function extractReferences(code, filename) {
        const references = [];
        const allDefinitions = [
            ...analysisResult.functions.values(),
            ...analysisResult.variables.values()
        ].flat();

        allDefinitions.forEach(def => {
            // 创建正则表达式匹配函数调用和变量引用
            const regex = new RegExp(`\\b${def.name}\\s*\\(|\\b${def.name}\\b(?!\\s*[:=])`, 'g');
            let match;
            
            while ((match = regex.exec(code)) !== null) {
                // 排除定义本身
                const context = getContext(code, match[0], 20);
                if (!context.includes('function ' + def.name) && 
                    !context.includes('const ' + def.name) &&
                    !context.includes('let ' + def.name) &&
                    !context.includes('var ' + def.name)) {
                    references.push({
                        name: def.name,
                        filename,
                        line: getLineNumber(code, match[0]),
                        context: context.trim()
                    });
                }
            }
        });

        return references;
    }

    /**
     * 获取匹配文本在代码中的行号
     * @param {string} code - 源代码
     * @param {string} match - 匹配的文本
     * @returns {number} 行号
     */
    function getLineNumber(code, match) {
        const index = code.indexOf(match);
        if (index === -1) return 0;
        return code.substring(0, index).split('\n').length;
    }

    /**
     * 获取匹配文本的上下文
     * @param {string} code - 源代码
     * @param {string} match - 匹配的文本
     * @param {number} contextLength - 上下文长度
     * @returns {string} 上下文文本
     */
    function getContext(code, match, contextLength = 30) {
        const index = code.indexOf(match);
        if (index === -1) return '';
        
        const start = Math.max(0, index - contextLength);
        const end = Math.min(code.length, index + match.length + contextLength);
        return code.substring(start, end);
    }

    /**
     * 分析单个文件
     * @param {string} filename - 文件名
     * @param {string} code - 文件内容
     */
    function analyzeFile(filename, code) {
        log(`分析文件: ${filename}`);

        // 提取函数定义
        const functions = extractFunctions(code, filename);
        functions.forEach(func => {
            if (!analysisResult.functions.has(func.name)) {
                analysisResult.functions.set(func.name, []);
            }
            analysisResult.functions.get(func.name).push(func);
        });

        // 提取变量定义
        const variables = extractVariables(code, filename);
        variables.forEach(variable => {
            if (!analysisResult.variables.has(variable.name)) {
                analysisResult.variables.set(variable.name, []);
            }
            analysisResult.variables.get(variable.name).push(variable);
        });

        // 提取引用
        const references = extractReferences(code, filename);
        references.forEach(ref => {
            if (!analysisResult.references.has(ref.name)) {
                analysisResult.references.set(ref.name, []);
            }
            analysisResult.references.get(ref.name).push(ref);
        });
    }

    /**
     * 检测死代码
     */
    function detectDeadCode() {
        log('开始检测死代码...');

        // 检查未使用的函数
        analysisResult.functions.forEach((definitions, name) => {
            // 跳过忽略模式匹配的函数
            if (CONFIG.ignorePatterns.some(pattern => pattern.test(name))) {
                return;
            }

            // 跳过全局函数
            if (CONFIG.knownGlobals.includes(name)) {
                return;
            }

            // 检查是否有引用
            const references = analysisResult.references.get(name) || [];
            
            if (references.length === 0) {
                definitions.forEach(def => {
                    analysisResult.deadCode.push({
                        type: 'function',
                        name: def.name,
                        filename: def.filename,
                        line: def.line,
                        reason: '函数未被调用'
                    });
                });
            }
        });

        // 检查未使用的变量
        analysisResult.variables.forEach((definitions, name) => {
            // 跳过忽略模式匹配的变量
            if (CONFIG.ignorePatterns.some(pattern => pattern.test(name))) {
                return;
            }

            // 跳过全局变量
            if (CONFIG.knownGlobals.includes(name)) {
                return;
            }

            // 检查是否有引用
            const references = analysisResult.references.get(name) || [];
            
            if (references.length === 0) {
                definitions.forEach(def => {
                    analysisResult.deadCode.push({
                        type: 'variable',
                        name: def.name,
                        filename: def.filename,
                        line: def.line,
                        reason: '变量未被使用'
                    });
                });
            }
        });

        log(`检测完成，发现 ${analysisResult.deadCode.length} 处死代码`);
    }

    /**
     * 生成分析报告
     * @returns {Object} 分析报告
     */
    function generateReport() {
        const report = {
            summary: {
                totalFunctions: Array.from(analysisResult.functions.values()).flat().length,
                totalVariables: Array.from(analysisResult.variables.values()).flat().length,
                totalReferences: Array.from(analysisResult.references.values()).flat().length,
                deadCodeCount: analysisResult.deadCode.length,
                analyzeTime: new Date().toISOString()
            },
            deadCode: analysisResult.deadCode.sort((a, b) => {
                if (a.filename !== b.filename) {
                    return a.filename.localeCompare(b.filename);
                }
                return a.line - b.line;
            }),
            warnings: analysisResult.warnings,
            details: {
                functions: Object.fromEntries(analysisResult.functions),
                variables: Object.fromEntries(analysisResult.variables),
                references: Object.fromEntries(analysisResult.references)
            }
        };

        return report;
    }

    /**
     * 打印分析报告
     * @param {Object} report - 分析报告
     */
    function printReport(report) {
        console.group('🔍 死代码检测报告');
        
        console.log('📊 总览:');
        console.log(`  - 总函数数: ${report.summary.totalFunctions}`);
        console.log(`  - 总变量数: ${report.summary.totalVariables}`);
        console.log(`  - 总引用数: ${report.summary.totalReferences}`);
        console.log(`  - 死代码数: ${report.summary.deadCodeCount}`);
        
        if (report.deadCode.length > 0) {
            console.log('\n💀 发现的死代码:');
            report.deadCode.forEach((item, index) => {
                console.log(`  ${index + 1}. ${item.type}: ${item.name}`);
                console.log(`     位置: ${item.filename}:${item.line}`);
                console.log(`     原因: ${item.reason}`);
            });
        } else {
            console.log('\n✅ 未发现死代码');
        }
        
        if (report.warnings.length > 0) {
            console.log('\n⚠️ 警告:');
            report.warnings.forEach((warning, index) => {
                console.log(`  ${index + 1}. ${warning}`);
            });
        }
        
        console.groupEnd();
    }

    /**
     * 生成清理建议
     * @param {Object} report - 分析报告
     * @returns {Array} 清理建议列表
     */
    function generateCleanupSuggestions(report) {
        const suggestions = [];

        if (report.deadCode.length > 0) {
            // 按文件分组
            const fileGroups = {};
            report.deadCode.forEach(item => {
                if (!fileGroups[item.filename]) {
                    fileGroups[item.filename] = [];
                }
                fileGroups[item.filename].push(item);
            });

            Object.entries(fileGroups).forEach(([filename, items]) => {
                suggestions.push({
                    type: 'file_cleanup',
                    filename,
                    items,
                    description: `在 ${filename} 中发现 ${items.length} 处死代码`,
                    action: `删除未使用的${items.map(i => i.type).join('、')}`
                });
            });
        }

        // 检查重复函数
        analysisResult.functions.forEach((definitions, name) => {
            if (definitions.length > 1) {
                suggestions.push({
                    type: 'duplicate_function',
                    name,
                    definitions,
                    description: `函数 ${name} 存在 ${definitions.length} 个定义`,
                    action: '合并或删除重复的函数定义'
                });
            }
        });

        return suggestions;
    }

    /**
     * 模拟文件内容获取（在实际环境中需要根据情况实现）
     * @param {string} filename - 文件名
     * @returns {Promise<string>} 文件内容
     */
    async function fetchFileContent(filename) {
        try {
            // 尝试从当前页面脚本中获取
            const scripts = document.querySelectorAll('script[src]');
            for (const script of scripts) {
                if (script.src.includes(filename.replace('/js/', ''))) {
                    const response = await fetch(script.src);
                    if (response.ok) {
                        return await response.text();
                    }
                }
            }
            
            // 直接尝试获取
            const response = await fetch(filename);
            if (response.ok) {
                return await response.text();
            }
            
            throw new Error(`无法获取文件: ${filename}`);
        } catch (error) {
            analysisResult.warnings.push(`无法读取文件 ${filename}: ${error.message}`);
            return '';
        }
    }

    /**
     * 分析当前页面的脚本
     */
    function analyzeCurrentPage() {
        // 分析内联脚本
        const inlineScripts = document.querySelectorAll('script:not([src])');
        inlineScripts.forEach((script, index) => {
            if (script.textContent.trim()) {
                analyzeFile(`inline-script-${index}`, script.textContent);
            }
        });

        // 分析全局对象中的函数
        if (window) {
            const globalFunctions = [];
            for (const key in window) {
                if (typeof window[key] === 'function' && 
                    !CONFIG.knownGlobals.includes(key) &&
                    !key.startsWith('_')) {
                    globalFunctions.push({
                        name: key,
                        type: 'global',
                        filename: 'window',
                        line: 0
                    });
                }
            }
            
            globalFunctions.forEach(func => {
                if (!analysisResult.functions.has(func.name)) {
                    analysisResult.functions.set(func.name, []);
                }
                analysisResult.functions.get(func.name).push(func);
            });
        }
    }

    /**
     * 执行完整分析
     * @param {Array} filenames - 要分析的文件列表（可选）
     * @returns {Promise<Object>} 分析报告
     */
    async function analyze(filenames = null) {
        log('开始死代码分析...');

        // 重置分析结果
        analysisResult.functions.clear();
        analysisResult.variables.clear();
        analysisResult.references.clear();
        analysisResult.deadCode = [];
        analysisResult.warnings = [];

        // 分析指定文件或使用默认配置
        const filesToAnalyze = filenames || CONFIG.filePatterns;
        
        for (const filename of filesToAnalyze) {
            const content = await fetchFileContent(filename);
            if (content) {
                analyzeFile(filename, content);
            }
        }

        // 分析当前页面
        analyzeCurrentPage();

        // 检测死代码
        detectDeadCode();

        // 生成报告
        const report = generateReport();
        
        // 生成清理建议
        report.cleanupSuggestions = generateCleanupSuggestions(report);

        log('分析完成');
        return report;
    }

    /**
     * 快速分析（仅分析当前页面）
     * @returns {Object} 分析报告
     */
    function quickAnalyze() {
        log('开始快速分析...');

        // 重置分析结果
        analysisResult.functions.clear();
        analysisResult.variables.clear();
        analysisResult.references.clear();
        analysisResult.deadCode = [];
        analysisResult.warnings = [];

        // 仅分析当前页面
        analyzeCurrentPage();

        // 检测死代码
        detectDeadCode();

        // 生成报告
        const report = generateReport();
        report.cleanupSuggestions = generateCleanupSuggestions(report);

        log('快速分析完成');
        return report;
    }

    /**
     * 清理建议执行器（仅提供建议，不实际修改文件）
     * @param {Array} suggestions - 清理建议列表
     * @returns {Array} 清理脚本列表
     */
    function generateCleanupScripts(suggestions) {
        const scripts = [];

        suggestions.forEach(suggestion => {
            switch (suggestion.type) {
                case 'file_cleanup':
                    const commands = suggestion.items.map(item => 
                        `// 删除未使用的${item.type}: ${item.name} (第${item.line}行)`
                    );
                    scripts.push({
                        filename: suggestion.filename,
                        description: suggestion.description,
                        commands
                    });
                    break;

                case 'duplicate_function':
                    scripts.push({
                        description: `处理重复函数: ${suggestion.name}`,
                        commands: [
                            `// 检查以下位置的重复定义:`,
                            ...suggestion.definitions.map(def =>
                                `// - ${def.filename}:${def.line} (${def.type})`
                            ),
                            `// 保留一个定义，删除其他重复项`
                        ]
                    });
                    break;
            }
        });

        return scripts;
    }

    // 导出公共API
    return {
        // 核心分析功能
        analyze,
        quickAnalyze,
        
        // 报告功能
        generateReport,
        printReport,
        
        // 清理功能
        generateCleanupSuggestions,
        generateCleanupScripts,
        
        // 工具功能
        analyzeFile,
        fetchFileContent,
        analyzeCurrentPage,
        
        // 配置访问
        getConfig: () => ({ ...CONFIG }),
        setConfig: (newConfig) => Object.assign(CONFIG, newConfig),
        
        // 结果访问
        getAnalysisResult: () => ({ ...analysisResult })
    };
})();

// 使用示例代码（可以放在开发者控制台中运行）
/*
// 快速分析当前页面
const report = DeadCodeDetector.quickAnalyze();
DeadCodeDetector.printReport(report);

// 完整分析（需要指定文件）
DeadCodeDetector.analyze(['/js/stats.js', '/js/stats-simplified.js']).then(report => {
    DeadCodeDetector.printReport(report);
    const suggestions = report.cleanupSuggestions;
    const scripts = DeadCodeDetector.generateCleanupScripts(suggestions);
    console.log('清理脚本:', scripts);
});
*/