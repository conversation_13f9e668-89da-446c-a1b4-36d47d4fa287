/**
 * 简化版本显示模块
 * 负责从 API 获取版本信息并动态更新页面显示
 */

class VersionDisplay {
    constructor() {
        this.versionElement = null;
        this.loadingText = '加载中...';
        this.errorText = 'v?.?.?';
        this.retryCount = 0;
        this.maxRetries = 3;
        
        this.init();
    }
    
    init() {
        // 等待 DOM 加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    setup() {
        // 查找版本显示元素
        this.versionElement = document.querySelector('#app-version, .version-display');
        
        if (!this.versionElement) {
            // 如果没有找到专门的版本元素，查找页脚中的版本显示
            const footerElements = document.querySelectorAll('footer span');
            for (const element of footerElements) {
                if (element.textContent.match(/v\d+\.\d+\.\d+/)) {
                    this.versionElement = element;
                    break;
                }
            }
        }
        
        if (this.versionElement) {
            this.loadVersion();
        } else {
            console.warn('未找到版本显示元素');
        }
    }
    
    async loadVersion() {
        try {
            // 显示加载状态
            this.updateDisplay(this.loadingText);
            
            // 从 API 获取版本信息
            const response = await fetch('/api/version');
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.success && data.version) {
                this.updateDisplay(`v${data.version}`);
                this.retryCount = 0; // 重置重试计数
            } else {
                throw new Error(data.error || '版本信息格式错误');
            }
            
        } catch (error) {
            console.error('获取版本信息失败:', error);
            this.handleError(error);
        }
    }
    
    updateDisplay(text) {
        if (this.versionElement) {
            this.versionElement.textContent = text;
        }
    }
    
    handleError(error) {
        this.updateDisplay(this.errorText);
        
        // 实现重试机制
        if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            console.log(`版本加载失败，${2000 * this.retryCount}ms 后重试 (${this.retryCount}/${this.maxRetries})`);
            
            setTimeout(() => {
                this.loadVersion();
            }, 2000 * this.retryCount);
        } else {
            console.error('版本加载失败，已达到最大重试次数');
        }
    }
    
    // 手动刷新版本信息
    refresh() {
        this.retryCount = 0;
        this.loadVersion();
    }
}

// 自动初始化版本显示
const versionDisplay = new VersionDisplay();

// 全局暴露接口
window.VersionDisplay = versionDisplay;