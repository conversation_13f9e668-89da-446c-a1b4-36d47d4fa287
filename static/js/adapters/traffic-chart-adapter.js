/**
 * TrafficChartManager适配器
 * 将TrafficChartManager适配到TabLifecycleHandler组件注册系统
 */

// 确保依赖项已加载
if (typeof window.TrafficChartManager === 'undefined') {
    console.error('TrafficChartManager未加载，适配器无法初始化');
}

if (typeof window.TabLifecycleHandler === 'undefined') {
    console.error('TabLifecycleHandler未加载，适配器无法初始化');
}

// 创建适配器
const TrafficChartAdapter = {
    // 组件ID
    id: 'traffic-chart-adapter',
    
    // 原始组件引用
    originalComponent: window.TrafficChartManager,
    
    // 缓存数据
    cachedData: {
        hs: null,
        ds: null,
        ms: null
    },
    
    // 初始化适配器
    init() {
        console.log('初始化TrafficChartAdapter...');
        
        // 注册到TabLifecycleHandler
        if (window.TabLifecycleHandler && typeof window.TabLifecycleHandler.registerComponent === 'function') {
            window.TabLifecycleHandler.registerComponent(this);
            console.log('TrafficChartAdapter已注册到TabLifecycleHandler');
        } else {
            console.warn('TabLifecycleHandler不可用，无法注册TrafficChartAdapter');
        }
        
        // 修改TrafficChartManager的updateAllCharts方法，添加对shouldBlockUpdate的检查
        this._patchUpdateAllCharts();
        
        // 修改TrafficChartManager的updateChart方法，添加对shouldBlockUpdate的检查
        this._patchUpdateChart();
        
        // 修改trafficDataUpdated事件监听器，添加对shouldBlockUpdate的检查
        this._patchEventListener();
        
        console.log('TrafficChartAdapter初始化完成');
    },
    
    // 修改TrafficChartManager的updateAllCharts方法
    _patchUpdateAllCharts() {
        if (!this.originalComponent || typeof this.originalComponent.updateAllCharts !== 'function') {
            console.warn('无法修改TrafficChartManager.updateAllCharts，方法不存在');
            return;
        }
        
        // 保存原始方法
        const originalUpdateAllCharts = this.originalComponent.updateAllCharts;
        
        // 替换为新方法
        this.originalComponent.updateAllCharts = async function(data) {
            // 使用全局拦截函数检查是否应该跳过更新
            if (window.TabLifecycleHandler && window.TabLifecycleHandler.shouldBlockUpdate && window.TabLifecycleHandler.shouldBlockUpdate()) {
                console.log('流量图表更新被阻止，标签页正在从休眠中恢复');
                
                // 缓存数据
                if (window.TrafficChartAdapter) {
                    window.TrafficChartAdapter.cacheData(data);
                }
                
                return; // 跳过更新
            }
            
            // 调用原始方法
            return originalUpdateAllCharts.call(this, data);
        };
        
        console.log('已修改TrafficChartManager.updateAllCharts方法');
    },
    
    // 修改TrafficChartManager的updateChart方法
    _patchUpdateChart() {
        if (!this.originalComponent || typeof this.originalComponent.updateChart !== 'function') {
            console.warn('无法修改TrafficChartManager.updateChart，方法不存在');
            return;
        }
        
        // 保存原始方法
        const originalUpdateChart = this.originalComponent.updateChart;
        
        // 替换为新方法
        this.originalComponent.updateChart = function(type, data) {
            // 使用全局拦截函数检查是否应该跳过更新
            if (window.TabLifecycleHandler && window.TabLifecycleHandler.shouldBlockUpdate && window.TabLifecycleHandler.shouldBlockUpdate()) {
                console.log(`流量图表 ${type} 更新被阻止，标签页正在从休眠中恢复`);
                
                // 缓存数据
                if (window.TrafficChartAdapter) {
                    window.TrafficChartAdapter.cacheChartData(type, data);
                }
                
                return; // 跳过更新
            }
            
            // 调用原始方法
            return originalUpdateChart.call(this, type, data);
        };
        
        console.log('已修改TrafficChartManager.updateChart方法');
    },
    
    // 修改trafficDataUpdated事件监听器
    _patchEventListener() {
        // 移除现有的事件监听器
        const existingListeners = getEventListeners(document, 'trafficDataUpdated');
        if (existingListeners && existingListeners.length > 0) {
            existingListeners.forEach(listener => {
                document.removeEventListener('trafficDataUpdated', listener.listener);
            });
            
            console.log(`已移除 ${existingListeners.length} 个trafficDataUpdated事件监听器`);
        }
        
        // 添加新的事件监听器
        document.addEventListener('trafficDataUpdated', function(event) {
            console.log('收到流量数据更新事件');
            
            // 使用全局拦截函数检查是否应该跳过更新
            if (window.TabLifecycleHandler && window.TabLifecycleHandler.shouldBlockUpdate && window.TabLifecycleHandler.shouldBlockUpdate()) {
                console.log('流量图表数据更新被阻止，标签页正在从休眠中恢复');
                
                // 缓存数据
                if (window.TrafficChartAdapter && event.detail && event.detail.data) {
                    window.TrafficChartAdapter.cacheData(event.detail.data);
                }
                
                return; // 跳过更新
            }
            
            // 检查事件数据是否有效
            if (event.detail && event.detail.data) {
                // 更新所有图表
                window.TrafficChartManager.updateAllCharts(event.detail.data);
            }
        });
        
        console.log('已添加新的trafficDataUpdated事件监听器');
    },
    
    // 缓存数据
    cacheData(data) {
        if (!data) return;
        
        console.log('缓存流量数据');
        
        if (Array.isArray(data.hs)) {
            this.cachedData.hs = data.hs;
        }
        
        if (Array.isArray(data.ds)) {
            this.cachedData.ds = data.ds;
        }
        
        if (Array.isArray(data.ms)) {
            this.cachedData.ms = data.ms;
        }
    },
    
    // 缓存单个图表数据
    cacheChartData(type, data) {
        if (!data || !Array.isArray(data)) return;
        
        console.log(`缓存流量图表数据: ${type}`);
        this.cachedData[type] = data;
    },
    
    // 应用缓存数据
    applyCache() {
        console.log('应用缓存的流量数据');
        
        if (this.cachedData.hs) {
            this.originalComponent.updateChart('hs', this.cachedData.hs);
            this.cachedData.hs = null;
        }
        
        if (this.cachedData.ds) {
            this.originalComponent.updateChart('ds', this.cachedData.ds);
            this.cachedData.ds = null;
        }
        
        if (this.cachedData.ms) {
            this.originalComponent.updateChart('ms', this.cachedData.ms);
            this.cachedData.ms = null;
        }
    },
    
    // 组件接口 - 休眠处理
    onSleep() {
        console.log('TrafficChartAdapter进入休眠状态');
        // 清空缓存数据
        this.cachedData = {
            hs: null,
            ds: null,
            ms: null
        };
    },
    
    // 组件接口 - 唤醒处理
    onWake() {
        console.log('TrafficChartAdapter唤醒');
    },
    
    // 组件接口 - 应用过渡效果
    applyTransition(force = false) {
        console.log(`TrafficChartAdapter应用过渡效果 (force=${force})`);
    },
    
    // 组件接口 - 检查是否应该更新
    shouldUpdate() {
        // 默认返回true，允许更新
        return true;
    }
};

// 导出适配器
window.TrafficChartAdapter = TrafficChartAdapter;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 检查是否使用ECharts版本
    const useECharts = localStorage.getItem('use_echarts_traffic') !== 'false';
    
    if (useECharts) {
        console.log('使用ECharts版本的流量图表，跳过ApexCharts适配器初始化');
        return;
    }
    
    // 延迟初始化，确保依赖项已加载
    setTimeout(() => {
        if (window.TrafficChartManager && window.TabLifecycleHandler) {
            TrafficChartAdapter.init();
        } else {
            console.warn('依赖项未加载，无法初始化TrafficChartAdapter');
        }
    }, 500);
});

// 辅助函数 - 获取事件监听器
function getEventListeners(element, eventName) {
    // 这个函数在生产环境中不可用，只是一个占位符
    // 在实际实现中，我们无法直接获取事件监听器
    console.warn('getEventListeners函数在生产环境中不可用');
    return [];
}
