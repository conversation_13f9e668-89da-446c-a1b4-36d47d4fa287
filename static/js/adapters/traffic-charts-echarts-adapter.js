/**
 * TrafficEChartsManager适配器
 * 将TrafficEChartsManager适配到TabLifecycleHandler组件注册系统
 */

// 使用条件检查避免重复声明
if (typeof window.TrafficEChartsAdapter === 'undefined') {
    // 创建适配器
    window.TrafficEChartsAdapter = {
    // 组件ID
    id: 'traffic-echarts-adapter',

    // 原始组件引用
    originalComponent: window.TrafficEChartsManager,

    // 缓存数据
    cachedData: {
        hs: null,
        ds: null,
        ms: null
    },

    // 初始化适配器
    init() {
        console.log('初始化TrafficEChartsAdapter...');

        // 注册到TabLifecycleHandler
        if (window.TabLifecycleHandler && typeof window.TabLifecycleHandler.registerComponent === 'function') {
            window.TabLifecycleHandler.registerComponent(this);
            console.log('TrafficEChartsAdapter已注册到TabLifecycleHandler');
        } else {
            console.warn('TabLifecycleHandler不可用，无法注册TrafficEChartsAdapter');
        }

        console.log('TrafficEChartsAdapter初始化完成');
    },

    // TabLifecycleHandler组件接口 - 激活组件
    activate() {
        console.log('TrafficEChartsAdapter: 激活');

        // 恢复所有图表
        this._restoreAllCharts();

        return true;
    },

    // TabLifecycleHandler组件接口 - 挂起组件
    suspend() {
        console.log('TrafficEChartsAdapter: 挂起');

        // 缓存当前数据
        this._cacheAllData();

        return true;
    },

    // TabLifecycleHandler组件接口 - 恢复组件
    resume() {
        console.log('TrafficEChartsAdapter: 恢复');

        // 恢复所有图表
        this._restoreAllCharts();

        return true;
    },

    // TabLifecycleHandler组件接口 - 组件ID
    getId() {
        return this.id;
    },

    // 缓存所有图表数据
    _cacheAllData() {
        if (!window.TrafficEChartsManager) return;

        // 缓存各图表的数据
        Object.keys(window.TrafficEChartsManager.dataCache).forEach(key => {
            this.cachedData[key] = window.TrafficEChartsManager.dataCache[key];
        });

        console.log('已缓存流量图表数据');
    },

    // 恢复所有图表
    _restoreAllCharts() {
        if (!window.TrafficEChartsManager) return;

        // 首先确保图表已创建
        window.TrafficEChartsManager._createAllCharts().then(() => {
            // 恢复缓存的数据
            Object.keys(this.cachedData).forEach(key => {
                if (this.cachedData[key] && window.TrafficEChartsManager.charts[key]) {
                    const series = this.cachedData[key];

                    window.TrafficEChartsManager.charts[key].setOption({
                        series: [
                            { name: '下行', data: series.in },
                            { name: '上行', data: series.out }
                        ]
                    });

                    console.log(`已恢复 ${key} 流量图表数据`);
                }
            });

            // 调整图表大小以确保正确显示
            setTimeout(() => {
                window.TrafficEChartsManager._resizeAllCharts();
            }, 100);
        });
    }
    };
}

// 页面加载完成后初始化适配器
document.addEventListener('DOMContentLoaded', () => {
    // 等待TrafficEChartsManager和TabLifecycleHandler加载
    setTimeout(() => {
        if (window.TrafficEChartsManager && window.TabLifecycleHandler && window.TrafficEChartsAdapter) {
            // 检查依赖项是否已加载
            if (typeof window.TrafficEChartsManager === 'undefined') {
                console.warn('TrafficEChartsManager未加载，适配器无法初始化');
            }

            if (typeof window.TabLifecycleHandler === 'undefined') {
                console.warn('TabLifecycleHandler未加载，适配器无法初始化');
            }

            window.TrafficEChartsAdapter.init();
        }
    }, 1000); // 增加延迟，确保依赖项已加载
});
