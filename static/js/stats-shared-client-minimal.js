// SharedWorker客户端接口 - 极简版
// 单一职责：提供与SharedWorker通信的简洁接口

class StatsSharedClient {
  // 静态方法：获取单例实例
  static getInstance(options = {}) {
    if (!StatsSharedClient.instance) {
      StatsSharedClient.instance = new StatsSharedClient(options);
    } else if (options.nodeId !== undefined && options.nodeId !== StatsSharedClient.instance.options.nodeId) {
      // nodeId变化时，不销毁实例，仅发送新的init消息切换连接
      StatsSharedClient.instance._sendToWorker('init', { 
        nodeId: options.nodeId,
        protocol: window.location.protocol,
        host: window.location.host
      });
      StatsSharedClient.instance.options.nodeId = options.nodeId;
    }
    return StatsSharedClient.instance;
  }
  
  /**
   * 构造函数
   * @param {Object} options 配置选项
   * @param {string|null} options.nodeId 节点ID，如果指定则连接到单个节点的WebSocket
   * @param {Function} options.onMessage 接收到消息时的回调函数
   * @param {Function} options.onConnected 连接成功时的回调函数
   * @param {Function} options.onDisconnected 连接断开时的回调函数
   * @param {boolean} options.debug 是否开启调试模式
   */
  constructor(options = {}) {
    this.options = Object.assign({
      nodeId: null,
      onMessage: null,
      onConnected: null,
      onDisconnected: null,
      debug: false
    }, options);
    
    // 初始化状态
    this.worker = null;
    this.port = null;
    this.isConnected = false;
    this.lastData = null;
    this.isSupported = typeof SharedWorker !== 'undefined';
    
    // 初始化worker
    if (this.isSupported) {
      this._initWorker();
    } else if (this.options.debug) {
      console.log('浏览器不支持SharedWorker，需要实现备选方案');
    }
    
    // 页面可见性处理
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && this.port) {
        // 页面变为可见时，请求最新数据
        this.requestLastData();
      }
    });
  }
  
  /**
   * 初始化SharedWorker
   * @private
   */
  _initWorker() {
    try {
      // 使用极简版worker脚本，添加固定版本号避免缓存问题
      // 使用固定版本号而不是时间戳，避免每次刷新创建新Worker实例
      const workerVersion = '2025.01.17.1'; // 修改此版本号以强制更新
      // 使用不同的URL区分全局与单节点模式，避免不同页面共享同一Worker导致订阅互相干扰
      const scopeMode = this.options.nodeId ? 'node' : 'global';
      const scopeSid  = this.options.nodeId ? `&sid=${encodeURIComponent(this.options.nodeId)}` : '';
      const workerUrl = `/js/stats-shared-worker-minimal.js?v=${workerVersion}&scope=${scopeMode}${scopeSid}`;
      this.worker = new SharedWorker(workerUrl);
      this.port = this.worker.port;
      
      // 监听消息
      this.port.addEventListener('message', (e) => this._onWorkerMessage(e));
      
      // 启动端口
      this.port.start();
      
      // 向worker发送初始化消息，包含页面的location信息
      this._sendToWorker('init', {
        nodeId: this.options.nodeId,
        protocol: window.location.protocol,
        host: window.location.host
      });
      
      // 初始化后立即请求最新数据
      setTimeout(() => {
        this.requestLastData();
      }, 50);
      
      // 添加页面卸载事件处理
      window.addEventListener('beforeunload', () => {
        if (this.port) {
          // 在页面卸载前尝试告知Worker
          this._sendToWorker('page_unload');
        }
      });
      
      // 定期检查连接状态
      this.connectionCheckTimer = setInterval(() => {
        if (this.port) {
          this._sendToWorker('check_connection');
        }
      }, 30000); // 每30秒检查一次
      
      if (this.options.debug) {
        console.log('SharedWorker初始化成功');
      }
    } catch (error) {
      console.error('初始化SharedWorker失败:', error);
      this.isSupported = false;
    }
  }
  
  /**
   * 处理从Worker收到的消息
   * @param {MessageEvent} e 消息事件
   * @private
   */
  _onWorkerMessage(e) {
    const message = e.data;
    
    if (!message || !message.type) return;
    
    switch (message.type) {
      case 'debug':
        // 输出调试信息
        console.log('[SharedWorker Debug]', message.message);
        break;
        
      case 'worker_ready':
        // SharedWorker 本身已就绪，立即触发成功回调
        // 这比等待 WebSocket 连接要快得多
        if (this.options.onWorkerReady) {
          this.options.onWorkerReady();
        }
        if (this.options.debug) {
          console.log('SharedWorker已就绪');
        }
        break;
        
      case 'connection_status':
        const isConnected = message.status === 'connected';
        
        // 只有状态变化才触发回调
        if (this.isConnected !== isConnected) {
          this.isConnected = isConnected;
          
          // 触发相应的回调
          if (isConnected && this.options.onConnected) {
            this.options.onConnected();
          } else if (!isConnected && this.options.onDisconnected) {
            this.options.onDisconnected();
          }
          
          // 触发自定义事件
          document.dispatchEvent(new CustomEvent('connection:status', {
            detail: { status: message.status }
          }));
        }
        break;
        
      case 'ws_message':
        this.lastData = message.data;
        
        // 调用消息回调
        if (this.options.onMessage) {
          // 来自HTTP的备用数据
          const isFromHttp = message.fromHttp === true;
          
          // 如果页面处于可见状态，直接处理消息
          if (!document.hidden) {
            // 传递数据来源信息，让回调知道这是WebSocket还是HTTP的数据
            this.options.onMessage(message.data, message.fromCache, isFromHttp);
          }
          // 如果页面不可见，且消息不是来自缓存，则不处理
          // 这避免了页面在后台时的不必要更新
          else if (message.fromCache) {
            this.options.onMessage(message.data, message.fromCache, isFromHttp);
          }
          
          // 如果是来自HTTP的备用数据，触发自定义事件
          if (isFromHttp) {
            document.dispatchEvent(new CustomEvent('httpBackupDataReceived', {
              detail: { timestamp: message.timestamp }
            }));
          }
        }
        break;
        
      case 'worker_heartbeat':
        // 心跳检测，不需要特殊处理
        break;
        
      case 'connection_stats':
        // 连接统计数据，包含WebSocket连接状态、HTTP请求统计等
        
        // 触发连接统计事件，供其他模块使用
        document.dispatchEvent(new CustomEvent('connection:stats', {
          detail: message.data
        }));
        break;
        
      default:
        if (this.options.debug) {
          console.log('收到未知类型的消息:', message);
        }
    }
  }
  
  /**
   * 向Worker发送消息
   * @param {string} type 消息类型
   * @param {Object} data 消息数据
   * @private
   */
  _sendToWorker(type, data = {}) {
    if (!this.isSupported || !this.port) return;
    
    try {
      this.port.postMessage({ type, ...data });
    } catch (error) {
      console.error('向SharedWorker发送消息失败:', error);
    }
  }
  
  /**
   * 请求最近一次接收到的数据
   */
  requestLastData() {
    this._sendToWorker('get_last_data');
  }
  
  /**
   * 检查连接状态
   */
  checkConnection() {
    this._sendToWorker('check_connection');
  }
  
  /**
   * 清除缓存
   */
  clearCache() {
    this._sendToWorker('clear_cache');
  }
  
  /**
   * 销毁客户端实例，清理资源
   */
  destroy() {
    // 清除定时器
    if (this.connectionCheckTimer) {
      clearInterval(this.connectionCheckTimer);
      this.connectionCheckTimer = null;
    }
    
    // 清除所有事件监听器
    if (this.port) {
      // 发送关闭消息
      this._sendToWorker('close');
      
      // 关闭连接不会立即生效，等待一段时间再清理资源
      setTimeout(() => {
        this.port = null;
        this.worker = null;
        this.isConnected = false;
        this.lastData = null;
      }, 100);
    }
    
    // 移除实例引用
    StatsSharedClient.instance = null;
  }
}

// 暴露给全局
window.StatsSharedClient = StatsSharedClient;
