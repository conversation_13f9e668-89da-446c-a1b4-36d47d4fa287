/**
 * 网络质量监控图表模块 - ECharts版本
 * 使用ECharts替代ApexCharts，提高大数据量渲染性能
 */

// 确保ECharts已加载
if (typeof echarts === 'undefined') {
    console.error('ECharts未加载，网络质量监控图表功能将不可用');
    // 尝试从window对象获取echarts
    if (window.echarts) {
        console.log('从window对象获取echarts成功');
        var echarts = window.echarts;
    } else {
        console.error('无法从window对象获取echarts');
    }
}

// 网络质量监控ECharts图表管理器
const NetworkQualityEChartsManager = {
    // 图表实例
    charts: {
        minute: null,
        hour: null,
        day: null,
        month: null
    },

    // 数据存储
    data: {
        // 分钟数据 (60分钟)
        minute: {
            latency: [],
            labels: []
        },
        // 小时数据 (24小时)
        hour: {
            latency: [],
            labels: []
        },
        // 天数据 (30天)
        day: {
            latency: [],
            labels: []
        },
        // 月数据 (12个月)
        month: {
            latency: [],
            labels: []
        }
    },

    // 图表容器ID映射
    chartContainerIds: {
        minute: 'network-quality-minute-echarts',
        hour: 'network-quality-hour-echarts',
        day: 'network-quality-day-echarts',
        month: 'network-quality-month-echarts'
    },

    // 初始化
    init() {
        console.log('初始化网络质量监控ECharts图表管理器...');

        // 注册事件监听器
        this._registerEventListeners();

        // 加载数据
        this._loadData();

        console.log('网络质量监控ECharts图表管理器初始化完成');
    },

    // 注册事件监听器
    _registerEventListeners() {
        console.log('注册网络质量监控图表事件监听器...');

        // 窗口大小改变时重新渲染图表
        window.addEventListener('resize', () => {
            // 获取当前活动的标签页
            const activeTab = document.querySelector('.network-quality-tab.active');
            const timeRange = activeTab ? activeTab.dataset.tab : 'minute';

            // 重新渲染当前活动的图表
            if (this.charts[timeRange]) {
                this.charts[timeRange].resize();
            }
        });

        // 刷新按钮点击事件
        const refreshButton = document.getElementById('refresh-network-quality');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this._loadData();
            });
        }

        // 监听网络质量标签页切换事件
        document.querySelectorAll('.network-quality-tab').forEach(tab => {
            tab.addEventListener('click', (event) => {
                const timeRange = event.target.dataset.tab;
                if (!timeRange) return;

                console.log(`切换到${timeRange}标签页`);

                // 移除所有标签页的active类
                document.querySelectorAll('.network-quality-tab').forEach(t => {
                    t.classList.remove('active');
                    t.classList.remove('border-indigo-600', 'dark:border-indigo-400', 'text-indigo-600', 'dark:text-indigo-400');
                    t.classList.add('border-transparent', 'text-slate-600', 'dark:text-slate-300');
                });

                // 添加当前标签页的active类
                tab.classList.add('active');
                tab.classList.add('border-indigo-600', 'dark:border-indigo-400', 'text-indigo-600', 'dark:text-indigo-400');
                tab.classList.remove('border-transparent', 'text-slate-600', 'dark:text-slate-300');

                // 隐藏所有图表容器
                document.querySelectorAll('.network-quality-chart-container > div').forEach(container => {
                    container.classList.add('hidden');
                    container.setAttribute('hidden', '');
                });

                // 显示当前图表容器
                const container = document.getElementById(`network-quality-${timeRange}`);
                if (container) {
                    container.classList.remove('hidden');
                    container.removeAttribute('hidden');

                    // 渲染图表
                    this._renderChart(timeRange);
                }
            });
        });

        console.log('注册网络质量监控图表事件监听器成功');
    },

    // 获取当前节点ID
    _getCurrentNodeId() {
        // 尝试从多个来源获取节点ID

        // 1. 从URL路径获取节点ID
        const statsMatch = location.pathname.match(/\/stats\/([^\/]+)/);
        if (statsMatch && statsMatch[1]) {
            console.log('从URL路径获取到节点ID:', statsMatch[1]);
            return statsMatch[1];
        }

        // 2. 从URL路径的最后一部分获取
        const pathParts = location.pathname.split('/').filter(Boolean);
        if (pathParts.length > 0) {
            const lastPart = pathParts[pathParts.length - 1];
            if (lastPart && !['stats', 'traffic', 'data', 'latest'].includes(lastPart)) {
                console.log('从URL路径最后一部分获取到节点ID:', lastPart);
                return lastPart;
            }
        }

        // 3. 从 #node-data 隐藏域获取
        const nodeDataElement = document.getElementById('node-data');
        if (nodeDataElement && nodeDataElement.value) {
            try {
                const nodeData = JSON.parse(nodeDataElement.value);
                if (nodeData && nodeData.id) {
                    console.log('从node-data元素获取到节点ID:', nodeData.id);
                    return nodeData.id;
                }
            } catch (e) {
                console.warn('解析node-data元素失败:', e);
            }
        }

        // 4. 从 #preprocessed-data 隐藏域获取
        const preProcessedElement = document.getElementById('preprocessed-data');
        if (preProcessedElement && preProcessedElement.value) {
            try {
                const preProcessedData = JSON.parse(preProcessedElement.value);
                if (preProcessedData && preProcessedData.id) {
                    console.log('从preprocessed-data元素获取到节点ID:', preProcessedData.id);
                    return preProcessedData.id;
                }
                if (preProcessedData && preProcessedData.stat && preProcessedData.stat.id) {
                    console.log('从preprocessed-data.stat元素获取到节点ID:', preProcessedData.stat.id);
                    return preProcessedData.stat.id;
                }
            } catch (e) {
                console.warn('解析preprocessed-data元素失败:', e);
            }
        }

        console.warn('无法获取当前节点ID');
        return null;
    },

    // 加载数据
    _loadData() {
        console.log('加载网络质量监控数据...');

        // 显示加载指示器
        const loadingIndicator = document.getElementById('network-quality-loading');
        if (loadingIndicator) {
            loadingIndicator.classList.remove('hidden');
        }

        // 获取当前节点ID
        const currentNodeId = this._getCurrentNodeId();
        console.log('当前节点ID:', currentNodeId);

        // 使用Promise.all并行加载所有时间范围的数据
        Promise.all([
            this._loadDataForTimeRange('minute', currentNodeId),
            this._loadDataForTimeRange('hour', currentNodeId),
            this._loadDataForTimeRange('day', currentNodeId),
            this._loadDataForTimeRange('month', currentNodeId)
        ])
        .then(() => {
            console.log('所有时间范围的数据加载完成');

            // 获取当前活动的标签页
            const activeTab = document.querySelector('.network-quality-tab.active');
            const timeRange = activeTab ? activeTab.dataset.tab : 'minute';

            // 渲染当前活动标签页的图表
            this._renderChart(timeRange);
        })
        .catch(error => {
            console.error('加载数据时出错:', error);
        })
        .finally(() => {
            // 隐藏加载指示器
            if (loadingIndicator) {
                loadingIndicator.classList.add('hidden');
            }
            console.log('数据加载完成');
        });
    },

    // 加载特定时间范围的数据
    _loadDataForTimeRange(timeRange, currentNodeId) {
        return new Promise((resolve, reject) => {
            console.log(`加载${timeRange}时间范围的数据...`);

            // 构建API请求参数
            const params = new URLSearchParams();

            // 确定API类型参数
            let typeParam;
            switch (timeRange) {
                case 'minute': typeParam = 'm'; break;
                case 'hour': typeParam = 'h'; break;
                case 'day': typeParam = 'd'; break;
                case 'month': typeParam = 'month'; break;
                default: typeParam = 'm';
            }
            params.append('type', typeParam);

            // 对于小时视图，请求详细数据（分钟级数据）
            if (timeRange === 'hour') {
                params.append('detail_level', 'detailed');
                params.append('sampling_rate', '1'); // 不进行采样，使用所有数据
                params.append('limit', '240'); // 请求最大数量的数据（4小时的分钟级数据）
            }

            // 对于天视图，请求详细数据（小时级数据）
            if (timeRange === 'day') {
                params.append('detail_level', 'detailed');
                params.append('sampling_rate', '1'); // 不进行采样，使用所有数据
            }

            // 添加节点ID参数（如果有）
            if (currentNodeId) {
                params.append('node_id', currentNodeId);
            }

            console.log(`${timeRange}请求参数:`, params.toString());

            // 发送请求
            fetch(`/api/monitor/data?${params.toString()}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    return response.json();
                })
                .then(result => {
                    if (result.success) {
                        console.log(`获取到${timeRange}数据: ${result.data.length}条记录`);
                        // 处理数据
                        this._processData(timeRange, result.data, currentNodeId);
                        resolve();
                    } else {
                        console.error(`获取${timeRange}数据失败:`, result.message);
                        // 设置默认数据
                        this._setDefaultData(timeRange);
                        resolve(); // 仍然解析Promise，避免阻塞其他时间范围的数据加载
                    }
                })
                .catch(error => {
                    console.error(`获取${timeRange}数据出错:`, error);
                    // 设置默认数据
                    this._setDefaultData(timeRange);
                    resolve(); // 仍然解析Promise，避免阻塞其他时间范围的数据加载
                });
        });
    },

    // 设置默认数据
    _setDefaultData(timeRange) {
        this.data[timeRange].latency = [0];
        this.data[timeRange].labels = ['无数据'];
    },

    // 处理数据
    _processData(timeRange, data, currentNodeId) {
        // 重置数据
        this.data[timeRange].latency = [];
        this.data[timeRange].labels = [];

        // 如果没有传入当前节点ID，尝试获取
        if (!currentNodeId) {
            currentNodeId = this._getCurrentNodeId();
        }

        console.log(`处理${timeRange}数据，当前节点ID:`, currentNodeId);

        // 如果数据为空，添加默认数据点
        if (!data || !Array.isArray(data) || data.length === 0) {
            console.warn(`${timeRange}数据为空，添加默认数据点`);
            this.data[timeRange].latency.push(0);
            this.data[timeRange].labels.push('无数据');
            return;
        }

        // 记录原始数据的时间范围
        if (data.length > 0) {
            const firstItem = data[0];
            const lastItem = data[data.length - 1];
            const startTime = new Date(Math.min(firstItem.created_at, lastItem.created_at) * 1000);
            const endTime = new Date(Math.max(firstItem.created_at, lastItem.created_at) * 1000);
            const spanHours = (Math.max(firstItem.created_at, lastItem.created_at) - Math.min(firstItem.created_at, lastItem.created_at)) / 3600;
            
            console.log(`${timeRange}原始数据时间范围: 开始=${startTime.toLocaleString()}, 结束=${endTime.toLocaleString()}, 跨度=${spanHours.toFixed(2)}小时`);
        }

        // 按时间排序
        data.sort((a, b) => a.created_at - b.created_at);
        console.log(`${timeRange}原始数据: ${data.length}条记录，已按时间升序排序`);

        // 检查数据中是否包含节点ID信息
        if (data.length > 0) {
            const firstItem = data[0];
            console.log(`${timeRange}数据样本:`, firstItem);

            // 检查可能的节点ID字段
            const possibleNodeIdFields = ['sid', 'node_id', 'server_id', 'id'];
            for (const field of possibleNodeIdFields) {
                if (firstItem[field]) {
                    console.log(`发现可能的节点ID字段: ${field} = ${firstItem[field]}`);
                }
            }
        }

        // 筛选当前节点的数据
        let filteredData = data;
        if (currentNodeId) {
            // 尝试多种可能的字段名
            const nodeIdFields = ['sid', 'node_id', 'server_id', 'id'];

            for (const field of nodeIdFields) {
                const tempData = data.filter(item => item[field] === currentNodeId);
                if (tempData.length > 0) {
                    console.log(`使用字段 ${field} 筛选当前节点(${currentNodeId})的${timeRange}数据，筛选前: ${data.length}条，筛选后: ${tempData.length}条`);
                    filteredData = tempData;
                    break;
                }
            }

            // 如果所有字段都没有匹配，记录警告
            if (filteredData.length === 0 || filteredData.length === data.length) {
                console.warn(`无法筛选当前节点(${currentNodeId})的${timeRange}数据，显示所有数据`);

                // 如果API已经过滤了数据，直接使用
                if (data.length > 0 && data.length < 100) {  // 假设小于100条的数据是已经过滤过的
                    console.log(`假设API已经过滤了${timeRange}数据，直接使用`);
                    filteredData = data;
                }
            }
        } else {
            console.warn(`未提供当前节点ID，显示所有${timeRange}数据`);
        }

        // 如果过滤后的数据为空，使用所有数据
        if (filteredData.length === 0) {
            console.warn(`过滤后的${timeRange}数据为空，使用全部数据`);
            filteredData = data;
        }

        // 记录过滤后数据的时间范围
        if (filteredData.length > 0) {
            const firstItem = filteredData[0];
            const lastItem = filteredData[filteredData.length - 1];
            const startTime = new Date(Math.min(firstItem.created_at, lastItem.created_at) * 1000);
            const endTime = new Date(Math.max(firstItem.created_at, lastItem.created_at) * 1000);
            const spanHours = (Math.max(firstItem.created_at, lastItem.created_at) - Math.min(firstItem.created_at, lastItem.created_at)) / 3600;
            
            console.log(`${timeRange}过滤后数据时间范围: 开始=${startTime.toLocaleString()}, 结束=${endTime.toLocaleString()}, 跨度=${spanHours.toFixed(2)}小时, 记录数=${filteredData.length}`);
        }

        // 提取数据
        filteredData.forEach(item => {
            // 延迟数据取整
            this.data[timeRange].latency.push(Math.round(item.avg_time));

            // 格式化时间标签
            const date = new Date(item.created_at * 1000);
            let formattedTime;

            switch (timeRange) {
                case 'minute':
                    // 分钟粒度只显示到分钟
                    formattedTime = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                    break;
                case 'hour':
                    formattedTime = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                    break;
                case 'day':
                    formattedTime = date.toLocaleDateString([], { month: 'numeric', day: 'numeric', hour: '2-digit' });
                    break;
                case 'month':
                    formattedTime = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
                    break;
                default:
                    formattedTime = date.toLocaleString();
            }

            this.data[timeRange].labels.push(formattedTime);
        });

        console.log(`处理了${this.data[timeRange].latency.length}条${timeRange}网络质量监控数据`);

        // 如果没有数据，添加一个默认数据点
        if (this.data[timeRange].latency.length === 0) {
            console.warn(`${timeRange}没有数据，添加默认数据点`);
            this.data[timeRange].latency.push(0);
            this.data[timeRange].labels.push('无数据');
        }
    },

    // 渲染图表
    _renderChart(timeRange = 'minute') {
        console.log(`开始渲染${timeRange} ECharts图表...`);

        // 获取图表容器ID
        const containerId = this.chartContainerIds[timeRange];
        if (!containerId) {
            console.error(`找不到${timeRange}图表容器ID`);
            return;
        }

        // 获取图表容器
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`找不到图表容器: ${containerId}`);
            return;
        }

        console.log(`找到图表容器: ${containerId}`);

        // 确保容器可见
        const parentContainer = document.getElementById(`network-quality-${timeRange}`);
        if (parentContainer && parentContainer.classList.contains('hidden')) {
            console.log(`${timeRange}图表容器父元素处于隐藏状态，暂不渲染图表`);
            return;
        }

        // 确保echarts可用
        if (typeof echarts === 'undefined' && typeof window.echarts === 'undefined') {
            console.error('ECharts库未加载，无法渲染图表');
            return;
        }

        // 使用正确的echarts对象
        const echartsObj = typeof echarts !== 'undefined' ? echarts : window.echarts;
        console.log('使用echarts对象:', echartsObj ? '可用' : '不可用');

        // 如果图表已存在，销毁它
        if (this.charts[timeRange]) {
            console.log(`销毁已存在的${timeRange} ECharts图表`);
            this.charts[timeRange].dispose();
        }

        try {
            // 初始化图表
            console.log(`初始化${timeRange} ECharts图表`);
            this.charts[timeRange] = echartsObj.init(container);

            // 检查图表是否成功初始化
            if (!this.charts[timeRange]) {
                console.error(`${timeRange} ECharts图表初始化失败`);
                return;
            }

            console.log(`${timeRange} ECharts图表初始化成功`);

            // 获取当前主题
            const isDarkMode = document.documentElement.classList.contains('dark');
            const textColor = isDarkMode ? '#f1f5f9' : '#1e293b';
            const gridLineColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
            const axisLineColor = isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)';

            // 根据时间范围调整图表配置
            let xAxisLabelInterval;
            let symbolSize = 0; // 默认不显示数据点标记
            let lineWidth = 1.5; // 默认线宽
            let enableDataZoom = true; // 默认启用缩放功能

            switch (timeRange) {
                case 'minute':
                    // 分钟视图可能有60个数据点
                    xAxisLabelInterval = index => index % 5 === 0; // 每5个标签显示一个
                    symbolSize = 2; // 显示小标记点
                    break;
                case 'hour':
                    // 小时视图可能有240个数据点（4小时的分钟级数据）
                    xAxisLabelInterval = index => index % 10 === 0; // 每10个标签显示一个
                    symbolSize = 0; // 不显示标记点
                    break;
                case 'day':
                    // 天视图可能有24个数据点（小时级数据）
                    xAxisLabelInterval = index => index % 3 === 0; // 每3个标签显示一个
                    symbolSize = 2; // 显示小标记点
                    break;
                case 'month':
                    // 月视图可能有30个数据点（天级数据）
                    xAxisLabelInterval = index => index % 5 === 0; // 每5个标签显示一个
                    symbolSize = 3; // 显示标记点
                    break;
                default:
                    xAxisLabelInterval = index => index % 5 === 0;
            }

            // 图表配置
            const option = {
                title: {
                    show: false // 隐藏标题，与原有图表保持一致
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: isDarkMode ? 'rgba(17, 24, 39, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                    borderColor: isDarkMode ? '#374151' : '#e2e8f0',
                    textStyle: {
                        color: textColor
                    },
                    formatter: function(params) {
                        const time = params[0].name;
                        const value = params[0].value;
                        return `<div style="font-weight: bold; margin-bottom: 4px;">时间: ${time}</div>延迟: ${value} ms`;
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '3%',
                    containLabel: true
                },
                toolbox: {
                    show: false // 隐藏工具箱，与原有图表保持一致
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: this.data[timeRange].labels,
                    axisLine: {
                        lineStyle: {
                            color: axisLineColor
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: textColor,
                        interval: xAxisLabelInterval,
                        fontSize: 10,
                        rotate: 0 // 不旋转标签
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: gridLineColor,
                            type: 'dashed'
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '延迟',
                    nameTextStyle: {
                        color: textColor,
                        fontSize: 10
                    },
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: textColor,
                        formatter: '{value} ms',
                        fontSize: 10
                    },
                    splitLine: {
                        lineStyle: {
                            color: gridLineColor,
                            type: 'dashed'
                        }
                    }
                },
                series: [
                    {
                        name: '延迟',
                        type: 'line',
                        sampling: 'lttb', // 使用LTTB采样算法，提高大数据量渲染性能
                        symbol: symbolSize > 0 ? 'circle' : 'none', // 根据symbolSize决定是否显示数据点标记
                        symbolSize: symbolSize,
                        lineStyle: {
                            width: lineWidth, // 使用更细的线宽，适合显示更多数据点
                            color: '#6366f1'
                        },
                        itemStyle: {
                            color: '#6366f1'
                        },
                        emphasis: {
                            itemStyle: {
                                color: '#4f46e5',
                                borderColor: '#6366f1',
                                borderWidth: 2
                            }
                        },
                        areaStyle: {
                            color: new echartsObj.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: 'rgba(99, 102, 241, 0.5)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(99, 102, 241, 0.05)'
                                }
                            ])
                        },
                        data: this.data[timeRange].latency,
                        smooth: true, // 使用平滑曲线
                        animation: false // 禁用动画，提高性能
                    }
                ],
                // 禁用缩放功能 - 只允许通过全屏图标放大
                dataZoom: []
            };

            // 设置配置
            console.log(`设置${timeRange} ECharts图表配置`);
            this.charts[timeRange].setOption(option);

            // 添加窗口大小变化事件监听器
            window.addEventListener('resize', () => {
                if (this.charts[timeRange]) {
                    this.charts[timeRange].resize();
                }
            });

            console.log(`渲染${timeRange}网络质量监控ECharts图表成功`);
        } catch (error) {
            console.error(`渲染${timeRange} ECharts图表时出错:`, error);
        }
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('页面加载完成，准备初始化网络质量监控ECharts图表管理器...');

    // 确保页面完全加载，包括所有资源
    window.addEventListener('load', () => {
        console.log('页面资源完全加载完成，开始初始化网络质量监控ECharts图表管理器');

        // 延迟初始化，确保DOM和其他依赖已完全加载
        setTimeout(() => {
            // 检查ECharts是否可用
            if (typeof echarts === 'undefined' && typeof window.echarts === 'undefined') {
                console.error('ECharts未加载，尝试动态加载ECharts库');

                // 动态加载ECharts库
                const script = document.createElement('script');
                script.src = '/js/libs/echarts.min.js';
                script.onload = () => {
                    console.log('ECharts库加载成功，初始化网络质量监控ECharts图表管理器');
                    // 初始化网络质量监控ECharts图表管理器
                    NetworkQualityEChartsManager.init();
                };
                script.onerror = () => {
                    console.error('ECharts库加载失败');
                };
                document.head.appendChild(script);
            } else {
                console.log('ECharts库已加载，直接初始化网络质量监控ECharts图表管理器');
                // 初始化网络质量监控ECharts图表管理器
                NetworkQualityEChartsManager.init();
            }
        }, 1500);
    });
});

// 导出网络质量监控ECharts图表管理器
window.NetworkQualityEChartsManager = NetworkQualityEChartsManager;
