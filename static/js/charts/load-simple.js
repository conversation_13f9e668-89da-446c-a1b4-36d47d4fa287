// 极简负载详情，仅依赖ECharts和原生JS
(function() {
    // 队列最大长度（10分钟，每2秒1点）
    const MAX_POINTS = 300;
    let cpuData = [], memData = [], swapData = [], labels = [];
    let chart = null;
    let currentRange = '10m';
    let statSubId = 'load-simple-realtime';
    let isRealtimeSubscribed = false;
    let currentTheme = null;
    let timestamps = []; // 保存时间戳（毫秒）用于tooltip与采样对齐

    // 获取节点ID
    function getSid() {
        const match = location.pathname.match(/\/stats\/([^\/]+)/);
        return match ? match[1] : null;
    }

    // API URL生成（统一使用bandwidth/history，带fields参数）
    function getApiUrl(sid, range) {
        const rangeMap = { '10m': '10min' };
        const r = rangeMap[range] || range;
        return `/api/stats/${sid}/bandwidth/history?range=${r}&fields=cpu,mem,swap`;
    }

    // 格式化时间标签（短周期）
    function formatTime(ts) {
        const d = new Date(ts);
        return `${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}`;
    }

    // 使用共享的 ChartLabels.formatFullTime

    // 使用共享的 ChartLabels.generateSmartLabels

    function getTheme() {
        // 兼容 tailwind/daisyui/自定义，优先检测 html 或 body 的 class
        if (document.documentElement.classList.contains('dark') || document.body.classList.contains('dark')) {
            return 'dark';
        }
        return 'light';
    }

    // 颜色缓存，避免重复计算
    const colorCache = new Map();
    
    function getColorByUsage(usage, type) {
        const cacheKey = `${type}-${Math.floor(usage/10)*10}`; // 按10%分组缓存
        if (colorCache.has(cacheKey)) {
            return colorCache.get(cacheKey);
        }
        
        let color;
        if (usage < 30) {
            color = type === 'cpu' ? '#3b82f6' : type === 'mem' ? '#22c55e' : '#ef4444';
        } else if (usage >= 30 && usage <= 50) {
            color = type === 'cpu' ? '#60a5fa' : type === 'mem' ? '#facc15' : '#fb923c';
        } else if (usage > 80) {
            color = type === 'cpu' ? '#7c3aed' : type === 'mem' ? '#eab308' : '#b91c1c';
        } else {
            color = type === 'cpu' ? '#3b82f6' : type === 'mem' ? '#22c55e' : '#ef4444';
        }
        
        colorCache.set(cacheKey, color);
        return color;
    }

    // 初始化ECharts
    function getChart(forceRecreate) {
        const chartDom = document.getElementById('load-echarts-chart');
        if (!chartDom) return null;
        const theme = getTheme();
        if (!chart || forceRecreate || currentTheme !== theme) {
            if (chart) { chart.dispose(); }
            chart = echarts.init(chartDom, theme);
            currentTheme = theme;
        }
        return chart;
    }

    // 渲染/更新图表
    function updateChart(title) {
        const c = getChart();
        if (!c) return;
        
        // 使用统一配置
        if (window.UnifiedChartConfig) {
            const baseConfig = window.UnifiedChartConfig.getCommonOptions({
                title: { text: title },
                xAxis: {
                    data: labels,
                    axisTick: { show: false },
                    axisLabel: { 
                        interval: 0, // 显示所有标签（空字符串会被自动隐藏）
                        rotate: window.innerWidth <= 600 ? 45 : 0, // 移动端旋转标签
                        fontSize: window.innerWidth <= 600 ? 10 : 12,
                        color: getTheme() === 'dark' ? '#94a3b8' : '#64748b'
                    },
                    axisPointer: {
                        label: {
                            formatter: function(obj){
                                try {
                                    const idx = (obj.seriesData && obj.seriesData[0]) ? obj.seriesData[0].dataIndex : null;
                                    const ts = (idx != null && window.loadTimestamps) ? window.loadTimestamps[idx] : null;
                                    return ts ? ChartLabels.formatFullTime(ts) : (obj.value || '');
                                } catch(e) { return obj.value || ''; }
                            }
                        }
                    }
                },
                yAxis: window.UnifiedChartConfig.getPercentageYAxis(),
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        let timeDisplay = '时间未知';
                        const idx = (params && params[0]) ? params[0].dataIndex : null;
                        if (idx != null && window.loadTimestamps && window.loadTimestamps[idx]) {
                            timeDisplay = ChartLabels.formatFullTime(window.loadTimestamps[idx]);
                        } else if (params[0] && params[0].axisValue) {
                            timeDisplay = params[0].axisValue;
                        }
                        let result = timeDisplay + '<br/>';
                        params.forEach(param => {
                            const value = param.value === undefined ? 0 : param.value;
                            result += `${param.marker} ${param.seriesName}: ${Number(value).toFixed(1)}%<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: ['CPU', '内存', 'SWAP']
                },
                series: [
                    window.UnifiedChartConfig.getCommonSeries('line', {
                        name: 'CPU',
                        data: cpuData,
                        lineStyle: { color: getColorByUsage(cpuData[cpuData.length - 1], 'cpu') },
                        itemStyle: { color: getColorByUsage(cpuData[cpuData.length - 1], 'cpu') },
                        dataLength: cpuData.length
                    }),
                    window.UnifiedChartConfig.getCommonSeries('line', {
                        name: '内存',
                        data: memData,
                        lineStyle: { color: getColorByUsage(memData[memData.length - 1], 'mem') },
                        itemStyle: { color: getColorByUsage(memData[memData.length - 1], 'mem') },
                        dataLength: memData.length
                    }),
                    window.UnifiedChartConfig.getCommonSeries('line', {
                        name: 'SWAP',
                        data: swapData,
                        lineStyle: { color: getColorByUsage(swapData[swapData.length - 1], 'swap') },
                        itemStyle: { color: getColorByUsage(swapData[swapData.length - 1], 'swap') },
                        dataLength: swapData.length
                    })
                ]
            });
            
            c.setOption(baseConfig);
        } else {
            // 降级处理，使用原有配置
            const isMobile = window.innerWidth <= 600;
            const theme = getTheme();
            c.setOption({
                backgroundColor: 'transparent',
                title: {
                    text: title,
                    left: 'center',
                    textStyle: {
                        fontSize: isMobile ? 12 : 14,
                        color: theme === 'dark' ? '#94a3b8' : '#64748b'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    },
                    formatter: function(params) {
                        let result = params[0].axisValue + '<br/>';
                        params.forEach(param => {
                            const value = param.value === undefined ? 0 : param.value;
                            result += `${param.marker} ${param.seriesName}: ${value.toFixed(1)}%<br/>`;
                        });
                        return result;
                    }
                },
                legend: isMobile ?
                    { show: false } :
                    {
                        data: ['CPU', '内存', 'SWAP'],
                        top: 30,
                        textStyle: {
                            color: theme === 'dark' ? '#94a3b8' : '#64748b'
                        }
                    },
                grid: { left: isMobile ? '2%' : '3%', right: isMobile ? '2%' : '4%', bottom: '3%', top: '15%', containLabel: true },
                xAxis: {
                    type: 'category',
                    data: labels,
                    axisLabel: {
                        fontSize: isMobile ? 10 : 12,
                        color: theme === 'dark' ? '#94a3b8' : '#64748b',
                        rotate: isMobile ? 45 : 0, // 移动端旋转标签
                        interval: 0 // 显示所有标签（空字符串会被自动隐藏）
                    },
                    axisTick: { show: false },
                    axisLine: {
                        lineStyle: {
                            color: theme === 'dark' ? '#475569' : '#cbd5e1'
                        }
                    },
                    splitLine: { show: false },
                    axisPointer: {
                        label: {
                            formatter: function (obj) {
                                try {
                                    const idx = (obj.seriesData && obj.seriesData[0]) ? obj.seriesData[0].dataIndex : null;
                                    const ts = (idx != null && window.loadTimestamps) ? window.loadTimestamps[idx] : null;
                                    return ts ? ChartLabels.formatFullTime(ts) : (obj.value || '');
                                } catch (e) { return obj.value || ''; }
                            }
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '%',
                    min: 0,
                    max: 100,
                    interval: 20,
                    nameTextStyle: {
                        color: theme === 'dark' ? '#94a3b8' : '#64748b'
                    },
                    axisLabel: {
                        fontSize: isMobile ? 9 : 12,
                        color: theme === 'dark' ? '#94a3b8' : '#64748b',
                        formatter: '{value}%'
                    },
                    axisLine: {
                        show: false
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: theme === 'dark' ? '#334155' : '#e2e8f0',
                            type: 'solid',
                            width: 1,
                            opacity: 0.6
                        }
                    }
                },
                series: [
                    {
                        name: 'CPU',
                        type: 'line',
                        data: cpuData,
                        smooth: true,
                        symbol: 'none',
                        lineStyle: { color: getColorByUsage(cpuData[cpuData.length - 1], 'cpu') },
                        itemStyle: { color: getColorByUsage(cpuData[cpuData.length - 1], 'cpu') }
                    },
                    {
                        name: '内存',
                        type: 'line',
                        data: memData,
                        smooth: true,
                        symbol: 'none',
                        lineStyle: { color: getColorByUsage(memData[memData.length - 1], 'mem') },
                        itemStyle: { color: getColorByUsage(memData[memData.length - 1], 'mem') }
                    },
                    {
                        name: 'SWAP',
                        type: 'line',
                        data: swapData,
                        smooth: true,
                        symbol: 'none',
                        lineStyle: { color: getColorByUsage(swapData[swapData.length - 1], 'swap') },
                        itemStyle: { color: getColorByUsage(swapData[swapData.length - 1], 'swap') }
                    }
                ]
            });
        }
    }

    function getMaxPoints() {
        const chartDom = document.getElementById('load-echarts-chart');
        const width = chartDom ? chartDom.clientWidth : window.innerWidth || 1200;
        const pxPerPoint = (window.innerWidth <= 600) ? 2.8 : 2.0;
        const safety = 1.2;
        let target = Math.round((width / pxPerPoint) * safety);
        const minPts = (window.innerWidth <= 600) ? 300 : 400;
        return Math.max(minPts, Math.min(1200, target));
    }

    // 订阅实时数据
    function subscribeRealtime() {
        if (isRealtimeSubscribed || !window.StatManager) return;
        window.StatManager.subscribe(statSubId, function(data) {
            const sid = getSid();
            if (!sid || !data || !data[sid] || !data[sid].stat) return;

            const stat = data[sid].stat;
            // 获取CPU、内存和交换分区数据
            const cpu = stat.cpu ? (stat.cpu.multi * 100).toFixed(1) : 0;

            // 确保mem对象存在且有正确的结构
            let mem = stat.mem;
            if (!mem || typeof mem !== 'object') {
                mem = {
                    virtual: { usedPercent: 0, used: 0, total: 1 },
                    swap: { usedPercent: 0, used: 0, total: 1 }
                };
            }

            // 计算内存和交换分区使用百分比
            const memPercent = mem.virtual ?
                (mem.virtual.usedPercent || (mem.virtual.used / mem.virtual.total * 100)).toFixed(1) : 0;

            const swapPercent = mem.swap && mem.swap.total > 0 ?
                (mem.swap.usedPercent || (mem.swap.used / mem.swap.total * 100)).toFixed(1) : 0;

            const now = new Date();
            // 每30秒显示一个标签，其他时间点留空
            const seconds = now.getSeconds();
            const timeLabel = (seconds % 30 === 0) ? 
                `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}` : 
                '';

            cpuData.push(Number(cpu));
            memData.push(Number(memPercent));
            swapData.push(Number(swapPercent));
            labels.push(timeLabel);

            const maxPoints = getMaxPoints();
            if (cpuData.length > maxPoints) cpuData.shift();
            if (memData.length > maxPoints) memData.shift();
            if (swapData.length > maxPoints) swapData.shift();
            if (labels.length > maxPoints) labels.shift();

            updateChart('实时负载');
        });
        isRealtimeSubscribed = true;
    }

    function unsubscribeRealtime() {
        if (window.StatManager && isRealtimeSubscribed) {
            window.StatManager.unsubscribe(statSubId);
            isRealtimeSubscribed = false;
        }
    }

    // 加载历史数据
    // 替换历史加载逻辑，统一走 bandwidth/history + 智能采样
    async function loadHistory(range) {
        const sid = getSid();
        if (!sid) return;
        const url = getApiUrl(sid, range);
        try {
            const resp = await fetch(url);
            const json = await resp.json();
            if (json.status !== 'success' || !json.data) return;

            const data = json.data;
            const maxPoints = getMaxPoints();

            // 处理不同时间范围的数据格式
            if (range === '10m' || range === '1h') {
                // 处理实时数据格式
                cpuData = (data.cpu || []).map(x => Number(x.toFixed(1)));
                memData = (data.mem || []).map(x => Number(x.toFixed(1)));
                swapData = (data.swap || []).map(x => Number(x.toFixed(1)));
                
                // 使用共享的智能标签生成
                labels = ChartLabels.generateSmartLabels(data.timestamps || [], range, 'load-echarts-chart');
                
                // 保存时间戳供tooltip使用
                window.loadTimestamps = data.timestamps || [];
            } else {
                // 处理历史数据格式 - API返回对象格式 {timestamps: [], cpu: [], mem: [], swap: []}
                const timestamps = data.timestamps || [];
                const cpuValues = data.cpu || [];
                const memValues = data.mem || [];
                const swapValues = data.swap || [];

                // 采样目标点数配置
                const SAMPLING_TARGETS = {
                    'default': 200,
                    '24h': 200,    // 24小时：目标200个点（减少58%）
                    '7d': 168,     // 7天：目标168个点（减少50%）
                    '30d': 180,    // 30天：目标180个点（减少62%）
                    '60d': 150     // 60天：目标150个点（减少67%）
                };

                // 根据时间范围进行数据采样 - 优化采样密度
                let targetPoints = SAMPLING_TARGETS[range] || SAMPLING_TARGETS.default;
                let step = Math.max(1, Math.ceil(timestamps.length / targetPoints));
                
                // 应用采样并提取数据
                let sampledIndices = [];
                for (let i = 0; i < timestamps.length; i += step) {
                    sampledIndices.push(i);
                }
                
                // 限制最大数据点数为300（减少40%）
                if (sampledIndices.length > 300) {
                    sampledIndices = sampledIndices.slice(-300);
                }
                
                // 提取采样后的数据
                cpuData = sampledIndices.map(i => Number((cpuValues[i] || 0).toFixed(1)));
                memData = sampledIndices.map(i => Number((memValues[i] || 0).toFixed(1)));
                swapData = sampledIndices.map(i => Number((swapValues[i] || 0).toFixed(1)));
                
                // 生成智能时间标签
                const selectedTimestamps = sampledIndices.map(i => timestamps[i]);
                labels = ChartLabels.generateSmartLabels(selectedTimestamps, range, 'load-echarts-chart');

                // 保存时间戳供tooltip使用
                window.loadTimestamps = sampledIndices.map(i => timestamps[i]);
            }

            // 确保数据点数量不超过最大值
            if (cpuData.length > maxPoints) cpuData = cpuData.slice(-maxPoints);
            if (memData.length > maxPoints) memData = memData.slice(-maxPoints);
            if (swapData.length > maxPoints) swapData = swapData.slice(-maxPoints);
            if (labels.length > maxPoints) labels = labels.slice(-maxPoints);

            let title = '实时负载';
            if (range === '1h') title = '1小时负载';
            if (range === '24h') title = '24小时负载';
            if (range === '7d') title = '7天负载';
            if (range === '30d') title = '30天负载';
            if (range === '90d') title = '90天负载';

            updateChart(title);
        } catch (error) {
            console.error('加载负载历史数据失败:', error);
        }
    }

    // 按钮事件
    function setupBtns() {
        const activeDayClasses = ['bg-blue-50', 'text-blue-600', 'border-blue-200'];
        // 暗黑模式的激活类依赖HTML中的dark:前缀，如 dark:bg-slate-700 dark:text-white dark:border-slate-700
        // JS主要负责切换日间模式的类，以及通用的非激活类

        const inactiveDayClasses = ['bg-gray-50', 'text-gray-600', 'border-gray-200'];
        // 暗黑模式的非激活类依赖HTML中的dark:前缀，如 dark:bg-slate-800 dark:text-slate-300 dark:border-slate-700

        // 通用的激活/非激活暗黑模式类，如果需要JS更细致地控制
        const activeDarkClasses = ['dark:bg-slate-700', 'dark:text-white', 'dark:border-slate-700'];
        const inactiveDarkClasses = ['dark:bg-slate-800', 'dark:text-slate-300', 'dark:border-slate-700'];


        document.querySelectorAll('.load-range-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const Sthis = this; // Store this for use in promises and callbacks

                // 移除所有按钮的激活状态和加载指示器
                document.querySelectorAll('.load-range-btn').forEach(b => {
                    b.classList.remove(...activeDayClasses);
                    b.classList.add(...inactiveDayClasses);
                    
                    // 确保暗黑模式的非激活类也被正确应用（如果之前是激活状态）
                    // 通常HTML中的dark:前缀会自动处理，但为保险起见可以显式移除激活的dark类并添加非激活的dark类
                    b.classList.remove(...activeDarkClasses); // 移除暗黑激活
                    b.classList.add(...inactiveDarkClasses);   // 添加暗黑非激活 (通常已存在)


                    const indicator = b.querySelector('.loading-indicator');
                    if (indicator) indicator.remove();
                    b.classList.remove('loading');
                });

                // 为当前点击的按钮设置激活状态
                Sthis.classList.remove(...inactiveDayClasses);
                Sthis.classList.add(...activeDayClasses);

                // 确保暗黑模式的激活类也被正确应用
                Sthis.classList.remove(...inactiveDarkClasses); // 移除暗黑非激活
                Sthis.classList.add(...activeDarkClasses);    // 添加暗黑激活


                // 添加加载状态反馈
                Sthis.classList.add('loading');
                const loadIcon = document.createElement('span');
                loadIcon.className = 'loading-indicator mr-1 animate-spin';
                loadIcon.innerHTML = '&#9696;';
                Sthis.prepend(loadIcon);

                const range = Sthis.dataset.range;
                currentRange = range;

                const removeLoadingState = () => {
                    Sthis.classList.remove('loading');
                    const indicator = Sthis.querySelector('.loading-indicator');
                    if (indicator) indicator.remove();
                };

                if (range === '10m') {
                    unsubscribeRealtime();
                    loadHistory('10m')
                        .then(() => {
                            subscribeRealtime();
                            removeLoadingState();
                        })
                        .catch(() => {
                            removeLoadingState();
                        });
                } else {
                    unsubscribeRealtime();
                    loadHistory(range)
                        .then(() => {
                            removeLoadingState();
                        })
                        .catch(() => {
                            removeLoadingState();
                        });
                }
            });
        });
    }

    // 监听主题切换
    function setupThemeListener() {
        // 1. 监听 class 变化
        const observer = new MutationObserver(() => {
            const theme = getTheme();
            if (theme !== currentTheme) {
                getChart(true); // 强制重建
                updateChart(currentRange==='10m'?'实时负载':(currentRange==='1h'?'1小时负载':'24小时负载'));
            }
        });
        observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
        observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });
        // 2. 监听自定义主题切换事件（如有）
        window.addEventListener('themechange', function() {
            const theme = getTheme();
            if (theme !== currentTheme) {
                getChart(true);
                updateChart(currentRange==='10m'?'实时负载':(currentRange==='1h'?'1小时负载':'24小时负载'));
            }
        });
    }

    function setupResize() {
        window.addEventListener('resize', () => {
            const c = getChart();
            if (c) c.resize();
            // 屏幕宽度变化时，自动裁剪数据点数量
            const maxPoints = getMaxPoints();
            if (cpuData.length > maxPoints) cpuData = cpuData.slice(-maxPoints);
            if (memData.length > maxPoints) memData = memData.slice(-maxPoints);
            if (swapData.length > maxPoints) swapData = swapData.slice(-maxPoints);
            if (labels.length > maxPoints) labels = labels.slice(-maxPoints);
            updateChart(currentRange==='10m'?'实时负载':(currentRange==='1h'?'1小时负载':'24小时负载'));
        });
        // 监听容器自身大小变化（更精细，兼容flex/grid等布局）
        const chartDom = document.getElementById('load-echarts-chart');
        if (window.ResizeObserver && chartDom) {
            const ro = new ResizeObserver(() => {
                const c = getChart();
                if (c) c.resize();
            });
            ro.observe(chartDom);
        }
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        setupBtns();
        setupThemeListener();
        setupResize();
        loadHistory('10m').then(() => {
            subscribeRealtime();
        });
        
        // 注册图表到主题管理器
        if (window.UnifiedChartThemeManager && chart) {
            window.UnifiedChartThemeManager.register('load-chart', chart, 'percentage');
        }
    });
})();
