/**
 * 网络质量监控统一管理模块
 * 整合实时监控(network-quality-echarts.js)和历史分析(archive-analysis.js)功能
 */

// 确保ECharts已加载
console.log('检查ECharts是否已加载...');
if (typeof echarts === 'undefined') {
    console.error('ECharts未加载，网络质量监控功能将不可用');
    // 尝试从window对象获取echarts
    if (window.echarts) {
        console.log('从window对象获取echarts成功');
        var echarts = window.echarts;
    } else {
        console.error('无法从window对象获取echarts');
        // 尝试动态加载ECharts
        console.log('尝试动态加载ECharts...');
        const script = document.createElement('script');
        script.src = '/js/libs/echarts.min.js';
        script.onload = function() {
            console.log('ECharts动态加载成功');
            if (window.echarts) {
                var echarts = window.echarts;
                // 重新初始化NetworkQualityManager
                if (window.NetworkQualityManager) {
                    console.log('重新初始化NetworkQualityManager');
                    window.NetworkQualityManager.init();
                }
            }
        };
        script.onerror = function() {
            console.error('ECharts动态加载失败');
        };
        document.head.appendChild(script);
    }
} else {
    console.log('ECharts已加载');
}

// 网络质量监控统一管理器
const NetworkQualityManager = {
    // 当前模式: 'realtime' 或 'archive'
    currentMode: 'realtime',

    // 图表实例
    chart: null,

    // 当前节点ID
    currentNodeId: null,

    // 数据存储
    data: {
        // 实时数据
        realtime: {
            minute: { latency: [], labels: [], minLatency: [], maxLatency: [] },
            hour: { latency: [], labels: [], minLatency: [], maxLatency: [] },
            day: { latency: [], labels: [], minLatency: [], maxLatency: [] },
            month: { latency: [], labels: [], minLatency: [], maxLatency: [] }
        },
        // 归档数据
        archive: {
            data: [],
            startTime: null,
            endTime: null,
            targetId: 'all'
        }
    },

    // 当前时间范围
    currentTimeRange: 'hour',

    // 图表容器ID
    chartContainerId: 'network-quality-chart',

    // 初始化
    init() {
        console.log('初始化网络质量监控统一管理器...');

        // 获取当前节点ID
        this.currentNodeId = this._getCurrentNodeId();

        if (!this.currentNodeId) {
            console.warn('无法获取当前节点ID，将使用默认行为');
        }

        // 设置节点信息
        this._setNodeInfo();

        // 加载监控目标
        this._loadTargets();

        // 注册事件监听器
        this._registerEventListeners();

        // 初始加载数据
        this._loadData();

        console.log('网络质量监控统一管理器初始化完成');
    },

    // 获取当前节点ID
    _getCurrentNodeId() {
        // 尝试从URL路径获取节点ID
        const statsMatch = location.pathname.match(/\/stats\/([^\/]+)/);
        if (statsMatch && statsMatch[1]) {
            console.log('从URL路径获取到节点ID:', statsMatch[1]);
            return statsMatch[1];
        }

        // 尝试从页面元素获取
        const nodeDataElement = document.getElementById('node-data');
        if (nodeDataElement) {
            try {
                const nodeData = JSON.parse(nodeDataElement.value);
                if (nodeData && nodeData.sid) {
                    console.log('从node-data元素获取到节点ID:', nodeData.sid);
                    return nodeData.sid;
                }
            } catch (error) {
                console.error('解析node-data元素失败:', error);
            }
        }

        console.warn('无法获取当前节点ID');
        return null;
    },

    // 设置节点信息
    _setNodeInfo() {
        if (!this.currentNodeId) return;

        const nodeNameElement = document.getElementById('current-node-name');
        if (nodeNameElement) {
            // 尝试从页面元素获取节点名称
            const nodeDataElement = document.getElementById('node-data');
            if (nodeDataElement) {
                try {
                    const nodeData = JSON.parse(nodeDataElement.value);
                    if (nodeData && nodeData.name) {
                        nodeNameElement.textContent = nodeData.name;
                    } else {
                        nodeNameElement.textContent = this.currentNodeId;
                    }
                } catch (error) {
                    console.error('解析node-data元素失败:', error);
                    nodeNameElement.textContent = this.currentNodeId;
                }
            } else {
                nodeNameElement.textContent = this.currentNodeId;
            }
        }
    },

    // 加载监控目标
    _loadTargets() {
        fetch('/api/monitor/targets')
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    this._renderTargetTags(result.data);
                } else {
                    console.error('获取监控目标失败:', result.message);
                }
            })
            .catch(error => {
                console.error('获取监控目标出错:', error);
            });
    },

    // 渲染目标标签
    _renderTargetTags(targets) {
        const tagsContainer = document.getElementById('network-quality-tags');
        if (!tagsContainer) return;

        // 清空容器
        tagsContainer.innerHTML = '';

        // 添加"全部"标签
        const allTag = document.createElement('button');
        allTag.className = 'target-tag py-1 px-3 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200 active';
        allTag.dataset.targetId = 'all';
        allTag.textContent = '全部';
        tagsContainer.appendChild(allTag);

        // 添加目标标签
        targets.forEach(target => {
            const tag = document.createElement('button');
            tag.className = 'target-tag py-1 px-3 text-xs font-medium rounded-full bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-200';
            tag.dataset.targetId = target.id;
            tag.textContent = target.name;
            tagsContainer.appendChild(tag);
        });

        // 添加标签点击事件
        document.querySelectorAll('.target-tag').forEach(tag => {
            tag.addEventListener('click', (event) => {
                const targetId = event.target.dataset.targetId;

                // 移除所有标签的active类
                document.querySelectorAll('.target-tag').forEach(t => {
                    t.classList.remove('active');
                    t.classList.remove('bg-indigo-100', 'text-indigo-800', 'dark:bg-indigo-900', 'dark:text-indigo-200');
                    t.classList.add('bg-slate-100', 'text-slate-800', 'dark:bg-slate-800', 'dark:text-slate-200');
                });

                // 添加当前标签的active类
                tag.classList.add('active');
                tag.classList.add('bg-indigo-100', 'text-indigo-800', 'dark:bg-indigo-900', 'dark:text-indigo-200');
                tag.classList.remove('bg-slate-100', 'text-slate-800', 'dark:bg-slate-800', 'dark:text-slate-200');

                // 根据当前模式加载数据
                if (this.currentMode === 'realtime') {
                    this.data.realtime.selectedTarget = targetId;
                    this._loadRealtimeData();
                } else {
                    this.data.archive.targetId = targetId;
                    this._loadArchiveData();
                }
            });
        });
    },

    // 注册事件监听器
    _registerEventListeners() {
        // 窗口大小改变时重新渲染图表
        window.addEventListener('resize', () => {
            if (this.chart) {
                this.chart.resize();
            }
        });

        // 刷新按钮点击事件
        const refreshButton = document.getElementById('refresh-network-quality');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this._loadData();
            });
        }

        // 模式切换按钮点击事件
        document.querySelectorAll('.mode-switch-btn').forEach(btn => {
            btn.addEventListener('click', (event) => {
                const mode = event.target.closest('button').dataset.mode;
                this._switchMode(mode);
            });
        });

        // 时间范围标签页切换事件
        document.querySelectorAll('.network-quality-tab').forEach(tab => {
            tab.addEventListener('click', (event) => {
                const timeRange = event.target.closest('button').dataset.tab;
                if (!timeRange) return;

                this._switchTimeRange(timeRange);
            });
        });

        // 时间范围选择器事件（归档模式）
        const timeRangeSelector = document.getElementById('archive-time-range');
        if (timeRangeSelector) {
            timeRangeSelector.addEventListener('change', () => {
                const range = timeRangeSelector.value;
                this._setArchiveTimeRange(range);
            });
        }
    },

    // 加载数据
    _loadData() {
        // 显示加载指示器
        this._showLoadingIndicator(true);

        if (this.currentMode === 'realtime') {
            this._loadRealtimeData();
        } else {
            this._loadArchiveData();
        }
    },

    // 显示/隐藏加载指示器
    _showLoadingIndicator(show) {
        const loadingIndicator = document.getElementById('network-quality-loading');
        if (loadingIndicator) {
            if (show) {
                loadingIndicator.classList.remove('hidden');
            } else {
                loadingIndicator.classList.add('hidden');
            }
        }
    },

    // 加载实时数据
    _loadRealtimeData() {
        console.log('加载实时监控数据...');

        // 获取当前选中的目标ID
        const selectedTarget = this.data.realtime.selectedTarget || 'all';

        // 使用Promise.all并行加载所有时间范围的数据
        Promise.all([
            this._loadRealtimeDataForTimeRange('minute', selectedTarget),
            this._loadRealtimeDataForTimeRange('hour', selectedTarget),
            this._loadRealtimeDataForTimeRange('day', selectedTarget),
            this._loadRealtimeDataForTimeRange('month', selectedTarget)
        ])
        .then(() => {
            console.log('所有时间范围的实时数据加载完成');

            // 渲染图表
            this._renderChart();
        })
        .catch(error => {
            console.error('加载实时数据时出错:', error);
        })
        .finally(() => {
            // 隐藏加载指示器
            this._showLoadingIndicator(false);
        });
    },

    // 加载指定时间范围的实时数据
    _loadRealtimeDataForTimeRange(timeRange, targetId) {
        return new Promise((resolve) => {
            try {
                // 构建API请求参数
                const params = new URLSearchParams();

                // 确定API类型参数
                let typeParam;
                switch (timeRange) {
                    case 'minute': typeParam = 'm'; break;
                    case 'hour': typeParam = 'h'; break;
                    case 'day': typeParam = 'd'; break;
                    case 'month': typeParam = 'month'; break;
                    default: typeParam = 'm';
                }
                params.append('type', typeParam);

                // 对于小时和天视图，请求详细数据
                if (timeRange === 'hour' || timeRange === 'day') {
                    params.append('detail_level', 'detailed');
                }

                // 添加目标ID参数
                if (targetId !== 'all') {
                    params.append('target_id', targetId);
                }

                // 添加节点ID参数
                if (this.currentNodeId) {
                    params.append('node_id', this.currentNodeId);
                }

                console.log(`${timeRange}请求参数:`, params.toString());

                // 发送请求
                fetch(`/api/monitor/data?${params.toString()}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP错误: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(result => {
                        if (result.success) {
                            console.log(`获取到${timeRange}数据: ${result.data ? result.data.length : 0}条记录`);

                            // 检查数据格式
                            if (!result.data || !Array.isArray(result.data)) {
                                console.error(`${timeRange}数据格式无效:`, result.data);
                                // 设置默认数据
                                this._setDefaultRealtimeData(timeRange);
                                resolve(); // 仍然解析Promise，避免阻塞其他时间范围的数据加载
                                return;
                            }

                            // 检查数据是否为空
                            if (result.data.length === 0) {
                                console.warn(`${timeRange}数据为空`);
                                // 设置默认数据
                                this._setDefaultRealtimeData(timeRange);
                                resolve(); // 仍然解析Promise，避免阻塞其他时间范围的数据加载
                                return;
                            }

                            // 处理数据
                            this._processRealtimeData(timeRange, result.data);
                            resolve();
                        } else {
                            console.error(`获取${timeRange}数据失败:`, result.message);
                            // 设置默认数据
                            this._setDefaultRealtimeData(timeRange);
                            resolve(); // 仍然解析Promise，避免阻塞其他时间范围的数据加载
                        }
                    })
                    .catch(error => {
                        console.error(`获取${timeRange}数据出错:`, error);
                        // 设置默认数据
                        this._setDefaultRealtimeData(timeRange);
                        resolve(); // 仍然解析Promise，避免阻塞其他时间范围的数据加载
                    });
            } catch (error) {
                console.error(`准备${timeRange}数据请求时出错:`, error);
                // 设置默认数据
                this._setDefaultRealtimeData(timeRange);
                resolve(); // 仍然解析Promise，避免阻塞其他时间范围的数据加载
            }
        });
    },

    // 处理实时数据
    _processRealtimeData(timeRange, data) {
        // 记录原始数据，用于调试
        console.log(`处理${timeRange}数据，原始数据长度:`, data ? data.length : 0);

        // 如果数据为空或无效，使用默认数据
        if (!data || !Array.isArray(data) || data.length === 0) {
            console.warn(`${timeRange}数据为空或无效，使用默认数据`);
            this._setDefaultRealtimeData(timeRange);
            return;
        }

        // 记录第一条数据的结构，用于调试
        if (data.length > 0) {
            console.log(`${timeRange}数据第一条记录结构:`, JSON.stringify(data[0], null, 2));
        }

        // 筛选当前节点的数据
        let filteredData = data;
        if (this.currentNodeId) {
            filteredData = data.filter(item => {
                return item.sid === this.currentNodeId ||
                       item.node_id === this.currentNodeId ||
                       item.id === this.currentNodeId ||
                       item.server_id === this.currentNodeId;
            });

            // 如果没有找到当前节点的数据，使用所有数据
            if (filteredData.length === 0) {
                console.warn(`未找到节点ID为${this.currentNodeId}的${timeRange}数据，使用所有数据`);
                filteredData = data;
            }
        }

        console.log(`${timeRange}筛选后数据长度:`, filteredData.length);

        // 提取延迟数据和标签
        const latency = [];
        const labels = [];
        const minLatency = [];
        const maxLatency = [];

        filteredData.forEach(item => {
            // 检查数据有效性
            if (typeof item.avg_time === 'undefined' || item.avg_time === null) {
                console.warn('数据项缺少avg_time字段:', item);
                return; // 跳过无效数据
            }

            if (typeof item.created_at === 'undefined' || item.created_at === null) {
                console.warn('数据项缺少created_at字段:', item);
                return; // 跳过无效数据
            }

            // 延迟数据取整
            latency.push(Math.round(item.avg_time));

            // 最小和最大延迟数据
            if (typeof item.min_time !== 'undefined' && item.min_time !== null) {
                minLatency.push(Math.round(item.min_time));
            } else {
                minLatency.push(Math.round(item.avg_time * 0.8)); // 如果没有最小值，模拟一个
            }

            if (typeof item.max_time !== 'undefined' && item.max_time !== null) {
                maxLatency.push(Math.round(item.max_time));
            } else {
                maxLatency.push(Math.round(item.avg_time * 1.2)); // 如果没有最大值，模拟一个
            }

            // 格式化时间标签
            const date = new Date(item.created_at * 1000);
            let label;

            switch (timeRange) {
                case 'minute':
                    label = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                    break;
                case 'hour':
                    label = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                    break;
                case 'day':
                    label = date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit' });
                    break;
                case 'month':
                    label = date.toLocaleDateString([], { month: 'short', day: 'numeric' });
                    break;
                default:
                    label = date.toLocaleTimeString();
            }

            labels.push(label);
        });

        // 如果没有有效数据，使用默认数据
        if (latency.length === 0) {
            console.warn(`${timeRange}没有有效数据，使用默认数据`);
            this._setDefaultRealtimeData(timeRange);
            return;
        }

        // 更新数据
        this.data.realtime[timeRange].latency = latency;
        this.data.realtime[timeRange].labels = labels;
        this.data.realtime[timeRange].minLatency = minLatency;
        this.data.realtime[timeRange].maxLatency = maxLatency;

        console.log(`${timeRange}数据处理完成，共${latency.length}条记录`);
    },

    // 设置默认实时数据
    _setDefaultRealtimeData(timeRange) {
        this.data.realtime[timeRange].latency = [];
        this.data.realtime[timeRange].labels = [];
        this.data.realtime[timeRange].minLatency = [];
        this.data.realtime[timeRange].maxLatency = [];
    },

    // 加载归档数据
    _loadArchiveData() {
        console.log('加载归档数据...');

        // 获取时间范围和目标ID
        const { startTime, endTime, targetId } = this.data.archive;

        if (!startTime || !endTime) {
            console.error('未设置归档数据时间范围');
            this._showLoadingIndicator(false);
            return;
        }

        // 构建API请求参数
        const params = new URLSearchParams();
        params.append('type', 'archive');
        params.append('start_time', startTime);
        params.append('end_time', endTime);

        if (this.currentNodeId) {
            params.append('node_id', this.currentNodeId);
        }

        if (targetId !== 'all') {
            params.append('target_id', targetId);
        }

        // 发送请求
        fetch(`/api/monitor/data?${params.toString()}`)
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    console.log(`获取到归档数据: ${result.data.length}条记录`);

                    // 处理数据
                    this._processArchiveData(result.data);

                    // 渲染图表
                    this._renderChart();

                    // 更新统计数据
                    if (result.stats) {
                        this._updateArchiveStats(result.stats);
                    }
                } else {
                    console.error('获取归档数据失败:', result.message);
                }
            })
            .catch(error => {
                console.error('获取归档数据出错:', error);
            })
            .finally(() => {
                // 隐藏加载指示器
                this._showLoadingIndicator(false);
            });
    },

    // 处理归档数据
    _processArchiveData(data) {
        // 记录原始数据，用于调试
        console.log(`处理归档数据，原始数据长度:`, data ? data.length : 0);

        // 如果数据为空或无效，使用默认数据
        if (!data || !Array.isArray(data) || data.length === 0) {
            console.warn(`归档数据为空或无效，使用默认数据`);
            this.data.archive.data = [];
            return;
        }

        // 记录第一条数据的结构，用于调试
        if (data.length > 0) {
            console.log(`归档数据第一条记录结构:`, JSON.stringify(data[0], null, 2));
        }

        try {
            // 转换数据
            this.data.archive.data = data.map(item => {
                // 检查数据有效性
                if (typeof item.avg_time === 'undefined' || item.avg_time === null) {
                    console.warn('归档数据项缺少avg_time字段:', item);
                    return null; // 返回null，后面会过滤掉
                }

                if (typeof item.created_at === 'undefined' || item.created_at === null) {
                    console.warn('归档数据项缺少created_at字段:', item);
                    return null; // 返回null，后面会过滤掉
                }

                return {
                    value: [
                        new Date(item.created_at * 1000),
                        Math.round(item.avg_time)
                    ]
                };
            }).filter(item => item !== null); // 过滤掉无效数据

            console.log(`归档数据处理完成，共${this.data.archive.data.length}条记录`);
        } catch (error) {
            console.error('处理归档数据时出错:', error);
            this.data.archive.data = [];
        }
    },

    // 更新归档统计数据
    _updateArchiveStats(stats) {
        const statsContainer = document.getElementById('archive-stats');
        if (!statsContainer) return;

        // 格式化统计数据
        const avgLatency = Math.round(stats.avg_latency || 0);
        const minLatency = Math.round(stats.min_latency || 0);
        const maxLatency = Math.round(stats.max_latency || 0);
        const avgSuccessRate = Math.round((stats.avg_success_rate || 0) * 100) / 100;
        const recordCount = stats.count || 0;

        // 更新统计数据显示
        statsContainer.innerHTML = `
            <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div class="stat-item">
                    <div class="text-sm font-medium text-slate-500 dark:text-slate-400">平均延迟</div>
                    <div class="text-lg font-semibold text-slate-800 dark:text-white">${avgLatency} ms</div>
                </div>
                <div class="stat-item">
                    <div class="text-sm font-medium text-slate-500 dark:text-slate-400">最小延迟</div>
                    <div class="text-lg font-semibold text-slate-800 dark:text-white">${minLatency} ms</div>
                </div>
                <div class="stat-item">
                    <div class="text-sm font-medium text-slate-500 dark:text-slate-400">最大延迟</div>
                    <div class="text-lg font-semibold text-slate-800 dark:text-white">${maxLatency} ms</div>
                </div>
                <div class="stat-item">
                    <div class="text-sm font-medium text-slate-500 dark:text-slate-400">平均成功率</div>
                    <div class="text-lg font-semibold text-slate-800 dark:text-white">${avgSuccessRate}%</div>
                </div>
                <div class="stat-item">
                    <div class="text-sm font-medium text-slate-500 dark:text-slate-400">记录数量</div>
                    <div class="text-lg font-semibold text-slate-800 dark:text-white">${recordCount}</div>
                </div>
            </div>
        `;
    },

    // 设置归档时间范围
    _setArchiveTimeRange(range) {
        const now = Math.floor(Date.now() / 1000);
        let startTime, endTime;

        switch (range) {
            case '1h':
                startTime = now - 3600;
                endTime = now;
                break;
            case '6h':
                startTime = now - 6 * 3600;
                endTime = now;
                break;
            case '12h':
                startTime = now - 12 * 3600;
                endTime = now;
                break;
            case '1d':
                startTime = now - 24 * 3600;
                endTime = now;
                break;
            case '3d':
                startTime = now - 3 * 24 * 3600;
                endTime = now;
                break;
            case '7d':
                startTime = now - 7 * 24 * 3600;
                endTime = now;
                break;
            case '14d':
                startTime = now - 14 * 24 * 3600;
                endTime = now;
                break;
            case '30d':
                startTime = now - 30 * 24 * 3600;
                endTime = now;
                break;
            default:
                startTime = now - 24 * 3600;
                endTime = now;
        }

        this.data.archive.startTime = startTime;
        this.data.archive.endTime = endTime;

        // 加载归档数据
        this._loadArchiveData();
    },

    // 切换模式
    _switchMode(mode) {
        if (mode === this.currentMode) return;

        console.log(`切换模式: ${this.currentMode} -> ${mode}`);
        this.currentMode = mode;

        // 更新UI
        document.querySelectorAll('.mode-switch-btn').forEach(btn => {
            const btnMode = btn.dataset.mode;
            if (btnMode === mode) {
                btn.classList.add('active');
                btn.classList.add('bg-indigo-100', 'text-indigo-800', 'dark:bg-indigo-900', 'dark:text-indigo-200');
                btn.classList.remove('bg-slate-100', 'text-slate-800', 'dark:bg-slate-800', 'dark:text-slate-200');
            } else {
                btn.classList.remove('active');
                btn.classList.remove('bg-indigo-100', 'text-indigo-800', 'dark:bg-indigo-900', 'dark:text-indigo-200');
                btn.classList.add('bg-slate-100', 'text-slate-800', 'dark:bg-slate-800', 'dark:text-slate-200');
            }
        });

        // 根据模式调整时间范围选择器的行为
        const timeRangeSelectors = document.querySelectorAll('.time-range-selector');
        if (mode === 'realtime') {
            // 实时模式下使用4h、24h、7d、31d时间范围
            console.log('切换到实时监控模式');
            timeRangeSelectors.forEach(selector => {
                selector.style.display = '';
            });
        } else {
            // 历史分析模式下可能需要不同的时间范围选择器行为
            console.log('切换到历史分析模式');
            // 这里可以根据需要进行调整
        }

        // 加载数据
        this._loadData();
    },

    // 切换时间范围
    _switchTimeRange(timeRange) {
        this.currentTimeRange = timeRange;

        // 更新UI
        document.querySelectorAll('.network-quality-tab').forEach(tab => {
            const tabTimeRange = tab.dataset.tab;
            if (tabTimeRange === timeRange) {
                tab.classList.add('active');
                tab.classList.add('border-indigo-600', 'dark:border-indigo-400', 'text-indigo-600', 'dark:text-indigo-400');
                tab.classList.remove('border-transparent', 'text-slate-600', 'dark:text-slate-300');
            } else {
                tab.classList.remove('active');
                tab.classList.remove('border-indigo-600', 'dark:border-indigo-400', 'text-indigo-600', 'dark:text-indigo-400');
                tab.classList.add('border-transparent', 'text-slate-600', 'dark:text-slate-300');
            }
        });

        // 渲染图表
        this._renderChart();
    },

    // 渲染图表
    _renderChart() {
        console.log(`开始渲染${this.currentMode}模式下的${this.currentTimeRange}图表...`);

        try {
            // 检查ECharts是否已加载
            if (typeof echarts === 'undefined' && typeof window.echarts === 'undefined') {
                console.error('ECharts未加载，无法渲染图表');
                // 显示错误信息
                const errorContainer = document.getElementById(this.chartContainerId);
                if (errorContainer) {
                    errorContainer.innerHTML = '<div class="flex items-center justify-center h-full"><div class="text-red-500">ECharts未加载，无法渲染图表</div></div>';
                }
                return;
            }

            // 使用window.echarts如果echarts未定义
            if (typeof echarts === 'undefined' && typeof window.echarts !== 'undefined') {
                console.log('使用window.echarts');
                var echarts = window.echarts;
            }

            // 获取图表容器
            const container = document.getElementById(this.chartContainerId);
            if (!container) {
                console.error(`找不到图表容器: ${this.chartContainerId}`);
                // 尝试创建容器
                console.log('尝试创建图表容器...');
                const parentContainer = document.querySelector('.network-quality-chart-container') || document.querySelector('.card');
                if (parentContainer) {
                    const newContainer = document.createElement('div');
                    newContainer.id = this.chartContainerId;
                    newContainer.className = 'w-full h-[400px] relative overflow-visible';
                    parentContainer.appendChild(newContainer);
                    console.log('创建图表容器成功');
                    // 重新获取容器
                    const container = document.getElementById(this.chartContainerId);
                    if (!container) {
                        console.error('创建图表容器后仍然找不到容器');
                        return;
                    }
                } else {
                    console.error('找不到父容器，无法创建图表容器');
                    return;
                }
            }

            // 获取当前主题
            const isDarkMode = document.documentElement.classList.contains('dark');
            const textColor = isDarkMode ? '#f1f5f9' : '#1e293b';
            const gridLineColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

            // 根据当前模式渲染不同的图表
            if (this.currentMode === 'realtime') {
                this._renderRealtimeChart(container, textColor, gridLineColor);
            } else {
                this._renderArchiveChart(container, textColor, gridLineColor);
            }
        } catch (error) {
            console.error('渲染图表时出错:', error);
            // 显示错误信息
            const errorContainer = document.getElementById(this.chartContainerId);
            if (errorContainer) {
                errorContainer.innerHTML = `<div class="flex items-center justify-center h-full"><div class="text-red-500">渲染图表时出错: ${error.message}</div></div>`;
            }
        }
    },

    // 渲染实时图表
    _renderRealtimeChart(container, textColor, gridLineColor) {
        try {
            console.log('开始渲染实时图表...');
            console.log('图表容器:', container);
            console.log('容器尺寸:', container.offsetWidth, 'x', container.offsetHeight);

            // 确保容器可见
            container.style.display = 'block';
            container.style.height = '400px';
            container.style.width = '100%';

            // 获取当前时间范围的数据
            const { latency, labels, minLatency, maxLatency } = this.data.realtime[this.currentTimeRange];
            console.log(`当前时间范围: ${this.currentTimeRange}, 数据长度: ${latency.length}`);
            console.log('延迟数据:', latency);
            console.log('标签数据:', labels);
            console.log('最小延迟数据:', minLatency);
            console.log('最大延迟数据:', maxLatency);

            // 如果没有数据，显示无数据提示
            if (latency.length === 0) {
                console.warn('没有数据，显示无数据提示');
                container.innerHTML = '<div class="flex items-center justify-center h-full"><div class="text-slate-500 dark:text-slate-400">暂无数据</div></div>';
                return;
            }

            // 配置图表选项
            const option = {
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        const avgParam = params[0];
                        const minParam = params[1] || { value: 'N/A' };
                        const maxParam = params[2] || { value: 'N/A' };

                        return `
                            <div style="font-weight: bold; margin-bottom: 4px;">时间: ${avgParam.name}</div>
                            <div>平均延迟: ${avgParam.value} ms</div>
                            <div>最小延迟: ${minParam.value} ms</div>
                            <div>最大延迟: ${maxParam.value} ms</div>
                        `;
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '8%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: labels,
                    axisLabel: {
                        color: textColor,
                        showMaxLabel: true
                    },
                    axisLine: {
                        lineStyle: {
                            color: gridLineColor
                        }
                    },
                    axisTick: {
                        alignWithLabel: true,
                        lineStyle: {
                            color: gridLineColor
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '延迟',
                    nameTextStyle: {
                        color: textColor
                    },
                    axisLabel: {
                        color: textColor,
                        formatter: '{value} ms'
                    },
                    splitLine: {
                        lineStyle: {
                            color: gridLineColor,
                            type: 'dashed'
                        }
                    }
                },
                series: [
                    {
                        name: '平均延迟',
                        type: 'line',
                        sampling: 'lttb', // 使用LTTB采样算法，提高大数据量渲染性能
                        symbol: latency.length < 60 ? 'circle' : 'none', // 数据点少时显示标记
                        symbolSize: latency.length < 60 ? 4 : 0,
                        lineStyle: {
                            width: 2, // 使用更细的线宽，适合显示更多数据点
                            color: '#6366f1'
                        },
                        itemStyle: {
                            color: '#6366f1'
                        },
                        emphasis: {
                            itemStyle: {
                                color: '#4f46e5',
                                borderColor: '#6366f1',
                                borderWidth: 2
                            }
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: 'rgba(99, 102, 241, 0.5)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(99, 102, 241, 0.05)'
                                }
                            ])
                        },
                        data: latency,
                        smooth: true, // 使用平滑曲线
                        animation: false, // 禁用动画，提高性能
                        z: 3 // 确保主线在最上层
                    },
                    {
                        name: '最小延迟',
                        type: 'line',
                        symbol: 'none',
                        lineStyle: {
                            width: 1,
                            color: '#10b981', // 绿色
                            type: 'dashed'
                        },
                        itemStyle: {
                            color: '#10b981'
                        },
                        data: minLatency,
                        smooth: true,
                        z: 2
                    },
                    {
                        name: '最大延迟',
                        type: 'line',
                        symbol: 'none',
                        lineStyle: {
                            width: 1,
                            color: '#f59e0b', // 橙色
                            type: 'dashed'
                        },
                        itemStyle: {
                            color: '#f59e0b'
                        },
                        data: maxLatency,
                        smooth: true,
                        z: 1
                    }
                ],
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100
                    },
                    {
                        type: 'slider',
                        show: latency.length > 60, // 数据点多时显示缩放控件
                        start: 0,
                        end: 100
                    }
                ]
            };

            // 初始化或更新图表
            if (!this.chart) {
                console.log('创建新的图表实例');
                try {
                    // 强制重新计算容器尺寸
                    container.style.width = '100%';
                    container.style.height = '400px';

                    // 等待浏览器重新计算尺寸
                    setTimeout(() => {
                        console.log('延迟初始化图表，容器尺寸:', container.offsetWidth, 'x', container.offsetHeight);

                        // 如果容器尺寸为0，尝试设置固定尺寸
                        if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                            console.warn('容器尺寸为0，设置固定尺寸');
                            container.style.width = '800px';
                            container.style.height = '400px';
                        }

                        try {
                            this.chart = echarts.init(container);
                            this.chart.setOption(option, true);
                            console.log(`延迟渲染${this.currentTimeRange}实时图表成功`);
                        } catch (error) {
                            console.error('延迟初始化图表时出错:', error);
                        }
                    }, 100);
                } catch (error) {
                    console.error('初始化图表时出错:', error);
                }
            } else {
                console.log('更新现有图表实例');
                // 设置图表选项
                this.chart.setOption(option, true);
            }

            console.log(`渲染${this.currentTimeRange}实时图表成功`);
        } catch (error) {
            console.error(`渲染${this.currentTimeRange}实时图表时出错:`, error);
            container.innerHTML = '<div class="flex items-center justify-center h-full"><div class="text-red-500">图表渲染失败</div></div>';
        }
    },

    // 渲染归档图表
    _renderArchiveChart(container, textColor, gridLineColor) {
        try {
            // 获取归档数据
            const data = this.data.archive.data;

            // 如果没有数据，显示无数据提示
            if (!data || data.length === 0) {
                container.innerHTML = '<div class="flex items-center justify-center h-full"><div class="text-slate-500 dark:text-slate-400">暂无数据</div></div>';
                return;
            }

            // 配置图表选项
            const option = {
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        const param = params[0];
                        const date = new Date(param.value[0]);
                        const formattedDate = date.toLocaleString();
                        return `${formattedDate}<br/>延迟: ${param.value[1]} ms`;
                    },
                    axisPointer: {
                        animation: false
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    top: '8%',
                    containLabel: true
                },
                xAxis: {
                    type: 'time',
                    splitLine: {
                        show: false
                    },
                    axisLabel: {
                        color: textColor
                    },
                    axisLine: {
                        lineStyle: {
                            color: gridLineColor
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '延迟',
                    nameTextStyle: {
                        color: textColor
                    },
                    boundaryGap: [0, '100%'],
                    axisLabel: {
                        color: textColor,
                        formatter: '{value} ms'
                    },
                    splitLine: {
                        lineStyle: {
                            color: gridLineColor,
                            type: 'dashed'
                        }
                    }
                },
                series: [
                    {
                        name: '延迟',
                        type: 'line',
                        showSymbol: false,
                        sampling: 'lttb',
                        lineStyle: {
                            width: 1.5,
                            color: '#6366f1'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: 'rgba(99, 102, 241, 0.5)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(99, 102, 241, 0.05)'
                                }
                            ])
                        },
                        data: data
                    }
                ],
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100
                    },
                    {
                        type: 'slider',
                        show: true,
                        start: 0,
                        end: 100
                    }
                ]
            };

            // 初始化或更新图表
            if (!this.chart) {
                console.log('创建新的归档图表实例');
                try {
                    // 强制重新计算容器尺寸
                    container.style.width = '100%';
                    container.style.height = '400px';

                    // 等待浏览器重新计算尺寸
                    setTimeout(() => {
                        console.log('延迟初始化归档图表，容器尺寸:', container.offsetWidth, 'x', container.offsetHeight);

                        // 如果容器尺寸为0，尝试设置固定尺寸
                        if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                            console.warn('归档图表容器尺寸为0，设置固定尺寸');
                            container.style.width = '800px';
                            container.style.height = '400px';
                        }

                        try {
                            this.chart = echarts.init(container);
                            this.chart.setOption(option, true);
                            console.log('延迟渲染归档图表成功');
                        } catch (error) {
                            console.error('延迟初始化归档图表时出错:', error);
                        }
                    }, 100);
                } catch (error) {
                    console.error('初始化归档图表时出错:', error);
                }
            } else {
                console.log('更新现有归档图表实例');
                // 设置图表选项
                this.chart.setOption(option, true);
            }

            console.log('渲染归档图表成功');
        } catch (error) {
            console.error('渲染归档图表时出错:', error);
            container.innerHTML = '<div class="flex items-center justify-center h-full"><div class="text-red-500">图表渲染失败</div></div>';
        }
    },

    // 导出数据为CSV
    exportCSV() {
        let data;
        let filename;

        if (this.currentMode === 'realtime') {
            // 导出实时数据
            const { latency, labels } = this.data.realtime[this.currentTimeRange];

            if (latency.length === 0) {
                alert('没有数据可导出');
                return;
            }

            // 创建CSV内容
            let csv = '时间,延迟(ms)\n';
            for (let i = 0; i < latency.length; i++) {
                csv += `${labels[i]},${latency[i]}\n`;
            }

            filename = `network-quality-realtime-${this.currentTimeRange}-${new Date().toISOString()}.csv`;
            data = csv;
        } else {
            // 导出归档数据
            const archiveData = this.data.archive.data;

            if (!archiveData || archiveData.length === 0) {
                alert('没有数据可导出');
                return;
            }

            // 创建CSV内容
            let csv = '时间,延迟(ms)\n';
            archiveData.forEach(item => {
                const date = new Date(item.value[0]);
                csv += `${date.toLocaleString()},${item.value[1]}\n`;
            });

            filename = `network-quality-archive-${new Date().toISOString()}.csv`;
            data = csv;
        }

        // 创建下载链接
        const blob = new Blob([data], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
};

// 添加WebSocket支持
NetworkQualityManager.setupWebSocket = function() {
    // 检查是否已经有WebSocket连接
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        console.log('WebSocket连接已存在');
        return;
    }

    // 创建WebSocket连接
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws`;

    try {
        this.ws = new WebSocket(wsUrl);

        // 连接建立时
        this.ws.onopen = () => {
            console.log('WebSocket连接已建立');
            // 发送订阅消息
            this.ws.send(JSON.stringify({
                type: 'subscribe',
                channel: 'network_quality',
                node_id: this.currentNodeId || 'all'
            }));
        };

        // 接收消息时
        this.ws.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);

                // 检查是否处于标签页恢复状态
                const isRecoveringFromSleep = window.TabLifecycleHandler && window.TabLifecycleHandler.isRecoveringFromSleep;
                const shouldBlockDataUpdates = window.TabLifecycleHandler && window.TabLifecycleHandler.blockDataUpdates;

                // 处理网络质量数据更新
                if (message.type === 'network_quality_update' && this.currentMode === 'realtime') {
                    console.log('收到网络质量数据更新');

                    // 使用全局拦截函数检查是否应该跳过更新
                    if (window.TabLifecycleHandler && window.TabLifecycleHandler.shouldBlockUpdate && window.TabLifecycleHandler.shouldBlockUpdate()) {
                        // 如果处于休眠恢复状态，只缓存数据，不触发事件，不更新UI
                        console.log('网络质量数据更新被阻止，标签页正在从休眠中恢复');
                        if (window.TabLifecycleHandler) {
                            window.TabLifecycleHandler.cachedNetworkQualityData = message.data;
                        }
                        return; // 不处理数据，不触发事件
                    }

                    // 正常处理数据
                    this._processWebSocketData(message.data);
                }
            } catch (error) {
                console.error('处理WebSocket消息时出错:', error);
            }
        };

        // 连接关闭时
        this.ws.onclose = () => {
            console.log('WebSocket连接已关闭');
            // 5秒后尝试重新连接
            setTimeout(() => {
                if (document.visibilityState === 'visible') {
                    console.log('尝试重新连接WebSocket...');
                    this.setupWebSocket();
                }
            }, 5000);
        };

        // 连接错误时
        this.ws.onerror = (error) => {
            console.error('WebSocket连接错误:', error);
        };

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // 页面变为可见时，检查WebSocket连接状态
                if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
                    console.log('页面可见，重新连接WebSocket');
                    this.setupWebSocket();
                }
            } else {
                // 页面不可见时，关闭WebSocket连接
                if (this.ws) {
                    console.log('页面不可见，关闭WebSocket连接');
                    this.ws.close();
                }
            }
        });
    } catch (error) {
        console.error('创建WebSocket连接时出错:', error);
    }
};

// 处理WebSocket数据
NetworkQualityManager._processWebSocketData = function(data) {
    // 记录原始数据，用于调试
    console.log(`处理WebSocket数据，原始数据长度:`, data ? data.length : 0);

    // 如果数据为空或无效，直接返回
    if (!data || !Array.isArray(data) || data.length === 0) {
        console.error('无效的WebSocket数据格式');
        return;
    }

    // 记录第一条数据的结构，用于调试
    if (data.length > 0) {
        console.log(`WebSocket数据第一条记录结构:`, JSON.stringify(data[0], null, 2));
    }

    try {
        // 筛选当前节点的数据
        let filteredData = data;
        if (this.currentNodeId) {
            filteredData = data.filter(item => {
                return item.sid === this.currentNodeId ||
                       item.node_id === this.currentNodeId ||
                       item.id === this.currentNodeId ||
                       item.server_id === this.currentNodeId;
            });

            // 如果没有找到当前节点的数据，使用所有数据
            if (filteredData.length === 0) {
                console.warn(`未找到节点ID为${this.currentNodeId}的WebSocket数据，使用所有数据`);
                filteredData = data;
            }
        }

        console.log(`WebSocket筛选后数据长度:`, filteredData.length);

        // 更新实时数据
        const latency = [];
        const labels = [];

        filteredData.forEach(item => {
            // 检查数据有效性
            if (typeof item.avg_time === 'undefined' || item.avg_time === null) {
                console.warn('WebSocket数据项缺少avg_time字段:', item);
                return; // 跳过无效数据
            }

            if (typeof item.created_at === 'undefined' || item.created_at === null) {
                console.warn('WebSocket数据项缺少created_at字段:', item);
                return; // 跳过无效数据
            }

            // 延迟数据取整
            latency.push(Math.round(item.avg_time));

            // 格式化时间标签
            const date = new Date(item.created_at * 1000);
            const label = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            labels.push(label);
        });

        // 如果没有有效数据，直接返回
        if (latency.length === 0) {
            console.warn(`WebSocket数据没有有效数据`);
            return;
        }

        // 更新数据
        this.data.realtime.minute.latency = latency;
        this.data.realtime.minute.labels = labels;

        console.log(`WebSocket数据处理完成，共${latency.length}条记录`);

        // 渲染图表
        if (this.currentMode === 'realtime' && this.currentTimeRange === 'minute') {
            console.log('更新实时图表');
            this._renderChart();
        }
    } catch (error) {
        console.error('处理WebSocket数据时出错:', error);
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('页面加载完成，准备初始化NetworkQualityManager...');

    // 检查是否在stat.html页面
    const isStatPage = location.pathname.match(/\/stats\/([^\/]+)/) || location.pathname === '/stat';
    if (!isStatPage) {
        console.log('不在stat.html页面，跳过初始化');
        return;
    }

    // 检查必要的DOM元素是否存在
    const chartContainer = document.getElementById('network-quality-chart');
    if (!chartContainer) {
        console.warn('找不到图表容器，可能不在正确的页面');
    } else {
        console.log('找到图表容器，尺寸:', chartContainer.offsetWidth, 'x', chartContainer.offsetHeight);
        // 确保容器可见
        chartContainer.style.display = 'block';
        chartContainer.style.height = '400px';
        chartContainer.style.width = '100%';
    }

    // 检查ECharts是否已加载
    if (typeof echarts === 'undefined' && typeof window.echarts === 'undefined') {
        console.error('ECharts未加载，尝试动态加载');
        const script = document.createElement('script');
        script.src = '/js/libs/echarts.min.js';
        script.onload = function() {
            console.log('ECharts动态加载成功，初始化NetworkQualityManager');
            // 延迟初始化，确保DOM和其他依赖已完全加载
            setTimeout(initNetworkQualityManager, 2000);
        };
        script.onerror = function() {
            console.error('ECharts动态加载失败');
        };
        document.head.appendChild(script);
    } else {
        console.log('ECharts已加载，延迟初始化NetworkQualityManager');
        // 延迟初始化，确保DOM和其他依赖已完全加载
        setTimeout(initNetworkQualityManager, 2000);
    }
});

// 监听窗口加载完成事件
window.addEventListener('load', () => {
    console.log('窗口加载完成，检查图表容器...');
    const chartContainer = document.getElementById('network-quality-chart');
    if (chartContainer) {
        console.log('窗口加载完成后，图表容器尺寸:', chartContainer.offsetWidth, 'x', chartContainer.offsetHeight);
        // 确保容器可见
        chartContainer.style.display = 'block';
        chartContainer.style.height = '400px';
        chartContainer.style.width = '100%';

        // 如果图表已经初始化，尝试重新渲染
        if (window.NetworkQualityManager && window.NetworkQualityManager.chart) {
            console.log('尝试重新渲染图表');
            window.NetworkQualityManager.chart.resize();
        }
    }
});

// 初始化NetworkQualityManager
function initNetworkQualityManager() {
    try {
        console.log('初始化NetworkQualityManager...');

        // 再次检查图表容器
        const chartContainer = document.getElementById('network-quality-chart');
        if (!chartContainer) {
            console.error('找不到图表容器，无法初始化NetworkQualityManager');
            return;
        }

        console.log('初始化前，图表容器尺寸:', chartContainer.offsetWidth, 'x', chartContainer.offsetHeight);

        // 确保容器可见
        chartContainer.style.display = 'block';
        chartContainer.style.height = '400px';
        chartContainer.style.width = '100%';

        // 确保ECharts已加载
        if (typeof echarts === 'undefined' && typeof window.echarts !== 'undefined') {
            console.log('使用window.echarts');
            var echarts = window.echarts;
        }

        if (typeof echarts === 'undefined' && typeof window.echarts === 'undefined') {
            console.error('ECharts未加载，无法初始化NetworkQualityManager');

            // 显示错误信息
            chartContainer.innerHTML = '<div class="flex items-center justify-center h-full"><div class="text-red-500">ECharts未加载，无法渲染图表</div></div>';
            return;
        }

        // 初始化NetworkQualityManager
        NetworkQualityManager.init();

        // 设置WebSocket连接
        NetworkQualityManager.setupWebSocket();

        // 延迟检查图表是否渲染成功
        setTimeout(() => {
            if (NetworkQualityManager.chart) {
                console.log('图表渲染成功');
            } else {
                console.warn('图表可能未渲染成功，尝试重新渲染');
                NetworkQualityManager._renderChart();
            }
        }, 3000);

        console.log('NetworkQualityManager初始化完成');
    } catch (error) {
        console.error('初始化NetworkQualityManager时出错:', error);

        // 显示错误信息
        const chartContainer = document.getElementById('network-quality-chart');
        if (chartContainer) {
            chartContainer.innerHTML = `<div class="flex items-center justify-center h-full"><div class="text-red-500">初始化NetworkQualityManager时出错: ${error.message}</div></div>`;
        }
    }
}

// 导出网络质量监控统一管理器
window.NetworkQualityManager = NetworkQualityManager;
