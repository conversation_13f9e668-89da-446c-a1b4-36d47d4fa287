/**
 * 网络质量监控优化图表管理器
 * 整合实时监控和历史分析功能
 * 使用ECharts实现，支持多种时间范围和数据颗粒度
 */

// 确保ECharts已加载
if (typeof echarts === 'undefined') {
    console.error('ECharts未加载，网络质量监控图表功能将不可用');
    // 尝试从window对象获取echarts
    if (window.echarts) {
        console.log('从window对象获取echarts成功');
        var echarts = window.echarts;
    } else {
        console.error('无法从window对象获取echarts');
    }
}

// 网络质量监控优化图表管理器
window.NetworkQualityManager = {
    // 状态管理
    state: {
        // 当前模式: 'realtime' 或 'archive'
        currentMode: 'archive',

        // 当前节点ID
        currentNodeId: null,

        // 当前目标ID
        currentTargetId: 'all',

        // 当前时间范围
        currentTimeRange: '4h',

        // 目标颜色映射
        targetColorMap: {},

        // WebSocket连接
        ws: null
    },

    // 图表实例
    chart: null,

    // 数据存储
    data: {
        // 按目标ID组织的数据
        targets: {},

        // 共享的时间标签
        labels: [],

        // 目标列表，用于图例显示
        targetList: [],

        // 原始时间戳，用于tooltip显示详细时间
        originalTimestamps: [],

        // 原始数据，用于显示实际值
        originalData: {},

        // 延迟阈值
        thresholds: {}
    },

    // 图表容器ID
    chartContainerId: 'network-quality-chart',

    // 标签容器ID
    tagsContainerId: 'network-quality-tags',

    // 初始化
    init() {
        console.log('初始化网络质量监控优化图表管理器...');

        // 获取当前节点ID
        this.state.currentNodeId = this._getCurrentNodeId();
        console.log('当前节点ID:', this.state.currentNodeId);

        // 注册事件监听器
        this._registerEventListeners();

        // 加载监控目标
        this._loadTargets();

        // 加载数据
        this._loadData();

        console.log('网络质量监控优化图表管理器初始化完成');
    },

    // 获取当前节点ID
    _getCurrentNodeId() {
        // 从URL中获取节点ID
        const match = location.pathname.match(/\/stats\/([^\/]+)/);
        if (match && match[1]) {
            return match[1];
        }
        return null;
    },

    // 注册事件监听器
    _registerEventListeners() {
        console.log('注册事件监听器...');

        // 监听目标标签点击事件
        document.addEventListener('click', (event) => {
            const targetTag = event.target.closest('.target-tag');
            if (targetTag) {
                const targetId = targetTag.dataset.targetId;
                this._switchTarget(targetId);
            }
        });

        // 监听时间范围选择器变化
        document.addEventListener('click', (event) => {
            const timeRangeButton = event.target.closest('.time-range-button');
            if (timeRangeButton) {
                const timeRange = timeRangeButton.dataset.timeRange;
                this._switchTimeRange(timeRange);
            }
        });

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // 页面变为可见时，检查WebSocket连接状态
                if (this.state.ws && this.state.ws.readyState !== WebSocket.OPEN) {
                    console.log('页面可见，重新连接WebSocket');
                    this._setupWebSocket();
                }
            } else {
                // 页面不可见时，关闭WebSocket连接
                if (this.state.ws) {
                    console.log('页面不可见，关闭WebSocket连接');
                    this.state.ws.close();
                }
            }
        });

        console.log('事件监听器注册完成');
    },

    // 切换目标
    _switchTarget(targetId) {
        if (this.state.currentTargetId === targetId) return;

        this.state.currentTargetId = targetId;
        console.log(`切换到目标: ${targetId}`);

        // 更新UI
        this._updateTargetsUI();

        // 重新加载数据
        this._loadData();
    },

    // 切换时间范围
    _switchTimeRange(timeRange) {
        if (this.state.currentTimeRange === timeRange) return;

        this.state.currentTimeRange = timeRange;
        console.log(`切换到时间范围: ${timeRange}`);

        // 更新UI
        this._updateTimeRangeUI();

        // 重新加载数据
        this._loadData();
    },

    // 更新目标UI
    _updateTargetsUI() {
        document.querySelectorAll('.target-tag').forEach(tag => {
            if (tag.dataset.targetId === this.state.currentTargetId) {
                tag.classList.add('active', 'bg-indigo-100', 'text-indigo-800', 'dark:bg-indigo-900', 'dark:text-indigo-200');
                tag.classList.remove('bg-slate-100', 'text-slate-600', 'dark:bg-slate-800', 'dark:text-slate-300');
            } else {
                tag.classList.remove('active', 'bg-indigo-100', 'text-indigo-800', 'dark:bg-indigo-900', 'dark:text-indigo-200');
                tag.classList.add('bg-slate-100', 'text-slate-600', 'dark:bg-slate-800', 'dark:text-slate-300');
            }
        });
    },

    // 更新时间范围UI
    _updateTimeRangeUI() {
        document.querySelectorAll('.time-range-button').forEach(button => {
            if (button.dataset.timeRange === this.state.currentTimeRange) {
                button.classList.add('active', 'bg-indigo-100', 'text-indigo-800', 'dark:bg-indigo-900', 'dark:text-indigo-200');
                button.classList.remove('bg-slate-100', 'text-slate-600', 'dark:bg-slate-800', 'dark:text-slate-300');
            } else {
                button.classList.remove('active', 'bg-indigo-100', 'text-indigo-800', 'dark:bg-indigo-900', 'dark:text-indigo-200');
                button.classList.add('bg-slate-100', 'text-slate-600', 'dark:bg-slate-800', 'dark:text-slate-300');
            }
        });
    },

    // 加载监控目标
    _loadTargets() {
        console.log('加载监控目标...');

        // 显示加载指示器
        const tagsContainer = document.getElementById(this.tagsContainerId);
        if (tagsContainer) {
            tagsContainer.innerHTML = '<div class="flex items-center justify-center"><div class="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-500"></div><span class="ml-2 text-xs text-slate-500">加载中...</span></div>';
        }

        // 获取所有监控目标
        fetch('/api/monitor/targets')
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    // 获取当前节点的监控数据
                    const params = new URLSearchParams();
                    params.append('type', 'archive');
                    if (this.state.currentNodeId) {
                        params.append('node_id', this.state.currentNodeId);
                    }

                    return fetch(`/api/monitor/data?${params.toString()}`)
                        .then(response => response.json())
                        .then(dataResult => {
                            if (dataResult.success) {
                                // 提取有当前节点数据的target_id列表
                                const targetIds = new Set();
                                dataResult.data.forEach(item => {
                                    if (item.target_id) {
                                        targetIds.add(item.target_id);
                                    }
                                });

                                // 筛选出有当前节点数据的目标
                                const filteredTargets = result.data.filter(target =>
                                    targetIds.has(target.id)
                                );

                                console.log(`找到${filteredTargets.length}个有当前节点数据的监控目标`);

                                // 初始化颜色映射
                                this._initializeColorMap(filteredTargets);

                                // 渲染目标标签
                                this._renderTargetTags(filteredTargets);
                            } else {
                                console.error('获取监控数据失败:', dataResult.message);
                                this._renderTargetTags([]);
                            }
                        });
                } else {
                    console.error('获取监控目标失败:', result.message);
                    this._renderTargetTags([]);
                }
            })
            .catch(error => {
                console.error('获取监控目标出错:', error);
                this._renderTargetTags([]);
            });
    },

    // 初始化颜色映射
    _initializeColorMap(targets) {
        const colors = this._generateColorList();

        // 重置颜色映射
        this.state.targetColorMap = {};

        // 为每个目标分配固定颜色
        targets.forEach((target, index) => {
            this.state.targetColorMap[target.id] = colors[index % colors.length];
        });

        // 为"全部"目标设置默认颜色
        this.state.targetColorMap['all'] = '#6366f1';

        console.log('颜色映射初始化完成:', this.state.targetColorMap);
    },

    // 获取目标颜色
    _getTargetColor(targetId) {
        return this.state.targetColorMap[targetId] || '#6366f1';
    },

    // 生成颜色列表
    _generateColorList() {
        // 预定义的颜色列表，确保每个目标有不同的颜色
        return [
            '#6366f1', // 靛蓝色
            '#10b981', // 绿色
            '#f59e0b', // 橙色
            '#ef4444', // 红色
            '#8b5cf6', // 紫色
            '#06b6d4', // 青色
            '#ec4899', // 粉色
            '#f97316', // 橙红色
            '#14b8a6', // 蓝绿色
            '#a855f7'  // 紫罗兰色
        ];
    },

    // 渲染目标标签
    _renderTargetTags(targets) {
        console.log('渲染监控目标标签...');

        const tagsContainer = document.getElementById(this.tagsContainerId);
        if (!tagsContainer) {
            console.error('找不到目标标签容器');
            return;
        }

        // 清空容器
        tagsContainer.innerHTML = '';

        // 如果没有目标，显示提示信息
        if (!targets || targets.length === 0) {
            tagsContainer.innerHTML = '<div class="text-slate-500 dark:text-slate-400 text-sm">暂无监控目标</div>';
            return;
        }

        // 添加"全部"标签
        const allTag = this._createTargetTag('all', '全部', this._getTargetColor('all'));
        tagsContainer.appendChild(allTag);

        // 添加目标标签
        targets.forEach(target => {
            const tag = this._createTargetTag(target.id, target.name, this._getTargetColor(target.id));
            tagsContainer.appendChild(tag);
        });

        console.log('渲染监控目标标签完成');
    },

    // 创建目标标签
    _createTargetTag(id, name, color) {
        const tag = document.createElement('button');
        tag.className = `target-tag ${this.state.currentTargetId === id ? 'active bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200' : 'bg-slate-100 text-slate-600 dark:bg-slate-800 dark:text-slate-300'} py-1 px-3 text-xs font-medium rounded-full flex items-center`;
        tag.dataset.targetId = id;

        // 添加颜色指示器
        tag.innerHTML = `<span class="inline-block w-2 h-2 rounded-full mr-1.5" style="background-color: ${color}"></span>${name}`;

        return tag;
    },

    // 加载数据
    _loadData() {
        console.log('加载网络质量监控数据...');

        // 显示加载指示器
        this._showLoadingIndicator(true);

        // 构建API请求参数
        const params = new URLSearchParams();
        params.append('type', 'archive'); // 使用归档数据

        // 添加时间范围参数
        params.append('time_range', this.state.currentTimeRange);

        // 添加节点ID参数
        if (this.state.currentNodeId) {
            params.append('node_id', this.state.currentNodeId);
        }

        // 添加目标ID参数
        if (this.state.currentTargetId && this.state.currentTargetId !== 'all') {
            params.append('target_id', this.state.currentTargetId);
        }

        // 根据时间范围设置数据颗粒度和采样率
        let samplingRate = 1;
        if (this.state.currentTimeRange === '4h' || this.state.currentTimeRange === '24h') {
            samplingRate = 1; // 不进行采样，确保显示所有监控点
            params.append('detail_level', 'detailed'); // 使用分钟级别数据
        } else if (this.state.currentTimeRange === '7d') {
            samplingRate = 1; // 7天数据，不进行采样
            params.append('detail_level', 'detailed'); // 分钟级颗粒度
        } else if (this.state.currentTimeRange === '31d') {
            samplingRate = 1; // 31天数据，完整采样
            params.append('detail_level', 'day'); // 天级颗粒度
        }
        params.append('sampling_rate', samplingRate.toString());

        console.log('请求参数:', params.toString());

        // 发送请求
        fetch(`/api/monitor/data?${params.toString()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(result => {
                if (result.success) {
                    console.log(`获取到数据: ${result.data.length}条记录`);
                    // 处理数据
                    this._processData(result.data);
                    // 渲染图表
                    this._renderChart();
                } else {
                    console.error('获取数据失败:', result.message);
                    // 设置默认数据
                    this._setDefaultData();
                    // 渲染图表
                    this._renderChart();
                }
            })
            .catch(error => {
                console.error('获取数据出错:', error);
                // 设置默认数据
                this._setDefaultData();
                // 渲染图表
                this._renderChart();
            })
            .finally(() => {
                // 隐藏加载指示器
                this._showLoadingIndicator(false);
            });
    },

    // 处理数据
    _processData(data) {
        console.log('开始处理数据...');

        // 重置数据
        this.data.targets = {};
        this.data.labels = [];
        this.data.targetList = [];
        this.data.originalTimestamps = [];
        this.data.originalData = {};

        // 如果数据为空，添加默认数据点
        if (!data || !Array.isArray(data) || data.length === 0) {
            console.warn('数据为空，添加默认数据点');
            this._setDefaultData();
            return;
        }

        // 按时间排序（从早到晚）
        data.sort((a, b) => a.created_at - b.created_at);

        // 提取唯一的分钟级时间戳和目标ID
        const minuteTimeLabels = new Map(); // 使用Map保存分钟级时间戳和格式化后的标签
        const targetIds = new Set();

        // 第一次遍历：收集所有唯一的分钟级时间戳和目标ID
        data.forEach(item => {
            // 确保每条记录都有target_id字段
            if (!item.target_id && this.state.currentTargetId && this.state.currentTargetId !== 'all') {
                item.target_id = this.state.currentTargetId;
            }

            // 收集目标ID
            if (item.target_id) {
                targetIds.add(item.target_id);
            }

            // 将时间戳转换为分钟级别（忽略秒和毫秒）
            const minuteTimestamp = Math.floor(item.created_at / 60) * 60;

            // 格式化时间标签
            if (!minuteTimeLabels.has(minuteTimestamp)) {
                const date = new Date(minuteTimestamp * 1000);
                let label;

                // 根据时间范围选择不同的格式
                if (this.state.currentTimeRange === '4h') {
                    // 4小时范围：显示时:分
                    label = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                } else if (this.state.currentTimeRange === '24h') {
                    // 24小时范围：显示时:分
                    label = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                } else if (this.state.currentTimeRange === '7d') {
                    // 7天范围：显示日期和时间
                    label = date.toLocaleDateString([], { month: 'numeric', day: 'numeric' }) + ' ' +
                            date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                } else {
                    // 31天范围：只显示日期
                    label = date.toLocaleDateString([], { month: 'numeric', day: 'numeric' });
                }

                minuteTimeLabels.set(minuteTimestamp, label);
            }
        });

        // 将分钟级时间戳转换为数组并排序
        const sortedMinuteTimestamps = Array.from(minuteTimeLabels.keys()).sort((a, b) => a - b);

        // 设置时间标签
        this.data.labels = sortedMinuteTimestamps.map(timestamp => minuteTimeLabels.get(timestamp));

        // 保存原始时间戳，用于tooltip显示详细时间
        this.data.originalTimestamps = sortedMinuteTimestamps;

        // 初始化目标数据结构
        targetIds.forEach(targetId => {
            // 查找目标名称
            const targetItem = data.find(item => item.target_id === targetId);
            const targetName = targetItem?.target_name || targetId;

            // 添加到目标列表
            this.data.targetList.push({
                id: targetId,
                name: targetName
            });

            // 初始化目标数据，为每个时间点创建空数据
            this.data.targets[targetId] = {
                id: targetId,
                name: targetName,
                latency: Array(sortedMinuteTimestamps.length).fill(null),
                min: Array(sortedMinuteTimestamps.length).fill(null),
                max: Array(sortedMinuteTimestamps.length).fill(null)
            };

            // 初始化原始数据存储
            this.data.originalData[targetId] = {
                latency: Array(sortedMinuteTimestamps.length).fill(null),
                min: Array(sortedMinuteTimestamps.length).fill(null),
                max: Array(sortedMinuteTimestamps.length).fill(null)
            };
        });

        // 第二次遍历：填充数据
        data.forEach(item => {
            // 确保每条记录都有target_id字段
            if (!item.target_id) {
                if (this.state.currentTargetId && this.state.currentTargetId !== 'all') {
                    item.target_id = this.state.currentTargetId;
                } else if (targetIds.size === 1) {
                    // 如果只有一个目标ID，使用它
                    item.target_id = Array.from(targetIds)[0];
                } else {
                    return; // 跳过没有target_id的记录
                }
            }

            // 将时间戳转换为分钟级别
            const minuteTimestamp = Math.floor(item.created_at / 60) * 60;

            // 找到分钟级时间戳对应的索引
            const timestampIndex = sortedMinuteTimestamps.indexOf(minuteTimestamp);
            if (timestampIndex === -1) {
                return; // 跳过无匹配的时间戳
            }

            // 填充数据（如果已有数据，取平均值）
            const target = this.data.targets[item.target_id];
            const originalTarget = this.data.originalData[item.target_id];

            // 保存原始数据
            if (originalTarget.latency[timestampIndex] === null) {
                // 如果该时间点还没有数据，直接填充
                originalTarget.latency[timestampIndex] = Math.round(item.avg_time);
                originalTarget.min[timestampIndex] = Math.round(item.min_time);
                originalTarget.max[timestampIndex] = Math.round(item.max_time);
            } else {
                // 如果该时间点已有数据，取平均值（这种情况应该很少发生）
                originalTarget.latency[timestampIndex] = Math.round((originalTarget.latency[timestampIndex] + Math.round(item.avg_time)) / 2);
                originalTarget.min[timestampIndex] = Math.min(originalTarget.min[timestampIndex], Math.round(item.min_time));
                originalTarget.max[timestampIndex] = Math.max(originalTarget.max[timestampIndex], Math.round(item.max_time));
            }

            // 暂时复制原始数据到目标数据（稍后会应用压缩）
            target.latency[timestampIndex] = originalTarget.latency[timestampIndex];
            target.min[timestampIndex] = originalTarget.min[timestampIndex];
            target.max[timestampIndex] = originalTarget.max[timestampIndex];
        });

        console.log(`处理了${data.length}条网络质量监控数据，包含${targetIds.size}个目标，${sortedMinuteTimestamps.length}个时间点`);

        // 计算每个目标的延迟阈值
        this.data.thresholds = this._calculateThresholds();

        // 应用高延迟压缩
        Object.keys(this.data.targets).forEach(targetId => {
            const target = this.data.targets[targetId];
            const originalTarget = this.data.originalData[targetId];
            const threshold = this.data.thresholds[targetId];

            // 压缩延迟数据
            for (let i = 0; i < target.latency.length; i++) {
                if (target.latency[i] !== null) {
                    target.latency[i] = this._compressHighLatency(originalTarget.latency[i], threshold);
                }
                if (target.min[i] !== null) {
                    target.min[i] = this._compressHighLatency(originalTarget.min[i], threshold);
                }
                if (target.max[i] !== null) {
                    target.max[i] = this._compressHighLatency(originalTarget.max[i], threshold);
                }
            }
        });

        console.log('数据处理完成，已应用高延迟压缩');
    },

    // 设置默认数据
    _setDefaultData() {
        this.data.targets = {
            'default': {
                id: 'default',
                name: '默认',
                latency: [0],
                min: [0],
                max: [0]
            }
        };
        this.data.labels = ['无数据'];
        this.data.targetList = [{
            id: 'default',
            name: '默认'
        }];
        this.data.originalTimestamps = [Math.floor(Date.now() / 1000)];
        this.data.originalData = {
            'default': {
                latency: [0],
                min: [0],
                max: [0]
            }
        };
        this.data.thresholds = {
            'default': 500
        };
    },

    // 计算每个目标的延迟阈值
    _calculateThresholds() {
        const thresholds = {};

        // 使用固定阈值500ms
        const fixedThreshold = 500;

        Object.keys(this.data.targets).forEach(targetId => {
            // 设置固定阈值为500ms
            thresholds[targetId] = fixedThreshold;
        });

        return thresholds;
    },

    // 压缩高延迟数据
    _compressHighLatency(value, threshold) {
        if (value === null || value === 0) {
            // 超时/失败值，保持原值
            return value;
        } else if (value <= threshold) {
            // 正常延迟，应用轻微压缩
            // 使用平方根压缩，保持相对关系但减小差异
            return Math.sqrt(value * 20); // 平方根压缩，乘以系数使小值更明显
        } else {
            // 高延迟，应用更强的压缩
            const baseValue = Math.sqrt(threshold * 20); // 阈值的压缩值
            const excessValue = value - threshold;
            // 对超出部分应用对数压缩
            const compressedExcess = Math.log10(excessValue + 1) * 50; // 调整系数使高延迟部分更明显
            return baseValue + compressedExcess;
        }
    },

    // 显示/隐藏加载指示器
    _showLoadingIndicator(show) {
        const loadingIndicator = document.getElementById('network-quality-loading');
        if (loadingIndicator) {
            if (show) {
                loadingIndicator.classList.remove('hidden');
            } else {
                loadingIndicator.classList.add('hidden');
            }
        }
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('页面加载完成，准备初始化NetworkQualityManager...');

    // 检查是否在stat.html页面
    const isStatPage = location.pathname.match(/\/stats\/([^\/]+)/) || location.pathname === '/stat';
    if (!isStatPage) {
        console.log('不在stat.html页面，跳过初始化');
        return;
    }

    // 检查必要的DOM元素是否存在
    const chartContainer = document.getElementById('network-quality-chart');
    if (!chartContainer) {
        console.warn('找不到图表容器，可能不在正确的页面');
        return;
    }

    // 确保ECharts已加载
    if (typeof echarts === 'undefined' && typeof window.echarts === 'undefined') {
        console.error('ECharts未加载，无法初始化NetworkQualityManager');
        return;
    }

    // 初始化NetworkQualityManager
    NetworkQualityManager.init();
});
