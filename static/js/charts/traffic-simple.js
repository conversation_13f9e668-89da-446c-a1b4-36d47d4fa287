// 极简流量统计，仅依赖ECharts和原生JS
(function() {
    // 工具：字节自动换算
    function formatBytes(bytes) {
        if (bytes < 1024) return bytes + ' B';
        let k = 1024, sizes = ['KB', 'MB', 'GB', 'TB'], i = -1;
        do { bytes = bytes / k; i++; } while (bytes >= k && i < sizes.length - 1);
        return bytes.toFixed(2) + ' ' + sizes[i];
    }
    // 获取节点ID
    function getSid() {
        const match = location.pathname.match(/\/stats\/([^\/]+)/);
        return match ? match[1] : null;
    }
    // 获取流量数据
    async function fetchTraffic() {
        const sid = getSid();
        if (!sid) return null;
        const resp = await fetch(`/stats/${sid}/traffic`);
        const json = await resp.json();
        return json.data || null;
    }
    // 渲染图表
    function renderChart(type, data) {
        const chartDom = document.getElementById('traffic-chart');
        if (!chartDom) return;
        const theme = (document.documentElement.classList.contains('dark') || document.body.classList.contains('dark')) ? 'dark' : 'light';
        if (window._trafficChart) { window._trafficChart.dispose(); }
        const chart = echarts.init(chartDom, theme);
        window._trafficChart = chart;
        let labels = [], inData = [], outData = [];
        if (type === 'hs') {
            labels = Array.from({length: 24}, (_, i) => `${i}:00`);
        } else if (type === 'ds') {
            labels = Array.from({length: 31}, (_, i) => `${i+1}日`);
        } else if (type === 'ms') {
            labels = Array.from({length: 12}, (_, i) => `${i+1}月`);
        }
        (data||[]).forEach((item, i) => {
            inData[i] = item && item[0] ? +(item[0]) : 0;
            outData[i] = item && item[1] ? +(item[1]) : 0;
        });
        // 自动单位
        const maxVal = Math.max(...inData, ...outData);
        let unit = 'B';
        if (maxVal >= 1024*1024*1024) unit = 'GB';
        else if (maxVal >= 1024*1024) unit = 'MB';
        else if (maxVal >= 1024) unit = 'KB';
        function toUnit(val) {
            if (unit==='GB') return +(val/1024/1024/1024).toFixed(2);
            if (unit==='MB') return +(val/1024/1024).toFixed(2);
            if (unit==='KB') return +(val/1024).toFixed(2);
            return val;
        }
        
        // 使用统一配置
        if (window.UnifiedChartConfig) {
            const convertedInData = inData.map(toUnit);
            const convertedOutData = outData.map(toUnit);
            
            const baseConfig = window.UnifiedChartConfig.getCommonOptions({
                title: { text: type==='hs'?'24小时流量':'ds'===type?'31天流量':'12个月流量' },
                xAxis: { data: labels },
                yAxis: window.UnifiedChartConfig.getDynamicYAxis([convertedInData, convertedOutData], {
                    name: unit
                }),
                tooltip: {
                    formatter: function(params) {
                        let result = params[0].axisValue + '<br/>';
                        params.forEach(param => {
                            const value = param.value === undefined ? 0 : param.value;
                            result += `${param.marker} ${param.seriesName}: ${value} ${unit}<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: ['下行', '上行']
                },
                series: [
                    window.UnifiedChartConfig.getCommonSeries('line', {
                        name: '下行',
                        data: convertedInData,
                        lineStyle: { color: '#22c55e' },
                        itemStyle: { color: '#22c55e' },
                        dataLength: convertedInData.length
                    }),
                    window.UnifiedChartConfig.getCommonSeries('line', {
                        name: '上行',
                        data: convertedOutData,
                        lineStyle: { color: '#2563eb' },
                        itemStyle: { color: '#2563eb' },
                        dataLength: convertedOutData.length
                    })
                ]
            });
            
            chart.setOption(baseConfig);
        } else {
            // 降级处理，使用原有配置
            const isMobile = window.innerWidth <= 600;
            chart.setOption({
                backgroundColor: 'transparent',
                title: { text: type==='hs'?'24小时流量':'ds'===type?'31天流量':'12个月流量', left: 'center', textStyle: { fontSize: isMobile ? 12 : 14, color: theme === 'dark' ? '#94a3b8' : '#64748b' } },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: { backgroundColor: '#6a7985' }
                    },
                    formatter: function(params) {
                        let result = params[0].axisValue + '<br/>';
                        params.forEach(param => {
                            const value = param.value === undefined ? 0 : param.value;
                            result += `${param.marker} ${param.seriesName}: ${value} ${unit}<br/>`;
                        });
                        return result;
                    }
                },
                legend: isMobile ? { show: false } : { data: ['下行', '上行'], top: 30 },
                grid: { left: isMobile ? '2%' : '3%', right: isMobile ? '2%' : '4%', bottom: '3%', top: '15%', containLabel: true },
                xAxis: {
                    type: 'category',
                    data: labels,
                    axisLabel: { 
                        fontSize: isMobile ? 9 : 12, 
                        color: theme === 'dark' ? '#94a3b8' : '#64748b',
                        rotate: 0
                    },
                    axisLine: { lineStyle: { color: theme === 'dark' ? '#475569' : '#cbd5e1' } },
                    splitLine: { show: false }
                },
                yAxis: {
                    type: 'value',
                    name: unit,
                    nameTextStyle: { color: theme === 'dark' ? '#94a3b8' : '#64748b' },
                    axisLabel: { fontSize: isMobile ? 9 : 12, color: theme === 'dark' ? '#94a3b8' : '#64748b' },
                    axisLine: { show: false },
                    splitLine: { 
                        show: true, 
                        lineStyle: { 
                            color: theme === 'dark' ? '#334155' : '#e2e8f0',
                            type: 'solid',
                            width: 1,
                            opacity: 0.6
                        } 
                    },
                    max: function(value) {
                        return Math.ceil(value.max * 1.2); // 为顶部留出20%空间
                    }
                },
                series: [
                    { name: '下行', type: 'line', data: inData.map(toUnit), smooth: true, symbol: 'none', lineStyle: { color: '#22c55e' }, itemStyle: { color: '#22c55e' } },
                    { name: '上行', type: 'line', data: outData.map(toUnit), smooth: true, symbol: 'none', lineStyle: { color: '#2563eb' }, itemStyle: { color: '#2563eb' } }
                ]
            });
        }
        
        window.addEventListener('resize', () => { chart.resize(); });
        if (window.ResizeObserver) {
            const ro = new ResizeObserver(() => { chart.resize(); });
            ro.observe(chartDom);
        }
        
        // 注册图表到主题管理器
        if (window.UnifiedChartThemeManager && chart) {
            window.UnifiedChartThemeManager.register('traffic-chart', chart, 'dynamic');
        }
    }
    
    // 按钮事件
    function setupBtns(traffic) {
        const activeDayClasses = ['bg-blue-50', 'text-blue-600', 'border-blue-200'];
        const inactiveDayClasses = ['bg-gray-50', 'text-gray-600', 'border-gray-200'];
        // 暗黑模式的激活/非激活类依赖HTML中的dark:前缀，并通过JS确保正确切换
        const activeDarkClasses = ['dark:bg-slate-700', 'dark:text-white', 'dark:border-slate-700'];
        const inactiveDarkClasses = ['dark:bg-slate-800', 'dark:text-slate-300', 'dark:border-slate-700'];

        document.querySelectorAll('.traffic-range-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const Sthis = this;

                // 移除所有按钮的激活状态
                document.querySelectorAll('.traffic-range-btn').forEach(b => {
                    b.classList.remove(...activeDayClasses);
                    b.classList.add(...inactiveDayClasses);
                    b.classList.remove(...activeDarkClasses);
                    b.classList.add(...inactiveDarkClasses);
                });

                // 为当前点击的按钮设置激活状态
                Sthis.classList.remove(...inactiveDayClasses);
                Sthis.classList.add(...activeDayClasses);
                Sthis.classList.remove(...inactiveDarkClasses);
                Sthis.classList.add(...activeDarkClasses);

                const range = Sthis.dataset.range;
                // 调用 renderChart 更新图表，数据已预加载
                renderChart(range, traffic[range]);
            });
        });
    }
    // 主题切换监听
    function setupThemeListener(traffic, currentType) {
        const chartDom = document.getElementById('traffic-chart');
        const observer = new MutationObserver(() => {
            renderChart(currentType, traffic[currentType]);
        });
        observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
        observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });
        window.addEventListener('themechange', function() {
            renderChart(currentType, traffic[currentType]);
        });
    }
    // 初始化
    document.addEventListener('DOMContentLoaded', async function() {
        const traffic = await fetchTraffic();
        if (!traffic) return;
        let currentType = 'hs';
        renderChart('hs', traffic.hs);
        setupBtns(traffic);
        setupThemeListener(traffic, currentType);
    });
})();