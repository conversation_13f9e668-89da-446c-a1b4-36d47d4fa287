/**
 * 网络质量监控统一图表管理器
 * 使用ECharts实现，支持多种时间范围和数据颗粒度
 * 利用tcping_archive表的长期数据
 */

// 确保ECharts已加载
if (typeof echarts === 'undefined') {
    console.error('ECharts未加载，网络质量监控图表功能将不可用');
    // 尝试从window对象获取echarts
    if (window.echarts) {
        console.log('从window对象获取echarts成功');
        var echarts = window.echarts;
    } else {
        console.error('无法从window对象获取echarts');
    }
}

// 网络质量监控统一图表管理器
window.NetworkQualityUnifiedManager = {
    // 图表实例
    chart: null,

    // 当前节点ID
    currentNodeId: null,

    // 当前目标ID
    currentTargetId: 'all',

    // 当前时间范围
    currentTimeRange: '4h',

    // 数据存储
    data: {
        targets: {}, // 按目标ID组织数据
        labels: [],  // 共享的时间标签
        targetList: [], // 目标列表，用于图例显示
        originalTimestamps: [] // 原始时间戳，用于tooltip显示详细时间
    },

    // 图表容器ID
    chartContainerId: 'network-quality-chart',

    // 初始化
    init() {
        console.log('初始化网络质量监控统一图表管理器...');

        // 获取当前节点ID
        this.currentNodeId = this._getCurrentNodeId();
        console.log('当前节点ID:', this.currentNodeId);

        // 注册事件监听器
        this._registerEventListeners();

        // 加载监控目标
        this._loadTargets();

        // 加载数据
        this._loadData();

        console.log('网络质量监控统一图表管理器初始化完成');
    },

    // 显示提示信息
    _showTooltip(message) {
        const container = document.getElementById(this.chartContainerId);
        if (!container) return;

        // 检查是否已存在提示元素
        let tooltip = container.querySelector('.network-quality-tooltip');
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.className = 'network-quality-tooltip absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black bg-opacity-70 text-white px-3 py-2 rounded-md text-sm z-20 transition-opacity duration-300 opacity-0 pointer-events-none';
            container.appendChild(tooltip);
        }

        // 设置提示内容
        tooltip.textContent = message;

        // 显示提示
        tooltip.classList.remove('opacity-0');
        tooltip.classList.add('opacity-100');

        // 2秒后隐藏提示
        setTimeout(() => {
            tooltip.classList.remove('opacity-100');
            tooltip.classList.add('opacity-0');

            // 完全隐藏后移除元素
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 300);
        }, 2000);
    },

    // 注册事件监听器
    _registerEventListeners() {
        console.log('注册网络质量监控图表事件监听器...');

        // 窗口大小改变时重新渲染图表
        window.addEventListener('resize', () => {
            if (this.chart) {
                this.chart.resize();
            }
        });

        // 监听主题变化事件
        document.addEventListener('theme:changed', (e) => {
            if (e.detail && typeof e.detail.isDark !== 'undefined') {
                console.log('主题已变化，重新渲染网络质量监控图表');
                // 重新渲染图表以适应新主题
                this._renderChart();
            }
        });

        // 刷新按钮点击事件
        const refreshButton = document.getElementById('refresh-network-quality');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this._loadData();
            });
        }

        // 时间范围选择器事件
        document.querySelectorAll('.time-range-selector').forEach(button => {
            button.addEventListener('click', (e) => {
                const range = e.target.dataset.range;
                this._setActiveTimeRange(range);
                
                // 显示/隐藏颗粒度选择器
                const granularitySelector = document.getElementById('granularity-selector');
                if (range === '7d') {
                    granularitySelector.classList.remove('hidden');
                } else {
                    granularitySelector.classList.add('hidden');
                }
                
                this._loadData();
            });
        });

        // 绑定颗粒度选择器事件
        const granularitySelect = document.getElementById('data-granularity');
        if (granularitySelect) {
            granularitySelect.addEventListener('change', () => {
                this._loadData();
            });
        }

        // 应用自定义时间范围按钮事件
        const applyCustomButton = document.getElementById('apply-custom-time-range');
        if (applyCustomButton) {
            applyCustomButton.addEventListener('click', () => {
                const startTime = document.getElementById('custom-start-time').value;
                const endTime = document.getElementById('custom-end-time').value;

                if (!startTime || !endTime) {
                    alert('请选择开始时间和结束时间');
                    return;
                }

                // 转换为时间戳
                const startTimestamp = new Date(startTime).getTime() / 1000;
                const endTimestamp = new Date(endTime).getTime() / 1000;

                if (startTimestamp >= endTimestamp) {
                    alert('开始时间必须早于结束时间');
                    return;
                }

                // 加载自定义时间范围的数据
                this._loadCustomTimeRangeData(startTimestamp, endTimestamp);
            });
        }

        // 目标标签点击事件
        document.addEventListener('click', (event) => {
            const targetTag = event.target.closest('.target-tag');
            if (!targetTag) return;

            const targetId = targetTag.dataset.targetId;
            if (!targetId) return;

            this._switchTarget(targetId);
        });

        console.log('注册网络质量监控图表事件监听器成功');
    },

    // 获取当前节点ID
    _getCurrentNodeId() {
        // 从URL中获取节点ID
        const match = location.pathname.match(/\/stats\/([^\/]+)/);
        if (match && match[1]) {
            return match[1];
        }
        return null;
    },

    // 切换时间范围
    _switchTimeRange(timeRange) {
        if (this.currentTimeRange === timeRange) return;

        this.currentTimeRange = timeRange;
        console.log(`切换到时间范围: ${timeRange}`);

        // 更新UI
        this._updateTimeRangeUI(timeRange);

        // 恢复自定义按钮原始文本
        if (timeRange !== 'custom') {
            const customButton = document.querySelector('.time-range-selector[data-range="custom"]');
            if (customButton) {
                customButton.innerHTML = '<i class="ti ti-calendar text-xs align-middle mr-0.5"></i>自定义';
            }
        }

        // 重新加载数据
        this._loadData();
    },

    // 更新时间范围UI
    _updateTimeRangeUI(timeRange) {
        document.querySelectorAll('.time-range-selector').forEach(button => {
            if (button.dataset.range === timeRange) {
                // 激活状态
                button.classList.add('bg-blue-50', 'text-blue-600', 'dark:bg-slate-700', 'dark:text-white');
                button.classList.remove('bg-gray-50', 'text-gray-600', 'dark:bg-slate-800', 'dark:text-slate-300');
            } else {
                // 非激活状态
                button.classList.remove('bg-blue-50', 'text-blue-600', 'dark:bg-slate-700', 'dark:text-white');
                button.classList.add('bg-gray-50', 'text-gray-600', 'dark:bg-slate-800', 'dark:text-slate-300');
            }
        });
    },

    // 切换目标
    _switchTarget(targetId) {
        if (this.currentTargetId === targetId) return;

        this.currentTargetId = targetId;
        console.log(`切换到目标: ${targetId}`);

        // 更新UI
        document.querySelectorAll('.target-tag').forEach(tag => {
            if (tag.dataset.targetId === targetId) {
                // 激活状态
                tag.classList.add('bg-blue-50', 'text-blue-600', 'dark:bg-slate-700', 'dark:text-white');
                tag.classList.remove('bg-gray-50', 'text-gray-600', 'dark:bg-slate-800', 'dark:text-slate-300');
            } else {
                // 非激活状态
                tag.classList.remove('bg-blue-50', 'text-blue-600', 'dark:bg-slate-700', 'dark:text-white');
                tag.classList.add('bg-gray-50', 'text-gray-600', 'dark:bg-slate-800', 'dark:text-slate-300');
            }
        });

        // 重新加载数据
        this._loadData();
    },

    // 加载数据
    _loadData() {
        console.log('加载网络质量监控数据...');

        // 显示加载指示器
        this._showLoadingIndicator(true);

        // 构建API请求参数
        const params = new URLSearchParams();
        params.append('type', 'archive'); // 使用归档数据

        // 添加时间范围参数
        params.append('time_range', this.currentTimeRange);

        // 为7天时间范围添加颗粒度参数
        if (this.currentTimeRange === '7d') {
            const granularitySelect = document.getElementById('data-granularity');
            const granularity = granularitySelect ? granularitySelect.value : '5min';
            params.append('granularity', granularity);
            console.log(`7天时间范围使用颗粒度: ${granularity}`);
        }

        // 添加节点ID参数
        if (this.currentNodeId) {
            params.append('node_id', this.currentNodeId);
        }

        // 添加目标ID参数
        if (this.currentTargetId && this.currentTargetId !== 'all') {
            params.append('target_id', this.currentTargetId);
        }

        // 根据时间范围设置数据颗粒度和采样率
        let samplingRate = 1;
        let expectedDataPoints = 0; // 预期数据点数量
        
        if (this.currentTimeRange === '4h' || this.currentTimeRange === '24h') {
            samplingRate = 1; // 不进行采样，确保显示所有监控点
            params.append('detail_level', 'detailed'); // 使用分钟级别数据（来自tcping_m表）
            expectedDataPoints = this.currentTimeRange === '4h' ? 240 : 1440; // 4小时240分钟，24小时1440分钟
            console.log(`使用详细的分钟级别数据显示${this.currentTimeRange}时间范围，预期数据点: ${expectedDataPoints}`);
        } else if (this.currentTimeRange === '7d') {
            samplingRate = 1; // 7天数据，不进行采样
            params.append('detail_level', 'detailed'); // 优先使用5分钟级数据，如果不足则使用分钟级
            expectedDataPoints = 2016; // 7天×24小时×12个5分钟间隔
            console.log('使用5分钟级聚合数据显示7天时间范围，预期数据点: ' + expectedDataPoints);
        } else if (this.currentTimeRange === '31d') {
            samplingRate = 1; // 31天数据，完整采样
            params.append('detail_level', 'day'); // 天级颗粒度
            expectedDataPoints = 31; // 31天
            console.log('使用天级颗粒度数据显示31天时间范围，预期数据点: ' + expectedDataPoints);
        }
        params.append('sampling_rate', samplingRate.toString());

        console.log('请求参数:', params.toString());

        // 发送请求
        fetch(`/api/monitor/data?${params.toString()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(result => {
                if (result.success) {
                    console.log(`获取到数据: ${result.data.length}条记录`);

                    // 检查数据是否为空
                    if (!result.data || result.data.length === 0) {
                        console.warn('返回的数据为空');
                        this._showTooltip('当前时间范围内无监控数据');
                        this._setDefaultData();
                        this._renderChart();
                        return;
                    }

                    // 数据质量检查
                    const qualityCheck = this._checkDataQuality(result.data);
                    console.log('数据质量检查结果:', qualityCheck);
                    
                    if (!qualityCheck.isValid) {
                        console.warn('数据质量检查失败:', qualityCheck.issues);
                        this._showTooltip(`数据质量问题: ${qualityCheck.issues.join(', ')}`);
                    }

                    // 数据间隙检测和智能处理
                    const dataGaps = this._detectAndHandleDataGaps(result.data);
                    console.log('数据间隙检测结果:', dataGaps);
                    
                    // 使用处理后的数据
                    const processedData = dataGaps.processedData;
                    
                    // 数据密度检测和性能优化
                    const dataCount = processedData.length;
                    const isHighDensity = this._detectHighDataDensity(dataCount, expectedDataPoints);
                    
                    if (dataGaps.hasGaps) {
                        console.warn(`检测到 ${dataGaps.gapCount} 个数据间隙，总时长: ${dataGaps.totalGapDuration}秒`);
                        this._showTooltip(`数据间隙较多，正在智能处理...`);
                    }
                    
                    if (isHighDensity) {
                        console.warn(`检测到高密度数据: ${dataCount}条记录，预期${expectedDataPoints}条`);
                        this._showTooltip(`数据量较大(${dataCount}条)，正在优化渲染...`);
                        
                        // 启用性能优化模式
                        this._enablePerformanceMode(true);
                        
                        // 对高密度数据进行采样优化
                        if (dataCount > 3000) {
                            const optimizedData = this._optimizeHighDensityData(processedData, 2000);
                            console.log(`数据采样优化: ${dataCount} -> ${optimizedData.length}`);
                            // 使用优化后的数据
                            this._processData(optimizedData);
                        } else {
                            // 处理数据
                            this._processData(processedData);
                        }
                        
                        // 如果是全部目标模式且数据量过大，建议用户切换到单个目标
                        if (this.currentTargetId === 'all' && dataCount > 3000) {
                            setTimeout(() => {
                                this._showTooltip('数据量较大，建议选择单个目标以获得更好的性能');
                            }, 3000);
                        }
                    } else {
                        // 禁用性能优化模式
                        this._enablePerformanceMode(false);
                        
                        // 处理数据
                        this._processData(processedData);
                    }

                    // 添加详细日志，查看返回的数据
                    if (this.currentTargetId !== 'all') {
                        console.log(`当前目标ID: ${this.currentTargetId}`);
                        // 检查返回的数据中是否包含当前目标ID的数据
                        const targetData = processedData.filter(item => item.target_id === this.currentTargetId);
                        console.log(`当前目标的数据量: ${targetData.length}条记录`);

                        // 检查数据的时间分布
                        if (targetData.length > 0) {
                            const timestamps = targetData.map(item => item.created_at);
                            const minTime = new Date(Math.min(...timestamps) * 1000);
                            const maxTime = new Date(Math.max(...timestamps) * 1000);
                            console.log(`数据时间范围: ${minTime.toISOString()} - ${maxTime.toISOString()}`);
                            console.log(`数据时间跨度: ${(Math.max(...timestamps) - Math.min(...timestamps)) / 3600}小时`);
                        }

                        // 检查是否有其他目标的数据
                        const otherTargetIds = new Set();
                        processedData.forEach(item => {
                            if (item.target_id && item.target_id !== this.currentTargetId) {
                                otherTargetIds.add(item.target_id);
                            }
                        });
                        console.log(`返回数据中包含其他目标: ${Array.from(otherTargetIds).join(', ')}`);
                    }

                    // 处理数据
                    this._processData(processedData);
                    // 渲染图表
                    this._renderChart();
                } else {
                    console.error('获取数据失败:', result.message);
                    // 设置默认数据
                    this._setDefaultData();
                    // 渲染图表
                    this._renderChart();
                }
            })
            .catch(error => {
                console.error('获取数据出错:', error);
                
                // 使用统一的错误处理器
                if (window.ChartErrorHandler) {
                    const container = document.getElementById('network-quality-chart');
                    window.ChartErrorHandler.handleError(error, '数据加载', container, {
                        showRetryButton: true,
                        retryCallback: () => this._loadData()
                    });
                } else {
                    // 降级到原有错误处理
                    if (error.message.includes('404')) {
                        this._showTooltip('目标不存在，请检查目标配置');
                    } else if (error.message.includes('500')) {
                        this._showTooltip('服务器内部错误，请稍后重试');
                    } else {
                        this._showTooltip('获取数据失败: ' + error.message);
                    }
                    
                    // 设置默认数据
                    this._setDefaultData();
                    // 渲染图表
                    this._renderChart();
                }
            })
            .finally(() => {
                // 隐藏加载指示器
                this._showLoadingIndicator(false);
            });
    },

    // 加载自定义时间范围的数据
    _loadCustomTimeRangeData(startTime, endTime) {
        console.log(`加载自定义时间范围数据: ${new Date(startTime * 1000).toLocaleString()} - ${new Date(endTime * 1000).toLocaleString()}`);

        // 显示加载指示器
        this._showLoadingIndicator(true);

        // 格式化展示时间
        const formatDisplayDate = (timestamp) => {
            const date = new Date(timestamp * 1000);
            return date.toLocaleString('zh-CN', {
                month: 'numeric',
                day: 'numeric'
            });
        };

        // 更新当前时间范围
        this.currentTimeRange = 'custom';

        // 在自定义按钮上显示选择的时间范围
        const customButton = document.querySelector('.time-range-selector[data-range="custom"]');
        if (customButton) {
            customButton.innerHTML = `<i class="ti ti-calendar text-xs align-middle mr-0.5"></i>${formatDisplayDate(startTime)} - ${formatDisplayDate(endTime)}`;
        }

        // 构建API请求参数
        const params = new URLSearchParams();
        params.append('type', 'archive'); // 使用归档数据
        params.append('start_time', startTime.toString());
        params.append('end_time', endTime.toString());

        // 添加节点ID参数
        if (this.currentNodeId) {
            params.append('node_id', this.currentNodeId);
        }

        // 添加目标ID参数
        if (this.currentTargetId && this.currentTargetId !== 'all') {
            params.append('target_id', this.currentTargetId);
        }

        // 计算时间范围跨度（秒）
        const timeSpan = endTime - startTime;

        // 根据时间跨度设置采样率
        let samplingRate = 1;
        if (timeSpan > 7 * 24 * 3600) { // 超过7天
            samplingRate = 1; // 改为完整采样
            params.append('detail_level', 'day'); // 天级颗粒度
        } else if (timeSpan > 24 * 3600) { // 超过1天但不超过7天
            samplingRate = 1; // 完整采样
            params.append('detail_level', 'detailed'); // 分钟级颗粒度
        }
        params.append('sampling_rate', samplingRate.toString());

        console.log('请求参数:', params.toString());

        // 发送请求
        fetch(`/api/monitor/data?${params.toString()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(result => {
                if (result.success) {
                    console.log(`获取到数据: ${result.data.length}条记录`);
                    
                    // 检查数据是否为空
                    if (!result.data || result.data.length === 0) {
                        console.warn('自定义时间范围返回的数据为空');
                        this._showTooltip('选择的时间范围内无监控数据');
                        this._setDefaultData();
                        this._renderChart();
                        return;
                    }
                    
                    // 处理数据
                    this._processData(result.data);
                    // 渲染图表
                    this._renderChart();

                    // 隐藏自定义时间选择器
                    const customSelector = document.getElementById('custom-time-range-selector');
                    if (customSelector) {
                        customSelector.classList.add('hidden');
                        customSelector.classList.remove('flex');
                    }
                } else {
                    console.error('获取数据失败:', result.message);
                    // 设置默认数据
                    this._setDefaultData();
                    // 渲染图表
                    this._renderChart();
                    // 显示错误提示
                    this._showTooltip('获取数据失败: ' + result.message);
                }
            })
            .catch(error => {
                console.error('获取数据出错:', error);
                
                // 根据错误类型显示不同的提示
                if (error.message.includes('404')) {
                    this._showTooltip('目标不存在，请检查目标配置');
                } else if (error.message.includes('500')) {
                    this._showTooltip('服务器内部错误，请稍后重试');
                } else {
                    this._showTooltip('获取数据出错: ' + error.message);
                }
                
                // 设置默认数据
                this._setDefaultData();
                // 渲染图表
                this._renderChart();
            })
            .finally(() => {
                // 隐藏加载指示器
                this._showLoadingIndicator(false);
            });
    },

    // 处理数据
    _processData(data) {
        console.log('开始处理数据...');

        // 重置数据
        this.data.targets = {};
        this.data.labels = [];
        this.data.targetList = [];
        this.data.originalTimestamps = [];

        // 存储原始数据，用于显示实际值
        this.data.originalData = {};

        // 如果数据为空，添加默认数据点
        if (!data || !Array.isArray(data) || data.length === 0) {
            console.warn('数据为空，添加默认数据点');
            this._setDefaultData();
            return;
        }

        // 检查数据结构
        console.log('数据样例:', data.slice(0, 2));

        // 检查当前目标ID
        if (this.currentTargetId !== 'all') {
            console.log(`处理单个目标数据: ${this.currentTargetId}`);
            // 检查数据中是否包含当前目标ID
            const targetData = data.filter(item => item.target_id === this.currentTargetId);
            console.log(`过滤后的目标数据量: ${targetData.length}条记录`);

            // 如果没有当前目标的数据，记录警告
            if (targetData.length === 0) {
                console.warn(`警告: 返回的数据中不包含目标ID ${this.currentTargetId} 的数据!`);
                // 检查数据中包含哪些目标ID
                const availableTargets = new Set();
                data.forEach(item => {
                    if (item.target_id) {
                        availableTargets.add(item.target_id);
                    }
                });
                console.log(`可用的目标ID: ${Array.from(availableTargets).join(', ')}`);
            }
        }

        // 按时间排序（从早到晚）
        data.sort((a, b) => a.created_at - b.created_at);

        // 提取唯一的分钟级时间戳和目标ID
        const minuteTimeLabels = new Map(); // 使用Map保存分钟级时间戳和格式化后的标签
        const targetIds = new Set();

        // 第一次遍历：收集所有唯一的分钟级时间戳和目标ID
        data.forEach(item => {
            // 确保每条记录都有target_id字段
            if (!item.target_id && this.currentTargetId && this.currentTargetId !== 'all') {
                console.log(`为缺少target_id的记录添加target_id: ${this.currentTargetId}`);
                item.target_id = this.currentTargetId;
            }

            // 收集目标ID
            if (item.target_id) {
                targetIds.add(item.target_id);
            } else {
                console.warn('警告: 记录缺少target_id字段', item);
            }

            // 将时间戳转换为分钟级别（忽略秒和毫秒）
            const minuteTimestamp = Math.floor(item.created_at / 60) * 60;

            // 格式化时间标签
            if (!minuteTimeLabels.has(minuteTimestamp)) {
                const date = new Date(minuteTimestamp * 1000);
                let formattedTime;

                // 根据时间范围选择不同的格式
                switch (this.currentTimeRange) {
                    case '4h':
                    case '24h':
                        // 4小时和24小时显示到分钟
                        formattedTime = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                        break;
                    case '7d':
                        // 7天显示日期和小时，便于区分
                        const isToday = date.toDateString() === new Date().toDateString();
                        const isYesterday = date.toDateString() === new Date(Date.now() - 24*60*60*1000).toDateString();
                        
                        if (isToday) {
                            formattedTime = `今天${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                        } else if (isYesterday) {
                            formattedTime = `昨天${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                        } else {
                            formattedTime = `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                        }
                        break;
                    case '31d':
                        // 31天显示到天
                        formattedTime = `${date.getMonth() + 1}/${date.getDate()}`;
                        break;
                    case 'custom':
                        // 自定义时间范围显示日期和时间
                        formattedTime = `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                        break;
                    default:
                        formattedTime = date.toLocaleString();
                }

                minuteTimeLabels.set(minuteTimestamp, formattedTime);
            }
        });

        // 将分钟级时间戳转换为数组并按时间排序
        const sortedMinuteTimestamps = Array.from(minuteTimeLabels.keys()).sort((a, b) => a - b);

        // 设置全局时间标签
        this.data.labels = sortedMinuteTimestamps.map(timestamp => minuteTimeLabels.get(timestamp));
        // 保存原始时间戳，用于tooltip显示详细时间
        this.data.originalTimestamps = sortedMinuteTimestamps;

        // 初始化目标数据结构
        targetIds.forEach(targetId => {
            // 查找目标名称
            const targetItem = data.find(item => item.target_id === targetId);
            const targetName = targetItem?.target_name || targetId;

            // 添加到目标列表
            this.data.targetList.push({
                id: targetId,
                name: targetName
            });

            // 初始化目标数据，为每个时间点创建空数据
            this.data.targets[targetId] = {
                id: targetId,
                name: targetName,
                latency: Array(sortedMinuteTimestamps.length).fill(null),
                min: Array(sortedMinuteTimestamps.length).fill(null),
                max: Array(sortedMinuteTimestamps.length).fill(null)
            };

            // 初始化原始数据存储
            this.data.originalData[targetId] = {
                latency: Array(sortedMinuteTimestamps.length).fill(null),
                min: Array(sortedMinuteTimestamps.length).fill(null),
                max: Array(sortedMinuteTimestamps.length).fill(null)
            };
        });

        // 第二次遍历：填充数据
        data.forEach(item => {
            // 确保每条记录都有target_id字段
            if (!item.target_id) {
                if (this.currentTargetId && this.currentTargetId !== 'all') {
                    item.target_id = this.currentTargetId;
                } else if (targetIds.size === 1) {
                    // 如果只有一个目标ID，使用它
                    item.target_id = Array.from(targetIds)[0];
                } else {
                    console.warn('无法确定记录的target_id', item);
                    return; // 跳过没有target_id的记录
                }
            }

            // 将时间戳转换为分钟级别
            const minuteTimestamp = Math.floor(item.created_at / 60) * 60;

            // 找到分钟级时间戳对应的索引
            const timestampIndex = sortedMinuteTimestamps.indexOf(minuteTimestamp);
            if (timestampIndex === -1) {
                console.warn(`未找到时间戳${minuteTimestamp}(${new Date(minuteTimestamp * 1000).toISOString()})的索引，数据点可能丢失`);
                return;  // 记录警告但仍然跳过无匹配的时间戳
            }

            // 填充数据（如果已有数据，取平均值）
            const target = this.data.targets[item.target_id];
            const originalTarget = this.data.originalData[item.target_id];

            // 保存原始数据
            if (originalTarget.latency[timestampIndex] === null) {
                // 如果该时间点还没有数据，直接填充
                originalTarget.latency[timestampIndex] = Math.round(item.avg_time);
                originalTarget.min[timestampIndex] = Math.round(item.min_time);
                originalTarget.max[timestampIndex] = Math.round(item.max_time);
            } else {
                // 如果该时间点已有数据，取平均值（这种情况应该很少发生）
                originalTarget.latency[timestampIndex] = Math.round((originalTarget.latency[timestampIndex] + Math.round(item.avg_time)) / 2);
                originalTarget.min[timestampIndex] = Math.min(originalTarget.min[timestampIndex], Math.round(item.min_time));
                originalTarget.max[timestampIndex] = Math.max(originalTarget.max[timestampIndex], Math.round(item.max_time));
            }

            // 直接使用原始数据，不应用压缩算法
            target.latency[timestampIndex] = originalTarget.latency[timestampIndex];
            target.min[timestampIndex] = originalTarget.min[timestampIndex];
            target.max[timestampIndex] = originalTarget.max[timestampIndex];
        });

        console.log(`处理了${data.length}条网络质量监控数据，包含${targetIds.size}个目标，${sortedMinuteTimestamps.length}个时间点`);

        // 不再应用压缩算法，直接使用原始延迟值
        console.log('数据处理完成，使用原始延迟值（未压缩）');
    },

    // 设置默认数据
    _setDefaultData() {
        this.data.targets = {
            'default': {
                id: 'default',
                name: '默认',
                latency: [0],
                min: [0],
                max: [0]
            }
        };
        this.data.labels = ['无数据'];
        this.data.targetList = [{
            id: 'default',
            name: '默认'
        }];
        this.data.originalTimestamps = [Math.floor(Date.now() / 1000)];
        this.data.originalData = {
            'default': {
                latency: [0],
                min: [0],
                max: [0]
            }
        };
        this.data.thresholds = {
            'default': 100
        };
    },

    // 计算每个目标的延迟阈值
    _calculateThresholds() {
        const thresholds = {};

        // 使用固定阈值500ms
        const fixedThreshold = 500;

        Object.keys(this.data.targets).forEach(targetId => {
            // 设置固定阈值为500ms
            thresholds[targetId] = fixedThreshold;
            console.log(`目标 ${targetId} 使用固定阈值: ${fixedThreshold}ms`);
        });

        return thresholds;
    },

    // 压缩延迟数据
    _compressHighLatency(value, threshold) {
        if (value === null || value === 0) {
            // 超时/失败值，保持原值
            return value;
        } else if (value <= threshold) {
            // 正常延迟，应用轻微压缩
            // 使用平方根压缩，保持相对关系但减小差异
            return Math.sqrt(value * 20); // 平方根压缩，乘以系数使小值更明显
        } else {
            // 高延迟，应用更强的压缩
            const baseValue = Math.sqrt(threshold * 20); // 阈值的压缩值
            const excessValue = value - threshold;
            // 对超出部分应用对数压缩
            const compressedExcess = Math.log10(excessValue + 1) * 50; // 调整系数使高延迟部分更明显
            return baseValue + compressedExcess;
        }
    },

    // 加载监控目标
    _loadTargets() {
        console.log('加载监控目标...');

        // 显示加载指示器
        const tagsContainer = document.getElementById('network-quality-tags');
        if (tagsContainer) {
            tagsContainer.innerHTML = '<div class="flex items-center justify-center"><div class="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-500"></div><span class="ml-2 text-xs text-slate-500">加载中...</span></div>';
        }

        // 获取所有监控目标
        fetch('/api/monitor/targets')
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    // 获取当前节点的监控数据
                    const params = new URLSearchParams();
                    params.append('type', 'archive');
                    if (this.currentNodeId) {
                        params.append('node_id', this.currentNodeId);
                    }

                    return fetch(`/api/monitor/data?${params.toString()}`)
                        .then(response => response.json())
                        .then(dataResult => {
                            if (dataResult.success) {
                                // 提取有当前节点数据的target_id列表
                                const targetIds = new Set();
                                dataResult.data.forEach(item => {
                                    if (item.target_id) {
                                        targetIds.add(item.target_id);
                                    }
                                });

                                // 筛选出有当前节点数据的目标
                                const filteredTargets = result.data.filter(target =>
                                    targetIds.has(target.id)
                                );

                                console.log(`找到${filteredTargets.length}个有当前节点数据的监控目标`);

                                // 渲染目标标签
                                this._renderTargetTags(filteredTargets);
                            } else {
                                console.error('获取监控数据失败:', dataResult.message);
                                this._renderTargetTags([]);
                            }
                        });
                } else {
                    console.error('获取监控目标失败:', result.message);
                    this._renderTargetTags([]);
                }
            })
            .catch(error => {
                console.error('获取监控目标出错:', error);
                this._renderTargetTags([]);
            });
    },

    // 渲染目标标签
    _renderTargetTags(targets) {
        console.log('渲染监控目标标签...');

        const tagsContainer = document.getElementById('network-quality-tags');
        if (!tagsContainer) {
            console.error('找不到目标标签容器');
            return;
        }

        // 清空容器
        tagsContainer.innerHTML = '';

        // 如果没有目标，显示提示信息
        if (!targets || targets.length === 0) {
            tagsContainer.innerHTML = '<div class="text-slate-500 dark:text-slate-400 text-sm">暂无监控目标</div>';
            return;
        }

        // 获取颜色列表
        const colors = this._generateColorList();

        // 添加"全部"标签
        const allTag = document.createElement('button');
        allTag.className = 'target-tag py-1 px-3 text-xs font-medium rounded-full flex items-center';
        allTag.dataset.targetId = 'all';
        allTag.innerHTML = '<span class="inline-block w-2 h-2 rounded-full mr-1.5" style="background-color: #6366f1"></span>全部';
        tagsContainer.appendChild(allTag);

        // 添加目标标签
        targets.forEach((target, index) => {
            const color = colors[index % colors.length];
            const tag = document.createElement('button');
            tag.className = 'target-tag py-1 px-3 text-xs font-medium rounded-full flex items-center';
            tag.dataset.targetId = target.id;

            // 添加颜色指示器
            tag.innerHTML = `<span class="inline-block w-2 h-2 rounded-full mr-1.5" style="background-color: ${color}"></span>${target.name}`;

            tagsContainer.appendChild(tag);
        });

        console.log('渲染监控目标标签完成');
    },

    // 显示/隐藏加载指示器
    _showLoadingIndicator(show) {
        const loadingIndicator = document.getElementById('network-quality-loading');
        if (loadingIndicator) {
            if (show) {
                loadingIndicator.classList.remove('hidden');
            } else {
                loadingIndicator.classList.add('hidden');
            }
        }
    },

    // 生成图例数据
    _generateLegendData() {
        const legendData = [];

        // 如果是单个目标，显示平均延迟、延迟范围和延迟等级说明
        if (this.currentTargetId !== 'all') {
            legendData.push({
                name: '平均延迟',
                icon: 'line'
            });
            legendData.push({
                name: '延迟范围',
                icon: 'rect'
            });
            
            // 添加延迟等级说明
            legendData.push({
                name: '延迟等级: 优秀(<50ms) 良好(50-100ms) 一般(100-200ms) 较差(200-500ms) 很差(>500ms)',
                icon: 'none',
                textStyle: {
                    fontSize: 10,
                    color: '#64748b'
                }
            });
            
            return legendData;
        }

        // 如果是全部目标，为每个目标添加图例（只显示平均延迟，不显示最大/最小延迟以避免图例过多）
        this.data.targetList.forEach(target => {
            legendData.push({
                name: `${target.name} - 平均延迟`,
                icon: 'line'
            });
            // 不添加最大/最小延迟的图例，以避免图例过多
            // 延迟范围通过区域显示，不需要单独的图例项
        });

        return legendData;
    },

    // 生成颜色列表
    _generateColorList() {
        // 预定义的颜色列表，确保每个目标有不同的颜色
        return [
            '#6366f1', // 靛蓝色
            '#10b981', // 绿色
            '#f59e0b', // 橙色
            '#ef4444', // 红色
            '#8b5cf6', // 紫色
            '#06b6d4', // 青色
            '#ec4899', // 粉色
            '#f97316', // 橙红色
            '#14b8a6', // 蓝绿色
            '#a855f7'  // 紫罗兰色
        ];
    },

    // 根据延迟值计算颜色（基于真实延迟值）
    _getLatencyColor(latencyValue, threshold = 500) {
        if (latencyValue === 0 || latencyValue === null || latencyValue === undefined) {
            return '#dc2626'; // 超时/失败：深红色
        }

        // 直接使用真实延迟值判断颜色
        if (latencyValue < 50) {
            return '#10b981'; // 优秀（<50ms）：深绿色
        } else if (latencyValue < 100) {
            return '#34d399'; // 良好（50-100ms）：浅绿色
        } else if (latencyValue < 200) {
            return '#6366f1'; // 一般（100-200ms）：蓝色
        } else if (latencyValue < 500) {
            return '#f59e0b'; // 较差（200-500ms）：橙色
        } else {
            return '#ef4444'; // 很差（>500ms）：红色
        }
    },

    // 获取延迟等级描述（基于真实延迟值）
    _getLatencyLevel(latencyValue, threshold = 500) {
        if (latencyValue === 0 || latencyValue === null || latencyValue === undefined) {
            return { level: '超时/失败', color: '#dc2626' };
        }

        // 直接使用真实延迟值判断等级
        if (latencyValue < 50) {
            return { level: '优秀', color: '#10b981' };
        } else if (latencyValue < 100) {
            return { level: '良好', color: '#34d399' };
        } else if (latencyValue < 200) {
            return { level: '一般', color: '#6366f1' };
        } else if (latencyValue < 500) {
            return { level: '较差', color: '#f59e0b' };
        } else {
            return { level: '很差', color: '#ef4444' };
        }
    },

    // 检测是否为超时
    _isTimeout(value, timeoutThreshold = 5000) {
        return value === 0 || value >= timeoutThreshold;
    },

    // 检测高密度数据
    _detectHighDataDensity(actualDataCount, expectedDataCount) {
        // 如果实际数据量超过预期数据量的1.5倍，认为是高密度数据
        const densityRatio = actualDataCount / expectedDataCount;
        const isHighDensity = densityRatio > 1.5;
        
        console.log(`数据密度检测: 实际${actualDataCount}条, 预期${expectedDataCount}条, 密度比${densityRatio.toFixed(2)}, 高密度: ${isHighDensity}`);
        
        return isHighDensity;
    },

    // 启用/禁用性能优化模式
    _enablePerformanceMode(enable) {
        this.performanceMode = enable;
        
        if (enable) {
            console.log('启用性能优化模式');
            // 设置性能优化参数
            this.performanceConfig = {
                maxSeriesCount: 10, // 最大系列数量限制
                enableSampling: true, // 启用数据采样
                reducedAnimations: true, // 减少动画效果
                simplifiedTooltip: true // 简化tooltip
            };
        } else {
            console.log('禁用性能优化模式');
            this.performanceConfig = {
                maxSeriesCount: Infinity,
                enableSampling: false,
                reducedAnimations: false,
                simplifiedTooltip: false
            };
        }
    },

    // 数据质量检查
    _checkDataQuality(data) {
        if (!data || data.length === 0) {
            return {
                isValid: false,
                issues: ['数据为空'],
                validRatio: 0
            };
        }

        let validCount = 0;
        let nullCount = 0;
        let zeroCount = 0;
        const issues = [];

        data.forEach(item => {
            if (item.avg_time === null || item.avg_time === undefined) {
                nullCount++;
            } else if (item.avg_time === 0) {
                zeroCount++;
            } else {
                validCount++;
            }
        });

        const validRatio = validCount / data.length;
        
        if (validRatio < 0.5) {
            issues.push(`数据有效率过低: ${(validRatio * 100).toFixed(1)}%`);
        }
        
        if (nullCount > data.length * 0.3) {
            issues.push(`空值数据过多: ${nullCount}条`);
        }

        return {
            isValid: validRatio >= 0.3, // 至少30%的数据有效
            issues: issues,
            validRatio: validRatio,
            stats: {
                total: data.length,
                valid: validCount,
                null: nullCount,
                zero: zeroCount
            }
        };
    },

    // 数据间隙检测和智能处理
    _detectAndHandleDataGaps(data) {
        if (!data || data.length === 0) {
            return {
                hasGaps: false,
                gapCount: 0,
                processedData: data,
                gapInfo: []
            };
        }

        console.log('开始数据间隙检测和处理...');
        
        // 按时间排序
        const sortedData = [...data].sort((a, b) => a.created_at - b.created_at);
        
        // 检测间隙
        const gaps = [];
        const expectedInterval = this._getExpectedDataInterval();
        
        for (let i = 1; i < sortedData.length; i++) {
            const timeDiff = sortedData[i].created_at - sortedData[i-1].created_at;
            
            // 如果时间间隔超过预期间隔的2倍，认为是间隙
            if (timeDiff > expectedInterval * 2) {
                gaps.push({
                    startTime: sortedData[i-1].created_at,
                    endTime: sortedData[i].created_at,
                    duration: timeDiff,
                    expectedPoints: Math.floor(timeDiff / expectedInterval) - 1
                });
            }
        }
        
        console.log(`检测到 ${gaps.length} 个数据间隙`);
        
        // 如果间隙较少且较小，可以尝试智能填充
        let processedData = sortedData;
        if (gaps.length > 0 && gaps.length < 50) {
            processedData = this._fillSmallDataGaps(sortedData, gaps, expectedInterval);
        }
        
        return {
            hasGaps: gaps.length > 0,
            gapCount: gaps.length,
            processedData: processedData,
            gapInfo: gaps.slice(0, 10), // 只返回前10个间隙信息
            totalGapDuration: gaps.reduce((sum, gap) => sum + gap.duration, 0)
        };
    },

    // 获取预期数据间隔（秒）
    _getExpectedDataInterval() {
        switch (this.currentTimeRange) {
            case '4h':
            case '24h':
                return 60; // 分钟级数据，预期间隔1分钟
            case '7d':
                return 300; // 5分钟级数据，预期间隔5分钟
            case '31d':
                return 86400; // 天级数据，预期间隔1天
            default:
                return 60; // 默认1分钟
        }
    },

    // 填充小的数据间隙
    _fillSmallDataGaps(data, gaps, expectedInterval) {
        console.log('尝试填充小的数据间隙...');
        
        let processedData = [...data];
        let fillCount = 0;
        
        // 只填充较小的间隙（少于10个缺失点）
        const smallGaps = gaps.filter(gap => gap.expectedPoints <= 10);
        
        smallGaps.forEach(gap => {
            // 找到间隙前后的数据点
            const beforeIndex = processedData.findIndex(item => item.created_at === gap.startTime);
            const afterIndex = processedData.findIndex(item => item.created_at === gap.endTime);
            
            if (beforeIndex >= 0 && afterIndex >= 0) {
                const beforeData = processedData[beforeIndex];
                const afterData = processedData[afterIndex];
                
                // 生成填充数据点
                const fillPoints = [];
                for (let i = 1; i <= gap.expectedPoints; i++) {
                    const fillTime = gap.startTime + (i * expectedInterval);
                    
                    // 使用线性插值生成延迟值
                    const ratio = i / (gap.expectedPoints + 1);
                    const interpolatedLatency = beforeData.avg_time + 
                        (afterData.avg_time - beforeData.avg_time) * ratio;
                    
                    fillPoints.push({
                        created_at: fillTime,
                        avg_time: Math.round(interpolatedLatency),
                        min_time: Math.round(interpolatedLatency * 0.8), // 估算最小值
                        max_time: Math.round(interpolatedLatency * 1.2), // 估算最大值
                        target_id: beforeData.target_id,
                        target_name: beforeData.target_name,
                        node_id: beforeData.node_id,
                        success_rate: (beforeData.success_rate + afterData.success_rate) / 2,
                        _filled: true // 标记为填充数据
                    });
                    
                    fillCount++;
                }
                
                // 插入填充数据
                processedData.splice(afterIndex, 0, ...fillPoints);
            }
        });
        
        if (fillCount > 0) {
            console.log(`已填充 ${fillCount} 个数据点，覆盖 ${smallGaps.length} 个小间隙`);
            // 重新排序
            processedData.sort((a, b) => a.created_at - b.created_at);
        }
        
        return processedData;
    },

    // 生成图表系列数据
    _generateSeries(echartsObj) {
        const series = [];
        const colors = this._generateColorList();

        // 如果是单个目标，显示平均延迟和延迟范围
        if (this.currentTargetId !== 'all') {
            const targetData = this.data.targets[this.currentTargetId];
            if (!targetData) return series;

            // 添加平均延迟线
            series.push({
                name: '平均延迟',
                type: 'line',
                sampling: 'lttb', // 使用LTTB采样算法，提高大数据量渲染性能
                symbol: 'circle', // 显示圆形数据点标记
                symbolSize: (value) => {
                    // 超时/失败的数据点使用更大的符号
                    return value === 0 ? 4 : 2;
                },
                showSymbol: false, // 只在hover时显示
                lineStyle: {
                    width: this._getOptimalLineWidth(), // 动态线条宽度
                    // 使用默认颜色，因为ECharts的lineStyle.color回调在某些版本中可能不支持
                    color: '#6366f1'
                },
                itemStyle: {
                    // 根据延迟值动态设置颜色
                    color: (params) => {
                        return this._getLatencyColor(params.value);
                    },
                    borderWidth: 1 // 减小边框宽度
                },
                emphasis: {
                    lineStyle: {
                        width: this._getOptimalLineWidth() + 0.3 // 强调时稍微加粗
                    },
                    itemStyle: {
                        // 强调状态下也使用动态颜色，但稍微加深
                        color: (params) => {
                            const baseColor = this._getLatencyColor(params.value);
                            // 将颜色稍微加深用于强调状态
                            if (baseColor === '#dc2626') return '#b91c1c'; // 超时/失败
                            if (baseColor === '#10b981') return '#059669'; // 优秀
                            if (baseColor === '#34d399') return '#10b981'; // 良好
                            if (baseColor === '#6366f1') return '#4f46e5'; // 一般
                            if (baseColor === '#f59e0b') return '#d97706'; // 较差
                            if (baseColor === '#ef4444') return '#dc2626'; // 很差
                            return baseColor;
                        },
                        borderColor: (params) => {
                            return this._getLatencyColor(params.value);
                        },
                        borderWidth: 1, // 减小边框宽度
                        symbolSize: (value) => {
                            // 超时/失败的数据点使用更大的符号
                            return value === 0 ? 6 : 4;
                        }
                    }
                },
                data: targetData.latency,
                smooth: true, // 使用平滑曲线
                connectNulls: true, // 连接空值点，解决线条不连贯问题
                z: 3 // 确保主线在最上层
            });

            // 添加最小延迟区域
            series.push({
                name: '延迟范围',
                type: 'line',
                symbol: 'none',
                stack: 'range',
                sampling: 'lttb',
                data: targetData.min,
                lineStyle: {
                    opacity: 0
                },
                areaStyle: {
                    color: new echartsObj.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgba(16, 185, 129, 0.35)' // 绿色半透明，增强对比度
                        },
                        {
                            offset: 1,
                            color: 'rgba(16, 185, 129, 0.1)'
                        }
                    ])
                },
                stack: 'confidence-band',
                z: 1
            });

            // 添加最大延迟区域
            series.push({
                name: '延迟范围',
                type: 'line',
                symbol: 'none',
                sampling: 'lttb',
                data: targetData.max.map((val, idx) => {
                    return val - targetData.min[idx];
                }),
                lineStyle: {
                    opacity: 0
                },
                areaStyle: {
                    color: new echartsObj.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgba(245, 158, 11, 0.4)' // 橙色半透明，增强对比度
                        },
                        {
                            offset: 1,
                            color: 'rgba(245, 158, 11, 0.15)'
                        }
                    ])
                },
                stack: 'confidence-band',
                z: 0
            });
        } else {
            // 全部目标模式：简化实现，只显示平均延迟线
            console.log('使用简化的全部目标渲染模式');

            // 性能优化：检查是否启用性能模式
            const performanceConfig = this.performanceConfig || {
                maxSeriesCount: Infinity,
                enableSampling: false,
                reducedAnimations: false,
                simplifiedTooltip: false
            };

            // 获取目标列表
            const targets = Object.values(this.data.targets);
            
            // 性能优化：限制目标数量
            let targetCount = targets.length;
            if (performanceConfig.maxSeriesCount < Infinity) {
                const maxTargets = performanceConfig.maxSeriesCount; // 每个目标只有1个系列
                if (targetCount > maxTargets) {
                    console.warn(`性能优化: 限制目标数量从${targetCount}个减少到${maxTargets}个`);
                    targetCount = maxTargets;
                }
            }

            // 遍历目标，只生成平均延迟线
            targets.slice(0, targetCount).forEach((target, index) => {
                const color = colors[index % colors.length];

                // 检查目标是否全为超时数据
                const isAllTimeout = target.latency.every(value => {
                    return value === 0 || value === null || value === undefined;
                });

                // 添加平均延迟线（统一处理超时和正常数据）
                series.push({
                    name: `${target.name} - 平均延迟`,
                    type: 'line',
                    sampling: performanceConfig.enableSampling ? 'lttb' : 'none',
                    symbol: 'circle',
                    symbolSize: (value) => {
                        // 超时/失败的数据点使用更大的符号
                        return (value === 0 || value === null) ? 4 : 2;
                    },
                    showSymbol: false, // 默认不显示符号，保持简洁
                    lineStyle: {
                        width: this._getOptimalLineWidth(),
                        color: color, // 使用目标固有颜色，便于区分
                        type: isAllTimeout ? 'dashed' : 'solid', // 超时数据使用虚线
                        opacity: this._getOptimalLineOpacity(targetCount) // 动态透明度
                    },
                    itemStyle: {
                        // 根据数据值动态设置颜色
                        color: (params) => {
                            // 如果是超时数据，使用超时颜色
                            if (params.value === 0 || params.value === null) {
                                return this._getLatencyColor(0);
                            }
                            // 正常数据使用延迟颜色
                            return this._getLatencyColor(params.value);
                        },
                        borderWidth: 1,
                        borderColor: (params) => {
                            if (params.value === 0 || params.value === null) {
                                return this._getLatencyColor(0);
                            }
                            return this._getLatencyColor(params.value);
                        }
                    },
                    emphasis: {
                        lineStyle: {
                            width: this._getOptimalLineWidth() + 0.5,
                            opacity: 1.0 // 强调时完全不透明
                        },
                        itemStyle: {
                            symbolSize: (value) => {
                                return (value === 0 || value === null) ? 6 : 4;
                            }
                        }
                    },
                    data: target.latency.map(value => {
                        // 确保数据有效性
                        if (value === null || value === undefined || isNaN(value)) {
                            return null;
                        }
                        return value;
                    }),
                    smooth: !isAllTimeout && !performanceConfig.reducedAnimations, // 超时数据不使用平滑
                    connectNulls: true, // 连接空值点，确保线条连贯
                    z: 3,
                    animation: !performanceConfig.reducedAnimations
                });
            });

            console.log(`简化模式：生成了${targetCount}个目标的平均延迟线，总计${targetCount}个系列`);
            
            // 性能优化提示
            if (targetCount < targets.length) {
                console.warn(`性能优化: 仅显示前${targetCount}个目标，共${targets.length}个目标可用`);
            }
        }

        return series;
    },

    // 渲染图表
    _renderChart() {
        console.log('开始渲染网络质量监控图表...');

        // 获取图表容器
        const container = document.getElementById(this.chartContainerId);
        if (!container) {
            console.error(`找不到图表容器: ${this.chartContainerId}`);
            
            // 使用统一的错误处理器
            if (window.ChartErrorHandler) {
                const errorContainer = document.body; // 使用body作为临时容器
                window.ChartErrorHandler.showErrorMessage(
                    errorContainer,
                    '图表容器不存在',
                    window.ChartErrorHandler.errorTypes.RENDER,
                    { showRetryButton: true }
                );
            }
            return;
        }

        // 确保echarts可用
        if (typeof echarts === 'undefined' && typeof window.echarts === 'undefined') {
            console.error('ECharts库未加载，无法渲染图表');
            
            // 使用统一的错误处理器
            if (window.ChartErrorHandler) {
                window.ChartErrorHandler.showLoadingError(container, 'ECharts库');
            } else {
                container.innerHTML = `
                    <div class="flex items-center justify-center h-full min-h-[200px]">
                        <div class="text-center">
                            <div class="text-red-500 mb-2">ECharts库未加载</div>
                            <button onclick="location.reload()" class="px-4 py-2 bg-blue-500 text-white rounded">
                                重新加载
                            </button>
                        </div>
                    </div>
                `;
            }
            return;
        }

        // 使用正确的echarts对象
        const echartsObj = typeof echarts !== 'undefined' ? echarts : window.echarts;

        // 如果图表已存在，销毁它
        if (this.chart) {
            this.chart.dispose();
        }

        try {
            // 初始化图表
            this.chart = echartsObj.init(container);

            // 获取当前主题
            const isDarkMode = document.documentElement.classList.contains('dark');
            const textColor = isDarkMode ? '#f1f5f9' : '#1e293b';
            // 定义网格线颜色（用于splitLine）
            const splitLineColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)';
            // 定义坐标轴线颜色
            const axisLineColor = isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)';

            // 图表配置
            const option = {
                title: {
                    show: false // 隐藏标题
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: isDarkMode ? 'rgba(17, 24, 39, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                    borderColor: isDarkMode ? '#374151' : '#e2e8f0',
                    textStyle: {
                        color: textColor
                    },
                    formatter: (params) => {
                        // 确保params是数组
                        if (!Array.isArray(params)) {
                            params = [params];
                        }

                        // 获取时间点数据
                        const time = params[0].name;
                        // 获取时间戳（从回调参数中获取）
                        const dataIndex = params[0].dataIndex;
                        const timestamps = this.data.originalTimestamps || [];
                        const timestamp = timestamps[dataIndex];

                        // 构建tooltip内容
                        let tooltipTitle = time;

                        // 对于7d和custom时间范围，添加详细时间
                        if ((this.currentTimeRange === '7d' || this.currentTimeRange === 'custom') && timestamp) {
                            const date = new Date(timestamp * 1000);
                            tooltipTitle = `${time} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                        }

                        let tooltipContent = `<div style="font-weight: bold; margin-bottom: 4px;">时间: ${tooltipTitle}</div>`;

                        // 如果是单个目标
                        if (this.currentTargetId !== 'all') {
                            // 直接获取真实延迟值
                            const avgValue = params[0].value;
                            const minValue = params[1] ? params[1].value : 'N/A';
                            const maxValue = minValue !== 'N/A' ? minValue + (params[2] ? params[2].value : 0) : 'N/A';

                            // 检查是否为超时/连接失败
                            if (this._isTimeout(avgValue)) {
                                tooltipContent += `
                                    <div style="display: flex; align-items: center; margin: 3px 0;">
                                        <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: #ef4444; margin-right: 5px;"></span>
                                        <span style="color: #ef4444; font-weight: bold;">状态: 连接超时/失败</span>
                                    </div>
                                    <div style="color: #64748b; font-size: 0.9em; margin-top: 2px;">该时间点的连接尝试失败</div>
                                `;
                            } else {
                                // 直接使用真实延迟值
                                const actualAvgValue = Math.round(avgValue);
                                const actualMinValue = minValue !== 'N/A' ? Math.round(minValue) : 'N/A';
                                const actualMaxValue = maxValue !== 'N/A' ? Math.round(maxValue) : 'N/A';

                                // 获取延迟等级和颜色
                                const avgLatencyLevel = this._getLatencyLevel(avgValue);
                                const minLatencyLevel = minValue !== 'N/A' ? this._getLatencyLevel(minValue) : { color: '#6b7280' };
                                const maxLatencyLevel = maxValue !== 'N/A' ? this._getLatencyLevel(maxValue) : { color: '#6b7280' };

                                tooltipContent += `
                                    <div style="display: flex; align-items: center; margin: 3px 0;">
                                        <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: ${avgLatencyLevel.color}; margin-right: 5px;"></span>
                                        <span>平均延迟: <span style="color: ${avgLatencyLevel.color}; font-weight: bold;">${actualAvgValue} ms</span> <span style="color: #64748b; font-size: 0.85em;">(${avgLatencyLevel.level})</span></span>
                                    </div>
                                    <div style="display: flex; align-items: center; margin: 3px 0;">
                                        <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: ${minLatencyLevel.color}; margin-right: 5px;"></span>
                                        <span>最小延迟: <span style="color: ${minLatencyLevel.color};">${actualMinValue} ms</span></span>
                                    </div>
                                    <div style="display: flex; align-items: center; margin: 3px 0;">
                                        <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: ${maxLatencyLevel.color}; margin-right: 5px;"></span>
                                        <span>最大延迟: <span style="color: ${maxLatencyLevel.color};">${actualMaxValue} ms</span></span>
                                    </div>
                                    <div style="color: #64748b; font-size: 0.9em; margin-top: 2px;">波动范围: ${actualMaxValue !== 'N/A' && actualMinValue !== 'N/A' ? (actualMaxValue - actualMinValue) : 'N/A'} ms</div>
                                `;
                            }
                        } else {
                            // 如果是全部目标，只显示平均延迟线的数据，过滤掉区域系列
                            const threshold = 500; // 统一使用固定阈值
                            
                            // 过滤出只包含"平均延迟"的系列
                            const avgLatencyParams = params.filter(param => 
                                param.seriesName && param.seriesName.includes('平均延迟')
                            );
                            
                            avgLatencyParams.forEach(param => {
                                if (param.value !== null && param.value !== undefined) {
                                    // 提取目标名称（去掉" - 平均延迟"后缀）
                                    const targetName = param.seriesName.replace(' - 平均延迟', '');
                                    
                                    // 获取当前时间点的最小/最大延迟数据
                                    // 首先尝试通过ID查找，然后通过名称查找
                                    let targetData = this.data.targets[targetName];
                                    if (!targetData) {
                                        targetData = Object.values(this.data.targets).find(t => t.name === targetName || t.id === targetName);
                                    }
                                    const minValue = targetData ? targetData.min[dataIndex] : null;
                                    const maxValue = targetData ? targetData.max[dataIndex] : null;

                                    // 直接使用真实延迟值，不需要解压
                                    const actualAvgValue = Math.round(param.value);
                                    const actualMinValue = minValue !== null ? Math.round(minValue) : null;
                                    const actualMaxValue = maxValue !== null ? Math.round(maxValue) : null;

                                    // 检查是否为超时/连接失败
                                    if (this._isTimeout(param.value)) {
                                        const timeoutColor = this._getLatencyColor(0);
                                        tooltipContent += `
                                            <div style="display: flex; align-items: center; margin: 3px 0;">
                                                <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: ${timeoutColor}; margin-right: 5px;"></span>
                                                <span>${targetName}: <span style="color: ${timeoutColor}; font-weight: bold;">连接超时/失败</span></span>
                                            </div>
                                        `;
                                    } else {
                                        // 获取延迟等级和颜色
                                        const avgLatencyLevel = this._getLatencyLevel(param.value);
                                        const minLatencyLevel = minValue !== null ? this._getLatencyLevel(minValue) : { color: '#6b7280' };
                                        const maxLatencyLevel = maxValue !== null ? this._getLatencyLevel(maxValue) : { color: '#6b7280' };
                                        
                                        // 统一使用延迟颜色作为指示器颜色，保持一致性
                                        const indicatorColor = avgLatencyLevel.color;
                                        let textColor = '';
                                        
                                        // 如果延迟较高（橙色或红色），使用加粗显示
                                        if (avgLatencyLevel.color === '#f59e0b' || avgLatencyLevel.color === '#ef4444') {
                                            textColor = `color: ${avgLatencyLevel.color}; font-weight: bold;`;
                                        }

                                        // 计算波动范围
                                        const fluctuation = (actualMaxValue !== null && actualMinValue !== null) ? 
                                            (actualMaxValue - actualMinValue) : 'N/A';

                                        // 紧凑的单行显示格式
                                        tooltipContent += `
                                            <div style="display: flex; align-items: center; margin: 2px 0; font-size: 0.9em;">
                                                <span style="display: inline-block; width: 6px; height: 6px; border-radius: 50%; background-color: ${indicatorColor}; margin-right: 4px; flex-shrink: 0;"></span>
                                                <span style="font-weight: bold; margin-right: 6px; min-width: 60px; flex-shrink: 0;">${targetName}:</span>
                                                <span style="${textColor}">${actualAvgValue}ms</span>
                                                <span style="color: #64748b; margin: 0 4px; font-size: 0.85em;">(${avgLatencyLevel.level})</span>
                                                <span style="color: #64748b; margin-left: 4px; font-size: 0.85em;">
                                                    [<span style="color: ${minLatencyLevel.color};">${actualMinValue || 'N/A'}</span>-<span style="color: ${maxLatencyLevel.color};">${actualMaxValue || 'N/A'}</span>] 
                                                    ±${fluctuation}ms
                                                </span>
                                            </div>
                                        `;
                                    }
                                }
                            });
                            
                            // 如果没有找到平均延迟数据，显示提示信息
                            if (avgLatencyParams.length === 0) {
                                tooltipContent += `
                                    <div style="color: #64748b; font-size: 0.9em; margin-top: 2px;">
                                        该时间点暂无延迟数据
                                    </div>
                                `;
                            }
                        }

                        return tooltipContent;
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%', // 增加底部边距，给X轴标签和dataZoom留出更多空间
                    top: '15%',    // 增加顶部边距，给图例留出更多空间
                    containLabel: true
                },
                // 添加图例配置
                legend: {
                    show: true,
                    top: 0,
                    left: 'center',
                    itemWidth: 25,
                    itemHeight: 14,
                    textStyle: {
                        color: textColor,
                        fontSize: 12
                    },
                    itemStyle: {
                        opacity: 1
                    },
                    // 动态生成图例数据
                    data: this._generateLegendData()
                },
                toolbox: {
                    show: true,
                    feature: {
                        dataZoom: {
                            show: true,
                            title: {
                                zoom: '区域缩放',
                                back: '缩放还原'
                            },
                            yAxisIndex: 'none' // 只缩放x轴
                        },
                        saveAsImage: {
                            show: true,
                            title: '保存为图片',
                            type: 'png'
                        }
                    },
                    right: 10,
                    top: 10
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: this.data.labels,
                    axisLine: {
                        lineStyle: {
                            color: axisLineColor
                        }
                    },
                    axisLabel: {
                        color: textColor,
                        interval: (index) => {
                            // 获取数据长度和颗粒度信息
                            const dataLength = this.data.labels.length;
                            const granularity = this._getCurrentGranularity();
                            
                            // 智能标签间隔算法
                            return this._calculateLabelInterval(index, dataLength, granularity);
                        },
                        rotate: 0, // Change from 45 to 0 for horizontal labels
                        margin: 16, // 增加标签与轴线的距离
                        fontSize: 11 // 字体大小
                    },
                    splitLine: {
                        show: false
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '延迟 (ms)',
                    nameTextStyle: {
                        color: textColor
                    },
                    // 统一的Y轴范围设置：从0开始，顶部留边距
                    min: 0, // 所有图表都从0开始
                    max: (value) => {
                        // 顶部留20%空间，确保图表不贴顶
                        const maxValue = value.max || 100; // 如果没有数据，默认最大值100
                        return Math.max(maxValue * 1.2, 50); // 至少50ms的显示范围
                    },
                    axisLine: {
                        lineStyle: {
                            color: axisLineColor
                        }
                    },
                    axisLabel: {
                        color: textColor,
                        formatter: (value) => {
                            // 直接显示真实延迟值，不使用压缩
                            if (value === 0) {
                                return '0 ms';
                            }
                            return Math.round(value) + ' ms';
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: splitLineColor
                        }
                    },
                    // Y轴指针标签也显示真实值
                    axisPointer: {
                        label: {
                            formatter: (params) => {
                                if (params.value === 0) {
                                    return '连接超时/失败';
                                }
                                if (params.value === null || params.value === undefined) {
                                    return 'N/A';
                                }
                                const value = Math.round(params.value);
                                if (value > 500) {
                                    return value + ' ms (高延迟)';
                                }
                                return value + ' ms';
                            }
                        }
                    },
                    // 显示高延迟阈值标记线（真实值500ms）
                    markLine: {
                        silent: true,
                        lineStyle: {
                            color: '#f59e0b',
                            type: 'dashed',
                            width: 1
                        },
                        data: [
                            {
                                yAxis: 500, // 真实的500ms阈值
                                label: {
                                    formatter: '高延迟阈值 (500ms)',
                                    position: 'insideEndTop',
                                    fontSize: 10,
                                    color: '#f59e0b'
                                }
                            }
                        ]
                    }
                },
                series: this._generateSeries(echartsObj),
                // 启用缩放功能
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100,
                        filterMode: 'filter',
                        zoomOnMouseWheel: false, // 默认禁用鼠标滚轮缩放
                        moveOnMouseMove: false, // 默认禁用鼠标拖动平移
                        preventDefaultMouseMove: false // 允许默认的鼠标移动行为，包括页面滚动
                    },
                    {
                        type: 'slider',
                        show: false, // 默认隐藏滑块控件
                        start: 0,
                        end: 100,
                        height: 30, // 增加高度
                        bottom: 5, // 增加底部间距
                        borderColor: 'transparent',
                        backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                        fillerColor: isDarkMode ? 'rgba(99, 102, 241, 0.2)' : 'rgba(99, 102, 241, 0.1)',
                        handleStyle: {
                            color: '#6366f1',
                            borderColor: '#6366f1'
                        },
                        textStyle: {
                            color: textColor
                        },
                        handleSize: '80%'
                    }
                ]
            };

            // 设置配置
            this.chart.setOption(option);

            // 用于跟踪滑块自动隐藏的计时器
            this.sliderHideTimer = null;

            // 监听dataZoom事件，显示滑块控件
            this.chart.on('dataZoom', () => {
                // 显示滑块控件
                this.chart.setOption({
                    dataZoom: [
                        {
                            // 内部dataZoom保持不变
                        },
                        {
                            type: 'slider',
                            show: true // 显示滑块控件
                        }
                    ]
                });

                // 重置自动隐藏计时器
                this._resetSliderHideTimer();
            });

            // 监听restore事件，隐藏滑块控件
            this.chart.on('restore', () => {
                console.log('图表还原，隐藏滑块控件');
                // 隐藏滑块控件
                this.chart.setOption({
                    dataZoom: [
                        {
                            // 内部dataZoom保持不变
                        },
                        {
                            type: 'slider',
                            show: false // 隐藏滑块控件
                        }
                    ]
                });
            });

            // 监听图表的鼠标移动事件，重置自动隐藏计时器
            this.chart.getZr().on('mousemove', () => {
                // 检查滑块是否可见
                const option = this.chart.getOption();
                if (option && option.dataZoom && option.dataZoom[1] && option.dataZoom[1].show) {
                    // 重置自动隐藏计时器
                    this._resetSliderHideTimer();
                }
            });

            // 添加双击图表事件，在双击时隐藏滑块控件
            this.chart.getZr().on('dblclick', () => {
                console.log('双击图表，隐藏滑块控件');
                // 隐藏滑块控件
                this.chart.setOption({
                    dataZoom: [
                        {
                            // 内部dataZoom保持不变
                        },
                        {
                            type: 'slider',
                            show: false // 隐藏滑块控件
                        }
                    ]
                });
            });

            // 添加鼠标滚轮事件处理器，确保在图表内可以滚动页面
            container.addEventListener('wheel', (event) => {
                // 阻止事件冒泡，允许页面滚动
                event.stopPropagation();

                // 手动触发页面滚动
                window.scrollBy({
                    top: event.deltaY,
                    behavior: 'auto'
                });
            }, { capture: true });

            console.log('渲染网络质量监控图表成功');
        } catch (error) {
            console.error('渲染图表时出错:', error);
            
            // 使用统一的错误处理器
            if (window.ChartErrorHandler) {
                window.ChartErrorHandler.handleError(error, '图表渲染', container, {
                    showRetryButton: true,
                    retryCallback: () => this._renderChart()
                });
            } else {
                // 降级到简单错误处理
                container.innerHTML = `
                    <div class="flex items-center justify-center h-full min-h-[200px]">
                        <div class="text-center">
                            <div class="text-red-500 mb-2">图表渲染失败</div>
                            <button onclick="location.reload()" class="px-4 py-2 bg-blue-500 text-white rounded">
                                重新加载
                            </button>
                        </div>
                    </div>
                `;
            }
        }
    },

    // 重置滑块自动隐藏计时器
    _resetSliderHideTimer() {
        // 清除现有计时器
        if (this.sliderHideTimer) {
            clearTimeout(this.sliderHideTimer);
            this.sliderHideTimer = null;
        }

        // 设置新的计时器，10秒后自动隐藏滑块
        this.sliderHideTimer = setTimeout(() => {
            // 隐藏滑块控件
            if (this.chart) {
                this.chart.setOption({
                    dataZoom: [
                        {
                            // 内部dataZoom保持不变
                        },
                        {
                            type: 'slider',
                            show: false // 隐藏滑块控件
                        }
                    ]
                });
            }
            this.sliderHideTimer = null;
        }, 10000); // 10秒 = 10000毫秒
    },

    // 数据采样优化（用于高密度数据）
    _optimizeHighDensityData(data, targetSampleCount = 2000) {
        if (!data || data.length <= targetSampleCount) {
            return data;
        }

        console.log(`开始数据采样优化: ${data.length} -> ${targetSampleCount}`);
        
        // 按时间排序
        const sortedData = [...data].sort((a, b) => a.created_at - b.created_at);
        
        // 计算采样间隔
        const sampleInterval = Math.ceil(sortedData.length / targetSampleCount);
        
        // 使用LTTB算法的简化版本进行采样
        const sampledData = [];
        
        // 始终保留第一个和最后一个数据点
        sampledData.push(sortedData[0]);
        
        // 中间数据点采样
        for (let i = sampleInterval; i < sortedData.length - sampleInterval; i += sampleInterval) {
            // 在采样窗口内选择最具代表性的点
            const windowStart = Math.max(0, i - Math.floor(sampleInterval / 2));
            const windowEnd = Math.min(sortedData.length, i + Math.floor(sampleInterval / 2));
            const window = sortedData.slice(windowStart, windowEnd);
            
            // 选择延迟值最接近窗口平均值的点
            const avgLatency = window.reduce((sum, item) => sum + (item.avg_time || 0), 0) / window.length;
            const representative = window.reduce((best, current) => {
                const bestDiff = Math.abs((best.avg_time || 0) - avgLatency);
                const currentDiff = Math.abs((current.avg_time || 0) - avgLatency);
                return currentDiff < bestDiff ? current : best;
            });
            
            sampledData.push(representative);
        }
        
        // 始终保留最后一个数据点
        if (sortedData.length > 1) {
            sampledData.push(sortedData[sortedData.length - 1]);
        }
        
        console.log(`数据采样完成: ${data.length} -> ${sampledData.length}`);
        return sampledData;
    },

    /**
     * 设置活跃的时间范围
     */
    _setActiveTimeRange(timeRange) {
        // 更新按钮UI
        document.querySelectorAll('.time-range-selector').forEach(btn => {
            btn.classList.remove('bg-blue-50', 'text-blue-600', 'dark:bg-slate-700', 'dark:text-white');
            btn.classList.add('bg-gray-50', 'text-gray-600', 'dark:bg-slate-800', 'dark:text-slate-300');
        });

        const activeButton = document.querySelector(`[data-range="${timeRange}"]`);
        if (activeButton) {
            activeButton.classList.remove('bg-gray-50', 'text-gray-600', 'dark:bg-slate-800', 'dark:text-slate-300');
            activeButton.classList.add('bg-blue-50', 'text-blue-600', 'dark:bg-slate-700', 'dark:text-white');
        }

        // 如果选择了自定义时间范围，显示自定义时间选择器
        const customSelector = document.getElementById('custom-time-range-selector');
        if (timeRange === 'custom') {
            if (customSelector) {
                customSelector.classList.remove('hidden');
                customSelector.classList.add('flex');

                // 默认设置时间范围为当前时间前24小时到当前时间
                const now = new Date();
                const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

                // 格式化为HTML datetime-local输入框所需的格式：YYYY-MM-DDThh:mm
                const formatDate = (date) => {
                    return date.toISOString().slice(0, 16);
                };

                const startTimeInput = document.getElementById('custom-start-time');
                const endTimeInput = document.getElementById('custom-end-time');

                if (startTimeInput && !startTimeInput.value) {
                    startTimeInput.value = formatDate(yesterday);
                }

                if (endTimeInput && !endTimeInput.value) {
                    endTimeInput.value = formatDate(now);
                }
            }
        } else {
            // 隐藏自定义时间选择器
            if (customSelector) {
                customSelector.classList.add('hidden');
                customSelector.classList.remove('flex');
            }
            this._switchTimeRange(timeRange);
        }
    },

    /**
     * 更新颗粒度提示信息
     */
    _updateGranularityHint() {
        const granularitySelect = document.getElementById('data-granularity');
        const hintElement = document.getElementById('granularity-hint');
        
        if (!granularitySelect || !hintElement) return;
        
        const granularity = granularitySelect.value;
        const hints = {
            'hour': '快速加载，约2K数据点',
            '5min': '详细监控，约77K数据点',
            'minute': '最详细分析，约365K数据点'
        };
        
        hintElement.textContent = hints[granularity] || '';
    },

    /**
     * 构建API URL
     */
    _buildApiUrl(timeRange, targetId = null, nodeId = null) {
        let url = `/api/monitor/tcping?time_range=${timeRange}`;
        
        if (targetId) {
            url += `&target_id=${targetId}`;
        }
        
        if (nodeId) {
            url += `&node_id=${nodeId}`;
        }
        
        // 为7天时间范围添加颗粒度参数
        if (timeRange === '7d') {
            const granularitySelect = document.getElementById('data-granularity');
            const granularity = granularitySelect ? granularitySelect.value : '5min';
            url += `&granularity=${granularity}`;
        }
        
        return url;
    },

    /**
     * 获取当前颗粒度
     */
    _getCurrentGranularity() {
        const granularitySelect = document.getElementById('data-granularity');
        return granularitySelect ? granularitySelect.value : '5min';
    },

    /**
     * 计算标签间隔
     */
    _calculateLabelInterval(index, dataLength, granularity) {
        // 始终显示首尾标签
        if (index === 0 || index === dataLength - 1) {
            return true;
        }
        
        // 根据数据量和颗粒度计算最优间隔
        let targetLabelCount;
        
        if (this.currentTimeRange === '7d') {
            // 7天数据根据颗粒度调整
            switch (granularity) {
                case 'hour':
                    targetLabelCount = 8; // 约每21小时一个标签
                    break;
                case '5min':
                    targetLabelCount = 12; // 约每14小时一个标签
                    break;
                case 'minute':
                    targetLabelCount = 14; // 约每12小时一个标签
                    break;
                default:
                    targetLabelCount = 8;
            }
        } else if (this.currentTimeRange === '24h') {
            targetLabelCount = 6; // 约每4小时一个标签
        } else if (this.currentTimeRange === '4h') {
            targetLabelCount = 8; // 约每30分钟一个标签
        } else if (this.currentTimeRange === '31d') {
            targetLabelCount = 8; // 约每4天一个标签
        } else {
            // 自定义时间范围，根据数据量自适应
            if (dataLength <= 50) targetLabelCount = 8;
            else if (dataLength <= 200) targetLabelCount = 10;
            else if (dataLength <= 1000) targetLabelCount = 12;
            else targetLabelCount = 15;
        }
        
        // 计算间隔
        const interval = Math.max(1, Math.floor(dataLength / targetLabelCount));
        
        // 对于特定时间范围，优先显示整点时间
        if (this.currentTimeRange === '24h' || this.currentTimeRange === '7d') {
            const label = this.data.labels[index];
            if (label && label.includes(':')) {
                // 优先显示整点时间
                if (label.endsWith(':00')) {
                    return index % Math.max(1, Math.floor(interval / 2)) === 0;
                }
                // 或者显示半点时间（如果间隔较小）
                if (interval <= 6 && label.endsWith(':30')) {
                    return index % interval === 0;
                }
            }
        }
        
        return index % interval === 0;
    },

    // 获取动态线条宽度
    _getOptimalLineWidth() {
        const granularity = this._getCurrentGranularity();
        const dataLength = this.data.labels ? this.data.labels.length : 0;
        const targetCount = this.currentTargetId === 'all' ? Object.keys(this.data.targets || {}).length : 1;
        
        // 基础线条宽度
        let baseWidth = 1.5;
        
        // 根据颗粒度调整
        if (granularity === 'minute') {
            baseWidth = 0.8; // 分钟级数据使用更细的线条
        } else if (granularity === '5min') {
            baseWidth = 1.0; // 5分钟级数据使用中等线条
        } else if (granularity === 'hour') {
            baseWidth = 1.5; // 小时级数据使用较粗的线条
        }
        
        // 根据目标数量调整（多目标时使用更细的线条避免重叠）
        if (targetCount > 5) {
            baseWidth *= 0.7;
        } else if (targetCount > 3) {
            baseWidth *= 0.85;
        }
        
        // 根据数据密度调整
        if (dataLength > 10000) {
            baseWidth *= 0.8; // 高密度数据使用更细的线条
        } else if (dataLength > 5000) {
            baseWidth *= 0.9;
        }
        
        return Math.max(0.5, Math.min(2.5, baseWidth)); // 限制在0.5-2.5之间
    },

    // 获取动态线条透明度
    _getOptimalLineOpacity(targetCount) {
        // 根据目标数量调整透明度
        if (targetCount > 5) {
            return 0.7; // 对于多目标，使用较低的透明度
        } else if (targetCount > 3) {
            return 0.85; // 对于中等数量目标，使用中等透明度
        } else {
            return 1.0; // 对于单个目标，使用完全不透明
        }
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('页面加载完成，准备初始化NetworkQualityUnifiedManager...');

    // 检查是否在stat.html页面
    const isStatPage = location.pathname.match(/\/stats\/([^\/]+)/) || location.pathname === '/stat';
    if (!isStatPage) {
        console.log('不在stat.html页面，跳过初始化');
        return;
    }

    // 检查必要的DOM元素是否存在
    const chartContainer = document.getElementById('network-quality-chart');
    if (!chartContainer) {
        console.warn('找不到图表容器，可能不在正确的页面');
        return;
    }

    // 使用EChartsLoader确保ECharts已加载
    if (window.EChartsLoader) {
        window.EChartsLoader.ensureLoaded(() => {
            console.log('ECharts加载完成，开始初始化图表管理器...');
            
            // 使用ContainerManager确保容器尺寸合适
            if (window.ContainerManager) {
                window.ContainerManager.ensureContainerSize(chartContainer, 300, 400)
                    .then(() => {
                        console.log('容器尺寸已就绪，开始初始化NetworkQualityUnifiedManager');
                        NetworkQualityUnifiedManager.init();
                    })
                    .catch((error) => {
                        console.error('容器尺寸优化失败:', error);
                        // 即使容器尺寸优化失败，也尝试初始化
                        NetworkQualityUnifiedManager.init();
                    });
            } else {
                console.warn('ContainerManager未加载，直接初始化');
                NetworkQualityUnifiedManager.init();
            }
        });
    } else {
        console.warn('EChartsLoader未加载，使用原有逻辑');
        // 降级到原有的ECharts检查逻辑
        if (typeof echarts === 'undefined' && typeof window.echarts === 'undefined') {
            console.error('ECharts未加载，无法初始化NetworkQualityUnifiedManager');
            return;
        }
        
        // 初始化NetworkQualityUnifiedManager
        NetworkQualityUnifiedManager.init();
    }
});
