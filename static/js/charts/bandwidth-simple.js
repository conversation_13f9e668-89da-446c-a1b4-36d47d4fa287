// 极简带宽监控，仅依赖ECharts和原生JS
(function() {
    // 队列最大长度（10分钟，每2秒1点）
    const MAX_POINTS = 300;
    let inData = [], outData = [], labels = [];
    let timestamps = []; // 🔧 存储时间戳数据，用于24小时时间轴
    let chart = null;
    let currentRange = '10min';
    let statSubId = 'bandwidth-simple-realtime';
    let isRealtimeSubscribed = false;
    let currentTheme = null;
    let apiErrorMessage = null; // 存储API错误消息
    let isInitialLoading = true; // 初始加载状态
    let loadingStartTime = Date.now(); // 加载开始时间

    // 获取节点ID
    function getSid() {
        const match = location.pathname.match(/\/stats\/([^\/]+)/);
        return match ? match[1] : null;
    }

    // API URL生成
    function getApiUrl(sid, range) {
        // 统一使用bandwidth/history API，后端会根据范围选择合适的数据源
        switch (range) {
            case '10min':
            case '1h':
            case '24h':
            case '7d':
                return `/api/stats/${sid}/bandwidth/history?range=${range}`;
            case '30d':
                return `/api/stats/${sid}/bandwidth/history?range=${range}`;  // 使用历史数据
            case '60d':
                return `/api/stats/${sid}/bandwidth/history?range=60d`;
            default:
                return `/api/stats/${sid}/bandwidth/history?range=${range}`;
        }
    }

    // 使用共享的图表标签工具 - 旧函数已移除

    // 使用共享的 ChartLabels.ChartLabels.formatFullTime

    function getTheme() {
        // 兼容 tailwind/daisyui/自定义，优先检测 html 或 body 的 class
        if (document.documentElement.classList.contains('dark') || document.body.classList.contains('dark')) {
            return 'dark';
        }
        return 'light';
    }

    // 初始化ECharts
    function getChart(forceRecreate) {
        const chartDom = document.getElementById('bandwidth-realtime-chart');
        if (!chartDom) return null;
        const theme = getTheme();
        if (!chart || forceRecreate || currentTheme !== theme) {
            if (chart) { chart.dispose(); }
            chart = echarts.init(chartDom, theme);
            currentTheme = theme;
        }
        return chart;
    }

    // 检查数据是否有效 - 修复：更宽松的数据验证
    function hasValidBandwidthData() {
        const hasInData = inData.length > 0;
        const hasOutData = outData.length > 0;
        const hasLabels = labels.length > 0;
        const lengthsMatch = inData.length === outData.length && outData.length === labels.length;

        // 检查是否有至少一些有效的数值数据
        const hasValidNumbers = (hasInData && inData.some(v => typeof v === 'number' && isFinite(v) && v >= 0)) ||
                               (hasOutData && outData.some(v => typeof v === 'number' && isFinite(v) && v >= 0));

        const result = hasValidNumbers && hasLabels && lengthsMatch;

        // 调试日志
        console.log(`[带宽图表] 数据验证详情: inData=${inData.length}, outData=${outData.length}, labels=${labels.length}, lengthsMatch=${lengthsMatch}, hasValidNumbers=${hasValidNumbers}, result=${result}`);

        return result;
    }

    // 🔧 [智能提示优化] 获取精确的状态消息
    function getStatusMessage() {
        // 优先显示API错误消息
        if (apiErrorMessage) {
            return apiErrorMessage;
        }

        // 智能加载提示：只在初始加载且超过2秒时显示
        if (isInitialLoading && inData.length === 0 && outData.length === 0) {
            const loadingDuration = Date.now() - loadingStartTime;
            if (loadingDuration > 2000) {
                return '正在加载带宽数据...';
            }
            return null; // 2秒内不显示任何提示
        }

        // 标记加载完成
        if (inData.length > 0 || outData.length > 0) {
            isInitialLoading = false;
        }

        // 只显示真正的无流量状态
        if (inData.length > 0 && outData.length > 0 &&
            inData.every(v => v === 0) && outData.every(v => v === 0)) {
            return '当前时段无网络流量';
        }

        // 其他情况不显示提示，让图表自然显示
        return null;
    }

    // 渲染/更新图表
    function updateChart(title) {
        const c = getChart();
        if (!c) return;

        // 检查是否有有效数据
        const hasData = hasValidBandwidthData();
        console.log(`[带宽图表] 数据状态检查: 下行${inData.length}点, 上行${outData.length}点, 标签${labels.length}个, 有效数据: ${hasData}`);

        // 使用统一配置
        if (window.UnifiedChartConfig) {
            let config;

            if (!hasData) {
                // 空数据状态配置
                config = window.UnifiedChartConfig.getCommonOptions({
                    title: {
                        text: title
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        data: ['00:00', '00:01', '00:02', '00:03', '00:04'] // 示例时间轴
                    },
                    yAxis: window.UnifiedChartConfig.getDynamicYAxis([], {
                        name: 'Mbps'
                    }),
                    tooltip: {
                        formatter: '暂无数据'
                    },
                    legend: {
                        data: ['下行', '上行']
                    },
                    series: [
                        window.UnifiedChartConfig.getCommonSeries('line', {
                            name: '下行',
                            data: [],
                            lineStyle: { color: '#22c55e' },
                            itemStyle: { color: '#22c55e' },
                            dataLength: 0
                        }),
                        window.UnifiedChartConfig.getCommonSeries('line', {
                            name: '上行',
                            data: [],
                            lineStyle: { color: '#2563eb' },
                            itemStyle: { color: '#2563eb' },
                            dataLength: 0
                        })
                    ],
                    graphic: getStatusMessage() ? {
                        type: 'text',
                        left: 'center',
                        top: 'center',
                        style: {
                            text: getStatusMessage(),
                            fill: '#94a3b8',
                            fontSize: 14,
                            fontWeight: 'normal'
                        }
                    } : null
                });
            } else {
                // 正常数据配置
                config = window.UnifiedChartConfig.getCommonOptions({
                    title: { text: title },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: (() => {
                        if (currentRange === '24h') {
                            return {
                                // 🔧 [24小时优化] 使用时间轴，ECharts自动处理整点标签
                                type: 'time',
                                axisLabel: {
                                    formatter: '{HH}:00'  // 显示整点格式
                                }
                            };
                        } else {
                            return {
                                // 其他时间范围使用分类轴
                                data: labels,
                                axisLabel: {
                                    interval: 'auto'
                                },
                                axisTick: {
                                    show: false
                                },
                                axisPointer: {
                                    label: {
                                        formatter: function (obj) {
                                            try {
                                                const idx = (obj.seriesData && obj.seriesData[0]) ? obj.seriesData[0].dataIndex : null;
                                                const ts = (idx != null && window.bandwidthTimestamps) ? window.bandwidthTimestamps[idx] : null;
                                                return ts ? (window.ChartLabels ? ChartLabels.formatFullTime(ts) : new Date(ts).toLocaleString()) : (obj.value || '');
                                            } catch (e) {
                                                return obj.value || '';
                                            }
                                        }
                                    }
                                }
                            };
                        }
                    })(),
                    yAxis: window.UnifiedChartConfig.getDynamicYAxis([inData, outData], {
                        name: 'Mbps'
                    }),
                    tooltip: {
                        formatter: function(params) {
                            if (!params || params.length === 0) return '无数据';

                            // 🔧 [Tooltip时间修复] 处理时间轴的时间显示
                            let timeDisplay;
                            const dataIndex = params[0].dataIndex;

                            if (currentRange === '24h' && params[0] && Array.isArray(params[0].value)) {
                                // 时间轴格式：从数据中获取时间戳并格式化
                                const timestamp = params[0].value[0];
                                timeDisplay = window.ChartLabels ? ChartLabels.formatFullTime(timestamp) : new Date(timestamp).toLocaleString();
                            } else {
                                // 分类轴格式：优先显示完整时间
                                if (window.bandwidthTimestamps && window.bandwidthTimestamps[dataIndex]) {
                                    // 有时间戳数据：显示完整的日期时间
                                    const timestamp = window.bandwidthTimestamps[dataIndex];
                                    timeDisplay = window.ChartLabels ? ChartLabels.formatFullTime(timestamp) : new Date(timestamp).toLocaleString();
                                } else {
                                    // 没有时间戳数据：使用axisValue
                                    timeDisplay = params[0].axisValue || '时间未知';
                                }
                            }

                            let result = timeDisplay + '<br/>';
                            params.forEach(param => {
                                // 🔧 [Tooltip修复] 处理时间轴数据格式 [timestamp, value]
                                let value;
                                if (Array.isArray(param.value)) {
                                    // 时间轴格式: [timestamp, value]
                                    value = param.value[1] === undefined || param.value[1] === null ? 0 : param.value[1];
                                } else {
                                    // 分类轴格式: value
                                    value = param.value === undefined || param.value === null ? 0 : param.value;
                                }
                                result += `${param.marker} ${param.seriesName}: ${value.toFixed(2)} Mbps<br/>`;
                            });
                            return result;
                        }
                    },
                    legend: {
                        data: ['下行', '上行']
                    },
                    series: currentRange === '24h' ? [
                        // 🔧 [24小时优化] 时间轴需要 [timestamp, value] 格式的数据
                        window.UnifiedChartConfig.getCommonSeries('line', { sampling: 'lttb', large: true, progressive: 2000, progressiveThreshold: 3000,
                            name: '下行',
                            data: inData.map((value, index) => {
                                return [timestamps[index] || Date.now(), value];
                            }),
                            lineStyle: { color: '#22c55e' },
                            itemStyle: { color: '#22c55e' },
                            dataLength: inData.length
                        }),
                        window.UnifiedChartConfig.getCommonSeries('line', { sampling: 'lttb', large: true, progressive: 2000, progressiveThreshold: 3000,
                            name: '上行',
                            data: outData.map((value, index) => {
                                return [timestamps[index] || Date.now(), value];
                            }),
                            lineStyle: { color: '#2563eb' },
                            itemStyle: { color: '#2563eb' },
                            dataLength: outData.length
                        })
                    ] : [
                        // 其他时间范围使用普通数据格式
                        window.UnifiedChartConfig.getCommonSeries('line', { sampling: 'lttb', large: true, progressive: 2000, progressiveThreshold: 3000,
                            name: '下行',
                            data: inData,
                            lineStyle: { color: '#22c55e' },
                            itemStyle: { color: '#22c55e' },
                            dataLength: inData.length
                        }),
                        window.UnifiedChartConfig.getCommonSeries('line', { sampling: 'lttb', large: true, progressive: 2000, progressiveThreshold: 3000,
                            name: '上行',
                            data: outData,
                            lineStyle: { color: '#2563eb' },
                            itemStyle: { color: '#2563eb' },
                            dataLength: outData.length
                        })
                    ]
                });
            }

            c.setOption(config);
        } else {
            // 降级处理，使用原有配置
            const isMobile = window.innerWidth <= 600;
            const theme = getTheme();
            c.setOption({
                backgroundColor: 'transparent',
                title: {
                    text: title,
                    left: 'center',
                    textStyle: {
                        fontSize: isMobile ? 12 : 14,
                        color: theme === 'dark' ? '#94a3b8' : '#64748b'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    },
                    formatter: function(params) {
                        if (!params || params.length === 0) return '无数据';

                        // 🔧 [Tooltip时间修复] 处理时间轴的时间显示
                        const dataIndex = params[0].dataIndex;
                        let timeDisplay;

                        if (currentRange === '24h' && params[0] && Array.isArray(params[0].value)) {
                            // 时间轴格式：从数据中获取时间戳并格式化
                            const timestamp = params[0].value[0];
                            timeDisplay = window.ChartLabels ? ChartLabels.formatFullTime(timestamp) : new Date(timestamp).toLocaleString();
                        } else {
                            // 分类轴格式：优先显示完整时间
                            if (window.bandwidthTimestamps && window.bandwidthTimestamps[dataIndex]) {
                                // 有原始timestamps数据，显示完整时间（适用于7天等长时间范围）
                                const timestamp = window.bandwidthTimestamps[dataIndex];
                                timeDisplay = window.ChartLabels ? ChartLabels.formatFullTime(timestamp) : new Date(timestamp).toLocaleString();
                            } else {
                                // 没有原始timestamps，使用axisValue（适用于实时等短时间范围）
                                timeDisplay = params[0].axisValue || '时间未知';
                            }
                        }

                        let result = timeDisplay + '<br/>';
                        params.forEach(param => {
                            // 🔧 [Tooltip修复] 处理时间轴数据格式 [timestamp, value]
                            let value;
                            if (Array.isArray(param.value)) {
                                // 时间轴格式: [timestamp, value]
                                value = param.value[1] === undefined || param.value[1] === null ? 0 : param.value[1];
                            } else {
                                // 分类轴格式: value
                                value = param.value === undefined || param.value === null ? 0 : param.value;
                            }
                            result += `${param.marker} ${param.seriesName}: ${value.toFixed(2)} Mbps<br/>`;
                        });
                        return result;
                    }
                },
                legend: isMobile ?
                    { show: false } :
                    {
                        data: ['下行', '上行'],
                        top: 30,
                        textStyle: {
                            color: theme === 'dark' ? '#94a3b8' : '#64748b'
                        }
                    },
                grid: { left: isMobile ? '2%' : '3%', right: isMobile ? '2%' : '4%', bottom: '3%', top: '15%', containLabel: true },
                xAxis: currentRange === '24h' ? {
                    // 🔧 [24小时优化] 使用时间轴，ECharts自动处理整点标签
                    type: 'time',
                    axisLabel: {
                        fontSize: isMobile ? 9 : 12,
                        color: theme === 'dark' ? '#94a3b8' : '#64748b',
                        formatter: '{HH}:00'  // 显示整点格式
                    },
                    axisLine: {
                        lineStyle: {
                            color: theme === 'dark' ? '#475569' : '#cbd5e1'
                        }
                    },
                    splitLine: {
                        show: false
                    }
                } : {
                    // 其他时间范围使用分类轴
                    type: 'category',
                    data: labels,
                    axisLabel: {
                        fontSize: isMobile ? 9 : 12,
                        color: theme === 'dark' ? '#94a3b8' : '#64748b',
                        rotate: 0,
                        interval: 'auto'
                    },
                    axisTick: {
                        show: false
                    },
                    axisPointer: {
                        label: {
                            formatter: function (obj) {
                                try {
                                    const idx = (obj.seriesData && obj.seriesData[0]) ? obj.seriesData[0].dataIndex : null;
                                    const ts = (idx != null && window.bandwidthTimestamps) ? window.bandwidthTimestamps[idx] : null;
                                    return ts ? (window.ChartLabels ? ChartLabels.formatFullTime(ts) : new Date(ts).toLocaleString()) : (obj.value || '');
                                } catch (e) {
                                    return obj.value || '';
                                }
                            }
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: theme === 'dark' ? '#475569' : '#cbd5e1'
                        }
                    },
                    splitLine: {
                        show: false
                    },
                },
                yAxis: {
                    type: 'value',
                    name: 'Mbps',
                    nameTextStyle: {
                        color: theme === 'dark' ? '#94a3b8' : '#64748b'
                    },
                    axisLabel: {
                        fontSize: isMobile ? 9 : 12,
                        color: theme === 'dark' ? '#94a3b8' : '#64748b'
                    },
                    axisLine: {
                        show: false
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: theme === 'dark' ? '#334155' : '#e2e8f0',
                            type: 'solid',
                            width: 1,
                            opacity: 0.6
                        }
                    },
                    max: function(value) {
                        return Math.ceil(value.max * 1.2); // 为顶部留出20%空间
                    }
                },
                series: currentRange === '24h' ? [
                    // 🔧 [24小时优化] 时间轴需要 [timestamp, value] 格式的数据
                    {
                        name: '下行',
                        type: 'line',
                        data: inData.map((value, index) => {
                            return [timestamps[index] || Date.now(), value];
                        }),
                        smooth: true,
                        symbol: 'none',
                        lineStyle: { color: '#22c55e' },
                        itemStyle: { color: '#22c55e' }
                    },
                    {
                        name: '上行',
                        type: 'line',
                        data: outData.map((value, index) => {
                            return [timestamps[index] || Date.now(), value];
                        }),
                        smooth: true,
                        symbol: 'none',
                        lineStyle: { color: '#2563eb' },
                        itemStyle: { color: '#2563eb' }
                    }
                ] : [
                    // 其他时间范围使用普通数据格式
                    { name: '下行', type: 'line', data: inData, smooth: true, symbol: 'none', lineStyle: { color: '#22c55e' }, itemStyle: { color: '#22c55e' } },
                    { name: '上行', type: 'line', data: outData, smooth: true, symbol: 'none', lineStyle: { color: '#2563eb' }, itemStyle: { color: '#2563eb' } }
                ]
            });
        }
    }

    function getMaxPoints() {
        const chartDom = document.getElementById('bandwidth-realtime-chart');
        const width = chartDom ? chartDom.clientWidth : window.innerWidth || 1200;
        // 动态目标点数：每2px一个点，附加安全系数
        const pxPerPoint = (window.innerWidth <= 600) ? 2.8 : 2.0;
        const safety = 1.2;
        let target = Math.round((width / pxPerPoint) * safety);
        // 限制范围，移动端至少300，桌面端上限1200
        const minPts = (window.innerWidth <= 600) ? 300 : 400;
        target = Math.max(minPts, Math.min(1200, target));
        return target;
    }

    // 新增：设置时间颗粒度
    function setTimeGranularity(seconds) {
        timeGranularity = seconds;
        // 重新处理和显示数据，而不是截断
        processDataWithGranularity();
        updateChart(currentRange === '10min' ? '实时带宽' : '带宽历史');
    }

    // 新增：设置24小时数据的时间颗粒度
    function set24hTimeGranularity(minutes) {
        time24hGranularity = minutes;
        // 重新处理和显示24小时数据
        process24hDataWithGranularity(minutes);
        updateChart('24小时带宽历史');
    }

    // 新增：根据颗粒度处理数据
    function processDataWithGranularity() {
        if (allInData.length === 0 || allOutData.length === 0) {
            return; // 没有数据可处理
        }

        // 如果颗粒度为2秒（默认值），直接使用原始数据
        if (timeGranularity === 2) {
            inData = [...allInData];
            outData = [...allOutData];
            labels = [...allLabels];
            return;
        }

        // 计算需要合并的点数
        const pointsToMerge = Math.floor(timeGranularity / 2);
        if (pointsToMerge <= 1) {
            inData = [...allInData];
            outData = [...allOutData];
            labels = [...allLabels];
            return;
        }

        // 清空现有处理后的数据
        inData = [];
        outData = [];
        labels = [];

        // 按照颗粒度合并数据点
        for (let i = 0; i < allInData.length; i += pointsToMerge) {
            // 取该区间的平均值
            let inSum = 0, outSum = 0;
            let count = 0;

            for (let j = 0; j < pointsToMerge && i + j < allInData.length; j++) {
                inSum += allInData[i + j];
                outSum += allOutData[i + j];
                count++;
            }

            if (count > 0) {
                inData.push(Number((inSum / count).toFixed(2)));
                outData.push(Number((outSum / count).toFixed(2)));
                labels.push(allLabels[i]); // 使用区间起始时间作为标签
            }
        }
    }

    // 处理24小时数据颗粒度（参数单位：分钟）
    function process24hDataWithGranularity(minutes) {
        if (allInData.length === 0 || allOutData.length === 0) {
            return; // 没有数据可处理
        }

        // 如果颗粒度为1分钟（原始数据），直接使用全部数据
        if (minutes === 1) {
            inData = [...allInData];
            outData = [...allOutData];
            labels = [...allLabels];
            return;
        }

        // 清空现有处理后的数据
        inData = [];
        outData = [];
        labels = [];

        // 按分钟间隔合并数据点
        for (let i = 0; i < allInData.length; i += minutes) {
            let inSum = 0, outSum = 0;
            let count = 0;

            for (let j = 0; j < minutes && i + j < allInData.length; j++) {
                inSum += allInData[i + j];
                outSum += allOutData[i + j];
                count++;
            }

            if (count > 0) {
                inData.push(Number((inSum / count).toFixed(2)));
                outData.push(Number((outSum / count).toFixed(2)));
                labels.push(allLabels[i]);
            }
        }
    }

    // 订阅实时数据
    function subscribeRealtime() {
        if (isRealtimeSubscribed || !window.StatManager) return;
        window.StatManager.subscribe(statSubId, function(data) {
            const sid = getSid();
            if (!sid || !data || !data[sid] || !data[sid].stat || !data[sid].stat.net) {
                console.log('[带宽图表] 实时数据无效，跳过更新');
                return;
            }

            const net = data[sid].stat.net;

            // 验证和处理实时数据
            const inBytes = net.delta?.in || 0;
            const outBytes = net.delta?.out || 0;

            // 数据验证
            const inBytesNum = Number(inBytes);
            const outBytesNum = Number(outBytes);

            if (isNaN(inBytesNum) || isNaN(outBytesNum) || !isFinite(inBytesNum) || !isFinite(outBytesNum)) {
                console.warn('[带宽图表] 实时数据包含无效数值，跳过', { inBytes, outBytes });
                return;
            }

            // 转换为Mbps，处理负值
            const inMbps = Number(Math.max(0, (inBytesNum * 8 / 1024 / 1024)).toFixed(2));
            const outMbps = Number(Math.max(0, (outBytesNum * 8 / 1024 / 1024)).toFixed(2));

            const now = new Date();
            const timeLabel = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

            // 添加新数据点
            inData.push(inMbps);
            outData.push(outMbps);
            labels.push(timeLabel);

            // 维护数据点数量
            const maxPoints = getMaxPoints();
            if (inData.length > maxPoints) inData.shift();
            if (outData.length > maxPoints) outData.shift();
            if (labels.length > maxPoints) labels.shift();

            // 调试日志（可选）
            if (window.DEBUG_MODE) {
                console.log(`[带宽图表] 实时更新: 下行${inMbps}Mbps, 上行${outMbps}Mbps, 数据点总数: ${inData.length}`);
            }

            updateChart('实时带宽');
        });
        isRealtimeSubscribed = true;
        console.log('[带宽图表] 已订阅实时数据更新');
    }
    function unsubscribeRealtime() {
        if (window.StatManager && isRealtimeSubscribed) {
            window.StatManager.unsubscribe(statSubId);
            isRealtimeSubscribed = false;
        }
    }

    // 数据验证和过滤函数
    function validateAndFilterData(rawData, fieldName) {
        if (!Array.isArray(rawData)) return [];

        return rawData.map(item => {
            let value = item;
            if (typeof item === 'object' && item !== null && fieldName !== null) {
                value = item[fieldName] || 0;
            }

            // 转换为数字并验证
            const numValue = Number(value || 0);

            // 过滤无效数据：NaN、无限数、负数
            if (isNaN(numValue) || !isFinite(numValue) || numValue < 0) {
                return 0;
            }

            // 转换为Mbps（字节/秒 -> Mbps）
            const mbps = (numValue * 8 / 1024 / 1024);

            // 保留2位小数
            return Number(mbps.toFixed(2));
        });
    }

    // 加载历史数据
    async function loadHistory(range) {
        const sid = getSid();
        if (!sid) {
            console.warn('[带宽图表] 无法获取服务器ID');
            return;
        }

        // 🔧 [智能提示优化] 重置加载状态
        isInitialLoading = true;
        loadingStartTime = Date.now();

        const url = getApiUrl(sid, range);
        console.log(`[带宽图表] 加载历史数据: ${range}, URL: ${url}`);

        try {
            const resp = await fetch(url);
            if (!resp.ok) {
                throw new Error(`HTTP ${resp.status}: ${resp.statusText}`);
            }

            const json = await resp.json();
            if (json.status !== 'success' || !json.data) {
                const errorType = !json.data ? 'NO_DATA' : 'API_ERROR';
                console.warn(`[带宽图表] ${errorType}:`, json);

                // 根据错误类型设置不同的状态
                inData = [];
                outData = [];
                labels = [];

                // 设置错误状态消息
                if (errorType === 'NO_DATA') {
                    apiErrorMessage = '暂无历史数据';
                } else {
                    apiErrorMessage = `API错误: ${json.message || '未知错误'}`;
                }

                updateChart(getTitle(range));
                return;
            }

            // 清除之前的错误消息
            apiErrorMessage = null;

            const data = json.data;
            console.log(`[带宽图表] 原始数据长度:`, Array.isArray(data) ? data.length : 'object');

            // 60天特殊处理：若请求90d，为了性能仅取最近60天（在原始数据层面裁剪）
            if (range === '60d') {
                const sixtyDaysSec = 60 * 24 * 3600;
                const nowSec = Math.floor(Date.now() / 1000);
                const cutoff = nowSec - sixtyDaysSec;
                const rawTs = Array.isArray(data.timestamps) ? data.timestamps : [];
                const fullTs = rawTs.filter(ts => ts && typeof ts === 'number');
                const startIdx = fullTs.findIndex(ts => ts >= cutoff);
                const sliceStart = startIdx >= 0 ? startIdx : Math.max(0, fullTs.length - Math.floor(sixtyDaysSec / 3600));
                if (sliceStart > 0) {
                    data.ibw = (data.ibw || []).slice(sliceStart);
                    data.obw = (data.obw || []).slice(sliceStart);
                    data.timestamps = fullTs.slice(sliceStart);
                }
            }
            console.log(`[带宽图表] 原始数据长度:`, Array.isArray(data) ? data.length : 'object');

            // 处理不同API返回的数据格式
            if (range === '7d' || range === '30d' || range === '60d') {
                // 🔧 [7天数据修复] 7d/30d/90d现在也返回{timestamps, ibw, obw}格式
                // 统一处理为数组格式，与24小时数据保持一致
                inData = validateAndFilterData(data.ibw || [], null);
                outData = validateAndFilterData(data.obw || [], null);
                // 🔧 [标签优化] 使用优化的标签生成，避免重复日期
                const dataTimestamps = (data.timestamps || []).filter(ts => ts && typeof ts === 'number');
                timestamps = dataTimestamps; // 🔧 更新全局timestamps变量
                labels = ChartLabels.generateSmartLabels(dataTimestamps, range, 'bandwidth-realtime-chart');

                // 🔧 [Tooltip优化] 保存原始timestamps供tooltip使用
                window.bandwidthTimestamps = dataTimestamps;

                console.log(`[带宽图表] ${range}数据处理: 原始${data.timestamps ? data.timestamps.length : 0}点`);

                // 应用智能采样（如果数据点过多）
                const maxPoints = getMaxPoints();
                if (inData.length > maxPoints) {
                    const sampleStep = Math.ceil(inData.length / maxPoints);
                    const sampledInData = [];
                    const sampledOutData = [];
                    const sampledLabels = [];
                    const sampledTimestamps = []; // 🔧 [Tooltip修复] 采样时间戳数组

                    for (let i = 0; i < inData.length; i += sampleStep) {
                        sampledInData.push(inData[i]);
                        sampledOutData.push(outData[i]);
                        sampledLabels.push(labels[i]);
                        // 🔧 [Tooltip修复] 同步采样时间戳，确保数组长度一致
                        sampledTimestamps.push(dataTimestamps[i] || Date.now());
                    }

                    inData = sampledInData;
                    outData = sampledOutData;
                    labels = sampledLabels;
                    // 🔧 [Tooltip修复] 更新window.bandwidthTimestamps为采样后的时间戳
                    window.bandwidthTimestamps = sampledTimestamps;

                    console.log(`[带宽图表] ${range}数据采样: ${inData.length}点 (采样步长: ${sampleStep})`);
                } else {
                    // 🔧 [Tooltip修复] 没有采样时，直接使用原始时间戳
                    window.bandwidthTimestamps = dataTimestamps;
                }

            } else {
                // 原有格式处理（10min, 1h, 24h）
                inData = validateAndFilterData(data.ibw || [], null);
                outData = validateAndFilterData(data.obw || [], null);
                // 🔧 [标签优化] 使用优化的标签生成
                const dataTimestamps = (data.timestamps || []).filter(ts => ts && typeof ts === 'number');
                timestamps = dataTimestamps; // 🔧 更新全局timestamps变量
                labels = ChartLabels.generateSmartLabels(dataTimestamps, range, 'bandwidth-realtime-chart');

                // 🔧 [Tooltip优化] 保存原始timestamps供tooltip使用
                window.bandwidthTimestamps = dataTimestamps;
            }

            // 数据长度同步检查
            const minLength = Math.min(inData.length, outData.length, labels.length);
            if (minLength < inData.length) inData = inData.slice(0, minLength);
            if (minLength < outData.length) outData = outData.slice(0, minLength);
            if (minLength < labels.length) labels = labels.slice(0, minLength);

            const maxPoints = getMaxPoints();

            // 🔧 [24小时修复] 24小时数据智能采样，保持标签优化
            if (range === '24h' && inData.length > maxPoints) {
                const sampleStep = Math.ceil(inData.length / maxPoints);
                const sampledInData = [];
                const sampledOutData = [];

                for (let i = 0; i < inData.length; i += sampleStep) {
                    sampledInData.push(inData[i]);
                    sampledOutData.push(outData[i]);
                }

                inData = sampledInData;
                outData = sampledOutData;

                // 🔧 [24小时标签优化] 生成8个均匀分布的时间标签
                const sampledLabels = new Array(inData.length).fill('');
                const labelCount = 8; // 24小时显示8个标签
                const labelStep = Math.floor(inData.length / (labelCount - 1));

                // 获取采样后的时间戳
                const dataTimestamps = (data.timestamps || []).filter(ts => ts && typeof ts === 'number');
                const sampledTimestamps = [];
                for (let i = 0; i < dataTimestamps.length; i += sampleStep) {
                    sampledTimestamps.push(dataTimestamps[i]);
                }
                timestamps = sampledTimestamps; // 🔧 更新全局timestamps变量

                // 在关键位置设置标签
                for (let i = 0; i < inData.length; i++) {
                    if (i === 0 || i === inData.length - 1 || i % labelStep === 0) {
                        if (sampledTimestamps[i]) {
                            sampledLabels[i] = formatTime(sampledTimestamps[i], range);
                        }
                    }
                }

                labels = sampledLabels;

                console.log(`[带宽图表] 24小时数据采样: ${inData.length}点 (采样步长: ${sampleStep}), 标签数: ${labels.filter(l => l).length}`);
            } else if (inData.length > maxPoints && !(range === '7d' || range === '30d' || range === '60d')) {
                // 非长周期范围才截断
                inData = inData.slice(-maxPoints);
                outData = outData.slice(-maxPoints);
                labels = labels.slice(-maxPoints);
            }

            console.log(`[带宽图表] 处理后数据: 下行${inData.length}点, 上行${outData.length}点, 时间标签${labels.length}个`);
            updateChart(getTitle(range));

        } catch (error) {
            console.error('[带宽图表] 加载数据失败:', error);
            // 设置空数据状态
            inData = [];
            outData = [];
            labels = [];
            updateChart(getTitle(range));
        }
    }

    // 获取图表标题
    function getTitle(range) {
        const titleMap = {
            '10min': '实时带宽',
            '1h': '1小时带宽',
            '24h': '24小时带宽',
            '7d': '7天带宽',
            '30d': '30天带宽',
            '60d': '60天带宽'
        };
        return titleMap[range] || '带宽监控';
    }

    // 按钮事件
    function setupBtns() {
        const activeDayClasses = ['bg-blue-50', 'text-blue-600', 'border-blue-200'];
        const inactiveDayClasses = ['bg-gray-50', 'text-gray-600', 'border-gray-200'];
        const activeDarkClasses = ['dark:bg-slate-700', 'dark:text-white', 'dark:border-slate-700'];
        const inactiveDarkClasses = ['dark:bg-slate-800', 'dark:text-slate-300', 'dark:border-slate-700'];

        document.querySelectorAll('.bandwidth-range-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const Sthis = this; // Store this for use in promises and callbacks

                // 移除所有按钮的激活状态和加载指示器
                document.querySelectorAll('.bandwidth-range-btn').forEach(b => {
                    b.classList.remove(...activeDayClasses);
                    b.classList.add(...inactiveDayClasses);
                    b.classList.remove(...activeDarkClasses);
                    b.classList.add(...inactiveDarkClasses);

                    const indicator = b.querySelector('.loading-indicator');
                    if (indicator) indicator.remove();
                    b.classList.remove('loading');
                });

                // 为当前点击的按钮设置激活状态
                Sthis.classList.remove(...inactiveDayClasses);
                Sthis.classList.add(...activeDayClasses);
                Sthis.classList.remove(...inactiveDarkClasses);
                Sthis.classList.add(...activeDarkClasses);

                // 添加加载状态反馈
                Sthis.classList.add('loading');
                const loadIcon = document.createElement('span');
                loadIcon.className = 'loading-indicator mr-1 animate-spin';
                loadIcon.innerHTML = '&#9696;';
                Sthis.prepend(loadIcon);

                const range = Sthis.dataset.range;
                currentRange = range;

                const removeLoadingState = () => {
                    Sthis.classList.remove('loading');
                    const indicator = Sthis.querySelector('.loading-indicator');
                    if (indicator) indicator.remove();
                };

                if (range === '10min') {
                    unsubscribeRealtime();
                    loadHistory('10min')
                        .then(() => {
                            subscribeRealtime();
                            removeLoadingState();
                        })
                        .catch(() => {
                            removeLoadingState();
                        });
                } else if (range === '1h') {
                    unsubscribeRealtime();
                    loadHistory('1h')
                        .then(() => {
                            removeLoadingState();
                        })
                        .catch(() => {
                            removeLoadingState();
                        });
                } else if (range === '24h') {
                    unsubscribeRealtime();

                    loadHistory('24h')
                        .then(() => {
                            removeLoadingState();
                        })
                        .catch(() => {
                            removeLoadingState();
                        });
                } else {
                    unsubscribeRealtime();

                    loadHistory(range)
                        .then(() => {
                            removeLoadingState();
                        })
                        .catch(() => {
                            removeLoadingState();
                        });
                }
            });
        });
    }

    // 监听主题切换（假设页面有自定义事件或MutationObserver）
    function setupThemeListener() {
        // 1. 监听 class 变化
        const observer = new MutationObserver(() => {
            const theme = getTheme();
            if (theme !== currentTheme) {
                getChart(true); // 强制重建
                updateChart(currentRange==='10min'?'实时带宽':'带宽历史');
            }
        });
        observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
        observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });
        // 2. 监听自定义主题切换事件（如有）
        window.addEventListener('themechange', function() {
            const theme = getTheme();
            if (theme !== currentTheme) {
                getChart(true);
                updateChart(currentRange==='10min'?'实时带宽':'带宽历史');
            }
        });
    }

    function setupResize() {
        window.addEventListener('resize', () => {
            const c = getChart();
            if (c) c.resize();
            updateChart(currentRange === '10min' ? '实时带宽' : '带宽历史');
        });
        // 监听容器自身大小变化（更精细，兼容flex/grid等布局）
        const chartDom = document.getElementById('bandwidth-realtime-chart');
        if (window.ResizeObserver && chartDom) {
            const ro = new ResizeObserver(() => {
                const c = getChart();
                if (c) c.resize();
            });
            ro.observe(chartDom);
        }
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
    setupBtns();
    setupThemeListener();
    setupResize();

    // 默认显示实时图表（10分钟）
    const realtimeBtn = document.querySelector('.bandwidth-range-btn[data-range="10min"]');
    if (realtimeBtn) {
    realtimeBtn.click();
    } else {
    // 如果找不到实时按钮，则直接加载实时数据
    loadHistory('10min').then(() => {
    subscribeRealtime();
    });
    }

            // 注册图表到主题管理器
            if (window.UnifiedChartThemeManager && chart) {
                window.UnifiedChartThemeManager.register('bandwidth-chart', chart, 'dynamic');
            }
        });
})();