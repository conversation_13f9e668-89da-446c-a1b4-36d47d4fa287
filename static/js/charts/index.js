
/**
 * 图表管理模块
 * 统一管理所有图表相关功能
 */

// 图表管理器
const ChartManager = {
    // 图表实例
    charts: {},

    // 配置
    config: {
        // 基础图表配置 - 极简风格
        baseOptions: {
            chart: {
                fontFamily: 'inherit',
                background: 'transparent',
                animations: {
                    enabled: false
                },
                toolbar: {
                    show: false
                }
            },
            stroke: {
                curve: 'smooth',
                width: 2
            },
            dataLabels: {
                enabled: false
            },
            tooltip: {
                theme: 'dark',
                shared: true,
                style: {
                    fontSize: '12px'
                }
            },
            legend: {
                show: false  // 极简风格，隐藏图例
            },
            grid: {
                show: false,  // 极简风格，隐藏网格
                borderColor: 'transparent',
                padding: {
                    left: 0,
                    right: 0,
                    top: 0,
                    bottom: 0
                }
            },
            // 极简坐标轴配置
            xaxis: {
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false
                },
                labels: {
                    show: false  // 隐藏X轴标签
                }
            },
            yaxis: {
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false
                },
                labels: {
                    show: false  // 隐藏Y轴标签
                }
            },
            // 移除所有边框和背景
            plotOptions: {
                bar: {
                    borderRadius: 0,
                    columnWidth: '70%'
                },
                line: {
                    strokeWidth: 2
                }
            }
        },

        // 容器配置
        container: {
            // 不再设置固定高度，使用CSS控制
            width: '100%'
        }
    },

    /**
     * 初始化图表管理器
     */
    init() {
        console.log('图表管理器初始化中...');

        // 注册事件监听器
        this._registerEventListeners();

        // 准备图表容器
        this._prepareContainers();

        console.log('图表管理器初始化完成');
    },

    /**
     * 注册事件监听器
     * @private
     */
    _registerEventListeners() {
        // 窗口大小改变时重新渲染图表
        window.addEventListener('resize', this._debounce(() => {
            this.renderAllCharts();
        }, 300));

        // 主题变化时更新图表主题
        document.addEventListener('theme:changed', (e) => {
            if (e.detail && typeof e.detail.isDark !== 'undefined') {
                this.updateTheme(e.detail.isDark);
            }
        });

        // 监听负载详情卡片的大小变化
        this._observeCardHeightChanges();
    },

    /**
     * 监听卡片高度变化
     * @private
     */
    _observeCardHeightChanges() {
        // 使用ResizeObserver监听卡片大小变化
        if (typeof ResizeObserver !== 'undefined') {
            // 获取系统状态和负载详情卡片
            const systemCard = document.querySelector('.grid.grid-cols-1.lg\\:grid-cols-5.gap-4 > .col-span-1.lg\\:col-span-2');
            const loadCard = document.querySelector('.load-details-card');

            if (systemCard && loadCard) {
                const observer = new ResizeObserver(this._debounce(() => {
                    // 当系统状态卡片高度变化时，重新渲染负载图表
                    const loadCharts = ['realtimeLoadChart', 'minuteLoadChart', 'hourLoadChart'];
                    loadCharts.forEach(id => {
                        if (this.charts[id]) {
                            this.renderChart(id);
                        }
                    });
                }, 300));

                // 监听系统状态卡片的大小变化
                observer.observe(systemCard);

                console.log('已启用卡片高度自适应监听');
            }
        } else {
            console.warn('ResizeObserver不可用，无法监听卡片高度变化');
        }
    },

    /**
     * 防抖函数
     * @param {Function} fn - 要执行的函数
     * @param {number} delay - 延迟时间（毫秒）
     * @returns {Function} 防抖后的函数
     * @private
     */
    _debounce(fn, delay) {
        let timer = null;
        return function(...args) {
            if (timer) clearTimeout(timer);
            timer = setTimeout(() => {
                fn.apply(this, args);
            }, delay);
        };
    },

    /**
     * 深度合并对象
     * @param {Object} target - 目标对象
     * @param {Object} source - 源对象
     * @returns {Object} 合并后的对象
     * @private
     */
    _deepMerge(target, source) {
        if (!source) return target;
        if (!target) return { ...source };

        const result = { ...target };

        Object.keys(source).forEach(key => {
            if (source[key] instanceof Object && key in target && target[key] instanceof Object) {
                // 如果两者都是对象，递归合并
                result[key] = this._deepMerge(target[key], source[key]);
            } else {
                // 否则直接覆盖
                result[key] = source[key];
            }
        });

        return result;
    },

    /**
     * 准备所有图表容器
     * @private
     */
    _prepareContainers() {
        const containers = document.querySelectorAll('.chart-container');
        if (!containers || containers.length === 0) {
            console.log('未找到图表容器');
            return;
        }

        console.log(`找到 ${containers.length} 个图表容器`);
        containers.forEach(container => this._prepareContainer(container));
    },

    /**
     * 准备单个图表容器
     * @param {HTMLElement} container - 图表容器
     * @private
     */
    _prepareContainer(container) {
        if (!container) return;

        // 清空容器
        container.innerHTML = '';

        // 预先设置图表容器的尺寸和样式，减少渲染时的抖动
        // 设置图表容器的样式 - 只设置必要的属性，避免覆盖CSS中的设置
        container.style.width = this.config.container.width;
        container.style.position = 'relative';
        container.style.display = 'block';

        // 设置 overflow 为 visible，确保提示框可见
        container.style.overflow = 'visible';

        // 移除内边距设置，让 HTML 中的 p-4 类生效
        // 移除以下行:
        // container.style.paddingTop = '5px';
        // container.style.paddingBottom = '2px';
        // container.style.paddingLeft = '0px';
        // container.style.paddingRight = '0px';

        // 移除隐藏属性
        container.classList.remove('hidden');
        if (container.hasAttribute('hidden')) {
            container.removeAttribute('hidden');
        }
        container.removeAttribute('aria-hidden');

        // 确保父容器可见，但不改变其显示状态
        const parentTab = container.closest('.tab-content');
        if (parentTab) {
            // 不修改父标签页的可见性，避免影响标签页切换逻辑
            // 只确保容器本身的样式正确
        }

        console.log(`准备图表容器: ${container.id || '未命名容器'}`);
    },

    /**
     * 创建图表
     * @param {string} id - 图表ID
     * @param {HTMLElement} container - 图表容器
     * @param {Object} options - 图表配置
     * @returns {Object} 图表实例
     */
    createChart(id, container, options) {
        if (!container) {
            console.error(`创建图表 ${id} 失败: 容器不存在`);
            return null;
        }

        try {
            // 确保容器已准备好
            this._prepareContainer(container);

            // 检查容器是否可见且有尺寸
            const rect = container.getBoundingClientRect();
            if (rect.width === 0) {
                console.warn(`图表容器 ${id} 宽度为零，设置默认宽度`);
                container.style.width = '100%';
                container.style.display = 'block';
                container.style.position = 'relative';
                // 不再设置高度和溢出属性，使用CSS中的设置
            }

            // 合并配置 - 使用深度合并确保嵌套属性正确合并
            const chartOptions = this._deepMerge(this.config.baseOptions, options);

            // 确保 tooltip 配置正确
            if (!chartOptions.tooltip) {
                chartOptions.tooltip = { theme: 'dark', shared: true };
            } else if (!chartOptions.tooltip.theme) {
                chartOptions.tooltip.theme = 'dark';
            }

            // 确保 tooltip 可见
            chartOptions.tooltip.style = {
                ...chartOptions.tooltip.style,
                overflow: 'visible'
            };

            // 确保图表有正确的布局设置，避免横轴文字和图例重叠
            if (!chartOptions.chart.height) {
                // 如果没有设置高度，使用容器高度
                chartOptions.chart.height = '100%';
            }

            // 确保有足够的边距
            if (!chartOptions.chart.margin) {
                chartOptions.chart.margin = {
                    top: 2,
                    right: 2,
                    bottom: 2,
                    left: 2
                };
            }

            // 确保图例设置正确
            if (!chartOptions.legend.offsetY) {
                chartOptions.legend.offsetY = 5;
            }

            // 确保坐标轴标签有足够的空间
            if (!chartOptions.xaxis.labels.offsetY) {
                chartOptions.xaxis.labels.offsetY = 0;
            }

            // 确保图表配置中有有效的数据
            if (!chartOptions.series || !Array.isArray(chartOptions.series)) {
                chartOptions.series = [];
            }

            // 确保每个系列都有数据
            chartOptions.series = chartOptions.series.map(series => {
                if (!series.data || !Array.isArray(series.data) || series.data.length === 0) {
                    return { ...series, data: [0, 0, 0] }; // 添加默认数据
                }
                return series;
            });

            // 创建图表实例
            try {
                // 检查是否已存在同ID图表
                if (this.charts[id]) {
                    this.destroyChart(id);
                }

                // 创建新图表
                this.charts[id] = new ApexCharts(container, chartOptions);
                console.log(`创建图表: ${id}`);

                return this.charts[id];
            } catch (error) {
                console.error(`创建图表 ${id} 失败:`, error);
                return null;
            }
        } catch (error) {
            console.error(`准备图表 ${id} 失败:`, error);
            return null;
        }
    },

    /**
     * 渲染图表
     * @param {string} id - 图表ID
     * @returns {boolean} 是否成功渲染
     */
    renderChart(id) {
        const chart = this.charts[id];
        if (!chart) {
            console.warn(`图表 ${id} 不存在`);
            return false;
        }

        try {
            // 渲染图表
            chart.render();
            console.log(`渲染图表: ${id}`);

            // 修复首次渲染时横轴文字和图例重叠的问题
            setTimeout(() => {
                try {
                    // 更新图表选项，强制重新计算布局
                    chart.updateOptions({
                        // 保持其他选项不变，只更新图例位置
                        legend: {
                            position: 'top',
                            horizontalAlign: 'right',
                            offsetY: 5, // 增加偏移，避免与坐标轴标签重叠
                            itemMargin: {
                                horizontal: 10,
                                vertical: 0
                            }
                        }
                    }, false, true); // 不重绘数据，只更新选项
                } catch (updateError) {
                    // 静默处理更新选项错误，不影响主要功能
                    console.warn(`更新图表 ${id} 布局失败:`, updateError);
                }
            }, 50); // 短暂停确保图表已经渲染

            return true;
        } catch (error) {
            console.error(`渲染图表 ${id} 失败:`, error);
            return false;
        }
    },

    /**
     * 渲染所有图表
     */
    renderAllCharts() {
        Object.keys(this.charts).forEach(id => {
            this.renderChart(id);
        });
    },

    /**
     * 更新图表数据
     * @param {string} id - 图表ID
     * @param {Array} series - 新的数据系列
     * @param {boolean} animate - 是否使用动画
     * @returns {boolean} 是否成功更新
     */
    updateChartData(id, series, animate = false) {
        const chart = this.charts[id];
        if (!chart) {
            console.warn(`图表 ${id} 不存在`);
            return false;
        }

        try {
            chart.updateSeries(series, animate);
            return true;
        } catch (error) {
            console.error(`更新图表 ${id} 数据失败:`, error);
            return false;
        }
    },

    /**
     * 更新图表选项
     * @param {string} id - 图表ID
     * @param {Object} options - 新的选项
     * @param {boolean} redraw - 是否重绘图表
     * @returns {boolean} 是否成功更新
     */
    updateChartOptions(id, options, redraw = false) {
        const chart = this.charts[id];
        if (!chart) {
            console.warn(`图表 ${id} 不存在`);
            return false;
        }

        try {
            chart.updateOptions(options, redraw);
            return true;
        } catch (error) {
            console.error(`更新图表 ${id} 选项失败:`, error);
            return false;
        }
    },

    /**
     * 销毁图表
     * @param {string} id - 图表ID
     * @returns {boolean} 是否成功销毁
     */
    destroyChart(id) {
        const chart = this.charts[id];
        if (!chart) {
            return false;
        }

        try {
            chart.destroy();
            delete this.charts[id];
            console.log(`销毁图表: ${id}`);
            return true;
        } catch (error) {
            console.error(`销毁图表 ${id} 失败:`, error);
            return false;
        }
    },

    /**
     * 销毁所有图表
     */
    destroyAllCharts() {
        Object.keys(this.charts).forEach(id => {
            this.destroyChart(id);
        });
    },

    /**
     * 更新图表主题 - 极简风格
     * @param {boolean} isDark - 是否为暗色主题
     */
    updateTheme(isDark) {
        const themeOptions = {
            theme: {
                mode: isDark ? 'dark' : 'light'
            },
            grid: {
                show: false,  // 极简风格，隐藏网格
                borderColor: 'transparent'
            },
            tooltip: {
                theme: isDark ? 'dark' : 'light',
                style: {
                    fontSize: '12px',
                    overflow: 'visible'
                }
            },
            legend: {
                show: false  // 极简风格，隐藏图例
            },
            xaxis: {
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false
                },
                labels: {
                    show: false
                }
            },
            yaxis: {
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false
                },
                labels: {
                    show: false
                }
            }
        };

        Object.keys(this.charts).forEach(id => {
            // 获取当前图表的选项
            const chart = this.charts[id];
            if (!chart || !chart.w || !chart.w.config) {
                this.updateChartOptions(id, themeOptions, true);
                return;
            }

            // 更新图表选项
            this.updateChartOptions(id, themeOptions, true);
        });
    }
};

// 导出图表管理器
window.ChartManager = ChartManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 检查ECharts是否可用
    if (typeof echarts === 'undefined') {
        console.error('ECharts未加载，图表功能将不可用');
        return;
    }

    // 初始化图表管理器
    ChartManager.init();
});
