/**
 * 统一图表样式配置
 * 用于所有ECharts图表的样式统一
 */

window.UnifiedChartConfig = {
    
    /**
     * 获取通用的ECharts配置选项
     * @param {Object} options 自定义配置选项
     * @returns {Object} ECharts配置对象
     */
    getCommonOptions(options = {}) {
        const isDarkMode = this._isDarkMode();
        const isMobile = window.innerWidth <= 600;
        
        // 默认配置
        const defaultConfig = {
            backgroundColor: 'transparent',
            
            // 统一的网格配置
            grid: {
                left: isMobile ? '2%' : '3%',
                right: isMobile ? '2%' : '4%',
                bottom: '3%',
                top: '15%', // 为顶部留出更多空间
                containLabel: true
            },
            
            // 统一的X轴配置
            xAxis: {
                type: 'category',
                axisLabel: {
                    fontSize: isMobile ? 9 : 12,
                    color: isDarkMode ? '#94a3b8' : '#64748b',
                    rotate: 0, // 横轴文字水平显示
                    interval: 'auto',
                    hideOverlap: true
                },
                axisLine: {
                    lineStyle: {
                        color: isDarkMode ? '#475569' : '#cbd5e1'
                    }
                },
                // 不显示垂直网格线
                splitLine: {
                    show: false
                },
                axisTick: {
                    show: true,
                    lineStyle: {
                        color: isDarkMode ? '#475569' : '#cbd5e1'
                    }
                }
            },
            
            // 统一的Y轴配置
            yAxis: {
                type: 'value',
                nameTextStyle: {
                    color: isDarkMode ? '#94a3b8' : '#64748b'
                },
                axisLabel: {
                    fontSize: isMobile ? 9 : 12,
                    color: isDarkMode ? '#94a3b8' : '#64748b'
                },
                axisLine: {
                    show: false // 隐藏Y轴线
                },
                // 只显示水平参考线
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: isDarkMode ? '#334155' : '#e2e8f0',
                        type: 'solid', // 使用实线而不是虚线
                        width: 1,
                        opacity: 0.6
                    }
                },
                axisTick: {
                    show: false // 隐藏Y轴刻度
                }
            },
            
            // 统一的图例配置
            legend: isMobile ? 
                { show: false } : 
                {
                    top: 30,
                    textStyle: {
                        color: isDarkMode ? '#94a3b8' : '#64748b',
                        fontSize: 12
                    }
                },
            
            // 统一的标题配置
            title: {
                left: 'center',
                top: 10,
                textStyle: {
                    fontSize: isMobile ? 12 : 14,
                    color: isDarkMode ? '#94a3b8' : '#64748b',
                    fontWeight: 'normal'
                }
            },
            
            // 统一的提示框配置
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    lineStyle: {
                        color: isDarkMode ? '#64748b' : '#94a3b8',
                        type: 'dashed'
                    },
                    label: {
                        backgroundColor: isDarkMode ? '#475569' : '#6a7985',
                        padding: [5, 7, 5, 7], // 上右下左内边距
                        margin: 8, // 与坐标轴的距离
                        fontSize: 11,
                        color: '#fff' // 确保文字颜色为白色
                    }
                },
                backgroundColor: isDarkMode ? 'rgba(51, 65, 85, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                borderColor: isDarkMode ? '#475569' : '#e2e8f0',
                textStyle: {
                    color: isDarkMode ? '#f1f5f9' : '#1e293b'
                }
            }
        };
        
        // 合并自定义配置
        return this._deepMerge(defaultConfig, options);
    },
    
    /**
     * 获取百分比类型Y轴配置
     * @param {Object} customOptions 自定义选项
     * @returns {Object} Y轴配置
     */
    getPercentageYAxis(customOptions = {}) {
        const isDarkMode = this._isDarkMode();
        const isMobile = window.innerWidth <= 600;
        
        const percentageYAxis = {
            type: 'value',
            name: '%',
            min: 0,
            max: 100, // 始终显示100%
            interval: 20, // 每20%一个刻度
            nameTextStyle: {
                color: isDarkMode ? '#94a3b8' : '#64748b'
            },
            axisLabel: {
                fontSize: isMobile ? 9 : 12,
                color: isDarkMode ? '#94a3b8' : '#64748b',
                formatter: '{value}%'
            },
            axisLine: {
                show: false
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: isDarkMode ? '#334155' : '#e2e8f0',
                    type: 'solid',
                    width: 1,
                    opacity: 0.6
                }
            },
            axisTick: {
                show: false
            }
        };
        
        return this._deepMerge(percentageYAxis, customOptions);
    },
    
    /**
     * 获取动态Y轴配置（自动留出顶部空间）
     * @param {Array} data 数据数组
     * @param {Object} customOptions 自定义选项
     * @returns {Object} Y轴配置
     */
    getDynamicYAxis(data, customOptions = {}) {
        const isDarkMode = this._isDarkMode();
        const isMobile = window.innerWidth <= 600;
        
        // 检查是否为带宽单位
        const isBandwidthChart = customOptions.name === 'Mbps' || customOptions.name === 'MB/s';
        
        // 计算数据的最大值，增强空数据检测
        let maxValue = 0;
        let hasValidData = false;
        
        if (Array.isArray(data) && data.length > 0) {
            // 支持多个数据系列
            if (Array.isArray(data[0])) {
                data.forEach(series => {
                    if (Array.isArray(series) && series.length > 0) {
                        // 过滤有效数据：非null、非undefined、非NaN、有限数字
                        const validValues = series.filter(v => 
                            v !== null && 
                            v !== undefined && 
                            typeof v === 'number' && 
                            !isNaN(v) && 
                            isFinite(v) && 
                            v >= 0
                        );
                        
                        if (validValues.length > 0) {
                            const seriesMax = Math.max(...validValues);
                            maxValue = Math.max(maxValue, seriesMax);
                            hasValidData = true;
                        }
                    }
                });
            } else {
                // 单一数据系列
                const validValues = data.filter(v => 
                    v !== null && 
                    v !== undefined && 
                    typeof v === 'number' && 
                    !isNaN(v) && 
                    isFinite(v) && 
                    v >= 0
                );
                
                if (validValues.length > 0) {
                    maxValue = Math.max(...validValues);
                    hasValidData = true;
                }
            }
        }
        
        // 空数据状态处理 - 为不同图表类型设置合理的默认范围
        if (!hasValidData || maxValue === 0) {
            if (isBandwidthChart) {
                // 带宽图表：默认显示0-10 Mbps
                maxValue = 10;
            } else {
                // 其他图表：默认显示0-100
                maxValue = 100;
            }
            
            console.log(`[图表] 检测到空数据状态，使用默认范围: 0-${maxValue}${customOptions.name || ''}`);
        }
        
        // 为顶部留出20%的空间
        let paddedMax = maxValue * 1.2;
        
        // 带宽图表特殊处理：确保最小显示范围
        if (isBandwidthChart) {
            // 带宽图表最小显示5 Mbps，避免显示过小的刻度
            if (paddedMax < 5) {
                paddedMax = 5;
            }
        }
        
        // 智能取整，确保纵轴不显示小数点
        if (paddedMax <= 10) {
            // 小于等于10时，向上取整到最近的整数
            paddedMax = Math.ceil(paddedMax);
        } else if (paddedMax <= 100) {
            // 10-100之间，向上取整到最近的5的倍数
            paddedMax = Math.ceil(paddedMax / 5) * 5;
        } else {
            // 大于100时，向上取整到最近的10的倍数
            paddedMax = Math.ceil(paddedMax / 10) * 10;
        }
        
        // 计算合适的刻度间隔，避免重复值
        let interval = undefined;
        if (isBandwidthChart) {
            // 智能interval计算，支持0到几千Mbps
            if (paddedMax <= 5) {
                interval = 1;
                paddedMax = Math.ceil(paddedMax);
            } else if (paddedMax <= 10) {
                interval = 2;
                paddedMax = Math.ceil(paddedMax / 2) * 2;
            } else if (paddedMax <= 50) {
                interval = 10;
                paddedMax = Math.ceil(paddedMax / 10) * 10;
            } else if (paddedMax <= 100) {
                interval = 20;
                paddedMax = Math.ceil(paddedMax / 20) * 20;
            } else if (paddedMax <= 500) {
                interval = 100;
                paddedMax = Math.ceil(paddedMax / 100) * 100;
            } else if (paddedMax <= 1000) {
                interval = 200;
                paddedMax = Math.ceil(paddedMax / 200) * 200;
            } else if (paddedMax <= 5000) {
                interval = 1000;
                paddedMax = Math.ceil(paddedMax / 1000) * 1000;
            } else {
                interval = 2000;
                paddedMax = Math.ceil(paddedMax / 2000) * 2000;
            }
        }
        
        // 最终安全检查
        if (!paddedMax || paddedMax <= 0 || !isFinite(paddedMax)) {
            paddedMax = isBandwidthChart ? 10 : 100;
            interval = isBandwidthChart ? 2 : 20;
            console.warn('[图表] Y轴范围异常，使用安全默认值:', paddedMax);
        }
        
        const dynamicYAxis = {
            type: 'value',
            min: 0,
            max: paddedMax,
            interval: interval,
            nameTextStyle: {
                color: isDarkMode ? '#94a3b8' : '#64748b'
            },
            axisLabel: {
                fontSize: isMobile ? 9 : 12,
                color: isDarkMode ? '#94a3b8' : '#64748b',
                // 对于带宽图表的格式化
                formatter: isBandwidthChart ? function(value) {
                    // 确保显示整数刻度
                    return Math.round(value).toString();
                } : undefined
            },
            axisLine: {
                show: false
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: isDarkMode ? '#334155' : '#e2e8f0',
                    type: 'solid',
                    width: 1,
                    opacity: 0.6
                }
            },
            axisTick: {
                show: false
            }
        };
        
        return this._deepMerge(dynamicYAxis, customOptions);
    },
    
    /**
     * 获取网络质量监控专用配置
     * @param {Object} customOptions 自定义选项
     * @returns {Object} 配置对象
     */
    getNetworkQualityConfig(customOptions = {}) {
        const baseConfig = this.getCommonOptions();
        
        // 网络质量监控特定配置
        const networkConfig = {
            xAxis: {
                ...baseConfig.xAxis,
                axisLabel: {
                    ...baseConfig.xAxis.axisLabel,
                    rotate: 0, // 确保横轴文字水平显示
                    interval: 'auto',
                    hideOverlap: true,
                    margin: 8
                }
            },
            yAxis: {
                ...baseConfig.yAxis,
                name: '延迟 (ms)',
                nameLocation: 'middle',
                nameGap: 50
            },
            grid: {
                ...baseConfig.grid,
                bottom: '8%' // 为横轴标签留出更多空间
            }
        };
        
        return this._deepMerge(networkConfig, customOptions);
    },
    
    /**
     * 获取通用系列样式
     * @param {string} type 图表类型 ('line', 'bar', 'area')
     * @param {Object} customOptions 自定义选项
     * @returns {Object} 系列配置
     */
    getCommonSeries(type = 'line', customOptions = {}) {
        const baseSeriesConfig = {
            line: {
                type: 'line',
                smooth: true,
                symbol: 'none',
                lineStyle: {
                    width: this._getDynamicLineWidth(customOptions)
                },
                emphasis: {
                    focus: 'series'
                }
            },
            bar: {
                type: 'bar',
                emphasis: {
                    focus: 'series'
                }
            },
            area: {
                type: 'line',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                    opacity: 0.3
                },
                lineStyle: {
                    width: this._getDynamicLineWidth(customOptions)
                },
                emphasis: {
                    focus: 'series'
                }
            }
        };
        
        return this._deepMerge(baseSeriesConfig[type] || baseSeriesConfig.line, customOptions);
    },
    
    /**
     * 获取预定义的颜色方案
     * @param {string} theme 主题名称
     * @returns {Array} 颜色数组
     */
    getColorScheme(theme = 'default') {
        const isDarkMode = this._isDarkMode();
        
        const colorSchemes = {
            default: isDarkMode ? 
                ['#60a5fa', '#34d399', '#fbbf24', '#f87171', '#a78bfa', '#fb7185'] :
                ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899'],
            network: isDarkMode ?
                ['#22c55e', '#2563eb', '#f59e0b', '#ef4444'] :
                ['#16a34a', '#1d4ed8', '#d97706', '#dc2626'],
            system: isDarkMode ?
                ['#3b82f6', '#a855f7', '#06b6d4', '#fbbf24'] :
                ['#2563eb', '#9333ea', '#0891b2', '#f59e0b']
        };
        
        return colorSchemes[theme] || colorSchemes.default;
    },
    
    /**
     * 格式化时间标签（网络质量监控专用）
     * @param {number} timestamp 时间戳
     * @param {string} timeRange 时间范围
     * @returns {string} 格式化后的时间标签
     */
    formatTimeLabel(timestamp, timeRange) {
        const date = new Date(timestamp * 1000);
        
        switch (timeRange) {
            case '4h':
            case '24h':
                // 4小时和24小时显示到分钟
                return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                
            case '7d':
                // 7天显示日期，不显示具体时间（使用1小时级别颗粒度）
                const today = new Date();
                const isToday = date.toDateString() === today.toDateString();
                const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
                const isYesterday = date.toDateString() === yesterday.toDateString();
                
                if (isToday) {
                    return '今天';
                } else if (isYesterday) {
                    return '昨天';
                } else {
                    return `${date.getMonth() + 1}/${date.getDate()}`;
                }
                
            case '31d':
                // 31天显示日期，不显示具体时间
                return `${date.getMonth() + 1}/${date.getDate()}`;
                
            case 'custom':
                // 自定义时间范围显示日期和时间
                return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                
            default:
                return date.toLocaleString();
        }
    },
    
    /**
     * 检查是否为暗黑模式
     * @returns {boolean} 是否为暗黑模式
     */
    _isDarkMode() {
        return document.documentElement.classList.contains('dark') || 
               document.body.classList.contains('dark');
    },
    
    /**
     * 深度合并对象
     * @param {Object} target 目标对象
     * @param {Object} source 源对象
     * @returns {Object} 合并后的对象
     */
    _deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    result[key] = this._deepMerge(result[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }
        
        return result;
    },
    
    /**
     * 获取动态线条宽度
     * @param {Object} options 自定义选项
     * @returns {number} 线条宽度
     */
    _getDynamicLineWidth(options = {}) {
        const isMobile = window.innerWidth <= 600;
        
        // 基础线条宽度
        let baseWidth = 1.5;
        
        // 移动端使用更细的线条
        if (isMobile) {
            baseWidth = 1.0;
        }
        
        // 根据图表类型调整
        if (options.name === 'Mbps' || options.name === 'MB/s') {
            // 带宽图表可能有多条线路，使用稍细的线条
            baseWidth *= 0.8;
        }
        
        // 数据点密度检测
        const dataLength = options.dataLength || 0;
        if (dataLength > 500) {
            baseWidth *= 0.8; // 数据密集时减细
        } else if (dataLength > 1000) {
            baseWidth *= 0.6; // 非常密集时更细
        }
        
        // 确保最小线条宽度
        return Math.max(0.6, baseWidth);
    },
    
    /**
     * 应用统一样式到现有图表
     * @param {Object} chart ECharts实例
     * @param {string} chartType 图表类型
     * @param {Object} customOptions 自定义选项
     */
    applyUnifiedStyle(chart, chartType = 'default', customOptions = {}) {
        if (!chart) return;
        
        let config;
        switch (chartType) {
            case 'percentage':
                config = this.getCommonOptions({
                    yAxis: this.getPercentageYAxis()
                });
                break;
            case 'network':
                config = this.getNetworkQualityConfig();
                break;
            case 'dynamic':
                // 需要传入数据来计算动态Y轴
                config = this.getCommonOptions(customOptions);
                break;
            default:
                config = this.getCommonOptions();
        }
        
        // 合并自定义选项
        const finalConfig = this._deepMerge(config, customOptions);
        
        // 应用配置
        chart.setOption(finalConfig, true);
    }
};

/**
 * 主题变化监听器
 */
window.UnifiedChartThemeManager = {
    charts: new Map(),
    
    /**
     * 注册图表实例
     * @param {string} id 图表ID
     * @param {Object} chart ECharts实例
     * @param {string} type 图表类型
     * @param {Object} options 自定义选项
     */
    register(id, chart, type = 'default', options = {}) {
        this.charts.set(id, { chart, type, options });
    },
    
    /**
     * 注销图表实例
     * @param {string} id 图表ID
     */
    unregister(id) {
        this.charts.delete(id);
    },
    
    /**
     * 更新所有注册的图表主题
     */
    updateAllCharts() {
        this.charts.forEach(({ chart, type, options }, id) => {
            if (chart && !chart.isDisposed()) {
                window.UnifiedChartConfig.applyUnifiedStyle(chart, type, options);
            }
        });
    },
    
    /**
     * 初始化主题监听器
     */
    init() {
        // 监听主题变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.attributeName === 'class') {
                    this.updateAllCharts();
                }
            });
        });
        
        observer.observe(document.documentElement, { attributes: true });
        
        // 监听自定义主题变化事件
        window.addEventListener('themechange', () => {
            this.updateAllCharts();
        });
        
        console.log('统一图表主题管理器已初始化');
    }
};

// 页面加载完成后初始化主题管理器
document.addEventListener('DOMContentLoaded', () => {
    window.UnifiedChartThemeManager.init();
});
