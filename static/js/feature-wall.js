/**
 * 功能权限检查前端模块
 * 用于在UI上控制功能的显示和访问
 * 支持动态Feature定义（从服务器获取）
 */

(function() {
  'use strict';
  
  // 功能权限信息（从服务器获取）
  let featureWallInfo = null;
  
  // 功能权限检查器
  const FeatureWall = {
    // 判断是否为高级分析编辑功能
    _isAdvancedAnalyticsEdit: function(name) {
      const norm = (s) => (s || '').toString().toLowerCase().replace(/[-_]/g, '');
      // 只限制高级分析的编辑功能
      return norm(name) === 'advancedanalyticsedit';
    },
    // 初始化
    init: async function() {
      await this.refresh();
    },

    // 刷新功能权限信息
    refresh: async function() {
      try {
        // 获取功能权限信息
        const response = await fetch('/api/license/feature-wall');
        if (response.ok) {
          const data = await response.json();
          // 兼容不同的响应格式
          if (data.success !== undefined) {
            // 如果有success字段，使用data.data
            featureWallInfo = data.success ? data.data : null;
          } else {
            // 如果没有success字段，直接使用data
            featureWallInfo = data;
          }
          console.log('[FeatureWall] 功能权限信息已刷新:', featureWallInfo);

          // 检查是否为动态功能墙
          if (featureWallInfo && featureWallInfo.isDynamic) {
            console.log('[FeatureWall] 使用动态功能定义');
          }

          // 应用功能限制
          this.applyFeatureRestrictions();

          return true;
        } else {
          console.error('[FeatureWall] 获取功能权限信息失败');
          return false;
        }
      } catch (error) {
        console.error('[FeatureWall] 刷新失败:', error);
        return false;
      }
    },
    
    // 检查是否拥有指定功能
    hasFeature: function(featureName) {
      // 只对高级分析编辑功能进行真正的权限检查
      if (!this._isAdvancedAnalyticsEdit(featureName)) {
        // 其他功能都默认允许
        return true;
      }
      
      // 高级分析编辑功能需要检查权限
      if (!featureWallInfo || !featureWallInfo.features) return false;
      
      // 支持多种格式的功能名称
      const normalizedName = featureName.toLowerCase().replace(/-/g, '').replace(/_/g, '');
      
      // 直接查找功能
      if (featureWallInfo.features[normalizedName]) {
        return featureWallInfo.features[normalizedName].available || false;
      }
      
      // 尝试其他可能的映射
      const featureMap = {
        'webssh': ['webssh', 'web_ssh', 'WEBSSH'],
        'autodiscovery': ['autoDiscovery', 'auto_discovery', 'AUTO_DISCOVERY'],
        'advancedanalytics': ['advancedAnalytics', 'advanced_analytics', 'ADVANCED_ANALYTICS'],
        'apiaccess': ['apiAccess', 'api_access', 'API_ACCESS'],
        'customalerts': ['customAlerts', 'custom_alerts', 'CUSTOM_ALERTS'],
        'aianalytics': ['aiAnalytics', 'ai_analytics', 'AI_ANALYTICS']
      };
      
      const possibleNames = featureMap[normalizedName] || [];
      for (const name of possibleNames) {
        if (featureWallInfo.features[name]) {
          return featureWallInfo.features[name].available || false;
        }
      }
      
      return false;
    },
    
    // 获取功能信息
    getFeatureInfo: function(featureName) {
      if (!featureWallInfo || !featureWallInfo.features) return null;
      
      const normalizedName = featureName.toLowerCase().replace(/-/g, '').replace(/_/g, '');
      
      // 如果是动态功能墙，直接返回功能信息
      if (featureWallInfo.isDynamic && featureWallInfo.features[normalizedName]) {
        return featureWallInfo.features[normalizedName];
      }
      
      // 静态功能墙的默认信息
      return featureWallInfo.features[normalizedName] || null;
    },
    
    // 应用功能限制
    applyFeatureRestrictions: function() {
      // 如果是动态功能墙，遍历所有功能
      if (featureWallInfo && featureWallInfo.isDynamic && featureWallInfo.features) {
        Object.entries(featureWallInfo.features).forEach(([key, feature]) => {
          // 只限制高级分析编辑功能
          if (this._isAdvancedAnalyticsEdit(key) || this._isAdvancedAnalyticsEdit(feature?.featureName)) {
            if (!feature.available) this.restrictFeature(key, feature);
          }
        });
      } else {
        // 静态功能限制（向后兼容） - 只限制高级分析编辑
        // this.restrictAdvancedAnalyticsEdit();
      }
    },
    
    // 通用功能限制方法
    restrictFeature: function(featureName, featureInfo) {
      // 只限制高级分析编辑功能
      if (!this._isAdvancedAnalyticsEdit(featureName)) return;
      const normalizedName = featureName.toLowerCase().replace(/_/g, '-');
      console.log('[FeatureWall] 限制功能:', featureName, '规范化名称:', normalizedName);
      
      // 查找相关元素
      const selectors = [
        `[data-feature="${normalizedName}"]`,
        `[data-feature="${featureName}"]`,
        `[onclick*="${normalizedName}"]`,
        `[href*="${normalizedName}"]`
      ];
      
      console.log('[FeatureWall] 使用选择器:', selectors);
      
      selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        console.log('[FeatureWall] 选择器', selector, '找到元素数:', elements.length);
        elements.forEach(elem => {
          elem.classList.add('feature-locked');
          elem.title = featureInfo.description || `${featureInfo.name}需要升级套餐`;
          
          // 处理点击事件
          const originalOnclick = elem.onclick;
          elem.onclick = (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.showUpgradeDialog(
              featureInfo.name || featureName,
              featureInfo.requiredPlan || '更高级套餐',
              featureInfo.description
            );
            return false;
          };
          
          // 如果是链接，阻止默认行为
          if (elem.tagName === 'A') {
            elem.addEventListener('click', (e) => {
              e.preventDefault();
              this.showUpgradeDialog(
                featureInfo.name || featureName,
                featureInfo.requiredPlan || '更高级套餐',
                featureInfo.description
              );
            });
          }
          
          // 在元素上添加视觉提示，但保留原始图标
          this.addFeatureLockIndicator(elem, featureInfo);
        });
      });
    },
    
    // 添加功能锁定指示器
    addFeatureLockIndicator: function(elem, featureInfo) {
      // 如果已经添加过指示器，不重复添加
      if (elem.querySelector('.feature-lock-indicator')) return;
      
      // 添加相对定位
      elem.classList.add('relative');
      
      // 创建锁定指示器
      const indicator = document.createElement('div');
      indicator.className = 'feature-lock-indicator';
      indicator.innerHTML = `
        <div class="feature-lock-badge">
          <i class="ti ti-lock"></i>
          <span>${featureInfo.requiredPlan || '升级'}</span>
        </div>
      `;
      
      elem.appendChild(indicator);
      
      // 根据元素类型添加样式
      if (elem.tagName === 'BUTTON' || elem.classList.contains('btn')) {
        elem.classList.add('opacity-60', 'hover:opacity-80', 'transition-opacity');
      }
      
      // 如果是卡片，添加边框和背景效果
      if (elem.classList.contains('card') || elem.classList.contains('feature-card')) {
        elem.classList.add('border-amber-200', 'bg-amber-50/50', 'dark:bg-amber-900/10', 'dark:border-amber-800');
      }
    },
    
    // 简化的功能限制方法（向后兼容）
    // 所有功能默认不限制，只有高级分析编辑功能需要特殊处理
    restrictWebSSH: function() { return; },
    restrictAutoDiscovery: function() { return; },
    restrictAdvancedAnalytics: function() { return; },
    restrictCustomAlerts: function() { return; },
    
    // 显示升级提示对话框
    showUpgradeDialog: function(featureName, requiredPlan, description) {
      const currentPlan = featureWallInfo?.currentPlan?.name || featureWallInfo?.currentPlan?.displayName || 'Free Plan';
      
      // 创建对话框
      const dialog = document.createElement('div');
      dialog.className = 'feature-upgrade-dialog';
      dialog.innerHTML = `
        <div class="feature-upgrade-overlay" onclick="FeatureWall.closeUpgradeDialog()"></div>
        <div class="feature-upgrade-content">
          <button class="feature-upgrade-close" onclick="FeatureWall.closeUpgradeDialog()">
            <i class="ti ti-x"></i>
          </button>
          
          <div class="feature-upgrade-body">
            <div class="feature-upgrade-header">
              <div class="feature-upgrade-icon">
                <i class="ti ti-stars"></i>
              </div>
              <h3 class="feature-upgrade-title">解锁高级功能</h3>
            </div>
            
            <div class="feature-upgrade-feature-info">
              <h4>${featureName}</h4>
              ${description ? `<p>${description}</p>` : ''}
            </div>
            
            <div class="feature-upgrade-plan-info">
              <div class="plan-current">
                <p class="plan-label">当前套餐</p>
                <p class="plan-name">${currentPlan}</p>
              </div>
              <i class="ti ti-arrow-right plan-arrow"></i>
              <div class="plan-required">
                <p class="plan-label">需要升级到</p>
                <p class="plan-name">${requiredPlan}</p>
              </div>
            </div>
            
            <div class="feature-upgrade-benefits">
              <p class="benefits-title">升级后您还将获得：</p>
              <div class="benefit-item">
                <i class="ti ti-circle-check"></i>
                <span>更多节点支持</span>
              </div>
              <div class="benefit-item">
                <i class="ti ti-circle-check"></i>
                <span>高级功能访问</span>
              </div>
              <div class="benefit-item">
                <i class="ti ti-circle-check"></i>
                <span>优先技术支持</span>
              </div>
            </div>
            
            <div class="feature-upgrade-actions">
              <button class="btn-cancel" onclick="FeatureWall.closeUpgradeDialog()">
                稍后再说
              </button>
              <button class="btn-upgrade" onclick="window.location.href='/admin/license-management'">
                <i class="ti ti-arrow-up-circle"></i>
                立即升级
              </button>
            </div>
          </div>
        </div>
      `;
      
      document.body.appendChild(dialog);
      
      // 添加动画
      setTimeout(() => {
        dialog.classList.add('show');
      }, 10);
      
      // ESC键关闭
      const handleEsc = (e) => {
        if (e.key === 'Escape') {
          this.closeUpgradeDialog();
          document.removeEventListener('keydown', handleEsc);
        }
      };
      document.addEventListener('keydown', handleEsc);
    },
    
    // 关闭升级对话框
    closeUpgradeDialog: function() {
      const dialog = document.querySelector('.feature-upgrade-dialog');
      if (dialog) {
        // 添加关闭动画
        dialog.classList.remove('show');
        
        setTimeout(() => {
          dialog.remove();
        }, 300);
      }
    },
    
    // 获取功能状态信息（供其他模块使用）
    getFeatureWallInfo: function() {
      return featureWallInfo;
    },
    
    // 手动刷新功能权限（避免与内部refresh方法冲突）
    manualRefresh: async function() {
      console.log('[FeatureWall] 手动刷新功能权限信息...');
      const result = await this.refresh();
      
      // 触发权限更新事件，通知其他组件
      if (result) {
        window.dispatchEvent(new CustomEvent('featureWallUpdated', {
          detail: { 
            featureWallInfo: featureWallInfo,
            timestamp: Date.now()
          }
        }));
        console.log('[FeatureWall] 权限刷新完成，已触发更新事件');
      }
      
      return result;
    },
    
    // 检查多个功能（批量检查）
    hasFeatures: function(...features) {
      return features.every(feature => this.hasFeature(feature));
    },
    
    // 检查任一功能（OR逻辑）
    hasAnyFeature: function(...features) {
      return features.some(feature => this.hasFeature(feature));
    }
  };
  
  // 暴露到全局
  window.FeatureWall = FeatureWall;
  
  // 页面加载完成后初始化
  // 轻量级空闲调度，避免阻塞渲染/其他连接
  function idle(fn, timeout = 1000) {
    if (typeof window.requestIdleCallback === 'function') {
      return window.requestIdleCallback(fn, { timeout });
    }
    return setTimeout(fn, 0);
  }

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => idle(() => FeatureWall.init()));
  } else {
    idle(() => FeatureWall.init());
  }
})();
