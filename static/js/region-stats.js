/**
 * region-stats.js
 * 地区分布功能模块 - 修复版本
 * 提供地区统计和筛选功能
 */

// 避免重复声明
if (typeof window.RegionStatsModule === 'undefined') {
window.RegionStatsModule = {
    // DOM 元素引用 - 改为函数形式，确保每次获取最新元素
    getElements() {
        return {
            regionStats: document.getElementById('region-stats')
        };
    },

    // 元素缓存
    elements: null,

    // 当前的地区统计数据
    regionData: new Map(),

    // 当前激活的筛选器
    activeFilter: null,

    // 图标缓存池 - 用于缓存已加载的图标HTML
    iconCache: {},

    // 自动更新设置
    updateSettings: {
        enabled: false,          // 是否启用自动更新
        interval: 300000,        // 5分钟更新间隔（毫秒）- 减少频繁刷新
        timer: null              // 定时器引用
    },

    // 显示数据哈希，用于防止重复更新
    _lastDisplayHash: '',
    _lastDataHash: '',

    // 初始化状态
    _initialized: false,

    /**
     * 初始化地区统计模块
     */
    init() {
        // 检查是否已经初始化，避免重复初始化
        if (this._initialized) {
            console.debug('地区统计模块已经初始化，跳过');
            return this;
        }

        console.debug('初始化地区统计模块');

        try {
            // 将模块实例先添加到window对象以便全局访问
            window.RegionStatsModule = this;

            // 获取必要的DOM元素
            this.elements = this.getElements();

            // 检查是否存在地区统计容器
            if (!this.elements.regionStats) {
                console.debug('当前页面不包含地区统计组件，仅初始化筛选功能');
                // 仅设置筛选事件监听器
                this.setupFilterListeners();
                // 标记为部分初始化
                this._initialized = 'partial';
                return this;
            }

            // 初始化样式
            this.initStyles();

            // 设置事件监听器
            this.setupEventListeners();

            // 加载初始数据
            this.loadInitialData();

            // 开始自动更新
            this.startAutoUpdate();

            // 标记为完全初始化
            this._initialized = true;

            console.debug('地区统计模块初始化完成');
        } catch (error) {
            console.error('地区统计模块初始化失败:', error);
            // 即使出错也标记为已尝试初始化，避免无限重试
            this._initialized = 'error';
        }

        return this;
    },

    /**
     * 初始化样式
     */
    initStyles() {
        // 样式已移动到components.css中
        // 不再需要动态添加样式
    },

    /**
     * 设置筛选事件监听器（用于没有地区统计容器的页面）
     */
    setupFilterListeners() {
        // 监听节点数据更新完成事件
        document.addEventListener('statsSyncComplete', () => {
            // 当有新的节点数据更新时，更新地区统计数据
            if (window.lastNodeData) {
                this.collectRegionStats(window.lastNodeData);
            }
        });

        console.debug('地区筛选监听器已设置');
    },

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听窗口加载完成事件
        window.addEventListener('load', () => {
            // 添加全局点击事件，当点击页面空白处时重置地区筛选
            document.addEventListener('click', (event) => {
                // 如果点击的不是地区筛选元素，且不是其子元素
                if (!event.target.closest('[data-region]')) {
                    // 检查是否有激活的筛选器
                    if (this.activeFilter) {
                        this.resetFilter();
                    }
                }
            });

            // 添加地区标签点击事件委托
            const regionStatsContainers = document.querySelectorAll('#region-stats');
            if (regionStatsContainers && regionStatsContainers.length > 0) {
                regionStatsContainers.forEach((container, index) => {
                    container.addEventListener('click', (e) => {
                        // 防止事件冒泡，避免触发文档点击监听器
                        e.stopPropagation();

                        // 查找最近的地区筛选元素
                        const filterElement = e.target.closest('[data-region]');
                        if (filterElement && filterElement.dataset.region) {
                            console.debug(`点击了地区标签 (容器 #${index+1}):`, filterElement.dataset.region);
                            // 使用数据属性中的地区代码进行筛选
                            this.filterByRegion(filterElement.dataset.region);
                        }
                    });
                });
            } else {
                console.debug('未找到地区标签容器，无法添加点击事件');
            }

            // 监听节点数据更新完成事件 - 降低频率
            let updateTimeout;
            document.addEventListener('statsSyncComplete', () => {
                // 防抖处理，避免频繁更新
                if (updateTimeout) {
                    clearTimeout(updateTimeout);
                }
                updateTimeout = setTimeout(() => {
                    if (window.lastNodeData) {
                        this.update(window.lastNodeData);
                    }
                }, 2000); // 2秒防抖延迟
            });
        });
    },

    /**
     * 加载初始数据
     */
    async loadInitialData() {
        // 优先使用已有的全局数据
        if (window.lastNodeData) {
            this.update(window.lastNodeData);
            return;
        }

        // 主动从API获取数据，不等待WebSocket
        console.debug('主动获取节点数据用于地区统计...');
        performance.mark('api-allnode_status-start');
        console.time('api-allnode_status');
        try {
            const response = await fetch('/api/allnode_status');
            const result = await response.json();
            console.timeEnd('api-allnode_status');
            performance.mark('api-allnode_status-end');
            performance.measure('api-allnode_status-duration', 'api-allnode_status-start', 'api-allnode_status-end');
            
            if (result.success && result.data) {
                console.debug('成功获取节点数据，更新地区统计');
                // 保存到全局变量供其他模块使用
                window.lastNodeData = result.data;
                // 立即更新地区统计
                this.update(result.data);
            } else {
                console.debug('API返回数据为空，等待WebSocket数据');
            }
        } catch (error) {
            console.error('获取节点数据失败:', error);
            console.debug('降级到等待WebSocket推送数据');
        }
    },

    /**
     * 开始自动更新
     */
    startAutoUpdate(interval) {
        // 如果已经在运行，先停止
        this.stopAutoUpdate();

        // 设置更新间隔
        if (interval) {
            this.updateSettings.interval = interval;
        }

        // 启用自动更新
        this.updateSettings.enabled = true;

        // 启动定时器
        this.updateSettings.timer = setInterval(() => {
            if (window.lastNodeData) {
                this.update(window.lastNodeData);
            }
        }, this.updateSettings.interval);

        console.debug(`地区统计自动更新已启动，间隔: ${this.updateSettings.interval}ms`);
    },

    /**
     * 停止自动更新
     */
    stopAutoUpdate() {
        if (this.updateSettings.timer) {
            clearInterval(this.updateSettings.timer);
            this.updateSettings.timer = null;
            this.updateSettings.enabled = false;
            console.debug('地区统计自动更新已停止');
        }
    },

    /**
     * 从节点数据中收集地区统计信息
     */
    collectRegionStats(nodesData) {
        const regionStats = new Map();
        let processedNodes = 0;
        let onlineNodes = 0;
        let nodesWithRegion = 0;

        // 处理每个节点
        Object.entries(nodesData || {}).forEach(([, node]) => {
            processedNodes++;
            // 跳过非节点数据
            if (!node || typeof node !== 'object' || !node.name) return;

            // 统计地区分布(包含所有节点，包括离线节点)
            const isOnline = node.stat && typeof node.stat === 'object' && !node.stat.offline;
            if (isOnline) {
                onlineNodes++;
            }

            // 检查地区信息 - 使用新的数据结构，包括离线节点
            let regionCode = null;
            if (node.data?.location?.code) {
                regionCode = node.data.location.code;
            } else if (node.region) {
                regionCode = node.region;
            } else {
                regionCode = 'UNKNOWN';
            }

            if (regionCode && regionCode !== 'UNKNOWN') {
                nodesWithRegion++;

                if (!regionStats.has(regionCode)) {
                    regionStats.set(regionCode, {
                        code: regionCode,
                        name: this._getRegionName(regionCode),
                        flag: this._getRegionFlag(regionCode),
                        count: 0,
                        onlineCount: 0,
                        offlineCount: 0
                    });
                }
                
                const regionData = regionStats.get(regionCode);
                regionData.count++;
                
                if (isOnline) {
                    regionData.onlineCount++;
                } else {
                    regionData.offlineCount++;
                }

                // 将地区信息添加到节点数据上，用于后续筛选
                if (!node.regionCode) {
                    node.regionCode = regionCode;
                }
            }
        });


        this.regionData = regionStats;
        return regionStats;
    },

    /**
     * 获取地区名称
     */
    _getRegionName(regionCode) {
        const countryMap = {
            'CN': '中国',
            'US': '美国',
            'JP': '日本',
            'KR': '韩国',
            'SG': '新加坡',
            'HK': '香港',
            'TW': '台湾',
            'GB': '英国',
            'UK': '英国',
            'DE': '德国',
            'FR': '法国',
            'RU': '俄罗斯',
            'CA': '加拿大',
            'AU': '澳大利亚',
            'LO': '本地网络',
            'OT': '其他地区'
        };

        return countryMap[regionCode] || `未知(${regionCode})`;
    },

    /**
     * 获取地区国旗
     */
    _getRegionFlag(regionCode) {
        if (regionCode === 'LO' || regionCode === 'OT') {
            return null; // 特殊地区使用图标
        }
        
        // UK 特殊处理
        const flagCode = regionCode === 'UK' ? 'GB' : regionCode;
        return `/img/flags/${flagCode}.SVG?v=1`;
    },

    /**
     * 获取排序后的前N个地区统计
     */
    getTopRegions(limit = 9) {
        return Array.from(this.regionData.values())
            .sort((a, b) => b.count - a.count)
            .slice(0, limit);
    },

    /**
     * 更新DOM中的地区统计显示 - 稳定版本
     */
    updateRegionStatsDisplay(topRegions) {
        if (!this.elements.regionStats) return;

        // 防止频繁更新 - 只有在数据真正变化时才更新
        const newDataHash = this._generateDataHash(topRegions);
        if (this._lastDisplayHash === newDataHash) {
            return;
        }
        this._lastDisplayHash = newDataHash;
        
        // 调试信息
        console.debug('更新地区显示，容器宽度:', this.elements.regionStats.offsetWidth);
        console.debug('父容器宽度:', this.elements.regionStats.parentElement?.offsetWidth);

        // 创建国旗元素
        const createFlagHtml = (region) => {
            const cacheKey = region.code;

            // 如果有缓存直接使用
            if (this.iconCache[cacheKey]) {
                return this.iconCache[cacheKey];
            }

            let flagHtml;
            if (region.flag) {
                flagHtml = `<img src="${region.flag}" alt="${region.code}" class="region-tag-flag" title="${region.code}" loading="lazy" data-region="${region.code}">`;
            } else if (region.code === 'LO') {
                flagHtml = '<i class="ti ti-home region-tag-flag" style="font-size: 14px;" title="本地网络"></i>';
            } else if (region.code === 'OT') {
                flagHtml = '<i class="ti ti-world region-tag-flag" style="font-size: 14px;" title="其他网络"></i>';
            } else {
                flagHtml = '<i class="ti ti-help-circle region-tag-flag" style="font-size: 14px;" title="未知地区"></i>';
            }

            this.iconCache[cacheKey] = flagHtml;
            return flagHtml;
        };

        // 清空当前内容
        this.elements.regionStats.innerHTML = '';

        // 限制显示最多9个地区，确保合理的布局
        const displayRegions = topRegions.slice(0, 9);

        // 创建地区标签
        displayRegions.forEach(region => {
            const tagContainer = document.createElement('a');
            tagContainer.className = 'region-tag';
            
            if (region.code === this.activeFilter) {
                tagContainer.classList.add('active');
            }
            
            tagContainer.dataset.region = region.code;
            tagContainer.title = `点击查看${region.name}的节点 (${region.count}个)`;

            // 创建内容
            tagContainer.innerHTML = `
                ${createFlagHtml(region)}
                <span class="region-tag-code">${region.code}</span>
                <span class="region-tag-count">${region.count}</span>
            `;

            this.elements.regionStats.appendChild(tagContainer);
        });

        // 添加图片错误处理
        this.elements.regionStats.querySelectorAll('img.region-tag-flag').forEach(img => {
            img.onerror = () => {
                const regionCode = img.dataset.region || img.alt;
                img.style.display = 'none';
                
                const icon = document.createElement('i');
                icon.className = 'ti ti-help-circle region-tag-flag';
                icon.style.fontSize = '14px';
                icon.title = '地区图标加载失败';
                img.parentNode.appendChild(icon);
                
                if (regionCode) {
                    this.iconCache[regionCode] = `<i class="ti ti-help-circle region-tag-flag" style="font-size: 14px;" title="地区图标加载失败"></i>`;
                }
            };
        });

        console.debug(`地区统计显示已更新: ${displayRegions.length} 个地区`);
    },

    /**
     * 生成数据哈希用于比较
     */
    _generateDataHash(regions) {
        return regions.map(r => `${r.code}:${r.count}`).join('|');
    },

    /**
     * 根据地区代码筛选服务器卡片
     */
    filterByRegion(regionCode) {
        console.debug('按地区筛选:', regionCode);

        try {
            // 如果点击的是当前激活的筛选器，则重置筛选
            if (this.activeFilter === regionCode) {
                console.debug('点击当前激活筛选器，将重置筛选');
                this.resetFilter();
                return;
            }

            // 使用性能优化器（如果可用）
            if (window.RegionPerformanceOptimizer) {
                this._optimizedFilterByRegion(regionCode);
                return;
            }

            // 后备实现
            this._fallbackFilterByRegion(regionCode);
        } catch (error) {
            console.error('按地区筛选时出错:', error);
        }
    },

    /**
     * 使用性能优化器的筛选方法
     */
    _optimizedFilterByRegion(regionCode) {
        const startTime = performance.now();
        
        // 更新激活状态
        this.activeFilter = regionCode;
        
        // 调用优化器
        window.RegionPerformanceOptimizer.optimizedFilterByRegion(regionCode);
        
        // 更新UI状态
        this.updateFilterButtonsUI();
        
        const endTime = performance.now();
        console.debug(`[优化筛选] 完成: ${regionCode}, 耗时: ${(endTime - startTime).toFixed(2)}ms`);
        
        // 触发事件
        const event = new CustomEvent('filterUpdated', {
            detail: {
                type: 'region',
                value: regionCode,
                optimized: true,
                duration: endTime - startTime
            }
        });
        document.dispatchEvent(event);
    },

    /**
     * 后备筛选实现
     */
    _fallbackFilterByRegion(regionCode) {
        this.activeFilter = regionCode;

        const cardContainer = document.getElementById('card-grid-container');
        if (!cardContainer) {
            console.warn('未找到卡片容器');
            return;
        }

        const allCards = cardContainer.querySelectorAll('.server-card');
        let matchCount = 0;

        // 使用新的统一筛选器
        if (window.UF) {
            window.UF.filterByRegion(regionCode);
            matchCount = allCards.length;
            allCards.forEach(card => {
                const cardRegion = card.dataset.region;
                if (cardRegion === regionCode) {
                    matchCount++;
                }
            });
        } else {
            // 后备方案
            allCards.forEach(card => {
                const cardRegion = card.dataset.region;
                const isMatch = cardRegion === regionCode;

                if (isMatch) {
                    matchCount++;
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        this.updateFilterButtonsUI();
        
        console.debug(`筛选结果: ${matchCount} 张卡片匹配地区 ${regionCode}`);
    },

    /**
     * 重置地区筛选
     */
    resetFilter() {
        console.debug('重置地区筛选');

        this.activeFilter = null;

        const allCards = document.querySelectorAll('.server-card');
        let visibleCount = 0;

        // 使用新的统一筛选器
        if (window.UF) {
            window.UF.filterByRegion('ALL');
            visibleCount = allCards.length;
        } else {
            // 后备方案
            allCards.forEach(card => {
                card.style.display = '';
                visibleCount++;
            });
        }

        this.updateFilterButtonsUI();

        console.debug(`筛选重置后: ${visibleCount} 张卡片可见`);
    },

    /**
     * 更新筛选按钮状态
     */
    updateFilterButtonsUI() {
        const filterButtons = this.elements.regionStats?.querySelectorAll('.region-tag[data-region]');
        if (!filterButtons) return;

        filterButtons.forEach(el => {
            if (el.dataset.region === this.activeFilter) {
                el.classList.add('active');
            } else {
                el.classList.remove('active');
            }
        });

        // 更新筛选指示器
        const indicator = document.getElementById('region-filter-indicator');
        if (indicator) {
            if (this.activeFilter) {
                indicator.classList.remove('hidden');
            } else {
                indicator.classList.add('hidden');
            }
        }
    },

    /**
     * 更新地区统计 - 智能更新版本
     */
    update(nodesData) {
        try {
            // 如果没有提供数据，尝试使用全局数据
            if (!nodesData && window.lastNodeData) {
                nodesData = window.lastNodeData;
            }

            // 如果仍然没有数据，退出
            if (!nodesData) {
                console.debug('无节点数据可用于更新地区统计');
                return;
            }

            // 每次更新时重新获取DOM元素
            this.elements = this.getElements();

            // 检查DOM元素是否存在
            if (!this.elements.regionStats) {
                return;
            }

            // 生成当前数据的哈希用于比较
            const currentDataHash = JSON.stringify(nodesData).slice(0, 100); // 简化的哈希
            if (this._lastDataHash === currentDataHash) {
                return;
            }
            this._lastDataHash = currentDataHash;

            // 收集地区统计数据
            this.collectRegionStats(nodesData);

            if (this.regionData.size === 0) {
                console.debug('无地区数据可显示');
                this.elements.regionStats.innerHTML = `
                    <div class="region-stats-loading">
                        <i class="ti ti-refresh animate-spin"></i>
                        加载地区数据...
                    </div>
                `;
                return;
            }

            // 获取并显示前9个地区
            const topRegions = this.getTopRegions(9);
            this.updateRegionStatsDisplay(topRegions);


            // 触发地区统计更新完成事件
            const event = new CustomEvent('regionStatsUpdated', {
                detail: {
                    timestamp: Date.now(),
                    regionCount: this.regionData.size
                }
            });
            document.dispatchEvent(event);
        } catch (error) {
            console.error('地区统计更新失败:', error);
        }
    },

    /**
     * 强制刷新地区统计数据
     */
    forceUpdate(nodesData) {
        // 使用提供的数据或全局数据
        const data = nodesData || window.lastNodeData;

        if (!data) {
            console.warn('无数据可用于强制更新地区统计');
            return;
        }

        // 清空现有数据
        this.regionData.clear();

        // 调用更新
        this.update(data);

        // 通知已移到 region-refresh.js 中处理，避免重复
        console.info('地区分布已更新');
    }
};

// 关闭 RegionStatsModule 对象声明
} // if (typeof window.RegionStatsModule === 'undefined')

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 首先检查是否已经初始化，避免重复初始化
    if (window.RegionStatsModule && window.RegionStatsModule._initialized) {
        console.debug('RegionStatsModule 已经初始化，跳过');
        return;
    }

    // 添加短暂延迟，确保DOM完全渲染
    setTimeout(() => {
        try {
            console.debug('开始初始化 RegionStatsModule...');
            if (window.RegionStatsModule) {
                window.RegionStatsModule.init();
            } else {
                console.warn('RegionStatsModule 未定义，无法初始化');
            }
        } catch (error) {
            console.error('初始化 RegionStatsModule 时出错:', error);
        }
    }, 100);
});

// 添加页面完全加载后的备份初始化
window.addEventListener('load', () => {
    if (window.RegionStatsModule && !window.RegionStatsModule._initialized) {
        console.debug('页面完全加载后，检测到 RegionStatsModule 未初始化，尝试再次初始化');
        try {
            window.RegionStatsModule.init();
        } catch (error) {
            console.error('备份初始化 RegionStatsModule 时出错:', error);
        }
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.RegionStatsModule && typeof window.RegionStatsModule.stopAutoUpdate === 'function') {
        window.RegionStatsModule.stopAutoUpdate();
    }
});