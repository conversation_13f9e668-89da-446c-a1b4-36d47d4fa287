/**
 * RegionCacheManager.js
 * 地区分布数据的专用缓存管理器
 * 提供高性能的数据缓存、比较和增量更新功能
 */

window.RegionCacheManager = (function() {
    'use strict';

    // 缓存配置
    const CACHE_CONFIG = {
        maxAge: 5 * 60 * 1000,      // 5分钟缓存过期时间
        maxSize: 100,               // 最大缓存条目数
        enableCompression: true,    // 启用数据压缩
        enableDiff: true           // 启用差分比较
    };

    // 内存缓存存储
    const cache = {
        regionData: new Map(),        // 地区统计数据缓存
        displayHash: new Map(),       // 显示哈希缓存
        rawNodeData: null,           // 原始节点数据缓存
        lastUpdate: 0,               // 最后更新时间
        metadata: new Map()          // 元数据缓存
    };

    /**
     * 生成数据哈希值
     */
    function generateHash(data) {
        if (!data || typeof data !== 'object') return '';
        if (data instanceof Map) {
            return Array.from(data.entries())
                .map(([code, region]) => `${code}:${region.count}:${region.name}`)
                .sort().join('|');
        }
        try {
            const jsonStr = JSON.stringify(data, (key, value) => {
                if (typeof value === 'function' || value === data) return undefined;
                return value;
            });
            return btoa(jsonStr).substring(0, 32);
        } catch (e) {
            return Object.keys(data).length.toString() + '_' + Date.now().toString().slice(-8);
        }
    }

    /**
     * 压缩数据
     */
    function compressData(data) {
        if (!CACHE_CONFIG.enableCompression) return data;
        if (data instanceof Map) {
            const compressed = new Map();
            for (const [code, region] of data.entries()) {
                compressed.set(code, {
                    c: region.count,
                    n: region.name,
                    f: region.flag
                });
            }
            return compressed;
        }
        return data;
    }

    /**
     * 解压数据
     */
    function decompressData(compressedData) {
        if (!CACHE_CONFIG.enableCompression) return compressedData;
        if (compressedData instanceof Map) {
            const decompressed = new Map();
            for (const [code, region] of compressedData.entries()) {
                decompressed.set(code, {
                    code: code,
                    count: region.c,
                    name: region.n,
                    flag: region.f
                });
            }
            return decompressed;
        }
        return compressedData;
    }

    /**
     * 检查缓存是否过期
     */
    function isCacheExpired(key) {
        const metadata = cache.metadata.get(key);
        if (!metadata) return true;
        return (Date.now() - metadata.timestamp) > CACHE_CONFIG.maxAge;
    }

    /**
     * 清理过期缓存
     */
    function cleanupExpiredCache() {
        const now = Date.now();
        const keysToDelete = [];
        for (const [key, metadata] of cache.metadata.entries()) {
            if ((now - metadata.timestamp) > CACHE_CONFIG.maxAge) {
                keysToDelete.push(key);
            }
        }
        keysToDelete.forEach(key => {
            cache.regionData.delete(key);
            cache.displayHash.delete(key);
            cache.metadata.delete(key);
        });
        if (keysToDelete.length > 0) {
            console.debug(`[RegionCache] 清理了 ${keysToDelete.length} 个过期缓存项`);
        }
    }

    /**
     * 设置地区数据缓存
     */
    function setRegionData(key, regionData, options = {}) {
        try {
            cleanupExpiredCache();
            const compressedData = compressData(regionData);
            const hash = generateHash(regionData);
            
            cache.regionData.set(key, compressedData);
            cache.displayHash.set(key, hash);
            cache.metadata.set(key, {
                timestamp: Date.now(),
                hash: hash,
                size: regionData.size,
                options: options
            });

            console.debug(`[RegionCache] 缓存地区数据: ${key}, 包含 ${regionData.size} 个地区`);
        } catch (error) {
            console.error('[RegionCache] 设置地区数据缓存失败:', error);
        }
    }

    /**
     * 获取地区数据缓存
     */
    function getRegionData(key) {
        try {
            if (isCacheExpired(key)) {
                console.debug(`[RegionCache] 缓存已过期: ${key}`);
                return null;
            }

            const compressedData = cache.regionData.get(key);
            if (!compressedData) return null;

            return decompressData(compressedData);
        } catch (error) {
            console.error('[RegionCache] 获取地区数据缓存失败:', error);
            return null;
        }
    }

    /**
     * 检查数据是否发生变化
     */
    function hasDataChanged(key, newData) {
        if (!CACHE_CONFIG.enableDiff) return true;
        const oldHash = cache.displayHash.get(key);
        if (!oldHash) return true;
        const newHash = generateHash(newData);
        return oldHash !== newHash;
    }

    /**
     * 设置原始节点数据缓存
     */
    function setRawNodeData(nodeData) {
        try {
            cache.rawNodeData = nodeData;
            cache.lastUpdate = Date.now();
            
            localStorage.setItem('region_cache_nodes', JSON.stringify({
                data: nodeData,
                timestamp: cache.lastUpdate
            }));

            console.debug('[RegionCache] 缓存原始节点数据，节点数量:', Object.keys(nodeData || {}).length);
        } catch (error) {
            console.error('[RegionCache] 设置原始节点数据失败:', error);
        }
    }

    /**
     * 获取原始节点数据缓存
     */
    function getRawNodeData() {
        if (cache.rawNodeData && (Date.now() - cache.lastUpdate) < CACHE_CONFIG.maxAge) {
            return cache.rawNodeData;
        }

        try {
            const stored = localStorage.getItem('region_cache_nodes');
            if (stored) {
                const parsed = JSON.parse(stored);
                if (parsed.data && (Date.now() - parsed.timestamp) < CACHE_CONFIG.maxAge * 2) {
                    cache.rawNodeData = parsed.data;
                    cache.lastUpdate = parsed.timestamp;
                    console.debug('[RegionCache] 从localStorage恢复节点数据');
                    return cache.rawNodeData;
                }
            }
        } catch (error) {
            console.error('[RegionCache] 从localStorage恢复节点数据失败:', error);
        }
        return null;
    }

    /**
     * 清除所有缓存
     */
    function clearAll() {
        cache.regionData.clear();
        cache.displayHash.clear();
        cache.metadata.clear();
        cache.rawNodeData = null;
        cache.lastUpdate = 0;
        localStorage.removeItem('region_cache_nodes');
        console.debug('[RegionCache] 所有缓存已清除');
    }

    /**
     * 获取缓存统计信息
     */
    function getStats() {
        return {
            regionDataCount: cache.regionData.size,
            memoryUsage: {
                regionData: cache.regionData.size,
                displayHash: cache.displayHash.size,
                metadata: cache.metadata.size
            },
            lastUpdate: cache.lastUpdate,
            hasRawData: !!cache.rawNodeData,
            config: { ...CACHE_CONFIG }
        };
    }

    /**
     * 获取地区数据的快速校验码
     */
    function getChecksum(regionData) {
        if (!regionData || regionData.size === 0) return '0';
        const totalCount = Array.from(regionData.values()).reduce((sum, region) => sum + region.count, 0);
        const regionCount = regionData.size;
        const firstRegion = Array.from(regionData.keys())[0] || '';
        return `${totalCount}_${regionCount}_${firstRegion}`;
    }

    // 定期优化缓存（每5分钟）
    setInterval(cleanupExpiredCache, 5 * 60 * 1000);

    // 公共API
    return {
        setRegionData,
        getRegionData,
        hasDataChanged,
        setRawNodeData,
        getRawNodeData,
        generateHash,
        getChecksum,
        clearAll,
        getStats,
        getConfig: () => ({ ...CACHE_CONFIG }),
        setConfig: (newConfig) => Object.assign(CACHE_CONFIG, newConfig)
    };
})();