/**
 * 统一的度量格式化工具类
 * 用于替代分散在各个文件中的格式化函数
 * 
 * 设计原则:
 * 1. 只返回数据，不返回HTML
 * 2. 与CSS系统配合，实现样式分离
 * 3. 统一的计算逻辑，避免不一致
 */

class MetricFormatter {
    /**
     * 格式化网络速度 (bps) - 增强版，支持到 YB 级别
     * @param {number} bps - 每秒字节数
     * @param {Object} options - 选项 {scale: 'auto'|'small'|'medium'|'large'|'enterprise', precision: number}
     * @returns {Object} {value: string, unit: string, scale: string}
     */
    static formatSpeed(bps, options = {}) {
        if (isNaN(bps) || bps === 0) {
            return { value: '0', unit: 'bps', scale: 'small' };
        }

        const k = 1024;
        const sizes = ['bps', 'Kbps', 'Mbps', 'Gbps', 'Tbps', 'Pbps', 'Ebps', 'Zbps', 'Ybps'];
        const i = Math.floor(Math.log(Math.abs(bps)) / Math.log(k));
        const unitIndex = Math.min(i, sizes.length - 1);
        const scaledValue = bps / Math.pow(k, unitIndex);
        
        // 检测流量规模
        const scale = options.scale === 'auto' || !options.scale 
            ? this.detectTrafficScale(bps)
            : options.scale;
        
        // 获取智能精度
        const precision = options.precision !== undefined 
            ? options.precision 
            : this.getSmartPrecision(scaledValue, unitIndex, scale);
        
        const value = scaledValue.toFixed(precision);

        return {
            value: parseFloat(value).toString(),
            unit: sizes[unitIndex],
            scale: scale
        };
    }

    /**
     * 格式化字节大小 - 增强版，支持到 YB 级别
     * @param {number} bytes - 字节数
     * @param {Object} options - 选项 {scale: 'auto'|'small'|'medium'|'large'|'enterprise', precision: number}
     * @returns {Object} {value: string, unit: string, scale: string}
     */
    static formatBytes(bytes, options = {}) {
        if (isNaN(bytes) || bytes === 0) {
            return { value: '0', unit: 'B', scale: 'small' };
        }

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        const i = Math.floor(Math.log(Math.abs(bytes)) / Math.log(k));
        const unitIndex = Math.min(i, sizes.length - 1);
        const scaledValue = bytes / Math.pow(k, unitIndex);
        
        // 检测流量规模
        const scale = options.scale === 'auto' || !options.scale 
            ? this.detectTrafficScale(bytes)
            : options.scale;
        
        // 获取智能精度
        const precision = options.precision !== undefined 
            ? options.precision 
            : this.getSmartPrecision(scaledValue, unitIndex, scale);

        const value = scaledValue.toFixed(precision);

        return {
            value: parseFloat(value).toString(),
            unit: sizes[unitIndex],
            scale: scale
        };
    }

    /**
     * 格式化百分比
     * @param {number} value - 数值 (0-100)
     * @param {number} decimals - 小数位数，默认1位
     * @returns {Object} {value: string, unit: string}
     */
    static formatPercentage(value, decimals = 1) {
        if (isNaN(value)) {
            return { value: '0', unit: '%' };
        }

        const clampedValue = Math.max(0, Math.min(100, value));
        const formattedValue = clampedValue.toFixed(decimals);

        return {
            value: parseFloat(formattedValue).toString(),
            unit: '%'
        };
    }

    /**
     * 格式化在线时间 (秒)
     * @param {number} seconds - 秒数
     * @returns {Object} {value: string, unit: string}
     */
    static formatUptime(seconds) {
        if (!seconds || isNaN(seconds) || seconds <= 0) {
            return { value: '0', unit: '天' };
        }

        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);

        // 优先显示天数
        if (days > 0) {
            return { value: days.toString(), unit: '天' };
        } else if (hours > 0) {
            return { value: hours.toString(), unit: '小时' };
        } else {
            return { value: Math.max(1, minutes).toString(), unit: '分钟' };
        }
    }

    /**
     * 应用格式化结果到DOM元素
     * @param {HTMLElement} element - 目标元素
     * @param {Object} formatResult - 格式化结果 {value, unit}
     * @param {string} sizeClass - 尺寸类名 ('large', 'medium', 'small')
     */
    static applyToElement(element, formatResult, sizeClass = 'medium') {
        if (!element || !formatResult) return;

        // 设置数值
        element.textContent = formatResult.value;
        
        // 设置单位属性
        element.setAttribute('data-unit', formatResult.unit);
        
        // 添加必要的CSS类
        // 如果元素已使用 metric-number 系统，则不添加 metric-display 类（防止双系统冲突）
        if (!element.classList.contains('metric-display') && !element.classList.contains('metric-number')) {
            element.classList.add('metric-display');
        }
        
        // 设置尺寸类
        element.classList.remove('large', 'medium', 'small');
        element.classList.add(sizeClass);
    }

    /**
     * 批量更新元素
     * @param {string} selector - CSS选择器
     * @param {number} value - 要格式化的数值
     * @param {string} type - 格式化类型 ('speed', 'bytes', 'percentage', 'uptime')
     * @param {string} sizeClass - 尺寸类名
     */
    static updateElements(selector, value, type = 'speed', sizeClass = 'medium') {
        const elements = document.querySelectorAll(selector);
        if (elements.length === 0) return;

        let formatResult;
        switch (type) {
            case 'speed':
                formatResult = this.formatSpeed(value);
                break;
            case 'bytes':
                formatResult = this.formatBytes(value);
                break;
            case 'percentage':
                formatResult = this.formatPercentage(value);
                break;
            case 'uptime':
                formatResult = this.formatUptime(value);
                break;
            default:
                console.warn(`Unknown format type: ${type}`);
                return;
        }

        elements.forEach(element => {
            this.applyToElement(element, formatResult, sizeClass);
        });
    }

    /**
     * 检测流量规模级别
     * @param {number} value - 数值（字节或bps）
     * @returns {string} 规模级别 ('small'|'medium'|'large'|'enterprise')
     */
    static detectTrafficScale(value) {
        const absValue = Math.abs(value);
        const k = 1024;
        
        if (absValue < Math.pow(k, 3)) return 'small';        // < 1GB
        if (absValue < Math.pow(k, 4)) return 'medium';       // 1GB-1TB  
        if (absValue < Math.pow(k, 6)) return 'large';        // 1TB-1PB
        return 'enterprise';                                   // > 1PB
    }
    
    /**
     * 获取智能精度控制
     * @param {number} scaledValue - 缩放后的数值
     * @param {number} unitIndex - 单位索引
     * @param {string} scale - 流量规模
     * @returns {number} 小数位数
     */
    static getSmartPrecision(scaledValue, unitIndex, scale) {
        // 根据流量规模决定基础精度
        const basePrecision = {
            small: 2,     // 小流量：精细显示
            medium: 1,    // 中流量：标准显示
            large: 1,     // 大流量：简化显示
            enterprise: 0 // 企业级：整数显示
        }[scale] || 1;
        
        // YB/ZB 级别特殊处理
        if (unitIndex >= 7) return Math.min(basePrecision + 1, 3);
        
        // PB/EB 级别适当增加精度
        if (unitIndex >= 5) return Math.min(basePrecision + 1, 2);
        
        // 小数值增加精度
        if (scaledValue < 1) return Math.min(basePrecision + 1, 2);
        if (scaledValue < 10 && basePrecision > 0) return basePrecision;
        if (scaledValue >= 100) return Math.max(basePrecision - 1, 0);
        
        return basePrecision;
    }
    
    /**
     * 获取自适应动画配置
     * @param {number} currentValue - 当前值
     * @param {number} maxValue - 最大值
     * @returns {Object} 动画配置 {duration, precision, updateInterval}
     */
    static getAdaptiveAnimationConfig(currentValue, maxValue) {
        const maxVal = Math.max(Math.abs(currentValue), Math.abs(maxValue));
        const scale = this.detectTrafficScale(maxVal);
        
        const configs = {
            small: { duration: 800, updateInterval: 200, animSpeed: 'normal' },
            medium: { duration: 600, updateInterval: 150, animSpeed: 'fast' },
            large: { duration: 400, updateInterval: 100, animSpeed: 'fast' },
            enterprise: { duration: 300, updateInterval: 80, animSpeed: 'fast' }
        };
        
        return {
            ...configs[scale],
            scale: scale,
            precision: this.getSmartPrecision(maxVal, 0, scale)
        };
    }

    /**
     * 兼容性方法：返回HTML格式（用于渐进式迁移）
     * @param {number} bps - 每秒字节数
     * @param {Object} options - 选项
     * @returns {string} HTML字符串
     * @deprecated 请使用 formatSpeed() 和 CSS 系统
     */
    static formatSpeedHTML(bps, options = {}) {
        const result = this.formatSpeed(bps, options);
        return `${result.value} <span class="metric-unit">${result.unit}</span>`;
    }

    /**
     * 兼容性方法：返回字节HTML格式
     * @param {number} bytes - 字节数
     * @param {Object} options - 选项
     * @returns {string} HTML字符串
     * @deprecated 请使用 formatBytes() 和 CSS 系统
     */
    static formatBytesHTML(bytes, options = {}) {
        const result = this.formatBytes(bytes, options);
        return `${result.value} <span class="metric-unit">${result.unit}</span>`;
    }
}

// 导出到全局作用域，保持兼容性
if (typeof window !== 'undefined') {
    window.MetricFormatter = MetricFormatter;
}

// 兼容性别名，逐步替换现有函数
if (typeof window !== 'undefined') {
    // 提供过渡期的兼容函数
    window.formatSpeedNew = (bps, options) => MetricFormatter.formatSpeed(bps, options);
    window.formatBytesNew = (bytes, options) => MetricFormatter.formatBytes(bytes, options);
    
    // 新增的智能检测函数
    window.detectTrafficScale = (value) => MetricFormatter.detectTrafficScale(value);
    window.getAdaptiveAnimationConfig = (current, max) => MetricFormatter.getAdaptiveAnimationConfig(current, max);
    
    // 标记新系统可用
    window.METRIC_FORMATTER_AVAILABLE = true;
    window.METRIC_FORMATTER_VERSION = '2.0'; // 版本标识
}

// 模块导出（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MetricFormatter;
}
