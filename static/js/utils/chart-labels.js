/**
 * 统一的图表时间标签工具
 * 为负载详情和带宽监控提供一致的x轴时间标签显示逻辑
 */

(function(window) {
    'use strict';

    const ChartLabels = {
        /**
         * 格式化时间标签
         * @param {number} timestamp - 时间戳（毫秒）
         * @param {string} range - 时间范围
         * @returns {string} 格式化的时间字符串
         */
        formatTime: function(timestamp, range) {
            const date = new Date(timestamp);

            // 10分钟范围：显示 MM:SS
            if (range === '10min' || range === '10m') {
                return `${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
            }

            // 1小时范围：智能显示（密集=分钟，稀疏=秒）
            if (range === '1h') {
                // 这里仅做格式选择，密度判断在 generateSmartLabels 中完成
                return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
            }

            if (range === '24h') {
                return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
            }

            // 长时间范围：显示月-日
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `${month}-${day}`;
        },

        /**
         * 格式化完整时间用于Tooltip
         * @param {number} timestamp - 时间戳（毫秒）
         * @returns {string} 完整的时间字符串
         */
        formatFullTime: function(timestamp) {
            if (!timestamp || isNaN(timestamp)) return '时间未知';
            
            const date = new Date(timestamp);
            if (isNaN(date.getTime())) return '时间格式错误';
            
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const hour = date.getHours().toString().padStart(2, '0');
            const minute = date.getMinutes().toString().padStart(2, '0');
            
            return `${year}-${month}-${day} ${hour}:${minute}`;
        },

        /**
         * 智能生成时间标签数组
         * @param {Array<number>} timestamps - 时间戳数组（毫秒）
         * @param {string} range - 时间范围 ('10min', '1h', '24h', '7d', '30d', '60d')
         * @param {string} chartId - 图表容器ID，用于计算容器宽度
         * @returns {Array<string>} 标签数组
         */
        generateSmartLabels: function(timestamps, range, chartId) {
            const total = timestamps.length;
            if (!total) return [];

            // 响应式标签数量计算
            const chartDom = chartId ? document.getElementById(chartId) : null;
            const containerWidth = chartDom ? chartDom.clientWidth : window.innerWidth || 1200;
            const isMobile = window.innerWidth <= 600;
            
            // 根据屏幕宽度和时间范围确定最大标签数
            let maxLabels;
            if (range === '10min' || range === '1h') {
                maxLabels = isMobile ? 5 : Math.min(8, Math.floor(containerWidth / 100));
            } else if (range === '24h') {
                maxLabels = isMobile ? 6 : Math.min(12, Math.floor(containerWidth / 80));
            } else if (range === '7d') {
                maxLabels = isMobile ? 4 : Math.min(8, Math.floor(containerWidth / 100));
            } else {
                // 30天/60天
                maxLabels = isMobile ? 3 : Math.min(6, Math.floor(containerWidth / 120));
            }

            // 确保至少显示3个标签（起始、中间、结束）
            maxLabels = Math.max(3, maxLabels);

            // 计算显示间隔
            const step = Math.max(1, Math.floor(total / (maxLabels - 1)));

            // 生成标签
            return timestamps.map((timestamp, i) => {
                // 决定是否显示此位置的标签
                const shouldShow = i === 0 || i === total - 1 || (i % step === 0 && i < total - step);
                
                if (!shouldShow) return '';

                // 根据时间范围和设备类型选择格式
                if (range === '7d' && !isMobile) {
                    // 7天桌面端：显示日期+小时
                    const date = new Date(timestamp);
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    const hour = date.getHours().toString().padStart(2, '0');
                    return `${month}-${day} ${hour}:00`;
                } else if (range === '1h') {
                    // 1小时：智能格式
                    // 密集阈值：>=180个点认为密集，使用到“分钟”；更稀疏则显示“秒”
                    const dense = total >= 180;
                    const d = new Date(timestamp);
                    if (dense) {
                        return `${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}`;
                    } else {
                        return `${d.getMinutes().toString().padStart(2, '0')}:${d.getSeconds().toString().padStart(2, '0')}`;
                    }
                } else {
                    // 其他情况使用标准格式
                    return this.formatTime(timestamp, range);
                }
            });
        },

        /**
         * 获取ECharts轴标签配置
         * @param {Array<string>} labels - 标签数组
         * @param {string} theme - 主题 ('light' | 'dark')
         * @returns {Object} ECharts轴标签配置
         */
        getAxisLabelConfig: function(labels, theme) {
            const isMobile = window.innerWidth <= 600;
            
            return {
                data: labels,
                axisTick: { show: false },
                axisLabel: { 
                    interval: 0, // 显示所有标签（空字符串会被自动隐藏）
                    rotate: isMobile ? 45 : 0, // 移动端旋转标签
                    fontSize: isMobile ? 10 : 12,
                    color: theme === 'dark' ? '#94a3b8' : '#64748b'
                },
                axisLine: {
                    lineStyle: {
                        color: theme === 'dark' ? '#475569' : '#cbd5e1'
                    }
                },
                splitLine: { show: false }
            };
        },

        /**
         * 获取主题
         * @returns {string} 主题名称 ('light' | 'dark')
         */
        getTheme: function() {
            if (document.documentElement.classList.contains('dark') || 
                document.body.classList.contains('dark')) {
                return 'dark';
            }
            return 'light';
        }
    };

    // 将工具暴露到全局作用域
    window.ChartLabels = ChartLabels;

})(window);
