/**
 * ECharts统一加载管理器
 * 解决多个图表文件中重复的ECharts检查和加载逻辑
 */
window.EChartsLoader = {
    // 加载状态
    isLoaded: false,
    isLoading: false,
    
    // 回调队列
    callbacks: [],
    
    // 错误状态
    hasError: false,
    errorMessage: '',
    
    /**
     * 确保ECharts已加载，如果未加载则自动加载
     * @param {Function} callback - 加载完成后的回调函数
     */
    ensureLoaded(callback) {
        if (typeof callback !== 'function') {
            console.warn('EChartsLoader.ensureLoaded: callback必须是函数');
            return;
        }
        
        // 如果已加载，直接执行回调
        if (this.isLoaded) {
            callback();
            return;
        }
        
        // 如果有错误，不执行回调
        if (this.hasError) {
            console.error('ECharts加载失败:', this.errorMessage);
            return;
        }
        
        // 添加到回调队列
        this.callbacks.push(callback);
        
        // 如果正在加载，等待完成
        if (this.isLoading) {
            return;
        }
        
        // 检查ECharts是否已存在
        if (this._checkEChartsExists()) {
            this._onLoaded();
            return;
        }
        
        // 开始加载ECharts
        this._loadECharts();
    },
    
    /**
     * 检查ECharts是否已存在
     * @returns {boolean}
     */
    _checkEChartsExists() {
        return (typeof echarts !== 'undefined') || (typeof window.echarts !== 'undefined');
    },
    
    /**
     * 加载ECharts库
     */
    _loadECharts() {
        this.isLoading = true;
        
        console.log('开始加载ECharts库...');
        
        // 优先尝试本地文件
        this._loadScript('/js/echarts.min.js')
            .then(() => {
                console.log('本地ECharts库加载成功');
                this._onLoaded();
            })
            .catch(() => {
                console.warn('本地ECharts库加载失败，尝试CDN...');
                // 本地加载失败，尝试CDN
                this._loadScript('/js/libs/echarts.min.js')
                    .then(() => {
                        console.log('CDN ECharts库加载成功');
                        this._onLoaded();
                    })
                    .catch((error) => {
                        console.error('CDN ECharts库加载失败:', error);
                        this._onError('ECharts库加载失败，请检查网络连接');
                    });
            });
    },
    
    /**
     * 加载脚本文件
     * @param {string} src - 脚本URL
     * @returns {Promise}
     */
    _loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = () => {
                // 验证ECharts是否真正加载
                if (this._checkEChartsExists()) {
                    resolve();
                } else {
                    reject(new Error('脚本加载完成但ECharts对象不存在'));
                }
            };
            script.onerror = () => {
                reject(new Error(`脚本加载失败: ${src}`));
            };
            
            // 设置超时
            setTimeout(() => {
                if (script.readyState !== 'complete') {
                    reject(new Error(`脚本加载超时: ${src}`));
                }
            }, 10000); // 10秒超时
            
            document.head.appendChild(script);
        });
    },
    
    /**
     * 加载成功处理
     */
    _onLoaded() {
        this.isLoaded = true;
        this.isLoading = false;
        
        console.log('ECharts库加载完成，执行回调队列');
        
        // 执行所有回调
        this.callbacks.forEach(callback => {
            try {
                callback();
            } catch (error) {
                console.error('ECharts加载回调执行失败:', error);
            }
        });
        
        // 清空回调队列
        this.callbacks = [];
    },
    
    /**
     * 加载失败处理
     * @param {string} message - 错误信息
     */
    _onError(message) {
        this.hasError = true;
        this.errorMessage = message;
        this.isLoading = false;
        
        console.error('ECharts加载失败:', message);
        
        // 清空回调队列
        this.callbacks = [];
    },
    
    /**
     * 获取ECharts对象
     * @returns {object|null}
     */
    getECharts() {
        if (!this.isLoaded) {
            console.warn('ECharts尚未加载完成');
            return null;
        }
        
        return (typeof echarts !== 'undefined') ? echarts : window.echarts;
    },
    
    /**
     * 创建支持放大功能的ECharts实例
     * @param {HTMLElement|string} container - 容器元素或选择器
     * @param {string} theme - 主题名称
     * @param {Object} opts - ECharts初始化选项
     * @param {Object} enlargeConfig - 放大功能配置
     * @returns {Object} ECharts实例
     */
    createEnlargableChart(container, theme = null, opts = {}, enlargeConfig = {}) {
        const echart = this.getECharts();
        if (!echart) {
            console.error('ECharts未加载，无法创建图表实例');
            return null;
        }

        // 获取容器元素
        const containerElement = typeof container === 'string' 
            ? document.querySelector(container) 
            : container;

        if (!containerElement) {
            console.error('图表容器元素未找到');
            return null;
        }

        // 创建ECharts实例
        const instance = echart.init(containerElement, theme, opts);

        // 添加放大功能
        this._addEnlargeCapability(instance, enlargeConfig, containerElement);

        return instance;
    },

    /**
     * 为现有ECharts实例添加放大功能
     * @param {Object} chartInstance - ECharts实例
     * @param {Object} config - 配置对象
     * @param {string} title - 图表标题
     */
    addEnlargeCapability(chartInstance, config = {}, title = 'Chart Details') {
        if (!chartInstance) {
            console.warn('图表实例无效，无法添加放大功能');
            return;
        }

        this._addEnlargeCapability(chartInstance, { ...config, title });
    },

    /**
     * 内部方法：添加放大功能
     * @param {Object} instance - ECharts实例
     * @param {Object} config - 配置对象
     * @param {HTMLElement} container - 容器元素
     */
    _addEnlargeCapability(instance, config = {}, container = null) {
        const defaultConfig = {
            title: 'Chart Details',
            serverId: null,
            chartType: 'default',
            enableClick: true,
            cursorStyle: 'pointer',
            tooltipText: 'Click to enlarge chart',
            ...config
        };

        if (!defaultConfig.enableClick) {
            return;
        }

        // 获取容器元素
        const containerElement = container || instance.getDom();
        
        if (containerElement) {
            // 设置鼠标样式和提示
            containerElement.style.cursor = defaultConfig.cursorStyle;
            if (defaultConfig.tooltipText) {
                containerElement.title = defaultConfig.tooltipText;
            }

            // 添加点击事件
            const clickHandler = (params) => {
                // 防止在数据点、图例、坐标轴等组件上的点击事件
                if (params && (
                    params.componentType === 'series' ||
                    params.componentType === 'legend' ||
                    params.componentType === 'xAxis' ||
                    params.componentType === 'yAxis' ||
                    params.componentType === 'toolbox'
                )) {
                    return;
                }

                // 确保图表模态框组件已加载
                if (typeof window.chartModal !== 'undefined') {
                    // 获取当前图表配置
                    const currentOption = instance.getOption();
                    
                    // 构建传递给模态框的配置
                    const modalConfig = {
                        ...currentOption,
                        serverId: defaultConfig.serverId,
                        chartType: defaultConfig.chartType,
                        originalInstance: instance
                    };

                    // 打开模态框
                    window.chartModal.open(modalConfig, defaultConfig.title);
                } else {
                    console.warn('图表模态框组件未加载，无法打开放大视图');
                    // 可以在这里添加回退逻辑，比如打开新页面等
                    this._fallbackEnlarge(instance, defaultConfig);
                }
            };

            // 绑定点击事件
            instance.on('click', clickHandler);

            // 也可以绑定容器的点击事件（用于点击空白区域）
            containerElement.addEventListener('click', (e) => {
                // 检查是否点击在图表区域内但不是特定组件
                const rect = containerElement.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // 简单的点击区域检测
                if (x > 0 && x < rect.width && y > 0 && y < rect.height) {
                    clickHandler();
                }
            });

            // 存储配置到实例上，便于后续访问
            instance.__enlargeConfig = defaultConfig;
        }
    },

    /**
     * 回退的放大处理（当模态框组件不可用时）
     * @param {Object} instance - ECharts实例
     * @param {Object} config - 配置对象
     */
    _fallbackEnlarge(instance, config) {
        // 简单的全屏显示回退方案
        const container = instance.getDom();
        if (container && container.requestFullscreen) {
            container.requestFullscreen().catch(err => {
                console.warn('无法进入全屏模式:', err);
                // 可以在这里添加其他回退方案，比如新窗口等
                alert('Please enable chart modal component for enlarge functionality');
            });
        }
    },

    /**
     * 移除图表的放大功能
     * @param {Object} chartInstance - ECharts实例
     */
    removeEnlargeCapability(chartInstance) {
        if (!chartInstance) {
            return;
        }

        // 移除点击事件
        chartInstance.off('click');

        // 重置容器样式
        const container = chartInstance.getDom();
        if (container) {
            container.style.cursor = '';
            container.title = '';
        }

        // 清除配置
        delete chartInstance.__enlargeConfig;
    },

    /**
     * 批量为页面中的所有ECharts实例添加放大功能
     * @param {Object} globalConfig - 全局配置
     */
    enableGlobalEnlarge(globalConfig = {}) {
        // 等待ECharts加载完成
        this.ensureLoaded(() => {
            // 查找页面中所有可能的图表容器
            const containers = document.querySelectorAll('[data-chart-enlarge="true"], .chart-container, .echarts-container');
            
            containers.forEach(container => {
                // 检查是否已有ECharts实例
                const instance = this.getECharts().getInstanceByDom(container);
                if (instance) {
                    const config = {
                        ...globalConfig,
                        title: container.dataset.chartTitle || globalConfig.title || 'Chart Details',
                        serverId: container.dataset.serverId || globalConfig.serverId,
                        chartType: container.dataset.chartType || globalConfig.chartType || 'default'
                    };
                    
                    this.addEnlargeCapability(instance, config);
                }
            });
        });
    },

    /**
     * 重置加载状态（用于测试）
     */
    reset() {
        this.isLoaded = false;
        this.isLoading = false;
        this.hasError = false;
        this.errorMessage = '';
        this.callbacks = [];
    }
};

// 页面加载完成后自动检查ECharts
document.addEventListener('DOMContentLoaded', () => {
    // 如果ECharts已存在，标记为已加载
    if (window.EChartsLoader._checkEChartsExists()) {
        window.EChartsLoader.isLoaded = true;
        console.log('检测到ECharts已加载');
    }
}); 