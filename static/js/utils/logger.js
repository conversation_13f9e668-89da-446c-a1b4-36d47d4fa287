/**
 * DStatus 日志管理器
 * 用于统一控制所有console日志输出
 * 生产环境下自动禁用所有日志
 */
(function(window) {
    'use strict';
    
    // 检测是否为生产环境
    const isProduction = window.location.hostname !== 'localhost' && 
                        window.location.hostname !== '127.0.0.1' &&
                        !window.location.hostname.includes('.local');
    
    // 从URL参数或localStorage读取调试模式
    const urlParams = new URLSearchParams(window.location.search);
    const debugMode = urlParams.get('debug') === 'true' || 
                     localStorage.getItem('dstatus_debug') === 'true';
    
    // 是否启用日志（生产环境默认关闭，除非开启调试模式）
    const enableLogs = !isProduction || debugMode;
    
    // 空函数，用于替代console方法
    const noop = function() {};
    
    // 日志等级常量
    const LEVELS = {
        ERROR: 0,   // 🔴 错误 - 总是显示
        WARN: 1,    // 🟡 警告 - 默认显示  
        INFO: 2,    // 🔵 信息 - 可选显示
        DEBUG: 3,   // 🟢 调试 - 开发模式显示
        SILENT: 4   // 🔇 静默 - 全部关闭(除ERROR)
    };
    
    // 从localStorage读取或设置默认等级
    const getStoredLevel = function() {
        const stored = localStorage.getItem('dstatus_log_level');
        if (stored && LEVELS.hasOwnProperty(stored)) {
            return LEVELS[stored];
        }
        return isProduction ? LEVELS.WARN : LEVELS.DEBUG;
    };
    
    // 日志管理器
    const Logger = {
        // 日志开关（向后兼容）
        enabled: enableLogs,
        
        // 日志等级常量（公开访问）
        LEVELS: LEVELS,
        
        // 当前日志等级
        currentLevel: debugMode ? LEVELS.DEBUG : getStoredLevel(),
        
        // 切换日志状态
        toggle: function() {
            this.enabled = !this.enabled;
            localStorage.setItem('dstatus_debug', this.enabled ? 'true' : 'false');
            this.init();
            return this.enabled;
        },
        
        // 启用日志
        enable: function() {
            this.enabled = true;
            localStorage.setItem('dstatus_debug', 'true');
            this.init();
        },
        
        // 禁用日志
        disable: function() {
            this.enabled = false;
            localStorage.removeItem('dstatus_debug');
            this.init();
        },
        
        // 初始化console方法（增强版，支持分级控制）
        init: function() {
            // 保存原生console方法（如果还没保存）
            if (!window.console._log) {
                window.console._log = window.console.log;
                window.console._error = window.console.error;
                window.console._warn = window.console.warn;
                window.console._info = window.console.info;
                window.console._debug = window.console.debug;
                window.console._trace = window.console.trace;
            }
            
            // 根据enabled状态和level等级决定console方法
            if (this.enabled) {
                // 启用状态：根据等级控制显示
                window.console.error = window.console._error; // ERROR总是显示
                window.console.warn = this.currentLevel >= LEVELS.WARN ? window.console._warn : noop;
                window.console.info = this.currentLevel >= LEVELS.INFO ? window.console._info : noop;
                window.console.log = this.currentLevel >= LEVELS.INFO ? window.console._log : noop;  // log视为INFO级别
                window.console.debug = this.currentLevel >= LEVELS.DEBUG ? window.console._debug : noop;
                window.console.trace = this.currentLevel >= LEVELS.DEBUG ? window.console._trace : noop;
            } else {
                // 禁用状态：全部关闭（向后兼容）
                window.console.log = noop;
                window.console.error = noop;
                window.console.warn = noop;
                window.console.info = noop;
                window.console.debug = noop;
                window.console.trace = noop;
            }
        },
        
        // 设置日志等级（新增功能）
        setLevel: function(level) {
            if (typeof level === 'string' && LEVELS.hasOwnProperty(level)) {
                this.currentLevel = LEVELS[level];
                localStorage.setItem('dstatus_log_level', level);
            } else if (typeof level === 'number' && level >= 0 && level <= 4) {
                this.currentLevel = level;
                // 找到对应的等级名称存储
                for (const name in LEVELS) {
                    if (LEVELS[name] === level) {
                        localStorage.setItem('dstatus_log_level', name);
                        break;
                    }
                }
            }
            this.init(); // 重新初始化console方法
        },
        
        // 快速静默模式（新增功能）
        setSilent: function() {
            this.setLevel('SILENT');
        },
        
        // 恢复默认等级（新增功能）
        setDefault: function() {
            this.currentLevel = isProduction ? LEVELS.WARN : LEVELS.DEBUG;
            localStorage.removeItem('dstatus_log_level');
            this.init();
        },
        
        // 获取当前等级（新增功能）
        getCurrentLevel: function() {
            for (const name in LEVELS) {
                if (LEVELS[name] === this.currentLevel) {
                    return name;
                }
            }
            return 'UNKNOWN';
        }
    };
    
    // 自动初始化
    Logger.init();
    
    // 暴露到全局
    window.DStatusLogger = Logger;
    
    // 在控制台提供快捷命令
    if (enableLogs) {
        console.log('%c[DStatus Logger] 日志系统已启动 - 增强版', 'color: #4CAF50');
        console.log('原有功能:');
        console.log('  DStatusLogger.disable() - 关闭所有日志');
        console.log('  DStatusLogger.enable() - 开启所有日志');
        console.log('  DStatusLogger.toggle() - 切换日志状态');
        console.log('新增功能 (分级控制):');
        console.log('  DStatusLogger.setSilent() - 静默模式(只显示错误)');
        console.log('  DStatusLogger.setLevel("WARN") - 只显示警告和错误');
        console.log('  DStatusLogger.setLevel("INFO") - 显示信息级别以上');
        console.log('  DStatusLogger.getCurrentLevel() - 查看当前等级');
        console.log('当前等级: ' + Logger.getCurrentLevel());
    }
    
})(window);