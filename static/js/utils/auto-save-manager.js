/**
 * AutoSaveManager - 通用自动保存工具类
 * 提供表单字段变更检测、防抖保存、状态指示等功能
 */
class AutoSaveManager {
    constructor(options = {}) {
        // 配置选项
        this.options = {
            apiEndpoint: options.apiEndpoint || '/api/settings',
            collectData: options.collectData || (() => this.defaultCollectData()),
            validateData: options.validateData || null,
            onSuccess: options.onSuccess || null,
            onError: options.onError || null,
            debounceMs: options.debounceMs || 2500,
            statusIndicator: options.statusIndicator || '#auto-save-status',
            watchSelector: options.watchSelector || '[data-auto-save]',
            enabled: options.enabled !== false // 默认启用
        };

        // 状态管理
        this.state = {
            isEnabled: this.options.enabled,
            hasChanges: false,
            isSaving: false,
            lastSavedData: null,
            saveTimer: null,
            watchedElements: new Set()
        };

        // 初始化
        this.init();
    }

    /**
     * 初始化自动保存管理器
     */
    init() {
        if (!this.state.isEnabled) return;

        try {
            this.setupWatchers();
            this.loadUserPreferences();
            this.createStatusIndicator();
            console.log('[AutoSave] 自动保存已启用');
        } catch (error) {
            console.error('[AutoSave] 初始化失败:', error);
        }
    }

    /**
     * 设置表单元素监听器
     */
    setupWatchers() {
        // 监听指定的表单元素
        const elements = document.querySelectorAll(this.options.watchSelector);
        
        elements.forEach(element => {
            this.addWatcher(element);
        });

        // 如果没有指定选择器，则监听所有表单元素
        if (elements.length === 0) {
            const defaultElements = document.querySelectorAll('input, select, textarea');
            defaultElements.forEach(element => {
                // 排除特定类型的元素
                if (!this.shouldSkipElement(element)) {
                    this.addWatcher(element);
                }
            });
        }

        console.log(`[AutoSave] 已监听 ${this.state.watchedElements.size} 个表单元素`);
    }

    /**
     * 为单个元素添加监听器
     */
    addWatcher(element) {
        if (this.state.watchedElements.has(element)) return;

        const events = this.getEventsForElement(element);
        
        events.forEach(event => {
            element.addEventListener(event, this.handleChange.bind(this));
        });

        this.state.watchedElements.add(element);
    }

    /**
     * 获取元素需要监听的事件
     */
    getEventsForElement(element) {
        const type = element.type?.toLowerCase();
        const tagName = element.tagName.toLowerCase();

        if (type === 'checkbox' || type === 'radio') {
            return ['change'];
        } else if (tagName === 'select') {
            return ['change'];
        } else if (tagName === 'textarea' || type === 'text' || type === 'email' || type === 'url') {
            return ['input', 'blur'];
        }

        return ['change'];
    }

    /**
     * 判断是否应该跳过监听此元素
     */
    shouldSkipElement(element) {
        const skipTypes = ['submit', 'button', 'reset', 'file', 'hidden'];
        const skipClasses = ['no-auto-save', 'search-input'];
        
        return (
            skipTypes.includes(element.type) ||
            skipClasses.some(cls => element.classList.contains(cls)) ||
            element.hasAttribute('data-no-auto-save')
        );
    }

    /**
     * 处理表单元素变更
     */
    handleChange() {
        if (!this.state.isEnabled || this.state.isSaving) return;

        // 标记有变更
        this.state.hasChanges = true;
        
        // 取消之前的保存计时器
        if (this.state.saveTimer) {
            clearTimeout(this.state.saveTimer);
        }

        // 设置新的保存计时器（防抖）
        this.state.saveTimer = setTimeout(() => {
            this.performSave();
        }, this.options.debounceMs);

        // 更新状态指示器
        this.updateStatus('pending', '即将保存...');
        
        console.log(`[AutoSave] 检测到变更，将在 ${this.options.debounceMs}ms 后保存`);
    }

    /**
     * 执行保存操作
     */
    async performSave() {
        if (!this.state.hasChanges || this.state.isSaving) return;

        this.state.isSaving = true;
        this.updateStatus('saving', '保存中...');

        try {
            // 收集数据
            const data = await this.collectData();
            
            // 验证数据（如果有验证函数）
            if (this.options.validateData) {
                const validation = await this.options.validateData(data);
                if (!validation.valid) {
                    throw new Error(validation.message || '数据验证失败');
                }
            }

            // 检查数据是否真的发生了变化
            if (this.isDuplicateSave(data)) {
                this.updateStatus('success', '已保存');
                return;
            }

            // 发送保存请求
            const response = await this.apiRequest(this.options.apiEndpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                throw new Error(`保存失败: ${response.status}`);
            }

            const result = await response.json();

            if (!this.isSuccessResponse(result)) {
                throw new Error(result.message || '保存失败');
            }

            // 保存成功
            this.state.hasChanges = false;
            this.state.lastSavedData = JSON.stringify(data);
            this.updateStatus('success', '已保存');

            // 执行成功回调
            if (this.options.onSuccess) {
                this.options.onSuccess(result);
            }

            console.log('[AutoSave] 保存成功');

        } catch (error) {
            console.error('[AutoSave] 保存失败:', error);
            this.updateStatus('error', '保存失败');

            // 执行错误回调
            if (this.options.onError) {
                this.options.onError(error);
            }
        } finally {
            this.state.isSaving = false;
        }
    }

    /**
     * 收集表单数据
     */
    async collectData() {
        if (this.options.collectData) {
            return await this.options.collectData();
        }
        return this.defaultCollectData();
    }

    /**
     * 默认数据收集方法
     */
    defaultCollectData() {
        const data = {};
        
        this.state.watchedElements.forEach(element => {
            const key = element.getAttribute('name') || 
                       element.getAttribute('data-key') || 
                       element.getAttribute('key') ||
                       element.id;
                       
            if (key) {
                if (element.type === 'checkbox' || element.type === 'radio') {
                    data[key] = element.checked;
                } else {
                    data[key] = element.value;
                }
            }
        });

        return data;
    }

    /**
     * 检查是否为重复保存
     */
    isDuplicateSave(data) {
        const currentDataStr = JSON.stringify(data);
        return this.state.lastSavedData === currentDataStr;
    }

    /**
     * 检查API响应是否成功
     */
    isSuccessResponse(result) {
        return result.success === true || result.code === 1;
    }

    /**
     * API请求封装
     */
    async apiRequest(url, options = {}) {
        const timestamp = new Date().getTime();
        const urlWithTimestamp = url.includes('?') ? `${url}&t=${timestamp}` : `${url}?t=${timestamp}`;
        
        const defaultOptions = {
            timeout: 10000,
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json'
            }
        };

        return fetch(urlWithTimestamp, { ...defaultOptions, ...options });
    }

    /**
     * 创建状态指示器
     */
    createStatusIndicator() {
        const existingIndicator = document.querySelector(this.options.statusIndicator);
        if (existingIndicator) return;

        const indicator = document.createElement('div');
        indicator.id = this.options.statusIndicator.replace('#', '');
        indicator.className = 'auto-save-status fixed bottom-20 right-6 px-3 py-2 rounded-lg text-sm border shadow-lg backdrop-blur-sm transition-all duration-300 z-40 hidden';
        
        indicator.innerHTML = `
            <div class="flex items-center gap-2">
                <i class="status-icon ti ti-device-floppy"></i>
                <span class="status-text">已保存</span>
            </div>
        `;

        document.body.appendChild(indicator);
    }

    /**
     * 更新状态指示器
     */
    updateStatus(status, message) {
        const indicator = document.querySelector(this.options.statusIndicator);
        if (!indicator) return;

        const icon = indicator.querySelector('.status-icon');
        const text = indicator.querySelector('.status-text');

        // 移除所有状态类
        indicator.classList.remove('bg-blue-100', 'border-blue-200', 'text-blue-800',
                                  'bg-green-100', 'border-green-200', 'text-green-800',
                                  'bg-red-100', 'border-red-200', 'text-red-800',
                                  'bg-yellow-100', 'border-yellow-200', 'text-yellow-800');

        // 移除所有图标类
        icon.className = 'status-icon ti';

        switch (status) {
            case 'pending':
                indicator.classList.add('bg-yellow-100', 'border-yellow-200', 'text-yellow-800');
                icon.classList.add('ti-clock');
                break;
            case 'saving':
                indicator.classList.add('bg-blue-100', 'border-blue-200', 'text-blue-800');
                icon.classList.add('ti-refresh', 'animate-spin');
                break;
            case 'success':
                indicator.classList.add('bg-green-100', 'border-green-200', 'text-green-800');
                icon.classList.add('ti-check');
                break;
            case 'error':
                indicator.classList.add('bg-red-100', 'border-red-200', 'text-red-800');
                icon.classList.add('ti-alert-circle');
                break;
        }

        text.textContent = message;

        // 显示指示器
        indicator.classList.remove('hidden');

        // 自动隐藏成功和错误状态
        if (status === 'success' || status === 'error') {
            setTimeout(() => {
                indicator.classList.add('hidden');
            }, status === 'success' ? 2000 : 5000);
        }
    }

    /**
     * 加载用户偏好设置
     */
    loadUserPreferences() {
        try {
            const savedState = localStorage.getItem('autoSave_enabled');
            if (savedState !== null) {
                this.state.isEnabled = savedState === 'true';
            }
        } catch (error) {
            console.warn('[AutoSave] 无法加载用户偏好设置:', error);
        }
    }

    /**
     * 启用自动保存
     */
    enable() {
        this.state.isEnabled = true;
        localStorage.setItem('autoSave_enabled', 'true');
        this.setupWatchers();
        console.log('[AutoSave] 已启用');
    }

    /**
     * 禁用自动保存
     */
    disable() {
        this.state.isEnabled = false;
        localStorage.setItem('autoSave_enabled', 'false');
        
        // 清除保存计时器
        if (this.state.saveTimer) {
            clearTimeout(this.state.saveTimer);
            this.state.saveTimer = null;
        }
        
        // 隐藏状态指示器
        const indicator = document.querySelector(this.options.statusIndicator);
        if (indicator) {
            indicator.classList.add('hidden');
        }
        
        console.log('[AutoSave] 已禁用');
    }

    /**
     * 立即保存（跳过防抖）
     */
    async saveNow() {
        if (this.state.saveTimer) {
            clearTimeout(this.state.saveTimer);
            this.state.saveTimer = null;
        }
        
        await this.performSave();
    }

    /**
     * 检查是否有未保存的更改
     */
    hasUnsavedChanges() {
        return this.state.hasChanges;
    }

    /**
     * 销毁管理器，清理所有资源
     */
    destroy() {
        // 清理计时器
        if (this.state.saveTimer) {
            clearTimeout(this.state.saveTimer);
        }

        // 移除事件监听器
        this.state.watchedElements.forEach(element => {
            const events = this.getEventsForElement(element);
            events.forEach(event => {
                element.removeEventListener(event, this.handleChange.bind(this));
            });
        });

        // 清理状态
        this.state.watchedElements.clear();
        
        // 移除状态指示器
        const indicator = document.querySelector(this.options.statusIndicator);
        if (indicator && indicator.id === this.options.statusIndicator.replace('#', '')) {
            indicator.remove();
        }

        console.log('[AutoSave] 已销毁');
    }
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AutoSaveManager;
} else {
    window.AutoSaveManager = AutoSaveManager;
}