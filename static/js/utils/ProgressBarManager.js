/**
 * ProgressBarManager.js
 * 统一的进度条管理器，消除重复代码并提供自适应基准功能
 * 支持动态流量区间适配和平滑过渡
 */

(function() {
    'use strict';
    
    class ProgressBarManager {
    constructor() {
        // 固定最优配置（不再允许外部修改）
        this.config = {
            enabled: true,
            duration: 1500,
            updateInterval: 200,
            useCssTransition: true,
            adaptiveBaseline: true,
            baselineMethod: 'percentile95', // 使用95百分位自适应
            fixedBaseline: 1000 * 1024 * 1024, // 单位bps（≈1.048 Gbps）
            historyWindow: 5 * 60 * 1000, // 5分钟历史窗口 - 快速适应
            cacheTimeout: 30 * 1000, // 30秒缓存 - 更快响应变化
            minBaseline: 1 * 1000 * 1000, // 1Mbps最小基准（bps）
            learningRate: 0.3, // 学习率 - 更快适应新速度

            // 滞回与满格黏性配置
            upshiftTrigger: 1.15, // 上升阈值：115%（更保守）
            upshiftSustainMs: 2500, // 上升持续时间：2.5秒（更长）
            downshiftTrigger: 0.25, // 下降阈值：25%（更保守）
            downshiftSustainMs: 4000, // 下降持续时间：4秒
            minHoldMs: 5000, // 最小保持时长：5秒（更长）
            headroom: 1.3, // 升档余量：130%（更大余量）
            celebrateFullBar: this.getCelebrateFullBarConfig() // 从本地配置读取
        };
        
        // 基准值缓存
        this.baselineCache = new Map();
        
        // 历史数据缓存
        this.historyCache = new Map();

        // 简化模式的基准状态（按 key 共享，快速上调、温和下调）
        this.baselineState = new Map();
        
        // 平滑因子（使用配置的学习率）
        this.smoothingFactor = this.config.learningRate || 0.3;
    }
    
    /**
     * 统一的进度条更新方法
     * @param {string} elementId - 进度条元素ID
     * @param {number} currentValue - 当前值（bps）
     * @param {number} maxValue - 最大值（可选，用于固定基准，bps）
     * @param {Object} options - 额外选项
     */
    updateProgressBar(elementId, currentValue, maxValue = null, options = {}) {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        // 合并选项
        const config = { ...this.config, ...options };
        
        // 计算基准值（传递baselineKey如果存在）
        const baseline = this.calculateBaseline(elementId, currentValue, maxValue, config, config.baselineKey);
        
        // 计算百分比
        const percent = Math.min(100, (currentValue / baseline) * 100);

        // 处理满格高光效果
        this.handleFullBarGlow(element, percent, config);

        // 应用CSS类
        this.applyAnimationClasses(element, config);
        
        // 检查是否使用SmoothTransition
        if (window.SmoothTransition && config.useSmoothTransition) {
            // 获取上次的百分比
            const lastPercent = parseFloat(element.dataset.lastPercent || '0');
            
            window.SmoothTransition.progressBar(
                elementId,
                lastPercent,
                percent,
                config.duration,
                null,
                config.updateInterval
            );
            
            element.dataset.lastPercent = percent;
        } else {
            // 直接设置宽度
            element.style.width = `${percent}%`;
            
            // 添加高亮效果（如果值有显著变化）
            const lastPercent = parseFloat(element.dataset.lastPercent || '0');
            const percentDiff = Math.abs(lastPercent - percent);
            if (percentDiff > 1) {
                this.addHighlightEffect(element, config);
                console.log(`[ProgressBarManager] 进度高亮触发: ${element.id} (${lastPercent}% → ${percent}%, Δ${percentDiff.toFixed(1)}%)`);
            }
            
            element.dataset.lastPercent = percent;
        }
        
        // 更新历史数据（用于自适应，使用baselineKey如果存在）
        if (config.adaptiveBaseline) {
            this.updateHistory(config.baselineKey || elementId, currentValue);
        }
        
        return percent;
    }
    
    /**
     * 计算基准值
     */
    calculateBaseline(elementId, currentValue, maxValue, config, baselineKey) {
        const key = baselineKey || elementId;

        // 如果提供了固定最大值，直接使用
        if (maxValue !== null) {
            return maxValue;
        }
        
        // 如果禁用自适应，使用固定基准
        if (!config.adaptiveBaseline) {
            return config.fixedBaseline;
        }

        // 简化自适应：按区间桶快速自适应（无历史依赖）
        if (config.baselineMethod === 'bucket') {
            // 统一上下行：若另一方向已有基线，取两者最大值做统一档位
            const otherKey = key === 'download' ? 'upload' : key === 'upload' ? 'download' : null;
            const unified = this._calculateBucketBaseline(key, currentValue, config);
            if (otherKey) {
                const otherState = this.baselineState.get(otherKey);
                if (otherState && otherState.baseline) {
                    const maxBaseline = Math.max(unified, otherState.baseline);
                    this._emitBaselineChanged('download', maxBaseline);
                    this._emitBaselineChanged('upload', maxBaseline);
                    return maxBaseline;
                }
            }
            return unified;
        }
        
        // 获取缓存的基准值（优先使用baselineKey，否则使用elementId）
        const cacheKey = `${key}_baseline`;
        const cached = this.baselineCache.get(cacheKey);
        
        // 如果缓存有效，使用缓存（使用配置的缓存超时时间）
        if (cached && (Date.now() - cached.timestamp < this.config.cacheTimeout)) {
            // 应用平滑处理
            const smoothed = this.applySmoothingFilter(cacheKey, cached.value, currentValue);
            this._emitBaselineChanged(key, smoothed);
            return smoothed;
        }
        
        // 根据方法计算新基准
        let baseline;
        const history = this.getHistory(key);
        
        // 如果没有历史数据，使用智能初始值
        if (!history || history.length === 0) {
            // 初始基准 = 当前值的2倍（留出增长空间）
            baseline = currentValue * 2;
            // 但不低于配置的默认值
            baseline = Math.max(baseline, config.fixedBaseline);
        } else {
            switch (config.baselineMethod) {
                case 'percentile95':
                    baseline = this.calculatePercentile(history, 0.95);
                    // 如果当前值明显高于历史，快速调整
                    if (currentValue > baseline * 1.5) {
                        baseline = currentValue * 1.2;
                    }
                    break;
                case 'max':
                    baseline = Math.max(...history, currentValue) * 1.1; // 留10%余量
                    break;
                case 'average':
                    baseline = this.calculateAverage(history) * 2; // 平均值的2倍
                    break;
                case 'fixed':
                default:
                    baseline = config.fixedBaseline;
                    break;
            }
        }
        
        // 确保基准值合理（使用配置的最小值）
        baseline = Math.max(baseline, this.config.minBaseline);
        
        // 缓存基准值
        this.baselineCache.set(cacheKey, {
            value: baseline,
            timestamp: Date.now()
        });
        this._emitBaselineChanged(key, baseline);
        
        return baseline;
    }

    /**
     * 按区间桶计算基准（轻量滞回与满格黏性）
     */
    _calculateBucketBaseline(key, valueBps, config) {
        const now = Date.now();
        const state = this.baselineState.get(key) || {
            baseline: Math.max(config.fixedBaseline, config.minBaseline),
            lastChangeTs: 0,
            upTimer: 0,
            downTimer: 0,
            upStartTs: 0,
            downStartTs: 0
        };

        const buckets = this._getBuckets();

        let baseline = state.baseline;
        const currentRatio = valueBps / baseline;

        // 重置计时器逻辑
        const resetTimers = () => {
            state.upTimer = 0;
            state.downTimer = 0;
            state.upStartTs = 0;
            state.downStartTs = 0;
        };

        // 换挡上调：当前值达到现有基准的98%时抬升到目标桶（更贴近“满刻度”直觉）
        if (currentRatio >= 0.95 && currentRatio <= 1.05) {
            // 在满格区间，重置计时器，保持当前档位
            resetTimers();
            // 触发满格高光效果
            this._emitFullBarEvent(key, currentRatio);
        }
        // 上升阈值检测：≥110% 且持续时间足够才升档
        else if (currentRatio >= config.upshiftTrigger) {
            if (state.upStartTs === 0) {
                state.upStartTs = now;
            }
            state.upTimer = now - state.upStartTs;
            state.downTimer = 0;
            state.downStartTs = 0;

            // 检查是否满足升档条件
            if (state.upTimer >= config.upshiftSustainMs &&
                (now - state.lastChangeTs) >= config.minHoldMs) {
                const newBaseline = this._pickBucket(valueBps * config.headroom, buckets);
                if (newBaseline > baseline) {
                    baseline = newBaseline;
                    state.lastChangeTs = now;
                    resetTimers();
                }
            }
        }
        // 下降阈值检测：≤30% 且持续时间足够才降档
        else if (currentRatio <= config.downshiftTrigger) {
            if (state.downStartTs === 0) {
                state.downStartTs = now;
            }
            state.downTimer = now - state.downStartTs;
            state.upTimer = 0;
            state.upStartTs = 0;

            // 检查是否满足降档条件
            if (state.downTimer >= config.downshiftSustainMs &&
                (now - state.lastChangeTs) >= config.minHoldMs) {
                const newBaseline = this._pickBucket(Math.max(valueBps * 1.5, config.minBaseline), buckets);
                if (newBaseline < baseline) {
                    baseline = newBaseline;
                    state.lastChangeTs = now;
                    resetTimers();
                }
            }
        }
        // 在正常范围内，重置计时器
        else {
            resetTimers();
        }

        baseline = Math.max(baseline, config.minBaseline);
        const prev = state.baseline;
        state.baseline = baseline;
        this.baselineState.set(key, state);
        // 只在基准实际改变时触发事件
        if (prev !== baseline) {
            this._emitBaselineChanged(key, baseline, prev);
        }

        return baseline;
    }

    _getBuckets() {
        const K = 1024;
        const M = K * K;
        const G = K * M;
        const T = K * G;
        // 参考 Speedtest 风格，避免 50/200 这种中间档，使用更直观的大档位
        return [
            100 * M, 500 * M,
            1 * G, 2 * G, 5 * G, 10 * G, 20 * G, 40 * G,
            100 * G, 200 * G, 400 * G,
            1 * T, 2 * T, 5 * T
        ];
    }

    _pickBucket(value, buckets) {
        for (let i = 0; i < buckets.length; i++) {
            if (value <= buckets[i]) return buckets[i];
        }
        return buckets[buckets.length - 1];
    }

    /**
     * 触发基准变更事件（去抖）
     */
    _emitBaselineChanged(key, baseline, prevBaseline) {
        try {
            if (typeof document === 'undefined') return;
            const st = this.baselineState.get(key) || {};
            const last = st.lastEmitted;
            // 若提供了前值，仅在变化时触发；否则与最近一次已发的比较
            const changed = prevBaseline !== undefined ? (prevBaseline !== baseline) : (last !== baseline);
            if (!changed) return;
            st.lastEmitted = baseline;
            st.lastEmittedTs = Date.now();
            this.baselineState.set(key, st);
            document.dispatchEvent(new CustomEvent('progress:baseline', { detail: { key, baseline } }));
        } catch (e) {
            // 忽略事件错误
        }
    }

    /**
     * 触发满格高光事件
     */
    _emitFullBarEvent(key, ratio) {
        try {
            if (typeof document === 'undefined') return;
            document.dispatchEvent(new CustomEvent('progress:fullbar', {
                detail: { key, ratio }
            }));
        } catch (e) {
            // 忽略事件错误
        }
    }

    /**
     * 应用动画CSS类
     */
    applyAnimationClasses(element, config) {
        // 移除所有动画类
        element.classList.remove('progress-bar-no-animation', 'progress-bar-normal', 'progress-bar-fast');
        
        if (!config.useCssTransition || !config.enabled) {
            element.classList.add('progress-bar-no-animation');
        } else if (config.duration <= 300) {
            element.classList.add('progress-bar-fast');
        } else {
            element.classList.add('progress-bar-normal');
        }
    }
    
    /**
     * 处理满格高光效果
     */
    handleFullBarGlow(element, percent, config) {
        // 检查是否启用满格高光
        if (!config.celebrateFullBar) return;

        // 检查reduced-motion设置
        if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            return;
        }

        const elementId = element.id;
        const glowKey = `${elementId}_glow`;

        // 获取或初始化glow状态
        if (!this.glowState) {
            this.glowState = new Map();
        }

        const glowState = this.glowState.get(glowKey) || {
            isActive: false,
            removeTimer: null
        };

        // ≥95% 时添加满格高光
        if (percent >= 95) {
            if (!glowState.isActive) {
                // 确保元素有正确的类
                if (!element.classList.contains('full-bar-glow')) {
                    element.classList.add('full-bar-glow');
                }
                element.classList.add('active');
                glowState.isActive = true;

                // 清除之前的移除计时器
                if (glowState.removeTimer) {
                    clearTimeout(glowState.removeTimer);
                    glowState.removeTimer = null;
                }

                console.log(`[ProgressBarManager] 满格高光激活: ${elementId} (${percent}%)`);
            }
        }
        // <92% 时开始计时移除高光
        else if (percent < 92 && glowState.isActive) {
            // 如果还没有设置移除计时器，则设置一个
            if (!glowState.removeTimer) {
                glowState.removeTimer = setTimeout(() => {
                    element.classList.remove('active');
                    // 延迟移除full-bar-glow类，让过渡更自然
                    setTimeout(() => {
                        element.classList.remove('full-bar-glow');
                    }, 300);
                    glowState.isActive = false;
                    glowState.removeTimer = null;
                    this.glowState.set(glowKey, glowState);
                    console.log(`[ProgressBarManager] 满格高光移除: ${elementId} (${percent}%)`);
                }, 1800); // 1.8秒延迟移除
            }
        }
        // 92-95% 之间，清除移除计时器但保持高光
        else if (percent >= 92 && percent < 95 && glowState.removeTimer) {
            clearTimeout(glowState.removeTimer);
            glowState.removeTimer = null;
        }

        // 更新状态
        this.glowState.set(glowKey, glowState);
    }

    /**
     * 添加高亮效果
     */
    addHighlightEffect(element, config = {}) {
        // 检查reduced-motion设置
        if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            return;
        }

        // 先移除类，强制reflow，再添加类
        element.classList.remove('progress-highlight');
        void element.offsetWidth; // 强制重排，确保动画重新开始
        element.classList.add('progress-highlight');

        // 动画结束后自动移除类
        setTimeout(() => {
            element.classList.remove('progress-highlight');
        }, 600); // 与CSS动画时长一致
    }
    
    /**
     * 更新历史数据
     */
    updateHistory(elementId, value) {
        const historyKey = `${elementId}_history`;
        let history = this.historyCache.get(historyKey) || [];
        
        // 添加新数据点
        history.push({
            value: value,
            timestamp: Date.now()
        });
        
        // 清理过期数据
        const cutoff = Date.now() - this.config.historyWindow;
        history = history.filter(item => item.timestamp > cutoff);
        
        // 限制数量（最多保留1000个数据点）
        if (history.length > 1000) {
            history = history.slice(-1000);
        }
        
        this.historyCache.set(historyKey, history);
        
        // 持久化到localStorage（限流，每分钟一次）
        this.persistHistory(historyKey, history);
    }
    
    /**
     * 获取历史数据
     */
    getHistory(elementId) {
        const historyKey = `${elementId}_history`;
        let history = this.historyCache.get(historyKey);
        
        if (!history) {
            // 尝试从localStorage加载
            try {
                const saved = localStorage.getItem(historyKey);
                if (saved) {
                    history = JSON.parse(saved);
                    this.historyCache.set(historyKey, history);
                }
            } catch (e) {
                history = [];
            }
        }
        
        return (history || []).map(item => item.value);
    }
    
    /**
     * 计算百分位数
     */
    calculatePercentile(values, percentile) {
        if (!values || values.length === 0) {
            return this.config.fixedBaseline;
        }
        
        // 如果历史数据太少，返回最大值的1.2倍
        if (values.length < 5) {
            return Math.max(...values) * 1.2;
        }
        
        const sorted = [...values].sort((a, b) => a - b);
        const index = Math.ceil(sorted.length * percentile) - 1;
        return sorted[Math.max(0, index)];
    }
    
    /**
     * 计算平均值
     */
    calculateAverage(values) {
        if (!values || values.length === 0) {
            return this.config.fixedBaseline;
        }
        
        const sum = values.reduce((a, b) => a + b, 0);
        return sum / values.length;
    }
    
    /**
     * 应用平滑滤波
     */
    applySmoothingFilter(cacheKey, oldValue, currentValue) {
        // 基于当前值计算建议基准
        const suggestedBaseline = currentValue * 1.5; // 当前值的1.5倍作为舒适基准
        
        // 如果当前值显著增大（超过旧基准的80%），快速调整
        if (currentValue > oldValue * 0.8) {
            // 快速提升基准以适应新速度
            return Math.max(oldValue * 1.2, suggestedBaseline);
        }
        
        // 如果当前值很小（低于旧基准的20%），考虑降低基准
        if (currentValue < oldValue * 0.2) {
            // 缓慢降低基准，避免过度收缩
            return Math.max(
                oldValue * 0.8, // 降低20%
                suggestedBaseline, // 但不低于建议值
                this.config.minBaseline // 且不低于最小值
            );
        }
        
        // 正常情况：平滑过渡
        return oldValue * (1 - this.smoothingFactor) + suggestedBaseline * this.smoothingFactor;
    }
    
    /**
     * 持久化历史数据（限流）
     */
    persistHistory(key, history) {
        // 使用简单的限流机制
        if (!this.persistTimer) {
            this.persistTimer = {};
        }
        
        if (this.persistTimer[key]) {
            clearTimeout(this.persistTimer[key]);
        }
        
        this.persistTimer[key] = setTimeout(() => {
            try {
                localStorage.setItem(key, JSON.stringify(history));
            } catch (e) {
                // 存储空间不足时，清理最旧的数据
                if (history.length > 100) {
                    const reduced = history.slice(-100);
                    localStorage.setItem(key, JSON.stringify(reduced));
                }
            }
        }, 60000); // 1分钟后保存
    }
    
    /**
     * 批量更新进度条（优化性能）
     */
    updateMultipleProgressBars(updates) {
        const results = {};
        
        for (const update of updates) {
            const { elementId, currentValue, maxValue, options } = update;
            results[elementId] = this.updateProgressBar(elementId, currentValue, maxValue, options);
        }
        
        return results;
    }

    /**
     * 获取满格高光配置
     */
    getCelebrateFullBarConfig() {
        try {
            // 尝试从SpeedScale组件获取配置
            if (window.SpeedScale && typeof window.SpeedScale.getConfig === 'function') {
                const config = window.SpeedScale.getConfig();
                return config.celebrateFullBar !== undefined ? config.celebrateFullBar : true;
            }

            // 直接从localStorage读取
            const stored = localStorage.getItem('speedScale_config');
            if (stored) {
                const parsed = JSON.parse(stored);
                return parsed.celebrateFullBar !== undefined ? parsed.celebrateFullBar : true;
            }
        } catch (e) {
            console.warn('[ProgressBarManager] 读取celebrateFullBar配置失败:', e);
        }

        // 默认开启
        return true;
    }

    /**
     * 动态更新满格高光配置
     */
    updateCelebrateFullBarConfig() {
        this.config.celebrateFullBar = this.getCelebrateFullBarConfig();
    }
    }
    
    // 创建单例实例
    const instance = new ProgressBarManager();
    
    // 仅暴露必要的方法给外部使用
    window.progressBarManager = {
        updateProgressBar: instance.updateProgressBar.bind(instance),
        updateMultipleProgressBars: instance.updateMultipleProgressBars.bind(instance),
        updateCelebrateFullBarConfig: instance.updateCelebrateFullBarConfig.bind(instance),
        baselineState: instance.baselineState // 暴露baselineState供aria属性使用
    };
    
    // Node.js环境导出
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = ProgressBarManager;
    }
})();