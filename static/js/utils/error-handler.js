/**
 * 图表错误处理器
 * 提供统一的错误处理和用户反馈机制
 */
window.ChartErrorHandler = {
    // 错误类型映射
    errorTypes: {
        NETWORK: 'network',
        DATA: 'data', 
        RENDER: 'render',
        CONFIG: 'config',
        UNKNOWN: 'unknown'
    },
    
    /**
     * 处理图表错误
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     * @param {HTMLElement} container - 图表容器
     * @param {Object} options - 选项
     */
    handleError(error, context, container, options = {}) {
        const errorInfo = this._analyzeError(error);
        
        console.error(`图表错误[${context}]:`, {
            message: error.message,
            type: errorInfo.type,
            stack: error.stack,
            context
        });
        
        // 显示用户友好的错误信息
        if (container) {
            this.showErrorMessage(container, errorInfo.userMessage, errorInfo.type, options);
        }
        
        // 上报错误（如果需要）
        if (options.reportError !== false) {
            this._reportError(error, context, errorInfo);
        }
        
        return errorInfo;
    },
    
    /**
     * 分析错误类型和生成用户友好消息
     * @param {Error} error - 错误对象
     * @returns {Object} 错误信息
     */
    _analyzeError(error) {
        const message = error.message || '';
        const stack = error.stack || '';
        
        // 网络相关错误
        if (message.includes('404') || message.includes('Not Found')) {
            return {
                type: this.errorTypes.NETWORK,
                userMessage: '数据不存在，请检查配置或稍后重试',
                suggestion: '检查目标配置是否正确'
            };
        }
        
        if (message.includes('500') || message.includes('Internal Server Error')) {
            return {
                type: this.errorTypes.NETWORK,
                userMessage: '服务器错误，请稍后重试',
                suggestion: '如果问题持续存在，请联系管理员'
            };
        }
        
        if (message.includes('timeout') || message.includes('超时')) {
            return {
                type: this.errorTypes.NETWORK,
                userMessage: '请求超时，请检查网络连接',
                suggestion: '检查网络连接或稍后重试'
            };
        }
        
        if (message.includes('fetch') || message.includes('XMLHttpRequest')) {
            return {
                type: this.errorTypes.NETWORK,
                userMessage: '网络连接失败，请检查网络',
                suggestion: '检查网络连接状态'
            };
        }
        
        // 数据相关错误
        if (message.includes('JSON') || message.includes('parse')) {
            return {
                type: this.errorTypes.DATA,
                userMessage: '数据格式错误，请刷新页面重试',
                suggestion: '数据可能已损坏，尝试刷新页面'
            };
        }
        
        if (message.includes('empty') || message.includes('null') || message.includes('undefined')) {
            return {
                type: this.errorTypes.DATA,
                userMessage: '暂无数据，请稍后重试',
                suggestion: '等待数据收集或检查数据源'
            };
        }
        
        // 渲染相关错误
        if (message.includes('echarts') || message.includes('chart') || stack.includes('echarts')) {
            return {
                type: this.errorTypes.RENDER,
                userMessage: '图表渲染失败，请刷新页面',
                suggestion: '尝试刷新页面或清除浏览器缓存'
            };
        }
        
        if (message.includes('container') || message.includes('element')) {
            return {
                type: this.errorTypes.RENDER,
                userMessage: '图表容器错误，请刷新页面',
                suggestion: '页面可能未完全加载，尝试刷新'
            };
        }
        
        // 配置相关错误
        if (message.includes('config') || message.includes('option')) {
            return {
                type: this.errorTypes.CONFIG,
                userMessage: '图表配置错误，请联系管理员',
                suggestion: '检查图表配置参数'
            };
        }
        
        // 未知错误
        return {
            type: this.errorTypes.UNKNOWN,
            userMessage: '图表加载失败，请刷新页面重试',
            suggestion: '尝试刷新页面，如果问题持续存在请联系管理员'
        };
    },
    
    /**
     * 显示错误消息
     * @param {HTMLElement} container - 容器元素
     * @param {string} message - 错误消息
     * @param {string} type - 错误类型
     * @param {Object} options - 选项
     */
    showErrorMessage(container, message, type, options = {}) {
        if (!container) return;
        
        const iconMap = {
            [this.errorTypes.NETWORK]: 'ti-wifi-off',
            [this.errorTypes.DATA]: 'ti-database',
            [this.errorTypes.RENDER]: 'ti-photo-off',
            [this.errorTypes.CONFIG]: 'ti-settings',
            [this.errorTypes.UNKNOWN]: 'ti-alert-circle'
        };
        
        const colorMap = {
            [this.errorTypes.NETWORK]: 'text-orange-500',
            [this.errorTypes.DATA]: 'text-blue-500',
            [this.errorTypes.RENDER]: 'text-red-500',
            [this.errorTypes.CONFIG]: 'text-purple-500',
            [this.errorTypes.UNKNOWN]: 'text-gray-500'
        };
        
        const icon = iconMap[type] || 'ti-alert-circle';
        const iconColor = colorMap[type] || 'text-gray-500';
        
        const showRetryButton = options.showRetryButton !== false;
        const retryCallback = options.retryCallback || (() => location.reload());
        
        container.innerHTML = `
            <div class="flex items-center justify-center h-full min-h-[200px]">
                <div class="text-center max-w-md mx-auto p-6">
                    <div class="mb-4">
                        <i class="ti ${icon} text-4xl ${iconColor}"></i>
                    </div>
                    <div class="text-slate-700 dark:text-slate-300 mb-4">
                        <div class="text-lg font-medium mb-2">${message}</div>
                        ${options.showSuggestion !== false ? `
                            <div class="text-sm text-slate-500 dark:text-slate-400">
                                ${this._getErrorSuggestion(type)}
                            </div>
                        ` : ''}
                    </div>
                    ${showRetryButton ? `
                        <div class="flex gap-2 justify-center">
                            <button onclick="location.reload()" 
                                    class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md text-sm transition-colors">
                                <i class="ti ti-refresh text-sm align-middle mr-1"></i>
                                刷新页面
                            </button>
                            ${options.showRetryOnly ? `
                                <button onclick="(${retryCallback.toString()})()" 
                                        class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md text-sm transition-colors">
                                    <i class="ti ti-reload text-sm align-middle mr-1"></i>
                                    重试
                                </button>
                            ` : ''}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
        
        console.log('已显示错误消息:', { message, type });
    },
    
    /**
     * 获取错误建议
     * @param {string} type - 错误类型
     * @returns {string}
     */
    _getErrorSuggestion(type) {
        const suggestions = {
            [this.errorTypes.NETWORK]: '请检查网络连接或稍后重试',
            [this.errorTypes.DATA]: '请等待数据收集完成或联系管理员',
            [this.errorTypes.RENDER]: '请尝试刷新页面或清除浏览器缓存',
            [this.errorTypes.CONFIG]: '请联系管理员检查系统配置',
            [this.errorTypes.UNKNOWN]: '如果问题持续存在，请联系技术支持'
        };
        
        return suggestions[type] || suggestions[this.errorTypes.UNKNOWN];
    },
    
    /**
     * 上报错误（可选）
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     * @param {Object} errorInfo - 错误信息
     */
    _reportError(error, context, errorInfo) {
        // 这里可以实现错误上报逻辑
        // 例如发送到错误监控服务
        
        if (window.DEBUG_MODE) {
            console.group('错误详情');
            console.log('上下文:', context);
            console.log('错误类型:', errorInfo.type);
            console.log('用户消息:', errorInfo.userMessage);
            console.log('建议:', errorInfo.suggestion);
            console.log('原始错误:', error);
            console.groupEnd();
        }
    },
    
    /**
     * 显示加载错误
     * @param {HTMLElement} container - 容器元素
     * @param {string} resourceName - 资源名称
     */
    showLoadingError(container, resourceName = '资源') {
        this.showErrorMessage(
            container,
            `${resourceName}加载失败`,
            this.errorTypes.NETWORK,
            {
                showSuggestion: true,
                retryCallback: () => location.reload()
            }
        );
    },
    
    /**
     * 显示数据为空的提示
     * @param {HTMLElement} container - 容器元素
     * @param {string} dataType - 数据类型
     */
    showEmptyData(container, dataType = '数据') {
        container.innerHTML = `
            <div class="flex items-center justify-center h-full min-h-[200px]">
                <div class="text-center">
                    <div class="mb-4">
                        <i class="ti ti-inbox text-4xl text-gray-400"></i>
                    </div>
                    <div class="text-slate-600 dark:text-slate-400">
                        <div class="text-lg font-medium mb-2">暂无${dataType}</div>
                        <div class="text-sm">请稍后重试或检查数据源配置</div>
                    </div>
                </div>
            </div>
        `;
    }
};

// 全局错误处理器
window.addEventListener('error', (event) => {
    if (window.DEBUG_MODE) {
        console.error('全局错误:', event.error);
    }
});

window.addEventListener('unhandledrejection', (event) => {
    if (window.DEBUG_MODE) {
        console.error('未处理的Promise拒绝:', event.reason);
    }
}); 