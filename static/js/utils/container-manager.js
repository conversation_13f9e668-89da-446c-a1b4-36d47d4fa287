/**
 * 图表容器尺寸管理器
 * 解决图表容器尺寸检测和响应式布局问题
 */
window.ContainerManager = {
    // 观察器实例缓存
    observers: new Map(),
    
    /**
     * 确保容器有合适的尺寸
     * @param {HTMLElement} container - 容器元素
     * @param {number} minWidth - 最小宽度
     * @param {number} minHeight - 最小高度
     * @returns {Promise} 返回Promise，容器准备好时resolve
     */
    ensureContainerSize(container, minWidth = 300, minHeight = 200) {
        if (!container) {
            return Promise.reject(new Error('容器元素不存在'));
        }
        
        // 如果容器已有合适尺寸，直接返回
        if (container.offsetWidth >= minWidth && container.offsetHeight >= minHeight) {
            return Promise.resolve();
        }
        
        console.log('容器尺寸不足，开始优化...', {
            current: { width: container.offsetWidth, height: container.offsetHeight },
            required: { width: minWidth, height: minHeight }
        });
        
        return new Promise((resolve) => {
            // 设置最小尺寸样式
            this._setMinimumSize(container, minWidth, minHeight);
            
            // 如果支持ResizeObserver，使用它来监听尺寸变化
            if (window.ResizeObserver) {
                this._waitForSizeWithObserver(container, minWidth, minHeight, resolve);
            } else {
                // 降级方案：使用定时器检查
                this._waitForSizeWithTimer(container, minWidth, minHeight, resolve);
            }
        });
    },
    
    /**
     * 设置容器最小尺寸
     * @param {HTMLElement} container - 容器元素
     * @param {number} minWidth - 最小宽度
     * @param {number} minHeight - 最小高度
     */
    _setMinimumSize(container, minWidth, minHeight) {
        // 保存原始样式
        if (!container.dataset.originalStyle) {
            container.dataset.originalStyle = container.style.cssText;
        }
        
        // 设置最小尺寸和必要的样式
        const styles = {
            minWidth: `${minWidth}px`,
            minHeight: `${minHeight}px`,
            width: container.style.width || '100%',
            height: container.style.height || `${minHeight}px`,
            display: container.style.display || 'block',
            position: container.style.position || 'relative'
        };
        
        Object.assign(container.style, styles);
        
        console.log('已设置容器最小尺寸:', styles);
    },
    
    /**
     * 使用ResizeObserver等待容器尺寸就绪
     * @param {HTMLElement} container - 容器元素
     * @param {number} minWidth - 最小宽度
     * @param {number} minHeight - 最小高度
     * @param {Function} resolve - Promise resolve函数
     */
    _waitForSizeWithObserver(container, minWidth, minHeight, resolve) {
        const containerId = this._getContainerId(container);
        
        // 清理已存在的观察器
        if (this.observers.has(containerId)) {
            this.observers.get(containerId).disconnect();
        }
        
        const observer = new ResizeObserver((entries) => {
            for (const entry of entries) {
                const { width, height } = entry.contentRect;
                
                if (width >= minWidth && height >= minHeight) {
                    console.log('容器尺寸已就绪:', { width, height });
                    observer.disconnect();
                    this.observers.delete(containerId);
                    resolve();
                    return;
                }
            }
        });
        
        this.observers.set(containerId, observer);
        observer.observe(container);
        
        // 超时保护
        setTimeout(() => {
            if (this.observers.has(containerId)) {
                console.warn('容器尺寸等待超时，使用当前尺寸');
                observer.disconnect();
                this.observers.delete(containerId);
                resolve();
            }
        }, 3000); // 3秒超时
    },
    
    /**
     * 使用定时器等待容器尺寸就绪（降级方案）
     * @param {HTMLElement} container - 容器元素
     * @param {number} minWidth - 最小宽度
     * @param {number} minHeight - 最小高度
     * @param {Function} resolve - Promise resolve函数
     */
    _waitForSizeWithTimer(container, minWidth, minHeight, resolve) {
        let attempts = 0;
        const maxAttempts = 30; // 最多尝试30次
        
        const checkSize = () => {
            attempts++;
            
            if (container.offsetWidth >= minWidth && container.offsetHeight >= minHeight) {
                console.log('容器尺寸已就绪:', { 
                    width: container.offsetWidth, 
                    height: container.offsetHeight 
                });
                resolve();
                return;
            }
            
            if (attempts >= maxAttempts) {
                console.warn('容器尺寸检查超时，使用当前尺寸');
                resolve();
                return;
            }
            
            setTimeout(checkSize, 100); // 每100ms检查一次
        };
        
        checkSize();
    },
    
    /**
     * 获取容器唯一ID
     * @param {HTMLElement} container - 容器元素
     * @returns {string}
     */
    _getContainerId(container) {
        if (container.id) {
            return container.id;
        }
        
        // 如果没有ID，生成一个唯一标识
        if (!container.dataset.containerId) {
            container.dataset.containerId = 'container_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }
        
        return container.dataset.containerId;
    },
    
    /**
     * 清理容器观察器
     * @param {HTMLElement} container - 容器元素
     */
    cleanup(container) {
        const containerId = this._getContainerId(container);
        
        if (this.observers.has(containerId)) {
            this.observers.get(containerId).disconnect();
            this.observers.delete(containerId);
            console.log('已清理容器观察器:', containerId);
        }
    },
    
    /**
     * 恢复容器原始样式
     * @param {HTMLElement} container - 容器元素
     */
    restoreOriginalStyle(container) {
        if (container.dataset.originalStyle) {
            container.style.cssText = container.dataset.originalStyle;
            delete container.dataset.originalStyle;
            console.log('已恢复容器原始样式');
        }
    },
    
    /**
     * 清理所有观察器（页面卸载时调用）
     */
    cleanupAll() {
        this.observers.forEach((observer, containerId) => {
            observer.disconnect();
            console.log('清理观察器:', containerId);
        });
        this.observers.clear();
    }
};

// 页面卸载时清理所有观察器
window.addEventListener('beforeunload', () => {
    window.ContainerManager.cleanupAll();
}); 