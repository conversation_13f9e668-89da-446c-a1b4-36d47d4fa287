/**
 * 毛玻璃效果控制器 - 简单的开关管理
 */

(function() {
    'use strict';

    const STORAGE_KEY = 'glassmorphism_enabled';

    // 读取localStorage设置
    function getGlassmorphismSetting() {
        return localStorage.getItem(STORAGE_KEY) === 'true';
    }

    // 保存设置到localStorage
    function saveGlassmorphismSetting(enabled) {
        localStorage.setItem(STORAGE_KEY, enabled.toString());
    }

    // 应用毛玻璃效果
    function applyGlassmorphism(enabled) {
        // 选择所有需要毛玻璃效果的卡片元素
        const allCards = document.querySelectorAll(
            '.server-card, .admin-card, .stat-card, .dashboard-card'
        );

        // 选择其他使用玻璃效果的元素（扩大覆盖范围，最小改动方案）
        // 覆盖：Tailwind 类（sm/md/通配）、通用 .backdrop-filter、内联 style 中的 backdrop-filter
        const blurSelectors = [
            '.backdrop-blur-sm',
            '.backdrop-blur-md',
            '.backdrop-filter',
            '[class*="backdrop-blur-"]',
            '[style*="backdrop-filter"]',
            // 保留原有的一些定向元素，确保兼容
            '.globe-controls',
            'footer[class*="backdrop"]',
            '[id*="tooltip"][class*="backdrop"]'
        ].join(', ');

        const blurElements = document.querySelectorAll(blurSelectors);
        
        // 为所有卡片添加或移除毛玻璃类
        allCards.forEach(card => {
            if (enabled) {
                card.classList.add('glass-card');
            } else {
                card.classList.remove('glass-card');
            }
        });

        // 为其他玻璃效果元素添加或移除统一标记类（禁用态兜底）
        blurElements.forEach(element => {
            if (enabled) {
                element.classList.remove('no-glass-effect');
            } else {
                element.classList.add('no-glass-effect');
            }
        });
        
        // 处理body的全局控制类（给离线遮罩等特殊元素用）
        if (enabled) {
            document.body.classList.remove('no-glassmorphism');
        } else {
            document.body.classList.add('no-glassmorphism');
        }
    }

    // 切换毛玻璃效果
    function toggleGlassmorphism() {
        const currentSetting = getGlassmorphismSetting();
        const newSetting = !currentSetting;
        
        saveGlassmorphismSetting(newSetting);
        applyGlassmorphism(newSetting);
        
        return newSetting;
    }

    // 页面加载时自动应用保存的设置
    function initGlassmorphism() {
        const enabled = getGlassmorphismSetting();
        applyGlassmorphism(enabled);
    }

    // 暴露全局函数
    window.toggleGlassmorphism = toggleGlassmorphism;
    window.getGlassmorphismSetting = getGlassmorphismSetting;
    window.applyGlassmorphism = function() {
        const enabled = getGlassmorphismSetting();
        applyGlassmorphism(enabled);
    };

    // DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initGlassmorphism);
    } else {
        initGlassmorphism();
    }

})();
