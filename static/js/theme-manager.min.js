/**
 * 统一主题管理器
 * 整合所有主题切换逻辑，避免重复和冲突
 * 支持亮色主题和暗色主题
 * @updated 2024-05-19 整合所有主题功能
 */

// 立即执行函数，避免污染全局作用域
(function() {
    // 主题类型
    const THEMES = {
        LIGHT: 'light',
        DARK: 'dark'
    };

    // 主题切换速度（毫秒）
    const TRANSITION_DURATION = 150;

    // 从本地存储获取主题
    function getSavedTheme() {
        return localStorage.getItem('theme') || THEMES.DARK;
    }

    // 保存主题到本地存储
    function saveTheme(theme) {
        localStorage.setItem('theme', theme);
    }

    // 获取系统主题偏好
    function getSystemTheme() {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ?
            THEMES.DARK : THEMES.LIGHT;
    }

    // 应用主题
    function applyTheme(theme) {
        // 添加切换中标记，暂时禁用过渡
        document.documentElement.classList.add('theme-switching');

        // 显示主题切换遮罩层
        const overlay = document.getElementById('theme-transition-overlay');
        if (overlay) {
            overlay.classList.remove('hidden');
            // 直接设置不透明度，无需等待下一帧
            overlay.classList.add('opacity-50');
        }

        // 先移除之前的过渡动画类，确保没有残留的过渡效果
        document.documentElement.classList.remove('theme-transition');

        // 使用一个requestAnimationFrame，减少帧等待
        requestAnimationFrame(() => {
            // 添加过渡动画类到文档根元素
            document.documentElement.classList.add('theme-transition');

            // 根据主题设置dark类
            if (theme === THEMES.DARK) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
            
            // 更新按钮状态
            updateThemeToggleButton(theme);
        });

        // 在主题切换完成后更新元素，减少等待时间
        setTimeout(() => {
            // 检查主题是否正确应用
            const isDark = document.documentElement.classList.contains('dark');

            // 更新所有需要响应主题变化的元素
            updateThemeElements(isDark);

            // 触发自定义事件，通知其他组件主题已变化
            const event = new CustomEvent('theme:changed', {
                detail: { isDark, theme },
                bubbles: true
            });
            document.dispatchEvent(event);

            // 移除过渡动画类
            document.documentElement.classList.remove('theme-transition');

            // 移除切换中标记，恢复过渡
            document.documentElement.classList.remove('theme-switching');

            // 隐藏主题切换遮罩层
            const overlay = document.getElementById('theme-transition-overlay');
            if (overlay) {
                overlay.classList.remove('opacity-50');
                // 减少遮罩层淡出时间
                setTimeout(() => {
                    overlay.classList.add('hidden');
                }, 100);
            }
        }, TRANSITION_DURATION);
    }

    // 切换主题
    function toggleTheme() {
        // 获取当前主题
        const currentTheme = getSavedTheme();

        // 切换到相反的主题
        const newTheme = currentTheme === THEMES.DARK ? THEMES.LIGHT : THEMES.DARK;

        // 保存主题偏好
        saveTheme(newTheme);

        // 应用新主题 (已包含过渡动画和更新元素)
        applyTheme(newTheme);

        // 不再触发theme:changed事件，避免与MutationObserver重复触发
    }

    // 更新主题切换按钮状态
    function updateThemeToggleButton(theme) {
        const themeToggleBtn = document.getElementById('theme-toggle');
        if (!themeToggleBtn) return;

        const themeToggleIcon = document.getElementById('theme-toggle-icon');
        const themeToggleText = document.getElementById('theme-toggle-text');

        if (themeToggleIcon) {
            themeToggleIcon.textContent = theme === THEMES.DARK ? 'dark_mode' : 'light_mode';
        }

        if (themeToggleText) {
            themeToggleText.textContent = theme === THEMES.DARK ? '夜间模式' : '日间模式';
        }
    }

    // 更新需要响应主题变化的元素
    function updateThemeElements(isDark) {
        // 一次性添加theme-border类到所有卡片
        // 这样在初始化时就添加了类，不需要在主题切换时再次添加
        document.querySelectorAll('.card, .server-card, .dashboard-inner-card').forEach(card => {
            if (!card.classList.contains('theme-border')) {
                card.classList.add('theme-border');
            }
        });

        // 避免使用requestAnimationFrame触发重绘，因为这可能导致闪烁
        // 而是直接处理所有需要样式更新的元素

        // 处理旧的主题类名兼容性
        handleLegacyThemeClasses(isDark);

        // 更新图表颜色（如果有）
        const charts = window.charts || [];
        for (const chartId in charts) {
            const chart = charts[chartId];
            if (chart && typeof chart.update === 'function') {
                // 更新图表主题
                const newOptions = getChartThemeOptions(isDark);
                chart.update({
                    options: newOptions
                });
            }
        }

        // 触发自定义事件，通知其他组件
        const event = new CustomEvent('theme:updated', {
            detail: { isDark },
            bubbles: true
        });
        document.dispatchEvent(event);
    }

    // 处理旧的主题类名兼容性
    function handleLegacyThemeClasses(isDark) {
        // 定义旧类名到新类名的映射
        const legacyClassMappings = {
            // 文本颜色
            'text-theme-light-text': isDark ? 'text-slate-800' : 'text-white',
            'text-theme-dark-text': isDark ? 'text-white' : 'text-slate-800',
            'text-theme-light-secondary': isDark ? 'text-slate-600' : 'text-slate-300',
            'text-theme-dark-secondary': isDark ? 'text-slate-300' : 'text-slate-600',

            // 背景颜色
            'bg-theme-light-hover': isDark ? 'bg-slate-200/10' : 'bg-slate-700/10',
            'bg-theme-dark-hover': isDark ? 'bg-slate-700/10' : 'bg-slate-200/10'
        };

        // 遍历所有元素，应用新的类名
        for (const [legacyClass, newClass] of Object.entries(legacyClassMappings)) {
            document.querySelectorAll(`.${legacyClass}`).forEach(element => {
                // 移除旧类名
                element.classList.remove(legacyClass);
                // 添加新类名
                element.classList.add(newClass);
            });
        }

        // 将所有带有旧边框类的元素添加theme-border类
        document.querySelectorAll('.border-theme-light-border, .border-theme-dark-border').forEach(element => {
            // 移除旧类名
            element.classList.remove('border-theme-light-border', 'border-theme-dark-border');
            // 添加theme-border类
            element.classList.add('theme-border');
        });
    }

    // 获取图表主题选项
    function getChartThemeOptions(isDark) {
        return {
            theme: {
                mode: isDark ? 'dark' : 'light'
            },
            grid: {
                borderColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
            },
            tooltip: {
                theme: isDark ? 'dark' : 'light'
            }
        };
    }

    // 初始化主题
    function initTheme() {
        // 设置主题初始化标记，阻止任何过渡效果
        document.documentElement.classList.add('theme-initializing');
        
        const savedTheme = getSavedTheme();

        // 设置文档的dark类
        if (savedTheme === THEMES.DARK) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }

        // 更新按钮状态
        updateThemeToggleButton(savedTheme);

        // 使用requestAnimationFrame确保在下一次渲染前完成初始化
        requestAnimationFrame(() => {
            // 添加主题初始化标记
            document.documentElement.classList.add('theme-initialized');
            document.documentElement.classList.remove('theme-initializing');
            
            // 延迟设置，确保在DOM完全加载后执行，同时不会产生闪烁
            document.addEventListener('DOMContentLoaded', () => {
                // 延迟一小段时间再更新元素以确保样式已应用
                setTimeout(() => {
                    const isDark = document.documentElement.classList.contains('dark');
                    updateThemeElements(isDark);
                }, 10);
            });
        });
    }

    // 初始化主题切换按钮
    function initThemeToggleButton() {
        const themeToggleBtn = document.getElementById('theme-toggle');
        if (!themeToggleBtn) return;

        themeToggleBtn.addEventListener('click', toggleTheme);
    }

    // 导出到全局作用域，以便其他脚本可以使用
    window.themeManager = {
        toggleTheme: toggleTheme,
        applyTheme: applyTheme,
        getSavedTheme: getSavedTheme,
        getSystemTheme: getSystemTheme,
        updateThemeElements: updateThemeElements
    };

    // 立即初始化主题，避免闪烁
    initTheme();

    // 当DOM加载完成后，初始化主题切换按钮
    document.addEventListener('DOMContentLoaded', function() {
        initThemeToggleButton();

        // 初始化时立即检查当前主题并更新元素
        const isDark = document.documentElement.classList.contains('dark');
        updateThemeElements(isDark);

        console.log(`主题管理器已初始化，当前主题: ${isDark ? '暗色' : '亮色'}`);
    });
})();
