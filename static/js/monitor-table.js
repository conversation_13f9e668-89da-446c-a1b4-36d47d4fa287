/**
 * Monitor Table - 监控目标表格化管理
 * 替代原有的复杂架构图界面，提供简洁的表格管理功能
 */

(function() {
    'use strict';

    // 应用状态
    const appState = {
        currentPage: 1,
        pageSize: 20,
        selectedTargets: [],
        searchKeyword: '',
        regionFilter: '',
        statusFilter: '',
        sortField: '',
        sortOrder: 'asc',
        isLoading: false,
        editingTarget: null,
        editingRegion: null
    };

    // 数据缓存
    let targetData = [];
    let regionData = [];
    let serverData = [];
    
    // 表格管理器实例
    let tableManager = null;

    // 初始化应用
    function initApp() {
        console.log('Monitor Table 初始化...');
        
        // 初始化地区面板折叠状态
        initRegionsPanelState();
        
        // 初始化表格管理器
        tableManager = new AdminTableManager('monitor-targets', {
            pageSize: 20,
            renderRow: createTargetRow,
            renderCard: createTargetCard
        });
        
        // 设置全局引用（兼容旧代码）
        window.currentTableManager = tableManager;
        
        bindEvents();
        loadData();
    }

    // 绑定事件
    function bindEvents() {
        // 刷新按钮
        const refreshBtn = document.getElementById('btn-refresh');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', loadData);
        }

        // 搜索
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('input', function(e) {
                appState.searchKeyword = e.target.value;
                applyFilters();
            });
        }

        // 筛选
        const regionFilter = document.getElementById('region-filter');
        if (regionFilter) {
            regionFilter.addEventListener('change', function(e) {
                appState.regionFilter = e.target.value;
                applyFilters();
            });
        }

        const statusFilter = document.getElementById('status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', function(e) {
                appState.statusFilter = e.target.value;
                applyFilters();
            });
        }

        // 全选 - 由AdminTableManager处理，这里只需要更新批量操作按钮
        const selectAll = document.getElementById('select-all-monitor-targets');
        if (selectAll) {
            selectAll.addEventListener('change', function() {
                updateBatchButtons();
            });
        }

        // 目标复选框
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('target-checkbox')) {
                const id = e.target.dataset.id;
                if (e.target.checked) {
                    if (!appState.selectedTargets.includes(id)) {
                        appState.selectedTargets.push(id);
                    }
                } else {
                    appState.selectedTargets = appState.selectedTargets.filter(targetId => targetId !== id);
                }
                updateBatchButtons();
                updateSelectAllState();
            }
        });

        // 按钮事件（地区和目标添加由 monitor-page.js 处理，避免重复绑定）
        // document.getElementById('btn-add-region').addEventListener('click', () => showRegionModal());
        // document.getElementById('btn-add-target').addEventListener('click', () => showTargetModal());
        const batchDeleteBtn = document.getElementById('btn-batch-delete');
        const cancelSelectionBtn = document.getElementById('btn-cancel-selection');
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', batchDelete);
        }
        if (cancelSelectionBtn) {
            cancelSelectionBtn.addEventListener('click', cancelSelection);
        }

        // 模态框事件（地区和目标表单由 monitor-page.js 处理，避免重复绑定）
        // document.getElementById('region-cancel').addEventListener('click', hideRegionModal);
        // document.getElementById('target-cancel').addEventListener('click', hideTargetModal);
        // document.getElementById('region-form').addEventListener('submit', handleRegionSubmit);
        // document.getElementById('target-form').addEventListener('submit', handleTargetSubmit);

        // 检测类型变化事件
        document.querySelectorAll('input[name="target-test-type"]').forEach(radio => {
            radio.addEventListener('change', handleTestTypeChange);
        });

        // 筛选器事件
        document.getElementById('clear-filters').addEventListener('click', clearAllFilters);
        document.getElementById('show-online-only').addEventListener('click', toggleOnlineFilter);

        // 筛选器折叠事件
        document.querySelectorAll('.filter-toggle').forEach(toggle => {
            toggle.addEventListener('click', function() {
                const targetId = this.dataset.target;
                const container = document.getElementById(targetId + '-container');
                const icon = this.querySelector('i:last-child');

                if (container && container.classList.contains('hidden')) {
                    container.classList.remove('hidden');
                    if (icon) icon.style.transform = 'rotate(180deg)';
                } else if (container) {
                    container.classList.add('hidden');
                    if (icon) icon.style.transform = 'rotate(0deg)';
                }
            });
        });

        // 分页事件 - 注释掉，因为已由 AdminTableManager 处理
        // document.getElementById('prev-page').addEventListener('click', () => goToPage(appState.currentPage - 1));
        // document.getElementById('next-page').addEventListener('click', () => goToPage(appState.currentPage + 1));

        // 模态框背景点击关闭（地区模态框由 monitor-page.js 处理）
        // document.getElementById('region-modal-backdrop').addEventListener('click', hideRegionModal);
        const targetModalBackdrop = document.getElementById('target-modal-backdrop');
        if (targetModalBackdrop) {
            targetModalBackdrop.addEventListener('click', hideTargetModal);
        }

        // 节点选择抽屉事件
        const closeNodeDrawer = document.getElementById('close-node-drawer');
        const nodeDrawerBackdrop = document.getElementById('node-drawer-backdrop');
        if (closeNodeDrawer) {
            closeNodeDrawer.addEventListener('click', hideNodeDrawer);
        }
        if (nodeDrawerBackdrop) {
            nodeDrawerBackdrop.addEventListener('click', hideNodeDrawer);
        }
        
        // 批量操作按钮事件
        document.getElementById('select-all-nodes').addEventListener('click', () => {
            if (currentTargetId) {
                selectAllNodes(currentTargetId);
            }
        });
        document.getElementById('deselect-all-nodes').addEventListener('click', () => {
            if (currentTargetId) {
                deselectAllNodes(currentTargetId);
            }
        });
        document.getElementById('select-online-nodes').addEventListener('click', () => {
            if (currentTargetId) {
                selectOnlineNodes(currentTargetId);
            }
        });
        
        // ESC键关闭抽屉
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !document.getElementById('node-selection-drawer').classList.contains('hidden')) {
                hideNodeDrawer();
            }
        });

        // 窗口大小变化时调整抽屉宽度和位置
        window.addEventListener('resize', () => {
            const drawer = document.getElementById('node-selection-drawer');
            const content = document.getElementById('node-drawer-content');

            if (drawer && content && !drawer.classList.contains('hidden')) {
                const isMobile = window.innerWidth <= 640;
                const drawerWidth = isMobile ? 'calc(100vw - 32px)' : '480px';

                content.style.width = drawerWidth;
                if (isMobile) {
                    content.style.right = '16px';
                    content.style.top = '16px';
                    content.style.bottom = '16px';
                    content.style.borderRadius = '12px';
                } else {
                    content.style.right = '0';
                    content.style.top = '0';
                    content.style.bottom = '0';
                    content.style.borderRadius = '0';
                }
                console.log('窗口大小变化 - 新宽度:', drawerWidth, '屏幕宽度:', window.innerWidth);
            }
        });

        // 快速筛选标签功能
        document.querySelectorAll('#quick-filters button[data-filter]').forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                const statusFilter = document.getElementById('status-filter');

                // 移除其他按钮的激活状态
                document.querySelectorAll('#quick-filters button[data-filter]').forEach(btn => {
                    btn.classList.remove('bg-indigo-100', 'dark:bg-indigo-900/30', 'text-indigo-700', 'dark:text-indigo-300', 'border-indigo-300', 'dark:border-indigo-700');
                    btn.classList.add('bg-slate-100', 'dark:bg-slate-700', 'text-slate-600', 'dark:text-slate-400', 'border-slate-200', 'dark:border-slate-700/20');
                });

                // 设置当前按钮为激活状态
                this.classList.remove('bg-slate-100', 'dark:bg-slate-700', 'text-slate-600', 'dark:text-slate-400', 'border-slate-200', 'dark:border-slate-700/20');
                this.classList.add('bg-indigo-100', 'dark:bg-indigo-900/30', 'text-indigo-700', 'dark:text-indigo-300', 'border-indigo-300', 'dark:border-indigo-700');

                // 应用筛选
                switch(filter) {
                    case 'online':
                        statusFilter.value = 'online';
                        break;
                    case 'offline':
                        statusFilter.value = 'offline';
                        break;
                    case 'recent':
                        // 可以添加按时间筛选的逻辑
                        statusFilter.value = '';
                        break;
                }

                // 触发筛选事件
                statusFilter.dispatchEvent(new Event('change'));
            });
        });
    }

    // 加载数据
    async function loadData() {
        if (appState.isLoading) return;
        
        appState.isLoading = true;
        updateStatsInfo('加载中...');
        
        try {
            // 并行加载所有必要的数据
            const [regionsResponse, targetsResponse, statusResponse] = await Promise.all([
                fetch('/api/admin/monitor/regions', { credentials: 'same-origin' }),
                fetch('/api/admin/monitor/targets', { credentials: 'same-origin' }),
                fetch('/api/allnode_status', { credentials: 'same-origin' })
            ]);

            if (regionsResponse.ok) {
                const regionsResult = await regionsResponse.json();
                if (regionsResult.success) {
                    regionData = regionsResult.data || [];
                    renderRegionFilter();
                    // 注意：不在这里渲染地区卡片，因为targetData还没加载
                }
            }

            // 加载节点状态数据
            if (statusResponse.ok) {
                const nodeStatusData = await statusResponse.json();
                if (nodeStatusData.success) {
                    nodeStatus = nodeStatusData.data || {};
                }
            }

            if (targetsResponse.ok) {
                const targetsResult = await targetsResponse.json();
                if (targetsResult.success) {
                    targetData = targetsResult.data || [];

                    // 先渲染表格（显示基本信息）
                    renderStatistics();

                    // 现在targetData已加载，可以渲染地区卡片了
                    renderRegionCards();

                    // 使用表格管理器设置数据
                    tableManager.setData(targetData);

                    // 异步加载节点绑定信息，完成后更新状态显示
                    loadTargetNodeBindings().then(() => {
                        // 重新渲染表格以更新状态
                        tableManager.renderPage();
                        renderStatistics();
                    }).catch(error => {
                        console.error('加载节点绑定信息失败:', error);
                    });
                }
            }

            // 计算在线和离线数量
            const onlineCount = targetData.filter(t => t.is_online).length;
            const offlineCount = targetData.length - onlineCount;
            updateStatsInfo(`已加载 ${targetData.length} 个目标 (在线: ${onlineCount}, 离线: ${offlineCount})`);
        } catch (error) {
            console.error('加载数据失败:', error);
            updateStatsInfo('加载失败');
            showNotification('加载数据失败: ' + error.message, 'error');
        } finally {
            appState.isLoading = false;
        }
    }

    // 加载所有目标的节点绑定信息
    async function loadTargetNodeBindings() {
        for (const target of targetData) {
            try {
                const response = await fetch(`/api/admin/monitor/targets/${target.id}`, { credentials: 'same-origin' });
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data && result.data.node_id) {
                        const nodeIds = parseNodeIds(result.data.node_id);
                        selectedNodes.set(target.id, new Set(nodeIds));
                    }
                }
            } catch (error) {
                console.error(`加载目标 ${target.id} 的节点绑定失败:`, error);
            }
        }
    }

    // 渲染地区筛选器
    function renderRegionFilter() {
        const select = document.getElementById('region-filter');
        const currentValue = select.value;
        
        select.innerHTML = '<option value="">全部地区</option>';
        
        regionData.forEach(region => {
            const option = document.createElement('option');
            option.value = region.id;
            option.textContent = region.name;
            if (region.id === currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    }

    // 新增：切换地区面板折叠状态
    function toggleRegionsPanel() {
        const panel = document.getElementById('regions-panel');
        const chevron = document.getElementById('regions-chevron');
        
        if (panel.classList.contains('hidden')) {
            // 展开
            panel.classList.remove('hidden');
            chevron.classList.add('rotate-90');
            localStorage.setItem('regionsPanelCollapsed', 'false');
        } else {
            // 折叠
            panel.classList.add('hidden');
            chevron.classList.remove('rotate-90');
            localStorage.setItem('regionsPanelCollapsed', 'true');
        }
    }

    // 新增：初始化地区面板折叠状态
    function initRegionsPanelState() {
        const isCollapsed = localStorage.getItem('regionsPanelCollapsed') === 'true'; // 默认展开
        const panel = document.getElementById('regions-panel');
        const chevron = document.getElementById('regions-chevron');

        if (isCollapsed) {
            panel.classList.add('hidden');
            chevron.classList.remove('rotate-90');
        } else {
            panel.classList.remove('hidden');
            chevron.classList.add('rotate-90');
        }
    }

    // 优化：渲染Mini地区卡片
    function renderRegionCards() {
        const container = document.getElementById('regions-container');
        const totalSpan = document.getElementById('regions-total');
        
        if (!container || !totalSpan) return;
        
        // 更新地区数量
        totalSpan.textContent = `${regionData.length} 个地区`;
        
        if (regionData.length === 0) {
            container.innerHTML = `
                <div class="flex items-center justify-center min-w-[120px] h-12 text-center border border-dashed border-slate-300/70 dark:border-slate-600/50 rounded-lg bg-slate-50/80 dark:bg-slate-800/30">
                    <div class="flex items-center gap-2">
                        <i class="ti ti-map-2 text-slate-400 dark:text-slate-500"></i>
                        <span class="text-xs text-slate-500 dark:text-slate-400">暂无地区</span>
                    </div>
                </div>
            `;
            return;
        }
        
        // 为每个地区计算目标数量
        const regionsWithTargets = regionData.map(region => {
            const targetsCount = targetData.filter(target => target.region_id === region.id).length;
            return { ...region, targetsCount };
        });
        
        // 生成Mini卡片HTML - 优化项目配色风格
        const cardsHtml = regionsWithTargets.map(region => {
            const canDelete = region.targetsCount === 0;
            const deleteButtonClass = canDelete 
                ? 'text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20' 
                : 'text-slate-300 dark:text-slate-500 cursor-not-allowed';
            const deleteTitle = canDelete 
                ? '删除地区' 
                : `此地区下有 ${region.targetsCount} 个监控目标，无法删除`;
            
            return `
                <div class="region-mini-card bg-white dark:bg-slate-800 border border-slate-200/60 dark:border-slate-700/40 rounded-lg hover:border-slate-300/80 dark:hover:border-slate-600/60 hover:shadow-sm dark:hover:shadow-slate-900/20 transition-all duration-200 relative group"
                     style="min-width: 120px; height: 48px; padding: 8px;"
                     title="${escapeHtml(region.description || region.name)}">
                    <div class="flex flex-col justify-center h-full">
                        <div class="font-medium text-slate-700 dark:text-slate-200 truncate text-xs leading-tight">
                            ${escapeHtml(region.name)}
                        </div>
                        <div class="text-xs text-slate-500 dark:text-slate-400 leading-tight">
                            ${region.targetsCount} 个目标
                        </div>
                    </div>
                    
                    <!-- Hover显示的操作按钮 -->
                    <div class="region-actions absolute top-1 right-1 flex gap-0.5 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <button onclick="editRegion('${region.id}'); event.stopPropagation();" 
                                class="w-5 h-5 rounded flex items-center justify-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors" 
                                title="编辑地区">
                            <i class="ti ti-edit text-xs"></i>
                        </button>
                        <button onclick="${canDelete ? `deleteRegion('${region.id}'); event.stopPropagation();` : ''}" 
                                class="w-5 h-5 rounded flex items-center justify-center transition-colors ${deleteButtonClass}" 
                                title="${deleteTitle}"
                                ${canDelete ? '' : 'disabled'}>
                            <i class="ti ti-trash text-xs"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');
        
        container.innerHTML = cardsHtml;
    }

    // 新增：编辑地区功能
    function editRegion(regionId) {
        const region = regionData.find(r => r.id === regionId);
        if (region) {
            // 填充表单并显示模态框
            document.getElementById('region-id').value = region.id;
            document.getElementById('region-name').value = region.name;
            document.getElementById('region-description').value = region.description || '';

            // 显示模态框（由 monitor-page.js 处理）
            const modal = document.getElementById('region-modal');
            if (modal) {
                modal.classList.remove('hidden');
            }
        }
    }

    // 新增：删除地区功能
    async function deleteRegion(regionId) {
        const region = regionData.find(r => r.id === regionId);
        if (!region) return;
        
        const targetsCount = targetData.filter(target => target.region_id === regionId).length;
        if (targetsCount > 0) {
            showNotification(`该地区下存在 ${targetsCount} 个监控目标，无法删除`, 'error');
            return;
        }
        
        if (!confirm(`确定要删除地区「${region.name}」吗？此操作不可撤销。`)) {
            return;
        }
        
        try {
            const response = await fetch(`/api/admin/monitor/regions/${regionId}`, {
                method: 'DELETE',
                credentials: 'same-origin'
            });
            
            const result = await response.json();
            
            if (result.success) {
                showNotification('地区删除成功', 'success');
                loadData();  // 重新加载数据
            } else {
                if (response.status === 409) {
                    // 后端返回冲突状态，显示详细错误信息
                    showNotification(result.message, 'error');
                } else {
                    throw new Error(result.message || '删除失败');
                }
            }
        } catch (error) {
            console.error('删除地区失败:', error);
            showNotification('删除地区失败: ' + error.message, 'error');
        }
    }

    // 渲染统计数据
    function renderStatistics() {
        const targets = getFilteredTargets();
        const onlineCount = targets.filter(t => getTargetStatus(t) === 'online').length;
        const offlineCount = targets.filter(t => getTargetStatus(t) === 'offline').length;

        // 更新标题栏的统计信息
        updateStatsInfo(`已加载 ${targets.length} 个目标 (在线: ${onlineCount}, 离线: ${offlineCount})`);
    }

    // 应用筛选
    function applyFilters() {
        const filters = getFilteredTargets();
        tableManager.setData(filters);
    }

    // 创建目标行（适配AdminTableManager）
    function createTargetRow(target, index) {
        const row = document.createElement('tr');
        row.className = 'hover:bg-slate-50 dark:hover:bg-slate-800 transition-all duration-200';
        
        const region = regionData.find(r => r.id === target.region_id);
        const status = getTargetStatus(target);
        const statusClass = getStatusClass(status);
        const statusText = getStatusText(status);

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="target-checkbox rounded border-slate-300 text-blue-600 focus:ring-blue-500" 
                       data-id="${target.id}" ${appState.selectedTargets.includes(target.id) ? 'checked' : ''}>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div>
                        <div class="text-sm font-medium text-slate-900 dark:text-white">${escapeHtml(target.name)}</div>
                        <div class="text-sm text-slate-500 dark:text-slate-400">${escapeHtml(target.description || '')}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-slate-900 dark:text-white">${escapeHtml(target.host)}${target.port ? ':' + target.port : ''}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                    ${escapeHtml(region ? region.name : '未知')}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-100">
                    ${target.mode === 'all' ? '全部节点' : '指定节点'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}">
                    ${statusText}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-1">
                    <button class="inline-flex items-center gap-1 px-2.5 py-1.5 text-sm font-medium text-slate-700 hover:bg-slate-100 hover:text-slate-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-slate-100"
                            onclick="openNodeSelection('${target.id}')" title="选择节点">
                        节点
                    </button>
                    <button class="inline-flex items-center gap-1 px-2.5 py-1.5 text-sm font-medium text-slate-700 hover:bg-slate-100 hover:text-slate-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-slate-100"
                            onclick="editTarget('${target.id}')" title="编辑">
                        编辑
                    </button>
                    <button class="inline-flex items-center gap-1 px-2.5 py-1.5 text-sm font-medium text-slate-700 hover:bg-slate-100 hover:text-slate-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-slate-100"
                            onclick="deleteTarget('${target.id}')" title="删除">
                        删除
                    </button>
                </div>
            </td>
        `;
        return row;
    }

    // 创建目标卡片（移动端）
    function createTargetCard(target, index) {
        const card = document.createElement('div');
        card.className = 'bg-white dark:bg-slate-800 border border-slate-200/60 dark:border-slate-800/60 rounded-lg p-4 shadow-sm';
        
        const region = regionData.find(r => r.id === target.region_id);
        const status = getTargetStatus(target);
        const statusClass = getStatusClass(status);
        const statusText = getStatusText(status);

        card.innerHTML = `
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center">
                    <input type="checkbox" class="target-checkbox rounded border-slate-300 text-blue-600 focus:ring-blue-500 mr-3" 
                           data-id="${target.id}" ${appState.selectedTargets.includes(target.id) ? 'checked' : ''}>
                    <div>
                        <div class="text-sm font-medium text-slate-900 dark:text-white">${escapeHtml(target.name)}</div>
                        <div class="text-xs text-slate-500 dark:text-slate-400">${escapeHtml(target.description || '')}</div>
                    </div>
                </div>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusClass}">
                    ${statusText}
                </span>
            </div>
            
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-slate-500 dark:text-slate-400">地址:</span>
                    <span class="text-slate-900 dark:text-white">${escapeHtml(target.host)}${target.port ? ':' + target.port : ''}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-slate-500 dark:text-slate-400">地区:</span>
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                        ${escapeHtml(region ? region.name : '未知')}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-slate-500 dark:text-slate-400">节点:</span>
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-100">
                        ${target.mode === 'all' ? '全部节点' : '指定节点'}
                    </span>
                </div>
            </div>
            
            <div class="flex justify-end gap-1 mt-3 pt-3 border-t border-slate-200/60 dark:border-slate-800/60">
                <button class="inline-flex items-center gap-1 px-2.5 py-1.5 text-xs font-medium text-slate-700 hover:bg-slate-100 hover:text-slate-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-slate-100"
                        onclick="openNodeSelection('${target.id}')" title="选择节点">
                    节点
                </button>
                <button class="inline-flex items-center gap-1 px-2.5 py-1.5 text-xs font-medium text-slate-700 hover:bg-slate-100 hover:text-slate-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-slate-100"
                        onclick="editTarget('${target.id}')" title="编辑">
                    编辑
                </button>
                <button class="inline-flex items-center gap-1 px-2.5 py-1.5 text-xs font-medium text-slate-700 hover:bg-slate-100 hover:text-slate-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-slate-100"
                        onclick="deleteTarget('${target.id}')" title="删除">
                    删除
                </button>
            </div>
        `;
        return card;
    }

    // 获取过滤后的目标
    function getFilteredTargets() {
        let targets = [...targetData];

        // 搜索过滤
        if (appState.searchKeyword) {
            const keyword = appState.searchKeyword.toLowerCase();
            targets = targets.filter(target => 
                target.name.toLowerCase().includes(keyword) ||
                target.host.toLowerCase().includes(keyword) ||
                (target.description || '').toLowerCase().includes(keyword)
            );
        }

        // 地区过滤
        if (appState.regionFilter) {
            targets = targets.filter(target => target.region_id === appState.regionFilter);
        }

        // 状态过滤
        if (appState.statusFilter) {
            targets = targets.filter(target => getTargetStatus(target) === appState.statusFilter);
        }

        // 排序
        if (appState.sortField) {
            targets.sort((a, b) => {
                let aValue = a[appState.sortField];
                let bValue = b[appState.sortField];
                
                if (typeof aValue === 'string') {
                    aValue = aValue.toLowerCase();
                    bValue = bValue.toLowerCase();
                }
                
                if (appState.sortOrder === 'asc') {
                    return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
                } else {
                    return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
                }
            });
        }

        return targets;
    }

    // 渲染分页
    function renderPagination() {
        const targets = getFilteredTargets();
        const totalPages = Math.ceil(targets.length / appState.pageSize);
        const startIndex = (appState.currentPage - 1) * appState.pageSize + 1;
        const endIndex = Math.min(startIndex + appState.pageSize - 1, targets.length);

        document.getElementById('start-index').textContent = targets.length > 0 ? startIndex : 0;
        document.getElementById('end-index').textContent = endIndex;
        document.getElementById('total-records').textContent = targets.length;

        // 渲染页码按钮
        const pageNumbers = document.getElementById('page-numbers');
        pageNumbers.innerHTML = '';

        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= appState.currentPage - 2 && i <= appState.currentPage + 2)) {
                const button = document.createElement('button');
                button.className = `inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                    i === appState.currentPage 
                        ? 'bg-blue-600 text-white rounded-lg shadow-sm hover:shadow-md hover:bg-blue-700 focus:ring-blue-500 dark:bg-blue-600 dark:hover:bg-blue-500'
                        : 'bg-white text-gray-700 border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 hover:shadow-md focus:ring-gray-500 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700'
                }`;
                button.textContent = i;
                button.onclick = () => tableManager.goToPage(i);
                pageNumbers.appendChild(button);
            } else if (i === appState.currentPage - 3 || i === appState.currentPage + 3) {
                const dots = document.createElement('span');
                dots.className = 'relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-slate-700/20 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300';
                dots.textContent = '...';
                pageNumbers.appendChild(dots);
            }
        }

        // 更新上一页/下一页按钮状态
        document.getElementById('prev-page').disabled = appState.currentPage === 1;
        document.getElementById('next-page').disabled = appState.currentPage === totalPages || totalPages === 0;
    }

    // 工具函数
    function getTargetStatus(target) {
        // 基于选中节点的实际状态判断目标状态
        const targetNodes = selectedNodes.get(target.id) || new Set();

        // 如果没有绑定节点数据
        if (targetNodes.size === 0) {
            // 如果是 'all' 模式，基于所有节点的在线状态判断
            if (target.mode === 'all' && allNodes && allNodes.length > 0) {
                let onlineCount = 0;
                let totalCount = 0;
                
                allNodes.forEach(node => {
                    totalCount++;
                    if (getNodeOnlineStatus(node.id)) onlineCount++;
                });
                
                if (totalCount === 0) return 'unknown';
                
                const onlineRate = onlineCount / totalCount;
                if (onlineRate >= 0.8) return 'online';
                if (onlineRate >= 0.5) return 'warning';
                return 'offline';
            }
            // 如果是指定节点模式但还没加载节点数据，返回 unknown
            return 'unknown';
        }

        // 有绑定节点数据时的正常逻辑
        let onlineCount = 0;
        targetNodes.forEach(nodeId => {
            if (getNodeOnlineStatus(nodeId)) onlineCount++;
        });

        const onlineRate = onlineCount / targetNodes.size;
        if (onlineRate >= 0.8) return 'online';
        if (onlineRate >= 0.5) return 'warning';
        return 'offline';
    }

    function getStatusClass(status) {
        const classes = {
            'online': 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100',
            'offline': 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100',
            'warning': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100',
            'unknown': 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100',
            'loading': 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100'
        };
        return classes[status] || classes.unknown;
    }

    function getStatusText(status) {
        const texts = {
            'online': '在线',
            'offline': '离线',
            'warning': '异常',
            'unknown': '检测中',
            'loading': '加载中'
        };
        return texts[status] || '检测中';
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function updateStatsInfo(message) {
        // 新的 mini dashboard 风格更新函数
        // 解析传入的消息格式："已加载 X 个目标 (在线: Y, 离线: Z)"
        const match = message.match(/已加载\s*(\d+)\s*个目标(?:\s*\(在线:\s*(\d+),\s*离线:\s*(\d+)\))?/);
        
        if (match) {
            const total = match[1] || '0';
            const online = match[2] || '0';
            const offline = match[3] || '0';
            
            // 更新各个计数器
            const totalElement = document.getElementById('total-count');
            const onlineElement = document.getElementById('online-count');
            const offlineElement = document.getElementById('offline-count');
            
            if (totalElement) totalElement.textContent = total;
            if (onlineElement) onlineElement.textContent = online;
            if (offlineElement) offlineElement.textContent = offline;
        } else if (message === '加载中...') {
            // 处理加载中状态
            const totalElement = document.getElementById('total-count');
            const onlineElement = document.getElementById('online-count');
            const offlineElement = document.getElementById('offline-count');
            
            if (totalElement) totalElement.textContent = '-';
            if (onlineElement) onlineElement.textContent = '-';
            if (offlineElement) offlineElement.textContent = '-';
        }
    }

    function updateBatchButtons() {
        const batchOperationsBar = document.getElementById('batch-operations-bar');
        const selectedCount = document.getElementById('selected-count');
        
        // 直接使用appState.selectedTargets
        const count = appState.selectedTargets.length;
        
        if (count > 0) {
            // 显示批量操作栏
            batchOperationsBar.classList.remove('hidden');
            selectedCount.textContent = count;
        } else {
            // 隐藏批量操作栏
            batchOperationsBar.classList.add('hidden');
        }
    }

    function updateSelectAllState() {
        const selectAll = document.getElementById('select-all-monitor-targets');
        const checkboxes = document.querySelectorAll('.target-checkbox');
        const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
        
        selectAll.checked = checkedCount === checkboxes.length && checkboxes.length > 0;
        selectAll.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
    }

    // 页面操作
    function goToPage(page) {
        // 使用表格管理器的分页功能
        if (tableManager) {
            tableManager.goToPage(page);
        }
    }

    function sortTable(field) {
        if (appState.sortField === field) {
            appState.sortOrder = appState.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            appState.sortField = field;
            appState.sortOrder = 'asc';
        }
        renderTable();
    }

    // 模态框操作（地区相关已移至 monitor-page.js，避免重复处理）
    /*
    function showRegionModal(region = null) {
        const modal = document.getElementById('region-modal');
        const title = document.getElementById('region-modal-title');
        const form = document.getElementById('region-form');

        if (region) {
            title.textContent = '编辑监控地区';
            document.getElementById('region-id').value = region.id;
            document.getElementById('region-name').value = region.name;
            document.getElementById('region-description').value = region.description || '';
            appState.editingRegion = region;
        } else {
            title.textContent = '添加监控地区';
            form.reset();
            document.getElementById('region-id').value = '';
            appState.editingRegion = null;
        }

        modal.classList.remove('hidden');
    }

    function hideRegionModal() {
        const modal = document.getElementById('region-modal');
        modal.classList.add('hidden');
        appState.editingRegion = null;
    }
    */

    async function showTargetModal(target = null) {
        const modal = document.getElementById('target-modal');
        const title = document.getElementById('target-modal-title');
        const form = document.getElementById('target-form');

        // 加载地区数据到下拉框
        await loadRegionOptions();

        if (target) {
            title.textContent = '编辑监控目标';
            document.getElementById('target-id').value = target.id;
            document.getElementById('target-name').value = target.name;
            document.getElementById('target-host').value = target.host;
            document.getElementById('target-port').value = target.port || '';
            document.getElementById('target-description').value = target.description || '';
            document.getElementById('target-region-id').value = target.region_id;

            // 设置检测类型
            const testType = target.test_type || 'tcping';
            const testTypeRadio = document.querySelector(`input[name="target-test-type"][value="${testType}"]`);
            if (testTypeRadio) {
                testTypeRadio.checked = true;
                // 触发变化事件以更新端口字段状态
                testTypeRadio.dispatchEvent(new Event('change'));
            }

            appState.editingTarget = target;
        } else {
            title.textContent = '添加监控目标';
            form.reset();
            document.getElementById('target-id').value = '';

            // 新增时默认选择tcping，并触发变化事件
            const defaultTestType = document.querySelector('input[name="target-test-type"][value="tcping"]');
            if (defaultTestType) {
                defaultTestType.checked = true;
                defaultTestType.dispatchEvent(new Event('change'));
            }

            appState.editingTarget = null;
        }

        modal.classList.remove('hidden');
    }

    function hideTargetModal() {
        const modal = document.getElementById('target-modal');
        modal.classList.add('hidden');
        appState.editingTarget = null;
    }

    // 加载地区选项到下拉框
    async function loadRegionOptions() {
        const regionSelect = document.getElementById('target-region-id');
        if (!regionSelect) return;

        // 清空现有选项，保留默认选项
        regionSelect.innerHTML = '<option value="">请选择地区</option>';

        // 如果没有地区数据，先尝试加载
        if (!regionData || regionData.length === 0) {
            try {
                const response = await fetch('/api/admin/monitor/regions', { credentials: 'same-origin' });
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        regionData = result.data || [];
                    }
                }
            } catch (error) {
                console.error('加载地区数据失败:', error);
            }
        }

        // 添加地区选项
        if (regionData && regionData.length > 0) {
            regionData.forEach(region => {
                const option = document.createElement('option');
                option.value = region.id;
                option.textContent = region.name;
                regionSelect.appendChild(option);
            });
            console.log('地区选项加载完成，共', regionData.length, '个地区');
        } else {
            // 如果没有地区数据，显示提示
            const option = document.createElement('option');
            option.value = '';
            option.textContent = '暂无地区数据，请先添加地区';
            option.disabled = true;
            regionSelect.appendChild(option);
            console.log('没有地区数据');
        }
    }

    // 表单提交（已移至 monitor-page.js，避免重复处理）
    /*
    async function handleRegionSubmit(e) {
        e.preventDefault();

        const formData = {
            name: document.getElementById('region-name').value,
            description: document.getElementById('region-description').value
        };

        try {
            const url = appState.editingRegion
                ? `/api/monitor/regions/${appState.editingRegion.id}`
                : '/api/monitor/regions';

            const response = await fetch(url, {
                method: appState.editingRegion ? 'PUT' : 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();

            if (result.success) {
                showNotification(appState.editingRegion ? '地区更新成功' : '地区创建成功', 'success');
                hideRegionModal();
                loadData();
            } else {
                showNotification(result.message || '操作失败', 'error');
            }
        } catch (error) {
            console.error('提交失败:', error);
            showNotification('提交失败: ' + error.message, 'error');
        }
    }
    */

    // 处理检测类型变化
    function handleTestTypeChange(e) {
        const testType = e.target.value;
        const portField = document.getElementById('target-port');
        const portLabel = portField.parentElement.querySelector('label');

        if (testType === 'ping') {
            // Ping模式：端口字段变为可选
            portField.removeAttribute('required');
            portField.placeholder = '可选，留空表示使用ICMP ping';
            portLabel.textContent = '端口 (可选)';
        } else {
            // TCPing模式：端口字段为必需
            portField.setAttribute('required', '');
            portField.placeholder = '目标端口号';
            portLabel.textContent = '端口';
        }
    }

    async function handleTargetSubmit(e) {
        e.preventDefault();
        
        // 获取选中的检测类型
        const testType = document.querySelector('input[name="target-test-type"]:checked').value;
        const portValue = document.getElementById('target-port').value.trim();

        // 验证表单数据
        if (testType === 'tcping' && !portValue) {
            showNotification('TCP连接模式下端口号是必需的', 'error');
            return;
        }

        const formData = {
            region_id: document.getElementById('target-region-id').value,
            name: document.getElementById('target-name').value,
            host: document.getElementById('target-host').value,
            port: portValue || null, // ping模式下端口可以为空
            description: document.getElementById('target-description').value,
            mode: 'all', // 暂时设为全部节点
            test_type: testType
        };

        try {
            const url = appState.editingTarget 
                ? `/api/monitor/targets/${appState.editingTarget.id}`
                : '/api/monitor/targets';
            
            const response = await fetch(url, {
                method: appState.editingTarget ? 'PUT' : 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();
            
            if (result.success) {
                showNotification(appState.editingTarget ? '目标更新成功' : '目标创建成功', 'success');
                hideTargetModal();
                loadData();
            } else {
                showNotification(result.message || '操作失败', 'error');
            }
        } catch (error) {
            console.error('提交失败:', error);
            showNotification('提交失败: ' + error.message, 'error');
        }
    }

    // 目标操作
    async function editTarget(id) {
        const target = targetData.find(t => t.id === id);
        if (target) {
            showTargetModal(target);
        }
    }

    async function deleteTarget(id) {
        if (!confirm('确定要删除这个监控目标吗？')) {
            return;
        }

        try {
            const response = await fetch(`/api/admin/monitor/targets/${id}`, {
                method: 'DELETE',
                credentials: 'same-origin'
            });

            const result = await response.json();
            
            if (result.success) {
                showNotification('目标删除成功', 'success');
                loadData();
            } else {
                showNotification(result.message || '删除失败', 'error');
            }
        } catch (error) {
            console.error('删除失败:', error);
            showNotification('删除失败: ' + error.message, 'error');
        }
    }

    // 取消选择
    function cancelSelection() {
        // 清空选中列表
        appState.selectedTargets = [];
        
        // 取消所有复选框的选中状态
        document.querySelectorAll('.target-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        
        // 取消全选复选框
        const selectAll = document.getElementById('select-all-monitor-targets');
        if (selectAll) {
            selectAll.checked = false;
        }
        
        // 更新批量操作按钮
        updateBatchButtons();
    }

    // 批量操作
    async function batchDelete() {
        if (appState.selectedTargets.length === 0) {
            showNotification('请选择要删除的目标', 'warning');
            return;
        }

        if (!confirm(`确定要删除选中的 ${appState.selectedTargets.length} 个目标吗？`)) {
            return;
        }

        try {
            const promises = appState.selectedTargets.map(id =>
                fetch(`/api/admin/monitor/targets/${id}`, { method: 'DELETE', credentials: 'same-origin' })
            );
            
            await Promise.all(promises);
            
            showNotification('批量删除成功', 'success');
            appState.selectedTargets = [];
            loadData();
        } catch (error) {
            console.error('批量删除失败:', error);
            showNotification('批量删除失败: ' + error.message, 'error');
        }
    }


    // 节点选择功能相关状态
    let currentTargetId = null;
    let selectedNodes = new Map();  // targetId -> Set(nodeIds)
    let nodeGroups = [];
    let allNodes = [];
    let nodeStatus = {};

    // 筛选状态管理
    const filterState = {
        groups: new Set(),      // 选中的分组ID
        countries: new Set(),   // 选中的国家代码
        tags: new Set(),        // 选中的标签ID
        showOnlineOnly: false   // 仅显示在线节点
    };

    // 核心筛选函数
    function filterNodes(nodes) {
        return nodes.filter(node => {
            // 分组筛选
            if (filterState.groups.size > 0 && !filterState.groups.has(node.group_id)) {
                return false;
            }

            // 国家筛选
            if (filterState.countries.size > 0) {
                const nodeStatusData = nodeStatus[node.id];
                const countryCode = nodeStatusData?.data?.location?.code;
                if (!countryCode || !filterState.countries.has(countryCode)) {
                    return false;
                }
            }

            // 标签筛选
            if (filterState.tags.size > 0) {
                const nodeStatusData = nodeStatus[node.id];
                const nodeTags = nodeStatusData?.data?.tags || [];
                const hasMatchingTag = nodeTags.some(tag => filterState.tags.has(tag.id));
                if (!hasMatchingTag) {
                    return false;
                }
            }

            // 在线状态筛选
            if (filterState.showOnlineOnly) {
                const isOnline = getNodeOnlineStatus(node.id);
                if (!isOnline) {
                    return false;
                }
            }

            return true;
        });
    }

    // 应用筛选并重新渲染
    function applyFilters() {
        if (!currentTargetId) return;

        const filteredNodes = filterNodes(allNodes);
        const nodesByGroup = organizeNodesByGroup(filteredNodes);
        renderNodeGroups(nodesByGroup, currentTargetId);
        updateFilterCount(filteredNodes.length);
    }

    // 更新筛选计数显示
    function updateFilterCount(filteredCount) {
        const filterCountElement = document.getElementById('filter-count');
        if (filterCountElement) {
            if (filteredCount === allNodes.length) {
                filterCountElement.textContent = `显示所有节点 (${filteredCount})`;
            } else {
                filterCountElement.textContent = `显示 ${filteredCount} / ${allNodes.length} 个节点`;
            }
        }
    }

    // 生成分组筛选按钮
    function generateGroupFilters() {
        const container = document.getElementById('group-filters');

        if (!container) {
            console.error('找不到group-filters容器');
            return;
        }

        if (!nodeGroups.length) {
            console.error('nodeGroups为空');
            container.innerHTML = '<div class="text-sm text-red-500">没有分组数据</div>';
            return;
        }

        container.innerHTML = '';

        // 收集所有分组和节点数量，按节点数量排序
        const groupsWithCounts = [];

        // 添加"全部"选项
        groupsWithCounts.push({
            id: 'all',
            name: '全部',
            count: allNodes.length,
            isAll: true
        });

        // 添加各个分组
        nodeGroups.forEach(group => {
            const groupNodes = allNodes.filter(node => node.group_id === group.id);
            if (groupNodes.length > 0) {
                groupsWithCounts.push({
                    id: group.id,
                    name: group.name,
                    count: groupNodes.length,
                    isAll: false
                });
            }
        });

        // 按节点数量降序排序（全部除外）
        const allOption = groupsWithCounts.find(g => g.isAll);
        const otherGroups = groupsWithCounts.filter(g => !g.isAll).sort((a, b) => b.count - a.count);
        const sortedGroups = [allOption, ...otherGroups];

        // 生成按钮
        sortedGroups.forEach(group => {
            const button = document.createElement('button');
            const isActive = group.isAll && filterState.groups.size === 0;
            button.className = isActive
                ? 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-blue-600 dark:hover:bg-blue-500 whitespace-nowrap cursor-pointer'
                : 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700 whitespace-nowrap cursor-pointer';

            button.innerHTML = `${group.name}(${group.count})`;
            button.dataset.groupId = group.id;
            button.addEventListener('click', () => toggleGroupFilter(group.id));
            container.appendChild(button);
        });
    }

    // 提取所有唯一的国家
    function extractCountries(nodes) {
        const countries = new Map();
        nodes.forEach(node => {
            const nodeStatusData = nodeStatus[node.id];
            const location = nodeStatusData?.data?.location;
            if (location?.code) {
                countries.set(location.code, {
                    code: location.code,
                    name: location.name_zh || location.country_name || location.code
                });
            }
        });
        return Array.from(countries.values());
    }

    // 提取所有唯一的标签
    function extractTags(nodes) {
        const tags = new Map();
        nodes.forEach(node => {
            const nodeStatusData = nodeStatus[node.id];
            const nodeTags = nodeStatusData?.data?.tags || [];
            nodeTags.forEach(tag => {
                if (tag.id && tag.name) {
                    tags.set(tag.id, tag);
                }
            });
        });
        return Array.from(tags.values());
    }

    // 生成国家筛选按钮
    function generateCountryFilters() {
        const container = document.getElementById('country-filters');
        if (!container) return;

        const countries = extractCountries(allNodes);
        if (countries.length === 0) {
            container.innerHTML = '<div class="text-xs text-slate-500 dark:text-slate-400">无</div>';
            return;
        }

        // 收集国家和节点数量，按节点数量排序
        const countriesWithCounts = countries.map(country => {
            const countryNodes = allNodes.filter(node => {
                const nodeStatusData = nodeStatus[node.id];
                return nodeStatusData?.data?.location?.code === country.code;
            });
            return { ...country, count: countryNodes.length };
        }).filter(country => country.count > 0).sort((a, b) => b.count - a.count);

        container.innerHTML = '';

        countriesWithCounts.forEach(country => {
            const button = document.createElement('button');
            button.className = 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700 whitespace-nowrap cursor-pointer';
            button.innerHTML = `${country.code}(${country.count})`;
            button.title = country.name;
            button.dataset.countryCode = country.code;
            button.addEventListener('click', () => toggleCountryFilter(country.code));
            container.appendChild(button);
        });
    }

    // 生成标签筛选按钮
    function generateTagFilters() {
        const container = document.getElementById('tag-filters');
        if (!container) return;

        const tags = extractTags(allNodes);
        if (tags.length === 0) {
            container.innerHTML = '<div class="text-xs text-slate-500 dark:text-slate-400">无</div>';
            return;
        }

        // 收集标签和节点数量，按节点数量排序
        const tagsWithCounts = tags.map(tag => {
            const tagNodes = allNodes.filter(node => {
                const nodeStatusData = nodeStatus[node.id];
                const nodeTags = nodeStatusData?.data?.tags || [];
                return nodeTags.some(nodeTag => nodeTag.id === tag.id);
            });
            return { ...tag, count: tagNodes.length };
        }).filter(tag => tag.count > 0).sort((a, b) => b.count - a.count);

        container.innerHTML = '';

        tagsWithCounts.forEach(tag => {
            const button = document.createElement('button');
            button.className = 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700 whitespace-nowrap cursor-pointer';
            button.innerHTML = `${tag.name}(${tag.count})`;
            button.dataset.tagId = tag.id;
            button.addEventListener('click', () => toggleTagFilter(tag.id));
            container.appendChild(button);
        });
    }

    // 切换分组筛选
    function toggleGroupFilter(groupId) {
        const container = document.getElementById('group-filters');
        const buttons = container.querySelectorAll('.filter-button');

        if (groupId === 'all') {
            // 选择全部，清除所有分组筛选
            filterState.groups.clear();
            buttons.forEach(btn => {
                if (btn.textContent.includes('全部')) {
                    btn.className = 'inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-blue-600 dark:hover:bg-blue-500 whitespace-nowrap cursor-pointer';
                } else {
                    btn.className = 'inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700 whitespace-nowrap cursor-pointer';
                }
            });
        } else {
            // 切换特定分组
            if (filterState.groups.has(groupId)) {
                filterState.groups.delete(groupId);
            } else {
                filterState.groups.add(groupId);
            }

            // 更新按钮样式
            buttons.forEach(btn => {
                if (btn.textContent.includes('全部')) {
                    // 如果有任何分组被选中，取消"全部"的激活状态
                    if (filterState.groups.size > 0) {
                        btn.className = 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700 whitespace-nowrap cursor-pointer';
                    } else {
                        btn.className = 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-blue-600 dark:hover:bg-blue-500 whitespace-nowrap cursor-pointer';
                    }
                } else if (btn.dataset.groupId === groupId) {
                    // 更新当前分组按钮状态
                    if (filterState.groups.has(groupId)) {
                        btn.className = 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-blue-600 dark:hover:bg-blue-500 whitespace-nowrap cursor-pointer';
                    } else {
                        btn.className = 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700 whitespace-nowrap cursor-pointer';
                    }
                }
            });
        }

        applyFilters();
    }

    // 切换国家筛选
    function toggleCountryFilter(countryCode) {
        if (filterState.countries.has(countryCode)) {
            filterState.countries.delete(countryCode);
        } else {
            filterState.countries.add(countryCode);
        }

        // 更新按钮状态
        const container = document.getElementById('country-filters');
        const button = container.querySelector(`[data-country-code="${countryCode}"]`);
        if (button) {
            if (filterState.countries.has(countryCode)) {
                button.className = 'filter-button px-2 py-1 text-xs font-medium rounded border transition-colors cursor-pointer bg-blue-500 text-white border-blue-500 hover:bg-blue-600 whitespace-nowrap';
            } else {
                button.className = 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700 whitespace-nowrap cursor-pointer';
            }
        }

        applyFilters();
    }

    // 切换标签筛选
    function toggleTagFilter(tagId) {
        if (filterState.tags.has(tagId)) {
            filterState.tags.delete(tagId);
        } else {
            filterState.tags.add(tagId);
        }

        // 更新按钮状态
        const container = document.getElementById('tag-filters');
        const button = container.querySelector(`[data-tag-id="${tagId}"]`);
        if (button) {
            if (filterState.tags.has(tagId)) {
                button.className = 'filter-button px-2 py-1 text-xs font-medium rounded border transition-colors cursor-pointer bg-blue-500 text-white border-blue-500 hover:bg-blue-600 whitespace-nowrap';
            } else {
                button.className = 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700 whitespace-nowrap cursor-pointer';
            }
        }

        applyFilters();
    }

    // 清除所有筛选
    function clearAllFilters() {
        filterState.groups.clear();
        filterState.countries.clear();
        filterState.tags.clear();
        filterState.showOnlineOnly = false;

        // 重置分组按钮状态
        const groupContainer = document.getElementById('group-filters');
        if (groupContainer) {
            const buttons = groupContainer.querySelectorAll('.filter-button');
            buttons.forEach(btn => {
                if (btn.textContent.includes('全部')) {
                    btn.className = 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-blue-600 dark:hover:bg-blue-500 whitespace-nowrap cursor-pointer';
                } else {
                    btn.className = 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700 whitespace-nowrap cursor-pointer';
                }
            });
        }

        // 重置国家按钮状态
        const countryContainer = document.getElementById('country-filters');
        if (countryContainer) {
            const buttons = countryContainer.querySelectorAll('.filter-button');
            buttons.forEach(btn => {
                btn.className = 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700 whitespace-nowrap cursor-pointer';
            });
        }

        // 重置标签按钮状态
        const tagContainer = document.getElementById('tag-filters');
        if (tagContainer) {
            const buttons = tagContainer.querySelectorAll('.filter-button');
            buttons.forEach(btn => {
                btn.className = 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700 whitespace-nowrap cursor-pointer';
            });
        }

        // 重置在线筛选按钮
        const onlineButton = document.getElementById('show-online-only');
        if (onlineButton) {
            onlineButton.className = 'flex items-center gap-1 px-2.5 py-1 text-sm bg-slate-200 dark:bg-slate-700 hover:bg-slate-300 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 rounded transition-colors';
        }

        applyFilters();
    }

    // 切换在线筛选
    function toggleOnlineFilter() {
        filterState.showOnlineOnly = !filterState.showOnlineOnly;

        // 更新按钮状态
        const button = document.getElementById('show-online-only');
        if (button) {
            if (filterState.showOnlineOnly) {
                button.className = 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-white bg-green-600 hover:bg-green-700 rounded-md shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 dark:bg-green-600 dark:hover:bg-green-500';
            } else {
                button.className = 'inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700';
            }
        }

        applyFilters();
    }

    // 节点选择功能
    function openNodeSelection(targetId, targetName = null) {
        // 处理新建目标的情况
        if (targetId === 'new-target') {
            console.log(`开启节点选择功能，新建目标: ${targetName}`);
            currentTargetId = targetId;

            // 更新抽屉标题
            document.getElementById('drawer-target-name').textContent = targetName || '新建目标';

            // 初始化空的节点选择
            if (!selectedNodes.has(targetId)) {
                selectedNodes.set(targetId, new Set());
            }

            // 显示抽屉
            showNodeDrawer();

            // 加载节点数据
            loadNodeData(targetId);
        } else {
            // 现有目标的逻辑保持不变
            const target = targetData.find(t => t.id === targetId);
            if (target) {
                console.log(`开启节点选择功能，目标ID: ${targetId}, 目标名称: ${target.name}`);
                currentTargetId = targetId;

                // 更新抽屉标题
                document.getElementById('drawer-target-name').textContent = `目标: ${target.name}`;

                // 显示抽屉
                showNodeDrawer();

                // 加载节点数据
                loadNodeData(targetId);
            } else {
                console.error(`未找到目标: ${targetId}`);
                showNotification('未找到指定的监控目标', 'error');
            }
        }
    }

    // 显示节点选择抽屉
    function showNodeDrawer() {
        const drawer = document.getElementById('node-selection-drawer');
        const backdrop = document.getElementById('node-drawer-backdrop');
        const content = document.getElementById('node-drawer-content');

        if (!drawer || !backdrop || !content) {
            console.error('抽屉元素未找到');
            return;
        }

        console.log('开始显示抽屉 - 屏幕宽度:', window.innerWidth);

        // 显示抽屉容器
        drawer.classList.remove('hidden');

        // 设置抽屉宽度和位置 - 符合行业设计标准
        const isMobile = window.innerWidth <= 640;
        const drawerWidth = isMobile ? 'calc(100vw - 32px)' : '480px'; // 移动端留16px边距

        // 设置初始样式（隐藏状态）
        content.style.width = drawerWidth;
        if (isMobile) {
            // 移动端：居中显示，留边距
            content.style.right = '16px';
            content.style.left = 'auto';
            content.style.top = '16px';
            content.style.bottom = '16px';
            content.style.borderRadius = '12px'; // 圆角增加呼吸感
            content.style.height = 'calc(100vh - 32px)'; // 明确设置高度
        } else {
            // 桌面端：右侧固定
            content.style.right = '0';
            content.style.left = 'auto';
            content.style.top = '0';
            content.style.bottom = '0';
            content.style.borderRadius = '0';
            content.style.height = '100vh'; // 明确设置高度
        }
        content.style.transform = 'translateX(100%)';
        content.style.transition = 'transform 300ms ease-out';
        content.style.zIndex = '60';

        console.log('抽屉样式设置:', {
            width: content.style.width,
            transform: content.style.transform,
            right: content.style.right
        });

        // 使用 requestAnimationFrame 确保动画流畅
        requestAnimationFrame(() => {
            // 显示背景遮罩
            backdrop.classList.add('opacity-100');

            // 滑入抽屉内容
            content.style.transform = 'translateX(0)';

            console.log('动画开始 - transform:', content.style.transform);
        });

        // 禁止背景滚动
        document.body.style.overflow = 'hidden';
    }

    // 隐藏节点选择抽屉
    function hideNodeDrawer() {
        const drawer = document.getElementById('node-selection-drawer');
        const backdrop = document.getElementById('node-drawer-backdrop');
        const content = document.getElementById('node-drawer-content');

        if (!drawer || !backdrop || !content) {
            console.error('抽屉元素未找到');
            return;
        }

        console.log('开始隐藏抽屉');

        // 淡出背景遮罩
        backdrop.classList.remove('opacity-100');

        // 滑出抽屉内容
        content.style.transform = 'translateX(100%)';

        // 在动画结束后隐藏容器
        setTimeout(() => {
            drawer.classList.add('hidden');
            // 恢复背景滚动
            document.body.style.overflow = '';
            // 清理内联样式
            content.style.width = '';
            content.style.right = '';
            content.style.left = '';
            content.style.top = '';
            content.style.bottom = '';
            content.style.borderRadius = '';
            content.style.transform = '';
            content.style.transition = '';
            content.style.zIndex = '';
            // 清理当前目标
            currentTargetId = null;
            console.log('抽屉隐藏完成');
        }, 300);
    }

    // 加载节点数据
    async function loadNodeData(targetId) {
        const container = document.getElementById('node-groups-container');
        container.innerHTML = `
            <div class="flex items-center justify-center h-64">
                <div class="text-center">
                    <i class="ti ti-loader-2 text-4xl text-slate-300 dark:text-slate-600 mb-2 animate-spin"></i>
                    <p class="text-slate-500 dark:text-slate-400">正在加载节点数据...</p>
                </div>
            </div>
        `;
        
        try {
            // 并行加载所有必要的数据
            const [nodesResponse, groupsResponse, statusResponse, targetResponse] = await Promise.all([
                fetch('/api/monitor/nodes'),
                fetch('/api/groups'),
                fetch('/api/allnode_status'),
                fetch(`/api/admin/monitor/targets/${targetId}`, { credentials: 'same-origin' })
            ]);
            
            if (!nodesResponse.ok || !groupsResponse.ok || !statusResponse.ok || !targetResponse.ok) {
                throw new Error('API请求失败');
            }
            
            const nodesData = await nodesResponse.json();
            const groups = await groupsResponse.json();
            const nodeStatusData = await statusResponse.json();
            const targetData = await targetResponse.json();
            
            // 存储数据 - 确保数组类型
            allNodes = Array.isArray(nodesData.data) ? nodesData.data : [];
            nodeGroups = Array.isArray(groups.data) ? groups.data : [];

            // 修复：正确访问API响应的data字段，并添加成功检查
            if (!statusResponse.ok || !nodeStatusData.success) {
                console.error('获取节点状态失败:', nodeStatusData.message || '未知错误');
                nodeStatus = {};
            } else {
                nodeStatus = nodeStatusData.data || {};
            }

            console.log('节点数据加载完成:', {
                节点数量: allNodes.length,
                分组数量: nodeGroups.length
            });
            
            // 初始化当前目标的节点选择状态
            if (!selectedNodes.has(targetId)) {
                selectedNodes.set(targetId, new Set());
            }
            
            // 处理已选择的节点
            if (targetData.data && targetData.data.node_id) {
                const nodeIds = parseNodeIds(targetData.data.node_id);
                selectedNodes.set(targetId, new Set(nodeIds));
            }
            
            // 使用已筛选的节点数据（/api/monitor/nodes 已经筛选过了）
            const availableNodes = allNodes;
            
            // 检查是否有可用节点
            if (availableNodes.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <div class="text-orange-500 dark:text-orange-400 mb-4">
                            <i class="ti ti-settings text-4xl mb-2"></i>
                            <p class="text-lg font-medium">没有可用的监控节点</p>
                            <p class="text-sm text-slate-600 dark:text-slate-400 mt-2">请先配置服务器的API通讯设置</p>
                        </div>
                        <div class="space-y-2 text-sm text-slate-600 dark:text-slate-400">
                            <p>配置步骤：</p>
                            <ol class="list-decimal list-inside space-y-1">
                                <li>进入 <strong>服务器管理</strong> → <strong>编辑服务器</strong></li>
                                <li>在 <strong>高级设置</strong> → <strong>API通讯设置</strong> 中配置</li>
                                <li>设置 <strong>通讯密钥</strong> 和 <strong>API端口</strong></li>
                                <li>保存并重新加载</li>
                            </ol>
                        </div>
                        <button onclick="loadNodeData('${targetId}')" 
                                class="mt-4 inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-800 rounded-lg shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-blue-600 dark:hover:bg-blue-500 dark:focus:bg-blue-500 dark:active:bg-blue-700">
                            重新加载
                        </button>
                    </div>
                `;
                return;
            }
            
            // 生成筛选器
            generateGroupFilters();
            generateCountryFilters();
            generateTagFilters();

            // 按分组组织节点
            const nodesByGroup = organizeNodesByGroup(availableNodes);

            // 渲染节点列表
            renderNodeGroups(nodesByGroup, targetId);

            // 更新计数
            updateNodeCounts(targetId);

            // 初始化筛选计数
            updateFilterCount(availableNodes.length);
            
        } catch (error) {
            console.error('加载节点数据失败:', error);
            container.innerHTML = `
                <div class="text-center py-8">
                    <i class="ti ti-alert-circle text-4xl text-red-300 dark:text-red-600 mb-2"></i>
                    <p class="text-slate-500 dark:text-slate-400">加载节点数据失败</p>
                    <p class="text-sm text-slate-400 dark:text-slate-500 mt-2">${escapeHtml(error.message)}</p>
                    <button class="mt-4 inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-800 rounded-lg shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 dark:bg-blue-600 dark:hover:bg-blue-500 dark:focus:bg-blue-500 dark:active:bg-blue-700"
                            onclick="loadNodeData('${targetId}')">
                        重试
                    </button>
                </div>
            `;
        }
    }
    
    // 解析节点ID
    function parseNodeIds(nodeIdData) {
        if (!nodeIdData) return [];
        
        // 如果是字符串，尝试解析为JSON
        if (typeof nodeIdData === 'string') {
            try {
                const parsed = JSON.parse(nodeIdData);
                return Array.isArray(parsed) ? parsed : [parsed];
            } catch {
                // 如果解析失败，作为单个节点ID返回
                return [nodeIdData];
            }
        }
        
        // 如果已经是数组，直接返回
        if (Array.isArray(nodeIdData)) {
            return nodeIdData;
        }
        
        // 其他情况，转换为字符串作为单个节点ID
        return [String(nodeIdData)];
    }
    
    // 按分组组织节点
    function organizeNodesByGroup(nodes) {
        const grouped = {};
        
        // 创建分组映射
        const groupMap = {};
        nodeGroups.forEach(group => {
            groupMap[group.id] = group.name;
            grouped[group.id] = {
                name: group.name,
                nodes: []
            };
        });
        
        // 确保有默认分组
        if (!grouped['default']) {
            grouped['default'] = {
                name: '默认分组',
                nodes: []
            };
        }
        
        // 分配节点到分组
        nodes.forEach(node => {
            const groupId = node.group_id || 'default';
            if (!grouped[groupId]) {
                grouped[groupId] = {
                    name: groupId,
                    nodes: []
                };
            }
            grouped[groupId].nodes.push(node);
        });
        
        // 移除空分组
        Object.keys(grouped).forEach(key => {
            if (grouped[key].nodes.length === 0) {
                delete grouped[key];
            }
        });
        
        return grouped;
    }
    
    // 渲染节点分组
    function renderNodeGroups(nodesByGroup, targetId) {
        const container = document.getElementById('node-groups-container');
        const targetNodes = selectedNodes.get(targetId) || new Set();
        
        if (Object.keys(nodesByGroup).length === 0) {
            container.innerHTML = `
                <div class="text-center py-8">
                    <i class="ti ti-server text-4xl text-slate-300 dark:text-slate-600 mb-2"></i>
                    <p class="text-slate-500 dark:text-slate-400">没有可用的监控节点</p>
                    <p class="text-sm text-slate-400 dark:text-slate-500 mt-2">请先配置节点API</p>
                </div>
            `;
            return;
        }
        
        let html = '';
        
        Object.entries(nodesByGroup).forEach(([groupId, groupData]) => {
            const groupNodes = groupData.nodes;
            const onlineCount = groupNodes.filter(node => getNodeOnlineStatus(node.id)).length;
            
            html += `
                <div class="border border-slate-200 dark:border-slate-700/40 rounded-lg overflow-hidden mb-4 bg-white dark:bg-slate-800 shadow-sm">
                    <div class="bg-slate-50 dark:bg-slate-700/50 px-4 py-3 cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors duration-200"
                         onclick="toggleNodeGroup('${groupId}')">
                        <div class="flex items-center justify-between">
                            <!-- 分组信息 -->
                            <div class="flex items-center gap-3 flex-1 min-w-0">
                                <i class="ti ti-chevron-down text-slate-400 dark:text-slate-500 transition-transform duration-200 flex-shrink-0"
                                   id="group-icon-${groupId}"></i>
                                <h4 class="font-medium text-slate-700 dark:text-slate-300 text-xs truncate">${escapeHtml(groupData.name)}</h4>
                                <span class="text-xs text-slate-500 dark:text-slate-400 flex-shrink-0">
                                    ${onlineCount}/${groupNodes.length}
                                </span>
                            </div>

                            <!-- 批量操作按钮 -->
                            <div class="flex items-center gap-1 flex-shrink-0">
                                <button class="inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700"
                                        onclick="event.stopPropagation(); selectGroupNodes('${targetId}', '${groupId}', 'all')"
                                        title="全选此分组">
                                    全
                                </button>
                                <button class="inline-flex items-center gap-1 px-2.5 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 dark:bg-gray-800 dark:text-gray-200 dark:border-slate-700/20 dark:hover:bg-gray-700"
                                        onclick="event.stopPropagation(); selectGroupNodes('${targetId}', '${groupId}', 'online')"
                                        title="选择在线节点">
                                    线
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white dark:bg-slate-800" id="group-nodes-${groupId}">
                        ${groupNodes.map(node => {
                            const isSelected = targetNodes.has(node.id);
                            const isOnline = getNodeOnlineStatus(node.id);
                            const nodeData = parseNodeData(node.data);

                            return `
                                <div class="flex items-center gap-2 p-2 hover:bg-slate-50 dark:hover:bg-slate-700/50 cursor-pointer transition-colors border-b border-slate-100 dark:border-slate-700/40 last:border-b-0 ${isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''}"
                                     data-node-id="${node.id}"
                                     onclick="toggleNodeSelection('${targetId}', '${node.id}')">
                                    <!-- 复选框 -->
                                    <input type="checkbox"
                                           class="node-checkbox-list w-3.5 h-3.5 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-1 flex-shrink-0"
                                           style="position: static !important; top: auto !important; right: auto !important;"
                                           ${isSelected ? 'checked' : ''}
                                           onclick="event.stopPropagation()">

                                    <!-- 节点信息区域 -->
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between gap-2">
                                            <!-- 节点名称和位置 -->
                                            <div class="flex-1 min-w-0">
                                                <h5 class="font-medium text-slate-700 dark:text-slate-300 text-xs truncate">${escapeHtml(node.name)}</h5>
                                                ${nodeData.location ? `
                                                    <p class="text-xs text-slate-500 dark:text-slate-400 truncate">${escapeHtml(nodeData.location)}</p>
                                                ` : ''}
                                            </div>

                                            <!-- 在线状态标签 -->
                                            <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium flex-shrink-0
                                                       ${isOnline ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' :
                                                                   'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100'}">
                                                ${isOnline ? '线' : '离'}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
        
        // 绑定复选框事件
        container.querySelectorAll('.node-checkbox-list').forEach(checkbox => {
            checkbox.addEventListener('change', function(e) {
                e.stopPropagation();
                const nodeItem = this.closest('[data-node-id]');
                const nodeId = nodeItem.getAttribute('data-node-id');
                if (nodeId) {
                    toggleNodeSelection(targetId, nodeId);
                }
            });
        });
    }
    
    // 获取节点在线状态
    function getNodeOnlineStatus(nodeId) {
        const status = nodeStatus[nodeId];
        if (!status) return false;

        // 检查是否离线
        if (status.stat === false || (status.stat && status.stat.offline)) {
            return false;
        }

        // -1 表示初始状态，0 表示离线，有具体数据表示在线
        return status.stat && status.stat !== -1 && status.stat !== 0;
    }
    
    // 解析节点数据
    function parseNodeData(data) {
        if (!data) return {};
        if (typeof data === 'string') {
            try {
                return JSON.parse(data);
            } catch {
                return {};
            }
        }
        return data;
    }
    
    // 切换节点分组展开/收缩
    function toggleNodeGroup(groupId) {
        const icon = document.getElementById(`group-icon-${groupId}`);
        const nodesContainer = document.getElementById(`group-nodes-${groupId}`);
        
        if (nodesContainer.style.display === 'none') {
            nodesContainer.style.display = '';
            // 使用Tabler Icons替换Material Icons
            icon.className = 'ti ti-chevron-down';
        } else {
            nodesContainer.style.display = 'none';
            // 使用Tabler Icons替换Material Icons
            icon.className = 'ti ti-chevron-right';
        }
    }
    
    // 切换节点选择状态
    function toggleNodeSelection(targetId, nodeId) {
        if (!selectedNodes.has(targetId)) {
            selectedNodes.set(targetId, new Set());
        }

        const targetNodes = selectedNodes.get(targetId);
        const wasSelected = targetNodes.has(nodeId);

        if (wasSelected) {
            targetNodes.delete(nodeId);
        } else {
            targetNodes.add(nodeId);
        }

        // 局部更新UI，避免重新渲染整个列表
        updateSingleNodeUI(nodeId, !wasSelected);

        updateNodeCounts(targetId);

        // 自动保存
        autoSaveNodeSelection(targetId);
    }

    // 新增：局部更新单个节点UI
    function updateSingleNodeUI(nodeId, isSelected) {
        const nodeItem = document.querySelector(`[data-node-id="${nodeId}"]`);
        if (!nodeItem) {
            console.warn(`节点项未找到: ${nodeId}`);
            return;
        }

        const checkbox = nodeItem.querySelector('.node-checkbox-list');
        if (checkbox) {
            checkbox.checked = isSelected;
        }

        // 更新列表项选中状态样式
        if (isSelected) {
            nodeItem.classList.add('bg-blue-50', 'dark:bg-blue-900/20');
        } else {
            nodeItem.classList.remove('bg-blue-50', 'dark:bg-blue-900/20');
        }
    }
    
    // 更新节点计数
    function updateNodeCounts(targetId) {
        const targetNodes = selectedNodes.get(targetId) || new Set();
        const availableNodes = allNodes; // /api/monitor/nodes 已经过滤过了
        
        document.getElementById('selected-nodes-count').textContent = targetNodes.size;
        document.getElementById('total-nodes-count').textContent = availableNodes.length;
    }
    
    // 自动保存节点选择（带防抖）
    let autoSaveTimer = null;
    function autoSaveNodeSelection(targetId) {
        if (autoSaveTimer) {
            clearTimeout(autoSaveTimer);
        }
        
        autoSaveTimer = setTimeout(async () => {
            // 新建目标时只同步，不保存
            if (targetId === 'new-target') {
                syncNodesToEditModal(targetId);
                return;
            }

            // 现有目标的保存逻辑保持不变
            const target = targetData.find(t => t.id === targetId);
            if (!target) return;

            const selectedNodeIds = Array.from(selectedNodes.get(targetId) || []);

            try {
                const data = {
                    region_id: target.region_id,
                    name: target.name,
                    host: target.host,
                    port: target.port,
                    test_type: target.test_type || 'tcping',
                    description: target.description,
                    mode: selectedNodeIds.length === 0 ? 'all' : 'specific',
                    node_ids: selectedNodeIds
                };

                const response = await fetch(`/api/admin/monitor/targets/${targetId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'same-origin',
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('节点配置已自动保存', 'success');
                } else {
                    throw new Error(result.message || '保存失败');
                }
            } catch (error) {
                console.error('自动保存失败:', error);
                showNotification('自动保存失败: ' + error.message, 'error');
            }
        }, 500);
    }
    
    // 批量操作函数
    
    // 全选所有节点
    function selectAllNodes(targetId) {
        if (!selectedNodes.has(targetId)) {
            selectedNodes.set(targetId, new Set());
        }

        const targetNodes = selectedNodes.get(targetId);
        const availableNodes = allNodes; // /api/monitor/nodes 已经过滤过了

        // 清空后添加所有节点
        targetNodes.clear();
        availableNodes.forEach(node => {
            targetNodes.add(node.id);
            // 局部更新每个节点的UI
            updateSingleNodeUI(node.id, true);
        });

        updateNodeCounts(targetId);
        autoSaveNodeSelection(targetId);

        showNotification(`已选择所有 ${availableNodes.length} 个节点`, 'success');
    }
    
    // 反选节点
    function deselectAllNodes(targetId) {
        if (!selectedNodes.has(targetId)) {
            selectedNodes.set(targetId, new Set());
        }

        const targetNodes = selectedNodes.get(targetId);
        const availableNodes = allNodes; // /api/monitor/nodes 已经过滤过了

        // 反选操作
        const newSelection = new Set();
        availableNodes.forEach(node => {
            const wasSelected = targetNodes.has(node.id);
            const willBeSelected = !wasSelected;

            if (willBeSelected) {
                newSelection.add(node.id);
            }

            // 局部更新每个节点的UI
            updateSingleNodeUI(node.id, willBeSelected);
        });

        selectedNodes.set(targetId, newSelection);

        updateNodeCounts(targetId);
        autoSaveNodeSelection(targetId);

        showNotification(`已反选节点，当前选中 ${newSelection.size} 个`, 'info');
    }
    
    // 仅选在线节点
    function selectOnlineNodes(targetId) {
        if (!selectedNodes.has(targetId)) {
            selectedNodes.set(targetId, new Set());
        }

        const targetNodes = selectedNodes.get(targetId);
        const availableNodes = allNodes; // /api/monitor/nodes 已经过滤过了

        // 获取所有在线节点
        const onlineNodes = availableNodes.filter(node => getNodeOnlineStatus(node.id));

        // 检查是否所有在线节点都已选中
        const allOnlineSelected = onlineNodes.every(node => targetNodes.has(node.id));

        if (allOnlineSelected) {
            // 如果都已选中，则取消选择所有在线节点
            onlineNodes.forEach(node => {
                targetNodes.delete(node.id);
                updateSingleNodeUI(node.id, false);
            });
            showNotification(`已取消选择所有在线节点`, 'info');
        } else {
            // 否则选择所有在线节点
            onlineNodes.forEach(node => {
                targetNodes.add(node.id);
                updateSingleNodeUI(node.id, true);
            });
            showNotification(`已选择 ${onlineNodes.length} 个在线节点`, 'success');
        }

        updateNodeCounts(targetId);
        autoSaveNodeSelection(targetId);
    }
    
    // 分组操作
    function selectGroupNodes(targetId, groupId, mode) {
        if (!selectedNodes.has(targetId)) {
            selectedNodes.set(targetId, new Set());
        }
        
        const targetNodes = selectedNodes.get(targetId);
        const availableNodes = allNodes; // /api/monitor/nodes 已经过滤过了
        
        // 获取分组节点
        const groupNodes = availableNodes.filter(node => 
            (node.group_id || 'default') === groupId
        );
        
        if (mode === 'all') {
            // 全选分组
            const allSelected = groupNodes.every(node => targetNodes.has(node.id));
            if (allSelected) {
                // 如果都已选中，则取消选择
                groupNodes.forEach(node => {
                    targetNodes.delete(node.id);
                    updateSingleNodeUI(node.id, false);
                });
            } else {
                // 否则全选
                groupNodes.forEach(node => {
                    targetNodes.add(node.id);
                    updateSingleNodeUI(node.id, true);
                });
            }
        } else if (mode === 'online') {
            // 仅选在线
            const onlineGroupNodes = groupNodes.filter(node => getNodeOnlineStatus(node.id));
            const allOnlineSelected = onlineGroupNodes.every(node => targetNodes.has(node.id));

            if (allOnlineSelected) {
                // 取消选择在线节点
                onlineGroupNodes.forEach(node => {
                    targetNodes.delete(node.id);
                    updateSingleNodeUI(node.id, false);
                });
            } else {
                // 选择在线节点
                onlineGroupNodes.forEach(node => {
                    targetNodes.add(node.id);
                    updateSingleNodeUI(node.id, true);
                });
            }
        }

        updateNodeCounts(targetId);
        autoSaveNodeSelection(targetId);
    }

    // 通知系统
    function showNotification(message, type = 'info') {
        if (window.showNotification) {
            window.showNotification(message, type);
        } else {
            console.log(`[${type}] ${message}`);
        }
    }

    // 全局函数暴露
    window.openNodeSelection = openNodeSelection;
    window.showNodeDrawer = showNodeDrawer;
    window.hideNodeDrawer = hideNodeDrawer;
    window.toggleNodeGroup = toggleNodeGroup;
    window.toggleNodeSelection = toggleNodeSelection;
    window.selectGroupNodes = selectGroupNodes;
    window.loadNodeData = loadNodeData;
    window.editTarget = editTarget;
    window.deleteTarget = deleteTarget;
    window.sortTable = sortTable;
    window.goToPage = goToPage;
    
    // 导出地区管理函数为全局函数
    window.toggleRegionsPanel = toggleRegionsPanel;
    window.editRegion = editRegion;
    window.deleteRegion = deleteRegion;
    window.loadData = loadData; // 暴露数据加载函数

    // 同步节点选择到编辑弹窗
    function syncNodesToEditModal(targetId) {
        if (window.state) {
            const selectedNodeIds = Array.from(selectedNodes.get(targetId) || []);
            // 更新编辑弹窗的状态
            window.state.currentTargetNodes = selectedNodeIds;
        }
    }

    // 从编辑弹窗同步节点选择
    function syncTargetNodesToGlobal(targetId, nodeIds) {
        if (targetId && Array.isArray(nodeIds)) {
            selectedNodes.set(targetId, new Set(nodeIds));
        }
    }

    // 关闭节点抽屉
    function closeNodeDrawer() {
        const drawer = document.getElementById('node-selection-drawer');
        const backdrop = document.getElementById('node-drawer-backdrop');
        const content = document.getElementById('node-drawer-content');

        // 在关闭前同步状态到编辑弹窗
        if (currentTargetId) {
            syncNodesToEditModal(currentTargetId);
        }

        if (drawer && backdrop && content) {
            content.style.transform = 'translateX(100%)';
            setTimeout(() => {
                drawer.classList.add('hidden');
            }, 300);
        }
    }

    // 暴露给编辑弹窗使用的全局函数
    window.openNodeSelection = openNodeSelection;
    window.syncTargetNodesToGlobal = syncTargetNodesToGlobal;
    window.syncNodesToEditModal = syncNodesToEditModal;
    window.closeNodeDrawer = closeNodeDrawer;

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initApp);
    } else {
        initApp();
    }

})();