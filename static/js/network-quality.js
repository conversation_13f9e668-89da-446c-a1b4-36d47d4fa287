/**
 * 网络质量监控页面JavaScript
 */

class NetworkQualityMonitor {
    constructor() {
        this.charts = new Map(); // 存储图表实例
        this.currentTimeRange = '24h';
        this.refreshInterval = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadData();
        this.startAutoRefresh();
    }

    bindEvents() {
        // 时间范围切换
        const timeRangeSelect = document.getElementById('time-range-select');
        if (timeRangeSelect) {
            timeRangeSelect.addEventListener('change', (e) => {
                this.currentTimeRange = e.target.value;
                this.loadData();
            });
        }

        // 重试按钮
        const retryBtn = document.getElementById('retry-btn');
        if (retryBtn) {
            retryBtn.addEventListener('click', () => {
                this.loadData();
            });
        }

        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopAutoRefresh();
            } else {
                this.startAutoRefresh();
                this.loadData();
            }
        });
    }

    async loadData() {
        try {
            this.showLoading();
            this.hideError();

            const response = await fetch(`/api/network-quality/nodes-overview?timeRange=${this.currentTimeRange}`);
            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.message || `HTTP ${response.status}`);
            }

            if (result.success) {
                this.renderData(result.data);
                this.hideLoading();
            } else {
                throw new Error(result.message || '数据加载失败');
            }
        } catch (error) {
            console.error('加载网络质量数据失败:', error);
            this.showError(error.message);
            this.hideLoading();
        }
    }

    renderData(data) {
        this.updateSummary(data.summary);
        
        if (data.nodes && data.nodes.length > 0) {
            this.renderNodes(data.nodes);
            this.hideEmptyState();
        } else {
            this.showEmptyState();
        }
    }

    updateSummary(summary) {
        // 更新概览统计
        this.updateElement('overall-health', `${summary.healthScore || 0}%`);
        this.updateElement('total-nodes', summary.totalNodes || 0);
        this.updateElement('active-nodes', `${summary.totalNodes || 0} 个活跃`);
        this.updateElement('avg-latency', `${summary.avgLatency || 0}`);
        this.updateElement('total-targets', summary.totalTargets || 0);
    }

    renderNodes(nodes) {
        const container = document.getElementById('nodes-container');
        if (!container) return;

        // 清空现有内容
        container.innerHTML = '';
        this.clearCharts();

        nodes.forEach(node => {
            const nodeCard = this.createNodeCard(node);
            container.appendChild(nodeCard);
        });
    }

    createNodeCard(node) {
        const template = document.getElementById('node-card-template');
        const nodeCard = template.content.cloneNode(true);

        // 设置节点信息
        nodeCard.querySelector('.node-name').textContent = node.nodeName;
        nodeCard.querySelector('.node-id').textContent = `节点ID: ${node.nodeId}`;
        nodeCard.querySelector('.node-avg-latency').textContent = `${node.nodeMetrics.avgLatency}ms`;
        nodeCard.querySelector('.node-health-score').textContent = `${node.nodeMetrics.healthScore}%`;

        // 设置节点状态
        const statusIndicator = nodeCard.querySelector('.node-status-indicator');
        statusIndicator.classList.add(node.nodeStatus === 1 ? 'online' : 'offline');

        // 渲染监控目标
        const targetsGrid = nodeCard.querySelector('.targets-grid');
        node.targets.forEach(target => {
            const targetCard = this.createTargetCard(target, node.nodeId);
            targetsGrid.appendChild(targetCard);
        });

        return nodeCard;
    }

    createTargetCard(target, nodeId) {
        const template = document.getElementById('target-card-template');
        const targetCard = template.content.cloneNode(true);

        // 设置目标信息
        targetCard.querySelector('.target-name').textContent = target.targetName;
        targetCard.querySelector('.target-endpoint').textContent = `${target.host}:${target.port}`;
        
        // 设置指标数据
        targetCard.querySelector('.target-latency').textContent = target.metrics.avgLatency || 0;
        targetCard.querySelector('.target-availability').textContent = 
            (target.metrics.availability || 0).toFixed(1);
        targetCard.querySelector('.target-packet-loss').textContent = 
            (target.metrics.packetLoss || 0).toFixed(1);

        // 设置状态徽章
        const statusBadge = targetCard.querySelector('.target-status-badge');
        const status = this.getTargetStatus(target.metrics);
        statusBadge.textContent = status.text;
        statusBadge.classList.add(status.class);

        // 应用指标颜色
        this.applyMetricColors(targetCard, target.metrics);

        // 创建图表
        const chartContainer = targetCard.querySelector('.target-chart');
        chartContainer.id = `chart-${nodeId}-${target.targetId}`;
        
        // 使用setTimeout确保DOM元素已插入
        setTimeout(() => {
            this.createChart(chartContainer.id, target.chartData, target.targetName);
        }, 100);

        return targetCard;
    }

    getTargetStatus(metrics) {
        const availability = metrics.availability || 0;
        const latency = metrics.avgLatency || 0;

        if (availability >= 99 && latency < 50) {
            return { text: '优秀', class: 'excellent' };
        } else if (availability >= 95 && latency < 100) {
            return { text: '良好', class: 'good' };
        } else if (availability >= 80) {
            return { text: '一般', class: 'poor' };
        } else {
            return { text: '离线', class: 'offline' };
        }
    }

    applyMetricColors(targetCard, metrics) {
        const latencyEl = targetCard.querySelector('.target-latency');
        const availabilityEl = targetCard.querySelector('.target-availability');
        const packetLossEl = targetCard.querySelector('.target-packet-loss');

        // 延迟颜色
        const latency = metrics.avgLatency || 0;
        if (latency === 0) {
            latencyEl.classList.add('metric-offline');
        } else if (latency < 50) {
            latencyEl.classList.add('metric-excellent');
        } else if (latency < 100) {
            latencyEl.classList.add('metric-good');
        } else {
            latencyEl.classList.add('metric-poor');
        }

        // 可用性颜色
        const availability = metrics.availability || 0;
        if (availability >= 99) {
            availabilityEl.classList.add('metric-excellent');
        } else if (availability >= 95) {
            availabilityEl.classList.add('metric-good');
        } else if (availability >= 80) {
            availabilityEl.classList.add('metric-poor');
        } else {
            availabilityEl.classList.add('metric-offline');
        }

        // 丢包率颜色（颜色与丢包率成反比）
        const packetLoss = metrics.packetLoss || 0;
        if (packetLoss <= 1) {
            packetLossEl.classList.add('metric-excellent');
        } else if (packetLoss <= 5) {
            packetLossEl.classList.add('metric-good');
        } else if (packetLoss <= 20) {
            packetLossEl.classList.add('metric-poor');
        } else {
            packetLossEl.classList.add('metric-offline');
        }
    }

    createChart(containerId, chartData, targetName) {
        const container = document.getElementById(containerId);
        if (!container || !chartData || !chartData.times || chartData.times.length === 0) {
            // 显示无数据状态
            container.innerHTML = `
                <div class="flex items-center justify-center h-full text-slate-400">
                    <div class="text-center">
                        <i class="ti ti-chart-line text-2xl mb-2"></i>
                        <div class="text-sm">暂无数据</div>
                    </div>
                </div>
            `;
            return;
        }

        try {
            const chart = echarts.init(container);
            
            const option = {
                title: {
                    show: false
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    formatter: function(params) {
                        let result = `<div class="font-medium">${new Date(params[0].name).toLocaleString()}</div>`;
                        params.forEach(param => {
                            if (param.seriesName === '延迟') {
                                result += `<div class="flex items-center gap-2">
                                    <span style="color: ${param.color}">●</span>
                                    ${param.seriesName}: ${param.value !== null ? param.value + 'ms' : '无数据'}
                                </div>`;
                            } else {
                                result += `<div class="flex items-center gap-2">
                                    <span style="color: ${param.color}">●</span>
                                    ${param.seriesName}: ${param.value !== null ? param.value + '%' : '无数据'}
                                </div>`;
                            }
                        });
                        return result;
                    }
                },
                legend: {
                    data: ['延迟', '丢包率'],
                    textStyle: {
                        fontSize: 12
                    },
                    bottom: 0
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    top: '5%',
                    containLabel: true
                },
                xAxis: {
                    type: 'time',
                    boundaryGap: false,
                    axisLabel: {
                        fontSize: 10,
                        formatter: function(value) {
                            const date = new Date(value);
                            if (chartData.times.length > 48) {
                                // 数据点多时只显示日期
                                return `${date.getMonth()+1}/${date.getDate()}`;
                            } else {
                                // 数据点少时显示时间
                                return `${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
                            }
                        }
                    },
                    splitLine: {
                        show: false
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '延迟(ms)',
                        position: 'left',
                        axisLabel: {
                            fontSize: 10,
                            formatter: '{value}ms'
                        },
                        splitLine: {
                            lineStyle: {
                                type: 'dashed',
                                opacity: 0.3
                            }
                        }
                    },
                    {
                        type: 'value',
                        name: '丢包率(%)',
                        position: 'right',
                        axisLabel: {
                            fontSize: 10,
                            formatter: '{value}%'
                        },
                        max: 100,
                        splitLine: {
                            show: false
                        }
                    }
                ],
                series: [
                    {
                        name: '延迟',
                        type: 'line',
                        yAxisIndex: 0,
                        data: chartData.times.map((time, index) => [time, chartData.latencies[index]]),
                        smooth: true,
                        symbol: 'none',
                        lineStyle: {
                            width: 2,
                            color: '#3b82f6'
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0, y: 0, x2: 0, y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
                                    { offset: 1, color: 'rgba(59, 130, 246, 0.1)' }
                                ]
                            }
                        }
                    },
                    {
                        name: '丢包率',
                        type: 'line',
                        yAxisIndex: 1,
                        data: chartData.times.map((time, index) => [time, chartData.packetLoss[index]]),
                        smooth: true,
                        symbol: 'none',
                        lineStyle: {
                            width: 2,
                            color: '#ef4444'
                        }
                    }
                ]
            };

            chart.setOption(option);
            this.charts.set(containerId, chart);

            // 响应式调整
            const resizeObserver = new ResizeObserver(() => {
                chart.resize();
            });
            resizeObserver.observe(container);

        } catch (error) {
            console.error(`创建图表失败 (${containerId}):`, error);
            container.innerHTML = `
                <div class="flex items-center justify-center h-full text-red-400">
                    <div class="text-center">
                        <i class="ti ti-alert-circle text-2xl mb-2"></i>
                        <div class="text-sm">图表加载失败</div>
                    </div>
                </div>
            `;
        }
    }

    clearCharts() {
        this.charts.forEach((chart, id) => {
            try {
                chart.dispose();
            } catch (error) {
                console.warn(`清理图表失败 (${id}):`, error);
            }
        });
        this.charts.clear();
    }

    startAutoRefresh() {
        this.stopAutoRefresh();
        // 每30秒自动刷新数据
        this.refreshInterval = setInterval(() => {
            this.loadData();
        }, 30000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    showLoading() {
        this.showElement('loading-indicator');
        this.hideElement('nodes-container');
        this.hideElement('empty-state');
    }

    hideLoading() {
        this.hideElement('loading-indicator');
        this.showElement('nodes-container');
    }

    showError(message) {
        const errorEl = document.getElementById('error-message');
        const errorTextEl = document.getElementById('error-text');
        if (errorEl && errorTextEl) {
            errorTextEl.textContent = message;
            errorEl.classList.remove('hidden');
        }
    }

    hideError() {
        this.hideElement('error-message');
    }

    showEmptyState() {
        this.showElement('empty-state');
        this.hideElement('nodes-container');
    }

    hideEmptyState() {
        this.hideElement('empty-state');
    }

    showElement(id) {
        const el = document.getElementById(id);
        if (el) el.classList.remove('hidden');
    }

    hideElement(id) {
        const el = document.getElementById(id);
        if (el) el.classList.add('hidden');
    }

    updateElement(id, content) {
        const el = document.getElementById(id);
        if (el) el.textContent = content;
    }

    destroy() {
        this.stopAutoRefresh();
        this.clearCharts();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.networkQualityMonitor = new NetworkQualityMonitor();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', function() {
    if (window.networkQualityMonitor) {
        window.networkQualityMonitor.destroy();
    }
});