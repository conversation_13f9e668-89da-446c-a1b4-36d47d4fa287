/**
 * Region Globe 3D可视化组件
 * 提供3D地球展示节点分布
 */
window.RegionGlobe = {
    // 组件状态
    globe: null,
    container: null,
    isInitialized: false,
    isPaused: false,
    animationId: null,
    // 性能档位（low/medium/high），用于渲染策略
    perfLevel: 'medium',
    
    // 数据缓存
    currentData: [],
    arcsData: [],
    countriesData: null,
    
    // 配置选项
    config: {
        // 性能配置
        enableAnimation: true,
        rotationSpeed: -0.003,  // 负值表示顺时针自转，进一步减慢速度
        arcAnimationDuration: 2000,
        maxArcs: 8,  // 增加最大弧线数量
        arcGenerateInterval: 1500,  // 减少生成间隔，更快初始化
        arcInitialDelay: 500,  // 初始延迟时间
        
        // GPU/WebGL性能优化
        enableMouseTracking: false,  // 禁用鼠标跟踪提升性能
        arcCurveResolution: 32,  // 降低弧线曲率分辨率（默认64）
        
        // 视觉配置（立体边界效果）
        pointAltitude: -0.001,    // 节点高度（负值让节点略低于地表，避免遮挡）
        pointRadius: 0.15,        // 节点半径（减小一半，避免影响小地区）
        pointResolution: 4,       // 节点分辨率（降低提升性能）
        polygonAltitude: 0.018,   // 多边形高度（增加立体效果）
        // polygonStrokeWidth: 1.2,  // 边框线宽度（API不支持）
        pathAltitude: 0.012,      // 边框线高度（与多边形同步）
        hoverAltitude: 0.02,      // 鼠标悬停时的高度
        showGraticules: false,    // 关闭经纬网提升性能
        showCountryBorders: true,
        showLabels: true,
        pulseAnimation: false,    // 关闭脉冲动画提升性能
        
        // 高级效果配置
        enableClouds: false,  // 云层效果
        enableBumpMap: false,  // 地形凹凸效果
        enableWaterEffect: false,  // 水面反射效果
        enableStarfield: true,    // 星空背景效果
        
        // 地球风格配置 - 简化为自动主题适配
        globeStyles: {
            dark: {
                name: '夜景模式',
                url: '/globe/textures/earth-night.jpg'
            },
            light: {
                name: '日景模式', 
                url: '/globe/textures/earth-blue-marble.jpg'
            }
        },
        
        // 主题配置
        themes: {
            dark: {
                globeImageUrl: '/globe/textures/earth-night.jpg',
                backgroundImageUrl: '/globe/textures/night-sky.png',  // 夜间星空背景
                atmosphereColor: '#6366f1',
                atmosphereAltitude: 0.15,
                pointColor: node => {
                    const intensity = node.intensity || 0.5;
                    // 修复：使用rgb()而不是rgba()避免THREE.js警告
                    const r = Math.floor(99 + intensity * 100);
                    const g = Math.floor(102 + intensity * 50);
                    const b = Math.floor(241);
                    return `rgb(${r}, ${g}, ${b})`;
                },
                arcColor: () => ['rgba(96, 165, 250, 0.5)', 'rgba(192, 132, 252, 0.5)'],
                backgroundColor: '#000411'  // 深空背景色
            },
            light: {
                globeImageUrl: '/globe/textures/earth-blue-marble.jpg',
                backgroundImageUrl: '',  // 亮色主题不使用星空背景
                atmosphereColor: '#6366f1',
                atmosphereAltitude: 0.12,
                pointColor: node => {
                    const intensity = node.intensity || 0.5;
                    // 修复：使用rgb()而不是rgba()避免THREE.js警告
                    const alpha = Math.floor((0.5 + intensity * 0.5) * 255);
                    return `rgb(59, 130, ${Math.min(255, 246 + alpha)})`;
                },
                arcColor: () => ['rgba(59, 130, 246, 0.5)', 'rgba(139, 92, 246, 0.5)'],
                backgroundColor: '#e0e7ff'  // 亮色主题背景
            }
        }
    },
    
    /**
     * 初始化3D地球
     */
    async init(containerId, initialData = []) {
        try {
            // 检查容器
            this.container = document.getElementById(containerId);
            if (!this.container) {
                throw new Error(`容器 ${containerId} 不存在`);
            }
            
            // 优先检测设备性能，提前设置低/中/高档参数（影响抗锯齿、指针交互等）
            try {
                this.perfLevel = this.detectPerformanceLevel() || 'medium';
            } catch (_) { this.perfLevel = 'medium'; }

            // 加载Globe.GL库
            const loaded = await GlobeLoader.load();
            if (!loaded) {
                throw new Error('Globe.GL库加载失败');
            }
            
            // 创建Globe实例
            this.createGlobe();
            
            // 尝试加载国家边界（不阻塞初始化）
            this.loadCountryBorders();
            
            // 设置初始数据
            if (initialData.length > 0) {
                this.updateData(initialData);
            }
            
            // 启动动画
            if (this.config.enableAnimation) {
                this.startAnimation();
                this.startArcGeneration();
            }
            
            // 添加交互事件
            this.setupInteractions();
            
            // 设置控制面板交互
            this.setupControlPanel();
            
            // 设置主题
            this.updateTheme();
            
            this.isInitialized = true;
            console.log('[RegionGlobe] 初始化成功');
            
            return true;
        } catch (error) {
            console.error('[RegionGlobe] 初始化失败:', error);
            this.showFallback();
            return false;
        }
    },
    
    /**
     * 创建Globe实例
     */
    createGlobe() {
        const theme = this.getCurrentTheme();
        const globeStyle = this.getCurrentGlobeStyle();
        const antialias = this.perfLevel !== 'low';
        
        this.globe = Globe({
                rendererConfig: {
                    powerPreference: 'high-performance',  // 优先使用高性能GPU
                    antialias,
                    alpha: true
                }
            })
            (this.container)
            .globeImageUrl(globeStyle.url)
            .backgroundColor(theme.backgroundColor || '#000411')  // 使用主题背景色
            .backgroundImageUrl(theme.backgroundImageUrl || '')  // 星空背景（仅夜间主题）
            .showAtmosphere(true)  // 显示大气层
            .atmosphereColor(theme.atmosphereColor)
            .atmosphereAltitude(theme.atmosphereAltitude)
            .pointAltitude(this.config.pointAltitude)
            .pointRadius(this.config.pointRadius)
            .pointResolution(this.config.pointResolution)
            .pointColor(theme.pointColor)
            .pointsMerge(true)  // 合并点mesh提升渲染性能
            .arcColor(theme.arcColor)
            .arcStroke(0.5)
            .arcDashLength(0.4)
            .arcDashGap(0.2)
            .arcDashAnimateTime(this.config.arcAnimationDuration)
            // 性能优化配置
            .arcCurveResolution(this.config.arcCurveResolution)  // 降低弧线分辨率
            .enablePointerInteraction(this.config.enableMouseTracking);  // 按性能档位开关交互追踪

        // 按设备性能限制像素比，避免高DPR设备GPU过载
        try {
            const renderer = this.globe.renderer();
            if (renderer && typeof renderer.setPixelRatio === 'function') {
                const maxDpr = this.perfLevel === 'high' ? 2 : (this.perfLevel === 'medium' ? 1.5 : 1.2);
                const dpr = Math.min(window.devicePixelRatio || 1, maxDpr);
                renderer.setPixelRatio(dpr);
            }
        } catch (e) {
            console.debug('[RegionGlobe] 设置像素比失败:', e);
        }
        
        // 设置节点标签（显示国旗和信息）
        try {
            // 使用HTML标签显示国旗
            this.globe
                .pointLabel(node => {
                    if (node && node.flag) {
                        // 返回HTML内容
                        return `
                            <div style="
                                text-align: center;
                                font-family: var(--font-sans);
                            ">
                                <div style="font-size: 24px; line-height: 1;">${node.flag}</div>
                                <div style="
                                    font-size: 11px;
                                    color: rgba(255, 255, 255, 0.9);
                                    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
                                    font-weight: 500;
                                    margin-top: 2px;
                                ">${node.name}</div>
                                <div style="
                                    font-size: 10px;
                                    color: rgba(147, 197, 253, 0.9);
                                    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
                                ">${node.count}节点</div>
                            </div>
                        `;
                    }
                    return '';
                })
            .pointLabelAltitude(0.008)  // 提高标签高度，确保在最上层
                .pointLabelColor(() => 'transparent')  // 标签背景透明
                .pointLabelDotRadius(0)  // 不显示点
                .pointLabelResolution(3);  // 标签分辨率
        } catch (error) {
            console.debug('[RegionGlobe] 设置节点标签失败:', error);
        }
        
        // 移除重复的pointLabel设置，避免多个tips
        
        // 设置初始视角（聚焦欧洲地区，避免过多海洋）
        this.globe.pointOfView({ lat: 48, lng: 10, altitude: 1.5 }, 100);  // 减少动画时间加快初始化
        
        // 显示经纬度网格（已禁用以提升性能）
        this.globe.showGraticules(this.config.showGraticules);
        
        // 可选高级效果
        this.applyAdvancedEffects();
        
        // 延迟创建星空背景，减少初始化负载
        setTimeout(() => {
            this.createStaticStarfield();
        }, 2000);  // 增加延迟到2秒，减少初始化卡顿
        
        // 自适应容器尺寸
        this.resizeToContainer();
        
        // 确保renderer背景透明
        this.setRendererBackground();
    },
    
    /**
     * 更新数据
     */
    updateData(regionData) {
        if (!this.globe) return;
        
        // 转换数据格式
        const points = this.convertToGlobeData(regionData);
        this.currentData = points;
        
        // 更新地球上的点
        this.globe.pointsData(points);
        
        // 如果已加载国家边界，更新国家高亮
        if (this.countriesData) {
            this.updateCountryHighlights();
        }
        
        console.log('[RegionGlobe] 数据更新:', points.length, '个节点');
    },
    
    /**
     * 转换数据格式
     */
    convertToGlobeData(regionData) {
        const maxCount = Math.max(...regionData.map(r => r.count || 0), 1);
        
        return regionData.map(region => {
            // 过滤掉UNKNOWN和特殊代码，避免在原点显示
            if (region.code === 'UNKNOWN' || region.code === 'OT' || region.code === 'LO') {
                console.debug('[RegionGlobe] 过滤特殊代码:', region.code);
                return null;
            }
            
            const coords = window.COUNTRY_COORDINATES[region.code];
            if (!coords) {
                console.warn('[RegionGlobe] 未找到国家坐标:', region.code);
                return null;
            }
            
            const intensity = region.count / maxCount;
            // 根据节点数量动态调整大小，更小的指示器
            let dynamicSize = 0.2 + (intensity * 0.4);  // 范围 0.2-0.6，比之前更小
            
            // 特殊处理小地区，增加其可见性
            if (region.code === 'SG' || region.code === 'HK' || region.code === 'TW') {
                dynamicSize = Math.max(0.4, dynamicSize * 1.3);  // 小地区但也不过大
            }
            
            return {
                lat: coords.lat,
                lng: coords.lng,
                size: dynamicSize,
                intensity: intensity,
                label: `${coords.flag || ''} ${region.name || coords.name}: ${region.count} 节点`,
                code: region.code,
                count: region.count,
                color: this.getNodeColor(intensity, region.code),
                flag: coords.flag || '',
                name: region.name || coords.name
            };
        }).filter(Boolean);
    },
    
    /**
     * 启动自动旋转
     */
    startAnimation() {
        if (this.animationId) return;
        
        const animate = () => {
            if (!this.isPaused && this.globe) {
                const controls = this.globe.controls();
                if (controls) {
                    controls.autoRotate = true;
                    controls.autoRotateSpeed = this.config.rotationSpeed * 60;
                }
            }
            this.animationId = requestAnimationFrame(animate);
        };
        
        animate();
    },
    
    /**
     * 启动数据包动画
     */
    startArcGeneration() {
        if (!this.config.enableAnimation) return;
        
        // 更快初始化，立即生成第一批弧线
        setTimeout(() => {
            this.generateMultipleArcs();
        }, this.config.arcInitialDelay);
        
        this.arcInterval = setInterval(() => {
            if (this.isPaused || !this.currentData.length || this.currentData.length < 2) return;
            
            // 基于权重选择源节点（节点数量越多，被选中的概率越大）
            const source = this.selectWeightedNode();
            if (!source) return;
            
            // 根据源节点的权重决定生成弧线数量
            const arcCount = Math.min(3, Math.ceil(source.count / 10));  // 每10个节点多生成一条弧线，最多3条
            
            for (let i = 0; i < arcCount; i++) {
                const target = this.selectNearbyNode(source);
                if (!target || source === target) continue;
                
                // 添加新弧线
                this.arcsData.push({
                    startLat: source.lat,
                    startLng: source.lng,
                    endLat: target.lat,
                    endLng: target.lng,
                    color: this.getCurrentTheme().arcColor()
                });
            }
            
            // 限制弧线数量
            while (this.arcsData.length > this.config.maxArcs) {
                this.arcsData.shift();
            }
            
            // 更新弧线数据
            this.globe.arcsData(this.arcsData);
        }, this.config.arcGenerateInterval);
    },
    
    /**
     * 基于权重选择节点
     */
    selectWeightedNode() {
        if (!this.currentData.length) return null;
        
        // 计算总权重
        const totalWeight = this.currentData.reduce((sum, node) => sum + (node.count || 1), 0);
        let random = Math.random() * totalWeight;
        
        // 根据权重选择节点
        for (const node of this.currentData) {
            random -= (node.count || 1);
            if (random <= 0) {
                return node;
            }
        }
        
        return this.currentData[0];
    },
    
    /**
     * 选择附近的节点
     */
    selectNearbyNode(source) {
        if (!this.currentData.length || this.currentData.length < 2) return null;
        
        // 计算所有节点与源节点的距离
        const distances = this.currentData
            .filter(node => node !== source)
            .map(node => ({
                node,
                distance: this.calculateDistance(source, node)
            }))
            .sort((a, b) => a.distance - b.distance);
        
        // 70%概率选择近距离节点，30%概率选择远距离节点
        if (Math.random() < 0.7 && distances.length > 3) {
            // 选择前50%的近距离节点
            const nearbyCount = Math.max(1, Math.floor(distances.length * 0.5));
            const nearbyNodes = distances.slice(0, nearbyCount);
            return nearbyNodes[Math.floor(Math.random() * nearbyNodes.length)].node;
        } else {
            // 随机选择任意节点
            return distances[Math.floor(Math.random() * distances.length)].node;
        }
    },
    
    /**
     * 计算两点之间的球面距离
     */
    calculateDistance(node1, node2) {
        const lat1 = node1.lat * Math.PI / 180;
        const lat2 = node2.lat * Math.PI / 180;
        const deltaLat = (node2.lat - node1.lat) * Math.PI / 180;
        const deltaLng = (node2.lng - node1.lng) * Math.PI / 180;
        
        const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                  Math.cos(lat1) * Math.cos(lat2) *
                  Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2);
        
        return 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    },
    
    /**
     * 批量生成多条弧线（初始化时使用）
     */
    generateMultipleArcs() {
        if (!this.currentData.length || this.currentData.length < 2) return;
        
        // 生成初始弧线，节点多的国家生成更多
        const initialArcs = Math.min(5, Math.floor(this.currentData.length / 2));
        
        for (let i = 0; i < initialArcs; i++) {
            const source = this.selectWeightedNode();
            if (!source) continue;
            
            // 根据节点权重生成多条弧线
            const arcCount = Math.min(2, Math.ceil(source.count / 15));
            
            for (let j = 0; j < arcCount; j++) {
                const target = this.selectNearbyNode(source);
                if (!target || source === target) continue;
                
                this.arcsData.push({
                    startLat: source.lat,
                    startLng: source.lng,
                    endLat: target.lat,
                    endLng: target.lng,
                    color: this.getCurrentTheme().arcColor()
                });
            }
        }
        
        // 限制弧线数量
        while (this.arcsData.length > this.config.maxArcs) {
            this.arcsData.shift();
        }
        
        // 更新弧线数据
        if (this.globe) {
            this.globe.arcsData(this.arcsData);
        }
    },
    
    /**
     * 加载国家边界
     */
    async loadCountryBorders() {
        if (!this.config.showCountryBorders || !window.GeoJSONLoader) {
            return;
        }
        
        try {
            const countries = await window.GeoJSONLoader.loadCountries();
            if (countries && this.globe) {
                this.countriesData = countries;
                
                // 添加国家边界层
                try {
                    this.globe
                        .polygonsData(countries.features)
                        .polygonCapColor((country) => {
                            // 检查该国家是否有节点，兼容不同的ISO代码格式
                            const countryCode = this.getCountryCode(country.properties);
                            const hasNodes = this.currentData.some(node => node.code === countryCode);
                            
                            // 只有点亮的区域才显示填充色
                            if (!hasNodes) {
                                return 'transparent';  // 未点亮区域完全透明
                            }
                            
                            // 点亮区域的填充色 - 小地区使用金色调
                            if (countryCode === 'SG' || countryCode === 'HK' || countryCode === 'TW') {
                                return '#ffd70066';  // 金色半透明填充（点亮的小地区）
                            }
                            return '#6366f166';  // 蓝色半透明填充（点亮的普通地区）
                        })
                        .polygonSideColor((country) => {
                            const countryCode = this.getCountryCode(country.properties);
                            const hasNodes = this.currentData.some(node => node.code === countryCode);
                            
                            if (!hasNodes) return 'transparent';  // 未点亮区域不显示侧面
                            
                            // 点亮区域的立体侧面效果（增加透明度）
                            if (countryCode === 'SG' || countryCode === 'HK' || countryCode === 'TW') {
                                return 'rgba(255, 215, 0, 0.8)';  // 金色侧面（增加到80%）
                            }
                            return 'rgba(99, 102, 241, 0.4)';  // 蓝色侧面（增加到40%）
                        })
                        .polygonStrokeColor((country) => {
                            const countryCode = this.getCountryCode(country.properties);
                            const hasNodes = this.currentData.some(node => node.code === countryCode);
                            
                            // 只有点亮的区域才显示醒目边框
                            if (!hasNodes) {
                                return '#ffffff08';  // 未点亮区域极低调的边框
                            }
                            
                            // 点亮区域的边框 - 小地区使用金色
                            if (countryCode === 'SG' || countryCode === 'HK' || countryCode === 'TW') {
                                return '#ffd700ff';  // 金色边框（点亮的小地区）
                            }
                            return '#00d4ffee';  // 亮蓝色边框（点亮的普通地区）
                        })
                        // .polygonStrokeWidth(this.config.polygonStrokeWidth)  // API不支持
                        .polygonAltitude((country) => {
                            const countryCode = this.getCountryCode(country.properties);
                            const hasNodes = this.currentData.some(node => node.code === countryCode);
                            // 有数据的国家抬高到配置高度，增加立体感
                            return hasNodes ? this.config.polygonAltitude : 0;
                        });
                    
                    // 移除polygonLabel避免重复显示tips
                    // 只使用带背景的tooltip显示国家信息
                } catch (error) {
                    console.warn('[RegionGlobe] 设置国家边界出错:', error);
                }
                
                console.log('[RegionGlobe] 国家边界已加载');
            }
        } catch (error) {
            console.warn('[RegionGlobe] 国家边界加载失败:', error);
        }
    },
    
    /**
     * 根据强度和地区代码获取节点颜色
     */
    getNodeColor(intensity, countryCode = '') {
        // 特殊处理小地区，使用醒目的橙色
        if (countryCode === 'SG' || countryCode === 'HK' || countryCode === 'TW') {
            return 'rgb(255, 107, 53)';  // 修复：移除alpha值
        }
        
        const isDark = document.documentElement.classList.contains('dark');
        
        if (isDark) {
            // 暗色主题：使用更鲜艳的颜色，修复alpha警告
            if (intensity < 0.3) {
                return 'rgb(0, 212, 255)';      // 亮青色
            } else if (intensity < 0.6) {
                return 'rgb(196, 181, 253)';    // 亮紫
            } else {
                return 'rgb(251, 113, 133)';    // 亮粉
            }
        } else {
            // 亮色主题：使用更深的颜色，修复alpha警告
            if (intensity < 0.3) {
                return 'rgb(59, 130, 246)';     // 深蓝
            } else if (intensity < 0.6) {
                return 'rgb(109, 40, 217)';     // 深紫
            } else {
                return 'rgb(190, 18, 60)';      // 深红
            }
        }
    },
    
    /**
     * 设置交互事件
     */
    setupInteractions() {
        if (!this.globe) return;
        
        // 点击节点事件
        this.globe.onPointClick(point => {
            if (point && point.code) {
                console.log('[RegionGlobe] 点击节点:', point.code, point.label);
                // 触发地区筛选
                if (window.RegionStatsModule) {
                    window.RegionStatsModule.filterByRegion(point.code);
                }
            }
        });
        
        // 悬停提示
        this.globe.onPointHover(point => {
            this.container.style.cursor = point ? 'pointer' : 'grab';
            
            // 显示或隐藏悬停提示
            if (point) {
                this.showTooltip(point);
            } else {
                this.hideTooltip();
            }
        });
        
        // 国家悬停效果 - 轻微上浮 + 显示工具提示
        if (this.globe.onPolygonHover) {
            this.globe.onPolygonHover(country => {
                // 更新所有国家的高度，悬停的国家稍微上浮
                this.globe.polygonAltitude((c) => {
                    const countryCode = this.getCountryCode(c.properties);
                    const hasNodes = this.currentData.some(node => node.code === countryCode);
                    
                    if (!hasNodes) return 0;  // 没有数据的国家不抬高
                    
                    // 悬停的国家使用更高的高度
                    if (country && this.getCountryCode(c.properties) === this.getCountryCode(country.properties)) {
                        return this.config.hoverAltitude;  // 悬停时抬高
                    }
                    
                    return this.config.polygonAltitude;  // 正常高度
                });
                
                // 显示或隐藏国家工具提示
                if (country) {
                    const countryCode = this.getCountryCode(country.properties);
                    const nodeData = this.currentData.find(node => node.code === countryCode);
                    if (nodeData) {
                        this.showTooltip(nodeData);
                    }
                } else {
                    this.hideTooltip();
                }
            });
        }
        
        // 窗口大小调整
        this.resizeHandler = () => {
            this.resizeToContainer();
        };
        window.addEventListener('resize', this.resizeHandler);
    },
    
    /**
     * 获取当前主题
     */
    getCurrentTheme() {
        const isDark = document.documentElement.classList.contains('dark');
        return this.config.themes[isDark ? 'dark' : 'light'];
    },
    
    /**
     * 获取当前地球风格 - 根据主题自动选择
     */
    getCurrentGlobeStyle() {
        const isDark = document.documentElement.classList.contains('dark');
        return this.config.globeStyles[isDark ? 'dark' : 'light'];
    },
    
    
    
    /**
     * 更新主题
     */
    updateTheme() {
        if (!this.globe) return;
        
        const theme = this.getCurrentTheme();
        
        const style = this.getCurrentGlobeStyle();
        
        this.globe
            .globeImageUrl(style.url)  // 自动更新地球纹理跟随主题
            .backgroundColor(theme.backgroundColor || '#000411')  // 使用主题背景色
            .backgroundImageUrl(theme.backgroundImageUrl || '')  // 更新星空背景
            .atmosphereColor(theme.atmosphereColor)
            .atmosphereAltitude(theme.atmosphereAltitude)
            .pointColor(theme.pointColor)
            .arcColor(theme.arcColor);
        
        // 更新主题后也设置背景
        this.setRendererBackground();
        
        console.log('[RegionGlobe] 主题和地球风格已自动更新');
    },
    
    /**
     * 暂停动画
     */
    pause() {
        this.isPaused = true;
        if (this.globe) {
            const controls = this.globe.controls();
            if (controls) {
                controls.autoRotate = false;
            }
        }
    },
    
    /**
     * 恢复动画
     */
    resume() {
        this.isPaused = false;
        if (this.globe) {
            const controls = this.globe.controls();
            if (controls) {
                controls.autoRotate = true;
            }
        }
    },
    
    /**
     * 重置视角
     */
    resetView() {
        if (this.globe) {
            // 如果有数据，聚焦到数据中心
            if (this.currentData.length > 0) {
                const avgLat = this.currentData.reduce((sum, d) => sum + d.lat, 0) / this.currentData.length;
                const avgLng = this.currentData.reduce((sum, d) => sum + d.lng, 0) / this.currentData.length;
                this.globe.pointOfView({ lat: avgLat, lng: avgLng, altitude: 2 }, 1000);
            } else {
                // 默认聚焦亚洲
                this.globe.pointOfView({ lat: 30, lng: 105, altitude: 1.5 }, 1000);
            }
        }
    },
    
    /**
     * 调整尺寸以适应容器
     */
    resizeToContainer() {
        if (this.globe && this.container) {
            // 使用容器的全部宽度，让地球尽可能大
            const width = this.container.offsetWidth;
            const height = this.container.offsetHeight || 600;  // 增加默认高度
            this.globe.width(width).height(height);
        }
    },
    
    /**
     * 获取国家代码（兼容不同的ISO格式）
     */
    getCountryCode(properties) {
        if (!properties) return null;
        
        // 优先获取ISO_A2
        let code = properties.ISO_A2 || properties.iso_a2 || properties.ISO2;
        
        // 处理-99或缺失值（如法国、挪威）
        if (!code || code === '-99' || code === '') {
            // 名称映射（处理特殊国家）
            const nameMapping = {
                'France': 'FR',
                'Norway': 'NO',
                'N. Cyprus': 'CY',
                'Somaliland': 'SO',
                'Kosovo': 'XK'
            };
            
            const name = properties.NAME || properties.ADMIN || properties.name;
            if (name && nameMapping[name]) {
                return nameMapping[name];
            }
        }
        
        // 如果还是没有，尝试ISO_A3映射
        if (!code || code === '-99') {
            // ISO代码映射表（ISO_A3 到 ISO_A2）
            const codeMapping = {
            'SGP': 'SG',  // 新加坡
            'FRA': 'FR',  // 法国
            'CHN': 'CN',  // 中国
            'USA': 'US',  // 美国
            'GBR': 'GB',  // 英国
            'DEU': 'DE',  // 德国
            'JPN': 'JP',  // 日本
            'KOR': 'KR',  // 韩国
            'IND': 'IN',  // 印度
            'AUS': 'AU',  // 澳大利亚
            'CAN': 'CA',  // 加拿大
            'BRA': 'BR',  // 巴西
            'RUS': 'RU',  // 俄罗斯
            'ITA': 'IT',  // 意大利
            'ESP': 'ES',  // 西班牙
            'NLD': 'NL',  // 荷兰
            'CHE': 'CH',  // 瑞士
            'SWE': 'SE',  // 瑞典
            'NOR': 'NO',  // 挪威
            'DNK': 'DK',  // 丹麦
            'FIN': 'FI',  // 芬兰
            'POL': 'PL',  // 波兰
            'BEL': 'BE',  // 比利时
            'AUT': 'AT',  // 奥地利
            'PRT': 'PT',  // 葡萄牙
            'GRC': 'GR',  // 希腊
            'TUR': 'TR',  // 土耳其
            'ARE': 'AE',  // 阿联酋
            'SAU': 'SA',  // 沙特
            'ISR': 'IL',  // 以色列
            'THA': 'TH',  // 泰国
            'VNM': 'VN',  // 越南
            'MYS': 'MY',  // 马来西亚
            'IDN': 'ID',  // 印度尼西亚
            'PHL': 'PH',  // 菲律宾
            'MEX': 'MX',  // 墨西哥
            'ARG': 'AR',  // 阿根廷
            'CHL': 'CL',  // 智利
            'COL': 'CO',  // 哥伦比亚
            'PER': 'PE',  // 秘鲁
            'VEN': 'VE',  // 委内瑞拉
            'ZAF': 'ZA',  // 南非
            'EGY': 'EG',  // 埃及
            'NGA': 'NG',  // 尼日利亚
            'KEN': 'KE',  // 肯尼亚
            'MAR': 'MA',  // 摩洛哥
            'NZL': 'NZ',  // 新西兰
            'IRL': 'IE',  // 爱尔兰
            'UKR': 'UA',  // 乌克兰
            'CZE': 'CZ',  // 捷克
            'ROU': 'RO',  // 罗马尼亚
            'HKG': 'HK',  // 香港
            'TWN': 'TW'   // 台湾
            };
            
            // 尝试从ISO_A3转换
            const iso3 = properties.ISO_A3 || properties.iso_a3 || properties.ISO3;
            if (iso3 && iso3 !== '-99' && codeMapping[iso3]) {
                code = codeMapping[iso3];
            }
        }
        
        // 最后检查
        if (code && code !== '-99') {
            return code.toUpperCase();
        }
        
        // 调试日志
        if (properties.NAME) {
            console.debug('[RegionGlobe] 未找到国家代码:', properties.NAME, 'ISO_A2:', properties.ISO_A2, 'ISO_A3:', properties.ISO_A3);
        }
        
        return null;
    },
    
    /**
     * 应用高级效果
     */
    applyAdvancedEffects() {
        if (!this.globe) return;
        
        // 地形凹凸效果
        if (this.config.enableBumpMap) {
            try {
                this.globe.bumpImageUrl('//unpkg.com/three-globe/example/img/earth-topology.png');
            } catch (error) {
                console.warn('[RegionGlobe] 地形效果加载失败:', error);
            }
        }
        
        // 云层效果
        if (this.config.enableClouds && this.globe.cloudsImageUrl) {
            try {
                this.globe
                    .cloudsImageUrl('//unpkg.com/three-globe/example/img/clouds.png')
                    .cloudsAltitude(0.004)
                    .cloudsRotateSpeed(-0.006);  // 云层缓慢旋转
            } catch (error) {
                console.warn('[RegionGlobe] 云层效果加载失败:', error);
            }
        }
        
        // 访问内部Three.js对象以添加更多效果
        if (this.globe.scene) {
            const scene = this.globe.scene();
            if (scene) {
                // 添加环境光
                try {
                    const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
                    scene.add(ambientLight);
                    
                    // 添加方向光
                    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
                    directionalLight.position.set(10, 10, 10);
                    scene.add(directionalLight);
                    
                    // 创建静态星空背景
                    this.createStaticBackground(scene);
                } catch (error) {
                    console.debug('[RegionGlobe] 光照效果设置失败:', error);
                }
            }
        }
        
        console.log('[RegionGlobe] 高级效果已应用');
    },
    
    /**
     * 创建静态背景
     */
    createStaticBackground(scene) {
        try {
            const isDark = document.documentElement.classList.contains('dark');
            
            if (isDark && window.THREE) {
                // 暗色模式：创建静态星空
                // 检查是否已存在星空
                const existingStars = scene.getObjectByName('staticStarfield');
                if (existingStars) {
                    scene.remove(existingStars);
                }
                
                // 创建星空粒子
                const starsGeometry = new window.THREE.BufferGeometry();
                const starVertices = [];
                
                for (let i = 0; i < 5000; i++) {
                    const x = (Math.random() - 0.5) * 2000;
                    const y = (Math.random() - 0.5) * 2000;
                    const z = (Math.random() - 0.5) * 2000;
                    starVertices.push(x, y, z);
                }
                
                starsGeometry.setAttribute('position', 
                    new window.THREE.Float32BufferAttribute(starVertices, 3));
                
                const starsMaterial = new window.THREE.PointsMaterial({
                    color: 0xffffff,
                    size: 1,
                    sizeAttenuation: false,
                    transparent: true,
                    opacity: 0.8
                });
                
                const stars = new window.THREE.Points(starsGeometry, starsMaterial);
                stars.name = 'staticStarfield';
                stars.renderOrder = -1;  // 确保在地球后面
                scene.add(stars);
                
                console.log('[RegionGlobe] 静态星空已创建');
            }
            
            // 设置场景背景色
            if (window.THREE) {
                scene.background = new window.THREE.Color(isDark ? 0x000411 : 0xe0e7ff);
            }
        } catch (error) {
            console.debug('[RegionGlobe] 创建静态背景失败:', error);
        }
    },
    
    /**
     * 设置渲染器背景
     */
    setRendererBackground() {
        try {
            if (this.globe && this.globe.renderer) {
                const renderer = this.globe.renderer();
                if (renderer) {
                    // 设置WebGL渲染器背景
                    const isDark = document.documentElement.classList.contains('dark');
                    const bgColor = isDark ? 0x000411 : 0xe0e7ff;
                    renderer.setClearColor(bgColor, 1);
                    
                    // 更新场景背景
                    if (this.globe.scene) {
                        const scene = this.globe.scene();
                        if (scene) {
                            this.createStaticBackground(scene);
                        }
                    }
                }
            }
        } catch (error) {
            console.debug('[RegionGlobe] 设置渲染器背景失败:', error);
        }
    },
    
    /**
     * 创建静态星空背景
     */
    createStaticStarfield() {
        if (!this.globe || !this.globe.scene) {
            console.warn('[RegionGlobe] Globe或scene不可用');
            return;
        }
        
        try {
            const scene = this.globe.scene();
            const renderer = this.globe.renderer();
            
            console.log('[RegionGlobe] 检查THREE.js:', !!window.THREE, !!scene, !!renderer);
            
            // 尝试多种方式获取THREE.js
            let THREE = window.THREE;
            
            // 如果THREE不存在，尝试从场景对象获取
            if (!THREE && scene.constructor) {
                // 从Three.js场景对象推断THREE命名空间
                const SceneClass = scene.constructor;
                if (SceneClass.name === 'Scene') {
                    // 尝试通过原型链找到THREE
                    THREE = SceneClass.prototype.constructor.THREE || 
                           (scene.type === 'Scene' ? scene.constructor : null);
                }
            }
            
            // 最后尝试动态加载
            if (!THREE) {
                console.log('[RegionGlobe] 尝试动态加载THREE.js');
                // Globe.GL内置了THREE.js，尝试从全局作用域找
                if (typeof requirejs !== 'undefined') {
                    THREE = requirejs('three');
                }
            }
            
            if (scene && renderer && THREE) {
                // 创建星空粒子系统
                const starsGeometry = new THREE.BufferGeometry();
                const starCount = 10000;  // 增加星星数量
                const positions = new Float32Array(starCount * 3);
                const colors = new Float32Array(starCount * 3);  // 添加颜色变化
                
                // 随机分布星星
                for (let i = 0, j = 0; i < starCount * 3; i += 3, j += 3) {
                    // 使用球形分布，距离更远
                    const radius = 500 + Math.random() * 1000;
                    const theta = Math.random() * Math.PI * 2;
                    const phi = Math.acos(2 * Math.random() - 1);
                    
                    positions[i] = radius * Math.sin(phi) * Math.cos(theta);
                    positions[i + 1] = radius * Math.sin(phi) * Math.sin(theta);
                    positions[i + 2] = radius * Math.cos(phi);
                    
                    // 随机星星颜色（白色到淡蓝色）
                    const colorIntensity = 0.5 + Math.random() * 0.5;
                    colors[j] = colorIntensity;
                    colors[j + 1] = colorIntensity;
                    colors[j + 2] = colorIntensity + Math.random() * 0.2;
                }
                
                starsGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
                starsGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
                
                // 创建星星材质
                const starsMaterial = new THREE.PointsMaterial({
                    size: 1.5,  // 增大星星尺寸
                    sizeAttenuation: false,
                    vertexColors: true,  // 使用顶点颜色
                    opacity: 0.9,
                    transparent: true,
                    blending: THREE.AdditiveBlending || 1  // 叠加混合模式
                });
                
                // 创建星空对象（不旋转）
                this.starfield = new THREE.Points(starsGeometry, starsMaterial);
                this.starfield.name = 'starfield';
                
                // 添加到场景
                scene.add(this.starfield);
                
                console.log('[RegionGlobe] 静态星空背景已创建:', this.starfield);
            } else {
                console.warn('[RegionGlobe] THREE.js不可用或场景未就绪');
            }
        } catch (error) {
            console.debug('[RegionGlobe] 星空背景创建失败:', error);
        }
    },
    
    /**
     * 显示降级方案
     */
    showFallback() {
        if (this.container) {
            this.container.innerHTML = `
                <div class="globe-fallback">
                    <div class="text-center py-8 text-slate-500 dark:text-slate-400">
                        <i class="ti ti-world text-4xl mb-2"></i>
                        <p class="text-sm">3D地球加载失败，请使用地区标签筛选</p>
                    </div>
                </div>
            `;
        }
    },
    
    /**
     * 更新国家高亮
     */
    updateCountryHighlights() {
        if (!this.globe || !this.countriesData) return;
        
        // 重新设置国家颜色
        try {
            this.globe
                .polygonCapColor((country) => {
                    const countryCode = this.getCountryCode(country.properties);
                    const nodeData = this.currentData.find(node => node.code === countryCode);
                    
                    // 只有点亮的区域才显示填充色
                    if (!nodeData) {
                        return 'transparent';  // 未点亮区域完全透明
                    }
                    
                    // 点亮区域的填充色 - 小地区使用金色调，根据强度调整
                    if (countryCode === 'SG' || countryCode === 'HK' || countryCode === 'TW') {
                        const intensity = Math.min(150, 80 + Math.floor(nodeData.intensity * 70));
                        const hex = intensity.toString(16).padStart(2, '0');
                        return `#ffd700${hex}`;  // 金色调，根据强度变化
                    }
                    
                    // 普通国家蓝色填充
                    const opacity = Math.min(120, 60 + Math.floor(nodeData.intensity * 60));
                    const hex = opacity.toString(16).padStart(2, '0');
                    return `#6366f1${hex}`;
                })
                .polygonStrokeColor((country) => {
                    const countryCode = this.getCountryCode(country.properties);
                    const hasNodes = this.currentData.some(node => node.code === countryCode);
                    
                    // 只有点亮的区域才显示醒目边框
                    if (!hasNodes) {
                        return '#ffffff08';  // 未点亮区域极低调的边框
                    }
                    
                    // 点亮区域的边框 - 小地区使用金色
                    if (countryCode === 'SG' || countryCode === 'HK' || countryCode === 'TW') {
                        return '#ffd700ff';  // 金色边框（点亮的小地区）
                    }
                    return '#00d4ffee';  // 亮蓝色边框（点亮的普通地区）
                })
                .polygonSideColor((country) => {
                    const countryCode = this.getCountryCode(country.properties);
                    const hasNodes = this.currentData.some(node => node.code === countryCode);
                    
                    if (!hasNodes) return 'transparent';  // 未点亮区域不显示侧面
                    
                    // 点亮区域的立体侧面效果（增加透明度）
                    if (countryCode === 'SG' || countryCode === 'HK' || countryCode === 'TW') {
                        return 'rgba(255, 215, 0, 0.8)';  // 金色侧面（增加到80%）
                    }
                    return 'rgba(99, 102, 241, 0.4)';  // 蓝色侧面（增加到40%）
                })
                // .polygonStrokeWidth(this.config.polygonStrokeWidth)  // API不支持
                .polygonAltitude((country) => {
                    const countryCode = this.getCountryCode(country.properties);
                    const hasNodes = this.currentData.some(node => node.code === countryCode);
                    // 有数据的国家抬高到配置高度，保持立体感
                    return hasNodes ? this.config.polygonAltitude : 0;
                });
        } catch (error) {
            console.warn('[RegionGlobe] 更新国家高亮出错:', error);
        }
        
        // 触发成就感动画（暂时禁用，避免整体跳动）
        // this.triggerAchievementAnimation();
    },
    
    /**
     * 触发成就感动画
     */
    triggerAchievementAnimation() {
        // 改为只添加光晕效果，不缩放整个容器
        if (this.container) {
            // 添加光晕效果而不是缩放
            this.container.style.boxShadow = '0 0 30px rgba(99, 102, 241, 0.5), 0 0 60px rgba(139, 92, 246, 0.3)';
            setTimeout(() => {
                this.container.style.boxShadow = '';
            }, 1500);
        }
    },
    
    /**
     * 显示悬停提示
     */
    showTooltip(point) {
        // 创建或更新提示框
        let tooltip = document.getElementById('globe-tooltip');
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.id = 'globe-tooltip';
            tooltip.className = 'globe-tooltip';
            document.body.appendChild(tooltip);
        }
        
        // 动态应用主题样式
        this.updateTooltipTheme(tooltip);
        
        // 设置内容
        tooltip.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span style="font-size: 20px;">${point.flag || '🌐'}</span>
                <div>
                    <div style="font-weight: bold;">${point.name || '未知'}</div>
                    <div style="font-size: 12px; opacity: 0.9;">${point.count} 个节点</div>
                </div>
            </div>
        `;
        
        // 更新位置（跟随鼠标）
        const updatePosition = (e) => {
            tooltip.style.left = (e.pageX + 15) + 'px';
            tooltip.style.top = (e.pageY - 30) + 'px';
        };
        
        // 监听鼠标移动
        document.addEventListener('mousemove', updatePosition);
        tooltip._updatePosition = updatePosition;
        
        // 显示提示框
        tooltip.style.display = 'block';
    },
    
    /**
     * 更新工具提示主题样式
     */
    updateTooltipTheme(tooltip) {
        const isDark = document.documentElement.classList.contains('dark');
        
        if (isDark) {
            // 暗色主题
            tooltip.style.cssText = `
                position: absolute;
                padding: 8px 12px;
                background: rgba(0, 0, 0, 0.85);
                color: white;
                border: 1px solid rgba(99, 102, 241, 0.3);
                border-radius: 6px;
                font-size: 14px;
                pointer-events: none;
                z-index: 1000;
                white-space: nowrap;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
                backdrop-filter: blur(8px);
            `;
        } else {
            // 亮色主题
            tooltip.style.cssText = `
                position: absolute;
                padding: 8px 12px;
                background: rgba(255, 255, 255, 0.95);
                color: #1e293b;
                border: 1px solid rgba(99, 102, 241, 0.2);
                border-radius: 6px;
                font-size: 14px;
                pointer-events: none;
                z-index: 1000;
                white-space: nowrap;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                backdrop-filter: blur(8px);
            `;
        }
    },
    
    /**
     * 隐藏悬停提示
     */
    hideTooltip() {
        const tooltip = document.getElementById('globe-tooltip');
        if (tooltip) {
            tooltip.style.display = 'none';
            // 移除鼠标移动监听
            if (tooltip._updatePosition) {
                document.removeEventListener('mousemove', tooltip._updatePosition);
                tooltip._updatePosition = null;
            }
        }
    },
    
    /**
     * 设置控制面板交互
     */
    setupControlPanel() {
        const controls = document.querySelector('.globe-controls');
        if (!controls) return;
        
        // 检测是否为移动设备
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // 移动端：使用触摸手势
            this.setupMobileGestures(controls);
        } else {
            // 桌面端：使用折叠按钮
            this.setupDesktopControls(controls);
        }
        
        // 检测设备性能并自动调整配置
        this.detectPerformanceLevel();
    },
    
    /**
     * 设置桌面端控制
     */
    setupDesktopControls(controls) {
        // 添加默认收起状态
        controls.classList.add('collapsed');
        
        // 创建折叠按钮（如果不存在）
        let toggleBtn = controls.querySelector('.globe-toggle-btn');
        if (!toggleBtn) {
            toggleBtn = document.createElement('button');
            toggleBtn.className = 'globe-toggle-btn';
            toggleBtn.innerHTML = '<i class="ti ti-settings-2"></i>';
            toggleBtn.title = '展开控制面板';
            controls.insertBefore(toggleBtn, controls.firstChild);
        }
        
        // 创建控制项容器（如果不存在）
        let controlItems = controls.querySelector('.globe-control-items');
        if (!controlItems) {
            controlItems = document.createElement('div');
            controlItems.className = 'globe-control-items';
            
            // 移动现有按钮到容器中（排除风格选择器）
            const buttons = Array.from(controls.querySelectorAll('button:not(.globe-toggle-btn)'));
            buttons.forEach(btn => controlItems.appendChild(btn));
            
            controls.appendChild(controlItems);
        }
        
        // 切换展开/收起
        toggleBtn.onclick = () => {
            const isCollapsed = controls.classList.toggle('collapsed');
            toggleBtn.innerHTML = isCollapsed ? 
                '<i class="ti ti-settings-2"></i>' : 
                '<i class="ti ti-x"></i>';
            toggleBtn.title = isCollapsed ? '展开控制面板' : '收起控制面板';
        };
    },
    
    /**
     * 设置移动端手势
     */
    setupMobileGestures(controls) {
        // 移动端使用与桌面端相同的折叠按钮逻辑
        // 添加默认收起状态
        controls.classList.add('collapsed');
        
        // 创建或获取折叠按钮
        let toggleBtn = controls.querySelector('.globe-toggle-btn');
        if (!toggleBtn) {
            toggleBtn = document.createElement('button');
            toggleBtn.className = 'globe-toggle-btn';
            toggleBtn.innerHTML = '<i class="ti ti-settings-2"></i>';
            toggleBtn.title = '展开控制面板';
            controls.insertBefore(toggleBtn, controls.firstChild);
        }
        
        // 确保控制项容器存在
        let controlItems = controls.querySelector('.globe-control-items');
        if (!controlItems) {
            controlItems = document.createElement('div');
            controlItems.className = 'globe-control-items';
            
            const buttons = Array.from(controls.querySelectorAll('button:not(.globe-toggle-btn)'));
            buttons.forEach(btn => controlItems.appendChild(btn));
            
            controls.appendChild(controlItems);
        }
        
        // 简单的点击切换
        toggleBtn.onclick = () => {
            const isCollapsed = controls.classList.toggle('collapsed');
            toggleBtn.innerHTML = isCollapsed ? 
                '<i class="ti ti-settings-2"></i>' : 
                '<i class="ti ti-x"></i>';
            toggleBtn.title = isCollapsed ? '展开控制面板' : '收起控制面板';
        };
        
        // 点击控制面板外部自动收起（可选）
        document.addEventListener('click', (e) => {
            if (!controls.contains(e.target) && !controls.classList.contains('collapsed')) {
                controls.classList.add('collapsed');
                toggleBtn.innerHTML = '<i class="ti ti-settings-2"></i>';
                toggleBtn.title = '展开控制面板';
            }
        });
    },
    
    /**
     * 检测设备性能并自动调整配置
     */
    detectPerformanceLevel() {
        const isMobile = /Android|iPhone|iPad|iPod/i.test(navigator.userAgent);
        const deviceMemory = navigator.deviceMemory || 4; // 默认4GB
        const hardwareConcurrency = navigator.hardwareConcurrency || 4; // CPU核心数
        
        // 根据设备性能调整配置
        if (isMobile || deviceMemory < 4 || hardwareConcurrency < 4) {
            // 低性能模式
            this.config.pointResolution = 3;
            this.config.arcCurveResolution = 24;
            this.config.maxArcs = 5;
            this.config.enableAnimation = false;
            this.config.enableMouseTracking = false;
            this.config.showGraticules = false;
            
            console.log('[RegionGlobe] 启用低性能模式');
            return 'low';
        } else if (deviceMemory >= 8 && hardwareConcurrency >= 8) {
            // 高性能模式
            this.config.pointResolution = 6;
            this.config.arcCurveResolution = 48;
            this.config.maxArcs = 12;
            this.config.enableAnimation = true;
            
            console.log('[RegionGlobe] 启用高性能模式');
            return 'high';
        } else {
            // 标准模式
            console.log('[RegionGlobe] 使用标准性能模式');
            return 'medium';
        }
    },
    
    /**
     * 销毁组件
     */
    destroy() {
        // 停止动画
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
        
        // 清除定时器
        if (this.arcInterval) {
            clearInterval(this.arcInterval);
            this.arcInterval = null;
        }
        
        // 移除事件监听
        if (this.resizeHandler) {
            window.removeEventListener('resize', this.resizeHandler);
            this.resizeHandler = null;
        }
        
        // 移除星空背景
        if (this.starfield && this.globe && this.globe.scene) {
            try {
                const scene = this.globe.scene();
                scene.remove(this.starfield);
                this.starfield.geometry.dispose();
                this.starfield.material.dispose();
                this.starfield = null;
            } catch (error) {
                console.debug('[RegionGlobe] 清理星空失败:', error);
            }
        }
        
        // 销毁Globe实例
        if (this.globe) {
            this.globe._destructor();
            this.globe = null;
        }
        
        // 清空容器
        if (this.container) {
            this.container.innerHTML = '';
        }
        
        this.isInitialized = false;
        this.currentData = [];
        this.arcsData = [];
        
        console.log('[RegionGlobe] 组件已销毁');
    }
};
