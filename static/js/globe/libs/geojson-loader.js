/**
 * GeoJSON 数据加载器
 * 用于加载和缓存国家边界数据
 */
window.GeoJSONLoader = {
    // 缓存的GeoJSON数据
    countriesData: null,
    loadStatus: 'pending',
    loadCallbacks: [],
    
    // 本地优先，远程备用 - 50m分辨率数据
    sources: [
        '/globe/geojson/countries-50m.json',  // 本地资源优先
        'https://raw.githubusercontent.com/martynafford/natural-earth-geojson/master/50m/cultural/ne_50m_admin_0_countries.json',
        'https://cdn.jsdelivr.net/gh/martynafford/natural-earth-geojson@master/50m/cultural/ne_50m_admin_0_countries.json'
    ],
    
    /**
     * 加载国家边界数据
     */
    async loadCountries() {
        if (this.loadStatus === 'loaded' && this.countriesData) {
            return this.countriesData;
        }
        
        if (this.loadStatus === 'loading') {
            return new Promise(resolve => {
                this.loadCallbacks.push(resolve);
            });
        }
        
        this.loadStatus = 'loading';
        // 加载 GeoJSON 边界数据
        
        for (const url of this.sources) {
            try {
                const response = await fetch(url);
                if (response.ok) {
                    const data = await response.json();
                    this.countriesData = data;
                    this.loadStatus = 'loaded';
                    // GeoJSON 数据加载成功
                    
                    // 通知所有等待的回调
                    this.loadCallbacks.forEach(cb => cb(data));
                    this.loadCallbacks = [];
                    
                    return data;
                }
            } catch (error) {
                console.warn('[GeoJSONLoader] 加载失败:', url.includes('/assets/') ? '本地资源' : 'CDN备用', error.message);
            }
        }
        
        // 所有源都失败，返回简化的边界数据
        this.loadStatus = 'failed';
        console.warn('[GeoJSONLoader] 使用备用边界数据');
        
        // 简化的边界数据（仅包含主要国家的矩形边界）
        const simpleBorders = this.getSimpleBorders();
        this.loadCallbacks.forEach(cb => cb(simpleBorders));
        this.loadCallbacks = [];
        
        return simpleBorders;
    },
    
    /**
     * 获取简化的边界数据（后备方案）
     */
    getSimpleBorders() {
        // 返回主要国家的简化矩形边界（包含小地区）
        return {
            type: 'FeatureCollection',
            features: [
                this.createCountryFeature('CN', [[73, 18], [135, 18], [135, 54], [73, 54], [73, 18]]),
                this.createCountryFeature('US', [[-125, 25], [-66, 25], [-66, 49], [-125, 49], [-125, 25]]),
                this.createCountryFeature('JP', [[123, 24], [146, 24], [146, 46], [123, 46], [123, 24]]),
                this.createCountryFeature('KR', [[125, 33], [130, 33], [130, 39], [125, 39], [125, 33]]),
                this.createCountryFeature('GB', [[-8, 50], [2, 50], [2, 60], [-8, 60], [-8, 50]]),
                this.createCountryFeature('DE', [[6, 47], [15, 47], [15, 55], [6, 55], [6, 47]]),
                this.createCountryFeature('FR', [[-5, 42], [8, 42], [8, 51], [-5, 51], [-5, 42]]),
                this.createCountryFeature('AU', [[113, -39], [154, -39], [154, -10], [113, -10], [113, -39]]),
                this.createCountryFeature('CA', [[-141, 42], [-52, 42], [-52, 83], [-141, 83], [-141, 42]]),
                this.createCountryFeature('RU', [[27, 42], [180, 42], [180, 82], [27, 82], [27, 42]]),
                this.createCountryFeature('BR', [[-74, -34], [-34, -34], [-34, 5], [-74, 5], [-74, -34]]),
                // 添加小地区
                this.createCountryFeature('SG', [[103.6, 1.2], [104, 1.2], [104, 1.5], [103.6, 1.5], [103.6, 1.2]]),
                this.createCountryFeature('HK', [[113.8, 22.1], [114.4, 22.1], [114.4, 22.6], [113.8, 22.6], [113.8, 22.1]]),
                this.createCountryFeature('TW', [[120, 22], [122, 22], [122, 25.5], [120, 25.5], [120, 22]])
            ]
        };
    },
    
    /**
     * 创建国家Feature
     */
    createCountryFeature(code, coordinates) {
        return {
            type: 'Feature',
            properties: {
                ISO_A2: code,
                NAME: window.COUNTRY_COORDINATES[code]?.name || code
            },
            geometry: {
                type: 'Polygon',
                coordinates: [coordinates]
            }
        };
    }
};