/**
 * THREE.js 兼容性补丁
 * 修复 Globe.GL 使用旧版 THREE.js API 的警告
 */

(function() {
    'use strict';
    
    // 保存原始的 console.warn
    const originalWarn = console.warn;
    let warningsSuppressed = 0;
    
    // 创建一个更智能的警告过滤器
    console.warn = function(...args) {
        const message = args[0];
        
        // 检查是否是我们要过滤的警告
        if (message && typeof message === 'string') {
            // THREE.js 相关的弃用警告
            const suppressPatterns = [
                'mergeBufferGeometries() has been renamed to mergeGeometries',
                'THREE.BufferGeometryUtils:',
                'THREE.WebGLRenderer:',
                'THREE.Material:'
            ];
            
            for (const pattern of suppressPatterns) {
                if (message.includes(pattern)) {
                    warningsSuppressed++;
                    return; // 静默忽略
                }
            }
        }
        
        // 其他警告正常输出
        originalWarn.apply(console, args);
    };
    
    // 定期报告被抑制的警告数量（仅在开发模式）
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        setInterval(() => {
            if (warningsSuppressed > 0) {
                console.log(`[Globe补丁] 已静默处理 ${warningsSuppressed} 个THREE.js弃用警告`);
                warningsSuppressed = 0;
            }
        }, 30000); // 每30秒报告一次
    }
    
    // 尝试修补THREE.js API
    const patchTHREE = () => {
        if (typeof THREE !== 'undefined') {
            // 检查 BufferGeometryUtils
            if (THREE.BufferGeometryUtils) {
                // 如果新方法存在但旧方法不存在，创建别名
                if (THREE.BufferGeometryUtils.mergeGeometries && !THREE.BufferGeometryUtils.mergeBufferGeometries) {
                    THREE.BufferGeometryUtils.mergeBufferGeometries = THREE.BufferGeometryUtils.mergeGeometries;
                }
                // 反向兼容：如果只有旧方法存在
                else if (THREE.BufferGeometryUtils.mergeBufferGeometries && !THREE.BufferGeometryUtils.mergeGeometries) {
                    THREE.BufferGeometryUtils.mergeGeometries = THREE.BufferGeometryUtils.mergeBufferGeometries;
                }
            }
            
            console.log('[Globe补丁] THREE.js兼容性补丁已成功应用');
            return true;
        }
        return false;
    };
    
    // 多次尝试应用补丁（因为THREE可能异步加载）
    let attempts = 0;
    const maxAttempts = 10;
    
    const tryPatch = () => {
        if (!patchTHREE() && attempts < maxAttempts) {
            attempts++;
            setTimeout(tryPatch, 500); // 每500ms重试一次
        }
    };
    
    // 开始尝试
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', tryPatch);
    } else {
        tryPatch();
    }
})();