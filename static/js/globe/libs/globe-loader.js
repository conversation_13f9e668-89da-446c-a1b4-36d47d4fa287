/**
 * Globe.GL 库加载器
 * 提供CDN加载和失败回退机制
 */
window.GlobeLoader = {
    // 本地优先，CDN备用
    cdnSources: [
        '/globe/globe.gl.min.js',  // 本地资源优先
        'https://unpkg.com/globe.gl@2.27.2/dist/globe.gl.min.js',
        'https://cdn.jsdelivr.net/npm/globe.gl@2.27.2/dist/globe.gl.min.js'
    ],
    
    // 加载状态
    loadStatus: 'pending', // pending | loading | loaded | failed
    loadCallbacks: [],
    
    /**
     * 加载Globe.GL库
     */
    async load() {
        if (this.loadStatus === 'loaded') {
            return true;
        }
        
        if (this.loadStatus === 'loading') {
            return new Promise(resolve => {
                this.loadCallbacks.push(resolve);
            });
        }
        
        this.loadStatus = 'loading';
        // 开始加载 Globe.GL 库
        
        // 尝试从CDN加载
        for (const cdn of this.cdnSources) {
            if (await this.loadFromCDN(cdn)) {
                this.loadStatus = 'loaded';
                // Globe.GL 加载成功
                this.loadCallbacks.forEach(cb => cb(true));
                this.loadCallbacks = [];
                return true;
            }
        }
        
        // 所有CDN都失败
        this.loadStatus = 'failed';
        console.error('[GlobeLoader] 所有加载源均失败');
        this.loadCallbacks.forEach(cb => cb(false));
        this.loadCallbacks = [];
        return false;
    },
    
    /**
     * 从指定CDN加载
     */
    loadFromCDN(url) {
        return new Promise(resolve => {
            const script = document.createElement('script');
            script.src = url;
            script.async = true;
            
            const timeout = setTimeout(() => {
                script.remove();
                console.warn('[GlobeLoader] CDN超时:', url);
                resolve(false);
            }, 10000); // 10秒超时
            
            script.onload = () => {
                clearTimeout(timeout);
                // 检查Globe是否正确加载
                if (typeof Globe !== 'undefined') {
                    resolve(true);
                } else {
                    script.remove();
                    console.warn('[GlobeLoader] Globe对象未定义:', url);
                    resolve(false);
                }
            };
            
            script.onerror = () => {
                clearTimeout(timeout);
                script.remove();
                console.warn('[GlobeLoader] CDN加载错误:', url);
                resolve(false);
            };
            
            document.head.appendChild(script);
        });
    },
    
    /**
     * 检查是否已加载
     */
    isLoaded() {
        return this.loadStatus === 'loaded' && typeof Globe !== 'undefined';
    }
};