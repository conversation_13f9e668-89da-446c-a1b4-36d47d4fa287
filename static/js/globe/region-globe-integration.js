/**
 * Region Globe 集成脚本
 * 处理3D地球与现有系统的数据同步
 */

// 防抖处理器
let updateDebounceTimer = null;
let lastUpdateHash = '';

// 生成数据哈希用于去重
function generateDataHash(data) {
    return data.map(d => `${d.code}:${d.count}`).join('|');
}

// 统一的更新函数
function updateGlobeData() {
    if (!window.RegionGlobe || !window.RegionGlobe.isInitialized) return;
    
    try {
        let regionData = [];
        if (window.RegionStatsModule && window.RegionStatsModule.regionData) {
            regionData = Array.from(window.RegionStatsModule.regionData.values())
                .filter(r => r.count > 0)
                .sort((a, b) => b.count - a.count);
        }
        
        if (regionData.length > 0) {
            // 检查数据是否真的变化了
            const currentHash = generateDataHash(regionData);
            if (currentHash !== lastUpdateHash) {
                lastUpdateHash = currentHash;
                window.RegionGlobe.updateData(regionData);
            }
        }
    } catch (error) {
        console.error('[Globe Integration] 更新数据失败:', error);
    }
}

// 监听地区统计更新事件（使用防抖）
document.addEventListener('regionStatsUpdated', (event) => {
    if (updateDebounceTimer) {
        clearTimeout(updateDebounceTimer);
    }
    updateDebounceTimer = setTimeout(updateGlobeData, 1000); // 1秒防抖
});

// 监听统计数据同步完成事件（使用防抖）
document.addEventListener('statsSyncComplete', () => {
    if (updateDebounceTimer) {
        clearTimeout(updateDebounceTimer);
    }
    updateDebounceTimer = setTimeout(updateGlobeData, 2000); // 2秒防抖
});

// 监听页面可见性变化
document.addEventListener('visibilitychange', () => {
    if (window.RegionGlobe && window.RegionGlobe.isInitialized) {
        if (document.hidden) {
            // 页面隐藏时暂停动画
            window.RegionGlobe.pause();
        } else {
            // 页面显示时恢复动画
            const wrapper = document.getElementById('region-stats-wrapper');
            if (wrapper && !wrapper.classList.contains('collapsed')) {
                window.RegionGlobe.resume();
            }
        }
    }
});

// 监听主题变化（统一处理）
let themeUpdateTimer = null;
const updateGlobeTheme = () => {
    if (themeUpdateTimer) clearTimeout(themeUpdateTimer);
    themeUpdateTimer = setTimeout(() => {
        if (window.RegionGlobe?.isInitialized) {
            window.RegionGlobe.updateTheme();
        }
    }, 100);
};

// 事件方式监听
document.addEventListener('themeChanged', updateGlobeTheme);

// DOM属性变化监听（备用）
new MutationObserver((mutations) => {
    if (mutations.some(m => m.type === 'attributes' && m.attributeName === 'class' && m.target === document.documentElement)) {
        updateGlobeTheme();
    }
}).observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });

// 简化的内存监控（仅生产环境）
if (window.performance?.memory && window.location.hostname !== 'localhost') {
    setInterval(() => {
        if (window.RegionGlobe?.isInitialized && window.performance.memory.jsHeapSizeLimit) {
            const usage = (window.performance.memory.usedJSHeapSize / window.performance.memory.jsHeapSizeLimit) * 100;
            if (usage > 85 && window.RegionGlobe.config?.enableAnimation) {
                window.RegionGlobe.config.enableAnimation = false;
                window.RegionGlobe.pause();
            }
        }
    }, 60000); // 1分钟检查一次
}