/**
 * 国家/地区坐标数据
 * 用于3D地球定位
 */
window.COUNTRY_COORDINATES = {
    // 亚洲
    'CN': { lat: 35.8617, lng: 104.1954, name: '中国', flag: '🇨🇳' },
    'JP': { lat: 36.2048, lng: 138.2529, name: '日本', flag: '🇯🇵' },
    'KR': { lat: 35.9078, lng: 127.7669, name: '韩国', flag: '🇰🇷' },
    'SG': { lat: 1.3521, lng: 103.8198, name: '新加坡', flag: '🇸🇬' },
    'HK': { lat: 22.3193, lng: 114.1694, name: '香港', flag: '🇭🇰' },
    'TW': { lat: 23.6978, lng: 120.9605, name: '台湾', flag: '🇹🇼' },
    'IN': { lat: 20.5937, lng: 78.9629, name: '印度' },
    'TH': { lat: 15.8700, lng: 100.9925, name: '泰国' },
    'VN': { lat: 14.0583, lng: 108.2772, name: '越南' },
    'MY': { lat: 4.2105, lng: 101.9758, name: '马来西亚' },
    'ID': { lat: -0.7893, lng: 113.9213, name: '印度尼西亚' },
    'PH': { lat: 12.8797, lng: 121.7740, name: '菲律宾' },
    'AE': { lat: 23.4241, lng: 53.8478, name: '阿联酋' },
    'SA': { lat: 23.8859, lng: 45.0792, name: '沙特阿拉伯' },
    'IL': { lat: 31.0461, lng: 34.8516, name: '以色列' },
    'TR': { lat: 38.9637, lng: 35.2433, name: '土耳其' },
    
    // 欧洲
    'GB': { lat: 55.3781, lng: -3.4360, name: '英国' },
    'UK': { lat: 55.3781, lng: -3.4360, name: '英国' }, // 兼容UK代码
    'DE': { lat: 51.1657, lng: 10.4515, name: '德国' },
    'FR': { lat: 46.2276, lng: 2.2137, name: '法国' },
    'IT': { lat: 41.8719, lng: 12.5674, name: '意大利' },
    'ES': { lat: 40.4637, lng: -3.7492, name: '西班牙', flag: '🇪🇸' },
    'NL': { lat: 52.1326, lng: 5.2913, name: '荷兰', flag: '🇳🇱' },
    'BE': { lat: 50.5039, lng: 4.4699, name: '比利时', flag: '🇧🇪' },
    'CH': { lat: 46.8182, lng: 8.2275, name: '瑞士', flag: '🇨🇭' },
    'SE': { lat: 60.1282, lng: 18.6435, name: '瑞典', flag: '🇸🇪' },
    'NO': { lat: 60.4720, lng: 8.4689, name: '挪威', flag: '🇳🇴' },
    'FI': { lat: 61.9241, lng: 25.7482, name: '芬兰', flag: '🇫🇮' },
    'DK': { lat: 56.2639, lng: 9.5018, name: '丹麦', flag: '🇩🇰' },
    'PL': { lat: 51.9194, lng: 19.1451, name: '波兰', flag: '🇵🇱' },
    'CZ': { lat: 49.8175, lng: 15.4730, name: '捷克', flag: '🇨🇿' },
    'AT': { lat: 47.5162, lng: 14.5501, name: '奥地利', flag: '🇦🇹' },
    'PT': { lat: 39.3999, lng: -8.2245, name: '葡萄牙', flag: '🇵🇹' },
    'GR': { lat: 39.0742, lng: 21.8243, name: '希腊', flag: '🇬🇷' },
    'IE': { lat: 53.4129, lng: -8.2439, name: '爱尔兰', flag: '🇮🇪' },
    'RU': { lat: 61.5240, lng: 105.3188, name: '俄罗斯', flag: '🇷🇺' },
    'UA': { lat: 48.3794, lng: 31.1656, name: '乌克兰', flag: '🇺🇦' },
    
    // 美洲
    'US': { lat: 37.0902, lng: -95.7129, name: '美国', flag: '🇺🇸' },
    'CA': { lat: 56.1304, lng: -106.3468, name: '加拿大', flag: '🇨🇦' },
    'MX': { lat: 23.6345, lng: -102.5528, name: '墨西哥', flag: '🇲🇽' },
    'BR': { lat: -14.2350, lng: -51.9253, name: '巴西', flag: '🇧🇷' },
    'AR': { lat: -38.4161, lng: -63.6167, name: '阿根廷', flag: '🇦🇷' },
    'CL': { lat: -35.6751, lng: -71.5430, name: '智利', flag: '🇨🇱' },
    'CO': { lat: 4.5709, lng: -74.2973, name: '哥伦比亚', flag: '🇨🇴' },
    'PE': { lat: -9.1900, lng: -75.0152, name: '秘鲁', flag: '🇵🇪' },
    'VE': { lat: 6.4238, lng: -66.5897, name: '委内瑞拉', flag: '🇻🇪' },
    
    // 大洋洲
    'AU': { lat: -25.2744, lng: 133.7751, name: '澳大利亚', flag: '🇦🇺' },
    'NZ': { lat: -40.9006, lng: 174.8860, name: '新西兰', flag: '🇳🇿' },
    
    // 非洲
    'ZA': { lat: -30.5595, lng: 22.9375, name: '南非', flag: '🇿🇦' },
    'EG': { lat: 26.8206, lng: 30.8025, name: '埃及', flag: '🇪🇬' },
    'NG': { lat: 9.0820, lng: 8.6753, name: '尼日利亚', flag: '🇳🇬' },
    'KE': { lat: -0.0236, lng: 37.9062, name: '肯尼亚', flag: '🇰🇪' },
    'MA': { lat: 31.7917, lng: -7.0926, name: '摩洛哥', flag: '🇲🇦' },
    
    // 特殊地区
    'LO': { lat: 0, lng: 0, name: '本地网络', flag: '🏠' },  // 本地网络使用原点
    'OT': { lat: 0, lng: 0, name: '其他地区', flag: '🌍' },  // 其他地区使用原点
    'UNKNOWN': { lat: 0, lng: 0, name: '未知地区', flag: '🌐' }  // 未知地区
};

// 提供反向查找功能
window.getCountryByCode = function(code) {
    return window.COUNTRY_COORDINATES[code] || window.COUNTRY_COORDINATES['UNKNOWN'];
};

// 获取所有国家代码
window.getAllCountryCodes = function() {
    return Object.keys(window.COUNTRY_COORDINATES);
};