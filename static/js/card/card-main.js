/**
 * 服务器卡片主入口文件
 * 整合所有卡片相关的功能模块
 */

// 导入所有模块（如果支持ES6模块）
// import GroupCounter from './group-counter.js';
// import TrafficManager from './traffic-manager.js';
// import QuickTagEditor from './quick-tag-editor.js';

// 兼容直接引入方式的模块初始化
document.addEventListener('DOMContentLoaded', function() {
    try {
        // 检查模块是否已加载
        if (typeof GroupCounter !== 'undefined') {
            window.groupCounter = new GroupCounter();
        } else {
            console.warn('❌ GroupCounter 模块未找到');
        }
        
        if (typeof TrafficManager !== 'undefined') {
            window.trafficManager = new TrafficManager();
        } else {
            console.warn('❌ TrafficManager 模块未找到');
        }
        
        if (typeof QuickTagEditor !== 'undefined') {
            window.quickTagEditor = new QuickTagEditor();
        } else {
            console.warn('❌ QuickTagEditor 模块未找到');
        }
        
        if (typeof NetworkCharts !== 'undefined') {
            window.networkCharts = new NetworkCharts();
        } else {
            console.warn('❌ NetworkCharts 模块未找到');
        }
        
        if (typeof SystemIconManager !== 'undefined') {
            window.systemIconManager = new SystemIconManager();
        } else {
            console.warn('❌ SystemIconManager 模块未找到');
        }
        
    } catch (error) {
        console.error('❌ 卡片模块初始化失败:', error);
    }
});

// 导出模块（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        GroupCounter: typeof GroupCounter !== 'undefined' ? GroupCounter : null,
        TrafficManager: typeof TrafficManager !== 'undefined' ? TrafficManager : null,
        QuickTagEditor: typeof QuickTagEditor !== 'undefined' ? QuickTagEditor : null
    };
}