/**
 * Tag UI utilities - shared rendering for card/edit/add pages
 * Minimal, framework-free helpers.
 */
(function(global){
  function normalizeIcon(icon){
    if(!icon || typeof icon !== 'string') return 'tag';
    return icon.toLowerCase().replace(/^ti\s*-/, '').replace(/_/g,'-') || 'tag';
  }
  function normalizeColor(color){
    return /^#[0-9A-Fa-f]{6}$/.test(color || '') ? color : '#6b7280';
  }
  function renderTagChip(tag, options){
    const name = (tag && tag.name) ? String(tag.name) : '';
    const icon = normalizeIcon(tag && tag.icon);
    const color = normalizeColor(tag && tag.color);
    const deletable = options && options.deletable;
    const index = options && typeof options.index === 'number' ? options.index : null;
    const deleteHtml = deletable && index !== null
      ? `<button type="button" data-tag-index="${index}" class="ml-1 text-slate-400 hover:text-red-500" title="删除"><i class="ti ti-x text-[10px]"></i></button>`
      : '';
    return `
      <span class="tag-item inline-flex items-center gap-0.5 px-1.5 py-0.5 text-[11px] font-medium rounded whitespace-nowrap flex-shrink-0"
            style="background-color:${color}20;border:1px solid ${color}40;color:${color};">
        <i class="ti ti-${icon} text-[10px]"></i>
        <span>${name}</span>
        ${deleteHtml}
      </span>`;
  }
  function renderTagList(container, tags, opts){
    if(!container) return;
    const deletable = opts && !!opts.deletable;
    const onDelete = opts && typeof opts.onDelete === 'function' ? opts.onDelete : null;
    const html = (Array.isArray(tags) ? tags : []).map((t, i)=>renderTagChip(t, {deletable, index:i})).join('');
    container.classList.add('flex','flex-wrap','gap-2');
    container.innerHTML = html || `<span class="text-xs text-slate-400">暂无标签</span>`;
    if (deletable && onDelete){
      container.querySelectorAll('button[data-tag-index]').forEach(btn=>{
        btn.addEventListener('click', (e)=>{
          e.stopPropagation();
          const idx = parseInt(btn.getAttribute('data-tag-index'),10);
          onDelete(idx);
        });
      });
    }
  }
  function bindEditorTrigger(container, sid){
    if (!container || !sid) return;
    container.addEventListener('click', function(e){
      if (typeof global.openQuickTagEditor === 'function') {
        e.stopPropagation();
        global.openQuickTagEditor(sid);
      }
    });
  }
  async function loadAndRenderInitialTags(container, sid){
    try{
      const resp = await fetch(`/api/servers/${encodeURIComponent(sid)}/tags?t=${Date.now()}`);
      const json = await resp.json();
      if (json && (json.status===1 || json.code===1)){
        renderTagList(container, json.data || [], { deletable: false });
      } else {
        renderTagList(container, [], { deletable: false });
      }
    }catch(_){
      renderTagList(container, [], { deletable: false });
    }
  }
  global.TagUI = { normalizeIcon, normalizeColor, renderTagChip, renderTagList, bindEditorTrigger, loadAndRenderInitialTags };
})(window);

