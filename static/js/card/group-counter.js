/**
 * 服务器卡片分组计数功能模块
 * 负责统计和更新各分组中的服务器数量
 */

class GroupCounter {
    constructor() {
        this.init();
    }

    init() {
        // 页面加载完成后立即更新一次分组计数
        setTimeout(() => this.updateGroupCounts(), 100);

        // 覆盖全局的updateGroupCounts函数
        if (typeof window.updateGroupCounts === 'undefined') {
            window.updateGroupCounts = () => this.updateGroupCounts();
        }

        // 监听数据更新事件，确保分组计数同步更新
        document.addEventListener('statsUpdated', () => this.updateGroupCounts());
    }

    /**
     * 优化的分组计数更新函数
     */
    updateGroupCounts() {
        try {
            // 获取所有服务器卡片
            const allCards = document.querySelectorAll('.server-card');
            const groupCounts = { all: allCards.length };

            // 统计每个分组的卡片数量
            allCards.forEach(card => {
                const groupId = card.dataset.group || 'default';
                groupCounts[groupId] = (groupCounts[groupId] || 0) + 1;
            });

            // 更新UI显示
            document.querySelectorAll('.group-option').forEach(option => {
                const groupId = option.dataset.group;
                const countElement = option.querySelector('.group-count');
                if (countElement) {
                    countElement.textContent = groupCounts[groupId] || 0;
                }
            });

            console.log('分组计数已更新:', groupCounts);
        } catch (error) {
            console.error('更新分组计数失败:', error);
        }
    }
}

// 浏览器环境下设置全局变量
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GroupCounter;
} else {
    window.GroupCounter = GroupCounter;
}

// 兼容直接引入的方式
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GroupCounter;
}