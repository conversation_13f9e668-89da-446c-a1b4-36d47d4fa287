/**
 * 快速标签编辑功能模块
 * 负责服务器标签的快速编辑、添加、删除和管理
 */

class QuickTagEditor {
    constructor() {
        this.data = {
            currentSid: null,
            tags: [],
            isVisible: false
        };
        
        this.presets = [
            { name: '生产环境', color: '#ef4444', icon: 'alert-triangle' },
            { name: '测试环境', color: '#f59e0b', icon: 'flask' },
            { name: '高优先级', color: '#8b5cf6', icon: 'star' },
            { name: '数据库', color: '#10b981', icon: 'database' },
            { name: 'Web服务', color: '#06b6d4', icon: 'world' }
        ];
        
        this.init();
    }

    init() {
        // 从 meta 标签读取管理员状态
        window.isAdmin = document.querySelector('meta[name="is-admin"]')?.content === 'true';
        
        // 将主要函数暴露到全局
        window.openQuickTagEditor = (sid) => this.openEditor(sid);
        window.hideQuickTagEditor = () => this.hideEditor();
        window.addQuickPresetTag = (name, color, icon) => this.addPresetTag(name, color, icon);
        window.addQuickCustomTag = () => this.addCustomTag();
        window.removeQuickTag = (index) => this.removeTag(index);
        
        // 阻止事件冒泡（防止点击标签时触发卡片点击）
        document.addEventListener('click', (e) => {
            if (e.target.closest('[id$="_CUSTOM_TAGS"]')) {
                e.stopPropagation();
            }
        });
    }

    /**
     * 打开快速标签编辑器
     */
    async openEditor(sid) {
        try {
            // 检查管理员权限
            if (!window.isAdmin) {
                console.log('非管理员用户，无法编辑标签');
                return;
            }
            
            if (this.data.isVisible) {
                this.hideEditor();
                return;
            }
            
            this.data.currentSid = sid;
            this.data.isVisible = true;
            
            // 加载标签数据
            await this.loadTags(sid);
            
            // 创建编辑器
            this.createEditor();
            
        } catch (error) {
            console.error('打开快速标签编辑器失败:', error);
        }
    }

    /**
     * 加载服务器标签数据
     */
    async loadTags(sid) {
        try {
            const response = await fetch(`/api/servers/${sid}/tags`);
            const result = await response.json();
            
            if (result.status) {
                this.data.tags = result.data || [];
            } else {
                console.error('加载标签失败:', result.data);
                this.data.tags = [];
            }
        } catch (error) {
            console.error('加载标签失败:', error);
            this.data.tags = [];
        }
    }

    /**
     * 创建快速标签编辑器
     */
    createEditor() {
        // 移除已有的编辑器
        const existingEditor = document.getElementById('quick-tag-editor');
        if (existingEditor) {
            existingEditor.remove();
        }
        
        // 创建编辑器HTML
        const editorHtml = `
            <div id="quick-tag-editor" class="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm z-[9999] flex items-center justify-center p-4" onclick="hideQuickTagEditor()">
                <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-lg w-full p-6" onclick="event.stopPropagation()">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-slate-800 dark:text-slate-200">快速标签管理</h3>
                        <button type="button" onclick="hideQuickTagEditor()" class="text-slate-400 hover:text-slate-600 dark:hover:text-slate-200">
                            <i class="ti ti-x"></i>
                        </button>
                    </div>
                    
                    <!-- 当前标签列表 -->
                    <div class="mb-4">
                        <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">当前标签</h4>
                        <div id="quick-tags-list" class="flex flex-wrap justify-center gap-2 min-h-[60px]">
                            <!-- 标签列表将在这里渲染 -->
                        </div>
                    </div>
                    
                    <!-- 常用标签（动态 TopN） -->
                    <div class="mb-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300">常用标签</h4>
                            <span id="popular-tags-hint" class="text-[11px] text-slate-400"></span>
                        </div>
                        <div id="popular-tags" class="flex flex-wrap justify-center gap-2 min-h-[34px]"></div>
                    </div>
                    
                    <!-- 常用图标预设 -->
                    <div class="border-t border-slate-200 dark:border-slate-700 pt-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300">常用图标</h4>
                            <a href="https://tabler-icons.io/" target="_blank" class="text-xs text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1" title="查看完整图标库">
                                <i class="ti ti-external-link text-xs"></i>
                                图标库
                            </a>
                        </div>
                        <div class="grid grid-cols-8 gap-2 mb-4 justify-items-center" id="icon-preset-grid">
                            <!-- 图标预设将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- 自定义添加（支持颜色与图标） -->
                    <div class="border-t border-slate-200 dark:border-slate-700 pt-4 space-y-2">
                        <div class="flex gap-2">
                            <input type="text" id="quick-tag-name" placeholder="输入标签名称" maxlength="20" class="flex-1 px-3 py-2 text-sm border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50">
                            <input type="color" id="quick-tag-color" value="#6b7280" class="w-12 h-8 border border-slate-300 dark:border-slate-600 rounded cursor-pointer" title="选择颜色">
                            <input type="text" id="quick-tag-icon" placeholder="icon，如: database" class="w-40 px-2 py-2 text-sm border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-800 dark:text-slate-200" title="Tabler 图标名称（裸名字）">
                        </div>
                        <div class="flex justify-end">
                            <button type="button" onclick="addQuickCustomTag()" class="px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-lg transition-colors">添加</button>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="flex justify-end items-center mt-6">
                        <button type="button" onclick="hideQuickTagEditor()" class="px-4 py-2 text-sm font-medium text-slate-700 dark:text-slate-300 bg-slate-100 dark:bg-slate-700 rounded-lg hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors">完成</button>
                    </div>
                </div>
            </div>
        `;
        
        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', editorHtml);
        
        // 渲染标签列表
        this.renderTagsList();

        // 加载常用标签 TopN
        this.loadPopularTags();

        // 渲染图标预设
        this.renderIconPresets();
        
        // 添加键盘事件
        document.getElementById('quick-tag-name').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addCustomTag();
            }
        });
    }

    /**
     * 渲染快速标签列表
     */
    renderTagsList() {
        const container = document.getElementById('quick-tags-list');
        if (!container) return;
        
        if (this.data.tags.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4 text-slate-500 dark:text-slate-400">
                    <i class="ti ti-tag text-xl mb-1"></i>
                    <p class="text-xs">暂无标签</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.data.tags.map((tag, index) => {
            const iconName = (tag.icon || 'tag').toString().toLowerCase().replace(/^ti\s*-/, '').replace(/_/g, '-');
            const safeColor = /^#[0-9A-Fa-f]{6}$/.test(tag.color || '') ? tag.color : '#6b7280';
            return `
                <span class="tag-item inline-flex items-center gap-0.5 px-1.5 py-0.5 text-[11px] font-medium rounded whitespace-nowrap flex-shrink-0"
                      title="点击管理标签：${tag.name}"
                      style="background-color: ${safeColor}20; border: 1px solid ${safeColor}40; color: ${safeColor};">
                    <i class="ti ti-${iconName} text-[10px]"></i>
                    <span>${tag.name}</span>
                    <button type="button" onclick="removeQuickTag(${index})" class="ml-1 text-slate-400 hover:text-red-500" title="删除">
                        <i class="ti ti-x text-[10px]"></i>
                    </button>
                </span>`;
        }).join('');
    }

    /**
     * 添加常用标签（从热门区点击）
     */
    async addPresetTag(name, color, icon) {
        try {
            if (this.data.tags.some(tag => tag.name === name)) {
                return; // 标签已存在，静默跳过
            }
            
            if (this.data.tags.length >= 5) {
                alert('标签数量不能超过5个');
                return;
            }
            
            const response = await fetch(`/api/servers/${this.data.currentSid}/tags`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ name, color, icon })
            });
            
            const result = await response.json();
            
            if (result.status) {
                await this.loadTags(this.data.currentSid);
                this.renderTagsList();
                this.refreshServerCard(this.data.currentSid);
            }
        } catch (error) {
            console.error('添加预设标签失败:', error);
        }
    }

    /**
     * 加载热门标签 TopN 并渲染
     */
    async loadPopularTags(limit = 5) {
        try {
            const hint = document.getElementById('popular-tags-hint');
            const box = document.getElementById('popular-tags');
            if (!box) return;
            if (hint) hint.textContent = '加载中...';

            const resp = await fetch(`/api/servers/tags/popular?limit=${limit}&t=${Date.now()}`);
            const json = await resp.json();
            if (!json || json.success !== true || !Array.isArray(json.data)) {
                box.innerHTML = `<span class="text-[12px] text-slate-400">暂无热门标签</span>`;
                if (hint) hint.textContent = '';
                return;
            }

            const items = json.data;
            if (items.length === 0) {
                box.innerHTML = `<span class="text-[12px] text-slate-400">暂无热门标签</span>`;
                if (hint) hint.textContent = '';
                return;
            }

            box.innerHTML = items.map(it => {
                const iconName = (it.icon || 'tag').toString().toLowerCase().replace(/^ti\s*-/, '').replace(/_/g, '-');
                const color = /^#[0-9A-Fa-f]{6}$/.test(it.color || '') ? it.color : '#6b7280';
                return `
                    <button type="button"
                            class="tag-item inline-flex items-center gap-0.5 px-1.5 py-0.5 text-[11px] font-medium rounded whitespace-nowrap flex-shrink-0 hover:opacity-90"
                            style="background-color: ${color}20; border: 1px solid ${color}40; color: ${color};"
                            title="添加标签：${it.name}"
                            onclick="addQuickPresetTag('${it.name.replace(/'/g, "\'")}', '${color}', '${iconName}')">
                        <i class="ti ti-${iconName} text-[10px]"></i>
                        <span>${it.name}</span>
                    </button>`;
            }).join('');
            if (hint) hint.textContent = `Top ${items.length}`;
        } catch (e) {
            const box = document.getElementById('popular-tags');
            if (box) box.innerHTML = `<span class="text-[12px] text-slate-400">加载失败</span>`;
            const hint = document.getElementById('popular-tags-hint');
            if (hint) hint.textContent = '';
            console.error('加载热门标签失败:', e);
        }
    }

    /**
     * 渲染图标预设
     */
    renderIconPresets() {
        const iconPresets = [
            { icon: 'server', title: '服务器' },
            { icon: 'database', title: '数据库' },
            { icon: 'cloud', title: '云服务' },
            { icon: 'shield', title: '安全' },
            { icon: 'network', title: '网络' },
            { icon: 'cpu', title: 'CPU' },
            { icon: 'device-desktop', title: '桌面' },
            { icon: 'world', title: '全球' },
            { icon: 'api', title: 'API' },
            { icon: 'code', title: '代码' },
            { icon: 'bug', title: '调试' },
            { icon: 'rocket', title: '性能' },
            { icon: 'settings', title: '设置' },
            { icon: 'brain', title: 'AI' },
            { icon: 'chart-line', title: '监控' },
            { icon: 'lock', title: '锁定' }
        ];

        const container = document.getElementById('icon-preset-grid');
        if (!container) return;

        container.innerHTML = iconPresets.map(preset => `
            <button type="button"
                    class="icon-preset-btn flex items-center justify-center w-8 h-8 rounded border border-slate-300 dark:border-slate-600 hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                    title="${preset.title}"
                    onclick="window.selectIconPreset('${preset.icon}')">
                <i class="ti ti-${preset.icon} text-sm text-slate-600 dark:text-slate-400"></i>
            </button>
        `).join('');

        // 添加图标选择函数到全局
        window.selectIconPreset = (iconName) => {
            const iconInput = document.getElementById('quick-tag-icon');
            if (iconInput) {
                iconInput.value = iconName;
                // 高亮选中的图标
                document.querySelectorAll('.icon-preset-btn').forEach(btn => {
                    btn.classList.remove('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                });
                event.target.closest('.icon-preset-btn').classList.add('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
            }
        };
    }

    /**
     * 添加自定义标签
     */
    async addCustomTag() {
        try {
            const nameInput = document.getElementById('quick-tag-name');
            const name = nameInput.value.trim();
            const colorInput = document.getElementById('quick-tag-color');
            const iconInput = document.getElementById('quick-tag-icon');

            if (!name) {
                alert('请输入标签名称');
                return;
            }
            
            if (this.data.tags.some(tag => tag.name === name)) {
                alert('标签名称已存在');
                return;
            }
            
            if (this.data.tags.length >= 5) {
                alert('标签数量不能超过5个');
                return;
            }
            
            // 归一化与兜底
            const color = /^#[0-9A-Fa-f]{6}$/.test(colorInput && colorInput.value ? colorInput.value : '') ? colorInput.value : '#6b7280';
            const icon = (iconInput && iconInput.value ? iconInput.value : 'tag').toLowerCase().replace(/^ti\s*-/, '').replace(/_/g, '-') || 'tag';

            const response = await fetch(`/api/servers/${this.data.currentSid}/tags`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    name, 
                    color, 
                    icon
                })
            });
            
            const result = await response.json();
            
            if (result.status) {
                nameInput.value = '';
                if (colorInput) colorInput.value = '#6b7280';
                if (iconInput) iconInput.value = '';
                await this.loadTags(this.data.currentSid);
                this.renderTagsList();
                this.refreshServerCard(this.data.currentSid);
            } else {
                alert(result.data || '添加标签失败');
            }
        } catch (error) {
            console.error('添加自定义标签失败:', error);
            alert('添加标签失败');
        }
    }

    /**
     * 删除标签
     */
    async removeTag(index) {
        try {
            const tag = this.data.tags[index];
            
            const response = await fetch(`/api/servers/${this.data.currentSid}/tags/${tag.id}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            if (result.status) {
                await this.loadTags(this.data.currentSid);
                this.renderTagsList();
                this.refreshServerCard(this.data.currentSid);
            }
        } catch (error) {
            console.error('删除标签失败:', error);
        }
    }

    /**
     * 隐藏快速标签编辑器
     */
    hideEditor() {
        const editor = document.getElementById('quick-tag-editor');
        if (editor) {
            editor.remove();
        }
        this.data.isVisible = false;
        this.data.currentSid = null;
    }

    /**
     * 刷新服务器卡片显示
     */
    async refreshServerCard(sid) {
        try {
            // 重新获取服务器数据并更新卡片中的标签显示
            // 添加时间戳避免缓存
            const response = await fetch(`/api/servers/${sid}/tags?t=${Date.now()}`);
            const result = await response.json();
            
            if (result.status) {
                const tags = result.data || [];
                const tagsContainer = document.getElementById(`${sid}_CUSTOM_TAGS`);
                
                if (tagsContainer) {
                    
                    // 重新渲染标签区域
                    let tagsHtml = '';
                    
                    if (tags.length > 0) {
                        // 显示所有标签，不限制数量
                        for (let i = 0; i < tags.length; i++) {
                            const tag = tags[i];
                            const hoverClass = window.isAdmin ? 'cursor-pointer hover:opacity-80' : 'cursor-default';
                            const titleText = window.isAdmin ? `点击管理标签：${tag.name}` : `标签：${tag.name}`;
                            const iconName = (tag.icon || 'tag').toString().toLowerCase().replace(/^ti\s*-/, '').replace(/_/g, '-');
                            const tagStyle = tag.color ? 
                                `background-color: ${tag.color}20; border: 1px solid ${tag.color}40; color: ${tag.color};` :
                                'background-color: #6b728080; border: 1px solid #6b728040; color: #6b7280;';
                            tagsHtml += `
                                <div class="flex items-center justify-center text-xs px-2 py-1 rounded-md ${hoverClass} transition-opacity whitespace-nowrap flex-shrink-0"
                                     title="${titleText}"
                                     style="${tagStyle}">
                                    <i class="ti ti-${iconName}" style="font-size:10px;margin-right:4px;"></i>
                                    <span class="font-medium text-xs text-center">${tag.name}</span>
                                </div>
                            `;
                        }
                        
                        // 管理员总是显示添加按钮（不再填充空位）
                        if (window.isAdmin) {
                            tagsHtml += `
                                <div class="flex items-center justify-center text-xs px-2 py-1 rounded-md border border-dashed border-slate-300 dark:border-slate-700 text-slate-400 dark:text-slate-600 cursor-pointer hover:border-slate-400 dark:hover:border-slate-500 hover:text-slate-500 dark:hover:text-slate-500 transition-colors whitespace-nowrap flex-shrink-0" title="点击添加标签">
                                    <i class="ti ti-plus text-sm"></i>
                                </div>
                            `;
                        }
                    } else {
                        if (window.isAdmin) {
                            // 管理员：无标签时显示添加提示
                            tagsHtml = `
                                <div class="flex items-center justify-center text-xs px-2 py-1 rounded-md border border-dashed border-slate-300 dark:border-slate-700 text-slate-400 dark:text-slate-600 cursor-pointer hover:border-slate-400 dark:hover:border-slate-500 hover:text-slate-500 dark:hover:text-slate-500 transition-colors whitespace-nowrap" title="点击添加标签">
                                    <i class="ti ti-plus text-sm mr-1"></i>
                                    <span class="text-xs">添加标签</span>
                                </div>
                            `;
                        } else {
                            // 游客：显示"暂无标签"
                            tagsHtml = `
                                <div class="flex items-center justify-center text-xs px-2 py-1 rounded-md bg-slate-100 dark:bg-slate-800/60 text-slate-400 dark:text-slate-500 whitespace-nowrap" title="暂无标签">
                                    <span class="text-xs">暂无标签</span>
                                </div>
                            `;
                        }
                    }
                    
                    tagsContainer.innerHTML = tagsHtml;
                    
                    // 更新标签计数属性
                    tagsContainer.setAttribute('data-tag-count', tags.length);
                    
                    // 重新设置点击事件和可编辑状态
                    if (window.isAdmin) {
                        tagsContainer.onclick = function(event) {
                            event.stopPropagation();
                            openQuickTagEditor(sid);
                        };
                        tagsContainer.setAttribute('data-editable', 'true');
                    } else {
                        tagsContainer.onclick = null;
                        tagsContainer.setAttribute('data-editable', 'false');
                    }
                }
            }
        } catch (error) {
            console.error('刷新服务器卡片失败:', error);
        }
    }
}

// 浏览器环境下设置全局变量
if (typeof module !== 'undefined' && module.exports) {
    module.exports = QuickTagEditor;
} else {
    window.QuickTagEditor = QuickTagEditor;
}
