/**
 * 系统图标管理模块
 * 根据系统信息显示对应的操作系统图标
 */

class SystemIconManager {
    constructor() {
        this.init();
    }

    init() {
        // 系统图标映射配置
        this.iconMappings = {
            // Linux 发行版（Tabler 品牌有限，其他回退通用设备图标）
            'ubuntu': { icon: 'ti ti-brand-ubuntu', color: 'text-orange-600', name: 'Ubuntu' },
            'debian': { icon: 'ti ti-brand-debian', color: 'text-red-600', name: 'Debian' },
            'centos': { icon: 'ti ti-device-desktop', color: 'text-purple-700', name: 'CentOS' },
            'fedora': { icon: 'ti ti-device-desktop', color: 'text-blue-700', name: 'Fedora' },
            'redhat': { icon: 'ti ti-brand-redhat', color: 'text-red-700', name: 'Red Hat' },
            'suse': { icon: 'ti ti-device-desktop', color: 'text-green-700', name: 'SUSE' },
            'arch': { icon: 'ti ti-device-desktop', color: 'text-blue-500', name: 'Arch Linux' },
            'alpine': { icon: 'ti ti-device-desktop', color: 'text-blue-600', name: 'Alpine Linux' },
            'rocky': { icon: 'ti ti-device-desktop', color: 'text-green-600', name: 'Rocky Linux' },
            'alma': { icon: 'ti ti-device-desktop', color: 'text-orange-700', name: 'AlmaLinux' },
            
            // 通用 Linux
            'linux': { icon: 'ti ti-device-desktop', color: 'text-black dark:text-white', name: 'Linux' },
            
            // Windows
            'windows': { icon: 'ti ti-brand-windows', color: 'text-blue-500', name: 'Windows' },
            'windows server': { icon: 'ti ti-brand-windows', color: 'text-blue-600', name: 'Windows Server' },
            
            // macOS
            'darwin': { icon: 'ti ti-brand-apple', color: 'text-gray-700 dark:text-gray-300', name: 'macOS' },
            'macos': { icon: 'ti ti-brand-apple', color: 'text-gray-700 dark:text-gray-300', name: 'macOS' },
            
            // BSD（无品牌，用终端图标表示）
            'freebsd': { icon: 'ti ti-terminal-2', color: 'text-red-600', name: 'FreeBSD' },
            'openbsd': { icon: 'ti ti-terminal-2', color: 'text-yellow-600', name: 'OpenBSD' },
            
            // 其他
            'android': { icon: 'ti ti-brand-android', color: 'text-green-500', name: 'Android' },
            'docker': { icon: 'ti ti-brand-docker', color: 'text-blue-400', name: 'Docker' },
            
            // 默认
            'unknown': { icon: 'ti ti-server', color: 'text-gray-500', name: '未知系统' }
        };
        
        // 页面加载完成后更新所有系统图标
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.updateAllSystemIcons());
        } else {
            this.updateAllSystemIcons();
        }
        
        // 监听数据更新事件
        document.addEventListener('statsUpdated', () => this.updateAllSystemIcons());
    }

    /**
     * 从服务器卡片获取详细的系统信息
     */
    getSystemInfoFromCard(card) {
        // 尝试从多个数据源获取系统信息
        const sid = card.dataset.sid;
        
        // 1. 从 platform 数据属性获取
        const iconContainer = card.querySelector('.system-icon-container');
        const platformData = iconContainer?.dataset.platform || '';
        
        // 2. 从全局 stats 数据获取（如果存在）
        let detailedInfo = '';
        if (window.statsData && window.statsData[sid]) {
            const stat = window.statsData[sid].stat;
            if (stat && stat.host) {
                // 优先使用 platformVersion（包含发行版信息）
                if (stat.host.platformVersion) {
                    detailedInfo = stat.host.platformVersion;
                } else if (stat.host.platform) {
                    detailedInfo = stat.host.platform;
                }
            }
        }
        
        return detailedInfo || platformData;
    }

    /**
     * 获取系统信息并返回对应的图标配置
     */
    getSystemIcon(platformInfo) {
        if (!platformInfo) {
            return this.iconMappings.unknown;
        }

        const platform = platformInfo.toLowerCase();
        
        // 检查是否包含特定关键词（优先级从高到低）
        if (platform.includes('ubuntu')) return this.iconMappings.ubuntu;
        if (platform.includes('debian')) return this.iconMappings.debian;
        if (platform.includes('centos')) return this.iconMappings.centos;
        if (platform.includes('fedora')) return this.iconMappings.fedora;
        if (platform.includes('red hat') || platform.includes('redhat') || platform.includes('rhel')) return this.iconMappings.redhat;
        if (platform.includes('suse') || platform.includes('opensuse')) return this.iconMappings.suse;
        if (platform.includes('arch')) return this.iconMappings.arch;
        if (platform.includes('alpine')) return this.iconMappings.alpine;
        if (platform.includes('rocky')) return this.iconMappings.rocky;
        if (platform.includes('alma')) return this.iconMappings.alma;
        if (platform.includes('windows server')) return this.iconMappings['windows server'];
        if (platform.includes('windows')) return this.iconMappings.windows;
        if (platform.includes('darwin') || platform.includes('mac') || platform.includes('osx')) return this.iconMappings.darwin;
        if (platform.includes('freebsd')) return this.iconMappings.freebsd;
        if (platform.includes('openbsd')) return this.iconMappings.openbsd;
        if (platform.includes('android')) return this.iconMappings.android;
        if (platform.includes('docker')) return this.iconMappings.docker;
        if (platform.includes('linux')) return this.iconMappings.linux;
        
        return this.iconMappings.unknown;
    }

    /**
     * 更新单个服务器的系统图标
     */
    updateSystemIcon(card) {
        const iconContainer = card.querySelector('.system-icon-container');
        if (!iconContainer) return;
        
        // 获取详细的平台信息
        const platformInfo = this.getSystemInfoFromCard(card);
        const iconConfig = this.getSystemIcon(platformInfo);
        
        // 创建新的图标元素（优先使用自建彩色 SVG 图标）
        if (window.OsIcons) {
            const el = window.OsIcons.create(platformInfo, { size: 18, className: 'align-middle', alt: iconConfig.name, title: platformInfo || iconConfig.name });
            iconContainer.innerHTML = '';
            if (el) iconContainer.appendChild(el);
        }
        
        // 更新 title 属性以显示详细信息
        if (platformInfo) {
            iconContainer.title = platformInfo;
        }
    }

    /**
     * 更新所有服务器卡片的系统图标
     */
    updateAllSystemIcons() {
        const cards = document.querySelectorAll('.server-card');
        cards.forEach(card => this.updateSystemIcon(card));
    }

    /**
     * 手动刷新特定服务器的系统图标
     */
    refreshSystemIcon(sid, platformInfo) {
        const card = document.querySelector(`.server-card[data-sid="${sid}"]`);
        if (!card) return;
        
        const iconContainer = card.querySelector('.system-icon-container');
        if (iconContainer) {
            iconContainer.dataset.platform = platformInfo;
            this.updateSystemIcon(card);
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SystemIconManager;
} else {
    // 浏览器环境下设置全局变量
    window.SystemIconManager = SystemIconManager;
    
    // 自动初始化
    window.systemIconManager = new SystemIconManager();
}
