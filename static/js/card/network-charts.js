/**
 * 网络图表管理模块
 * 负责更新延迟和丢包率的迷你柱状图
 */

class NetworkCharts {
    constructor() {
        this.latencyHistory = new Map(); // 存储每个服务器的延迟历史
        this.packetLossHistory = new Map(); // 存储每个服务器的丢包率历史
        this.maxHistoryLength = 24; // 24小时数据，每小时一个点，共24个点
        this.updateInterval = 300000; // 5分钟更新一次历史数据
        this.lastUpdateTime = new Map(); // 记录每个服务器的最后更新时间
        this.dataInitialized = new Map(); // 标记数据是否已初始化，避免重复添加
        this.dataCache = new Map(); // 数据缓存
        this.cacheExpiry = 300000; // 缓存5分钟有效期
        this.useBatchAPI = true; // 启用批量 API（可以通过配置关闭以保持兼容性）
        this.batchCacheKey = 'network_batch_data'; // 批量数据缓存键
        this.updateTimer = null; // 定时器ID
        this.isPageVisible = true; // 页面可见状态
        this.init();
    }

    init() {
        // 将函数暴露到全局
        window.updateNetworkCharts = (sid, latency, packetLoss) => this.updateCharts(sid, latency, packetLoss);
        
        // 监听数据更新事件
        document.addEventListener('statsUpdated', (event) => {
            if (event.detail && event.detail.data) {
                this.updateAllCharts(event.detail.data);
            }
        });

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });

        // 初始化所有图表占位符
        this.initializePlaceholders();

        // 页面加载后立即获取历史数据
        this.loadAllHistoryData();

        // 开始定时更新（只在页面可见时运行）
        this.startAutoUpdate();

    }

    /**
     * 初始化所有图表占位符
     */
    initializePlaceholders() {
        // 初始化延迟图表占位符
        document.querySelectorAll('.latency-mini-chart').forEach(chart => {
            if (chart.children.length === 0) {
                this.createPlaceholderBars(chart);
            }
        });
        
        // 初始化丢包率图表占位符
        document.querySelectorAll('.packet-loss-mini-chart').forEach(chart => {
            if (chart.children.length === 0) {
                this.createPlaceholderBars(chart);
            }
        });
    }

    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        this.isPageVisible = !document.hidden;
        
        if (this.isPageVisible) {
            console.log('[NetworkCharts] 页面变为可见，恢复数据更新');
            // 页面重新可见时，立即更新数据并重启定时器
            this.loadAllHistoryData();
            this.startAutoUpdate();
        } else {
            console.log('[NetworkCharts] 页面变为隐藏，暂停数据更新');
            // 页面隐藏时，停止定时器
            this.stopAutoUpdate();
        }
    }

    /**
     * 开始自动更新
     */
    startAutoUpdate() {
        // 清除旧的定时器
        this.stopAutoUpdate();
        
        // 只在页面可见时设置定时器
        if (this.isPageVisible) {
            this.updateTimer = setInterval(() => {
                if (this.isPageVisible) {
                    this.loadAllHistoryData();
                }
            }, this.updateInterval);
        }
    }

    /**
     * 停止自动更新
     */
    stopAutoUpdate() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
    }

    /**
     * 更新单个服务器的网络图表
     */
    updateCharts(sid, latency, packetLoss) {
        this.updateLatencyChart(sid, latency);
        this.updatePacketLossChart(sid, packetLoss);
    }

    /**
     * 更新延迟图表
     */
    updateLatencyChart(sid, latency) {
        // 如果数据已从API初始化，不再从WebSocket添加实时数据
        // 只更新最新值的显示
        if (this.dataInitialized.get(sid)) {
            const latencyElement = document.getElementById(`${sid}_LATENCY`);
            if (latencyElement) {
                latencyElement.textContent = `${Math.round(latency)} ms`;
                latencyElement.setAttribute('data-latency', latency);
            }
            return;
        }
        
        // 更新历史数据（仅在未初始化时）
        if (!this.latencyHistory.has(sid)) {
            this.latencyHistory.set(sid, []);
        }
        
        const history = this.latencyHistory.get(sid);
        history.push(latency);
        
        // 保持历史数据长度
        if (history.length > this.maxHistoryLength) {
            history.shift();
        }

        // 更新图表显示
        const chartElement = document.querySelector(`.latency-mini-chart[data-sid="${sid}"]`);
        if (chartElement) {
            this.renderMiniChart(chartElement, history, this.getLatencyColor);
        }
    }

    /**
     * 更新丢包率图表
     */
    updatePacketLossChart(sid, packetLoss) {
        // 如果数据已从API初始化，不再从WebSocket添加实时数据
        // 只更新最新值的显示
        if (this.dataInitialized.get(sid)) {
            const packetLossElement = document.getElementById(`${sid}_PACKET_LOSS`);
            if (packetLossElement) {
                packetLossElement.textContent = `${packetLoss.toFixed(1)} %`;
                packetLossElement.setAttribute('data-packet-loss', packetLoss);
            }
            return;
        }
        
        // 更新历史数据（仅在未初始化时）
        if (!this.packetLossHistory.has(sid)) {
            this.packetLossHistory.set(sid, []);
        }
        
        const history = this.packetLossHistory.get(sid);
        history.push(packetLoss);
        
        // 保持历史数据长度
        if (history.length > this.maxHistoryLength) {
            history.shift();
        }

        // 更新图表显示
        const chartElement = document.querySelector(`.packet-loss-mini-chart[data-sid="${sid}"]`);
        if (chartElement) {
            this.renderMiniChart(chartElement, history, this.getPacketLossColor);
        }
    }

    /**
     * 渲染迷你图表 - 竖条版本
     */
    renderMiniChart(chartElement, data, colorFunction) {
        // 检查是否已有竖条元素
        const existingBars = chartElement.children;
        const hasExistingBars = existingBars.length === 24;
        
        // 如果没有竖条，先创建占位符
        if (!hasExistingBars) {
            this.createPlaceholderBars(chartElement);
        }
        
        // 确保始终显示24个竖条
        const targetCount = 24;
        let displayData = [];
        
        if (data && data.length > 0) {
            // 如果数据超过24个，取最新的24个
            displayData = data.slice(-targetCount);
        }
        
        // 如果数据不足24个，在前面补充占位符
        while (displayData.length < targetCount) {
            displayData.unshift(null); // null表示无数据
        }
        
        // 使用requestAnimationFrame确保平滑过渡
        requestAnimationFrame(() => {
            const bars = chartElement.children;
            
            // 更新每个竖条的样式（不重建DOM）
            displayData.forEach((value, index) => {
                const bar = bars[index];
                if (!bar) return;
                
                // 保存旧的类名以便过渡
                const oldClasses = bar.className;
                
                if (value !== null) {
                    // 有数据时显示彩色竖条
                    const color = colorFunction(value);
                    bar.className = color;
                    bar.style.opacity = '0.8';
                    
                    // 如果是边框样式，移除边框类
                    bar.classList.remove('border', 'border-gray-300', 'dark:border-gray-600');
                } else {
                    // 无数据时显示灰色空心竖条
                    bar.className = 'border border-gray-300 dark:border-gray-600';
                    bar.style.opacity = '0.5';
                }
                
                // 保持过渡效果
                if (!bar.style.transition) {
                    bar.style.transition = 'all 0.3s ease';
                }
                
                // 更新title提示
                if (value !== null) {
                    bar.title = `${index + 1}时: ${value.toFixed(1)}${chartElement.classList.contains('latency-mini-chart') ? 'ms' : '%'}`;
                } else {
                    bar.title = `${index + 1}时: 无数据`;
                }
            });
        });
    }

    /**
     * 创建占位符竖条（静态灰色块）
     */
    createPlaceholderBars(chartElement) {
        chartElement.innerHTML = ''; // 清空现有内容
        for (let i = 0; i < 24; i++) {
            const placeholder = document.createElement('div');
            placeholder.className = 'bg-gray-200 dark:bg-gray-700';
            placeholder.style.opacity = '0.4';
            placeholder.style.transition = 'all 0.3s ease'; // 为后续颜色变化准备
            placeholder.setAttribute('data-index', i);
            chartElement.appendChild(placeholder);
        }
    }

    /**
     * 根据延迟值获取颜色 - 彩色竖条版
     */
    getLatencyColor(latency) {
        if (latency < 50) return 'bg-green-500';    // 优秀 - 绿色
        if (latency < 100) return 'bg-yellow-500';  // 良好 - 黄色
        if (latency < 200) return 'bg-orange-500';  // 一般 - 橙色
        return 'bg-red-500';                        // 较差 - 红色
    }

    /**
     * 根据丢包率获取颜色 - 彩色竖条版
     */
    getPacketLossColor(packetLoss) {
        if (packetLoss < 1) return 'bg-green-500';   // 优秀 - 绿色
        if (packetLoss < 5) return 'bg-yellow-500';  // 良好 - 黄色
        if (packetLoss < 10) return 'bg-orange-500'; // 一般 - 橙色
        return 'bg-red-500';                         // 较差 - 红色
    }

    /**
     * 批量更新所有服务器的图表
     */
    updateAllCharts(data) {
        if (!data || typeof data !== 'object') return;

        Object.keys(data).forEach(sid => {
            const nodeData = data[sid];
            if (nodeData && nodeData.latency !== undefined && nodeData.packet_loss !== undefined) {
                this.updateCharts(sid, nodeData.latency, nodeData.packet_loss);
            }
        });
    }

    /**
     * 清理指定服务器的历史数据
     */
    clearHistory(sid) {
        this.latencyHistory.delete(sid);
        this.packetLossHistory.delete(sid);
    }

    /**
     * 清理所有历史数据
     */
    clearAllHistory() {
        this.latencyHistory.clear();
        this.packetLossHistory.clear();
    }

    /**
     * 加载所有服务器的历史数据
     */
    async loadAllHistoryData() {
        const cards = document.querySelectorAll('.server-card');
        const serverIds = [];
        
        // 收集所有服务器 ID
        cards.forEach(card => {
            const sid = card.dataset.sid;
            if (sid) {
                serverIds.push(sid);
            }
        });

        if (serverIds.length === 0) {
            return;
        }

        // 如果启用了批量 API，尝试批量加载
        if (this.useBatchAPI) {
            const batchSuccess = await this.loadBatchHistoryData(serverIds);
            if (batchSuccess) {
                return;
            }
            console.warn('批量 API 加载失败，回退到单个请求模式');
        }

        // 如果批量 API 失败或被禁用，使用并行请求方式
        const loadPromises = [];
        for (const card of cards) {
            const sid = card.dataset.sid;
            if (sid) {
                loadPromises.push(this.loadHistoryData(sid));
            }
        }
        // 并行加载所有数据
        await Promise.all(loadPromises);
    }

    /**
     * 批量加载多个服务器的历史数据
     */
    async loadBatchHistoryData(serverIds) {
        try {
            // 检查批量缓存
            const cachedBatch = this.dataCache.get(this.batchCacheKey);
            const now = Date.now();
            
            if (cachedBatch && (now - cachedBatch.timestamp < this.cacheExpiry)) {
                console.log('使用批量缓存数据');
                this.processBatchData(cachedBatch.data);
                return true;
            }

            // 发送批量请求
            const response = await fetch('/api/monitor/batch-network-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    serverIds: serverIds,
                    timeRange: '24h'
                })
            });

            const result = await response.json();

            if (result.success && result.data) {
                // 缓存批量数据
                this.dataCache.set(this.batchCacheKey, {
                    data: result.data,
                    timestamp: now
                });

                // 处理批量数据
                this.processBatchData(result.data);
                
                // 更新所有服务器的最后更新时间
                serverIds.forEach(sid => {
                    this.lastUpdateTime.set(sid, now);
                });

                return true;
            }

            return false;
        } catch (error) {
            console.error('批量加载网络数据失败:', error);
            return false;
        }
    }

    /**
     * 处理批量数据
     */
    processBatchData(batchData) {
        for (const [sid, data] of Object.entries(batchData)) {
            if (data && data.length > 0) {
                this.processHistoryData(sid, data);
            }
        }
    }

    /**
     * 从 API 加载历史数据
     */
    async loadHistoryData(sid) {
        try {
            // 检查缓存
            const cacheKey = `network_data_${sid}`;
            const cachedData = this.dataCache.get(cacheKey);
            const now = Date.now();
            
            if (cachedData && (now - cachedData.timestamp < this.cacheExpiry)) {
                console.log(`使用缓存数据: ${sid}`);
                this.processHistoryData(sid, cachedData.data);
                return;
            }

            // 避免频繁请求
            const lastUpdate = this.lastUpdateTime.get(sid) || 0;
            if (now - lastUpdate < 30000) { // 30秒内不重复请求
                return;
            }

            // 获取最近24小时的数据
            const response = await fetch(`/api/monitor/data?type=archive&time_range=24h&node_id=${sid}&detail_level=detailed&sampling_rate=1`);
            const result = await response.json();

            if (result.success && result.data && result.data.length > 0) {
                // 缓存数据
                this.dataCache.set(cacheKey, {
                    data: result.data,
                    timestamp: now
                });
                
                this.processHistoryData(sid, result.data);
                this.lastUpdateTime.set(sid, now);
            }
        } catch (error) {
            console.warn(`加载服务器 ${sid} 网络质量历史数据失败:`, error);
        }
    }

    /**
     * 处理历史数据并更新图表
     */
    processHistoryData(sid, data) {
        // 标记该服务器的数据已从API初始化
        this.dataInitialized.set(sid, true);
        
        // 按时间排序
        const sortedData = data.sort((a, b) => a.created_at - b.created_at);
        
        // 将分钟级数据聚合为小时级数据
        const hourlyData = this.aggregateToHourly(sortedData);
        
        // 取最近24个小时的数据
        const recentData = hourlyData.slice(-this.maxHistoryLength);

        // 提取延迟和丢包率数据
        const latencyData = recentData.map(item => item.avg_time || 0);
        const packetLossData = recentData.map(item => item.packet_loss || 0);

        // 更新历史数据
        this.latencyHistory.set(sid, latencyData);
        this.packetLossHistory.set(sid, packetLossData);

        // 更新图表显示
        const latencyChart = document.querySelector(`.latency-mini-chart[data-sid="${sid}"]`);
        const packetLossChart = document.querySelector(`.packet-loss-mini-chart[data-sid="${sid}"]`);

        if (latencyChart) {
            this.renderMiniChart(latencyChart, latencyData, this.getLatencyColor);
        }

        if (packetLossChart) {
            this.renderMiniChart(packetLossChart, packetLossData, this.getPacketLossColor);
        }

        // 更新最新值显示
        if (latencyData.length > 0) {
            const latencyElement = document.getElementById(`${sid}_LATENCY`);
            if (latencyElement) {
                const latestLatency = latencyData[latencyData.length - 1];
                latencyElement.textContent = `${Math.round(latestLatency)} ms`;
                latencyElement.setAttribute('data-latency', latestLatency);
            }
        }
        
        if (packetLossData.length > 0) {
            const packetLossElement = document.getElementById(`${sid}_PACKET_LOSS`);
            if (packetLossElement) {
                const latestPacketLoss = packetLossData[packetLossData.length - 1];
                packetLossElement.textContent = `${latestPacketLoss.toFixed(1)} %`;
                packetLossElement.setAttribute('data-packet-loss', latestPacketLoss);
            }
        }

    }
    
    /**
     * 将分钟级数据聚合为小时级数据
     */
    aggregateToHourly(minuteData) {
        const hourlyMap = new Map();
        
        minuteData.forEach(item => {
            // 获取小时时间戳（将分钟清零）
            const hourTimestamp = Math.floor(item.created_at / 3600) * 3600;
            
            if (!hourlyMap.has(hourTimestamp)) {
                hourlyMap.set(hourTimestamp, {
                    created_at: hourTimestamp,
                    avg_times: [],
                    success_rates: [],
                    count: 0
                });
            }
            
            const hourData = hourlyMap.get(hourTimestamp);
            hourData.avg_times.push(item.avg_time || 0);
            hourData.success_rates.push(item.success_rate || 0);
            hourData.count++;
        });
        
        // 计算每小时的平均值
        const hourlyData = [];
        hourlyMap.forEach((data, timestamp) => {
            const avgLatency = data.avg_times.reduce((a, b) => a + b, 0) / data.avg_times.length;
            const avgSuccessRate = data.success_rates.reduce((a, b) => a + b, 0) / data.success_rates.length;
            const packetLoss = (1 - avgSuccessRate) * 100;
            
            hourlyData.push({
                created_at: timestamp,
                avg_time: avgLatency,
                success_rate: avgSuccessRate,
                packet_loss: packetLoss,
                sample_count: data.count
            });
        });
        
        return hourlyData.sort((a, b) => a.created_at - b.created_at);
    }

    /**
     * 设置是否使用批量 API
     * @param {boolean} enable - true 启用批量 API，false 使用传统单个请求
     */
    setUseBatchAPI(enable) {
        this.useBatchAPI = enable;
        console.log(`网络图表批量 API ${enable ? '已启用' : '已禁用'}`);
    }

    /**
     * 清除批量缓存
     */
    clearBatchCache() {
        this.dataCache.delete(this.batchCacheKey);
        console.log('批量网络数据缓存已清除');
    }
}

// 浏览器环境下设置全局变量
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NetworkCharts;
} else {
    window.NetworkCharts = NetworkCharts;
}