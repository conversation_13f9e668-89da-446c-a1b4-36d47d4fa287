/**
 * 服务器卡片流量管理功能模块
 * 负责流量显示、格式化和数据更新
 */

class TrafficManager {
    constructor() {
        // 批量 API 配置
        this.useBatchAPI = true; // 启用批量 API（可以通过配置关闭以保持兼容性）
        this.batchCacheKey = 'traffic_batch_data'; // 批量数据缓存键
        this.batchCacheDuration = 2 * 60 * 1000; // 缓存时长：2分钟（降低重装影响）
        this.systemVersionKey = 'dstatus_system_version'; // 系统版本缓存键
        this.serverListHashKey = 'dstatus_server_list_hash'; // 服务器列表哈希缓存键
        
        // 页面可见性相关
        this.updateTimer = null; // 定时器ID
        this.isPageVisible = true; // 页面可见状态
        this.updateInterval = 5 * 60 * 1000; // 更新间隔：5分钟
        
        this.init();
    }

    init() {
        // 立即更新流量显示（移除200ms延迟）
        this.updateTrafficDisplay();

        // 监听数据更新事件，同步更新流量显示
        document.addEventListener('statsUpdated', () => this.updateTrafficDisplay());

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });

        // 将函数暴露到全局，供stats.js调用
        window.updateMonthlyTrafficDisplay = (sid, monthlyData) => this.updateMonthlyTrafficDisplay(sid, monthlyData);
        window.fetchAndUpdateTrafficForServer = (sid) => this.fetchAndUpdateTrafficForServer(sid);
        window.updateAllTrafficData = () => this.updateAllTrafficData();

        // 页面加载后快速获取流量数据（减少延迟至100ms）
        setTimeout(() => this.updateAllTrafficData(), 100);

        // 开始定时更新（只在页面可见时运行）
        this.startAutoUpdate();
    }

    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        this.isPageVisible = !document.hidden;
        
        if (this.isPageVisible) {
            console.log('[TrafficManager] 页面变为可见，恢复数据更新');
            // 页面重新可见时，立即更新数据并重启定时器
            this.updateAllTrafficData();
            this.startAutoUpdate();
        } else {
            console.log('[TrafficManager] 页面变为隐藏，暂停数据更新');
            // 页面隐藏时，停止定时器
            this.stopAutoUpdate();
        }
    }

    /**
     * 开始自动更新
     */
    startAutoUpdate() {
        // 清除旧的定时器
        this.stopAutoUpdate();
        
        // 只在页面可见时设置定时器
        if (this.isPageVisible) {
            this.updateTimer = setInterval(() => {
                if (this.isPageVisible) {
                    this.updateAllTrafficData();
                }
            }, this.updateInterval);
        }
    }

    /**
     * 停止自动更新
     */
    stopAutoUpdate() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
    }

    /**
     * 流量显示优化函数
     * 处理模板数据的格式化显示
     */
    updateTrafficDisplay() {
        try {
            // 获取所有服务器卡片
            document.querySelectorAll('.server-card').forEach(card => {
                const sid = card.dataset.sid;
                if (!sid) return;

                const trafficUsedElement = card.querySelector(`[id="${sid}_TRAFFIC"]`);
                const trafficBarElement = card.querySelector(`.traffic-bar[data-sid="${sid}"]`);
                const trafficUnlimitedText = card.querySelector(`.traffic-unlimited-text[data-sid="${sid}"]`);

                if (!trafficUsedElement) return;

                // 从data属性获取流量数据
                const trafficUsed = parseInt(trafficUsedElement.dataset.trafficUsed) || 0;
                const trafficLimit = parseInt(trafficUsedElement.dataset.trafficLimit) || 0;
                const isUnlimited = trafficLimit === 0;

                // 更新流量数值显示
                this.updateTrafficText(trafficUsedElement, trafficUsed, trafficLimit, isUnlimited);

                // 更新条形图显示
                this.updateTrafficBar(trafficBarElement, trafficUnlimitedText, trafficUsed, trafficLimit, isUnlimited);
            });
        } catch (error) {
            console.error('更新流量显示失败:', error);
        }
    }

    /**
     * 更新流量文字显示 - 分阶段智能显示策略
     */
    updateTrafficText(element, used, limit, isUnlimited) {
        // 直接更新外层元素，与其他指标保持一致的结构
        if (isUnlimited) {
            // 无限制流量：仅显示已用量
            const formattedUsed = this.formatTrafficSize(used);
            element.innerHTML = formattedUsed;
            
            // 设置悬停提示
            element.title = `本月已用 ${formattedUsed}`;
        } else {
            // 有限制流量：分阶段显示
            const percentage = limit > 0 ? Math.min(100, (used / limit) * 100) : 0;
            const remaining = Math.max(0, limit - used);
            
            let displayText = '';
            let tooltipText = '';
            
            if (percentage < 70) {
                // 轻松期：显示已用量
                const formattedUsed = this.formatTrafficSize(used);
                displayText = formattedUsed;
                tooltipText = `已用 ${formattedUsed} / ${this.formatTrafficSize(limit)} (${percentage.toFixed(1)}%)`;
            } else if (percentage < 90) {
                // 关注期：显示百分比
                displayText = `${Math.round(percentage)}%`;
                tooltipText = `已用 ${this.formatTrafficSize(used)} / ${this.formatTrafficSize(limit)}，剩 ${this.formatTrafficSize(remaining)}`;
            } else {
                // 警戒期：显示剩余量
                const formattedRemaining = this.formatTrafficSize(remaining);
                displayText = `剩 ${formattedRemaining}`;
                tooltipText = `已用 ${this.formatTrafficSize(used)} / ${this.formatTrafficSize(limit)} (${percentage.toFixed(1)}%)`;
            }
            
            element.innerHTML = displayText;
            element.title = tooltipText;
        }
    }

    /**
     * 更新条形图显示 - 支持无限流量虚线样式
     */
    updateTrafficBar(barElement, unlimitedTextElement, used, limit, isUnlimited) {
        if (!barElement) return;

        if (isUnlimited) {
            // 无限制流量：静态渐变 + ∞符号
            barElement.style.width = '100%';
            barElement.style.backgroundColor = 'transparent';
            barElement.style.backgroundImage = 'linear-gradient(90deg, #3b82f6, #8b5cf6, #3b82f6)';
            barElement.style.backgroundSize = 'auto';
            barElement.style.animation = 'none';
            
            if (unlimitedTextElement) {
                unlimitedTextElement.style.display = 'flex';
                unlimitedTextElement.style.color = '#ffffff';
                unlimitedTextElement.style.fontWeight = '600';
                unlimitedTextElement.style.textShadow = '0 1px 2px rgba(0,0,0,0.5)';
            }
        } else {
            // 有限制流量：实线进度条
            const percentage = limit > 0 ? Math.min(100, (used / limit) * 100) : 0;
            barElement.style.width = `${percentage}%`;
            barElement.style.backgroundImage = 'none';
            barElement.style.backgroundSize = 'auto';

            // 统一使用青色，不再根据使用率变色
            barElement.style.backgroundColor = '#06b6d4'; // cyan-500
            
            // 高使用率时添加脉冲动画提醒
            if (percentage >= 90) {
                barElement.style.animation = 'traffic-pulse 2s infinite';
            } else {
                barElement.style.animation = 'none';
            }

            if (unlimitedTextElement) {
                unlimitedTextElement.style.display = 'none';
            }
        }
    }

    /**
     * 流量单位格式化函数 - 优化显示宽度
     * 核心原则：最多显示3个字符的数字部分，减少文字占用
     * - 小于1000：显示当前单位（如 999B, 999KB）
     * - 大于等于1000：转换到下一单位（如 1000KB → 0.9MB）
     */
    formatTrafficSize(bytes) {
        if (!bytes || bytes === 0) return '0B';

        const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB'];
        const k = 1024;
        let size = Math.abs(bytes);
        let unitIndex = 0;

        // 找到合适的单位，使得数值在 0-999 范围内
        while (size >= 1000 && unitIndex < units.length - 1) {
            size /= k;
            unitIndex++;
        }
        
        // 特殊处理：如果转换后小于1，说明原值接近1000
        // 例如：1000B/1024 = 0.9765625KB
        if (size < 1 && unitIndex > 0) {
            // 已经升级了单位，保持在新单位
            // size 已经是正确的值了，不需要调整
        }

        // 格式化规则（确保数字部分不超过3个字符）：
        let formatted;
        
        if (size < 1) {
            // 特殊处理小于1的情况（如0.9765625）
            // 使用Math.floor确保0.97显示为0.9而不是四舍五入到1.0
            formatted = (Math.floor(size * 10) / 10).toFixed(1);
        } else if (size < 10) {
            // 1-9.9 范围：显示一位小数
            formatted = size.toFixed(1);
            // 移除无意义的 .0
            if (formatted.endsWith('.0')) {
                formatted = formatted.slice(0, -2);
            }
        } else if (size < 100) {
            // 10-99 范围：智能处理小数
            formatted = size.toFixed(1);
            // 如果总长度超过3，则不显示小数
            if (formatted.length > 3) {
                formatted = Math.round(size).toString();
            }
        } else {
            // 100-999 范围：不显示小数
            formatted = Math.round(size).toString();
        }

        return `${formatted}<span class="metric-unit">${units[unitIndex]}</span>`;
    }    /**
     * 月度流量数据更新函数
     * 用于实时更新流量数据
     */
    updateMonthlyTrafficDisplay(sid, monthlyData) {
        try {
            const card = document.querySelector(`.server-card[data-sid="${sid}"]`);
            if (!card) return;

            const trafficUsedElement = card.querySelector(`[id="${sid}_TRAFFIC"]`);
            const trafficBarElement = card.querySelector(`.traffic-bar[data-sid="${sid}"]`);
            const trafficUnlimitedText = card.querySelector(`.traffic-unlimited-text[data-sid="${sid}"]`);

            if (!trafficUsedElement) return;

            // 判断是否为无限流量：limit为0或undefined表示无限流量
            const isUnlimited = !monthlyData.limit || monthlyData.limit === 0;

            // 更新流量文字显示
            this.updateTrafficText(trafficUsedElement, monthlyData.used, monthlyData.limit, isUnlimited);

            // 更新条形图显示
            this.updateTrafficBar(trafficBarElement, trafficUnlimitedText, monthlyData.used, monthlyData.limit, isUnlimited);

            // console.log(`服务器 ${sid}: ${isUnlimited ? '无限流量' : '有限流量'}, 已用=${this.formatTrafficSize(monthlyData.used)}`);
        } catch (error) {
            console.error('更新月度流量显示失败:', error);
        }
    }

    /**
     * 获取并更新单个服务器的月度流量数据
     */
    async fetchAndUpdateTrafficForServer(sid) {
        try {
            console.log(`正在获取服务器 ${sid} 的流量数据...`);
            const response = await fetch(`/stats/${sid}/traffic`);

            if (!response.ok) {
                console.error(`API请求失败: ${response.status} ${response.statusText}`);
                return;
            }

            const result = await response.json();
            console.log(`服务器 ${sid} 流量数据:`, result);

            if (result.data && result.data.monthly) {
                console.log(`更新服务器 ${sid} 月度流量显示:`, result.data.monthly);
                this.updateMonthlyTrafficDisplay(sid, result.data.monthly);
            } else {
                console.warn(`服务器 ${sid} 没有月度流量数据`);
            }
        } catch (error) {
            console.error(`获取服务器 ${sid} 流量数据失败:`, error);
        }
    }    /**
     * 批量获取流量数据（使用新的批量API）
     */
    async loadBatchTrafficData(serverIds) {
        try {
            console.log(`[流量管理器] 批量获取 ${serverIds.length} 个服务器的流量数据...`);
            
            const response = await fetch('/stats/batch-traffic', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    serverIds: serverIds
                })
            });

            if (!response.ok) {
                console.error(`[流量管理器] 批量API请求失败: ${response.status} ${response.statusText}`);
                return null;
            }

            const result = await response.json();
            
            if (result.success) {
                console.log(`[流量管理器] 批量API成功，获取到 ${result.summary.serversWithData} 个服务器的数据`);
                
                // 缓存结果
                const cacheData = {
                    data: result.data,
                    timestamp: Date.now()
                };
                try {
                    sessionStorage.setItem(this.batchCacheKey, JSON.stringify(cacheData));
                } catch (e) {
                    console.warn('[流量管理器] 缓存数据失败:', e);
                }
                
                return result.data;
            } else {
                console.error('[流量管理器] 批量API返回错误:', result.message);
                return null;
            }
        } catch (error) {
            console.error('[流量管理器] 批量请求失败:', error);
            return null;
        }
    }

    /**
     * 检查系统版本和服务器列表变化，智能清理过期缓存
     */
    async checkAndClearStaleCache(serverIds) {
        try {
            // 生成当前服务器列表的哈希
            const currentHash = this.generateServerListHash(serverIds);
            const cachedHash = localStorage.getItem(this.serverListHashKey);
            
            // 获取系统启动时间作为版本标识
            const versionResponse = await fetch('/api/version');
            if (versionResponse.ok) {
                const versionData = await versionResponse.json();
                const currentVersion = versionData.uptime || Date.now();
                const cachedVersion = localStorage.getItem(this.systemVersionKey);
                
                // 检查版本变化或服务器列表变化
                if (cachedVersion !== currentVersion.toString() || cachedHash !== currentHash) {
                    console.log('[流量管理器] 检测到系统重启或服务器列表变化，清理缓存');
                    this.clearAllTrafficCache();
                    
                    // 更新缓存的版本和哈希
                    localStorage.setItem(this.systemVersionKey, currentVersion.toString());
                    localStorage.setItem(this.serverListHashKey, currentHash);
                    return true; // 表示已清理缓存
                }
            }
        } catch (error) {
            console.warn('[流量管理器] 版本检查失败，清理缓存:', error);
            this.clearAllTrafficCache();
            return true;
        }
        return false; // 未清理缓存
    }
    
    /**
     * 生成服务器列表的简单哈希
     */
    generateServerListHash(serverIds) {
        return serverIds.sort().join(',').length + '_' + serverIds.length;
    }
    
    /**
     * 清理所有流量相关缓存
     */
    clearAllTrafficCache() {
        try {
            sessionStorage.removeItem(this.batchCacheKey);
            console.log('[流量管理器] 已清理批量流量缓存');
        } catch (e) {
            console.warn('[流量管理器] 清理缓存失败:', e);
        }
    }

    /**
     * 批量更新所有服务器的流量数据
     */
    async updateAllTrafficData() {
        const cards = document.querySelectorAll('.server-card');
        const serverIds = Array.from(cards)
            .map(card => card.dataset.sid)
            .filter(Boolean);

        if (serverIds.length === 0) {
            console.log('[流量管理器] 没有找到需要更新的服务器');
            return;
        }
        
        // 智能缓存检查和清理
        const cacheCleared = await this.checkAndClearStaleCache(serverIds);
        
        if (cacheCleared) {
            console.log('[流量管理器] 缓存已清理，跳过缓存检查直接请求API');
        }

        // 检查缓存（只有在未清理缓存时才使用）
        if (this.useBatchAPI && !cacheCleared) {
            try {
                const cached = sessionStorage.getItem(this.batchCacheKey);
                if (cached) {
                    const cacheData = JSON.parse(cached);
                    if (Date.now() - cacheData.timestamp < this.batchCacheDuration) {
                        console.log('[流量管理器] 使用缓存的批量数据');
                        // 验证缓存中的服务器ID是否仍然有效
                        const cachedServerIds = Object.keys(cacheData.data);
                        const validCachedIds = cachedServerIds.filter(sid => serverIds.includes(sid));
                        
                        if (validCachedIds.length === serverIds.length) {
                            // 缓存完全匹配当前服务器列表
                            for (const [sid, data] of Object.entries(cacheData.data)) {
                                if (data && data.monthly && serverIds.includes(sid)) {
                                    this.updateMonthlyTrafficDisplay(sid, data.monthly);
                                }
                            }
                            return;
                        } else {
                            console.log('[流量管理器] 缓存与当前服务器列表不匹配，清理并重新获取');
                            this.clearAllTrafficCache();
                        }
                    }
                }
            } catch (e) {
                console.warn('[流量管理器] 读取缓存失败:', e);
                this.clearAllTrafficCache();
            }
        }

        // 尝试使用批量API
        if (this.useBatchAPI) {
            const batchData = await this.loadBatchTrafficData(serverIds);
            
            if (batchData) {
                // 批量更新成功，更新所有服务器的显示
                for (const [sid, data] of Object.entries(batchData)) {
                    if (data && data.monthly) {
                        this.updateMonthlyTrafficDisplay(sid, data.monthly);
                    }
                }
                return;
            } else {
                console.warn('[流量管理器] 批量API失败，回退到单个请求模式');
            }
        }

        // 回退到原有的单个请求模式
        console.log('[流量管理器] 使用单个请求模式更新流量数据');
        const promises = serverIds.map(sid => this.fetchAndUpdateTrafficForServer(sid));
        await Promise.all(promises);
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TrafficManager;
} else {
    // 浏览器环境下设置全局变量
    window.TrafficManager = TrafficManager;

    // 页面加载完成后自动初始化
    function initTrafficManager() {
        if (!window.trafficManager) {
            window.trafficManager = new TrafficManager();
            // 延迟执行更新，确保DOM完全加载
            setTimeout(() => {
                window.trafficManager.updateTrafficDisplay();
            }, 100);
        }
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTrafficManager);
    } else {
        // 如果页面已经加载完成，立即初始化
        initTrafficManager();
    }
}