/**
 * Admin表格管理器
 * 提供表格数据管理、排序、分页等功能
 */
class AdminTableManager {
    constructor(tableId, options = {}) {
        this.tableId = tableId;
        this.tbody = document.getElementById(`${tableId}-tbody`);
        this.mobileCards = document.getElementById(`${tableId}-mobile-cards`);
        this.currentPage = 1;
        this.pageSize = options.pageSize || 20;
        this.data = [];
        this.filteredData = [];
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.renderRow = options.renderRow || this.defaultRenderRow;
        this.renderCard = options.renderCard || this.defaultRenderCard;
        
        // 初始化事件监听
        this.initEventListeners();
    }
    
    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 全选复选框
        const selectAll = document.getElementById(`select-all-${this.tableId}`);
        if (selectAll) {
            selectAll.addEventListener('change', (e) => {
                const checkboxes = this.tbody.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(cb => cb.checked = e.target.checked);
            });
        }
        
        // 分页按钮
        const prevBtn = document.getElementById(`prev-page-${this.tableId}`);
        const nextBtn = document.getElementById(`next-page-${this.tableId}`);
        const prevBtnMobile = document.getElementById(`prev-page-mobile-${this.tableId}`);
        const nextBtnMobile = document.getElementById(`next-page-mobile-${this.tableId}`);
        
        if (prevBtn) prevBtn.addEventListener('click', () => this.prevPage());
        if (nextBtn) nextBtn.addEventListener('click', () => this.nextPage());
        if (prevBtnMobile) prevBtnMobile.addEventListener('click', () => this.prevPage());
        if (nextBtnMobile) nextBtnMobile.addEventListener('click', () => this.nextPage());
    }
    
    /**
     * 设置表格数据
     */
    setData(data) {
        this.data = data;
        this.filteredData = [...data];
        this.currentPage = 1;
        this.renderPage();
    }
    
    /**
     * 过滤数据
     */
    filterData(filterFn) {
        this.filteredData = this.data.filter(filterFn);
        this.currentPage = 1;
        this.renderPage();
    }
    
    /**
     * 排序表格
     */
    sortTable(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }
        
        this.filteredData.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];
            
            // 处理中文排序
            if (typeof aVal === 'string' && typeof bVal === 'string') {
                const result = aVal.localeCompare(bVal, 'zh-CN');
                return this.sortDirection === 'asc' ? result : -result;
            }
            
            // 数字排序
            if (aVal === bVal) return 0;
            const result = aVal > bVal ? 1 : -1;
            return this.sortDirection === 'asc' ? result : -result;
        });
        
        this.renderPage();
    }
    
    /**
     * 渲染当前页
     */
    renderPage() {
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        const pageData = this.filteredData.slice(start, end);
        
        // 清空表格和移动端卡片
        this.tbody.innerHTML = '';
        if (this.mobileCards) {
            this.mobileCards.innerHTML = '';
        }
        
        // 渲染数据
        if (pageData.length === 0) {
            this.renderEmptyState();
        } else {
            pageData.forEach((item, index) => {
                // 渲染桌面端表格行
                const row = this.renderRow(item, start + index);
                this.tbody.appendChild(row);
                
                // 渲染移动端卡片
                if (this.mobileCards) {
                    const card = this.renderCard(item, start + index);
                    this.mobileCards.appendChild(card);
                }
            });
        }
        
        // 更新分页信息
        this.updatePaginationInfo();
    }
    
    /**
     * 默认行渲染函数
     */
    defaultRenderRow(item, index) {
        const tr = document.createElement('tr');
        tr.innerHTML = `<td class="px-4 py-4 text-sm text-slate-500">请自定义renderRow函数</td>`;
        return tr;
    }
    
    /**
     * 默认卡片渲染函数
     */
    defaultRenderCard(item, index) {
        const card = document.createElement('div');
        card.className = 'bg-white dark:bg-slate-800 border border-slate-200/60 dark:border-slate-700/40 rounded-lg p-4 shadow-sm';
        card.innerHTML = `
            <div class="text-sm text-slate-500 dark:text-slate-400">请自定义renderCard函数</div>
        `;
        return card;
    }
    
    /**
     * 渲染空状态
     */
    renderEmptyState() {
        const tr = document.createElement('tr');
        const colCount = this.tbody.closest('table').querySelectorAll('thead th').length;
        tr.innerHTML = `
            <td colspan="${colCount}" class="px-6 py-12 text-center">
                <div class="text-gray-500">暂无数据</div>
            </td>
        `;
        this.tbody.appendChild(tr);
    }
    
    /**
     * 更新分页信息
     */
    updatePaginationInfo() {
        const totalRecords = this.filteredData.length;
        const start = Math.min((this.currentPage - 1) * this.pageSize + 1, totalRecords);
        const end = Math.min(this.currentPage * this.pageSize, totalRecords);
        
        // 更新显示信息
        const startEl = document.getElementById(`start-index-${this.tableId}`);
        const endEl = document.getElementById(`end-index-${this.tableId}`);
        const totalEl = document.getElementById(`total-records-${this.tableId}`);
        
        if (startEl) startEl.textContent = start;
        if (endEl) endEl.textContent = end;
        if (totalEl) totalEl.textContent = totalRecords;
        
        // 更新页码按钮
        this.updatePageNumbers();
        
        // 更新按钮状态
        this.updateButtonStates();
    }
    
    /**
     * 更新页码按钮
     */
    updatePageNumbers() {
        const container = document.getElementById(`page-numbers-${this.tableId}`);
        if (!container) return;
        
        const totalPages = Math.ceil(this.filteredData.length / this.pageSize);
        container.innerHTML = '';
        
        // 生成页码按钮
        const maxButtons = 5;
        let start = Math.max(1, this.currentPage - Math.floor(maxButtons / 2));
        let end = Math.min(totalPages, start + maxButtons - 1);
        
        if (end - start + 1 < maxButtons) {
            start = Math.max(1, end - maxButtons + 1);
        }
        
        for (let i = start; i <= end; i++) {
            const button = document.createElement('button');
            button.className = `relative inline-flex items-center px-4 py-2 text-sm font-medium ${
                i === this.currentPage
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            } border dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600`;
            button.textContent = i;
            button.onclick = () => this.goToPage(i);
            container.appendChild(button);
        }
    }
    
    /**
     * 更新按钮状态
     */
    updateButtonStates() {
        const totalPages = Math.ceil(this.filteredData.length / this.pageSize);
        const prevBtns = [
            document.getElementById(`prev-page-${this.tableId}`),
            document.getElementById(`prev-page-mobile-${this.tableId}`)
        ];
        const nextBtns = [
            document.getElementById(`next-page-${this.tableId}`),
            document.getElementById(`next-page-mobile-${this.tableId}`)
        ];
        
        prevBtns.forEach(btn => {
            if (btn) btn.disabled = this.currentPage === 1;
        });
        
        nextBtns.forEach(btn => {
            if (btn) btn.disabled = this.currentPage === totalPages || totalPages === 0;
        });
    }
    
    /**
     * 上一页
     */
    prevPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.renderPage();
        }
    }
    
    /**
     * 下一页
     */
    nextPage() {
        const totalPages = Math.ceil(this.filteredData.length / this.pageSize);
        if (this.currentPage < totalPages) {
            this.currentPage++;
            this.renderPage();
        }
    }
    
    /**
     * 跳转到指定页
     */
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredData.length / this.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderPage();
        }
    }
    
    /**
     * 获取选中的数据
     */
    getSelectedItems() {
        const checkboxes = this.tbody.querySelectorAll('input[type="checkbox"]:checked');
        const selectedIds = Array.from(checkboxes).map(cb => cb.value);
        return this.filteredData.filter(item => selectedIds.includes(String(item.id)));
    }
}

// 全局排序函数（兼容旧代码）
window.sortTable = function(column) {
    // 查找当前页面的表格管理器实例
    if (window.currentTableManager) {
        window.currentTableManager.sortTable(column);
    }
};