// AI Analytics Core - 核心管理器
(function() {
    'use strict';

    // 全局命名空间
    window.AIAnalytics = window.AIAnalytics || {};

    /**
     * AI分析核心管理器
     * 负责统一管理所有AI分析功能
     */
    class AIAnalyticsCore {
        constructor() {
            this.apiBase = '/api/analytics';
            this.initialized = false;
            this.components = new Map();
            this.currentReport = null;
            this.servers = [];
            
            // 默认配置
            this.config = {
                autoRefresh: true,
                refreshInterval: 300000, // 5分钟
                theme: 'default'
            };
        }

        /**
         * 初始化AI分析系统
         */
        async init() {
            try {
                console.log('[AI Analytics] 初始化开始...');
                
                // 检查权限
                await this.checkPermissions();
                
                // 加载服务器列表
                await this.loadServers();
                
                // 初始化主题
                this.initTheme();
                
                this.initialized = true;
                console.log('[AI Analytics] 初始化完成');
                
                return true;
            } catch (error) {
                console.error('[AI Analytics] 初始化失败:', error);
                throw error;
            }
        }

        /**
         * 检查AI分析权限
         */
        async checkPermissions() {
            try {
                // 添加超时控制，避免长时间等待
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时
                
                const response = await fetch(`${this.apiBase}/ai/test`, {
                    signal: controller.signal
                });
                clearTimeout(timeoutId);
                
                const result = await response.json();
                
                if (!result.success) {
                    throw new Error('AI服务不可用: ' + result.error);
                }
                
                console.log('[AI Analytics] 权限检查通过');
                return true;
            } catch (error) {
                if (error.name === 'AbortError') {
                    console.warn('[AI Analytics] 权限检查超时，假定服务可用');
                    return true; // 超时时假定服务可用，让用户可以继续使用
                }
                if (error.message.includes('403')) {
                    throw new Error('当前套餐不支持AI分析功能，请升级套餐');
                }
                throw error;
            }
        }

        /**
         * 加载服务器列表
         */
        async loadServers() {
            try {
                const response = await fetch(`${this.apiBase}/servers`);
                const result = await response.json();
                
                if (result.success) {
                    this.servers = result.data;
                    console.log(`[AI Analytics] 加载了 ${this.servers.length} 个服务器`);
                } else {
                    throw new Error(result.error || '加载服务器列表失败');
                }
            } catch (error) {
                console.error('[AI Analytics] 加载服务器失败:', error);
                throw error;
            }
        }

        /**
         * 生成AI分析报告
         */
        async generateReport(options = {}) {
            try {
                const requestData = {
                    timeRange: options.timeRange || this.getDefaultTimeRange(),
                    serverIds: options.serverIds || [],
                    includeData: options.includeData !== false
                };

                console.log('[AI Analytics] 开始生成AI报告...');
                
                const response = await fetch(`${this.apiBase}/ai/report`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();
                
                if (result.success) {
                    this.currentReport = result.data;
                    console.log('[AI Analytics] AI报告生成成功');
                    return result.data;
                } else {
                    throw new Error(result.error || 'AI报告生成失败');
                }
            } catch (error) {
                console.error('[AI Analytics] 生成报告失败:', error);
                throw error;
            }
        }

        /**
         * 执行异常检测
         */
        async detectAnomalies(serverId, options = {}) {
            try {
                const requestData = {
                    serverId,
                    startTime: options.startTime || Math.floor(Date.now() / 1000) - 86400,
                    endTime: options.endTime || Math.floor(Date.now() / 1000),
                    dataType: options.dataType || 'tcping',
                    algorithm: options.algorithm || 'threeSigma',
                    options: {
                        granularity: options.granularity || '5min'
                    }
                };

                console.log(`[AI Analytics] 开始异常检测: ${serverId}`);
                
                const response = await fetch(`${this.apiBase}/anomalies/detect`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();
                
                if (result.success) {
                    console.log(`[AI Analytics] 异常检测完成: 发现 ${result.data.summary.anomalyCount} 个异常`);
                    return result.data;
                } else {
                    throw new Error(result.error || '异常检测失败');
                }
            } catch (error) {
                console.error('[AI Analytics] 异常检测失败:', error);
                throw error;
            }
        }

        /**
         * 获取趋势分析数据
         */
        async getTrendAnalysis(serverId, options = {}) {
            try {
                const params = new URLSearchParams({
                    startTime: options.startTime || Math.floor(Date.now() / 1000) - 86400,
                    endTime: options.endTime || Math.floor(Date.now() / 1000),
                    dataType: options.dataType || 'tcping',
                    algorithm: options.algorithm || 'simple',
                    windowSize: options.windowSize || 5,
                    granularity: options.granularity || '5min'
                });

                const response = await fetch(`${this.apiBase}/trends/${serverId}?${params}`);
                const result = await response.json();
                
                if (result.success) {
                    return result.data;
                } else {
                    throw new Error(result.error || '获取趋势分析失败');
                }
            } catch (error) {
                console.error('[AI Analytics] 获取趋势分析失败:', error);
                throw error;
            }
        }

        /**
         * 注册组件
         */
        registerComponent(name, component) {
            this.components.set(name, component);
            console.log(`[AI Analytics] 注册组件: ${name}`);
        }

        /**
         * 获取组件
         */
        getComponent(name) {
            return this.components.get(name);
        }

        /**
         * 广播事件到所有组件
         */
        broadcast(event, data) {
            this.components.forEach((component, name) => {
                if (component.onEvent && typeof component.onEvent === 'function') {
                    try {
                        component.onEvent(event, data);
                    } catch (error) {
                        console.error(`[AI Analytics] 组件 ${name} 处理事件 ${event} 失败:`, error);
                    }
                }
            });
        }

        /**
         * 获取默认时间范围
         */
        getDefaultTimeRange() {
            const now = Math.floor(Date.now() / 1000);
            return {
                start: now - 86400, // 24小时前
                end: now
            };
        }

        /**
         * 初始化主题
         */
        initTheme() {
            // 检测当前主题
            const isDark = document.body.classList.contains('dark-theme') || 
                          document.documentElement.getAttribute('data-theme') === 'dark';
            
            this.config.theme = isDark ? 'dark' : 'default';
            console.log(`[AI Analytics] 主题设置: ${this.config.theme}`);
        }

        /**
         * 显示通知
         */
        showNotification(message, type = 'info', duration = 3000) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `ai-notification ai-notification-${type}`;
            notification.innerHTML = `
                <div class="ai-notification-content">
                    <i class="ai-notification-icon ti ${this.getNotificationIcon(type)}">
                    </i>
                    <span class="ai-notification-text">${message}</span>
                    <button class="ai-notification-close" onclick="this.parentElement.parentElement.remove()">
                        <i class="ti ti-x"></i>
                    </button>
                </div>
            `;

            // 添加到页面
            document.body.appendChild(notification);

            // 自动移除
            if (duration > 0) {
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, duration);
            }
        }

        /**
         * 获取通知图标
         */
        getNotificationIcon(type) {
            const icons = {
                info: 'ti-info-circle',
                success: 'ti-circle-check',
                warning: 'ti-alert-triangle',
                error: 'ti-alert-circle'
            };
            return icons[type] || 'ti-info-circle';
        }

        /**
         * 显示加载状态
         */
        showLoading(container, message = '正在加载...') {
            if (typeof container === 'string') {
                container = document.getElementById(container);
            }
            
            if (container) {
                container.innerHTML = `
                    <div class="ai-loading">
                        <div class="ai-loading-spinner"></div>
                        <div class="ai-loading-text">${message}</div>
                    </div>
                `;
            }
        }

        /**
         * 格式化数值
         */
        formatValue(value, type = 'number', precision = 2) {
            if (value === null || value === undefined) {
                return 'N/A';
            }

            switch (type) {
                case 'percentage':
                    return `${(value * 100).toFixed(precision)}%`;
                case 'bytes':
                    return this.formatBytes(value);
                case 'duration':
                    return this.formatDuration(value);
                default:
                    return Number(value).toFixed(precision);
            }
        }

        /**
         * 格式化字节数
         */
        formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        /**
         * 格式化时长
         */
        formatDuration(seconds) {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            
            if (days > 0) return `${days}天${hours}小时`;
            if (hours > 0) return `${hours}小时${minutes}分钟`;
            return `${minutes}分钟`;
        }

        /**
         * 销毁AI分析系统
         */
        destroy() {
            // 销毁所有组件
            this.components.forEach((component, name) => {
                if (component.destroy && typeof component.destroy === 'function') {
                    try {
                        component.destroy();
                    } catch (error) {
                        console.error(`[AI Analytics] 销毁组件 ${name} 失败:`, error);
                    }
                }
            });
            
            this.components.clear();
            this.currentReport = null;
            this.initialized = false;
            
            console.log('[AI Analytics] 系统已销毁');
        }
    }

    // 创建全局实例
    window.AIAnalytics.core = new AIAnalyticsCore();

    console.log('[AI Analytics Core] 核心模块已加载');

})();