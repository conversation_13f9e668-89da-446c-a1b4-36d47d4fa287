// AI Dashboard - AI分析仪表板组件
(function() {
    'use strict';

    // 确保命名空间存在
    window.AIAnalytics = window.AIAnalytics || {};

    /**
     * AI分析仪表板
     * 提供AI分析的总览界面
     */
    class AIDashboard {
        constructor(containerId) {
            this.containerId = containerId;
            this.container = null;
            this.charts = new Map();
            this.refreshTimer = null;
            
            // 状态
            this.currentReport = null;
            this.selectedServers = [];
            this.timeRange = this.getDefaultTimeRange();
            
            // 绑定方法
            this.refresh = this.refresh.bind(this);
            this.destroy = this.destroy.bind(this);
        }

        /**
         * 初始化仪表板
         */
        async init() {
            try {
                console.log('[AI Dashboard] 初始化开始...');
                
                // 获取容器
                this.container = document.getElementById(this.containerId);
                if (!this.container) {
                    throw new Error(`找不到容器: ${this.containerId}`);
                }

                // 创建UI
                this.createUI();
                
                // 设置事件监听
                this.setupEventListeners();
                
                // 注册到核心管理器
                window.AIAnalytics.core.registerComponent('dashboard', this);
                
                // 初始加载
                await this.refresh();
                
                console.log('[AI Dashboard] 初始化完成');
            } catch (error) {
                console.error('[AI Dashboard] 初始化失败:', error);
                this.showError(error.message);
            }
        }

        /**
         * 创建UI界面
         */
        createUI() {
            this.container.innerHTML = `
                <div class="ai-dashboard">
                    <!-- 头部控制面板 -->
                    <div class="ai-dashboard-header">
                        <div class="ai-dashboard-title">
                            <h2><i class="ti ti-brain"></i> AI智能分析</h2>
                            <p>基于人工智能的服务器监控数据分析与预测</p>
                        </div>
                        <div class="ai-dashboard-controls">
                            <div class="control-group">
                                <label>分析时间范围:</label>
                                <select id="ai-timerange-selector" class="form-control">
                                    <option value="1h">最近1小时</option>
                                    <option value="6h">最近6小时</option>
                                    <option value="24h" selected>最近24小时</option>
                                    <option value="7d">最近7天</option>
                                    <option value="30d">最近30天</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <button id="ai-generate-report" class="btn btn-primary">
                                    <i class="ti ti-sparkles"></i>
                                    生成AI报告
                                </button>
                                <button id="ai-refresh" class="btn btn-secondary">
                                    <i class="ti ti-refresh"></i>
                                    刷新
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 主要内容区域 -->
                    <div class="ai-dashboard-content">
                        <!-- 概览卡片 -->
                        <div class="ai-overview-cards">
                            <div class="ai-card">
                                <div class="ai-card-header">
                                    <h3>系统健康度</h3>
                                    <i class="ti ti-heart"></i>
                                </div>
                                <div class="ai-card-content">
                                    <div id="health-score" class="ai-metric-large">--</div>
                                    <div class="ai-metric-label">总体评分</div>
                                    <div id="health-details" class="ai-metric-details">等待分析...</div>
                                </div>
                            </div>

                            <div class="ai-card">
                                <div class="ai-card-header">
                                    <h3>监控服务器</h3>
                                    <i class="ti ti-server"></i>
                                </div>
                                <div class="ai-card-content">
                                    <div id="server-count" class="ai-metric-large">--</div>
                                    <div class="ai-metric-label">总服务器数</div>
                                    <div id="server-status" class="ai-metric-details">
                                        <span class="status-item">健康: <span id="healthy-count">--</span></span>
                                        <span class="status-item">警告: <span id="warning-count">--</span></span>
                                        <span class="status-item">异常: <span id="critical-count">--</span></span>
                                    </div>
                                </div>
                            </div>

                            <div class="ai-card">
                                <div class="ai-card-header">
                                    <h3>AI分析状态</h3>
                                    <i class="ti ti-robot"></i>
                                </div>
                                <div class="ai-card-content">
                                    <div id="ai-status" class="ai-metric-large">就绪</div>
                                    <div class="ai-metric-label">服务状态</div>
                                    <div id="last-analysis" class="ai-metric-details">未进行分析</div>
                                </div>
                            </div>

                            <div class="ai-card">
                                <div class="ai-card-header">
                                    <h3>实时警报</h3>
                                    <i class="ti ti-alert-triangle"></i>
                                </div>
                                <div class="ai-card-content">
                                    <div id="alert-count" class="ai-metric-large">--</div>
                                    <div class="ai-metric-label">活跃警报</div>
                                    <div id="alert-details" class="ai-metric-details">等待数据...</div>
                                </div>
                            </div>
                        </div>

                        <!-- AI分析结果 -->
                        <div class="ai-analysis-section">
                            <div class="ai-section-header">
                                <h3><i class="ti ti-chart-bar"></i> AI分析结果</h3>
                                <div class="ai-section-controls">
                                    <button id="export-report" class="btn btn-outline" disabled>
                                        <i class="ti ti-download"></i>
                                        导出报告
                                    </button>
                                </div>
                            </div>
                            
                            <div id="ai-analysis-content" class="ai-analysis-content">
                                <div class="ai-analysis-placeholder">
                                    <i class="ti ti-brain"></i>
                                    <h4>准备开始AI分析</h4>
                                    <p>点击"生成AI报告"开始智能分析您的服务器监控数据</p>
                                    <button class="btn btn-primary btn-large" onclick="document.getElementById('ai-generate-report').click()">
                                        <i class="ti ti-sparkles"></i>
                                        开始AI分析
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 推荐操作 -->
                        <div class="ai-recommendations-section">
                            <div class="ai-section-header">
                                <h3><i class="ti ti-bulb"></i> AI推荐操作</h3>
                            </div>
                            <div id="ai-recommendations" class="ai-recommendations">
                                <div class="ai-recommendations-placeholder">
                                    <p>AI分析完成后，这里将显示智能推荐的优化建议</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 状态指示器 -->
                    <div id="ai-status-indicator" class="ai-status-indicator"></div>
                </div>
            `;
        }

        /**
         * 设置事件监听
         */
        setupEventListeners() {
            // 时间范围选择
            document.getElementById('ai-timerange-selector').addEventListener('change', (e) => {
                this.updateTimeRange(e.target.value);
            });

            // 生成报告按钮
            document.getElementById('ai-generate-report').addEventListener('click', () => {
                this.generateReport();
            });

            // 刷新按钮
            document.getElementById('ai-refresh').addEventListener('click', () => {
                this.refresh();
            });

            // 导出报告按钮
            document.getElementById('export-report').addEventListener('click', () => {
                this.exportReport();
            });
        }

        /**
         * 更新时间范围
         */
        updateTimeRange(range) {
            const now = Math.floor(Date.now() / 1000);
            const ranges = {
                '1h': 3600,
                '6h': 21600,
                '24h': 86400,
                '7d': 604800,
                '30d': 2592000
            };

            const duration = ranges[range] || 86400;
            this.timeRange = {
                start: now - duration,
                end: now
            };

            console.log(`[AI Dashboard] 时间范围更新: ${range}`);
        }

        /**
         * 刷新仪表板
         */
        async refresh() {
            try {
                this.showStatus('正在刷新数据...', 'info');
                
                // 更新基础信息
                await this.updateBasicInfo();
                
                // 如果有当前报告，重新生成
                if (this.currentReport) {
                    await this.generateReport();
                }
                
                this.showStatus('数据已更新', 'success');
            } catch (error) {
                console.error('[AI Dashboard] 刷新失败:', error);
                this.showStatus('刷新失败: ' + error.message, 'error');
            }
        }

        /**
         * 更新基础信息
         */
        async updateBasicInfo() {
            const core = window.AIAnalytics.core;
            
            // 更新服务器数量
            document.getElementById('server-count').textContent = core.servers.length;
            
            // 检查AI服务状态
            try {
                await core.checkPermissions();
                document.getElementById('ai-status').textContent = '在线';
                document.getElementById('ai-status').className = 'ai-metric-large status-online';
            } catch (error) {
                document.getElementById('ai-status').textContent = '离线';
                document.getElementById('ai-status').className = 'ai-metric-large status-offline';
            }
        }

        /**
         * 生成AI报告
         */
        async generateReport() {
            try {
                this.showStatus('正在生成AI分析报告...', 'info');
                
                // 禁用生成按钮
                const generateBtn = document.getElementById('ai-generate-report');
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="ti ti-hourglass-empty"></i> 分析中...';
                
                // 显示加载状态
                const contentArea = document.getElementById('ai-analysis-content');
                window.AIAnalytics.core.showLoading(contentArea, '正在进行AI分析，请稍候...');
                
                // 调用AI报告生成
                const report = await window.AIAnalytics.core.generateReport({
                    timeRange: this.timeRange,
                    includeData: true
                });
                
                this.currentReport = report;
                
                // 更新UI
                this.updateReportDisplay(report);
                this.updateRecommendations(report.recommendations);
                this.updateOverviewCards(report.summary);
                
                // 启用导出按钮
                document.getElementById('export-report').disabled = false;
                
                this.showStatus('AI分析报告生成完成', 'success');
                
            } catch (error) {
                console.error('[AI Dashboard] 生成报告失败:', error);
                this.showStatus('生成报告失败: ' + error.message, 'error');
                
                // 显示错误状态
                const contentArea = document.getElementById('ai-analysis-content');
                contentArea.innerHTML = `
                    <div class="ai-error-state">
                        <i class="ti ti-alert-circle"></i>
                        <h4>AI分析失败</h4>
                        <p>${error.message}</p>
                        <button class="btn btn-primary" onclick="document.getElementById('ai-generate-report').click()">
                            重试
                        </button>
                    </div>
                `;
            } finally {
                // 恢复生成按钮
                const generateBtn = document.getElementById('ai-generate-report');
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="ti ti-sparkles"></i> 生成AI报告';
            }
        }

        /**
         * 更新报告显示
         */
        updateReportDisplay(report) {
            const contentArea = document.getElementById('ai-analysis-content');
            
            contentArea.innerHTML = `
                <div class="ai-report-summary">
                    <div class="ai-report-grid">
                        <div class="ai-report-item">
                            <h4>流量分析</h4>
                            <div class="ai-analysis-result">
                                <div class="result-metric">
                                    <span class="metric-label">总流量使用:</span>
                                    <span class="metric-value">${report.trafficAnalysis.totalTrafficUsed}</span>
                                </div>
                                <div class="result-metric">
                                    <span class="metric-label">平均使用率:</span>
                                    <span class="metric-value">${report.trafficAnalysis.averageUsage}</span>
                                </div>
                                ${report.trafficAnalysis.warnings.length > 0 ? `
                                    <div class="result-warnings">
                                        <strong>⚠️ ${report.trafficAnalysis.warnings.length} 个流量警告</strong>
                                    </div>
                                ` : ''}
                            </div>
                        </div>

                        <div class="ai-report-item">
                            <h4>网络质量</h4>
                            <div class="ai-analysis-result">
                                <div class="result-metric">
                                    <span class="metric-label">平均延迟:</span>
                                    <span class="metric-value">${report.networkQuality.averageLatency}ms</span>
                                </div>
                                <div class="result-metric">
                                    <span class="metric-label">质量评分:</span>
                                    <span class="metric-value score-${this.getScoreClass(report.networkQuality.qualityScore)}">
                                        ${report.networkQuality.qualityScore}/100
                                    </span>
                                </div>
                                ${report.networkQuality.issueServers.length > 0 ? `
                                    <div class="result-warnings">
                                        <strong>🔴 ${report.networkQuality.issueServers.length} 个服务器有网络问题</strong>
                                    </div>
                                ` : ''}
                            </div>
                        </div>

                        <div class="ai-report-item">
                            <h4>磁盘容量</h4>
                            <div class="ai-analysis-result">
                                ${report.diskWarnings.length > 0 ? `
                                    <div class="result-warnings">
                                        <strong>💾 ${report.diskWarnings.length} 个磁盘容量警告</strong>
                                        ${report.diskWarnings.slice(0, 3).map(warning => `
                                            <div class="warning-item">
                                                ${warning.serverName}: ${warning.currentUsage}
                                            </div>
                                        `).join('')}
                                    </div>
                                ` : `
                                    <div class="result-ok">
                                        <i class="ti ti-circle-check"></i>
                                        所有服务器磁盘容量正常
                                    </div>
                                `}
                            </div>
                        </div>

                        <div class="ai-report-item">
                            <h4>负载分析</h4>
                            <div class="ai-analysis-result">
                                <div class="result-metric">
                                    <span class="metric-label">平均CPU使用率:</span>
                                    <span class="metric-value">${report.loadAnalysis.averageCpuUsage}%</span>
                                </div>
                                <div class="result-metric">
                                    <span class="metric-label">平均内存使用率:</span>
                                    <span class="metric-value">${report.loadAnalysis.averageMemoryUsage}%</span>
                                </div>
                                ${report.loadAnalysis.highLoadServers.length > 0 ? `
                                    <div class="result-warnings">
                                        <strong>⚡ ${report.loadAnalysis.highLoadServers.length} 个高负载服务器</strong>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        /**
         * 更新推荐操作
         */
        updateRecommendations(recommendations) {
            const container = document.getElementById('ai-recommendations');
            
            if (!recommendations || recommendations.length === 0) {
                container.innerHTML = `
                    <div class="ai-recommendations-empty">
                        <i class="ti ti-circle-check"></i>
                        <p>暂无需要关注的问题，系统运行良好</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = recommendations.map((rec, index) => `
                <div class="ai-recommendation-item priority-${rec.priority}">
                    <div class="recommendation-header">
                        <div class="recommendation-icon">
                            <i class="ti ${this.getRecommendationIcon(rec.type)}"></i>
                        </div>
                        <div class="recommendation-title">
                            <h4>${rec.title}</h4>
                            <span class="recommendation-priority">${this.formatPriority(rec.priority)}</span>
                        </div>
                    </div>
                    <div class="recommendation-content">
                        <p class="recommendation-description">${rec.description}</p>
                        <div class="recommendation-action">
                            <strong>建议操作:</strong> ${rec.action}
                        </div>
                        <div class="recommendation-impact">
                            <strong>预期效果:</strong> ${rec.estimatedImpact}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        /**
         * 更新概览卡片
         */
        updateOverviewCards(summary) {
            // 健康度评分
            document.getElementById('health-score').textContent = summary.overallScore;
            document.getElementById('health-score').className = `ai-metric-large score-${this.getScoreClass(summary.overallScore)}`;
            
            // 健康度详情
            const healthDetails = document.getElementById('health-details');
            healthDetails.innerHTML = `
                <span class="status-item">健康: ${summary.healthyServers}</span>
                <span class="status-item">警告: ${summary.warningServers}</span>
                <span class="status-item">异常: ${summary.criticalServers}</span>
            `;

            // 更新状态计数
            document.getElementById('healthy-count').textContent = summary.healthyServers;
            document.getElementById('warning-count').textContent = summary.warningServers;
            document.getElementById('critical-count').textContent = summary.criticalServers;

            // 更新最后分析时间
            document.getElementById('last-analysis').textContent = '刚刚完成分析';
            
            // 更新警报数量（基于推荐中的critical和high优先级项目）
            const alertCount = this.currentReport ? 
                this.currentReport.recommendations.filter(r => r.priority === 'critical' || r.priority === 'high').length : 0;
            document.getElementById('alert-count').textContent = alertCount;
            document.getElementById('alert-details').textContent = alertCount > 0 ? `${alertCount} 个高优先级问题` : '无活跃警报';
        }

        /**
         * 导出报告
         */
        exportReport() {
            if (!this.currentReport) {
                this.showStatus('没有可导出的报告', 'warning');
                return;
            }

            try {
                const exportData = {
                    metadata: {
                        exportTime: new Date().toISOString(),
                        timeRange: this.timeRange,
                        version: '1.0'
                    },
                    report: this.currentReport
                };

                const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                    type: 'application/json'
                });
                
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `ai-analysis-report-${Date.now()}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.showStatus('报告已导出', 'success');
            } catch (error) {
                console.error('[AI Dashboard] 导出失败:', error);
                this.showStatus('导出失败: ' + error.message, 'error');
            }
        }

        /**
         * 获取评分等级类名
         */
        getScoreClass(score) {
            if (score >= 80) return 'excellent';
            if (score >= 60) return 'good';
            if (score >= 40) return 'warning';
            return 'critical';
        }

        /**
         * 获取推荐图标
         */
        getRecommendationIcon(type) {
            const icons = {
                optimization: 'ti-adjustments',
                warning: 'ti-alert-triangle',
                maintenance: 'ti-tool',
                security: 'ti-shield-check',
                performance: 'ti-gauge',
                error: 'ti-alert-circle'
            };
            return icons[type] || 'ti-info-circle';
        }

        /**
         * 格式化优先级
         */
        formatPriority(priority) {
            const labels = {
                low: '低优先级',
                medium: '中优先级',
                high: '高优先级',
                critical: '紧急'
            };
            return labels[priority] || priority;
        }

        /**
         * 获取默认时间范围
         */
        getDefaultTimeRange() {
            const now = Math.floor(Date.now() / 1000);
            return {
                start: now - 86400, // 24小时前
                end: now
            };
        }

        /**
         * 显示状态
         */
        showStatus(message, type = 'info') {
            window.AIAnalytics.core.showNotification(message, type);
        }

        /**
         * 显示错误
         */
        showError(message) {
            this.showStatus(message, 'error');
        }

        /**
         * 事件处理
         */
        onEvent(event, data) {
            switch (event) {
                case 'refresh':
                    this.refresh();
                    break;
                case 'theme-changed':
                    // 主题变更处理
                    break;
            }
        }

        /**
         * 销毁仪表板
         */
        destroy() {
            if (this.refreshTimer) {
                clearInterval(this.refreshTimer);
                this.refreshTimer = null;
            }

            this.charts.forEach(chart => {
                if (chart.destroy) {
                    chart.destroy();
                }
            });
            this.charts.clear();

            if (this.container) {
                this.container.innerHTML = '';
            }

            console.log('[AI Dashboard] 已销毁');
        }
    }

    // 导出到命名空间
    window.AIAnalytics.Dashboard = AIDashboard;

    console.log('[AI Dashboard] 仪表板组件已加载');

})();