/*
 * OsIcons: Streamline OS Logos Pack integration (colored SVG, self-hosted)
 * Usage:
 *   const el = OsIcons.create(platformInfo, { size: 20, className: 'align-middle' });
 *   container.appendChild(el);
 *
 * Expectations:
 *   Place SVGs under: /img/os-logos/streamline/{slug}.svg
 *   Example slugs: ubuntu, debian, redhat, centos, fedora, opensuse, archlinux,
 *                  alpinelinux, rockylinux, almalinux, linuxmint, windows, apple,
 *                  android, docker, freebsd, openbsd, linux
 */
(function (global) {
  const BASE = '/img/os-logos/streamline';
  const map = [
    { slug: 'ubuntu', keywords: ['ubuntu'] },
    { slug: 'debian', keywords: ['debian'] },
    { slug: 'redhat', keywords: ['red hat','redhat','rhel'] },
    { slug: 'centos', keywords: ['centos'] },
    { slug: 'fedora', keywords: ['fedora'] },
    { slug: 'opensuse', keywords: ['opensuse','suse'] },
    { slug: 'archlinux', keywords: ['arch linux','arch'] },
    { slug: 'alpinelinux', keywords: ['alpine linux','alpine'] },
    { slug: 'rockylinux', keywords: ['rocky linux','rocky'] },
    { slug: 'almalinux', keywords: ['alma linux','alma'] },
    { slug: 'linuxmint', keywords: ['linux mint','mint'] },
    { slug: 'freebsd', keywords: ['freebsd'] },
    { slug: 'openbsd', keywords: ['openbsd'] },
    { slug: 'windows', keywords: ['windows server','windows'] },
    { slug: 'apple', keywords: ['darwin','macos','mac','osx','apple'] },
    { slug: 'android', keywords: ['android'] },
    { slug: 'docker', keywords: ['docker'] },
    { slug: 'linux', keywords: ['linux'] }
  ];

  function detectSlug(platformInfo) {
    if (!platformInfo) return null;
    const s = String(platformInfo).toLowerCase();
    for (const item of map) {
      if (item.keywords.some(k => s.includes(k))) return item.slug;
    }
    return null;
  }

  function imgElement(slug, opts) {
    const img = document.createElement('img');
    img.className = 'os-icon-img' + (opts?.className ? (' ' + opts.className) : '');
    img.width = opts?.size || 18;
    img.height = opts?.size || 18;
    img.loading = 'lazy';
    img.decoding = 'async';
    img.alt = opts?.alt || slug || 'os';
    img.src = `${BASE}/${slug}.svg`;
    // 无回退：若缺失则隐藏，避免占位错乱
    img.onerror = function () { this.style.display = 'none'; };
    return img;
  }

  function create(platformInfo, opts) {
    const slug = detectSlug(platformInfo);
    if (!slug) return null;
    const el = imgElement(slug, opts || {});
    if (opts && opts.title) el.title = opts.title;
    return el;
  }

  function styleOnce() {
    if (document.getElementById('os-icons-style')) return;
    const s = document.createElement('style');
    s.id = 'os-icons-style';
    s.textContent = '.os-icon-img{display:inline-block;vertical-align:middle;width:1em;height:1em;image-rendering:auto;}';
    document.head.appendChild(s);
  }

  function init() { styleOnce(); }
  if (document.readyState === 'loading') document.addEventListener('DOMContentLoaded', init); else init();

  global.OsIcons = { create, detectSlug, base: BASE };
})(window);
