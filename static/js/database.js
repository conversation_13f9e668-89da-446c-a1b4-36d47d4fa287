/**
 * 数据库管理模块
 * 提供数据库备份和恢复功能
 */
window.DatabaseManager = {
    /**
     * 下载数据库备份
     */
    downloadBackup() {
        window.location.href = '/admin/db/backup';
    },

    /**
     * 开始恢复流程
     */
    startRestore() {
        document.getElementById('dbFileInput').click();
    },

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化的文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 处理文件选择
     * @param {HTMLInputElement} input - 文件输入元素
     */
    handleFileSelect(input) {
        if (!input.files || !input.files[0]) return;
        
        const file = input.files[0];
        if (!file.name.endsWith('.db')) {
            this.showError('请选择正确的数据库文件（.db）');
            return;
        }
        
        const MAX_FILE_SIZE = 500 * 1024 * 1024; // 500MB
        if (file.size > MAX_FILE_SIZE) {
            const fileSize = this.formatFileSize(file.size);
            const maxSize = this.formatFileSize(MAX_FILE_SIZE);
            this.showError(`文件太大（${fileSize}），最大支持 ${maxSize}`);
            return;
        }
        
        if (file.size === 0) {
            this.showError('上传的文件为空');
            return;
        }

        console.log(`准备恢复数据库文件: ${file.name} (${this.formatFileSize(file.size)})`);
        this.showDialog();
        this.handleRestore(file);
    },

    /**
     * 处理数据库恢复
     * @param {File} file - 数据库文件
     */
    async handleRestore(file) {
        try {
            const formData = new FormData();
            formData.append('database', file);

            this.showState('upload');
            this.updateProgressMessage('正在上传数据库文件...');
            
            // 创建超时控制器 - 30分钟超时
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30 * 60 * 1000);
            
            const response = await fetch('/admin/db/restore', {
                method: 'POST',
                body: formData,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            this.updateProgressMessage('正在处理数据库恢复...');
            
            if (!response.ok) {
                if (response.status === 502) {
                    throw new Error('服务器网关错误 (502) - 可能是超时或服务器过载，请稍后重试');
                }
                if (response.status === 524) {
                    // 常见于 Cloudflare/代理层等待原站超时
                    throw new Error('HTTP 524: 代理层等待原站超时（可能为 Cloudflare/Nginx/负载均衡超时）');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();

            if (result.status) {
                // 显示恢复成功状态
                this.showState('success');
                
                // 检查是否为热恢复（无需重启）
                if (result.data && result.data.includes('热恢复成功')) {
                    // 热恢复成功，无需重启
                    const hotRestoreMessage = `
                        <div class="space-y-3">
                            <p class="text-green-600 dark:text-green-400 font-semibold">
                                🎉 ${result.data}
                            </p>
                            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 p-4 rounded-lg">
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                                            无需重启系统
                                        </h3>
                                        <p class="mt-1 text-sm text-green-700 dark:text-green-300">
                                            数据库已成功热恢复，所有更改立即生效。您可以继续使用系统，无需任何额外操作。
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                <p>✅ 数据已恢复</p>
                                <p>✅ 服务持续运行</p>
                                <p>✅ 连接保持稳定</p>
                            </div>
                        </div>
                    `;
                    
                    this.updateRestartStatus(hotRestoreMessage);
                } else {
                    // 传统恢复，需要重启
                    const restartMessage = `
                        <div class="space-y-3">
                            <p>数据库恢复成功！</p>
                            <p>请按照以下方式手动重启系统：</p>
                            
                            <div class="space-y-2">
                                <p>1. 如果使用PM2：</p>
                                <pre class="bg-slate-800 px-3 py-2 rounded font-mono text-sm">pm2 restart nekonekostatus</pre>
                            </div>
                            
                            <div class="space-y-2">
                                <p>2. 如果使用Forever：</p>
                                <pre class="bg-slate-800 px-3 py-2 rounded font-mono text-sm">forever restart nekonekostatus.js</pre>
                            </div>
                            
                            <div class="space-y-2">
                                <p>3. 如果使用Docker：</p>
                                <pre class="bg-slate-800 px-3 py-2 rounded font-mono text-sm">docker restart dstatus</pre>
                            </div>
                            
                            <div class="space-y-2">
                                <p>4. 其他情况：</p>
                                <p class="text-sm">请直接重启应用</p>
                            </div>
                            
                            <p class="text-yellow-400 mt-4">重启后系统将加载新的数据库内容。</p>
                        </div>
                    `;
                    
                    this.updateRestartStatus(restartMessage);
                }
            } else {
                this.showError(result.data);
            }
        } catch (error) {
            console.error('恢复过程出错:', error);
            
            if (error.name === 'AbortError') {
                this.showError('恢复超时（30分钟），请检查数据库文件大小或网络连接');
            } else if (error.message.includes('502')) {
                this.showError('恢复请求失败 - 服务器可能正在处理大文件，请耐心等待或稍后重试');
            } else if (error.message.includes('524')) {
                this.showError('恢复请求在代理层超时（HTTP 524）。建议：\n\n1) 暂时绕过 CDN/代理或使用源站地址访问后台；\n2) 提升代理超时（如 Nginx 的 proxy_read_timeout/ send_timeout/ client_body_timeout ≥ 1800s）；\n3) 使用更稳定的网络环境或缩小备份体积后重试。');
            } else if (error.message === 'Failed to fetch' || ('' + error).includes('Failed to fetch')) {
                // 浏览器在上传过程中检测到底层文件发生变化（如 Chrome 的 net::ERR_UPLOAD_FILE_CHANGED）或网络被中断
                this.showError('浏览器中断了上传（可能原因：所选文件在上传过程中被修改、移动或被同步工具占用；或网络被中断）。\n\n如果开发者工具出现 net::ERR_UPLOAD_FILE_CHANGED：\n- 请确保 .db 文件在上传期间不会被修改/复制/同步；\n- 将文件复制到本地磁盘非同步目录（如桌面）并重命名后再试；\n- 关闭占用该文件的程序（备份/同步/杀毒），或重新导出稳定的备份文件；\n- 也可尝试使用不同浏览器或无痕模式重试。');
            } else {
                this.showError('恢复过程出错: ' + error.message);
            }

            // 失败后重置文件选择，避免同一文件受缓存/引用影响无法重新触发上传
            try {
                const input = document.getElementById('dbFileInput');
                if (input) input.value = '';
            } catch (e) {
                // ignore
            }
        }
    },

    /**
     * 更新重启状态显示
     */
    updateRestartStatus(message) {
        const messageElement = document.getElementById('restartMessage');
        if (messageElement) {
            messageElement.innerHTML = message;
        }
    },

    /**
     * 显示对话框
     */
    showDialog() {
        const dialog = document.getElementById('restoreDialog');
        dialog.classList.remove('hidden');
        dialog.classList.add('flex');
    },

    /**
     * 关闭对话框
     */
    closeDialog() {
        const dialog = document.getElementById('restoreDialog');
        dialog.classList.add('hidden');
        dialog.classList.remove('flex');
        this.resetStates();
    },

    /**
     * 显示特定状态
     * @param {string} state - 状态名称
     */
    showState(state) {
        this.resetStates();
        const element = document.getElementById(`${state}State`);
        if (element) {
            element.classList.remove('hidden');
        }
    },

    /**
     * 重置所有状态
     */
    resetStates() {
        ['upload', 'restore', 'restart', 'success', 'error'].forEach(state => {
            const element = document.getElementById(`${state}State`);
            if (element) {
                element.classList.add('hidden');
            }
        });
    },

    /**
     * 显示错误
     * @param {string} message - 错误信息
     */
    showError(message) {
        this.showState('error');
        document.getElementById('errorMessage').textContent = message;
    },

    /**
     * 显示完成状态
     */
    showCompleted() {
        this.showState('success');
        this.updateRestartStatus('恢复完成，系统已重启');
    },

    /**
     * 更新进度消息
     * @param {string} message - 进度消息
     */
    updateProgressMessage(message) {
        const progressElement = document.getElementById('progressMessage');
        if (progressElement) {
            progressElement.textContent = message;
        }
        console.log(`[数据库恢复] ${message}`);
    }
}; 
