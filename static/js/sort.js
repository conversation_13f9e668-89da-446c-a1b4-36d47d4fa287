/**
 * @file sort.js
 * @description 服务器状态卡片排序、拖拽功能和数据管理系统
 */

// 等待 Sortable 加载完成
(function waitForSortable() {
    if (typeof Sortable === 'undefined') {
        // 如果 Sortable 还未加载，等待 100ms 后重试
        setTimeout(waitForSortable, 100);
        return;
    }
    
    // Sortable 已加载，初始化 ServerCardSystem
    initializeServerCardSystem();
})();

function initializeServerCardSystem() {
// 创建命名空间，避免全局污染
// 检查是否已经定义，如果已定义则不重复声明
if (typeof window.ServerCardSystem === 'undefined') {
window.ServerCardSystem = (() => {
    // 内部变量和工具函数
    const config = {
      // 全局配置
      animation: {
        duration: 150,
        easing: "cubic-bezier(0.4, 0, 0.2, 1)"
      },
      retry: {
        maxAttempts: 20,
        delay: 500
      },
      update: {
        interval: 2000,
        retryDelay: 5000
      }
    };

    // 工具函数
    const Utils = {
      // DOM辅助函数
      dom: {
        // 获取元素，支持选择器或元素
        get(selector, parent = document) {
          return typeof selector === 'string' ? parent.querySelector(selector) : selector;
        },
        // 获取多个元素
        getAll(selector, parent = document) {
          return Array.from(parent.querySelectorAll(selector));
        },
        // 创建元素
        create(tag, attributes = {}, children = []) {
          const element = document.createElement(tag);
          Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'class') {
              element.className = value;
            } else if (key === 'style' && typeof value === 'object') {
              Object.assign(element.style, value);
            } else {
              element.setAttribute(key, value);
            }
          });
          children.forEach(child => element.appendChild(typeof child === 'string' ? document.createTextNode(child) : child));
          return element;
        },
        // 添加样式表
        addStyles(css, id) {
          if (id && document.getElementById(id)) return;
          const style = document.createElement('style');
          if (id) style.id = id;
          style.textContent = css;
          document.head.appendChild(style);
        }
      },

      // 异步辅助函数
      async: {
        // 等待条件满足
        waitFor(condition, timeout = 10000, interval = 100) {
          return new Promise((resolve, reject) => {
            const startTime = Date.now();
            const check = () => {
              const result = condition();
              if (result) {
                resolve(result);
              } else if (Date.now() - startTime > timeout) {
                reject(new Error('Timeout waiting for condition'));
              } else {
                setTimeout(check, interval);
              }
            };
            check();
          });
        },
        // 延迟执行
        delay(ms) {
          return new Promise(resolve => setTimeout(resolve, ms));
        },
        // 带重试的执行函数
        retry(fn, retries = 3, delay = 1000, backoff = 1.5) {
          return new Promise(async (resolve, reject) => {
            let attempt = 0;
            while (attempt < retries) {
              try {
                const result = await fn();
                return resolve(result);
              } catch (error) {
                attempt++;
                console.warn(`尝试 ${attempt}/${retries} 失败:`, error);
                if (attempt >= retries) {
                  return reject(error);
                }
                await Utils.async.delay(delay * Math.pow(backoff, attempt - 1));
              }
            }
          });
        }
      },

      // 事件辅助函数
      events: {
        // 自定义事件发布
        emit(name, detail = {}) {
          const event = new CustomEvent(name, { detail, bubbles: true });
          document.dispatchEvent(event);
          return event;
        },
        // 订阅事件
        on(name, handler) {
          document.addEventListener(name, handler);
          return () => document.removeEventListener(name, handler);
        },
        // 带防抖的事件处理
        debounce(fn, delay = 250) {
          let timer;
          return function(...args) {
            clearTimeout(timer);
            timer = setTimeout(() => fn.apply(this, args), delay);
          };
        }
      },

      // 通知函数
      notify(message, type = 'info', duration = 3000) {
        if (typeof notice === 'function') {
          notice(message);
          return;
        }

        const typeColors = {
          error: 'bg-red-500',
          success: 'bg-green-500',
          info: 'bg-blue-500',
          warning: 'bg-amber-500'
        };

        const toast = Utils.dom.create('div', {
          class: `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${typeColors[type] || typeColors.info} text-white transition-opacity duration-300`
        }, [message]);

        document.body.appendChild(toast);

        setTimeout(() => {
          toast.classList.add('opacity-0');
          setTimeout(() => toast.remove(), 300);
        }, duration);
      },

      // 设备检测
      isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      },

      // 本地存储
      storage: {
        get(key, defaultValue = null) {
          try {
            const value = localStorage.getItem(key);
            return value !== null ? JSON.parse(value) : defaultValue;
          } catch {
            return defaultValue;
          }
        },
        set(key, value) {
          try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
          } catch {
            return false;
          }
        }
      }
    };

    // 状态管理模块
    const StateManager = (() => {
      const state = {
        isUpdating: false,
        lastUpdateTime: null,
        updateError: null,
        connectionStatus: 'disconnected',
        dragActive: false,
        sortType: 'default',
        sortDirection: 'desc'
      };

      const observers = new Map();

      // 更新状态并通知
      function setState(newState) {
        const changedKeys = [];

        // 记录变化的键
        Object.entries(newState).forEach(([key, value]) => {
          if (state[key] !== value) {
            state[key] = value;
            changedKeys.push(key);
          }
        });

        // 如果有变化，通知观察者
        if (changedKeys.length > 0) {
          notify(changedKeys);
        }
      }

      // 通知观察者状态变化
      function notify(changedKeys) {
        // 通知所有观察者
        observers.forEach((callback, keys) => {
          // 如果观察者关注任何变化，或者关注的键发生了变化
          if (keys === '*' || keys.some(key => changedKeys.includes(key))) {
            callback(state);
          }
        });
      }

      // 订阅状态变化
      function subscribe(callback, keys = '*') {
        const id = Date.now().toString(36) + Math.random().toString(36).substring(2);
        observers.set(keys, callback);
        return id;
      }

      // 取消订阅
      function unsubscribe(id) {
        observers.delete(id);
      }

      // 获取当前状态
      function getState(key) {
        return key ? state[key] : {...state};
      }

      // 初始化
      function init() {
        // 监听统计更新事件
        Utils.events.on('statsUpdate', () => {
          setState({ lastUpdateTime: Date.now() });
        });

        return Promise.resolve(true);
      }

      return {
        getState,
        setState,
        subscribe,
        unsubscribe,
        init
      };
    })();

    // 数据管理模块
    const DataManager = (() => {
      let updateInterval = null;
      let retryTimeout = null;

      // 更新统计数据
      async function updateStats() {
        if (StateManager.getState('isUpdating')) return;

        try {
          StateManager.setState({ isUpdating: true });

          if (typeof StatsController === 'undefined') {
            throw new Error('StatsController not found');
          }

          await StatsController.update();

          StateManager.setState({
            lastUpdateTime: Date.now(),
            updateError: null,
            connectionStatus: 'connected'
          });

        } catch (error) {
          console.error('数据更新失败:', error);
          StateManager.setState({
            updateError: error.message || String(error),
            connectionStatus: 'error'
          });
          scheduleRetry();
        } finally {
          StateManager.setState({ isUpdating: false });
        }
      }

      // 启动自动更新
      function startAutoUpdate(interval = config.update.interval) {
        stopAutoUpdate();
        updateInterval = setInterval(() => updateStats(), interval);
      }

      // 停止自动更新
      function stopAutoUpdate() {
        if (updateInterval) {
          clearInterval(updateInterval);
          updateInterval = null;
        }
      }

      // 安排重试
      function scheduleRetry(delay = config.update.retryDelay) {
        if (retryTimeout) {
          clearTimeout(retryTimeout);
        }
        retryTimeout = setTimeout(() => updateStats(), delay);
      }

      // 初始化
      async function init() {
        startAutoUpdate();
        return Promise.resolve(true);
      }

      // 获取卡片数据
      function getCardData(card) {
        return {
          id: card.dataset.sid,
          group: card.closest('.group-view')?.dataset.group,
          top: parseInt(card.dataset.top, 10) || 0,
          cpu: parseFloat(card.dataset.cpu) || 0,
          memory: parseFloat(card.dataset.memory) || 0,
          download: parseFloat(card.dataset.download) || 0,
          upload: parseFloat(card.dataset.upload) || 0,
          expiration: parseInt(card.dataset.expiration, 10) || 0,
          status: card.dataset.status || 'unknown'
        };
      }

      // 获取所有卡片数据
      function getAllCardsData() {
        return Utils.dom.getAll('.server-card').map(getCardData);
      }

      return {
        updateStats,
        startAutoUpdate,
        stopAutoUpdate,
        init,
        getCardData,
        getAllCardsData
      };
    })();

    // SortableJS 配置
    const SortableConfig = {
      // 基础配置
      base: {
        group: 'servers',
        animation: config.animation.duration,
        easing: config.animation.easing,
        delay: 50,
        delayOnTouchOnly: true,

        // 拖拽手柄
        handle: '.drag-handle',

        // 拖拽样式
        ghostClass: "sortable-ghost",
        dragClass: "sortable-drag",
        chosenClass: "sortable-chosen",

        // 性能优化
        forceFallback: false,
        fallbackOnBody: true,

        // 滚动设置
        scroll: true,
        scrollSensitivity: 30,
        scrollSpeed: 10,

        // 排序设置 - 优化网格布局的拖拽
        swap: true, // 启用交换模式，对网格布局更友好
        swapThreshold: 0.5, // 交换模式下，50% 重叠就触发交换
        fallbackTolerance: 0, // 立即开始拖拽
        touchStartThreshold: 0, // 立即响应触摸

        // 禁用离线和隐藏项
        filter: '.offline, .hidden', // 统一使用 offline 类排除离线节点
        preventOnFilter: false // 允许在过滤项上触发事件，解决拖拽问题
      },

      // 移动端配置 - 提高灵敏度
      mobile: {
        delay: 100, // 减少延迟，使拖拽更加灵敏
        touchStartThreshold: 1, // 减少触摸拖拽的启动阈值
        scrollSensitivity: 50,
        swapThreshold: 0.2, // 设置更低的交换阈值，使交换更加灵敏
        invertedSwapThreshold: 0.2, // 设置反向交换阈值
        direction: 'auto', // 自动检测拖拽方向
        fallbackTolerance: 1, // 设置更低的拖拽开始阈值
        preventOnFilter: false // 允许在过滤项上触发事件
      },

      // 合并配置
      getConfig(isMobile) {
        return {
          ...this.base,
          ...(isMobile ? this.mobile : {}),
          
          // 添加交换时的视觉反馈
          onChoose: function(evt) {
            evt.item.classList.add('dragging-item');
          },
          
          onUnchoose: function(evt) {
            evt.item.classList.remove('dragging-item');
          },
          
          // 拖拽过程中的反馈
          onMove: function(evt) {
            // 使用防抖来减少频繁的DOM操作
            const targetCard = evt.related;
            
            // 只有当目标改变时才更新高亮
            const currentDropTarget = document.querySelector('.drop-target');
            if (targetCard && targetCard.classList.contains('server-card')) {
              if (currentDropTarget !== targetCard) {
                // 移除之前的高亮
                if (currentDropTarget) {
                  currentDropTarget.classList.remove('drop-target');
                }
                // 添加新的高亮
                targetCard.classList.add('drop-target');
              }
            } else if (currentDropTarget) {
              currentDropTarget.classList.remove('drop-target');
            }
            
            return true;
          },
          
          onChange: function(evt) {
            // 交换时的动画反馈
            const item = evt.item;
            const swapped = evt.swapItem;
            
            if (item && swapped) {
              // 添加交换动画类
              item.classList.add('swap-animation');
              swapped.classList.add('swap-animation');
              
              // 移除动画类
              setTimeout(() => {
                item.classList.remove('swap-animation');
                swapped.classList.remove('swap-animation');
              }, 300);
            }
          }
        };
      }
    };

    // 拖拽状态
    const DragState = {
      active: false,
      source: null,
      target: null,
      startIndex: -1
    };

    // API 接口
    const API = {
      endpoints: {
        updateOrder: '/admin/servers/ord'
      },

      // 更新服务器顺序
      async updateServerOrder(serverIds) {
        try {
          const response = await fetch(this.endpoints.updateOrder, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              servers: serverIds,
              group_context: true
            })
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();
          if (!result.status) {
            throw new Error(result.msg || '更新排序失败');
          }

          return result;
        } catch (error) {
          console.error('更新服务器排序失败:', error);
          throw error;
        }
      }
    };

    // 拖拽动画
    const DragAnimations = {
      // 初始化样式
      init() {
        // Utils.dom.addStyles(this.styles, 'sortable-styles'); // REMOVED
      },

      // 添加拖拽反馈
      addDragFeedback(element) {
        requestAnimationFrame(() => {
          element.style.transform = 'scale(1.02)';
          element.style.boxShadow = '0 5px 15px rgba(0,0,0,0.3)';
          element.style.transition = 'transform 0.2s, box-shadow 0.2s';
        });
      },

      // 移除拖拽反馈
      removeDragFeedback(element) {
        requestAnimationFrame(() => {
          element.style.transform = '';
          element.style.boxShadow = '';
        });
      },

      // 添加放置动画
      addDropAnimation(element, type = 'success') {
        element.style.animation = `${type} 0.5s cubic-bezier(0.4, 0, 0.2, 1)`;
        setTimeout(() => element.style.animation = '', 500);
      }
    };

    // 历史记录管理
    const DragHistory = {
      history: [],
      currentIndex: -1,
      maxHistory: 20,
      
      // 记录操作
      record(state) {
        // 移除当前索引之后的历史
        this.history = this.history.slice(0, this.currentIndex + 1);
        this.history.push({
          timestamp: Date.now(),
          serverOrder: state
        });
        
        // 限制历史记录数量
        if (this.history.length > this.maxHistory) {
          this.history.shift();
        }
        this.currentIndex = this.history.length - 1;
        this.updateUndoRedoButtons();
      },
      
      // 撤销
      undo() {
        if (this.currentIndex > 0) {
          this.currentIndex--;
          const state = this.history[this.currentIndex];
          this.updateUndoRedoButtons();
          return state.serverOrder;
        }
        return null;
      },
      
      // 重做
      redo() {
        if (this.currentIndex < this.history.length - 1) {
          this.currentIndex++;
          const state = this.history[this.currentIndex];
          this.updateUndoRedoButtons();
          return state.serverOrder;
        }
        return null;
      },
      
      // 更新撤销/重做按钮状态
      updateUndoRedoButtons() {
        const undoBtn = document.getElementById('undo-sort-btn');
        const redoBtn = document.getElementById('redo-sort-btn');
        
        if (undoBtn) {
          undoBtn.disabled = this.currentIndex <= 0;
          undoBtn.classList.toggle('opacity-50', this.currentIndex <= 0);
          // 更新提示文本
          if (this.currentIndex > 0) {
            undoBtn.title = `撤销上一步操作 (还可撤销 ${this.currentIndex} 步)`;
          } else {
            undoBtn.title = '没有可撤销的操作';
          }
        }
        if (redoBtn) {
          redoBtn.disabled = this.currentIndex >= this.history.length - 1;
          redoBtn.classList.toggle('opacity-50', this.currentIndex >= this.history.length - 1);
          // 更新提示文本
          const redoSteps = this.history.length - 1 - this.currentIndex;
          if (redoSteps > 0) {
            redoBtn.title = `重做下一步操作 (还可重做 ${redoSteps} 步)`;
          } else {
            redoBtn.title = '没有可重做的操作';
          }
        }
      }
    };

    // 拖拽管理器
    const DragManager = (() => {
      const sortableInstances = new Map();
      let mainListContainer = null; // 存储主列表容器

      // 检查拖拽是否启用
      function isDragEnabled() {
        const dragSortToggle = document.getElementById('enable-drag-sort');
        return dragSortToggle && dragSortToggle.checked && !dragSortToggle.disabled;
      }

      // 获取主卡片容器
      function getMainContainer() {
        // 先尝试获取标准ID的容器
        const cardContainer = document.getElementById('card-grid-container');
        const listContainer = document.getElementById('list-grid-container');

        // 如果找到了标准容器，直接使用
        if (cardContainer || listContainer) {
          mainListContainer = cardContainer || listContainer;
          return mainListContainer;
        }

        // 如果没找到标准容器，尝试从当前活动的分组视图中查找
        const activeGroupView = document.querySelector('.group-view:not(.hidden)');
        if (activeGroupView) {
          // 尝试找到当前活动视图中的网格容器
          const gridContainer = activeGroupView.querySelector('.grid');
          if (gridContainer) {
            console.log('使用当前活动分组的网格容器作为主卡片容器');
            mainListContainer = gridContainer;
            return mainListContainer;
          }
        }

        // 如果还是找不到，输出警告
        console.warn('未找到主卡片容器元素');
        return null;
      }

      // 分组功能已移除，统一由编辑和添加服务器设置分组

      // 初始化拖拽管理器
      async function init() {
        try {
          // 1. 初始化动画样式
          DragAnimations.init();

          // 2. 获取容器
          const mainContainer = getMainContainer();

          // 3. 验证容器
          if (!mainContainer) {
            throw new Error('无法初始化拖拽：未找到主卡片容器。');
          }

          // 4. 创建拖拽实例
          createMainListSortable(mainContainer);
          
          // 5. 记录初始排序状态到历史
          const initialOrder = Array.from(mainContainer.children).map(el => el.dataset.sid);
          if (initialOrder.length > 0) {
            DragHistory.record(initialOrder);
          }

          console.log('拖拽排序功能初始化成功');
          return true;
        } catch (error) {
          console.error('拖拽管理器初始化失败:', error);
          destroy(); // 清理资源
          return Promise.reject(error);
        }
      }

      // 创建主卡片列表的 Sortable 实例
      function createMainListSortable(container) {
        if (!container) return;

        // 1. 清理旧实例
        if (sortableInstances.has(container)) {
          sortableInstances.get(container).destroy();
        }

        // 2. 设置卡片可拖拽
        Utils.dom.getAll('.server-card', container).forEach(card => {
          card.draggable = true;
          card.setAttribute('data-has-drag-events', 'true');
        });

        // 3. 创建新实例
        const baseConfig = SortableConfig.getConfig(Utils.isMobile());
        const sortableConfig = {
          ...baseConfig,
          onStart: handleDragStart,
          onEnd: handleDragEnd,
          // 保留基础配置中的其他事件处理器
          onChoose: baseConfig.onChoose,
          onUnchoose: baseConfig.onUnchoose,
          onChange: baseConfig.onChange,
          // 合并 onMove 函数
          onMove: function(evt) {
            if (!isDragEnabled()) return false;
            
            // 执行基础配置中的 onMove
            if (baseConfig.onMove) {
              baseConfig.onMove(evt);
            }
            
            return handleDragMove(evt);
          }
        };

        // 输出调试信息
        console.log('创建 Sortable 实例配置:', {
          swapThreshold: sortableConfig.swapThreshold,
          invertedSwapThreshold: sortableConfig.invertedSwapThreshold,
          direction: sortableConfig.direction,
          invertSwap: sortableConfig.invertSwap,
          fallbackTolerance: sortableConfig.fallbackTolerance,
          touchStartThreshold: sortableConfig.touchStartThreshold,
          preventOnFilter: sortableConfig.preventOnFilter,
          delay: sortableConfig.delay,
          isMobile: Utils.isMobile(),
          container: container.id || 'unknown'
        });

        const sortable = new Sortable(container, sortableConfig);

        // 4. 保存实例
        sortableInstances.set(container, sortable);
        return sortable;
      }

      // 拖拽开始处理
      function handleDragStart(e) {
        // Ensure drag is enabled
        if (!isDragEnabled()) {
          e.preventDefault();
          // 显示禁用提示
          if (typeof notice === 'function') {
            notice('拖拽排序功能已禁用，请先开启拖拽开关', 'warning');
          } else {
            Utils.notify('拖拽排序功能已禁用，请先开启拖拽开关', 'warning');
          }
          // 高亮拖拽开关
          const dragToggle = document.getElementById('enable-drag-sort');
          if (dragToggle) {
            const toggleLabel = dragToggle.closest('label');
            if (toggleLabel) {
              toggleLabel.classList.add('ring-2', 'ring-amber-500', 'ring-offset-2');
              setTimeout(() => {
                toggleLabel.classList.remove('ring-2', 'ring-amber-500', 'ring-offset-2');
              }, 2000);
            }
          }
          return false;
        }

        // Set the dragged item and its initial index
        draggedItem = e.item; // Use evt.item from Sortable
        draggedItemIndex = e.oldIndex;

        // Apply dragging styles using Tailwind classes
        draggedItem.classList.add('opacity-70', 'scale-105', 'shadow-2xl', 'z-50', 'cursor-grabbing', 'sortable-chosen', 'sortable-drag');

        // Add placeholder logic if needed (SortableJS often handles this visually)
        // placeholder = draggedItem.cloneNode(true);
        // placeholder.classList.add('placeholder', 'opacity-50', 'border-dashed', 'border-2', 'border-gray-400');
        // placeholder.style.height = `${draggedItem.offsetHeight}px`;
        // e.from.insertBefore(placeholder, draggedItem.nextSibling);

        // Add transition for smooth movement of other items (Sortable handles this via CSS)
        // Array.from(e.from.children).forEach(child => {
        //     if (child !== draggedItem && child !== placeholder) {
        //         child.style.transition = 'transform 0.2s ease-in-out';
        //     }
        // });

        // Update global state
        StateManager.setState({ dragActive: true });
        document.body.classList.add('dragging-active');

        console.log('Drag started:', draggedItem.dataset.sid);
      }

      // 拖拽移动处理
      function handleDragMove(evt, originalEvent) {
        if (!isDragEnabled()) {
          return false; // Prevent moving if drag is disabled
        }
        // Keep dynamic transform for sibling item visual feedback during dragOver
        // This is complex to replicate perfectly with only classes,
        // SortableJS handles much of the visual movement. We focus on start/end states.
        // console.log('Drag move'); // Logging for debugging
        return true; // Allow the move
      }

      // 移动过程中的动画 (Let SortableJS handle this via CSS classes)
      // function handleMoveAnimation(dragged, related, container) { ... }

      // 拖拽结束处理 (处理排序)
      async function handleDragEnd(evt) {
        const { item, to, from, oldIndex, newIndex } = evt;

        // Clean up styles added in handleDragStart
        item.classList.remove('opacity-70', 'scale-105', 'shadow-2xl', 'z-50', 'cursor-grabbing', 'sortable-chosen', 'sortable-drag');
        item.style.cursor = ''; // Reset cursor explicitly
        
        // 清理所有拖拽相关的类
        document.querySelectorAll('.drop-target').forEach(el => {
          el.classList.remove('drop-target');
        });
        document.querySelectorAll('.dragging-item').forEach(el => {
          el.classList.remove('dragging-item');
        });
        document.querySelectorAll('.swap-animation').forEach(el => {
          el.classList.remove('swap-animation');
        });

        // Reset global state
        StateManager.setState({ dragActive: false });
        document.body.classList.remove('dragging-active');

        console.log('Drag ended:', item.dataset.sid, 'Moved:', oldIndex !== newIndex);

        // If item position changed, update the order on the backend
        if (to && oldIndex !== newIndex) {
          try {
            // 记录当前排序状态到历史
            const currentOrder = Array.from(to.children).map(el => el.dataset.sid);
            DragHistory.record(currentOrder);
            
            // Add drop animation (optional)
             DragAnimations.addDropAnimation(item, 'success');

            // Update server order
            await updateCardPosition(item, to);
          } catch (error) {
            console.error('排序更新失败 (handleDragEnd):', error);
            // Rollback handled by SortableJS or manually if needed
            // For simplicity, we rely on SortableJS rollback or notify user
            if (typeof notice === 'function') {
                notice(error.message || '更新排序失败', 'error');
            } else {
                Utils.notify(error.message || '更新排序失败', 'error');
            }
            // Optional: Manually revert the DOM change if SortableJS doesn't
            // from.insertBefore(item, from.children[oldIndex]);
             DragAnimations.addDropAnimation(item, 'error');
          }
        }
      }

      // 清理拖拽效果 (Simplified, focus on removing classes)
      // function cleanupDragEffects(container) { ... }

      // 回滚到原始位置 (Let SortableJS handle this)
      // function rollbackToOriginalPosition(item, container) { ... }

      // 显示保存状态
      function showSaveStatus(status = 'saving') {
        const indicator = document.getElementById('sort-save-indicator');
        const icon = document.getElementById('save-status-icon');
        const text = document.getElementById('save-status-text');
        
        if (!indicator) return;
        
        indicator.style.display = 'flex';
        
        switch(status) {
          case 'saving':
            icon.textContent = '';
            icon.className = 'ti ti-refresh text-sm text-blue-500 animate-spin';
            text.textContent = '正在保存...';
            text.className = 'text-blue-500';
            break;
          case 'saved':
            icon.textContent = '';
            icon.className = 'ti ti-circle-check text-sm text-green-500';
            text.textContent = '已自动保存';
            text.className = 'text-green-500';
            // 3秒后隐藏
            setTimeout(() => {
              indicator.style.display = 'none';
            }, 3000);
            break;
          case 'error':
            icon.textContent = '';
            icon.className = 'ti ti-alert-circle text-sm text-red-500';
            text.textContent = '保存失败';
            text.className = 'text-red-500';
            break;
        }
      }

      // 更新卡片位置 (只处理排序)
      async function updateCardPosition(card, container) {
        if (StateManager.getState('isUpdating')) {
            console.warn('状态更新中，请稍后再试');
            return;
        }
        try {
            StateManager.setState({ isUpdating: true });
            showSaveStatus('saving');

            // 更新排序
            if (container) {
                const cards = Utils.dom.getAll('.server-card', container);
                const visibleCards = cards.filter(c => !c.classList.contains('hidden') && !c.matches('.hidden, [class*="hidden-"]'));
                if (visibleCards.length > 0) {
                    await API.updateServerOrder(visibleCards.map(c => c.dataset.sid));
                }
            }
            showSaveStatus('saved');
            // 使用全局的 notice 函数（如果存在）以获得更好的主题支持
            if (typeof notice === 'function') {
                notice('排序已保存', 'success');
            } else {
                Utils.notify('排序已保存', 'success');
            }
            
            // 刷新数据显示最新排序结果
            if (typeof StatsController !== 'undefined' && StatsController.update) {
                setTimeout(() => StatsController.update(), 500);
            }
        } catch (error) {
            console.error('更新排序失败:', error);
            showSaveStatus('error');
            // 使用全局的 notice 函数（如果存在）以获得更好的主题支持
            if (typeof notice === 'function') {
                notice(error.message || '更新排序失败', 'error');
            } else {
                Utils.notify(error.message || '更新排序失败', 'error');
            }
            throw error; // 抛出错误以便 handleDragEnd 捕获并回滚
        } finally {
            StateManager.setState({ isUpdating: false });
        }
      }

      // 销毁资源
      function destroy() {
        try {
          // 清理排序实例
          sortableInstances.forEach(instance => instance.destroy());
          sortableInstances.clear();

          // 移除卡片拖拽属性
          if(mainListContainer) {
            Utils.dom.getAll('.server-card', mainListContainer).forEach(card => {
                card.draggable = false;
                card.removeAttribute('data-has-drag-events');
            });
          }

          // 重置引用
          mainListContainer = null;

          // 移除拖拽中类
          document.body.classList.remove('dragging-active');

          console.log('拖拽功能已清理');
        } catch (error) {
          console.error('清理资源失败:', error);
        }
      }

      // 重置状态
      function reset() {
        destroy();
        // 重新初始化由外部开关控制
        // return init();
      }

      // 执行撤销操作
      function executeUndo() {
        const previousOrder = DragHistory.undo();
        if (previousOrder) {
          applyServerOrder(previousOrder);
        }
      }
      
      // 执行重做操作
      function executeRedo() {
        const nextOrder = DragHistory.redo();
        if (nextOrder) {
          applyServerOrder(nextOrder);
        }
      }
      
      // 应用服务器顺序
      async function applyServerOrder(serverIds) {
        const container = getMainContainer();
        if (!container) return;
        
        try {
          showSaveStatus('saving');
          
          // 创建一个临时容器来重新排序元素
          const fragment = document.createDocumentFragment();
          
          // 按照指定顺序添加元素
          serverIds.forEach(sid => {
            const card = container.querySelector(`[data-sid="${sid}"]`);
            if (card) {
              fragment.appendChild(card);
            }
          });
          
          // 一次性更新 DOM
          container.appendChild(fragment);
          
          // 更新服务器
          await API.updateServerOrder(serverIds);
          
          showSaveStatus('saved');
          if (typeof notice === 'function') {
              notice('排序已恢复并保存', 'success');
          } else {
              Utils.notify('排序已恢复并保存', 'success');
          }
        } catch (error) {
          showSaveStatus('error');
          if (typeof notice === 'function') {
              notice('保存失败，请重试', 'error');
          } else {
              Utils.notify('保存失败，请重试', 'error');
          }
          console.error('应用排序失败:', error);
        }
      }

      return {
        init,
        destroy,
        reset,
        isDragEnabled,
        executeUndo,
        executeRedo
      };
    })();

    // 性能监控器
    const PerformanceMonitor = (() => {
      const metrics = {
        updateTimes: [],
        errors: [],
        lastResponseTime: null
      };

      // 开始监控
      function startMonitoring() {
        StateManager.subscribe(handleStateChange, ['isUpdating', 'lastUpdateTime', 'updateError']);
      }

      // 处理状态变化
      function handleStateChange(state) {
        if (!state.isUpdating && state.lastUpdateTime) {
          recordUpdate(Date.now() - state.lastUpdateTime);
        }

        if (state.updateError) {
          recordError(state.updateError);
        }
      }

      // 记录更新时间
      function recordUpdate(duration) {
        metrics.updateTimes.push({
          time: Date.now(),
          duration
        });

        if (metrics.updateTimes.length > 100) {
          metrics.updateTimes.shift();
        }

        analyzePerformance();
      }

      // 记录错误
      function recordError(error) {
        metrics.errors.push({
          time: Date.now(),
          error: typeof error === 'string' ? error : error.message
        });

        if (metrics.errors.length > 50) {
          metrics.errors.shift();
        }
      }

      // 分析性能
      function analyzePerformance() {
        const recentUpdates = metrics.updateTimes.slice(-10);
        if (recentUpdates.length === 0) return;

        const avgDuration = recentUpdates.reduce((sum, record) => sum + record.duration, 0) / recentUpdates.length;

        if (avgDuration > 1000) {
          console.warn('性能警告: 数据更新平均耗时超过1秒');
        }
      }

      // 获取指标
      function getMetrics() {
        return {
          averageUpdateTime: calculateAverageUpdateTime(),
          errorRate: calculateErrorRate(),
          totalUpdates: metrics.updateTimes.length,
          totalErrors: metrics.errors.length
        };
      }

      // 计算平均更新时间
      function calculateAverageUpdateTime() {
        if (metrics.updateTimes.length === 0) return 0;
        const sum = metrics.updateTimes.reduce((acc, record) => acc + record.duration, 0);
        return sum / metrics.updateTimes.length;
      }

      // 计算错误率
      function calculateErrorRate() {
        if (metrics.updateTimes.length === 0) return 0;
        return metrics.errors.length / metrics.updateTimes.length;
      }

      return {
        startMonitoring,
        getMetrics
      };
    })();

    // 系统初始化器 (简化，只初始化必要的)
    const SystemInitializer = (() => {
      let initialized = false;

      // 初始化系统
      async function init() {
        if (initialized) return true;

        try {
          // 1. 等待页面完全加载
          await waitForPageLoad();

          // 2. 等待 StatsController 加载
          await waitForController();

          // 3. 等待首次数据更新完成
          await waitForDataLoad();

          // 4. 初始化各个管理器
          await Promise.all([
            // TabManager.init(), // 删除
            StateManager.init(),
            DataManager.init(),
            // DragManager.init() // 删除，由开关控制
          ]);

          // 5. 启动性能监控
          PerformanceMonitor.startMonitoring();

          initialized = true;
          return true;
        } catch (error) {
          console.error('系统初始化失败:', error);
          setTimeout(() => init(), 5000);
          return false;
        }
      }

      // 等待页面加载
      function waitForPageLoad() {
        return new Promise(resolve => {
          if (document.readyState === 'complete') {
            resolve();
          } else {
            window.addEventListener('load', resolve, { once: true });
          }
        });
      }

      // 等待控制器加载
      async function waitForController() {
        return Utils.async.waitFor(
          () => typeof StatsController !== 'undefined',
          config.retry.maxAttempts * config.retry.delay,
          config.retry.delay
        );
      }

      // 等待数据加载
      async function waitForDataLoad() {
        return Utils.async.waitFor(() => {
          // 检查是否有服务器卡片被渲染
          const cards = document.querySelectorAll('.server-card');
          if (cards.length === 0) return false;

          // 检查数据是否已加载
          return Array.from(cards).some(card => {
            const cpu = card.querySelector('[id$="_CPU"]');
            return cpu && cpu.textContent !== 'NaN';
          });
        }, config.retry.maxAttempts * config.retry.delay, config.retry.delay);
      }

      return {
        init,
        get isInitialized() {
          return initialized;
        }
      };
    })();

    // 导出主接口
    return {
      // 公共模块
      Utils,
      StateManager,
      DataManager,
      DragManager, // 导出 DragManager 用于开关控制
      // TabManager, // 删除

      // 系统初始化
      init: SystemInitializer.init,

      // 其他辅助函数
      getPerformanceMetrics: PerformanceMonitor.getMetrics
    };
  })();

  // 系统主初始化入口
  document.addEventListener('DOMContentLoaded', async () => {
    try {
      // 初始化系统
      await ServerCardSystem.init();
    } catch (error) {
      console.error('系统启动失败:', error);
      if (typeof notice === 'function') {
          notice('系统启动失败，请刷新页面重试', 'error');
      } else {
          ServerCardSystem.Utils.notify('系统启动失败，请刷新页面重试', 'error');
      }
    }
  });

  // 拖拽排序开关控制
  document.addEventListener('DOMContentLoaded', () => {
    // 1. 获取必要元素
    const dragSortToggle = document.getElementById('enable-drag-sort');
    const serverViewContainer = document.getElementById('card-grid-container') ||
                               document.getElementById('list-grid-container');

    // 2. 验证元素
    if (!dragSortToggle || !serverViewContainer) {
      console.warn('未找到拖拽开关或服务器容器，无法控制拖拽功能');
      return;
    }

    // 3. 检查限制条件
    const isGuest = document.body.classList.contains('guest-user');
    const isMobile = ServerCardSystem.Utils.isMobile();
    const isRestricted = isGuest || isMobile;

    // 4. 初始化卡片状态
    ServerCardSystem.Utils.dom.getAll('.server-card').forEach(card => {
      card.draggable = false;
    });

    // 5. 设置拖拽状态函数
    function setDragState(enabled) {
      if (enabled) {
        // 启用拖拽
        ServerCardSystem.DragManager.init();
        serverViewContainer.classList.add('card-sort-enabled');
      } else {
        // 禁用拖拽
        ServerCardSystem.DragManager.destroy();
        serverViewContainer.classList.remove('card-sort-enabled');
      }
      // 更新拖拽手柄的视觉状态
      if (typeof window.updateDragHandleState === 'function') {
        window.updateDragHandleState();
      }
    }

    // 6. 处理限制情况
    if (isRestricted) {
      dragSortToggle.checked = false;
      dragSortToggle.disabled = true;
      dragSortToggle.title = isGuest ? '游客不能使用拖拽排序功能' : '移动端不支持拖拽排序功能';
      localStorage.setItem('dragSortEnabled', 'false');
      ServerCardSystem.DragManager.destroy();
      return;
    }

    // 7. 处理正常情况
    const isDragEnabledInitially = localStorage.getItem('dragSortEnabled') === 'true';
    dragSortToggle.checked = isDragEnabledInitially;

    // 应用初始状态
    setDragState(isDragEnabledInitially);

    // 监听开关变化
    dragSortToggle.addEventListener('change', (e) => {
      const enabled = e.target.checked;
      localStorage.setItem('dragSortEnabled', enabled);
      setDragState(enabled);
      
      // 显示/隐藏撤销重做按钮
      const undoRedoButtons = document.getElementById('undo-redo-buttons');
      if (undoRedoButtons) {
        undoRedoButtons.style.display = enabled ? 'flex' : 'none';
      }

      // 显示通知
      if (typeof notice === 'function') {
        if (enabled) {
          // 检查是否首次使用
          const hasUsedDragSort = localStorage.getItem('hasUsedDragSort');
          if (!hasUsedDragSort) {
            notice('已启用拖拽排序 - 通过拖拽手柄图标移动卡片，排序会自动保存');
            localStorage.setItem('hasUsedDragSort', 'true');
          } else {
            notice('已启用拖拽排序功能');
          }
        } else {
          notice('已禁用拖拽排序功能');
        }
      }
    });
    
    // 初始化撤销/重做按钮
    const undoBtn = document.getElementById('undo-sort-btn');
    const redoBtn = document.getElementById('redo-sort-btn');
    
    if (undoBtn) {
      undoBtn.addEventListener('click', () => {
        ServerCardSystem.DragManager.executeUndo();
      });
    }
    
    if (redoBtn) {
      redoBtn.addEventListener('click', () => {
        ServerCardSystem.DragManager.executeRedo();
      });
    }
    
    // 如果拖拽已启用，显示撤销/重做按钮
    if (isDragEnabledInitially) {
      const undoRedoButtons = document.getElementById('undo-redo-buttons');
      if (undoRedoButtons) {
        undoRedoButtons.style.display = 'flex';
      }
    }
  });

  // 导出全局接口 (移除或调整)
  window.SystemInitializer = { init: ServerCardSystem.init };
  window.StateManager = ServerCardSystem.StateManager;
  window.DataManager = ServerCardSystem.DataManager;
  window.DragManager = ServerCardSystem.DragManager;
  // window.TabManager = ServerCardSystem.TabManager; // 移除
  window.Utils = ServerCardSystem.Utils;
  // window.DragState = ServerCardSystem.DragManager.DragState; // DragState 内部管理
}
} // 结束 initializeServerCardSystem 函数