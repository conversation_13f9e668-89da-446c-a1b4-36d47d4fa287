/**
 * @file dashboard.js
 * @description 仪表盘相关功能模块，负责仪表盘数据的更新和交互
 */

// 🐛 调试日志 - 检查DashboardModule是否加载

// ============================================
// 公共工具：从 Dashboard 跳转到服务器列表并附带筛选参数
// ============================================
console.log('[Dashboard] DashboardModule 开始加载');

// 仪表盘模块
window.DashboardModule = {
    // 存储进度条实例
    progressBars: {
        download: null,
        upload: null,
        downloadMobile: null,
        uploadMobile: null
    },

    /**
     * 初始化进度条
     */
    initProgressBars() {
        // 检查ProgressBar.js是否已加载
        if (typeof ProgressBar === 'undefined') {
            console.warn('ProgressBar.js未加载，将使用默认进度条');
            return;
        }

        // 使用原始进度条
        this.useOriginalProgressBars = true;
        console.log('使用原始进度条');
    },

    /**
     * 更新进度条值 - 使用新的ProgressBarManager
     * @param {number} downloadSpeed 下载速度 (bytes/s) 或百分比（兼容旧代码）
     * @param {number} uploadSpeed 上传速度 (bytes/s) 或百分比（兼容旧代码）
     * @param {Object} [config] 动画配置
     */
    updateProgressBars(downloadSpeed, uploadSpeed, config) {
        // 检查ProgressBarManager是否可用
        if (window.progressBarManager) {
            // 新模式：直接传入速度值（bytes/s），管理器内部会转换为bps
            this.updateProgressBarsWithManager(downloadSpeed, uploadSpeed, config);
            return;
        }
        
        // 兼容旧模式：传入的是百分比
        const downloadPercent = downloadSpeed;
        const uploadPercent = uploadSpeed;
        
        // 获取动画配置
        const animConfig = config || (window.AnimationSpeedControl ? window.AnimationSpeedControl.getConfig() : {
            duration: 1500,
            downloadUpdateInterval: 200,
            uploadUpdateInterval: 300,
            smoothFactor: 0.3
        });

        // 设置动画时间（毫秒）
        const animationDuration = animConfig.duration;

        // 存储当前值以检测变化
        if (!this.lastValues) {
            this.lastValues = {
                download: 0,
                upload: 0
            };
        }

        // 检测值是否变化
        const downloadChanged = Math.abs(this.lastValues.download - downloadPercent) > 1;
        const uploadChanged = Math.abs(this.lastValues.upload - uploadPercent) > 1;

        // 检查是否有SmoothTransition工具
        const hasSmoothTransition = typeof window.SmoothTransition !== 'undefined';

        // 更新PC端进度条
        if (hasSmoothTransition && downloadChanged) {
            // 使用平滑过渡进度条
            window.SmoothTransition.progressBar(
                'download-speed-progress',
                this.lastValues.download,
                downloadPercent,
                animationDuration,
                null,
                animConfig.downloadUpdateInterval // 使用配置的下载速度更新间隔
            );
        } else {
            // 回退到原始方法
            const downloadProgress = document.getElementById('download-speed-progress');
            if (downloadProgress) {
                // 移除所有动画模式类
                downloadProgress.classList.remove('progress-bar-no-animation', 'progress-bar-normal', 'progress-bar-fast');

                // 检查是否使用CSS过渡
                if (animConfig.useCssTransition) {
                    // 根据不同模式添加相应的CSS类
                    if (animConfig.enabled === false) {
                        // 关闭模式
                        downloadProgress.classList.add('progress-bar-no-animation');
                    } else if (animConfig.duration <= 300) {
                        // 迅速模式
                        downloadProgress.classList.add('progress-bar-fast');
                    } else {
                        // 正常模式
                        downloadProgress.classList.add('progress-bar-normal');
                    }
                }

                downloadProgress.style.width = `${downloadPercent}%`;
                if (downloadChanged) {
                    downloadProgress.classList.remove('progress-highlight');
                    void downloadProgress.offsetWidth;
                    downloadProgress.classList.add('progress-highlight');
                }
            }
        }

        if (hasSmoothTransition && uploadChanged) {
            // 使用平滑过渡进度条
            window.SmoothTransition.progressBar(
                'upload-speed-progress',
                this.lastValues.upload,
                uploadPercent,
                animationDuration,
                null,
                animConfig.uploadUpdateInterval // 使用配置的上传速度更新间隔
            );
        } else {
            // 回退到原始方法
            const uploadProgress = document.getElementById('upload-speed-progress');
            if (uploadProgress) {
                // 移除所有动画模式类
                uploadProgress.classList.remove('progress-bar-no-animation', 'progress-bar-normal', 'progress-bar-fast');

                // 检查是否使用CSS过渡
                if (animConfig.useCssTransition) {
                    // 根据不同模式添加相应的CSS类
                    if (animConfig.enabled === false) {
                        // 关闭模式
                        uploadProgress.classList.add('progress-bar-no-animation');
                    } else if (animConfig.duration <= 300) {
                        // 迅速模式
                        uploadProgress.classList.add('progress-bar-fast');
                    } else {
                        // 正常模式
                        uploadProgress.classList.add('progress-bar-normal');
                    }
                }

                uploadProgress.style.width = `${uploadPercent}%`;
                if (uploadChanged) {
                    uploadProgress.classList.remove('progress-highlight');
                    void uploadProgress.offsetWidth;
                    uploadProgress.classList.add('progress-highlight');
                }
            }
        }

        // 更新移动端进度条
        if (hasSmoothTransition && downloadChanged) {
            window.SmoothTransition.progressBar(
                'mobile-download-speed-progress',
                this.lastValues.download,
                downloadPercent,
                animationDuration,
                null,
                animConfig.downloadUpdateInterval // 使用配置的下载速度更新间隔
            );
        } else {
            const mobileDownloadProgress = document.getElementById('mobile-download-speed-progress');
            if (mobileDownloadProgress) {
                // 移除所有动画模式类
                mobileDownloadProgress.classList.remove('progress-bar-no-animation', 'progress-bar-normal', 'progress-bar-fast');

                // 检查是否使用CSS过渡
                if (animConfig.useCssTransition) {
                    // 根据不同模式添加相应的CSS类
                    if (animConfig.enabled === false) {
                        // 关闭模式
                        mobileDownloadProgress.classList.add('progress-bar-no-animation');
                    } else if (animConfig.duration <= 300) {
                        // 迅速模式
                        mobileDownloadProgress.classList.add('progress-bar-fast');
                    } else {
                        // 正常模式
                        mobileDownloadProgress.classList.add('progress-bar-normal');
                    }
                }

                mobileDownloadProgress.style.width = `${downloadPercent}%`;
                if (downloadChanged) {
                    mobileDownloadProgress.classList.remove('progress-highlight');
                    void mobileDownloadProgress.offsetWidth;
                    mobileDownloadProgress.classList.add('progress-highlight');
                }
            }
        }

        if (hasSmoothTransition && uploadChanged) {
            window.SmoothTransition.progressBar(
                'mobile-upload-speed-progress',
                this.lastValues.upload,
                uploadPercent,
                animationDuration,
                null,
                animConfig.uploadUpdateInterval // 使用配置的上传速度更新间隔
            );
        } else {
            const mobileUploadProgress = document.getElementById('mobile-upload-speed-progress');
            if (mobileUploadProgress) {
                // 移除所有动画模式类
                mobileUploadProgress.classList.remove('progress-bar-no-animation', 'progress-bar-normal', 'progress-bar-fast');

                // 检查是否使用CSS过渡
                if (animConfig.useCssTransition) {
                    // 根据不同模式添加相应的CSS类
                    if (animConfig.enabled === false) {
                        // 关闭模式
                        mobileUploadProgress.classList.add('progress-bar-no-animation');
                    } else if (animConfig.duration <= 300) {
                        // 迅速模式
                        mobileUploadProgress.classList.add('progress-bar-fast');
                    } else {
                        // 正常模式
                        mobileUploadProgress.classList.add('progress-bar-normal');
                    }
                }

                mobileUploadProgress.style.width = `${uploadPercent}%`;
                if (uploadChanged) {
                    mobileUploadProgress.classList.remove('progress-highlight');
                    void mobileUploadProgress.offsetWidth;
                    mobileUploadProgress.classList.add('progress-highlight');
                }
            }
        }

        // 更新存储的值
        this.lastValues.download = downloadPercent;
        this.lastValues.upload = uploadPercent;
    },
    
    /**
     * 使用ProgressBarManager更新进度条（新方法）
     */
    updateProgressBarsWithManager(downloadSpeed, uploadSpeed, config) {
        // 获取动画配置
        const animConfig = config || (window.AnimationSpeedControl ? window.AnimationSpeedControl.getConfig() : {
            duration: 1500,
            downloadUpdateInterval: 200,
            uploadUpdateInterval: 300,
            smoothFactor: 0.3,
            useSmoothTransition: typeof window.SmoothTransition !== 'undefined'
        });
        
        // 转换为bits per second (乘以8)
        const downloadBps = downloadSpeed * 8;
        const uploadBps = uploadSpeed * 8;
        
        // 批量更新4个进度条（采用 bucket 简化自适应，提高灵敏度）
        // 统一使用 'net' 作为 baselineKey，实现上下行共享档位基准
        window.progressBarManager.updateMultipleProgressBars([
            {
                elementId: 'download-speed-progress',
                currentValue: downloadBps,
                maxValue: null, // 使用自适应基准
                options: {
                    ...animConfig,
                    baselineMethod: 'bucket',
                    updateInterval: animConfig.downloadUpdateInterval,
                    baselineKey: 'net'
                }
            },
            {
                elementId: 'upload-speed-progress',
                currentValue: uploadBps,
                maxValue: null, // 使用自适应基准
                options: {
                    ...animConfig,
                    baselineMethod: 'bucket',
                    updateInterval: animConfig.uploadUpdateInterval,
                    baselineKey: 'net'
                }
            },
            {
                elementId: 'mobile-download-speed-progress',
                currentValue: downloadBps,
                maxValue: null, // 使用自适应基准
                options: {
                    ...animConfig,
                    baselineMethod: 'bucket',
                    updateInterval: animConfig.downloadUpdateInterval,
                    baselineKey: 'net'
                }
            },
            {
                elementId: 'mobile-upload-speed-progress',
                currentValue: uploadBps,
                maxValue: null, // 使用自适应基准
                options: {
                    ...animConfig,
                    baselineMethod: 'bucket',
                    updateInterval: animConfig.uploadUpdateInterval,
                    baselineKey: 'net'
                }
            }
        ]);

        // 更新aria属性
        this.updateProgressBarAria('download-speed-progress', downloadBps);
        this.updateProgressBarAria('upload-speed-progress', uploadBps);
        this.updateProgressBarAria('mobile-download-speed-progress', downloadBps);
        this.updateProgressBarAria('mobile-upload-speed-progress', uploadBps);
    },

    /**
     * 更新仪表盘网络数据
     * @param {Object} netStats 网络统计数据
     * @param {boolean} fromCache 是否从缓存加载
     * @param {boolean} forceUpdate 是否强制更新（忽略恢复状态）
     */
    updateDashboardNetwork(netStats, fromCache = false, forceUpdate = false) {
        if (!netStats) return;

        // 检查标签页是否处于恢复状态
        const isRecovering = window.TabLifecycleHandler && window.TabLifecycleHandler.isRecoveringFromSleep;
        const shouldBlock = window.TabLifecycleHandler && window.TabLifecycleHandler.blockDataUpdates;

        // 如果正在从休眠中恢复且应该阻止数据更新，且不是强制更新
        if (isRecovering && shouldBlock && !forceUpdate) {
            console.log('[仪表盘] 标签页正在从休眠中恢复，暂停网络数据更新');

            // 如果标签页正在从休眠中恢复，缓存最新数据而不更新UI
            if (window.TabLifecycleHandler.cachedNetworkData) {
                // 如果新数据的时间戳更新，替换缓存的数据
                const cachedTimestamp = window.TabLifecycleHandler.cachedNetworkTimestamp || 0;
                const currentTimestamp = Date.now();

                if (currentTimestamp > cachedTimestamp) {
                    window.TabLifecycleHandler.cachedNetworkData = netStats;
                    window.TabLifecycleHandler.cachedNetworkTimestamp = currentTimestamp;
                    console.log(`[仪表盘] 缓存最新网络数据，时间戳: ${currentTimestamp}`);
                }
            } else {
                // 初始化缓存
                window.TabLifecycleHandler.cachedNetworkData = netStats;
                window.TabLifecycleHandler.cachedNetworkTimestamp = Date.now();
                console.log('[仪表盘] 初始化网络数据缓存');
            }

            return; // 不继续更新UI
        }

        // 如果是从缓存加载或强制更新，应用平滑过渡
        if (fromCache || forceUpdate) {
            // 应用平滑过渡样式
            const transitionElements = document.querySelectorAll('.network-speed, .network-total');
            transitionElements.forEach(el => {
                if (!el) return;
                // 保存原始过渡样式
                const originalTransition = el.style.transition;
                // 应用新的过渡样式
                el.style.transition = `all 1500ms ease-in-out`;
                // 恢复原始样式
                setTimeout(() => {
                    el.style.transition = originalTransition;
                }, 1600);
            });
            console.log(`[仪表盘] 应用网络数据平滑过渡样式`);
        }

        // 禁用日志输出
        // 如果需要调试，请手动打开以下代码
        /*
        if (window.DEBUG_MODE) {
            console.log('更新仪表盘网络数据:', netStats);
        }
        */

        // 检查页面可见性状态
        const isPageVisible = !document.hidden;
        const now = Date.now();

        // 获取页面可见性变化时间
        const lastVisibilityChange = parseInt(localStorage.getItem('last_visibility_change') || '0', 10);
        const timeSinceVisibilityChange = now - lastVisibilityChange;

        // 检测页面是否刚刚变为可见
        const justBecameVisible = isPageVisible && timeSinceVisibilityChange < 1000;

        // 确保所有值都是有效的数字
        const safeNetStats = {
            downloadSpeed: Math.max(0, Number(netStats.downloadSpeed) || 0),
            uploadSpeed: Math.max(0, Number(netStats.uploadSpeed) || 0),
            totalDownload: Math.max(0, Number(netStats.totalDownload) || 0),
            totalUpload: Math.max(0, Number(netStats.totalUpload) || 0)
        };

        // 获取动画配置
        let animConfig = window.AnimationSpeedControl ? window.AnimationSpeedControl.getConfig() : {
            duration: 1500,
            downloadUpdateInterval: 200,
            uploadUpdateInterval: 300,
            smoothFactor: 0.3
        };

        // 如果页面刚刚变为可见，并且不可见时间超过5秒，应用平滑过渡
        if (justBecameVisible && timeSinceVisibilityChange > 5000 && this.lastSpeedValues) {
            // 应用平滑因子，避免数据突变
            const smoothFactor = animConfig.smoothFactor; // 平滑因子，越小过渡越平滑

            // 平滑过渡到新值
            safeNetStats.downloadSpeed = this.lastSpeedValues.download / 8 * (1 - smoothFactor) + safeNetStats.downloadSpeed * smoothFactor;
            safeNetStats.uploadSpeed = this.lastSpeedValues.upload / 8 * (1 - smoothFactor) + safeNetStats.uploadSpeed * smoothFactor;

            // 禁用日志输出
            // if (window.DEBUG_MODE) {
            //     console.log('应用平滑过渡，调整后的数据:', safeNetStats);
            // }
        }

        // 网络数据不再缓存，直接使用WebSocket实时数据
        // 基于第一性原理：流量数据是累积性的，应该始终获取最新值
        // 注释掉实时更新日志，避免过多输出
        // console.debug('[仪表盘] 网络数据缓存已禁用，使用WebSocket实时数据');

        // 更新进度条 - 现在直接传递速度值
        // ProgressBarManager会自动计算自适应的百分比
        if (window.progressBarManager) {
            // 新模式：直接传递速度值（bytes/s）
            this.updateProgressBars(netStats.downloadSpeed, netStats.uploadSpeed, animConfig);
        } else {
            // 降级模式：使用旧的百分比计算
            const maxBandwidth = 1000 * 1024 * 1024; // 1Gbps
            const downloadPercent = Math.min(100, (netStats.downloadSpeed * 8 / maxBandwidth) * 100);
            const uploadPercent = Math.min(100, (netStats.uploadSpeed * 8 / maxBandwidth) * 100);
            this.updateProgressBars(downloadPercent, uploadPercent, animConfig);
        }

        // 存储上一次的值
        if (!this.lastSpeedValues) {
            this.lastSpeedValues = {
                download: 0,
                upload: 0
            };
        }

        // 使用之前定义的animConfig变量

        // 设置动画时间（毫秒）
        let animationDuration = animConfig.duration;

        // 禁用日志输出
        // 如果需要调试，请手动打开以下代码
        /*
        if (window.DEBUG_MODE) {
            console.debug('当前动画配置:', {
                duration: animationDuration,
                downloadUpdateInterval: animConfig.downloadUpdateInterval,
                uploadUpdateInterval: animConfig.uploadUpdateInterval,
                smoothFactor: animConfig.smoothFactor
            });
        }
        */

        // 如果页面刚刚变为可见，并且不可见时间超过5秒，使用更长的动画时间
        if (justBecameVisible && timeSinceVisibilityChange > 5000) {
            animationDuration = animConfig.duration * 1.5; // 使用更长的动画时间，使过渡更加平滑
            // 禁用日志输出
            // if (window.DEBUG_MODE) {
            //     console.log('页面刚刚变为可见，使用更长的动画时间:', animationDuration);
            // }
        }

        // 检查是否有SmoothTransition工具
        const hasSmoothTransition = typeof window.SmoothTransition !== 'undefined';

        // 准备数据
        const newDownloadSpeed = netStats.downloadSpeed * 8;
        const newUploadSpeed = netStats.uploadSpeed * 8;
        const downloadSpeedText = window.strbps(newDownloadSpeed);
        const uploadSpeedText = window.strbps(newUploadSpeed);
        const totalDownloadText = window.strB(netStats.totalDownload);
        const totalUploadText = window.strB(netStats.totalUpload);

        // 检测值是否变化
        // 如果是从缓存加载，总是触发动画效果
        const downloadSpeedChanged = fromCache || Math.abs(this.lastSpeedValues.download - newDownloadSpeed) > 1024;
        const uploadSpeedChanged = fromCache || Math.abs(this.lastSpeedValues.upload - newUploadSpeed) > 1024;

        // 更新PC端实时带宽 - 使用新的 MetricFormatter 系统
        this.updateDesktopSpeed(newDownloadSpeed, newUploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration);

        // 更新移动端实时带宽 - 使用新的 MetricFormatter 系统
        this.updateMobileSpeed(newDownloadSpeed, newUploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration);

        // 更新PC端总流量（依旧用于回退，UI以徽章为准）
        this.updateTotalTraffic(netStats.totalDownload, netStats.totalUpload);
        // 更新总流量小标签（标题右侧 + 实时带宽卡片右上角 + 移动端标题右侧）
        try {
            const d = document.getElementById('total-badge-download-text'); // 标题右侧桌面
            const u = document.getElementById('total-badge-upload-text');
            // 移动端实时带宽容器内的总流量标签已移除
            const dl = window.MetricFormatter ? window.MetricFormatter.formatBytes(netStats.totalDownload) : this.formatBytesSimple(netStats.totalDownload);
            const ul = window.MetricFormatter ? window.MetricFormatter.formatBytes(netStats.totalUpload) : this.formatBytesSimple(netStats.totalUpload);
            const dlText = `${dl.value} ${dl.unit}`;
            const ulText = `${ul.value} ${ul.unit}`;
            if (d) d.textContent = dlText;
            if (u) u.textContent = ulText;
            
            // 更新移动端总流量徽章
            const mobileD = document.getElementById('mobile-total-badge-download-text');
            const mobileU = document.getElementById('mobile-total-badge-upload-text');
            if (mobileD) mobileD.textContent = dlText;
            if (mobileU) mobileU.textContent = ulText;
        } catch (e) { /* ignore */ }

        // 更新移动端总流量
        // 改为通过小标签展示，移除原 DOM 批量写入

        // 更新存储的值
        this.lastSpeedValues.download = newDownloadSpeed;
        this.lastSpeedValues.upload = newUploadSpeed;
    },

    /**
     * 更新桌面端速度显示 - 使用新的 MetricFormatter 系统
     * @param {number} downloadSpeed - 下载速度 (bps)
     * @param {number} uploadSpeed - 上传速度 (bps)
     * @param {boolean} hasSmoothTransition - 是否启用平滑过渡
     * @param {boolean} downloadSpeedChanged - 下载速度是否变化
     * @param {boolean} uploadSpeedChanged - 上传速度是否变化
     * @param {number} animationDuration - 动画持续时间
     */
    updateDesktopSpeed(downloadSpeed, uploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration) {
        this.updateDesktopSpeedSimplified(downloadSpeed, uploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration);
    },

    /**
     * 简化的桌面端速度更新方法 - 使用独立的数值和单位元素
     */
    updateDesktopSpeedSimplified(downloadSpeed, uploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration) {
        // 获取容器元素
        const downloadContainer = document.getElementById('current-download-speed');
        const uploadContainer = document.getElementById('current-upload-speed');
        
        // 确保容器存在
        if (!downloadContainer || !uploadContainer) {
            console.error('Speed containers not found');
            return;
        }
        
        // 确保并获取子元素（非破坏性，不清空徽章等自定义节点，且移除遗留文本节点）
        const { valueEl: downloadValueEl, unitEl: downloadUnitEl } = this.ensureSpeedChildren(
            downloadContainer,
            'current-download-speed-value',
            'current-download-speed-unit'
        );
        const { valueEl: uploadValueEl, unitEl: uploadUnitEl } = this.ensureSpeedChildren(
            uploadContainer,
            'current-upload-speed-value',
            'current-upload-speed-unit'
        );

        // 格式化速度数据
        const downloadResult = window.MetricFormatter ? 
            window.MetricFormatter.formatSpeed(downloadSpeed) : 
            this.formatSpeedSimple(downloadSpeed);
        const uploadResult = window.MetricFormatter ? 
            window.MetricFormatter.formatSpeed(uploadSpeed) : 
            this.formatSpeedSimple(uploadSpeed);
            

        // 更新下载速度
        if (downloadValueEl && downloadUnitEl) {
            // 始终更新单位，确保初始化时有内容
            downloadUnitEl.textContent = downloadResult.unit;

            // 动画或直接更新数值
            if (hasSmoothTransition && downloadSpeedChanged && window.SmoothTransition) {
                window.SmoothTransition.speedtestStyleTransition(
                    'current-download-speed-value',
                    this.lastSpeedValues.download,
                    downloadSpeed,
                    animationDuration,
                    (value) => {
                        const result = window.MetricFormatter ? 
                            window.MetricFormatter.formatSpeed(value) : 
                            this.formatSpeedSimple(value);
                        // 动画过程中也更新单位（如果变化）
                        if (downloadUnitEl.textContent !== result.unit) {
                            downloadUnitEl.textContent = result.unit;
                        }
                        return result.value;
                    }
                );
            } else {
                downloadValueEl.textContent = downloadResult.value;
            }
        }

        // 更新上传速度
        if (uploadValueEl && uploadUnitEl) {
            // 始终更新单位，确保初始化时有内容
            uploadUnitEl.textContent = uploadResult.unit;

            // 动画或直接更新数值
            if (hasSmoothTransition && uploadSpeedChanged && window.SmoothTransition) {
                window.SmoothTransition.speedtestStyleTransition(
                    'current-upload-speed-value',
                    this.lastSpeedValues.upload,
                    uploadSpeed,
                    animationDuration,
                    (value) => {
                        const result = window.MetricFormatter ? 
                            window.MetricFormatter.formatSpeed(value) : 
                            this.formatSpeedSimple(value);
                        // 动画过程中也更新单位（如果变化）
                        if (uploadUnitEl.textContent !== result.unit) {
                            uploadUnitEl.textContent = result.unit;
                        }
                        return result.value;
                    }
                );
            } else {
                uploadValueEl.textContent = uploadResult.value;
            }
        }

        // 添加调试信息
    },

    /**
     * 简单的速度格式化函数（备用）
     */
    formatSpeedSimple(bps) {
        if (isNaN(bps) || bps === 0) return { value: '0', unit: 'bps' };
        const k = 1024;
        const sizes = ['bps', 'Kbps', 'Mbps', 'Gbps', 'Tbps'];
        const i = Math.floor(Math.log(Math.abs(bps)) / Math.log(k));
        const unitIndex = Math.min(i, sizes.length - 1);
        const value = (bps / Math.pow(k, unitIndex)).toFixed(2);
        return {
            value: parseFloat(value).toString(),
            unit: sizes[unitIndex]
        };
    },

    /**
     * 更新总流量显示 - 使用简化版系统
     */
    updateTotalTraffic(totalDownload, totalUpload) {
        // 获取元素
        const downloadValueEl = document.getElementById('total-download-value');
        const downloadUnitEl = document.getElementById('total-download-unit');
        const uploadValueEl = document.getElementById('total-upload-value');
        const uploadUnitEl = document.getElementById('total-upload-unit');

        // 格式化流量数据
        const downloadResult = window.MetricFormatter ? 
            window.MetricFormatter.formatBytes(totalDownload) : 
            this.formatBytesSimple(totalDownload);
        const uploadResult = window.MetricFormatter ? 
            window.MetricFormatter.formatBytes(totalUpload) : 
            this.formatBytesSimple(totalUpload);

        // 更新下载总流量
        if (downloadValueEl && downloadUnitEl) {
            downloadValueEl.textContent = downloadResult.value;
            downloadUnitEl.textContent = downloadResult.unit;
        } else {
            // 回退到旧方式
            const totalDownloadEl = document.getElementById('total-download');
            if (totalDownloadEl && !downloadValueEl) {
                totalDownloadEl.innerHTML = window.strB ? window.strB(totalDownload) : totalDownload + ' B';
            }
        }

        // 更新上传总流量
        if (uploadValueEl && uploadUnitEl) {
            uploadValueEl.textContent = uploadResult.value;
            uploadUnitEl.textContent = uploadResult.unit;
        } else {
            // 回退到旧方式
            const totalUploadEl = document.getElementById('total-upload');
            if (totalUploadEl && !uploadValueEl) {
                totalUploadEl.innerHTML = window.strB ? window.strB(totalUpload) : totalUpload + ' B';
            }
        }
    },

    /**
     * 简单的字节格式化函数（备用）
     */
    formatBytesSimple(bytes) {
        if (isNaN(bytes) || bytes === 0) return { value: '0', unit: 'B' };
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(Math.abs(bytes)) / Math.log(k));
        const unitIndex = Math.min(i, sizes.length - 1);
        const value = (bytes / Math.pow(k, unitIndex)).toFixed(2);
        return {
            value: parseFloat(value).toString(),
            unit: sizes[unitIndex]
        };
    },

    /**
     * 更新移动端速度显示 - 使用新的 MetricFormatter 系统
     * @param {number} downloadSpeed - 下载速度 (bps)
     * @param {number} uploadSpeed - 上传速度 (bps)
     * @param {boolean} hasSmoothTransition - 是否启用平滑过渡
     * @param {boolean} downloadSpeedChanged - 下载速度是否变化
     * @param {boolean} uploadSpeedChanged - 上传速度是否变化
     * @param {number} animationDuration - 动画持续时间
     */
    updateMobileSpeed(downloadSpeed, uploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration) {
        this.updateMobileSpeedSimplified(downloadSpeed, uploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration);
    },

    /**
     * 简化的移动端速度更新方法 - 使用独立的数值和单位元素（与PC端统一）
     */
    updateMobileSpeedSimplified(downloadSpeed, uploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration) {
        // 确保子节点存在（非破坏性）
        const mobileDownloadContainer = document.getElementById('mobile-download-speed');
        const mobileUploadContainer = document.getElementById('mobile-upload-speed');
        const { valueEl: downloadValueEl, unitEl: downloadUnitEl } = this.ensureSpeedChildren(
            mobileDownloadContainer,
            'mobile-download-speed-value',
            'mobile-download-speed-unit'
        );
        const { valueEl: uploadValueEl, unitEl: uploadUnitEl } = this.ensureSpeedChildren(
            mobileUploadContainer,
            'mobile-upload-speed-value',
            'mobile-upload-speed-unit'
        );

        // 格式化速度数据
        const downloadResult = window.MetricFormatter ? 
            window.MetricFormatter.formatSpeed(downloadSpeed) : 
            this.formatSpeedSimple(downloadSpeed);
        const uploadResult = window.MetricFormatter ? 
            window.MetricFormatter.formatSpeed(uploadSpeed) : 
            this.formatSpeedSimple(uploadSpeed);

        // 更新下载速度
        if (downloadValueEl && downloadUnitEl) {
            // 始终更新单位，确保初始化时有内容
            downloadUnitEl.textContent = downloadResult.unit;

            // 动画或直接更新数值
            if (hasSmoothTransition && downloadSpeedChanged && window.SmoothTransition) {
                window.SmoothTransition.speedtestStyleTransition(
                    'mobile-download-speed-value',
                    this.lastSpeedValues.download,
                    downloadSpeed,
                    animationDuration,
                    (value) => {
                        const result = window.MetricFormatter ? 
                            window.MetricFormatter.formatSpeed(value) : 
                            this.formatSpeedSimple(value);
                        return result.value;
                    }
                );
            } else {
                downloadValueEl.textContent = downloadResult.value;
            }
        }

        // 更新上传速度
        if (uploadValueEl && uploadUnitEl) {
            // 始终更新单位，确保初始化时有内容
            uploadUnitEl.textContent = uploadResult.unit;

            // 动画或直接更新数值
            if (hasSmoothTransition && uploadSpeedChanged && window.SmoothTransition) {
                window.SmoothTransition.speedtestStyleTransition(
                    'mobile-upload-speed-value',
                    this.lastSpeedValues.upload,
                    uploadSpeed,
                    animationDuration,
                    (value) => {
                        const result = window.MetricFormatter ? 
                            window.MetricFormatter.formatSpeed(value) : 
                            this.formatSpeedSimple(value);
                        return result.value;
                    }
                );
            } else {
                uploadValueEl.textContent = uploadResult.value;
            }
        }

    },

    /**
     * 更新总体统计数据
     * @param {Object} totals 总体统计数据
     */
    updateTotalStats(totals) {
        try {
            
            // 1. 数据验证
            if (!totals || typeof totals !== 'object') {
                console.log('[Dashboard] 数据验证失败，totals无效');
                return;
            }

            // 2. 确保所有数值有效
            const stats = {
                // 兼容两种格式：直接数字或对象格式
                nodes: typeof totals.nodes === 'object' ?
                      Object.keys(totals.nodes || {}).length :
                      Math.max(0, Number(totals.nodes) || 0),
                online: Math.max(0, Number(totals.online) || 0),
                offline: Math.max(0, Number(totals.offline) || 0),
                download: Math.max(0, Number(totals.download) || 0),
                upload: Math.max(0, Number(totals.upload) || 0),
                downloadTotal: Math.max(0, Number(totals.downloadTotal) || 0),
                uploadTotal: Math.max(0, Number(totals.uploadTotal) || 0)
            };
            

            // 3. 更新显示元素
            const elements = {
                totalNodes: document.getElementById('total-nodes'),
                onlineNodes: document.getElementById('online-nodes'),
                offlineNodes: document.getElementById('offline-nodes'),
                currentNetIn: document.getElementById('current-download-speed'),
                currentNetOut: document.getElementById('current-upload-speed'),
                totalNetIn: document.getElementById('total-download'),
                totalNetOut: document.getElementById('total-upload'),
                expiringNodes3: document.getElementById('expiring-nodes-3'),
                expiringNodes7: document.getElementById('expiring-nodes-7'),
                expiringNodes30: document.getElementById('expiring-nodes-30'),
                regionStats: document.getElementById('region-stats')
            };

            // 4. 更新基础统计
            
            if (elements.totalNodes) elements.totalNodes.textContent = stats.nodes;
            if (elements.onlineNodes) elements.onlineNodes.textContent = stats.online;
            if (elements.offlineNodes) elements.offlineNodes.textContent = stats.offline;
            // 实时带宽显示由 updateDashboardNetwork 负责，这里不再写入以避免清空徽章/结构
            if (elements.totalNetIn) elements.totalNetIn.textContent = window.strB(stats.downloadTotal);
            if (elements.totalNetOut) elements.totalNetOut.textContent = window.strB(stats.uploadTotal);

            // 6. 计算不同天数内到期的节点
            const now = Math.floor(Date.now() / 1000);
            const threeDaysFromNow = now + (3 * 24 * 60 * 60);
            const sevenDaysFromNow = now + (7 * 24 * 60 * 60);
            const thirtyDaysFromNow = now + (30 * 24 * 60 * 60);
            let expiringCount3Days = 0;
            let expiringCount7Days = 0;
            let expiringCount30Days = 0;

            // 7. 处理每个节点 - 提取到期时间检查
            Object.entries(totals.nodes || {}).forEach(([, node]) => {
                // 跳过非节点数据
                if (!node || typeof node !== 'object' || !node.name) return;

                // 检查到期时间
                if (node.expire_time && node.expire_time > now) {
                    // 3天内到期
                    if (node.expire_time <= threeDaysFromNow) {
                        expiringCount3Days++;
                        expiringCount7Days++;
                        expiringCount30Days++;
                    }
                    // 7天内到期
                    else if (node.expire_time <= sevenDaysFromNow) {
                        expiringCount7Days++;
                        expiringCount30Days++;
                    }
                    // 30天内到期
                    else if (node.expire_time <= thirtyDaysFromNow) {
                        expiringCount30Days++;
                    }
                }
            });

            // 8. 处理节点地区信息，便于后续筛选
            let regionCount = 0;
            Object.entries(totals.nodes || {}).forEach(([, node]) => {
                if (!node || typeof node !== 'object' || !node.name) return;

                // 将地区信息添加到节点数据上，用于后续筛选
                if (!node.regionCode) {
                    if (node.data?.location?.code) {
                        node.regionCode = node.data.location.code;
                        regionCount++;
                    }
                }
            });

            // 9. 更新地区统计
            if (window.RegionStatsModule) {
                // 不再主动调用update，而是让RegionStatsModule自行决定何时更新
                // RegionStatsModule从window.lastNodeData获取数据
                window.lastNodeData = totals.nodes || {};
            }

            // 10. 更新分组统计和到期时间显示
            if (totals.groups) {
                Object.entries(totals.groups).forEach(([groupId, groupStats]) => {
                    const countElement = document.getElementById(`group-${groupId}-count-tab`);
                    if (countElement) {
                        countElement.textContent = `${groupStats.online}/${groupStats.total}`;
                    }
                });
            }

            // 更新到期时间显示
            if (elements.expiringNodes3) {
                elements.expiringNodes3.textContent = expiringCount3Days;
            }
            if (elements.expiringNodes7) {
                elements.expiringNodes7.textContent = expiringCount7Days;
            }
            if (elements.expiringNodes30) {
                elements.expiringNodes30.textContent = expiringCount30Days;
            }
        } catch (error) {
            console.error('更新总体统计出错:', error);
        }
    },

    /**
     * 初始化地区统计功能
     */
    initRegionStats() {
        // 检查是否已经加载RegionStatsModule
        if (!window.RegionStatsModule) {
            // 检查当前页面是否需要地区统计功能
            // 只有仪表板页面才需要，定义为存在region-stats元素的页面
            const needRegionStats = document.getElementById('region-stats') || document.getElementById('region-stats-mobile');

            if (needRegionStats) {
                console.warn('RegionStatsModule未加载，地区统计功能可能不可用');
            }
        }
    },

    /**
     * 初始化到期节点筛选功能
     */
    initExpiryFilters() {
        // 获取所有到期筛选按钮
        const expiryFilters = document.querySelectorAll('.expiry-filter');

        // 为每个到期筛选按钮添加点击事件
        expiryFilters.forEach(filter => {
            filter.addEventListener('click', (event) => {
                // 防止事件冒泡到文档级别
                event.stopPropagation();

                const days = parseInt(filter.dataset.days, 10);
                const isActive = filter.classList.contains('active');
                
                // 更新激活状态
                expiryFilters.forEach(f => f.classList.remove('active'));
                if (!isActive) {
                    filter.classList.add('active');
                }
                
                if (window.UF) {
                    // 如果点击已激活的按钮，则取消筛选
                    window.UF.filterByExpiry(isActive ? '' : (isNaN(days) ? '' : days));
                }
            });
        });

        // 初始化清除筛选按钮
        this.initClearFiltersButton();
    },

    /**
     * 初始化节点状态筛选功能
     */
    initStatusFilters() {
        // 获取所有状态筛选按钮
        const statusFilters = document.querySelectorAll('.status-filter');

        // 默认不激活任何按钮（显示所有）
        // 注释掉默认激活 ALL 按钮，因为现在支持组合筛选
        // const allButton = document.querySelector('.status-filter[data-status="ALL"]');
        // if (allButton) {
        //     allButton.classList.add('active');
        // }

        // 为每个状态筛选按钮添加点击事件
        statusFilters.forEach(filter => {
            filter.addEventListener('click', (event) => {
                event.stopPropagation();
                const status = filter.dataset.status;
                const isActive = filter.classList.contains('active');
                
                // 🐛 调试日志
                console.log('[Dashboard] 状态筛选按钮点击:', status, '当前是否激活:', isActive);
                
                // 如果点击的是ALL按钮
                if (status === 'ALL') {
                    // 清除所有状态筛选
                    statusFilters.forEach(f => f.classList.remove('active'));
                    if (window.UF) {
                        window.UF.filterByStatus('ALL');
                    }
                } else {
                    // 切换当前按钮的激活状态
                    if (isActive) {
                        filter.classList.remove('active');
                        // 取消筛选，回到ALL
                        if (window.UF) {
                            window.UF.filterByStatus('ALL');
                        }
                    } else {
                        // 清除其他状态筛选，激活当前
                        statusFilters.forEach(f => f.classList.remove('active'));
                        filter.classList.add('active');
                        if (window.UF) {
                            window.UF.filterByStatus(status);
                        }
                    }
                }
            });
        });
    },

    /**
     * 初始化清除筛选按钮
     */
    initClearFiltersButton() {
        const clearBtn = document.getElementById('clear-all-filters');
        if (!clearBtn) return;
        
        // 清除按钮点击事件
        clearBtn.addEventListener('click', () => {
            if (window.UF) {
                window.UF.reset();
                
                // 更新所有筛选按钮状态
                document.querySelectorAll('.status-filter, .expiry-filter, .region-tag').forEach(btn => {
                    btn.classList.remove('active');
                });
                
                // 重置分组筛选到 'all'
                const groupOptions = document.querySelectorAll('.group-option');
                groupOptions.forEach(opt => opt.classList.remove('active'));
                const allOption = document.querySelector('.group-option[data-group="all"]');
                if (allOption) {
                    allOption.classList.add('active');
                    const currentGroupText = document.getElementById('current-group-text');
                    if (currentGroupText) {
                        currentGroupText.textContent = '全部节点';
                    }
                }
                
                // 更新分组按钮状态
                const groupBtn = document.getElementById('group-dropdown-btn');
                if (groupBtn) {
                    groupBtn.classList.remove('has-active-filter');
                }
                
                // 隐藏清除按钮
                clearBtn.classList.add('hidden');
            }
        });

        // 监听筛选状态变化，显示/隐藏清除按钮
        if (window.UF) {
            window.UF.onChange((state) => {
                const hasFilters = state.group !== 'all' || 
                                  state.status !== 'ALL' || 
                                  state.expiry !== '' || 
                                  state.region !== '';
                
                clearBtn.classList.toggle('hidden', !hasFilters);
                
                // 更新筛选按钮的激活状态
                // 更新状态筛选按钮
                document.querySelectorAll('.status-filter').forEach(btn => {
                    const btnStatus = btn.getAttribute('data-status');
                    btn.classList.toggle('active', btnStatus === state.status && state.status !== 'ALL');
                });
                
                // 更新到期筛选按钮
                document.querySelectorAll('.expiry-filter').forEach(btn => {
                    const btnDays = btn.getAttribute('data-days');
                    btn.classList.toggle('active', btnDays === state.expiry);
                });
                
                // 更新地区筛选标签
                document.querySelectorAll('.region-tag').forEach(tag => {
                    const tagRegion = tag.getAttribute('data-region');
                    tag.classList.toggle('active', tagRegion === state.region);
                });
                
                // 更新分组筛选状态
                const groupOptions = document.querySelectorAll('.group-option');
                groupOptions.forEach(opt => {
                    const optGroup = opt.getAttribute('data-group');
                    opt.classList.toggle('active', optGroup === state.group);
                });
                
                // 更新分组按钮指示
                const groupBtn = document.getElementById('group-dropdown-btn');
                if (groupBtn) {
                    groupBtn.classList.toggle('has-active-filter', state.group !== 'all');
                }
            });
        }
    },

    /**
     * 初始化页面可见性监听
     */
    initVisibilityTracking() {
        // 记录初始可见性状态
        localStorage.setItem('last_visibility_change', Date.now().toString());

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            const now = Date.now();
            const isVisible = !document.hidden;

            if (window.DEBUG_MODE) {
                console.log(`页面可见性变化: ${isVisible ? '可见' : '不可见'}`);
            }

            // 更新可见性变化时间戳
            localStorage.setItem('last_visibility_change', now.toString());

            // 如果页面变为可见，清除缓存的带宽数据
            if (isVisible) {
                // 检查不可见时间是否超过5秒
                const lastHiddenTime = parseInt(localStorage.getItem('last_hidden_time') || '0', 10);
                const hiddenDuration = now - lastHiddenTime;

                if (hiddenDuration > 5000) {
                    if (window.DEBUG_MODE) {
                        console.log(`页面不可见时间超过5秒: ${hiddenDuration}ms，将应用平滑过渡`);
                    }
                }
            } else {
                // 记录页面变为不可见的时间
                localStorage.setItem('last_hidden_time', now.toString());
            }
        });
    },

    /**
     * 初始化网络数据显示
     */
    initNetworkDisplay() {
        // 显示加载状态
        const loadingText = '--';

        // 设置所有网络数据显示为加载状态
        const elements = {
            downloadSpeed: document.getElementById('current-download-speed'),
            uploadSpeed: document.getElementById('current-upload-speed'),
            totalDownload: document.getElementById('total-download'),
            totalUpload: document.getElementById('total-upload'),
            mobileDownloadSpeeds: document.querySelectorAll('.mobile-download-speed'),
            mobileUploadSpeeds: document.querySelectorAll('.mobile-upload-speed'),
            mobileTotalDownloads: document.querySelectorAll('.mobile-total-download'),
            mobileTotalUploads: document.querySelectorAll('.mobile-total-upload')
        };

        // 设置进度条初始宽度为0
        const progressBars = {
            downloadProgress: document.getElementById('download-speed-progress'),
            uploadProgress: document.getElementById('upload-speed-progress'),
            mobileDownloadProgress: document.getElementById('mobile-download-speed-progress'),
            mobileUploadProgress: document.getElementById('mobile-upload-speed-progress')
        };

        // 桌面端：确保子节点并设置占位
        const dd = this.ensureSpeedChildren(elements.downloadSpeed, 'current-download-speed-value', 'current-download-speed-unit');
        const du = this.ensureSpeedChildren(elements.uploadSpeed, 'current-upload-speed-value', 'current-upload-speed-unit');
        if (dd.valueEl) dd.valueEl.textContent = loadingText;
        if (du.valueEl) du.valueEl.textContent = loadingText;
        if (elements.totalDownload) elements.totalDownload.textContent = loadingText;
        if (elements.totalUpload) elements.totalUpload.textContent = loadingText;

        // 移动端：确保子节点并设置占位
        elements.mobileDownloadSpeeds.forEach(el => {
            const { valueEl } = this.ensureSpeedChildren(el, 'mobile-download-speed-value', 'mobile-download-speed-unit');
            if (valueEl) valueEl.textContent = loadingText;
        });
        elements.mobileUploadSpeeds.forEach(el => {
            const { valueEl } = this.ensureSpeedChildren(el, 'mobile-upload-speed-value', 'mobile-upload-speed-unit');
            if (valueEl) valueEl.textContent = loadingText;
        });
        elements.mobileTotalDownloads.forEach(el => el.textContent = loadingText);
        elements.mobileTotalUploads.forEach(el => el.textContent = loadingText);

        // 设置进度条
        if (progressBars.downloadProgress) progressBars.downloadProgress.style.width = '0%';
        if (progressBars.uploadProgress) progressBars.uploadProgress.style.width = '0%';
        if (progressBars.mobileDownloadProgress) progressBars.mobileDownloadProgress.style.width = '0%';
        if (progressBars.mobileUploadProgress) progressBars.mobileUploadProgress.style.width = '0%';
    },

    /**
     * 初始化动画速度变化事件监听器
     */
    initAnimationSpeedListener() {
        // 监听动画速度变化事件
        document.addEventListener('animation:speed:changed', (e) => {
            if (e.detail && e.detail.config) {
                if (window.DEBUG_MODE) {
                    console.log('动画速度已变化:', e.detail.speed);
                }
                // 如果需要，可以在这里添加其他处理逻辑
            }
        });
    },

    /**
     * 确保速度显示容器的子节点存在（非破坏性：不清空其他自定义节点，如档位徽章）
     * 顺序：value → unit → 其他
     */
    ensureSpeedChildren(container, valueId, unitId) {
        if (!container) return { valueEl: null, unitEl: null };
        const gear = container.querySelector('.gear-badge');

        let valueEl = document.getElementById(valueId);
        let unitEl = document.getElementById(unitId);

        if (!valueEl) {
            valueEl = document.createElement('span');
            valueEl.id = valueId;
            valueEl.className = 'text-xl font-medium metric-number tabular-nums';
            valueEl.textContent = '--';
            container.insertBefore(valueEl, gear || container.firstChild);
        }

        if (!unitEl) {
            unitEl = document.createElement('span');
            unitEl.id = unitId;
            unitEl.className = 'text-xs opacity-70 ml-1 w-10 text-left';
            unitEl.textContent = '';
            if (gear) {
                container.insertBefore(unitEl, gear);
            } else if (valueEl.nextSibling) {
                container.insertBefore(unitEl, valueEl.nextSibling);
            } else {
                container.appendChild(unitEl);
            }
        }

        // 清理容器内可能存在的遗留纯文本节点，避免与 value/unit 重复显示
        Array.from(container.childNodes).forEach(node => {
            if (node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0) {
                container.removeChild(node);
            }
        });

        return { valueEl, unitEl };
    },

    /**
     * 初始化速度刻度组件
     */
    initSpeedScale() {
        try {
            if (window.SpeedScale && typeof window.SpeedScale.init === 'function') {
                window.SpeedScale.init();
                console.log('[Dashboard] SpeedScale组件初始化成功');
            } else {
                console.warn('[Dashboard] SpeedScale组件未加载');
            }
        } catch (error) {
            console.error('[Dashboard] SpeedScale组件初始化失败:', error);
        }
    },

    /**
     * 初始化进度条无障碍属性
     */
    initProgressBarAccessibility() {
        const progressBars = [
            { id: 'download-speed-progress', label: '下载速度' },
            { id: 'upload-speed-progress', label: '上传速度' },
            { id: 'mobile-download-speed-progress', label: '移动端下载速度' },
            { id: 'mobile-upload-speed-progress', label: '移动端上传速度' }
        ];

        progressBars.forEach(({ id, label }) => {
            const element = document.getElementById(id);
            if (element) {
                element.setAttribute('role', 'progressbar');
                element.setAttribute('aria-label', label);
                element.setAttribute('aria-valuenow', '0');
                element.setAttribute('aria-valuemin', '0');
                element.setAttribute('aria-valuemax', '100');
                element.setAttribute('aria-live', 'polite');
            }
        });
    },

    /**
     * 非破坏性设置速度容器文本（保留档位徽章）
     */
    setSpeedContainerText(container, text) {
        if (!container) return;

        // 查找现有的value元素
        let valueEl = container.querySelector('[id$="-value"]');

        if (valueEl) {
            // 如果有value元素，只更新它的文本
            valueEl.textContent = text;
        } else {
            // 检查是否有档位徽章
            const gearBadge = container.querySelector('.gear-badge');

            if (gearBadge) {
                // 有档位徽章，需要保留它
                // 检查是否已经有其他文本节点
                const textNodes = Array.from(container.childNodes).filter(node =>
                    node.nodeType === Node.TEXT_NODE && node.textContent.trim()
                );

                if (textNodes.length > 0) {
                    // 更新第一个文本节点
                    textNodes[0].textContent = text;
                } else {
                    // 创建新的文本节点，插入到徽章前面
                    const textNode = document.createTextNode(text);
                    container.insertBefore(textNode, gearBadge);
                }
            } else {
                // 没有档位徽章，直接设置容器文本
                container.textContent = text;
            }
        }
    },

    /**
     * 更新进度条aria属性
     */
    updateProgressBarAria(elementId, currentValue) {
        const element = document.getElementById(elementId);
        if (!element) return;

        // 获取当前基准值（从ProgressBarManager）
        let baseline = 100 * 1000 * 1000; // 默认100Mbps
        if (window.progressBarManager && window.progressBarManager.baselineState) {
            const state = window.progressBarManager.baselineState.get('net');
            if (state && state.baseline) {
                baseline = state.baseline;
            }
        }

        const percent = Math.min(100, Math.round((currentValue / baseline) * 100));
        const speedText = window.strbps ? window.strbps(currentValue) : `${Math.round(currentValue / 1000000)}Mbps`;
        const baselineText = window.strbps ? window.strbps(baseline) : `${Math.round(baseline / 1000000)}Mbps`;

        element.setAttribute('aria-valuenow', percent.toString());
        element.setAttribute('aria-valuetext', `${speedText} (${percent}% of ${baselineText})`);
    },

    /**
     * 初始化仪表盘
     */
    init() {
        // 检查新的格式化系统
        if (window.METRIC_FORMATTER_AVAILABLE && window.MetricFormatter) {
            // 启用调试模式用于测试
            window.DEBUG_MODE = true;
        } else {
            console.log('⚠️ Dashboard initialized with legacy formatting system');
        }

        // 初始化网络数据显示
        this.initNetworkDisplay();

        // 初始化速度刻度组件
        this.initSpeedScale();

        // 初始化进度条无障碍属性
        this.initProgressBarAccessibility();

        this.initRegionStats();
        this.initExpiryFilters();
        this.initStatusFilters();
        this.initVisibilityTracking();
        this.initAnimationSpeedListener();

        // 地区分布折叠：默认收起一行
        try {
            const wrapper = document.getElementById('region-stats-wrapper');
            const toggle = document.getElementById('region-collapse-toggle');
            if (wrapper && toggle) {
                wrapper.classList.add('collapsed');
                const updateLabel = () => {
                    const icon = toggle.querySelector('i');
                    const text = toggle.querySelector('span');
                    if (wrapper.classList.contains('collapsed')) {
                        if (icon) icon.classList.add('ti-chevron-down'), icon.classList.remove('ti-chevron-up');
                        if (text) text.textContent = '展开';
                    } else {
                        if (icon) icon.classList.add('ti-chevron-up'), icon.classList.remove('ti-chevron-down');
                        if (text) text.textContent = '收起';
                    }
                };
                updateLabel();
                toggle.addEventListener('click', async () => {
                    wrapper.classList.toggle('collapsed');
                    updateLabel();
                    
                    // 集成3D地球组件
                    const globeContainer = document.getElementById('region-globe-container');
                    if (!wrapper.classList.contains('collapsed')) {
                        // 展开时显示3D地球（优化加载流程）
                        if (globeContainer && window.RegionGlobe) {
                            globeContainer.classList.remove('hidden');
                            
                            // 如果未初始化，则通过优化的加载流程初始化3D地球
                            if (!window.RegionGlobe.isInitialized) {
                                try {
                                    // 显示详细加载状态
                                    this.updateGlobeLoadingStatus('正在准备地球数据...');
                                    
                                    // 并行加载所有必要资源
                                    const [regionData, globeReady, geoDataReady] = await Promise.all([
                                        this.prepareRegionData(),
                                        this.ensureGlobeLibrary(),
                                        this.preloadGeoData()
                                    ]);
                                    
                                    console.log('[Dashboard] 资源加载状态:', { regionData: regionData.length, globeReady, geoDataReady });
                                    
                                    // 检查关键资源是否就绪
                                    if (!globeReady || !window.RegionGlobe || typeof window.RegionGlobe.init !== 'function') {
                                        throw new Error(`关键资源加载失败: globeReady=${globeReady}, RegionGlobe=${!!window.RegionGlobe}, initFunction=${!!(window.RegionGlobe && typeof window.RegionGlobe.init === 'function')}`);
                                    }
                                    
                                    this.updateGlobeLoadingStatus('正在初始化3D地球...');
                                    
                                    // 所有资源准备完成，初始化3D地球
                                    const success = await window.RegionGlobe.init('region-globe-wrapper', regionData);
                                    
                                    if (success) {
                                        console.log('[Dashboard] 3D地球初始化成功');
                                        
                                        // 设置按钮事件
                                        const resetBtn = document.getElementById('globe-reset-view');
                                        if (resetBtn) {
                                            resetBtn.addEventListener('click', () => {
                                                window.RegionGlobe.resetView();
                                            });
                                        }
                                        
                                        // 地球风格选择器已移除 - 地图自动跟随网站主题
                                        
                                        // 监听主题变化
                                        document.addEventListener('themeChanged', () => {
                                            window.RegionGlobe.updateTheme();
                                        });
                                        
                                        // 隐藏加载状态
                                        this.updateGlobeLoadingStatus('', false);
                                    }
                                } catch (error) {
                                    console.error('[Dashboard] 3D地球初始化失败:', error);
                                    this.updateGlobeLoadingStatus('地球初始化失败', false);
                                }
                            } else {
                                // 恢复动画
                                window.RegionGlobe.resume();
                            }
                        }
                    } else {
                        // 收起时隐藏3D地球
                        if (globeContainer) {
                            globeContainer.classList.add('hidden');
                            // 暂停动画以节省性能
                            if (window.RegionGlobe && window.RegionGlobe.isInitialized) {
                                window.RegionGlobe.pause();
                            }
                        }
                    }
                });
            }
        } catch (e) { /* ignore */ }

        // 档位徽章改为移除，不再初始化

        // 加载ProgressBar.js库
        this.loadProgressBarLibrary();

        // 从缓存加载网络数据
        this.loadNetworkDataFromCache();

        // 地区统计刷新按钮事件由 region-refresh.js 处理
    },

    // 移除：档位徽章监听

    /**
     * 从缓存加载网络数据
     * @deprecated 网络数据不再缓存，直接等待WebSocket推送实时数据
     */
    loadNetworkDataFromCache() {
        // 基于第一性原理：流量数据是累积性的，应该始终获取最新值
        console.debug('[仪表盘] 网络数据缓存已禁用，等待WebSocket推送实时数据');

        // 清理可能存在的旧缓存
        try {
            localStorage.removeItem('network_data_cache');
        } catch (error) {
            console.error('清理网络数据缓存失败:', error);
        }
    },

    /**
     * 加载ProgressBar.js库
     * 注意：我们已经决定使用原始进度条，不再加载ProgressBar.js库
     */
    loadProgressBarLibrary() {
        // 直接使用原始进度条
        console.log('使用原始进度条，不加载ProgressBar.js库');
    },

    /**
     * 地球风格选择器已移除 - 地图自动跟随网站主题
     */
    // setupGlobeStyleSelector() 已移除

    /**
     * 更新地球加载状态
     */
    updateGlobeLoadingStatus(message, show = true) {
        const loadingElement = document.querySelector('.globe-loading');
        if (loadingElement) {
            if (show && message) {
                loadingElement.innerHTML = `
                    <i class="ti ti-loader-2"></i>
                    <p>${message}</p>
                `;
                loadingElement.style.display = 'block';
            } else {
                loadingElement.style.display = 'none';
            }
        }
    },

    /**
     * 准备地区数据
     */
    async prepareRegionData() {
        return new Promise((resolve) => {
            let regionData = [];
            if (window.RegionStatsModule && window.RegionStatsModule.regionData) {
                // 获取所有有数据的地区
                regionData = Array.from(window.RegionStatsModule.regionData.values())
                    .filter(r => r.count > 0)
                    .sort((a, b) => b.count - a.count);
                console.log('[Dashboard] 地区数据准备完成:', regionData.length, '个地区');
            }
            resolve(regionData);
        });
    },

    /**
     * 确保Globe.GL库已加载
     */
    async ensureGlobeLibrary() {
        // 检查RegionGlobe是否已加载并可用（这是关键的检查）
        if (window.RegionGlobe && typeof window.RegionGlobe.init === 'function') {
            // 尝试直接检查Globe是否可用
            try {
                // 测试Globe构造函数是否可用
                if (typeof Globe !== 'undefined') {
                    console.log('[Dashboard] Globe.GL库已就绪');
                    return true;
                }
            } catch (e) {
                // Globe可能没有正确暴露，但RegionGlobe可能已经有了引用
                console.log('[Dashboard] Globe全局变量不可用，但RegionGlobe已加载');
            }
            
            // 如果RegionGlobe存在且init函数可用，认为库已经加载
            console.log('[Dashboard] RegionGlobe已就绪，可以继续初始化');
            return true;
        }
        
        console.log('[Dashboard] 等待RegionGlobe加载...');
        // 等待RegionGlobe加载完成
        return new Promise((resolve) => {
            const checkInterval = setInterval(() => {
                if (window.RegionGlobe && typeof window.RegionGlobe.init === 'function') {
                    clearInterval(checkInterval);
                    console.log('[Dashboard] RegionGlobe加载完成');
                    resolve(true);
                }
            }, 100);
            
            // 3秒超时（进一步缩短）
            setTimeout(() => {
                clearInterval(checkInterval);
                // 检查当前状态
                const isReady = window.RegionGlobe && typeof window.RegionGlobe.init === 'function';
                console.log('[Dashboard] 库加载检查完成，状态:', {
                    RegionGlobe: !!window.RegionGlobe,
                    initFunction: !!(window.RegionGlobe && typeof window.RegionGlobe.init === 'function'),
                    isReady
                });
                resolve(isReady);
            }, 3000);
        });
    },

    /**
     * 预加载地理数据
     */
    async preloadGeoData() {
        // 触发地理数据预加载
        if (window.GeoJSONLoader && typeof window.GeoJSONLoader.loadCountries === 'function') {
            console.log('[Dashboard] 开始预加载地理数据...');
            try {
                await window.GeoJSONLoader.loadCountries();
                console.log('[Dashboard] 地理数据预加载完成');
                return true;
            } catch (error) {
                console.warn('[Dashboard] 地理数据预加载失败:', error);
                return false; // 预加载失败但不阻塞整体流程
            }
        }
        console.log('[Dashboard] GeoJSONLoader未就绪，跳过预加载');
        return true; // 如果GeoJSONLoader不存在，返回true继续
    },

    
};

// 当DOM加载完成时初始化仪表盘
document.addEventListener('DOMContentLoaded', () => {
    if (window.DashboardModule) {
        window.DashboardModule.init();
    }
});
