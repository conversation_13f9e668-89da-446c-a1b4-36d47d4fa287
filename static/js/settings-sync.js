/**
 * settings-sync.js
 * @description 全局设置同步工具，确保个性化设置在所有页面上实时生效
 * 1. 从sessionStorage读取最新设置
 * 2. 应用壁纸设置到当前页面
 * 3. 监听设置变更事件
 */

(function() {
    // 防止重复初始化
    if (window.settingsSyncInitialized) return;
    window.settingsSyncInitialized = true;

    // 应用壁纸设置
    function applyWallpaperSettings(settings) {
        if (!settings || !settings.wallpaper) return;

        const wallpaperSettings = settings.wallpaper;

        // 移除旧的壁纸样式
        const oldStyle = document.getElementById('dynamic-wallpaper-style');
        if (oldStyle) oldStyle.remove();

        // 只有启用壁纸时才应用
        if (!wallpaperSettings.enabled) {
            // 统一使用 has-background-image 类名
            document.body.classList.remove('has-background-image');
            document.body.classList.remove('has-wallpaper');
            document.body.classList.remove('wallpaper-loaded');
            return;
        }

        // 创建新的样式元素
        const styleEl = document.createElement('style');
        styleEl.id = 'dynamic-wallpaper-style';

        let wallpaperCss = '';

        // 添加基本壁纸样式
        if (wallpaperSettings.url) {
            // 统一使用 has-background-image 类名
            document.body.classList.add('has-background-image');
            document.body.classList.add('wallpaper-loaded');

            let backgroundSize = 'cover';
            let backgroundRepeat = 'no-repeat';

            // 设置平铺方式
            if (wallpaperSettings.size === 'repeat') {
                backgroundSize = 'auto';
                backgroundRepeat = wallpaperSettings.repeat || 'repeat';
            } else {
                backgroundSize = wallpaperSettings.size || 'cover';
            }

            // 设置是否固定
            const attachment = wallpaperSettings.fixed ? 'fixed' : 'scroll';

            // 构建完整背景样式
            wallpaperCss += `
            body.has-background-image {
                background-image: url('${wallpaperSettings.url}') !important;
                background-size: ${backgroundSize} !important;
                background-repeat: ${backgroundRepeat} !important;
                background-attachment: ${attachment} !important;
                background-position: center center !important;
            }`;

            // 设置亮度叠加 - 始终应用，无论是否启用模糊
            if (wallpaperSettings.brightness !== undefined) {
                const brightness = wallpaperSettings.brightness / 100;
                const overlayOpacity = 1 - brightness;
                wallpaperCss += `
                body.has-background-image::before {
                    content: '';
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, ${overlayOpacity});
                    pointer-events: none;
                    z-index: -1;
                }`;
            }

            // 设置壁纸模糊效果 - 简化方案：固定1.02倍放大
            if (wallpaperSettings.blur && wallpaperSettings.blur.enabled) {
                // 修复：确保0值不被||运算符替换，支持真正的0px模糊
                const blurAmount = wallpaperSettings.blur.amount !== undefined ? wallpaperSettings.blur.amount : 5;
                
                if (blurAmount > 0) {
                    // 固定1.02倍放大，简单有效解决模糊边缘泛白
                    wallpaperCss += `
                    body.has-background-image::after {
                        content: '';
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background-image: url('${wallpaperSettings.url}');
                        background-size: ${backgroundSize};
                        background-repeat: ${backgroundRepeat};
                        background-attachment: ${attachment};
                        background-position: center center;
                        transform: scale(1.02);
                        filter: blur(${blurAmount}px);
                        -webkit-filter: blur(${blurAmount}px);
                        z-index: -3;
                        pointer-events: none;
                    }
                    /* 启用模糊时隐藏主背景，使用模糊层 */
                    body.has-background-image {
                        background-image: none !important;
                    }`;
                    
                    // 如果同时有亮度调整，确保亮度叠加层在模糊层之上
                    if (wallpaperSettings.brightness !== undefined) {
                        wallpaperCss += `
                        /* 确保亮度叠加层在模糊层之上 */
                        body.has-background-image::before {
                            z-index: -2 !important;
                        }`;
                    }
                }
                // 如果blurAmount为0，不添加任何模糊样式，保持清晰背景
            }
        } else {
            document.body.classList.remove('has-background-image');
            document.body.classList.remove('has-wallpaper');
            document.body.classList.remove('wallpaper-loaded');
        }

        // 添加样式到文档
        styleEl.textContent = wallpaperCss;
        document.head.appendChild(styleEl);
    }

    // 获取端口隔离的缓存键
    function getPersonalizationCacheKey() {
        const port = location.port || '80';
        const host = location.hostname.replace(/\./g, '_');
        return `personalization-settings_${host}_${port}`;
    }

    // 防止并发请求的标志
    let isLoadingFromServer = false;
    let serverSettingsCache = null;
    let lastServerFetchTime = 0;
    const SERVER_CACHE_DURATION = 600000; // 10分钟缓存
    
    // 从服务器获取个性化设置
    async function loadSettingsFromServer() {
        // 检查是否正在加载
        if (isLoadingFromServer) {
            console.log('正在从服务器加载设置，跳过重复请求');
            return serverSettingsCache;
        }
        
        // 检查缓存是否有效
        const now = Date.now();
        if (serverSettingsCache && (now - lastServerFetchTime) < SERVER_CACHE_DURATION) {
            console.log('使用缓存的服务器设置');
            return serverSettingsCache;
        }
        
        try {
            isLoadingFromServer = true;
            console.log('尝试从服务器获取个性化设置...');
            const response = await fetch('/api/personalization-settings', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.code === 1 && data.data) {
                    // 为服务器数据添加版本信息
                    const settingsWithVersion = {
                        ...data.data,
                        _version: now, // 使用获取时间作为版本号
                        _lastUpdate: new Date().toISOString(),
                        _source: 'server'
                    };
                    serverSettingsCache = settingsWithVersion;
                    lastServerFetchTime = now;
                    
                    // 立即更新本地缓存（带端口隔离）
                    try {
                        localStorage.setItem(getPersonalizationCacheKey(), JSON.stringify(settingsWithVersion));
                    } catch (e) {
                        console.warn('无法更新本地缓存:', e);
                    }
                    
                    return settingsWithVersion;
                }
            }
        } catch (error) {
            console.warn('从服务器获取个性化设置失败:', error);
        } finally {
            isLoadingFromServer = false;
        }
        
        return null;
    }

    // 从多个来源加载设置并应用 - 优化版（减少不必要的API请求）
    async function loadAndApplySettings() {
        let settings = null;
        let embeddedSettings = null;
        let localStorageSettings = null;
        const CACHE_DURATION = 300000; // 5分钟缓存有效期

        // 🎯 第一步：读取页面内嵌数据
        try {
            const scriptEl = document.getElementById('personalization-data');
            if (scriptEl && scriptEl.textContent) {
                embeddedSettings = JSON.parse(scriptEl.textContent);
                // 为内嵌数据添加默认版本号（如果没有的话）
                if (!embeddedSettings._version) {
                    embeddedSettings._version = 0; // 内嵌数据默认版本号为0，表示较旧
                }
                embeddedSettings._source = 'page-embedded';
                console.log('📄 读取到页面内嵌数据，版本:', embeddedSettings._version);
            }
        } catch (e) {
            console.warn('页面内嵌数据解析失败:', e);
        }

        // 🎯 第二步：读取localStorage缓存
        try {
            const cachedJson = localStorage.getItem(getPersonalizationCacheKey());
            if (cachedJson) {
                const cachedData = JSON.parse(cachedJson);
                const now = Date.now();

                // 检查缓存是否在有效期内
                if (cachedData._cacheTime && (now - cachedData._cacheTime) < CACHE_DURATION) {
                    localStorageSettings = cachedData;
                    console.log('💾 读取到localStorage缓存数据，版本:', localStorageSettings._version || 'unknown');
                } else {
                    console.log('localStorage缓存已过期，需要更新');
                }
            }
        } catch (e) {
            console.warn('localStorage缓存读取失败:', e);
        }

        // 🎯 第三步：版本号比较，选择最新的设置
        if (embeddedSettings && localStorageSettings) {
            // 两个数据源都存在，比较版本号
            const embeddedVersion = embeddedSettings._version || 0;
            const localVersion = localStorageSettings._version || 0;

            if (localVersion > embeddedVersion) {
                settings = localStorageSettings;
                console.log('🔄 localStorage设置更新，使用localStorage数据 (版本:', localVersion, '>', embeddedVersion, ')');
            } else {
                settings = embeddedSettings;
                console.log('📄 使用页面内嵌数据 (版本:', embeddedVersion, '>=', localVersion, ')');

                // 更新localStorage缓存，确保版本号正确
                try {
                    const settingsWithCache = {
                        ...settings,
                        _cacheTime: Date.now(),
                        _version: embeddedVersion,
                        _source: 'page-embedded'
                    };
                    localStorage.setItem(getPersonalizationCacheKey(), JSON.stringify(settingsWithCache));
                } catch (e) {
                    console.warn('无法更新localStorage缓存:', e);
                }
            }
        } else if (embeddedSettings) {
            // 只有内嵌数据
            settings = embeddedSettings;
            console.log('📄 仅使用页面内嵌数据');

            // 缓存到localStorage
            try {
                const settingsWithCache = {
                    ...settings,
                    _cacheTime: Date.now(),
                    _version: settings._version || 0,
                    _source: 'page-embedded'
                };
                localStorage.setItem(getPersonalizationCacheKey(), JSON.stringify(settingsWithCache));
            } catch (e) {
                console.warn('无法更新localStorage缓存:', e);
            }
        } else if (localStorageSettings) {
            // 只有localStorage数据
            settings = localStorageSettings;
            console.log('💾 仅使用localStorage缓存数据');
        }

        // 🎯 第三优先级：服务器请求（仅在必要时）
        if (!settings) {
            console.log('本地缓存无效，从服务器获取设置...');
            settings = await loadSettingsFromServer();
            
            // 如果服务器获取成功，缓存到localStorage
            if (settings) {
                try {
                    const settingsWithCache = {
                        ...settings,
                        _cacheTime: Date.now(),
                        _version: Date.now(),
                        _source: 'server'
                    };
                    localStorage.setItem(getPersonalizationCacheKey(), JSON.stringify(settingsWithCache));
                    console.log('✅ 服务器设置已缓存到localStorage');
                } catch (e) {
                    console.warn('无法缓存服务器设置:', e);
                }
            }
        }

        // 应用设置
        if (settings) {
            console.log('应用个性化设置:', settings._source || 'unknown-source');
            applyWallpaperSettings(settings);
        } else {
            console.log('未找到个性化设置，使用默认设置');
        }
    }

    // 初始化监听器，接收设置更改事件
    function initSettingsListener() {
        let lastUpdateTimestamp = 0;
        
        // 防重复应用设置的辅助函数
        function throttledApplySettings(settings, source = 'unknown') {
            const now = Date.now();
            // 防止100ms内重复应用相同设置
            if (now - lastUpdateTimestamp < 100) {
                console.log(`跳过重复设置更新 (${source}):`, '距离上次更新', now - lastUpdateTimestamp, 'ms');
                return;
            }
            
            lastUpdateTimestamp = now;
            console.log(`应用设置更新 (${source}):`, settings);
            
            try {
                // 更新跨会话缓存（添加版本号机制）
                const settingsWithVersion = {
                    ...settings,
                    _version: Date.now(), // 添加版本号机制
                    _lastUpdate: new Date().toISOString()
                };
                localStorage.setItem(getPersonalizationCacheKey(), JSON.stringify(settingsWithVersion));
                // 应用新设置
                applyWallpaperSettings(settings);
            } catch (e) {
                console.error(`设置更新失败 (${source}):`, e);
            }
        }

        // 统一使用 'personalization-settings-updated' 事件名称
        document.addEventListener('personalization-settings-updated', function(e) {
            if (e.detail) {
                throttledApplySettings(e.detail, 'CustomEvent');
            }
        });

        // 监听 postMessage 事件，实现跨页面通信
        window.addEventListener('message', function(e) {
            // 验证消息来源和类型
            if (e.data && e.data.type) {
                if (e.data.type === 'personalization-settings-updated' && e.data.settings) {
                    throttledApplySettings(e.data.settings, 'PostMessage');
                } else if (e.data.type === 'personalization-cache-cleared') {
                    // 处理缓存清除通知
                    console.log('🧹 收到缓存清除通知，重新加载设置');
                    loadAndApplySettings();
                }
            }
        });

        // 监听localStorage变化事件，实现标签页间同步（跨会话，带端口隔离）
        window.addEventListener('storage', function(e) {
            const cacheKey = getPersonalizationCacheKey();
            if (e.key === cacheKey && e.newValue) {
                try {
                    const settings = JSON.parse(e.newValue);
                    // 检查版本，避免应用过期设置
                    const currentSettings = localStorage.getItem(cacheKey);
                    if (currentSettings) {
                        const current = JSON.parse(currentSettings);
                        if (settings._version && current._version && settings._version <= current._version) {
                            console.log('跳过过期的设置更新，版本:', settings._version, '<=', current._version);
                            return;
                        }
                    }
                    throttledApplySettings(settings, 'LocalStorage');
                } catch (err) {
                    console.warn('解析存储的设置数据失败:', err);
                }
            }
        });
    }

    // 防止重复初始化的标志
    let initialized = false;
    
    // 统一的初始化函数
    function initializeSettings() {
        if (initialized) {
            console.log('设置同步已初始化，跳过重复初始化');
            return;
        }
        initialized = true;
        
        console.log('初始化全局设置同步');
        loadAndApplySettings();
        initSettingsListener();
    }
    
    // 页面加载完成后执行初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            // 在空闲时初始化，避免阻塞首次渲染/其他连接
            idle(initializeSettings);
        });
    } else {
        // 页面已就绪，安排在空闲时执行
        idle(() => {
            console.log('页面已加载，空闲时初始化设置同步');
            initializeSettings();
        });
    }

    // 清除缓存的函数，用于设置更新时立即失效 - 优化版
    function clearSettingsCache() {
        console.log('🧹 清除个性化设置缓存');
        
        // 清除内存缓存
        serverSettingsCache = null;
        lastServerFetchTime = 0;
        
        // 清除localStorage缓存（带端口隔离）
        try {
            localStorage.removeItem(getPersonalizationCacheKey());
            console.log('✅ localStorage缓存已清除');
        } catch (e) {
            console.warn('清除localStorage缓存失败:', e);
        }
        
        // 通知其他页面清除缓存
        try {
            window.postMessage({
                type: 'personalization-cache-cleared',
                timestamp: Date.now()
            }, '*');
            
            // 派发自定义事件
            document.dispatchEvent(new CustomEvent('personalization-cache-cleared', {
                detail: { timestamp: Date.now() },
                bubbles: true
            }));
            
            console.log('✅ 已通知其他页面清除缓存');
        } catch (e) {
            console.warn('通知其他页面失败:', e);
        }
    }

    // 导出到全局作用域，供其他脚本调用
    window.settingsSync = {
        applyWallpaperSettings: applyWallpaperSettings,
        loadAndApplySettings: loadAndApplySettings,
        clearSettingsCache: clearSettingsCache // 新增：缓存清除功能
    };
    // 轻量级空闲调度（避免阻塞主线程）
    function idle(fn, timeout = 1000) {
        if (typeof window.requestIdleCallback === 'function') {
            return window.requestIdleCallback(fn, { timeout });
        }
        return setTimeout(fn, 50);
    }
})();
