/**
 * TCPing监控目标管理平台演示脚本
 * 演示PRD中设计的所有功能
 */

(function() {
    'use strict';

    // 生成模拟数据函数
    function generateMockTargets(count) {
        const targets = [];
        const regions = ['北京', '上海', '广州', '深圳', '杭州', '成都', '南京', '武汉', '西安', '重庆', '天津', '郑州', '沈阳', '长沙', '厦门'];
        const nodeNames = ['BJ-01', 'BJ-02', 'SH-01', 'SH-02', 'GZ-01', 'GZ-02', 'SZ-01', 'SZ-02', 'HZ-01', 'HZ-02', 'CD-01', 'CD-02', 'NJ-01', 'NJ-02', 'WH-01', 'WH-02', 'XA-01', 'XA-02', 'CQ-01', 'CQ-02', 'TJ-01', 'TJ-02', 'ZZ-01', 'ZZ-02', 'SY-01', 'SY-02', 'CS-01', 'CS-02', 'XM-01', 'XM-02'];
        const serviceTypes = ['数据库', 'Web服务', 'API网关', '缓存服务', '消息队列', '文件服务', '认证服务', '监控服务', '日志服务', '配置中心', '注册中心', '搜索服务', '推送服务', '支付服务', '用户服务'];
        const environments = ['生产', '测试', '开发', '灰度', '演示'];
        const domains = ['example.com', 'test.com', 'dev.com', 'staging.com', 'demo.com'];
        const ports = [80, 443, 3306, 6379, 5432, 27017, 8080, 8443, 9200, 5672, 22, 21, 25, 53, 123];
        
        for (let i = 1; i <= count; i++) {
            const serviceType = serviceTypes[Math.floor(Math.random() * serviceTypes.length)];
            const environment = environments[Math.floor(Math.random() * environments.length)];
            const region = regions[Math.floor(Math.random() * regions.length)];
            const domain = domains[Math.floor(Math.random() * domains.length)];
            const port = ports[Math.floor(Math.random() * ports.length)];
            
            // 随机选择2-6个节点
            const nodeCount = Math.floor(Math.random() * 5) + 2;
            const selectedNodes = [];
            const shuffledNodes = [...nodeNames].sort(() => 0.5 - Math.random());
            for (let j = 0; j < nodeCount; j++) {
                selectedNodes.push(shuffledNodes[j]);
            }
            
            // 生成可用率和延迟
            const availability = Math.random() > 0.1 ? 95 + Math.random() * 5 : 85 + Math.random() * 10;
            const latency = Math.floor(Math.random() * 200) + 20;
            
            // 确定状态
            let status = 'online';
            if (availability < 90) status = 'offline';
            else if (availability < 95 || latency > 150) status = 'warning';
            
            targets.push({
                id: i,
                name: `${environment}${serviceType}${i.toString().padStart(2, '0')}`,
                description: `${environment}环境${serviceType}实例`,
                address: `${serviceType.toLowerCase()}${i}.${domain}`,
                port: port,
                region: region,
                nodes: selectedNodes,
                availability: Math.round(availability * 10) / 10,
                latency: latency,
                status: status,
                priority: ['P1', 'P2', 'P3'][Math.floor(Math.random() * 3)],
                tags: [serviceType.toLowerCase(), environment.toLowerCase(), region.toLowerCase()]
            });
        }
        
        return targets;
    }

    // 模拟数据 - 生成更多数据以模拟真实场景
    const mockData = {
        targets: generateMockTargets(128), // 生成128个目标，模拟几十个节点场景
        
        // 模拟权限信息
        permissions: {
            hasNetworkQuality: true,
            currentPlan: 'Professional',
            maxTargets: 50
        }
    };

    // 应用状态
    const appState = {
        currentPage: 1,
        pageSize: 25,
        selectedTargets: [],
        searchKeyword: '',
        regionFilter: '',
        statusFilter: '',
        priorityFilter: '',
        environmentFilter: '',
        sortField: '',
        sortOrder: 'asc',
        isDrawerOpen: false,
        editingTarget: null
    };

    // 初始化应用
    function initApp() {
        // 检查权限
        if (!mockData.permissions.hasNetworkQuality) {
            showPermissionWall();
            return;
        }

        // 初始化主题
        initTheme();

        // 渲染页面
        renderStatistics();
        renderTargetTable();
        renderPagination();
        bindEvents();

        console.log('TCPing监控目标管理平台演示版已加载');
    }

    // 初始化主题
    function initTheme() {
        const html = document.documentElement;
        const themeToggle = document.getElementById('themeToggle');
        const icon = themeToggle.querySelector('.ti');

        if (html.classList.contains('dark')) {
            icon.className = 'ti ti-sun';
        } else {
            icon.className = 'ti ti-moon';
        }
    }

    // 渲染统计数据
    function renderStatistics() {
        const targets = getFilteredTargets();
        const onlineCount = targets.filter(t => t.status === 'online').length;
        const offlineCount = targets.filter(t => t.status === 'offline').length;
        const avgLatency = Math.round(targets.reduce((sum, t) => sum + t.latency, 0) / targets.length);

        document.getElementById('totalTargets').textContent = targets.length;
        document.getElementById('onlineTargets').textContent = onlineCount;
        document.getElementById('offlineTargets').textContent = offlineCount;
        document.getElementById('avgLatency').textContent = avgLatency + 'ms';
    }

    // 渲染目标表格
    function renderTargetTable() {
        const targets = getFilteredTargets();
        const startIndex = (appState.currentPage - 1) * appState.pageSize;
        const endIndex = startIndex + appState.pageSize;
        const pageTargets = targets.slice(startIndex, endIndex);

        const tbody = document.getElementById('targetTableBody');
        tbody.innerHTML = '';

        pageTargets.forEach(target => {
            const row = createTargetRow(target);
            tbody.appendChild(row);
        });

        // 更新选择状态
        updateSelectAllState();
    }

    // 创建目标行
    function createTargetRow(target) {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700';
        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="target-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" 
                       data-id="${target.id}" ${appState.selectedTargets.includes(target.id) ? 'checked' : ''}>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white cursor-pointer hover:text-blue-600" 
                             onclick="showTargetDetail(${target.id})">${target.name}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">${target.description}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">${target.address}:${target.port}</div>
                <button class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300" onclick="copyAddress('${target.address}:${target.port}')">
                    复制
                </button>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="badge badge-secondary">${target.region}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <span class="badge badge-primary mr-2">${target.nodes.length}个节点</span>
                    <button class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300" 
                            onclick="showNodeDetails(${target.id})" title="查看节点详情">
                        <i class="ti ti-info-circle text-sm"></i>
                    </button>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <span class="status-dot ${getStatusClass(target.availability)}"></span>
                    <span class="text-sm text-gray-900 dark:text-white">${target.availability}%</span>
                    <div class="availability-bar ml-2">
                        <div class="availability-fill ${getAvailabilityClass(target.availability)}" 
                             style="width: ${target.availability}%"></div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-sm ${getLatencyClass(target.latency)}">${target.latency}ms</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" ${target.status === 'online' ? 'checked' : ''} 
                           onchange="toggleTargetStatus(${target.id})">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="table-actions flex space-x-2">
                    <button class="text-blue-600 hover:text-blue-900" onclick="editTarget(${target.id})" title="编辑">
                        <i class="ti ti-edit text-sm"></i>
                    </button>
                    <button class="text-green-600 hover:text-green-900" onclick="cloneTarget(${target.id})" title="克隆">
                        <i class="ti ti-copy text-sm"></i>
                    </button>
                    <button class="text-red-600 hover:text-red-900" onclick="deleteTarget(${target.id})" title="删除">
                        <i class="ti ti-trash text-sm"></i>
                    </button>
                </div>
            </td>
        `;
        return row;
    }

    // 获取过滤后的目标
    function getFilteredTargets() {
        let targets = [...mockData.targets];

        // 搜索过滤
        if (appState.searchKeyword) {
            const keyword = appState.searchKeyword.toLowerCase();
            targets = targets.filter(target => 
                target.name.toLowerCase().includes(keyword) ||
                target.address.toLowerCase().includes(keyword) ||
                target.description.toLowerCase().includes(keyword)
            );
        }

        // 地区过滤
        if (appState.regionFilter) {
            targets = targets.filter(target => target.region === appState.regionFilter);
        }

        // 状态过滤
        if (appState.statusFilter) {
            targets = targets.filter(target => target.status === appState.statusFilter);
        }

        // 优先级过滤
        if (appState.priorityFilter) {
            targets = targets.filter(target => target.priority === appState.priorityFilter);
        }

        // 环境过滤
        if (appState.environmentFilter) {
            targets = targets.filter(target => target.description.includes(appState.environmentFilter));
        }

        // 排序
        if (appState.sortField) {
            targets.sort((a, b) => {
                let aValue = a[appState.sortField];
                let bValue = b[appState.sortField];
                
                if (typeof aValue === 'string') {
                    aValue = aValue.toLowerCase();
                    bValue = bValue.toLowerCase();
                }
                
                if (appState.sortOrder === 'asc') {
                    return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
                } else {
                    return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
                }
            });
        }

        return targets;
    }

    // 渲染分页
    function renderPagination() {
        const targets = getFilteredTargets();
        const totalPages = Math.ceil(targets.length / appState.pageSize);
        const startIndex = (appState.currentPage - 1) * appState.pageSize + 1;
        const endIndex = Math.min(startIndex + appState.pageSize - 1, targets.length);

        document.getElementById('startIndex').textContent = startIndex;
        document.getElementById('endIndex').textContent = endIndex;
        document.getElementById('totalRecords').textContent = targets.length;

        // 渲染页码按钮
        const pageNumbers = document.getElementById('pageNumbers');
        pageNumbers.innerHTML = '';

        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= appState.currentPage - 2 && i <= appState.currentPage + 2)) {
                const button = document.createElement('button');
                button.className = `relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                    i === appState.currentPage 
                        ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                }`;
                button.textContent = i;
                button.onclick = () => goToPage(i);
                pageNumbers.appendChild(button);
            } else if (i === appState.currentPage - 3 || i === appState.currentPage + 3) {
                const dots = document.createElement('span');
                dots.className = 'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700';
                dots.textContent = '...';
                pageNumbers.appendChild(dots);
            }
        }

        // 更新上一页/下一页按钮状态
        document.getElementById('prevPage').disabled = appState.currentPage === 1;
        document.getElementById('nextPage').disabled = appState.currentPage === totalPages;
    }

    // 绑定事件
    function bindEvents() {
        // 搜索
        document.getElementById('searchInput').addEventListener('input', function(e) {
            appState.searchKeyword = e.target.value;
            appState.currentPage = 1;
            renderTargetTable();
            renderPagination();
            renderStatistics();
        });

        // 筛选
        document.getElementById('regionFilter').addEventListener('change', function(e) {
            appState.regionFilter = e.target.value;
            appState.currentPage = 1;
            renderTargetTable();
            renderPagination();
            renderStatistics();
        });

        document.getElementById('statusFilter').addEventListener('change', function(e) {
            appState.statusFilter = e.target.value;
            appState.currentPage = 1;
            renderTargetTable();
            renderPagination();
            renderStatistics();
        });

        document.getElementById('priorityFilter').addEventListener('change', function(e) {
            appState.priorityFilter = e.target.value;
            appState.currentPage = 1;
            renderTargetTable();
            renderPagination();
            renderStatistics();
        });

        document.getElementById('environmentFilter').addEventListener('change', function(e) {
            appState.environmentFilter = e.target.value;
            appState.currentPage = 1;
            renderTargetTable();
            renderPagination();
            renderStatistics();
        });

        // 全选
        document.getElementById('selectAll').addEventListener('change', function(e) {
            const checkboxes = document.querySelectorAll('.target-checkbox');
            checkboxes.forEach(cb => {
                cb.checked = e.target.checked;
                const id = parseInt(cb.dataset.id);
                if (e.target.checked) {
                    if (!appState.selectedTargets.includes(id)) {
                        appState.selectedTargets.push(id);
                    }
                } else {
                    appState.selectedTargets = appState.selectedTargets.filter(targetId => targetId !== id);
                }
            });
            updateBatchButtons();
        });

        // 目标复选框
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('target-checkbox')) {
                const id = parseInt(e.target.dataset.id);
                if (e.target.checked) {
                    if (!appState.selectedTargets.includes(id)) {
                        appState.selectedTargets.push(id);
                    }
                } else {
                    appState.selectedTargets = appState.selectedTargets.filter(targetId => targetId !== id);
                }
                updateBatchButtons();
                updateSelectAllState();
            }
        });

        // 按钮事件
        document.getElementById('btnNewTarget').addEventListener('click', () => showTargetDrawer());
        document.getElementById('btnBatchImport').addEventListener('click', () => showBatchImport());
        document.getElementById('btnExport').addEventListener('click', () => exportData());
        document.getElementById('btnBatchDelete').addEventListener('click', () => batchDelete());

        // 抽屉事件
        document.getElementById('closeDrawer').addEventListener('click', () => hideTargetDrawer());
        document.getElementById('cancelBtn').addEventListener('click', () => hideTargetDrawer());
        document.getElementById('targetForm').addEventListener('submit', handleFormSubmit);

        // 分页事件
        document.getElementById('prevPage').addEventListener('click', () => goToPage(appState.currentPage - 1));
        document.getElementById('nextPage').addEventListener('click', () => goToPage(appState.currentPage + 1));

        // 常用端口选择
        document.getElementById('commonPorts').addEventListener('change', function(e) {
            if (e.target.value) {
                document.getElementById('targetPort').value = e.target.value;
            }
        });

        // 节点绑定策略
        document.getElementById('bindStrategy').addEventListener('change', function(e) {
            const nodeSelection = document.getElementById('nodeSelection');
            if (e.target.value === 'all') {
                nodeSelection.style.display = 'none';
            } else {
                nodeSelection.style.display = 'block';
                renderNodeList();
            }
            updateMatchedNodes();
        });

        // 节点搜索
        document.getElementById('nodeSearch').addEventListener('input', function(e) {
            filterNodes(e.target.value);
        });

        // 主题切换
        document.getElementById('themeToggle').addEventListener('click', toggleTheme);
    }

    // 工具函数
    function getStatusClass(availability) {
        if (availability >= 99) return 'status-online';
        if (availability >= 95) return 'status-warning';
        return 'status-offline';
    }

    function getAvailabilityClass(availability) {
        if (availability >= 99) return 'availability-excellent';
        if (availability >= 95) return 'availability-good';
        if (availability >= 90) return 'availability-fair';
        return 'availability-poor';
    }

    function getLatencyClass(latency) {
        if (latency < 100) return 'text-green-600';
        if (latency < 500) return 'text-yellow-600';
        return 'text-red-600';
    }

    function updateBatchButtons() {
        const batchDeleteBtn = document.getElementById('btnBatchDelete');
        const selectedCount = document.getElementById('selectedCount');
        
        if (appState.selectedTargets.length > 0) {
            batchDeleteBtn.classList.remove('hidden');
            selectedCount.textContent = appState.selectedTargets.length;
        } else {
            batchDeleteBtn.classList.add('hidden');
        }
    }

    function updateSelectAllState() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.target-checkbox');
        const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
        
        selectAll.checked = checkedCount === checkboxes.length && checkboxes.length > 0;
        selectAll.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
    }

    // 生成节点数据
    function generateNodeData() {
        const nodeNames = ['BJ-01', 'BJ-02', 'SH-01', 'SH-02', 'GZ-01', 'GZ-02', 'SZ-01', 'SZ-02', 'HZ-01', 'HZ-02', 'CD-01', 'CD-02', 'NJ-01', 'NJ-02', 'WH-01', 'WH-02', 'XA-01', 'XA-02', 'CQ-01', 'CQ-02', 'TJ-01', 'TJ-02', 'ZZ-01', 'ZZ-02', 'SY-01', 'SY-02', 'CS-01', 'CS-02', 'XM-01', 'XM-02'];
        const regions = {
            'BJ': '北京',
            'SH': '上海', 
            'GZ': '广州',
            'SZ': '深圳',
            'HZ': '杭州',
            'CD': '成都',
            'NJ': '南京',
            'WH': '武汉',
            'XA': '西安',
            'CQ': '重庆',
            'TJ': '天津',
            'ZZ': '郑州',
            'SY': '沈阳',
            'CS': '长沙',
            'XM': '厦门'
        };
        
        return nodeNames.map(name => {
            const regionCode = name.split('-')[0];
            const status = Math.random() > 0.1 ? 'online' : (Math.random() > 0.5 ? 'warning' : 'offline');
            return {
                name: name,
                region: regions[regionCode] || '未知',
                status: status,
                checked: false
            };
        });
    }

    // 节点数据
    let nodeData = generateNodeData();
    let filteredNodes = [...nodeData];

    // 渲染节点列表
    function renderNodeList() {
        const nodeList = document.getElementById('nodeList');
        nodeList.innerHTML = '';
        
        filteredNodes.forEach(node => {
            const nodeItem = document.createElement('div');
            nodeItem.className = 'node-item';
            nodeItem.innerHTML = `
                <input type="checkbox" id="node-${node.name}" 
                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3" 
                       ${node.checked ? 'checked' : ''}>
                <div class="node-status node-status-${node.status}"></div>
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">${node.name}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">${node.region}</div>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                    ${node.status === 'online' ? '在线' : node.status === 'warning' ? '异常' : '离线'}
                </div>
            `;
            
            // 添加点击事件
            nodeItem.addEventListener('click', function(e) {
                if (e.target.type !== 'checkbox') {
                    const checkbox = nodeItem.querySelector('input[type="checkbox"]');
                    checkbox.checked = !checkbox.checked;
                    node.checked = checkbox.checked;
                    updateMatchedNodes();
                }
            });
            
            // 添加checkbox事件
            nodeItem.querySelector('input[type="checkbox"]').addEventListener('change', function(e) {
                node.checked = e.target.checked;
                updateMatchedNodes();
            });
            
            nodeList.appendChild(nodeItem);
        });
    }

    // 过滤节点
    function filterNodes(keyword) {
        if (!keyword) {
            filteredNodes = [...nodeData];
        } else {
            filteredNodes = nodeData.filter(node => 
                node.name.toLowerCase().includes(keyword.toLowerCase()) ||
                node.region.includes(keyword)
            );
        }
        renderNodeList();
    }

    // 快速选择功能
    function selectAllNodes() {
        filteredNodes.forEach(node => {
            node.checked = true;
        });
        renderNodeList();
        updateMatchedNodes();
    }

    function selectNoNodes() {
        filteredNodes.forEach(node => {
            node.checked = false;
        });
        renderNodeList();
        updateMatchedNodes();
    }

    function selectNodesByRegion(region) {
        filteredNodes.forEach(node => {
            node.checked = node.region === region;
        });
        renderNodeList();
        updateMatchedNodes();
    }

    function updateMatchedNodes() {
        const strategy = document.getElementById('bindStrategy').value;
        const matchedNodes = document.getElementById('matchedNodes');
        
        if (strategy === 'all') {
            matchedNodes.textContent = nodeData.length;
        } else {
            const checkedCount = nodeData.filter(node => node.checked).length;
            matchedNodes.textContent = checkedCount;
        }
    }

    // 页面操作
    function goToPage(page) {
        appState.currentPage = page;
        renderTargetTable();
        renderPagination();
    }

    function sortTable(field) {
        if (appState.sortField === field) {
            appState.sortOrder = appState.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            appState.sortField = field;
            appState.sortOrder = 'asc';
        }
        renderTargetTable();
    }

    function showTargetDrawer(target = null) {
        appState.editingTarget = target;
        appState.isDrawerOpen = true;
        
        const drawer = document.getElementById('targetDrawer');
        const title = document.getElementById('drawerTitle');
        const saveBtn = document.getElementById('saveBtn');
        
        if (target) {
            title.textContent = '编辑监控目标';
            saveBtn.textContent = '保存';
            fillForm(target);
        } else {
            title.textContent = '新建监控目标';
            saveBtn.textContent = '创建';
            clearForm();
        }
        
        drawer.style.display = 'block';
        setTimeout(() => {
            drawer.querySelector('.drawer-content').classList.add('show');
        }, 10);
    }

    function hideTargetDrawer() {
        const drawer = document.getElementById('targetDrawer');
        drawer.querySelector('.drawer-content').classList.remove('show');
        setTimeout(() => {
            drawer.style.display = 'none';
            appState.isDrawerOpen = false;
            appState.editingTarget = null;
        }, 300);
    }

    function fillForm(target) {
        document.getElementById('targetName').value = target.name;
        document.getElementById('targetDescription').value = target.description;
        document.getElementById('targetAddress').value = target.address;
        document.getElementById('targetPort').value = target.port;
        document.getElementById('targetRegion').value = target.region;
        document.getElementById('priority').value = target.priority;
        document.getElementById('customTags').value = target.tags.join(', ');
    }

    function clearForm() {
        document.getElementById('targetForm').reset();
        document.getElementById('testInterval').value = '60';
        document.getElementById('timeout').value = '5000';
        document.getElementById('retryCount').value = '3';
        document.getElementById('priority').value = 'P2';
        document.getElementById('bindStrategy').value = 'all';
        document.getElementById('nodeSelection').style.display = 'none';
        document.getElementById('nodeSearch').value = '';
        
        // 重置节点选择
        nodeData.forEach(node => {
            node.checked = false;
        });
        filteredNodes = [...nodeData];
        
        updateMatchedNodes();
    }

    function handleFormSubmit(e) {
        e.preventDefault();
        
        const formData = {
            name: document.getElementById('targetName').value,
            description: document.getElementById('targetDescription').value,
            address: document.getElementById('targetAddress').value,
            port: parseInt(document.getElementById('targetPort').value),
            region: document.getElementById('targetRegion').value
        };

        // 简单验证
        if (!formData.name || !formData.address || !formData.port) {
            alert('请填写必填字段');
            return;
        }

        // 模拟保存
        if (appState.editingTarget) {
            // 更新现有目标
            const index = mockData.targets.findIndex(t => t.id === appState.editingTarget.id);
            if (index !== -1) {
                mockData.targets[index] = { ...mockData.targets[index], ...formData };
            }
        } else {
            // 创建新目标
            const newTarget = {
                id: Date.now(),
                ...formData,
                nodes: ['节点A', '节点B'],
                availability: 100,
                latency: 50,
                status: 'online',
                priority: document.getElementById('priority').value,
                tags: document.getElementById('customTags').value.split(',').map(t => t.trim()).filter(t => t)
            };
            mockData.targets.push(newTarget);
        }

        hideTargetDrawer();
        renderTargetTable();
        renderPagination();
        renderStatistics();
        
        showNotification(appState.editingTarget ? '目标更新成功' : '目标创建成功', 'success');
    }

    // 目标操作
    function editTarget(id) {
        const target = mockData.targets.find(t => t.id === id);
        if (target) {
            showTargetDrawer(target);
        }
    }

    function cloneTarget(id) {
        const target = mockData.targets.find(t => t.id === id);
        if (target) {
            const cloned = { ...target, name: target.name + ' (副本)', id: null };
            showTargetDrawer(cloned);
        }
    }

    function deleteTarget(id) {
        if (confirm('确定要删除这个监控目标吗？')) {
            const index = mockData.targets.findIndex(t => t.id === id);
            if (index !== -1) {
                mockData.targets.splice(index, 1);
                renderTargetTable();
                renderPagination();
                renderStatistics();
                showNotification('目标删除成功', 'success');
            }
        }
    }

    function toggleTargetStatus(id) {
        const target = mockData.targets.find(t => t.id === id);
        if (target) {
            target.status = target.status === 'online' ? 'offline' : 'online';
            renderTargetTable();
            renderStatistics();
            showNotification(`目标已${target.status === 'online' ? '启用' : '禁用'}`, 'success');
        }
    }

    function showTargetDetail(id) {
        const target = mockData.targets.find(t => t.id === id);
        if (target) {
            alert(`目标详情：\n名称：${target.name}\n地址：${target.address}:${target.port}\n可用率：${target.availability}%\n延迟：${target.latency}ms`);
        }
    }

    function showNodeDetails(targetId) {
        const target = mockData.targets.find(t => t.id === targetId);
        if (target) {
            const nodeDetails = target.nodes.map(nodeName => {
                const node = nodeData.find(n => n.name === nodeName);
                const status = node ? node.status : 'unknown';
                const statusText = status === 'online' ? '在线' : status === 'warning' ? '异常' : '离线';
                return `${nodeName} (${node ? node.region : '未知'}) - ${statusText}`;
            }).join('\n');
            
            alert(`目标 "${target.name}" 的节点详情：\n\n${nodeDetails}`);
        }
    }

    function copyAddress(address) {
        navigator.clipboard.writeText(address).then(() => {
            showNotification('地址已复制', 'success');
        });
    }

    // 批量操作
    function batchDelete() {
        if (appState.selectedTargets.length === 0) {
            alert('请选择要删除的目标');
            return;
        }

        if (confirm(`确定要删除选中的 ${appState.selectedTargets.length} 个目标吗？`)) {
            mockData.targets = mockData.targets.filter(t => !appState.selectedTargets.includes(t.id));
            appState.selectedTargets = [];
            renderTargetTable();
            renderPagination();
            renderStatistics();
            updateBatchButtons();
            showNotification('批量删除成功', 'success');
        }
    }

    function showBatchImport() {
        alert('批量导入功能演示\n\n支持的格式：\n- CSV文件\n- JSON文件\n- Excel文件\n\n包含字段：目标名称、地址、端口、地区、描述等');
    }

    function exportData() {
        const targets = getFilteredTargets();
        const csvContent = [
            ['目标名称', '地址', '端口', '地区', '描述', '可用率', '延迟'],
            ...targets.map(t => [t.name, t.address, t.port, t.region, t.description, t.availability + '%', t.latency + 'ms'])
        ].map(row => row.join(',')).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = '监控目标数据.csv';
        link.click();
        
        showNotification('数据导出成功', 'success');
    }

    // 高级选项切换
    function toggleAdvanced() {
        const options = document.getElementById('advancedOptions');
        const icon = document.getElementById('advancedIcon');
        
        if (options.classList.contains('hidden')) {
            options.classList.remove('hidden');
            // 使用Tabler Icons
            icon.className = 'ti ti-chevron-up float-right text-sm';
        } else {
            options.classList.add('hidden');
            // 使用Tabler Icons
            icon.className = 'ti ti-chevron-down float-right text-sm';
        }
    }

    // 权限相关
    function showPermissionWall() {
        document.getElementById('permissionWall').style.display = 'flex';
    }

    function closePermissionWall() {
        document.getElementById('permissionWall').style.display = 'none';
    }

    function upgradeNow() {
        alert('跳转到升级页面...\n\n这里会跳转到用户前端的升级页面');
    }

    // 主题切换
    function toggleTheme() {
        const html = document.documentElement;
        const themeToggle = document.getElementById('themeToggle');
        const icon = themeToggle.querySelector('.ti');

        if (html.classList.contains('dark')) {
            html.classList.remove('dark');
            localStorage.setItem('theme', 'light');
            icon.className = 'ti ti-moon';
        } else {
            html.classList.add('dark');
            localStorage.setItem('theme', 'dark');
            icon.className = 'ti ti-sun';
        }
    }

    // 通知系统
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg shadow-lg text-white ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 
            'bg-blue-500'
        }`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="ti mr-2 ${
                    type === 'success' ? 'ti-circle-check' :
                    type === 'error' ? 'ti-alert-circle' :
                    'ti-info-circle'
                }"></i>
                ${message}
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // 全局函数暴露
    window.editTarget = editTarget;
    window.cloneTarget = cloneTarget;
    window.deleteTarget = deleteTarget;
    window.toggleTargetStatus = toggleTargetStatus;
    window.showTargetDetail = showTargetDetail;
    window.showNodeDetails = showNodeDetails;
    window.copyAddress = copyAddress;
    window.sortTable = sortTable;
    window.goToPage = goToPage;
    window.toggleAdvanced = toggleAdvanced;
    window.closePermissionWall = closePermissionWall;
    window.upgradeNow = upgradeNow;
    window.toggleTheme = toggleTheme;
    window.selectAllNodes = selectAllNodes;
    window.selectNoNodes = selectNoNodes;
    window.selectNodesByRegion = selectNodesByRegion;

    // 样式定义
    const style = document.createElement('style');
    style.textContent = `
        .btn {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background-color: #2563eb;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #1d4ed8;
        }
        
        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #4b5563;
        }
        
        .btn-danger {
            background-color: #dc2626;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #b91c1c;
        }
        
        .drawer-overlay {
            backdrop-filter: blur(2px);
        }
        
        .drawer-content {
            box-shadow: -8px 0 32px rgba(0, 0, 0, 0.1);
        }
        
        .dark .drawer-content {
            box-shadow: -8px 0 32px rgba(0, 0, 0, 0.3);
        }
        
        @media (max-width: 768px) {
            .drawer-content {
                width: 100%;
                right: -100%;
            }
        }
    `;
    document.head.appendChild(style);

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initApp);
    } else {
        initApp();
    }

})();