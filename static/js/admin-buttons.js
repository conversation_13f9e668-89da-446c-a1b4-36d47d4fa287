/**
 * @description 管理页面按钮控制脚本
 * 1. 实现保存按钮在页面滚动时的行为控制
 * 2. 当页面向下滚动一定距离后，按钮位置固定在页面底部
 * 3. 当页面滚动到底部附近时，按钮上移，避免挡住底部内容
 * @modified 2023-10-24
 */
document.addEventListener('DOMContentLoaded', function() {
    // 获取保存按钮元素 - 更新选择器适应新的类结构
    const saveButton = document.querySelector('.fixed.bottom-6.right-6.z-50');
    
    if (!saveButton) return; // 如果页面上没有保存按钮，则退出
    
    let initialBottom = 20; // Default bottom position in px
    let isDocked = false;

    if (saveButton) {
        initialBottom = parseInt(window.getComputedStyle(saveButton).bottom, 10);

        window.addEventListener('scroll', () => {
            const distanceToBottom = document.documentElement.scrollHeight - window.innerHeight - window.scrollY;

            if (distanceToBottom < 50 && !isDocked) {
                 // Docked state: Use Tailwind classes for full opacity and larger shadow
                saveButton.style.bottom = `${initialBottom + (100 - distanceToBottom)}px`; // Keep dynamic bottom
                saveButton.classList.remove('opacity-90', 'shadow-md');
                saveButton.classList.add('opacity-100', 'shadow-lg');
                isDocked = true;
            } else if (distanceToBottom >= 50 && isDocked) {
                // Normal floating state: Use Tailwind classes for slight opacity and smaller shadow
                saveButton.style.bottom = `${initialBottom}px`; // Keep dynamic bottom
                saveButton.classList.remove('opacity-100', 'shadow-lg');
                saveButton.classList.add('opacity-90', 'shadow-md');
                isDocked = false;
            }
        });
         // Initial state setup based on Tailwind (assuming default is floating)
         if (!isDocked) {
             saveButton.classList.add('opacity-90', 'shadow-md');
         }
    }
}); 