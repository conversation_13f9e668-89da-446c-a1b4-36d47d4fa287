/**
 * 调试下拉菜单 hover 效果的脚本
 * 在浏览器控制台运行此脚本来诊断问题
 */

(function debugDropdownHover() {
    console.log('=== 开始诊断下拉菜单 Hover 效果 ===');
    
    // 1. 检查 HTML 元素的类
    const htmlClasses = document.documentElement.classList;
    console.log('HTML 类:', Array.from(htmlClasses).join(', '));
    console.log('是否为深色模式:', htmlClasses.contains('dark'));
    console.log('是否有 theme-initializing:', htmlClasses.contains('theme-initializing'));
    console.log('是否有 theme-initialized:', htmlClasses.contains('theme-initialized'));
    
    // 2. 查找下拉菜单
    const dropdownMenu = document.getElementById('settings-dropdown-menu');
    if (!dropdownMenu) {
        console.error('未找到下拉菜单 #settings-dropdown-menu');
        return;
    }
    
    // 3. 查找所有下拉菜单链接
    const menuLinks = dropdownMenu.querySelectorAll('a');
    console.log(`找到 ${menuLinks.length} 个菜单链接`);
    
    // 4. 检查每个链接的样式
    menuLinks.forEach((link, index) => {
        console.log(`\n--- 链接 ${index + 1} ---`);
        console.log('类名:', link.className);
        
        const computedStyle = window.getComputedStyle(link);
        console.log('当前背景色:', computedStyle.backgroundColor);
        console.log('过渡效果:', computedStyle.transition);
        
        // 模拟 hover 状态
        link.classList.add('hover-test');
        
        // 检查是否有 dark:hover:bg-slate-700 类
        const hasDarkHoverClass = link.className.includes('dark:hover:bg-slate-700');
        console.log('是否有 dark:hover:bg-slate-700 类:', hasDarkHoverClass);
        
        // 获取所有应用的 CSS 规则
        try {
            const sheets = document.styleSheets;
            const matchedRules = [];
            
            for (let sheet of sheets) {
                try {
                    const rules = sheet.cssRules || sheet.rules;
                    if (!rules) continue;
                    
                    for (let rule of rules) {
                        if (rule.selectorText && link.matches(rule.selectorText)) {
                            matchedRules.push({
                                selector: rule.selectorText,
                                style: rule.style.cssText,
                                source: sheet.href || '内联样式'
                            });
                        }
                    }
                } catch (e) {
                    // 跨域样式表无法访问
                }
            }
            
            console.log('匹配的 CSS 规则:', matchedRules);
        } catch (e) {
            console.log('无法获取 CSS 规则:', e.message);
        }
        
        link.classList.remove('hover-test');
    });
    
    // 5. 测试创建一个新元素看 Tailwind 类是否工作
    console.log('\n=== 测试 Tailwind 类是否正常工作 ===');
    const testElement = document.createElement('div');
    testElement.className = 'bg-slate-700 dark:bg-slate-800';
    document.body.appendChild(testElement);
    
    const testStyle = window.getComputedStyle(testElement);
    console.log('测试元素背景色:', testStyle.backgroundColor);
    console.log('预期 slate-700:', 'rgb(51, 65, 85)');
    console.log('预期 slate-800:', 'rgb(30, 41, 59)');
    
    document.body.removeChild(testElement);
    
    // 6. 检查是否有覆盖 hover 的样式
    console.log('\n=== 检查可能的样式覆盖 ===');
    
    // 创建一个临时的 hover 测试
    if (menuLinks.length > 0) {
        const firstLink = menuLinks[0];
        
        // 添加一个临时的 style 标签来测试
        const testStyle = document.createElement('style');
        testStyle.textContent = `
            .test-hover-fix:hover {
                background-color: rgb(51, 65, 85) !important;
            }
        `;
        document.head.appendChild(testStyle);
        
        firstLink.classList.add('test-hover-fix');
        console.log('已添加测试修复类 .test-hover-fix');
        console.log('请尝试 hover 第一个菜单项，看背景是否变化');
        
        // 5秒后清理
        setTimeout(() => {
            firstLink.classList.remove('test-hover-fix');
            testStyle.remove();
            console.log('测试修复已移除');
        }, 5000);
    }
    
    console.log('\n=== 诊断完成 ===');
    console.log('建议：');
    console.log('1. 检查是否有 theme-initializing 类未被移除');
    console.log('2. 检查 Tailwind CSS 是否正确编译了 dark:hover 变体');
    console.log('3. 使用开发者工具的 "Force element state > :hover" 来测试');
})();