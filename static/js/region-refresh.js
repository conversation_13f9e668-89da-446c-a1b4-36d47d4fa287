/**
 * region-refresh.js
 * 处理地区分布的刷新功能
 */

document.addEventListener('DOMContentLoaded', function() {
    // 获取刷新按钮
    const refreshButton = document.getElementById('refresh-region-stats');
    const refreshButtonMobile = document.getElementById('refresh-region-stats-mobile');
    
    // 防抖定时器
    let refreshDebounceTimer = null;
    
    // 刷新函数
    function refreshRegionStats() {
        // 清除之前的定时器，防止重复触发
        if (refreshDebounceTimer) {
            clearTimeout(refreshDebounceTimer);
        }
        
        refreshDebounceTimer = setTimeout(() => {
            if (window.RegionStatsModule) {
                // 判断当前是否开启了自动更新
                const isAutoUpdating = window.RegionStatsModule.updateSettings && 
                                      window.RegionStatsModule.updateSettings.enabled;
                
                // 强制刷新地区统计
                window.RegionStatsModule.forceUpdate();
                
                // 显示刷新成功通知
                if (typeof notice === 'function') {
                    notice('地区分布已更新', 'success');
                }
                
                // 如果不是自动更新模式，显示额外提示
                if (!isAutoUpdating) {
                    const autoUpdateMsg = '提示：可以通过RegionStatsModule.startAutoUpdate()启用自动更新';
                    console.info(autoUpdateMsg);
                }
            } else {
                console.error('RegionStatsModule 未加载，无法刷新地区分布');
                if (typeof notice === 'function') {
                    notice('刷新失败，地区分布模块未加载', 'error');
                }
            }
            
            refreshDebounceTimer = null;
        }, 300); // 300ms 防抖延迟
    }
    
    // 添加点击事件
    if (refreshButton) {
        refreshButton.addEventListener('click', refreshRegionStats);
    }
    
    if (refreshButtonMobile) {
        refreshButtonMobile.addEventListener('click', refreshRegionStats);
    }
    
    // 添加自动刷新切换按钮事件（如果存在）
    const autoUpdateToggle = document.getElementById('toggle-region-auto-update');
    if (autoUpdateToggle && window.RegionStatsModule) {
        // 初始设置按钮状态
        if (window.RegionStatsModule.updateSettings.enabled) {
            autoUpdateToggle.classList.add('active');
            autoUpdateToggle.title = '点击停止自动更新';
        } else {
            autoUpdateToggle.classList.remove('active');
            autoUpdateToggle.title = '点击启动自动更新';
        }
        
        // 添加点击事件
        autoUpdateToggle.addEventListener('click', function() {
            if (window.RegionStatsModule.updateSettings.enabled) {
                // 关闭自动更新
                window.RegionStatsModule.stopAutoUpdate();
                autoUpdateToggle.classList.remove('active');
                autoUpdateToggle.title = '点击启动自动更新';
                if (typeof notice === 'function') {
                    notice('地区分布自动更新已关闭', 'info');
                }
            } else {
                // 开启自动更新
                window.RegionStatsModule.startAutoUpdate();
                autoUpdateToggle.classList.add('active');
                autoUpdateToggle.title = '点击停止自动更新';
                if (typeof notice === 'function') {
                    notice('地区分布自动更新已开启', 'success');
                }
            }
        });
    }
}); 