/**
 * 壁纸加载优化脚本
 * 从base.html中提取出的壁纸加载功能
 */
(function() {
    // 获取个性化缓存键名（与settings-sync保持一致）
    function getPersonalizationCacheKey() {
        const port = window.location.port || '80';
        return `personalization-settings-${port}`;
    }

    // 获取壁纸设置（优化版：支持多数据源）
    function getWallpaperSettings() {
        let settings = null;

        try {
            // 🎯 第一优先级：sessionStorage（页面间快速共享）
            const sessionData = sessionStorage.getItem('personalization-settings');
            if (sessionData) {
                settings = JSON.parse(sessionData);
                console.log('📱 wallpaper-loader从sessionStorage获取设置');
            }
        } catch (e) {
            console.warn('无法从sessionStorage读取设置:', e);
        }

        // 🎯 第二优先级：localStorage（跨会话持久化，带版本号检查）
        if (!settings) {
            try {
                const localData = localStorage.getItem(getPersonalizationCacheKey());
                if (localData) {
                    const cachedSettings = JSON.parse(localData);
                    const now = Date.now();
                    const CACHE_DURATION = 300000; // 5分钟缓存有效期

                    // 检查缓存是否在有效期内
                    if (cachedSettings._cacheTime && (now - cachedSettings._cacheTime) < CACHE_DURATION) {
                        settings = cachedSettings;
                        console.log('💾 wallpaper-loader从localStorage获取设置，版本:', settings._version || 'unknown');

                        // 同步到sessionStorage，提升后续访问速度
                        try {
                            sessionStorage.setItem('personalization-settings', JSON.stringify(settings));
                        } catch (syncError) {
                            console.warn('无法同步设置到sessionStorage:', syncError);
                        }
                    } else {
                        console.log('localStorage缓存已过期，wallpaper-loader跳过');
                    }
                }
            } catch (e) {
                console.warn('无法从localStorage读取设置:', e);
            }
        }

        return settings;
    }

    // 保存壁纸设置到 sessionStorage
    function saveWallpaperSettings(settings) {
        try {
            sessionStorage.setItem('personalization-settings', JSON.stringify(settings));
        } catch(e) {
            console.warn('无法保存壁纸设置到会话存储:', e);
        }
    }

    // 应用壁纸样式
    function applyWallpaperStyles(settings) {
        // 移除旧的壁纸样式
        const oldStyle = document.getElementById('dynamic-wallpaper-style');
        if (oldStyle) oldStyle.remove();

        // 如果壁纸未启用，则不添加新样式
        if (!settings || !settings.wallpaper || !settings.wallpaper.enabled || !settings.wallpaper.url) {
            document.body.classList.remove('has-background-image');
            return;
        }

        // 添加标记类
        document.body.classList.add('has-background-image');
    }

    // 创建并缓存壁纸图片
    function loadAndCacheWallpaper(settings) {
        if (!settings || !settings.url) return;
        
        // 检查是否已经加载过壁纸
        if (document.body.classList.contains('wallpaper-loaded')) {
            return; // 已加载过，跳过
        }

        var img = new Image();

        img.onload = function() {
            // 图片加载成功
            document.body.classList.add('wallpaper-loaded');
            document.body.classList.add('has-background-image');
        };

        img.onerror = function() {
            console.error('壁纸图片加载失败:', settings.url);
        };

        // 使用缓存加载图片
        img.src = settings.url;
    }

    // 立即尝试初始化壁纸（不等待DOM加载完成，提升首屏性能）
    function initializeWallpaper() {
        const settings = getWallpaperSettings();
        if (settings) {
            applyWallpaperStyles(settings);
            loadAndCacheWallpaper(settings);
            console.log('🚀 壁纸预加载器已启动（立即执行）');
        }
    }

    // 立即执行初始化
    initializeWallpaper();

    // 同时保留DOM加载完成后的初始化作为备用（防止立即执行时sessionStorage未准备好）
    document.addEventListener('DOMContentLoaded', function() {
        // 如果还没有加载壁纸，再次尝试
        if (!document.body.classList.contains('wallpaper-loaded')) {
            initializeWallpaper();
            console.log('🔄 DOM加载完成后重试壁纸初始化');
        }

        // 初始化跨页面同步监听器
        initCrossPageSync();
    });

    // 跨页面设置同步机制
    function initCrossPageSync() {
        // 监听localStorage变化事件（标签页间同步）
        window.addEventListener('storage', function(e) {
            const cacheKey = getPersonalizationCacheKey();
            if (e.key === cacheKey && e.newValue) {
                try {
                    const newSettings = JSON.parse(e.newValue);
                    console.log('🔄 wallpaper-loader检测到localStorage更新，版本:', newSettings._version || 'unknown');

                    // 检查是否需要更新壁纸
                    const currentSettings = getWallpaperSettings();
                    const shouldUpdate = !currentSettings ||
                                       !newSettings._version ||
                                       !currentSettings._version ||
                                       newSettings._version > currentSettings._version;

                    if (shouldUpdate) {
                        // 更新sessionStorage
                        sessionStorage.setItem('personalization-settings', JSON.stringify(newSettings));

                        // 重新应用壁纸设置
                        if (newSettings.wallpaper) {
                            applyWallpaperStyles(newSettings);
                            loadAndCacheWallpaper(newSettings.wallpaper);
                            console.log('✅ wallpaper-loader已同步最新设置');
                        }
                    } else {
                        console.log('⏭️ wallpaper-loader跳过过期设置更新');
                    }
                } catch (err) {
                    console.warn('wallpaper-loader解析localStorage更新失败:', err);
                }
            }
        });

        // 监听个性化设置更新事件
        document.addEventListener('personalization-settings-updated', function(e) {
            if (e.detail && e.detail.wallpaper) {
                console.log('🎨 wallpaper-loader收到设置更新事件');
                applyWallpaperStyles(e.detail);
                loadAndCacheWallpaper(e.detail.wallpaper);
            }
        });

        console.log('🔗 wallpaper-loader跨页面同步已初始化');
    }
})();