/**
 * Network Quality Filter Manager - Tailwind CSS Version
 * 网络质量页面筛选管理器
 */

class NetworkQualityFilterManager {
    constructor(options = {}) {
        this.options = {
            enableSearch: true,
            enableInlineFilters: true,
            debounceDelay: 300,
            ...options
        };

        // 组件实例
        this.components = {
            search: null,
            inlineFilters: null
        };

        // 状态管理
        this.state = {
            searchQuery: '',
            activeFilters: {
                group: null,
                region: null,
                status: null
            },
            filteredNodes: [],
            totalNodes: 0,
            // 新增：服务端返回的facets与映射
            facets: null,
            facetsAll: null,
            maps: null
        };

        // 回调函数
        this.callbacks = {
            onFilterChange: null,
            ...options.callbacks
        };

        // 防抖定时器
        this.debounceTimer = null;

        // 初始化
        this.init();
    }

    /**
     * 初始化筛选管理器
     */
    init() {
        this.initComponents();
        this.bindComponentEvents();
        
        // 设置全局引用
        window.networkQualityFilterManager = this;
    }

    /**
     * 初始化组件
     */
    initComponents() {
        // 初始化搜索组件
        if (this.options.enableSearch && window.NetworkQualitySearch) {
            this.components.search = new NetworkQualitySearch({
                callbacks: {
                    onSearch: (query, results) => this.handleSearchChange(query, results),
                    onClear: () => this.handleSearchClear(),
                    onChange: (query) => this.handleSearchInput(query)
                }
            });
        }

        // 初始化一行集成筛选器组件
        if (this.options.enableInlineFilters && window.NetworkQualityInlineFilters) {
            this.components.inlineFilters = new NetworkQualityInlineFilters({
                callbacks: {
                    onFilterChange: (data) => this.handleInlineFilterChange(data)
                }
            });
        }
    }

    /**
     * 绑定组件事件
     */
    bindComponentEvents() {
        // 监听页面数据更新事件
        document.addEventListener('networkQualityDataUpdate', (e) => {
            this.handleDataUpdate(e.detail);
        });

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // 监听滚动事件，控制回到顶部按钮
        window.addEventListener('scroll', () => {
            this.handleScroll();
        });

        // 绑定回到顶部按钮
        const scrollToTopBtn = document.getElementById('scroll-to-top-btn');
        if (scrollToTopBtn) {
            scrollToTopBtn.addEventListener('click', () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        }
    }

    /**
     * 处理搜索变化
     */
    handleSearchChange(query, results) {
        this.state.searchQuery = query;
        this.applyAllFilters();
        
    }

    /**
     * 处理搜索清除
     */
    handleSearchClear() {
        this.state.searchQuery = '';
        this.applyAllFilters();
        
    }

    /**
     * 处理搜索输入
     */
    handleSearchInput(query) {
        // 实时更新搜索状态但不立即应用筛选
        this.state.searchQuery = query;
    }

    /**
     * 处理一行筛选器变化
     */
    handleInlineFilterChange(data) {
        Object.assign(this.state.activeFilters, data.filters);
        this.applyAllFilters();
    }



    /**
     * 处理筛选变化
     */
    handleFilterChange(data) {
        Object.assign(this.state.activeFilters, data.filters);
        this.applyAllFilters();
        
    }

    /**
     * 处理地区选择
     */
    handleRegionSelect(region) {
        this.state.activeFilters.region = region;
        
        // 同步到筛选组件
        if (this.components.filters) {
            this.components.filters.state.activeFilters.region = region;
            this.components.filters.applyFilters();
        }
    }

    /**
     * 处理数据更新
     */
    handleDataUpdate(detail) {
        if (detail.nodes) {
            const wasEmpty = this.state.totalNodes === 0;
            this.state.totalNodes = detail.nodes.length;
            
            // 始终更新组件数据，不论是否首次加载
            setTimeout(() => {
                if (this.components.inlineFilters) {
                    this.components.inlineFilters.updateData();
                }

                // 重新应用筛选（标记为数据驱动，避免触发页面重复拉取）
                this.applyAllFilters({ reason: 'data' });
            }, 150);
        }
    }

    /**
     * 更新 facets 数据（分组和地区的真实计数）
     */
    updateFacets(facets) {
        if (!facets) return;
        // 兼容旧签名：直接传入filtered facets 对象
        if (facets.filtered || facets.all || facets.maps) {
            this.state.facets = facets.filtered || this.state.facets;
            this.state.facetsAll = facets.all || this.state.facetsAll;
            this.state.maps = facets.maps || this.state.maps;
        } else {
            this.state.facets = facets;
        }

        // 传递给内联筛选器组件更新下拉菜单
        if (this.components.inlineFilters && this.components.inlineFilters.updateFacets) {
            this.components.inlineFilters.updateFacets({
                filtered: this.state.facets,
                all: this.state.facetsAll,
                maps: this.state.maps
            });
        }
    }

    /**
     * 强制更新按钮文本显示
     */
    forceUpdateButtonTexts() {
        try {
            if (this.components.filters) {
                const filters = this.components.filters;
                const totalNodes = this.getAllNodes().length;
                
                // 更新分组按钮文本（兼容旧选择器）
                const groupBtnNew = document.querySelector('#nq-group-filter .nq-group-text');
                const groupBtnOld = document.querySelector('#nq-group-dropdown .nq-dropdown-text');
                const groupBtn = groupBtnNew || groupBtnOld;
                if (groupBtn && (!filters.state.activeFilters.group || filters.state.activeFilters.group === 'all')) {
                    groupBtn.textContent = `全部分组`;
                }

                // 更新地区按钮文本（兼容旧选择器）
                const regionBtnNew = document.querySelector('#nq-region-filter .nq-region-text');
                const regionBtnOld = document.querySelector('#nq-region-dropdown .nq-dropdown-text');
                const regionBtn = regionBtnNew || regionBtnOld;
                if (regionBtn && (!filters.state.activeFilters.region || filters.state.activeFilters.region === 'all')) {
                    regionBtn.textContent = `全部地区`;
                }
                
                // 更新节点计数指示器
                this.updateNodeCounter(totalNodes, totalNodes);
            }
        } catch (error) {
            // 静默失败
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 防抖处理
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        
        this.debounceTimer = setTimeout(() => {
            // 触发组件重新布局
            this.triggerLayout();
        }, this.options.debounceDelay);
    }

    /**
     * 应用所有筛选
     */
    applyAllFilters(options = {}) {
        const { reason = 'user', silent = false } = options;
        const allNodes = this.getAllNodes();
        let filteredNodes = allNodes;

        // 应用搜索筛选
        if (this.state.searchQuery) {
            filteredNodes = this.applySearchFilter(filteredNodes, this.state.searchQuery);
        }

        // 应用其他筛选
        filteredNodes = this.applyFilters(filteredNodes, this.state.activeFilters);

        this.state.filteredNodes = filteredNodes;
        this.updateDOM(filteredNodes);
        this.updateNodeCounter(filteredNodes.length, allNodes.length);
        this.updateScrollHint(filteredNodes.length);
        if (!silent) this.triggerFilterEvent({ reason });
    }

    /**
     * 应用搜索筛选
     */
    applySearchFilter(nodes, query) {
        if (!query) return nodes;
        
        const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);
        
        return nodes.filter(node => {
            const searchText = this.getNodeSearchText(node).toLowerCase();
            return searchTerms.every(term => searchText.includes(term));
        });
    }

    /**
     * 应用筛选条件
     */
    applyFilters(nodes, filters) {
        let filtered = nodes;

        // 应用分组筛选
        if (filters.group) {
            filtered = filtered.filter(node => node.group === filters.group);
        }

        // 应用地区筛选
        if (filters.region) {
            filtered = filtered.filter(node => node.region === filters.region);
        }

        // 应用状态筛选
        if (filters.status) {
            filtered = filtered.filter(node => node.status === filters.status);
        }

        return filtered;
    }

    /**
     * 获取节点搜索文本
     */
    getNodeSearchText(node) {
        const parts = [
            node.name,
            node.id,
            node.group,
            node.region
        ];
        
        // 添加目标信息
        if (node.targets) {
            parts.push(...node.targets);
        }

        return parts.filter(Boolean).join(' ');
    }

    /**
     * 获取所有节点
     */
    getAllNodes() {
        const nodeCards = document.querySelectorAll('.network-status-card[data-node-id]');
        return Array.from(nodeCards).map(card => {
            const nodeId = card.dataset.nodeId;
            const nodeName = card.querySelector('h3')?.textContent?.trim() || '';
            const group = card.dataset.group || 'default';
            const region = card.dataset.region || 'unknown';
            
            // 判断节点状态：优先使用服务端服务器在线状态（dataset.serverStatus），
            // 仅在缺失时回退到基于网络质量状态指示器的判断，避免语义混淆
            let status = (card.dataset.serverStatus || '').toLowerCase();
            if (!status || (status !== 'online' && status !== 'offline')) {
                const statusIndicator = card.querySelector('.server-status-indicator');
                status = 'offline';
                if (statusIndicator) {
                    if (statusIndicator.classList.contains('server-status-online')) {
                        status = 'online';
                    } else if (statusIndicator.classList.contains('server-status-offline')) {
                        status = 'offline';
                    } else if (statusIndicator.classList.contains('server-status-warning')) {
                        status = 'warning';
                    } else if (statusIndicator.classList.contains('server-status-error')) {
                        status = 'error';
                    }
                }
            }

            // 获取目标信息
            const targets = Array.from(card.querySelectorAll('.target-item')).map(item => {
                return item.querySelector('.target-name')?.textContent?.trim() || '';
            });

            return {
                id: nodeId,
                name: nodeName,
                group: group,
                region: region,
                status: status,
                targets: targets,
                element: card
            };
        });
    }

    /**
     * 更新DOM显示
     */
    updateDOM(filteredNodes) {
        const allNodeCards = document.querySelectorAll('.network-status-card[data-node-id]');
        const filteredNodeIds = new Set(filteredNodes.map(node => node.id));

        allNodeCards.forEach(card => {
            const nodeId = card.dataset.nodeId;
            if (filteredNodeIds.has(nodeId)) {
                card.classList.remove('hidden-by-filter');
                card.style.display = '';
            } else {
                card.classList.add('hidden-by-filter');
                card.style.display = 'none';
            }
        });
    }

    /**
     * 触发筛选事件
     */
    triggerFilterEvent(meta = {}) {
        const eventData = {
            totalNodes: this.state.totalNodes,
            filteredNodes: this.state.filteredNodes.length,
            filters: { ...this.state.activeFilters },
            searchQuery: this.state.searchQuery,
            nodes: this.state.filteredNodes,
            reason: meta.reason || 'user',
            timestamp: Date.now()
        };

        // 触发自定义事件
        const event = new CustomEvent('networkQualityfiltersApplied', {
            detail: eventData
        });
        document.dispatchEvent(event);

        // 触发回调
        if (this.callbacks.onFilterChange) {
            this.callbacks.onFilterChange(eventData);
        }
    }

    /**
     * 触发布局事件
     */
    triggerLayout() {
        const event = new CustomEvent('networkQualityLayoutUpdate', {
            detail: {
                timestamp: Date.now(),
                source: 'NetworkQualityFilterManager'
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 清除所有筛选
     */
    clearAllFilters() {
        // 清除搜索
        if (this.components.search) {
            this.components.search.clearSearch();
        }

        // 清除一行筛选器
        if (this.components.inlineFilters) {
            this.components.inlineFilters.clearAllFilters();
        }

        // 重置状态
        this.state.searchQuery = '';
        this.state.activeFilters = {
            group: null,
            region: null,
            status: null
        };

        this.applyAllFilters();
    }

    /**
     * 更新节点计数指示器
     */
    updateNodeCounter(filtered, total) {
        const counter = document.getElementById('node-counter');
        if (counter) {
            counter.textContent = `显示 ${filtered} 个节点（共 ${total} 个）`;
            
            // 更新样式
            if (filtered !== total) {
                counter.classList.add('bg-blue-100', 'dark:bg-blue-900/30', 'text-blue-700', 'dark:text-blue-300');
                counter.classList.remove('bg-slate-100', 'dark:bg-slate-700');
            } else {
                counter.classList.remove('bg-blue-100', 'dark:bg-blue-900/30', 'text-blue-700', 'dark:text-blue-300');
                counter.classList.add('bg-slate-100', 'dark:bg-slate-700');
            }
        }
    }

    /**
     * 更新滚动提示
     */
    updateScrollHint(visibleCount) {
        const hint = document.getElementById('scroll-hint');
        if (hint && visibleCount > 0) {
            const estimatedVisibleCards = this.getEstimatedVisibleCards();
            if (visibleCount > estimatedVisibleCards) {
                hint.classList.remove('hidden');
            } else {
                hint.classList.add('hidden');
            }
        }
    }

    /**
     * 估算首屏可见卡片数量
     */
    getEstimatedVisibleCards() {
        const viewportHeight = window.innerHeight;
        const headerHeight = 400; // 大概的头部高度
        const availableHeight = viewportHeight - headerHeight;
        const cardHeight = 280; // 预估的卡片高度
        const rows = Math.floor(availableHeight / cardHeight);
        const columns = this.getEstimatedColumns();
        return Math.max(rows * columns, 2); // 至少2个
    }

    /**
     * 估算网格列数
     */
    getEstimatedColumns() {
        const width = window.innerWidth;
        if (width >= 2400) return 4;
        if (width >= 2000) return 3;
        if (width >= 1600) return 3;
        if (width >= 1200) return 2;
        if (width >= 769) return 2;
        return 1;
    }

    /**
     * 处理滚动事件
     */
    handleScroll() {
        const scrollToTopBtn = document.getElementById('scroll-to-top-btn');
        if (scrollToTopBtn) {
            if (window.scrollY > 300) {
                scrollToTopBtn.classList.remove('hidden');
            } else {
                scrollToTopBtn.classList.add('hidden');
            }
        }
    }

    /**
     * 获取当前状态
     */
    getState() {
        return {
            searchQuery: this.state.searchQuery,
            activeFilters: { ...this.state.activeFilters },
            filteredNodes: this.state.filteredNodes.length,
            totalNodes: this.state.totalNodes,
            components: {
                search: !!this.components.search,
                filters: !!this.components.filters,
                regions: !!this.components.regions
            }
        };
    }

    /**
     * 销毁筛选管理器
     */
    destroy() {
        // 销毁组件
        Object.values(this.components).forEach(component => {
            if (component && typeof component.destroy === 'function') {
                component.destroy();
            }
        });

        // 清理定时器
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        // 清理全局引用
        if (window.networkQualityFilterManager === this) {
            delete window.networkQualityFilterManager;
        }

    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    // 等待其他组件加载
    setTimeout(() => {
        if (!window.networkQualityFilterManager) {
            window.networkQualityFilterManager = new NetworkQualityFilterManager();
        }
    }, 100);
});

// 导出组件
window.NetworkQualityFilterManager = NetworkQualityFilterManager;
