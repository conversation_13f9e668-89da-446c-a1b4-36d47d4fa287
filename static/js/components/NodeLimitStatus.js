/**
 * 节点使用状态组件
 * 任务: T056 - 客户端节点数量控制功能
 * 功能: 显示节点使用情况，支持祖父条款用户特殊显示
 */

class NodeLimitStatus {
    constructor(containerId = 'nodeLimitStatus', miniMode = false) {
        this.containerId = containerId;
        this.miniMode = miniMode;
        this.stats = null;
        this.refreshInterval = null;
        this.isLoading = false;
        this.hasError = false;

        this.init();
    }
    
    /**
     * 初始化组件
     */
    async init() {
        this.render();
        await this.loadStats();
        this.startAutoRefresh();
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopAutoRefresh();
            } else {
                this.startAutoRefresh();
            }
        });

        // 监听许可证激活事件
        window.addEventListener('licenseActivated', (event) => {
            console.log('[NodeLimitStatus] 检测到许可证激活事件，刷新节点状态');
            // 延迟刷新，确保后端状态已同步
            setTimeout(() => {
                this.loadStats();
            }, 1500);
        });
    }
    
    /**
     * 渲染组件HTML结构
     */
    render() {
        const container = document.getElementById(this.containerId);
        if (!container) {
            console.warn(`[NodeLimitStatus] 容器 #${this.containerId} 不存在`);
            return;
        }

        if (this.miniMode) {
            // Mini模式：只更新现有的mini元素，不渲染新的HTML
            return;
        }

        container.innerHTML = `
            <div class="bg-white/95 dark:bg-slate-800/95 border border-slate-200/60 dark:border-slate-700/40 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm mb-4" id="nodeLimitCard">
                <div class="p-3">
                    <!-- 主要信息行 -->
                    <div class="flex items-center justify-between gap-3 mb-2">
                        <!-- 左侧：节点统计 -->
                        <div class="flex flex-col gap-1">
                            <span class="text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wide">节点使用</span>
                            <div class="flex items-baseline gap-1">
                                <span class="text-lg font-bold text-slate-800 dark:text-slate-200" id="nodeCount">-</span>
                                <span class="text-sm text-slate-500 dark:text-slate-400">/</span>
                                <span class="text-base font-semibold text-slate-600 dark:text-slate-300" id="nodeLimit">-</span>
                            </div>
                        </div>

                        <!-- 右侧：套餐信息和升级按钮 -->
                        <div class="flex items-center gap-2">
                            <div class="flex flex-col items-end gap-1">
                                <span class="text-xs font-medium text-slate-600 dark:text-slate-300 bg-slate-100/60 dark:bg-slate-700/40 px-2 py-1 rounded" id="planName">-</span>
                                <span class="text-xs font-semibold text-slate-500 dark:text-slate-400 bg-slate-50/60 dark:bg-slate-800/40 px-2 py-1 rounded" id="nodeUsagePercent">-</span>
                            </div>
                            <a href="/upgrade" class="hidden w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 items-center justify-center transform hover:scale-105 active:scale-95" id="upgradeBtn">
                                <i class="ti ti-arrow-up-circle"></i>
                            </a>
                        </div>
                    </div>

                    <!-- 进度条 -->
                    <div class="w-full bg-slate-200/60 dark:bg-slate-700/40 rounded-full h-1.5 overflow-hidden">
                        <div class="h-full bg-gradient-to-r from-emerald-500 to-emerald-600 transition-all duration-700 ease-out rounded-full" id="nodeProgressFill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 加载节点统计数据
     */
    async loadStats() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.setLoadingState(true);
        
        try {
            const response = await fetch('/api/admin/nodes/stats');
            const result = await response.json();
            
            if (result.success) {
                this.stats = result.data;
                this.hasError = false;
                this.updateUI();
                
                // 显示祖父条款横幅
                if (this.stats.isGrandfathered && this.stats.specialOffer) {
                    this.showLegacyUserBanner(this.stats.specialOffer);
                }
            } else {
                throw new Error(result.error || '获取节点统计失败');
            }
        } catch (error) {
            console.error('[NodeLimitStatus] 加载统计数据失败:', error);
            this.hasError = true;
            this.showError(error.message);
        } finally {
            this.isLoading = false;
            this.setLoadingState(false);
        }
    }
    
    /**
     * 更新UI显示
     */
    updateUI() {
        if (!this.stats) return;

        const {
            active,
            limit,
            planName,
            canAddMore,
            isGrandfathered,
            specialOffer,
            usage
        } = this.stats;

        if (this.miniMode) {
            // Mini模式更新
            this.updateMiniUI(active, limit, usage.percentage, usage.warningLevel);
        } else {
            // 完整模式更新
            this.updateProgressBar(usage.percentage, usage.warningLevel, isGrandfathered);
            this.updateTextInfo(active, limit, planName, usage.percentage, isGrandfathered);
            this.updateUpgradeButton(canAddMore, isGrandfathered, specialOffer);
            this.clearError();
        }
    }

    /**
     * 更新Mini模式UI
     */
    updateMiniUI(active, limit, percentage, warningLevel) {
        const miniNodeCount = document.getElementById('miniNodeCount');
        const miniNodeLimit = document.getElementById('miniNodeLimit');
        const miniProgressBar = document.getElementById('miniProgressBar');

        if (miniNodeCount) miniNodeCount.textContent = active;
        if (miniNodeLimit) miniNodeLimit.textContent = limit;

        if (miniProgressBar) {
            miniProgressBar.style.width = `${Math.min(percentage, 100)}%`;

            // 根据使用率设置颜色
            let colorClass = 'bg-blue-500';
            if (warningLevel === 'critical') {
                colorClass = 'bg-red-500';
            } else if (warningLevel === 'warning') {
                colorClass = 'bg-yellow-500';
            } else if (warningLevel === 'high') {
                colorClass = 'bg-orange-500';
            }

            // 清除所有颜色类并添加新的
            miniProgressBar.className = miniProgressBar.className.replace(/bg-(blue|red|yellow|orange)-500/g, '');
            miniProgressBar.classList.add(colorClass);
        }
    }

    /**
     * 更新进度条
     */
    updateProgressBar(percentage, warningLevel, isGrandfathered) {
        const progressFill = document.getElementById('nodeProgressFill');
        if (!progressFill) return;

        progressFill.style.width = `${Math.min(percentage, 100)}%`;

        // 清除所有状态类，保留基础类
        progressFill.className = 'h-full transition-all duration-700 ease-out rounded-full';

        // 根据状态添加对应的背景色
        if (isGrandfathered) {
            progressFill.classList.add('bg-gradient-to-r', 'from-purple-500', 'to-purple-600');
        } else {
            switch (warningLevel) {
                case 'critical':
                    progressFill.classList.add('bg-gradient-to-r', 'from-red-500', 'to-red-600');
                    break;
                case 'warning':
                    progressFill.classList.add('bg-gradient-to-r', 'from-amber-500', 'to-amber-600');
                    break;
                default:
                    progressFill.classList.add('bg-gradient-to-r', 'from-emerald-500', 'to-emerald-600');
            }
        }
    }
    
    /**
     * 更新文本信息
     */
    updateTextInfo(active, limit, planName, percentage, isGrandfathered) {
        const nodeCount = document.getElementById('nodeCount');
        const nodeLimit = document.getElementById('nodeLimit');
        const planNameEl = document.getElementById('planName');
        const usagePercent = document.getElementById('nodeUsagePercent');

        if (nodeCount) nodeCount.textContent = active;
        if (nodeLimit) {
            nodeLimit.textContent = isGrandfathered ?
                `${limit} (传统)` : limit;
        }
        if (planNameEl) planNameEl.textContent = planName;
        if (usagePercent) {
            usagePercent.textContent = `${Math.round(percentage)}%`;
        }
    }
    
    /**
     * 更新升级按钮
     */
    updateUpgradeButton(canAddMore, isGrandfathered, specialOffer) {
        const upgradeBtn = document.getElementById('upgradeBtn');
        if (!upgradeBtn) return;

        const shouldShow = !canAddMore || isGrandfathered;

        if (shouldShow) {
            upgradeBtn.classList.remove('hidden');
            upgradeBtn.classList.add('flex');

            if (isGrandfathered && specialOffer) {
                upgradeBtn.innerHTML = `<i class="ti ti-star"></i>`;
                upgradeBtn.classList.remove('from-blue-500', 'to-purple-600', 'hover:from-blue-600', 'hover:to-purple-700');
                upgradeBtn.classList.add('from-pink-500', 'to-rose-600', 'hover:from-pink-600', 'hover:to-rose-700');
                upgradeBtn.href = `/upgrade?coupon=${specialOffer.code}`;
                upgradeBtn.title = `升级享${specialOffer.discount}%优惠`;
            } else {
                upgradeBtn.innerHTML = `<i class="ti ti-arrow-up-circle"></i>`;
                upgradeBtn.classList.remove('from-pink-500', 'to-rose-600', 'hover:from-pink-600', 'hover:to-rose-700');
                upgradeBtn.classList.add('from-blue-500', 'to-purple-600', 'hover:from-blue-600', 'hover:to-purple-700');
                upgradeBtn.href = '/upgrade';
                upgradeBtn.title = '升级套餐';
            }
        } else {
            upgradeBtn.classList.add('hidden');
            upgradeBtn.classList.remove('flex');
        }
    }
    
    /**
     * 显示祖父条款用户横幅
     */
    showLegacyUserBanner(offer) {
        // 避免重复显示
        const existingBanner = document.getElementById('legacyUserBanner');
        if (existingBanner) return;
        
        const banner = document.createElement('div');
        banner.id = 'legacyUserBanner';
        banner.className = 'legacy-user-banner';
        banner.innerHTML = `
            <div class="banner-content">
                <i class="ti ti-info-circle"></i>
                <div style="flex: 1;">
                    <div>您是我们的早期支持者！当前拥有超过免费版限制的节点。</div>
                    <strong>${offer.message}</strong>
                </div>
                <a href="/upgrade?coupon=${offer.code}" class="banner-action">
                    立即升级
                </a>
            </div>
        `;
        
        const container = document.getElementById(this.containerId);
        if (container) {
            container.parentNode.insertBefore(banner, container);
        }
    }
    
    /**
     * 设置加载状态
     */
    setLoadingState(loading) {
        const card = document.getElementById('nodeLimitCard');
        if (card) {
            if (loading) {
                card.classList.add('opacity-60', 'pointer-events-none');
            } else {
                card.classList.remove('opacity-60', 'pointer-events-none');
            }
        }
    }

    /**
     * 显示错误状态
     */
    showError(message) {
        const card = document.getElementById('nodeLimitCard');
        if (card) {
            card.classList.add('border-red-300', 'dark:border-red-700', 'bg-red-50/50', 'dark:bg-red-900/20');
            card.classList.remove('border-slate-200/60', 'dark:border-slate-700/40', 'bg-white/95', 'dark:bg-slate-800/95');
        }

        // 显示错误信息
        const nodeCount = document.getElementById('nodeCount');
        const nodeLimit = document.getElementById('nodeLimit');
        const planName = document.getElementById('planName');

        if (nodeCount) nodeCount.textContent = '?';
        if (nodeLimit) nodeLimit.textContent = '?';
        if (planName) planName.textContent = '加载失败';

        console.error('[NodeLimitStatus] 错误:', message);
    }

    /**
     * 清除错误状态
     */
    clearError() {
        const card = document.getElementById('nodeLimitCard');
        if (card) {
            card.classList.remove('border-red-300', 'dark:border-red-700', 'bg-red-50/50', 'dark:bg-red-900/20');
            card.classList.add('border-slate-200/60', 'dark:border-slate-700/40', 'bg-white/95', 'dark:bg-slate-800/95');
        }
    }
    
    /**
     * 开始自动刷新
     */
    startAutoRefresh() {
        this.stopAutoRefresh();
        this.refreshInterval = setInterval(() => {
            this.loadStats();
        }, 30000); // 30秒刷新一次
    }
    
    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    /**
     * 手动刷新
     */
    refresh() {
        return this.loadStats();
    }
    
    /**
     * 销毁组件
     */
    destroy() {
        this.stopAutoRefresh();
        
        // 移除横幅
        const banner = document.getElementById('legacyUserBanner');
        if (banner) {
            banner.remove();
        }
        
        // 清空容器
        const container = document.getElementById(this.containerId);
        if (container) {
            container.innerHTML = '';
        }
    }
}

// 全局实例
window.NodeLimitStatus = NodeLimitStatus;
