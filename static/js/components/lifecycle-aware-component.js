/**
 * 生命周期感知组件示例
 * 展示如何创建一个与TabLifecycleHandler集成的组件
 */

// 生命周期感知组件
class LifecycleAwareComponent {
    /**
     * 构造函数
     * @param {string} id - 组件ID
     * @param {Object} options - 配置选项
     */
    constructor(id, options = {}) {
        this.id = id;
        this.options = Object.assign({
            debug: false,
            transitionDuration: 1000,
            cacheEnabled: true
        }, options);
        
        this.state = {
            isActive: true,
            isSleeping: false,
            isTransitioning: false,
            lastUpdateTime: Date.now(),
            cachedData: null
        };
        
        this.log('组件已创建');
    }
    
    /**
     * 初始化组件
     */
    init() {
        this.log('初始化组件');
        
        // 注册到TabLifecycleHandler
        if (window.TabLifecycleHandler && typeof window.TabLifecycleHandler.registerComponent === 'function') {
            window.TabLifecycleHandler.registerComponent(this);
            this.log('已注册到TabLifecycleHandler');
        } else {
            this.warn('TabLifecycleHandler不可用，无法注册组件');
        }
        
        return this;
    }
    
    /**
     * 销毁组件
     */
    destroy() {
        this.log('销毁组件');
        
        // 从TabLifecycleHandler注销
        if (window.TabLifecycleHandler && typeof window.TabLifecycleHandler.unregisterComponent === 'function') {
            window.TabLifecycleHandler.unregisterComponent(this.id);
            this.log('已从TabLifecycleHandler注销');
        }
        
        // 清理状态
        this.state = null;
        this.options = null;
    }
    
    /**
     * 更新数据
     * @param {*} data - 要更新的数据
     * @returns {boolean} - 是否成功更新
     */
    updateData(data) {
        // 检查是否应该跳过更新
        if (window.TabLifecycleHandler && window.TabLifecycleHandler.shouldBlockUpdate()) {
            this.log('数据更新被阻止，标签页正在从休眠中恢复');
            
            // 缓存数据
            if (this.options.cacheEnabled) {
                this.state.cachedData = data;
                this.log('数据已缓存');
            }
            
            return false;
        }
        
        this.log('更新数据');
        this.state.lastUpdateTime = Date.now();
        
        // 在这里实现实际的数据更新逻辑
        // ...
        
        return true;
    }
    
    /**
     * 应用缓存的数据
     * @returns {boolean} - 是否成功应用缓存
     */
    applyCache() {
        if (!this.state.cachedData) {
            this.log('没有缓存的数据');
            return false;
        }
        
        this.log('应用缓存的数据');
        
        // 在这里实现应用缓存数据的逻辑
        // ...
        
        // 清除缓存
        const cachedData = this.state.cachedData;
        this.state.cachedData = null;
        
        return true;
    }
    
    /**
     * 组件接口 - 休眠处理
     */
    onSleep() {
        this.log('进入休眠状态');
        this.state.isSleeping = true;
        this.state.isActive = false;
        
        // 在这里实现休眠逻辑
        // ...
    }
    
    /**
     * 组件接口 - 唤醒处理
     */
    onWake() {
        this.log('唤醒');
        this.state.isSleeping = false;
        this.state.isActive = true;
        
        // 在这里实现唤醒逻辑
        // ...
        
        // 应用缓存的数据
        if (this.options.cacheEnabled && this.state.cachedData) {
            this.applyCache();
        }
    }
    
    /**
     * 组件接口 - 应用过渡效果
     * @param {boolean} force - 是否强制应用
     */
    applyTransition(force = false) {
        this.log(`应用过渡效果 (force=${force})`);
        this.state.isTransitioning = true;
        
        // 在这里实现过渡效果
        // ...
        
        // 设置定时器，在过渡结束后重置状态
        setTimeout(() => {
            this.state.isTransitioning = false;
            this.log('过渡效果结束');
        }, this.options.transitionDuration);
    }
    
    /**
     * 组件接口 - 检查是否应该更新
     * @returns {boolean} - 是否应该更新
     */
    shouldUpdate() {
        // 如果组件处于休眠状态或正在过渡，不应该更新
        if (this.state.isSleeping || this.state.isTransitioning) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 记录日志
     * @param {...*} args - 日志参数
     */
    log(...args) {
        if (this.options.debug) {
            console.log(`[${this.id}]`, ...args);
        }
    }
    
    /**
     * 记录警告
     * @param {...*} args - 警告参数
     */
    warn(...args) {
        console.warn(`[${this.id}]`, ...args);
    }
    
    /**
     * 记录错误
     * @param {...*} args - 错误参数
     */
    error(...args) {
        console.error(`[${this.id}]`, ...args);
    }
}

// 导出组件
window.LifecycleAwareComponent = LifecycleAwareComponent;

// 创建一个示例组件实例
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化，确保TabLifecycleHandler已加载
    setTimeout(() => {
        if (window.TabLifecycleHandler) {
            // 创建并初始化示例组件
            window.exampleComponent = new LifecycleAwareComponent('example-component', {
                debug: true,
                transitionDuration: 1500,
                cacheEnabled: true
            }).init();
            
            console.log('示例组件已创建并初始化');
        } else {
            console.warn('TabLifecycleHandler未加载，无法创建示例组件');
        }
    }, 500);
});
