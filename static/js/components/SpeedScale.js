/**
 * @file SpeedScale.js
 * @description 轻量组件：为进度条动态注入紧凑刻度与档位徽章
 */

(function() {
    'use strict';
    
    class SpeedScale {
        constructor() {
            this.initialized = false;
            this.progressContainers = [];
            this.gearLabels = new Map(); // 存储档位标签元素
            this.scaleOverlays = new Map(); // 存储刻度覆盖层
            this.currentBaselines = new Map(); // 存储当前基准值

            // 本地配置（默认开启）
            this.config = this.loadConfig();
            
            // 档位映射表（bps到档位名称）- 调整1G-2G间距
            this.gearMapping = [
                { threshold: 0, label: '0' },
                { threshold: 1000 * 1000, label: '1M' },        // 1 Mbps
                { threshold: 10 * 1000 * 1000, label: '10M' },   // 10 Mbps
                { threshold: 100 * 1000 * 1000, label: '100M' }, // 100 Mbps
                { threshold: 500 * 1000 * 1000, label: '500M' }, // 500 Mbps
                { threshold: 1000 * 1000 * 1000, label: '1G' },  // 1 Gbps
                { threshold: 1500 * 1000 * 1000, label: '1.5G' }, // 1.5 Gbps (新增)
                { threshold: 2000 * 1000 * 1000, label: '2G' }, // 2 Gbps
                { threshold: 3000 * 1000 * 1000, label: '3G' }, // 3 Gbps (新增)
                { threshold: 5000 * 1000 * 1000, label: '5G' }, // 5 Gbps
                { threshold: 10 * 1000 * 1000 * 1000, label: '10G' }, // 10 Gbps
                { threshold: 25 * 1000 * 1000 * 1000, label: '25G' }, // 25 Gbps
                { threshold: 40 * 1000 * 1000 * 1000, label: '40G' }, // 40 Gbps
                { threshold: 100 * 1000 * 1000 * 1000, label: '100G' } // 100 Gbps
            ];
        }
        
        /**
         * 初始化组件
         */
        init() {
            if (this.initialized) return;
            
            console.log('[SpeedScale] 初始化开始...');
            
            // 查找四个进度条容器
            this.progressContainers = [
                'download-progress-container',
                'upload-progress-container', 
                'mobile-download-progress-container',
                'mobile-upload-progress-container'
            ].map(id => document.getElementById(id)).filter(el => el);
            
            if (this.progressContainers.length === 0) {
                console.warn('[SpeedScale] 未找到进度条容器');
                return;
            }
            
            // 设置全局DOM保护
            this.setupGlobalDOMProtection();

            // 为每个容器注入刻度和档位徽章
            this.progressContainers.forEach(container => {
                this.injectScaleAndBadge(container);
            });

            // 监听基准变更事件
            this.setupEventListeners();

            // 应用本地配置
            this.applyConfig();

            this.initialized = true;
            console.log(`[SpeedScale] 初始化完成，处理了 ${this.progressContainers.length} 个进度条`);
            console.log(`[SpeedScale] 当前配置:`, this.config);
        }
        
        /**
         * 设置全局DOM保护机制
         */
        setupGlobalDOMProtection() {
            // 保护速度容器不被破坏性操作影响
            const speedContainerIds = [
                'current-download-speed',
                'current-upload-speed',
                'mobile-download-speed',
                'mobile-upload-speed'
            ];

            speedContainerIds.forEach(containerId => {
                const container = document.getElementById(containerId);
                if (container) {
                    // 拦截innerHTML设置
                    this.protectContainerInnerHTML(container, containerId);
                }
            });
        }

        /**
         * 保护容器的innerHTML不被破坏性设置
         */
        protectContainerInnerHTML(container, containerId) {
            // 检查是否已经保护过
            if (container._speedScaleProtected) return;

            const originalInnerHTMLDescriptor = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');

            Object.defineProperty(container, 'innerHTML', {
                get: originalInnerHTMLDescriptor.get,
                set: function(value) {
                    // 检查是否包含旧格式的metric-unit
                    if (typeof value === 'string' && value.includes('metric-unit')) {
                        console.warn(`[SpeedScale] 拦截对 ${containerId} 的破坏性innerHTML设置:`, value);

                        // 解析值和单位
                        const match = value.match(/^([^<]+)\s*<span[^>]*class="[^"]*metric-unit[^"]*"[^>]*>([^<]+)<\/span>/);
                        if (match) {
                            const numValue = match[1].trim();
                            const unit = match[2].trim();

                            // 使用安全的方式更新
                            const valueEl = container.querySelector('[id$="-value"]');
                            const unitEl = container.querySelector('[id$="-unit"]');

                            if (valueEl && unitEl) {
                                valueEl.textContent = numValue;
                                unitEl.textContent = unit;
                                console.log(`[SpeedScale] 已安全更新 ${containerId}: ${numValue} ${unit}`);
                                return; // 阻止原始设置
                            }
                        }
                    }

                    // 对于其他情况，允许正常设置
                    originalInnerHTMLDescriptor.set.call(this, value);
                },
                configurable: true
            });

            // 标记已保护
            container._speedScaleProtected = true;
            console.log(`[SpeedScale] 已保护容器: ${containerId}`);
        }

        /**
         * 为容器注入刻度和档位徽章
         */
        injectScaleAndBadge(container) {
            const containerId = container.id;

            // 创建刻度覆盖层（仍然绝对定位在进度条容器内）
            const scaleOverlay = document.createElement('div');
            scaleOverlay.className = 'scale-overlay';
            scaleOverlay.setAttribute('data-container', containerId);
            scaleOverlay.setAttribute('aria-hidden', 'true');

            // 添加刻度覆盖层到进度条容器
            container.appendChild(scaleOverlay);
            this.scaleOverlays.set(containerId, scaleOverlay);

            // 为进度条右侧注入档位徽章（行内右侧，不覆盖在条上）
            this.injectGearBadge(containerId);

            // 初始化刻度
            this.updateScale(container);

            // 首屏乐观基准渲染
            this.initOptimisticBaseline(containerId);

            // 监听容器大小变化
            if (window.ResizeObserver) {
                const resizeObserver = new ResizeObserver(() => {
                    this.updateScale(container);
                });
                resizeObserver.observe(container);
            }
        }

        /**
         * 为进度条容器注入档位徽章（置于进度条右侧，非覆盖）
         */
        injectGearBadge(progressContainerId) {
            const progressContainer = document.getElementById(progressContainerId);
            if (!progressContainer) {
                console.warn(`[SpeedScale] 未找到进度条容器: ${progressContainerId}`);
                return;
            }

            // 创建或获取行内包装器：将进度条与徽章排成一行
            let wrapper = progressContainer.parentElement;
            const needWrap = !wrapper || !wrapper.classList.contains('progress-inline-wrapper');
            if (needWrap) {
                wrapper = document.createElement('div');
                wrapper.className = 'progress-inline-wrapper flex items-center';
                // 将包装器插入到原位置，并把进度容器移入包装器
                progressContainer.parentElement.insertBefore(wrapper, progressContainer);
                wrapper.appendChild(progressContainer);
            }

            // 确保进度条容器在一行中占满剩余空间
            progressContainer.classList.add('flex-1');

            // 已存在徽章则跳过
            if (wrapper.querySelector('.gear-badge')) {
                return;
            }

            // 创建档位徽章（右侧行内）
            const gearBadge = document.createElement('span');
            gearBadge.className = 'gear-badge gear-badge--right';
            gearBadge.textContent = '--';
            gearBadge.title = '等待数据...';
            gearBadge.setAttribute('aria-hidden', 'true');
            gearBadge.setAttribute('data-container', progressContainerId);

            if (progressContainerId.includes('download')) {
                gearBadge.classList.add('gear-badge--down');
            } else if (progressContainerId.includes('upload')) {
                gearBadge.classList.add('gear-badge--up');
            }

            // 追加到包装器右侧
            wrapper.appendChild(gearBadge);

            // 存储引用（按进度容器ID索引）
            this.gearLabels.set(progressContainerId, gearBadge);

            // 观察包装器，若徽章被移除则重建
            this.setupGearBadgeObserver(wrapper, progressContainerId);

            console.log(`[SpeedScale] 档位徽章已注入到进度条右侧: ${progressContainerId}`);
        }
        
        /**
         * 设置档位徽章观察器（防御机制）
         */
        setupGearBadgeObserver(container, progressContainerId) {
            if (!window.MutationObserver) return;

            // 防抖计时器
            let reinjectTimer = null;

            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        // 检查档位徽章是否被移除（仅针对进度容器）
                        const gearBadge = container.querySelector('.gear-badge');
                        if (!gearBadge && container.isConnected) {
                            // 档位徽章被移除，延迟重新注入
                            if (reinjectTimer) clearTimeout(reinjectTimer);
                            reinjectTimer = setTimeout(() => {
                                console.warn(`[SpeedScale] 检测到档位徽章被移除，重新注入: ${progressContainerId}`);
                                this.injectGearBadge(progressContainerId);
                            }, 100);
                        }
                    }
                });
            });

            observer.observe(container, {
                childList: true,
                subtree: true
            });

            // 存储observer引用以便后续清理
            if (!this.observers) {
                this.observers = new Map();
            }
            this.observers.set(progressContainerId, observer);
        }

        /**
         * 创建正确的容器结构
         */
        createCorrectStructure(speedContainer, progressContainerId) {
            const isDownload = progressContainerId.includes('download');
            const isMobile = progressContainerId.includes('mobile');

            let valueId, unitId;
            if (isMobile) {
                valueId = isDownload ? 'mobile-download-speed-value' : 'mobile-upload-speed-value';
                unitId = isDownload ? 'mobile-download-speed-unit' : 'mobile-upload-speed-unit';
            } else {
                valueId = isDownload ? 'current-download-speed-value' : 'current-upload-speed-value';
                unitId = isDownload ? 'current-download-speed-unit' : 'current-upload-speed-unit';
            }

            // 创建value元素
            const valueEl = document.createElement('span');
            valueEl.id = valueId;
            valueEl.className = 'text-xl font-medium metric-number tabular-nums';
            valueEl.textContent = '--';

            // 创建unit元素
            const unitEl = document.createElement('span');
            unitEl.id = unitId;
            unitEl.className = 'text-xs opacity-70 ml-1 w-10 text-left';
            unitEl.textContent = '';

            // 添加到容器
            speedContainer.appendChild(valueEl);
            speedContainer.appendChild(unitEl);

            console.log(`[SpeedScale] 已重新创建正确结构: ${speedContainer.id}`);
        }

        /**
         * 首屏乐观基准渲染
         */
        initOptimisticBaseline(containerId) {
            // 获取乐观基准值
            let optimisticBaseline = 100 * 1000 * 1000; // 默认100Mbps

            // 尝试从ProgressBarManager获取fixedBaseline
            if (window.progressBarManager && window.progressBarManager.config) {
                optimisticBaseline = window.progressBarManager.config.fixedBaseline || optimisticBaseline;
            }

            // 立即更新档位标签
            this.updateGearLabel(containerId, optimisticBaseline);

            // 标记为已初始化，避免重复
            const container = document.getElementById(containerId);
            if (container) {
                container.setAttribute('data-speedscale-initialized', 'true');
            }
        }

        /**
         * 更新刻度显示
         */
        updateScale(container) {
            const scaleOverlay = this.scaleOverlays.get(container.id);
            if (!scaleOverlay) return;
            
            const containerWidth = container.offsetWidth;
            const showDetailedScale = containerWidth >= 360;
            
            // 清空现有刻度
            scaleOverlay.innerHTML = '';

            if (showDetailedScale) {
                // 显示 0%, 50%, 100% 刻度
                [0, 50, 100].forEach(percent => {
                    const scaleMark = document.createElement('div');
                    scaleMark.className = 'scale-label';
                    scaleMark.style.left = `${percent}%`;
                    scaleMark.title = `${percent}%`;
                    scaleMark.setAttribute('aria-hidden', 'true');
                    scaleOverlay.appendChild(scaleMark);
                });
                console.log(`[SpeedScale] 详细刻度已添加到 ${container.id} (宽度: ${containerWidth}px)`);
            } else {
                // 仅显示 25%, 50%, 75% 的细微刻度
                [25, 50, 75].forEach(percent => {
                    const scaleMark = document.createElement('div');
                    scaleMark.className = 'scale-label';
                    scaleMark.style.left = `${percent}%`;
                    scaleMark.style.opacity = '0.3';
                    scaleMark.style.height = '50%';
                    scaleMark.style.top = '25%';
                    scaleMark.setAttribute('aria-hidden', 'true');
                    scaleOverlay.appendChild(scaleMark);
                });
                console.log(`[SpeedScale] 简化刻度已添加到 ${container.id} (宽度: ${containerWidth}px)`);
            }
        }
        
        /**
         * 设置事件监听器
         */
        setupEventListeners() {
            // 监听基准变更事件
            document.addEventListener('progress:baseline', (event) => {
                const { key, baseline } = event.detail;
                this.updateBaseline(key, baseline);
            });

            // 监听满格高光事件
            document.addEventListener('progress:fullbar', (event) => {
                const { key, ratio } = event.detail;
                this.handleFullBarHighlight(key, ratio);
            });
        }
        
        /**
         * 处理满格高光效果
         */
        handleFullBarHighlight(key, ratio) {
            // 根据key确定要高亮的容器
            let containerIds = [];
            if (key === 'net' || key === 'download') {
                containerIds.push('download-progress-container', 'mobile-download-progress-container');
            }
            if (key === 'net' || key === 'upload') {
                containerIds.push('upload-progress-container', 'mobile-upload-progress-container');
            }

            // 为对应的进度条容器添加满格高光
            containerIds.forEach(containerId => {
                const container = document.getElementById(containerId);
                if (container) {
                    const progressBar = container.querySelector('.progress-bar');
                    if (progressBar) {
                        // 添加满格高光类
                        progressBar.classList.add('full-bar-glow', 'active');

                        // 更新档位徽章的高亮状态
                        const gearBadge = this.gearLabels.get(containerId);
                        if (gearBadge) {
                            gearBadge.style.background = 'rgba(34, 197, 94, 0.9)'; // 绿色高亮
                            gearBadge.style.color = 'white';
                            gearBadge.style.fontWeight = '700';

                            // 1.5秒后恢复正常样式
                            setTimeout(() => {
                                gearBadge.style.background = '';
                                gearBadge.style.color = '';
                                gearBadge.style.fontWeight = '';
                            }, 1500);
                        }
                    }
                }
            });
        }

        /**
         * 更新基准值和档位标签
         */
        updateBaseline(key, baseline) {
            this.currentBaselines.set(key, baseline);
            
            // 根据key确定要更新的容器
            let containerIds = [];
            if (key === 'net' || key === 'download') {
                containerIds.push('download-progress-container', 'mobile-download-progress-container');
            }
            if (key === 'net' || key === 'upload') {
                containerIds.push('upload-progress-container', 'mobile-upload-progress-container');
            }
            
            // 更新对应的档位标签
            containerIds.forEach(containerId => {
                this.updateGearLabel(containerId, baseline);
            });
        }
        
        /**
         * 更新档位标签
         */
        updateGearLabel(containerId, baseline) {
            const gearLabel = this.gearLabels.get(containerId);
            if (!gearLabel) return;

            const currentGear = this.getGearFromBaseline(baseline);
            const nextGear = this.getNextGear(baseline);
            const oldText = gearLabel.textContent;

            // 检查档位是否发生变化
            const isChanged = oldText !== currentGear.label && oldText !== '--';

            // 更新标签文本
            gearLabel.textContent = currentGear.label;

            // 更新title提示
            if (nextGear) {
                const nextThreshold = this.formatSpeed(nextGear.threshold);
                gearLabel.title = `→ ${nextGear.label} | 阈值 ${nextThreshold}`;
            } else {
                gearLabel.title = `当前档位: ${currentGear.label}`;
            }

            // 如果档位发生变化，添加动画效果
            if (isChanged && !window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                gearLabel.classList.add('is-changing');

                // 200ms后移除动画类
                setTimeout(() => {
                    gearLabel.classList.remove('is-changing');
                }, 200);

                console.log(`[SpeedScale] 档位变更: ${oldText} → ${currentGear.label} (${containerId})`);
            }
        }
        
        /**
         * 根据基准值获取当前档位
         */
        getGearFromBaseline(baseline) {
            for (let i = this.gearMapping.length - 1; i >= 0; i--) {
                if (baseline >= this.gearMapping[i].threshold) {
                    return this.gearMapping[i];
                }
            }
            return this.gearMapping[0];
        }
        
        /**
         * 获取下一档位
         */
        getNextGear(baseline) {
            for (let i = 0; i < this.gearMapping.length; i++) {
                if (this.gearMapping[i].threshold > baseline) {
                    return this.gearMapping[i];
                }
            }
            return null; // 已经是最高档位
        }
        
        /**
         * 格式化速度显示
         */
        formatSpeed(bps) {
            if (bps >= 1000 * 1000 * 1000) {
                return `${(bps / (1000 * 1000 * 1000)).toFixed(1)}G`;
            } else if (bps >= 1000 * 1000) {
                return `${(bps / (1000 * 1000)).toFixed(0)}M`;
            } else if (bps >= 1000) {
                return `${(bps / 1000).toFixed(0)}K`;
            } else {
                return `${bps}`;
            }
        }
        
        /**
         * 手动更新基准值（用于外部调用）
         */
        update(baseline) {
            // 为所有容器更新基准值
            this.progressContainers.forEach(container => {
                this.updateGearLabel(container.id, baseline);
            });
        }
        
        /**
         * 加载本地配置
         */
        loadConfig() {
            const defaultConfig = {
                showScale: true,
                showNextGear: true,
                celebrateFullBar: true
            };

            try {
                const stored = localStorage.getItem('speedScale_config');
                if (stored) {
                    const parsed = JSON.parse(stored);
                    return { ...defaultConfig, ...parsed };
                }
            } catch (e) {
                console.warn('[SpeedScale] 配置加载失败，使用默认配置:', e);
            }

            return defaultConfig;
        }

        /**
         * 保存配置到本地存储
         */
        saveConfig() {
            try {
                localStorage.setItem('speedScale_config', JSON.stringify(this.config));
            } catch (e) {
                console.warn('[SpeedScale] 配置保存失败:', e);
            }
        }

        /**
         * 更新配置项
         */
        updateConfig(key, value) {
            if (this.config.hasOwnProperty(key)) {
                this.config[key] = value;
                this.saveConfig();
                this.applyConfig();

                // 如果是celebrateFullBar配置，同步到ProgressBarManager
                if (key === 'celebrateFullBar' && window.progressBarManager) {
                    window.progressBarManager.updateCelebrateFullBarConfig();
                }

                console.log(`[SpeedScale] 配置已更新: ${key} = ${value}`);
            }
        }

        /**
         * 应用配置到UI
         */
        applyConfig() {
            // 应用刻度显示配置
            this.scaleOverlays.forEach(overlay => {
                overlay.style.display = this.config.showScale ? '' : 'none';
            });

            // 应用档位徽章配置
            this.gearLabels.forEach(label => {
                if (this.config.showNextGear) {
                    label.style.display = '';
                } else {
                    label.style.display = 'none';
                }
            });

            // celebrateFullBar配置会传递给ProgressBarManager
        }

        /**
         * 获取配置（供外部调用）
         */
        getConfig() {
            return { ...this.config };
        }

        /**
         * 销毁组件
         */
        destroy() {
            // 移除注入的元素
            this.scaleOverlays.forEach(overlay => {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            });

            this.gearLabels.forEach(label => {
                if (label.parentNode) {
                    label.parentNode.removeChild(label);
                }
            });

            // 清理MutationObserver
            if (this.observers) {
                this.observers.forEach(observer => {
                    observer.disconnect();
                });
                this.observers.clear();
            }

            // 清空引用
            this.scaleOverlays.clear();
            this.gearLabels.clear();
            this.currentBaselines.clear();

            this.initialized = false;
        }
    }
    
    // 导出到全局
    window.SpeedScale = new SpeedScale();

    // 暴露配置管理方法到全局，方便用户在控制台中使用
    window.SpeedScaleConfig = {
        get: () => window.SpeedScale.getConfig(),
        set: (key, value) => window.SpeedScale.updateConfig(key, value),
        reset: () => {
            localStorage.removeItem('speedScale_config');
            window.SpeedScale.config = window.SpeedScale.loadConfig();
            window.SpeedScale.applyConfig();
            if (window.progressBarManager) {
                window.progressBarManager.updateCelebrateFullBarConfig();
            }
            console.log('[SpeedScale] 配置已重置为默认值');
        }
    };

    // 暴露测试方法到全局，方便调试
    window.SpeedScaleTest = {
        // 测试满格高光效果
        testFullBarGlow: (elementId = 'download-speed-progress') => {
            const element = document.getElementById(elementId);
            if (element) {
                element.classList.add('full-bar-glow', 'active');
                console.log(`[Test] 满格高光已添加到 ${elementId}`);
                setTimeout(() => {
                    element.classList.remove('active');
                    setTimeout(() => element.classList.remove('full-bar-glow'), 300);
                    console.log(`[Test] 满格高光已移除`);
                }, 3000);
            }
        },

        // 测试进度高亮效果
        testProgressHighlight: (elementId = 'download-speed-progress') => {
            const element = document.getElementById(elementId);
            if (element) {
                element.classList.add('progress-highlight');
                console.log(`[Test] 进度高亮已添加到 ${elementId}`);
                setTimeout(() => {
                    element.classList.remove('progress-highlight');
                    console.log(`[Test] 进度高亮已移除`);
                }, 600);
            }
        },

        // 模拟进度变化
        simulateProgress: (elementId = 'download-speed-progress', targetPercent = 95) => {
            const element = document.getElementById(elementId);
            if (element && window.progressBarManager) {
                // 模拟进度变化
                element.style.width = `${targetPercent}%`;
                element.dataset.lastPercent = targetPercent;

                // 手动触发高光效果
                if (targetPercent >= 95) {
                    window.progressBarManager.handleFullBarGlow(element, targetPercent, { celebrateFullBar: true });
                }

                console.log(`[Test] 模拟进度设置为 ${targetPercent}%`);
            }
        },

        // 检查刻度和徽章状态
        checkScaleAndBadges: () => {
            const containers = ['download-progress-container', 'upload-progress-container',
                              'mobile-download-progress-container', 'mobile-upload-progress-container'];

            const speedContainers = ['current-download-speed', 'current-upload-speed',
                                   'mobile-download-speed', 'mobile-upload-speed'];

            containers.forEach((containerId, index) => {
                const container = document.getElementById(containerId);
                const speedContainer = document.getElementById(speedContainers[index]);

                if (container) {
                    const scaleOverlay = container.querySelector('.scale-overlay');
                    const scaleLabels = container.querySelectorAll('.scale-label');

                    console.log(`[Test] ${containerId}:`);
                    console.log(`  - 刻度覆盖层: ${scaleOverlay ? '✓' : '✗'}`);
                    console.log(`  - 刻度标签: ${scaleLabels.length} 个`);
                    console.log(`  - 容器宽度: ${container.offsetWidth}px`);
                }

                if (speedContainer) {
                    const gearBadge = speedContainer.querySelector('.gear-badge');
                    console.log(`[Test] ${speedContainers[index]}:`);
                    console.log(`  - 档位徽章: ${gearBadge ? '✓' : '✗'} ${gearBadge ? `(${gearBadge.textContent})` : ''}`);
                    console.log(`  - 徽章类名: ${gearBadge ? gearBadge.className : 'N/A'}`);
                }
            });
        },

        // 测试档位变更动画
        testGearChange: (containerId = 'download-progress-container') => {
            const gearBadge = window.SpeedScale.gearLabels.get(containerId);
            if (gearBadge) {
                const originalText = gearBadge.textContent;
                gearBadge.textContent = '5G';
                gearBadge.classList.add('is-changing');

                setTimeout(() => {
                    gearBadge.classList.remove('is-changing');
                    gearBadge.textContent = originalText;
                    console.log('[Test] 档位变更动画测试完成');
                }, 1000);

                console.log('[Test] 档位变更动画已触发');
            }
        },

        // 测试DOM破坏恢复
        testDOMDestruction: () => {
            console.log('[Test] 开始DOM破坏测试...');

            // 模拟updateDesktopSpeedSimplified的破坏性操作
            const downloadContainer = document.getElementById('current-download-speed');
            const uploadContainer = document.getElementById('current-upload-speed');

            if (downloadContainer) {
                console.log('[Test] 破坏下载速度容器...');
                downloadContainer.innerHTML = `
                    <span id="current-download-speed-value" class="text-xl font-medium metric-number tabular-nums">测试</span>
                    <span id="current-download-speed-unit" class="text-xs opacity-70 ml-1 w-10 text-left">Mbps</span>
                `;
            }

            if (uploadContainer) {
                console.log('[Test] 破坏上传速度容器...');
                uploadContainer.innerHTML = `
                    <span id="current-upload-speed-value" class="text-xl font-medium metric-number tabular-nums">测试</span>
                    <span id="current-upload-speed-unit" class="text-xs opacity-70 ml-1 w-10 text-left">Mbps</span>
                `;
            }

            // 等待MutationObserver触发
            setTimeout(() => {
                console.log('[Test] 检查恢复结果...');
                SpeedScaleTest.checkScaleAndBadges();
            }, 200);
        },

        // 强制重新初始化
        reinitialize: () => {
            if (window.SpeedScale) {
                window.SpeedScale.destroy();
                window.SpeedScale.initialized = false;
                window.SpeedScale.init();
                console.log('[Test] SpeedScale 已重新初始化');
            }
        }
    };
    
})();
