/**
 * Network Quality Inline Filters Component
 * 网络质量页面一行集成筛选器组件
 */

class NetworkQualityInlineFilters {
    constructor(options = {}) {
        this.options = {
            enableGroup: true,
            enableRegion: true,
            enableStatus: true,
            ...options
        };

        // 状态管理
        this.state = {
            activeFilters: {
                group: null,
                region: null,
                status: null
            },
            groupData: new Map(),
            regionData: new Map(),
            openDropdown: null
        };

        // 回调函数
        this.callbacks = {
            onFilterChange: null,
            ...options.callbacks
        };

        // DOM 元素
        this.elements = {};

        // 初始化
        this.init();
    }

    /**
     * 初始化组件
     */
    init() {
        this.cacheElements();
        this.bindEvents();
        this.loadInitialData();
    }

    /**
     * 缓存DOM元素
     */
    cacheElements() {
        this.elements = {
            // 分组筛选器
            groupFilter: document.querySelector('#nq-group-filter'),
            groupBtn: document.querySelector('#nq-group-filter .nq-filter-btn'),
            groupText: document.querySelector('#nq-group-filter .nq-group-text'),
            groupDropdown: document.querySelector('#nq-group-filter .nq-dropdown-menu'),
            groupDropdownContent: document.querySelector('#nq-group-filter .nq-dropdown-menu > div'),

            // 地区筛选器
            regionFilter: document.querySelector('#nq-region-filter'),
            regionBtn: document.querySelector('#nq-region-filter .nq-filter-btn'),
            regionText: document.querySelector('#nq-region-filter .nq-region-text'),
            regionDropdown: document.querySelector('#nq-region-filter .nq-dropdown-menu'),
            regionDropdownContent: document.querySelector('#nq-region-filter .nq-dropdown-menu > div'),

            // 状态筛选器
            statusButtons: document.querySelectorAll('.nq-status-btn')
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 分组筛选器事件
        if (this.elements.groupBtn) {
            this.elements.groupBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDropdown('group');
            });
        }

        // 地区筛选器事件
        if (this.elements.regionBtn) {
            this.elements.regionBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDropdown('region');
            });
        }

        // 状态筛选器事件
        this.elements.statusButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const status = btn.dataset.status;
                this.selectStatus(status);
            });
        });

        // 点击外部关闭下拉菜单
        document.addEventListener('click', () => {
            this.closeAllDropdowns();
        });

        // 下拉菜单项点击事件（事件委托）
        if (this.elements.groupDropdown) {
            this.elements.groupDropdown.addEventListener('click', (e) => {
                const item = e.target.closest('.nq-dropdown-item');
                if (item) {
                    e.stopPropagation();
                    const group = item.dataset.group;
                    this.selectGroup(group);
                }
            });
        }

        if (this.elements.regionDropdown) {
            this.elements.regionDropdown.addEventListener('click', (e) => {
                const item = e.target.closest('.nq-dropdown-item');
                if (item) {
                    e.stopPropagation();
                    const region = item.dataset.region;
                    this.selectRegion(region);
                }
            });
        }
    }

    /**
     * 切换下拉菜单
     */
    toggleDropdown(type) {
        if (this.state.openDropdown === type) {
            this.closeAllDropdowns();
        } else {
            this.closeAllDropdowns();
            this.openDropdown(type);
        }
    }

    /**
     * 打开下拉菜单
     */
    openDropdown(type) {
        const dropdown = type === 'group' ? this.elements.groupDropdown : this.elements.regionDropdown;
        if (dropdown) {
            dropdown.classList.add('show');
            this.state.openDropdown = type;
        }
    }

    /**
     * 关闭所有下拉菜单
     */
    closeAllDropdowns() {
        [this.elements.groupDropdown, this.elements.regionDropdown].forEach(dropdown => {
            if (dropdown) {
                dropdown.classList.remove('show');
            }
        });
        this.state.openDropdown = null;
    }

    /**
     * 选择分组
     */
    selectGroup(group) {
        this.state.activeFilters.group = group === 'all' ? null : group;
        
        // 更新UI
        this.updateGroupUI(group);
        this.closeAllDropdowns();
        
        // 触发回调
        this.triggerFilterChange();
    }

    /**
     * 选择地区
     */
    selectRegion(region) {
        this.state.activeFilters.region = region === 'all' ? null : region;
        
        // 更新UI
        this.updateRegionUI(region);
        this.closeAllDropdowns();
        
        // 触发回调
        this.triggerFilterChange();
    }

    /**
     * 选择状态
     */
    selectStatus(status) {
        this.state.activeFilters.status = status === 'all' ? null : status;
        
        // 更新UI
        this.updateStatusUI(status);
        
        // 触发回调
        this.triggerFilterChange();
    }

    /**
     * 更新分组UI
     */
    updateGroupUI(group) {
        if (this.elements.groupText) {
            let groupName = '全部分组';
            if (group && group !== 'all') {
                // 优先使用 maps.groups 中的友好名称
                const mapped = this.state.maps && this.state.maps.groups ? this.state.maps.groups[group] : null;
                groupName = mapped || this.getGroupName(group) || group;
            }
            this.elements.groupText.textContent = groupName;
        }

        // 更新下拉菜单项状态
        if (this.elements.groupDropdownContent) {
            const items = this.elements.groupDropdownContent.querySelectorAll('.nq-dropdown-item');
            items.forEach(item => {
                if (item.dataset.group === group) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        }
    }

    /**
     * 更新地区UI
     */
    updateRegionUI(region) {
        if (this.elements.regionText) {
            let regionName = '全部地区';
            if (region && region !== 'all') {
                const code = String(region).toUpperCase();
                const fromMap = this.state.maps && this.state.maps.regions && this.state.maps.regions[code] ? this.state.maps.regions[code].name : null;
                if (fromMap) {
                    regionName = fromMap;
                } else {
                    // 尝试从 facets 中匹配名称
                    const pickName = (list) => {
                        if (!list) return null;
                        const hit = list.find(r => String(r.code).toUpperCase() === code);
                        return hit ? (hit.name || null) : null;
                    };
                    regionName = pickName(this.state.facetsAll && this.state.facetsAll.regions) || pickName(this.state.facets && this.state.facets.regions) || this.getRegionName(code) || code;
                }
            }
            this.elements.regionText.textContent = regionName;
        }

        // 更新下拉菜单项状态
        if (this.elements.regionDropdownContent) {
            const items = this.elements.regionDropdownContent.querySelectorAll('.nq-dropdown-item');
            items.forEach(item => {
                if (item.dataset.region === region) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        }
    }

    /**
     * 更新状态UI
     */
    updateStatusUI(status) {
        this.elements.statusButtons.forEach(btn => {
            if (btn.dataset.status === status) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
    }

    /**
     * 触发筛选变化回调
     */
    triggerFilterChange() {
        if (this.callbacks.onFilterChange) {
            this.callbacks.onFilterChange({
                filters: { ...this.state.activeFilters },
                source: 'inline-filters'
            });
        }
    }

    /**
     * 加载初始数据
     */
    loadInitialData() {
        setTimeout(() => {
            this.loadGroupData();
            this.loadRegionData();
        }, 300);
    }

    /**
     * 加载分组数据
     */
    loadGroupData() {
        const nodes = this.getAllNodes();
        const groupMap = new Map();
        
        nodes.forEach(node => {
            const group = node.group || 'default';
            const count = groupMap.get(group) || 0;
            groupMap.set(group, count + 1);
        });

        this.state.groupData = groupMap;
        this.updateGroupDropdown();
    }

    /**
     * 加载地区数据
     */
    loadRegionData() {
        const nodes = this.getAllNodes();
        const regionMap = new Map();

        // 更新节点卡片的地区属性，并统计地区数据
        nodes.forEach(node => {
            const region = node.region || 'unknown';

            // 更新节点卡片的data-region属性
            if (node.element && node.element.dataset.region !== region) {
                node.element.dataset.region = region;
            }

            const count = regionMap.get(region) || 0;
            regionMap.set(region, count + 1);
        });

        this.state.regionData = regionMap;
        this.updateRegionDropdown();
    }

    /**
     * 更新分组下拉菜单
     */
    updateGroupDropdown() {
        if (!this.elements.groupDropdownContent) return;

        // 优先使用 facetsAll（全量）用于完整分组列表与“全部分组计数”
        let totalCount, groups;
        if (this.state.facetsAll && this.state.facetsAll.groups) {
            totalCount = this.state.facetsAll.totalCount || 0;
            groups = this.state.facetsAll.groups.map(g => [g.id, g.count, g.name]);
        } else if (this.state.facets && this.state.facets.groups) {
            totalCount = this.state.facets.totalCount || 0;
            groups = this.state.facets.groups.map(g => [g.id, g.count, g.name]);
        } else {
            totalCount = Array.from(this.state.groupData.values()).reduce((sum, count) => sum + count, 0);
            groups = Array.from(this.state.groupData.entries()).sort((a, b) => b[1] - a[1]).map(([id, count]) => [id, count, this.getGroupName(id)]);
        }

        let html = `
            <div class="nq-dropdown-item active flex items-center justify-between px-3 py-2 hover:bg-slate-50 dark:hover:bg-slate-600 cursor-pointer transition-colors" data-group="all">
                <span class="text-sm text-slate-800 dark:text-slate-200">全部分组</span>
                <span class="nq-item-count text-xs text-slate-500 dark:text-slate-400">${totalCount}</span>
            </div>
        `;

        groups.forEach(([group, count, name]) => {
            const fromMap = this.state.maps && this.state.maps.groups ? this.state.maps.groups[group] : null;
            const groupName = fromMap || name || this.getGroupName(group);
            html += `
                <div class="nq-dropdown-item flex items-center justify-between px-3 py-2 hover:bg-slate-50 dark:hover:bg-slate-600 cursor-pointer transition-colors" data-group="${group}">
                    <span class="text-sm text-slate-800 dark:text-slate-200">${groupName}</span>
                    <span class="nq-item-count text-xs text-slate-500 dark:text-slate-400">${count}</span>
                </div>
            `;
        });

        this.elements.groupDropdownContent.innerHTML = html;
    }

    /**
     * 更新地区下拉菜单
     */
    updateRegionDropdown() {
        if (!this.elements.regionDropdownContent) return;

        // 优先使用 facetsAll（忽略 groupId 过滤）
        let totalCount, regions;
        if (this.state.facetsAll && this.state.facetsAll.regions) {
            regions = this.state.facetsAll.regions.map(r => [r.code, r.count, r.name]);
            totalCount = regions.reduce((sum, x) => sum + (x[1] || 0), 0);
        } else if (this.state.facets && this.state.facets.regions) {
            regions = this.state.facets.regions.map(r => [r.code, r.count, r.name]);
            totalCount = regions.reduce((sum, x) => sum + (x[1] || 0), 0);
        } else {
            totalCount = Array.from(this.state.regionData.values()).reduce((sum, count) => sum + count, 0);
            regions = Array.from(this.state.regionData.entries()).sort((a, b) => b[1] - a[1]).map(([code, count]) => [code, count, this.getRegionName(code)]);
        }

        let html = `
            <div class="nq-dropdown-item active flex items-center justify-between px-3 py-2 hover:bg-slate-50 dark:hover:bg-slate-600 cursor-pointer transition-colors" data-region="all">
                <span class="text-sm text-slate-800 dark:text-slate-200">全部地区</span>
                <span class="nq-item-count text-xs text-slate-500 dark:text-slate-400">${totalCount}</span>
            </div>
        `;

        regions.forEach(([region, count, name]) => {
            if (region !== 'unknown' && region !== 'UNKNOWN') {
                const code = String(region).toUpperCase();
                const mapped = this.state.maps && this.state.maps.regions && this.state.maps.regions[code] ? this.state.maps.regions[code].name : null;
                const regionName = mapped || name || this.getRegionName(code);
                html += `
                    <div class="nq-dropdown-item flex items-center justify-between px-3 py-2 hover:bg-slate-50 dark:hover:bg-slate-600 cursor-pointer transition-colors" data-region="${code}">
                        <span class="text-sm text-slate-800 dark:text-slate-200">${regionName}</span>
                        <span class="nq-item-count text-xs text-slate-500 dark:text-slate-400">${count}</span>
                    </div>
                `;
            }
        });

        this.elements.regionDropdownContent.innerHTML = html;
    }

    /**
     * 获取分组名称
     */
    getGroupName(group) {
        const groupNames = {
            'default': '默认分组',
            '默认分组': '默认分组',
            'monitor': '监控节点',
            'cdn': 'CDN节点',
            'server': '服务器',
            'network': '网络节点',
            '主力': '主力',
            '腾讯': '腾讯',
            '阿里云1': '阿里云1',
            '阿里云': '阿里云'
        };
        return groupNames[group] || group;
    }

    /**
     * 获取地区名称
     */
    getRegionName(region) {
        const code = String(region || '').toUpperCase();
        const regionNames = {
            'CN': '中国',
            'US': '美国',
            'JP': '日本',
            'KR': '韩国',
            'SG': '新加坡',
            'HK': '香港',
            'TW': '台湾',
            'DE': '德国',
            'UK': '英国',
            'FR': '法国',
            'CA': '加拿大',
            'AU': '澳大利亚',
            'UNKNOWN': '未知地区'
        };
        return regionNames[code] || region;
    }

    /**
     * 获取所有节点
     */
    getAllNodes() {
        const nodeCards = document.querySelectorAll('.network-status-card[data-node-id]');
        return Array.from(nodeCards).map(card => {
            const nodeId = card.dataset.nodeId;
            const nodeName = card.querySelector('h3')?.textContent?.trim() || '';
            let region = card.dataset.region || 'unknown';
            const group = card.dataset.group || 'default';

            // 如果地区是"未知地区"，尝试从节点名称中提取地区信息
            if (region === '未知地区' || region === 'unknown') {
                region = this.extractRegionFromName(nodeName);
            }

            return {
                id: nodeId,
                name: nodeName,
                region: region,
                group: group,
                element: card
            };
        });
    }

    /**
     * 从节点名称中提取地区信息
     */
    extractRegionFromName(nodeName) {
        if (!nodeName) return 'unknown';

        const regionPatterns = {
            'cn': ['CN', 'China', '中国', '北京', '上海', '深圳', '杭州', '广州', '腾讯', '阿里', 'aliyun'],
            'us': ['US', 'USA', 'America', '美国', '洛杉矶', '纽约', '芝加哥', 'akile', 'ak_us', 'ak-us'],
            'jp': ['JP', 'Japan', '日本', '东京', 'Tokyo', 'jp'],
            'hk': ['HK', 'Hong Kong', '香港', 'hkg'],
            'sg': ['SG', 'Singapore', '新加坡', 'sg'],
            'de': ['DE', 'Germany', '德国', '法兰克福'],
            'uk': ['UK', 'Britain', '英国', '伦敦', 'uklite'],
            'ca': ['CA', 'Canada', '加拿大'],
            'au': ['AU', 'Australia', '澳大利亚', '悉尼'],
            'kr': ['KR', 'Korea', '韩国', '首尔', 'kr'],
            'fr': ['FR', 'France', '法国', '巴黎'],
            'tw': ['TW', 'Taiwan', '台湾', '台北']
        };

        const lowerName = nodeName.toLowerCase();

        for (const [regionCode, patterns] of Object.entries(regionPatterns)) {
            if (patterns.some(pattern => lowerName.includes(pattern.toLowerCase()))) {
                return regionCode;
            }
        }

        return 'unknown';
    }

    /**
     * 清除所有筛选
     */
    clearAllFilters() {
        this.selectGroup('all');
        this.selectRegion('all');
        this.selectStatus('all');
    }

    /**
     * 获取当前筛选状态
     */
    getActiveFilters() {
        return { ...this.state.activeFilters };
    }

    /**
     * 设置筛选状态（外部调用）
     */
    setFilters(filters) {
        if (filters.group !== undefined) {
            this.selectGroup(filters.group || 'all');
        }
        if (filters.region !== undefined) {
            this.selectRegion(filters.region || 'all');
        }
        if (filters.status !== undefined) {
            this.selectStatus(filters.status || 'all');
        }
    }

    /**
     * 更新数据（外部调用）
     */
    updateData() {
        this.loadGroupData();
        this.loadRegionData();
    }

    /**
     * 更新 facets 数据（使用服务端提供的真实计数）
     */
    updateFacets(facets) {
        if (!facets) return;
        // 兼容旧签名：直接传入filtered facets 对象
        if (facets.filtered || facets.all || facets.maps) {
            this.state.facets = facets.filtered || this.state.facets;
            this.state.facetsAll = facets.all || this.state.facetsAll;
            this.state.maps = facets.maps || this.state.maps || null;
        } else {
            this.state.facets = facets;
        }
        // 使用现有的方法刷新下拉
        this.updateGroupDropdown();
        this.updateRegionDropdown();
    }
}

// 导出到全局
window.NetworkQualityInlineFilters = NetworkQualityInlineFilters;
