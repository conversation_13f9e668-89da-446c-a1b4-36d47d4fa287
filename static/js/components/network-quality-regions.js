/**
 * Network Quality Regions Component - Tailwind CSS Version
 * 网络质量页面地区分布组件
 */

class NetworkQualityRegions {
    constructor(options = {}) {
        this.options = {
            container: '#nq-regions-container',
            showRegionStats: true,
            enableRegionFilter: true,
            ...options
        };

        // 状态管理
        this.state = {
            regionData: new Map(),
            selectedRegion: null,
            totalNodes: 0
        };

        // 回调函数
        this.callbacks = {
            onRegionSelect: null,
            ...options.callbacks
        };

        // DOM 元素
        this.elements = {};

        // 初始化
        this.init();
    }

    /**
     * 初始化地区组件
     */
    init() {
        this.createElements();
        this.bindEvents();
        this.loadRegionData();
        console.log('[NetworkQualityRegions] 地区组件初始化完成');
    }

    /**
     * 创建DOM元素
     */
    createElements() {
        const container = document.querySelector(this.options.container);
        if (!container) {
            console.error('[NetworkQualityRegions] 容器元素未找到:', this.options.container);
            return;
        }

        container.innerHTML = `
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 flex items-center gap-2">
                        <i class="ti ti-world text-sm"></i>
                        地区分布
                    </h4>
                    <button class="nq-region-toggle text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors" 
                            type="button">
                        <span class="nq-toggle-text">展开</span>
                        <i class="ti ti-chevron-down text-sm ml-1"></i>
                    </button>
                </div>
                
                <div class="nq-regions-content hidden">
                    <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-2" id="nq-regions-grid">
                        <!-- 地区项目将在这里动态生成 -->
                    </div>
                </div>
            </div>
        `;

        // 缓存DOM元素
        this.elements = {
            container: container,
            toggle: container.querySelector('.nq-region-toggle'),
            toggleText: container.querySelector('.nq-toggle-text'),
            toggleIcon: container.querySelector('.nq-region-toggle .ti'),
            content: container.querySelector('.nq-regions-content'),
            grid: container.querySelector('#nq-regions-grid')
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 切换显示/隐藏
        this.elements.toggle.addEventListener('click', () => {
            this.toggleContent();
        });

        // 地区项目点击事件
        this.elements.grid.addEventListener('click', (e) => {
            const regionItem = e.target.closest('.nq-region-item');
            if (regionItem) {
                const regionCode = regionItem.dataset.region;
                this.selectRegion(regionCode);
            }
        });
    }

    /**
     * 切换内容显示
     */
    toggleContent() {
        const isHidden = this.elements.content.classList.contains('hidden');
        
        if (isHidden) {
            this.elements.content.classList.remove('hidden');
            this.elements.toggleText.textContent = '收起';
            // 使用Tabler Icons替换Material Icons
            this.elements.toggleIcon.className = 'ti ti-chevron-up';
        } else {
            this.elements.content.classList.add('hidden');
            this.elements.toggleText.textContent = '展开';
            // 使用Tabler Icons替换Material Icons
            this.elements.toggleIcon.className = 'ti ti-chevron-down';
        }
    }

    /**
     * 选择地区
     */
    selectRegion(regionCode) {
        // 更新选中状态
        const items = this.elements.grid.querySelectorAll('.nq-region-item');
        items.forEach(item => {
            if (item.dataset.region === regionCode) {
                if (item.classList.contains('active')) {
                    // 如果已选中，则取消选择
                    item.classList.remove('active', 'ring-2', 'ring-blue-500', 'bg-blue-50', 'dark:bg-blue-900/30');
                    this.state.selectedRegion = null;
                    regionCode = null;
                } else {
                    // 选中该地区
                    item.classList.add('active', 'ring-2', 'ring-blue-500', 'bg-blue-50', 'dark:bg-blue-900/30');
                    this.state.selectedRegion = regionCode;
                }
            } else {
                item.classList.remove('active', 'ring-2', 'ring-blue-500', 'bg-blue-50', 'dark:bg-blue-900/30');
            }
        });

        // 触发回调
        if (this.callbacks.onRegionSelect) {
            this.callbacks.onRegionSelect(regionCode);
        }

        console.log('[NetworkQualityRegions] 地区选择:', regionCode);
    }

    /**
     * 加载地区数据
     */
    loadRegionData() {
        const nodes = this.getAllNodes();
        const regionMap = new Map();
        
        nodes.forEach(node => {
            const region = node.region || 'unknown';
            const count = regionMap.get(region) || 0;
            regionMap.set(region, count + 1);
        });

        this.state.regionData = regionMap;
        this.state.totalNodes = nodes.length;
        this.renderRegions();
    }

    /**
     * 获取所有节点
     */
    getAllNodes() {
        const nodeCards = document.querySelectorAll('.network-status-card[data-node-id]');
        return Array.from(nodeCards).map(card => {
            const nodeId = card.dataset.nodeId;
            const nodeName = card.querySelector('h4')?.textContent?.trim() || '';
            const region = card.dataset.region || this.extractRegionFromName(nodeName);
            
            return {
                id: nodeId,
                name: nodeName,
                region: region,
                element: card
            };
        });
    }

    /**
     * 从节点名称中提取地区信息
     */
    extractRegionFromName(nodeName) {
        if (!nodeName) return 'unknown';
        
        const regionPatterns = {
            '美国': ['US', 'USA', 'America', '美国', '洛杉矶', '纽约', '芝加哥'],
            '中国': ['CN', 'China', '中国', '北京', '上海', '深圳', '杭州', '广州'],
            '日本': ['JP', 'Japan', '日本', '东京', 'Tokyo'],
            '香港': ['HK', 'Hong Kong', '香港'],
            '新加坡': ['SG', 'Singapore', '新加坡'],
            '德国': ['DE', 'Germany', '德国', '法兰克福'],
            '英国': ['UK', 'Britain', '英国', '伦敦'],
            '加拿大': ['CA', 'Canada', '加拿大'],
            '澳大利亚': ['AU', 'Australia', '澳大利亚', '悉尼'],
            '韩国': ['KR', 'Korea', '韩国', '首尔']
        };

        for (const [region, patterns] of Object.entries(regionPatterns)) {
            if (patterns.some(pattern => nodeName.includes(pattern))) {
                return region;
            }
        }

        return 'unknown';
    }

    /**
     * 渲染地区列表
     */
    renderRegions() {
        if (this.state.regionData.size === 0) {
            this.elements.grid.innerHTML = `
                <div class="col-span-full text-center py-4 text-sm text-slate-500 dark:text-slate-400">
                    暂无地区数据
                </div>
            `;
            return;
        }

        // 按节点数量排序
        const sortedRegions = Array.from(this.state.regionData.entries())
            .sort((a, b) => b[1] - a[1]);

        const html = sortedRegions.map(([region, count]) => {
            const percentage = ((count / this.state.totalNodes) * 100).toFixed(1);
            const regionInfo = this.getRegionInfo(region);
            
            return `
                <div class="nq-region-item" 
                     data-region="${region}"
                     title="${regionInfo.name} - ${count} 个节点 (${percentage}%)">
                <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center gap-2 min-w-0">
                            <span class="text-lg">${regionInfo.flag}</span>
                            <span class="text-sm font-medium text-slate-800 dark:text-slate-200 truncate">${regionInfo.name}</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between text-xs">
                        <span class="text-slate-600 dark:text-slate-400">${count} 节点</span>
                        <span class="text-slate-500 dark:text-slate-500">${percentage}%</span>
                    </div>
                    <div class="mt-2 h-1 bg-slate-100 dark:bg-slate-600 rounded-full overflow-hidden">
                        <div class="h-full bg-blue-500 transition-all duration-300" style="width: ${percentage}%"></div>
                    </div>
                </div>
            `;
        }).join('');

        this.elements.grid.innerHTML = html;
    }

    /**
     * 获取地区信息
     */
    getRegionInfo(region) {
        const code = String(region || '').toUpperCase();
        const maps = (window.networkQualityFilterManager && window.networkQualityFilterManager.state && window.networkQualityFilterManager.state.maps) ? window.networkQualityFilterManager.state.maps : null;
        let name = '未知地区';
        let flagHtml = '<i class="ti ti-world" style="font-size: 14px;"></i>';

        if (maps && maps.regions && maps.regions[code]) {
            name = maps.regions[code].name || name;
            const f = maps.regions[code].flag || '';
            if (f) flagHtml = `<img src="${f}" alt="${code}" class="h-4 w-6 object-cover rounded-sm" loading="lazy">`;
        } else {
            // 兜底：常见国家中文名
            const fallback = {
                'CN': '中国', 'US': '美国', 'JP': '日本', 'KR': '韩国', 'SG': '新加坡', 'HK': '香港', 'TW': '台湾',
                'GB': '英国', 'UK': '英国', 'DE': '德国', 'FR': '法国', 'RU': '俄罗斯', 'CA': '加拿大', 'AU': '澳大利亚',
                'LO': '本地网络', 'OT': '其他地区', 'UNKNOWN': '未知地区'
            };
            name = fallback[code] || `未知(${code})`;
            if (code === 'LO') flagHtml = '<i class="ti ti-home" style="font-size: 14px;"></i>';
            else if (code === 'OT') flagHtml = '<i class="ti ti-world" style="font-size: 14px;"></i>';
            else if (code !== 'UNKNOWN') {
                const flagCode = code === 'UK' ? 'GB' : code;
                flagHtml = `<img src="/img/flags/${flagCode}.SVG?v=1" alt="${code}" class="h-4 w-6 object-cover rounded-sm" loading="lazy">`;
            }
        }

        return { name, flag: flagHtml };
    }

    /**
     * 更新地区数据
     */
    updateData() {
        this.loadRegionData();
    }

    /**
     * 清除地区选择
     */
    clearSelection() {
        this.selectRegion(null);
    }

    /**
     * 获取当前状态
     */
    getState() {
        return {
            selectedRegion: this.state.selectedRegion,
            regionData: Array.from(this.state.regionData.entries()),
            totalNodes: this.state.totalNodes
        };
    }

    /**
     * 销毁地区组件
     */
    destroy() {
        this.elements = {};
        this.state = {};
        this.callbacks = {};
        console.log('[NetworkQualityRegions] 地区组件已销毁');
    }
}

// 导出组件
window.NetworkQualityRegions = NetworkQualityRegions;
