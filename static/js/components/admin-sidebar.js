/**
 * AdminSidebar 组件
 * @description 管理后台侧边栏组件，提供菜单管理、权限控制、搜索等功能
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2025-01-31
 */

class AdminSidebar {
    constructor(options = {}) {
        // 默认配置
        this.config = {
            sidebarId: 'admin-sidebar',
            toggleBtnId: 'sidebar-toggle',
            mobileToggleId: 'mobile-toggle-sidebar',
            closeBtnId: 'close-sidebar',
            overlayId: 'sidebar-overlay',
            searchEnabled: true,
            searchPlaceholder: '搜索菜单...',
            searchHotkey: 'ctrl+k,cmd+k',
            animationDuration: 300,
            localStorageKey: 'admin-sidebar-state',
            wsUrl: null, // WebSocket URL for real-time updates
            ...options
        };

        // 状态管理
        this.state = {
            collapsed: false,
            mobileOpen: false,
            searchActive: false,
            searchQuery: '',
            activeMenuId: null,
            permissions: {},
            features: 0,
            favorites: [],
            recentItems: [],
            menuOrder: [],
            searchHistory: []
        };

        // 事件发射器
        this.events = new EventTarget();

        // 元素缓存
        this.elements = {};

        // 初始化
        this.init();
    }

    /**
     * 初始化组件
     */
    init() {
        // 缓存DOM元素
        this.cacheElements();

        // 加载保存的状态
        this.loadState();

        // 初始化菜单
        this.initializeMenu();

        // 添加搜索功能
        if (this.config.searchEnabled) {
            this.initializeSearch();
        }

        // 绑定事件
        this.bindEvents();

        // 页面切换动画控制
        this.initPageTransitionControl();

        // 初始化WebSocket连接
        if (this.config.wsUrl) {
            this.initializeWebSocket();
        }

        // 设置初始状态
        this.applyState();

        // 触发初始化完成事件
        this.emit('initialized', { sidebar: this });
    }

    /**
     * 缓存DOM元素
     */
    cacheElements() {
        this.elements = {
            sidebar: document.getElementById(this.config.sidebarId),
            toggleBtn: document.getElementById(this.config.toggleBtnId),
            mobileToggle: document.getElementById(this.config.mobileToggleId),
            closeBtn: document.getElementById(this.config.closeBtnId),
            overlay: document.getElementById(this.config.overlayId),
            nav: document.querySelector(`#${this.config.sidebarId} nav`),
            menuList: document.querySelector(`#${this.config.sidebarId} nav ul`)
        };
    }

    /**
     * 加载保存的状态
     */
    loadState() {
        try {
            const savedState = localStorage.getItem(this.config.localStorageKey);
            if (savedState) {
                const parsedState = JSON.parse(savedState);
                this.state = { ...this.state, ...parsedState };
            }
            
            // 兼容旧版本的localStorage键
            if (this.state.collapsed === undefined) {
                const oldCollapsedState = localStorage.getItem('sidebarCollapsed') === 'true';
                this.state.collapsed = oldCollapsedState;
            }
        } catch (error) {
            console.error('[AdminSidebar] Failed to load state:', error);
        }
    }

    /**
     * 保存状态
     */
    saveState() {
        try {
            const stateToSave = {
                collapsed: this.state.collapsed,
                favorites: this.state.favorites,
                recentItems: this.state.recentItems.slice(0, 10), // 只保存最近10个
                menuOrder: this.state.menuOrder,
                searchHistory: this.state.searchHistory.slice(0, 20) // 只保存最近20个搜索
            };
            localStorage.setItem(this.config.localStorageKey, JSON.stringify(stateToSave));
        } catch (error) {
            console.error('[AdminSidebar] Failed to save state:', error);
        }
    }

    /**
     * 初始化菜单
     */
    initializeMenu() {
        // 高亮当前页面
        this.highlightCurrentPage();

        // 添加拖拽排序功能
        this.initializeDragAndDrop();

        // 添加徽章更新监听
        this.initializeBadgeUpdates();
    }

    /**
     * 初始化搜索功能
     */
    initializeSearch() {
        // 创建搜索框
        const searchContainer = this.createSearchBox();
        
        // 插入到侧边栏顶部
        const sidebarHeader = this.elements.sidebar.querySelector('.border-b');
        if (sidebarHeader) {
            sidebarHeader.insertAdjacentElement('afterend', searchContainer);
        }

        // 绑定搜索事件
        this.bindSearchEvents();
    }

    /**
     * 创建搜索框
     */
    createSearchBox() {
        const container = document.createElement('div');
        container.id = 'sidebar-search-container';
        container.className = 'px-4 py-3 border-b border-slate-200 dark:border-slate-700';
        container.innerHTML = `
            <div class="relative">
                <input type="text" 
                       id="sidebar-search" 
                       class="w-full px-3 py-2 pl-9 text-sm bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 text-slate-900 dark:text-white placeholder-slate-500 dark:placeholder-slate-400"
                       placeholder="${this.config.searchPlaceholder}"
                       autocomplete="off">
                <i class="ti ti-search absolute left-3 top-1/2 -translate-y-1/2 text-slate-400 dark:text-slate-500 text-base"></i>
                <div class="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-slate-400 dark:text-slate-500 hidden lg:block">
                    <kbd class="px-1.5 py-0.5 bg-slate-100 dark:bg-slate-700 rounded text-xs">${this.getSearchHotkey()}</kbd>
                </div>
                <!-- 搜索结果下拉框 -->
                <div id="sidebar-search-results" class="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg shadow-lg max-h-80 overflow-y-auto hidden z-50"></div>
            </div>
        `;

        // 缓存搜索相关元素
        this.elements.searchInput = container.querySelector('#sidebar-search');
        this.elements.searchResults = container.querySelector('#sidebar-search-results');

        return container;
    }

    /**
     * 获取搜索快捷键显示文本
     */
    getSearchHotkey() {
        const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
        return isMac ? '⌘K' : 'Ctrl+K';
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 折叠/展开按钮
        if (this.elements.toggleBtn) {
            this.elements.toggleBtn.addEventListener('click', () => this.toggle());
        }

        // 移动端菜单按钮
        if (this.elements.mobileToggle) {
            this.elements.mobileToggle.addEventListener('click', () => this.toggleMobile());
        }

        // 关闭按钮
        if (this.elements.closeBtn) {
            this.elements.closeBtn.addEventListener('click', () => this.closeMobile());
        }

        // 遮罩层点击
        if (this.elements.overlay) {
            this.elements.overlay.addEventListener('click', () => this.closeMobile());
        }

        // 窗口大小变化
        window.addEventListener('resize', this.debounce(() => this.handleResize(), 150));

        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));

        // 菜单项点击追踪
        this.elements.menuList?.addEventListener('click', (e) => {
            const menuItem = e.target.closest('.sidebar-item');
            if (menuItem) {
                this.trackMenuClick(menuItem);
            }
        });
    }

    /**
     * 绑定搜索事件
     */
    bindSearchEvents() {
        if (!this.elements.searchInput) return;

        // 输入事件
        this.elements.searchInput.addEventListener('input', 
            this.debounce((e) => this.handleSearch(e.target.value), 300)
        );

        // 焦点事件
        this.elements.searchInput.addEventListener('focus', () => {
            this.state.searchActive = true;
            this.showSearchHistory();
        });

        // 失焦事件
        this.elements.searchInput.addEventListener('blur', () => {
            setTimeout(() => {
                this.state.searchActive = false;
                this.hideSearchResults();
            }, 200);
        });

        // 搜索结果点击
        this.elements.searchResults.addEventListener('click', (e) => {
            const item = e.target.closest('.search-result-item');
            if (item) {
                const href = item.dataset.href;
                if (href) {
                    this.addToSearchHistory(this.elements.searchInput.value);
                    window.location.href = href;
                }
            }
        });
    }

    /**
     * 处理搜索
     */
    handleSearch(query) {
        this.state.searchQuery = query.trim().toLowerCase();
        
        if (!this.state.searchQuery) {
            this.showSearchHistory();
            return;
        }

        // 搜索菜单项
        const results = this.searchMenuItems(this.state.searchQuery);
        this.displaySearchResults(results);
    }

    /**
     * 搜索菜单项
     */
    searchMenuItems(query) {
        const results = [];
        const menuItems = this.elements.menuList.querySelectorAll('.sidebar-item');

        menuItems.forEach(item => {
            const text = item.querySelector('.sidebar-text')?.textContent.toLowerCase() || '';
            const pinyin = this.getPinyinInitials(text);
            const href = item.getAttribute('href');
            
            // 检查是否匹配
            let score = 0;
            if (text.includes(query)) {
                score = query.length / text.length;
            } else if (pinyin.includes(query)) {
                score = query.length / pinyin.length * 0.8;
            }

            if (score > 0) {
                results.push({
                    text: item.querySelector('.sidebar-text')?.textContent || '',
                    icon: item.querySelector('.sidebar-icon')?.className || '',
                    href: href,
                    score: score
                });
            }
        });

        // 按分数排序
        results.sort((a, b) => b.score - a.score);
        
        return results.slice(0, 10); // 最多返回10个结果
    }

    /**
     * 获取拼音首字母
     */
    getPinyinInitials(text) {
        // 简单的拼音首字母映射
        const pinyinMap = {
            '网络监控配置': 'wljkpz',
            '服务器管理': 'fwqgl',
            '分组管理': 'fzgl',
            '自动发现': 'zdfx',
            'SSH脚本': 'sshjb',
            '系统设置': 'xtsz',
            '高级设置': 'gjsz',
            '美化设置': 'mhsz',
            '日志管理': 'rzgl',
            '授权管理': 'sqgl',
            '高级分析': 'gjfx',
            '返回前台': 'fhqt'
        };
        
        return pinyinMap[text] || text;
    }

    /**
     * 显示搜索结果
     */
    displaySearchResults(results) {
        if (!this.elements.searchResults) return;

        if (results.length === 0) {
            this.elements.searchResults.innerHTML = `
                <div class="px-4 py-3 text-sm text-slate-500 dark:text-slate-400">
                    没有找到相关菜单项
                </div>
            `;
        } else {
            this.elements.searchResults.innerHTML = results.map(item => `
                <div class="search-result-item px-4 py-2 hover:bg-slate-50 dark:hover:bg-slate-700 cursor-pointer flex items-center gap-3 transition-colors" data-href="${item.href}">
                    <i class="${item.icon}"></i>
                    <span class="text-sm text-slate-700 dark:text-slate-300">${this.highlightText(item.text, this.state.searchQuery)}</span>
                </div>
            `).join('');
        }

        this.elements.searchResults.classList.remove('hidden');
    }

    /**
     * 高亮匹配文本
     */
    highlightText(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 text-inherit">$1</mark>');
    }

    /**
     * 显示搜索历史
     */
    showSearchHistory() {
        if (!this.elements.searchResults || this.state.searchHistory.length === 0) return;

        this.elements.searchResults.innerHTML = `
            <div class="px-4 py-2 text-xs font-medium text-slate-500 dark:text-slate-400 uppercase">
                搜索历史
            </div>
            ${this.state.searchHistory.slice(0, 5).map(item => `
                <div class="search-result-item px-4 py-2 hover:bg-slate-50 dark:hover:bg-slate-700 cursor-pointer flex items-center gap-3 transition-colors">
                    <i class="ti ti-clock text-slate-400 dark:text-slate-500"></i>
                    <span class="text-sm text-slate-600 dark:text-slate-400">${item}</span>
                </div>
            `).join('')}
        `;

        this.elements.searchResults.classList.remove('hidden');
    }

    /**
     * 隐藏搜索结果
     */
    hideSearchResults() {
        if (this.elements.searchResults) {
            this.elements.searchResults.classList.add('hidden');
        }
    }

    /**
     * 添加到搜索历史
     */
    addToSearchHistory(query) {
        if (!query || query.trim() === '') return;
        
        // 移除重复项
        this.state.searchHistory = this.state.searchHistory.filter(item => item !== query);
        
        // 添加到开头
        this.state.searchHistory.unshift(query);
        
        // 限制数量
        this.state.searchHistory = this.state.searchHistory.slice(0, 20);
        
        this.saveState();
    }

    /**
     * 初始化拖拽排序
     */
    initializeDragAndDrop() {
        if (!this.checkUserPermission('admin')) return;

        let draggedElement = null;
        let placeholder = null;

        this.elements.menuList.querySelectorAll('.sidebar-item').forEach(item => {
            // 添加拖拽手柄
            const handle = document.createElement('span');
            handle.className = 'drag-handle absolute right-2 top-1/2 -translate-y-1/2 cursor-move opacity-0 group-hover:opacity-100 transition-opacity';
            handle.innerHTML = '<i class="ti ti-grip-vertical text-slate-400 dark:text-slate-500"></i>';
            handle.draggable = true;
            
            item.style.position = 'relative';
            item.appendChild(handle);

            // 拖拽事件
            handle.addEventListener('dragstart', (e) => {
                draggedElement = item.parentElement;
                placeholder = document.createElement('li');
                placeholder.className = 'h-10 bg-blue-100 dark:bg-blue-900/50 rounded-md transition-all';
                
                e.dataTransfer.effectAllowed = 'move';
                item.classList.add('opacity-50');
            });

            handle.addEventListener('dragend', () => {
                if (draggedElement) {
                    draggedElement.classList.remove('opacity-50');
                }
                if (placeholder && placeholder.parentNode) {
                    placeholder.parentNode.removeChild(placeholder);
                }
                draggedElement = null;
                placeholder = null;
                
                // 保存新顺序
                this.saveMenuOrder();
            });
        });

        // 拖拽目标事件
        this.elements.menuList.addEventListener('dragover', (e) => {
            e.preventDefault();
            if (!draggedElement || !placeholder) return;

            const afterElement = this.getDragAfterElement(this.elements.menuList, e.clientY);
            if (afterElement == null) {
                this.elements.menuList.appendChild(placeholder);
            } else {
                this.elements.menuList.insertBefore(placeholder, afterElement);
            }
        });

        this.elements.menuList.addEventListener('drop', (e) => {
            e.preventDefault();
            if (!draggedElement || !placeholder) return;

            placeholder.parentNode.replaceChild(draggedElement, placeholder);
            this.emit('menuReordered', { menuOrder: this.getMenuOrder() });
        });
    }

    /**
     * 获取拖拽后的元素
     */
    getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('li:not(.dragging)')];
        
        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;
            
            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    /**
     * 保存菜单顺序
     */
    saveMenuOrder() {
        const order = [];
        this.elements.menuList.querySelectorAll('.sidebar-item').forEach(item => {
            const href = item.getAttribute('href');
            if (href) {
                order.push(href);
            }
        });
        
        this.state.menuOrder = order;
        this.saveState();
    }

    /**
     * 获取菜单顺序
     */
    getMenuOrder() {
        return this.state.menuOrder;
    }

    /**
     * 初始化徽章更新
     */
    initializeBadgeUpdates() {
        // 监听徽章更新事件
        this.on('badgeUpdate', (data) => {
            this.updateBadge(data.menuId, data.count);
        });
    }

    /**
     * 更新菜单徽章
     */
    updateBadge(menuId, count) {
        const menuItem = this.elements.menuList.querySelector(`[data-menu-id="${menuId}"]`);
        if (!menuItem) return;

        let badge = menuItem.querySelector('.sidebar-badge');
        
        if (count > 0) {
            if (!badge) {
                badge = document.createElement('span');
                badge.className = 'sidebar-badge ml-auto bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 text-xs px-2 py-0.5 rounded-full';
                menuItem.appendChild(badge);
            }
            badge.textContent = count > 99 ? '99+' : count;
            badge.setAttribute('aria-label', `${count} 个新项目`);
        } else if (badge) {
            badge.remove();
        }
    }

    /**
     * 初始化WebSocket连接
     */
    initializeWebSocket() {
        if (!this.config.wsUrl) return;

        try {
            this.ws = new WebSocket(this.config.wsUrl);
            
            this.ws.onopen = () => {
                console.log('[AdminSidebar] WebSocket connected');
                this.emit('wsConnected');
            };

            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (error) {
                    console.error('[AdminSidebar] Failed to parse WebSocket message:', error);
                }
            };

            this.ws.onerror = (error) => {
                console.error('[AdminSidebar] WebSocket error:', error);
                this.emit('wsError', { error });
            };

            this.ws.onclose = () => {
                console.log('[AdminSidebar] WebSocket disconnected');
                this.emit('wsDisconnected');
                
                // 5秒后重连
                setTimeout(() => this.initializeWebSocket(), 5000);
            };
        } catch (error) {
            console.error('[AdminSidebar] Failed to initialize WebSocket:', error);
        }
    }

    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'permissionUpdate':
                this.updatePermissions(data.permissions, data.features);
                break;
            case 'badgeUpdate':
                this.updateBadge(data.menuId, data.count);
                break;
            case 'menuUpdate':
                this.updateMenu(data.menu);
                break;
            default:
                this.emit('wsMessage', { data });
        }
    }

    /**
     * 更新权限
     */
    updatePermissions(permissions, features) {
        this.state.permissions = permissions || {};
        this.state.features = features || 0;
        
        // 重新渲染菜单
        this.refreshMenu();
        
        this.emit('permissionsUpdated', { 
            permissions: this.state.permissions,
            features: this.state.features 
        });
    }

    /**
     * 🔧 简化：许可证验证指示器已移除
     */
    hideLicenseStatusIndicator() {
        // 指示器已从页面中移除，此方法保留以兼容现有调用
        console.log('[AdminSidebar] License status indicator has been removed');
    }

    /**
     * 更新菜单
     */
    updateMenu(menuData) {
        if (!menuData) return;
        
        // 更新菜单数据
        this.menuData = menuData;
        
        // 重新渲染菜单
        this.refreshMenu();
        
        this.emit('menuUpdated', { menu: menuData });
    }

    /**
     * 刷新菜单
     */
    refreshMenu() {
        // 这里需要根据实际的菜单渲染逻辑来实现
        // 可能需要调用服务端API获取最新菜单数据
        console.log('[AdminSidebar] Refreshing menu...');
    }

    /**
     * 切换折叠状态
     */
    toggle() {
        if (this.isMobile()) {
            this.toggleMobile();
        } else {
            this.state.collapsed = !this.state.collapsed;
            this.applyCollapsedState();
            this.saveState();
            this.emit('toggled', { collapsed: this.state.collapsed });
        }
    }

    /**
     * 展开侧边栏
     */
    expand() {
        if (this.state.collapsed) {
            this.state.collapsed = false;
            this.applyCollapsedState();
            this.saveState();
            this.emit('expanded');
        }
    }

    /**
     * 折叠侧边栏
     */
    collapse() {
        if (!this.state.collapsed) {
            this.state.collapsed = true;
            this.applyCollapsedState();
            this.saveState();
            this.emit('collapsed');
        }
    }

    /**
     * 切换移动端菜单
     */
    toggleMobile() {
        this.state.mobileOpen = !this.state.mobileOpen;
        this.applyMobileState();
        this.emit('mobileToggled', { open: this.state.mobileOpen });
    }

    /**
     * 打开移动端菜单
     */
    openMobile() {
        if (!this.state.mobileOpen) {
            this.state.mobileOpen = true;
            this.applyMobileState();
            this.emit('mobileOpened');
        }
    }

    /**
     * 关闭移动端菜单
     */
    closeMobile() {
        if (this.state.mobileOpen) {
            this.state.mobileOpen = false;
            this.applyMobileState();
            this.emit('mobileClosed');
        }
    }

    /**
     * 应用状态
     */
    applyState() {
        if (this.isMobile()) {
            this.applyMobileState();
            // 移动端确保body没有折叠类
            document.body.classList.remove('sidebar-collapsed');
        } else {
            this.applyCollapsedState();
        }
    }

    /**
     * 应用折叠状态
     */
    applyCollapsedState() {
        if (!this.elements.sidebar) return;

        if (this.state.collapsed) {
            this.elements.sidebar.classList.add('sidebar-collapsed', 'w-16');
            this.elements.sidebar.classList.remove('w-64');
            document.body.classList.add('sidebar-collapsed');
        } else {
            this.elements.sidebar.classList.remove('sidebar-collapsed', 'w-16');
            this.elements.sidebar.classList.add('w-64');
            document.body.classList.remove('sidebar-collapsed');
        }
    }

    /**
     * 应用移动端状态
     */
    applyMobileState() {
        if (!this.elements.sidebar) return;

        if (this.state.mobileOpen) {
            this.elements.sidebar.classList.remove('-translate-x-full');
            this.elements.overlay?.classList.remove('opacity-0', 'pointer-events-none');
            this.elements.overlay?.classList.add('opacity-100');
            document.body.classList.add('overflow-hidden');
            
            // 更新按钮图标
            const icon = this.elements.mobileToggle?.querySelector('.toggle-icon');
            if (icon) {
                icon.className = 'ti ti-x toggle-icon text-xl';
            }
        } else {
            this.elements.sidebar.classList.add('-translate-x-full');
            this.elements.overlay?.classList.add('opacity-0', 'pointer-events-none');
            this.elements.overlay?.classList.remove('opacity-100');
            document.body.classList.remove('overflow-hidden');
            
            // 更新按钮图标
            const icon = this.elements.mobileToggle?.querySelector('.toggle-icon');
            if (icon) {
                icon.className = 'ti ti-menu-2 toggle-icon text-xl';
            }
        }
    }

    /**
     * 高亮当前页面
     */
    highlightCurrentPage() {
        const currentPath = window.location.pathname;
        const menuItems = this.elements.menuList?.querySelectorAll('.sidebar-item');
        
        menuItems?.forEach(item => {
            const href = item.getAttribute('href');
            if (!href) return;

            if (href === currentPath) {
                item.classList.add('active', 'bg-blue-50', 'dark:bg-blue-950/50', 
                                  'border-l-4', 'border-blue-500', 'dark:border-blue-400', 
                                  'text-blue-900', 'dark:text-blue-100', 'font-medium');
                item.classList.remove('text-slate-700', 'dark:text-slate-300', 'border-transparent');
                item.setAttribute('aria-current', 'page');
                
                // 更新当前活动菜单ID
                this.state.activeMenuId = item.dataset.menuId;
            } else {
                item.classList.remove('active', 'bg-blue-50', 'dark:bg-blue-950/50', 
                                    'border-blue-500', 'dark:border-blue-400', 
                                    'text-blue-900', 'dark:text-blue-100', 'font-medium');
                item.classList.add('text-slate-700', 'dark:text-slate-300', 'border-transparent');
                item.setAttribute('aria-current', 'false');
            }
        });
    }

    /**
     * 追踪菜单点击
     */
    trackMenuClick(menuItem) {
        const href = menuItem.getAttribute('href');
        const text = menuItem.querySelector('.sidebar-text')?.textContent;
        
        if (href && text) {
            // 添加到最近访问
            this.addToRecentItems({ href, text });
            
            // 触发事件
            this.emit('menuClicked', { href, text });
        }
    }

    /**
     * 添加到最近访问
     */
    addToRecentItems(item) {
        // 移除重复项
        this.state.recentItems = this.state.recentItems.filter(i => i.href !== item.href);
        
        // 添加到开头
        this.state.recentItems.unshift(item);
        
        // 限制数量
        this.state.recentItems = this.state.recentItems.slice(0, 10);
        
        this.saveState();
    }

    /**
     * 添加到收藏
     */
    addToFavorites(menuId) {
        if (!this.state.favorites.includes(menuId)) {
            this.state.favorites.push(menuId);
            this.saveState();
            this.emit('favoriteAdded', { menuId });
        }
    }

    /**
     * 从收藏移除
     */
    removeFromFavorites(menuId) {
        const index = this.state.favorites.indexOf(menuId);
        if (index > -1) {
            this.state.favorites.splice(index, 1);
            this.saveState();
            this.emit('favoriteRemoved', { menuId });
        }
    }

    /**
     * 检查是否已收藏
     */
    isFavorite(menuId) {
        return this.state.favorites.includes(menuId);
    }

    /**
     * 处理键盘事件
     */
    handleKeyboard(e) {
        // 搜索快捷键
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            this.focusSearch();
        }

        // ESC键关闭移动端菜单
        if (e.key === 'Escape' && this.isMobile() && this.state.mobileOpen) {
            this.closeMobile();
        }
    }

    /**
     * 聚焦搜索框
     */
    focusSearch() {
        if (this.elements.searchInput) {
            this.elements.searchInput.focus();
            this.elements.searchInput.select();
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        this.applyState();
    }

    /**
     * 检查是否为移动设备
     */
    isMobile() {
        return window.innerWidth < 1024;
    }

    /**
     * 检查用户权限
     */
    checkUserPermission(permission) {
        return this.state.permissions[permission] === true;
    }

    /**
     * 检查功能特性
     */
    checkFeature(feature) {
        const featureMap = {
            BASIC_MONITORING: 1,
            WEBSSH: 2,
            AUTO_DISCOVERY: 4,
            ADVANCED_ANALYTICS: 8,
            API_ACCESS: 16,
            CUSTOM_ALERTS: 32,
            AI_ANALYTICS: 64
        };
        
        const featureBit = featureMap[feature];
        return featureBit ? (this.state.features & featureBit) !== 0 : false;
    }

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 事件发射
     */
    emit(eventName, detail = {}) {
        this.events.dispatchEvent(new CustomEvent(eventName, { detail }));
    }

    /**
     * 事件监听
     */
    on(eventName, handler) {
        this.events.addEventListener(eventName, handler);
    }

    /**
     * 移除事件监听
     */
    off(eventName, handler) {
        this.events.removeEventListener(eventName, handler);
    }

    /**
     * 初始化页面切换动画控制
     */
    initPageTransitionControl() {
        // 检测页面切换开始
        const handlePageTransitionStart = () => {
            document.body.classList.add('page-transitioning');
        };

        // 检测页面切换结束
        const handlePageTransitionEnd = () => {
            // 延迟移除类，确保页面完全加载
            setTimeout(() => {
                document.body.classList.remove('page-transitioning');
            }, 100);
        };

        // 监听页面卸载（准备跳转）
        window.addEventListener('beforeunload', handlePageTransitionStart);
        
        // 监听页面加载完成
        window.addEventListener('load', handlePageTransitionEnd);
        document.addEventListener('DOMContentLoaded', handlePageTransitionEnd);

        // 监听链接点击（现代 SPA 导航）
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href]');
            if (link && link.href && 
                !link.href.startsWith('javascript:') &&
                !link.href.startsWith('#') &&
                !link.target &&
                link.href !== window.location.href) {
                
                handlePageTransitionStart();
                
                // 如果是同域链接，在短时间后清除类（防止卡死）
                if (link.hostname === window.location.hostname) {
                    setTimeout(() => {
                        document.body.classList.remove('page-transitioning');
                    }, 1000);
                }
            }
        });

        // 监听浏览器历史记录变化
        window.addEventListener('popstate', () => {
            handlePageTransitionStart();
            setTimeout(handlePageTransitionEnd, 50);
        });

        // 支持用户设置的动画偏好
        if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.body.classList.add('reduced-motion');
        }
    }

    /**
     * 销毁组件
     */
    destroy() {
        // 关闭WebSocket连接
        if (this.ws) {
            this.ws.close();
        }

        // 移除事件监听
        window.removeEventListener('resize', this.handleResize);
        document.removeEventListener('keydown', this.handleKeyboard);

        // 清空状态
        this.state = {};
        this.elements = {};

        this.emit('destroyed');
    }
}

// 导出组件
window.AdminSidebar = AdminSidebar;

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('admin-sidebar')) {
        window.adminSidebar = new AdminSidebar({
            wsUrl: window.ADMIN_WS_URL || null
        });
        
        // 🔧 简化：只需要自动隐藏许可证验证指示器即可
        if (window.adminSidebar.hideLicenseStatusIndicator) {
            window.adminSidebar.hideLicenseStatusIndicator();
        }
    }
});