/**
 * Network Quality Search Component
 * 网络质量页面搜索组件
 */

class NetworkQualitySearch {
    constructor(options = {}) {
        this.options = {
            container: '#nq-search-container',
            placeholder: '搜索节点名称、IP地址或目标...',
            debounceDelay: 300,
            maxSuggestions: 5,
            maxHistory: 10,
            enableHistory: true,
            enableSuggestions: true,
            ...options
        };

        // 状态管理
        this.state = {
            isActive: false,
            currentQuery: '',
            suggestions: [],
            history: [],
            filteredNodes: [],
            highlightedIndex: -1
        };

        // 回调函数
        this.callbacks = {
            onSearch: null,
            onClear: null,
            onChange: null,
            onSelect: null,
            ...options.callbacks
        };

        // DOM 元素
        this.elements = {};

        // 防抖定时器
        this.debounceTimer = null;

        // 初始化
        this.init();
    }

    /**
     * 初始化搜索组件
     */
    init() {
        this.createElements();
        this.bindEvents();
        this.loadHistory();
        console.log('[NetworkQualitySearch] 搜索组件初始化完成');
    }

    /**
     * 创建DOM元素
     */
    createElements() {
        const container = document.querySelector(this.options.container);
        if (!container) {
            console.error('[NetworkQualitySearch] 容器元素未找到:', this.options.container);
            return;
        }

        container.innerHTML = `
            <div class="relative flex-1 min-w-0">
                <input
                    type="text"
                    class="w-full pl-10 pr-24 py-2.5 text-sm border border-slate-200 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-800 dark:text-slate-200 placeholder-slate-500 dark:placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="${this.options.placeholder}"
                    autocomplete="off"
                    spellcheck="false"
                >
                <i class="ti ti-search absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 text-lg pointer-events-none"></i>

                <!-- 搜索状态和清除按钮 -->
                <div class="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
                    <span class="nq-search-status-text text-xs text-slate-500 dark:text-slate-400 hidden sm:inline whitespace-nowrap">
                        <span class="nq-search-status-count font-medium text-blue-600 dark:text-blue-400">0</span>
                    </span>
                    <button class="p-1 rounded-md text-slate-400 hover:text-slate-600 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors opacity-0 invisible" type="button" aria-label="清除搜索">
                        <i class="ti ti-x text-sm"></i>
                    </button>
                </div>

                <!-- 搜索建议下拉框 -->
                <div class="absolute top-full left-0 right-0 z-20 mt-1 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg shadow-lg max-h-60 overflow-y-auto opacity-0 invisible transition-all duration-200 pointer-events-none">
                    <div class="py-1">
                        <!-- 搜索建议将在这里动态生成 -->
                    </div>
                </div>
            </div>
        `;

        // 缓存DOM元素
        this.elements = {
            container: container.querySelector('div'),
            input: container.querySelector('input'),
            clearBtn: container.querySelector('button'),
            suggestions: container.querySelector('.absolute.top-full'),
            suggestionsContent: container.querySelector('.py-1'),
            statusText: container.querySelector('.nq-search-status-text'),
            statusCount: container.querySelector('.nq-search-status-count')
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        if (!this.elements.input) return;

        // 输入框事件
        this.elements.input.addEventListener('input', (e) => {
            this.handleInput(e.target.value);
        });

        this.elements.input.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });

        this.elements.input.addEventListener('focus', () => {
            this.handleFocus();
        });

        this.elements.input.addEventListener('blur', () => {
            // 延迟隐藏建议，允许点击建议项
            setTimeout(() => this.hideSuggestions(), 150);
        });

        // 清除按钮事件
        this.elements.clearBtn.addEventListener('click', () => {
            this.clearSearch();
        });

        // 全局键盘快捷键
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.focus();
            }

            if (e.key === 'Escape' && this.state.isActive) {
                this.clearSearch();
                this.elements.input.blur();
            }
        });

        // 点击外部隐藏建议
        document.addEventListener('click', (e) => {
            if (!this.elements.container.contains(e.target)) {
                this.hideSuggestions();
            }
        });
    }

    /**
     * 处理输入事件
     * @param {string} value - 输入值
     */
    handleInput(value) {
        this.state.currentQuery = value.trim();
        
        // 显示/隐藏清除按钮
        if (value) {
            this.elements.clearBtn.classList.remove('opacity-0', 'invisible');
            this.elements.clearBtn.classList.add('opacity-100', 'visible');
        } else {
            this.elements.clearBtn.classList.remove('opacity-100', 'visible');
            this.elements.clearBtn.classList.add('opacity-0', 'invisible');
        }
        
        // 清除防抖定时器
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        // 防抖搜索
        this.debounceTimer = setTimeout(() => {
            this.performSearch(this.state.currentQuery);
        }, this.options.debounceDelay);

        // 实时更新建议
        if (this.options.enableSuggestions) {
            this.updateSuggestions(this.state.currentQuery);
        }

        // 触发变化回调
        if (this.callbacks.onChange) {
            this.callbacks.onChange(this.state.currentQuery);
        }
    }

    /**
     * 处理键盘事件
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeydown(e) {
        if (!this.state.isActive) return;

        const suggestionsVisible = !this.elements.suggestions.classList.contains('invisible');
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                if (suggestionsVisible) {
                    this.highlightNext();
                }
                break;

            case 'ArrowUp':
                e.preventDefault();
                if (suggestionsVisible) {
                    this.highlightPrevious();
                }
                break;

            case 'Enter':
                e.preventDefault();
                if (suggestionsVisible && this.state.highlightedIndex >= 0) {
                    this.selectSuggestion(this.state.highlightedIndex);
                } else {
                    this.performSearch(this.state.currentQuery);
                }
                break;

            case 'Escape':
                this.hideSuggestions();
                break;
        }
    }

    /**
     * 处理焦点事件
     */
    handleFocus() {
        this.state.isActive = true;
        
        if (this.options.enableSuggestions) {
            if (this.state.currentQuery) {
                this.updateSuggestions(this.state.currentQuery);
            } else if (this.options.enableHistory && this.state.history.length > 0) {
                this.showHistory();
            }
        }
    }

    /**
     * 执行搜索
     * @param {string} query - 搜索查询
     */
    performSearch(query) {
        if (!query) {
            this.clearSearch();
            return;
        }

        // 添加搜索样式
        this.elements.input.classList.add('animate-pulse');

        // 保存到历史记录
        if (this.options.enableHistory) {
            this.addToHistory(query);
        }

        // 执行搜索逻辑
        const results = this.searchNodes(query);
        this.state.filteredNodes = results;

        // 更新状态显示
        this.updateStatus(results.length);

        // 移除搜索样式
        setTimeout(() => {
            this.elements.input.classList.remove('animate-pulse');
        }, 500);

        // 隐藏建议
        this.hideSuggestions();

        // 触发搜索回调
        if (this.callbacks.onSearch) {
            this.callbacks.onSearch(query, results);
        }

        console.log('[NetworkQualitySearch] 搜索完成:', query, '结果数量:', results.length);
    }

    /**
     * 搜索节点
     * @param {string} query - 搜索查询
     * @returns {Array} 搜索结果
     */
    searchNodes(query) {
        const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);
        if (searchTerms.length === 0) return [];

        const allNodes = this.getAllNodes();
        
        return allNodes.filter(node => {
            const searchText = this.getNodeSearchText(node).toLowerCase();
            
            // 所有搜索词都必须匹配（AND逻辑）
            return searchTerms.every(term => searchText.includes(term));
        });
    }

    /**
     * 获取所有节点
     * @returns {Array} 节点列表
     */
    getAllNodes() {
        const nodeCards = document.querySelectorAll('.network-status-card[data-node-id]');
        return Array.from(nodeCards).map(card => {
            const nodeId = card.dataset.nodeId;
            const nodeName = card.querySelector('h3')?.textContent?.trim() || '';
            const targets = Array.from(card.querySelectorAll('.target-item')).map(item => {
                return item.querySelector('.target-name')?.textContent?.trim() || '';
            });

            return {
                id: nodeId,
                name: nodeName,
                targets: targets,
                element: card
            };
        });
    }

    /**
     * 获取节点的搜索文本
     * @param {Object} node - 节点对象
     * @returns {string} 搜索文本
     */
    getNodeSearchText(node) {
        const searchParts = [
            node.name,
            node.id,
            ...node.targets
        ];

        return searchParts.join(' ');
    }

    /**
     * 更新搜索建议
     * @param {string} query - 搜索查询
     */
    updateSuggestions(query) {
        if (!query) {
            if (this.options.enableHistory && this.state.history.length > 0) {
                this.showHistory();
            } else {
                this.hideSuggestions();
            }
            return;
        }

        const suggestions = this.generateSuggestions(query);
        this.state.suggestions = suggestions;
        this.renderSuggestions(suggestions);
        
        if (suggestions.length > 0) {
            this.showSuggestions();
        } else {
            this.hideSuggestions();
        }
    }

    /**
     * 生成搜索建议
     * @param {string} query - 搜索查询
     * @returns {Array} 建议列表
     */
    generateSuggestions(query) {
        const suggestions = [];
        const lowerQuery = query.toLowerCase();
        const allNodes = this.getAllNodes();

        // 节点名称匹配
        allNodes.forEach(node => {
            if (node.name.toLowerCase().includes(lowerQuery)) {
                suggestions.push({
                    type: 'node',
                    text: node.name,
                    value: node.name,
                    icon: 'ti-server',
                    matches: this.highlightMatches(node.name, query)
                });
            }

            // 目标匹配
            node.targets.forEach(target => {
                if (target.toLowerCase().includes(lowerQuery)) {
                    suggestions.push({
                        type: 'target',
                        text: target,
                        value: target,
                        icon: 'ti-target',
                        matches: this.highlightMatches(target, query),
                        nodeContext: node.name
                    });
                }
            });
        });

        // 去重并限制数量
        const uniqueSuggestions = suggestions.filter((suggestion, index, self) => 
            index === self.findIndex(s => s.value === suggestion.value && s.type === suggestion.type)
        );

        return uniqueSuggestions.slice(0, this.options.maxSuggestions);
    }

    /**
     * 高亮匹配文本
     * @param {string} text - 原文本
     * @param {string} query - 搜索查询
     * @returns {string} 高亮后的HTML
     */
    highlightMatches(text, query) {
        const lowerText = text.toLowerCase();
        const lowerQuery = query.toLowerCase();
        const index = lowerText.indexOf(lowerQuery);
        
        if (index === -1) return text;
        
        const before = text.substring(0, index);
        const match = text.substring(index, index + query.length);
        const after = text.substring(index + query.length);
        
        return `${before}<span class="highlight">${match}</span>${after}`;
    }

    /**
     * 渲染搜索建议
     * @param {Array} suggestions - 建议列表
     */
    renderSuggestions(suggestions) {
        if (!suggestions || suggestions.length === 0) {
            this.elements.suggestionsContent.innerHTML = `
                <div class="flex flex-col items-center justify-center py-6 text-slate-500 dark:text-slate-400">
                    <i class="ti ti-search-off text-2xl mb-2"></i>
                    <div class="text-sm">无匹配结果</div>
                </div>
            `;
            return;
        }

        const html = suggestions.map((suggestion, index) => `
            <div class="flex items-center px-3 py-2 hover:bg-slate-50 dark:hover:bg-slate-600 cursor-pointer transition-colors group" data-index="${index}">
                <i class="ti ${suggestion.icon} text-slate-400 text-sm mr-3"></i>
                <div class="flex-1 min-w-0">
                    <div class="text-sm text-slate-800 dark:text-slate-200 truncate">${suggestion.matches}</div>
                </div>
                <div class="text-xs text-slate-500 dark:text-slate-400 ml-2">${this.getSuggestionTypeText(suggestion.type)}</div>
            </div>
        `).join('');

        this.elements.suggestionsContent.innerHTML = html;

        // 绑定点击事件
        this.elements.suggestionsContent.querySelectorAll('[data-index]').forEach((item, index) => {
            item.addEventListener('click', () => {
                this.selectSuggestion(index);
            });
        });
    }

    /**
     * 显示搜索历史
     */
    showHistory() {
        if (!this.state.history.length) return;

        const html = `
            <div class="px-3 py-2 border-b border-slate-200 dark:border-slate-600 text-xs font-medium text-slate-600 dark:text-slate-400">最近搜索</div>
            ${this.state.history.map(item => `
                <div class="flex items-center px-3 py-2 hover:bg-slate-50 dark:hover:bg-slate-600 cursor-pointer transition-colors" data-value="${item}">
                    <i class="ti ti-history text-slate-400 text-sm mr-3"></i>
                    <span class="flex-1 text-sm text-slate-700 dark:text-slate-300 truncate">${item}</span>
                </div>
            `).join('')}
            <div class="border-t border-slate-200 dark:border-slate-600 p-2">
                <button type="button" class="w-full text-xs text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 py-1 transition-colors">清除历史记录</button>
            </div>
        `;

        this.elements.suggestionsContent.innerHTML = html;

        // 绑定历史记录事件
        this.elements.suggestionsContent.querySelectorAll('[data-value]').forEach(item => {
            item.addEventListener('click', () => {
                const value = item.dataset.value;
                this.elements.input.value = value;
                this.performSearch(value);
            });
        });

        // 绑定清除历史记录事件
        const clearBtn = this.elements.suggestionsContent.querySelector('button');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearHistory();
                this.hideSuggestions();
            });
        }

        this.showSuggestions();
    }

    /**
     * 获取建议类型文本
     * @param {string} type - 建议类型
     * @returns {string} 类型文本
     */
    getSuggestionTypeText(type) {
        const typeMap = {
            node: '节点',
            target: '目标',
            ip: 'IP地址'
        };
        return typeMap[type] || type;
    }

    /**
     * 选择建议项
     * @param {number} index - 建议索引
     */
    selectSuggestion(index) {
        const suggestion = this.state.suggestions[index];
        if (!suggestion) return;

        this.elements.input.value = suggestion.value;
        this.performSearch(suggestion.value);

        if (this.callbacks.onSelect) {
            this.callbacks.onSelect(suggestion);
        }
    }

    /**
     * 高亮下一个建议
     */
    highlightNext() {
        const maxIndex = this.state.suggestions.length - 1;
        this.state.highlightedIndex = Math.min(this.state.highlightedIndex + 1, maxIndex);
        this.updateHighlight();
    }

    /**
     * 高亮上一个建议
     */
    highlightPrevious() {
        this.state.highlightedIndex = Math.max(this.state.highlightedIndex - 1, -1);
        this.updateHighlight();
    }

    /**
     * 更新高亮显示
     */
    updateHighlight() {
        const items = this.elements.suggestionsContent.querySelectorAll('[data-index]');
        items.forEach((item, index) => {
            if (index === this.state.highlightedIndex) {
                item.classList.add('bg-blue-50', 'dark:bg-blue-900/30');
            } else {
                item.classList.remove('bg-blue-50', 'dark:bg-blue-900/30');
            }
        });
    }

    /**
     * 显示建议框
     */
    showSuggestions() {
        this.elements.suggestions.classList.remove('opacity-0', 'invisible', 'pointer-events-none');
        this.elements.suggestions.classList.add('opacity-100', 'visible', 'pointer-events-auto');
        this.state.highlightedIndex = -1;
    }

    /**
     * 隐藏建议框
     */
    hideSuggestions() {
        this.elements.suggestions.classList.remove('opacity-100', 'visible', 'pointer-events-auto');
        this.elements.suggestions.classList.add('opacity-0', 'invisible', 'pointer-events-none');
        this.state.highlightedIndex = -1;
        this.state.isActive = false;
    }

    /**
     * 清除搜索
     */
    clearSearch() {
        this.elements.input.value = '';
        this.state.currentQuery = '';
        this.state.filteredNodes = [];
        
        // 隐藏清除按钮
        this.elements.clearBtn.classList.remove('opacity-100', 'visible');
        this.elements.clearBtn.classList.add('opacity-0', 'invisible');
        
        this.hideSuggestions();
        this.updateStatus(this.getAllNodes().length);

        if (this.callbacks.onClear) {
            this.callbacks.onClear();
        }

        console.log('[NetworkQualitySearch] 搜索已清除');
    }

    /**
     * 聚焦搜索框
     */
    focus() {
        this.elements.input.focus();
    }

    /**
     * 更新状态显示
     * @param {number} count - 节点数量
     */
    updateStatus(count) {
        if (this.elements.statusCount) {
            this.elements.statusCount.textContent = count;
        }
        if (this.elements.statusText) {
            if (this.state.currentQuery) {
                this.elements.statusText.innerHTML = `搜索到 <span class="nq-search-status-count">${count}</span> 个节点`;
            } else {
                this.elements.statusText.innerHTML = `共 <span class="nq-search-status-count">${count}</span> 个节点`;
            }
            // 更新引用，保持后续可直接修改数字
            this.elements.statusCount = this.elements.statusText.querySelector('.nq-search-status-count');
        }
    }

    /**
     * 添加到搜索历史
     * @param {string} query - 搜索查询
     */
    addToHistory(query) {
        if (!query || this.state.history.includes(query)) return;

        this.state.history.unshift(query);
        if (this.state.history.length > this.options.maxHistory) {
            this.state.history = this.state.history.slice(0, this.options.maxHistory);
        }

        this.saveHistory();
    }

    /**
     * 加载搜索历史
     */
    loadHistory() {
        try {
            const saved = localStorage.getItem('nq-search-history');
            if (saved) {
                this.state.history = JSON.parse(saved);
            }
        } catch (error) {
            console.warn('[NetworkQualitySearch] 加载搜索历史失败:', error);
            this.state.history = [];
        }
    }

    /**
     * 保存搜索历史
     */
    saveHistory() {
        try {
            localStorage.setItem('nq-search-history', JSON.stringify(this.state.history));
        } catch (error) {
            console.warn('[NetworkQualitySearch] 保存搜索历史失败:', error);
        }
    }

    /**
     * 清除搜索历史
     */
    clearHistory() {
        this.state.history = [];
        this.saveHistory();
        console.log('[NetworkQualitySearch] 搜索历史已清除');
    }

    /**
     * 获取当前搜索状态
     * @returns {Object} 搜索状态
     */
    getState() {
        return {
            query: this.state.currentQuery,
            results: this.state.filteredNodes.length,
            isActive: this.state.isActive
        };
    }

    /**
     * 销毁搜索组件
     */
    destroy() {
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        
        this.elements = {};
        this.state = {};
        this.callbacks = {};
        
        console.log('[NetworkQualitySearch] 搜索组件已销毁');
    }
}

// 导出组件
window.NetworkQualitySearch = NetworkQualitySearch;
