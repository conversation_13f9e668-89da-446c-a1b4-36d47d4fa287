/**
 * Granularity Controller Component
 * 数据颗粒度控制器组件
 */

class GranularityController {
    constructor(options = {}) {
        this.options = {
            container: null,
            defaultGranularity: '5m',
            defaultRange: '24h',
            onGranularityChange: null,
            availableOptions: [
                { granularity: 'm', range: '4h', label: '4 Hours', description: 'Minute-level data for 4 hours' },
                { granularity: '5m', range: '24h', label: '24 Hours', description: '5-minute data for 24 hours' },
                { granularity: '5m', range: '7d', label: '7 Days', description: '5-minute data for 7 days' },
                { granularity: 'h', range: '31d', label: '31 Days', description: 'Hourly data for 31 days' },
                { granularity: 'd', range: '12m', label: '12 Months', description: 'Daily data for 12 months' }
            ],
            ...options
        };

        this.currentGranularity = this.options.defaultGranularity;
        this.currentRange = this.options.defaultRange;
        this.container = null;

        this.init();
    }

    /**
     * 初始化控制器
     */
    init() {
        if (this.options.container) {
            this.container = typeof this.options.container === 'string' 
                ? document.querySelector(this.options.container)
                : this.options.container;
        }

        if (this.container) {
            this.render();
            this.bindEvents();
        }
    }

    /**
     * 渲染控制器UI
     */
    render() {
        if (!this.container) return;

        const html = `
            <div class="granularity-controller">
                <div class="granularity-label">Time Range:</div>
                <div class="granularity-options">
                    ${this.options.availableOptions.map(option => `
                        <button 
                            class="granularity-option ${this.isActive(option) ? 'active' : ''}"
                            data-granularity="${option.granularity}"
                            data-range="${option.range}"
                            title="${option.description}"
                        >
                            ${option.label}
                        </button>
                    `).join('')}
                </div>
                <div class="granularity-info">
                    <span class="current-info">${this.getCurrentDescription()}</span>
                </div>
            </div>
        `;

        this.container.innerHTML = html;
        this.addStyles();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        if (!this.container) return;

        const options = this.container.querySelectorAll('.granularity-option');
        options.forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                this.selectOption(option);
            });
        });
    }

    /**
     * 选择颗粒度选项
     * @param {HTMLElement} optionElement - 被选择的选项元素
     */
    selectOption(optionElement) {
        const granularity = optionElement.dataset.granularity;
        const range = optionElement.dataset.range;

        // 更新状态
        this.currentGranularity = granularity;
        this.currentRange = range;

        // 更新UI
        this.container.querySelectorAll('.granularity-option').forEach(opt => {
            opt.classList.remove('active');
        });
        optionElement.classList.add('active');

        // 更新描述信息
        const infoElement = this.container.querySelector('.current-info');
        if (infoElement) {
            infoElement.textContent = this.getCurrentDescription();
        }

        // 触发回调
        if (this.options.onGranularityChange) {
            this.options.onGranularityChange({
                granularity,
                range,
                description: this.getCurrentDescription()
            });
        }

        // 触发自定义事件
        this.container.dispatchEvent(new CustomEvent('granularityChange', {
            detail: {
                granularity,
                range,
                description: this.getCurrentDescription()
            }
        }));
    }

    /**
     * 检查选项是否为当前活跃状态
     * @param {Object} option - 选项配置
     * @returns {boolean} 是否活跃
     */
    isActive(option) {
        return option.granularity === this.currentGranularity && 
               option.range === this.currentRange;
    }

    /**
     * 获取当前选项的描述
     * @returns {string} 描述文本
     */
    getCurrentDescription() {
        const current = this.options.availableOptions.find(opt => 
            opt.granularity === this.currentGranularity && 
            opt.range === this.currentRange
        );
        return current ? current.description : 'Custom granularity';
    }

    /**
     * 获取当前选择的颗粒度配置
     * @returns {Object} 当前配置
     */
    getCurrentConfig() {
        return {
            granularity: this.currentGranularity,
            range: this.currentRange,
            description: this.getCurrentDescription()
        };
    }

    /**
     * 程序化设置颗粒度
     * @param {string} granularity - 颗粒度
     * @param {string} range - 时间范围
     */
    setGranularity(granularity, range) {
        this.currentGranularity = granularity;
        this.currentRange = range;

        if (this.container) {
            // 更新UI状态
            this.container.querySelectorAll('.granularity-option').forEach(opt => {
                const isActive = opt.dataset.granularity === granularity && 
                                opt.dataset.range === range;
                opt.classList.toggle('active', isActive);
            });

            // 更新描述
            const infoElement = this.container.querySelector('.current-info');
            if (infoElement) {
                infoElement.textContent = this.getCurrentDescription();
            }
        }
    }

    /**
     * 添加组件样式
     */
    addStyles() {
        if (document.getElementById('granularity-controller-styles')) {
            return;
        }

        const styles = `
            <style id="granularity-controller-styles">
                .granularity-controller {
                    display: flex;
                    flex-direction: column;
                    gap: 12px;
                    padding: 16px;
                    background: var(--bg-secondary, #f9fafb);
                    border: 1px solid var(--border-color, #e5e7eb);
                    border-radius: 8px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                }

                .granularity-label {
                    font-size: 14px;
                    font-weight: 600;
                    color: var(--text-color, #1f2937);
                    margin: 0;
                }

                .granularity-options {
                    display: flex;
                    gap: 8px;
                    flex-wrap: wrap;
                }

                .granularity-option {
                    padding: 8px 16px;
                    font-size: 13px;
                    font-weight: 500;
                    border: 1px solid var(--border-color, #e5e7eb);
                    border-radius: 6px;
                    background: var(--bg-color, #ffffff);
                    color: var(--text-secondary, #6b7280);
                    cursor: pointer;
                    transition: all 0.2s ease;
                    white-space: nowrap;
                }

                .granularity-option:hover {
                    border-color: var(--primary-color, #3b82f6);
                    color: var(--primary-color, #3b82f6);
                    background: var(--primary-bg-light, #eff6ff);
                }

                .granularity-option.active {
                    background: var(--primary-color, #3b82f6);
                    border-color: var(--primary-color, #3b82f6);
                    color: white;
                    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                }

                .granularity-info {
                    font-size: 12px;
                    color: var(--text-tertiary, #9ca3af);
                    font-style: italic;
                }

                .current-info {
                    font-weight: 500;
                }

                /* Dark Theme Support */
                .dark .granularity-controller {
                    background: var(--bg-secondary, #374151);
                    border-color: var(--border-color, #4b5563);
                }

                .dark .granularity-option {
                    background: var(--bg-color, #1f2937);
                    border-color: var(--border-color, #4b5563);
                    color: var(--text-secondary, #d1d5db);
                }

                .dark .granularity-option:hover {
                    background: var(--bg-tertiary, #111827);
                    border-color: var(--primary-color, #3b82f6);
                }

                /* Responsive Design */
                @media (max-width: 768px) {
                    .granularity-controller {
                        padding: 12px;
                    }

                    .granularity-options {
                        gap: 6px;
                    }

                    .granularity-option {
                        padding: 6px 12px;
                        font-size: 12px;
                    }
                }

                @media (max-width: 480px) {
                    .granularity-options {
                        flex-direction: column;
                    }

                    .granularity-option {
                        text-align: center;
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    /**
     * 销毁控制器
     */
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
        }
    }

    /**
     * 获取API查询参数
     * @returns {Object} API查询参数
     */
    getApiParams() {
        return {
            type: this.currentGranularity,
            range: this.currentRange
        };
    }

    /**
     * 获取时间范围的毫秒数
     * @returns {number} 时间范围毫秒数
     */
    getRangeMilliseconds() {
        const rangeMappings = {
            '4h': 4 * 60 * 60 * 1000,
            '24h': 24 * 60 * 60 * 1000,
            '7d': 7 * 24 * 60 * 60 * 1000,
            '31d': 31 * 24 * 60 * 60 * 1000,
            '12m': 12 * 30 * 24 * 60 * 60 * 1000
        };

        return rangeMappings[this.currentRange] || 24 * 60 * 60 * 1000;
    }

    /**
     * 生成时间范围的开始和结束时间戳
     * @returns {Object} 包含start和end的时间戳对象
     */
    getTimeRange() {
        const now = Date.now();
        const rangeMs = this.getRangeMilliseconds();
        
        return {
            start: now - rangeMs,
            end: now,
            granularity: this.currentGranularity,
            range: this.currentRange
        };
    }
}

// 静态工厂方法
GranularityController.create = function(container, options = {}) {
    return new GranularityController({
        container,
        ...options
    });
};

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GranularityController;
}

// 全局暴露
window.GranularityController = GranularityController;