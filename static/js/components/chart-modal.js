/**
 * Chart Modal Component
 * 图表放大模态框组件
 */

class ChartModal {
    constructor() {
        this.isVisible = false;
        this.currentChart = null;
        this.currentChartInstance = null;
        this.overlay = null;
        this.granularityController = null;
        this.init();
    }

    /**
     * 初始化模态框
     */
    init() {
        this.createModalElements();
        this.bindEvents();
    }

    /**
     * 创建模态框DOM结构
     */
    createModalElements() {
        // 创建模态框覆盖层
        this.overlay = document.createElement('div');
        this.overlay.className = 'chart-modal-overlay';
        this.overlay.innerHTML = `
            <div class="chart-modal-container">
                <div class="chart-modal-header">
                    <h3 class="chart-modal-title">Chart Details</h3>
                    <div class="chart-modal-controls">
                        <div class="granularity-controls">
                            <span class="granularity-label">Data Granularity:</span>
                            <div class="granularity-buttons">
                                <button class="granularity-btn" data-granularity="m" data-range="4h">4H</button>
                                <button class="granularity-btn active" data-granularity="5m" data-range="24h">24H</button>
                                <button class="granularity-btn" data-granularity="5m" data-range="7d">7D</button>
                                <button class="granularity-btn" data-granularity="h" data-range="31d">31D</button>
                                <button class="granularity-btn" data-granularity="d" data-range="12m">12M</button>
                            </div>
                        </div>
                        <button class="chart-modal-close" aria-label="Close modal">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="chart-modal-body">
                    <div class="chart-modal-chart-container">
                        <div class="chart-modal-loading" style="display: none;">
                            <div class="spinner"></div>
                            <span>Loading chart data...</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(this.overlay);
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 点击覆盖层关闭模态框
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.close();
            }
        });

        // 关闭按钮
        const closeBtn = this.overlay.querySelector('.chart-modal-close');
        closeBtn.addEventListener('click', () => this.close());

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                this.close();
            }
        });

        // 颗粒度切换按钮
        const granularityBtns = this.overlay.querySelectorAll('.granularity-btn');
        granularityBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchGranularity(btn);
            });
        });

        // 窗口大小改变时重新调整图表
        window.addEventListener('resize', () => {
            if (this.isVisible && this.currentChartInstance) {
                this.resizeChart();
            }
        });

        // 监听主题变化事件
        document.addEventListener('theme:changed', () => {
            if (this.isVisible && this.currentChartInstance) {
                // 重新初始化图表以应用新主题
                this.updateChartTheme();
            }
        });
    }

    /**
     * 打开模态框显示图表
     * @param {Object} chartConfig - 图表配置
     * @param {string} title - 图表标题
     */
    open(chartConfig, title = 'Chart Details') {
        if (this.isVisible) {
            this.close();
        }

        this.currentChart = chartConfig;
        
        // 设置标题
        const titleElement = this.overlay.querySelector('.chart-modal-title');
        titleElement.textContent = title;

        // 显示模态框
        this.isVisible = true;
        this.overlay.classList.add('active');
        document.body.style.overflow = 'hidden';

        // 渲染图表
        this.renderChart();
    }

    /**
     * 关闭模态框
     */
    close() {
        if (!this.isVisible) return;

        this.isVisible = false;
        this.overlay.classList.remove('active');
        document.body.style.overflow = '';

        // 销毁当前图表实例
        if (this.currentChartInstance) {
            this.currentChartInstance.dispose();
            this.currentChartInstance = null;
        }

        this.currentChart = null;
    }

    /**
     * 渲染图表
     */
    async renderChart() {
        const container = this.overlay.querySelector('.chart-modal-chart-container');
        const loading = this.overlay.querySelector('.chart-modal-loading');
        
        // 显示加载状态
        loading.style.display = 'flex';

        try {
            // 清空容器
            container.innerHTML = '<div class="chart-modal-loading" style="display: flex;"><div class="spinner"></div><span>Loading chart data...</span></div>';
            
            // 创建图表容器
            const chartDiv = document.createElement('div');
            chartDiv.style.width = '100%';
            chartDiv.style.height = '100%';
            chartDiv.style.minHeight = '400px';
            chartDiv.style.position = 'relative';
            container.appendChild(chartDiv);

            // 等待ECharts加载
            if (typeof echarts === 'undefined') {
                await this.loadECharts();
            }

            // 确保容器有明确的尺寸
            const containerRect = container.getBoundingClientRect();
            if (containerRect.width > 0 && containerRect.height > 0) {
                chartDiv.style.width = containerRect.width + 'px';
                chartDiv.style.height = containerRect.height + 'px';
            }

            // 创建ECharts实例
            this.currentChartInstance = echarts.init(chartDiv, this.getTheme(), {
                renderer: 'canvas',
                useDirtyRect: false
            });

            // 获取当前活跃的颗粒度设置
            const activeBtn = this.overlay.querySelector('.granularity-btn.active');
            const granularity = activeBtn.dataset.granularity;
            const range = activeBtn.dataset.range;

            // 获取图表数据
            const chartData = await this.fetchChartData(granularity, range);
            
            // 合并配置
            const option = {
                ...this.currentChart,
                ...chartData,
                animation: true,
                animationDuration: 1000,
                grid: {
                    top: 60,
                    left: 60,
                    right: 60,
                    bottom: 60,
                    containLabel: true
                },
                toolbox: {
                    show: true,
                    feature: {
                        saveAsImage: {
                            show: true,
                            title: 'Save as Image'
                        },
                        dataZoom: {
                            show: true,
                            title: {
                                zoom: 'Zoom',
                                back: 'Reset Zoom'
                            }
                        }
                    },
                    right: 20,
                    top: 20
                }
            };

            // 设置图表选项
            this.currentChartInstance.setOption(option, true);

            // 确保图表完全适应容器
            setTimeout(() => {
                this.resizeChart();
            }, 200);

            // 隐藏加载状态
            loading.style.display = 'none';

        } catch (error) {
            console.error('Error rendering chart:', error);
            loading.innerHTML = `
                <div style="color: #ef4444;">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                    <span>Failed to load chart data</span>
                </div>
            `;
        }
    }

    /**
     * 切换数据颗粒度
     * @param {HTMLElement} btn - 被点击的按钮
     */
    async switchGranularity(btn) {
        // 更新按钮状态
        this.overlay.querySelectorAll('.granularity-btn').forEach(b => {
            b.classList.remove('active');
        });
        btn.classList.add('active');

        // 重新渲染图表
        await this.renderChart();
    }

    /**
     * 获取图表数据
     * @param {string} granularity - 数据颗粒度
     * @param {string} range - 时间范围
     * @returns {Promise<Object>} 图表数据
     */
    async fetchChartData(granularity, range) {
        try {
            // 如果是网络质量图表，使用专门的API
            if (this.currentChart.chartType === 'tcping' || this.currentChart.chartType === 'network-quality') {
                return await this.fetchNetworkQualityData(granularity, range);
            }

            // 如果原始图表有serverId，使用它获取数据
            if (this.currentChart.serverId) {
                const url = `/api/monitor/targets/${this.currentChart.serverId}/data?type=${granularity}&range=${range}`;
                console.log(`[Chart Modal] 获取图表数据: ${url}`);
                
                const response = await fetch(url);
                if (response.ok) {
                    const apiData = await response.json();
                    if (apiData.success && apiData.data) {
                        return this.transformApiDataToChart(apiData, granularity, range);
                    } else {
                        console.warn('[Chart Modal] API响应无数据:', apiData);
                        return {};
                    }
                } else {
                    console.error('[Chart Modal] API请求失败:', response.status, response.statusText);
                    return {};
                }
            }

            // 如果没有serverId，尝试使用原始图表数据
            if (this.currentChart.series && this.currentChart.xAxis) {
                console.log('[Chart Modal] 使用原始图表数据');
                return {
                    xAxis: this.currentChart.xAxis,
                    series: this.currentChart.series
                };
            }

            // 否则返回空数据
            console.warn('[Chart Modal] 无可用数据源');
            return {};
        } catch (error) {
            console.error('[Chart Modal] 获取图表数据失败:', error);
            return {};
        }
    }

    /**
     * 获取网络质量图表数据
     * @param {string} granularity - 数据颗粒度
     * @param {string} range - 时间范围
     * @returns {Promise<Object>} 图表数据
     */
    async fetchNetworkQualityData(granularity, range) {
        try {
            // 将图表模态框的range参数转换为network-quality API的timeRange参数
            const timeRangeMap = {
                '4h': '6h',    // 用6h替代4h
                '24h': '24h',
                '7d': '7d',
                '31d': '30d',  // 用30d替代31d
                '12m': '30d'   // 用30d替代12m
            };
            
            const timeRange = timeRangeMap[range] || '24h';
            
            // 如果有targetId或serverId，获取单个目标的详细数据
            const targetId = this.currentChart.targetId || this.currentChart.serverId;
            if (targetId) {
                const url = `/api/network-quality/target/${targetId}?timeRange=${timeRange}&granularity=${granularity}`;
                console.log(`[Chart Modal] 获取网络质量数据: ${url}`);
                
                const response = await fetch(url);
                if (response.ok) {
                    const apiData = await response.json();
                    if (apiData.success && apiData.data) {
                        return this.transformNetworkQualityData(apiData.data, granularity, range);
                    } else {
                        console.warn('[Chart Modal] Network Quality API响应无数据:', apiData);
                        return {};
                    }
                } else {
                    console.error('[Chart Modal] Network Quality API请求失败:', response.status, response.statusText);
                    return {};
                }
            } else {
                // 获取概览数据
                const url = `/api/network-quality/overview?timeRange=${timeRange}`;
                console.log(`[Chart Modal] 获取网络质量概览数据: ${url}`);
                
                const response = await fetch(url);
                if (response.ok) {
                    const apiData = await response.json();
                    if (apiData.success && apiData.data) {
                        return this.transformNetworkQualityOverviewData(apiData.data, granularity, range);
                    } else {
                        console.warn('[Chart Modal] Network Quality Overview API响应无数据:', apiData);
                        return {};
                    }
                } else {
                    console.error('[Chart Modal] Network Quality Overview API请求失败:', response.status, response.statusText);
                    return {};
                }
            }
        } catch (error) {
            console.error('[Chart Modal] 获取网络质量数据失败:', error);
            return {};
        }
    }

    /**
     * 转换网络质量数据为图表格式
     * @param {Object} data - Network Quality API返回的数据
     * @param {string} granularity - 数据颗粒度
     * @param {string} range - 时间范围
     * @returns {Object} 图表配置数据
     */
    transformNetworkQualityData(data, granularity, range) {
        if (!data.chartData) {
            console.warn('[Chart Modal] 网络质量数据中无chartData');
            return {};
        }

        const chartData = data.chartData;
        const times = chartData.times || [];
        const latencies = chartData.latencies || [];
        const packetLoss = chartData.packetLoss || [];

        return {
            xAxis: {
                type: 'time',
                data: times.map(time => new Date(time).getTime())
            },
            series: [
                {
                    name: 'Average Latency',
                    type: 'line',
                    data: times.map((time, index) => [new Date(time).getTime(), latencies[index]]),
                    smooth: true,
                    color: '#10b981',
                    yAxisIndex: 0
                },
                {
                    name: 'Packet Loss',
                    type: 'line',
                    data: times.map((time, index) => [new Date(time).getTime(), packetLoss[index]]),
                    smooth: true,
                    color: '#ef4444',
                    yAxisIndex: 1
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: 'Latency (ms)',
                    position: 'left',
                    axisLabel: {
                        formatter: '{value} ms'
                    }
                },
                {
                    type: 'value',
                    name: 'Packet Loss (%)',
                    position: 'right',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                }
            ]
        };
    }

    /**
     * 转换网络质量概览数据为图表格式
     * @param {Object} data - Network Quality Overview API返回的数据
     * @param {string} granularity - 数据颗粒度
     * @param {string} range - 时间范围
     * @returns {Object} 图表配置数据
     */
    transformNetworkQualityOverviewData(data, granularity, range) {
        if (!data.targets || !Array.isArray(data.targets)) {
            console.warn('[Chart Modal] 网络质量概览数据中无targets');
            return {};
        }

        // 聚合所有目标的数据
        const allTimes = [];
        const timeIndexMap = new Map();
        
        // 收集所有时间点
        data.targets.forEach(target => {
            if (target.chartData && target.chartData.times) {
                target.chartData.times.forEach(time => {
                    const timestamp = new Date(time).getTime();
                    if (!timeIndexMap.has(timestamp)) {
                        timeIndexMap.set(timestamp, allTimes.length);
                        allTimes.push(timestamp);
                    }
                });
            }
        });

        allTimes.sort((a, b) => a - b);

        // 计算平均延迟和丢包率
        const avgLatencies = new Array(allTimes.length).fill(0);
        const avgPacketLoss = new Array(allTimes.length).fill(0);
        const counts = new Array(allTimes.length).fill(0);

        data.targets.forEach(target => {
            if (target.chartData && target.chartData.times) {
                target.chartData.times.forEach((time, index) => {
                    const timestamp = new Date(time).getTime();
                    const timeIndex = timeIndexMap.get(timestamp);
                    if (timeIndex !== undefined) {
                        const latency = target.chartData.latencies[index];
                        const packetLoss = target.chartData.packetLoss[index];
                        
                        if (latency !== null && latency !== undefined) {
                            avgLatencies[timeIndex] += latency;
                            avgPacketLoss[timeIndex] += packetLoss || 0;
                            counts[timeIndex]++;
                        }
                    }
                });
            }
        });

        // 计算平均值
        for (let i = 0; i < allTimes.length; i++) {
            if (counts[i] > 0) {
                avgLatencies[i] = Math.round(avgLatencies[i] / counts[i]);
                avgPacketLoss[i] = Math.round(avgPacketLoss[i] / counts[i]);
            }
        }

        return {
            xAxis: {
                type: 'time',
                data: allTimes
            },
            series: [
                {
                    name: 'Average Latency (All Targets)',
                    type: 'line',
                    data: allTimes.map((time, index) => [time, avgLatencies[index]]),
                    smooth: true,
                    color: '#10b981',
                    yAxisIndex: 0
                },
                {
                    name: 'Average Packet Loss (All Targets)',
                    type: 'line',
                    data: allTimes.map((time, index) => [time, avgPacketLoss[index]]),
                    smooth: true,
                    color: '#ef4444',
                    yAxisIndex: 1
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: 'Latency (ms)',
                    position: 'left',
                    axisLabel: {
                        formatter: '{value} ms'
                    }
                },
                {
                    type: 'value',
                    name: 'Packet Loss (%)',
                    position: 'right',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                }
            ]
        };
    }

    /**
     * 转换API数据为图表格式
     * @param {Object} apiData - API返回的数据
     * @param {string} granularity - 数据颗粒度
     * @param {string} range - 时间范围
     * @returns {Object} 图表配置数据
     */
    transformApiDataToChart(apiData, granularity, range) {
        if (!apiData.success || !apiData.data) {
            console.warn('[Chart Modal] API数据无效:', apiData);
            return {};
        }

        const responseData = apiData.data;
        let statsData = [];
        
        // 处理不同的API响应格式
        if (responseData.stats) {
            // 格式: { target: {...}, stats: [...], nodes: {...} }
            statsData = Array.isArray(responseData.stats) ? responseData.stats : [];
        } else if (Array.isArray(responseData)) {
            // 格式: [...]
            statsData = responseData;
        } else {
            console.warn('[Chart Modal] 未知的API数据格式:', responseData);
            return {};
        }

        if (!statsData.length) {
            console.warn('[Chart Modal] 无统计数据');
            return {};
        }

        // 提取时间戳和格式化数据
        const processedData = statsData.map(item => ({
            timestamp: item.created_at ? item.created_at * 1000 : new Date(item.timestamp || Date.now()).getTime(),
            ...item
        })).sort((a, b) => a.timestamp - b.timestamp);

        const timestamps = processedData.map(item => item.timestamp);
        
        // 根据图表类型生成配置
        const chartType = this.currentChart.chartType || 'default';
        
        if (chartType === 'traffic' || chartType.includes('bandwidth')) {
            return {
                xAxis: {
                    type: 'time',
                    data: timestamps
                },
                series: [
                    {
                        name: 'Upload',
                        type: 'line',
                        data: processedData.map(item => [item.timestamp, this.extractValue(item, 'upload', 'up', 'tx')]),
                        smooth: true,
                        color: '#ef4444'
                    },
                    {
                        name: 'Download', 
                        type: 'line',
                        data: processedData.map(item => [item.timestamp, this.extractValue(item, 'download', 'down', 'rx')]),
                        smooth: true,
                        color: '#3b82f6'
                    }
                ]
            };
        } else if (chartType === 'tcping' || chartType.includes('network-quality')) {
            return {
                xAxis: {
                    type: 'time',
                    data: timestamps
                },
                series: [
                    {
                        name: 'Average Latency',
                        type: 'line',
                        data: processedData.map(item => [item.timestamp, this.extractValue(item, 'avg_time', 'avg_latency', 'latency')]),
                        smooth: true,
                        color: '#10b981'
                    },
                    {
                        name: 'Success Rate',
                        type: 'line',
                        yAxisIndex: 1,
                        data: processedData.map(item => [item.timestamp, this.extractValue(item, 'success_rate') * 100]),
                        smooth: true,
                        color: '#f59e0b'
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: 'Latency (ms)',
                        position: 'left'
                    },
                    {
                        type: 'value',
                        name: 'Success Rate (%)',
                        position: 'right',
                        min: 0,
                        max: 100
                    }
                ]
            };
        } else if (chartType === 'load' || chartType.includes('system')) {
            return {
                xAxis: {
                    type: 'time',
                    data: timestamps
                },
                series: [
                    {
                        name: 'CPU Usage',
                        type: 'line',
                        data: processedData.map(item => [item.timestamp, this.extractValue(item, 'cpu', 'cpu_usage') * 100]),
                        smooth: true,
                        color: '#8b5cf6'
                    },
                    {
                        name: 'Memory Usage',
                        type: 'line',
                        data: processedData.map(item => [item.timestamp, this.extractValue(item, 'memory', 'mem_usage') * 100]),
                        smooth: true,
                        color: '#06b6d4'
                    }
                ]
            };
        }

        // 默认处理
        console.log('[Chart Modal] 使用默认图表格式');
        return {
            xAxis: {
                type: 'time',
                data: timestamps
            },
            series: [
                {
                    name: 'Data',
                    type: 'line',
                    data: processedData.map(item => [item.timestamp, Object.values(item).find(v => typeof v === 'number') || 0]),
                    smooth: true
                }
            ]
        };
    }

    /**
     * 从数据项中提取值
     * @param {Object} item - 数据项
     * @param {...string} keys - 可能的键名
     * @returns {number} 提取的数值
     */
    extractValue(item, ...keys) {
        for (const key of keys) {
            if (item[key] !== undefined && item[key] !== null) {
                const value = parseFloat(item[key]);
                return isNaN(value) ? 0 : value;
            }
        }
        return 0;
    }

    /**
     * 加载ECharts库
     * @returns {Promise} 加载Promise
     */
    loadECharts() {
        return new Promise((resolve, reject) => {
            if (typeof echarts !== 'undefined') {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = '/js/libs/echarts.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    /**
     * 调整图表大小以适应容器
     */
    resizeChart() {
        if (!this.currentChartInstance || this.currentChartInstance.isDisposed()) {
            return;
        }

        // 延迟调整确保容器尺寸变化完成
        setTimeout(() => {
            if (this.currentChartInstance && !this.currentChartInstance.isDisposed()) {
                try {
                    this.currentChartInstance.resize();
                    console.log('[Chart Modal] 图表大小已自适应调整');
                } catch (error) {
                    console.error('[Chart Modal] 图表大小调整失败:', error);
                }
            }
        }, 100);
    }

    /**
     * 获取当前主题
     * @returns {string} 主题名称
     */
    getTheme() {
        // 统一主题检测逻辑，与项目中其他组件保持一致
        const isDark = document.documentElement.classList.contains('dark');
        return isDark ? 'dark' : null;
    }

    /**
     * 更新图表主题
     */
    updateChartTheme() {
        if (!this.currentChartInstance || this.currentChartInstance.isDisposed()) {
            return;
        }

        try {
            // 销毁当前图表实例
            this.currentChartInstance.dispose();

            // 重新创建图表实例以应用新主题
            const container = this.overlay.querySelector('.chart-modal-chart-container');
            const chartDiv = container.querySelector('div');

            if (chartDiv) {
                this.currentChartInstance = echarts.init(chartDiv, this.getTheme(), {
                    renderer: 'canvas',
                    useDirtyRect: false
                });

                // 重新设置图表配置
                if (this.currentChart) {
                    // 获取当前活跃的颗粒度设置
                    const activeBtn = this.overlay.querySelector('.granularity-btn.active');
                    const granularity = activeBtn.dataset.granularity;
                    const range = activeBtn.dataset.range;

                    // 重新获取图表数据并应用
                    this.fetchChartData(granularity, range).then(chartData => {
                        const option = {
                            ...this.currentChart,
                            ...chartData,
                            animation: false, // 主题切换时禁用动画
                            grid: {
                                top: 60,
                                left: 60,
                                right: 60,
                                bottom: 60,
                                containLabel: true
                            },
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {
                                        show: true,
                                        title: 'Save as Image'
                                    },
                                    dataZoom: {
                                        show: true,
                                        title: {
                                            zoom: 'Zoom',
                                            back: 'Reset Zoom'
                                        }
                                    }
                                },
                                right: 20,
                                top: 20
                            }
                        };

                        this.currentChartInstance.setOption(option, true);
                        console.log('[Chart Modal] 图表主题已更新');
                    }).catch(error => {
                        console.error('[Chart Modal] 更新图表主题时获取数据失败:', error);
                    });
                }
            }
        } catch (error) {
            console.error('[Chart Modal] 更新图表主题失败:', error);
        }
    }
}

// 创建全局实例
window.chartModal = new ChartModal();

// 为ECharts实例添加全屏图标放大功能
window.addChartEnlargeCapability = function(chartInstance, chartConfig, title) {
    if (!chartInstance || !chartConfig) {
        console.warn('Invalid chart instance or config for enlarge capability');
        return;
    }

    // 移除图表的点击放大功能，只保留全屏图标触发
    const chartDom = chartInstance.getDom();
    if (chartDom) {
        // 移除鼠标悬停提示
        chartDom.style.cursor = 'default';
        chartDom.title = '';
        
        // 添加全屏图标
        addFullscreenIcon(chartDom, chartConfig, title);
    }
};

// 添加全屏图标到图表容器
function addFullscreenIcon(chartContainer, chartConfig, title) {
    // 检查是否已经添加了全屏图标
    if (chartContainer.querySelector('.chart-fullscreen-icon')) {
        return;
    }
    
    // 确保图表容器有相对定位
    const containerStyle = window.getComputedStyle(chartContainer);
    if (containerStyle.position === 'static') {
        chartContainer.style.position = 'relative';
    }
    
    // 创建全屏图标
    const fullscreenIcon = document.createElement('div');
    fullscreenIcon.className = 'chart-fullscreen-icon';
    fullscreenIcon.innerHTML = `
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3M21 16v3a2 2 0 0 1-2 2h-3M3 16v3a2 2 0 0 0 2 2h3"/>
        </svg>
    `;
    fullscreenIcon.title = 'Click to enlarge chart';
    
    // 设置图标样式
    Object.assign(fullscreenIcon.style, {
        position: 'absolute',
        top: '8px',
        right: '8px',
        width: '24px',
        height: '24px',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderRadius: '4px',
        padding: '4px',
        cursor: 'pointer',
        zIndex: '1000',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#64748b',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
    });
    
    // 暗色主题适配
    const isDarkMode = document.documentElement.classList.contains('dark');
    if (isDarkMode) {
        fullscreenIcon.style.backgroundColor = 'rgba(30, 41, 59, 0.9)';
        fullscreenIcon.style.color = '#e2e8f0';
    }
    
    // 添加悬停效果
    fullscreenIcon.addEventListener('mouseenter', () => {
        fullscreenIcon.style.backgroundColor = isDarkMode ? 'rgba(51, 65, 85, 0.9)' : 'rgba(248, 250, 252, 0.9)';
        fullscreenIcon.style.transform = 'scale(1.1)';
    });
    
    fullscreenIcon.addEventListener('mouseleave', () => {
        fullscreenIcon.style.backgroundColor = isDarkMode ? 'rgba(30, 41, 59, 0.9)' : 'rgba(255, 255, 255, 0.9)';
        fullscreenIcon.style.transform = 'scale(1)';
    });
    
    // 添加点击事件
    fullscreenIcon.addEventListener('click', (e) => {
        e.stopPropagation();
        e.preventDefault();
        
        // 打开模态框
        window.chartModal.open({
            ...chartConfig,
            serverId: chartConfig.serverId,
            chartType: chartConfig.chartType
        }, title);
    });
    
    // 添加到图表容器
    chartContainer.appendChild(fullscreenIcon);
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChartModal;
}