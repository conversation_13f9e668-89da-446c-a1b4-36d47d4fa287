/**
 * 网络监控配置 - 组织架构图风格
 * 分类 → 目标 → 节点 的层级架构展示
 * T006: 监控界面优化版本
 */
class MonitorMindMapApp {
    constructor() {
        this.categories = [];
        this.targets = [];
        this.nodes = [];
        this.nodeStatus = {};
        this.groups = []; // 节点分组数据
        this.expandedCategories = new Set();
        this.expandedTargets = new Set();
        this.selectedNodes = new Map(); // targetId -> Set of nodeIds
        this.currentActiveCategory = null;
        this.currentActiveTarget = null;
        this.expandedGroups = new Set(); // 展开的分组

        // T006: 性能优化相关属性
        this.isLoading = false;
        this.lastRenderTime = 0;
        this.renderThrottle = 100; // 渲染节流时间(ms)
        this.updateInterval = null;
        this.retryCount = 0;
        this.maxRetries = 3;
        this.connectionStatus = 'connected';

        // T006: 缓存DOM元素
        this.domCache = {
            container: null,
            refreshBtn: null,
            statsInfo: null,
            loadingOverlay: null
        };

        this.init();
    }

    async init() {
        try {
            // T006: 缓存DOM元素
            this.cacheDOMElements();

            // T006: 显示初始加载状态
            this.showLoadingState(true, '正在初始化...');

            this.bindEvents();
            await this.loadData();
            this.renderOrgChart();

            // T006: 启动自动更新
            this.startAutoUpdate();

            this.showLoadingState(false);
            this.updateConnectionStatus('connected');
        } catch (error) {
            console.error('初始化失败:', error);
            this.showLoadingState(false);
            this.showErrorState('初始化失败，请刷新页面重试');
            this.updateConnectionStatus('error');
        }
    }

    /**
     * T006: 缓存DOM元素以提高性能
     */
    cacheDOMElements() {
        this.domCache.container = document.getElementById('mindmap-container');
        this.domCache.refreshBtn = document.getElementById('btn-refresh');
        this.domCache.statsInfo = document.getElementById('stats-info');

        // 创建加载遮罩层
        if (!this.domCache.loadingOverlay) {
            this.createLoadingOverlay();
        }
    }

    /**
     * T006: 创建加载遮罩层
     */
    createLoadingOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'monitor-loading-overlay';
        overlay.className = 'monitor-loading-overlay hidden';
        overlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">加载中...</div>
            </div>
        `;

        document.body.appendChild(overlay);
        this.domCache.loadingOverlay = overlay;
    }

    bindEvents() {
        // T006: 刷新按钮 - 添加防抖和错误处理
        if (this.domCache.refreshBtn) {
            this.domCache.refreshBtn.addEventListener('click', this.debounce(async () => {
                try {
                    this.showLoadingState(true, '正在刷新数据...');
                    await this.loadData();
                    this.renderOrgChart();
                    this.showLoadingState(false);
                    this.showNotification('数据已刷新', 'success');
                    this.updateConnectionStatus('connected');
                    this.retryCount = 0; // 重置重试计数
                } catch (error) {
                    console.error('刷新数据失败:', error);
                    this.showLoadingState(false);
                    this.showNotification('刷新失败，请稍后重试', 'error');
                    this.updateConnectionStatus('error');
                }
            }, 1000));
        }

        // 新增分类按钮
        document.getElementById('btn-add-category').addEventListener('click', () => {
            this.openCategoryModal();
        });

        // 新增目标按钮
        document.getElementById('btn-add-target').addEventListener('click', () => {
            if (this.categories.length === 0) {
                showNotification('请先创建监控分类', 'warning');
                return;
            }
            this.openTargetModal();
        });

        // 模态框事件
        this.bindModalEvents();
    }

    bindModalEvents() {
        // 分类模态框
        const categoryModal = document.getElementById('category-modal');
        const categoryBackdrop = document.getElementById('category-modal-backdrop');
        const categoryCancel = document.getElementById('category-cancel');
        const categoryForm = document.getElementById('category-form');

        categoryBackdrop.addEventListener('click', () => this.closeCategoryModal());
        categoryCancel.addEventListener('click', () => this.closeCategoryModal());
        categoryForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.saveCategoryForm();
        });

        // 目标模态框
        const targetModal = document.getElementById('target-modal');
        const targetBackdrop = document.getElementById('target-modal-backdrop');
        const targetCancel = document.getElementById('target-cancel');
        const targetForm = document.getElementById('target-form');

        targetBackdrop.addEventListener('click', () => this.closeTargetModal());
        targetCancel.addEventListener('click', () => this.closeTargetModal());
        targetForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.saveTargetForm();
        });

        // 检测类型切换
        const radioButtons = targetForm.querySelectorAll('input[name="target-test-type"]');
        radioButtons.forEach(radio => {
            radio.addEventListener('change', () => {
                const portField = document.getElementById('target-port-field');
                portField.style.display = radio.value === 'tcping' ? 'block' : 'none';
            });
        });
    }

    async loadData() {
        // T006: 防止重复加载
        if (this.isLoading) {
            console.log('数据正在加载中，跳过重复请求');
            return;
        }

        this.isLoading = true;

        try {
            // T006: 添加超时控制
            const timeout = 15000; // 15秒超时
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);

            const [regionsRes, targetsRes, nodesRes, statusRes, serversRes, groupsRes] = await Promise.all([
                fetch('/api/admin/monitor/regions', { signal: controller.signal, credentials: 'same-origin' }),
                fetch('/api/admin/monitor/targets', { signal: controller.signal, credentials: 'same-origin' }),
                fetch('/api/monitor/nodes', { signal: controller.signal }),
                fetch('/api/allnode_status', { signal: controller.signal }),
                fetch('/api/servers', { signal: controller.signal }), // 获取服务器数据
                fetch('/api/groups', { signal: controller.signal })   // 获取分组数据
            ]);

            clearTimeout(timeoutId);

            // T006: 检查响应状态
            const responses = [regionsRes, targetsRes, nodesRes, statusRes, serversRes, groupsRes];
            const failedRequests = responses.filter(res => !res.ok);

            if (failedRequests.length > 0) {
                console.warn(`${failedRequests.length} 个API请求失败`);
                this.updateConnectionStatus('warning');
            }

            const regionsData = regionsRes.ok ? await regionsRes.json() : { success: false, data: [] };
            const targetsData = targetsRes.ok ? await targetsRes.json() : { success: false, data: [] };
            const nodesData = nodesRes.ok ? await nodesRes.json() : { success: false, data: [] };
            const statusData = statusRes.ok ? await statusRes.json() : { success: false, data: {} };
            const serversData = serversRes.ok ? await serversRes.json() : { success: false, data: [] };
            const groupsData = groupsRes.ok ? await groupsRes.json() : { success: false, data: [] };

            this.categories = regionsData.success ? regionsData.data : [];
            this.targets = targetsData.success ? targetsData.data : [];
            this.nodes = nodesData.success ? nodesData.data : [];
            this.nodeStatus = statusData.success ? statusData.data : {};

            // 处理分组数据 - 使用正确的API数据
            this.processGroupData(
                serversData.success ? serversData.data : [],
                groupsData.success ? groupsData.data : []
            );

            // 加载已选择的节点配置
            this.loadSelectedNodes();
            this.updateStatsInfo();

            // T006: 重置重试计数
            this.retryCount = 0;
            this.updateConnectionStatus('connected');

        } catch (error) {
            console.error('加载数据失败:', error);

            // T006: 实现重试机制
            if (this.retryCount < this.maxRetries && !error.name === 'AbortError') {
                this.retryCount++;
                console.log(`正在重试 (${this.retryCount}/${this.maxRetries})...`);
                this.updateConnectionStatus('retrying');

                // 延迟重试
                setTimeout(() => {
                    this.loadData();
                }, 2000 * this.retryCount); // 递增延迟

                return;
            }

            // T006: 重试失败或超时，显示错误状态
            this.showNotification('加载数据失败，请检查网络连接', 'error');
            this.updateConnectionStatus('error');

            // T006: 设置默认空数据
            this.categories = [];
            this.targets = [];
            this.nodes = [];
            this.nodeStatus = {};
            this.groups = [];
        } finally {
            this.isLoading = false;
        }
    }

    processGroupData(serversData, groupsData) {
        // 建立分组ID到分组名称的映射
        const groupIdToName = new Map();
        groupsData.forEach(group => {
            groupIdToName.set(group.id, group.name);
        });

        // 从服务器数据中提取分组信息，并为每个节点关联分组
        const groupMap = new Map();

        // 为节点添加分组信息
        this.nodes.forEach(node => {
            const server = serversData.find(s => s.id === node.id || s.sid === node.id);
            let groupId = 'default';
            let groupName = '未分组';

            if (server && server.group_id) {
                // 从服务器数据中获取分组ID
                groupId = server.group_id;
                groupName = groupIdToName.get(groupId) || '未分组';
            }

            node.groupId = groupId;
            node.groupName = groupName;
            
            if (!groupMap.has(groupId)) {
                groupMap.set(groupId, {
                    id: groupId,
                    name: groupName,
                    nodes: [],
                    onlineCount: 0,
                    totalCount: 0
                });
            }

            const group = groupMap.get(groupId);
            group.nodes.push(node);
            group.totalCount++;

            // 检查节点是否在线
            const status = this.getNodeStatus(node.id);
            if (status.online) {
                group.onlineCount++;
            }
        });

        this.groups = Array.from(groupMap.values());
        console.log('分组数据处理完成:', this.groups);
    }

    loadSelectedNodes() {
        this.selectedNodes.clear();

        this.targets.forEach(target => {
            if (target.node_id) {
                let nodeIds = [];

                if (typeof target.node_id === 'string') {
                    try {
                        nodeIds = JSON.parse(target.node_id);
                    } catch {
                        nodeIds = [target.node_id];
                    }
                } else if (Array.isArray(target.node_id)) {
                    nodeIds = target.node_id;
                } else {
                    nodeIds = [target.node_id];
                }

                this.selectedNodes.set(target.id, new Set(nodeIds));
            }
        });
    }

    updateStatsInfo() {
        const statsInfo = document.getElementById('stats-info');
        const categoryCount = this.categories.length;
        const targetCount = this.targets.length;
        const nodeCount = this.nodes.length;

        statsInfo.textContent = `${categoryCount} 个分类, ${targetCount} 个目标, ${nodeCount} 个节点`;
    }

    getNodeStatus(nodeId) {
        const status = this.nodeStatus[nodeId];
        if (!status) return { online: false, text: '未知' };

        // 检查是否离线
        if (status.stat === false || (status.stat && status.stat.offline)) {
            return { online: false, text: '离线' };
        }

        return { online: true, text: '在线' };
    }

    getNodeCountryCode(node) {
        // 优先从节点状态数据中获取
        const status = this.nodeStatus[node.id];
        if (status && status.data && status.data.location && status.data.location.code) {
            return status.data.location.code;
        }

        // 从节点基础数据中获取
        if (node.data && node.data.location && node.data.location.code) {
            return node.data.location.code;
        }

        // 如果都没有，返回null使用默认图标
        return null;
    }

    renderOrgChart() {
        // T006: 渲染节流优化
        const now = Date.now();
        if (now - this.lastRenderTime < this.renderThrottle) {
            return;
        }
        this.lastRenderTime = now;

        const container = this.domCache.container || document.getElementById('mindmap-container');

        if (this.categories.length === 0) {
            container.innerHTML = `
                <div class="text-center py-16">
                    <i class="ti ti-category"></i>
                    <h3 class="text-lg font-medium text-slate-800 dark:text-slate-200 mb-2">暂无监控分类</h3>
                    <p class="text-sm text-slate-500 dark:text-slate-400">点击"新增分类"开始配置监控结构</p>
                </div>
            `;
            return;
        }

        let html = '';

        // 第一层：分类层级
        html += `<div class="flex flex-wrap justify-center gap-2 sm:gap-3 md:gap-4 w-full max-w-6xl">`;
        this.categories.forEach(category => {
            const categoryTargets = this.targets.filter(t => t.region_id === category.id);
            const isExpanded = this.expandedCategories.has(category.id);

            html += `
                <div class="flex flex-col items-center">
                    <div class="relative group cursor-pointer ${isExpanded ? 'card-enter' : ''}" data-category-id="${category.id}">
                        <div class="category-card ${isExpanded ? 'expanded' : ''}">
                            <div class="category-content">
                                <i class="ti ti-folder"></i>
                                <h3 class="category-title">${this.escapeHtml(category.name)}</h3>
                                <p class="category-count">${categoryTargets.length} 个目标</p>
                            </div>

                            <div class="expand-indicator ${isExpanded ? 'expanded' : ''}">
                                <i class="ti ti-chevron-down"></i>
                            </div>

                            <div class="category-actions">
                                <button class="action-btn" onclick="app.editCategory('${category.id}')" title="编辑">
                                    <i class="ti ti-edit"></i>
                                </button>
                                <button class="action-btn" onclick="app.deleteCategory('${category.id}')" title="删除">
                                    <i class="ti ti-trash"></i>
                                </button>
                                <button class="action-btn" onclick="app.addTargetToCategory('${category.id}')" title="添加">
                                    <i class="ti ti-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        html += `</div>`;

        // 连接线
        const activeTargets = this.getActiveTargets();
        if (activeTargets.length > 0) {
            html += `<div class="level-connector rounded-full"></div>`;
        }

        // 第二层：目标层级
        if (activeTargets.length > 0) {
            html += `<div class="flex flex-wrap justify-center gap-2 sm:gap-3 md:gap-4 w-full max-w-5xl card-enter">`;
            activeTargets.forEach(target => {
                const isExpanded = this.expandedTargets.has(target.id);
                const targetNodes = this.selectedNodes.get(target.id) || new Set();

                html += `
                    <div class="flex flex-col items-center">
                        <div class="relative group cursor-pointer ${isExpanded ? 'card-enter' : ''}" data-target-id="${target.id}">
                            <div class="target-card ${isExpanded ? 'expanded' : 'collapsed'}">
                                <div class="target-content">
                                    <h4 class="target-title">${this.escapeHtml(target.name)}</h4>
                                    <p class="target-host">${this.escapeHtml(target.host)}${target.port ? ':' + target.port : ''}</p>
                                    <span class="target-count ${isExpanded ? 'expanded' : 'collapsed'}">${targetNodes.size}/${this.nodes.length}</span>
                                </div>

                                <div class="target-expand-indicator ${isExpanded ? 'expanded' : ''}">
                                    <i class="ti text-xs ${isExpanded ? 'ti-chevron-up' : 'ti-chevron-down'}"></i>
                                </div>

                                <div class="target-actions">
                                    <button class="target-action-btn" onclick="app.editTarget('${target.id}')" title="编辑">
                                        <i class="ti ti-edit"></i>
                                    </button>
                                    <button class="target-action-btn" onclick="app.deleteTarget('${target.id}')" title="删除">
                                        <i class="ti ti-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += `</div>`;
        }

        // 连接线到节点
        if (this.currentActiveTarget) {
            html += `<div class="target-connector rounded-full"></div>`;
        }

        // 第三层：节点层级（按分组显示）
        if (this.currentActiveTarget) {
            // 添加批量操作工具栏
            html += `
                <div class="w-full max-w-4xl mb-4">
                    <div class="flex flex-wrap items-center justify-between gap-3 p-4 bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700">
                        <div class="flex items-center gap-2">
                            <i class="ti ti-list-check"></i>
                            <span class="text-sm font-medium text-slate-700 dark:text-slate-300">批量操作</span>
                        </div>
                        <div class="flex gap-2">
                            <button class="batch-btn" onclick="app.selectAllNodes('${this.currentActiveTarget}')" title="全选">
                                <i class="ti ti-select-all"></i>
                                <span class="hidden sm:inline">全选</span>
                            </button>
                            <button class="batch-btn" onclick="app.deselectAllNodes('${this.currentActiveTarget}')" title="反选">
                                <i class="ti ti-square-off"></i>
                                <span class="hidden sm:inline">反选</span>
                            </button>
                            <button class="batch-btn batch-btn-primary" onclick="app.selectOnlineNodes('${this.currentActiveTarget}')" title="选择在线节点">
                                <i class="ti ti-wifi"></i>
                                <span class="hidden sm:inline">仅在线</span>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // 按分组渲染节点
            html += `<div class="w-full max-w-4xl space-y-4 card-enter">`;
            
            this.groups.forEach(group => {
                const isGroupExpanded = this.expandedGroups.has(group.name);
                const groupNodes = group.nodes || [];

                html += `
                    <div class="group-container">
                        <div class="group-header">
                            <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg border border-slate-200 dark:border-slate-600">
                                <div class="flex items-center gap-3 cursor-pointer" onclick="app.toggleGroup('${group.name}')">
                                    <div class="w-6 h-6 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/40 dark:to-purple-800/30 rounded-lg flex items-center justify-center">
                                        <i class="ti ti-folder"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-slate-800 dark:text-slate-200">${this.escapeHtml(group.name)}</h4>
                                        <p class="text-xs text-slate-500 dark:text-slate-400">${group.onlineCount}/${group.totalCount} 在线</p>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <div class="flex items-center gap-1.5">
                                        <button class="group-batch-btn group-batch-btn-secondary" onclick="event.stopPropagation(); app.selectAllGroupNodes('${this.currentActiveTarget}', '${group.name}')" title="全选分组">
                                            <i class="ti ti-select-all"></i>
                                        </button>
                                        <button class="group-batch-btn" onclick="event.stopPropagation(); app.deselectAllGroupNodes('${this.currentActiveTarget}', '${group.name}')" title="反选分组">
                                            <i class="ti ti-square-off"></i>
                                        </button>
                                        <button class="group-batch-btn group-batch-btn-primary" onclick="event.stopPropagation(); app.selectOnlineGroupNodes('${this.currentActiveTarget}', '${group.name}')" title="选择在线节点">
                                            <i class="ti ti-wifi"></i>
                                        </button>
                                    </div>
                                    <span class="text-xs px-2 py-1 bg-slate-200 dark:bg-slate-600 text-slate-600 dark:text-slate-300 rounded-full">
                                        ${groupNodes.length} 个节点
                                    </span>
                                    <i class="ti ti-chevron-down text-sm transition-transform duration-200 cursor-pointer ${isGroupExpanded ? 'rotate-180' : ''}"></i>
                                </div>
                            </div>
                        </div>
                        
                        ${isGroupExpanded ? `
                            <div class="group-nodes mt-3 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-1.5 sm:gap-2 md:gap-3">
                                ${groupNodes.map(node => {
                                    const targetNodes = this.selectedNodes.get(this.currentActiveTarget) || new Set();
                                    const isSelected = targetNodes.has(node.id);
                                    const nodeStatus = this.getNodeStatus(node.id);

                                    // 获取国家标识
                                    const countryCode = this.getNodeCountryCode(node);
                                    const iconContent = countryCode
                                        ? `<img src="/img/flags/${countryCode.toUpperCase()}.SVG" alt="${countryCode}" class="node-flag" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline'"><i class="ti ti-device-desktop node-icon"></i>`
                                        : `<i class="ti ti-device-desktop"></i>`;

                                    return `
                                        <div class="relative group cursor-pointer" data-target-id="${this.currentActiveTarget}" data-node-id="${node.id}" data-group="${group.name}">
                                            <div class="node-card ${isSelected ? 'selected' : 'unselected'} ${nodeStatus.online ? '' : 'offline-node'}">
                                                <div class="node-content">
                                                    <input type="checkbox" ${isSelected ? 'checked' : ''} class="node-checkbox" onclick="event.stopPropagation()">
                                                    <div class="node-title-row">
                                                        ${iconContent}
                                                        <h5 class="node-title" title="${this.escapeHtml(node.name)}">${this.escapeHtml(node.name)}</h5>
                                                    </div>
                                                    <span class="node-status ${isSelected ? 'selected' : 'unselected'} ${nodeStatus.online ? 'status-online' : 'status-offline'}">${nodeStatus.text}</span>
                                                </div>
                                            </div>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        ` : ''}
                    </div>
                `;
            });
            
            html += `</div>`;
        }

        container.innerHTML = html;
        this.bindOrgChartEvents();
    }

    /**
     * T006: 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * T006: 节流函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * T006: 显示加载状态
     */
    showLoadingState(show, message = '加载中...') {
        if (!this.domCache.loadingOverlay) return;

        const overlay = this.domCache.loadingOverlay;
        const textElement = overlay.querySelector('.loading-text');

        if (show) {
            if (textElement) textElement.textContent = message;
            overlay.classList.remove('hidden');

            // 禁用刷新按钮
            if (this.domCache.refreshBtn) {
                this.domCache.refreshBtn.disabled = true;
                this.domCache.refreshBtn.classList.add('opacity-50', 'cursor-not-allowed');
            }
        } else {
            overlay.classList.add('hidden');

            // 启用刷新按钮
            if (this.domCache.refreshBtn) {
                this.domCache.refreshBtn.disabled = false;
                this.domCache.refreshBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        }
    }

    /**
     * T006: 显示错误状态
     */
    showErrorState(message) {
        if (!this.domCache.container) return;

        this.domCache.container.innerHTML = `
            <div class="error-state">
                <div class="error-content">
                    <i class="ti ti-alert-circle"></i>
                    <h3 class="error-title">加载失败</h3>
                    <p class="error-message">${message}</p>
                    <button class="error-retry-btn" onclick="app.retryLoad()">
                        <i class="ti ti-refresh"></i>
                        <span>重试</span>
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * T006: 更新连接状态
     */
    updateConnectionStatus(status) {
        this.connectionStatus = status;

        if (!this.domCache.statsInfo) return;

        const statusIndicator = this.domCache.statsInfo.querySelector('.connection-status') ||
            this.createConnectionStatusIndicator();

        // 移除所有状态类
        statusIndicator.classList.remove('status-connected', 'status-warning', 'status-error', 'status-retrying');

        // 添加当前状态类
        statusIndicator.classList.add(`status-${status}`);

        // 更新状态文本和图标
        const statusConfig = {
            connected: { icon: 'ti-wifi', text: '已连接', color: 'text-green-600' },
            warning: { icon: 'ti-wifi-off', text: '部分失败', color: 'text-yellow-600' },
            error: { icon: 'ti-wifi-off', text: '连接失败', color: 'text-red-600' },
            retrying: { icon: 'ti-refresh', text: '重试中...', color: 'text-blue-600' }
        };

        const config = statusConfig[status] || statusConfig.error;
        statusIndicator.innerHTML = `
            <i class="ti text-xs ${config.color} ${config.icon}"></i>
            <span class="text-xs ${config.color}">${config.text}</span>
        `;
    }

    /**
     * T006: 创建连接状态指示器
     */
    createConnectionStatusIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'connection-status flex items-center gap-1 ml-2';
        this.domCache.statsInfo.appendChild(indicator);
        return indicator;
    }

    /**
     * T006: 重试加载
     */
    async retryLoad() {
        this.retryCount = 0;
        await this.loadData();
        this.renderOrgChart();
    }

    /**
     * T006: 启动自动更新
     */
    startAutoUpdate() {
        // 清除现有的定时器
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }

        // 每30秒自动更新一次
        this.updateInterval = setInterval(() => {
            if (!this.isLoading && this.connectionStatus === 'connected') {
                this.loadData().then(() => {
                    // 只在数据变化时重新渲染
                    this.renderOrgChart();
                }).catch(error => {
                    console.error('自动更新失败:', error);
                });
            }
        }, 30000);
    }

    /**
     * T006: 停止自动更新
     */
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * T006: 优化的通知函数
     */
    showNotification(message, type = 'info', duration = 3000) {
        // 使用全局通知函数，但添加去重逻辑
        const existingNotifications = document.querySelectorAll('.notification-toast');
        const isDuplicate = Array.from(existingNotifications).some(
            notification => notification.textContent.includes(message)
        );

        if (!isDuplicate) {
            if (window.showNotification) {
                window.showNotification(message, type);
            } else {
                console.log(`[${type.toUpperCase()}] ${message}`);
            }
        }
    }

    getActiveTargets() {
        if (this.expandedCategories.size === 0) return [];

        let activeTargets = [];
        this.expandedCategories.forEach(categoryId => {
            const categoryTargets = this.targets.filter(t => t.region_id === categoryId);
            activeTargets = activeTargets.concat(categoryTargets);
        });
        return activeTargets;
    }

    bindOrgChartEvents() {
        const container = document.getElementById('mindmap-container');

        // 分类点击事件
        container.querySelectorAll('[data-category-id]').forEach(item => {
            item.addEventListener('click', (e) => {
                if (e.target.closest('button')) return;

                const categoryId = item.dataset.categoryId;
                this.toggleCategory(categoryId);
            });
        });

        // 目标点击事件
        container.querySelectorAll('[data-target-id]').forEach(item => {
            if (!item.dataset.nodeId) { // 只处理目标卡片，不处理节点卡片
                item.addEventListener('click', (e) => {
                    if (e.target.closest('button')) return;

                    const targetId = item.dataset.targetId;
                    this.toggleTarget(targetId);
                });
            }
        });

        // 节点复选框事件和点击事件
        container.querySelectorAll('[data-node-id]').forEach(nodeItem => {
            const checkbox = nodeItem.querySelector('input[type="checkbox"]');

            // 复选框事件
            if (checkbox) {
                checkbox.addEventListener('change', (e) => {
                    const targetId = nodeItem.dataset.targetId;
                    const nodeId = nodeItem.dataset.nodeId;
                    this.toggleNodeSelection(targetId, nodeId, e.target.checked);
                });
            }

            // 节点卡片点击事件（切换复选框状态）
            nodeItem.addEventListener('click', (e) => {
                if (e.target.type === 'checkbox') return;

                const targetId = nodeItem.dataset.targetId;
                const nodeId = nodeItem.dataset.nodeId;
                const isSelected = checkbox.checked;

                checkbox.checked = !isSelected;
                this.toggleNodeSelection(targetId, nodeId, !isSelected);
            });
        });
    }

    toggleCategory(categoryId) {
        // 清除之前的状态
        this.currentActiveTarget = null;
        this.expandedTargets.clear();

        if (this.expandedCategories.has(categoryId)) {
            this.expandedCategories.clear();
        } else {
            this.expandedCategories.clear();
            this.expandedCategories.add(categoryId);
        }

        this.renderOrgChart();
    }

    toggleTarget(targetId) {
        if (this.expandedTargets.has(targetId)) {
            this.expandedTargets.clear();
            this.currentActiveTarget = null;
        } else {
            this.expandedTargets.clear();
            this.expandedTargets.add(targetId);
            this.currentActiveTarget = targetId;
            
            // 展开目标时，默认展开第一个分组
            if (this.groups.length > 0) {
                this.expandedGroups.add(this.groups[0].name);
            }
        }

        this.renderOrgChart();
    }

    toggleGroup(groupName) {
        if (this.expandedGroups.has(groupName)) {
            this.expandedGroups.delete(groupName);
        } else {
            this.expandedGroups.add(groupName);
        }
        this.renderOrgChart();
    }

    // 批量操作方法
    selectAllNodes(targetId) {
        if (!this.selectedNodes.has(targetId)) {
            this.selectedNodes.set(targetId, new Set());
        }

        const targetNodes = this.selectedNodes.get(targetId);
        
        // 添加所有节点
        this.nodes.forEach(node => {
            targetNodes.add(node.id);
        });

        // 更新显示和保存
        this.updateNodeCountDisplay(targetId);
        this.autoSaveTargetNodes(targetId);
        this.renderOrgChart();
        showNotification('已全选所有节点', 'success');
    }

    // 分组级别的批量操作方法
    selectAllGroupNodes(targetId, groupName) {
        if (!this.selectedNodes.has(targetId)) {
            this.selectedNodes.set(targetId, new Set());
        }

        const targetNodes = this.selectedNodes.get(targetId);
        const group = this.groups.find(g => g.name === groupName);
        
        if (group && group.nodes) {
            group.nodes.forEach(node => {
                targetNodes.add(node.id);
            });

            // 更新显示和保存
            this.updateNodeCountDisplay(targetId);
            this.autoSaveTargetNodes(targetId);
            this.renderOrgChart();
            showNotification(`已全选分组「${groupName}」的所有节点`, 'success');
        }
    }

    deselectAllGroupNodes(targetId, groupName) {
        if (!this.selectedNodes.has(targetId)) {
            this.selectedNodes.set(targetId, new Set());
        }

        const targetNodes = this.selectedNodes.get(targetId);
        const group = this.groups.find(g => g.name === groupName);
        
        if (group && group.nodes) {
            group.nodes.forEach(node => {
                targetNodes.delete(node.id);
            });

            // 更新显示和保存
            this.updateNodeCountDisplay(targetId);
            this.autoSaveTargetNodes(targetId);
            this.renderOrgChart();
            showNotification(`已取消选择分组「${groupName}」的所有节点`, 'success');
        }
    }

    selectOnlineGroupNodes(targetId, groupName) {
        if (!this.selectedNodes.has(targetId)) {
            this.selectedNodes.set(targetId, new Set());
        }

        const targetNodes = this.selectedNodes.get(targetId);
        const group = this.groups.find(g => g.name === groupName);
        
        if (group && group.nodes) {
            const onlineNodes = [];
            
            // 收集分组中的在线节点
            group.nodes.forEach(node => {
                const status = this.getNodeStatus(node.id);
                if (status.online) {
                    onlineNodes.push(node.id);
                }
            });

            // 检查是否所有在线节点都已被选择
            const allOnlineSelected = onlineNodes.every(nodeId => targetNodes.has(nodeId));
            
            if (allOnlineSelected) {
                // 如果所有在线节点都已选择，则反选（取消选择所有在线节点）
                onlineNodes.forEach(nodeId => {
                    targetNodes.delete(nodeId);
                });
                showNotification(`已取消选择分组「${groupName}」的 ${onlineNodes.length} 个在线节点`, 'success');
            } else {
                // 如果不是所有在线节点都被选择，则选择所有在线节点
                onlineNodes.forEach(nodeId => {
                    targetNodes.add(nodeId);
                });
                showNotification(`已选择分组「${groupName}」的 ${onlineNodes.length} 个在线节点`, 'success');
            }

            // 更新显示和保存
            this.updateNodeCountDisplay(targetId);
            this.autoSaveTargetNodes(targetId);
            this.renderOrgChart();
        }
    }

    deselectAllNodes(targetId) {
        if (!this.selectedNodes.has(targetId)) {
            this.selectedNodes.set(targetId, new Set());
        }

        const targetNodes = this.selectedNodes.get(targetId);
        targetNodes.clear();

        // 更新显示和保存
        this.updateNodeCountDisplay(targetId);
        this.autoSaveTargetNodes(targetId);
        this.renderOrgChart();
        showNotification('已取消选择所有节点', 'success');
    }

    selectOnlineNodes(targetId) {
        if (!this.selectedNodes.has(targetId)) {
            this.selectedNodes.set(targetId, new Set());
        }

        const targetNodes = this.selectedNodes.get(targetId);
        const onlineNodes = [];
        
        // 收集所有在线节点
        this.nodes.forEach(node => {
            const status = this.getNodeStatus(node.id);
            if (status.online) {
                onlineNodes.push(node.id);
            }
        });

        // 检查是否所有在线节点都已被选择
        const allOnlineSelected = onlineNodes.every(nodeId => targetNodes.has(nodeId));
        
        if (allOnlineSelected) {
            // 如果所有在线节点都已选择，则反选（取消选择所有在线节点）
            onlineNodes.forEach(nodeId => {
                targetNodes.delete(nodeId);
            });
            showNotification(`已取消选择 ${onlineNodes.length} 个在线节点`, 'success');
        } else {
            // 如果不是所有在线节点都被选择，则选择所有在线节点
            onlineNodes.forEach(nodeId => {
                targetNodes.add(nodeId);
            });
            showNotification(`已选择 ${onlineNodes.length} 个在线节点`, 'success');
        }

        // 更新显示和保存
        this.updateNodeCountDisplay(targetId);
        this.autoSaveTargetNodes(targetId);
        this.renderOrgChart();
    }

    toggleNodeSelection(targetId, nodeId, isSelected) {
        if (!this.selectedNodes.has(targetId)) {
            this.selectedNodes.set(targetId, new Set());
        }

        const targetNodes = this.selectedNodes.get(targetId);

        if (isSelected) {
            targetNodes.add(nodeId);
        } else {
            targetNodes.delete(nodeId);
        }

        // 更新节点计数显示
        this.updateNodeCountDisplay(targetId);

        // 自动保存节点配置
        this.autoSaveTargetNodes(targetId);
    }

    updateNodeCountDisplay(targetId) {
        const targetItem = document.querySelector(`[data-target-id="${targetId}"]:not([data-node-id])`);
        if (targetItem) {
            const countSpan = targetItem.querySelector('span');
            const targetNodes = this.selectedNodes.get(targetId) || new Set();
            countSpan.textContent = `${targetNodes.size}/${this.nodes.length}`;
        }
    }

    async saveTargetNodes(targetId) {
        const target = this.targets.find(t => t.id === targetId);
        if (!target) {
            showNotification('目标不存在', 'error');
            return;
        }

        const selectedNodeIds = Array.from(this.selectedNodes.get(targetId) || []);

        try {
            const data = {
                region_id: target.region_id,
                name: target.name,
                host: target.host,
                port: target.port,
                test_type: target.test_type,
                description: target.description,
                mode: 'specific',
                node_ids: selectedNodeIds
            };

            const response = await fetch(`/api/admin/monitor/targets/${targetId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'same-origin',
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                showNotification('节点配置保存成功', 'success');
                await this.loadData();
                this.renderOrgChart();
            } else {
                throw new Error(result.message || '保存失败');
            }
        } catch (error) {
            console.error('保存节点配置失败:', error);
            showNotification('保存节点配置失败: ' + error.message, 'error');
        }
    }

    // 自动保存节点配置（防抖处理）
    autoSaveTargetNodes(targetId) {
        // 清除之前的定时器
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
        }

        // 设置新的定时器，延迟500ms执行保存
        this.autoSaveTimer = setTimeout(async () => {
            const target = this.targets.find(t => t.id === targetId);
            if (!target) {
                return;
            }

            const selectedNodeIds = Array.from(this.selectedNodes.get(targetId) || []);

            try {
                const data = {
                    region_id: target.region_id,
                    name: target.name,
                    host: target.host,
                    port: target.port,
                    test_type: target.test_type,
                    description: target.description,
                    mode: 'specific',
                    node_ids: selectedNodeIds
                };

                const response = await fetch(`/api/admin/monitor/targets/${targetId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('节点配置已自动保存', 'success');
                    // 更新本地数据，但不重新渲染整个界面
                    const targetIndex = this.targets.findIndex(t => t.id === targetId);
                    if (targetIndex !== -1) {
                        this.targets[targetIndex].node_id = JSON.stringify(selectedNodeIds);
                    }
                } else {
                    throw new Error(result.message || '自动保存失败');
                }
            } catch (error) {
                console.error('自动保存节点配置失败:', error);
                showNotification('自动保存失败: ' + error.message, 'error');
            }
        }, 500);
    }

    // 分类管理
    openCategoryModal(category = null) {
        const modal = document.getElementById('category-modal');
        const title = document.getElementById('category-modal-title');
        const form = document.getElementById('category-form');

        if (category) {
            title.textContent = '编辑监控分类';
            document.getElementById('category-id').value = category.id;
            document.getElementById('category-name').value = category.name;
            document.getElementById('category-description').value = category.description || '';
        } else {
            title.textContent = '添加监控分类';
            form.reset();
            document.getElementById('category-id').value = '';
        }

        modal.classList.remove('hidden');
    }

    closeCategoryModal() {
        document.getElementById('category-modal').classList.add('hidden');
    }

    async saveCategoryForm() {
        const categoryId = document.getElementById('category-id').value;

        const data = {
            name: document.getElementById('category-name').value,
            description: document.getElementById('category-description').value
        };

        try {
            const url = categoryId ? `/api/monitor/regions/${categoryId}` : '/api/monitor/regions';
            const method = categoryId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                showNotification(`分类${categoryId ? '更新' : '创建'}成功`, 'success');
                this.closeCategoryModal();
                await this.loadData();
                this.renderOrgChart();
            } else {
                throw new Error(result.message || `${categoryId ? '更新' : '创建'}失败`);
            }
        } catch (error) {
            console.error('保存分类失败:', error);
            showNotification(`分类${categoryId ? '更新' : '创建'}失败: ` + error.message, 'error');
        }
    }

    editCategory(categoryId) {
        const category = this.categories.find(c => c.id === categoryId);
        if (category) {
            this.openCategoryModal(category);
        }
    }

    async deleteCategory(categoryId) {
        if (!confirm('确定要删除这个分类吗？这将同时删除分类下的所有监控目标。')) {
            return;
        }

        try {
            const response = await fetch(`/api/admin/monitor/regions/${categoryId}`, {
                method: 'DELETE',
                credentials: 'same-origin'
            });

            const result = await response.json();

            if (result.success) {
                showNotification('分类删除成功', 'success');
                await this.loadData();
                this.renderOrgChart();
            } else {
                throw new Error(result.message || '删除失败');
            }
        } catch (error) {
            console.error('删除分类失败:', error);
            showNotification('删除分类失败: ' + error.message, 'error');
        }
    }

    // 目标管理
    addTargetToCategory(categoryId) {
        document.getElementById('target-category-id').value = categoryId;
        this.openTargetModal();
    }

    openTargetModal(target = null) {
        const modal = document.getElementById('target-modal');
        const title = document.getElementById('target-modal-title');
        const form = document.getElementById('target-form');

        if (target) {
            title.textContent = '编辑监控目标';
            document.getElementById('target-id').value = target.id;
            document.getElementById('target-category-id').value = target.region_id;
            document.getElementById('target-name').value = target.name;
            document.getElementById('target-host').value = target.host;
            document.getElementById('target-port').value = target.port || '';
            document.getElementById('target-description').value = target.description || '';

            // 设置测试类型
            const testTypeRadios = form.querySelectorAll('input[name="target-test-type"]');
            testTypeRadios.forEach(radio => {
                radio.checked = radio.value === target.test_type;
            });

            // 显示/隐藏端口字段
            const portField = document.getElementById('target-port-field');
            portField.style.display = target.test_type === 'tcping' ? 'block' : 'none';
        } else {
            title.textContent = '添加监控目标';
            form.reset();
            document.getElementById('target-id').value = '';

            // 如果没有指定分类ID，使用第一个分类
            if (!document.getElementById('target-category-id').value && this.categories.length > 0) {
                document.getElementById('target-category-id').value = this.categories[0].id;
            }

            // 默认选择tcping
            document.querySelector('input[name="target-test-type"][value="tcping"]').checked = true;
            document.getElementById('target-port-field').style.display = 'block';
        }

        modal.classList.remove('hidden');
    }

    closeTargetModal() {
        document.getElementById('target-modal').classList.add('hidden');
    }

    async saveTargetForm() {
        const targetId = document.getElementById('target-id').value;

        const testType = document.querySelector('input[name="target-test-type"]:checked').value;

        const data = {
            region_id: document.getElementById('target-category-id').value,
            name: document.getElementById('target-name').value,
            host: document.getElementById('target-host').value,
            test_type: testType,
            description: document.getElementById('target-description').value,
            mode: 'auto'
        };

        if (testType === 'tcping') {
            const port = document.getElementById('target-port').value;
            if (port) {
                data.port = parseInt(port);
            }
        }

        try {
            const url = targetId ? `/api/monitor/targets/${targetId}` : '/api/monitor/targets';
            const method = targetId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                showNotification(`目标${targetId ? '更新' : '创建'}成功`, 'success');
                this.closeTargetModal();
                await this.loadData();
                this.renderOrgChart();
            } else {
                throw new Error(result.message || `${targetId ? '更新' : '创建'}失败`);
            }
        } catch (error) {
            console.error('保存目标失败:', error);
            showNotification(`目标${targetId ? '更新' : '创建'}失败: ` + error.message, 'error');
        }
    }

    editTarget(targetId) {
        const target = this.targets.find(t => t.id === targetId);
        if (target) {
            this.openTargetModal(target);
        }
    }

    async deleteTarget(targetId) {
        if (!confirm('确定要删除这个监控目标吗？')) {
            return;
        }

        try {
            const response = await fetch(`/api/admin/monitor/targets/${targetId}`, {
                method: 'DELETE',
                credentials: 'same-origin'
            });

            const result = await response.json();

            if (result.success) {
                showNotification('目标删除成功', 'success');
                await this.loadData();
                this.renderOrgChart();
            } else {
                throw new Error(result.message || '删除失败');
            }
        } catch (error) {
            console.error('删除目标失败:', error);
            showNotification('删除目标失败: ' + error.message, 'error');
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// T006: 页面卸载时的清理
window.addEventListener('beforeunload', function() {
    if (window.app) {
        window.app.stopAutoUpdate();
    }
});

// T006: 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (window.app) {
        if (document.hidden) {
            // 页面隐藏时停止自动更新
            window.app.stopAutoUpdate();
        } else {
            // 页面显示时恢复自动更新
            window.app.startAutoUpdate();
            // 立即刷新一次数据
            window.app.loadData().then(() => {
                window.app.renderOrgChart();
            }).catch(error => {
                console.error('页面恢复时刷新数据失败:', error);
            });
        }
    }
});

// T006: 网络状态变化处理
window.addEventListener('online', function() {
    if (window.app) {
        window.app.showNotification('网络连接已恢复', 'success');
        window.app.updateConnectionStatus('connected');
        window.app.retryLoad();
    }
});

window.addEventListener('offline', function() {
    if (window.app) {
        window.app.showNotification('网络连接已断开', 'warning');
        window.app.updateConnectionStatus('error');
        window.app.stopAutoUpdate();
    }
});

// T006: 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.app = new MonitorMindMapApp();

        // T006: 添加全局错误处理
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error);
            if (window.app) {
                window.app.showNotification('页面发生错误，请刷新重试', 'error');
            }
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise拒绝:', event.reason);
            if (window.app) {
                window.app.showNotification('操作失败，请重试', 'error');
            }
        });

    } catch (error) {
        console.error('应用初始化失败:', error);
        document.body.innerHTML = `
            <div class="error-state">
                <div class="error-content">
                    <i class="ti ti-alert-circle"></i>
                    <h3 class="error-title">应用初始化失败</h3>
                    <p class="error-message">请刷新页面重试</p>
                    <button class="error-retry-btn" onclick="location.reload()">
                        <i class="ti ti-refresh"></i>
                        <span>刷新页面</span>
                    </button>
                </div>
            </div>
        `;
    }
});