/**
 * 统一的系统状态管理模块
 * 整合了流量统计、格式化和工具函数
 * 
 * 🚨 重要开发指南：避免移动端显示HTML源代码问题
 * 
 * 问题：formatBytes() 和 formatBandwidth() 返回包含HTML标签的字符串
 * 例如：'3.65 <span class="metric-unit">Mbps</span>'
 * 
 * ❌ 错误做法：
 * element.textContent = formatBytes(value);  // 显示：3.65 <span class="metric-unit">MB</span>
 * 
 * ✅ 正确做法：
 * element.innerHTML = formatBytes(value);    // 显示：3.65 MB (带样式)
 * 或使用：this.setFormattedContent(element, formatBytes(value));  // 自动检测
 * 
 * 所有包含单位的格式化数据都必须使用 innerHTML 或 setFormattedContent() 方法！
 */

// IIFE 立即执行函数，避免全局污染
(function() {
    'use strict';

    // 初始化调试模式标志 (值会被服务器设置覆盖)
    window.DEBUG_MODE = false;

    // 检查服务器设置中的调试模式
    if (typeof window.SERVER_DEBUG_MODE !== 'undefined') {
        window.DEBUG_MODE = !!window.SERVER_DEBUG_MODE;
    } else {
        // 如果没有设置 SERVER_DEBUG_MODE，默认禁用调试模式
        window.DEBUG_MODE = false;
    }

    // 常量定义
    const BYTE_UNITS = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const BPS_UNITS = ['bps', 'Kbps', 'Mbps', 'Gbps', 'Tbps', 'Pbps', 'Ebps', 'Zbps', 'Ybps'];
    // 紧凑单位（用于实时速度显示）
    const BYTE_UNITS_COMPACT = ['B', 'K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'];
    const BPS_UNITS_COMPACT = ['b', 'K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'];
    const UPDATE_INTERVAL = 60000; // 1分钟 (HTTP轮询间隔)
    const DEBOUNCE_DELAY = 300;    // 300ms

    // 单位转换常量 (TrafficFormat 内部使用，这里保留可能用于其他地方，但实际可以移除)
    const KB = 1024;
    const MB = KB * 1024;
    const GB = MB * 1024;
    const TB = GB * 1024;
    const Kbps = 1000;
    const Mbps = Kbps * 1000;
    const Gbps = Mbps * 1000;
    const Tbps = Gbps * 1000;

    // 动态加载SharedWorker客户端
    function loadSharedClientScript() {
        return new Promise((resolve, reject) => {
            // 检查是否已经加载
            if (window.StatsSharedClient) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = '/js/stats-shared-client-minimal.js';
            script.onload = () => resolve();
            script.onerror = (e) => reject(new Error('无法加载SharedWorker客户端脚本'));
            document.head.appendChild(script);
        });
    }

    // 加载客户端
    loadSharedClientScript().catch(err => {
        // 在这里处理加载失败的情况，避免使用 console.error
        // 可以考虑向用户显示一个UI提示
    });

    /**
     * 流量格式化工具
     */
    const TrafficFormat = {
        /**
         * 格式化字节数为可读字符串
         * @param {number|string|bigint} bytes 字节数
         * @param {number} decimals 小数位数
         * @returns {string} 格式化后的字符串
         */
        formatBytes(bytes, decimals = 2, compact = false) {
            if (!bytes) return compact ? '0<span class="metric-unit-compact">B</span>' : '0 B';

            let value = typeof bytes === 'bigint' ? Number(bytes) :
                       typeof bytes === 'string' ? Number(bytes) : bytes;

            if (isNaN(value) || !isFinite(value) || value === 0) return compact ? '0<span class="metric-unit-compact">B</span>' : '0 B';

            const k = 1024;
            const sizes = compact ? BYTE_UNITS_COMPACT : BYTE_UNITS;

            if (value < 0) value = 0; // 处理负数情况

            const i = Math.floor(Math.log(value) / Math.log(k));

            // 确保 i 在有效范围内
            const unitIndex = Math.min(i, sizes.length - 1);

            if (compact) {
                // 紧凑模式：无小数，单位更小
                return Math.round(value / Math.pow(k, unitIndex)) + '<span class="metric-unit-compact">' + sizes[unitIndex] + '</span>';
            } else {
                // 标准模式
                return parseFloat((value / Math.pow(k, unitIndex)).toFixed(decimals)) + ' <span class="metric-unit">' + sizes[unitIndex] + '</span>';
            }
        },

        /**
         * 安全地设置格式化内容到DOM元素
         * 自动检测内容是否包含HTML标签，选择正确的设置方式
         * 
         * 重要：避免在移动端显示HTML源代码的问题
         * - 格式化的字节/带宽数据包含 <span class="metric-unit"> 标签
         * - 使用 textContent 会显示原始HTML: "3.65 <span class="metric-unit">Mbps</span>"
         * - 使用 innerHTML 会正确渲染: "3.65 Mbps" (带样式)
         * 
         * @param {HTMLElement} element - 目标DOM元素
         * @param {string} content - 要设置的内容
         */
        setFormattedContent(element, content) {
            if (!element || content === null || content === undefined) return;
            
            // 检测内容是否包含HTML标签
            if (typeof content === 'string' && /<[^>]*>/g.test(content)) {
                // 包含HTML标签，使用innerHTML
                element.innerHTML = content;
            } else {
                // 纯文本，使用textContent
                element.textContent = content;
            }
        },

        /**
         * 格式化比特率
         * @param {number} bps 比特每秒
         * @returns {string} 格式化后的字符串
         */
        formatBps(bps, compact = false) {
            if (!bps) return compact ? '0<span class="metric-unit-compact">b</span>' : '0 bps';

            let value = Number(bps);
            if (isNaN(value) || !isFinite(value) || value === 0) return compact ? '0<span class="metric-unit-compact">b</span>' : '0 bps';

            const k = 1000; // 网络速率单位是1000
            const sizes = compact ? BPS_UNITS_COMPACT : BPS_UNITS;

            if (value < 0) value = 0; // 处理负数情况

            const i = Math.floor(Math.log(value) / Math.log(k));

            // 确保 i 在有效范围内
            const unitIndex = Math.min(i, sizes.length - 1);

            if (compact) {
                // 紧凑模式：无小数，单位更小
                return '<span class="speed-metric">' + Math.round(value / Math.pow(k, unitIndex)) + '</span><span class="metric-unit-compact">' + sizes[unitIndex] + '</span>';
            } else {
                // 标准模式：保留2位小数，统一格式
                return parseFloat((value / Math.pow(k, unitIndex)).toFixed(2)) + ' <span class="metric-unit">' + sizes[unitIndex] + '</span>';
            }
        },

        /**
         * 验证流量值
         * @param {*} value 要验证的值
         * @returns {boolean} 是否为有效的流量值
         */
        validateTrafficValue(value) {
            if (value === undefined || value === null) return false;
            const num = Number(value);
            // 允许为 0
            return !isNaN(num) && isFinite(num) && num >= 0;
        },

        /**
         * 格式化在线时间
         * @param {number} seconds 秒数
         * @returns {string} 格式化后的在线时间字符串
         */
        formatUptime(seconds) {
            if (!seconds || isNaN(seconds) || seconds <= 0) return '0分钟';

            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);

            // 简化显示：只显示天数
            if (days > 0) {
                return `${days}天`;
            } else {
                return '0天';
            }
        }
    };

    /**
     * 流量数据处理工具
     */
    const TrafficUtils = {
        /**
         * 计算总流量
         * @param {Array} data 流量数据数组
         * @returns {bigint} 总流量
         */
        calculateTotalTraffic(data) {
            if (!Array.isArray(data)) {
                // console.warn('无效的流量数据格式'); // Removed log
                return BigInt(0);
            }

            try {
                return data.reduce((total, item) => {
                    // 假设 item[0] 是流量值
                    const value = Array.isArray(item) ? item[0] : item;
                    // 确保使用 BigInt 进行加法，并验证值
                    if (TrafficFormat.validateTrafficValue(value)) {
                         // 如果 value 是字符串形式的数字，需要先转为 BigInt
                         try {
                            return total + BigInt(value);
                         } catch (e) {
                             // 转换失败，忽略该值
                             return total;
                         }
                    } else {
                        return total;
                    }
                }, BigInt(0));
            } catch (error) {
                // console.warn('计算总流量失败:', error); // Removed log
                return BigInt(0);
            }
        }
    };

    /**
     * 状态管理器
     */
    const StatManager = {
        // 私有属性
        _updateTimer: null,
        _trafficUpdateTimer: null,  // 流量更新定时器
        _debounceTimer: null,
        _isUpdating: false,         // 实时数据更新状态
        _isTrafficUpdating: false,  // 流量数据更新状态
        _updateInterval: 1000, // 实时数据默认1秒更新一次 (WebSocket优先)
        _trafficUpdateInterval: UPDATE_INTERVAL, // 流量数据默认1分钟更新一次
        _maxRetries: 3,
        _retryCount: 0,
        _subscribers: new Map(),

        /**
         * 调试日志函数 - 已禁用
         * @param {string} message - 日志消息
         * @param {any} data - 可选的数据对象
         * @param {boolean} [force=false] - 是否强制输出（即使在非调试模式下）
         * @private
         */
        _debug(message, data, force = false) {
            // 完全禁用日志输出
            return;
        },

        // WebSocket相关属性
        _ws: null, // 旧的 ws 属性，在 SharedWorker 模式下可能不再直接使用
        _wsReconnectTimer: null, // 可能也不再需要，由 SharedWorker 内部处理重连
        _wsReconnectInterval: 3000, // SharedWorker 可能有自己的重连逻辑
        _useWebSocket: true, // 是否尝试使用WebSocket (通过 SharedWorker)
        _sharedClient: null, // SharedWorker客户端实例

        /**
         * 订阅数据更新
         * @param {string} id 订阅者ID
         * @param {Function} callback 回调函数
         */
        subscribe(id, callback) {
            // this._debug('新订阅者:', id); // Removed log
            this._subscribers.set(id, callback);
        },

        /**
         * 取消订阅
         * @param {string} id 订阅者ID
         */
        unsubscribe(id) {
            // this._debug('取消订阅:', id); // Removed log
            this._subscribers.delete(id);
        },

        /**
         * 通知所有订阅者
         * @param {Object} data 更新的数据 (期望格式: { sid: 'xxx', name: 'yyy', stat: {... 或 false} })
         */
        _notifySubscribers(data) {
            // this._debug('通知订阅者, 当前订阅者数量:', this._subscribers.size); // Removed log
            // 验证传入的数据结构是否符合预期
            if (!data || !data.sid || (data.stat !== false && !data.stat)) {
                // console.warn('无效的数据格式，跳过通知:', data); // Removed log
                return;
            }

            // 包装成 { [sid]: { name: ..., stat: ... } } 的格式，以便回调函数统一处理
            const processedData = {
                [data.sid]: {
                    name: data.name,
                    stat: data.stat
                }
            };

            // // 如果节点离线，添加日志 (日志已移除)
            // if (data.stat === false) {
            //     this._debug(`节点 ${data.name || data.sid} 已离线，但仍然通知订阅者`);
            // }

            this._subscribers.forEach((callback, id) => {
                try {
                    // this._debug('通知订阅者:', id); // Removed log
                    callback(processedData);
                } catch (error) {
                    // 避免使用 console.error
                    // 可以考虑记录到一个内部错误列表或发送到监控系统
                }
            });
        },

        /**
         * 初始化状态管理器
         */
        async init() {
            try {
                // this._debug('开始初始化状态管理器...'); // Removed log
                this._retryCount = 0;
                this._initEventListeners();

                // 订阅自己来更新系统状况 (例如 CPU, Mem 等)
                this.subscribe('system-status', this._updateOtherStats.bind(this));

                // 立即显示UI，不等待数据加载
                this._showInitialUI();
                
                // 初始化网络速度仪表盘
                this._initNetworkSpeedGauge();

                // 异步初始化WebSocket和获取数据
                setTimeout(async () => {
                    // 初始化WebSocket (通过SharedWorker)
                    if (this._useWebSocket) {
                        this._initWebSocket(); // 尝试启动 SharedWorker 连接
                    }

                    // 异步获取数据，不阻塞UI渲染
                    // 第一次获取实时数据和流量数据
                    this._fetchRealtimeStats().catch(e => { /* 获取实时数据失败处理，无日志 */ });
                    this.updateTrafficStats().catch(e => { /* 获取流量数据失败处理，无日志 */ });

                    // 设置定时更新 (如果WebSocket未成功连接，或者需要定期刷新流量)
                    // 实时数据定时器 (仅当WS不可用时启动轮询)
                    if (!this._useWebSocket || !this._sharedClient || !this._sharedClient.isConnected) {
                        this._startUpdateTimer();
                    }
                    // 流量数据定时器 (总是启动，流量信息不通过实时WS推送)
                    this._startTrafficUpdateTimer();
                }, 0);

                // console.info('状态管理器初始化完成'); // Removed log
                // this._debug('状态管理器初始化详情', { ... }); // Removed log
            } catch (error) {
                // 初始化失败处理，避免使用 console.error
                // 可以在UI上显示错误信息
                // 尝试重新初始化可能导致无限循环，需要更健壮的错误处理
                // setTimeout(() => this.init(), 1000); // 移除自动重试
            }
        },

        /**
         * 初始化网络速度仪表盘
         * @private
         */
        _initNetworkSpeedGauge() {
            const container = document.getElementById('network-speed-gauge');
            if (!container) return;
            
            // 检查 ECharts 是否可用
            if (typeof echarts === 'undefined') {
                console.warn('ECharts 未加载，无法初始化网络速度仪表盘');
                return;
            }
            
            // 检查容器是否可见（响应式隐藏时不初始化）
            if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                // 延迟初始化，等待容器可见
                const checkVisibility = () => {
                    if (container.offsetWidth > 0 && container.offsetHeight > 0) {
                        this._networkGaugeChart = echarts.init(container);
                        this._setupGaugeChart();
                        window.removeEventListener('resize', checkVisibility);
                    }
                };
                window.addEventListener('resize', checkVisibility);
                return;
            }
            
            // 初始化速度范围配置
            this._speedRanges = [
                { max: 100, unit: 'Mbps', threshold: 80 },    // 100M - 适合百兆网络
                { max: 1000, unit: 'Mbps', threshold: 800 },  // 1G - 适合千兆网络
                { max: 10000, unit: 'Mbps', threshold: 8000 } // 10G - 适合万兆网络
            ];
            this._currentSpeedRange = 0; // 默认使用第一个范围
            this._peakSpeed = 0; // 记录峰值速度
            
            // 初始化 ECharts 实例
            this._networkGaugeChart = echarts.init(container);
            this._setupGaugeChart();
        },
        
        /**
         * 设置仪表盘配置
         * @private
         */
        _setupGaugeChart() {
            if (!this._networkGaugeChart) return;
            
            // 确保速度范围已初始化
            if (!this._speedRanges) {
                this._speedRanges = [
                    { max: 100, unit: 'Mbps', threshold: 80 },
                    { max: 1000, unit: 'Mbps', threshold: 800 },
                    { max: 10000, unit: 'Mbps', threshold: 8000 }
                ];
                this._currentSpeedRange = 0;
                this._peakSpeed = 0;
            }
            
            // 获取当前速度范围配置
            const currentRange = this._speedRanges[this._currentSpeedRange];
            
            // 配置仪表盘选项 - 适配更小的容器
            const option = {
                series: [{
                    type: 'gauge',
                    center: ['50%', '50%'],
                    radius: '85%',
                    startAngle: 210,
                    endAngle: -30,
                    min: 0,
                    max: currentRange.max,
                    splitNumber: 5,
                    itemStyle: {
                        color: '#22c55e' // 默认绿色
                    },
                    progress: {
                        show: true,
                        width: 6
                    },
                    pointer: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            width: 6,
                            color: [[1, '#e5e7eb']]
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    splitLine: {
                        show: false
                    },
                    axisLabel: {
                        show: false
                    },
                    anchor: {
                        show: false
                    },
                    title: {
                        show: false
                    },
                    detail: {
                        valueAnimation: true,
                        width: '60%',
                        lineHeight: 16,
                        borderRadius: 4,
                        offsetCenter: [0, '0%'],
                        fontSize: 14,
                        fontWeight: 'bold',
                        formatter: function(value) {
                            // 格式化显示，根据范围决定显示精度
                            if (currentRange.max >= 1000) {
                                // 千兆及以上，显示小数点后一位
                                return value.toFixed(1) + '\n' + currentRange.unit;
                            } else {
                                // 百兆，显示整数
                                return Math.round(value) + '\n' + currentRange.unit;
                            }
                        },
                        color: 'inherit'
                    },
                    data: [{
                        value: 0
                    }]
                }]
            };
            
            // 设置选项
            this._networkGaugeChart.setOption(option);
            
            // 监听容器大小变化
            const container = document.getElementById('network-speed-gauge');
            if (container) {
                const resizeObserver = new ResizeObserver(() => {
                    if (container.offsetWidth > 0 && container.offsetHeight > 0) {
                        this._networkGaugeChart.resize();
                    }
                });
                resizeObserver.observe(container);
            }
            
            // 监听窗口大小变化，处理响应式显示
            const resizeHandler = () => {
                if (container && container.offsetWidth > 0 && container.offsetHeight > 0) {
                    this._networkGaugeChart.resize();
                }
            };
            window.addEventListener('resize', resizeHandler);
        },
        
        /**
         * 根据当前速度自动调整速度范围
         * @param {number} speedMbps - 当前速度（Mbps）
         * @private
         */
        _adjustSpeedRange(speedMbps) {
            if (!this._speedRanges || !this._networkGaugeChart) return;
            
            // 记录峰值速度
            if (speedMbps > this._peakSpeed) {
                this._peakSpeed = speedMbps;
            }
            
            // 查找合适的速度范围
            let newRangeIndex = this._currentSpeedRange;
            
            // 检查是否需要升级到更高档位
            for (let i = 0; i < this._speedRanges.length; i++) {
                const range = this._speedRanges[i];
                if (speedMbps > range.threshold) {
                    // 需要升级到下一个档位
                    if (i < this._speedRanges.length - 1) {
                        newRangeIndex = i + 1;
                    }
                } else {
                    // 当前档位合适
                    newRangeIndex = i;
                    break;
                }
            }
            
            // 如果速度很低且长时间没有高速，可以降档以提高精度
            // 如果当前速度小于当前档位最大值的20%，且不是最低档位
            if (newRangeIndex > 0) {
                const currentRange = this._speedRanges[newRangeIndex];
                if (speedMbps < currentRange.max * 0.2 && this._peakSpeed < currentRange.max * 0.3) {
                    // 考虑降档
                    for (let i = newRangeIndex - 1; i >= 0; i--) {
                        const lowerRange = this._speedRanges[i];
                        if (this._peakSpeed < lowerRange.threshold) {
                            newRangeIndex = i;
                        } else {
                            break;
                        }
                    }
                }
            }
            
            // 如果范围改变了，更新仪表盘配置
            if (newRangeIndex !== this._currentSpeedRange) {
                const oldRangeIndex = this._currentSpeedRange;
                this._currentSpeedRange = newRangeIndex;
                this._setupGaugeChart(); // 重新配置仪表盘
                
                // 如果速度下降导致降档，重置峰值速度
                if (newRangeIndex < oldRangeIndex) {
                    this._peakSpeed = speedMbps;
                }
            }
        },

        /**
         * 显示初始UI (骨架屏处理)
         * @private
         */
        _showInitialUI() {
            // this._debug('立即显示初始UI'); // Removed log

            // 显示骨架屏，隐藏实际内容（除非有预加载数据）
            const hostnameElem = document.getElementById('system-hostname');
            const hostnameSkeleton = document.getElementById('system-hostname-skeleton');
            const uptimeElem = document.getElementById('uptime-display');
            const uptimeSkeleton = document.getElementById('uptime-display-skeleton');

            // 骨架屏默认显示
            if (hostnameSkeleton) hostnameSkeleton.classList.remove('hidden');
            if (uptimeSkeleton) uptimeSkeleton.classList.remove('hidden');

            // 根据元素是否有内容决定是否隐藏骨架屏
            if (hostnameElem && hostnameElem.textContent.trim()) {
                hostnameElem.classList.remove('hidden');
                if (hostnameSkeleton) hostnameSkeleton.classList.add('hidden');
            } else if (hostnameElem) {
                hostnameElem.classList.add('hidden'); // 确保没有内容时不显示
            }

            if (uptimeElem && uptimeElem.textContent.trim()) {
                uptimeElem.classList.remove('hidden');
                if (uptimeSkeleton) uptimeSkeleton.classList.add('hidden');
            } else if (uptimeElem) {
                uptimeElem.classList.add('hidden'); // 确保没有内容时不显示
            }

            // 尝试使用预加载数据更新UI
            try {
                const preloadedData = this._getPreProcessedData();
                if (preloadedData) {
                    // this._debug('使用预加载数据立即更新UI'); // Removed log
                    const nodeId = this._getNodeId(); // 需要节点ID来匹配数据
                    if (nodeId) {
                        // 构造符合 _notifySubscribers 预期的数据格式
                        const processedData = {
                            sid: nodeId,
                            name: preloadedData.name || 'Unknown', // 确保有 name
                            // 假设预加载数据结构与stat对象类似或包含stat
                            stat: preloadedData.stat || preloadedData
                        };
                        // 直接调用通知，会触发 _updateOtherStats
                        this._notifySubscribers(processedData);
                    }
                }
            } catch (error) {
                // console.warn('预加载数据处理失败:', error); // Removed log
                // 失败时不阻塞UI显示
            }

            // 预先初始化图表容器，使其立即显示
            this._initChartContainers();
        },

        /**
         * 预先初始化图表容器 (简化版，只确保容器可见)
         * 注意：详细的容器初始化已移至 ChartManager._prepareContainers()
         * @private
         */
        _initChartContainers() {
            try {
                // 查找所有图表容器
                const chartContainers = document.querySelectorAll('.chart-container');

                // 只确保容器可见，不再设置样式和尺寸
                chartContainers.forEach(container => {
                    // 确保容器可见
                    container.classList.remove('hidden');
                    if (container.hasAttribute('hidden')) {
                        container.removeAttribute('hidden');
                    }
                });

                // 不再创建容器，由 ChartManager 负责

                // 设置全局标记，表示已尝试初始化容器
                window.chartContainersInitialized = true;

                // 不再添加全局错误处理函数，由 ChartManager 负责
            } catch (error) {
                // 出错时不抛出异常，避免阻塞UI初始化
                if (window.DEBUG_MODE) {
                    console.warn('初始化图表容器时出错 (已处理):', error.message);
                }
            }
        },

        /**
         * 初始化WebSocket连接 (通过 SharedWorker)
         * @private
         */
        _initWebSocket() {
            try {
                const nodeId = this._getNodeId();
                if (!nodeId) {
                    // console.warn('无法获取节点ID，WebSocket将不可用'); // Removed log
                    this._useWebSocket = false; // 标记不使用WS
                    this._startUpdateTimer(); // 确保启动HTTP轮询
                    return;
                }

                // 检查SharedWorker客户端是否已加载
                if (!window.StatsSharedClient) {
                    // console.warn('SharedWorker客户端未加载，等待加载完成'); // Removed log
                    // 稍后重试，避免阻塞
                    setTimeout(() => this._initWebSocket(), 500);
                    return;
                }

                // 防止重复初始化
                if (this._sharedClient) {
                    return;
                }

                // 初始化SharedWorker客户端
                this._sharedClient = new StatsSharedClient({
                    nodeId: nodeId, // 传递节点ID给客户端
                    debug: window.DEBUG_MODE, // 传递调试模式
                    onMessage: (data, fromCache) => { // 接收消息回调
                        // 验证收到的数据结构
                        if (!data || !data.type || data.type !== 'stats' || !data.data || !data.data[nodeId]) {
                            // console.warn('收到无效或不相关的WebSocket数据格式'); // Removed log
                            return;
                        }

                        // this._debug('收到WebSocket数据, ' + (fromCache ? '(缓存)' : '') + ' 节点ID: ' + nodeId); // Removed log

                        // 提取当前节点的数据
                        const nodeData = data.data[nodeId];

                        // this._debug('收到节点数据更新: ' + (nodeData.name || nodeId)); // Removed log

                        // 构造标准格式通知订阅者
                        const processedData = {
                            sid: nodeId,
                            name: nodeData.name,
                            stat: nodeData.stat // stat 可能是对象或 false
                        };
                        this._notifySubscribers(processedData); // 这会触发 _updateOtherStats

                        // ----- SharedWorker消息处理中不再直接更新DOM -----
                        // _notifySubscribers -> _updateOtherStats 会处理所有UI更新
                        // 包括CPU, Mem, Swap, Host Info, Network Speed (delta)
                        // 仅处理 WebSocket 特有的、可能不在 stat 中的数据（如果未来有的话）
                        // 目前看，所有数据都在 stat 中，所以这里不需要额外DOM操作了

                        // 例如，如果WebSocket直接推送流量总量(total)，可以在这里处理
                        // 但当前逻辑是流量总量通过 /traffic 接口获取
                    },
                    onConnected: () => {
                        // this._debug('WebSocket (SharedWorker) 连接成功'); // Removed log
                        // 连接成功后，可以停止HTTP轮询（如果它在运行）
                        if (this._updateTimer) {
                            // this._debug('停止HTTP轮询，使用WebSocket通信'); // Removed log
                            clearInterval(this._updateTimer);
                            this._updateTimer = null;
                        }
                    },
                    onDisconnected: () => {
                        // this._debug('WebSocket (SharedWorker) 连接断开'); // Removed log
                        // 连接断开，需要重新启动HTTP轮询作为备选
                        this._startUpdateTimer();
                    }
                });

                // this._debug('SharedWorker客户端已初始化'); // Removed log

            } catch (error) {
                // 初始化WebSocket失败处理，避免使用 console.error
                this._useWebSocket = false; // 标记不使用WS
                // 确保HTTP轮询作为备选
                this._startUpdateTimer();
            }
        },

        /**
         * 初始化事件监听
         */
        _initEventListeners() {
            // this._debug('初始化事件监听器'); // Removed log

            // 页面可见性变化监听
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    // this._debug('页面隐藏，降低更新频率'); // Removed log
                    // 页面隐藏时不停止更新，降低轮询频率 (如果WS断开)
                    this._adjustUpdateInterval(120000); // 2分钟
                } else {
                    // this._debug('页面可见，恢复正常频率并立即更新'); // Removed log
                    this._adjustUpdateInterval(this._trafficUpdateInterval); // 恢复到流量更新间隔（例如1分钟）

                    // 检查是否处于标签页恢复状态
                    const isRecoveringFromSleep = window.TabLifecycleHandler && window.TabLifecycleHandler.isRecoveringFromSleep;
                    const shouldBlockDataUpdates = window.TabLifecycleHandler && window.TabLifecycleHandler.blockDataUpdates;

                    // 只有在不处于恢复状态时才立即更新
                    if (!isRecoveringFromSleep || !shouldBlockDataUpdates) {
                        // 添加延迟，避免与其他可见性处理冲突
                        setTimeout(() => {
                            this.updateStats(); // 立即更新一次（包括实时和流量）
                        }, 200);
                    }

                    // 如果WebSocket应该使用但未连接，尝试重连
                    if (this._useWebSocket && (!this._sharedClient || !this._sharedClient.isConnected)) {
                        // this._debug('尝试重新连接WebSocket'); // Removed log
                        // 添加延迟，避免与其他可见性处理冲突
                        setTimeout(() => {
                            this._initWebSocket();
                        }, 300);
                    }
                }
            });

            // 页面焦点变化监听 (通常与可见性变化行为类似)
            window.addEventListener('focus', () => {
                // this._debug('页面获得焦点，恢复正常频率并立即更新'); // Removed log
                this._adjustUpdateInterval(this._trafficUpdateInterval);
                this.updateStats();
            });

            window.addEventListener('blur', () => {
                // this._debug('页面失去焦点'); // Removed log
                // 失去焦点时可以考虑降低频率，但当前逻辑是保持不变或由visibilitychange处理
            });

            // 添加网络状态监听
            window.addEventListener('online', () => {
                // this._debug('网络已连接，尝试更新并重连WebSocket'); // Removed log
                this.updateStats(); // 网络恢复，立即更新

                // 如果WebSocket应该使用但未连接，尝试重连
                if (this._useWebSocket && (!this._sharedClient || !this._sharedClient.isConnected)) {
                    this._initWebSocket();
                }
            });

             window.addEventListener('offline', () => {
                 // this._debug('网络已断开'); // Removed log
                 // 网络断开时，WS会自动尝试重连（如果实现得好），HTTP请求会失败
                 // 无需特殊操作，等待 online 事件或定时器重试
             });
        },

        /**
         * 调整 HTTP 轮询更新间隔 (仅影响 _updateTimer)
         */
        _adjustUpdateInterval(interval) {
            // this._debug(`调整HTTP轮询间隔为: ${interval}ms`); // Removed log
            this._updateInterval = interval;
            // 重启实时数据更新定时器 (如果WS未连接)
            if (!this._useWebSocket || !this._sharedClient || !this._sharedClient.isConnected) {
                 this._startUpdateTimer(); // 这会清除旧定时器并用新间隔启动
            }
        },

        /**
         * 格式化带宽数据 (带缓存)
         * @param {number} bps - 比特每秒
         * @returns {string} 格式化后的字符串
         */
        _formatBandwidth(bps, compact = false) {
            // 简单缓存以减少重复计算和DOM更新跳动
            const cacheKey = `bw_${Math.round(bps / 1000)}_${compact ? 'c' : 's'}`; // 按 Kbps 级别缓存
            if (this._bandwidthCache && this._bandwidthCache[cacheKey]) {
                return this._bandwidthCache[cacheKey];
            }

            // 初始化缓存对象
            if (!this._bandwidthCache) {
                this._bandwidthCache = {};
            }

            // 限制缓存大小 (简单策略)
            const cacheKeys = Object.keys(this._bandwidthCache);
            if (cacheKeys.length > 50) { // 减少缓存大小
                delete this._bandwidthCache[cacheKeys[0]];
            }

            // 格式化并缓存结果
            const result = TrafficFormat.formatBps(bps, compact); // 使用标准格式化函数
            this._bandwidthCache[cacheKey] = result;
            return result;
        },

        /**
         * 格式化字节数 (带缓存)
         * @param {number|bigint} bytes - 字节数
         * @returns {string} 格式化后的字符串
         */
        _formatBytes(bytes) {
             // 对于 BigInt，转换为 Number 进行缓存 key 计算可能丢失精度，但对于显示足够
            const approxBytes = typeof bytes === 'bigint' ? Number(bytes) : bytes;
            const cacheKey = `bytes_${Math.round(approxBytes / 1024)}`; // 按 KB 缓存
            if (this._bytesCache && this._bytesCache[cacheKey]) {
                return this._bytesCache[cacheKey];
            }

            // 初始化缓存对象
            if (!this._bytesCache) {
                this._bytesCache = {};
            }

            // 限制缓存大小
            const cacheKeys = Object.keys(this._bytesCache);
            if (cacheKeys.length > 50) { // 减少缓存大小
                delete this._bytesCache[cacheKeys[0]];
            }

            // 格式化并缓存结果
            const result = TrafficFormat.formatBytes(bytes); // 使用标准格式化函数
            this._bytesCache[cacheKey] = result;
            return result;
        },

        /**
         * 辅助方法，更新UI为离线状态
         */
        _updateOfflineStatus() {
            // 更新关键状态显示为 '离线' 或 0
            const statusElements = {
                cpu: document.getElementById('CPU'),
                mem: document.getElementById('MEM'),
                swap: document.getElementById('SWAP'),
                // 主机名和OS信息通常是静态的，离线时不应清除
                // hostname: document.getElementById('system-hostname'),
                // os: document.getElementById('system-os'),
                uptime: document.getElementById('uptime-display') // 在线时间可以设为未知或N/A
            };

            if (statusElements.cpu) statusElements.cpu.textContent = '离线';
            if (statusElements.mem) statusElements.mem.textContent = '离线';
            if (statusElements.swap) statusElements.swap.textContent = '离线';
            if (statusElements.uptime) statusElements.uptime.textContent = '未知'; // 或 'N/A'

            // 网络设备表格流量可以不清空，因为是历史累计值
        },

        /**
         * 开始定时更新 (HTTP轮询，仅在WebSocket不可用时)
         */
        _startUpdateTimer() {
            // 如果已经有定时器，先清除
            if (this._updateTimer) {
                clearInterval(this._updateTimer);
                this._updateTimer = null;
            }

            // 检查WebSocket连接状态
            if (this._useWebSocket && this._sharedClient && this._sharedClient.isConnected) {
                // WebSocket已连接，不需要启动HTTP轮询
                return;
            }

            // 检查节点是否为主动模式
            const isActiveMode = this._isActiveNode();

            // 设置轮询间隔 - 主动模式使用较长间隔，非主动模式使用较短间隔
            const interval = isActiveMode ? 10000 : 5000; // 主动模式10秒，非主动模式5秒

            // 启动定时器
            this._updateTimer = setInterval(async () => {
                // 再次检查WebSocket连接状态，如果已连接则停止HTTP轮询
                if (this._useWebSocket && this._sharedClient && this._sharedClient.isConnected) {
                    clearInterval(this._updateTimer);
                    this._updateTimer = null;
                    return;
                }

                // 执行HTTP轮询
                try {
                    await this._fetchRealtimeStats();
                } catch (error) {
                    // 错误已在 _fetchRealtimeStats 中处理
                }
            }, interval);
        },

        /**
         * 开始流量数据更新定时器 (总是运行)
         */
        _startTrafficUpdateTimer() {
            // this._debug(`启动流量更新定时器，间隔: ${this._trafficUpdateInterval}ms`); // Removed log

            // 清除旧定时器
            if (this._trafficUpdateTimer) {
                clearInterval(this._trafficUpdateTimer);
                this._trafficUpdateTimer = null;
            }

            // 立即获取一次流量数据
             this.updateTrafficStats().catch(error => {
                 // 初始流量获取失败处理，无日志
             });

            // 设置新的定时器
            this._trafficUpdateTimer = setInterval(() => {
                // this._debug('流量更新定时器触发'); // Removed log
                 // 页面隐藏时也更新流量，因为流量统计通常不需要高频率交互
                if (!this._isTrafficUpdating) {
                    this.updateTrafficStats().catch(error => {
                        // 定时流量获取失败处理，无日志
                    });
                }
            }, this._trafficUpdateInterval);
        },

        /**
         * 停止所有定时更新
         */
        _stopUpdateTimer() {
            if (this._updateTimer) {
                // this._debug('停止实时数据更新定时器'); // Removed log
                clearInterval(this._updateTimer);
                this._updateTimer = null;
            }
            if (this._trafficUpdateTimer) {
                // this._debug('停止流量数据更新定时器'); // Removed log
                clearInterval(this._trafficUpdateTimer);
                this._trafficUpdateTimer = null;
            }
            // 清除可能的WebSocket重连定时器（如果未使用SharedWorker的内部重连）
            if (this._wsReconnectTimer) {
                clearTimeout(this._wsReconnectTimer);
                this._wsReconnectTimer = null;
            }
        },

        /**
         * 防抖函数
         * @param {Function} fn 要执行的函数
         * @returns {Function} 防抖后的函数
         */
        _debounce(fn) {
            return (...args) => {
                if (this._debounceTimer) {
                    clearTimeout(this._debounceTimer);
                }
                this._debounceTimer = setTimeout(() => fn.apply(this, args), DEBOUNCE_DELAY);
            };
        },

        /**
         * 获取节点ID
         * @returns {string|null} 节点ID
         */
        _getNodeId() {
            try {
                // this._debug('当前URL路径:', location.pathname); // Removed log

                // 1. 从URL路径的特定模式获取 (/stats/[nodeId])
                const statsMatch = location.pathname.match(/\/stats\/([^\/]+)/);
                if (statsMatch && statsMatch[1]) {
                    // this._debug('从URL (/stats/) 获取到节点ID:', statsMatch[1]); // Removed log
                    return statsMatch[1];
                }

                // 2. 从URL路径的最后一部分获取 (通用性更强，但可能不准确)
                const pathParts = location.pathname.split('/').filter(Boolean);
                if (pathParts.length > 0) {
                    const lastPart = pathParts[pathParts.length - 1];
                    // 简单验证一下 lastPart 是否像一个 ID (例如不是 'stats', 'traffic' 等)
                    if (lastPart && !['stats', 'traffic', 'data', 'latest'].includes(lastPart)) {
                       // this._debug('从URL路径最后一部分获取到节点ID:', lastPart); // Removed log
                       return lastPart;
                    }
                }

                // 3. 尝试从 #node-data 隐藏域获取 (如果存在)
                const nodeDataElement = document.getElementById('node-data');
                if (nodeDataElement && nodeDataElement.value) {
                    try {
                        const nodeData = JSON.parse(nodeDataElement.value);
                        if (nodeData && nodeData.id) {
                            // this._debug('从node-data元素获取到节点ID:', nodeData.id); // Removed log
                            return nodeData.id;
                        }
                    } catch (e) { /* 解析失败，忽略 */ }
                }

                // 4. 尝试从 #preprocessed-data 隐藏域获取 (如果存在)
                const preProcessedElement = document.getElementById('preprocessed-data');
                if (preProcessedElement && preProcessedElement.value) {
                    try {
                        const preProcessedData = JSON.parse(preProcessedElement.value);
                        // 假设预处理数据直接包含 id 或在某个属性下
                        if (preProcessedData && preProcessedData.id) {
                            // this._debug('从preprocessed-data元素获取到节点ID:', preProcessedData.id); // Removed log
                            return preProcessedData.id;
                        }
                        // 可能在 stat 对象里？
                         if (preProcessedData && preProcessedData.stat && preProcessedData.stat.id) {
                            // this._debug('从preprocessed-data.stat获取到节点ID:', preProcessedData.stat.id); // Removed log
                             return preProcessedData.stat.id;
                         }
                    } catch (e) { /* 解析失败，忽略 */ }
                }

                // console.warn('所有方法都无法获取到节点ID'); // Removed log
                return null;
            } catch (error) {
                // console.warn('获取节点ID时发生错误:', error); // Removed log
                return null;
            }
        },

        /**
         * 获取预处理数据 (用于初始化UI)
         * @returns {Object|null} 预处理数据
         */
        _getPreProcessedData() {
            try {
                // 优先尝试 #preprocessed-data
                const preProcessedElement = document.getElementById('preprocessed-data');
                if (preProcessedElement && preProcessedElement.value) {
                    try {
                        return JSON.parse(preProcessedElement.value);
                    } catch (parseError) {
                        // console.warn('解析 preprocessed-data 失败:', parseError); // Removed log
                    }
                }

                // 其次尝试 #node-data
                const nodeDataElement = document.getElementById('node-data');
                if (nodeDataElement && nodeDataElement.value) {
                    try {
                        return JSON.parse(nodeDataElement.value);
                    } catch (parseError) {
                        // console.warn('解析 node-data 失败:', parseError); // Removed log
                    }
                }

                return null; // 没有找到或解析失败
            } catch (error) {
                // console.warn('获取预处理数据失败:', error); // Removed log
                return null;
            }
        },

        /**
         * 检查节点是否为主动模式
         * @returns {boolean} 是否为主动模式
         * @private
         */
        _isActiveNode() {
            try {
                // 从预处理数据中获取 API 模式信息
                const preProcessedData = this._getPreProcessedData();

                // 检查是否存在主动模式标志
                if (preProcessedData) {
                    // 先检查新的数据结构
                    if (preProcessedData.data && preProcessedData.data.api) {
                        return preProcessedData.data.api.mode === true;
                    }

                    // 再检查旧的数据结构
                    if (preProcessedData.api) {
                        return preProcessedData.api.mode === true;
                    }
                }

                // 如果没有预处理数据或数据中没有模式信息，默认为非主动模式
                return false;
            } catch (error) {
                // 出错时默认非主动模式
                return false;
            }
        },

        /**
         * 检查WebSocket是否已连接
         * @returns {boolean} WebSocket是否已连接
         * @private
         */
        _isWebSocketConnected() {
            return this._useWebSocket && this._sharedClient && this._sharedClient.isConnected;
        },

       /**
         * 获取实时状态数据 (用于HTTP轮询)
         * @param {boolean} forceRefresh 是否强制刷新
         */
        async _fetchRealtimeStats(forceRefresh = false) {
            // 检查是否已有更新进行中
            if (this._isUpdating) return null;

            // 检查WebSocket连接状态 - 如果已连接且不是强制刷新，则跳过HTTP请求
            if (!forceRefresh && this._useWebSocket && this._sharedClient && this._sharedClient.isConnected) {
                // 如果WebSocket已连接，尝试从SharedWorker获取最新数据
                if (this._sharedClient) {
                    try {
                        this._sharedClient.requestLastData();
                    } catch (e) {
                        // 忽略错误
                    }
                }
                return null; // WebSocket已连接，不需要HTTP请求
            }

            // 标记正在更新
            this._isUpdating = true;
            this._retryCount = 0; // 重置重试计数器

            try {
                // 获取节点ID
                const nodeId = this._getNodeId();
                if (!nodeId) {
                    throw new Error('无法获取节点ID');
                }

                // 检查节点是否为主动模式
                const isActiveMode = this._isActiveNode();

                // 只发送一个请求 - 只使用 /latest 接口
                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

                    const latestResponse = await fetch(`/stats/${nodeId}/latest`, { signal: controller.signal });
                    clearTimeout(timeoutId);

                    if (latestResponse.ok) {
                        const latestData = await latestResponse.json();

                        // 验证数据结构
                        if (latestData && latestData.status && latestData.data && latestData.data[nodeId]) {
                            const nodeData = latestData.data[nodeId];
                            const dataToProcess = { // 构造成通知订阅者需要的格式
                                sid: nodeId,
                                name: nodeData.name,
                                stat: nodeData.stat
                            };

                            // 触发自定义事件 (如果其他组件需要监听)
                            const updateEvent = new CustomEvent('statsDataUpdated', {
                                detail: { timestamp: latestData.timestamp, data: nodeData }
                            });
                            document.dispatchEvent(updateEvent);

                            // 通知订阅者并返回数据
                            this._notifySubscribers(dataToProcess);
                            return dataToProcess;
                        } else {
                            // 数据格式无效
                            throw new Error('接口返回数据格式无效');
                        }
                    } else {
                        // 请求失败
                        throw new Error(`获取最新数据失败: ${latestResponse.status}`);
                    }

                } catch (error) {
                    // 处理错误
                    if (this._retryCount < this._maxRetries) {
                        this._retryCount++;
                        await new Promise(resolve => setTimeout(resolve, 1000 * this._retryCount)); // 等待时间递增
                        this._isUpdating = false; // 允许下一次尝试
                        return this._fetchRealtimeStats(forceRefresh); // 重新尝试
                    } else {
                        // 达到最大重试次数

                        // 如果不是主动模式，减少重试或停止重试
                        if (!isActiveMode) {
                            this._updateOfflineStatus(); // 更新UI为离线
                            return null; // 返回 null 表示停止重试
                        }

                        // 多次重试失败，更新UI到离线状态
                        this._updateOfflineStatus();
                        throw error; // 将最终错误抛出，由上层处理
                    }
                }

            } catch (error) {
                // 最外层错误处理
                this._updateOfflineStatus(); // 更新UI为离线
                return null; // 返回 null 表示出错
            } finally {
                this._isUpdating = false; // 确保解除锁定状态
            }
        },


        /**
         * 更新所有统计信息 (通常由外部事件触发，如页面可见、获得焦点)
         * 这个函数现在主要负责触发实时数据和流量数据的更新逻辑。
         */
        async updateStats() {
            // 防止在已有更新进行时重复触发
            if (this._isUpdating || this._isTrafficUpdating) {
                return;
            }

            try {
                // 检查WebSocket连接状态
                const wsConnected = this._useWebSocket && this._sharedClient && this._sharedClient.isConnected;

                // 1. 获取实时数据
                if (wsConnected) {
                    // WebSocket已连接，尝试从SharedWorker获取最新数据
                    if (this._sharedClient) {
                        try {
                            this._sharedClient.requestLastData();
                        } catch (e) {
                            // 忽略错误
                        }
                    }
                } else {
                    // WebSocket未连接，使用HTTP请求获取数据
                    await this._fetchRealtimeStats();
                }

                // 2. 获取流量数据 (总是获取，因为它有独立的定时器和API)
                await this.updateTrafficStats();

            } catch (error) {
                // 错误已在各自的 fetch 函数中处理（包括重试和UI更新）
                // 此处无需额外处理，避免重复记录错误
            }
        },

        /**
         * 更新流量统计信息 (简化版)
         * 只负责获取数据并存储到全局变量，由 TrafficChartManager 负责图表更新
         */
        async updateTrafficStats() {
            // 检查是否处于标签页恢复状态
            const isRecoveringFromSleep = window.TabLifecycleHandler && window.TabLifecycleHandler.isRecoveringFromSleep;
            const shouldBlockDataUpdates = window.TabLifecycleHandler && window.TabLifecycleHandler.blockDataUpdates;

            if (isRecoveringFromSleep && shouldBlockDataUpdates) {
                console.log('流量数据更新被阻止，标签页正在从休眠中恢复');
                return; // 跳过更新
            }

            if (this._isTrafficUpdating) {
                return;
            }

            this._isTrafficUpdating = true;

            try {
                const nodeId = this._getNodeId();
                if (!nodeId) {
                    throw new Error('无法获取节点ID');
                }

                // 设置超时
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 8000); // 8秒超时

                const response = await fetch(`/stats/${nodeId}/traffic`, { signal: controller.signal });
                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`获取流量数据失败: ${response.status}`);
                }

                const result = await response.json();
                if (!result || !result.data) {
                    throw new Error('无效的流量数据格式');
                }

                // 存储流量数据，供图表初始化后使用
                if (!window.trafficData) {
                    window.trafficData = {};
                }

                // 存储各类流量数据
                if (Array.isArray(result.data.hs)) window.trafficData.hs = result.data.hs;
                if (Array.isArray(result.data.ds)) window.trafficData.ds = result.data.ds;
                if (Array.isArray(result.data.ms)) window.trafficData.ms = result.data.ms;

                // 触发自定义事件，通知其他组件数据已更新
                const trafficUpdateEvent = new CustomEvent('trafficDataUpdated', {
                    detail: { timestamp: Date.now(), data: result.data }
                });
                document.dispatchEvent(trafficUpdateEvent);

                // 不再直接调用 TrafficChartManager 的方法
                // 而是通过 window.trafficData 传递数据
                // TrafficChartManager 应该监听 trafficDataUpdated 事件或定期检查 window.trafficData

                // 更新月度流量显示
                if (result.data.monthly) {
                    await this._updateTrafficDisplay(result.data.monthly);
                }

            } catch (error) {
                // 静默处理错误，避免重试循环
            } finally {
                this._isTrafficUpdating = false;
            }
        },

        /**
         * 更新流量显示相关的DOM元素
         * @param {Object} monthly - 月度流量数据对象
         */
        async _updateTrafficDisplay(monthly) {
            // this._debug('开始更新流量显示DOM:', monthly); // Removed log
            try {
                // 判断是否为无限制流量
                const isUnlimited = monthly.remaining === -1 && monthly.limit === 0;
                // this._debug('是否为无限制流量:', isUnlimited); // Removed log

                // 验证和处理数据
                const validData = {
                    used: Math.max(0, Number(monthly.used) || 0),
                    // 对于 Infinity，格式化时会处理
                    remaining: isUnlimited ? Infinity : Math.max(0, Number(monthly.remaining) || 0),
                    limit: isUnlimited ? Infinity : Math.max(0, Number(monthly.limit) || 0),
                    // 计算比例，如果是无限则为0
                    ratio: isUnlimited ? 0 : (monthly.limit > 0 ? Math.max(0, Math.min(100, (monthly.used / monthly.limit) * 100)) : 0).toFixed(0),
                    reset_day: monthly.reset_day || 1,
                    status: monthly.status || 'normal',
                    isUnlimited: isUnlimited,
                    next_reset: monthly.next_reset // 下次重置时间戳 (秒)
                };

                // this._debug('处理后的流量数据:', validData); // Removed log

                // 获取格式化工具，确保已加载
                const format = window.TrafficFormat;
                if (!format) {
                     throw new Error('TrafficFormat 工具未定义');
                }

                // 格式化流量数据用于显示
                const formattedData = {
                    used: format.formatBytes(validData.used),
                    remaining: isUnlimited ? '无限制' : format.formatBytes(validData.remaining),
                    limit: isUnlimited ? '无限制' : format.formatBytes(validData.limit),
                    ratio: validData.ratio // ratio 是数字字符串，直接用
                };

                // this._debug('格式化后的流量数据:', formattedData); // Removed log

                // 根据不同情况生成提示语
                const getTips = (isUnlimited, ratio) => {
                    if (isUnlimited) return '随便造吧';
                    const r = parseFloat(ratio);
                    if (r < 50) return '弹药充足';
                    if (r < 80) return '够用不愁';
                    if (r < 90) return '悠着点啦';
                    return '该补货了';
                };
                const tips = getTips(isUnlimited, validData.ratio);
                // this._debug('当前提示语:', tips); // Removed log

                // 获取DOM元素 - 只保留系统概览元素
                const elements = {
                    // 系统概览元素
                    usedOverview: document.getElementById('monthly-used-overview'),
                    remainingOverview: document.getElementById('monthly-remaining-overview'),
                    limitOverview: document.getElementById('monthly-limit-overview'),
                    progressOverview: document.getElementById('monthly-progress-overview'),
                    percentOverview: document.getElementById('monthly-percent-overview')
                };
                
                // 更新系统概览元素
                if (elements.usedOverview) elements.usedOverview.innerHTML = formattedData.used;
                if (elements.remainingOverview) elements.remainingOverview.innerHTML = formattedData.remaining;
                if (elements.limitOverview) elements.limitOverview.innerHTML = formattedData.limit;
                
                // 更新系统概览进度条
                if (elements.progressOverview) {
                    elements.progressOverview.style.width = isUnlimited ? '10%' : `${formattedData.ratio}%`;
                    // 移除旧的颜色类，添加新的
                    elements.progressOverview.classList.remove('bg-primary-500', 'bg-blue-500', 'bg-yellow-500', 'bg-red-500',
                                                              'dark:bg-primary-400', 'dark:bg-blue-400', 'dark:bg-yellow-400', 'dark:bg-red-400');
                    if (!isUnlimited) {
                        const r = parseFloat(validData.ratio);
                        if (r >= 90) {
                            elements.progressOverview.classList.add('bg-red-500', 'dark:bg-red-400');
                        } else if (r >= 80) {
                            elements.progressOverview.classList.add('bg-yellow-500', 'dark:bg-yellow-400');
                        } else if (r >= 50) {
                            elements.progressOverview.classList.add('bg-blue-500', 'dark:bg-blue-400');
                        } else {
                            elements.progressOverview.classList.add('bg-primary-500', 'dark:bg-primary-400');
                        }
                    } else {
                        elements.progressOverview.classList.add('bg-primary-500', 'dark:bg-primary-400');
                    }
                }

                // 更新系统概览中的月度流量百分比
                if (elements.percentOverview) {
                    elements.percentOverview.textContent = isUnlimited ? '∞' : `${validData.ratio}%`;
                }
            } catch (displayError) {
                 // console.error('[Debug] 更新流量显示DOM时出错:', displayError); // Removed log
                 // 可以考虑给用户一些反馈
            }
        },

        /**
         * 更新其他统计信息 (CPU, Mem, Swap, Host, Network Speed等)
         * 由 _notifySubscribers 调用
         * @param {Object} data 统计数据 (格式: { [sid]: { name: 'xxx', stat: {... 或 false} } })
         */
        _updateOtherStats(data) {
            // this._debug('接收到 _updateOtherStats 数据:', data); // Removed log
            try {
                const nodeId = this._getNodeId();
                if (!nodeId || !data || !data[nodeId]) {
                    // console.warn('[Debug] 无效的节点数据传入 _updateOtherStats:', {nodeId, data}); // Removed log
                    return;
                }

                const nodeData = data[nodeId];
                const stat = nodeData.stat; // stat 可以是对象或 false

                // 检查节点是否离线
                if (stat === false || (stat && typeof stat === 'object' && stat.offline === true)) {
                    // console.warn(`[Debug] 节点 ${nodeId} 已离线，更新UI状态`); // Removed log
                    this._updateOfflineStatus();
                    return;
                }

                // 确保 stat 是一个有效的对象
                if (typeof stat !== 'object' || stat === null) {
                    // console.warn('[Debug] stat 数据不是有效对象:', stat); // Removed log
                     this._updateOfflineStatus(); // 数据无效，也按离线处理
                    return;
                }

                // this._debug('开始更新系统状态DOM:', stat); // Removed log

                // ---- 更新系统信息 (Host) ----
                if (stat.host) {
                    const hostnameElem = document.getElementById('system-hostname');
                    const osElem = document.getElementById('system-os');
                    const uptimeElem = document.getElementById('uptime-display');

                    // 更新主机名
                    if (hostnameElem && stat.host.hostname) {
                        hostnameElem.textContent = stat.host.hostname;
                        hostnameElem.classList.remove('hidden'); // 确保可见
                        document.getElementById('system-hostname-skeleton')?.classList.add('hidden'); // 隐藏骨架
                    }
                    // 更新OS信息
                    if (osElem && stat.host.platform) {
                        const osInfo = `${stat.host.platform} ${stat.host.platformVersion || ''}`.trim();
                        osElem.textContent = osInfo;
                    }
                    // 更新在线时间
                    if (uptimeElem && typeof stat.host.uptime === 'number') {
                        const uptimeText = TrafficFormat.formatUptime(stat.host.uptime);
                        uptimeElem.textContent = uptimeText;
                        uptimeElem.classList.remove('hidden'); // 确保可见
                        document.getElementById('uptime-display-skeleton')?.classList.add('hidden'); // 隐藏骨架
                    }
                }


                // ---- 更新内存和Swap使用率 ----
                if (stat.mem) {
                    // 虚拟内存 (RAM)
                    if (stat.mem.virtual) {
                        const memTotalElem = document.getElementById('MEM');

                        const memTotal = Number(stat.mem.virtual.total) || 0;
                        const memUsed = Number(stat.mem.virtual.used) || 0;

                        if (memTotal > 0) {
                            const memUsage = ((memUsed / memTotal) * 100).toFixed(0);
                            if (memTotalElem) {
                                memTotalElem.textContent = `${memUsage}%`;
                                memTotalElem.classList.add('fixed-width-value', 'fixed-width-memory');
                            }
                        } else {
                            // 内存总量无效
                            if (memTotalElem) memTotalElem.textContent = 'N/A';
                        }
                    }
                    // Swap 内存 - 已移除详细显示，只保留系统概览更新
                }

                // ---- 更新网络速度 (Delta) ----
                if (stat.net && stat.net.delta) {
                    const deltaIn = Number(stat.net.delta.in) || 0; // 确保是数字
                    const deltaOut = Number(stat.net.delta.out) || 0; // 确保是数字
                    
                    // 更新系统概览中的网络带宽（使用紧凑模式）
                    const netInOverviewElem = document.getElementById('NET_IN_overview');
                    const netOutOverviewElem = document.getElementById('NET_OUT_overview');
                    if (netInOverviewElem) {
                        netInOverviewElem.innerHTML = this._formatBandwidth(deltaIn * 8, true); // 字节转比特，紧凑模式
                    }
                    if (netOutOverviewElem) {
                        netOutOverviewElem.innerHTML = this._formatBandwidth(deltaOut * 8, true); // 字节转比特，紧凑模式
                    }
                    
                    // 更新 ECharts 速度仪表盘
                    if (this._networkGaugeChart) {
                        // 计算总速度 (下载 + 上传)
                        const totalSpeed = (deltaIn + deltaOut) * 8; // 转换为比特
                        
                        // 转换为 Mbps 用于显示
                        const speedMbps = totalSpeed / 1000000;
                        
                        // 先调整速度范围
                        this._adjustSpeedRange(speedMbps);
                        
                        // 获取当前速度范围配置
                        const currentRange = this._speedRanges[this._currentSpeedRange];
                        
                        // 根据速度相对于当前范围的比例决定颜色
                        let gaugeColor = '#22c55e'; // 绿色
                        const speedRatio = speedMbps / currentRange.max;
                        if (speedRatio > 0.8) {
                            gaugeColor = '#ef4444'; // 红色 - 接近满载
                        } else if (speedRatio > 0.6) {
                            gaugeColor = '#f97316'; // 橙色 - 较高负载
                        } else if (speedRatio > 0.3) {
                            gaugeColor = '#eab308'; // 黄色 - 中等负载
                        }
                        
                        // 更新仪表盘
                        this._networkGaugeChart.setOption({
                            series: [{
                                itemStyle: {
                                    color: gaugeColor
                                },
                                data: [{
                                    value: speedMbps
                                }]
                            }]
                        });
                    }
                } else {
                    // 如果没有 delta 数据，将速度设为 0
                    // 更新系统概览中的网络带宽（使用紧凑模式）
                    const netInOverviewElem = document.getElementById('NET_IN_overview');
                    const netOutOverviewElem = document.getElementById('NET_OUT_overview');
                    if (netInOverviewElem) netInOverviewElem.innerHTML = '0<span class="metric-unit-compact">b</span>';
                    if (netOutOverviewElem) netOutOverviewElem.innerHTML = '0<span class="metric-unit-compact">b</span>';
                    
                    // 重置 ECharts 速度仪表盘
                    if (this._networkGaugeChart) {
                        // 速度为0时，调整到最低档位
                        this._adjustSpeedRange(0);
                        
                        this._networkGaugeChart.setOption({
                            series: [{
                                itemStyle: {
                                    color: '#22c55e' // 绿色
                                },
                                data: [{
                                    value: 0
                                }]
                            }]
                        });
                    }
                }

                 // ---- 更新网络总流量 ----
                 if (stat.net && stat.net.total) {
                     const netInTotalOverview = document.getElementById('NET_IN_TOTAL_overview');
                     const netOutTotalOverview = document.getElementById('NET_OUT_TOTAL_overview');
                     
                     if (netInTotalOverview && typeof stat.net.total.in === 'number') {
                         netInTotalOverview.innerHTML = this._formatBytes(stat.net.total.in);
                     }
                     if (netOutTotalOverview && typeof stat.net.total.out === 'number') {
                         netOutTotalOverview.innerHTML = this._formatBytes(stat.net.total.out);
                     }
                 }

                // ---- 更新系统概览圆形进度条 ----
                // CPU圆形进度条
                if (stat.cpu && typeof stat.cpu.multi === 'number') {
                    const cpuCircle = document.getElementById('cpu-progress-circle');
                    if (cpuCircle) {
                        const cpuPercent = stat.cpu.multi;
                        cpuCircle.setAttribute('stroke-dashoffset', 201.06 - (201.06 * cpuPercent));
                    }
                    // 更新左侧的大字体百分比
                    const cpuMainText = document.getElementById('cpu-percent-overview');
                    if (cpuMainText) {
                        cpuMainText.innerHTML = `${(stat.cpu.multi * 100).toFixed(0)}<span class="text-lg font-normal text-slate-600 dark:text-slate-400">%</span>`;
                    }
                    // 更新核心数
                    const cpuCoresText = document.getElementById('cpu-cores-overview');
                    if (cpuCoresText && Array.isArray(stat.cpu.single)) {
                        cpuCoresText.textContent = `${stat.cpu.single.length} 核心`;
                    }
                    
                    // CPU核心热力图已移除
                    
                }

                // 内存圆形进度条
                if (stat.mem && stat.mem.virtual) {
                    const memCircle = document.getElementById('mem-progress-circle');
                    if (memCircle && stat.mem.virtual.total > 0) {
                        const memPercent = stat.mem.virtual.used / stat.mem.virtual.total;
                        memCircle.setAttribute('stroke-dashoffset', 201.06 - (201.06 * memPercent));
                    }
                    // 圆形进度条中心已改为显示"RAM"，不再需要更新百分比
                    // 更新左侧的大字体百分比
                    const memMainText = document.getElementById('mem-percent-overview');
                    if (memMainText && stat.mem.virtual.total > 0) {
                        memMainText.innerHTML = `${((stat.mem.virtual.used / stat.mem.virtual.total) * 100).toFixed(0)}<span class="text-sm font-normal text-slate-600 dark:text-slate-400">%</span>`;
                    }
                    // 更新内存使用详情
                    const memDetailText = document.getElementById('mem-detail-overview');
                    if (memDetailText) {
                        memDetailText.innerHTML = `${this._formatBytes(stat.mem.virtual.used)} / ${this._formatBytes(stat.mem.virtual.total)}`;
                    }
                    
                    // 更新Swap信息（如果存在）
                    if (stat.mem.swap && stat.mem.swap.total > 0) {
                        const swapCircle = document.getElementById('swap-progress-circle');
                        if (swapCircle) {
                            const swapPercent = stat.mem.swap.used / stat.mem.swap.total;
                            swapCircle.setAttribute('stroke-dashoffset', 125.66 - (125.66 * swapPercent));
                        }
                        const swapPercentText = document.getElementById('swap-percent-overview');
                        if (swapPercentText) {
                            swapPercentText.innerHTML = `${((stat.mem.swap.used / stat.mem.swap.total) * 100).toFixed(0)}<span class="text-sm font-normal text-slate-600 dark:text-slate-400">%</span>`;
                        }
                        const swapDetailText = document.getElementById('swap-detail-overview');
                        if (swapDetailText) {
                            swapDetailText.innerHTML = `${this._formatBytes(stat.mem.swap.used)} / ${this._formatBytes(stat.mem.swap.total)}`;
                        }
                    }
                }

                // 硬盘圆形进度条
                if (stat.disk && stat.disk.total > 0) {
                    const diskCircle = document.querySelector('.text-amber-500.dark\\:text-amber-400[stroke-dasharray="150.8"]');
                    if (diskCircle) {
                        const diskPercent = stat.disk.used / stat.disk.total;
                        diskCircle.setAttribute('stroke-dashoffset', 150.8 - (150.8 * diskPercent));
                    }
                    // 圆形进度条中心已改为显示"DISK"，不再需要更新百分比
                    // 更新左侧的大字体百分比
                    const diskMainText = document.querySelector('.card:has(.text-amber-600) .text-2xl.font-bold');
                    if (diskMainText) {
                        diskMainText.innerHTML = `${((stat.disk.used / stat.disk.total) * 100).toFixed(0)}<span class="text-base font-normal text-slate-600 dark:text-slate-400">%</span>`;
                    }
                    // 硬盘使用详情已从UI中移除，注释掉相关代码
                    // const diskDetailText = document.querySelector('.card:has(.text-amber-600) .text-xs.text-slate-500');
                    // if (diskDetailText) {
                    //     diskDetailText.innerHTML = `${this._formatBytes(stat.disk.used)} / ${this._formatBytes(stat.disk.total)}`;
                    // }
                }
                 
                 // ---- 更新网络设备总流量 (Total) ----
                 // 注意：这部分数据通常来自 WebSocket 推送或特定的流量接口，
                 // 如果实时数据 stat 中也包含这部分，就在这里更新
                 // 否则，这部分应由 updateTrafficStats 或 WebSocket 回调处理
                 if (stat.net && stat.net.devices) {
                     Object.entries(stat.net.devices).forEach(([device, netData]) => {
                         const inElement = document.getElementById(`net_${device}_total_in`);
                         const outElement = document.getElementById(`net_${device}_total_out`);

                         if (netData.total) {
                             if (inElement && typeof netData.total.in === 'number') {
                                 this.setFormattedContent(inElement, this._formatBytes(netData.total.in));
                             }
                             if (outElement && typeof netData.total.out === 'number') {
                                 this.setFormattedContent(outElement, this._formatBytes(netData.total.out));
                             }
                         }
                     });
                 }

                // ---- 更新磁盘使用率 ----
                 if (Array.isArray(stat.disk?.partitions)) {
                     stat.disk.partitions.forEach((part, index) => {
                         const progressElem = document.getElementById(`disk_${index}_progress`);
                         const detailElem = document.getElementById(`disk_${index}_detail`);
                         const usageElem = document.getElementById(`disk_${index}_usage`); // 百分比显示

                         if (part && typeof part.percent === 'number') {
                             const usagePercent = part.percent.toFixed(0);
                             if (usageElem) {
                                 usageElem.textContent = `${usagePercent}%`;
                             }
                             if (progressElem) {
                                 progressElem.style.width = `${usagePercent}%`;
                                 // 可以根据使用率设置颜色
                                 const r = parseFloat(usagePercent);
                                 let colorClass = 'bg-teal-500'; // Default for disk
                                 if (r >= 95) colorClass = 'bg-red-500';
                                 else if (r >= 85) colorClass = 'bg-yellow-500';
                                 progressElem.className = `progress-bar ${colorClass}`;
                             }
                             if (detailElem && typeof part.used === 'number' && typeof part.total === 'number') {
                                 this.setFormattedContent(detailElem, `${this._formatBytes(part.used)} / ${this._formatBytes(part.total)}`);
                             }
                         } else {
                              // 分区数据无效
                              if (usageElem) usageElem.textContent = 'N/A';
                              if (progressElem) progressElem.style.width = '0%';
                              if (detailElem) detailElem.textContent = 'N/A';
                         }
                     });
                 }

                // ---- 设置全局数据状态并触发事件 ----
                // 设置全局statsData，供其他组件使用
                if (!window.statsData) window.statsData = {};
                window.statsData[nodeId] = { name: nodeData.name, stat: stat };

                // 触发statsUpdated事件，通知其他组件数据已更新
                document.dispatchEvent(new CustomEvent('statsUpdated', {
                    detail: { nodeId, stat, data }
                }));

            } catch (error) {
                // 更新UI时发生错误处理，避免使用 console.error
                // 也许可以记录到一个内部错误列表
                // 考虑是否需要将对应部分UI置为错误状态
            }
        }
    };

    // 导出全局函数和对象 (保持不变)
    window.TrafficFormat = TrafficFormat;
    window.TrafficUtils = TrafficUtils;
    window.StatManager = StatManager;

    // 兼容旧代码的全局函数 - 避免覆盖已定义的函数
    window.strB = (bytes) => TrafficFormat.formatBytes(bytes);
    if (typeof window.strbps === 'undefined') {
        window.strbps = (bps) => TrafficFormat.formatBps(bps);
    }


    /**
     * 负载详情标签页管理器 (独立实现版)
     * 不再依赖ChartTabManager，直接实现基本功能
     */
    const LoadTabManager = {
        init() {
            // 初始化标签页功能
            console.log('初始化标签页管理器');
            
            // 添加事件监听器
            document.querySelectorAll('.tab-button').forEach(tab => {
                tab.addEventListener('click', () => {
                    this.activateTab(tab);
                });
            });
            
            // 激活默认标签页
            const defaultTab = document.querySelector('.tab-button.active') || 
                              document.querySelector('.tab-button');
            if (defaultTab) {
                this.activateTab(defaultTab);
            }
        },

        /**
         * 激活标签页
         * @param {HTMLElement} tab - 标签按钮元素
         */
        activateTab(tab) {
            // 检查参数
            if (!tab || !tab.dataset || !tab.dataset.tab) {
                console.warn('无效的标签元素');
                return;
            }

            // 获取目标内容ID
            const targetId = tab.dataset.tab;
            const tabContent = document.getElementById(targetId);

            if (!tabContent) {
                console.warn(`找不到标签内容: ${targetId}`);
                return;
            }

            // 移除所有活动状态
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // 设置活动状态
            tab.classList.add('active');
            tabContent.classList.remove('hidden');

            // 确保图表容器可见
            this._ensureContainerVisible(tabContent);
        },

        /**
         * 确保图表容器可见
         * @param {HTMLElement} tabContent - 标签内容元素
         * @private
         */
        _ensureContainerVisible(tabContent) {
            // 查找图表容器
            const chartContainers = tabContent.querySelectorAll('.chart-container');
            
            // 确保容器可见
            chartContainers.forEach(container => {
                container.classList.remove('hidden');
                if (container.hasAttribute('hidden')) {
                    container.removeAttribute('hidden');
                }
            });
        }
    };

    // DOM加载完成后初始化 (保持不变)
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            StatManager.init();
            LoadTabManager.init();
        });
    } else {
        // 如果 DOM 已经加载完成
        StatManager.init();
        LoadTabManager.init();
    }

})();
