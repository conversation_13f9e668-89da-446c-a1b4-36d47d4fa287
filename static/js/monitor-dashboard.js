/**
 * 监控仪表盘 - 增强版监控界面
 * 提供更好的数据可视化和交互体验
 */

class MonitorDashboard {
    constructor() {
        this.charts = {};
        this.refreshInterval = 30000; // 30秒刷新
        this.updateTimer = null;
        this.isInitialized = false;
        
        // 数据缓存
        this.cache = {
            categories: [],
            targets: [],
            nodes: [],
            nodeStatus: {},
            trafficData: [],
            alertsData: []
        };
        
        // 图表配置
        this.chartOptions = {
            theme: {
                mode: 'light',
                palette: 'palette1',
                monochrome: {
                    enabled: false,
                    color: '#8b5cf6',
                    shadeTo: 'light',
                    shadeIntensity: 0.65
                }
            }
        };
        
        this.init();
    }
    
    async init() {
        try {
            // 初始化DOM元素
            this.initializeDOM();
            
            // 加载初始数据
            await this.loadAllData();
            
            // 初始化图表
            this.initializeCharts();
            
            // 绑定事件
            this.bindEvents();
            
            // 开始自动更新
            this.startAutoUpdate();
            
            this.isInitialized = true;
        } catch (error) {
            console.error('监控仪表盘初始化失败:', error);
            this.showError('初始化失败，请刷新页面重试');
        }
    }
    
    initializeDOM() {
        // 创建主容器
        this.container = document.getElementById('monitor-dashboard');
        if (!this.container) {
            console.error('找不到监控仪表盘容器');
            return;
        }
        
        // 创建各个区域
        this.createDashboardLayout();
    }
    
    createDashboardLayout() {
        this.container.innerHTML = `
            <!-- 统计卡片区域 -->
            <div class="stats-cards-container grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="stat-card" id="total-nodes-card">
                    <div class="stat-card-content">
                        <div class="stat-icon bg-purple-100 dark:bg-purple-900/30">
                            <i class="ti ti-device-desktop text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-title">总节点数</h3>
                            <p class="stat-value" id="total-nodes-value">--</p>
                            <span class="stat-trend" id="total-nodes-trend"></span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card" id="online-nodes-card">
                    <div class="stat-card-content">
                        <div class="stat-icon bg-green-100 dark:bg-green-900/30">
                            <i class="ti ti-wifi text-green-600 dark:text-green-400"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-title">在线节点</h3>
                            <p class="stat-value" id="online-nodes-value">--</p>
                            <span class="stat-trend" id="online-nodes-trend"></span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card" id="avg-latency-card">
                    <div class="stat-card-content">
                        <div class="stat-icon bg-blue-100 dark:bg-blue-900/30">
                            <i class="ti ti-gauge text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-title">平均延迟</h3>
                            <p class="stat-value" id="avg-latency-value">--</p>
                            <span class="stat-trend" id="avg-latency-trend"></span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card" id="alerts-card">
                    <div class="stat-card-content">
                        <div class="stat-icon bg-red-100 dark:bg-red-900/30">
                            <i class="ti ti-alert-triangle text-red-600 dark:text-red-400"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-title">活跃告警</h3>
                            <p class="stat-value" id="alerts-value">--</p>
                            <span class="stat-trend" id="alerts-trend"></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 图表区域 -->
            <div class="charts-grid grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- 节点状态分布图 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">节点状态分布</h3>
                        <div class="chart-actions">
                            <button class="chart-action-btn" onclick="dashboard.refreshNodeStatusChart()">
                                <i class="ti ti-refresh"></i>
                            </button>
                        </div>
                    </div>
                    <div class="chart-body">
                        <div id="node-status-chart" class="chart-container"></div>
                    </div>
                </div>
                
                <!-- 响应时间趋势图 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">响应时间趋势</h3>
                        <div class="chart-actions">
                            <select class="chart-filter" id="latency-time-range">
                                <option value="1h">1小时</option>
                                <option value="6h">6小时</option>
                                <option value="24h" selected>24小时</option>
                                <option value="7d">7天</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-body">
                        <div id="latency-trend-chart" class="chart-container"></div>
                    </div>
                </div>
                
                <!-- 地区分布图 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">节点地区分布</h3>
                        <div class="chart-actions">
                            <button class="chart-action-btn" onclick="dashboard.toggleRegionView()">
                                <i class="ti ti-world"></i>
                            </button>
                        </div>
                    </div>
                    <div class="chart-body">
                        <div id="region-distribution-chart" class="chart-container"></div>
                    </div>
                </div>
                
                <!-- 流量统计图 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">流量统计</h3>
                        <div class="chart-actions">
                            <select class="chart-filter" id="traffic-type">
                                <option value="both">上传/下载</option>
                                <option value="upload">仅上传</option>
                                <option value="download">仅下载</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-body">
                        <div id="traffic-chart" class="chart-container"></div>
                    </div>
                </div>
            </div>
            
            <!-- 节点列表区域 -->
            <div class="nodes-section">
                <div class="section-header">
                    <h3 class="section-title">节点监控详情</h3>
                    <div class="section-actions">
                        <input type="text" id="node-search" class="node-search-input" placeholder="搜索节点...">
                        <select id="node-filter-status" class="node-filter">
                            <option value="all">全部状态</option>
                            <option value="online">在线</option>
                            <option value="offline">离线</option>
                            <option value="warning">警告</option>
                        </select>
                        <button class="action-btn primary" onclick="dashboard.exportNodeData()">
                            <i class="ti ti-download"></i>
                            导出数据
                        </button>
                    </div>
                </div>
                
                <div class="nodes-table-container">
                    <table class="nodes-table" id="nodes-table">
                        <thead>
                            <tr>
                                <th>节点名称</th>
                                <th>状态</th>
                                <th>地区</th>
                                <th>延迟</th>
                                <th>上行</th>
                                <th>下行</th>
                                <th>CPU</th>
                                <th>内存</th>
                                <th>在线时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="nodes-table-body">
                            <!-- 动态加载节点数据 -->
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 实时事件流 -->
            <div class="events-section mt-6">
                <div class="section-header">
                    <h3 class="section-title">实时事件</h3>
                    <div class="section-actions">
                        <button class="action-btn" onclick="dashboard.clearEvents()">
                            <i class="ti ti-clear-all"></i>
                            清空
                        </button>
                    </div>
                </div>
                <div class="events-container" id="events-container">
                    <!-- 动态加载事件 -->
                </div>
            </div>
        `;
    }
    
    async loadAllData() {
        try {
            const [categoriesRes, targetsRes, nodesRes, statusRes, trafficRes] = await Promise.all([
                fetch('/api/monitor/regions'),
                fetch('/api/monitor/targets'),
                fetch('/api/monitor/nodes'),
                fetch('/api/allnode_status'),
                fetch('/api/traffic/latest')
            ]);
            
            this.cache.categories = await this.handleResponse(categoriesRes);
            this.cache.targets = await this.handleResponse(targetsRes);
            this.cache.nodes = await this.handleResponse(nodesRes);
            this.cache.nodeStatus = await this.handleResponse(statusRes);
            this.cache.trafficData = await this.handleResponse(trafficRes);
            
            // 处理数据
            this.processData();
            
            // 更新统计卡片
            this.updateStatCards();
            
            // 更新节点表格
            this.updateNodesTable();
            
        } catch (error) {
            console.error('加载数据失败:', error);
            throw error;
        }
    }
    
    async handleResponse(response) {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        return data.success ? (data.data || data) : [];
    }
    
    processData() {
        // 计算统计数据
        const totalNodes = this.cache.nodes.length;
        let onlineNodes = 0;
        let totalLatency = 0;
        let latencyCount = 0;
        
        this.cache.nodes.forEach(node => {
            const status = this.cache.nodeStatus[node.id];
            if (status) {
                if (status.stat && !status.stat.offline) {
                    onlineNodes++;
                }
                if (status.data && status.data.latency) {
                    totalLatency += status.data.latency;
                    latencyCount++;
                }
            }
        });
        
        this.stats = {
            totalNodes,
            onlineNodes,
            offlineNodes: totalNodes - onlineNodes,
            avgLatency: latencyCount > 0 ? Math.round(totalLatency / latencyCount) : 0,
            activeAlerts: this.countActiveAlerts()
        };
    }
    
    countActiveAlerts() {
        let alerts = 0;
        this.cache.nodes.forEach(node => {
            const status = this.cache.nodeStatus[node.id];
            if (status && status.data) {
                // CPU > 90%
                if (status.data.cpu && status.data.cpu > 90) alerts++;
                // 内存 > 90%
                if (status.data.memory && status.data.memory > 90) alerts++;
                // 磁盘 > 90%
                if (status.data.disk && status.data.disk > 90) alerts++;
                // 离线
                if (status.stat && status.stat.offline) alerts++;
            }
        });
        return alerts;
    }
    
    updateStatCards() {
        // 更新总节点数
        document.getElementById('total-nodes-value').textContent = this.stats.totalNodes;
        
        // 更新在线节点数
        document.getElementById('online-nodes-value').textContent = this.stats.onlineNodes;
        const onlineRate = this.stats.totalNodes > 0 
            ? Math.round((this.stats.onlineNodes / this.stats.totalNodes) * 100) 
            : 0;
        document.getElementById('online-nodes-trend').innerHTML = 
            `<span class="text-green-600 dark:text-green-400">${onlineRate}%</span>`;
        
        // 更新平均延迟
        document.getElementById('avg-latency-value').textContent = this.stats.avgLatency + 'ms';
        
        // 更新告警数
        document.getElementById('alerts-value').textContent = this.stats.activeAlerts;
        if (this.stats.activeAlerts > 0) {
            document.getElementById('alerts-card').classList.add('alert-active');
        }
    }
    
    initializeCharts() {
        // 初始化节点状态分布图
        this.initNodeStatusChart();
        
        // 初始化响应时间趋势图
        this.initLatencyTrendChart();
        
        // 初始化地区分布图
        this.initRegionDistributionChart();
        
        // 初始化流量统计图
        this.initTrafficChart();
    }
    
    initNodeStatusChart() {
        const chartDom = document.getElementById('node-status-chart');
        if (!chartDom) return;
        
        this.charts.nodeStatus = echarts.init(chartDom, null, {
            renderer: 'canvas',
            useDirtyRect: false
        });
        
        const option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 'left'
            },
            series: [
                {
                    name: '节点状态',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '20',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        { value: this.stats.onlineNodes, name: '在线', itemStyle: { color: '#10b981' } },
                        { value: this.stats.offlineNodes, name: '离线', itemStyle: { color: '#ef4444' } }
                    ]
                }
            ]
        };
        
        this.charts.nodeStatus.setOption(option);
    }
    
    initLatencyTrendChart() {
        const chartDom = document.getElementById('latency-trend-chart');
        if (!chartDom) return;
        
        this.charts.latencyTrend = echarts.init(chartDom, null, {
            renderer: 'canvas',
            useDirtyRect: false
        });
        
        // 生成模拟数据
        const hours = 24;
        const now = new Date();
        const data = [];
        
        for (let i = hours; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 3600 * 1000);
            data.push({
                time: time.toISOString(),
                value: Math.random() * 50 + 20
            });
        }
        
        const option = {
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    const date = new Date(params[0].data.time);
                    return `${date.toLocaleString()}<br>延迟: ${params[0].data.value.toFixed(2)}ms`;
                }
            },
            xAxis: {
                type: 'time',
                boundaryGap: false
            },
            yAxis: {
                type: 'value',
                name: '延迟 (ms)'
            },
            series: [{
                name: '平均延迟',
                type: 'line',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                        offset: 0,
                        color: 'rgba(139, 92, 246, 0.3)'
                    }, {
                        offset: 1,
                        color: 'rgba(139, 92, 246, 0.05)'
                    }])
                },
                lineStyle: {
                    color: '#8b5cf6',
                    width: 2
                },
                data: data.map(item => ({
                    value: [item.time, item.value],
                    time: item.time
                }))
            }]
        };
        
        this.charts.latencyTrend.setOption(option);
    }
    
    initRegionDistributionChart() {
        const chartDom = document.getElementById('region-distribution-chart');
        if (!chartDom) return;
        
        this.charts.regionDistribution = echarts.init(chartDom, null, {
            renderer: 'canvas',
            useDirtyRect: false
        });
        
        // 统计地区分布
        const regionCount = {};
        this.cache.nodes.forEach(node => {
            const status = this.cache.nodeStatus[node.id];
            if (status && status.data && status.data.location) {
                const region = status.data.location.name || '未知';
                regionCount[region] = (regionCount[region] || 0) + 1;
            }
        });
        
        const data = Object.entries(regionCount)
            .map(([name, value]) => ({ name, value }))
            .sort((a, b) => b.value - a.value)
            .slice(0, 10);
        
        const option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value'
            },
            yAxis: {
                type: 'category',
                data: data.map(item => item.name)
            },
            series: [{
                name: '节点数',
                type: 'bar',
                data: data.map(item => item.value),
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                        offset: 0,
                        color: '#8b5cf6'
                    }, {
                        offset: 1,
                        color: '#3b82f6'
                    }])
                }
            }]
        };
        
        this.charts.regionDistribution.setOption(option);
    }
    
    initTrafficChart() {
        const chartDom = document.getElementById('traffic-chart');
        if (!chartDom) return;
        
        this.charts.traffic = echarts.init(chartDom, null, {
            renderer: 'canvas',
            useDirtyRect: false
        });
        
        // 生成模拟流量数据
        const hours = 24;
        const now = new Date();
        const uploadData = [];
        const downloadData = [];
        
        for (let i = hours; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 3600 * 1000);
            uploadData.push({
                time: time.toISOString(),
                value: Math.random() * 100 + 50
            });
            downloadData.push({
                time: time.toISOString(),
                value: Math.random() * 200 + 100
            });
        }
        
        const option = {
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    const date = new Date(params[0].data.time);
                    let result = date.toLocaleString() + '<br>';
                    params.forEach(param => {
                        result += `${param.seriesName}: ${param.data.value.toFixed(2)} MB/s<br>`;
                    });
                    return result;
                }
            },
            legend: {
                data: ['上传', '下载']
            },
            xAxis: {
                type: 'time',
                boundaryGap: false
            },
            yAxis: {
                type: 'value',
                name: '流量 (MB/s)'
            },
            series: [
                {
                    name: '上传',
                    type: 'line',
                    smooth: true,
                    symbol: 'none',
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: 'rgba(59, 130, 246, 0.3)'
                        }, {
                            offset: 1,
                            color: 'rgba(59, 130, 246, 0.05)'
                        }])
                    },
                    lineStyle: {
                        color: '#3b82f6',
                        width: 2
                    },
                    data: uploadData.map(item => ({
                        value: [item.time, item.value],
                        time: item.time
                    }))
                },
                {
                    name: '下载',
                    type: 'line',
                    smooth: true,
                    symbol: 'none',
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: 'rgba(16, 185, 129, 0.3)'
                        }, {
                            offset: 1,
                            color: 'rgba(16, 185, 129, 0.05)'
                        }])
                    },
                    lineStyle: {
                        color: '#10b981',
                        width: 2
                    },
                    data: downloadData.map(item => ({
                        value: [item.time, item.value],
                        time: item.time
                    }))
                }
            ]
        };
        
        this.charts.traffic.setOption(option);
    }
    
    updateNodesTable() {
        const tbody = document.getElementById('nodes-table-body');
        if (!tbody) return;
        
        const searchTerm = document.getElementById('node-search').value.toLowerCase();
        const statusFilter = document.getElementById('node-filter-status').value;
        
        let html = '';
        
        this.cache.nodes.forEach(node => {
            const status = this.cache.nodeStatus[node.id];
            if (!status) return;
            
            // 过滤逻辑
            if (searchTerm && !node.name.toLowerCase().includes(searchTerm)) return;
            
            const isOnline = status.stat && !status.stat.offline;
            if (statusFilter === 'online' && !isOnline) return;
            if (statusFilter === 'offline' && isOnline) return;
            
            const data = status.data || {};
            const location = data.location || {};
            
            html += `
                <tr class="node-row ${isOnline ? '' : 'offline'}">
                    <td>
                        <div class="node-name">
                            ${location.code ? `<img src="/img/flags/${location.code.toLowerCase()}.svg" class="node-flag" alt="${location.code}">` : ''}
                            <span>${this.escapeHtml(node.name)}</span>
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${isOnline ? 'online' : 'offline'}">
                            ${isOnline ? '在线' : '离线'}
                        </span>
                    </td>
                    <td>${this.escapeHtml(location.name || '未知')}</td>
                    <td>${data.latency ? data.latency + 'ms' : '--'}</td>
                    <td>${this.formatTraffic(data.upload)}</td>
                    <td>${this.formatTraffic(data.download)}</td>
                    <td>
                        <div class="resource-usage">
                            <div class="usage-bar">
                                <div class="usage-fill cpu" style="width: ${data.cpu || 0}%"></div>
                            </div>
                            <span class="usage-text">${data.cpu || 0}%</span>
                        </div>
                    </td>
                    <td>
                        <div class="resource-usage">
                            <div class="usage-bar">
                                <div class="usage-fill memory" style="width: ${data.memory || 0}%"></div>
                            </div>
                            <span class="usage-text">${data.memory || 0}%</span>
                        </div>
                    </td>
                    <td>${this.formatUptime(data.uptime)}</td>
                    <td>
                        <div class="node-actions">
                            <button class="node-action-btn" onclick="dashboard.viewNodeDetails('${node.id}')" title="查看详情">
                                <i class="ti ti-eye"></i>
                            </button>
                            <button class="node-action-btn" onclick="dashboard.editNode('${node.id}')" title="编辑">
                                <i class="ti ti-edit"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
        
        tbody.innerHTML = html || '<tr><td colspan="10" class="text-center">暂无数据</td></tr>';
    }
    
    formatTraffic(bytes) {
        if (!bytes) return '--';
        const mb = bytes / (1024 * 1024);
        return mb.toFixed(2) + ' MB/s';
    }
    
    formatUptime(seconds) {
        if (!seconds) return '--';
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (days > 0) {
            return `${days}天 ${hours}小时`;
        } else if (hours > 0) {
            return `${hours}小时 ${minutes}分钟`;
        } else {
            return `${minutes}分钟`;
        }
    }
    
    bindEvents() {
        // 搜索框事件
        const searchInput = document.getElementById('node-search');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(() => {
                this.updateNodesTable();
            }, 300));
        }
        
        // 状态过滤器事件
        const statusFilter = document.getElementById('node-filter-status');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.updateNodesTable();
            });
        }
        
        // 时间范围选择器
        const latencyRange = document.getElementById('latency-time-range');
        if (latencyRange) {
            latencyRange.addEventListener('change', () => {
                this.refreshLatencyChart();
            });
        }
        
        // 流量类型选择器
        const trafficType = document.getElementById('traffic-type');
        if (trafficType) {
            trafficType.addEventListener('change', () => {
                this.refreshTrafficChart();
            });
        }
        
        // 窗口大小改变时重新调整图表
        window.addEventListener('resize', this.debounce(() => {
            Object.values(this.charts).forEach(chart => {
                if (chart && chart.resize) {
                    chart.resize();
                }
            });
        }, 300));
        
        // 主题切换监听
        if (window.themeManager) {
            window.themeManager.onThemeChange = () => {
                this.updateChartsTheme();
            };
        }
    }
    
    updateChartsTheme() {
        const isDark = document.documentElement.classList.contains('dark');
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.setOption) {
                // 更新图表主题
                chart.dispose();
                this.initializeCharts();
            }
        });
    }
    
    refreshNodeStatusChart() {
        if (this.charts.nodeStatus) {
            this.processData();
            this.initNodeStatusChart();
        }
    }
    
    refreshLatencyChart() {
        // 根据选择的时间范围刷新延迟图表
        if (this.charts.latencyTrend) {
            this.initLatencyTrendChart();
        }
    }
    
    refreshTrafficChart() {
        // 根据选择的流量类型刷新流量图表
        if (this.charts.traffic) {
            this.initTrafficChart();
        }
    }
    
    toggleRegionView() {
        // 切换地区视图（地图/柱状图）
        console.log('切换地区视图');
    }
    
    viewNodeDetails(nodeId) {
        // 查看节点详情
        window.location.href = `/admin/servers/edit?id=${nodeId}`;
    }
    
    editNode(nodeId) {
        // 编辑节点
        window.location.href = `/admin/servers/edit?id=${nodeId}`;
    }
    
    exportNodeData() {
        // 导出节点数据
        const data = this.cache.nodes.map(node => {
            const status = this.cache.nodeStatus[node.id] || {};
            const nodeData = status.data || {};
            return {
                名称: node.name,
                状态: status.stat && !status.stat.offline ? '在线' : '离线',
                地区: nodeData.location?.name || '未知',
                延迟: nodeData.latency ? nodeData.latency + 'ms' : '--',
                CPU使用率: nodeData.cpu ? nodeData.cpu + '%' : '--',
                内存使用率: nodeData.memory ? nodeData.memory + '%' : '--'
            };
        });
        
        const csv = this.convertToCSV(data);
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `nodes_${new Date().toISOString().slice(0, 10)}.csv`;
        link.click();
    }
    
    convertToCSV(data) {
        if (data.length === 0) return '';
        
        const headers = Object.keys(data[0]);
        const csvHeaders = headers.join(',');
        const csvRows = data.map(row => 
            headers.map(header => JSON.stringify(row[header] || '')).join(',')
        );
        
        return [csvHeaders, ...csvRows].join('\n');
    }
    
    addEvent(type, message) {
        const container = document.getElementById('events-container');
        if (!container) return;
        
        const event = document.createElement('div');
        event.className = `event-item event-${type}`;
        event.innerHTML = `
            <div class="event-icon">
                <i class="ti ${this.getEventIcon(type)}"></i>
            </div>
            <div class="event-content">
                <span class="event-time">${new Date().toLocaleTimeString()}</span>
                <span class="event-message">${this.escapeHtml(message)}</span>
            </div>
        `;
        
        container.insertBefore(event, container.firstChild);
        
        // 限制事件数量
        while (container.children.length > 50) {
            container.removeChild(container.lastChild);
        }
    }
    
    getEventIcon(type) {
        const icons = {
            'online': 'ti-wifi',
            'offline': 'ti-wifi-off',
            'warning': 'ti-alert-triangle',
            'error': 'ti-alert-circle',
            'info': 'ti-info-circle'
        };
        return icons[type] || 'ti-info-circle';
    }
    
    clearEvents() {
        const container = document.getElementById('events-container');
        if (container) {
            container.innerHTML = '';
        }
    }
    
    startAutoUpdate() {
        this.updateTimer = setInterval(() => {
            this.loadAllData().then(() => {
                this.updateStatCards();
                this.updateNodesTable();
                this.refreshNodeStatusChart();
                this.addEvent('info', '数据已自动更新');
            }).catch(error => {
                console.error('自动更新失败:', error);
                this.addEvent('error', '自动更新失败');
            });
        }, this.refreshInterval);
    }
    
    stopAutoUpdate() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    showError(message) {
        this.addEvent('error', message);
        if (window.showNotification) {
            window.showNotification(message, 'error');
        }
    }
    
    destroy() {
        // 停止自动更新
        this.stopAutoUpdate();
        
        // 销毁图表
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.dispose) {
                chart.dispose();
            }
        });
        
        // 清空缓存
        this.cache = {};
        this.charts = {};
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 只在监控仪表盘页面初始化
    if (document.getElementById('monitor-dashboard')) {
        window.dashboard = new MonitorDashboard();
    }
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.dashboard) {
        window.dashboard.destroy();
    }
});