// SharedWorker核心实现 - 极简版
// 单一职责：管理WebSocket连接并在不同标签页间共享数据

// 常量定义
const SLEEP_THRESHOLD = 120000;     // 休眠阈值：2分钟（增大以减少连接循环）
const HEARTBEAT_INTERVAL = 30000;   // 心跳检测：30秒

// 全局状态
const state = {
  // WebSocket相关
  ws: null,                        // WebSocket实例
  isConnecting: false,             // 是否正在连接
  reconnectTimer: null,            // 重连定时器
  heartbeatTimer: null,            // 心跳定时器
  statusCheckTimer: null,          // 状态检查定时器
  
  // 共享数据
  lastData: null,                  // 最后接收的数据
  ports: new Set(),                // 连接的端口集合
  lastDataTimestamp: 0,            // 最后数据接收时间戳
  dataSource: null,                // 数据来源：'ws' 或 'http'
  pendingHttpRequest: false,       // 是否有HTTP请求正在进行中
  
  // 状态标志
  isInSleepMode: false,            // 是否处于休眠模式
  lastActiveTime: Date.now(),      // 最后活跃时间
  
  // 统计信息
  connectionAttempts: 0,           // 连接尝试次数
  maxConnectionAttempts: 10,       // 最大连接尝试次数
  
  // 配置
  nodeId: null,                    // 节点ID
  pageProtocol: null,               // 页面协议（从客户端传递）
  pageHost: null                    // 页面主机（从客户端传递）
};

// 辅助函数：广播消息到所有客户端
function broadcastToAll(message) {
  state.ports.forEach(port => {
    try {
      port.postMessage(message);
    } catch (e) {
      state.ports.delete(port);
    }
  });
}

// 初始化WebSocket连接
function initWebSocket(nodeId = null) {
  if (state.ws || state.isConnecting) return;
  
  // 始终根据传入的 nodeId 更新 state.nodeId
  // 允许在从单节点模式切换回全局模式时，将 nodeId 重置为 null
  state.nodeId = nodeId;
  
  state.isConnecting = true;
  state.connectionAttempts++;
  
  try {
    const wsUrl = state.nodeId 
      ? `/ws/stats/${state.nodeId}`
      : '/ws/stats';
      
    // 构建WebSocket URL - 支持远程访问
    // 优先使用客户端传递的页面主机信息，如果没有则使用 worker 的 location（本地访问时）
    const wsProtocol = state.pageProtocol ? 
      (state.pageProtocol === 'https:' ? 'wss:' : 'ws:') : 
      (location.protocol === 'https:' ? 'wss:' : 'ws:');
    const wsHost = state.pageHost || location.host;
    const wsFullUrl = `${wsProtocol}//${wsHost}${wsUrl}`;
    console.log('[SharedWorker] Connecting to:', wsFullUrl);
    // 同时发送到客户端
    broadcastToAll({ type: 'debug', message: `Connecting to: ${wsFullUrl}` });
    state.ws = new WebSocket(wsFullUrl);
    
    // 连接建立
    state.ws.onopen = () => {
      state.isConnecting = false;
      state.connectionAttempts = 0;
      console.log('[SharedWorker] WebSocket connected');
      
      // 广播连接状态
      broadcastToAll({ type: 'connection_status', status: 'connected' });
      
      // 启动心跳检测
      startHeartbeat();

      // 首帧兜底：若短时间内仍无数据，从 HTTP 回补一份，避免首屏等待假象
      try {
        if (state.wsFirstFrameTimer) {
          clearTimeout(state.wsFirstFrameTimer);
          state.wsFirstFrameTimer = null;
        }
        state.wsFirstFrameTimer = setTimeout(() => {
          const noRecentData = !state.lastData || (Date.now() - state.lastDataTimestamp > 1000);
          if (noRecentData && !state.pendingHttpRequest) {
            fetchLatestDataViaHttp();
          }
          state.wsFirstFrameTimer = null;
        }, 800);
      } catch (_) {}
    };
    
    // 接收消息
    state.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        state.lastData = message;
        state.lastDataTimestamp = Date.now();
        state.dataSource = 'ws';
        
        // 广播消息给所有客户端
        broadcastToAll({ 
          type: 'ws_message', 
          data: message,
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('[SharedWorker] Message parse error:', error);
      }
    };
    
    // 连接关闭
    state.ws.onclose = () => {
      state.isConnecting = false;
      state.ws = null;
      
      // 广播连接断开消息
      broadcastToAll({ type: 'connection_status', status: 'disconnected' });
      
      // 停止心跳检测
      stopHeartbeat();
      
      // 尝试通过HTTP获取最新数据（作为临时替代方案）
      if (state.ports.size > 0 && !state.isInSleepMode) {
        fetchLatestDataViaHttp();
      }
      
      // 设置重连 - 使用指数退避策略
      if (state.reconnectTimer) {
        clearTimeout(state.reconnectTimer);
        state.reconnectTimer = null;
      }
      
      // 如果尝试次数超过上限，延长重连间隔
      const delay = state.connectionAttempts > state.maxConnectionAttempts 
        ? 60000  // 1分钟
        : 5000;  // 5秒
        
      state.reconnectTimer = setTimeout(() => {
        state.reconnectTimer = null;
        if (state.ports.size > 0 && !state.isInSleepMode) {
          initWebSocket(state.nodeId);
        }
      }, delay);
    };
    
    // 连接错误
    state.ws.onerror = (error) => {
      state.isConnecting = false;
      console.error('[SharedWorker] WebSocket error:', error);
      
      // 在连接错误时尝试使用HTTP备用请求
      if (state.ports.size > 0 && !state.isInSleepMode) {
        fetchLatestDataViaHttp();
      }
    };
  } catch (error) {
    state.isConnecting = false;
    console.error('[SharedWorker] WebSocket init error:', error);
    
    // 在连接初始化失败时尝试使用HTTP备用请求
    if (state.ports.size > 0 && !state.isInSleepMode) {
      fetchLatestDataViaHttp();
    }
    
    if (state.reconnectTimer) {
      clearTimeout(state.reconnectTimer);
      state.reconnectTimer = null;
    }
    
    state.reconnectTimer = setTimeout(() => {
      state.reconnectTimer = null;
      if (state.ports.size > 0 && !state.isInSleepMode) {
        initWebSocket(state.nodeId);
      }
    }, 5000);
  }
}

// 启动心跳检测
function startHeartbeat() {
  // 清理已有定时器，避免重复创建
  stopHeartbeat();
  
  state.heartbeatTimer = setInterval(() => {
    if (state.ws && state.ws.readyState === WebSocket.OPEN) {
      try {
        state.ws.send(JSON.stringify({ type: 'heartbeat', timestamp: Date.now() }));
      } catch (e) {
        // 忽略错误
      }
    }
  }, HEARTBEAT_INTERVAL);
}

// 停止心跳检测
function stopHeartbeat() {
  if (state.heartbeatTimer) {
    clearInterval(state.heartbeatTimer);
    state.heartbeatTimer = null;
  }
}

// 添加连接统计数据收集功能
function collectConnectionStats() {
  // 收集连接状态统计数据
  const stats = {
    timestamp: Date.now(),
    wsConnected: state.ws && state.ws.readyState === WebSocket.OPEN,
    httpRequests: {
      total: state._httpRequestsTotal || 0,
      success: state._httpRequestsSuccess || 0,
      failed: state._httpRequestsFailed || 0
    },
    clientPorts: state.ports.size,
    lastDataSource: state.dataSource,
    lastDataTimestamp: state.lastDataTimestamp
  };
  
  // 更新总请求计数
  if (!state._httpRequestsTotal) state._httpRequestsTotal = 0;
  if (!state._httpRequestsSuccess) state._httpRequestsSuccess = 0;
  if (!state._httpRequestsFailed) state._httpRequestsFailed = 0;
  
  // 广播统计数据给所有客户端
  broadcastToAll({
    type: 'connection_stats',
    data: stats
  });
  
  return stats;
}

// 使用HTTP请求获取最新数据（作为WebSocket的备用方案）
function fetchLatestDataViaHttp() {
  // 如果已经有请求在进行中，避免重复请求
  if (state.pendingHttpRequest) return;
  
  state.pendingHttpRequest = true;
  
  // 更新HTTP请求计数
  if (!state._httpRequestsTotal) state._httpRequestsTotal = 0;
  state._httpRequestsTotal++;
  
  try {
    // 构建API请求URL - 支持远程访问
    // 全局数据：使用 /api/allnode_status
    // 单节点：仅在显式 nodeId 模式下使用 /stats/:sid/latest
    // 优先使用客户端传递的页面主机信息构建完整URL
    const protocol = state.pageProtocol || location.protocol;
    const host = state.pageHost || location.host;
    const path = state.nodeId 
      ? `/stats/${state.nodeId}/latest`
      : `/api/allnode_status`;
    const url = `${protocol}//${host}${path}`;
    console.log('[SharedWorker] HTTP fallback to:', url);
    
    // 使用fetch API发送请求
    fetch(url)
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP请求失败: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        state.pendingHttpRequest = false;
        
        // 更新成功计数
        if (!state._httpRequestsSuccess) state._httpRequestsSuccess = 0;
        state._httpRequestsSuccess++;
        
        if (data) {
          let statsData = null;
          
          // 处理不同的响应格式
          // 单节点：/stats/:sid/latest -> { status: 'success', data: { [sid]: node } }
          if (state.nodeId && (data.status === 'success' || data.success === true) && data.data && data.data[state.nodeId]) {
            statsData = { [state.nodeId]: data.data[state.nodeId] };
          }
          // 全局：/api/allnode_status -> { success: true, data: { sid: node, ... } }
          else if (!state.nodeId && (data.status === 'success' || data.success === true) && data.data && typeof data.data === 'object') {
            statsData = data.data;
          }
          // 直接返回映射对象
          else if (typeof data === 'object' && (data.status === undefined && data.success === undefined)) {
            statsData = data;
          }
          
          if (statsData) {
            // 只有在较新的数据或没有WebSocket数据时才更新
            const currentTime = Date.now();
            const shouldUpdate = (currentTime - state.lastDataTimestamp > 5000) || // 超过5秒没有新数据
                                 (state.dataSource !== 'ws') ||                    // 不是来自WebSocket的数据
                                 (!state.lastData);                               // 没有现有数据
            
            if (shouldUpdate) {
              // 统一对外消息结构：确保包含 type:'stats'
              const wrapped = { type: 'stats', timestamp: currentTime, data: statsData };

              // 缓存数据
              state.lastData = wrapped;
              state.lastDataTimestamp = currentTime;
              state.dataSource = 'http';
              
              // 广播数据给所有客户端
              broadcastToAll({
                type: 'ws_message',
                data: wrapped,
                timestamp: currentTime,
                fromHttp: true  // 标记数据来源为HTTP请求
              });
            }
          }
        }
      })
      .catch((error) => {
        state.pendingHttpRequest = false;
        console.error('[SharedWorker] HTTP fallback failed:', error);
        
        // 更新失败计数
        if (!state._httpRequestsFailed) state._httpRequestsFailed = 0;
        state._httpRequestsFailed++;
        
        // 请求失败，不做特殊处理
        // 后续将通过重连机制继续尝试连接WebSocket
      });
  } catch (e) {
    state.pendingHttpRequest = false;
    
    // 更新失败计数
    if (!state._httpRequestsFailed) state._httpRequestsFailed = 0;
    state._httpRequestsFailed++;
    
    // 忽略错误
  }
}

// 状态检查
function startStatusCheck() {
  // 清理已有定时器，避免重复创建
  stopStatusCheck();
  
  state.statusCheckTimer = setInterval(() => {
    // 1. 检查连接状态并广播
    const status = state.ws && state.ws.readyState === WebSocket.OPEN ? 'connected' : 'disconnected';
    broadcastToAll({ type: 'connection_status', status });
    
    // 2. 收集连接统计数据（每10秒统计一次）
    const stats = collectConnectionStats();
    
    // 3. 如果连接已断开但有客户端连接，尝试重新连接
    if (status === 'disconnected' && state.ports.size > 0 && !state.isConnecting && !state.reconnectTimer && !state.isInSleepMode) {
      // 先尝试通过HTTP获取数据，再尝试重新连接WebSocket
      fetchLatestDataViaHttp();
      // 重新建立WebSocket连接
      initWebSocket(state.nodeId);
    }
    
    // 4. 检查是否应该进入休眠模式
    const now = Date.now();
    
    // 如果有活跃客户端，更新最后活跃时间并退出休眠模式
    if (state.ports.size > 0) {
      state.lastActiveTime = now;
      state.isInSleepMode = false;
    } else {
      // 如果没有活跃客户端，检查是否超过休眠阈值
      const inactiveTime = now - state.lastActiveTime;
      
      // 如果超过休眠阈值且WebSocket连接仍然活跃，进入休眠模式
      if (inactiveTime > SLEEP_THRESHOLD && state.ws && state.ws.readyState === WebSocket.OPEN && !state.isInSleepMode) {
        state.isInSleepMode = true;
        
        // 关闭WebSocket连接
        try {
          state.ws.close();
        } catch (e) {
          // 忽略错误
        }
        state.ws = null;
      }
      
      // 如果没有活跃客户端且已经过去很长时间，停止状态检查定时器以节省资源
      if (inactiveTime > SLEEP_THRESHOLD * 3) {
        stopStatusCheck();
      }
    }
  }, 10000); // 每10秒检查一次
}

// 停止状态检查定时器
function stopStatusCheck() {
  if (state.statusCheckTimer) {
    clearInterval(state.statusCheckTimer);
    state.statusCheckTimer = null;
  }
}

// 监听连接
self.addEventListener('connect', (e) => {
  const port = e.ports[0];
  state.ports.add(port);
  
  // 更新最后活跃时间
  state.lastActiveTime = Date.now();
  
  // 如果处于休眠模式，立即退出休眠模式
  if (state.isInSleepMode) {
    state.isInSleepMode = false;
  }
  
  // 立即发送 worker_ready 消息，表示 SharedWorker 本身已就绪
  // 这与 WebSocket 连接状态无关，用于快速判定 SharedWorker 启动成功
  port.postMessage({ type: 'worker_ready', timestamp: Date.now() });
  
  // 向新客户端发送当前 WebSocket 连接状态
  const connectionStatus = state.ws && state.ws.readyState === WebSocket.OPEN ? 'connected' : 'disconnected';
  port.postMessage({ type: 'connection_status', status: connectionStatus });
  
  // 若当前无缓存数据，且 WebSocket 尚未提供首帧，立即尝试通过 HTTP 获取一份最新数据，降低首屏等待
  if (!state.lastData && !state.pendingHttpRequest) {
    // 在 WS 未连接或仍在连接时优先尝试 HTTP 回补
    if (!state.ws || state.ws.readyState !== WebSocket.OPEN) {
      try { fetchLatestDataViaHttp(); } catch (_) {}
    }
  }
  
  // 如果有上次的数据，发送给新连接的客户端
  if (state.lastData) {
    port.postMessage({
      type: 'ws_message',
      data: state.lastData,
      timestamp: Date.now(),
      fromCache: true
    });
  }
  
  // 注意：MessagePort 没有 'close' 事件，端口清理通过以下方式处理：
  // 1. page_unload 消息时主动删除
  // 2. 定时心跳检测（portCheckTimer）中捕获发送失败的端口
  
  // 监听客户端消息
  port.addEventListener('message', (e) => {
    const data = e.data;
    
    switch (data.type) {
      case 'init':
        // 保存客户端传递的页面主机信息
        console.log('[SharedWorker] Init received:', { protocol: data.protocol, host: data.host, nodeId: data.nodeId });
        if (data.protocol) state.pageProtocol = data.protocol;
        if (data.host) state.pageHost = data.host;
        
        // 初始化 WebSocket 连接
        // 检查是否需要新连接：1)没有连接 2)连接已关闭 3)nodeId发生变化
        const needNewConnection = !state.ws || 
                                state.ws.readyState !== WebSocket.OPEN || 
                                state.nodeId !== data.nodeId;
        
        if (needNewConnection) {
          // 如果是因为nodeId变化，先关闭现有连接
          if (state.ws && state.ws.readyState === WebSocket.OPEN && state.nodeId !== data.nodeId) {
            try {
              state.ws.close();
            } catch (e) {
              // 忽略关闭错误
            }
            state.ws = null;
          }
          
          // 创建新连接
          initWebSocket(data.nodeId);
        } else {
          // nodeId匹配且连接正常，复用现有连接
          port.postMessage({ type: 'connection_status', status: 'connected' });
        }
        break;
        
      case 'close':
        // 关闭连接
        if (state.ws) {
          try {
            state.ws.close();
          } catch (e) {
            // 忽略错误
          }
          state.ws = null;
        }
        break;
        
      case 'page_unload':
        // 页面卸载，移除相关端口
        state.ports.delete(port);
        
        // 如果没有活跃端口了，考虑进入休眠模式
        if (state.ports.size === 0 && state.ws && state.ws.readyState === WebSocket.OPEN) {
          // 延迟一小段时间再关闭，避免页面刷新时的误判
          setTimeout(() => {
            if (state.ports.size === 0) {
              state.isInSleepMode = true;
              try {
                state.ws.close();
              } catch (e) {
                // 忽略错误
              }
              state.ws = null;
            }
          }, 1000);
        }
        break;
        
      case 'get_last_data':
        // 客户端请求最后一次数据
        if (state.lastData) {
          port.postMessage({
            type: 'ws_message',
            data: state.lastData,
            timestamp: Date.now(),
            fromCache: true
          });
        }
        break;
        
      case 'check_connection':
        // 客户端请求检查连接状态
        const status = state.ws && state.ws.readyState === WebSocket.OPEN ? 'connected' : 'disconnected';
        port.postMessage({ type: 'connection_status', status });
        break;
        
      case 'clear_cache':
        // 清除最后接收的数据
        state.lastData = null;
        break;
    }
  });
  
  // 启动端口
  port.start();
  
  // 不再自动初始化WebSocket，等待客户端发送包含主机信息的init消息
  // 这避免了使用错误的location信息
  
  // 如果这是第一个客户端连接，启动状态检查
  if (state.ports.size === 1) {
    startStatusCheck();
  }
});

// 断开连接检测和清理
const portCheckTimer = setInterval(() => {
  state.ports.forEach(port => {
    try {
      // 发送一个简单的心跳检测
      port.postMessage({ type: 'worker_heartbeat', timestamp: Date.now() });
    } catch (e) {
      state.ports.delete(port);
    }
  });
  
  // 如果没有活跃端口，且状态检查定时器正在运行，停止它
  if (state.ports.size === 0 && state.statusCheckTimer) {
    stopStatusCheck();
  }
}, 30000); // 每30秒检测一次

// 启动状态检查 (仅在有连接的客户端时)
if (state.ports.size > 0) {
  startStatusCheck();
}
