/**
 * 网络质量监控页面 JavaScript 模块
 * 按节点显示网络质量监控数据，每个节点一个图表
 */

// 确保ECharts已加载
if (typeof echarts === 'undefined') {
    console.error('ECharts未加载，网络质量页面功能将不可用');
}

// 网络质量页面管理器
const NetworkQualityPage = {
    // CSS变量获取工具
    getCSSVariable(varName) {
        return getComputedStyle(document.documentElement).getPropertyValue(varName).trim();
    },

    // 获取主题相关的颜色值
    getThemeColors() {
        return {
            chartTextPrimary: this.getCSSVariable('--chart-text-primary'),
            chartTextSecondary: this.getCSSVariable('--chart-text-secondary'),
            chartTextTertiary: this.getCSSVariable('--chart-text-tertiary'),
            chartAxisLine: this.getCSSVariable('--chart-axis-line'),
            chartSplitLine: this.getCSSVariable('--chart-split-line'),
            chartTooltipBg: this.getCSSVariable('--chart-tooltip-bg'),
            chartTooltipBorder: this.getCSSVariable('--chart-tooltip-border'),
            modalOverlayBg: this.getCSSVariable('--modal-overlay-bg'),
            modalBg: this.getCSSVariable('--modal-bg'),
            modalText: this.getCSSVariable('--modal-text'),
            modalTextSecondary: this.getCSSVariable('--modal-text-secondary'),
            modalBorder: this.getCSSVariable('--modal-border'),
            modalButtonBg: this.getCSSVariable('--modal-button-bg'),
            modalButtonHover: this.getCSSVariable('--modal-button-hover'),
            modalInputBorder: this.getCSSVariable('--modal-input-border'),
            modalInputFocus: this.getCSSVariable('--modal-input-focus'),
            modalPrimaryBg: this.getCSSVariable('--modal-primary-bg'),
            modalPrimaryHover: this.getCSSVariable('--modal-primary-hover')
        };
    },

    // 生成主题相关的弹窗CSS样式
    generateModalCSS() {
        const colors = this.getThemeColors();
        return `
            .time-range-upgrade-modal {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: ${colors.modalOverlayBg};
                backdrop-filter: blur(4px);
            }
            .time-range-upgrade-modal .modal-content {
                position: relative;
                background: ${colors.modalBg};
                border-radius: 12px;
                max-width: 480px;
                width: 90%;
                max-height: 90vh;
                overflow: hidden;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                transform: translateY(20px);
                transition: transform 0.3s ease;
            }
            .time-range-upgrade-modal.show .modal-content {
                transform: translateY(0);
            }
            .time-range-upgrade-modal .modal-header {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 20px 24px 16px;
                border-bottom: 1px solid ${colors.modalBorder};
            }
            .time-range-upgrade-modal .modal-icon {
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, #fef3c7, #fde68a);
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .time-range-upgrade-modal .modal-title {
                flex: 1;
                font-size: 18px;
                font-weight: 600;
                color: ${colors.modalText};
            }
            .time-range-upgrade-modal .modal-subtitle {
                margin-top: 4px;
                font-size: 14px;
                color: ${colors.modalTextSecondary};
            }
            .time-range-upgrade-modal .modal-body {
                padding: 20px 24px;
            }
            .time-range-upgrade-modal .feature-item {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 12px;
                padding: 8px 0;
            }
            .time-range-upgrade-modal .feature-item:last-child {
                margin-bottom: 0;
            }
            .time-range-upgrade-modal .feature-icon {
                color: #10b981;
                font-size: 16px;
            }
            .time-range-upgrade-modal .feature-text {
                color: ${colors.modalText};
                font-size: 14px;
                line-height: 1.4;
            }
            .time-range-upgrade-modal .modal-footer {
                padding: 16px 24px 20px;
                background: ${colors.modalButtonBg};
                display: flex;
                gap: 12px;
                justify-content: flex-end;
            }
            .time-range-upgrade-modal .btn {
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                border: 1px solid ${colors.modalInputBorder};
                color: ${colors.modalText};
            }
            .time-range-upgrade-modal .btn:hover {
                background: ${colors.modalButtonHover};
                border-color: ${colors.modalInputFocus};
            }
            .time-range-upgrade-modal .btn-primary {
                background: ${colors.modalPrimaryBg};
                color: white;
                border: none;
            }
            .time-range-upgrade-modal .btn-primary:hover {
                background: ${colors.modalPrimaryHover};
            }
        `;
    },
    // 配置
    config: {
        maxRetries: 3,
        currentTimeRange: '1h', // 默认1小时
        apiUrl: '/api/network-quality/nodes-overview',
        // 懒加载/批次渲染参数
        initialVisibleCount: 10,
        batchSize: 10,
        initialChartCount: 6
    },

    // 状态
        state: {
            isLoading: false,
            retryCount: 0,
            charts: {}, // 存储所有图表实例
            data: {
                summary: null,
                nodes: []
            },
            lastUpdated: null,
            isFullWidth: false, // 全宽模式状态
            // 懒加载状态
            visibleCount: 0,
            chartObserver: null,
            scrollObserver: null,
            isLoadingNextPage: false,
            isAppendingBatch: false,
            hasUserInteracted: false,
            // 请求竞争防护
            req: {
                overview: null,
                nextPage: null,
                stats: null
            },
            // 顶部统计请求竞争防护ID（仅允许最后一次生效）
            statsReqId: 0,
            // 追加节流
            lastAppendAt: 0,
            scrollSentinelEl: null
        },

    // 初始化
    init() {
        
        // 初始化事件监听器
        this.initEventListeners();
        
        // 验证时间范围按钮
        this.validateTimeRangeButtons();
        
        // 恢复宽度状态
        this.restoreWidthState();
        
        // 初始化最后更新时间显示
        this.updateLastRefreshTime();
        
        // 初始化筛选管理器集成
        this.initFilterIntegration();
        
        // 初始化功能墙集成 - 等待权限检查完成后再加载数据
        this.initFeatureWallIntegrationAndLoadData();
        
        console.log('[NetworkQuality] 网络质量页面初始化完成');
    },

    // 初始化事件监听器
    initEventListeners() {
        // 时间范围选择器
        const timeRangeButtons = document.querySelectorAll('.time-range-btn');
        timeRangeButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                // 使用currentTarget确保获取按钮元素，避免点击span导致的undefined
                const timeRange = e.currentTarget.dataset.range;
                this.changeTimeRange(timeRange);
            });
        });

        // 手动刷新按钮事件
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadData();
            });
        }

        // 宽度切换按钮事件
        const widthToggleBtn = document.getElementById('width-toggle-btn');
        if (widthToggleBtn) {
            widthToggleBtn.addEventListener('click', () => {
                this.toggleWidth();
            });
        }

        // 窗口大小变化处理（防抖）
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.resizeAllCharts();
            }, 200);
        });

        // 监听主题变化事件
        document.addEventListener('theme:changed', () => {
            console.log('[NetworkQuality] 主题已变化，更新所有图表主题');
            this.updateAllChartsTheme();
        });
    },

    // 验证时间范围按钮
    validateTimeRangeButtons() {
        const expectedRanges = ['1h', '6h', '24h', '7d', '30d'];
        const foundButtons = [];
        
        expectedRanges.forEach(range => {
            const btn = document.querySelector(`.time-range-btn[data-range="${range}"]`);
            if (btn) {
                foundButtons.push(range);
                // 验证按钮结构
                if (!btn.dataset.range) {
                    console.warn(`[NetworkQuality] 按钮 ${range} 缺少 data-range 属性`);
                }
            } else {
                console.error(`[NetworkQuality] 未找到时间范围按钮: ${range}`);
            }
        });
        
        console.log(`[NetworkQuality] 找到的时间范围按钮: [${foundButtons.join(', ')}]`);
        
        // 验证所有按钮都有正确的事件监听器
        const allButtons = document.querySelectorAll('.time-range-btn');
        console.log(`[NetworkQuality] 总共找到 ${allButtons.length} 个时间范围按钮`);
    },

    // 恢复宽度状态
    restoreWidthState() {
        const savedState = localStorage.getItem('networkQuality_fullWidth');
        if (savedState === 'true') {
            this.state.isFullWidth = true;
            this.applyWidthMode();
        }
        this.updateWidthButton();
    },

    // 切换宽度模式
    toggleWidth() {
        this.state.isFullWidth = !this.state.isFullWidth;
        
        // 保存状态到localStorage
        localStorage.setItem('networkQuality_fullWidth', this.state.isFullWidth.toString());
        
        // 应用宽度模式
        this.applyWidthMode();
        
        // 更新按钮状态
        this.updateWidthButton();
        
        // 延迟调整图表大小，确保容器宽度变化完成
        setTimeout(() => {
            this.resizeAllCharts();
        }, 300);
        
        console.log(`[NetworkQuality] 切换宽度模式: ${this.state.isFullWidth ? '全宽' : '标准'}`);
    },

    // 应用宽度模式
    applyWidthMode() {
        if (this.state.isFullWidth) {
            document.body.classList.add('full-width-mode');
        } else {
            document.body.classList.remove('full-width-mode');
        }
    },

    // 更新宽度切换按钮状态
    updateWidthButton() {
        const widthToggleBtn = document.getElementById('width-toggle-btn');
        if (widthToggleBtn) {
            const icon = widthToggleBtn.querySelector('.ti');
            const text = widthToggleBtn.querySelector('span');
            
            if (this.state.isFullWidth) {
                widthToggleBtn.classList.add('active');
                widthToggleBtn.title = '切换标准宽度';
                // 使用Tabler Icons替换Material Icons
                if (icon) icon.className = 'ti ti-arrows-minimize';
                if (text) text.textContent = '标准';
            } else {
                widthToggleBtn.classList.remove('active');
                widthToggleBtn.title = '切换全宽显示';
                // 使用Tabler Icons替换Material Icons
                if (icon) icon.className = 'ti ti-arrows-maximize';
                if (text) text.textContent = '全宽';
            }
        }
    },

    // 初始化筛选管理器集成
    initFilterIntegration() {
        // 尝试立即集成筛选管理器
        this.trySetupFilterIntegration();
        
        // 如果立即集成失败，延迟重试
        setTimeout(() => {
            if (!this.filterIntegrationSetup) {
                this.trySetupFilterIntegration();
            }
        }, 1000);
        
        // 再次延迟重试
        setTimeout(() => {
            if (!this.filterIntegrationSetup) {
                this.trySetupFilterIntegration();
            }
        }, 2000);
    },

    // 尝试设置筛选集成
    trySetupFilterIntegration() {
        if (window.networkQualityFilterManager && !this.filterIntegrationSetup) {
            console.log('[NetworkQuality] 筛选管理器已集成');
            
            // 监听筛选事件
            document.addEventListener('networkQualityfiltersApplied', (e) => {
                this.handleFiltersApplied(e.detail);
            });
            
            // 监听数据更新事件
            this.setupFilterDataSync();
            
            // 标记集成已完成
            this.filterIntegrationSetup = true;
            
            // 如果已经有数据，立即通知筛选管理器
            if (this.state.data && this.state.data.nodes) {
                setTimeout(() => {
                    this.notifyFilterManagerDataUpdate(this.state.data.nodes);
                }, 100);
            }
            
            return true;
        }
        return false;
    },

    // 设置筛选数据同步
    setupFilterDataSync() {
        // 当页面数据更新时，通知筛选管理器
        const originalRenderNodes = this.renderNodes;
        this.renderNodes = (nodes) => {
            // 调用原始渲染方法
            const result = originalRenderNodes.call(this, nodes);
            
            // 延迟通知筛选管理器，确保DOM已完全渲染
            setTimeout(() => {
                this.notifyFilterManagerDataUpdate(nodes);
            }, 100);
            
            return result;
        };
    },

    // 初始化功能墙集成
    // 初始化功能墙集成并等待权限检查完成后加载数据
    async initFeatureWallIntegrationAndLoadData() {
        console.log('[NetworkQuality] 开始功能墙集成和权限检查...');
        
        // 等待功能墙权限检查完成
        const hasAccess = await this.waitForFeatureWallPermissionCheck();
        
        if (hasAccess) {
            console.log('[NetworkQuality] 权限检查通过，开始加载数据');
            // 设置功能墙集成
            this.initFeatureWallIntegration();
            // 加载初始数据
            await this.loadData();
        } else {
            console.log('[NetworkQuality] 权限检查失败，功能墙应该已显示');
            // 即使权限检查失败，也要设置集成以便处理时间范围按钮
            this.initFeatureWallIntegration();
        }
    },

    // 等待功能墙权限检查完成
    async waitForFeatureWallPermissionCheck() {
        const maxWaitTime = 10000; // 最大等待10秒
        const checkInterval = 100; // 每100ms检查一次
        const startTime = Date.now();
        
        console.log('[NetworkQuality] 等待功能墙权限检查完成...');
        
        while (Date.now() - startTime < maxWaitTime) {
            // 检查功能墙是否存在且已完成初始化
            if (window.networkQualityFeatureWall) {
                // 检查功能墙是否完成了权限检查
                // 通过检查 hasAccess 属性是否已设置来判断
                const featureWall = window.networkQualityFeatureWall;
                
                // 如果 hasAccess 已经被设置（不管是 true 或 false），说明权限检查完成
                if (typeof featureWall.hasAccess === 'boolean') {
                    console.log('[NetworkQuality] 功能墙权限检查完成，结果:', featureWall.hasAccess);
                    return featureWall.hasAccess;
                }
            }
            
            // 等待一小段时间后再次检查
            await new Promise(resolve => setTimeout(resolve, checkInterval));
        }
        
        // 超时处理：假设有权限，避免阻塞页面
        console.warn('[NetworkQuality] 功能墙权限检查超时，默认允许访问');
        return true;
    },

    initFeatureWallIntegration() {
        // 尝试立即集成功能墙
        this.trySetupFeatureWallIntegration();
        
        // 如果立即集成失败，延迟重试
        setTimeout(() => {
            if (!this.featureWallIntegrationSetup) {
                this.trySetupFeatureWallIntegration();
            }
        }, 1000);
        
        // 再次延迟重试
        setTimeout(() => {
            if (!this.featureWallIntegrationSetup) {
                this.trySetupFeatureWallIntegration();
            }
        }, 2500);
    },

    // 尝试设置功能墙集成
    trySetupFeatureWallIntegration() {
        if (window.networkQualityFeatureWall && !this.featureWallIntegrationSetup) {
            console.log('[NetworkQuality] 功能墙已集成');
            
            // 应用时间范围按钮限制
            this.applyTimeRangeRestrictions();
            
            // 标记集成已完成
            this.featureWallIntegrationSetup = true;
            
            return true;
        }
        return false;
    },

    // 应用时间范围限制
    applyTimeRangeRestrictions() {
        if (!window.networkQualityFeatureWall) return;
        
        const timeRangeButtons = document.querySelectorAll('.time-range-btn');
        
        timeRangeButtons.forEach(btn => {
            const range = btn.dataset.range;
            if (range && !window.networkQualityFeatureWall.isTimeRangeAllowed(range)) {
                // 禁用按钮
                btn.disabled = true;
                btn.classList.add('opacity-50', 'cursor-not-allowed');
                
                // 添加提示
                const accessStatus = window.networkQualityFeatureWall.getAccessStatus();
                if (accessStatus.planLimits?.networkQuality?.maxTimeRange) {
                    const maxRange = accessStatus.planLimits.networkQuality.maxTimeRange;
                    const limitText = this.formatTimeRangeLimit(maxRange);
                    btn.title = `当前套餐最长支持${limitText}的数据查询，点击升级`;
                } else {
                    btn.title = '当前套餐不支持此时间范围，点击升级';
                }
                
                // 添加锁定图标
                const lockIcon = document.createElement('i');
                lockIcon.className = 'ti ti-lock text-xs ml-1 opacity-75';
                lockIcon.textContent = 'lock';
                
                if (!btn.querySelector('.ti.ti-lock')) {
                    btn.appendChild(lockIcon);
                }
                
                // 为禁用的按钮添加点击处理，显示升级提示
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showTimeRangeUpgradePrompt(range);
                }, true);
            }
        });
        
        console.log('[NetworkQuality] 时间范围限制已应用');
    },

    // 格式化时间范围限制显示
    formatTimeRangeLimit(seconds) {
        if (seconds >= 30 * 24 * 3600) {
            return `${Math.round(seconds / (30 * 24 * 3600))}个月`;
        } else if (seconds >= 24 * 3600) {
            return `${Math.round(seconds / (24 * 3600))}天`;
        } else if (seconds >= 3600) {
            return `${Math.round(seconds / 3600)}小时`;
        } else {
            return `${Math.round(seconds / 60)}分钟`;
        }
    },

    // 通知筛选管理器数据更新
    notifyFilterManagerDataUpdate(nodes) {
        const notifyFilterManager = () => {
            if (window.networkQualityFilterManager) {
                console.log('[NetworkQuality] 通知筛选管理器数据更新，节点数量:', nodes.length);
                
                // 触发数据更新事件
                const event = new CustomEvent('networkQualityDataUpdate', {
                    detail: {
                        nodes: nodes,
                        timestamp: Date.now(),
                        source: 'NetworkQualityPage'
                    }
                });
                document.dispatchEvent(event);
                return true;
            }
            return false;
        };

        // 立即尝试通知
        if (!notifyFilterManager()) {
            // 如果筛选管理器还没有初始化，等待一段时间后重试
            console.log('[NetworkQuality] 筛选管理器尚未初始化，等待后重试...');
            setTimeout(() => {
                if (!notifyFilterManager()) {
                    console.warn('[NetworkQuality] 筛选管理器初始化超时，可能需要手动刷新');
                }
            }, 1500);
        }
    },

    // 处理筛选应用事件
    handleFiltersApplied(detail) {
        console.log('[NetworkQuality] 筛选已应用:', detail);
        
        // 更新页面统计信息
        this.updateStatsWithFilter(detail);
        // 覆盖顶部为“全局（按筛选）”统计
        this.fetchAndApplyGlobalStats(detail?.totalNodes || this.state.pagination?.totalNodes || 0)
            .catch(e => console.warn('[NetworkQuality] 获取按筛选的全局统计失败:', e));

        // 若为数据驱动的筛选应用（页面刚拉取数据时触发），避免形成数据加载循环
        if (detail && detail.reason === 'data') {
            // 仅在必要时调整布局
            setTimeout(() => { this.resizeVisibleCharts(); }, 300);
            return;
        }

        // 用户触发的筛选变化：按筛选重新加载服务端数据（分页重置）
        try {
            const container = document.getElementById('nodes-grid');
            if (container) container.innerHTML = '';
            this.state.renderedNodeIds = new Set();
            this.state.visibleCount = 0;
            this.state.pagination = { page: 1, pageSize: this.config.initialVisibleCount, totalNodes: 0, hasMore: true };
            this.loadData();
        } catch (e) { console.warn('[NetworkQuality] 重置并重新加载失败:', e); }
        
        // 触发图表重新布局（如果需要）
        setTimeout(() => {
            this.resizeVisibleCharts();
        }, 300);
    },

    // 更新带筛选的统计信息
    updateStatsWithFilter(filterDetail) {
        // 最小化修复：顶部仪表盘统计仅由全局统计API驱动，避免与分页/本地筛选竞争更新
        // 此处不再改动顶部统计，保留方法占位以兼容调用方
        return;
    },

    // 调整可见图表大小
    resizeVisibleCharts() {
        Object.entries(this.state.charts).forEach(([nodeId, chart]) => {
            const cardElement = document.querySelector(`[data-node-id="${nodeId}"]`);
            if (cardElement && cardElement.style.display !== 'none' && !cardElement.classList.contains('hidden-by-filter')) {
                try {
                    chart.resize();
                } catch (error) {
                    console.warn(`[NetworkQuality] 调整图表大小失败 ${nodeId}:`, error);
                }
            }
        });
    },

    // 加载数据
    async loadData() {
        if (this.state.isLoading) return;

        this.state.isLoading = true;
        this.showLoading();

        try {
            const maxPoints = this.estimateMaxPoints();
            const params = new URLSearchParams({
                timeRange: this.config.currentTimeRange,
                series: 'latency',
                charts: 'top:5',
                maxPoints: String(maxPoints),
                page: '1',
                pageSize: String(this.config.initialVisibleCount)
            });
            // 携带筛选参数
            this.appendFilterParams(params);
            const response = await fetch(`${this.config.apiUrl}?${params.toString()}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || '获取数据失败');
            }

            // 更新状态
            this.state.data = result.data;
            // 记录分页信息
            this.state.pagination = result.data.pagination || { page: 1, pageSize: this.config.initialVisibleCount, totalNodes: (result.data.nodes || []).length, hasMore: false };
            this.state.retryCount = 0;
            this.state.lastUpdated = Date.now();

            // 如果有 facets 数据，更新筛选器计数
            if (result.data.facets) {
                // 延迟执行确保组件已初始化
                setTimeout(() => {
                    if (window.networkQualityFilterManager) {
                        window.networkQualityFilterManager.updateFacets(result.data.facets);
                    }
                }, 200);
            }

            // 顶部仪表盘数据仅通过全局统计API更新，避免与分页/本地筛选竞争
            this.renderNodes(result.data.nodes);
            this.updateLastRefreshTime();
            // 异步获取全局统计并覆盖概览（保证概览为全量数据）
            try {
                const totalNodes = (result.data.pagination && typeof result.data.pagination.totalNodes === 'number')
                    ? result.data.pagination.totalNodes
                    : (result.data.summary?.totalNodes || (result.data.nodes || []).length);
                await this.fetchAndApplyGlobalStats(totalNodes);
            } catch (e) {
                console.warn('[NetworkQuality] 获取全局统计失败（使用当前页统计作为兜底）:', e);
            }

            console.log('[NetworkQuality] 数据加载成功', result.data);

        } catch (error) {
            console.error('[NetworkQuality] 数据加载失败:', error);
            this.handleError(error);
        } finally {
            this.state.isLoading = false;
            this.hideLoading();
        }
    },

    // 异步获取全局统计并更新概览（totalNodes 使用 nodes-overview 的全量数量）
    async fetchAndApplyGlobalStats(totalNodes) {
        // 竞争更新防护：仅允许最后一次请求结果生效
        const reqId = (this.state.statsReqId || 0) + 1;
        this.state.statsReqId = reqId;
        const params = new URLSearchParams({ timeRange: this.config.currentTimeRange });
        // 携带筛选参数：与筛选管理器保持一致
        try {
            if (window.networkQualityFilterManager) {
                const fm = window.networkQualityFilterManager;
                const filters = fm.state?.activeFilters || {};
                const q = fm.state?.searchQuery || '';
                if (filters.group && filters.group !== 'all') params.set('groupId', filters.group);
                if (filters.region && filters.region !== 'all') params.set('region', filters.region);
                if (filters.status && filters.status !== 'all') params.set('status', filters.status);
                if (q) params.set('q', q);
            }
        } catch (_) {}
        const res = await fetch(`/api/network-quality/stats?${params.toString()}`);
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        const json = await res.json();
        if (!json.success) throw new Error(json.message || '获取全局统计失败');
        const stats = json.data || {};

        // 如果期间发起了更新的请求，则丢弃当前结果
        if (reqId !== this.state.statsReqId) {
            return;
        }

        const summary = {
            totalNodes: typeof stats.nodesCount === 'number' ? stats.nodesCount : (totalNodes || 0),
            totalTargets: stats.totalTargets || 0,
            avgLatency: stats.avgLatency || 0,
            healthScore: typeof stats.avgAvailability === 'number' ? Math.round(stats.avgAvailability) : 0
        };
        this.updateOverviewStats(summary);
        // 显示“汇总（按筛选）”标签
        try {
            const label = document.getElementById('overview-scope-label');
            if (label) {
                // 若存在任何筛选或默认也可显示，按需调整
                if (window.networkQualityFilterManager) {
                    const fm = window.networkQualityFilterManager;
                    const hasFilter = !!(fm.state?.activeFilters?.group || fm.state?.activeFilters?.region || fm.state?.activeFilters?.status || fm.state?.searchQuery);
                    label.classList.toggle('hidden', !hasFilter);
                } else {
                    label.classList.remove('hidden');
                }
            }
        } catch (_) {}
    },

    // 更新概览统计
    updateOverviewStats(summary) {
        if (!summary) return;

        const elements = {
            totalNodes: document.getElementById('stat-total-nodes'),
            totalTargets: document.getElementById('stat-total-targets'),
            avgLatency: document.getElementById('stat-avg-latency'),
            healthScore: document.getElementById('stat-health-score')
        };

        if (elements.totalNodes) elements.totalNodes.textContent = summary.totalNodes || 0;
        if (elements.totalTargets) elements.totalTargets.textContent = summary.totalTargets || 0;
        if (elements.avgLatency) elements.avgLatency.textContent = summary.avgLatency ? `${summary.avgLatency}ms` : '--';
        if (elements.healthScore) elements.healthScore.textContent = summary.healthScore ? `${summary.healthScore}%` : '--';
    },

    // 渲染节点（支持批次渲染与懒加载图表）
    renderNodes(nodes) {
        const container = document.getElementById('nodes-grid');
        const nodesContainer = document.getElementById('nodes-container');
        const emptyContainer = document.getElementById('empty-container');

        if (!nodes || nodes.length === 0) {
            // 显示空状态
            nodesContainer.classList.add('hidden');
            emptyContainer.classList.remove('hidden');
            return;
        }

        // 显示节点容器，隐藏空状态
        nodesContainer.classList.remove('hidden');
        emptyContainer.classList.add('hidden');

        // 清空现有内容并重置状态
        container.innerHTML = '';
        // 初始化已渲染节点集合，避免重复DOM
        this.state.renderedNodeIds = new Set();
        this.state.visibleCount = 0;
        this.state.data.nodes = nodes;

        // 渲染首批节点（去重）
        const initialToRender = Math.min(this.config.initialVisibleCount, nodes.length);
        let appended = 0;
        for (let i = 0; i < nodes.length && appended < initialToRender; i++) {
            const node = nodes[i];
            if (this.state.renderedNodeIds.has(String(node.nodeId))) continue;
            const existing = document.querySelector(`[data-node-id="${node.nodeId}"]`);
            if (existing) continue;
            const nodeCard = this.createNodeCard(node);
            container.appendChild(nodeCard);
            this.state.renderedNodeIds.add(String(node.nodeId));
            appended++;
        }
        this.state.visibleCount += appended;

        // 首屏仅创建少量图表
        const initialCharts = Math.min(this.config.initialChartCount, this.state.visibleCount);
        for (let i = 0; i < initialCharts; i++) {
            const cardEl = container.querySelector('#nodes-grid [data-node-id]');
            if (!cardEl) break;
            const nodeId = cardEl.getAttribute('data-node-id');
            const node = (this.state.data.nodes || []).find(n => String(n.nodeId) === String(nodeId));
            if (!node) continue;
            this.createNodeChart(node);
            if (cardEl) cardEl.setAttribute('data-chart-ready', 'true');
        }

        // 设置懒加载观察器与滚动批次加载
        this.setupChartLazyObserver();
        this.setupInfiniteScroll();

        // 首次根据分页状态显示/隐藏滚动提示
        const hint = document.getElementById('scroll-hint');
        if (hint) {
            if (this.state.pagination?.hasMore) {
                hint.classList.remove('hidden');
            } else {
                hint.classList.add('hidden');
            }
        }

        // 刷新节点计数显示
        this.updateNodeCounterUI();
    },

    // 估算合适的服务端下采样点数
    estimateMaxPoints() {
        try {
            const container = document.getElementById('nodes-grid');
            const width = (container && container.clientWidth) ? container.clientWidth : window.innerWidth || 1200;
            // 每 ~10px 一个点，限制在 [60, 160]
            const est = Math.round(width / 10);
            return Math.max(60, Math.min(est, 160));
        } catch (e) {
            return 96;
        }
    },

    // 设置图表懒加载观察器
    setupChartLazyObserver() {
        if (this.state.chartObserver) {
            try { this.state.chartObserver.disconnect(); } catch (_) {}
        }

        // 更宽松的可见条件，提前创建图表，避免“生成图表中...”长时间不触发
        const options = { root: null, rootMargin: '200px', threshold: 0.1 };
        this.state.chartObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const el = entry.target;
                    const card = el.closest('[data-node-id]');
                    if (!card) return;
                    const nodeId = card.getAttribute('data-node-id');
                    if (card.getAttribute('data-chart-ready') === 'true') return;

                    const node = (this.state.data.nodes || []).find(n => String(n.nodeId) === String(nodeId));
                    if (!node) return;
                    console.debug(`[NetworkQuality] 懒加载触发，创建图表: nodeId=${nodeId}`);
                    this.createNodeChart(node);
                    card.setAttribute('data-chart-ready', 'true');
                    this.state.chartObserver.unobserve(el);
                }
            });
        }, options);

        // 观察当前已渲染的卡片
        document.querySelectorAll('#nodes-grid .chart-container').forEach(el => this.state.chartObserver.observe(el));

        // 兜底：对当前视口内的卡片立即尝试创建一次图表
        setTimeout(() => this.ensureChartsForVisibleCards(), 200);
    },

    // 兜底：为可见卡片强制创建图表，避免因阈值或容器尺寸边界未触发懒加载
    ensureChartsForVisibleCards() {
        const containers = Array.from(document.querySelectorAll('#nodes-grid .chart-container'));
        const viewportH = window.innerHeight || document.documentElement.clientHeight || 800;
        containers.forEach(el => {
            const rect = el.getBoundingClientRect();
            if (rect.top < viewportH + 50 && rect.bottom > -50) {
                const card = el.closest('[data-node-id]');
                if (!card) return;
                const nodeId = card.getAttribute('data-node-id');
                if (card.getAttribute('data-chart-ready') === 'true') return;
                const node = (this.state.data.nodes || []).find(n => String(n.nodeId) === String(nodeId));
                if (!node) return;
                console.debug(`[NetworkQuality] 兜底创建图表: nodeId=${nodeId}`);
                this.createNodeChart(node);
                card.setAttribute('data-chart-ready', 'true');
            }
        });
    },

    // 设置滚动批次加载
    setupInfiniteScroll() {
        if (this.state.scrollObserver) {
            try { this.state.scrollObserver.disconnect(); } catch (_) {}
        }

        const sentinel = document.getElementById('scroll-hint');
        if (!sentinel) return;

        const options = { root: null, rootMargin: '0px', threshold: 1 };
        this.state.scrollObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // 未发生用户交互时，忽略首次触发，防止首屏自动把后续批次都加上
                    if (!this.state.hasUserInteracted) return;
                    // 防重入：批次追加或翻页进行中则忽略
                    if (this.state.isAppendingBatch || this.state.isLoadingNextPage) return;
                    this.appendNextBatch();
                }
            });
        }, options);

        this.state.scrollObserver.observe(sentinel);

        // 记录用户交互，避免首屏即刻无限追加
        const markInteracted = () => { this.state.hasUserInteracted = true; };
        window.addEventListener('scroll', markInteracted, { passive: true, once: true });
        window.addEventListener('wheel', markInteracted, { passive: true, once: true });
        window.addEventListener('touchstart', markInteracted, { passive: true, once: true });
        window.addEventListener('keydown', (e) => {
            if (['PageDown','ArrowDown','End','Space'].includes(e.key)) {
                this.state.hasUserInteracted = true;
            }
        }, { once: true });
    },

    // 追加下一批节点卡片
    appendNextBatch() {
        if (this.state.isAppendingBatch) return;
        this.state.isAppendingBatch = true;
        const nodes = this.state.data.nodes || [];
        // 若本地已显示完且服务端还有更多，先拉下一页
        if (this.state.visibleCount >= nodes.length && this.state.pagination?.hasMore) {
            this.state.isAppendingBatch = false;
            this.loadNextPage();
            return;
        }
        if (this.state.visibleCount >= nodes.length) return;

        const container = document.getElementById('nodes-grid');
        let appended = 0;
        for (let i = 0; i < nodes.length && appended < this.config.batchSize; i++) {
            const node = nodes[i];
            const idStr = String(node.nodeId);
            if (this.state.renderedNodeIds && this.state.renderedNodeIds.has(idStr)) continue;
            const existing = document.querySelector(`[data-node-id="${node.nodeId}"]`);
            if (existing) continue;
            const nodeCard = this.createNodeCard(node);
            container.appendChild(nodeCard);
            const chartEl = nodeCard.querySelector('.chart-container');
            if (chartEl && this.state.chartObserver) this.state.chartObserver.observe(chartEl);
            if (!this.state.renderedNodeIds) this.state.renderedNodeIds = new Set();
            this.state.renderedNodeIds.add(idStr);
            appended++;
        }
        this.state.visibleCount += appended;

        // 显示/隐藏滚动提示
        const hint = document.getElementById('scroll-hint');
        if (hint) {
            if (this.state.visibleCount >= nodes.length && !this.state.pagination?.hasMore) {
                hint.classList.add('hidden');
            } else {
                hint.classList.remove('hidden');
            }
        }
        this.state.isAppendingBatch = false;

        // 追加后刷新节点计数
        this.updateNodeCounterUI();
    },

    // 加载下一页节点并追加
    async loadNextPage() {
        if (this.state.isLoadingNextPage) return;
        this.state.isLoadingNextPage = true;
        try {
            const nextPage = (this.state.pagination?.page || 1) + 1;
            const maxPoints = this.estimateMaxPoints();
            const params = new URLSearchParams({
                timeRange: this.config.currentTimeRange,
                series: 'latency',
                charts: 'top:5',
                maxPoints: String(maxPoints),
                page: String(nextPage),
                pageSize: String(this.config.initialVisibleCount)
            });
            this.appendFilterParams(params);
            const res = await fetch(`${this.config.apiUrl}?${params.toString()}`);
            if (!res.ok) throw new Error(`HTTP ${res.status}`);
            const json = await res.json();
            if (!json.success) throw new Error(json.message || '加载下一页失败');

            // 分页加载时也更新 facets（如果有的话）
            if (json.data?.facets) {
                console.log('[NetworkQuality] 分页加载收到 facets 数据:', json.data.facets);
                if (window.networkQualityFilterManager) {
                    window.networkQualityFilterManager.updateFacets(json.data.facets);
                }
            }

            let newNodes = json.data?.nodes || [];
            // 去重新页节点，避免重复DOM
            const existingIds = new Set((this.state.data.nodes || []).map(n => String(n.nodeId)));
            newNodes = newNodes.filter(n => !existingIds.has(String(n.nodeId)));
            const container = document.getElementById('nodes-grid');

            // 追加卡片
            newNodes.forEach(node => {
                if (this.state.renderedNodeIds && this.state.renderedNodeIds.has(String(node.nodeId))) return;
                if (document.querySelector(`[data-node-id="${node.nodeId}"]`)) return;
                const nodeCard = this.createNodeCard(node);
                container.appendChild(nodeCard);
                const chartEl = nodeCard.querySelector('.chart-container');
                if (chartEl && this.state.chartObserver) this.state.chartObserver.observe(chartEl);
                if (!this.state.renderedNodeIds) this.state.renderedNodeIds = new Set();
                this.state.renderedNodeIds.add(String(node.nodeId));
                this.state.visibleCount += 1;
            });

            // 合并状态
            this.state.data.nodes = (this.state.data.nodes || []).concat(newNodes);
            this.state.pagination = json.data.pagination || { page: nextPage, pageSize: this.config.initialVisibleCount, totalNodes: this.state.pagination?.totalNodes || 0, hasMore: false };
        } catch (e) {
            console.error('[NetworkQuality] 加载下一页失败:', e);
        } finally {
            this.state.isLoadingNextPage = false;
            // 翻页结束后刷新节点计数
            this.updateNodeCounterUI();
        }
    },

    // 将筛选器参数拼接到请求
    appendFilterParams(params) {
        try {
            if (!params) return;
            if (window.networkQualityFilterManager) {
                const fm = window.networkQualityFilterManager;
                const filters = fm.state?.activeFilters || {};
                const q = fm.state?.searchQuery || '';
                if (filters.group && filters.group !== 'all') params.set('groupId', filters.group);
                if (filters.region && filters.region !== 'all') params.set('region', filters.region);
                if (filters.status && filters.status !== 'all') params.set('status', filters.status);
                if (q) params.set('q', q);
            }
        } catch (_) {}
    },

    // 刷新页面“显示N个节点（共M个）”计数器
    updateNodeCounterUI() {
        try {
            const counter = document.getElementById('node-counter');
            if (!counter) return;
            let displayed = 0;
            if (this.state.renderedNodeIds && this.state.renderedNodeIds.size) {
                displayed = this.state.renderedNodeIds.size;
            } else {
                displayed = document.querySelectorAll('#nodes-grid [data-node-id]').length;
            }
            const total = (this.state.pagination && typeof this.state.pagination.totalNodes === 'number')
                ? this.state.pagination.totalNodes
                : displayed;
            counter.textContent = `显示 ${displayed} 个节点（共 ${total} 个）`;
            counter.title = `显示 ${displayed} 个节点，共 ${total} 个`;
        } catch (e) {
            console.warn('[NetworkQuality] 更新节点计数失败:', e);
        }
    },

    // 创建节点卡片
    createNodeCard(node) {
        const card = document.createElement('div');
        card.className = 'network-status-card card theme-border card-hover';
        card.dataset.nodeId = node.nodeId;
        
        // 添加分组和地区信息用于筛选
        card.dataset.group = node.groupName || 'default';
        card.dataset.region = node.location?.region || '未知地区';

        // 计算状态
        const status = this.getNodeStatus(node);
        const statusClass = this.getStatusClass(status);
        const statusText = this.getStatusText(status);

        card.innerHTML = `
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <span class="server-status-indicator ${statusClass}"></span>
                        <div>
                            <h4 class="font-semibold text-slate-800 dark:text-white">${this.escapeHtml(node.nodeName)}</h4>
                            <p class="text-sm text-slate-600 dark:text-slate-400">监控目标: ${node.nodeMetrics.totalTargets}</p>
                            <p class="text-xs text-slate-500 dark:text-slate-500">活跃: ${node.nodeMetrics.activeTargets}</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center gap-2">
                        <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full">${statusText}</span>
                        <button class="enlarge-card-btn p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors" title="放大查看详细信息">
                            <i class="ti ti-arrows-maximize text-slate-600 dark:text-slate-400"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-4">
                <!-- 网络质量图表容器 -->
                <div class="chart-container" id="chart-${node.nodeId}">
                    <div class="flex items-center justify-center h-full">
                        <div class="flex flex-col items-center gap-2">
                            <i class="ti ti-signal-4g"></i>
                            <span class="text-sm text-slate-500 dark:text-slate-400">生成图表中...</span>
                        </div>
                    </div>
                </div>
                
                <!-- 快速统计 -->
                <div class="mt-4 grid grid-cols-3 gap-3 text-center">
                    <div class="bg-slate-50 dark:bg-slate-700/50 rounded-lg p-2">
                        <div class="text-xs text-slate-600 dark:text-slate-400">平均延迟</div>
                        <div class="text-sm font-semibold text-slate-800 dark:text-white">${node.nodeMetrics.avgLatency > 0 ? node.nodeMetrics.avgLatency + 'ms' : '--'}</div>
                    </div>
                    <div class="bg-slate-50 dark:bg-slate-700/50 rounded-lg p-2">
                        <div class="text-xs text-slate-600 dark:text-slate-400">可用性</div>
                        <div class="text-sm font-semibold text-slate-800 dark:text-white">${node.nodeMetrics.avgAvailability > 0 ? node.nodeMetrics.avgAvailability + '%' : '--'}</div>
                    </div>
                    <div class="bg-slate-50 dark:bg-slate-700/50 rounded-lg p-2">
                        <div class="text-xs text-slate-600 dark:text-slate-400">健康分数</div>
                        <div class="text-sm font-semibold text-slate-800 dark:text-white">${node.nodeMetrics.healthScore > 0 ? node.nodeMetrics.healthScore + '%' : '--'}</div>
                    </div>
                </div>
            </div>
        `;

        // 只允许通过专门的放大按钮来放大
        const enlargeBtn = card.querySelector('.enlarge-card-btn');
        if (enlargeBtn) {
            enlargeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.enlargeNodeCard(node, card);
            });
        }

        // 移除整个卡片的点击放大功能 - 只允许通过专门的图标按钮放大

        return card;
    },

    // 创建节点图表
    createNodeChart(node) {
        const chartContainer = document.getElementById(`chart-${node.nodeId}`);
        if (!chartContainer) {
            console.error(`图表容器未找到: chart-${node.nodeId}`);
            return;
        }

        // 销毁现有图表
        if (this.state.charts[node.nodeId]) {
            this.state.charts[node.nodeId].dispose();
        }

        // 确保容器有明确的尺寸，延迟初始化以确保DOM完全渲染
        setTimeout(() => {
            // 再次检查容器是否存在
            const container = document.getElementById(`chart-${node.nodeId}`);
            if (!container) return;

            // 确保容器有明确的尺寸
            if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                console.warn(`图表容器 chart-${node.nodeId} 尺寸为0，延迟重试`);
                setTimeout(() => this.createNodeChart(node), 200);
                return;
            }

            // 创建新图表
            const chart = echarts.init(container);
            this.state.charts[node.nodeId] = chart;

            // 准备图表数据
            const chartOption = this.getNodeChartOption(node);
            chart.setOption(chartOption);

            // 立即调用resize确保正确尺寸
            setTimeout(() => {
                if (chart && !chart.isDisposed()) {
                    chart.resize();
                }
            }, 100);

            // 移除图表的点击事件，因为现在是通过卡片点击来放大
            // 图表不再有独立的放大功能，而是通过整个卡片的放大来展示
        }, 100);
    },

    // 放大节点卡片
    enlargeNodeCard(node, originalCard) {
        // 创建模态框
        this.createNodeCardModal(node, originalCard);
    },

    // 创建节点卡片模态框
    createNodeCardModal(node, originalCard) {
        // 移除现有的模态框
        const existingModal = document.querySelector('.node-card-modal-overlay');
        if (existingModal) {
            existingModal.remove();
        }

        // 创建模态框结构
        const modalOverlay = document.createElement('div');
        modalOverlay.className = 'node-card-modal-overlay';
        modalOverlay.innerHTML = `
            <div class="node-card-modal-container">
                <div class="node-card-modal-header">
                    <h3 class="node-card-modal-title">${this.escapeHtml(node.nodeName)} - 详细监控</h3>
                    <div class="node-card-modal-controls">
                        <div class="time-range-controls">
                            <span class="time-range-label">时间范围:</span>
                            <select class="time-range-select">
                                <option value="1h" ${this.config.currentTimeRange === '1h' ? 'selected' : ''}>1小时</option>
                                <option value="6h" ${this.config.currentTimeRange === '6h' ? 'selected' : ''}>6小时</option>
                                <option value="24h" ${this.config.currentTimeRange === '24h' ? 'selected' : ''}>24小时</option>
                                <option value="7d" ${this.config.currentTimeRange === '7d' ? 'selected' : ''}>7天</option>
                                <option value="30d" ${this.config.currentTimeRange === '30d' ? 'selected' : ''}>30天</option>
                            </select>
                        </div>
                        <button class="node-card-modal-close" aria-label="关闭模态框">
                            <i class="ti ti-x"></i>
                        </button>
                    </div>
                </div>
                <div class="node-card-modal-body">
                    <div class="enlarged-node-card"></div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(modalOverlay);

        // 克隆并放大原始卡片
        this.createEnlargedCard(node, modalOverlay);

        // 绑定事件
        this.bindNodeCardModalEvents(modalOverlay, node);

        // 显示模态框
        setTimeout(() => {
            modalOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }, 10);
    },

    // 创建放大的卡片
    createEnlargedCard(node, modalOverlay) {
        const enlargedCardContainer = modalOverlay.querySelector('.enlarged-node-card');
        
        // 计算状态
        const status = this.getNodeStatus(node);
        const statusClass = this.getStatusClass(status);
        const statusText = this.getStatusText(status);

        // 创建放大的卡片内容
        enlargedCardContainer.innerHTML = `
            <div class="enlarged-card-content">
                <!-- 节点信息头部 -->
                <div class="enlarged-card-header">
                    <div class="flex items-center gap-4">
                        <span class="server-status-indicator ${statusClass}" style="width: 16px; height: 16px;"></span>
                        <div>
                            <h4 class="text-xl font-bold text-slate-800 dark:text-white">${this.escapeHtml(node.nodeName)}</h4>
                            <div class="flex items-center gap-4 mt-1">
                                <span class="text-sm text-slate-600 dark:text-slate-400">监控目标: ${node.nodeMetrics.totalTargets}</span>
                                <span class="text-sm text-slate-600 dark:text-slate-400">活跃: ${node.nodeMetrics.activeTargets}</span>
                                <span class="px-3 py-1 text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full">${statusText}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计信息区域 -->
                <div class="enlarged-card-stats">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="ti ti-gauge"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">平均延迟</div>
                                <div class="stat-value">${node.nodeMetrics.avgLatency > 0 ? node.nodeMetrics.avgLatency + 'ms' : '--'}</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="ti ti-circle-check"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">可用性</div>
                                <div class="stat-value">${node.nodeMetrics.avgAvailability > 0 ? node.nodeMetrics.avgAvailability + '%' : '--'}</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="ti ti-heart"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">健康分数</div>
                                <div class="stat-value">${node.nodeMetrics.healthScore > 0 ? node.nodeMetrics.healthScore + '%' : '--'}</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="ti ti-router"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">监控目标</div>
                                <div class="stat-value">${node.nodeMetrics.totalTargets}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="enlarged-card-chart">
                    <div class="chart-header">
                        <h5 class="text-lg font-semibold text-slate-800 dark:text-white">网络质量趋势</h5>
                    </div>
                    <div class="enlarged-chart-container" id="enlarged-chart-${node.nodeId}">
                        <div class="flex items-center justify-center h-full">
                            <div class="flex flex-col items-center gap-3">
                                <i class="ti ti-chart-line"></i>
                                <span class="text-lg text-slate-500 dark:text-slate-400">生成详细图表中...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 目标详情区域 -->
                <div class="enlarged-card-targets">
                    <div class="targets-header">
                        <h5 class="text-lg font-semibold text-slate-800 dark:text-white">监控目标详情</h5>
                    </div>
                    <div class="targets-list">
                        ${node.targets.map(target => {
                            // 计算实际的延迟和可用性数据
                            const latestLatency = target.chartData && target.chartData.latencies ? 
                                target.chartData.latencies[target.chartData.latencies.length - 1] : null;
                            const latestPacketLoss = target.chartData && target.chartData.packetLoss ? 
                                target.chartData.packetLoss[target.chartData.packetLoss.length - 1] : null;
                            const availability = latestPacketLoss !== null ? Math.round(100 - latestPacketLoss) : null;
                            
                            return `
                            <div class="target-item">
                                <div class="target-info">
                                    <span class="target-name">${this.escapeHtml(target.targetName)}</span>
                                    <span class="target-status ${target.isActive !== false ? 'active' : 'inactive'}">${target.isActive !== false ? '在线' : '离线'}</span>
                                </div>
                                <div class="target-metrics">
                                    <span class="target-metric">延迟: ${latestLatency !== null ? Math.round(latestLatency) + 'ms' : '--'}</span>
                                    <span class="target-metric">可用性: ${availability !== null ? availability + '%' : '--'}</span>
                                </div>
                            </div>
                        `;
                        }).join('')}
                    </div>
                </div>
            </div>
        `;

        // 创建放大的图表
        setTimeout(() => {
            this.createEnlargedChart(node, modalOverlay);
        }, 100);
    },

    // 创建放大的图表
    createEnlargedChart(node, modalOverlay) {
        const chartContainer = modalOverlay.querySelector(`#enlarged-chart-${node.nodeId}`);
        if (!chartContainer) {
            console.error('放大图表容器未找到');
            return;
        }

        // 确保容器有明确的尺寸
        setTimeout(() => {
            if (chartContainer.offsetWidth === 0 || chartContainer.offsetHeight === 0) {
                console.warn('放大图表容器尺寸为0，延迟重试');
                setTimeout(() => this.createEnlargedChart(node, modalOverlay), 200);
                return;
            }

            // 创建ECharts实例
            const chart = echarts.init(chartContainer);
            
            // 使用更大尺寸的图表配置
            const chartOption = this.getEnlargedChartOption(node);
            chart.setOption(chartOption);

            // 调整图表大小
            setTimeout(() => {
                if (chart && !chart.isDisposed()) {
                    chart.resize();
                }
            }, 100);

            // 存储图表实例以便后续管理
            modalOverlay._chartInstance = chart;
        }, 150);
    },

    // 获取放大图表的配置
    getEnlargedChartOption(node) {
        const isDark = document.documentElement.classList.contains('dark');
        const timeRange = this.config.currentTimeRange;
        
        // 基于原始图表配置，但使用更大的尺寸和更详细的设置
        const baseOption = this.getNodeChartOption(node);
        
        return {
            ...baseOption,
            title: {
                ...baseOption.title,
                left: 'left',
                textStyle: {
                    ...baseOption.title.textStyle,
                    fontSize: 16
                }
            },
            grid: {
                left: '5%',
                right: '5%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            legend: {
                ...baseOption.legend,
                top: 40,
                textStyle: {
                    color: this.getThemeColors().chartTextSecondary,
                    fontSize: 13
                }
            },
            tooltip: {
                ...baseOption.tooltip,
                textStyle: {
                    fontSize: 13
                }
            },
            toolbox: {
                show: true,
                feature: {
                    saveAsImage: {
                        show: true,
                        title: '保存图片'
                    },
                    dataZoom: {
                        show: true,
                        title: {
                            zoom: '区域缩放',
                            back: '重置缩放'
                        }
                    }
                },
                right: 20,
                top: 15
            }
        };
    },

    // 绑定节点卡片模态框事件
    bindNodeCardModalEvents(modalOverlay, node) {
        // 关闭按钮
        const closeBtn = modalOverlay.querySelector('.node-card-modal-close');
        closeBtn.addEventListener('click', () => {
            this.closeNodeCardModal(modalOverlay);
        });

        // 点击遮罩层关闭
        modalOverlay.addEventListener('click', (e) => {
            if (e.target === modalOverlay) {
                this.closeNodeCardModal(modalOverlay);
            }
        });

        // ESC键关闭
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                this.closeNodeCardModal(modalOverlay);
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);

        // 时间范围选择器
        const timeRangeSelect = modalOverlay.querySelector('.time-range-select');
        timeRangeSelect.addEventListener('change', (e) => {
            this.changeModalTimeRange(e.target.value, node, modalOverlay);
        });

        // 窗口大小变化处理
        const resizeHandler = () => {
            if (modalOverlay._chartInstance && !modalOverlay._chartInstance.isDisposed()) {
                modalOverlay._chartInstance.resize();
            }
        };
        window.addEventListener('resize', resizeHandler);
        modalOverlay._resizeHandler = resizeHandler;

        // 主题变化处理
        const themeHandler = () => {
            if (modalOverlay._chartInstance && !modalOverlay._chartInstance.isDisposed()) {
                console.log('[NetworkQuality] 节点卡片模态框主题已变化，更新图表主题');
                // 重新生成图表配置以应用新主题
                const option = this.getEnlargedChartOption(node);
                modalOverlay._chartInstance.setOption(option, true);
            }
        };
        document.addEventListener('theme:changed', themeHandler);
        modalOverlay._themeHandler = themeHandler;
    },

    // 关闭节点卡片模态框
    closeNodeCardModal(modalOverlay) {
        modalOverlay.classList.remove('active');
        document.body.style.overflow = '';

        // 清理图表实例
        if (modalOverlay._chartInstance) {
            modalOverlay._chartInstance.dispose();
        }

        // 清理resize事件监听器
        if (modalOverlay._resizeHandler) {
            window.removeEventListener('resize', modalOverlay._resizeHandler);
        }

        // 清理主题变化事件监听器
        if (modalOverlay._themeHandler) {
            document.removeEventListener('theme:changed', modalOverlay._themeHandler);
        }

        // 移除DOM元素
        setTimeout(() => {
            modalOverlay.remove();
        }, 300);
    },

    // 更改模态框时间范围
    changeModalTimeRange(timeRange, node, modalOverlay) {
        console.log(`[NetworkQuality] 模态框时间范围切换: ${this.config.currentTimeRange} -> ${timeRange}`);

        // 更新全局配置
        this.config.currentTimeRange = timeRange;

        // 销毁现有图表实例
        if (modalOverlay._chartInstance && !modalOverlay._chartInstance.isDisposed()) {
            modalOverlay._chartInstance.dispose();
            modalOverlay._chartInstance = null;
        }

        // 显示加载状态
        const chartContainer = modalOverlay.querySelector(`#enlarged-chart-${node.nodeId}`);
        if (chartContainer) {
            chartContainer.innerHTML = `
                <div class="flex items-center justify-center h-full">
                    <div class="flex flex-col items-center gap-3">
                        <i class="ti ti-chart-line"></i>
                        <span class="text-lg text-slate-500 dark:text-slate-400">加载新数据中...</span>
                    </div>
                </div>
            `;

            // 重新加载数据并创建图表
            this.reloadModalData(node, modalOverlay);
        }
    },

    // 重新加载模态框数据
    async reloadModalData(node, modalOverlay) {
        try {
            console.log(`[NetworkQuality] 重新加载节点 ${node.nodeId} 的数据，时间范围: ${this.config.currentTimeRange}`);

            // 重新加载整个页面数据以获取新时间范围的数据
            await this.loadData();

            // 找到更新后的节点数据
            const updatedNode = this.state.data.nodes.find(n => n.nodeId === node.nodeId);
            if (updatedNode) {
                // 使用更新后的数据创建图表
                setTimeout(() => {
                    this.createEnlargedChart(updatedNode, modalOverlay);
                }, 100);

                console.log(`[NetworkQuality] 节点 ${node.nodeId} 数据重新加载完成`);
            } else {
                console.error(`[NetworkQuality] 未找到节点 ${node.nodeId} 的更新数据`);
                this.showModalError(modalOverlay, '数据加载失败');
            }
        } catch (error) {
            console.error(`[NetworkQuality] 重新加载节点 ${node.nodeId} 数据失败:`, error);
            this.showModalError(modalOverlay, '数据加载失败: ' + error.message);
        }
    },

    // 显示模态框错误信息
    showModalError(modalOverlay, message) {
        const chartContainer = modalOverlay.querySelector('.enlarged-chart-container');
        if (chartContainer) {
            chartContainer.innerHTML = `
                <div class="flex items-center justify-center h-full">
                    <div class="flex flex-col items-center gap-3">
                        <i class="ti ti-alert-circle"></i>
                        <span class="text-lg text-red-600 dark:text-red-400">${message}</span>
                        <button class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors" onclick="location.reload()">
                            刷新页面
                        </button>
                    </div>
                </div>
            `;
        }
    },

    // 获取节点图表配置
    getNodeChartOption(node) {
        const isDark = document.documentElement.classList.contains('dark');
        const timeRange = this.config.currentTimeRange;
        
        // 合并所有目标的数据
        const allTimes = [];
        const targetSeries = [];
        
        node.targets.forEach(target => {
            if (!target.chartData || !target.chartData.times || !target.chartData.latencies) {
                return;
            }

            // 收集所有时间点
            target.chartData.times.forEach(time => {
                if (!allTimes.includes(time)) {
                    allTimes.push(time);
                }
            });

            // 创建目标的数据系列
            targetSeries.push({
                name: target.targetName,
                type: 'line',
                smooth: true,
                showSymbol: false,
                lineStyle: {
                    width: 2
                },
                data: target.chartData.times.map((time, index) => [
                    time,
                    target.chartData.latencies[index]
                ])
            });
        });

        // 排序时间
        allTimes.sort();

        return {
            backgroundColor: 'transparent',
            title: {
                text: `${node.nodeName} - 网络质量监控`,
                left: 'center',
                textStyle: {
                    color: this.getThemeColors().chartTextPrimary,
                    fontSize: 14,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                backgroundColor: this.getThemeColors().chartTooltipBg,
                borderColor: this.getThemeColors().chartTooltipBorder,
                textStyle: {
                    color: this.getThemeColors().chartTextPrimary
                },
                formatter: (params) => {
                    if (!params || params.length === 0) return '';
                    
                    let content = `<div style="margin-bottom: 4px;">${new Date(params[0].axisValue).toLocaleString()}</div>`;
                    params.forEach(param => {
                        if (param.value && param.value[1] !== null) {
                            content += `<div>${param.marker} ${param.seriesName}: ${param.value[1]}ms</div>`;
                        }
                    });
                    return content;
                }
            },
            legend: {
                data: targetSeries.map(s => s.name),
                top: 25,
                textStyle: {
                    color: this.getThemeColors().chartTextSecondary,
                    fontSize: 12
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '12%', // 增加底部空间给横轴标签
                top: '15%',    // 减少顶部空间，给图表更多高度
                containLabel: true
            },
            xAxis: {
                type: 'time',
                axisLine: {
                    lineStyle: {
                        color: this.getThemeColors().chartAxisLine
                    }
                },
                axisLabel: {
                    color: this.getThemeColors().chartTextTertiary,
                    fontSize: 11,
                    formatter: (value) => {
                        const date = new Date(value);
                        
                        // 根据时间范围决定显示格式
                        switch (timeRange) {
                            case '1h':
                                // 1小时：显示时:分，每15分钟显示
                                return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                            
                            case '6h':
                                // 6小时：显示时:分，每小时显示
                                if (date.getMinutes() === 0) {
                                    return `${date.getHours().toString().padStart(2, '0')}:00`;
                                }
                                return '';
                            
                            case '24h':
                                // 24小时：显示时间，每4小时显示一次
                                if (date.getMinutes() === 0 && date.getHours() % 4 === 0) {
                                    return `${date.getHours().toString().padStart(2, '0')}:00`;
                                }
                                return '';
                            
                            case '7d':
                                // 7天：显示月/日
                                return `${(date.getMonth() + 1)}/${date.getDate()}`;
                            
                            case '30d':
                                // 30天：显示月/日，减少密度
                                return `${(date.getMonth() + 1)}/${date.getDate()}`;
                            
                            default:
                                return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                        }
                    },
                    rotate: timeRange === '30d' ? 30 : (timeRange === '7d' ? 15 : 0), // 长时间范围时旋转标签
                    margin: timeRange === '30d' || timeRange === '7d' ? 12 : 8, // 增加标签间距
                    hideOverlap: true // 自动隐藏重叠标签
                },
                // 根据时间范围设置合适的刻度间隔
                ...(timeRange === '1h' && {
                    interval: 15 * 60 * 1000 // 15分钟间隔
                }),
                ...(timeRange === '6h' && {
                    interval: 60 * 60 * 1000 // 1小时间隔
                }),
                ...(timeRange === '24h' && {
                    interval: 4 * 60 * 60 * 1000 // 4小时间隔
                }),
                ...(timeRange === '7d' && {
                    interval: 24 * 60 * 60 * 1000 // 1天间隔
                }),
                ...(timeRange === '30d' && {
                    interval: 3 * 24 * 60 * 60 * 1000 // 3天间隔
                }),
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: this.getThemeColors().chartSplitLine,
                        type: 'dashed'
                    }
                }
            },
            yAxis: {
                type: 'value',
                name: '延迟 (ms)',
                nameTextStyle: {
                    color: this.getThemeColors().chartTextTertiary,
                    fontSize: 11
                },
                axisLine: {
                    lineStyle: {
                        color: this.getThemeColors().chartAxisLine
                    }
                },
                axisLabel: {
                    color: this.getThemeColors().chartTextTertiary,
                    fontSize: 11
                },
                splitLine: {
                    lineStyle: {
                        color: this.getThemeColors().chartSplitLine,
                        type: 'dashed'
                    }
                }
            },
            series: targetSeries
        };
    },

    // 获取节点状态
    getNodeStatus(node) {
        if (!node.nodeMetrics.activeTargets) return 'offline';
        
        const healthScore = node.nodeMetrics.healthScore;
        if (healthScore >= 95) return 'excellent';
        if (healthScore >= 85) return 'good';
        if (healthScore >= 70) return 'fair';
        return 'poor';
    },

    // 获取状态CSS类
    getStatusClass(status) {
        const classes = {
            'excellent': 'server-status-online',
            'good': 'server-status-online',
            'fair': 'server-status-warning',
            'poor': 'server-status-error',
            'offline': 'server-status-offline'
        };
        return classes[status] || 'server-status-offline';
    },

    // 获取状态文本
    getStatusText(status) {
        const texts = {
            'excellent': '优秀',
            'good': '良好',
            'fair': '一般',
            'poor': '较差',
            'offline': '离线'
        };
        return texts[status] || '未知';
    },

    // 注：这些方法已被废弃，现在直接使用API返回的分组和地区信息
    // extractGroupFromNode 和 extractRegionFromNode 方法已不再需要
    // 因为API现在直接返回 groupId 和 location 信息

    // 更改时间范围
    changeTimeRange(timeRange) {
        // 验证timeRange参数有效性
        if (!timeRange || typeof timeRange !== 'string') {
            console.error('[NetworkQuality] 无效的时间范围参数:', timeRange);
            return;
        }

        // 防止重复点击相同的时间范围
        if (this.config.currentTimeRange === timeRange && !this.state.isLoading) {
            return;
        }

        // 检查时间范围权限限制
        if (window.networkQualityFeatureWall) {
            if (!window.networkQualityFeatureWall.isTimeRangeAllowed(timeRange)) {
                console.log(`[NetworkQuality] 时间范围 ${timeRange} 被套餐限制阻止`);
                this.showTimeRangeUpgradePrompt(timeRange);
                return;
            }
        }

        // 更新按钮状态
        document.querySelectorAll('.time-range-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // 更精确地查找按钮，确保是time-range-btn类的按钮
        const targetBtn = document.querySelector(`.time-range-btn[data-range="${timeRange}"]`);
        if (targetBtn) {
            targetBtn.classList.add('active');
        } else {
            console.error(`[NetworkQuality] 未找到时间范围按钮: ${timeRange}`);
            // 尝试恢复到默认状态
            const defaultBtn = document.querySelector('.time-range-btn[data-range="1h"]');
            if (defaultBtn) {
                defaultBtn.classList.add('active');
                this.config.currentTimeRange = '1h';
            }
            return;
        }

        // 显示简短的过渡状态
        const nodesContainer = document.getElementById('nodes-container');
        if (nodesContainer) {
            nodesContainer.style.opacity = '0.6';
            nodesContainer.style.pointerEvents = 'none';
        }

        // 更新配置并重新加载数据
        this.config.currentTimeRange = timeRange;
        this.loadData().finally(() => {
            // 恢复状态
            if (nodesContainer) {
                nodesContainer.style.opacity = '1';
                nodesContainer.style.pointerEvents = 'auto';
            }
        });
    },

    // 显示加载状态
    showLoading() {
        const loadingContainer = document.getElementById('loading-container');
        const nodesContainer = document.getElementById('nodes-container');
        const emptyContainer = document.getElementById('empty-container');
        const refreshBtn = document.getElementById('refresh-btn');

        if (loadingContainer) loadingContainer.classList.remove('hidden');
        if (nodesContainer) nodesContainer.classList.add('hidden');
        if (emptyContainer) emptyContainer.classList.add('hidden');
        
        // 设置刷新按钮为loading状态
        if (refreshBtn) {
            refreshBtn.classList.add('loading');
            refreshBtn.disabled = true;
        }

        // 禁用时间范围按钮
        document.querySelectorAll('.time-range-btn').forEach(btn => {
            btn.disabled = true;
            btn.style.opacity = '0.6';
        });
    },

    // 隐藏加载状态
    hideLoading() {
        const loadingContainer = document.getElementById('loading-container');
        const refreshBtn = document.getElementById('refresh-btn');
        
        if (loadingContainer) loadingContainer.classList.add('hidden');
        
        // 恢复刷新按钮状态
        if (refreshBtn) {
            refreshBtn.classList.remove('loading');
            refreshBtn.disabled = false;
        }

        // 恢复时间范围按钮状态
        document.querySelectorAll('.time-range-btn').forEach(btn => {
            btn.disabled = false;
            btn.style.opacity = '1';
        });
    },

    // 处理错误
    handleError(error) {
        console.error('[NetworkQuality] 错误:', error);
        
        this.state.retryCount++;
        
        if (this.state.retryCount < this.config.maxRetries) {
            console.log(`[NetworkQuality] 重试 ${this.state.retryCount}/${this.config.maxRetries}`);
            setTimeout(() => {
                this.loadData();
            }, 2000 * this.state.retryCount);
        } else {
            // 显示错误状态
            this.showError(error.message);
        }
    },

    // 显示错误状态
    showError(message) {
        const container = document.getElementById('nodes-grid');
        if (container) {
            container.innerHTML = `
                <div class="col-span-full">
                    <div class="card theme-border p-8 text-center">
                        <i class="ti ti-alert-circle"></i>
                        <h3 class="text-xl font-semibold text-slate-800 dark:text-white mb-2">加载失败</h3>
                        <p class="text-slate-600 dark:text-slate-400 mb-4">${this.escapeHtml(message)}</p>
                        <button onclick="NetworkQualityPage.loadData()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                            重试
                        </button>
                    </div>
                </div>
            `;
        }
    },

    // 更新最后刷新时间显示
    updateLastRefreshTime() {
        const element = document.getElementById('last-updated-time');
        if (element) {
            if (this.state.lastUpdated) {
                const time = new Date(this.state.lastUpdated);
                element.textContent = time.toLocaleTimeString();
            } else {
                element.textContent = '--';
            }
        }
    },

    // 调整所有图表大小
    resizeAllCharts() {
        Object.values(this.state.charts).forEach(chart => {
            if (chart && !chart.isDisposed()) {
                chart.resize();
            }
        });
    },

    // 更新所有图表主题
    updateAllChartsTheme() {
        Object.entries(this.state.charts).forEach(([nodeId, chart]) => {
            if (!chart || chart.isDisposed()) return;

            try {
                // 找到对应的节点数据
                const nodeData = this.state.data.nodes.find(node => node.nodeId === nodeId);
                if (!nodeData) return;

                // 重新生成图表配置以应用新主题
                const option = this.getNodeChartOption(nodeData);
                chart.setOption(option, true);

                console.log(`[NetworkQuality] 节点 ${nodeId} 图表主题已更新`);
            } catch (error) {
                console.error(`[NetworkQuality] 更新节点 ${nodeId} 图表主题失败:`, error);
            }
        });
    },

    // 显示时间范围升级提示
    showTimeRangeUpgradePrompt(timeRange) {
        // 获取时间范围的显示名称
        const timeRangeNames = {
            '1h': '1小时',
            '6h': '6小时', 
            '24h': '24小时',
            '7d': '7天',
            '30d': '30天'
        };
        
        const timeRangeName = timeRangeNames[timeRange] || timeRange;
        
        // 创建提示弹窗
        const modal = document.createElement('div');
        modal.className = 'time-range-upgrade-modal';
        modal.innerHTML = `
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-icon">
                        <i class="ti ti-clock"></i>
                    </div>
                    <h3 class="modal-title">时间范围受限</h3>
                    <button class="modal-close" aria-label="关闭">
                        <i class="ti ti-x"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <p class="upgrade-message">
                        当前套餐无法查看<strong>${timeRangeName}</strong>的数据。
                        升级套餐即可解锁更长时间范围的网络质量监控数据。
                    </p>
                    <div class="upgrade-benefits">
                        <h4>升级后您将获得：</h4>
                        <ul>
                            <li><i class="ti ti-check"></i>更长时间范围的数据查询</li>
                            <li><i class="ti ti-check"></i>详细的历史趋势分析</li>
                            <li><i class="ti ti-check"></i>专业级监控功能</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="cancel-btn">稍后再说</button>
                    <button class="upgrade-btn">立即升级</button>
                </div>
            </div>
        `;
        
        // 添加样式
        if (!document.querySelector('#time-range-upgrade-styles')) {
            const style = document.createElement('style');
            style.id = 'time-range-upgrade-styles';
            style.textContent = `
                .time-range-upgrade-modal {
                    z-index: 10000;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }
                .time-range-upgrade-modal.show {
                    opacity: 1;
                    visibility: visible;
                }
                .time-range-upgrade-modal .modal-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    backdrop-filter: blur(4px);
                }
                
                ${this.generateModalCSS()}
            `;
            document.head.appendChild(style);
        }
        
        // 添加到页面
        document.body.appendChild(modal);
        
        // 绑定事件
        const closeBtn = modal.querySelector('.modal-close');
        const cancelBtn = modal.querySelector('.cancel-btn');
        const upgradeBtn = modal.querySelector('.upgrade-btn');
        const overlay = modal.querySelector('.modal-overlay');
        
        const closeModal = () => {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        };
        
        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);
        overlay.addEventListener('click', closeModal);
        
        upgradeBtn.addEventListener('click', () => {
            closeModal();
            // 跳转到升级页面
            this.handleUpgradeClick(timeRange);
        });
        
        // 显示模态框
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        
        // ESC键关闭
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);
    },
    
    // 处理升级点击
    handleUpgradeClick(fromTimeRange) {
        try {
            // 构建升级URL，带上来源信息
            const upgradeUrl = new URL('/upgrade', window.location.origin);
            upgradeUrl.searchParams.set('source', 'network_quality_time_range');
            upgradeUrl.searchParams.set('plan', 'pro');
            if (fromTimeRange) {
                upgradeUrl.searchParams.set('feature', fromTimeRange);
            }
            
            // 跳转到升级页面
            window.open(upgradeUrl.toString(), '_blank');
            
            console.log('[NetworkQuality] 跳转到升级页面:', upgradeUrl.toString());
        } catch (error) {
            console.error('[NetworkQuality] 升级跳转失败:', error);
            // 备用方案：跳转到许可证管理页面
            window.location.href = '/admin/license-management?upgrade=network-quality-time-range';
        }
    },

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    // 清理资源
    cleanup() {
        // 销毁所有图表
        Object.values(this.state.charts).forEach(chart => {
            if (chart && !chart.isDisposed()) {
                chart.dispose();
            }
        });
        this.state.charts = {};
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 等待ECharts加载
    const checkECharts = setInterval(() => {
        if (typeof echarts !== 'undefined') {
            clearInterval(checkECharts);
            NetworkQualityPage.init();
        }
    }, 100);
    
    // 10秒后停止检查
    setTimeout(() => {
        clearInterval(checkECharts);
        if (typeof echarts === 'undefined') {
            console.error('[NetworkQuality] ECharts加载超时，网络质量页面功能将不可用');
        }
    }, 10000);
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    NetworkQualityPage.cleanup();
});

// 导出到全局作用域
window.NetworkQualityPage = NetworkQualityPage;
