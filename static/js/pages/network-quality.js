/**
 * 网络质量聚合监控页面 JavaScript 模块
 * 负责网络质量聚合页面的所有交互逻辑和数据展示
 */

// 确保ECharts已加载
if (typeof echarts === 'undefined') {
    console.error('ECharts未加载，网络质量聚合页面功能将不可用');
}

// 网络质量聚合页面管理器
const NetworkQualityAggregatedPage = {
    // 配置
    config: {
        refreshInterval: 30000, // 30秒刷新一次
        maxRetries: 3,
        currentTimeRange: '24h'
    },

    // 状态
    state: {
        isLoading: false,
        retryCount: 0,
        charts: {}, // 存储所有图表实例
        data: {
            overview: null,
            targets: []
        }
    },

    // 初始化
    init() {
        console.log('[NetworkQuality] 初始化网络质量聚合页面');
        
        // 初始化事件监听器
        this.initEventListeners();
        
        // 初始化图表
        this.initCharts();
        
        // 加载初始数据
        this.loadData();
        
        // 启动定时刷新
        this.startAutoRefresh();
        
        console.log('[NetworkQuality] 网络质量聚合页面初始化完成');
    },

    // 初始化事件监听器
    initEventListeners() {
        // 时间范围选择器
        const timeRangeBtns = document.querySelectorAll('.time-range-btn');
        timeRangeBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const range = btn.dataset.range;
                this.changeTimeRange(range);
            });
        });

        // 监听窗口大小变化，重新调整图表大小
        window.addEventListener('resize', () => {
            this.resizeCharts();
        });

        // 监听主题变化
        document.addEventListener('theme:changed', () => {
            this.updateChartThemes();
        });
    },

    // 初始化图表
    initCharts() {
        console.log('[NetworkQuality] 初始化图表容器');
        
        // 初始化汇总图表
        this.initSummaryChart();
        
        // 初始化各服务器图表（在数据加载后进行）
    },

    // 初始化汇总图表
    initSummaryChart() {
        const container = document.getElementById('network-quality-summary-chart');
        if (!container) {
            console.error('[NetworkQuality] 汇总图表容器不存在');
            return;
        }

        try {
            this.state.charts.summary = echarts.init(container, this.getChartTheme());
            
            // 设置初始配置
            const option = this.getSummaryChartOption([]);
            this.state.charts.summary.setOption(option);
            
            console.log('[NetworkQuality] 汇总图表初始化成功');
        } catch (error) {
            console.error('[NetworkQuality] 汇总图表初始化失败:', error);
        }
    },

    // 初始化目标图表
    initTargetCharts(targets) {
        targets.forEach(target => {
            const container = document.getElementById(`network-chart-${target.id}`);
            if (!container) {
                console.warn(`[NetworkQuality] 监控目标 ${target.id} 图表容器不存在`);
                return;
            }

            try {
                const chart = echarts.init(container, this.getChartTheme());
                this.state.charts[target.id] = chart;
                
                // 设置初始配置
                const option = this.getTargetChartOption(target);
                chart.setOption(option);
                
                console.log(`[NetworkQuality] 监控目标 ${target.id} 图表初始化成功`);
            } catch (error) {
                console.error(`[NetworkQuality] 监控目标 ${target.id} 图表初始化失败:`, error);
            }
        });
    },

    // 加载数据
    async loadData() {
        if (this.state.isLoading) {
            console.log('[NetworkQuality] 数据正在加载中，跳过重复请求');
            return;
        }

        this.state.isLoading = true;
        this.showLoadingState(true);

        try {
            console.log(`[NetworkQuality] 加载网络质量数据，时间范围: ${this.config.currentTimeRange}`);
            
            // 调用API获取数据
            const response = await fetch(`/api/network-quality/overview?timeRange=${this.config.currentTimeRange}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.message || '获取数据失败');
            }

            // 更新数据
            this.state.data.overview = result.data;
            this.state.data.targets = result.data.targets || [];

            // 更新UI
            this.updateUI();
            
            // 重置重试计数
            this.state.retryCount = 0;
            
            console.log('[NetworkQuality] 数据加载成功');
        } catch (error) {
            console.error('[NetworkQuality] 数据加载失败:', error);
            this.handleLoadError(error);
        } finally {
            this.state.isLoading = false;
            this.showLoadingState(false);
        }
    },

    // 更新UI
    updateUI() {
        const data = this.state.data.overview;
        if (!data) return;

        // 更新汇总图表
        this.updateSummaryChart(data);
        
        // 初始化目标图表（如果还没有初始化）
        if (Object.keys(this.state.charts).length <= 1) { // 只有summary图表
            this.initTargetCharts(data.targets);
        }
        
        // 更新目标图表
        this.updateTargetCharts(data.targets);
        
        // 更新统计信息
        this.updateStatistics(data.targets);
    },

    // 更新汇总图表
    updateSummaryChart(data) {
        const chart = this.state.charts.summary;
        if (!chart) return;

        try {
            const option = this.getSummaryChartOption(data.targets);
            chart.setOption(option, true);
        } catch (error) {
            console.error('[NetworkQuality] 更新汇总图表失败:', error);
        }
    },

    // 更新目标图表
    updateTargetCharts(targets) {
        targets.forEach(target => {
            const chart = this.state.charts[target.id];
            if (!chart) return;

            try {
                const option = this.getTargetChartOption(target);
                chart.setOption(option, true);
            } catch (error) {
                console.error(`[NetworkQuality] 更新监控目标 ${target.id} 图表失败:`, error);
            }
        });
    },

    // 更新统计信息
    updateStatistics(targets) {
        targets.forEach(target => {
            const metrics = target.metrics || {};
            
            // 更新平均延迟
            const avgLatencyEl = document.getElementById(`avg-latency-${target.id}`);
            if (avgLatencyEl) {
                avgLatencyEl.textContent = metrics.avgLatency ? `${metrics.avgLatency}ms` : '--';
            }
            
            // 更新丢包率
            const packetLossEl = document.getElementById(`packet-loss-${target.id}`);
            if (packetLossEl) {
                packetLossEl.textContent = metrics.packetLoss ? `${metrics.packetLoss}%` : '--';
            }
            
            // 更新可用性
            const availabilityEl = document.getElementById(`availability-${target.id}`);
            if (availabilityEl) {
                availabilityEl.textContent = metrics.availability ? `${metrics.availability}%` : '--';
            }
        });
    },

    // 切换时间范围
    changeTimeRange(range) {
        if (this.config.currentTimeRange === range) return;

        console.log(`[NetworkQuality] 切换时间范围: ${this.config.currentTimeRange} -> ${range}`);
        
        // 更新配置
        this.config.currentTimeRange = range;
        
        // 更新按钮状态
        this.updateTimeRangeButtons(range);
        
        // 重新加载数据
        this.loadData();
    },

    // 更新时间范围按钮状态
    updateTimeRangeButtons(activeRange) {
        const buttons = document.querySelectorAll('.time-range-btn');
        buttons.forEach(btn => {
            if (btn.dataset.range === activeRange) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
    },

    // 获取图表主题
    getChartTheme() {
        const isDark = document.documentElement.classList.contains('dark');
        return isDark ? 'dark' : null;
    },

    // 获取汇总图表配置
    getSummaryChartOption(targets) {
        const isDark = document.documentElement.classList.contains('dark');
        
        // 准备数据
        const targetNames = targets.map(t => t.name || `Target ${t.id}`);
        const latencies = targets.map(t => t.metrics?.avgLatency || 0);
        const packetLoss = targets.map(t => t.metrics?.packetLoss || 0);

        return {
            backgroundColor: 'transparent',
            title: {
                text: '网络质量概览',
                left: 'center',
                textStyle: {
                    color: isDark ? '#f1f5f9' : '#1e293b',
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                backgroundColor: isDark ? 'rgba(30, 41, 59, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                borderColor: isDark ? '#475569' : '#e2e8f0',
                textStyle: {
                    color: isDark ? '#f1f5f9' : '#1e293b'
                }
            },
            legend: {
                data: ['平均延迟', '丢包率'],
                top: 30,
                textStyle: {
                    color: isDark ? '#cbd5e1' : '#64748b'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: targetNames,
                axisLine: {
                    lineStyle: {
                        color: isDark ? '#475569' : '#e2e8f0'
                    }
                },
                axisLabel: {
                    color: isDark ? '#94a3b8' : '#64748b',
                    rotate: 45
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '延迟 (ms)',
                    position: 'left',
                    axisLine: {
                        lineStyle: {
                            color: isDark ? '#475569' : '#e2e8f0'
                        }
                    },
                    axisLabel: {
                        color: isDark ? '#94a3b8' : '#64748b'
                    },
                    splitLine: {
                        lineStyle: {
                            color: isDark ? '#334155' : '#f1f5f9',
                            type: 'dashed'
                        }
                    }
                },
                {
                    type: 'value',
                    name: '丢包率 (%)',
                    position: 'right',
                    axisLine: {
                        lineStyle: {
                            color: isDark ? '#475569' : '#e2e8f0'
                        }
                    },
                    axisLabel: {
                        color: isDark ? '#94a3b8' : '#64748b'
                    }
                }
            ],
            series: [
                {
                    name: '平均延迟',
                    type: 'bar',
                    yAxisIndex: 0,
                    data: latencies,
                    itemStyle: {
                        color: '#3b82f6'
                    }
                },
                {
                    name: '丢包率',
                    type: 'line',
                    yAxisIndex: 1,
                    data: packetLoss,
                    lineStyle: {
                        color: '#ef4444'
                    },
                    itemStyle: {
                        color: '#ef4444'
                    }
                }
            ]
        };
    },

    // 获取监控目标图表配置
    getTargetChartOption(target) {
        const isDark = document.documentElement.classList.contains('dark');
        const chartData = target.chartData || { times: [], latencies: [] };
        
        // 格式化时间标签
        const timeLabels = chartData.times.map(time => {
            const date = new Date(time);
            return date.toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        });

        return {
            backgroundColor: 'transparent',
            title: {
                text: `${target.name} 延迟趋势`,
                left: 'center',
                textStyle: {
                    color: isDark ? '#f1f5f9' : '#1e293b',
                    fontSize: 14
                }
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    const time = chartData.times[params[0].dataIndex];
                    const latency = params[0].value;
                    const timeStr = new Date(time).toLocaleString('zh-CN');
                    return `${timeStr}<br/>延迟: ${latency}ms`;
                },
                backgroundColor: isDark ? 'rgba(30, 41, 59, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                borderColor: isDark ? '#475569' : '#e2e8f0',
                textStyle: {
                    color: isDark ? '#f1f5f9' : '#1e293b'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: timeLabels,
                axisLine: {
                    lineStyle: {
                        color: isDark ? '#475569' : '#e2e8f0'
                    }
                },
                axisLabel: {
                    color: isDark ? '#94a3b8' : '#64748b'
                }
            },
            yAxis: {
                type: 'value',
                name: '延迟 (ms)',
                axisLine: {
                    lineStyle: {
                        color: isDark ? '#475569' : '#e2e8f0'
                    }
                },
                axisLabel: {
                    color: isDark ? '#94a3b8' : '#64748b'
                },
                splitLine: {
                    lineStyle: {
                        color: isDark ? '#334155' : '#f1f5f9',
                        type: 'dashed'
                    }
                }
            },
            series: [{
                name: '延迟',
                type: 'line',
                data: chartData.latencies,
                smooth: true,
                lineStyle: {
                    color: '#10b981',
                    width: 2
                },
                itemStyle: {
                    color: '#10b981'
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0,
                            color: 'rgba(16, 185, 129, 0.3)'
                        }, {
                            offset: 1,
                            color: 'rgba(16, 185, 129, 0.1)'
                        }]
                    }
                }
            }]
        };
    },

    // 调整图表大小
    resizeCharts() {
        Object.values(this.state.charts).forEach(chart => {
            if (chart && typeof chart.resize === 'function') {
                chart.resize();
            }
        });
    },

    // 更新图表主题
    updateChartThemes() {
        Object.entries(this.state.charts).forEach(([key, chart]) => {
            if (!chart) return;
            
            try {
                if (key === 'summary') {
                    const option = this.getSummaryChartOption(this.state.data.targets);
                    chart.setOption(option, true);
                } else {
                    const target = this.state.data.targets.find(t => t.id === key);
                    if (target) {
                        const option = this.getTargetChartOption(target);
                        chart.setOption(option, true);
                    }
                }
            } catch (error) {
                console.error(`[NetworkQuality] 更新图表 ${key} 主题失败:`, error);
            }
        });
    },

    // 显示加载状态
    showLoadingState(show) {
        const summaryChart = document.getElementById('network-quality-summary-chart');
        if (summaryChart) {
            const loadingEl = summaryChart.querySelector('.flex.items-center.justify-center');
            if (loadingEl) {
                loadingEl.style.display = show ? 'flex' : 'none';
            }
        }
    },

    // 处理加载错误
    handleLoadError(error) {
        this.state.retryCount++;
        
        if (this.state.retryCount < this.config.maxRetries) {
            console.log(`[NetworkQuality] 数据加载失败，${2000 * this.state.retryCount}ms后重试 (${this.state.retryCount}/${this.config.maxRetries})`);
            setTimeout(() => {
                this.loadData();
            }, 2000 * this.state.retryCount);
        } else {
            console.error('[NetworkQuality] 数据加载失败，已达到最大重试次数');
            // 显示错误信息
            this.showErrorMessage('网络质量数据加载失败，请刷新页面重试');
        }
    },

    // 显示错误信息
    showErrorMessage(message) {
        console.error('[NetworkQuality] 显示错误信息:', message);
        // 这里可以实现错误提示UI
    },

    // 启动自动刷新
    startAutoRefresh() {
        setInterval(() => {
            if (!this.state.isLoading) {
                console.log('[NetworkQuality] 自动刷新数据');
                this.loadData();
            }
        }, this.config.refreshInterval);
    },

    // 销毁图表
    destroy() {
        Object.values(this.state.charts).forEach(chart => {
            if (chart && typeof chart.dispose === 'function') {
                chart.dispose();
            }
        });
        this.state.charts = {};
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('[NetworkQuality] DOM加载完成，准备初始化网络质量聚合页面');
    
    // 确保ECharts已加载
    if (typeof echarts !== 'undefined') {
        NetworkQualityAggregatedPage.init();
    } else {
        console.warn('[NetworkQuality] ECharts未加载，等待ECharts加载完成');
        // 等待ECharts加载
        const checkECharts = setInterval(() => {
            if (typeof echarts !== 'undefined') {
                clearInterval(checkECharts);
                NetworkQualityAggregatedPage.init();
            }
        }, 100);
        
        // 10秒后停止检查
        setTimeout(() => {
            clearInterval(checkECharts);
            if (typeof echarts === 'undefined') {
                console.error('[NetworkQuality] ECharts加载超时，网络质量聚合页面功能将不可用');
            }
        }, 10000);
    }
});

// 导出到全局作用域
window.NetworkQualityAggregatedPage = NetworkQualityAggregatedPage;