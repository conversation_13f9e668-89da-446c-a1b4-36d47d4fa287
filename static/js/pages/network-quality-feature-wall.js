/**
 * 网络质量功能墙管理器
 * 处理权限检查、403错误处理和功能墙UI显示
 */

class NetworkQualityFeatureWall {
    constructor() {
        this.hasAccess = false;
        this.currentPlan = null;
        this.planLimits = null;
        this.featureWallContainer = null;
        
        this.init();
    }

    async init() {
        this.featureWallContainer = document.getElementById('feature-wall-container');

        // 由于采用入口严格控制，能进入页面说明已有权限
        // 简化初始化流程，直接隐藏功能墙并启用所有功能
        this.hasAccess = true;
        this.hideFeatureWall();

        // 启用所有时间范围按钮，移除任何限制
        this.updateTimeRangeButtons();

        // 移除API拦截逻辑，不再需要监听403错误
        // this.interceptFetchErrors(); // 已移除

        this.bindEvents();
    }

    async checkFeatureAccess() {
        try {
            // 检查NETWORK_QUALITY功能权限
            const response = await fetch('/api/license-enhanced/feature-check', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    feature: 'NETWORK_QUALITY'
                })
            });

            if (response.ok) {
                const result = await response.json();
                this.hasAccess = result.success && result.data?.allowed;
                
                if (result.data?.planInfo) {
                    this.currentPlan = result.data.planInfo;
                }
                
                if (result.data?.limits) {
                    this.planLimits = result.data.limits;
                }
                
                console.log('[FeatureWall] 权限检查结果:', {
                    hasAccess: this.hasAccess,
                    reason: result.data?.reason,
                    plan: this.currentPlan?.name
                });
                
            } else if (response.status === 403) {
                this.hasAccess = false;
                console.log('[FeatureWall] 403 Forbidden - 无网络质量功能权限');
            } else {
                // 网络错误时假定有权限，避免误拦截
                this.hasAccess = true;
                console.warn('[FeatureWall] 权限检查失败，默认允许访问');
            }
        } catch (error) {
            console.error('[FeatureWall] 权限检查异常:', error);
            // 异常时假定有权限，避免误拦截
            this.hasAccess = true;
        }
    }

    showFeatureWall() {
        if (!this.featureWallContainer) return;

        // 显示功能墙
        this.featureWallContainer.classList.remove('hidden');
        
        // 隐藏主要内容区域
        this.hideMainContent();
        
        // 更新功能墙内容
        this.updateFeatureWallContent();
        
        console.log('[FeatureWall] 功能墙已显示');
    }

    hideFeatureWall() {
        if (!this.featureWallContainer) return;

        // 隐藏功能墙
        this.featureWallContainer.classList.add('hidden');
        
        // 显示主要内容区域
        this.showMainContent();
        
        console.log('[FeatureWall] 功能墙已隐藏');
    }

    hideMainContent() {
        // 隐藏搜索和筛选工具栏
        const searchToolbar = this.featureWallContainer?.previousElementSibling?.previousElementSibling;
        if (searchToolbar?.classList?.contains('card')) {
            searchToolbar.style.display = 'none';
        }
        
        // 隐藏时间范围选择器
        const timeRangeCard = this.featureWallContainer?.previousElementSibling;
        if (timeRangeCard?.classList?.contains('card')) {
            timeRangeCard.style.display = 'none';
        }
        
        // 隐藏其他内容区域
        const elementsToHide = [
            'loading-container',
            'nodes-container', 
            'empty-container'
        ];
        
        elementsToHide.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.style.display = 'none';
            }
        });
    }

    showMainContent() {
        // 显示搜索和筛选工具栏
        const searchToolbar = this.featureWallContainer?.previousElementSibling?.previousElementSibling;
        if (searchToolbar?.classList?.contains('card')) {
            searchToolbar.style.display = '';
        }
        
        // 显示时间范围选择器
        const timeRangeCard = this.featureWallContainer?.previousElementSibling;
        if (timeRangeCard?.classList?.contains('card')) {
            timeRangeCard.style.display = '';
        }
        
        // 显示其他内容区域
        const elementsToShow = [
            'loading-container',
            'nodes-container',
            'empty-container'
        ];
        
        elementsToShow.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.style.display = '';
            }
        });
    }

    updateFeatureWallContent() {
        // 更新当前套餐名称
        const planNameEl = document.getElementById('current-plan-name');
        if (planNameEl && this.currentPlan) {
            planNameEl.textContent = this.currentPlan.name || '免费版';
        }

        // 更新时间范围限制信息
        const limitTextEl = document.getElementById('time-range-limit-text');
        if (limitTextEl) {
            if (this.planLimits?.networkQuality?.maxTimeRange) {
                const maxRange = this.planLimits.networkQuality.maxTimeRange;
                const limitText = this.formatTimeRangeLimit(maxRange);
                limitTextEl.textContent = `数据查询限制：${limitText}`;
            } else {
                limitTextEl.textContent = '数据查询限制：仅限基础监控';
            }
        }

        // 更新功能描述
        const descriptionEl = document.getElementById('feature-wall-description');
        if (descriptionEl && this.currentPlan) {
            let description = '实时监控所有节点的网络连接质量、延迟状况和连通性指标，提供详细的TCPing数据分析和历史趋势。';
            
            if (this.planLimits?.networkQuality?.maxTimeRange) {
                const maxRange = this.planLimits.networkQuality.maxTimeRange;
                const limitText = this.formatTimeRangeLimit(maxRange);
                description += ` 当前套餐支持最长${limitText}的数据查询。`;
            }
            
            descriptionEl.textContent = description;
        }
    }

    formatTimeRangeLimit(seconds) {
        if (seconds >= 30 * 24 * 3600) {
            return `${Math.round(seconds / (30 * 24 * 3600))}个月`;
        } else if (seconds >= 24 * 3600) {
            return `${Math.round(seconds / (24 * 3600))}天`;
        } else if (seconds >= 3600) {
            return `${Math.round(seconds / 3600)}小时`;
        } else {
            return `${Math.round(seconds / 60)}分钟`;
        }
    }

    updateTimeRangeButtons() {
        // 由于采用入口严格控制且移除了时间范围限制，所有按钮都应该可用
        // 移除任何可能存在的禁用状态和锁定图标
        const timeRangeButtons = document.querySelectorAll('.time-range-btn');

        timeRangeButtons.forEach(btn => {
            // 启用所有时间范围按钮
            btn.disabled = false;
            btn.title = ''; // 清除限制提示
            btn.classList.remove('opacity-50', 'cursor-not-allowed');

            // 移除锁定图标
            const lockIcon = btn.querySelector('.ti');
            if (lockIcon && lockIcon.textContent === 'lock') {
                lockIcon.remove();
            }
        });
    }

    getTimeRangeInSeconds(timeRange) {
        switch (timeRange) {
            case '1h': return 3600;
            case '6h': return 6 * 3600;
            case '24h': return 24 * 3600;
            case '7d': return 7 * 24 * 3600;
            case '30d': return 30 * 24 * 3600;
            default: return 24 * 3600;
        }
    }

    bindEvents() {
        // 升级按钮点击事件（保留作为后备机制）
        const upgradeBtn = document.getElementById('upgrade-plan-btn');
        if (upgradeBtn) {
            upgradeBtn.addEventListener('click', () => {
                this.handleUpgradeClick();
            });
        }

        // 移除API拦截逻辑，因为采用入口严格控制，不再需要监听403错误
        // this.interceptFetchErrors(); // 已移除
    }

    handleUpgradeClick() {
        try {
            // 构建升级URL
            const upgradeUrl = new URL('/upgrade', window.location.origin);
            upgradeUrl.searchParams.set('source', 'network_quality');
            upgradeUrl.searchParams.set('plan', 'pro'); // 推荐升级到专业版
            
            // 跳转到升级页面
            window.open(upgradeUrl.toString(), '_blank');
            
            console.log('[FeatureWall] 跳转到升级页面:', upgradeUrl.toString());
        } catch (error) {
            console.error('[FeatureWall] 升级跳转失败:', error);
            // 备用方案：跳转到许可证管理页面
            window.location.href = '/admin/license-management?upgrade=network-quality';
        }
    }

    // interceptFetchErrors() 方法已移除
    // 由于采用入口严格控制，不再需要拦截API的403错误

    // 供外部调用的方法：检查特定时间范围是否被允许
    isTimeRangeAllowed(timeRange) {
        // 由于采用入口严格控制且移除了时间范围限制，所有时间范围都被允许
        return true;
    }

    // 供外部调用的方法：获取访问状态
    getAccessStatus() {
        return {
            hasAccess: this.hasAccess,
            currentPlan: this.currentPlan,
            planLimits: this.planLimits
        };
    }
}

// 全局实例
window.networkQualityFeatureWall = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.networkQualityFeatureWall = new NetworkQualityFeatureWall();
});