/**
 * Network Quality State Module
 * 网络质量状态管理模块 - 负责状态管理、事件系统和滚动观察
 */

class NetworkQualityState {
    constructor() {
        // 核心状态
        this.state = {
            // 数据状态
            data: { nodes: [], facets: null, summary: null },
            pagination: { totalNodes: 0, hasMore: false },
            
            // 加载状态
            isLoading: false,
            retryCount: 0,
            lastUpdated: null,
            statsReqId: 0,
            
            // UI状态
            visibleCount: 0,
            renderedNodeIds: new Set(),
            hasUserInteracted: false,
            
            // 观察器
            chartObserver: null
        };
        
        // 配置
        this.config = {
            currentTimeRange: '24h',
            initialChartCount: 6
        };
        
        // 事件监听器
        this.listeners = {
            dataUpdate: [],
            stateChange: [],
            error: [],
            userInteraction: []
        };
        
        this.setupEventListeners();
    }

    // 设置全局事件监听器
    setupEventListeners() {
        // 窗口尺寸变化
        window.addEventListener('resize', () => {
            this.debounce(() => {
                this.emit('stateChange', { type: 'resize' });
            }, 300);
        });

        // 主题变化检测
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const target = mutation.target;
                    if (target === document.documentElement) {
                        this.emit('stateChange', { type: 'themeChange' });
                    }
                }
            });
        });
        
        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['class']
        });
    }

    // 已移除分页滚动功能
    setupInfiniteScroll() {
        console.debug('[NetworkQualityState] 分页滚动功能已移除');
    }

    // 设置用户交互跟踪
    setupUserInteractionTracking() {
        const markInteracted = () => { 
            this.state.hasUserInteracted = true;
            this.emit('userInteraction', { type: 'firstInteraction' });
        };

        // 各种用户交互事件
        const events = [
            { name: 'scroll', options: { passive: true, once: true } },
            { name: 'wheel', options: { passive: true, once: true } },
            { name: 'touchstart', options: { passive: true, once: true } }
        ];

        events.forEach(({ name, options }) => {
            window.addEventListener(name, markInteracted, options);
        });

        // 键盘交互
        window.addEventListener('keydown', (e) => {
            if (['PageDown', 'ArrowDown', 'End', 'Space'].includes(e.key)) {
                this.state.hasUserInteracted = true;
                this.emit('userInteraction', { type: 'keyboardInteraction', key: e.key });
            }
        }, { once: true });
    }

    // 已移除批次追加功能
    appendNextBatch() {
        console.debug('[NetworkQualityState] 批次追加功能已移除');
    }

    // 更新节点计数器UI状态
    updateNodeCounterUI() {
        const totalCount = this.state.pagination?.totalNodes || 0;
        
        this.emit('stateChange', { 
            type: 'updateNodeCounter', 
            displayedCount: totalCount, 
            totalCount 
        });
    }

    // 设置配置
    setConfig(key, value) {
        if (key in this.config) {
            this.config[key] = value;
            console.debug(`[NetworkQualityState] 配置更新: ${key} = ${value}`);
            this.emit('stateChange', { type: 'configUpdate', key, value });
        }
    }

    // 获取配置
    getConfig(key = null) {
        return key ? this.config[key] : { ...this.config };
    }

    // 更新状态
    setState(updates) {
        const oldState = { ...this.state };
        Object.assign(this.state, updates);
        
        console.debug('[NetworkQualityState] 状态更新:', updates);
        this.emit('stateChange', { 
            type: 'stateUpdate', 
            updates, 
            oldState: oldState 
        });
    }

    // 获取状态
    getState(key = null) {
        return key ? this.state[key] : { ...this.state };
    }

    // 重置状态
    reset() {
        const defaultState = {
            data: { nodes: [], facets: null, summary: null },
            pagination: { page: 1, pageSize: 10, totalNodes: 0, hasMore: false },
            isLoading: false,
            isLoadingNextPage: false,
            isAppendingBatch: false,
            retryCount: 0,
            lastUpdated: null,
            statsReqId: 0,
            visibleCount: 0,
            renderedNodeIds: new Set(),
            hasUserInteracted: false,
            chartObserver: null,
            scrollObserver: null
        };
        
        // 清理观察器
        this.cleanup();
        
        this.state = defaultState;
        console.debug('[NetworkQualityState] 状态已重置');
        this.emit('stateChange', { type: 'reset' });
    }

    // 添加事件监听器
    on(eventType, callback) {
        if (!this.listeners[eventType]) {
            this.listeners[eventType] = [];
        }
        this.listeners[eventType].push(callback);
        return () => this.off(eventType, callback);
    }

    // 移除事件监听器
    off(eventType, callback) {
        if (this.listeners[eventType]) {
            const index = this.listeners[eventType].indexOf(callback);
            if (index > -1) {
                this.listeners[eventType].splice(index, 1);
            }
        }
    }

    // 触发事件
    emit(eventType, data = null) {
        if (this.listeners[eventType]) {
            this.listeners[eventType].forEach(callback => {
                try {
                    callback(data);
                } catch (e) {
                    console.error(`[NetworkQualityState] 事件回调错误 (${eventType}):`, e);
                }
            });
        }
    }

    // 防抖工具函数
    debounce(func, wait) {
        if (this._debounceTimeout) {
            clearTimeout(this._debounceTimeout);
        }
        this._debounceTimeout = setTimeout(func, wait);
    }

    // 节流工具函数
    throttle(func, limit) {
        if (!this._throttleInProgress) {
            this._throttleInProgress = true;
            func();
            setTimeout(() => {
                this._throttleInProgress = false;
            }, limit);
        }
    }

    // 更新最后刷新时间
    updateLastRefreshTime() {
        this.setState({ lastUpdated: Date.now() });
    }

    // 获取格式化的最后更新时间
    getFormattedLastUpdateTime() {
        if (!this.state.lastUpdated) return '从未更新';
        
        const now = Date.now();
        const diff = now - this.state.lastUpdated;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours}小时前`;
        } else if (minutes > 0) {
            return `${minutes}分钟前`;
        } else {
            return `${seconds}秒前`;
        }
    }

    // 检查是否需要加载更多 - 已移除分页
    shouldLoadMore() {
        return false;
    }

    // 获取统计摘要
    getStatsSummary() {
        return {
            totalNodes: this.state.pagination?.totalNodes || 0,
            displayedNodes: this.state.visibleCount,
            loadedNodes: (this.state.data.nodes || []).length,
            isLoading: this.state.isLoading,
            lastUpdated: this.getFormattedLastUpdateTime()
        };
    }

    // 清理资源
    cleanup() {
        // 清理观察器
        if (this.state.chartObserver) {
            try { 
                this.state.chartObserver.disconnect(); 
            } catch (_) {}
            this.state.chartObserver = null;
        }
        
        if (this.state.scrollObserver) {
            try { 
                this.state.scrollObserver.disconnect(); 
            } catch (_) {}
            this.state.scrollObserver = null;
        }
        
        // 清理定时器
        if (this._debounceTimeout) {
            clearTimeout(this._debounceTimeout);
            this._debounceTimeout = null;
        }
    }

    // 销毁实例
    destroy() {
        this.cleanup();
        
        // 清理所有事件监听器
        Object.keys(this.listeners).forEach(eventType => {
            this.listeners[eventType] = [];
        });
        
        console.debug('[NetworkQualityState] 实例已销毁');
    }
}

// 导出模块
window.NetworkQualityState = NetworkQualityState;