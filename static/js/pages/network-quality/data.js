/**
 * Network Quality Data Module
 * 网络质量数据管理模块 - 负责API调用、数据处理和状态管理
 */

class NetworkQualityData {
    constructor(config = {}) {
        this.config = {
            apiUrl: '/api/network-quality/nodes-overview',
            statsApiUrl: '/api/network-quality/stats',
            initialVisibleCount: 10,
            currentTimeRange: '24h',
            ...config
        };
        
        this.state = {
            data: { nodes: [], facets: null, summary: null },
            pagination: { totalNodes: 0, hasMore: false },
            isLoading: false,
            retryCount: 0,
            lastUpdated: null,
            statsReqId: 0
        };
        
        // 事件回调
        this.onDataLoaded = null;
        this.onDataError = null;
        this.onStatsUpdated = null;
        this.onFacetsUpdated = null;
    }

    // 估算最大数据点数
    estimateMaxPoints() {
        try {
            const timeRange = this.config.currentTimeRange;
            const rangeToMinutes = {
                '1h': 60, '6h': 360, '24h': 1440, '7d': 10080, '30d': 43200
            };
            const minutes = rangeToMinutes[timeRange] || 1440;
            const targetInterval = Math.max(1, Math.floor(minutes / 100));
            const est = Math.floor(minutes / targetInterval);
            return Math.max(60, Math.min(est, 160));
        } catch (e) {
            return 96;
        }
    }

    // 获取筛选参数
    getFilterParams() {
        const params = {};
        try {
            if (window.networkQualityFilterManager) {
                const fm = window.networkQualityFilterManager;
                const filters = fm.state?.activeFilters || {};
                const q = fm.state?.searchQuery || '';
                
                if (filters.group && filters.group !== 'all') {
                    params.groupId = filters.group;
                }
                if (filters.region && filters.region !== 'all') {
                    params.region = filters.region;
                }
                // 如果status为null，默认发送'all'显示所有节点
                if (filters.status && filters.status !== 'all') {
                    params.status = filters.status;
                } else if (!filters.status) {
                    params.status = 'all';
                }
                if (q) {
                    params.q = q;
                }
            }
        } catch (e) {
            console.warn('获取筛选参数失败:', e);
        }
        return params;
    }

    // 将筛选器参数拼接到URLSearchParams
    appendFilterParams(params) {
        try {
            if (!params) return;
            const filterParams = this.getFilterParams();
            Object.entries(filterParams).forEach(([key, value]) => {
                params.set(key, value);
            });
        } catch (e) {
            console.warn('拼接筛选参数失败:', e);
        }
    }

    // 构建请求参数
    buildRequestParams() {
        const maxPoints = this.estimateMaxPoints();
        const params = new URLSearchParams({
            timeRange: this.config.currentTimeRange,
            series: 'latency',
            charts: 'top:5',
            maxPoints: String(maxPoints),
            facetsScope: 'both'
        });

        // 首次加载拿齐 facets+maps+nodes+summary，后续仅取 nodes+summary+facets 精简载荷
        try {
            const includeParts = [];
            if (!this._hasLoadedOnce) includeParts.push('nodes','summary','facets','maps');
            else includeParts.push('nodes','summary','facets');
            params.set('include', includeParts.join(','));
        } catch (_) {}
        
        this.appendFilterParams(params);
        return params;
    }

    // 加载数据（主要入口）
    async loadData() {
        if (this.state.isLoading) {
            console.debug('[NetworkQualityData] 正在加载中，跳过重复请求');
            return;
        }

        this.state.isLoading = true;
        
        try {
            console.debug('[NetworkQualityData] 开始加载数据');
            
            const params = this.buildRequestParams();
            const response = await fetch(`${this.config.apiUrl}?${params.toString()}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || '获取数据失败');
            }

            // 更新状态
            this.state.data = result.data;
            this.state.pagination = { 
                totalNodes: (result.data.nodes || []).length, 
                hasMore: false 
            };
            this.state.retryCount = 0;
            this.state.lastUpdated = Date.now();
            this._hasLoadedOnce = true;

            console.debug('[NetworkQualityData] 数据加载成功', {
                nodes: result.data.nodes?.length || 0,
                totalNodes: this.state.pagination.totalNodes
            });

            // 触发回调
            this.onDataLoaded?.(result.data);

            // 处理facets数据（支持 facetsAll 与 maps）
            if (result.data.facets || result.data.facetsAll || result.data.maps) {
                const payload = {
                    filtered: result.data.facets || null,
                    all: result.data.facetsAll || null,
                    maps: result.data.maps || null
                };
                this.onFacetsUpdated?.(payload);
            }

            // 异步获取全局统计
            try {
                const totalNodes = this.state.pagination.totalNodes;
                await this.fetchGlobalStats(totalNodes);
            } catch (e) {
                console.warn('[NetworkQualityData] 获取全局统计失败:', e);
            }

            return result.data;

        } catch (error) {
            console.error('[NetworkQualityData] 数据加载失败:', error);
            this.state.retryCount++;
            this.onDataError?.(error);
            throw error;
        } finally {
            this.state.isLoading = false;
        }
    }

    // 已移除分页功能 - 直接返回空结果
    async loadNextPage() {
        console.debug('[NetworkQualityData] 分页功能已移除');
        return { nodes: [], pagination: this.state.pagination };
    }

    // 获取全局统计数据
    async fetchGlobalStats(totalNodes) {
        // 竞争更新防护：仅允许最后一次请求结果生效
        const reqId = (this.state.statsReqId || 0) + 1;
        this.state.statsReqId = reqId;
        
        try {
            const params = new URLSearchParams({ 
                timeRange: this.config.currentTimeRange 
            });
            
            // 携带筛选参数
            this.appendFilterParams(params);
            
            console.debug('[NetworkQualityData] 开始获取全局统计');
            
            const response = await fetch(`${this.config.statsApiUrl}?${params.toString()}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.message || '获取全局统计失败');
            }

            // 如果期间发起了更新的请求，则丢弃当前结果
            if (reqId !== this.state.statsReqId) {
                console.debug('[NetworkQualityData] 统计请求已过期，丢弃结果');
                return;
            }

            const stats = result.data || {};
            const summary = {
                totalNodes: typeof stats.nodesCount === 'number' ? stats.nodesCount : (totalNodes || 0),
                totalTargets: stats.totalTargets || 0,
                avgLatency: stats.avgLatency || 0,
                healthScore: typeof stats.avgAvailability === 'number' ? Math.round(stats.avgAvailability) : 0
            };

            console.debug('[NetworkQualityData] 全局统计获取成功', summary);
            
            this.onStatsUpdated?.(summary);
            return summary;

        } catch (error) {
            console.error('[NetworkQualityData] 获取全局统计失败:', error);
            throw error;
        }
    }

    // 重新加载模态框数据
    async reloadModalData(nodeId, timeRange = null) {
        try {
            const maxPoints = this.estimateMaxPoints();
            const params = new URLSearchParams({
                timeRange: timeRange || this.config.currentTimeRange,
                series: 'latency',
                charts: 'detailed',
                maxPoints: String(maxPoints),
                nodeId: String(nodeId)
            });

            console.debug('[NetworkQualityData] 重新加载模态框数据', { nodeId, timeRange });

            const response = await fetch(`${this.config.apiUrl}?${params.toString()}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || '重新加载数据失败');
            }

            // 查找对应的节点并更新数据
            const nodeData = (result.data.nodes || []).find(n => String(n.nodeId) === String(nodeId));
            
            if (nodeData) {
                // 更新本地数据中的对应节点
                const localNodeIndex = this.state.data.nodes.findIndex(n => String(n.nodeId) === String(nodeId));
                if (localNodeIndex >= 0) {
                    this.state.data.nodes[localNodeIndex] = nodeData;
                }
                
                console.debug('[NetworkQualityData] 模态框数据重新加载成功', { nodeId });
                return nodeData;
            }

            throw new Error('未找到对应节点数据');

        } catch (error) {
            console.error('[NetworkQualityData] 重新加载模态框数据失败:', error);
            this.onDataError?.(error);
            throw error;
        }
    }

    // 设置时间范围
    setTimeRange(timeRange) {
        this.config.currentTimeRange = timeRange;
        console.debug('[NetworkQualityData] 时间范围更新为:', timeRange);
    }

    // 获取当前数据
    getData() {
        return { ...this.state.data };
    }

    // 获取分页信息
    getPagination() {
        return { ...this.state.pagination };
    }

    // 获取加载状态
    getLoadingState() {
        return {
            isLoading: this.state.isLoading,
            retryCount: this.state.retryCount,
            lastUpdated: this.state.lastUpdated
        };
    }

    // 重置状态
    reset() {
        this.state = {
            data: { nodes: [], facets: null, summary: null },
            pagination: { totalNodes: 0, hasMore: false },
            isLoading: false,
            retryCount: 0,
            lastUpdated: null,
            statsReqId: 0
        };
        console.debug('[NetworkQualityData] 状态已重置');
    }

    // 销毁实例
    destroy() {
        this.reset();
        this.onDataLoaded = null;
        this.onDataError = null;
        this.onStatsUpdated = null;
        this.onFacetsUpdated = null;
        console.debug('[NetworkQualityData] 实例已销毁');
    }
}

// 导出模块
window.NetworkQualityData = NetworkQualityData;
