/**
 * Network Quality Main Module
 * 网络质量主模块 - 入口协调器，负责模块组装和初始化
 */

class NetworkQualityMain {
    constructor() {
        this.modules = {
            data: null,
            charts: null,
            ui: null,
            state: null
        };
        
        this.config = {
            currentTimeRange: '24h',
            initialVisibleCount: 10,
            initialChartCount: 6
        };
        
        this.isInitialized = false;
        this.lastAppliedFilters = { group: null, region: null, status: null, q: '' };
        this.initialize();
    }

    // 初始化主模块
    async initialize() {
        try {
            console.debug('[NetworkQualityMain] 开始初始化模块');
            
            // 确保依赖模块已加载
            await this.waitForDependencies();
            
            // 初始化各个子模块
            this.initializeModules();
            
            // 设置模块间通信
            this.setupModuleCommunication();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 初始化UI状态
            this.initializeUIState();
            
            // 加载初始数据
            await this.loadInitialData();
            
            this.isInitialized = true;
            console.debug('[NetworkQualityMain] 模块初始化完成');
            
        } catch (error) {
            console.error('[NetworkQualityMain] 初始化失败:', error);
            this.handleInitializationError(error);
        }
    }

    // 等待依赖模块加载
    async waitForDependencies() {
        const dependencies = [
            'NetworkQualityData',
            'NetworkQualityCharts', 
            'NetworkQualityUI',
            'NetworkQualityState'
        ];
        
        const maxWaitTime = 10000; // 最大等待10秒
        const checkInterval = 100; // 每100ms检查一次
        const startTime = Date.now();
        
        while (Date.now() - startTime < maxWaitTime) {
            const allLoaded = dependencies.every(dep => window[dep]);
            if (allLoaded) {
                console.debug('[NetworkQualityMain] 所有依赖模块已加载');
                return;
            }
            await new Promise(resolve => setTimeout(resolve, checkInterval));
        }
        
        throw new Error('依赖模块加载超时');
    }

    // 初始化各个子模块
    initializeModules() {
        // 初始化状态管理
        this.modules.state = new NetworkQualityState();
        
        // 初始化UI模块
        this.modules.ui = new NetworkQualityUI();
        
        // 初始化数据模块
        this.modules.data = new NetworkQualityData(this.config);
        
        // 初始化图表模块
        this.modules.charts = new NetworkQualityCharts();
        this.modules.charts.setTimeRange(this.config.currentTimeRange);
        
        console.debug('[NetworkQualityMain] 子模块初始化完成');
    }

    // 设置模块间通信
    setupModuleCommunication() {
        // 数据模块回调
        this.modules.data.onDataLoaded = (data) => {
            // 更新状态和分页信息
            this.modules.state.setState({ 
                data,
                pagination: {
                    totalNodes: (data.nodes || []).length,
                    hasMore: false
                }
            });
            this.modules.ui.renderNodes(data.nodes);
            this.createInitialCharts(data.nodes);
            // 重新挂载懒加载观察器，确保筛选/时间范围刷新后新DOM能被观察
            try {
                this.modules.charts.setupChartLazyObserver();
                // 立即为当前视口内的卡片补建图表，避免长时间显示“生成图表中...”
                setTimeout(() => this.modules.charts.ensureChartsForVisibleCards(), 100);
            } catch (_) {}
            // 更新节点计数显示
            this.modules.ui.updateNodeCounterUI(
                (data.nodes || []).length,
                (data.nodes || []).length
            );

            // 向筛选管理器广播数据更新，便于其根据最新DOM应用筛选
            try {
                const event = new CustomEvent('networkQualityDataUpdate', {
                    detail: {
                        nodes: data.nodes || [],
                        timestamp: Date.now(),
                        source: 'NetworkQualityMain'
                    }
                });
                document.dispatchEvent(event);
            } catch (_) {}
        };
        
        this.modules.data.onStatsUpdated = (summary) => {
            this.modules.ui.updateOverviewStats(summary);
        };
        
        this.modules.data.onFacetsUpdated = (facets) => {
            // 重试机制，确保 filter manager 初始化后能接收到 facets 数据
            let retryCount = 0;
            const maxRetries = 10;
            const tryUpdateFacets = () => {
                if (window.networkQualityFilterManager) {
                    window.networkQualityFilterManager.updateFacets(facets);
                    console.debug('[NetworkQualityMain] Facets 数据已更新');
                } else if (retryCount < maxRetries) {
                    retryCount++;
                    setTimeout(tryUpdateFacets, 200);
                } else {
                    console.warn('[NetworkQualityMain] FilterManager 未初始化，Facets 数据丢失');
                }
            };
            tryUpdateFacets();
        };
        
        this.modules.data.onDataError = (error) => {
            this.modules.ui.showError(error);
        };

        // 状态模块回调
        this.modules.state.on('stateChange', (event) => {
            this.handleStateChange(event);
        });
        
        this.modules.state.on('userInteraction', (event) => {
            console.debug('[NetworkQualityMain] 用户交互:', event);
        });

        // 图表模块回调
        this.modules.charts.onChartIntersection = (nodeId) => {
            const node = this.findNodeById(nodeId);
            if (node) {
                this.modules.charts.createNodeChart(node);
                const card = document.querySelector(`[data-node-id="${nodeId}"]`);
                if (card) card.setAttribute('data-chart-ready', 'true');
            }
        };
        
        this.modules.charts.onVisibleChartNeeded = (nodeId) => {
            const node = this.findNodeById(nodeId);
            if (node) {
                this.modules.charts.createNodeChart(node);
                const card = document.querySelector(`[data-node-id="${nodeId}"]`);
                if (card) card.setAttribute('data-chart-ready', 'true');
            }
        };
    }

    // 处理状态变化
    handleStateChange(event) {
        switch (event.type) {
            case 'resize':
                this.modules.charts.resizeVisibleCharts();
                break;
                
            case 'themeChange':
                this.modules.charts.updateAllChartsTheme();
                break;
                
            // 已移除分页相关处理
                
            case 'updateNodeCounter':
                this.modules.ui.updateNodeCounterUI(event.displayedCount, event.totalCount);
                break;
        }
    }

    // 已移除分页相关方法

    // 设置事件监听器
    setupEventListeners() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshData();
            });
        }

        // 全宽切换按钮
        const widthToggleBtn = document.getElementById('width-toggle-btn');
        if (widthToggleBtn) {
            widthToggleBtn.addEventListener('click', () => {
                this.modules.ui.toggleFullWidth();
                setTimeout(() => {
                    this.modules.charts.resizeAllCharts();
                }, 300);
            });
        }

        // 时间范围按钮
        const timeRangeButtons = document.querySelectorAll('.time-range-btn');
        timeRangeButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const timeRange = e.currentTarget.dataset.range;
                this.changeTimeRange(timeRange);
            });
        });

        // 用户应用筛选后，主动刷新服务端数据与统计
        document.addEventListener('networkQualityfiltersApplied', (e) => {
            const detail = e && e.detail ? e.detail : {};
            if (detail.reason === 'data') return; // 初始化广播，忽略

            const next = detail.filters || {};
            const prev = this.lastAppliedFilters || {};
            const onlyGroupChanged = (
                (next.group || null) !== (prev.group || null) &&
                (next.region || null) === (prev.region || null) &&
                (next.status || null) === (prev.status || null) &&
                (detail.searchQuery || '') === (prev.q || '')
            );

            // 更新记忆
            this.lastAppliedFilters = {
                group: next.group || null,
                region: next.region || null,
                status: next.status || null,
                q: detail.searchQuery || ''
            };

            if (onlyGroupChanged) {
                // 分组切换：使用本地数据更新概览统计，避免重复请求
                try { this.updateOverviewFromLocalGroup(next.group || null); } catch (_) {}
            } else {
                // 其他筛选变化：发起精简刷新
                this.refreshData();
            }
        });
    }

    // 基于本地数据与所选分组更新概览统计
    updateOverviewFromLocalGroup(groupId) {
        const data = this.modules.state.getState('data') || {};
        const nodes = Array.isArray(data.nodes) ? data.nodes : [];
        const subset = (!groupId || groupId === 'all')
            ? nodes
            : nodes.filter(n => String(n.groupId) === String(groupId));

        let totalTargets = 0;
        let totalLatency = 0;
        let totalAvailability = 0;
        let validNodes = 0;

        subset.forEach(n => {
            const m = n.nodeMetrics || {};
            totalTargets += (m.totalTargets || 0);
            if ((m.activeTargets || 0) > 0) {
                totalLatency += (m.avgLatency || 0);
                totalAvailability += (m.avgAvailability || 0);
                validNodes++;
            }
        });

        const summary = {
            totalNodes: subset.length,
            totalTargets: totalTargets,
            avgLatency: validNodes > 0 ? Math.round(totalLatency / validNodes) : 0,
            healthScore: validNodes > 0 ? Math.round(totalAvailability / validNodes) : 0
        };
        this.modules.ui.updateOverviewStats(summary);
    }

    // 初始化UI状态
    initializeUIState() {
        this.modules.ui.initializeFullWidthMode();
        this.setupTimeRangeButtons();
    }

    // 设置时间范围按钮状态
    setupTimeRangeButtons() {
        const timeRangeButtons = document.querySelectorAll('.time-range-btn');
        timeRangeButtons.forEach(btn => {
            const range = btn.dataset.range;
            btn.classList.toggle('active', range === this.config.currentTimeRange);
        });
    }

    // 更改时间范围
    async changeTimeRange(timeRange) {
        if (timeRange === this.config.currentTimeRange) return;
        
        this.config.currentTimeRange = timeRange;
        this.modules.data.setTimeRange(timeRange);
        this.modules.charts.setTimeRange(timeRange);
        
        this.setupTimeRangeButtons();
        await this.refreshData();
        
        console.debug('[NetworkQualityMain] 时间范围更改为:', timeRange);
    }

    // 加载初始数据
    async loadInitialData() {
        try {
            this.modules.ui.showLoading();
            await this.modules.data.loadData();
            this.setupScrollAndChartObservers();
        } catch (error) {
            console.error('[NetworkQualityMain] 加载初始数据失败:', error);
        } finally {
            this.modules.ui.hideLoading();
        }
    }

    // 刷新数据
    async refreshData() {
        try {
            console.debug('[NetworkQualityMain] 开始刷新数据');
            await this.modules.data.loadData();
            this.modules.ui.updateLastRefreshTime();
        } catch (error) {
            console.error('[NetworkQualityMain] 刷新数据失败:', error);
        }
    }

    // 已移除分页功能

    // 创建初始图表
    createInitialCharts(nodes) {
        if (!nodes || nodes.length === 0) return;
        
        const initialCharts = Math.min(this.config.initialChartCount, nodes.length);
        
        for (let i = 0; i < initialCharts; i++) {
            const node = nodes[i];
            if (node) {
                this.modules.charts.createNodeChart(node);
                const card = document.querySelector(`[data-node-id="${node.nodeId}"]`);
                if (card) card.setAttribute('data-chart-ready', 'true');
            }
        }
        
        console.debug(`[NetworkQualityMain] 创建了 ${initialCharts} 个初始图表`);
    }

    // 设置滚动和图表观察器
    setupScrollAndChartObservers() {
        this.modules.charts.setupChartLazyObserver();
        this.modules.state.setupInfiniteScroll();
        
        setTimeout(() => {
            this.modules.charts.ensureChartsForVisibleCards();
        }, 200);
    }

    // 根据ID查找节点
    findNodeById(nodeId) {
        const data = this.modules.state.getState('data');
        return (data.nodes || []).find(n => String(n.nodeId) === String(nodeId));
    }

    // 处理初始化错误
    handleInitializationError(error) {
        console.error('[NetworkQualityMain] 初始化错误:', error);
        
        const container = document.getElementById('nodes-grid');
        if (container) {
            container.innerHTML = `
                <div class="col-span-full flex flex-col items-center justify-center py-12">
                    <div class="text-red-400 mb-4">
                        <i class="ti ti-alert-circle text-6xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-slate-600 dark:text-slate-400 mb-2">初始化失败</h3>
                    <p class="text-sm text-slate-500 dark:text-slate-500 text-center max-w-md mb-4">
                        ${error.message || '未知错误'}
                    </p>
                    <button onclick="location.reload()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        重新加载
                    </button>
                </div>
            `;
        }
    }

    // 销毁实例
    destroy() {
        if (this.modules.data) this.modules.data.destroy();
        if (this.modules.charts) this.modules.charts.disposeAllCharts();
        if (this.modules.ui) this.modules.ui.destroy();
        if (this.modules.state) this.modules.state.destroy();
        
        this.modules = {};
        this.isInitialized = false;
        
        console.debug('[NetworkQualityMain] 主模块已销毁');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 将主实例绑定到全局变量，兼容原有代码
    window.NetworkQualityPage = new NetworkQualityMain();
});

// 导出模块
window.NetworkQualityMain = NetworkQualityMain;
