/**
 * Network Quality UI Module
 * 网络质量UI管理模块 - 负责DOM操作、页面渲染和用户界面更新
 */

class NetworkQualityUI {
    constructor() {
        this.elements = {
            container: null,
            nodesGrid: null,
            loadingIndicator: null,
            refreshButton: null,
            widthToggleButton: null
        };
        
        this.initializeElements();
    }

    // 初始化DOM元素引用
    initializeElements() {
        this.elements = {
            container: document.querySelector('.network-quality-container'),
            nodesGrid: document.getElementById('nodes-grid'),
            loadingContainer: document.getElementById('loading-container'),
            nodesContainer: document.getElementById('nodes-container'),
            refreshButton: document.getElementById('refresh-btn'),
            widthToggleButton: document.getElementById('width-toggle-btn')
        };
    }

    // HTML转义工具函数
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // （已移除质量标签展示，保留纯在线/离线指示灯）

    // 服务器在线状态对应的指示灯颜色
    getServerStatusClass(serverStatus) {
        const statusClasses = {
            online: 'bg-green-500',
            offline: 'bg-gray-400'
        };
        return statusClasses[serverStatus] || 'bg-gray-300';
    }

    // 服务器在线状态文本
    getServerStatusText(serverStatus) {
        return serverStatus === 'online' ? '在线' : '离线';
    }

    // 服务器在线状态徽章样式
    getServerBadgeClass(serverStatus) {
        if (serverStatus === 'online') {
            return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border border-green-200 dark:border-green-700/40';
        }
        return 'bg-slate-100 text-slate-700 dark:bg-slate-700/60 dark:text-slate-300 border border-slate-200 dark:border-slate-600/50';
    }


    // 创建节点卡片
    createNodeCard(node) {
        // 指示灯与主徽章：基于服务器在线状态
        const serverStatus = (node.nodeStatus === 1) ? 'online' : 'offline';
        const statusClass = this.getServerStatusClass(serverStatus);
        const statusText = this.getServerStatusText(serverStatus);
        const serverBadgeClass = this.getServerBadgeClass(serverStatus);

        const card = document.createElement('div');
        card.className = 'network-status-card bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600 cursor-pointer transition-all duration-200';
        card.setAttribute('data-node-id', node.nodeId);
        // 为筛选管理器提供必要的数据标注
        try {
            // 服务器在线状态（来自后端），用于“在线/离线”筛选的权威来源
            const srvStatus = (node.nodeStatus === 1) ? 'online' : 'offline';
            card.dataset.serverStatus = srvStatus;
            if (node.groupId) {
                card.dataset.group = String(node.groupId);
            }
            const regionCode = (node.location && node.location.country && node.location.country.code) ? String(node.location.country.code).toUpperCase() : 'UNKNOWN';
            card.dataset.region = regionCode;
        } catch (_) {}
        
        card.innerHTML = `
            <div class="p-4">
                <!-- 头部信息 -->
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center gap-3">
                        <span class="server-status-indicator ${statusClass} ${this.getStatusMarkerClass(serverStatus)}" style="width: 12px; height: 12px; border-radius: 50%;"></span>
                        <h3 class="font-semibold text-slate-800 dark:text-white">${this.escapeHtml(node.nodeName)}</h3>
                    </div>
                    <span class="px-2 py-1 text-xs font-medium rounded-full ${serverBadgeClass}">${statusText}</span>
                </div>
                <!-- 图表容器 -->
                <div class="chart-container" id="chart-${node.nodeId}">
                    <div class="flex items-center justify-center h-full min-h-[220px]">
                        <div class="flex flex-col items-center gap-3">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                            <span class="text-sm text-slate-500 dark:text-slate-400">生成图表中...</span>
                        </div>
                    </div>
                </div>

                <!-- 统计信息（移动到底部） -->
                <div class="grid grid-cols-3 gap-3 mt-3 pt-3 border-t border-slate-200 dark:border-slate-700">
                    <div class="text-center">
                        <div class="text-xs text-slate-500 dark:text-slate-400">延迟</div>
                        <div class="text-sm font-semibold text-slate-800 dark:text-white">
                            ${node.nodeMetrics.avgLatency > 0 ? node.nodeMetrics.avgLatency + 'ms' : '--'}
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="text-xs text-slate-500 dark:text-slate-400">可用性</div>
                        <div class="text-sm font-semibold text-slate-800 dark:text-white">
                            ${node.nodeMetrics.avgAvailability > 0 ? node.nodeMetrics.avgAvailability + '%' : '--'}
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="text-xs text-slate-500 dark:text-slate-400">目标</div>
                        <div class="text-sm font-semibold text-slate-800 dark:text-white">
                            ${node.nodeMetrics.activeTargets}/${node.nodeMetrics.totalTargets}
                        </div>
                    </div>
                </div>
            </div>
        `;

        return card;
    }

    // 为状态指示器增加统一的判定类，供筛选器检测
    getStatusMarkerClass(status) {
        switch (status) {
            case 'online':
                return 'server-status-online';
            case 'warning':
                return 'server-status-warning';
            case 'error':
                return 'server-status-error';
            case 'offline':
                return 'server-status-offline';
            default:
                return '';
        }
    }

    // 渲染节点列表
    renderNodes(nodes) {
        if (!this.elements.nodesGrid) {
            console.error('[NetworkQualityUI] nodes-grid 容器未找到');
            return;
        }

        // 清空现有内容
        this.elements.nodesGrid.innerHTML = '';

        if (!nodes || nodes.length === 0) {
            this.showEmptyState();
            return;
        }

        // 渲染节点卡片
        nodes.forEach(node => {
            const nodeCard = this.createNodeCard(node);
            this.elements.nodesGrid.appendChild(nodeCard);
        });

        // 隐藏加载状态，显示节点容器
        this.hideLoading();

        console.debug(`[NetworkQualityUI] 渲染了 ${nodes.length} 个节点卡片`);
    }

    // 追加节点卡片
    appendNodeCards(nodes, startIndex = 0) {
        if (!this.elements.nodesGrid || !nodes || nodes.length === 0) {
            return;
        }

        nodes.forEach((node, index) => {
            const nodeCard = this.createNodeCard(node);
            this.elements.nodesGrid.appendChild(nodeCard);
        });

        console.debug(`[NetworkQualityUI] 追加了 ${nodes.length} 个节点卡片`);
    }

    // 显示空状态
    showEmptyState() {
        if (!this.elements.nodesGrid) return;

        this.elements.nodesGrid.innerHTML = `
            <div class="col-span-full flex flex-col items-center justify-center py-12">
                <div class="text-slate-400 dark:text-slate-500 mb-4">
                    <i class="ti ti-search text-6xl"></i>
                </div>
                <h3 class="text-lg font-medium text-slate-600 dark:text-slate-400 mb-2">未找到匹配的节点</h3>
                <p class="text-sm text-slate-500 dark:text-slate-500 text-center max-w-md">
                    请尝试调整筛选条件或搜索关键词
                </p>
            </div>
        `;
    }

    // 显示加载状态
    showLoading() {
        if (this.elements.loadingContainer) {
            this.elements.loadingContainer.style.display = 'block';
        }
        if (this.elements.nodesContainer) {
            this.elements.nodesContainer.classList.add('hidden');
        }
    }

    // 隐藏加载状态
    hideLoading() {
        if (this.elements.loadingContainer) {
            this.elements.loadingContainer.style.display = 'none';
        }
        if (this.elements.nodesContainer) {
            this.elements.nodesContainer.classList.remove('hidden');
        }
    }

    // 更新概览统计
    updateOverviewStats(summary) {
        if (!summary) return;

        const elements = {
            totalNodes: document.getElementById('stat-total-nodes'),
            totalTargets: document.getElementById('stat-total-targets'),
            avgLatency: document.getElementById('stat-avg-latency'),
            healthScore: document.getElementById('stat-health-score')
        };

        Object.entries(elements).forEach(([key, element]) => {
            if (element && summary[key] !== undefined) {
                const value = summary[key];
                let displayValue = value;

                // 格式化数值显示
                if (key === 'avgLatency') {
                    displayValue = value > 0 ? `${value}ms` : '--';
                } else if (key === 'healthScore') {
                    displayValue = value > 0 ? `${value}%` : '--';
                } else {
                    displayValue = value || '--';
                }

                element.textContent = displayValue;
            }
        });

        console.debug('[NetworkQualityUI] 概览统计已更新', summary);
    }

    // 更新节点计数器
    updateNodeCounterUI(displayedCount, totalCount) {
        const counterElement = document.getElementById('node-counter');
        if (counterElement) {
            counterElement.textContent = `显示 ${displayedCount} 个节点（共 ${totalCount} 个）`;
        }
    }

    // 更新最后刷新时间
    updateLastRefreshTime() {
        const timeElement = document.getElementById('last-updated-time');
        if (timeElement) {
            const now = new Date().toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            timeElement.textContent = `${now}`;
        }
    }

    // 显示错误信息
    showError(error) {
        if (!this.elements.nodesGrid) return;

        this.elements.nodesGrid.innerHTML = `
            <div class="col-span-full flex flex-col items-center justify-center py-12">
                <div class="text-red-400 mb-4">
                    <i class="ti ti-alert-circle text-6xl"></i>
                </div>
                <h3 class="text-lg font-medium text-slate-600 dark:text-slate-400 mb-2">加载失败</h3>
                <p class="text-sm text-slate-500 dark:text-slate-500 text-center max-w-md mb-4">
                    ${this.escapeHtml(error.message || '未知错误')}
                </p>
                <button onclick="location.reload()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    重试
                </button>
            </div>
        `;
    }

    // 切换全宽模式
    toggleFullWidth() {
        const container = document.querySelector('.network-quality-grid');
        if (!container) return;

        const isFullWidth = container.classList.contains('full-width');
        
        if (isFullWidth) {
            container.classList.remove('full-width');
            localStorage.setItem('networkQuality_fullWidth', 'false');
        } else {
            container.classList.add('full-width');
            localStorage.setItem('networkQuality_fullWidth', 'true');
        }

        // 更新按钮状态
        this.updateWidthToggleButton(!isFullWidth);
        
        console.debug('[NetworkQualityUI] 全宽模式切换:', !isFullWidth);
    }

    // 更新全宽切换按钮状态
    updateWidthToggleButton(isFullWidth) {
        if (!this.elements.widthToggleButton) return;

        const icon = this.elements.widthToggleButton.querySelector('.ti');
        const text = this.elements.widthToggleButton.querySelector('span');

        if (icon && text) {
            if (isFullWidth) {
                icon.className = 'ti ti-layout-grid';
                text.textContent = '网格视图';
            } else {
                icon.className = 'ti ti-layout-columns';
                text.textContent = '全宽视图';
            }
        }
    }

    // 初始化全宽模式状态
    initializeFullWidthMode() {
        const savedState = localStorage.getItem('networkQuality_fullWidth');
        const isFullWidth = savedState === 'true';
        
        const container = document.querySelector('.network-quality-grid');
        if (container) {
            container.classList.toggle('full-width', isFullWidth);
        }
        
        this.updateWidthToggleButton(isFullWidth);
    }

    // 获取元素是否可见
    isElementVisible(element) {
        if (!element) return false;
        
        const rect = element.getBoundingClientRect();
        const viewHeight = Math.max(document.documentElement.clientHeight, window.innerHeight);
        return !(rect.bottom < 0 || rect.top - viewHeight >= 0);
    }

    // 滚动到顶部
    scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // 获取所有节点卡片元素
    getAllNodeCards() {
        return Array.from(document.querySelectorAll('[data-node-id]'));
    }

    // 根据节点ID查找卡片元素
    findNodeCard(nodeId) {
        return document.querySelector(`[data-node-id="${nodeId}"]`);
    }

    // 清理UI状态
    cleanup() {
        // 清理加载状态
        this.hideLoading();
        
        // 清空网格
        if (this.elements.nodesGrid) {
            this.elements.nodesGrid.innerHTML = '';
        }
        
        console.debug('[NetworkQualityUI] UI状态已清理');
    }

    // 销毁实例
    destroy() {
        this.cleanup();
        this.elements = {};
        console.debug('[NetworkQualityUI] UI实例已销毁');
    }
}

// 导出模块
window.NetworkQualityUI = NetworkQualityUI;
