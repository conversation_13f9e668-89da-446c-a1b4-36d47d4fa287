/**
 * Network Quality Charts Module
 * 网络质量图表管理模块 - 负责ECharts实例创建、配置和管理
 */

class NetworkQualityCharts {
    constructor() {
        this.charts = {}; // 存储所有图表实例
        this.chartObserver = null; // 图表懒加载观察器
        this.currentTimeRange = '24h'; // 默认时间范围
        this.colorMap = new Map(); // 颜色映射表，确保系列颜色稳定性
        
        // 确保ECharts已加载
        if (typeof echarts === 'undefined') {
            console.error('ECharts未加载，网络质量图表功能将不可用');
            throw new Error('ECharts library not loaded');
        }
    }

    // 获取CSS变量
    getCSSVariable(varName) {
        return getComputedStyle(document.documentElement).getPropertyValue(varName).trim();
    }

    // 获取主题相关的颜色值
    getThemeColors() {
        return {
            chartTextPrimary: this.getCSSVariable('--chart-text-primary'),
            chartTextSecondary: this.getCSSVariable('--chart-text-secondary'),
            chartTextTertiary: this.getCSSVariable('--chart-text-tertiary'),
            chartAxisLine: this.getCSSVariable('--chart-axis-line'),
            chartSplitLine: this.getCSSVariable('--chart-split-line'),
            chartTooltipBg: this.getCSSVariable('--chart-tooltip-bg'),
            chartTooltipBorder: this.getCSSVariable('--chart-tooltip-border')
        };
    }

    // 获取目标颜色（确保颜色稳定性）
    getColorForTarget(targetId, targetName) {
        const key = targetId || targetName;
        if (this.colorMap.has(key)) {
            return this.colorMap.get(key);
        }

        // 使用固定调色板循环分配颜色
        const colorPalette = [
            '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
            '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#d7504b',
            '#c6e579', '#f4e001', '#f0805a', '#26c6da', '#ffa726'
        ];
        
        const color = colorPalette[this.colorMap.size % colorPalette.length];
        this.colorMap.set(key, color);
        return color;
    }

    // 设置当前时间范围
    setTimeRange(timeRange) {
        this.currentTimeRange = timeRange;
    }

    // 创建节点图表
    createNodeChart(node) {
        const chartContainer = document.getElementById(`chart-${node.nodeId}`);
        if (!chartContainer) {
            console.error(`图表容器未找到: chart-${node.nodeId}`);
            return;
        }

        // 销毁现有图表
        if (this.charts[node.nodeId]) {
            this.charts[node.nodeId].dispose();
            delete this.charts[node.nodeId];
        }

        // 确保容器有明确的尺寸，增加延迟确保CSS样式应用
        setTimeout(() => {
            // 再次检查容器是否存在
            const container = document.getElementById(`chart-${node.nodeId}`);
            if (!container) return;

            // 强制设置容器尺寸，确保图表能正常初始化
            if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                container.style.minHeight = '220px';
                container.style.height = '220px';
                container.style.width = '100%';
                container.style.display = 'block';
                container.style.position = 'relative';
                
                // 强制触发重新计算
                container.offsetHeight; // 触发重排
                
                // 限制重试次数，避免无限循环
                if (!container._retryCount) container._retryCount = 0;
                if (container._retryCount < 2) {
                    container._retryCount++;
                    console.debug(`图表容器 chart-${node.nodeId} 尺寸为0，强制设置样式后第${container._retryCount}次重试，当前尺寸: ${container.offsetWidth}x${container.offsetHeight}`);
                    setTimeout(() => this.createNodeChart(node), 150);
                    return;
                } else {
                    console.warn(`图表容器 chart-${node.nodeId} 在2次重试后仍无法获得正确尺寸，强制继续创建`);
                }
            }

            // 创建新图表
            const chart = echarts.init(container);
            this.charts[node.nodeId] = chart;

            // 准备图表数据
            const chartOption = this.getNodeChartOption(node);
            
            // 调试：检查图表配置和数据
            console.debug(`[NetworkQuality] 图表配置:`, {
                nodeId: node.nodeId,
                hasTargets: node.targets?.length || 0,
                containerSize: `${container.offsetWidth}x${container.offsetHeight}`,
                optionSeries: chartOption.series?.length || 0
            });
            
            chart.setOption(chartOption);

            // 立即调用resize确保正确尺寸
            setTimeout(() => {
                if (chart && !chart.isDisposed()) {
                    chart.resize();
                    console.debug(`[NetworkQuality] 图表resize完成: nodeId=${node.nodeId}`);
                }
            }, 100);

            console.debug(`[NetworkQuality] 图表创建成功: nodeId=${node.nodeId}`);
        }, 100);
    }

    // 创建放大图表
    createEnlargedChart(node, containerId) {
        const chartContainer = document.getElementById(containerId);
        if (!chartContainer) {
            console.error(`放大图表容器未找到: ${containerId}`);
            return null;
        }

        // 确保容器有明确的尺寸
        setTimeout(() => {
            const container = document.getElementById(containerId);
            if (!container) return;

            if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                console.warn(`放大图表容器 ${containerId} 尺寸为0，延迟重试`);
                setTimeout(() => this.createEnlargedChart(node, containerId), 200);
                return;
            }

            // 创建放大的图表
            const chart = echarts.init(container);
            
            // 获取基础配置并调整为放大版本
            const baseOption = this.getNodeChartOption(node);
            const enlargedOption = this.adjustForEnlargedChart(baseOption);
            
            chart.setOption(enlargedOption);

            // 立即调用resize确保正确尺寸
            setTimeout(() => {
                if (chart && !chart.isDisposed()) {
                    chart.resize();
                }
            }, 100);

            // 存储放大图表实例（使用特殊键名）
            this.charts[`enlarged-${node.nodeId}`] = chart;
            
            console.debug(`[NetworkQuality] 放大图表创建成功: nodeId=${node.nodeId}`);
        }, 100);
    }

    // 调整配置为放大版本
    adjustForEnlargedChart(baseOption) {
        return {
            ...baseOption,
            title: {
                ...baseOption.title,
                textStyle: {
                    ...baseOption.title.textStyle,
                    fontSize: 18
                }
            },
            grid: {
                ...baseOption.grid,
                bottom: '10%',
                top: '12%'
            },
            legend: {
                ...baseOption.legend,
                textStyle: {
                    ...baseOption.legend.textStyle,
                    fontSize: 14
                }
            },
            series: baseOption.series.map(series => ({
                ...series,
                showSymbol: true, // 放大版显示数据点
                lineStyle: {
                    ...series.lineStyle,
                    width: 3 // 放大版线条加粗
                }
            }))
        };
    }

    // 获取节点图表配置选项
    getNodeChartOption(node) {
        const timeRange = this.currentTimeRange;
        
        // 合并所有目标的数据
        const allTimes = [];
        const targetSeries = [];
        
        node.targets.forEach(target => {
            if (!target.chartData || !target.chartData.times || !target.chartData.latencies) {
                return;
            }

            // 收集所有时间点
            target.chartData.times.forEach(time => {
                if (!allTimes.includes(time)) {
                    allTimes.push(time);
                }
            });

            // 创建目标的数据系列
            targetSeries.push({
                name: target.targetName,
                type: 'line',
                smooth: true,
                showSymbol: false,
                sampling: 'lttb', // 局部抽稀保持走势
                animation: false, // 关闭动画避免卡顿
                lineStyle: {
                    width: 2,
                    color: this.getColorForTarget(target.targetId, target.targetName)
                },
                emphasis: {
                    focus: 'series',
                    lineStyle: {
                        width: 3
                    }
                },
                blur: {
                    lineStyle: {
                        opacity: 0.15,
                        width: 1
                    },
                    itemStyle: {
                        opacity: 0.15
                    }
                },
                data: target.chartData.times.map((time, index) => [
                    time,
                    target.chartData.latencies[index]
                ])
            });
        });

        // 排序时间
        allTimes.sort();

        return {
            backgroundColor: 'transparent',
            title: {
                text: `${node.nodeName} - 网络质量监控`,
                left: 'center',
                textStyle: {
                    color: this.getThemeColors().chartTextPrimary,
                    fontSize: 14,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                backgroundColor: this.getThemeColors().chartTooltipBg,
                borderColor: this.getThemeColors().chartTooltipBorder,
                textStyle: {
                    color: this.getThemeColors().chartTextPrimary
                },
                confine: true, // 限制在容器内部，防止遮挡外部UI
                order: 'valueDesc', // 按当前值从大到小排序，先读重点
                formatter: (params) => {
                    if (!params || params.length === 0) return '';
                    
                    // 按值排序（从大到小）
                    const sortedParams = [...params].sort((a, b) => {
                        const aValue = a.value ? a.value[1] : 0;
                        const bValue = b.value ? b.value[1] : 0;
                        return bValue - aValue;
                    });
                    
                    let content = `<div style="margin-bottom: 4px;">${new Date(sortedParams[0].axisValue).toLocaleString()}</div>`;
                    
                    // 只展示前6条，多余用提示信息
                    const maxItems = 6;
                    const hasMore = sortedParams.length > maxItems;
                    const displayParams = hasMore ? sortedParams.slice(0, maxItems) : sortedParams;
                    
                    displayParams.forEach(param => {
                        if (param.value && param.value[1] !== null) {
                            content += `<div>${param.marker} ${param.seriesName}: ${param.value[1]}ms</div>`;
                        }
                    });
                    
                    if (hasMore) {
                        content += `<div style="margin-top: 4px; color: #999; font-size: 11px;">+${sortedParams.length - maxItems}条已隐藏</div>`;
                    }
                    
                    return content;
                }
            },
            legend: {
                data: targetSeries.map(s => s.name),
                type: 'scroll', // 滚动图例避免挤压
                selectedMode: 'single', // 单选模式，聚焦查看
                top: 25,
                textStyle: {
                    color: this.getThemeColors().chartTextSecondary,
                    fontSize: 12
                },
                formatter: (name) => {
                    // 截断长名称，避免撑爆图例
                    return name.length > 16 ? name.substring(0, 14) + '...' : name;
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '12%',
                top: '40%', // 增加顶部间距，避免图例遮挡
                containLabel: true
            },
            xAxis: {
                type: 'time',
                axisLine: {
                    lineStyle: {
                        color: this.getThemeColors().chartAxisLine
                    }
                },
                axisLabel: {
                    color: this.getThemeColors().chartTextTertiary,
                    fontSize: 11,
                    formatter: (value) => this.formatTimeLabel(value, timeRange),
                    rotate: timeRange === '30d' ? 30 : (timeRange === '7d' ? 15 : 0),
                    margin: timeRange === '30d' || timeRange === '7d' ? 12 : 8,
                    hideOverlap: true
                },
                ...this.getTimeAxisInterval(timeRange)
            },
            yAxis: {
                type: 'value',
                name: '延迟 (ms)',
                nameTextStyle: {
                    color: this.getThemeColors().chartTextSecondary,
                    fontSize: 11
                },
                axisLine: {
                    lineStyle: {
                        color: this.getThemeColors().chartAxisLine
                    }
                },
                axisLabel: {
                    color: this.getThemeColors().chartTextTertiary,
                    fontSize: 10
                },
                splitLine: {
                    lineStyle: {
                        color: this.getThemeColors().chartSplitLine,
                        type: 'dashed'
                    }
                }
            },
            series: targetSeries,
            dataZoom: [
                {
                    type: 'inside',
                    xAxisIndex: 0,
                    filterMode: 'weakFilter' // 支持鼠标滚轮/触屏缩放时间轴
                }
            ]
        };
    }

    // 格式化时间标签
    formatTimeLabel(value, timeRange) {
        const date = new Date(value);
        
        switch (timeRange) {
            case '1h':
                return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
            
            case '6h':
                if (date.getMinutes() === 0) {
                    return `${date.getHours().toString().padStart(2, '0')}:00`;
                }
                return '';
            
            case '24h':
                if (date.getMinutes() === 0 && date.getHours() % 4 === 0) {
                    return `${date.getHours().toString().padStart(2, '0')}:00`;
                }
                return '';
            
            case '7d':
                return `${(date.getMonth() + 1)}/${date.getDate()}`;
            
            case '30d':
                return `${(date.getMonth() + 1)}/${date.getDate()}`;
            
            default:
                return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        }
    }

    // 获取时间轴间隔配置
    getTimeAxisInterval(timeRange) {
        const intervals = {
            '1h': { interval: 15 * 60 * 1000 },
            '6h': { interval: 60 * 60 * 1000 },
            '24h': { interval: 4 * 60 * 60 * 1000 },
            '7d': { interval: 24 * 60 * 60 * 1000 },
            '30d': { interval: 3 * 24 * 60 * 60 * 1000 }
        };
        
        return intervals[timeRange] || {};
    }

    // 设置图表懒加载观察器
    setupChartLazyObserver() {
        if (this.chartObserver) {
            try { 
                this.chartObserver.disconnect(); 
            } catch (_) {}
        }

        const options = { 
            root: null, 
            rootMargin: '200px', 
            threshold: 0.1 
        };
        
        this.chartObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const el = entry.target;
                    const card = el.closest('[data-node-id]');
                    if (!card) return;
                    
                    const nodeId = card.getAttribute('data-node-id');
                    if (card.getAttribute('data-chart-ready') === 'true') return;

                    // 需要从外部获取节点数据
                    if (this.onChartIntersection) {
                        this.onChartIntersection(nodeId);
                    }
                    this.chartObserver.unobserve(el);
                }
            });
        }, options);

        // 观察当前已渲染的卡片
        document.querySelectorAll('#nodes-grid .chart-container').forEach(el => {
            this.chartObserver.observe(el);
        });
    }

    // 强制为可见卡片创建图表
    ensureChartsForVisibleCards() {
        const containers = Array.from(document.querySelectorAll('#nodes-grid .chart-container'));
        const viewportH = window.innerHeight || document.documentElement.clientHeight || 800;
        
        containers.forEach(el => {
            const rect = el.getBoundingClientRect();
            if (rect.top < viewportH + 50 && rect.bottom > -50) {
                const card = el.closest('[data-node-id]');
                if (!card) return;
                
                const nodeId = card.getAttribute('data-node-id');
                if (card.getAttribute('data-chart-ready') === 'true') return;
                
                // 需要从外部获取节点数据
                if (this.onVisibleChartNeeded) {
                    this.onVisibleChartNeeded(nodeId);
                }
            }
        });
    }

    // 调整所有图表尺寸
    resizeAllCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart && !chart.isDisposed()) {
                try {
                    chart.resize();
                } catch (e) {
                    console.warn('图表resize失败:', e);
                }
            }
        });
    }

    // 调整可见图表尺寸
    resizeVisibleCharts() {
        Object.entries(this.charts).forEach(([nodeId, chart]) => {
            if (chart && !chart.isDisposed()) {
                const container = document.getElementById(`chart-${nodeId.replace('enlarged-', '')}`);
                if (container && this.isElementVisible(container)) {
                    try {
                        chart.resize();
                    } catch (e) {
                        console.warn(`图表resize失败 (${nodeId}):`, e);
                    }
                }
            }
        });
    }

    // 检查元素是否可见
    isElementVisible(element) {
        const rect = element.getBoundingClientRect();
        const viewHeight = Math.max(document.documentElement.clientHeight, window.innerHeight);
        return !(rect.bottom < 0 || rect.top - viewHeight >= 0);
    }

    // 更新所有图表主题
    updateAllChartsTheme() {
        Object.entries(this.charts).forEach(([nodeId, chart]) => {
            if (chart && !chart.isDisposed()) {
                try {
                    // 获取当前配置并更新主题相关部分
                    const currentOption = chart.getOption();
                    const colors = this.getThemeColors();
                    
                    // 更新主题相关配置
                    chart.setOption({
                        title: {
                            textStyle: {
                                color: colors.chartTextPrimary
                            }
                        },
                        tooltip: {
                            backgroundColor: colors.chartTooltipBg,
                            borderColor: colors.chartTooltipBorder,
                            textStyle: {
                                color: colors.chartTextPrimary
                            }
                        },
                        legend: {
                            textStyle: {
                                color: colors.chartTextSecondary
                            }
                        },
                        xAxis: {
                            axisLine: {
                                lineStyle: {
                                    color: colors.chartAxisLine
                                }
                            },
                            axisLabel: {
                                color: colors.chartTextTertiary
                            }
                        },
                        yAxis: {
                            nameTextStyle: {
                                color: colors.chartTextSecondary
                            },
                            axisLine: {
                                lineStyle: {
                                    color: colors.chartAxisLine
                                }
                            },
                            axisLabel: {
                                color: colors.chartTextTertiary
                            },
                            splitLine: {
                                lineStyle: {
                                    color: colors.chartSplitLine
                                }
                            }
                        }
                    }, { merge: true });
                } catch (e) {
                    console.warn(`更新图表主题失败 (${nodeId}):`, e);
                }
            }
        });
    }

    // 销毁图表
    disposeChart(nodeId) {
        if (this.charts[nodeId]) {
            try {
                this.charts[nodeId].dispose();
            } catch (e) {
                console.warn(`销毁图表失败 (${nodeId}):`, e);
            }
            delete this.charts[nodeId];
        }
    }

    // 销毁所有图表
    disposeAllCharts() {
        Object.keys(this.charts).forEach(nodeId => {
            this.disposeChart(nodeId);
        });
        
        if (this.chartObserver) {
            try {
                this.chartObserver.disconnect();
            } catch (_) {}
            this.chartObserver = null;
        }
    }

    // 获取图表实例
    getChart(nodeId) {
        return this.charts[nodeId];
    }

    // 获取所有图表实例
    getAllCharts() {
        return { ...this.charts };
    }
}

// 导出模块
window.NetworkQualityCharts = NetworkQualityCharts;
