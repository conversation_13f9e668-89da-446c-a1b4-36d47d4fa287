/**
 * Universal Filter Manager (UF)
 * 统一管理服务器列表的筛选状态，使用 CSS 属性选择器实现高性能筛选
 */
(function() {
    'use strict';
    
    // 筛选状态对象
    const state = {
        group: 'all',      // 分组ID
        status: 'ALL',     // 状态: ALL/ONLINE/OFFLINE
        expiry: '',        // 到期时间: ''/'3'/'7'/'30'
        region: '',        // 地区代码
        tags: new Set()    // 标签集合
    };
    
    // 回调函数列表
    const callbacks = [];
    
    /**
     * 获取筛选容器
     * 支持多个容器 ID，优先使用 card-grid-container，然后是 list-grid-container，
     * 最后 fallback 到 groups-container 和 node-status-container
     */
    function getFilterContainer() {
        return document.getElementById('card-grid-container') || 
               document.getElementById('list-grid-container') ||
               document.getElementById('groups-container') || 
               document.getElementById('node-status-container');
    }
    
    /**
     * 强制Grid容器重新计算高度
     * 修复筛选后容器高度不正确的问题
     */
    function forceGridReflow(container) {
        if (!container) return;
        
        // 方法1: 触发重排
        const style = container.style;
        const originalDisplay = style.display;
        style.display = 'none';
        container.offsetHeight; // 强制重排
        style.display = originalDisplay || '';
        
        // 方法2: 重置Grid容器高度并动态调整最小高度
        if (container.classList.contains('grid')) {
            style.height = 'auto';
            
            // 动态计算最小高度：根据可见卡片数量调整
            const visibleCards = container.querySelectorAll('.server-card:not([data-region-filtered="true"]):not([data-hidden-by-group="true"]):not([data-hidden-by-expiry="true"]):not(.tag-hidden)');
            const visibleCount = visibleCards.length;
            
            if (visibleCount === 0) {
                style.minHeight = '60px'; // 无卡片时保持一定高度
            } else if (visibleCount <= 2) {
                style.minHeight = '0'; // 少量卡片时允许收缩
            } else {
                style.minHeight = '100px'; // 多卡片时保持默认高度
            }
        }
    }
    
    /**
     * 更新分组可见性
     * 使用 JavaScript 控制分组筛选，因为 CSS 无法处理动态分组名称
     */
    function updateGroupVisibility() {
        const container = getFilterContainer();
        if (!container) {
            console.error('[UF] updateGroupVisibility: 找不到筛选容器');
            return;
        }
        
        const cards = container.querySelectorAll('.server-card');
        let visibleCount = 0;
        let hiddenCount = 0;
        const groupStats = {};
        
        cards.forEach(card => {
            // 获取卡片的分组属性，处理 null/undefined/空字符串
            const cardGroup = card.getAttribute('data-group') || '';
            
            // 统计各分组的卡片数量
            groupStats[cardGroup] = (groupStats[cardGroup] || 0) + 1;
            
            // 判断是否应该显示该卡片
            const shouldShow = state.group === 'all' || cardGroup === state.group;
            
            // 清理可能的内联样式冲突，确保CSS规则生效
            if (card.style.display) {
                card.style.display = '';
            }
            
            // 使用 data 属性标记分组隐藏状态
            if (shouldShow) {
                card.removeAttribute('data-hidden-by-group');
                visibleCount++;
            } else {
                card.setAttribute('data-hidden-by-group', 'true');
                hiddenCount++;
            }
            
            // 调试：显示前几个卡片的分组匹配情况
            if (visibleCount + hiddenCount <= 3) {
                console.log(`[UF] 卡片${visibleCount + hiddenCount}: 分组='${cardGroup}', 筛选='${state.group}', 匹配=${shouldShow}`);
            }
        });
        
        // 强制Grid容器重新计算高度
        forceGridReflow(container);
        
        // 调试日志：显示分组筛选结果
        console.log('[UF] 分组筛选完成:', {
            currentGroup: state.group,
            totalCards: cards.length,
            visibleCount,
            hiddenCount,
            groupStats,
            containerAttributes: {
                'data-group': container.getAttribute('data-group'),
                'data-status': container.getAttribute('data-status'),
                'data-region': container.getAttribute('data-region')
            }
        });
    }
    
    /**
     * 更新到期时间筛选
     * 使用 JavaScript 控制，因为 CSS 无法进行数值比较
     */
    function updateExpiryVisibility() {
        const container = getFilterContainer();
        if (!container) return;
        
        const cards = container.querySelectorAll('.server-card');
        cards.forEach(card => {
            // 如果没有设置到期筛选，显示所有卡片
            if (!state.expiry || state.expiry === '') {
                card.removeAttribute('data-hidden-by-expiry');
                return;
            }
            
            // 获取卡片的到期天数（兼容两种属性名）
            const expiryDays = parseInt(
                card.getAttribute('data-days-until-expiry') || 
                card.getAttribute('data-expiry-days') || 
                '999', 10
            );
            const filterDays = parseInt(state.expiry, 10);
            
            // 判断是否应该显示该卡片
            // 排除已过期的节点（days_until_expiry < 0）和永久节点（999）
            const shouldShow = expiryDays >= 0 && expiryDays <= filterDays;
            
            // 清理可能的内联样式冲突，确保CSS规则生效
            if (card.style.display) {
                card.style.display = '';
            }
            
            // 使用 data 属性标记到期隐藏状态
            if (shouldShow) {
                card.removeAttribute('data-hidden-by-expiry');
            } else {
                card.setAttribute('data-hidden-by-expiry', 'true');
            }
        });
        
        // 强制Grid容器重新计算高度
        forceGridReflow(container);
    }
    
    /**
     * 更新地区筛选
     * 使用 JavaScript 控制，支持任意地区代码
     */
    function updateRegionVisibility() {
        const container = getFilterContainer();
        if (!container) return;
        
        const cards = container.querySelectorAll('.server-card');
        cards.forEach(card => {
            // 如果没有设置地区筛选，显示所有卡片
            if (!state.region || state.region === '') {
                card.removeAttribute('data-region-filtered');
                return;
            }
            
            // 获取卡片的地区代码
            const cardRegion = card.getAttribute('data-region');
            
            // 判断是否应该显示该卡片
            const shouldShow = cardRegion === state.region;
            
            // 清理可能的内联样式冲突，确保CSS规则生效
            if (card.style.display) {
                card.style.display = '';
            }
            
            // 使用 data 属性标记地区筛选状态
            if (shouldShow) {
                card.removeAttribute('data-region-filtered');
            } else {
                card.setAttribute('data-region-filtered', 'true');
            }
        });
        
        // 强制Grid容器重新计算高度
        forceGridReflow(container);
        
        // 调试日志
        const visibleCards = container.querySelectorAll('.server-card:not([data-region-filtered="true"])').length;
        const totalCards = cards.length;
        console.log('[UF] 地区筛选完成:', {
            region: state.region,
            visibleCards,
            totalCards,
            hiddenCards: totalCards - visibleCards
        });
    }
    
    /**
     * 更新容器的 data-* 属性并应用筛选
     */
    function updateContainer() {
        const container = getFilterContainer();
        if (!container) return;
        
        // 设置筛选属性
        container.setAttribute('data-group', state.group);
        container.setAttribute('data-status', state.status);
        container.setAttribute('data-expiry', state.expiry || '');
        container.setAttribute('data-region', state.region || '');
        
        // 应用分组筛选（JavaScript 控制）
        updateGroupVisibility();
        
        // 应用到期时间筛选（JavaScript 控制）
        updateExpiryVisibility();
        
        // 应用地区筛选（JavaScript 控制）
        updateRegionVisibility();
        
        // 处理标签筛选（已有逻辑）
        updateTagFilters();
    }
    
    /**
     * 处理标签筛选（标签逻辑复杂，使用 class 方式）
     */
    function updateTagFilters() {
        const container = getFilterContainer();
        const serverCards = document.querySelectorAll('.server-card');
        
        serverCards.forEach(card => {
            // 清理可能的内联样式冲突，确保CSS规则生效
            if (card.style.display) {
                card.style.display = '';
            }
            
            if (state.tags.size === 0) {
                // 无标签筛选时，移除隐藏类
                card.classList.remove('tag-hidden');
            } else {
                // 检查卡片是否包含任意选中的标签
                const cardTags = card.getAttribute('data-tags');
                const hasMatchingTag = cardTags && 
                    Array.from(state.tags).some(tag => 
                        cardTags.includes(tag)
                    );
                
                if (hasMatchingTag) {
                    card.classList.remove('tag-hidden');
                } else {
                    card.classList.add('tag-hidden');
                }
            }
        });
        
        // 强制Grid容器重新计算高度
        if (container) {
            forceGridReflow(container);
        }
    }
    
    /**
     * 通知所有注册的回调函数
     */
    function notifyCallbacks() {
        const currentState = getState();
        callbacks.forEach(callback => {
            try {
                callback(currentState);
            } catch (error) {
                console.error('Filter callback error:', error);
            }
        });

        // After applying filters, refresh the sort
        if (window.SortManager) {
            window.SortManager.applyCurrentSort();
        }
    }
    
    /**
     * 获取当前状态的深拷贝
     */
    function getState() {
        return {
            group: state.group,
            status: state.status,
            expiry: state.expiry,
            region: state.region,
            tags: new Set(state.tags)
        };
    }
    
    // 公开 API
    const UF = {
        /**
         * 按分组筛选
         * @param {string} groupId - 分组ID，'all' 表示全部
         */
        filterByGroup(groupId) {
            // 🔍 分组筛选调试日志
            const beforeCards = document.querySelectorAll('.server-card').length;
            console.log('[UF] filterByGroup 被调用:', {
                currentGroup: state.group,
                newGroup: groupId,
                willUpdate: state.group !== groupId,
                筛选前卡片数: beforeCards
            });
            
            // 点击相同按钮时取消筛选
            if (state.group === groupId) {
                state.group = 'all';
            } else {
                state.group = groupId || 'all';
            }
            // 不再重置其他筛选，支持组合筛选
            
            updateContainer();
            notifyCallbacks();
            
            // 筛选后统计
            const afterCards = document.querySelectorAll('.server-card').length;
            console.log('[UF] 分组筛选完成:', {
                group: state.group,
                region: state.region,
                status: state.status,
                筛选后卡片数: afterCards,
                卡片数变化: afterCards - beforeCards
            });
        },
        
        /**
         * 按状态筛选
         * @param {string} status - 状态值: ALL/ONLINE/OFFLINE
         */
        filterByStatus(status) {
            const beforeCards = document.querySelectorAll('.server-card').length;
            console.log('[UF] filterByStatus 被调用:', {
                currentStatus: state.status,
                newStatus: status,
                筛选前卡片数: beforeCards
            });
            
            // 点击相同按钮时取消筛选（恢复到ALL）
            if (state.status === status) {
                state.status = 'ALL';
            } else {
                state.status = status || 'ALL';
            }
            // 不再重置其他筛选，支持组合筛选
            
            updateContainer();
            notifyCallbacks();
            
            // 筛选后统计
            const afterCards = document.querySelectorAll('.server-card').length;
            console.log('[UF] 状态筛选完成:', {
                status: state.status,
                筛选后卡片数: afterCards,
                卡片数变化: afterCards - beforeCards
            });
        },
        
        /**
         * 按到期时间筛选
         * @param {string} days - 天数: ''/'3'/'7'/'30'
         */
        filterByExpiry(days) {
            // 点击相同按钮时取消筛选
            if (state.expiry === days) {
                state.expiry = '';
            } else {
                state.expiry = days || '';
            }
            // 不再重置其他筛选，支持组合筛选
            updateContainer();
            notifyCallbacks();
        },
        
        /**
         * 按地区筛选
         * @param {string} regionCode - 地区代码，空字符串或'ALL'表示全部
         */
        filterByRegion(regionCode) {
            const beforeCards = document.querySelectorAll('.server-card').length;
            console.log('[UF] filterByRegion 被调用:', {
                currentRegion: state.region,
                newRegion: regionCode,
                筛选前卡片数: beforeCards
            });
            
            // 处理'ALL'为空字符串，表示显示全部
            const normalizedCode = (regionCode === 'ALL' || !regionCode) ? '' : regionCode;
            
            // 点击相同按钮时取消筛选
            if (state.region === normalizedCode) {
                state.region = '';
            } else {
                state.region = normalizedCode;
            }
            // 不再重置其他筛选，支持组合筛选
            updateContainer();
            notifyCallbacks();
            
            // 筛选后统计
            const afterCards = document.querySelectorAll('.server-card').length;
            console.log('[UF] 地区筛选完成:', {
                region: state.region,
                筛选后卡片数: afterCards,
                卡片数变化: afterCards - beforeCards
            });
        },
        
        /**
         * 切换标签筛选
         * @param {string} tag - 标签名称
         */
        toggleTag(tag) {
            if (!tag) return;
            
            if (state.tags.has(tag)) {
                state.tags.delete(tag);
            } else {
                state.tags.add(tag);
            }
            
            updateTagFilters();
            notifyCallbacks();
        },
        
        /**
         * 注册状态变化回调
         * @param {Function} callback - 回调函数，接收当前状态对象
         * @returns {Function} 取消注册的函数
         */
        onChange(callback) {
            if (typeof callback !== 'function') return;
            
            callbacks.push(callback);
            
            // 返回取消注册函数
            return () => {
                const index = callbacks.indexOf(callback);
                if (index > -1) {
                    callbacks.splice(index, 1);
                }
            };
        },
        
        /**
         * 获取当前筛选状态
         * @returns {Object} 状态对象的深拷贝
         */
        getState,
        
        /**
         * 重置所有筛选
         */
        reset() {
            state.group = 'all';
            state.status = 'ALL';
            state.expiry = '';
            state.region = '';
            state.tags.clear();
            
            updateContainer();
            notifyCallbacks();
        },
        
        /**
         * 强制刷新所有筛选
         * 用于动态添加新卡片后的重新筛选
         */
        refresh() {
            updateContainer();
            notifyCallbacks();
        }
    };
    
    // 挂载到全局
    window.UF = UF;
})();