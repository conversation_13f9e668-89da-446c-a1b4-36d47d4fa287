/**
 * DStatus 动画系统
 * 统一管理所有动画相关功能，包括配置管理、速度控制和平滑过渡效果
 */

// 立即执行函数表达式(IIFE)，避免全局命名空间污染
(function() {
    'use strict';

    // 1. 常量定义区域
    // 配置存储键
    const ANIMATION_CONFIG_KEY = 'dstatus_animation_config';
    const ANIMATION_SPEED_KEY = 'dstatus_animation_speed';

    // 默认配置
    const DEFAULT_ANIMATION_CONFIG = {
        // 动画持续时间(毫秒)
        duration: 1500,

        // 下载速度更新间隔(毫秒)
        downloadUpdateInterval: 200,

        // 上传速度更新间隔(毫秒)
        uploadUpdateInterval: 300,

        // 平滑因子(0-1之间，越小过渡越平滑)
        smoothFactor: 0.3,

        // 配置版本
        version: '1.0'
    };

    // 预设配置
    const ANIMATION_PRESETS = {
        // 正常速度 - 使用CSS过渡效果
        normal: {
            duration: 800,              // 动画持续时间(毫秒) - 优化为更合适的时长
            downloadUpdateInterval: 400, // 下载速度更新间隔(毫秒) - 稍微减少
            uploadUpdateInterval: 400,   // 上传速度更新间隔(毫秒) - 稍微减少
            smoothFactor: 0.3,          // 平滑因子 - 调整为更平滑的过渡
            enabled: true,             // 启用动画
            useCssTransition: true     // 使用 CSS 过渡效果
        },
        // 迅速速度 - 使用CSS过渡效果，优化参数平衡性能和效果
        fast: {
            duration: 400,              // 动画持续时间(毫秒) - 优化为更快但不过分急躁
            downloadUpdateInterval: 80,  // 下载速度更新间隔(毫秒) - 稍微减少
            uploadUpdateInterval: 80,    // 上传速度更新间隔(毫秒) - 稍微减少
            smoothFactor: 0.15,         // 平滑因子 - 调整为更敏感的响应
            enabled: true,             // 启用动画
            useCssTransition: true     // 使用 CSS 过渡效果
        },
        // 关闭动画
        slow: {
            duration: 300,             // 保留小的动画持续时间，用于CSS过渡
            downloadUpdateInterval: 0,   // 下载速度更新间隔(毫秒)
            uploadUpdateInterval: 0,     // 上传速度更新间隔(毫秒)
            smoothFactor: 0,            // 平滑因子(0-1之间，越小过渡越平滑)
            enabled: false,            // 关闭 JavaScript 动画
            useCssTransition: true     // 使用 CSS 过渡效果
        }
    };

    // 存储所有正在进行的过渡动画
    const activeTransitions = new Map();

    // 2. 配置管理模块
    const ConfigManager = {
        /**
         * 获取当前配置
         * @returns {Object} 当前配置
         */
        getConfig() {
            try {
                const savedConfig = localStorage.getItem(ANIMATION_CONFIG_KEY);
                if (savedConfig) {
                    const config = JSON.parse(savedConfig);
                    // 合并默认配置和保存的配置，确保所有字段都存在
                    return { ...DEFAULT_ANIMATION_CONFIG, ...config };
                }
            } catch (error) {
                console.error('读取动画配置失败:', error);
            }

            // 如果没有保存的配置或读取失败，返回默认配置
            return { ...DEFAULT_ANIMATION_CONFIG };
        },

        /**
         * 保存配置
         * @param {Object} config 要保存的配置
         */
        saveConfig(config) {
            try {
                // 合并默认配置和新配置，确保所有字段都存在
                const newConfig = { ...DEFAULT_ANIMATION_CONFIG, ...config };
                localStorage.setItem(ANIMATION_CONFIG_KEY, JSON.stringify(newConfig));
                console.log('动画配置已保存');

                // 触发配置更新事件
                document.dispatchEvent(new CustomEvent('animation:config:updated', {
                    detail: { config: newConfig }
                }));

                return true;
            } catch (error) {
                console.error('保存动画配置失败:', error);
                return false;
            }
        },

        /**
         * 重置配置为默认值
         */
        resetConfig() {
            this.saveConfig(DEFAULT_ANIMATION_CONFIG);
            return DEFAULT_ANIMATION_CONFIG;
        }
    };

    // 3. 速度控制模块
    const SpeedControl = {
        /**
         * 获取当前配置
         * @returns {Object} 当前配置
         */
        getConfig() {
            try {
                const savedSpeed = localStorage.getItem(ANIMATION_SPEED_KEY);
                if (savedSpeed && ANIMATION_PRESETS[savedSpeed]) {
                    return ANIMATION_PRESETS[savedSpeed];
                }
            } catch (error) {
                console.error('读取动画速度设置失败:', error);
            }

            // 如果没有保存的配置或读取失败，返回正常速度配置
            return ANIMATION_PRESETS.normal;
        },

        /**
         * 设置动画速度
         * @param {string} speed - 速度预设名称 ('normal', 'fast', 'slow')
         */
        setSpeed(speed) {
            if (!ANIMATION_PRESETS[speed]) {
                console.error('无效的速度预设:', speed);
                return false;
            }

            try {
                localStorage.setItem(ANIMATION_SPEED_KEY, speed);
                console.log('动画速度已设置为:', speed);

                // 触发配置更新事件
                document.dispatchEvent(new CustomEvent('animation:speed:changed', {
                    detail: {
                        speed: speed,
                        config: ANIMATION_PRESETS[speed]
                    }
                }));

                return true;
            } catch (error) {
                console.error('保存动画速度设置失败:', error);
                return false;
            }
        },

        /**
         * 获取当前速度预设名称
         * @returns {string} 速度预设名称
         */
        getCurrentSpeed() {
            try {
                const savedSpeed = localStorage.getItem(ANIMATION_SPEED_KEY);
                if (savedSpeed && ANIMATION_PRESETS[savedSpeed]) {
                    return savedSpeed;
                }
            } catch (error) {
                console.error('读取动画速度设置失败:', error);
            }

            return 'normal';
        },

        /**
         * 初始化速度控制按钮
         */
        initSpeedButtons() {
            // 获取当前速度
            const currentSpeed = this.getCurrentSpeed();

            // 更新按钮状态
            this.updateButtonStates(currentSpeed);

            // 添加按钮点击事件
            const speedButtons = document.querySelectorAll('.animation-speed-btn');
            speedButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    // 获取速度设置
                    const buttonId = e.target.id;
                    let speed = 'normal';

                    if (buttonId.includes('fast')) {
                        speed = 'fast';
                    } else if (buttonId.includes('slow')) {
                        speed = 'slow';
                    }

                    // 设置速度
                    this.setSpeed(speed);

                    // 更新按钮状态
                    this.updateButtonStates(speed);

                    // 显示提示
                    if (typeof notice === 'function') {
                        const speedNames = {
                            normal: '正常',
                            fast: '迅速',
                            slow: '关闭'
                        };
                        notice(`动画速率已设置为: ${speedNames[speed]} (只影响前端动画效果)`, 'success');
                    }
                });
            });
        },

        /**
         * 更新按钮状态
         * @param {string} activeSpeed - 当前激活的速度
         */
        updateButtonStates(activeSpeed) {
            // 移除所有按钮的激活状态
            document.querySelectorAll('.animation-speed-btn').forEach(button => {
                button.classList.remove('active');
                button.classList.remove('bg-blue-100', 'text-blue-700', 'dark:bg-blue-900/50', 'dark:text-blue-300');
                button.classList.add('bg-gray-100', 'text-gray-700', 'dark:bg-gray-800/50', 'dark:text-gray-300');
            });

            // 添加激活按钮的状态
            const normalButtons = document.querySelectorAll('#animation-speed-normal, #animation-speed-normal-mobile');
            const fastButtons = document.querySelectorAll('#animation-speed-fast, #animation-speed-fast-mobile');
            const slowButtons = document.querySelectorAll('#animation-speed-slow, #animation-speed-slow-mobile');

            let activeButtons;

            switch (activeSpeed) {
                case 'fast':
                    activeButtons = fastButtons;
                    break;
                case 'slow':
                    activeButtons = slowButtons;
                    break;
                default:
                    activeButtons = normalButtons;
            }

            activeButtons.forEach(button => {
                button.classList.add('active');
                button.classList.remove('bg-gray-100', 'text-gray-700', 'dark:bg-gray-800/50', 'dark:text-gray-300');
                button.classList.add('bg-blue-100', 'text-blue-700', 'dark:bg-blue-900/50', 'dark:text-blue-300');
            });
        }
    };

    // 4. 平滑过渡模块
    const Transition = {
        /**
         * Speedtest风格的数字变动效果
         * @param {string} id - 元素ID
         * @param {number} startValue - 起始值(bps)
         * @param {number} endValue - 目标值(bps)
         * @param {number} duration - 持续时间(毫秒)
         * @param {Function} formatCallback - 可选的格式化回调函数
         * @param {Function} completeCallback - 可选的完成回调函数
         */
        speedtestStyleTransition(id, startValue, endValue, duration = 1000, formatCallback = null, completeCallback = null) {
            const element = document.getElementById(id);
            if (!element) return;

            // 字体样式已通过CSS类metric-number统一管理
            // 不再需要内联样式设置

            // 取消之前的动画
            if (activeTransitions.has(id)) {
                cancelAnimationFrame(activeTransitions.get(id));
            }

            const startTime = performance.now();
            const valueChange = endValue - startValue;
            let lastUpdateTime = 0;
            let lastDisplayedValue = null;

            // 获取动画配置
            const animConfig = SpeedControl.getConfig();
            
            // 获取自适应配置（如果可用）
            let adaptiveConfig = null;
            if (typeof window !== 'undefined' && window.MetricFormatter && window.MetricFormatter.getAdaptiveAnimationConfig) {
                adaptiveConfig = window.MetricFormatter.getAdaptiveAnimationConfig(startValue, endValue);
            }

            // 如果动画已禁用，直接设置为结束值
            if (animConfig.enabled === false) {
                const formattedText = typeof formatCallback === 'function'
                    ? formatCallback(endValue)
                    : this.formatBandwidth(endValue);
                
                // 检查是否是简化版元素（只包含数值）
                if (element.id && element.id.endsWith('-value')) {
                    element.textContent = formattedText;
                } else {
                    element.innerHTML = formattedText;
                }
                
                // 调用完成回调
                if (typeof completeCallback === 'function') {
                    completeCallback();
                }
                return;
            }

            // 缓动函数 - 根据流量规模调整缓动效果
            function getEasingFunction() {
                if (adaptiveConfig) {
                    // 大流量环境使用更简单的缓动
                    if (adaptiveConfig.scale === 'large' || adaptiveConfig.scale === 'enterprise') {
                        return function(t) { return t; }; // 线性
                    }
                }
                // 小流量环境使用平滑缓动
                return function easeInOutQuad(t) {
                    return t < 0.5 ? 1.5 * t * t : 1 - 1.5 * (1 - t) * (1 - t);
                };
            }
            
            const easingFunction = getEasingFunction();

            // 动画函数
            const animate = (currentTime) => {
                // 计算动画进度(0-1)
                let elapsed = currentTime - startTime;
                let progress = Math.min(elapsed / duration, 1);

                // 应用缓动函数 - 自适应缓动
                progress = easingFunction(progress);

                // 计算当前值
                const currentValue = startValue + valueChange * progress;

                // 确定更新间隔 - 根据自适应配置和页面可见性智能调整
                const baseUpdateInterval = adaptiveConfig ? (1000 / (adaptiveConfig.updateInterval || 60)) : (1000 / 60);
                const isVisible = !document.hidden;
                const updateInterval = isVisible ? baseUpdateInterval : 1000 / 10;

                // 检查是否到了更新间隔
                const shouldUpdate = (currentTime - lastUpdateTime >= updateInterval) || progress === 1;

                if (shouldUpdate) {
                    lastUpdateTime = currentTime;

                    // 格式化并更新显示 - 使用自适应配置
                    const formatOptions = adaptiveConfig ? { scale: adaptiveConfig.scale } : {};
                    const formattedText = typeof formatCallback === 'function'
                        ? formatCallback(currentValue)
                        : this.formatBandwidth(currentValue, formatOptions);

                    // 只有当格式化后的文本与上次不同时才更新DOM
                    if (formattedText !== lastDisplayedValue) {
                        // 检查是否是简化版元素（只包含数值）
                        if (element.id && element.id.endsWith('-value')) {
                            // 简化版：只更新文本内容
                            element.textContent = formattedText;
                        } else {
                            // 传统版：使用innerHTML（包含HTML标签）
                            element.innerHTML = formattedText;
                        }
                        lastDisplayedValue = formattedText;
                    }
                }

                // 如果动画未完成，继续下一帧
                if (progress < 1) {
                    const requestId = requestAnimationFrame(animate);
                    activeTransitions.set(id, requestId);
                } else {
                    // 动画完成，从活动过渡中移除
                    activeTransitions.delete(id);
                    // 调用完成回调
                    if (typeof completeCallback === 'function') {
                        completeCallback();
                    }
                }
            };

            // 开始动画
            const requestId = requestAnimationFrame(animate);
            activeTransitions.set(id, requestId);
        },

        /**
         * 格式化带宽值 - 使用统一 MetricFormatter 支持大流量环境
         * @param {number} value - 带宽值(bps)
         * @param {Object} options - 选项 {scale: 'auto'|'small'|'medium'|'large'|'enterprise'}
         * @returns {string} 格式化后的带宽值
         */
        formatBandwidth(value, options = {}) {
            // 使用统一的 MetricFormatter，如果可用
            if (typeof window !== 'undefined' && window.MetricFormatter) {
                const result = window.MetricFormatter.formatSpeed(value, { scale: 'auto', ...options });
                return `${result.value} <span class="metric-unit">${result.unit}</span>`;
            }
            
            // 备用方案：支持更大的单位
            const k = 1024;
            const units = ['bps', 'Kbps', 'Mbps', 'Gbps', 'Tbps', 'Pbps', 'Ebps'];
            let scaledValue = value;
            let unitIndex = 0;
            
            while (scaledValue >= k && unitIndex < units.length - 1) {
                scaledValue /= k;
                unitIndex++;
            }

            // 智能精度控制
            let precision = 2;
            if (unitIndex >= 5) precision = 3;      // PB/EB 级别保留 3 位小数
            else if (scaledValue >= 100) precision = 1;
            else if (scaledValue >= 1000) precision = 0;

            return `${scaledValue.toFixed(precision)} <span class="metric-unit">${units[unitIndex]}</span>`;
        },

        /**
         * 平滑过渡数值 - 支持自适应配置
         * @param {string} id - 元素ID
         * @param {number} startValue - 起始值
         * @param {number} endValue - 目标值
         * @param {number} duration - 持续时间(毫秒)
         * @param {Function} updateCallback - 更新回调函数
         * @param {Function} formatCallback - 格式化回调函数
         * @param {number} updateInterval - 更新间隔(毫秒)
         */
        value(id, startValue, endValue, duration = 1500, updateCallback = null, formatCallback = null, updateInterval = 1000) {
            // 获取动画配置和自适应配置
            const animConfig = SpeedControl.getConfig();
            let adaptiveConfig = null;
            
            // 尝试获取自适应配置
            if (typeof window !== 'undefined' && window.MetricFormatter && window.MetricFormatter.getAdaptiveAnimationConfig) {
                adaptiveConfig = window.MetricFormatter.getAdaptiveAnimationConfig(startValue, endValue);
                // 如果有自适应配置，覆盖默认参数
                duration = adaptiveConfig.duration;
                updateInterval = adaptiveConfig.updateInterval;
            }

            // 获取元素
            let element = document.getElementById(id);
            if (!element) return;

            // 检查是否是进度条元素
            const isProgressBar = element.classList.contains('progress-bar');

            // 如果是进度条元素且配置为使用CSS过渡
            if (isProgressBar && animConfig.useCssTransition) {
                // 移除所有动画模式类
                element.classList.remove('progress-bar-no-animation', 'progress-bar-normal', 'progress-bar-fast');

                // 根据不同模式添加相应的CSS类
                if (animConfig.enabled === false) {
                    // 关闭模式
                    element.classList.add('progress-bar-no-animation');
                } else if (animConfig.duration <= 300) {
                    // 迅速模式
                    element.classList.add('progress-bar-fast');
                } else {
                    // 正常模式
                    element.classList.add('progress-bar-normal');
                }

                // 如果是进度条且动画已禁用，直接设置为结束值
                if (animConfig.enabled === false) {
                    if (typeof updateCallback === 'function') {
                        updateCallback(endValue);
                    }
                    return;
                }
            }

            // 如果是数字显示元素，始终使用JavaScript动画
            // 或者如果不是进度条元素且动画已禁用，直接设置为结束值
            if (!isProgressBar && animConfig.enabled === false) {
                if (typeof updateCallback === 'function') {
                    updateCallback(endValue);
                } else if (typeof formatCallback === 'function') {
                    element.innerHTML = formatCallback(endValue);
                } else {
                    element.innerHTML = Math.round(endValue);
                }
                return;
            }
            // 如果已经有正在进行的过渡，取消它
            if (activeTransitions.has(id)) {
                cancelAnimationFrame(activeTransitions.get(id));
            }

            // 重新获取元素，确保它仍然存在
            element = document.getElementById(id);
            if (!element) return;

            const startTime = performance.now();
            const valueChange = endValue - startValue;
            let lastUpdateTime = 0;
            let lastDisplayedValue = null;

            // 使用easeOutQuad缓动函数
            function easeOutQuad(t) {
                return t * (2 - t);
            }

            // 格式化带宽值的特殊处理
            function formatBandwidthValue(value, formattedText) {
                // 如果是带宽值（包含bps, Kbps, Mbps等单位）
                if (formattedText.includes('bps')) {
                    // 提取数字部分
                    const numericPart = parseFloat(formattedText.replace(/[^0-9.]/g, ''));
                    // 提取单位部分
                    const unitPart = formattedText.replace(/[0-9.]/g, '');

                    // 如果是小数，保留一位小数
                    if (numericPart % 1 !== 0) {
                        return numericPart.toFixed(1) + unitPart;
                    }
                    // 如果是整数，直接返回
                    return Math.round(numericPart) + unitPart;
                }

                return formattedText;
            }

            // 动画帧函数
            function animate(currentTime) {
                const elapsedTime = currentTime - startTime;
                const progress = Math.min(elapsedTime / duration, 1);
                const easedProgress = easeOutQuad(progress);
                const currentValue = startValue + valueChange * easedProgress;

                // 检查是否到了更新间隔
                const shouldUpdate = (currentTime - lastUpdateTime >= updateInterval) || progress === 1;

                if (shouldUpdate) {
                    lastUpdateTime = currentTime;

                    // 如果提供了更新回调，使用它
                    if (typeof updateCallback === 'function') {
                        updateCallback(currentValue);
                    } else {
                        // 否则更新元素文本
                        if (typeof formatCallback === 'function') {
                            const formattedText = formatCallback(currentValue);

                            // 如果格式化后的文本与上次不同，才更新
                            if (formattedText !== lastDisplayedValue) {
                                // 对带宽值进行特殊处理
                                element.innerHTML = formatBandwidthValue(currentValue, formattedText);
                                lastDisplayedValue = formattedText;
                            }
                        } else {
                            // 四舍五入到整数，或者保留一位小数
                            const roundedValue = Math.round(currentValue);
                            if (roundedValue !== lastDisplayedValue) {
                                element.innerHTML = roundedValue;
                                lastDisplayedValue = roundedValue;
                            }
                        }
                    }
                }

                // 如果动画未完成，继续下一帧
                if (progress < 1) {
                    const requestId = requestAnimationFrame(animate);
                    activeTransitions.set(id, requestId);
                } else {
                    // 动画完成，从活动过渡中移除
                    activeTransitions.delete(id);
                }
            }

            // 开始动画
            const requestId = requestAnimationFrame(animate);
            activeTransitions.set(id, requestId);
        },

        /**
         * 平滑过渡进度条
         * @param {string} id - 进度条元素ID
         * @param {number} startPercent - 起始百分比
         * @param {number} endPercent - 目标百分比
         * @param {number} duration - 持续时间(毫秒)
         */
        progressBar(id, startPercent, endPercent, duration = 1500) {
            const progressBar = document.getElementById(id);
            if (!progressBar) return;

            // 获取动画配置
            const animConfig = SpeedControl.getConfig();

            // 移除所有动画模式类
            progressBar.classList.remove('progress-bar-no-animation', 'progress-bar-normal', 'progress-bar-fast');

            // 确保进度条有过渡效果的类
            if (!progressBar.classList.contains('transition-width')) {
                progressBar.classList.add('transition-width');
            }

            // 检查是否使用CSS过渡
            if (animConfig.useCssTransition) {
                // 根据不同模式添加相应的CSS类
                if (animConfig.enabled === false) {
                    // 关闭模式
                    progressBar.classList.add('progress-bar-no-animation');
                } else if (animConfig.duration <= 300) {
                    // 迅速模式
                    progressBar.classList.add('progress-bar-fast');
                } else {
                    // 正常模式
                    progressBar.classList.add('progress-bar-normal');
                }
            } else {
                // 如果不使用CSS过渡，则移除过渡效果
                progressBar.style.transition = 'none';
            }

            // 如果动画已禁用，直接设置最终值
            if (animConfig.enabled === false) {

                // 设置宽度
                progressBar.style.width = `${endPercent}%`;

                // 添加高亮效果，即使动画已禁用
                if (Math.abs(endPercent - startPercent) > 1) {
                    progressBar.classList.remove('progress-highlight');
                    void progressBar.offsetWidth; // 触发回流
                    progressBar.classList.add('progress-highlight');
                }
                return;
            }

            this.value(
                id,
                startPercent,
                endPercent,
                duration,
                (currentValue) => {
                    progressBar.style.width = `${currentValue}%`;
                }
            );

            // 添加高亮效果
            if (Math.abs(endPercent - startPercent) > 1) {
                progressBar.classList.remove('progress-highlight');
                void progressBar.offsetWidth; // 触发回流
                progressBar.classList.add('progress-highlight');
            }
        },

        /**
         * 平滑过渡卡片和列表视图
         * @param {string} fromView - 起始视图类型 ('card' 或 'list')
         * @param {string} toView - 目标视图类型 ('card' 或 'list')
         * @param {number} duration - 持续时间(毫秒)
         */
        viewTransition(fromView, toView, duration = 300) {
            // 获取所有卡片元素
            const elements = document.querySelectorAll('.server-card');
            if (!elements.length) return;

            // 对每个元素应用过渡效果
            elements.forEach(element => {
                // 添加过渡样式
                element.style.transition = `all ${duration}ms ease`;

                // 根据目标视图类型应用不同的效果
                if (toView === 'card') {
                    // 列表到卡片的过渡
                    element.style.opacity = '0';
                    element.style.transform = 'scale(0.95)';

                    // 延迟显示
                    setTimeout(() => {
                        element.style.opacity = '1';
                        element.style.transform = 'scale(1)';
                    }, 50);
                } else {
                    // 卡片到列表的过渡
                    element.style.opacity = '0';
                    element.style.transform = 'translateY(10px)';

                    // 延迟显示
                    setTimeout(() => {
                        element.style.opacity = '1';
                        element.style.transform = 'translateY(0)';
                    }, 50);
                }

                // 过渡结束后清除样式
                setTimeout(() => {
                    element.style.transition = '';
                    element.style.transform = '';
                }, duration + 100);
            });

            // 对容器应用过渡效果
            const container = document.getElementById('list-grid-container');
            if (container) {
                container.style.transition = `opacity ${duration}ms ease`;
                container.style.opacity = '0';

                // 延迟显示
                setTimeout(() => {
                    container.style.opacity = '1';
                }, 50);

                // 过渡结束后清除样式
                setTimeout(() => {
                    container.style.transition = '';
                }, duration + 100);
            }
        }
    };

    // 5. 配置面板模块
    const ConfigPanel = {
        /**
         * 加载配置到面板
         */
        loadConfig() {
            const configPanel = document.getElementById('animation-config-panel');
            if (!configPanel) return;

            // 获取配置面板中的输入元素
            const durationInput = document.getElementById('animation-duration');
            const durationValue = document.getElementById('animation-duration-value');
            const downloadIntervalInput = document.getElementById('download-update-interval');
            const downloadIntervalValue = document.getElementById('download-update-interval-value');
            const uploadIntervalInput = document.getElementById('upload-update-interval');
            const uploadIntervalValue = document.getElementById('upload-update-interval-value');
            const smoothFactorInput = document.getElementById('smooth-factor');
            const smoothFactorValue = document.getElementById('smooth-factor-value');

            if (!durationInput || !downloadIntervalInput || !uploadIntervalInput || !smoothFactorInput) return;

            const config = ConfigManager.getConfig();

            // 设置输入值
            durationInput.value = config.duration;
            durationValue.textContent = config.duration;

            downloadIntervalInput.value = config.downloadUpdateInterval;
            downloadIntervalValue.textContent = config.downloadUpdateInterval;

            uploadIntervalInput.value = config.uploadUpdateInterval;
            uploadIntervalValue.textContent = config.uploadUpdateInterval;

            smoothFactorInput.value = config.smoothFactor;
            smoothFactorValue.textContent = config.smoothFactor;
        },

        /**
         * 保存配置
         */
        saveConfig() {
            const configPanel = document.getElementById('animation-config-panel');
            if (!configPanel) return;

            // 获取配置面板中的输入元素
            const durationInput = document.getElementById('animation-duration');
            const downloadIntervalInput = document.getElementById('download-update-interval');
            const uploadIntervalInput = document.getElementById('upload-update-interval');
            const smoothFactorInput = document.getElementById('smooth-factor');

            if (!durationInput || !downloadIntervalInput || !uploadIntervalInput || !smoothFactorInput) return;

            const config = {
                duration: parseInt(durationInput.value),
                downloadUpdateInterval: parseInt(downloadIntervalInput.value),
                uploadUpdateInterval: parseInt(uploadIntervalInput.value),
                smoothFactor: parseFloat(smoothFactorInput.value)
            };

            ConfigManager.saveConfig(config);
            this.hidePanel();

            // 显示保存成功提示
            if (typeof notice === 'function') {
                notice('动画配置已保存', 'success');
            }
        },

        /**
         * 重置配置
         */
        resetConfig() {
            const configPanel = document.getElementById('animation-config-panel');
            if (!configPanel) return;

            // 获取配置面板中的输入元素
            const durationInput = document.getElementById('animation-duration');
            const durationValue = document.getElementById('animation-duration-value');
            const downloadIntervalInput = document.getElementById('download-update-interval');
            const downloadIntervalValue = document.getElementById('download-update-interval-value');
            const uploadIntervalInput = document.getElementById('upload-update-interval');
            const uploadIntervalValue = document.getElementById('upload-update-interval-value');
            const smoothFactorInput = document.getElementById('smooth-factor');
            const smoothFactorValue = document.getElementById('smooth-factor-value');

            if (!durationInput || !downloadIntervalInput || !uploadIntervalInput || !smoothFactorInput) return;

            const config = ConfigManager.resetConfig();

            // 更新输入值
            durationInput.value = config.duration;
            durationValue.textContent = config.duration;

            downloadIntervalInput.value = config.downloadUpdateInterval;
            downloadIntervalValue.textContent = config.downloadUpdateInterval;

            uploadIntervalInput.value = config.uploadUpdateInterval;
            uploadIntervalValue.textContent = config.uploadUpdateInterval;

            smoothFactorInput.value = config.smoothFactor;
            smoothFactorValue.textContent = config.smoothFactor;

            // 显示重置成功提示
            if (typeof notice === 'function') {
                notice('动画配置已重置为默认值', 'info');
            }
        },

        /**
         * 显示面板
         */
        showPanel() {
            const configPanel = document.getElementById('animation-config-panel');
            if (!configPanel) return;

            this.loadConfig();
            configPanel.classList.remove('hidden');
        },

        /**
         * 隐藏面板
         */
        hidePanel() {
            const configPanel = document.getElementById('animation-config-panel');
            if (!configPanel) return;

            configPanel.classList.add('hidden');
        },

        /**
         * 初始化面板事件
         */
        init() {
            const configPanel = document.getElementById('animation-config-panel');
            if (!configPanel) return;

            // 获取配置面板中的输入元素
            const durationInput = document.getElementById('animation-duration');
            const durationValue = document.getElementById('animation-duration-value');
            const downloadIntervalInput = document.getElementById('download-update-interval');
            const downloadIntervalValue = document.getElementById('download-update-interval-value');
            const uploadIntervalInput = document.getElementById('upload-update-interval');
            const uploadIntervalValue = document.getElementById('upload-update-interval-value');
            const smoothFactorInput = document.getElementById('smooth-factor');
            const smoothFactorValue = document.getElementById('smooth-factor-value');

            // 获取按钮元素
            const saveButton = document.getElementById('save-animation-config');
            const resetButton = document.getElementById('reset-animation-config');
            const closeButton = document.getElementById('close-animation-config');

            if (!durationInput || !downloadIntervalInput || !uploadIntervalInput || !smoothFactorInput ||
                !saveButton || !resetButton || !closeButton) return;

            // 添加事件监听器

            // 输入值变化时更新显示
            durationInput.addEventListener('input', () => {
                durationValue.textContent = durationInput.value;
            });

            downloadIntervalInput.addEventListener('input', () => {
                downloadIntervalValue.textContent = downloadIntervalInput.value;
            });

            uploadIntervalInput.addEventListener('input', () => {
                uploadIntervalValue.textContent = uploadIntervalInput.value;
            });

            smoothFactorInput.addEventListener('input', () => {
                smoothFactorValue.textContent = smoothFactorInput.value;
            });

            // 按钮点击事件
            saveButton.addEventListener('click', () => this.saveConfig());
            resetButton.addEventListener('click', () => this.resetConfig());
            closeButton.addEventListener('click', () => this.hidePanel());

            // 添加全局事件监听器，用于打开配置面板
            document.addEventListener('animation:config:open', () => this.showPanel());

            // 添加按钮点击事件
            const openConfigBtn = document.getElementById('open-animation-config');
            if (openConfigBtn) {
                openConfigBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.showPanel();
                });
            }

            const openConfigMobileBtn = document.getElementById('open-animation-config-mobile');
            if (openConfigMobileBtn) {
                openConfigMobileBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.showPanel();
                });
            }

            console.log('[DStatusAnimation] 配置面板初始化完成');
        }
    };

    // 6. 初始化函数
    function init() {
        console.log('[DStatusAnimation] 初始化动画系统');

        // 初始化速度控制按钮
        SpeedControl.initSpeedButtons();

        // 初始化配置面板
        ConfigPanel.init();

        // 监听动画速度变化事件
        document.addEventListener('animation:speed:changed', (e) => {
            if (e.detail && e.detail.config) {
                console.log('[DStatusAnimation] 动画速度已变化:', e.detail.speed);
            }
        });

        // 监听配置更新事件
        document.addEventListener('animation:config:updated', (e) => {
            if (e.detail && e.detail.config) {
                console.log('[DStatusAnimation] 动画配置已更新');
            }
        });
    }

    // 7. 导出到全局作用域
    window.DStatusAnimation = {
        // 配置管理
        getConfig: ConfigManager.getConfig,
        saveConfig: ConfigManager.saveConfig,
        resetConfig: ConfigManager.resetConfig,

        // 速度控制
        getSpeedConfig: SpeedControl.getConfig,
        setSpeed: SpeedControl.setSpeed,
        getCurrentSpeed: SpeedControl.getCurrentSpeed,

        // 平滑过渡
        smoothTransition: Transition.value,
        speedtestStyleTransition: Transition.speedtestStyleTransition,
        smoothProgressBar: Transition.progressBar,
        viewTransition: Transition.viewTransition,

        // 配置面板
        showConfigPanel: ConfigPanel.showPanel.bind(ConfigPanel),
        hideConfigPanel: ConfigPanel.hidePanel.bind(ConfigPanel)
    };

    // 兼容原有的全局API
    window.AnimationConfig = ConfigManager;
    window.AnimationSpeedControl = SpeedControl;
    window.SmoothTransition = {
        value: Transition.value,
        speedtestStyleTransition: Transition.speedtestStyleTransition,
        formatBandwidth: Transition.formatBandwidth,
        progressBar: Transition.progressBar,
        viewTransition: Transition.viewTransition
    };
    window.AnimationConfigPanel = {
        show: ConfigPanel.showPanel.bind(ConfigPanel),
        hide: ConfigPanel.hidePanel.bind(ConfigPanel)
    };

    // 8. 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', init);
})();
