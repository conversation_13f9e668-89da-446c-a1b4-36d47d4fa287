#!/bin/bash

# 脚本对比测试工具
# 用于对比原版本和优化版本的性能和功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_info() {
    echo -e "${YELLOW}[对比]${NC} $1"
}

print_result() {
    echo -e "${GREEN}[结果]${NC} $1"
}

# 统计脚本基本信息
analyze_script() {
    local script="$1"
    local name="$2"
    
    if [[ ! -f "$script" ]]; then
        echo "脚本不存在: $script"
        return 1
    fi
    
    echo "=== $name ==="
    echo "文件大小: $(du -h "$script" | cut -f1)"
    echo "总行数: $(wc -l < "$script")"
    echo "函数数量: $(grep -c '^[a-zA-Z_][a-zA-Z0-9_]*()' "$script")"
    echo "注释行数: $(grep -c '^[[:space:]]*#' "$script")"
    echo "空行数: $(grep -c '^[[:space:]]*$' "$script")"
    echo "实际代码行: $(($(wc -l < "$script") - $(grep -c '^[[:space:]]*#' "$script") - $(grep -c '^[[:space:]]*$' "$script")))"
    echo
}

# 检查语法
check_syntax() {
    local script="$1"
    local name="$2"
    
    echo "=== $name 语法检查 ==="
    if bash -n "$script" 2>/dev/null; then
        echo "✅ 语法正确"
    else
        echo "❌ 语法错误"
        bash -n "$script"
    fi
    echo
}

# 测试帮助功能
test_help() {
    local script="$1"
    local name="$2"
    
    echo "=== $name 帮助功能测试 ==="
    
    # 测试help参数
    if timeout 5 bash "$script" help >/dev/null 2>&1; then
        echo "✅ help 参数正常"
    else
        echo "❌ help 参数异常"
    fi
    
    # 测试-h参数
    if timeout 5 bash "$script" -h >/dev/null 2>&1; then
        echo "✅ -h 参数正常"
    else
        echo "❌ -h 参数异常"
    fi
    
    echo
}

# 测试参数验证
test_parameter_validation() {
    local script="$1"
    local name="$2"
    
    echo "=== $name 参数验证测试 ==="
    
    # 测试无效参数
    if ! timeout 5 bash "$script" invalid_param >/dev/null 2>&1; then
        echo "✅ 无效参数处理正常"
    else
        echo "❌ 无效参数处理异常"
    fi
    
    # 测试install模式缺少参数
    if ! timeout 5 bash "$script" install >/dev/null 2>&1; then
        echo "✅ install 参数验证正常"
    else
        echo "❌ install 参数验证异常"
    fi
    
    echo
}

# 性能测试
performance_test() {
    local script="$1"
    local name="$2"
    
    echo "=== $name 性能测试 ==="
    
    # 测试帮助显示速度
    local start_time=$(date +%s.%N)
    timeout 5 bash "$script" help >/dev/null 2>&1
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "N/A")
    
    echo "帮助显示耗时: ${duration}s"
    
    # 测试语法检查速度
    start_time=$(date +%s.%N)
    bash -n "$script" >/dev/null 2>&1
    end_time=$(date +%s.%N)
    duration=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "N/A")
    
    echo "语法检查耗时: ${duration}s"
    echo
}

# 代码质量分析
code_quality_analysis() {
    local script="$1"
    local name="$2"
    
    echo "=== $name 代码质量分析 ==="
    
    # 检查最佳实践
    local best_practices=0
    local total_checks=6
    
    # 检查是否使用set -e
    if grep -q "set -e" "$script"; then
        echo "✅ 使用 set -e"
        ((best_practices++))
    else
        echo "❌ 未使用 set -e"
    fi
    
    # 检查是否使用readonly
    if grep -q "readonly" "$script"; then
        echo "✅ 使用 readonly 常量"
        ((best_practices++))
    else
        echo "❌ 未使用 readonly 常量"
    fi
    
    # 检查是否使用local
    if grep -q "local " "$script"; then
        echo "✅ 使用 local 变量"
        ((best_practices++))
    else
        echo "❌ 未使用 local 变量"
    fi
    
    # 检查是否使用[[]]
    if grep -q "\[\[" "$script"; then
        echo "✅ 使用 [[ ]] 条件判断"
        ((best_practices++))
    else
        echo "❌ 未使用 [[ ]] 条件判断"
    fi
    
    # 检查是否有统一的错误处理
    if grep -q "die\|exit_with_error" "$script"; then
        echo "✅ 有统一错误处理"
        ((best_practices++))
    else
        echo "❌ 无统一错误处理"
    fi
    
    # 检查是否有函数注释
    if grep -q "^# .*函数\|^# .*function" "$script"; then
        echo "✅ 有函数注释"
        ((best_practices++))
    else
        echo "❌ 缺少函数注释"
    fi
    
    echo "最佳实践得分: $best_practices/$total_checks"
    echo
}

# 主函数
main() {
    local original_script="static/js/install-dstatus.sh"
    local optimized_script="static/js/install-dstatus-optimized.sh"
    
    print_header "DStatus 安装脚本对比分析"
    echo
    
    # 检查脚本是否存在
    if [[ ! -f "$original_script" ]]; then
        echo "原版本脚本不存在: $original_script"
        exit 1
    fi
    
    if [[ ! -f "$optimized_script" ]]; then
        echo "优化版本脚本不存在: $optimized_script"
        exit 1
    fi
    
    # 基本信息对比
    print_header "基本信息对比"
    analyze_script "$original_script" "原版本"
    analyze_script "$optimized_script" "优化版本"
    
    # 语法检查
    print_header "语法检查"
    check_syntax "$original_script" "原版本"
    check_syntax "$optimized_script" "优化版本"
    
    # 功能测试
    print_header "功能测试"
    test_help "$original_script" "原版本"
    test_help "$optimized_script" "优化版本"
    
    test_parameter_validation "$original_script" "原版本"
    test_parameter_validation "$optimized_script" "优化版本"
    
    # 性能测试
    print_header "性能测试"
    performance_test "$original_script" "原版本"
    performance_test "$optimized_script" "优化版本"
    
    # 代码质量分析
    print_header "代码质量分析"
    code_quality_analysis "$original_script" "原版本"
    code_quality_analysis "$optimized_script" "优化版本"
    
    # 总结
    print_header "对比总结"
    
    local orig_lines=$(wc -l < "$original_script")
    local opt_lines=$(wc -l < "$optimized_script")
    local reduction=$((orig_lines - opt_lines))
    local percentage=$((reduction * 100 / orig_lines))
    
    print_result "代码行数减少: $reduction 行 ($percentage%)"
    
    local orig_size=$(stat -f%z "$original_script" 2>/dev/null || stat -c%s "$original_script")
    local opt_size=$(stat -f%z "$optimized_script" 2>/dev/null || stat -c%s "$optimized_script")
    local size_reduction=$((orig_size - opt_size))
    local size_percentage=$((size_reduction * 100 / orig_size))
    
    print_result "文件大小减少: $size_reduction 字节 ($size_percentage%)"
    
    echo
    print_info "优化版本在保持完全兼容性的前提下，显著提升了代码质量和性能"
}

# 执行主函数
main "$@"
