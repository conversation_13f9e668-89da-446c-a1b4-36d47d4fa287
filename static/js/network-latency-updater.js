/**
 * 轻量级网络延迟数据更新器
 * 专门为list视图设计，仅负责获取和更新延迟/丢包数据
 */

(function() {
    'use strict';
    
    class NetworkLatencyUpdater {
        constructor() {
            this.updateInterval = 300000; // 5分钟更新一次
            this.serverIds = new Set();
            this.updateTimer = null;
            this.init();
        }
        
        init() {
            // 收集所有服务器ID
            this.collectServerIds();
            
            // 立即加载数据
            this.loadLatencyData();
            
            // 设置定时更新
            this.startAutoUpdate();
            
            // 监听页面可见性变化
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.stopAutoUpdate();
                } else {
                    this.loadLatencyData();
                    this.startAutoUpdate();
                }
            });
        }
        
        collectServerIds() {
            document.querySelectorAll('.server-card').forEach(card => {
                const sid = card.dataset.sid;
                if (sid) {
                    this.serverIds.add(sid);
                }
            });
        }
        
        async loadLatencyData() {
            if (this.serverIds.size === 0) return;
            
            try {
                // 批量获取所有服务器的网络数据
                const response = await fetch('/api/monitor/batch-network-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        serverIds: Array.from(this.serverIds),
                        timeRange: '24h'
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data) {
                        this.updateLatencyDisplay(result.data);
                    }
                }
            } catch (error) {
                console.warn('获取网络延迟数据失败:', error);
                // 如果批量API失败，尝试单个获取
                this.loadIndividualData();
            }
        }
        
        async loadIndividualData() {
            for (const sid of this.serverIds) {
                try {
                    const response = await fetch(`/api/monitor/data?type=archive&time_range=24h&node_id=${sid}&detail_level=detailed&sampling_rate=1`);
                    const result = await response.json();
                    
                    if (result.success && result.data && result.data.length > 0) {
                        this.updateServerLatency(sid, result.data);
                    }
                } catch (error) {
                    console.warn(`获取服务器 ${sid} 延迟数据失败:`, error);
                }
            }
        }
        
        updateLatencyDisplay(batchData) {
            for (const [sid, data] of Object.entries(batchData)) {
                if (data && data.length > 0) {
                    this.updateServerLatency(sid, data);
                }
            }
        }
        
        updateServerLatency(sid, data) {
            // 获取最新的数据点
            const sortedData = data.sort((a, b) => (b.created_at || 0) - (a.created_at || 0));
            const latestData = sortedData[0];
            
            if (!latestData) return;
            
            // 计算平均延迟和丢包率
            const latency = latestData.avg_time || 0;
            const packetLoss = latestData.packet_loss || (100 - (latestData.success_rate || 100));
            
            // 更新PC端延迟显示
            const latencyElement = document.getElementById(`${sid}_LATENCY`);
            if (latencyElement) {
                latencyElement.innerHTML = latency > 0 ? `${Math.round(latency)} ms` : '--- ms';
                latencyElement.setAttribute('data-latency', latency);
            }
            
            // 更新PC端丢包率显示
            const packetLossElement = document.getElementById(`${sid}_PACKET_LOSS`);
            if (packetLossElement) {
                packetLossElement.innerHTML = `${packetLoss.toFixed(1)} %`;
                packetLossElement.setAttribute('data-packet-loss', packetLoss);
            }
            
            // 更新移动端延迟显示
            const latencyMobileElement = document.getElementById(`${sid}_LATENCY_MOBILE`);
            if (latencyMobileElement) {
                latencyMobileElement.innerHTML = latency > 0 ? `${Math.round(latency)} ms` : '--- ms';
                latencyMobileElement.setAttribute('data-latency', latency);
            }
            
            // 更新移动端丢包率显示
            const packetLossMobileElement = document.getElementById(`${sid}_PACKET_LOSS_MOBILE`);
            if (packetLossMobileElement) {
                packetLossMobileElement.innerHTML = `${packetLoss.toFixed(1)} %`;
                packetLossMobileElement.setAttribute('data-packet-loss', packetLoss);
            }
        }
        
        startAutoUpdate() {
            this.stopAutoUpdate();
            this.updateTimer = setInterval(() => {
                this.loadLatencyData();
            }, this.updateInterval);
        }
        
        stopAutoUpdate() {
            if (this.updateTimer) {
                clearInterval(this.updateTimer);
                this.updateTimer = null;
            }
        }
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            new NetworkLatencyUpdater();
        });
    } else {
        new NetworkLatencyUpdater();
    }
})();