/**
 * @file tab-menu.js
 * @description Tab导航栏和下拉菜单功能
 * @updated 2024-05-30 性能和可维护性优化
 */

// 创建命名空间，避免全局污染
const TabMenuSystem = (() => {
    // 工具函数
    const Utils = {
        // DOM辅助函数
        dom: {
            // 获取元素，支持选择器或元素
            get(selector, parent = document) {
                return typeof selector === 'string' ? parent.querySelector(selector) : selector;
            },
            // 获取多个元素
            getAll(selector, parent = document) {
                return Array.from(parent.querySelectorAll(selector));
            }
        },
        // 事件辅助函数
        events: {
            // 自定义事件发布
            emit(name, detail = {}) {
                const event = new CustomEvent(name, { detail, bubbles: true });
                document.dispatchEvent(event);
                return event;
            },
            // 订阅事件
            on(name, handler) {
                document.addEventListener(name, handler);
                return () => document.removeEventListener(name, handler);
            }
        },
        // 通知函数
        notify(message, type = 'info', duration = 3000) {
            if (typeof notice === 'function') {
                notice(message);
                return;
            }

            const typeColors = {
                error: 'bg-red-500',
                success: 'bg-green-500',
                info: 'bg-blue-500',
                warning: 'bg-amber-500'
            };

            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${typeColors[type] || typeColors.info} text-white transition-opacity duration-300`;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.classList.add('opacity-0');
                setTimeout(() => toast.remove(), 300);
            }, duration);
        },
        // 本地存储
        storage: {
            // 保存数据到localStorage
            save(key, value) {
                try {
                    localStorage.setItem(key, JSON.stringify(value));
                    return true;
                } catch (e) {
                    console.error(`存储数据失败: ${key}`, e);
                    return false;
                }
            },
            // 从localStorage获取数据
            get(key, defaultValue = null) {
                try {
                    const data = localStorage.getItem(key);
                    return data ? JSON.parse(data) : defaultValue;
                } catch (e) {
                    console.error(`获取数据失败: ${key}`, e);
                    return defaultValue;
                }
            }
        },
        // 检测设备类型
        isMobileDevice() {
            return window.innerWidth < 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }
    };

    // 下拉菜单模块
    const DropdownManager = {
        // 注册的下拉菜单
        dropdowns: new Map(),

        // 当前打开的下拉菜单
        currentOpenDropdown: null,

        // 初始化下拉菜单
        initDropdown(btnId, menuId, options = {}) {
            const btn = document.getElementById(btnId);
            const menu = document.getElementById(menuId);

            if (!btn || !menu) {
                console.warn(`下拉菜单初始化失败: ${btnId} 或 ${menuId} 不存在`);
                return false;
            }

            // 注册下拉菜单
            this.dropdowns.set(btnId, {
                btn,
                menu,
                options
            });

            // 切换下拉菜单显示/隐藏 - 使用事件委托
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDropdown(btnId);
            });

            // 添加键盘支持
            this._addKeyboardSupport(btn, menu, btnId);

            // 防止点击下拉菜单内部时关闭菜单
            menu.addEventListener('click', (e) => {
                e.stopPropagation();
            });

            // 使用事件委托处理菜单项点击
            const itemSelector = options.itemSelector || '.dropdown-item';
            menu.addEventListener('click', (e) => {
                const item = e.target.closest(itemSelector);
                if (!item) return;

                if (options.onItemClick) {
                    options.onItemClick(item);
                }

                if (options.closeOnSelect !== false) {
                    this.closeDropdown(btnId);
                }
            });

            return true;
        },

        // 添加键盘支持
        _addKeyboardSupport(btn, menu, btnId) {
            btn.addEventListener('keydown', (e) => {
                // 按下回车或空格键展开菜单
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggleDropdown(btnId);
                }
            });

            menu.addEventListener('keydown', (e) => {
                const itemSelector = this.dropdowns.get(btnId).options.itemSelector || '.dropdown-item';
                const items = Utils.dom.getAll(itemSelector, menu);
                const currentIndex = items.findIndex(item => item === document.activeElement);

                if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                    e.preventDefault();

                    let newIndex = currentIndex;
                    if (e.key === 'ArrowDown') {
                        newIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
                    } else {
                        newIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
                    }

                    items[newIndex].focus();
                } else if (e.key === 'Escape') {
                    this.closeDropdown(btnId);
                    btn.focus();
                }
            });
        },

        // 切换下拉菜单
        toggleDropdown(btnId) {
            const dropdown = this.dropdowns.get(btnId);
            if (!dropdown) return;

            const isCurrentlyOpen = this.currentOpenDropdown === btnId;

            // 关闭所有打开的下拉菜单
            this.closeAll();

            // 如果点击的不是当前打开的菜单，则打开它
            if (!isCurrentlyOpen) {
                this.openDropdown(btnId);
            }
        },

        // 打开下拉菜单
        openDropdown(btnId) {
            const dropdown = this.dropdowns.get(btnId);
            if (!dropdown) return;

            const { btn, menu } = dropdown;

            // 记录当前打开的下拉菜单
            this.currentOpenDropdown = btnId;

            // 显示菜单
            menu.classList.remove('hidden');

            // 移动设备适配
            const isMobile = Utils.isMobileDevice();

            if (isMobile) {
                // 移动设备上使用全宽菜单
                menu.style.left = '0.5rem';
                menu.style.right = '0.5rem';
                menu.style.width = 'auto';
                menu.style.maxWidth = 'calc(100vw - 1rem)';
            } else {
                // 桌面版定位逻辑
                const rect = btn.getBoundingClientRect();
                const viewportWidth = window.innerWidth;
                const menuWidth = menu.offsetWidth || 240;

                // 计算左侧位置，确保不超出视口
                let leftPos = rect.left;
                if (leftPos + menuWidth > viewportWidth) {
                    leftPos = Math.max(8, viewportWidth - menuWidth - 8);
                }

                menu.style.width = 'auto';
                menu.style.maxWidth = '240px';
                menu.style.left = `${leftPos}px`;
                menu.style.right = 'auto';
            }

            // 设置顶部位置 - 确保从按钮下方弹出
            const rect = btn.getBoundingClientRect();
            menu.style.top = `${rect.bottom + 5}px`;

            // 添加高亮效果
            btn.classList.add('active-dropdown');

            // 设置aria属性
            btn.setAttribute('aria-expanded', 'true');
            menu.setAttribute('aria-hidden', 'false');

            // 触发打开事件
            Utils.events.emit('dropdown:opened', { id: btnId });
        },

        // 关闭下拉菜单
        closeDropdown(btnId) {
            const dropdown = this.dropdowns.get(btnId);
            if (!dropdown) return;

            const { btn, menu } = dropdown;

            // 隐藏菜单
            menu.classList.add('hidden');

            // 移除高亮效果
            btn.classList.remove('active-dropdown');

            // 更新aria属性
            btn.setAttribute('aria-expanded', 'false');
            menu.setAttribute('aria-hidden', 'true');

            // 清除当前打开的菜单记录（如果是当前菜单）
            if (this.currentOpenDropdown === btnId) {
                this.currentOpenDropdown = null;
            }

            // 触发关闭事件
            Utils.events.emit('dropdown:closed', { id: btnId });
        },

        // 关闭所有下拉菜单
        closeAll() {
            this.dropdowns.forEach((dropdown, btnId) => {
                this.closeDropdown(btnId);
            });
            this.currentOpenDropdown = null;
        },

        // 更新下拉菜单位置 - 在滚动时调用
        _updateDropdownPosition(btnId) {
            const dropdown = this.dropdowns.get(btnId);
            if (!dropdown) return;

            const { btn, menu } = dropdown;

            // 移动设备适配
            const isMobile = Utils.isMobileDevice();

            if (isMobile) {
                // 移动设备上使用全宽菜单
                menu.style.left = '0.5rem';
                menu.style.right = '0.5rem';
                menu.style.width = 'auto';
                menu.style.maxWidth = 'calc(100vw - 1rem)';
            } else {
                // 桌面版定位逻辑
                const rect = btn.getBoundingClientRect();
                const viewportWidth = window.innerWidth;
                const menuWidth = menu.offsetWidth || 240;

                // 计算左侧位置，确保不超出视口
                let leftPos = rect.left;
                if (leftPos + menuWidth > viewportWidth) {
                    leftPos = Math.max(8, viewportWidth - menuWidth - 8);
                }

                menu.style.width = 'auto';
                menu.style.maxWidth = '240px';
                menu.style.left = `${leftPos}px`;
                menu.style.right = 'auto';
            }

            // 设置顶部位置 - 确保从按钮下方弹出
            const rect = btn.getBoundingClientRect();
            menu.style.top = `${rect.bottom + 5}px`;
        },

        // 销毁所有下拉菜单
        destroy() {
            this.closeAll();
            this.dropdowns.clear();
        }
    };

    // 排序菜单模块
    const SortMenu = {
        // 当前排序配置
        currentConfig: null,

        // 存储键
        STORAGE_KEY: 'sortConfig',
        REALTIME_SORT_KEY: 'realtimeSortEnabled',

        init() {
            // 清除之前可能存在的事件监听器
            const sortDropdownMenu = document.getElementById('sort-dropdown-menu');
            if (sortDropdownMenu) {
                // 创建一个新的元素来替换旧元素，从而清除所有事件监听器
                const newMenu = sortDropdownMenu.cloneNode(true);
                sortDropdownMenu.parentNode.replaceChild(newMenu, sortDropdownMenu);
            }

            // 初始化排序下拉菜单 - 使用直接绑定事件而不是通过DropdownManager
            const sortDropdownBtn = document.getElementById('sort-dropdown-btn');
            const newSortDropdownMenu = document.getElementById('sort-dropdown-menu');

            if (sortDropdownBtn && newSortDropdownMenu) {
                // 切换下拉菜单显示/隐藏
                sortDropdownBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    
                    // 先关闭其他下拉菜单
                    const groupMenu = document.getElementById('group-dropdown-menu');
                    const settingsMenu = document.getElementById('dashboard-settings-dropdown-menu');
                    
                    if (groupMenu && !groupMenu.classList.contains('hidden')) {
                        groupMenu.classList.add('hidden');
                        const groupBtn = document.getElementById('group-dropdown-btn');
                        if (groupBtn) {
                            groupBtn.classList.remove('active-dropdown');
                            groupBtn.setAttribute('aria-expanded', 'false');
                        }
                    }
                    
                    if (settingsMenu && !settingsMenu.classList.contains('hidden')) {
                        settingsMenu.classList.add('hidden');
                        const settingsBtn = document.getElementById('settings-dropdown-btn');
                        if (settingsBtn) {
                            settingsBtn.classList.remove('active-dropdown');
                            settingsBtn.setAttribute('aria-expanded', 'false');
                        }
                    }
                    
                    // 切换排序菜单
                    if (newSortDropdownMenu.classList.contains('hidden')) {
                        // 显示菜单
                        newSortDropdownMenu.classList.remove('hidden');
                        sortDropdownBtn.classList.add('active-dropdown');
                        sortDropdownBtn.setAttribute('aria-expanded', 'true');
                        newSortDropdownMenu.setAttribute('aria-hidden', 'false');

                        // 设置位置
                        const rect = sortDropdownBtn.getBoundingClientRect();
                        newSortDropdownMenu.style.top = `${rect.bottom + 5}px`;
                        newSortDropdownMenu.style.left = `${rect.left}px`;
                    } else {
                        // 隐藏菜单
                        newSortDropdownMenu.classList.add('hidden');
                        sortDropdownBtn.classList.remove('active-dropdown');
                        sortDropdownBtn.setAttribute('aria-expanded', 'false');
                        newSortDropdownMenu.setAttribute('aria-hidden', 'true');
                    }
                });

                // 防止点击下拉菜单内部时关闭菜单
                newSortDropdownMenu.addEventListener('click', (e) => {
                    e.stopPropagation();
                });

                // 直接绑定排序选项点击事件
                const sortOptions = newSortDropdownMenu.querySelectorAll('.sort-option');
                sortOptions.forEach(option => {
                    // 移除可能存在的旧事件监听器
                    const newOption = option.cloneNode(true);
                    option.parentNode.replaceChild(newOption, option);

                    // 添加新的事件监听器
                    newOption.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this._handleSortOptionClick(newOption);
                    });
                });

                // 点击页面其他区域关闭下拉菜单
                document.addEventListener('click', () => {
                    if (!newSortDropdownMenu.classList.contains('hidden')) {
                        newSortDropdownMenu.classList.add('hidden');
                        sortDropdownBtn.classList.remove('active-dropdown');
                        sortDropdownBtn.setAttribute('aria-expanded', 'false');
                        newSortDropdownMenu.setAttribute('aria-hidden', 'true');
                    }
                });

                console.log('排序下拉菜单事件绑定完成');
            }

            // 初始化实时排序开关
            this._initRealtimeSortToggle();

            // 恢复保存的排序设置
            this._restoreSavedSortConfig();

            // 添加调试日志
        },

        // 移除了实时排序定时器相关代码
        // 排序逻辑已统一由 SortManager 管理
        // 数据更新时会自动触发排序（如果开关开启）

        // 初始化实时排序开关
        _initRealtimeSortToggle() {
            const realtimeSortToggle = document.getElementById('realtime-sort');
            if (!realtimeSortToggle) return;

            // 恢复保存的实时排序设置
            const savedRealtimeSort = Utils.storage.get(this.REALTIME_SORT_KEY, true); // 默认启用
            realtimeSortToggle.checked = savedRealtimeSort;

            // 同步到 SortManager（单一真相源）
            if (window.SortManager && typeof window.SortManager.setRealtimeSort === 'function') {
                window.SortManager.setRealtimeSort(savedRealtimeSort);
            }

            // 监听开关变化
            realtimeSortToggle.addEventListener('change', (e) => {
                const enabled = e.target.checked;
                Utils.storage.save(this.REALTIME_SORT_KEY, enabled);
                
                // 同步到 SortManager
                if (window.SortManager && typeof window.SortManager.setRealtimeSort === 'function') {
                    window.SortManager.setRealtimeSort(enabled);
                }

                // 如果启用了实时排序，立即应用当前排序
                if (enabled && this.currentConfig && typeof window.applySort === 'function') {
                    window.applySort(this.currentConfig.type, this.currentConfig.direction);
                }

                // 显示提示
                if (typeof notice === 'function') {
                    notice(`实时排序已${enabled ? '启用' : '禁用'}`, 'success');
                }
            });
        },

        _handleSortOptionClick(item) {
            const type = item.dataset.sort;

            // 忽略上传和下载流量排序选项
            if (type === 'upload-traffic' || type === 'download-traffic') {
                console.log(`忽略排序选项: ${type} - 该功能已禁用`);
                return;
            }

            let direction = item.dataset.direction;
            const currentSortText = document.getElementById('current-sort-text');
            const sortOptions = Utils.dom.getAll('.sort-option');

            // 添加调试日志
            console.log(`排序选项点击: 类型=${type}, 方向=${direction}, 元素=`, item);

            // 切换排序方向
            if (item.classList.contains('active')) {
                direction = direction === 'asc' ? 'desc' : 'asc';
                item.dataset.direction = direction;
                console.log(`切换排序方向: ${direction}`);

                // 更新图标
                const icon = item.querySelector('i');
                if (icon) {
                    // 使用Tabler Icons替换Material Icons
                    icon.className = direction === 'asc' ? 'ti ti-chevron-up' : 'ti ti-chevron-down';
                }
            } else {
                // 移除其他选项的激活状态
                sortOptions.forEach(opt => {
                    opt.classList.remove('active');
                    const icon = opt.querySelector('i');
                    if (icon) {
                        // 使用Tabler Icons替换Material Icons
                        icon.className = 'ti ti-chevrons-down';
                    }
                });

                // 激活当前选项
                item.classList.add('active');
                console.log(`激活排序选项: ${type}`);
                const icon = item.querySelector('i');
                if (icon) {
                    // 使用Tabler Icons替换Material Icons
                    icon.className = direction === 'asc' ? 'ti ti-chevron-up' : 'ti ti-chevron-down';
                }
            }

            // 更新当前排序文本
            if (currentSortText) {
                currentSortText.textContent = item.querySelector('span').textContent;
            }

            // 保存排序状态
            this.currentConfig = { type, direction };
            Utils.storage.save(this.STORAGE_KEY, this.currentConfig);
            console.log(`保存排序配置: `, this.currentConfig);

            // 确保全局排序配置正确设置
            window.currentSortConfig = { type, direction };
            console.log(`设置全局排序配置: `, window.currentSortConfig);

            // 应用排序
            if (typeof window.applySort === 'function') {
                console.log(`调用applySort函数: 类型=${type}, 方向=${direction}`);
                window.applySort(type, direction);
            } else {
                console.error(`applySort函数不存在!`);
            }
        },

        _restoreSavedSortConfig() {
            // 获取保存的排序配置
            const savedConfig = Utils.storage.get(this.STORAGE_KEY);

            if (savedConfig && savedConfig.type) {
                const { type, direction } = savedConfig;
                const option = document.querySelector(`.sort-option[data-sort="${type}"]`);

                if (option) {
                    // 恢复保存的排序设置
                    option.dataset.direction = direction;
                    option.classList.add('active');

                    // 更新图标
                    const icon = option.querySelector('i');
                    if (icon) {
                        // 使用Tabler Icons替换Material Icons
                        icon.className = direction === 'asc' ? 'ti ti-chevron-up' : 'ti ti-chevron-down';
                    }

                    // 更新当前排序文本
                    const currentSortText = document.getElementById('current-sort-text');
                    if (currentSortText) {
                        currentSortText.textContent = option.querySelector('span').textContent;
                    }

                    // 应用排序
                    if (typeof window.applySort === 'function') {
                        window.applySort(type, direction);
                    }

                    // 保存为当前配置
                    this.currentConfig = { type, direction };
                    return;
                }
            }

            // 如果没有保存的配置或无法应用，则设置默认排序
            this._setDefaultSort();
        },

        _setDefaultSort() {
            // 设置默认排序选项为激活状态
            const defaultOption = document.querySelector('.sort-option[data-sort="default"]');
            if (!defaultOption) return;

            defaultOption.classList.add('active');
            const direction = defaultOption.dataset.direction || 'desc';

            const icon = defaultOption.querySelector('i');
            if (icon) {
                // 使用Tabler Icons替换Material Icons
                icon.className = direction === 'asc' ? 'ti ti-chevron-up' : 'ti ti-chevron-down';
            }

            // 应用默认排序
            if (typeof window.applySort === 'function') {
                window.applySort('default', direction);
            }

            // 保存为当前配置
            this.currentConfig = { type: 'default', direction };
            Utils.storage.save(this.STORAGE_KEY, this.currentConfig);
        }
    };

    // 分组菜单模块
    const GroupMenu = {
        // 当前分组
        currentGroupId: null,

        // 存储键
        STORAGE_KEY: 'groupConfig',

        init() {
            // 移除可能存在的事件监听器（使用更安全的方式）
            console.log('🔧 初始化分组菜单系统...');

            // 初始化分组下拉菜单 - 使用直接绑定事件而不是通过DropdownManager
            const groupDropdownBtn = document.getElementById('group-dropdown-btn');
            const groupDropdownMenu = document.getElementById('group-dropdown-menu');

            if (groupDropdownBtn && groupDropdownMenu) {
                // 切换下拉菜单显示/隐藏
                groupDropdownBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    
                    // 先关闭其他下拉菜单
                    const sortMenu = document.getElementById('sort-dropdown-menu');
                    const settingsMenu = document.getElementById('dashboard-settings-dropdown-menu');
                    
                    if (sortMenu && !sortMenu.classList.contains('hidden')) {
                        sortMenu.classList.add('hidden');
                        const sortBtn = document.getElementById('sort-dropdown-btn');
                        if (sortBtn) {
                            sortBtn.classList.remove('active-dropdown');
                            sortBtn.setAttribute('aria-expanded', 'false');
                        }
                    }
                    
                    if (settingsMenu && !settingsMenu.classList.contains('hidden')) {
                        settingsMenu.classList.add('hidden');
                        const settingsBtn = document.getElementById('settings-dropdown-btn');
                        if (settingsBtn) {
                            settingsBtn.classList.remove('active-dropdown');
                            settingsBtn.setAttribute('aria-expanded', 'false');
                        }
                    }
                    
                    // 切换分组菜单
                    if (groupDropdownMenu.classList.contains('hidden')) {
                        // 显示菜单
                        groupDropdownMenu.classList.remove('hidden');
                        groupDropdownBtn.classList.add('active-dropdown');
                        groupDropdownBtn.setAttribute('aria-expanded', 'true');
                        groupDropdownMenu.setAttribute('aria-hidden', 'false');

                        // 设置位置
                        const rect = groupDropdownBtn.getBoundingClientRect();
                        groupDropdownMenu.style.top = `${rect.bottom + 5}px`;
                        groupDropdownMenu.style.left = `${rect.left}px`;
                    } else {
                        // 隐藏菜单
                        groupDropdownMenu.classList.add('hidden');
                        groupDropdownBtn.classList.remove('active-dropdown');
                        groupDropdownBtn.setAttribute('aria-expanded', 'false');
                        groupDropdownMenu.setAttribute('aria-hidden', 'true');
                    }
                });

                // 使用事件委托绑定分组选项点击事件（更可靠）
                groupDropdownMenu.addEventListener('click', (e) => {
                    e.stopPropagation(); // 防止关闭菜单
                    
                    // 查找最近的group-option元素
                    const groupOption = e.target.closest('.group-option');
                    if (groupOption) {
                        console.log('[分组筛选] 点击分组选项:', {
                            groupId: groupOption.dataset.group,
                            groupText: groupOption.textContent.trim(),
                            target: e.target,
                            currentFilter: window.UF ? window.UF.getState() : 'UF未初始化'
                        });
                        this._handleGroupOptionClick(groupOption);
                    }
                });

                // 点击页面其他区域关闭下拉菜单
                document.addEventListener('click', () => {
                    if (!groupDropdownMenu.classList.contains('hidden')) {
                        groupDropdownMenu.classList.add('hidden');
                        groupDropdownBtn.classList.remove('active-dropdown');
                        groupDropdownBtn.setAttribute('aria-expanded', 'false');
                        groupDropdownMenu.setAttribute('aria-hidden', 'true');
                    }
                });

                console.log('分组下拉菜单事件绑定完成');
            }

            // 恢复保存的分组设置
            this._restoreSavedGroupConfig();

            // 添加调试日志

            // 分组计数由stats.js中的updateGroupCounts函数处理
            // 在WebSocket数据更新时自动调用
        },

        _handleGroupOptionClick(item) {
            const groupId = item.dataset.group;
            const currentGroupText = document.getElementById('current-group-text');
            const groupOptions = Utils.dom.getAll('.group-option');

            console.log('[分组筛选] _handleGroupOptionClick 开始处理:', {
                groupId,
                currentText: currentGroupText?.textContent,
                totalOptions: groupOptions.length
            });

            // 移除其他选项的激活状态
            groupOptions.forEach(opt => {
                opt.classList.remove('active');
            });

            // 激活当前选项
            item.classList.add('active');

            // 更新当前分组文本
            if (currentGroupText) {
                currentGroupText.textContent = item.querySelector('span').textContent;
            }

            // 保存分组状态
            this.currentGroupId = groupId;
            Utils.storage.save(this.STORAGE_KEY, { groupId });

            // 更新分组按钮的激活指示
            const groupBtn = document.getElementById('group-dropdown-btn');
            if (groupBtn) {
                if (groupId !== 'all') {
                    groupBtn.classList.add('has-active-filter');
                } else {
                    groupBtn.classList.remove('has-active-filter');
                }
            }

            console.log('[分组筛选] 准备调用 applyGroupFilter:', {
                groupId,
                hasApplyGroupFilter: typeof window.applyGroupFilter === 'function'
            });

            // 应用分组过滤 - 注意：applyGroupFilter内部已经调用updateGroupCounts
            // 所以我们不需要再调用this._updateGroupCounts()
            if (typeof window.applyGroupFilter === 'function') {
                window.applyGroupFilter(groupId);
            }
            
            console.log('[分组筛选] 分组切换完成');
        },

        _restoreSavedGroupConfig() {
            // 获取保存的分组配置
            const savedConfig = Utils.storage.get(this.STORAGE_KEY);

            if (savedConfig && savedConfig.groupId) {
                const { groupId } = savedConfig;
                const option = document.querySelector(`.group-option[data-group="${groupId}"]`);

                if (option) {
                    // 激活保存的分组选项
                    const groupOptions = Utils.dom.getAll('.group-option');
                    groupOptions.forEach(opt => opt.classList.remove('active'));
                    option.classList.add('active');

                    // 更新当前分组文本
                    const currentGroupText = document.getElementById('current-group-text');
                    if (currentGroupText) {
                        currentGroupText.textContent = option.querySelector('span').textContent;
                    }

                    // 应用分组过滤
                    if (typeof window.applyGroupFilter === 'function') {
                        window.applyGroupFilter(groupId);
                    }

                    // 更新分组按钮的激活指示
                    const groupBtn = document.getElementById('group-dropdown-btn');
                    if (groupBtn) {
                        if (groupId !== 'all') {
                            groupBtn.classList.add('has-active-filter');
                        } else {
                            groupBtn.classList.remove('has-active-filter');
                        }
                    }

                    // 保存为当前分组ID
                    this.currentGroupId = groupId;
                    return;
                }
            }

            // 如果没有保存的配置或无法应用，则设置默认分组
            this._setDefaultGroup();
        },

        _setDefaultGroup() {
            // 设置默认分组选项为激活状态
            const defaultOption = document.querySelector('.group-option[data-group="all"]');
            if (!defaultOption) return;

            defaultOption.classList.add('active');
            const groupId = 'all';

            // 应用默认分组过滤 - 注意：applyGroupFilter内部已经调用updateGroupCounts
            if (typeof window.applyGroupFilter === 'function') {
                window.applyGroupFilter(groupId);
            }

            // 保存为当前分组ID
            this.currentGroupId = groupId;
            Utils.storage.save(this.STORAGE_KEY, { groupId });
        },

        // 删除这个方法，因为它不再需要
        // 分组计数由stats.js中的updateGroupCounts函数处理
    };

    // 设置菜单
    const SettingsMenu = {
        init() {
            // 清除之前可能存在的事件监听器
            const settingsDropdownMenu = document.getElementById('dashboard-settings-dropdown-menu');
            if (settingsDropdownMenu) {
                // 创建一个新的元素来替换旧元素，从而清除所有事件监听器
                const newMenu = settingsDropdownMenu.cloneNode(true);
                settingsDropdownMenu.parentNode.replaceChild(newMenu, settingsDropdownMenu);

                // 添加调试日志
            }

            // 初始化设置下拉菜单
            const settingsDropdownBtn = document.getElementById('settings-dropdown-btn');
            const newSettingsDropdownMenu = document.getElementById('dashboard-settings-dropdown-menu');

            if (settingsDropdownBtn && newSettingsDropdownMenu) {
                // 切换下拉菜单显示/隐藏
                settingsDropdownBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    
                    // 先关闭其他下拉菜单
                    const sortMenu = document.getElementById('sort-dropdown-menu');
                    const groupMenu = document.getElementById('group-dropdown-menu');
                    
                    if (sortMenu && !sortMenu.classList.contains('hidden')) {
                        sortMenu.classList.add('hidden');
                        const sortBtn = document.getElementById('sort-dropdown-btn');
                        if (sortBtn) {
                            sortBtn.classList.remove('active-dropdown');
                            sortBtn.setAttribute('aria-expanded', 'false');
                        }
                    }
                    
                    if (groupMenu && !groupMenu.classList.contains('hidden')) {
                        groupMenu.classList.add('hidden');
                        const groupBtn = document.getElementById('group-dropdown-btn');
                        if (groupBtn) {
                            groupBtn.classList.remove('active-dropdown');
                            groupBtn.setAttribute('aria-expanded', 'false');
                        }
                    }
                    
                    // 切换设置菜单
                    if (newSettingsDropdownMenu.classList.contains('hidden')) {
                        // 显示菜单
                        newSettingsDropdownMenu.classList.remove('hidden');
                        settingsDropdownBtn.classList.add('active-dropdown');
                        settingsDropdownBtn.setAttribute('aria-expanded', 'true');
                        newSettingsDropdownMenu.setAttribute('aria-hidden', 'false');

                        // 设置位置 - 添加右边界检测
                        const rect = settingsDropdownBtn.getBoundingClientRect();
                        const menuWidth = 240; // 菜单宽度 w-60 = 15rem = 240px
                        const viewportWidth = window.innerWidth;
                        
                        newSettingsDropdownMenu.style.top = `${rect.bottom + 5}px`;
                        
                        // 检查是否会超出右边界
                        if (rect.left + menuWidth > viewportWidth - 10) {
                            // 如果会超出，改为右对齐
                            newSettingsDropdownMenu.style.left = 'auto';
                            newSettingsDropdownMenu.style.right = `${viewportWidth - rect.right}px`;
                        } else {
                            // 正常左对齐
                            newSettingsDropdownMenu.style.left = `${rect.left}px`;
                            newSettingsDropdownMenu.style.right = 'auto';
                        }

                        console.log('设置下拉菜单已打开');
                    } else {
                        // 隐藏菜单
                        newSettingsDropdownMenu.classList.add('hidden');
                        settingsDropdownBtn.classList.remove('active-dropdown');
                        settingsDropdownBtn.setAttribute('aria-expanded', 'false');
                        newSettingsDropdownMenu.setAttribute('aria-hidden', 'true');

                        console.log('设置下拉菜单已关闭');
                    }
                });

                console.log('设置下拉菜单初始化完成');
            }
        }
    };

    // 标签页管理器
    const TabManager = {
        // 活动标签页
        activeTabsByGroup: new Map(),

        init() {
            // 使用事件委托处理所有标签页点击
            document.addEventListener('click', (e) => {
                const tab = e.target.closest('.tab-button');
                if (!tab) return;

                this.activateTab(tab);
            });

            // 使用事件委托处理键盘导航
            document.addEventListener('keydown', (e) => {
                const tab = e.target.closest('.tab-button');
                if (!tab || tab.getAttribute('role') !== 'tab') return;

                const group = tab.dataset.tabGroup ||
                              (tab.dataset.tab.startsWith('load-') ? 'load' :
                               (tab.dataset.tab.startsWith('bandwidth-') ? 'bandwidth' : null));

                const tabList = this._getTabsInGroup(group);
                const currentIndex = tabList.indexOf(tab);

                if (currentIndex === -1) return;

                let newIndex = currentIndex;

                // 左箭头或上箭头 - 移动到上一个标签
                if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                    e.preventDefault();
                    newIndex = currentIndex > 0 ? currentIndex - 1 : tabList.length - 1;
                }
                // 右箭头或下箭头 - 移动到下一个标签
                else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                    e.preventDefault();
                    newIndex = currentIndex < tabList.length - 1 ? currentIndex + 1 : 0;
                }
                // Home键 - 移动到第一个标签
                else if (e.key === 'Home') {
                    e.preventDefault();
                    newIndex = 0;
                }
                // End键 - 移动到最后一个标签
                else if (e.key === 'End') {
                    e.preventDefault();
                    newIndex = tabList.length - 1;
                }
                // Enter或Space键 - 激活当前标签
                else if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.activateTab(tab);
                    return;
                }

                // 如果索引改变，聚焦并激活新标签
                if (newIndex !== currentIndex) {
                    const newTab = tabList[newIndex];
                    newTab.focus();
                    this.activateTab(newTab);
                }
            });

            // 初始化激活默认标签页
            this._activateDefaultTabs();
        },

        // 获取一个组中的所有标签
        _getTabsInGroup(group) {
            const selector = group ?
                `[data-tab-group="${group}"].tab-button` :
                `[data-tab^="${group ? group : ''}-"].tab-button`;

            return Array.from(document.querySelectorAll(selector));
        },

        // 激活标签页
        activateTab(tab) {
            if (!tab) return;

            const targetId = tab.dataset.tab;
            if (!targetId) return;

            const group = tab.dataset.tabGroup ||
                          (targetId.startsWith('load-') ? 'load' :
                           (targetId.startsWith('bandwidth-') ? 'bandwidth' : null));

            // 记录当前激活的标签
            this.activeTabsByGroup.set(group || 'default', targetId);

            // 处理标签内容显示
            const selector = group ?
                `[data-tab-group="${group}"].tab-content` :
                `[id^="${targetId.split('-')[0]}-"].tab-content`;

            document.querySelectorAll(selector).forEach(content => {
                content.classList.add('hidden');
                // 更新aria属性
                if (content.hasAttribute('role') && content.getAttribute('role') === 'tabpanel') {
                    content.setAttribute('hidden', '');
                    content.setAttribute('aria-hidden', 'true');
                }
            });

            // 显示目标内容
            const targetContent = document.getElementById(targetId);
            if (targetContent) {
                targetContent.classList.remove('hidden');
                // 更新aria属性
                if (targetContent.hasAttribute('role') && targetContent.getAttribute('role') === 'tabpanel') {
                    targetContent.removeAttribute('hidden');
                    targetContent.setAttribute('aria-hidden', 'false');
                }
            }

            // 更新标签样式
            const tabSelector = group ?
                `[data-tab-group="${group}"].tab-button` :
                `[data-tab^="${targetId.split('-')[0]}-"].tab-button`;

            document.querySelectorAll(tabSelector).forEach(t => {
                // 移除所有标签的激活状态
                t.classList.remove('active');
                t.classList.remove('text-white');
                // 不再需要移除bg-slate-700/50，因为我们改用CSS变量
                t.classList.add('text-slate-300');

                // 移除激活指示器
                const indicator = t.querySelector('span.absolute.bottom-0');
                if (indicator) {
                    indicator.remove();
                }

                // 更新aria属性
                if (t.hasAttribute('role') && t.getAttribute('role') === 'tab') {
                    t.setAttribute('aria-selected', 'false');
                    t.setAttribute('tabindex', '-1');
                }
            });

            // 添加当前标签的激活状态
            tab.classList.add('active');
            // 不再直接添加背景色类，而是通过CSS变量控制
            tab.classList.remove('text-slate-300');

            // 添加激活指示器（如果不存在）
            if (!tab.querySelector('span.absolute.bottom-0')) {
                const indicator = document.createElement('span');
                indicator.className = 'absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500 dark:bg-blue-400 rounded-t-sm shadow-[0_0_4px_rgba(59,130,246,0.4)]';
                tab.appendChild(indicator);
            }

            // 更新aria属性
            if (tab.hasAttribute('role') && tab.getAttribute('role') === 'tab') {
                tab.setAttribute('aria-selected', 'true');
                tab.setAttribute('tabindex', '0');
            }

            // 触发标签切换事件
            Utils.events.emit('tab:changed', {
                tabId: targetId,
                groupId: group,
                tab: tab
            });
        },

        _activateDefaultTabs() {
            // 查找所有标签组
            const groups = this._getTabGroups();

            // 对每个组激活默认标签
            groups.forEach(group => {
                const defaultTab = document.querySelector(`[data-tab-group="${group}"].tab-button.active`) ||
                                   document.querySelector(`[data-tab-group="${group}"].tab-button`);

                if (defaultTab) this.activateTab(defaultTab);
            });

            // 处理非组标签（例如：load-10m, load-ms, load-hs）
            const loadTab = document.querySelector('[data-tab^="load-"].active') ||
                            document.querySelector('[data-tab^="load-"]');

            if (loadTab) this.activateTab(loadTab);
        },

        _getTabGroups() {
            // 获取所有标签组
            const groupsSet = new Set();
            document.querySelectorAll('[data-tab-group]').forEach(el => {
                groupsSet.add(el.dataset.tabGroup);
            });
            return Array.from(groupsSet);
        }
    };

    // 视图切换模块
    const ViewToggle = {
        init() {
            // 添加视图切换按钮的悬停效果
            const viewToggleBtn = document.getElementById('view-toggle-btn') || document.querySelector('a[href*="theme="]');
            if (viewToggleBtn) {
                // 高亮当前视图模式
                viewToggleBtn.classList.add('view-toggle-btn');

                // 添加过渡动画
                viewToggleBtn.style.transition = 'all 0.2s ease';

                // 添加点击事件处理，在视图切换前保存连接状态
                viewToggleBtn.addEventListener('click', () => {
                    // 确保WebSocket连接状态在视图切换后能被恢复
                    try {
                        // 标记连接为活跃
                        localStorage.setItem('stats_connection_active', 'true');
                        // 更新连接时间戳，确保连接不会被视为过期
                        localStorage.setItem('stats_connection_timestamp', Date.now().toString());
                        console.log('视图切换前已保存WebSocket连接状态');
                    } catch (e) {
                        console.error('保存WebSocket连接状态失败:', e);
                    }
                });
            }

            // 监听主题变化
            this._setupThemeChangeListener();
        },

        // 设置主题变化监听器
        _setupThemeChangeListener() {
            // 创建一个观察器来监听html元素的class变化
            const observer = new MutationObserver((mutations) => {
                for (const mutation of mutations) {
                    if (mutation.attributeName === 'class' &&
                        mutation.target === document.documentElement &&
                        ((mutation.oldValue || '').includes('dark') !== document.documentElement.classList.contains('dark'))) {

                        // 当主题变化时，通知其他组件
                        const isDark = document.documentElement.classList.contains('dark');
                        const theme = isDark ? 'dark' : 'light';

                        Utils.events.emit('theme:changed', { theme, isDark });

                        // 关闭所有打开的下拉菜单
                        DropdownManager.closeAll();

                        // 找到一个变化后就退出循环
                        break;
                    }
                }
            });

            // 开始监视html元素的类变化
            observer.observe(document.documentElement, {
                attributes: true,
                attributeFilter: ['class'],
                attributeOldValue: true
            });

            // 监听主题变化事件
            Utils.events.on('theme:changed', (e) => {
                const isDark = e.detail.isDark;
                console.log(`主题已切换到${isDark ? '暗色' : '亮色'}模式`);
            });
        }
    };

    // 初始化标志，防止重复初始化
    let initialized = false;

    // 初始化函数
    function init() {
        // 如果已经初始化过，则直接返回
        if (initialized) {
            console.log('TabMenuSystem 已经初始化，跳过重复初始化');
            return true;
        }

        // 清除所有可能存在的事件监听器
        try {
            // 移除所有排序选项的点击事件
            const sortOptions = document.querySelectorAll('.sort-option');
            sortOptions.forEach(option => {
                const newOption = option.cloneNode(true);
                if (option.parentNode) {
                    option.parentNode.replaceChild(newOption, option);
                }
            });

            // 移除所有分组选项的点击事件
            const groupOptions = document.querySelectorAll('.group-option');
            groupOptions.forEach(option => {
                const newOption = option.cloneNode(true);
                if (option.parentNode) {
                    option.parentNode.replaceChild(newOption, option);
                }
            });

            // 移除排序下拉菜单按钮的点击事件
            const sortDropdownBtn = document.getElementById('sort-dropdown-btn');
            if (sortDropdownBtn) {
                const newSortDropdownBtn = sortDropdownBtn.cloneNode(true);
                if (sortDropdownBtn.parentNode) {
                    sortDropdownBtn.parentNode.replaceChild(newSortDropdownBtn, sortDropdownBtn);
                }
            }

            // 移除分组下拉菜单按钮的点击事件
            const groupDropdownBtn = document.getElementById('group-dropdown-btn');
            if (groupDropdownBtn) {
                const newGroupDropdownBtn = groupDropdownBtn.cloneNode(true);
                if (groupDropdownBtn.parentNode) {
                    groupDropdownBtn.parentNode.replaceChild(newGroupDropdownBtn, groupDropdownBtn);
                }
            }

            // 清除可能存在的全局事件监听器
            if (window._clickListeners) {
                window._clickListeners.forEach(listener => {
                    document.removeEventListener('click', listener);
                });
            }

            if (window._scrollListeners) {
                window._scrollListeners.forEach(listener => {
                    window.removeEventListener('scroll', listener);
                });
            }

        } catch (error) {
            console.error('清除事件监听器时出错:', error);
        }

        // 存储新的事件监听器引用，以便将来可以清除
        window._clickListeners = [];
        window._scrollListeners = [];

        // 点击页面任何地方关闭所有下拉菜单（使用事件委托）
        const clickListener = () => {
            DropdownManager.closeAll();
        };
        document.addEventListener('click', clickListener);
        window._clickListeners.push(clickListener);

        // 添加滚动事件处理，使下拉菜单跟随滚动
        const scrollListener = () => {
            if (DropdownManager.currentOpenDropdown) {
                DropdownManager._updateDropdownPosition(DropdownManager.currentOpenDropdown);
            }
        };
        window.addEventListener('scroll', scrollListener, { passive: true }); // 使用passive模式提高性能
        window._scrollListeners.push(scrollListener);

        // 初始化标签页管理器
        TabManager.init();

        // 初始化排序菜单
        SortMenu.init();

        // 初始化分组菜单
        GroupMenu.init();

        // 初始化设置菜单
        SettingsMenu.init();

        // 初始化视图切换
        ViewToggle.init();

        // 设置初始化标志
        initialized = true;

        return true;
    }

    // 公开 API
    return {
        init,
        Utils,
        DropdownManager,
        SortMenu,
        GroupMenu,
        SettingsMenu,
        TabManager,
        ViewToggle
    };
})();

// 在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    TabMenuSystem.init();
});

// 暴露到全局
window.TabMenuSystem = TabMenuSystem;