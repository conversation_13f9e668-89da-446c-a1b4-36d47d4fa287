/**
 * Typography - Unified Font Management
 * 统一字体管理系统
 * 
 * 解决跨平台显示一致性问题，统一等宽字体定义
 * 基于系统字体的高性能方案，零延迟加载
 */

:root {
  /* 
   * 标准等宽字体栈
   * 使用系统默认等宽字体，确保跨平台一致性
   * macOS: SF Mono → Menlo → Monaco
   * Windows: Consolas → Courier New
   */
  --font-mono: ui-monospace, Menlo, Consolas, "Liberation Mono", monospace;
  
  /* 
   * 标准无衬线字体栈 - 包含中文安全字体
   * 优先级：macOS(PingFang) → Windows(Microsoft YaHei) → 通用中文(Noto/Source Han) → 西文fallback
   */
  --font-sans: system-ui, -apple-system, "PingFang SC", "Hiragino Sans GB",
               "Microsoft YaHei", "Noto Sans CJK SC", sans-serif;
  
  /* 
   * 字体渲染优化
   * 确保跨平台一致的字体渲染效果
   */
  --font-mono-rendering: antialiased;
}

/* 
 * 基础等宽字体类
 * 替代原有的分散定义
 */
.font-mono {
  font-family: var(--font-mono);
  -webkit-font-smoothing: var(--font-mono-rendering);
  -moz-osx-font-smoothing: grayscale;
  font-variant-numeric: tabular-nums;
}

/* 
 * 数值显示专用类
 * 用于仪表盘指标、统计数据等
 */
.metric-number {
  font-family: var(--font-mono);
  font-weight: 600; /* semibold for better readability */
  font-variant-numeric: tabular-nums;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/*
 * 代码显示和终端样式类 - 已弃用
 * @deprecated 这些类已弃用，请使用 .font-mono 替代
 * 保留定义以维持向后兼容性
 */
.font-code,
.font-terminal {
  /* @deprecated - 使用 .font-mono 替代 */
  font-family: var(--font-mono);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/*
 * 许可证密钥显示
 * 确保字符宽度一致，便于复制粘贴
 */
.license-key-display {
  font-family: var(--font-mono);
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
}

/* 
 * 默认基线应用 - 全站默认字族
 * 确保所有元素继承统一的字体系统
 */
html {
  font-family: var(--font-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
