/**
 * 功能权限限制相关样式
 */

/* 按钮图标样式 - 确保与锁定指示器兼容 */
.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  cursor: pointer;
  background-color: transparent;
  border: none;
  position: relative;
  min-width: 2.5rem;
  min-height: 2.5rem;
  flex-shrink: 0; /* 防止按钮被挤压 */
}

.btn-icon:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark .btn-icon:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* 被锁定的功能元素 */
.feature-locked {
  position: relative;
  opacity: 0.6;
  cursor: not-allowed !important;
}

.feature-locked::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  pointer-events: none;
}

/* 暗色模式下的锁定效果 */
.dark .feature-locked::after {
  background: rgba(255, 255, 255, 0.1);
}

/* 升级提示对话框 */
.feature-upgrade-dialog {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.feature-upgrade-dialog .feature-upgrade-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-upgrade-dialog.show .feature-upgrade-overlay {
  opacity: 1;
}

.feature-upgrade-dialog .feature-upgrade-content {
  position: relative;
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 28rem;
  width: 100%;
  transform: scale(0.95) translateY(1rem);
  opacity: 0;
  transition: all 0.3s ease;
}

.dark .feature-upgrade-content {
  background-color: rgb(30 41 59);
}

.feature-upgrade-dialog.show .feature-upgrade-content {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* 关闭按钮 */
.feature-upgrade-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  border-radius: 0.375rem;
  color: rgb(156 163 175);
  cursor: pointer;
  transition: all 0.2s;
}

.feature-upgrade-close:hover {
  color: rgb(107 114 128);
  background-color: rgb(249 250 251);
}

.dark .feature-upgrade-close:hover {
  color: rgb(209 213 219);
  background-color: rgb(51 65 85);
}

/* 对话框主体 */
.feature-upgrade-body {
  padding: 2rem;
}

/* 头部 */
.feature-upgrade-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1.5rem;
}

.feature-upgrade-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, rgb(251 191 36), rgb(249 115 22));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  box-shadow: 0 10px 15px -3px rgba(251 191 36, 0.3);
}

.feature-upgrade-icon i {
  color: white;
  /* font-size removed - use Tailwind text-2xl */
}

.feature-upgrade-title {
  /* font-size removed - use Tailwind text-2xl */
  font-weight: 700;
  color: rgb(17 24 39);
  margin: 0;
}

.dark .feature-upgrade-title {
  color: white;
}

/* 功能信息 */
.feature-upgrade-feature-info {
  background-color: rgb(249 250 251);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.dark .feature-upgrade-feature-info {
  background-color: rgb(51 65 85 / 0.5);
}

.feature-upgrade-feature-info h4 {
  /* font-size removed - use Tailwind text-lg */
  font-weight: 600;
  color: rgb(17 24 39);
  margin: 0 0 0.5rem 0;
}

.dark .feature-upgrade-feature-info h4 {
  color: white;
}

.feature-upgrade-feature-info p {
  /* font-size removed - use Tailwind text-sm */
  color: rgb(107 114 128);
  margin: 0;
}

.dark .feature-upgrade-feature-info p {
  color: rgb(209 213 219);
}

/* 套餐信息 */
.feature-upgrade-plan-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(to right, rgb(249 250 251), rgb(254 243 199));
  dark: linear-gradient(to right, rgb(51 65 85), rgb(180 83 9 / 0.2));
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.dark .feature-upgrade-plan-info {
  background: linear-gradient(to right, rgb(51 65 85), rgb(180 83 9 / 0.2));
}

.plan-current,
.plan-required {
  text-align: center;
}

.plan-label {
  /* font-size removed - use Tailwind text-xs */
  color: rgb(107 114 128);
  margin: 0 0 0.25rem 0;
}

.dark .plan-label {
  color: rgb(156 163 175);
}

.plan-name {
  /* font-size removed - use Tailwind text-base */
  font-weight: 600;
  color: rgb(17 24 39);
  margin: 0;
}

.dark .plan-name {
  color: white;
}

.plan-required .plan-name {
  color: rgb(217 119 6);
}

.dark .plan-required .plan-name {
  color: rgb(251 191 36);
}

.plan-arrow {
  color: rgb(251 191 36);
  /* font-size removed - use Tailwind text-xl */
}

/* 福利列表 */
.feature-upgrade-benefits {
  margin-bottom: 2rem;
}

.benefits-title {
  /* font-size removed - use Tailwind text-sm */
  font-weight: 500;
  color: rgb(55 65 81);
  margin: 0 0 0.75rem 0;
}

.dark .benefits-title {
  color: rgb(209 213 219);
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  /* font-size removed - use Tailwind text-sm */
  color: rgb(107 114 128);
}

.dark .benefit-item {
  color: rgb(156 163 175);
}

.benefit-item i {
  color: rgb(34 197 94);
  /* font-size removed - use Tailwind text-lg */
}

/* 按钮组 */
.feature-upgrade-actions {
  display: flex;
  gap: 0.75rem;
}

.feature-upgrade-actions button {
  flex: 1;
  padding: 0.625rem 1rem;
  border-radius: 0.5rem;
  /* font-size removed - use Tailwind text-sm */
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.feature-upgrade-actions .btn-cancel {
  border: 1px solid rgb(209 213 219);
  background-color: transparent;
  color: rgb(55 65 81);
}

.dark .feature-upgrade-actions .btn-cancel {
  border-color: rgb(75 85 99);
  color: rgb(209 213 219);
}

.feature-upgrade-actions .btn-cancel:hover {
  background-color: rgb(249 250 251);
}

.dark .feature-upgrade-actions .btn-cancel:hover {
  background-color: rgb(51 65 85);
}

.feature-upgrade-actions .btn-upgrade {
  background: linear-gradient(to right, rgb(245 158 11), rgb(249 115 22));
  color: white;
}

.feature-upgrade-actions .btn-upgrade:hover {
  background: linear-gradient(to right, rgb(217 119 6), rgb(234 88 12));
  transform: scale(1.05);
  box-shadow: 0 10px 15px -3px rgba(245 158 11, 0.3);
}

.feature-upgrade-actions .btn-upgrade i {
  /* font-size removed - use Tailwind text-lg */
}

/* 功能锁定标记 */
.feature-badge-locked {
  display: inline-block;
  background: #fee2e2;
  color: #dc2626;
  padding: 2px 8px;
  border-radius: 4px;
  /* font-size removed - use custom text-[11px] */
  font-weight: 500;
  margin-left: 8px;
}

.dark .feature-badge-locked {
  background: #7f1d1d;
  color: #fca5a5;
}

/* 侧边栏中的锁定项 */
.sidebar-item.feature-locked {
  position: relative;
}

.sidebar-item.feature-locked .sidebar-link {
  opacity: 0.6;
}

.sidebar-item.feature-locked .sidebar-link::after {
  content: '🔒';
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  /* font-size removed - use Tailwind text-sm */
}

/* 锁定指示器容器 - 用于功能墙 */
.feature-lock-indicator {
  position: absolute;
  top: -0.25rem;
  right: -0.25rem;
  z-index: 10;
  pointer-events: none; /* 防止指示器干扰点击 */
}

/* 锁定徽章样式 */
.feature-lock-badge {
  display: flex;
  align-items: center;
  gap: 0.125rem;
  background-color: rgb(245 158 11);
  color: white;
  /* font-size removed - use custom text-[0.625rem] */
  padding: 0.125rem 0.375rem; /* 更小的内边距 */
  border-radius: 9999px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  white-space: nowrap; /* 防止文字换行 */
}

.feature-lock-badge i {
  /* font-size removed - use Tailwind text-xs */
}

.feature-lock-badge span {
  font-weight: 500;
  display: none; /* 默认隐藏文字 */
}

/* 在较大屏幕上显示文字 */
@media (min-width: 768px) {
  .feature-lock-badge span {
    display: inline;
  }
}

/* 针对小按钮的特殊处理 */
.btn-icon .feature-lock-indicator {
  top: -0.125rem;
  right: -0.125rem;
}

.btn-icon .feature-lock-badge {
  padding: 0.125rem;
}

.btn-icon .feature-lock-badge span {
  display: none; /* 在图标按钮上始终隐藏文字 */
}

/* 升级对话框 - 新样式 */
.feature-upgrade-dialog-modern {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

/* 对话框遮罩 */
.feature-upgrade-overlay-modern {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transition: opacity 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-upgrade-overlay-modern.active {
  opacity: 1;
}

/* 对话框内容 */
.feature-upgrade-content-modern {
  position: relative;
  width: 100%;
  max-width: 28rem;
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: 1.5rem;
  transform: scale(0.95);
  opacity: 0;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .feature-upgrade-content-modern {
  background-color: rgb(30 41 59);
}

.feature-upgrade-content-modern.active {
  transform: scale(1);
  opacity: 1;
}

/* 对话框头部 */
.feature-upgrade-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.feature-upgrade-icon-modern {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(to bottom right, rgb(251 191 36), rgb(251 146 60));
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-upgrade-icon-modern i {
  color: white;
  /* font-size removed - use Tailwind text-2xl */
}

.feature-upgrade-title h3 {
  /* font-size removed - use Tailwind text-lg */
  font-weight: 600;
  color: rgb(17 24 39);
  margin-bottom: 0.25rem;
}

.dark .feature-upgrade-title h3 {
  color: white;
}

.feature-upgrade-title p {
  /* font-size removed - use Tailwind text-sm */
  color: rgb(107 114 128);
}

.dark .feature-upgrade-title p {
  color: rgb(156 163 175);
}

/* 功能列表 */
.feature-upgrade-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.feature-upgrade-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  /* font-size removed - use Tailwind text-sm */
  color: rgb(75 85 99);
}

.dark .feature-upgrade-item {
  color: rgb(156 163 175);
}

.feature-upgrade-item i {
  color: rgb(34 197 94);
  /* font-size removed - use Tailwind text-lg */
}

/* 按钮组 */
.feature-upgrade-actions-modern {
  display: flex;
  gap: 0.75rem;
}

.feature-upgrade-btn {
  flex: 1;
  padding: 0.625rem 1rem;
  border-radius: 0.5rem;
  /* font-size removed - use Tailwind text-sm */
  font-weight: 500;
  transition: all 200ms;
  cursor: pointer;
  border: none;
  outline: none;
}

.feature-upgrade-btn-secondary {
  border: 1px solid rgb(209 213 219);
  color: rgb(55 65 81);
  background-color: transparent;
}

.dark .feature-upgrade-btn-secondary {
  border-color: rgb(75 85 99);
  color: rgb(209 213 219);
}

.feature-upgrade-btn-secondary:hover {
  background-color: rgb(249 250 251);
}

.dark .feature-upgrade-btn-secondary:hover {
  background-color: rgb(51 65 85);
}

.feature-upgrade-btn-primary {
  background: linear-gradient(to right, rgb(245 158 11), rgb(249 115 22));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.feature-upgrade-btn-primary:hover {
  background: linear-gradient(to right, rgb(217 119 6), rgb(234 88 12));
  transform: scale(1.05);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.feature-upgrade-btn-primary i {
  /* font-size removed - use Tailwind text-lg */
}

/* 受限元素样式 */
.feature-restricted {
  position: relative;
  cursor: not-allowed !important;
  user-select: none;
}

.feature-restricted::after {
  content: '';
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.1);
  pointer-events: none;
  border-radius: inherit;
}

/* 动画 */
@keyframes feature-shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

.feature-shake {
  animation: feature-shake 0.5s ease-in-out;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .feature-upgrade-content-modern {
    max-width: calc(100vw - 2rem);
  }
  
  .feature-upgrade-actions-modern {
    flex-direction: column;
  }
  
  .feature-upgrade-btn {
    width: 100%;
  }
}