/**
 * Chart Enlarge Hints Styles
 * 图表放大提示样式
 */

/* 图表容器悬停效果 */
.chart-container[data-chart-enlarge="true"] {
    position: relative;
    transition: all 0.2s ease;
}

.chart-container[data-chart-enlarge="true"]:hover {
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

/* 放大按钮提示 */
.chart-container[data-chart-enlarge="true"]::after {
    content: "🔍";
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    /* font-size removed - use Tailwind text-xs */
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    z-index: 10;
}

.chart-container[data-chart-enlarge="true"]:hover::after {
    opacity: 1;
}

/* 暗色主题适配 */
.dark .chart-container[data-chart-enlarge="true"]:hover {
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

.dark .chart-container[data-chart-enlarge="true"]::after {
    background: rgba(255, 255, 255, 0.9);
    color: #1f2937;
}

/* 移动设备上的优化 */
@media (max-width: 768px) {
    .chart-container[data-chart-enlarge="true"]:hover {
        transform: none;
        box-shadow: none;
    }
    
    .chart-container[data-chart-enlarge="true"]::after {
        opacity: 1;
        background: rgba(59, 130, 246, 0.9);
        color: white;
        /* font-size removed - use Tailwind text-sm */
        padding: 6px 10px;
        border-radius: 6px;
    }
}

/* 触摸设备上的提示 */
@media (hover: none) and (pointer: coarse) {
    .chart-container[data-chart-enlarge="true"]::after {
        content: "轻触放大";
        /* font-size removed - use custom text-[11px] */
        padding: 4px 6px;
        opacity: 0.8;
    }
}

/* 加载状态样式 */
.chart-container.loading {
    opacity: 0.6;
    pointer-events: none;
}

.chart-container.loading::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #e5e7eb;
    border-top-color: #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 错误状态样式 */
.chart-container.error {
    border: 2px dashed #ef4444;
    background: rgba(239, 68, 68, 0.05);
}

.chart-container.error::after {
    content: "点击重新加载";
    background: rgba(239, 68, 68, 0.9);
    color: white;
    opacity: 1;
}

/* 禁用状态 */
.chart-container[data-chart-enlarge="false"] {
    cursor: default;
}

.chart-container[data-chart-enlarge="false"]:hover {
    transform: none;
    box-shadow: none;
}

.chart-container[data-chart-enlarge="false"]::after {
    display: none;
}