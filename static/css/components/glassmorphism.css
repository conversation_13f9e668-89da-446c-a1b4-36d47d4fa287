/**
 * 毛玻璃效果组件 - 基础实现
 * 支持亮色/暗色模式，包含浏览器兼容性
 */

:root {
  --glass-blur: 20px;
  --glass-opacity: 0.18; /* 降低亮度，避免偏白 */
  --glass-border-opacity: 0.22; /* 降低高光边框强度 */
  /* 暗色模式玻璃底色参数 */
  --glass-dark-rgb: 15, 23, 42; /* slate-900 */
  --glass-bg-alpha-dark: 0.32; /* 暗色下更自然的深色蒙层 */
  /* 控制饱和度，默认不改变色彩（100%） */
  --glass-saturation: 100%;
  /* Hover 背景透明度（轻微变化，保持可读性） */
  --glass-hover-bg-alpha-light: 0.24;
  --glass-hover-bg-alpha-dark: 0.40;
  --glass-shadow-light: rgba(255, 255, 255, 0.4);
  --glass-shadow-dark: rgba(0, 0, 0, 0.15);
}

/* 基础毛玻璃效果类 */
.glass-effect {
  /* 毛玻璃核心效果 - 增强模糊和饱和度 */
  backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation));
  -webkit-backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation));
  
  /* 半透明背景 - 亮色模式，增强透明度 */
  background: rgba(255, 255, 255, var(--glass-opacity));
  
  /* 增强边框效果 */
  border: 1px solid rgba(255, 255, 255, var(--glass-border-opacity));
  
  /* 增强阴影效果 */
  box-shadow: 0 8px 15px var(--glass-shadow-dark), 
              0 2px 4px rgba(0, 0, 0, 0.05);
  
  /* 降级方案 - 不支持backdrop-filter时 */
  background-clip: padding-box;
  
  /* 平滑过渡 */
  transition: all 0.3s ease;
}

.glass-effect:hover {
  background: rgba(255, 255, 255, calc(var(--glass-opacity) + 0.1));
  box-shadow: 0 12px 25px var(--glass-shadow-dark), 
              0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: rgba(255, 255, 255, calc(var(--glass-border-opacity) + 0.1));
}

/* 暗色模式 */
@media (prefers-color-scheme: dark) {
  .glass-effect {
    background: rgba(var(--glass-dark-rgb), var(--glass-bg-alpha-dark));
    border-color: rgba(148, 163, 184, 0.18); /* slate-400/18 */
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.4), 
                0 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  .glass-effect:hover {
    background: rgba(var(--glass-dark-rgb), calc(var(--glass-bg-alpha-dark) + 0.04));
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.5), 
                0 4px 8px rgba(0, 0, 0, 0.3);
    border-color: rgba(148, 163, 184, 0.26);
  }
}

/* 手动暗色模式类 */
.dark .glass-effect {
  background: rgba(var(--glass-dark-rgb), var(--glass-bg-alpha-dark));
  border-color: rgba(148, 163, 184, 0.18);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.4), 
              0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark .glass-effect:hover {
  background: rgba(var(--glass-dark-rgb), calc(var(--glass-bg-alpha-dark) + 0.04));
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.5), 
              0 4px 8px rgba(0, 0, 0, 0.3);
  border-color: rgba(148, 163, 184, 0.26);
}

/* 服务器卡片毛玻璃效果 - 使用更高优先级 */
.server-card.glass-card {
  backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation)) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation)) !important;
  background-color: rgba(255, 255, 255, 0.18) !important;
  border: 1px solid rgba(255, 255, 255, 0.22) !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 
              0 3px 6px rgba(0, 0, 0, 0.08) !important;
}

.server-card.glass-card:hover {
  background-color: rgba(255, 255, 255, var(--glass-hover-bg-alpha-light)) !important;
  transform: translateY(-2px);
}

/* 暗色模式 - 服务器卡片 */
.dark .server-card.glass-card {
  background-color: rgba(var(--glass-dark-rgb), 0.35) !important;
  border: 1px solid rgba(148, 163, 184, 0.18) !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.5), 
              0 3px 6px rgba(0, 0, 0, 0.3) !important;
}

.dark .server-card.glass-card:hover {
  background-color: rgba(var(--glass-dark-rgb), var(--glass-hover-bg-alpha-dark)) !important;
}

/* 管理卡片毛玻璃效果 */
.admin-card.glass-card {
  backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation)) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation)) !important;
  background-color: rgba(255, 255, 255, 0.18) !important;
  border: 1px solid rgba(255, 255, 255, 0.22) !important;
}

.dark .admin-card.glass-card {
  background-color: rgba(var(--glass-dark-rgb), 0.35) !important;
  border: 1px solid rgba(148, 163, 184, 0.18) !important;
}

/* 统计卡片毛玻璃效果 */
.stat-card.glass-card {
  backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation)) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation)) !important;
  background-color: rgba(255, 255, 255, 0.18) !important;
  border: 1px solid rgba(255, 255, 255, 0.22) !important;
}

.dark .stat-card.glass-card {
  background-color: rgba(var(--glass-dark-rgb), 0.35) !important;
  border: 1px solid rgba(148, 163, 184, 0.18) !important;
}

/* 仪表板卡片毛玻璃效果 */
.dashboard-card.glass-card {
  backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation)) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation)) !important;
  background-color: rgba(255, 255, 255, 0.18) !important;
  border: 1px solid rgba(255, 255, 255, 0.22) !important;
}

.dark .dashboard-card.glass-card {
  background-color: rgba(var(--glass-dark-rgb), 0.35) !important;
  border: 1px solid rgba(148, 163, 184, 0.18) !important;
}

/* 离线状态遮罩专用毛玻璃效果 */
.glass-overlay {
  /* 毛玻璃效果 - 更柔和的模糊 */
  backdrop-filter: blur(12px) saturate(var(--glass-saturation));
  -webkit-backdrop-filter: blur(12px) saturate(var(--glass-saturation));
  /* 增强背景不透明度，提高文本对比度 */
  background: rgba(255, 255, 255, 0.8); /* 更白的背景 */
  border: 1px solid rgba(226, 232, 240, 0.9); /* slate-200 */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 
              0 2px 4px rgba(0, 0, 0, 0.05);
  background-clip: padding-box;
  
  /* 平滑过渡 */
  transition: all 0.3s ease;
  
  /* 亮色模式文字颜色 */
  color: rgb(51 65 85); /* slate-700 */
}

/* 离线遮罩暗色模式 */
@media (prefers-color-scheme: dark) {
  .glass-overlay {
    background: rgba(30, 41, 59, 0.8); /* slate-800 色值，更深的背景 */
    border-color: rgba(71, 85, 105, 0.6); /* slate-600 */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    /* 暗色模式文字颜色 - 浅色 */
    color: rgb(226 232 240); /* slate-200 */
  }
}

.dark .glass-overlay {
  background: rgba(30, 41, 59, 0.8); /* slate-800 色值，更深的背景 */
  border-color: rgba(71, 85, 105, 0.6); /* slate-600 */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  /* 暗色模式文字颜色 - 浅色 */
  color: rgb(226 232 240); /* slate-200 */
}

/* 禁用毛玻璃时的离线遮罩样式 */
.no-glassmorphism .glass-overlay {
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  background: rgba(255, 255, 255, 0.95); /* 纯白色背景 */
  border: 1px solid rgba(226, 232, 240, 0.9); /* slate-200 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  color: rgb(51 65 85); /* slate-700 */
}

/* 禁用其他玻璃效果元素 */
.no-glass-effect {
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* 禁用态全局兜底：在开启 no-glassmorphism 时，统一关闭类名与内联样式中的 backdrop 过滤 */
.no-glassmorphism [class*="backdrop-blur-"],
.no-glassmorphism .backdrop-filter {
  -webkit-backdrop-filter: none !important;
  backdrop-filter: none !important;
}

.no-glassmorphism [style*="backdrop-filter"] {
  -webkit-backdrop-filter: none !important;
  backdrop-filter: none !important;
}

@media (prefers-color-scheme: dark) {
  .no-glassmorphism .glass-overlay {
    background: rgba(30, 41, 59, 0.95); /* slate-800 */
    border-color: rgba(71, 85, 105, 0.6); /* slate-600 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    color: rgb(226 232 240); /* slate-200 */
  }
}

.dark .no-glassmorphism .glass-overlay {
  background: rgba(30, 41, 59, 0.95); /* slate-800 */
  border-color: rgba(71, 85, 105, 0.6); /* slate-600 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: rgb(226 232 240); /* slate-200 */
}

/* 降级方案 - 不支持backdrop-filter的浏览器 */
@supports not (backdrop-filter: blur(1px)) and not (-webkit-backdrop-filter: blur(1px)) {
  .glass-effect,
  .glass-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
  }
  
  .glass-overlay {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    color: rgb(51 65 85); /* slate-700 for light mode */
  }
  
  @media (prefers-color-scheme: dark) {
    .glass-effect,
    .glass-card {
      background: rgba(0, 0, 0, 0.85);
      border-color: rgba(255, 255, 255, 0.2);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.4);
    }
    
    .glass-overlay {
      background: rgba(0, 0, 0, 0.85);
      border-color: rgba(255, 255, 255, 0.2);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.4);
      color: rgb(226 232 240); /* slate-200 for dark mode */
    }
  }
  
  .dark .glass-effect,
  .dark .glass-card {
    background: rgba(0, 0, 0, 0.85);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.4);
  }
  
  .dark .glass-overlay {
    background: rgba(0, 0, 0, 0.85);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.4);
    color: rgb(226 232 240); /* slate-200 for dark mode */
  }
}
