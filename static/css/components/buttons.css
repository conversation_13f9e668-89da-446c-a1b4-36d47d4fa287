/**
 * 按钮组件样式（精简版）
 * @description 保留必要的自定义样式，其余迁移至TailwindCSS
 * @migrated 2025-08-13 - 从208行减至86行
 */

/* ===== CSS变量定义 ===== */
:root {
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --text-color: #1e293b;
  --accent-color: #3b82f6;
  --accent-light-color: #eff6ff;
  --hover-bg-color: #f8fafc;
  --light-hover-bg-color: #f1f5f9;
  --dark-card-bg-color: #1e293b;
  --dark-border-color: #334155;
  --dark-hover-bg-color: #334155;
}

/* ===== 核心按钮样式（必须保留的!important） ===== */
/* 兼容旧的 btn-text 类 - 37个!important减至12个 */
.btn-text {
  /* 使用@apply替代重复样式 */
  @apply relative flex items-center justify-center rounded transition-all duration-200 font-normal;

  /* 必须保留的自定义样式 */
  box-shadow: var(--shadow-sm) !important;
  background-color: rgba(248, 250, 252, 0.8) !important;
  border: 1px solid rgba(226, 232, 240, 0.8) !important;
}



/* 暗色模式 */
:root.dark .btn-text {
  background-color: var(--dark-card-bg-color) !important;
  border: 1px solid var(--dark-border-color) !important;
}

/* 悬停效果 */
.btn-text:hover {
  box-shadow: var(--shadow-md) !important;
  transform: translateY(-1px) !important;
}

:root:not(.dark) .btn-text:hover {
  background-color: var(--light-hover-bg-color) !important;
}

:root.dark .btn-text:hover {
  background-color: var(--dark-hover-bg-color) !important;
}

/* ===== 下拉菜单焦点状态 ===== */
.dropdown-item:focus,
.sort-option:focus,
.group-option:focus {
  @apply bg-blue-50 outline-2 outline-blue-500/50 -outline-offset-2;
}

/* ===== 激活状态 ===== */
.active-dropdown {
  @apply bg-slate-50 border-blue-500 text-blue-500;
}

.sort-option.active {
  @apply bg-indigo-50 text-indigo-600;
}

.dark .sort-option.active {
  @apply bg-indigo-900/20 text-indigo-400;
}

/* ===== 动画（必须保留） ===== */
@keyframes dropdown-fade-in {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== 响应式（使用Tailwind断点） ===== */
@media (max-width: 768px) {
  .dropdown-menu {
    @apply max-w-[calc(100vw-1rem)] mx-2;
  }
}