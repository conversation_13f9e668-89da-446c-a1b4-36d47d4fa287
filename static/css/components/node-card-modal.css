/**
 * Node Card Modal Component Styles
 * 节点卡片放大模态框样式
 */

/* Modal Overlay */
.node-card-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    padding: 20px;
}

.node-card-modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Modal Container - 悬浮窗口样式，适应DStatus主题 */
.node-card-modal-container {
    background: rgb(var(--card-rgb, 255 255 255));
    border-radius: 16px;
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 10px 20px -5px rgba(0, 0, 0, 0.1);
    border: 1px solid rgb(var(--border-rgb, 226 232 240));
    width: 90vw;
    height: 85vh;
    min-width: 800px;
    min-height: 600px;
    max-width: 1400px;
    max-height: 900px;
    position: relative;
    display: flex;
    flex-direction: column;
    transform: scale(0.9) translateY(30px);
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    backdrop-filter: blur(10px);
}

.node-card-modal-overlay.active .node-card-modal-container {
    transform: scale(1) translateY(0);
}

/* Modal Header */
.node-card-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgb(var(--border-rgb, 226 232 240));
    background: rgb(var(--card-rgb, 255 255 255));
    border-radius: 16px 16px 0 0;
    flex-shrink: 0;
}

.node-card-modal-title {
    /* font-size removed - use Tailwind text-xl */
    font-weight: 600;
    color: rgb(var(--foreground-rgb, 15 23 42));
    margin: 0;
}

.node-card-modal-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Time Range Controls */
.time-range-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.time-range-label {
    /* font-size removed - use Tailwind text-sm */
    color: rgb(var(--muted-foreground-rgb, 100 116 139));
    font-weight: 500;
}

.time-range-select {
    padding: 6px 12px;
    border: 1px solid rgb(var(--border-rgb, 226 232 240));
    border-radius: 6px;
    background: rgb(var(--card-rgb, 255 255 255));
    color: rgb(var(--foreground-rgb, 15 23 42));
    /* font-size removed - use Tailwind text-sm */
    cursor: pointer;
    outline: none;
    transition: border-color 0.2s ease;
}

.time-range-select:focus {
    border-color: rgb(var(--primary-rgb, 59 130 246));
}

/* Close Button */
.node-card-modal-close {
    background: none;
    border: none;
    color: rgb(var(--muted-foreground-rgb, 100 116 139));
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.node-card-modal-close:hover {
    background: rgb(var(--muted-rgb, 248 250 252));
    color: rgb(var(--foreground-rgb, 15 23 42));
}

.node-card-modal-close .ti {
    /* font-size removed - use Tailwind text-xl */
}

/* Modal Body */
.node-card-modal-body {
    flex: 1;
    padding: 0;
    overflow-y: auto;
    position: relative;
}

.enlarged-node-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.enlarged-card-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 24px;
    height: 100%;
}

/* Enlarged Card Header */
.enlarged-card-header {
    padding: 20px 24px;
    border-bottom: 1px solid rgb(var(--border-rgb, 226 232 240));
    background: rgb(var(--muted-rgb, 248 250 252));
    border-radius: 12px;
}

/* Stats Grid */
.enlarged-card-stats {
    flex-shrink: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    background: rgb(var(--card-rgb, 255 255 255));
    border: 1px solid rgb(var(--border-rgb, 226 232 240));
    border-radius: 12px;
    transition: all 0.2s ease;
}

.stat-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: rgba(59, 130, 246, 0.1);
}

.stat-icon .ti {
    /* font-size removed - use Tailwind text-2xl */
}

.stat-content {
    flex: 1;
}

.stat-label {
    /* font-size removed - use Tailwind text-sm */
    color: rgb(var(--muted-foreground-rgb, 100 116 139));
    margin-bottom: 4px;
}

.stat-value {
    /* font-size removed - use Tailwind text-lg */
    font-weight: 600;
    color: rgb(var(--foreground-rgb, 15 23 42));
}

/* Chart Area */
.enlarged-card-chart {
    flex: 1;
    min-height: 400px;
    display: flex;
    flex-direction: column;
}

.chart-header {
    padding: 0 0 12px 0;
    border-bottom: 1px solid rgb(var(--border-rgb, 226 232 240));
    margin-bottom: 16px;
}

.enlarged-chart-container {
    flex: 1;
    width: 100%;
    min-height: 400px;
    position: relative;
    background: rgb(var(--card-rgb, 255 255 255));
    border: 1px solid rgb(var(--border-rgb, 226 232 240));
    border-radius: 8px;
    padding: 8px;
}

/* Targets Section */
.enlarged-card-targets {
    flex-shrink: 0;
}

.targets-header {
    padding: 0 0 12px 0;
    border-bottom: 1px solid rgb(var(--border-rgb, 226 232 240));
    margin-bottom: 16px;
}

.targets-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.target-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgb(var(--card-rgb, 255 255 255));
    border: 1px solid rgb(var(--border-rgb, 226 232 240));
    border-radius: 8px;
}

.target-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.target-name {
    font-weight: 500;
    color: rgb(var(--foreground-rgb, 15 23 42));
}

.target-status {
    padding: 2px 8px;
    border-radius: 12px;
    /* font-size removed - use Tailwind text-xs */
    font-weight: 500;
}

.target-status.active {
    background: #dcfce7;
    color: #166534;
}

.target-status.inactive {
    background: #fef2f2;
    color: #dc2626;
}

.target-metrics {
    display: flex;
    gap: 16px;
}

.target-metric {
    /* font-size removed - use Tailwind text-sm */
    color: rgb(var(--muted-foreground-rgb, 100 116 139));
}

/* Dark Theme Support */
.dark .node-card-modal-container {
    background: rgb(var(--card-rgb, 30 41 59));
    border: 1px solid rgb(var(--border-rgb, 51 65 85));
}

.dark .node-card-modal-header {
    background: rgb(var(--card-rgb, 30 41 59));
    border-bottom-color: rgb(var(--border-rgb, 51 65 85));
}

.dark .enlarged-card-header {
    background: rgb(var(--muted-rgb, 51 65 85));
    border-bottom-color: rgb(var(--border-rgb, 75 85 99));
}

.dark .stat-card {
    background: rgb(var(--card-rgb, 30 41 59));
    border-color: rgb(var(--border-rgb, 51 65 85));
}

.dark .enlarged-chart-container {
    background: rgb(var(--card-rgb, 30 41 59));
    border-color: rgb(var(--border-rgb, 51 65 85));
}

.dark .target-item {
    background: rgb(var(--card-rgb, 30 41 59));
    border-color: rgb(var(--border-rgb, 51 65 85));
}

.dark .target-status.active {
    background: rgba(34, 197, 94, 0.2);
    color: #4ade80;
}

.dark .target-status.inactive {
    background: rgba(239, 68, 68, 0.2);
    color: #f87171;
}

.dark .time-range-select {
    background: rgb(var(--card-rgb, 30 41 59));
    border-color: rgb(var(--border-rgb, 75 85 99));
    color: #f8fafc;
}

.dark .time-range-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.dark .time-range-select option {
    background: #1e293b;
    color: #f8fafc;
}

.dark .node-card-modal-close:hover {
    background: rgb(var(--muted-rgb, 51 65 85));
}

/* Dark Theme Text Colors */
.dark .node-card-modal-title {
    color: #f8fafc;
}

.dark .time-range-label {
    color: #cbd5e1;
}

.dark .stat-label {
    color: #94a3b8;
}

.dark .stat-value {
    color: #f8fafc;
}

.dark .target-name {
    color: #f8fafc;
}

.dark .target-metric {
    color: #94a3b8;
}

/* Ensure Tailwind dark classes work properly */
.dark .text-slate-800 {
    color: #f8fafc !important;
}

.dark .text-slate-600 {
    color: #94a3b8 !important;
}

.dark .text-lg {
    color: inherit;
}

/* Additional dark theme text adaptations */
.dark .enlarged-card-header h4 {
    color: #f8fafc !important;
}

.dark .enlarged-card-header span {
    color: #94a3b8 !important;
}

.dark .chart-header h5 {
    color: #f8fafc !important;
}

.dark .targets-header h5 {
    color: #f8fafc !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .node-card-modal-container {
        width: 95vw;
        height: 90vh;
        min-width: 600px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 12px;
    }
}

@media (max-width: 768px) {
    .node-card-modal-container {
        width: 100vw;
        height: 100vh;
        min-width: 350px;
        border-radius: 0;
    }
    
    .node-card-modal-header {
        padding: 16px 20px;
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .node-card-modal-controls {
        flex-direction: column;
        gap: 8px;
    }
    
    .enlarged-card-content {
        padding: 16px;
        gap: 16px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }
    
    .stat-card {
        padding: 12px 16px;
    }
    
    .stat-icon {
        width: 32px;
        height: 32px;
    }
    
    .stat-icon .ti {
        /* font-size removed - use Tailwind text-xl */
    }
    
    .stat-value {
        /* font-size removed - use Tailwind text-base */
    }
    
    .target-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .target-metrics {
        gap: 12px;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .enlarged-card-content {
        padding: 12px;
        gap: 12px;
    }
    
    .node-card-modal-header {
        padding: 12px 16px;
    }
}

/* 优化触摸设备上的交互 */
@media (hover: none) and (pointer: coarse) {
    .node-card-modal-close {
        min-width: 44px;
        min-height: 44px;
    }
    
    .time-range-select {
        min-height: 44px;
    }
    
    .node-card-modal-container {
        touch-action: manipulation;
    }
}