/**
 * Region Globe 3D地球组件样式
 */

/* 地球容器 */
#region-globe-container {
    width: 100%;
    min-height: 600px;  /* 增加最小高度 */
    margin-top: 1rem;
    border-radius: 0.5rem;
    position: relative; /* 作为绝对定位子元素的定位上下文（如 .globe-controls） */
    transition: all 0.3s ease;
}

/* 暗色主题容器背景 - 简单渐变，不遮挡地球 */
.dark #region-globe-container {
    background: linear-gradient(180deg, #000 0%, #0a0a0a 100%);
}


#region-globe-container.hidden {
    display: none;
}

/* 亮色主题背景 */
.light #region-globe-container {
    background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
}

/* 添加闪烁星星动画 */
@keyframes twinkle {
    0%, 100% { opacity: 0.4; }
    50% { opacity: 0.8; }
}


/* 地球globe元素的样式 */
#region-globe-wrapper > div {
    background: transparent !important;
}

/* 地球包装器 */
#region-globe-wrapper {
    width: 100%;
    height: 600px;  /* 增加高度让地球更大 */
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 控制按钮组 - 移至右下角，默认收起 */
.globe-controls {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    z-index: 200; /* 高于Globe默认canvas叠加元素和菜单 */
    display: flex;
    gap: 0.5rem;
    background: rgba(15, 23, 42, 0.9);
    padding: 0.5rem;
    border-radius: 0.75rem;
    backdrop-filter: blur(12px);
    flex-wrap: wrap;
    max-width: calc(100% - 2rem);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 控制面板收起状态 */
.globe-controls.collapsed {
    padding: 0.5rem;
    width: auto;
    height: auto;
    overflow: hidden;
}

.globe-controls.collapsed .globe-control-items {
    display: none;
}

/* 折叠触发按钮 */
.globe-toggle-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 0.5rem;
    background: rgba(99, 102, 241, 0.2);
    color: #a5b4fc;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
    min-height: 2.5rem;
}

.light .globe-toggle-btn {
    background: rgba(99, 102, 241, 0.15);
    color: #6366f1;
}

.globe-toggle-btn:hover {
    background: rgba(99, 102, 241, 0.3);
    color: #c7d2fe;
    transform: scale(1.05);
}

.light .globe-toggle-btn:hover {
    background: rgba(99, 102, 241, 0.25);
    color: #4f46e5;
}

/* 控制项容器 */
.globe-control-items {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    align-items: center;
}

.light .globe-controls {
    background: rgba(255, 255, 255, 0.9);
}

/* 控制按钮 */
.globe-controls button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.375rem;
    background: rgba(99, 102, 241, 0.1);
    color: #a5b4fc;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.light .globe-controls button {
    background: rgba(99, 102, 241, 0.1);
    color: #6366f1;
}

.globe-controls button:hover {
    background: rgba(99, 102, 241, 0.2);
    color: #c7d2fe;
}

.light .globe-controls button:hover {
    background: rgba(99, 102, 241, 0.2);
    color: #4f46e5;
}

.globe-controls button.active {
    background: rgba(99, 102, 241, 0.3);
    color: #e0e7ff;
}

.light .globe-controls button.active {
    background: rgba(99, 102, 241, 0.3);
    color: #4338ca;
}

/* 加载状态 */
.globe-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #a5b4fc;
}

.light .globe-loading {
    color: #6366f1;
}

.globe-loading i {
    font-size: 2rem;
    animation: spin 1s linear infinite;
}

.globe-loading p {
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

/* 降级方案样式 */
.globe-fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 400px;
}

/* 2D/3D切换按钮 */
.view-toggle {
    display: inline-flex;
    background: rgba(99, 102, 241, 0.1);
    border-radius: 0.375rem;
    padding: 0.125rem;
    gap: 0.125rem;
}

.view-toggle button {
    padding: 0.375rem 0.75rem;
    border: none;
    background: transparent;
    color: #94a3b8;
    font-size: 0.75rem;
    cursor: pointer;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.view-toggle button.active {
    background: rgba(99, 102, 241, 0.3);
    color: #e0e7ff;
}

/* 提示信息 */
.globe-tooltip {
    position: absolute;
    background: rgba(15, 23, 42, 0.95);
    color: #e2e8f0;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    pointer-events: none;
    z-index: 100;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(99, 102, 241, 0.2);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.light .globe-tooltip {
    background: rgba(255, 255, 255, 0.95);
    color: #1e293b;
    border-color: rgba(99, 102, 241, 0.3);
}

/* 性能模式指示器 */
.performance-indicator {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    font-size: 0.75rem;
    color: #64748b;
    background: rgba(15, 23, 42, 0.8);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.light .performance-indicator {
    background: rgba(255, 255, 255, 0.9);
    color: #64748b;
}

/* 响应式适配 */
@media (max-width: 768px) {
    #region-globe-wrapper {
        height: 60vh;  /* 使用视口高度，自适应不同屏幕 */
        min-height: 350px;
        max-height: 500px;
    }
    
    #region-globe-container {
        min-height: 400px;
        position: relative; /* 确保作为控制面板的定位容器 */
    }
    
    /* 移动端控制面板 - 相对于容器定位，不占用全局空间 */
    .globe-controls {
        position: absolute; /* 改为absolute，相对于容器 */
        bottom: 0.5rem;
        right: 0.5rem;
        left: auto; /* 只在右侧 */
        border-radius: 0.5rem;
        max-width: calc(100% - 1rem);
        padding: 0.375rem;
        background: rgba(15, 23, 42, 0.92);
        backdrop-filter: blur(12px);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
    }
    
    .light .globe-controls {
        background: rgba(255, 255, 255, 0.92);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    /* 移动端也使用折叠按钮，保持一致性 */
    .globe-toggle-btn {
        display: flex !important;
        padding: 0.375rem;
        min-width: 2rem;
        min-height: 2rem;
    }
    
    /* 控制项容器 - 移动端紧凑布局 */
    .globe-control-items {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 0.375rem;
        max-width: 280px; /* 限制最大宽度 */
    }
    
    /* 收起状态下隐藏控制项 */
    .globe-controls.collapsed .globe-control-items {
        display: none;
    }
    
    .globe-controls.collapsed {
        padding: 0.375rem;
        width: auto;
    }
    
    .globe-controls button {
        padding: 0.375rem 0.5rem;
        font-size: 0.7rem;
        flex: 0 0 auto;
        white-space: nowrap;
    }
    
    /* 移除风格选择器移动端样式 */
}

/* 平板适配 */
@media (min-width: 769px) and (max-width: 1024px) {
    #region-globe-wrapper {
        height: 500px;
    }
    
    .globe-controls {
        bottom: 1.5rem;
        right: 1.5rem;
    }
}

/* 动画 */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 过渡效果 */
.fade-in {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 节点脉冲动画（只用于单个节点，不影响整体） */
@keyframes pulse-glow {
    0%, 100% {
        opacity: 0.8;
        filter: brightness(1) drop-shadow(0 0 10px currentColor);
    }
    50% {
        opacity: 1;
        filter: brightness(1.8) drop-shadow(0 0 25px currentColor);
    }
}

/* 国家整体高亮效果 */
@keyframes country-highlight {
    0% {
        opacity: 0.2;
        stroke-width: 1;
        filter: brightness(1);
    }
    50% {
        opacity: 0.8;
        stroke-width: 2;
        filter: brightness(1.3) drop-shadow(0 0 15px rgba(99, 102, 241, 0.8));
    }
    100% {
        opacity: 0.2;
        stroke-width: 1;
        filter: brightness(1);
    }
}

/* 成就感动画 - 点亮国家（只使用光晕效果，不缩放） */
@keyframes achievement-unlock {
    0% {
        filter: brightness(1);
        box-shadow: 0 0 0 rgba(99, 102, 241, 0);
    }
    20% {
        filter: brightness(1.5);
        box-shadow: 0 0 50px rgba(99, 102, 241, 0.8), 0 0 100px rgba(139, 92, 246, 0.4);
    }
    40% {
        filter: brightness(1.3);
        box-shadow: 0 0 30px rgba(99, 102, 241, 0.6), 0 0 60px rgba(139, 92, 246, 0.3);
    }
    100% {
        filter: brightness(1.1);
        box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
    }
}

/* 波纹扩散效果 */
@keyframes ripple-effect {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

.globe-node-pulse {
    animation: pulse-glow 2s ease-in-out infinite;
}

.globe-country-active {
    animation: country-highlight 3s ease-in-out infinite;
}

.globe-achievement-unlock {
    animation: achievement-unlock 1.5s ease-out;
}

.globe-ripple {
    animation: ripple-effect 1.5s ease-out;
}

/* 地球canvas样式优化 */
#region-globe-wrapper canvas {
    display: block;
    cursor: grab;
}

#region-globe-wrapper canvas:active {
    cursor: grabbing;
}

/* 移除风格选择器样式 - 地图自动跟随网站主题 */

/* 隐藏滚动条但保持滚动功能 */
#region-globe-container::-webkit-scrollbar {
    display: none;
}

#region-globe-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}