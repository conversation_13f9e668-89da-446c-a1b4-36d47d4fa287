/* 图表相关样式 - 从monitor.css拆分 */
/* 包含：加载状态、错误状态、连接状态指示器、动画效果等 */

/* T006: 加载遮罩层样式 */
.monitor-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.3s ease;
}

.monitor-loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    background-color: white;
    border-radius: 0.75rem;
    padding: 2rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    min-width: 200px;
}

html.dark .loading-content {
    background-color: #1e293b;
    color: #e2e8f0;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #8b5cf6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

html.dark .loading-spinner {
    border-color: #475569;
    border-top-color: #8b5cf6;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: #64748b;
}

html.dark .loading-text {
    color: #94a3b8;
}

/* T006: 错误状态样式 */
.error-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    padding: 2rem;
}

.error-content {
    text-align: center;
    max-width: 400px;
}

.error-icon {
    font-size: 4rem;
    color: #ef4444;
    margin-bottom: 1rem;
}

.error-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

html.dark .error-title {
    color: #e2e8f0;
}

.error-message {
    font-size: 0.875rem;
    color: #64748b;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

html.dark .error-message {
    color: #94a3b8;
}

.error-retry-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: #8b5cf6;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.error-retry-btn:hover {
    background-color: #7c3aed;
    transform: translateY(-1px);
}

.error-retry-btn:active {
    transform: translateY(0);
}

/* T006: 连接状态指示器样式 */
.connection-status {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    background-color: rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

html.dark .connection-status {
    background-color: rgba(255, 255, 255, 0.05);
}

.connection-status.status-connected {
    background-color: rgba(34, 197, 94, 0.1);
}

.connection-status.status-warning {
    background-color: rgba(245, 158, 11, 0.1);
}

.connection-status.status-error {
    background-color: rgba(239, 68, 68, 0.1);
}

.connection-status.status-retrying {
    background-color: rgba(59, 130, 246, 0.1);
}

.connection-status.status-retrying .ti {
    animation: spin 1s linear infinite;
}

/* 连接线样式 - 极致优化响应式高度 */
.level-connector {
    width: 0.125rem;
    height: 0.5rem;  /* 进一步减少默认高度 */
    background-image: linear-gradient(to bottom, #8b5cf6, #3b82f6);
    transition: all 0.3s ease;
}

@media (min-width: 480px) {
    .level-connector {
        height: 0.75rem;
    }
}

@media (min-width: 640px) {
    .level-connector {
        height: 1rem;
    }
}

@media (min-width: 768px) {
    .level-connector {
        height: 1.25rem;
    }
}

.target-connector {
    width: 0.125rem;
    height: 0.5rem;  /* 进一步减少默认高度 */
    background-image: linear-gradient(to bottom, #3b82f6, #22c55e);
    transition: all 0.3s ease;
}

@media (min-width: 480px) {
    .target-connector {
        height: 0.75rem;
    }
}

@media (min-width: 640px) {
    .target-connector {
        height: 1rem;
    }
}

@media (min-width: 768px) {
    .target-connector {
        height: 1.25rem;
    }
}

/* 卡片进入动画 */
.card-enter {
    animation: cardEnter 0.3s ease-out;
}

@keyframes cardEnter {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* T006: 减少动画以提高性能 */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .loading-spinner {
        animation: none;
        border: 3px solid #8b5cf6;
    }
}

/* T006: 高对比度模式支持 */
@media (prefers-contrast: high) {
    .connection-status {
        border: 2px solid currentColor;
    }
}

/* T006: 打印样式优化 */
@media print {
    .monitor-loading-overlay,
    .error-state,
    .connection-status {
        display: none !important;
    }
}