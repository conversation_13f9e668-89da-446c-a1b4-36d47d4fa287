/**
 * 简化的度量显示系统 - 覆盖旧系统
 * 确保PC端和移动端样式完全一致
 */

/* 覆盖旧系统的样式 - 确保简化系统正常工作 */
/* 重复规则已移至tailwind.css @layer components，避免冲突 */

/* 确保单位样式一致 */
#current-download-speed-unit,
#current-upload-speed-unit,
#mobile-download-speed-unit,
#mobile-upload-speed-unit,
#total-download-unit,
#total-upload-unit {
  /* 统一单位样式 */
  font-family: var(--font-sans) !important;
  font-weight: 400 !important;
  /* font-size removed - use Tailwind text-xs */
  opacity: 0.7 !important;
  margin-left: 0.25rem !important; /* ml-1 */
  width: 2.5rem !important; /* w-10 */
  text-align: left !important;
  display: inline-block !important;
}

/* 禁用旧系统的伪元素 */
#current-download-speed-value::after,
#current-upload-speed-value::after,
#mobile-download-speed-value::after,
#mobile-upload-speed-value::after,
#total-download-value::after,
#total-upload-value::after,
#current-download-speed::after,
#current-upload-speed::after,
#mobile-download-speed::after,
#mobile-upload-speed::after,
#total-download::after,
#total-upload::after {
  display: none !important;
  content: none !important;
}

/* 确保容器布局正确 */
#current-download-speed,
#current-upload-speed,
#mobile-download-speed,
#mobile-upload-speed,
#total-download,
#total-upload {
  /* 移除旧系统的样式 */
  padding-right: 0 !important;
  min-width: auto !important;
  position: static !important;
  
  /* 确保flex布局正常工作 */
  display: inline-flex !important;
  align-items: baseline !important;
}

/* 双系统冲突规则已移除 - 统一使用 TailwindCSS + typography.css 字体系统 */

/* macOS特定优化 - 解决字体过细问题 */
@media screen and (-webkit-min-device-pixel-ratio: 2) {
  #current-download-speed-value,
  #current-upload-speed-value,
  #mobile-download-speed-value,
  #mobile-upload-speed-value,
  #total-download-value,
  #total-upload-value {
    /* macOS下增强字体显示 - 使用标准方案替代webkit hack */
    font-weight: 700 !important; /* 替代text-stroke实现加粗 */
  }
}

/* Windows特定优化 */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
  #current-download-speed-value,
  #current-upload-speed-value,
  #mobile-download-speed-value,
  #mobile-upload-speed-value,
  #total-download-value,
  #total-upload-value {
    /* Windows下字体正常，保持当前设置 */
    font-weight: 500 !important;
  }
}