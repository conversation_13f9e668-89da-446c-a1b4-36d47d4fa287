/**
 * 纯 CSS 度量显示系统
 * 通过 data 属性和 CSS 伪元素实现样式统一
 */

/* 基础度量容器 */
.metric-display {
  font-family: var(--font-mono);
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum" 1, "lnum" 1;
  white-space: nowrap;
}

/* 数值部分 */
.metric-value {
  font-weight: 500;
  color: inherit;
}

/* 单位部分 - 通过 data-unit 属性自动添加 */
.metric-display[data-unit]::after {
  content: " " attr(data-unit);
  font-family: var(--font-sans);
  /* font-size removed - use Tailwind text-sm */
  font-weight: 400;
  opacity: 0.8;
  margin-left: 0.25em;
  font-variant-numeric: normal;
  font-feature-settings: normal;
}

/* 不同尺寸 */
.metric-large {
  /* font-size removed - use Tailwind text-2xl */
  line-height: 1.2;
}

.metric-medium {
  /* font-size removed - use Tailwind text-lg */
  line-height: 1.3;
}

.metric-small {
  /* font-size removed - use Tailwind text-sm */
  line-height: 1.4;
}

/* 速度专用样式 */
.speed-display {
  font-weight: 600;
  min-width: 6ch;
  display: inline-block;
  text-align: right;
}

/* 流量专用样式 */
.traffic-display {
  font-weight: 500;
}

/* 百分比专用样式 */
.percentage-display[data-unit="%"]::after {
  content: "%";
  font-family: inherit;
  /* font-size removed - use custom font-size-[0.9em] */
  opacity: 0.9;
  margin-left: 0.1em;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .metric-large {
    /* font-size removed - use Tailwind text-xl */
  }
  
  .metric-medium {
    /* font-size removed - use Tailwind text-base */
  }
}

/* 暗色模式优化 */
:root.dark .metric-display {
  -webkit-font-smoothing: subpixel-antialiased;
}

/* 动画优化 */
.metric-display {
  transition: none;
  will-change: contents;
}

/* 特定元素的增强样式 */
#current-download-speed,
#current-upload-speed {
  font-family: var(--font-mono) !important;
  font-weight: 600 !important;
}

/* 移动端速度显示 */
.mobile-download-speed,
.mobile-upload-speed {
  font-family: var(--font-mono) !important;
  font-weight: 500 !important;
}
