/* 管理后台侧边栏 - 移动端优先，无折叠功能 */

/* 基础样式 */
#admin-sidebar {
    transition: transform 200ms ease;
}

/* 桌面端：正常显示 */
@media (min-width: 1024px) {
    #admin-sidebar {
        transform: translateX(0);
    }
}

/* 移动端：默认隐藏，通过 Tailwind 类控制显示 */
@media (max-width: 1023px) {
    #admin-sidebar {
        /* 配合外部组件的 -translate-x-full 类 */
        transform: translateX(-100%);
    }
    
    /* 外部组件移除 -translate-x-full 时显示 */
    #admin-sidebar:not(.-translate-x-full) {
        transform: translateX(0) !important;
    }
}

/* 桌面端布局适配 - 固定16rem宽度 */
@media (min-width: 1024px) {
    .admin-layout-container {
        margin-left: 16rem;
    }
}

/* 移动端重置 */
@media (max-width: 1023px) {
    .admin-layout-container {
        margin-left: 0 !important;
    }
}

/* 打印时隐藏 */
@media print {
    #admin-sidebar,
    #mobile-toggle-sidebar,
    #sidebar-overlay {
        @apply hidden;
    }
    
    .admin-layout-container {
        margin-left: 0 !important;
    }
}

