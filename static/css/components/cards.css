/**
 * 卡片组件样式
 * @description 所有卡片类型的样式和布局
 * @created 2025-08-12 - 从components.css中拆分
 */

/* ===== 卡片样式 ===== */

/* 卡片基础样式 - 所有卡片类型的共享属性 */
.card-base {
  border-radius: 0.5rem;
  border-width: 1px;
  border-style: solid;
  border-color: var(--border-color);
  position: relative;
  overflow: hidden;
  transition: all 0.15s ease;
  background-color: var(--card-bg-color);
}

/* 卡片背景 */
.card-bg {
  background-color: var(--card-bg-color);
}

/* Dashboard卡片 */
.dashboard-card {
  border-radius: 0.5rem;
  border-width: 1px;
  border-style: solid;
  border-color: var(--border-color);
  position: relative;
  overflow: hidden;
  background-color: var(--card-bg-color) !important;
  box-shadow: var(--card-shadow, 0 1px 3px rgba(0, 0, 0, 0.1));
  transition: all 0.15s ease;
}

/* Dashboard卡片暗色模式 */
:root.dark .dashboard-card {
  background-color: var(--dark-card-bg-color) !important;
  border-color: var(--dark-border-color, #1f293c);
  box-shadow: var(--card-shadow-dark, 0 2px 4px 0 rgb(0 0 0 / 0.2), 0 1px 3px -1px rgb(0 0 0 / 0.15));
}

/* 地区项目样式 - 使用卡片设置的背景色 */
.region-item {
  background-color: var(--card-bg-color) !important;
}

:root.dark .region-item {
  background-color: var(--dark-card-bg-color) !important;
}

/* 卡片阴影 */
.card-shadow {
  box-shadow: var(--card-shadow);
}

:root.dark .card-shadow {
  box-shadow: var(--card-shadow-dark);
}

/* 标准卡片 */
.card {
  border-radius: 0.5rem;
  border-width: 1px;
  border-style: solid;
  border-color: var(--border-color);
  position: relative;
  overflow: hidden;
  background-color: var(--card-bg-color);
  box-shadow: var(--card-shadow, 0 1px 3px rgba(0, 0, 0, 0.1));
  transition: box-shadow 0.15s ease;
}

/* 确保卡片内部元素不覆盖圆角 */
.card > div:first-child {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.card > div:last-child {
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

/* 暗色模式卡片 */
:root.dark .card {
  background-color: var(--dark-card-bg-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 服务器卡片 */
.server-card {
  border-radius: 0.5rem;
  /* 添加与控制台外层容器相同的边框样式 */
  border: 1px solid var(--light-border-color, #e2e8f0) !important;
  position: relative;
  overflow: hidden;
  background-color: var(--card-bg-color) !important;
  /* 增强的阴影效果，提供更好的深度感 */
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05), 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  transition: box-shadow 0.2s ease, transform 0.2s ease;
  transform-origin: center center;
  will-change: transform, opacity;
}

/* 服务器卡片hover效果 */
.server-card:hover {
  /* 提升的阴影效果 */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.07), 0 2px 4px -1px rgba(0, 0, 0, 0.05);
  /* 微妙的上移效果 */
  transform: translateY(-1px);
}

/* 服务器卡片暗色模式 - 使用个性化设置变量而非硬编码背景色 */
:root.dark .server-card {
  background-color: var(--dark-card-bg-color) !important;
  border-color: var(--dark-border-color, #1f293c) !important; /* 使用与控制台外层容器相同的边框颜色 */
  /* 暗色模式下的增强阴影 */
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.15), 0 1px 2px 0 rgba(0, 0, 0, 0.1);
}

/* 深色模式下的服务器卡片hover效果 */
:root.dark .server-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.15);
}

/* 服务器卡片内部组件样式 */
.server-card-content {
  flex-grow: 1;
  /* 内边距通过 Tailwind 类控制: pt-8 px-5 pb-5 */
  /* 添加与控制台内层工具栏相同的边框样式 */
  border-bottom: 1px solid rgba(226, 232, 240, 0.1); /* border-slate-200/10 */
}

:root.dark .server-card-content {
  border-bottom-color: rgba(51, 65, 85, 0.8); /* dark:border-slate-700/80 */
}

.server-card-header {
  margin-bottom: 1rem;
}

.server-card-handle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.server-card-footer {
  margin-top: 1rem;
}

/* 服务器卡片详情按钮 */
.server-card-detail-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
  padding: 0.5rem 0;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

/* ===== 新卡片布局区域样式 ===== */

/* 性能指标区域 */
.performance-metrics {
  padding: 0;
}

/* 存储与流量指标区域 */
.storage-traffic-metrics {
  padding: 0;
}

/* 网络指标区域 */
.network-metrics {
  padding: 0;
}

/* 指标项通用样式 */
.metric-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

/* 环形图容器居中对齐 */
.metric-item .flex.justify-center {
  margin-top: 0.25rem;
}

/* 卡片样式已转换为内联Tailwind CSS */

/* 卡片悬停效果 */
.card-hover {
  transition: box-shadow 0.2s ease, background-color 0.12s ease;
}

.card-hover:hover {
  /* 悬停时稍微增强阴影，提供更好的交互反馈，与控制台外层容器视觉效果一致 */
  box-shadow: var(--card-hover-shadow, 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1));
}

/* 背景色hover效果已禁用 - 仅保留阴影效果 */
/* :root:not(.dark) .card-hover:hover {
  background-color: var(--light-hover-bg-color, #f8fafc);
  border-color: var(--light-border-hover-color, #cbd5e1);
}

:root.dark .card-hover:hover {
  background-color: var(--dark-hover-bg-color, #1e293b);
  border-color: var(--dark-border-hover-color, #3e4a6e);
} */

/* 暗色模式下悬停时的阴影，与控制台外层容器视觉效果一致 */
:root.dark .card-hover:hover {
  box-shadow: var(--card-hover-shadow-dark, 0 10px 15px -3px rgb(0 0 0 / 0.25), 0 4px 6px -4px rgb(0 0 0 / 0.15));
}

/* 卡片内边距 */
.card-padding {
  padding: 1rem; /* 16px */
}

.card-padding-sm {
  padding: 0.75rem; /* 12px */
}

.card-padding-lg {
  padding: 1.5rem; /* 24px */
}

/* 卡片头部 */
.card-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 卡片内容 */
.card-content {
  padding: 1rem;
  flex-grow: 1;
}

/* 卡片底部 */
.card-footer {
  padding: 0.75rem 1rem;
  border-top: 1px solid var(--border-color);
}

/* Admin页面卡片 - 兼容性修复版本 */
.admin-card {
  border-radius: 0.5rem;
  border: 1px solid rgba(226, 232, 240, 0.6); /* 更淡的边框 */
  background-color: white;
  position: relative;
  padding: 1rem; /* 恢复基础padding，支持传统架构 */
  overflow: hidden; /* 确保圆角正确显示 */
}

/* 表格组件模式：为p-0类提供特殊处理 */
.admin-card.p-0 {
  padding: 0; /* 表格组件模式，让组件内部控制间距 */
}

/* 夜间模式 */
:root.dark .admin-card {
  background-color: rgb(30 41 59); /* slate-800 */
  border-color: rgba(71, 85, 105, 0.3); /* 暗色模式下的淡边框 */
}

/* Admin页面内部卡片 */
.admin-inner-card {
  border-radius: 0.5rem;
  border-width: 1px;
  border-style: solid;
  border-color: var(--border-color);
  position: relative;
  overflow: hidden;
  background-color: transparent;
  /* 内部卡片使用稍微柔和的阴影 */
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.04), 0 1px 1px 0 rgba(0, 0, 0, 0.02);
  transition: box-shadow 0.2s ease, transform 0.15s ease;
  backdrop-filter: blur(5px);
  padding: 1.25rem;
}

/* Admin内部卡片hover效果 */
.admin-inner-card:hover {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.04);
  transform: translateY(-0.5px);
}

/* 日间模式 */
:root:not(.dark) .admin-inner-card {
  background-color: transparent;
  border-color: #e2e8f0;
}

/* 夜间模式 */
:root.dark .admin-inner-card {
  background-color: transparent;
  border-color: rgba(42, 54, 85, 0.5);
  /* 深色模式下的内部卡片阴影 */
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.12), 0 1px 1px 0 rgba(0, 0, 0, 0.08);
}

/* 深色模式下的内部卡片hover效果 */
:root.dark .admin-inner-card:hover {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.18), 0 1px 2px -1px rgba(0, 0, 0, 0.12);
}

/* 卡片动作区域 */
.card-action {
  display: flex;
  margin-top: 0;
  margin-left: -1rem;
  margin-right: -1rem;
  margin-bottom: -1rem;
}

/* 卡片特殊效果 */
/* 壁纸样式 */
body.has-background-image {
    position: relative;
    min-height: 100vh;
}

/* 壁纸背景蒙版 */
body.has-background-image::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    z-index: -2;
}

/* 壁纸亮度蒙版 */
body.has-background-image::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, var(--wallpaper-overlay-opacity, 0.3));
    z-index: -1;
    pointer-events: none;
}

/* 卡片位置过渡效果 - 用于实时排序 */
.card-position-transition {
  transition: transform 0.3s ease-out;
  will-change: transform;
  z-index: 1;
}

/* 当卡片正在移动时增加一个微小的阴影效果 */
.card-position-transition[style*="transform"] {
  z-index: 2;
  box-shadow: var(--card-hover-shadow);
}

/* 更新中的卡片样式 */
.updating {
  transition: all 0.3s ease;
}

/* 确保卡片之间有足够的间距 */
.space-y-4 > * + * {
  margin-top: 1rem !important;
}

/* 卡片样式优化 */
.dashboard-inner-card {
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.dark .dashboard-inner-card {
  background-color: #0f172a;
}

/* License卡片样式 */
.license-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.license-card:hover {
  border-color: rgba(99, 102, 241, 0.3);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.1);
  transform: translateY(-1px);
}

.dark .license-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.6) 100%);
  border-color: rgba(71, 85, 105, 0.4);
}

.dark .license-card:hover {
  border-color: rgba(165, 180, 252, 0.3);
  box-shadow: 0 4px 12px rgba(165, 180, 252, 0.1);
}

/* 预设壁纸项样式增强 */
.wallpaper-preset-item {
  transition: all 0.3s ease;
  transform-origin: center;
}

.wallpaper-preset-item:hover {
  transform: scale(1.05);
}

.wallpaper-preset-item.selected {
  border-color: #8b5cf6;
  background-color: rgb(243 232 255 / 1);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(196 181 253 / 1);
}

:root.dark .wallpaper-preset-item.selected {
  background-color: rgb(88 28 135 / 0.2);
}

/* 网络质量地区卡片 */
.nq-region-item {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--card-bg-color);
  cursor: pointer;
  transition: all var(--transition-normal) ease;
  box-shadow: var(--card-shadow);
}

.nq-region-item:hover {
  border-color: var(--light-border-hover-color);
  background-color: var(--hover-bg-color);
  box-shadow: var(--card-hover-shadow);
}

.dark .nq-region-item:hover {
  border-color: var(--dark-border-hover-color);
  box-shadow: var(--card-hover-shadow-dark);
}