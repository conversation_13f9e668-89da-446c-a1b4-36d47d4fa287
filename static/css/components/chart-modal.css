/**
 * Chart Modal Component Styles
 * 图表放大模态框样式
 */

/* Modal Overlay */
.chart-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.chart-modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Modal Container - 悬浮窗口样式，适应DStatus主题 */
.chart-modal-container {
    background: rgb(var(--card-rgb, 255 255 255));
    border-radius: 16px;
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 10px 20px -5px rgba(0, 0, 0, 0.1);
    border: 1px solid rgb(var(--border-rgb, 226 232 240));
    width: 75vw;
    height: 65vh;
    min-width: 700px;
    min-height: 500px;
    max-width: 1000px;
    max-height: 700px;
    position: relative;
    display: flex;
    flex-direction: column;
    transform: scale(0.85) translateY(50px);
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    backdrop-filter: blur(10px);
}

.chart-modal-overlay.active .chart-modal-container {
    transform: scale(1) translateY(0);
}

/* Modal Header */
.chart-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgb(var(--border-rgb, 226 232 240));
    background: rgb(var(--card-rgb, 255 255 255));
    border-radius: 12px 12px 0 0;
    flex-shrink: 0;
}

.chart-modal-title {
    /* font-size removed - use Tailwind text-xl */
    font-weight: 600;
    color: rgb(var(--foreground-rgb, 15 23 42));
    margin: 0;
}

.chart-modal-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Granularity Controls */
.granularity-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.granularity-label {
    /* font-size removed - use Tailwind text-sm */
    color: rgb(var(--muted-foreground-rgb, 100 116 139));
    font-weight: 500;
}

.granularity-buttons {
    display: flex;
    gap: 4px;
    background: rgb(var(--muted-rgb, 248 250 252));
    padding: 4px;
    border-radius: 8px;
    border: 1px solid rgb(var(--border-rgb, 226 232 240));
}

.granularity-btn {
    padding: 6px 12px;
    /* font-size removed - use Tailwind text-sm */
    font-weight: 500;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: transparent;
    color: rgb(var(--muted-foreground-rgb, 100 116 139));
}

.granularity-btn:hover {
    background: rgb(var(--card-rgb, 255 255 255));
    color: rgb(var(--foreground-rgb, 15 23 42));
}

.granularity-btn.active {
    background: rgb(var(--primary-rgb, 59 130 246));
    color: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Time Range Controls */
.time-range-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.time-range-select {
    padding: 6px 12px;
    border: 1px solid rgb(var(--border-rgb, 226 232 240));
    border-radius: 6px;
    background: rgb(var(--card-rgb, 255 255 255));
    color: rgb(var(--foreground-rgb, 15 23 42));
    /* font-size removed - use Tailwind text-sm */
    cursor: pointer;
    outline: none;
    transition: border-color 0.2s ease;
}

.time-range-select:focus {
    border-color: rgb(var(--primary-rgb, 59 130 246));
}

/* Close Button */
.chart-modal-close {
    background: none;
    border: none;
    color: rgb(var(--muted-foreground-rgb, 100 116 139));
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-modal-close:hover {
    background: rgb(var(--muted-rgb, 248 250 252));
    color: rgb(var(--foreground-rgb, 15 23 42));
}

.chart-modal-close svg {
    width: 20px;
    height: 20px;
}

/* Modal Body */
.chart-modal-body {
    flex: 1;
    padding: 24px;
    overflow: hidden;
    position: relative;
}

.chart-modal-chart-container {
    width: 100%;
    height: 100%;
    position: relative;
}

/* Loading State */
.chart-modal-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: rgb(var(--muted-foreground-rgb, 100 116 139));
}

.chart-modal-loading .spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgb(var(--muted-rgb, 248 250 252));
    border-top-color: rgb(var(--primary-rgb, 59 130 246));
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Dark Theme Support */
.dark .chart-modal-container {
    background: rgb(var(--card-rgb, 30 41 59));
    border: 1px solid rgb(var(--border-rgb, 51 65 85));
}

.dark .chart-modal-header {
    background: rgb(var(--card-rgb, 30 41 59));
    border-bottom-color: rgb(var(--border-rgb, 51 65 85));
}

.dark .granularity-buttons {
    background: rgb(var(--muted-rgb, 51 65 85));
    border-color: rgb(var(--border-rgb, 75 85 99));
}

.dark .granularity-btn:hover {
    background: rgb(var(--muted-rgb, 51 65 85));
}

.dark .time-range-select {
    background: rgb(var(--card-rgb, 30 41 59));
    border-color: rgb(var(--border-rgb, 75 85 99));
}

.dark .chart-modal-close:hover {
    background: rgb(var(--muted-rgb, 51 65 85));
}

/* Responsive Design */
@media (max-width: 1024px) {
    .chart-modal-container {
        width: 90vw;
        height: 80vh;
        min-width: 700px;
        min-height: 500px;
    }
}

@media (max-width: 768px) {
    .chart-modal-container {
        width: 95vw;
        height: 85vh;
        min-width: 350px;
        min-height: 400px;
        border-radius: 12px;
    }

    .chart-modal-header {
        padding: 16px 20px;
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .chart-modal-controls {
        flex-direction: column;
        gap: 12px;
    }

    .granularity-controls {
        justify-content: center;
    }

    .time-range-controls {
        justify-content: center;
    }

    .chart-modal-body {
        padding: 16px;
    }

    .granularity-buttons {
        flex-wrap: wrap;
        justify-content: center;
    }

    .granularity-btn {
        /* font-size removed - use Tailwind text-xs */
        padding: 6px 10px;
        min-width: 50px;
    }

    .chart-modal-title {
        /* font-size removed - use Tailwind text-lg */
        text-align: center;
    }
}

@media (max-width: 480px) {
    .chart-modal-container {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
    }

    .chart-modal-header {
        padding: 12px 16px;
    }

    .chart-modal-body {
        padding: 12px;
    }

    .granularity-btn {
        /* font-size removed - use custom text-[11px] */
        padding: 5px 8px;
        flex: 1;
        min-width: 40px;
    }

    .granularity-buttons {
        gap: 2px;
        padding: 2px;
    }

    .granularity-label {
        /* font-size removed - use Tailwind text-sm */
        text-align: center;
    }

    .chart-modal-title {
        /* font-size removed - use Tailwind text-base */
    }
}

/* 优化触摸设备上的交互 */
@media (hover: none) and (pointer: coarse) {
    .granularity-btn {
        min-height: 44px;
        /* font-size removed - use Tailwind text-sm */
    }

    .chart-modal-close {
        min-width: 44px;
        min-height: 44px;
    }

    .chart-modal-container {
        touch-action: manipulation;
    }
}