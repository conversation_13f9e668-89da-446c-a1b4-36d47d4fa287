/**
 * 布局组件样式
 * @description 布局、图表、管理界面等样式
 * @created 2025-08-12 - 从components.css中拆分
 */

/* ===== 图表容器样式 ===== */

/* 图表容器基础样式 */
.chart-container {
  position: relative;
  width: 100%;
  /* 使用固定高度而不是比例，避免高度计算冲突 */
  height: 350px;
  margin: 0 auto;
  overflow: visible;
}

/* 确保图表容器在标签页中正确显示 */
.tab-content {
  width: 100%;
  position: relative;
  /* 移除固定的最小高度，让内容决定高度 */
  height: auto;
}

/* 隐藏的标签页不应该占用空间 */
.tab-content.hidden {
  display: none !important;
  height: 0 !important;
  overflow: hidden !important;
  visibility: hidden !important;
  position: absolute !important;
  pointer-events: none !important;
}

/* 确保图表容器在标签页切换时正确显示 */
.tab-content:not(.hidden) .chart-container {
  display: block;
  visibility: visible;
}

/* 系统状态和负载详情卡片高度适配 - 仅在大屏幕上生效 */
@media (min-width: 1024px) {
  /* 父容器使用 flex 布局 */
  .grid.grid-cols-1.lg\:grid-cols-5.gap-4 {
    display: flex;
    flex-wrap: wrap;
  }

  /* 系统状态卡片 */
  .grid.grid-cols-1.lg\:grid-cols-5.gap-4 > .col-span-1.lg\:col-span-2 {
    flex: 2;
    display: flex;
    flex-direction: column;
  }

  /* 负载详情卡片 */
  .grid.grid-cols-1.lg\:grid-cols-5.gap-4 > .col-span-1.lg\:col-span-3 {
    flex: 3;
    display: flex;
    flex-direction: column;
  }

  /* 卡片内容区域自适应高度 */
  .grid.grid-cols-1.lg\:grid-cols-5.gap-4 > div > .p-4 {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  /* 负载详情卡片内的标签页内容区域自适应高度 */
  .load-details-card .p-4 {
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  /* 标签页内容自适应高度 */
  .load-details-card .tab-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

/* ===== 网络统计样式 ===== */

/* 定义进度条动画关键帧 */
@keyframes progress-bar-stripes {
  from { background-position: 1rem 0; }
  to { background-position: 0 0; }
}

/* 定义进度条渐变动画 */
@keyframes progress-bar-glow {
  0% { box-shadow: 0 0 3px rgba(255, 255, 255, 0.1); }
  50% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }
  100% { box-shadow: 0 0 3px rgba(255, 255, 255, 0.1); }
}

/* 为其他进度条添加动画类 */
[id$="_CPU_progress"],
[id$="_MEM_progress"],
[id$="_SWAP_progress"] {
  transition: width 0.8s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: width;
}

/* ProgressBar.js样式 */
:root {
  --download-color: rgba(34, 197, 94, 1);
  --upload-color: rgba(59, 130, 246, 1);
}

.dark {
  --download-color: rgba(34, 197, 94, 0.8);
  --upload-color: rgba(59, 130, 246, 0.8);
}

/* 确保SVG元素正确显示 */
.network-progress svg {
  width: 100%;
  height: 100%;
  border-radius: 999px;
}

/* 进度条路径样式 */
.network-progress path.progressbar-trail {
  stroke: rgba(229, 231, 235, 1);
}

.dark .network-progress path.progressbar-trail {
  stroke: rgba(55, 65, 81, 1);
}

.network-progress path.progressbar-path {
  stroke-linecap: round;
  transition: stroke-dashoffset 1.4s cubic-bezier(0.22, 1, 0.36, 1) 0s;
  will-change: stroke-dashoffset;
}

/* 文本样式优化 */
.network-value-download {
  color: rgb(22, 163, 74);
}

.dark .network-value-download {
  color: rgb(34, 197, 94);
}

.network-value-upload {
  color: rgb(37, 99, 235);
}

.dark .network-value-upload {
  color: rgb(59, 130, 246);
}

/* 数据加载动画 */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.network-value.loading {
  animation: pulse 1.5s infinite;
}

/* ===== 新增柔和缓动与进度条样式 ===== */

/* 柔和缓动曲线 */
.ease-smooth {
  transition-timing-function: cubic-bezier(0.22, 1, 0.36, 1);
}

/* 进度条高亮效果 */
.progress-highlight {
  animation: progress-pulse 0.6s ease-out;
}

@keyframes progress-pulse {
  0% {
    transform: scale(1);
    filter: brightness(1);
    box-shadow: 0 0 0 rgba(59, 130, 246, 0);
  }
  50% {
    transform: scale(1.02);
    filter: brightness(1.2);
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
  }
  100% {
    transform: scale(1);
    filter: brightness(1);
    box-shadow: 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* 满格高光效果 */
.full-bar-glow {
  position: relative;
  overflow: visible !important; /* 覆盖容器的overflow-hidden */
}

/* 当进度条有高光效果时，确保容器允许溢出 */
#download-progress-container:has(.full-bar-glow),
#upload-progress-container:has(.full-bar-glow),
#mobile-download-progress-container:has(.full-bar-glow),
#mobile-upload-progress-container:has(.full-bar-glow) {
  overflow: visible !important;
}

.full-bar-glow::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(90deg,
    rgba(34, 197, 94, 0.3),
    rgba(59, 130, 246, 0.3),
    rgba(34, 197, 94, 0.3)
  );
  border-radius: inherit;
  filter: blur(6px);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.4s ease;
  animation: glow-pulse 2s ease-in-out infinite;
}

.full-bar-glow.active::before {
  opacity: 0.6;
}

@keyframes glow-pulse {
  0%, 100% {
    filter: blur(6px) brightness(1);
  }
  50% {
    filter: blur(8px) brightness(1.2);
  }
}

.dark .full-bar-glow::before {
  background: linear-gradient(90deg,
    rgba(34, 197, 94, 0.4),
    rgba(59, 130, 246, 0.4),
    rgba(34, 197, 94, 0.4)
  );
}

/* 档位徽章样式 - 行内微胶囊 */
.gear-badge {
  display: inline-flex;
  align-items: center;
  font-size: 0.85em;
  line-height: 1;
  height: 1.2em;
  padding: 0 0.45em;
  margin-left: 0.5em;
  border-radius: 9999px;
  border: 1px solid var(--gb-border);
  background: var(--gb-bg);
  color: var(--gb-fg);
  box-shadow: 0 0 0 1px var(--gb-ring) inset;
  vertical-align: baseline;
  white-space: nowrap;
  pointer-events: none;
  font-weight: 600;
}

/* 主题变量 - 日间模式 */
:root {
  --gb-bg: rgba(15, 23, 42, 0.04);
  --gb-ring: rgba(15, 23, 42, 0.06);
  --gb-border: rgba(15, 23, 42, 0.10);
  --gb-fg: #475569;
}

/* 主题变量 - 暗黑模式 */
.dark {
  --gb-bg: rgba(148, 163, 184, 0.08);
  --gb-ring: rgba(148, 163, 184, 0.10);
  --gb-border: rgba(148, 163, 184, 0.18);
  --gb-fg: #cbd5e1;
}

/* 方向色（可选） */
.gear-badge--down {
  color: #16a34a;
}

.gear-badge--up {
  color: #3b82f6;
}

/* 档位变更动画 */
.gear-badge.is-changing {
  animation: gear-pop 200ms ease-out;
}

@keyframes gear-pop {
  0% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 容器防裁切 */
#current-download-speed,
#current-upload-speed,
#mobile-download-speed,
#mobile-upload-speed {
  overflow: visible !important;
}

/* 刻度覆盖层 */
.scale-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 5;
}

/* 刻度标签 */
.scale-label {
  position: absolute;
  top: -2px;
  width: 2px;
  height: calc(100% + 4px);
  background: rgba(0, 0, 0, 0.25);
  opacity: 0.8;
  border-radius: 1px;
}

.dark .scale-label {
  background: rgba(255, 255, 255, 0.3);
}

/* 定义通用进度条类 */
.progress-bar {
  transition: width 0.8s cubic-bezier(0.22, 1, 0.36, 1);
  height: 100%;
  border-radius: 999px;
  will-change: width;
}

/* 关闭动画模式下的进度条过渡效果 */
.progress-bar-no-animation {
  transition: width 0.3s ease-out !important;
}

/* 正常速度模式下的进度条过渡效果 */
.progress-bar-normal {
  transition: width 0.6s cubic-bezier(0.22, 1, 0.36, 1) !important;
}

/* 迅速速度模式下的进度条过渡效果 */
.progress-bar-fast {
  transition: width 0.3s cubic-bezier(0.22, 1, 0.36, 1) !important;
}

/* 总流量精致小标签 */
.total-badge {
  padding: 4px 10px;
  border-radius: 9999px;
  font-size: 11px;
  line-height: 1.2;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
  
  /* 日间模式：使用清晰的卡片风格 */
  color: #475569; /* slate-600 */
  background-color: #f8fafc; /* slate-50 */
  border: 1px solid #e2e8f0; /* slate-200 */
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.total-badge:hover {
  background-color: #f1f5f9; /* slate-100 */
  border-color: #cbd5e1; /* slate-300 */
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

/* 图标样式 */
.total-badge i {
  font-size: 10px;
  line-height: 1;
}

/* 暗色模式 */
:root.dark .total-badge {
  color: #cbd5e1; /* slate-300 */
  background-color: #1e293b; /* slate-800 */
  border-color: #334155; /* slate-700 */
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.3);
}

:root.dark .total-badge:hover {
  background-color: #334155; /* slate-700 */
  border-color: #475569; /* slate-600 */
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.3);
}

/* 地区分布折叠：默认一行，高度随内容渐变 */
#region-stats-wrapper.collapsed {
  max-height: 44px;
}

/* 增强进度条颜色对比度 */
[id$="_CPU_progress"] {
  background-color: var(--progress-cpu-color) !important;
  opacity: 1; /* 提高不透明度，增加对比度 */
  box-shadow: 0 0 4px rgba(59, 130, 246, 0.4); /* 增强阴影效果 */
}

[id$="_MEM_progress"] {
  background-color: var(--progress-memory-color) !important;
  opacity: 1; /* 提高不透明度，增加对比度 */
  box-shadow: 0 0 4px rgba(139, 92, 246, 0.4); /* 增强阴影效果 */
}

/* 优化离线节点的进度条样式 */
.offline [id$="_CPU_progress"],
.offline [id$="_MEM_progress"] {
  opacity: 0.7; /* 提高离线节点进度条的不透明度 */
  filter: saturate(0.5); /* 降低饱和度但保持可见性 */
}

/* 离线节点悬停时的进度条样式 */
.offline.card-hover:hover [id$="_CPU_progress"],
.offline.card-hover:hover [id$="_MEM_progress"] {
  opacity: 0.8; /* 悬停时略微提高不透明度 */
  filter: saturate(0.6); /* 悬停时略微提高饱和度 */
}

/* 进度条相关样式已转换为 Tailwind 类 */

/* 进度条过渡效果 */
[id$="_CPU_progress"],
[id$="_MEM_progress"] {
  transition: width 0.3s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: width;
}

/* ===== Reduced Motion 支持 ===== */
@media (prefers-reduced-motion: reduce) {
  .progress-highlight {
    animation: none;
  }

  .full-bar-glow::before {
    animation: none;
    transition: none;
    opacity: 0 !important;
  }

  .gear-badge.is-changing {
    animation: none;
  }

  .progress-bar,
  .progress-bar-normal,
  .progress-bar-fast,
  [id$="_CPU_progress"],
  [id$="_MEM_progress"],
  [id$="_SWAP_progress"] {
    transition: width 0.1s ease !important;
  }

  @keyframes progress-bar-glow {
    0%, 100% { box-shadow: none; }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
  }
}

/* 数值更新过渡效果 */
[id$="_NET_IN"],
[id$="_NET_OUT"],
[id$="_NET_IN_TOTAL"],
[id$="_NET_OUT_TOTAL"] {
  transition: color 0.3s ease;
}

/* 性能优化 */
@media (prefers-reduced-motion: reduce) {
  .card-base,
  .card-hover,
  .card-hover:hover,
  .transition-width,
  [id$="_CPU_progress"],
  [id$="_MEM_progress"],
  [id$="_NET_IN"],
  [id$="_NET_OUT"],
  [id$="_NET_IN_TOTAL"],
  [id$="_NET_OUT_TOTAL"] {
    transition: none !important;
  }
}

/* ==== 紧凑档位徽章与防裁切 ==== */

#current-download-speed,
#current-upload-speed,
#mobile-download-speed,
#mobile-upload-speed {
  overflow: visible;
}

.gear-badge {
  display: inline-flex;
  align-items: center;
  line-height: 1;
  height: 1.2em;
  padding: 0 0.45em;
  margin-left: 0.5em;
  border-radius: 9999px;
  vertical-align: baseline;
  border: 1px solid rgba(15, 23, 42, 0.10); /* slate-900/10 */
  background: rgba(15, 23, 42, 0.04);       /* slate-900/4  */
  color: #475569; /* slate-600 */
}

.dark .gear-badge {
  border-color: rgba(148, 163, 184, 0.18);  /* slate-400/18 */
  background: rgba(148, 163, 184, 0.08);    /* slate-400/8  */
  color: #cbd5e1; /* slate-300 */
}

.gear-badge.is-changing {
  animation: gear-pop 200ms ease-out;
}

@keyframes gear-pop {
  0% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 进度条右侧徽章（贴合条内） */
.gear-badge--onbar {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  height: calc(100% - 2px);
  font-size: 10px;
  line-height: 1;
  padding: 0 6px;
  z-index: 1;
  pointer-events: none;
}

/* 进度条右侧行内徽章（不覆盖条，位于右侧） */
.gear-badge--right {
  margin-left: 8px;
  font-size: 10px;
}

/* 稍微加高进度条（统一四个容器） */
#download-progress-container,
#upload-progress-container,
#mobile-download-progress-container,
#mobile-upload-progress-container {
  height: 0.5rem !important; /* 8px */
}

/* ===== 动画效果 ===== */

/* 页面加载动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* 旋转动画 */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* 脉冲动画 */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 弹跳动画 */
@keyframes bounce {
    0%, 100% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
        transform: translateY(0);
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}

.animate-bounce {
    animation: bounce 1s infinite;
}

/* 滑入动画 */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

/* 缩放动画 */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-scale-in {
    animation: scaleIn 0.2s ease-out;
}

/* 悬停缩放效果 */
.hover\:scale-\[1\.02\]:hover {
    transform: scale(1.02);
}

.hover\:scale-105:hover {
    transform: scale(1.05);
}

/* 过渡效果 */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.duration-200 {
    transition-duration: 200ms;
}

.duration-300 {
    transition-duration: 300ms;
}

.ease-out {
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.ease-in-out {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== Admin页面标题样式系统 ===== */

/* Admin页面标题样式系统 - 确保内容对齐 */
.admin-card-header {
  margin: 0;
  padding: 0.75rem 0; /* 移除左右padding，让内容与admin-list-header对齐 */
  background-color: rgba(248, 250, 252, 0.5); /* slate-50/50 */
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

/* 常规admin-card中的标题左右padding */
.admin-card:not(.p-0) .admin-card-header {
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 表格组件模式的特殊处理 - 保持内容对齐 */
.admin-card.p-0 .admin-card-header {
  margin: 0;
  padding-left: 1rem; /* 保持与admin-list-header一致的左padding */
  padding-right: 1rem; /* 保持与admin-list-header一致的右padding */
  position: static;
  z-index: auto;
  isolation: auto;
}

:root.dark .admin-card-header {
  background-color: rgba(30, 41, 59, 0.3); /* slate-800/30 */
}

.admin-page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.admin-page-title {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* 从0.625rem减少到0.5rem */
}

.admin-title-icon {
  width: 2.25rem; /* 36px - 调整为用户要求的大小 */
  height: 2.25rem; /* 36px - 调整为用户要求的大小 */
  border-radius: 0.5rem; /* rounded-lg */
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); /* shadow-sm */
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: linear-gradient(to bottom right, var(--tw-gradient-from), var(--tw-gradient-to));
  border: 1px solid;
}

.admin-title-text h1 {
  /* font-size removed - use Tailwind text-sm */
  font-weight: 600; /* font-semibold */
  color: rgb(30, 41, 59); /* text-slate-800 */
  letter-spacing: 0.025em; /* tracking-wide */
  line-height: 1.25; /* leading-tight */
}

:root.dark .admin-title-text h1 {
  color: rgb(226, 232, 240); /* text-slate-200 */
}

.admin-title-text p {
  /* font-size removed - use Tailwind text-xs */
  color: rgb(100, 116, 139); /* text-slate-500 */
  line-height: 1.25; /* leading-tight */
  margin-top: 0.125rem; /* mt-0.5 */
}

:root.dark .admin-title-text p {
  color: rgb(148, 163, 184); /* text-slate-400 */
}

/* 列表标题样式 - 添加flex布局解决图标文字对齐问题 */
.admin-list-header {
  display: flex; /* 添加flex布局 */
  align-items: center; /* 垂直居中对齐图标和文字 */
  gap: 0.5rem; /* 调整为与 .admin-page-title 一致的间距 */
  margin: 0; /* 移除负margin，测试垂直居中效果 */
  padding: 0.75rem 0; /* 仅保留上下padding，左右由父容器控制 */
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  background-color: rgba(248, 250, 252, 0.3); /* slate-50/30 */
}

/* 常规admin-card中的列表标题左右padding */
.admin-card:not(.p-0) .admin-list-header {
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 表格组件模式的特殊处理 - 保持内容对齐 */
.admin-card.p-0 .admin-list-header {
  margin: 0; /* 表格组件模式不使用负margin */
  padding-left: 1rem; /* 保持与常规admin-card内容的左对齐 */
  padding-right: 1rem; /* 保持与常规admin-card内容的右对齐 */
  position: static;
  z-index: auto;
  isolation: auto;
}

:root.dark .admin-list-header {
  border-bottom-color: rgba(71, 85, 105, 0.4);
  background-color: rgba(30, 41, 59, 0.2); /* slate-800/20 */
}

.admin-list-title h2 {
  /* font-size removed - use Tailwind text-base sm:text-base */
  font-weight: 600; /* 调整为与 .admin-title-text h1 一致的字重 */
  color: rgb(30, 41, 59); /* text-slate-800 */
  letter-spacing: 0.025em; /* tracking-wide */
  line-height: 1.25; /* leading-tight */
}

:root.dark .admin-list-title h2 {
  color: rgb(226, 232, 240); /* text-slate-200 */
}

.admin-list-title p {
  /* font-size removed - use Tailwind text-xs */
  color: rgb(100, 116, 139); /* text-slate-500 */
  line-height: 1.25; /* leading-tight */
  margin-top: 0.125rem; /* mt-0.5 */
}

:root.dark .admin-list-title p {
  color: rgb(148, 163, 184); /* text-slate-400 */
}

/* 响应式增强 - 优化版 */
@media (min-width: 640px) {
  .admin-card-header { 
    padding: 0.875rem 1.25rem;
  }
  .admin-title-text h1 { 
    /* font-size removed - use Tailwind text-base */
  }
  .admin-list-title h2 { 
    /* font-size removed - use Tailwind text-base */
  }
}

/* 图标颜色预设 */
.admin-icon-purple { 
  --tw-gradient-from: rgb(233, 213, 255); /* purple-100 */
  --tw-gradient-to: rgb(221, 214, 254); /* purple-200 */
  border-color: rgba(221, 214, 254, 0.5);
}

:root.dark .admin-icon-purple {
  --tw-gradient-from: rgba(88, 28, 135, 0.4); /* purple-900/40 */
  --tw-gradient-to: rgba(107, 33, 168, 0.3); /* purple-800/30 */
  border-color: rgba(109, 40, 217, 0.3);
}

.admin-icon-blue { 
  --tw-gradient-from: rgb(219, 234, 254); /* blue-100 */
  --tw-gradient-to: rgb(191, 219, 254); /* blue-200 */
  border-color: rgba(191, 219, 254, 0.5);
}

:root.dark .admin-icon-blue {
  --tw-gradient-from: rgba(30, 58, 138, 0.4); /* blue-900/40 */
  --tw-gradient-to: rgba(29, 78, 216, 0.3); /* blue-800/30 */
  border-color: rgba(37, 99, 235, 0.3);
}

.admin-icon-green { 
  --tw-gradient-from: rgb(220, 252, 231); /* green-100 */
  --tw-gradient-to: rgb(187, 247, 208); /* green-200 */
  border-color: rgba(187, 247, 208, 0.5);
}

:root.dark .admin-icon-green {
  --tw-gradient-from: rgba(20, 83, 45, 0.4); /* green-900/40 */
  --tw-gradient-to: rgba(22, 101, 52, 0.3); /* green-800/30 */
  border-color: rgba(34, 197, 94, 0.3);
}

.admin-icon-orange { 
  --tw-gradient-from: rgb(254, 237, 213); /* orange-100 */
  --tw-gradient-to: rgb(254, 220, 186); /* orange-200 */
  border-color: rgba(254, 220, 186, 0.5);
}

:root.dark .admin-icon-orange {
  --tw-gradient-from: rgba(124, 45, 18, 0.4); /* orange-900/40 */
  --tw-gradient-to: rgba(154, 52, 18, 0.3); /* orange-800/30 */
  border-color: rgba(251, 146, 60, 0.3);
}

.admin-icon-cyan { 
  --tw-gradient-from: rgb(207, 250, 254); /* cyan-100 */
  --tw-gradient-to: rgb(165, 243, 252); /* cyan-200 */
  border-color: rgba(165, 243, 252, 0.5);
}

:root.dark .admin-icon-cyan {
  --tw-gradient-from: rgba(22, 78, 99, 0.4); /* cyan-900/40 */
  --tw-gradient-to: rgba(21, 94, 117, 0.3); /* cyan-800/30 */
  border-color: rgba(6, 182, 212, 0.3);
}

/* ===== Admin页面侧边栏自适应 ===== */

/* Admin页面侧边栏自适应 - 完整版本 */
.admin-layout-container {
    transition: margin-left 0.2s ease, width 0.2s ease;
}

/* 保持导航栏全宽布局，通过层级管理重叠 */
@media (min-width: 1024px) {
    /* 确保admin页面导航栏全宽显示，并位于侧边栏之上 */
    body.admin-page #main-navbar {
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        z-index: 50 !important; /* 使用!important覆盖Tailwind的z-40 */
    }
}

/* 移动端navbar全宽显示 */
@media (max-width: 1023px) {
    body.admin-page #main-navbar {
        left: 0 !important;
        width: 100% !important;
    }
}

/* 桌面端侧边栏自适应 */
@media (min-width: 1024px) {
    /* 默认展开状态 */
    body.admin-page .admin-layout-container {
        margin-left: 16rem; /* 256px */
        width: calc(100% - 16rem);
    }
    
    /* 侧边栏展开时 */
    body.admin-page.sidebar-expanded .admin-layout-container {
        margin-left: 16rem;
        width: calc(100% - 16rem);
    }
    
    /* 侧边栏折叠时 */
    body.admin-page.sidebar-collapsed .admin-layout-container {
        margin-left: 4rem; /* 64px */
        width: calc(100% - 4rem);
    }
}

/* 移动端和平板端 */
@media (max-width: 1023px) {
    /* 移动端内容区域全宽显示 */
    body.admin-page .admin-layout-container {
        margin-left: 0 !important;
        width: 100% !important;
    }
    
    /* 移动端侧边栏展开时防止页面滚动 */
    body.admin-page.mobile-sidebar-open {
        overflow: hidden;
    }
    
    /* 确保移动端侧边栏在最上层 */
    #admin-sidebar {
        z-index: 50;
    }
    
    /* 移动端遮罩层样式 */
    #sidebar-overlay {
        z-index: 40;
    }
    
    body.admin-page.mobile-sidebar-open #sidebar-overlay {
        opacity: 1;
        pointer-events: auto;
    }
}

/* 过渡动画优化 */
.admin-layout-container {
    will-change: margin-left, width;
}

/* 动画完成后清除 will-change */
body:not(.sidebar-transitioning) .admin-layout-container {
    will-change: auto;
}

/* 页面切换时禁用sidebar动画 */
body.page-transitioning .admin-layout-container,
body.page-transitioning #main-navbar,
body.page-transitioning #admin-sidebar,
body.page-transitioning .sidebar-text,
body.page-transitioning .sidebar-badge,
body.page-transitioning .sidebar-icon,
body.page-transitioning .sidebar-toggle-icon {
    transition: none !important;
}

/* 减少动画强度的选项类 */
body.reduced-motion .admin-layout-container {
    transition: margin-left 0.15s ease, width 0.15s ease;
}

body.reduced-motion #main-navbar {
    transition: left 0.15s ease, width 0.15s ease;
}

body.reduced-motion #admin-sidebar {
    --sidebar-transition: 0.15s ease;
}
