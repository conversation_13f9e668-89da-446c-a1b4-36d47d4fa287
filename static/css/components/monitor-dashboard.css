/* 监控仪表板样式 */
.monitor-dashboard {
    padding: 20px;
    background: var(--background);
    min-height: calc(100vh - 60px);
}

/* 统计卡片 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    /* font-size removed - use Tailwind text-3xl */
}

.stat-icon.online {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.stat-icon.warning {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
}

.stat-icon.traffic {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
}

.stat-icon.performance {
    background: linear-gradient(135deg, #9C27B0, #7B1FA2);
    color: white;
}

.stat-content {
    flex: 1;
}

.stat-value {
    /* font-size removed - use Tailwind text-3xl */
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1;
}

.stat-label {
    /* font-size removed - use Tailwind text-sm */
    color: var(--text-secondary);
    margin-top: 5px;
}

.stat-trend {
    position: absolute;
    top: 10px;
    right: 10px;
    /* font-size removed - use Tailwind text-xs */
    font-weight: 500;
}

.stat-trend.up {
    color: #4CAF50;
}

.stat-trend.down {
    color: #f44336;
}

/* 图表网格 */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.chart-panel {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--border-color);
}

.chart-panel.full-width {
    grid-column: span 2;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.panel-header h3 {
    /* font-size removed - use Tailwind text-lg */
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.chart-container {
    height: 300px;
    width: 100%;
}

/* 时间选择器 */
.time-selector {
    display: flex;
    gap: 5px;
}

.time-btn {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    background: transparent;
    border-radius: 6px;
    /* font-size removed - use Tailwind text-xs */
    cursor: pointer;
    transition: all 0.2s;
}

.time-btn:hover {
    background: var(--hover-bg);
}

.time-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 视图切换器 */
.view-switcher {
    display: flex;
    gap: 5px;
    background: var(--background);
    padding: 4px;
    border-radius: 8px;
}

.view-btn {
    padding: 8px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    color: var(--text-secondary);
}

.view-btn:hover {
    background: var(--hover-bg);
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
}

/* 筛选栏 */
.filter-bar {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.search-box {
    flex: 1;
    min-width: 200px;
    position: relative;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-box input {
    width: 100%;
    padding: 10px 10px 10px 40px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--card-bg);
    color: var(--text-primary);
    /* font-size removed - use Tailwind text-sm */
}

.filter-chips {
    display: flex;
    gap: 8px;
}

.chip {
    padding: 6px 16px;
    border-radius: 20px;
    border: 1px solid var(--border-color);
    background: var(--card-bg);
    cursor: pointer;
    /* font-size removed - use Tailwind text-sm */
    transition: all 0.2s;
}

.chip:hover {
    background: var(--hover-bg);
}

.chip.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 节点网格 */
.nodes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

/* 优化的节点卡片 */
.node-card {
    background: var(--card-bg);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.node-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.node-name {
    font-weight: 500;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.node-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.node-status.online {
    background: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.node-status.offline {
    background: #f44336;
    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.2);
}

.node-health {
    /* font-size removed - use Tailwind text-xl */
    font-weight: 600;
}

.health-excellent { color: #4CAF50; }
.health-good { color: #8BC34A; }
.health-fair { color: #FFC107; }
.health-poor { color: #FF5722; }

/* 节点指标 */
.node-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-top: 12px;
}

.metric {
    text-align: center;
}

.metric-value {
    /* font-size removed - use Tailwind text-base */
    font-weight: 500;
    color: var(--text-primary);
}

.metric-label {
    /* font-size removed - use custom text-[11px] */
    color: var(--text-secondary);
    margin-top: 2px;
}

/* 迷你图表 */
.node-sparkline {
    margin-top: 10px;
    height: 40px;
    opacity: 0.7;
}

/* 实时事件面板 */
.events-panel {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--border-color);
    max-height: 400px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.event-badge {
    background: #f44336;
    color: white;
    /* font-size removed - use Tailwind text-xs */
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.events-list {
    flex: 1;
    overflow-y: auto;
    margin-top: 15px;
}

.event-item {
    display: flex;
    gap: 12px;
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    transition: background 0.2s;
}

.event-item:hover {
    background: var(--hover-bg);
}

.event-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.event-icon.info { background: #2196F3; color: white; }
.event-icon.warning { background: #ff9800; color: white; }
.event-icon.error { background: #f44336; color: white; }
.event-icon.success { background: #4CAF50; color: white; }

.event-content {
    flex: 1;
}

.event-title {
    /* font-size removed - use Tailwind text-sm */
    font-weight: 500;
    color: var(--text-primary);
}

.event-time {
    /* font-size removed - use custom text-[11px] */
    color: var(--text-secondary);
    margin-top: 2px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-panel.full-width {
        grid-column: span 1;
    }
}

@media (max-width: 768px) {
    .stats-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .filter-bar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        width: 100%;
    }
    
    .nodes-grid {
        grid-template-columns: 1fr;
    }
}

/* 暗色主题优化 */
@media (prefers-color-scheme: dark) {
    .stat-card {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .chart-panel {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .node-card {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.1);
    }
}

/* 加载动画 */
.loading-skeleton {
    background: linear-gradient(90deg, var(--skeleton-base) 25%, var(--skeleton-shine) 50%, var(--skeleton-base) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* 工具提示 */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    /* font-size removed - use Tailwind text-xs */
    white-space: nowrap;
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.2s;
}

.tooltip.show {
    opacity: 1;
}

/* 高级筛选面板 */
.advanced-filters {
    position: fixed;
    right: 0;
    top: 60px;
    width: 320px;
    height: calc(100vh - 60px);
    background: var(--card-bg);
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 100;
    overflow-y: auto;
}

.advanced-filters.show {
    transform: translateX(0);
}

/* 节点快捷操作 */
.node-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    opacity: 0;
    transition: opacity 0.2s;
}

.node-card:hover .node-actions {
    opacity: 1;
}

.action-btn {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    border: none;
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-primary);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 4px;
    transition: all 0.2s;
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
}