/* 仪表盘布局样式 - 从monitor.css拆分 */
/* 包含：响应式布局、批量操作、分组样式、移动端优化等 */

/* T006: 优化的响应式尺寸 - 分类卡片 */
@media (max-width: 479px) {
    .category-card {
        width: 10rem;
        height: 9rem;
    }

    .category-content {
        padding: 0.75rem;
    }

    .category-icon {
        font-size: 1.25rem;
        margin-bottom: 0.375rem;
    }

    .category-title {
        font-size: 0.75rem;
        margin-bottom: 0.25rem;
    }

    .category-count {
        font-size: 0.625rem;
        padding: 0.125rem 0.5rem;
    }
}

@media (min-width: 480px) {
    .category-card {
        width: 13rem;
        height: 10.5rem;
    }
}

@media (min-width: 640px) {
    .category-card {
        width: 14rem;
        height: 11rem;
    }
}

@media (min-width: 768px) {
    .category-card {
        width: 15rem;
        height: 11.5rem;
    }
}

@media (min-width: 1024px) {
    .category-card {
        width: 16rem;
        height: 12rem;
    }
}

/* T006: 目标卡片优化的响应式尺寸 */
@media (max-width: 479px) {
    .target-card {
        width: 9rem;
        height: 7.5rem;
    }

    .target-content {
        padding: 0.625rem;
    }

    .target-title {
        font-size: 0.75rem;
        margin-bottom: 0.25rem;
    }

    .target-host {
        font-size: 0.625rem;
        margin-bottom: 0.25rem;
    }

    .target-count {
        font-size: 0.625rem;
        padding: 0.125rem 0.375rem;
    }
}

@media (min-width: 480px) {
    .target-card {
        width: 12rem;
        height: 9rem;
    }
}

@media (min-width: 640px) {
    .target-card {
        width: 13rem;
        height: 9.5rem;
    }
}

@media (min-width: 768px) {
    .target-card {
        width: 14rem;
        height: 10rem;
    }
}

@media (min-width: 1024px) {
    .target-card {
        width: 15rem;
        height: 10.5rem;
    }
}

/* T006: 节点卡片移动端优化 */
@media (max-width: 479px) {
    .node-card {
        height: 5rem;
    }

    .node-content {
        padding: 0.5rem;
    }

    .node-checkbox {
        width: 0.875rem;
        height: 0.875rem;
        margin-bottom: 0.25rem;
    }

    .node-title {
        font-size: 0.625rem;
        line-height: 1.2;
        margin-bottom: 0.125rem;
    }

    .node-status {
        font-size: 0.625rem;
        margin-top: 0.25rem;
        padding: 0.125rem 0.375rem;
    }

    .node-flag {
        width: 0.875rem;
        height: 0.875rem;
    }
}

@media (min-width: 640px) {
    .node-card {
        height: 7rem;
    }
}

/* 响应式调整 - 更精细的断点控制 */

/* 超小屏幕（320px - 479px） */
@media (max-width: 479px) {
    .category-card {
        width: 11rem;
        height: 8.5rem;
    }

    .target-card {
        width: 10.5rem;
        height: 7.5rem;
    }

    .node-card {
        height: 4.5rem;
    }

    .category-content {
        padding: 0.75rem;
    }

    .target-content {
        padding: 0.625rem;
    }

    .node-content {
        padding: 0.5rem;
    }

    /* 操作区适配 */
    .category-actions {
        padding: 0.375rem 0.5rem;
        gap: 0.375rem;
    }

    .target-actions {
        padding: 0.3rem 0.4rem;
        gap: 0.3rem;
    }

    .action-btn {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.75rem;
    }

    .target-action-btn {
        width: 1.5rem;
        height: 1.5rem;
        font-size: 0.625rem;
    }

    .category-title {
        font-size: 0.75rem;
        line-height: 1.2;
        margin-bottom: 0.25rem;
    }

    .target-title {
        font-size: 0.75rem;
        line-height: 1.2;
        margin-bottom: 0.375rem;
    }

    .node-title {
        font-size: 0.625rem;
        line-height: 1.2;
    }

    .category-count,
    .target-count,
    .node-status {
        font-size: 0.625rem;
        padding: 0.125rem 0.375rem;
    }

    .category-icon {
        font-size: 1.125rem;
        margin-bottom: 0.25rem;
    }

    .target-host {
        font-size: 0.625rem;
        line-height: 1.2;
        margin-bottom: 0.25rem;
    }

    /* 优化超小屏幕下的指示器 */
    .expand-indicator {
        top: 0.5rem;
        right: 0.5rem;
        width: 1.25rem;
        height: 1.25rem;
    }

    .target-expand-indicator {
        top: 0.4rem;
        right: 0.4rem;
        width: 1rem;
        height: 1rem;
    }

    /* 优化超小屏幕下的勾选框位置 */
    .node-checkbox {
        top: 0.25rem;
        right: 0.25rem;
        width: 0.875rem;
        height: 0.875rem;
    }

    /* 调整标题行间距 */
    .node-title-row {
        gap: 0.25rem;
        margin-bottom: 0.25rem;
        width: calc(100% - 1.5rem);
    }

    .node-icon {
        font-size: 0.875rem;
    }

    .node-flag {
        width: 0.875rem;
        height: 0.625rem;
    }
}

/* 小屏幕（480px - 639px） - 现在这个断点由上面的@media (min-width: 480px)处理 */
/* 这里只需要处理特殊的小屏幕调整 */
@media (min-width: 480px) and (max-width: 639px) {
    .node-card {
        height: 5rem;
    }

    .node-content {
        padding: 0.5rem;
    }

    .category-content {
        padding: 1rem;
    }

    .target-content {
        padding: 0.75rem;
    }

    .category-title,
    .target-title {
        font-size: 0.875rem;
        line-height: 1.3;
    }

    .category-icon {
        font-size: 1.375rem;
        margin-bottom: 0.375rem;
    }

    .target-host {
        font-size: 0.75rem;
        line-height: 1.3;
    }
}

/* 批量操作按钮样式 */
.batch-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    color: #64748b;
    transition: all 0.2s ease;
    cursor: pointer;
}

.batch-btn:hover {
    background-color: #f8fafc;
    border-color: #cbd5e1;
    color: #374151;
    transform: translateY(-1px);
}

.batch-btn:active {
    transform: scale(0.98);
}

.batch-btn-primary {
    background-color: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.batch-btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
    color: white;
}

html.dark .batch-btn {
    background-color: #1e293b;
    border-color: #475569;
    color: #94a3b8;
}

html.dark .batch-btn:hover {
    background-color: #334155;
    border-color: #64748b;
    color: #e2e8f0;
}

html.dark .batch-btn-primary {
    background-color: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

html.dark .batch-btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
    color: white;
}

/* 分组容器样式 */
.group-container {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px -1px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    transition: all 0.3s ease;
}

.group-container:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.08);
}

html.dark .group-container {
    background-color: #1e293b;
    border-color: #334155;
    box-shadow: 0 1px 3px -1px rgba(0, 0, 0, 0.3);
}

html.dark .group-container:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
}

.group-header {
    transition: all 0.2s ease;
}

.group-header:hover {
    background-color: rgba(248, 250, 252, 0.5);
}

html.dark .group-header:hover {
    background-color: rgba(51, 65, 85, 0.3);
}

.group-nodes {
    padding: 1rem;
    background-color: #fafafa;
    border-top: 1px solid #e2e8f0;
}

html.dark .group-nodes {
    background-color: #0f172a;
    border-top-color: #334155;
}

/* 分组批量操作按钮样式 - 优化版 */
.group-batch-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    padding: 4px;
    background: rgba(255, 255, 255, 0.95);
    border: 1.5px solid rgba(148, 163, 184, 0.4);
    border-radius: 8px;
    color: #64748b;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    font-size: 11px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.group-batch-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.group-batch-btn:hover::before {
    left: 100%;
}

.group-batch-btn:hover {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(148, 163, 184, 0.6);
    color: #475569;
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.group-batch-btn:active {
    transform: translateY(0) scale(0.98);
    transition-duration: 0.1s;
}

/* 主要操作按钮 - 选择在线 */
.group-batch-btn-primary {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    border-color: #8b5cf6;
    color: white;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(139, 92, 246, 0.2);
}

.group-batch-btn-primary::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.group-batch-btn-primary:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    border-color: #7c3aed;
    color: white;
    box-shadow: 0 6px 12px rgba(139, 92, 246, 0.3);
    transform: translateY(-2px) scale(1.08);
}

/* 次要操作按钮 - 全选 */
.group-batch-btn-secondary {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.4);
    color: #3b82f6;
}

.group-batch-btn-secondary:hover {
    background: rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.6);
    color: #2563eb;
}

/* 响应式优化 */
@media (min-width: 640px) {
    .group-batch-btn {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }
}

/* 移动端优化 */
@media (max-width: 479px) {
    .group-batch-btn {
        width: 36px;
        height: 36px;
        font-size: 13px;
        border-radius: 10px;
    }
    
    /* 在超小屏幕上，按钮之间的间距更大 */
    .group-batch-btn + .group-batch-btn {
        margin-left: 6px;
    }
}

/* 暗色模式优化 */
.dark .group-batch-btn {
    background: rgba(30, 41, 59, 0.95);
    border-color: rgba(71, 85, 105, 0.5);
    color: #94a3b8;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.dark .group-batch-btn::before {
    background: linear-gradient(90deg, transparent, rgba(148, 163, 184, 0.2), transparent);
}

.dark .group-batch-btn:hover {
    background: rgba(30, 41, 59, 1);
    border-color: rgba(71, 85, 105, 0.7);
    color: #cbd5e1;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.dark .group-batch-btn-primary {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    border-color: #8b5cf6;
    color: white;
    box-shadow: 0 2px 4px rgba(139, 92, 246, 0.4);
}

.dark .group-batch-btn-primary:hover {
    background: linear-gradient(135deg, #a78bfa, #8b5cf6);
    border-color: #a78bfa;
    color: white;
    box-shadow: 0 6px 12px rgba(139, 92, 246, 0.5);
}

.dark .group-batch-btn-secondary {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.4);
    color: #60a5fa;
}

.dark .group-batch-btn-secondary:hover {
    background: rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.6);
    color: #93c5fd;
}

/* T006: 通用移动端优化 */
@media (max-width: 479px) {
    /* 减少间距以适应小屏幕 */
    .admin-card {
        margin: 0.5rem;
        padding: 0.75rem;
    }

    /* 优化按钮尺寸 */
    .btn-refresh, .btn-add-category, .btn-add-target {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }

    /* 优化模态框 */
    .modal-content {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }

    /* 优化表单元素 */
    .form-input, .form-textarea, .form-select {
        font-size: 16px; /* 防止iOS缩放 */
    }

    /* 优化批量操作工具栏 */
    .batch-toolbar {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.75rem;
    }

    .batch-toolbar .batch-btn {
        width: 100%;
        justify-content: center;
    }
}

/* T006: 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    /* 触摸设备上增大点击区域 */
    .group-batch-btn {
        min-width: 44px;
        min-height: 44px;
    }
}

/* T006: 打印样式优化 */
@media print {
    .group-batch-btn,
    .batch-toolbar {
        display: none !important;
    }
}