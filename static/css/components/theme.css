/**
 * 主题样式
 * @description 定义主题相关的变量和样式
 * @created 2024-05-19
 */

/* 主题变量 */
:root {
  /* 基础UI动画速度变量 */
  --transition-speed: 0.12s;
  --loading-transition-speed: 0.3s;

  /* 全局阴影样式 */
  --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --card-shadow-dark: 0 2px 4px 0 rgb(0 0 0 / 0.2), 0 1px 3px -1px rgb(0 0 0 / 0.15);
  --card-hover-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --card-hover-shadow-dark: 0 10px 15px -3px rgb(0 0 0 / 0.25), 0 4px 6px -4px rgb(0 0 0 / 0.15);

  /* 日间模式颜色 */
  --light-bg-color: #f8fafc;
  --light-bg-color-rgb: 248, 250, 252;
  --light-bg-opacity: 1;
  --light-card-bg-color: #ffffff;
  --light-card-bg-color-rgb: 255, 255, 255;
  --light-card-opacity: 0.95;
  --light-text-color: #1e293b;
  --light-secondary-text-color: #64748b;
  --light-border-color: #e2e8f0;
  --light-border-hover-color: #cbd5e1;
  --light-hover-bg-color: #f1f5f9;
  --light-highlight-color: #f1f5f9;
  --light-progress-bg-color: #f3f4f6;

  /* 进度条颜色 */
  --light-progress-cpu-color: #3b82f6;
  --light-progress-memory-color: #8b5cf6;
  --light-progress-swap-color: #6366f1;
  --light-progress-disk-color: #f59e0b;
  --light-progress-network-color: #10b981;

  /* 夜间模式颜色 */
  --dark-bg-color: #0f172a;
  --dark-bg-color-rgb: 15, 23, 42;
  --dark-bg-opacity: 0.98;
  --dark-card-bg-color: #111d32;
  --dark-card-bg-color-rgb: 17, 29, 50;
  --dark-card-opacity: 0.7;
  --dark-text-color: #f8fafc;
  --dark-secondary-text-color: #cbd5e1;
    /* 夜间模式边框颜色 */
  --dark-border-color: rgba(31, 41, 60, 0.8); /* 从50%提升到80%，提高可见性 */
  --dark-border-hover-color: #3e4a6e;
  /* 夜间模式悬停背景颜色 */
  --dark-hover-bg-color: #1a2439;
  /* 夜间模式高亮颜色 */
  --dark-highlight-color: #334155;
  /* 夜间模式进度条背景 */
  --dark-progress-bg-color: rgba(55, 65, 81, 0.5);

  /* 夜间模式进度条颜色 */
  --dark-progress-cpu-color: #60a5fa;
  --dark-progress-memory-color: #a78bfa;
  --dark-progress-swap-color: #818cf8;
  --dark-progress-disk-color: #fbbf24;
  --dark-progress-network-color: #34d399;

  /* 强调色 */
  --accent-color: #3b82f6;
  --accent-hover-color: #2563eb;
  --accent-light-color: rgba(59, 130, 246, 0.1);
  --accent-dark-color: rgba(59, 130, 246, 0.2);

  /* 状态颜色 */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* 离线节点样式变量 */
  --offline-opacity: 0.75;  /* 提高不透明度，使其更清晰 */
  --offline-filter: grayscale(30%) brightness(0.95);  /* 减少灰度，增加亮度，移除暖色调 */
  --offline-border-color: rgba(148, 148, 148, 0.3);  /* 更中性的边框颜色 */
  --dark-offline-opacity: 0.8;  /* 提高不透明度，使其更清晰 */
  --offline-bg-color: rgba(241, 241, 241, 0.4);  /* 亮色模式下的中性背景 */
  --dark-offline-bg-color: rgba(51, 51, 51, 0.2);  /* 暗色模式下的中性背景 */
  --dark-offline-border-color: rgba(120, 120, 120, 0.25);  /* 暗色模式下的中性边框 */

  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, #3b82f6, #2563eb);
  --gradient-success: linear-gradient(135deg, #10b981, #059669);
  --gradient-warning: linear-gradient(135deg, #f59e0b, #d97706);
  --gradient-error: linear-gradient(135deg, #ef4444, #dc2626);

  /* 状态样式变量 */
  --online-indicator-color: #10b981;
  --offline-indicator-color: #ef4444;
  --hidden-indicator-color: #64748b;

  /* 卡片标题颜色变量 */
  --card-title-color: #1e293b; /* 亮色模式使用接近黑色的深灰色 */
  --card-title-hover-color: #0f172a; /* 亮色模式悬停时的颜色，更深 */
  --card-title-color-dark: #e2e8f0; /* 暗色模式使用接近白色的浅灰色 */
  --card-title-hover-color-dark: #f8fafc; /* 暗色模式悬停时的颜色，更亮 */

  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* 动画时间 */
  --transition-fast: 0.08s;
  --transition-normal: 0.15s;
  --transition-slow: 0.25s;

  /* 网络质量监控页面专用主题变量 */
  /* 图表颜色变量 */
  --nq-chart-text-primary: #1e293b;
  --nq-chart-text-secondary: #64748b;  
  --nq-chart-text-tertiary: #94a3b8;
  --nq-chart-axis-line: #e2e8f0;
  --nq-chart-split-line: #f1f5f9;
  --nq-chart-tooltip-bg: rgba(255, 255, 255, 0.9);
  --nq-chart-tooltip-border: #e2e8f0;

  /* 暗色模式图表颜色 */
  --nq-dark-chart-text-primary: #f1f5f9;
  --nq-dark-chart-text-secondary: #cbd5e1;
  --nq-dark-chart-text-tertiary: #94a3b8;
  --nq-dark-chart-axis-line: #475569;
  --nq-dark-chart-split-line: #334155;
  --nq-dark-chart-tooltip-bg: rgba(30, 41, 59, 0.9);
  --nq-dark-chart-tooltip-border: #475569;

  /* 弹窗样式变量 */
  --nq-modal-overlay-bg: rgba(0, 0, 0, 0.5);
  --nq-modal-bg: #ffffff;
  --nq-modal-text: #374151;
  --nq-modal-text-secondary: #6b7280;
  --nq-modal-border: #e5e7eb;
  --nq-modal-button-bg: #f3f4f6;
  --nq-modal-button-hover: #f9fafb;
  --nq-modal-input-border: #d1d5db;
  --nq-modal-input-focus: #9ca3af;
  --nq-modal-primary-bg: linear-gradient(135deg, #3b82f6, #1d4ed8);
  --nq-modal-primary-hover: linear-gradient(135deg, #2563eb, #1e40af);

  /* 暗色模式弹窗变量 */
  --nq-dark-modal-bg: #1f2937;
  --nq-dark-modal-text: #f9fafb;
  --nq-dark-modal-text-secondary: #9ca3af;
  --nq-dark-modal-border: #374151;
  --nq-dark-modal-button-bg: #374151;
  --nq-dark-modal-button-hover: #4b5563;
  --nq-dark-modal-input-border: #4b5563;
  --nq-dark-modal-input-focus: #6b7280;

  /* 导航条相关变量 */
  --nav-icon-size: 22px;
  --nav-icon-size-sm: 20px;
  --nav-padding-x: 0.75rem;
  --nav-padding-y: 0.75rem;

  /* 全局过渡效果 */
  transition: background-color var(--transition-speed) ease,
             color var(--transition-speed) ease,
             border-color var(--transition-speed) ease;
}

/* 防止主题加载时闪烁 */
html.theme-initializing * {
  transition: none !important;
}


/* 禁用加载中的过渡效果 */
#server-cards-loading * {
  transition: none !important;
}

/* 禁用卡片初始化时的过渡效果，防止闪烁 */
html:not(.theme-initialized) .card,
html:not(.theme-initialized) .dashboard-inner-card,
html:not(.theme-initialized) .dashboard-outer-card {
  transition: none !important;
}

/* 日间模式默认样式 */
:root:not(.dark) {
  --bg-color: var(--light-bg-color);
  --bg-color-rgb: var(--light-bg-color-rgb);
  --bg-opacity: var(--light-bg-opacity);
  --card-bg-color: var(--light-card-bg-color);
  --card-bg-color-rgb: var(--light-card-bg-color-rgb);
  --card-opacity: var(--light-card-opacity);
  --text-color: var(--light-text-color);
  --secondary-text-color: var(--light-secondary-text-color);
  --border-color: var(--light-border-color);
  --hover-bg-color: var(--light-hover-bg-color);
  --highlight-color: var(--light-highlight-color);

  /* 进度条颜色变量 */
  --progress-bg-color: var(--light-progress-bg-color);
  --progress-cpu-color: var(--light-progress-cpu-color);
  --progress-memory-color: var(--light-progress-memory-color);
  --progress-swap-color: var(--light-progress-swap-color);
  --progress-disk-color: var(--light-progress-disk-color);
  --progress-network-color: var(--light-progress-network-color);

  /* 网络质量监控页面主题映射 - 亮色模式 */
  --chart-text-primary: var(--nq-chart-text-primary);
  --chart-text-secondary: var(--nq-chart-text-secondary);
  --chart-text-tertiary: var(--nq-chart-text-tertiary);
  --chart-axis-line: var(--nq-chart-axis-line);
  --chart-split-line: var(--nq-chart-split-line);
  --chart-tooltip-bg: var(--nq-chart-tooltip-bg);
  --chart-tooltip-border: var(--nq-chart-tooltip-border);
  --modal-overlay-bg: var(--nq-modal-overlay-bg);
  --modal-bg: var(--nq-modal-bg);
  --modal-text: var(--nq-modal-text);
  --modal-text-secondary: var(--nq-modal-text-secondary);
  --modal-border: var(--nq-modal-border);
  --modal-button-bg: var(--nq-modal-button-bg);
  --modal-button-hover: var(--nq-modal-button-hover);
  --modal-input-border: var(--nq-modal-input-border);
  --modal-input-focus: var(--nq-modal-input-focus);
  --modal-primary-bg: var(--nq-modal-primary-bg);
  --modal-primary-hover: var(--nq-modal-primary-hover);
}

/* 夜间模式默认样式 */
:root.dark {
  --bg-color: var(--dark-bg-color);
  --bg-color-rgb: var(--dark-bg-color-rgb);
  --bg-opacity: var(--dark-bg-opacity);
  --card-bg-color: var(--dark-card-bg-color);
  --card-bg-color-rgb: var(--dark-card-bg-color-rgb);
  --card-opacity: var(--dark-card-opacity);
  --text-color: var(--dark-text-color);
  --secondary-text-color: var(--dark-secondary-text-color);
  --border-color: var(--dark-border-color);
  --hover-bg-color: var(--dark-hover-bg-color);
  --highlight-color: var(--dark-highlight-color);

  /* 进度条颜色变量 */
  --progress-bg-color: var(--dark-progress-bg-color);
  --progress-cpu-color: var(--dark-progress-cpu-color);
  --progress-memory-color: var(--dark-progress-memory-color);
  --progress-swap-color: var(--dark-progress-swap-color);
  --progress-disk-color: var(--dark-progress-disk-color);
  --progress-network-color: var(--dark-progress-network-color);

  /* 网络质量监控页面主题映射 - 暗色模式 */
  --chart-text-primary: var(--nq-dark-chart-text-primary);
  --chart-text-secondary: var(--nq-dark-chart-text-secondary);
  --chart-text-tertiary: var(--nq-dark-chart-text-tertiary);
  --chart-axis-line: var(--nq-dark-chart-axis-line);
  --chart-split-line: var(--nq-dark-chart-split-line);
  --chart-tooltip-bg: var(--nq-dark-chart-tooltip-bg);
  --chart-tooltip-border: var(--nq-dark-chart-tooltip-border);
  --modal-bg: var(--nq-dark-modal-bg);
  --modal-text: var(--nq-dark-modal-text);
  --modal-text-secondary: var(--nq-dark-modal-text-secondary);
  --modal-border: var(--nq-dark-modal-border);
  --modal-button-bg: var(--nq-dark-modal-button-bg);
  --modal-button-hover: var(--nq-dark-modal-button-hover);
  --modal-input-border: var(--nq-dark-modal-input-border);
  --modal-input-focus: var(--nq-dark-modal-input-focus);
}

/* 文本样式已转换为 Tailwind 类 */

/* 全局文本颜色和背景 */
body {
  @apply bg-slate-50 dark:bg-slate-900;
  color: var(--text-color);
  background-color: rgba(var(--bg-color-rgb), var(--bg-opacity)) !important;
  transition: background-color var(--transition-fast) ease, color var(--transition-fast) ease;
}

/* 卡片悬停效果 */
.card-hover {
  @apply hover:shadow-md
         transition-all duration-300;
  /* 移除变形效果，只保留阴影变化 */
}

/* 主题类名样式 - 仅用于兼容性，不设置边框颜色 */
.theme-dark {
  /* 不再设置边框颜色，避免与 theme-border 冲突 */
}

.theme-light {
  /* 不再设置边框颜色，避免与 theme-border 冲突 */
}

/* 性能优化 */
@media (prefers-reduced-motion: reduce) {
  .theme-transition,
  .theme-transition * {
    transition: none !important;
  }

  .card-hover {
    @apply hover:shadow;
    /* 移除所有变形效果 */
  }
}

:root.dark .card-hover {
  transition: all 0.2s ease;
}

/* 不再设置边框颜色，避免与 tailwind.css 中的设置冲突 */

/* ===== 节点状态样式 ===== */
/* 在线节点样式 */
.online {
  opacity: 1;
  filter: none;
  /* 移除边框颜色设置，与无边框设计保持一致 */
  transition: all 0.3s ease;
}

/* 离线节点样式 - 从 card.css 移入 */
.offline {
  opacity: var(--offline-opacity, 0.75);
  filter: var(--offline-filter, grayscale(30%) brightness(0.95));
  /* 移除边框颜色设置，使用阴影效果替代 */
  background-color: var(--offline-bg-color, rgba(241, 241, 241, 0.4));
  /* 使用更中性的阴影效果 */
  box-shadow: 0 0 5px rgba(120, 120, 120, 0.15), 0 0 0 1px rgba(120, 120, 120, 0.1);
  transition: all 0.3s ease;
}

:root.dark .offline {
  opacity: var(--dark-offline-opacity, 0.8);
  /* 移除边框颜色设置，使用阴影效果替代 */
  background-color: var(--dark-offline-bg-color, rgba(51, 51, 51, 0.2));
  /* 使用更中性的阴影效果 */
  box-shadow: 0 0 5px rgba(120, 120, 120, 0.15), 0 0 0 1px rgba(120, 120, 120, 0.1);
}

/* 离线节点悬停效果 */
.offline.card-hover:hover {
  opacity: 0.85;  /* 悬停时略微提高不透明度 */
  filter: grayscale(20%) brightness(1);  /* 悬停时减少滤镜效果 */
  /* 移除边框颜色设置，使用阴影效果替代 */
  /* 增强阴影效果，替代边框 */
  box-shadow: 0 0 8px rgba(120, 120, 120, 0.2), 0 0 0 1px rgba(120, 120, 120, 0.15);
}

:root.dark .offline.card-hover:hover {
  opacity: 0.9;
  /* 移除边框颜色设置，使用阴影效果替代 */
  /* 增强阴影效果，替代边框 */
  box-shadow: 0 0 8px rgba(120, 120, 120, 0.2), 0 0 0 1px rgba(120, 120, 120, 0.15);
}

/* ===== 卡片标题样式 ===== */
/* 卡片标题链接样式 - 使用自定义类而不是Tailwind类 */
.card-title-link {
  color: var(--card-title-color, #1e293b) !important; /* 亮色模式使用接近黑色的深灰色 */
  transition: color 0.2s ease;
}

:root.dark .card-title-link {
  color: var(--card-title-color-dark, #e2e8f0) !important; /* 暗色模式使用接近白色的浅灰色 */
}

/* 卡片标题链接悬停样式 */
.card-title-link:hover {
  color: var(--card-title-hover-color, #0f172a) !important; /* 亮色模式悬停时的颜色，更深 */
}

:root.dark .card-title-link:hover {
  color: var(--card-title-hover-color-dark, #f8fafc) !important; /* 暗色模式悬停时的颜色，更亮 */
}

/* 保留对Tailwind类的兼容性支持 */
.server-name a,
.text-primary-600,
a.text-primary-600 {
  color: var(--card-title-color, #1e293b) !important;
  transition: color 0.2s ease;
}

:root.dark .server-name a,
:root.dark .text-primary-600,
:root.dark a.text-primary-600,
.dark\:text-primary-400 {
  color: var(--card-title-color-dark, #e2e8f0) !important;
}

.server-name a:hover,
.text-primary-600:hover,
a.text-primary-600:hover,
.hover\:text-primary-700:hover {
  color: var(--card-title-hover-color, #0f172a) !important;
}

:root.dark .server-name a:hover,
:root.dark .text-primary-600:hover,
:root.dark a.text-primary-600:hover,
.dark\:hover\:text-primary-300:hover {
  color: var(--card-title-hover-color-dark, #f8fafc) !important;
}

/* ===== Admin页面布局过渡控制 ===== */
/* 防止页面加载时的布局闪烁 */
.theme-initializing #container,
.theme-initializing #footer-wrapper,
.theme-initializing #main-navbar,
.theme-initializing #admin-sidebar {
  transition: none !important;
}

/* 正常的过渡效果 - 仅在初始化完成后应用 */
.theme-initialized #container,
.theme-initialized #footer-wrapper {
  transition: padding-left 0.3s ease-out;
}

.theme-initialized #main-navbar {
  transition: left 0.3s ease-out;
}

.theme-initialized #admin-sidebar {
  transition: transform 0.3s ease-out;
}

/* 主题切换时禁用所有过渡 */
.theme-switching * {
  transition: none !important;
}

/* Admin布局特定样式 */
@media (min-width: 1024px) {
  /* 确保admin页面的导航栏全宽显示，不受侧边栏影响 */
  #admin-sidebar ~ #main-navbar,
  .has-admin-sidebar #main-navbar {
    left: 0 !important; /* 保持全宽，不偏移 */
    width: 100% !important;
    z-index: 50 !important; /* 使用!important确保在侧边栏之上 */
  }
}
