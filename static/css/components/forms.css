/**
 * 表单组件样式
 * @description 表单相关的样式和验证效果
 * @created 2025-08-12 - 从components.css中拆分
 */

/* ===== 环形图样式 ===== */

/* 环形图基础样式 - 小尺寸用于底部标签 */
.ring-chart {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* 中等尺寸环形图用于主要指标区域 */
.ring-chart-medium {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

/* 大尺寸环形图用于主要指标区域 */
.ring-chart-large {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

/* 环形图动画 */
.ring-chart .progress-ring,
.ring-chart-medium .progress-ring,
.ring-chart-large .progress-ring {
  transition: stroke-dashoffset 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              stroke 0.3s ease;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .ring-chart {
    width: 14px;
    height: 14px;
  }
  
  .ring-chart-medium {
    width: 20px;
    height: 20px;
  }
  
  .ring-chart-large {
    width: 28px;
    height: 28px;
  }
  
  /* 移动端布局调整 */
  .storage-traffic-metrics .grid {
    gap: 0.5rem;
  }
  
  .metric-item {
    padding: 0.5rem;
  }
}

/* ===== 数据单位样式 ===== */

/* 数据单位样式 - 确保在所有设备上正确显示 */
.metric-unit,
.metric-unit-compact {
  /* font-size removed - use Tailwind text-xs */
  opacity: 0.7;
  font-weight: 500;
  margin-left: 1px;
  display: inline;
  white-space: nowrap;
}

/* 紧凑模式单位 */
.metric-unit-compact {
  /* font-size removed - use custom text-[0.6rem] */
  margin-left: 0;
}

/* 移动端优化 */
@media (max-width: 640px) {
  .metric-unit {
    /* font-size removed - use custom text-[0.65rem] */
  }
  
  .metric-unit-compact {
    /* font-size removed - use custom text-[0.55rem] */
  }
}

/* 暗色模式下的单位显示 */
:root.dark .metric-unit,
:root.dark .metric-unit-compact {
  opacity: 0.6;
}

/* ===== 辅助功能 ===== */

/* 辅助功能 - 屏幕阅读器 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* ===== 授权管理页面样式 ===== */

/* 状态指示器 */
.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.status-active { background-color: #10b981; }
.status-inactive { background-color: #6b7280; }
.status-error { background-color: #ef4444; }
.status-warning { background-color: #f59e0b; }

/* License Key显示样式 */
.license-key-display {
  font-family: var(--font-mono);
  background: rgba(0, 0, 0, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
  /* font-size removed - use Tailwind text-sm */
}

.dark .license-key-display {
  background: rgba(255, 255, 255, 0.1);
}

/* Tab样式 */
.tab-button {
  color: #64748b;
  background: transparent;
  border: 2px solid transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.tab-button:hover {
  color: #475569;
  background: rgba(255, 255, 255, 0.5);
  border-color: rgba(99, 102, 241, 0.2);
}

.tab-button.active {
  color: #6366f1;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-color: rgba(99, 102, 241, 0.3);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.dark .tab-button {
  color: #94a3b8;
}

.dark .tab-button:hover {
  color: #cbd5e1;
  background: rgba(30, 41, 59, 0.5);
  border-color: rgba(165, 180, 252, 0.2);
}

.dark .tab-button.active {
  color: #a5b4fc;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(15, 23, 42, 0.8) 100%);
  border-color: rgba(165, 180, 252, 0.3);
  box-shadow: 0 2px 8px rgba(165, 180, 252, 0.15), 0 1px 3px rgba(0, 0, 0, 0.3);
  font-weight: 600;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* ===== 国旗图标样式 ===== */

/* 国旗容器样式 - 使用标准国旗比例 3:2 */
.country-flag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 22px; /* 增加宽度以更好地显示国旗 */
  height: 15px; /* 保持 3:2 的比例 */
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  border-radius: 2px;
  background-color: #f1f5f9; /* 浅灰色背景，防止透明区域 */
}

:root.dark .country-flag {
  background-color: #334155; /* 暗色模式下的背景 */
}

/* 国旗图片样式 - 保持原始比例 */
.country-flag .flag-img {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 使用 contain 而不是 cover，保持完整的国旗显示 */
  display: block;
}

/* Tabler Icons 备用图标 */
.country-flag .ti {
  /* font-size removed - use Tailwind text-sm */
  color: var(--text-secondary, #64748b);
}

:root.dark .country-flag .ti {
  color: var(--text-secondary-dark, #94a3b8);
}

/* 特殊国旗比例调整 */
/* 瑞士和梵蒂冈使用正方形国旗 */
.country-flag[title="CH"],
.country-flag[title="VA"] {
  width: 16px;
  height: 16px;
}

/* 尼泊尔的特殊形状需要特别处理 */
.country-flag[title="NP"] {
  background-color: transparent;
}

/* ===== 网络测速小工具样式 ===== */

/* Speedtest Container */
.speedtest-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* ===== 地区分布样式 ===== */

/* 地区分布网格布局 - 仅关注布局优化 */
.region-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(72px, 80px));
  gap: 6px;
  max-height: 96px;
  overflow-y: auto;
  justify-content: center;
  align-items: start;
  padding: 8px 4px;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  .region-stats-grid {
    grid-template-columns: repeat(auto-fill, minmax(68px, 75px));
    gap: 4px;
    max-height: 88px;
    padding: 6px 2px;
  }
}

@media (max-width: 480px) {
  .region-stats-grid {
    grid-template-columns: repeat(auto-fill, minmax(64px, 70px));
    gap: 3px;
    max-height: 84px;
  }
}

/* 地区标签 - 扁平样式, 使用CSS变量 */
.region-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
  color: var(--secondary-text-color);
  cursor: pointer;
  user-select: none;
  transition: all var(--transition-normal) ease;
  text-decoration: none;
}

.region-tag:hover {
  background-color: var(--hover-bg-color);
  border-color: var(--light-border-hover-color);
  color: var(--text-color);
}

.dark .region-tag:hover {
  border-color: var(--dark-border-hover-color);
}

.region-tag.active {
  background-color: var(--accent-light-color);
  border-color: var(--accent-color);
  color: var(--accent-color);
  font-weight: 600;
}

/* 地区标签内容 */
.region-tag-flag {
  flex-shrink: 0;
  width: 14px;
  height: 10px;
  border-radius: 2px;
  object-fit: cover;
  margin-right: 4px;
}

.region-tag-code {
  font-size: 11px;
  font-weight: 600;
  line-height: 1;
  margin: 0 3px;
  min-width: 16px;
  text-align: center;
}

.region-tag-count {
  font-size: 10px;
  font-weight: 700;
  border-radius: 10px;
  padding: 2px 6px;
  min-width: 18px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  background-color: var(--highlight-color);
  color: var(--secondary-text-color);
  transition: all var(--transition-normal) ease;
}

.region-tag.active .region-tag-count {
    background-color: var(--accent-color);
    color: #ffffff;
}

/* 简化的状态和加载样式 */
.region-stats-loading {
  grid-column: 1 / -1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 64px;
  font-size: 12px;
  color: var(--secondary-text-color);
}

.region-stats-loading i {
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

.region-stats-empty {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80px;
  font-size: 12px;
  text-align: center;
  color: var(--secondary-text-color);
}

.region-stats-empty i {
  font-size: 20px;
  margin-bottom: 8px;
  opacity: 0.6;
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .region-tag {
    min-height: 36px;
    padding: 8px 12px;
  }
}

/* 旋转动画 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}