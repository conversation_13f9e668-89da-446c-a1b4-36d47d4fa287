/* 管理后台侧边栏样式 */

/* CSS变量定义 */
.admin-sidebar {
    --sidebar-width: 16rem; /* 256px = w-64 */
    --sidebar-collapsed-width: 4rem; /* 64px = w-16 */
    --sidebar-transition: 0.2s ease;
    --sidebar-icon-size: 1.125rem;
    --sidebar-border-color: rgb(226 232 240);
    --sidebar-dark-border-color: rgb(51 65 85);
    --sidebar-bg: white;
    --sidebar-dark-bg: rgb(15 23 42);
    --sidebar-text-color: rgb(51 65 85);
    --sidebar-dark-text-color: rgb(203 213 225);
    --sidebar-hover-bg: rgb(241 245 249);
    --sidebar-dark-hover-bg: rgb(30 41 59);
    --sidebar-active-bg: rgb(239 246 255);
    --sidebar-dark-active-bg: rgb(30 58 138);
    --sidebar-active-border: rgb(59 130 246);
}

/* 侧边栏折叠样式 */
.sidebar-collapsed {
    width: var(--sidebar-collapsed-width) !important;
}

.sidebar-collapsed .sidebar-text {
    opacity: 0;
    visibility: hidden;
    width: 0;
    overflow: hidden;
    white-space: nowrap;
    transition: all var(--sidebar-transition);
}

.sidebar-collapsed .sidebar-badge {
    opacity: 0;
    visibility: hidden;
    width: 0;
    overflow: hidden;
    transition: all var(--sidebar-transition);
}

.sidebar-collapsed .sidebar-item {
    justify-content: center;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.sidebar-collapsed .sidebar-icon {
    margin: 0;
    /* 折叠时放大图标 */
    width: 1.5rem !important;
    height: 1.5rem !important;
    font-size: 1.5rem !important;
}

.sidebar-collapsed .sidebar-toggle-icon {
    transform: rotate(180deg);
}

/* 折叠时隐藏搜索框 */
.sidebar-collapsed #sidebar-search-container {
    display: none;
}

/* 折叠时隐藏分组标题 */
.sidebar-collapsed .sidebar-section-title {
    opacity: 0;
    visibility: hidden;
    height: 0;
    padding: 0;
    margin: 0;
    overflow: hidden;
}

/* Tooltip 样式 */
.sidebar-tooltip {
    display: none;
}

.sidebar-collapsed .sidebar-tooltip {
    display: block !important;
}

/* 图标居中样式 */
.sidebar-collapsed .sidebar-logo {
    justify-content: center;
}

/* 折叠时隐藏logo文字 */
.sidebar-collapsed .sidebar-logo h2 {
    display: none;
}

/* 折叠时调整头部布局 */
.sidebar-collapsed .sidebar-logo .flex-shrink-0 {
    margin: 0 auto;
}

/* 折叠按钮样式 */
.sidebar-collapsed #sidebar-toggle {
    position: absolute;
    right: -0.75rem;
    background: var(--sidebar-bg);
    border: 1px solid var(--sidebar-border-color);
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

.dark .sidebar-collapsed #sidebar-toggle {
    background: var(--sidebar-dark-bg);
    border-color: var(--sidebar-dark-border-color);
}

/* 平滑过渡效果 */
#admin-sidebar {
    transition: width var(--sidebar-transition);
}

/* 折叠时调整导航区域，避免横向滚动条 */
.sidebar-collapsed nav {
    overflow-x: hidden !important;
    overflow-y: auto;
}

/* 折叠时确保列表不超出宽度 */
.sidebar-collapsed nav ul {
    width: var(--sidebar-collapsed-width);
    overflow: hidden;
}

.sidebar-text,
.sidebar-icon,
.sidebar-toggle-icon {
    transition: all var(--sidebar-transition);
}

/* 菜单项基础样式 */
.sidebar-item {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-item:hover {
    transform: translateX(2px);
}

/* 统一图标大小 */
.sidebar-icon {
    width: var(--sidebar-icon-size);
    height: var(--sidebar-icon-size);
    font-size: var(--sidebar-icon-size) !important;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    /* 确保图标总是居中 */
    text-align: center;
}

/* 高亮当前页面样式优化 */
.sidebar-item.active {
    background: var(--sidebar-active-bg);
    border-left: 2px solid var(--sidebar-active-border);
    font-weight: 500;
}

.dark .sidebar-item.active {
    background: var(--sidebar-dark-active-bg);
    border-left-color: var(--sidebar-active-border);
}

/* 分组标题样式 */
.sidebar-section-title {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--sidebar-text-color);
    opacity: 0.6;
}

.dark .sidebar-section-title {
    color: var(--sidebar-dark-text-color);
}

/* 移动端样式优化 */
@media (max-width: 1023px) {
    #admin-sidebar {
        position: fixed;
        left: 0;
        top: 0 !important;
        height: 100% !important;
        z-index: 50;
        transform: translateX(-100%);
        transition: transform var(--sidebar-transition);
    }
    
    /* 移动端侧边栏展开时显示 */
    body.mobile-sidebar-open #admin-sidebar {
        transform: translateX(0);
    }

    /* 移动端菜单按钮优化 */
    #mobile-toggle-sidebar {
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        transition: all 0.3s ease;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    /* 滚动时的按钮样式 */
    #mobile-toggle-sidebar.scrolled {
        transform: scale(0.9);
        opacity: 0.9;
    }
}

/* 平板适配 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .admin-sidebar {
        --sidebar-width: 12rem;
    }
}

/* Tooltip 统一样式 */
.sidebar-tooltip {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    transition: opacity 0.2s, visibility 0.2s;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 滚动条样式 */
.admin-sidebar nav {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

.admin-sidebar nav::-webkit-scrollbar {
    width: 6px;
}

.admin-sidebar nav::-webkit-scrollbar-track {
    background: transparent;
}

.admin-sidebar nav::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.3);
    border-radius: 3px;
}

.admin-sidebar nav::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.5);
}

/* 暗色模式滚动条 */
.dark .admin-sidebar nav {
    scrollbar-color: rgba(71, 85, 105, 0.5) transparent;
}

.dark .admin-sidebar nav::-webkit-scrollbar-thumb {
    background-color: rgba(71, 85, 105, 0.5);
}

.dark .admin-sidebar nav::-webkit-scrollbar-thumb:hover {
    background-color: rgba(71, 85, 105, 0.7);
}

/* 动画增强 */
@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.sidebar-item {
    animation: slideIn 0.3s ease-out;
    animation-fill-mode: both;
}

.sidebar-item:nth-child(1) { animation-delay: 0.05s; }
.sidebar-item:nth-child(2) { animation-delay: 0.1s; }
.sidebar-item:nth-child(3) { animation-delay: 0.15s; }
.sidebar-item:nth-child(4) { animation-delay: 0.2s; }
.sidebar-item:nth-child(5) { animation-delay: 0.25s; }
.sidebar-item:nth-child(6) { animation-delay: 0.3s; }
.sidebar-item:nth-child(7) { animation-delay: 0.35s; }
.sidebar-item:nth-child(8) { animation-delay: 0.4s; }
.sidebar-item:nth-child(9) { animation-delay: 0.45s; }
.sidebar-item:nth-child(10) { animation-delay: 0.5s; }

/* 折叠动画优化 */
.sidebar-collapsed .sidebar-item {
    animation: none;
}

/* 响应式断点优化 */
@media (min-width: 640px) and (max-width: 767px) {
    /* 小平板优化 */
    #mobile-toggle-sidebar {
        top: 1rem;
        left: 1rem;
    }
}

/* 访问性增强 */
.sidebar-item:focus {
    outline: 2px solid var(--sidebar-active-border);
    outline-offset: 2px;
}

.sidebar-item:focus:not(:focus-visible) {
    outline: none;
}

/* 移动端手势反馈 */
@media (max-width: 1023px) {
    #admin-sidebar.swiping {
        transition: none !important;
    }
    
    /* 添加滑动提示 */
    #admin-sidebar::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 60px;
        background: rgba(59, 130, 246, 0.3);
        border-radius: 0 3px 3px 0;
        opacity: 0;
        transition: opacity 0.3s;
    }
    
    #admin-sidebar:not(.-translate-x-full)::after {
        opacity: 1;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .sidebar-item {
        min-height: 44px; /* iOS推荐的最小触摸目标 */
    }
    
    /* 增强触摸反馈 */
    .sidebar-item:active {
        background-color: var(--sidebar-hover-bg);
        transform: scale(0.98);
    }
    
    .dark .sidebar-item:active {
        background-color: var(--sidebar-dark-hover-bg);
    }
}

/* 主内容区域自适应样式 */
@media (min-width: 1024px) {
    /* 正常状态下，主内容区域留出侧边栏宽度 */
    body:not(.sidebar-collapsed) .admin-content,
    body:not(.sidebar-collapsed) .admin-layout-container main.flex-1,
    body:not(.sidebar-collapsed) .admin-layout-container .flex-1.space-y-6 {
        margin-left: var(--sidebar-width);
        transition: margin-left var(--sidebar-transition);
    }
    
    /* 折叠状态下，主内容区域留出折叠宽度 */
    body.sidebar-collapsed .admin-content,
    body.sidebar-collapsed .admin-layout-container main.flex-1,
    body.sidebar-collapsed .admin-layout-container .flex-1.space-y-6 {
        margin-left: var(--sidebar-collapsed-width);
        transition: margin-left var(--sidebar-transition);
    }
    
    /* 如果使用padding而不是margin的布局 */
    body:not(.sidebar-collapsed) .lg\:pl-64 {
        padding-left: var(--sidebar-width) !important;
        transition: padding-left var(--sidebar-transition);
    }
    
    body.sidebar-collapsed .lg\:pl-64 {
        padding-left: var(--sidebar-collapsed-width) !important;
        transition: padding-left var(--sidebar-transition);
    }
    
    /* 🛡️ 防护规则：确保 appbar 及其子元素不受 sidebar 样式影响 */
    #main-navbar,
    #main-navbar *,
    .admin-appbar,
    .admin-appbar * {
        margin-left: 0 !important;
        padding-left: 0 !important;
        transition: none !important; /* 禁用可能的过渡动画 */
    }
}

/* 移动端不需要调整主内容区域 */
@media (max-width: 1023px) {
    .admin-content,
    main.flex-1,
    .flex-1.space-y-6 {
        margin-left: 0;
    }
}

/* 打印时隐藏侧边栏 */
@media print {
    #admin-sidebar,
    #mobile-toggle-sidebar,
    #sidebar-overlay {
        display: none !important;
    }
    
    /* 打印时重置主内容区域边距 */
    .admin-content,
    main.flex-1,
    .flex-1.space-y-6 {
        margin-left: 0 !important;
    }
}