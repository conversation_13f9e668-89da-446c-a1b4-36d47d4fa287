/* 指标展示相关样式 - 从monitor.css拆分 */
/* 包含：分类卡片、目标卡片、节点卡片、操作按钮等 */

/* 分类卡片样式 - 重新设计 */
.category-card {
    width: 12rem;
    height: 10rem;
    background-color: white;
    border: 2px solid #e2e8f0;
    border-radius: 0.75rem;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.category-card:hover {
    box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.06);
    transform: translateY(-2px);
    border-color: #cbd5e1;
}

.category-card:active {
    transform: scale(0.98);
}

.category-card.expanded {
    border-color: #8b5cf6;
    box-shadow: 0 8px 12px -2px rgba(139, 92, 246, 0.12), 0 4px 6px -2px rgba(139, 92, 246, 0.08);
}

html.dark .category-card {
    background-color: #1e293b;
    border-color: #334155;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.3), 0 1px 2px -1px rgba(0, 0, 0, 0.2);
}

html.dark .category-card:hover {
    border-color: #475569;
    box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

html.dark .category-card.expanded {
    border-color: #8b5cf6;
    box-shadow: 0 8px 12px -2px rgba(139, 92, 246, 0.25), 0 4px 6px -2px rgba(139, 92, 246, 0.15);
}

/* 分类卡片内容区 */
.category-content {
    flex: 1;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #1e293b;
    text-align: center;
}

html.dark .category-content {
    color: #e2e8f0;
}

/* 分类卡片操作区 */
.category-actions {
    padding: 0.5rem 0.75rem;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

html.dark .category-actions {
    background-color: #0f172a;
    border-top-color: #334155;
}

.category-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.category-title {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.375rem;
    line-height: 1.3;
    text-align: center;
    word-break: break-word;
}

.category-count {
    font-size: 0.75rem;
    opacity: 0.9;
    background-color: #f1f5f9;
    color: #64748b;
    padding: 0.25rem 0.625rem;
    border-radius: 9999px;
    white-space: nowrap;
}

html.dark .category-count {
    background-color: #334155;
    color: #94a3b8;
}

/* 分类卡片展开指示器 */
.expand-indicator {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(148, 163, 184, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.expand-indicator.expanded {
    transform: rotate(180deg);
    background-color: rgba(139, 92, 246, 0.1);
}

html.dark .expand-indicator {
    background-color: rgba(148, 163, 184, 0.2);
}

html.dark .expand-indicator.expanded {
    background-color: rgba(139, 92, 246, 0.2);
}

/* 重新设计的操作按钮 */
.action-btn {
    width: 2rem;
    height: 2rem;
    background-color: transparent;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.action-btn:hover {
    background-color: #f1f5f9;
    border-color: #cbd5e1;
    color: #374151;
    transform: translateY(-1px);
}

.action-btn:active {
    transform: translateY(0);
}

html.dark .action-btn {
    border-color: #475569;
    color: #94a3b8;
}

html.dark .action-btn:hover {
    background-color: #334155;
    border-color: #64748b;
    color: #cbd5e1;
}

/* 目标卡片样式 - 重新设计 */
.target-card {
    width: 11rem;
    height: 8.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.target-card:hover {
    box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.06);
    transform: translateY(-2px);
}

.target-card:active {
    transform: scale(0.98);
}

.target-card.collapsed {
    background-color: white;
    border: 2px solid #e2e8f0;
    color: #1e293b;
}

.target-card.expanded {
    background-color: white;
    border: 2px solid #3b82f6;
    color: #1e293b;
    box-shadow: 0 8px 12px -2px rgba(59, 130, 246, 0.12), 0 4px 6px -2px rgba(59, 130, 246, 0.08);
}

html.dark .target-card.collapsed {
    background-color: #1e293b;
    border: 2px solid #334155;
    color: #e2e8f0;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.3), 0 1px 2px -1px rgba(0, 0, 0, 0.2);
}

html.dark .target-card.expanded {
    background-color: #1e293b;
    border: 2px solid #3b82f6;
    color: #e2e8f0;
    box-shadow: 0 8px 12px -2px rgba(59, 130, 246, 0.25), 0 4px 6px -2px rgba(59, 130, 246, 0.15);
}

/* 目标卡片内容区 */
.target-content {
    flex: 1;
    padding: 0.875rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

/* 目标卡片操作区 */
.target-actions {
    padding: 0.4rem 0.6rem;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: center;
    gap: 0.4rem;
}

.target-card.expanded .target-actions {
    background-color: #eff6ff;
    border-top-color: #dbeafe;
}

html.dark .target-actions {
    background-color: #0f172a;
    border-top-color: #334155;
}

html.dark .target-card.expanded .target-actions {
    background-color: rgba(30, 58, 138, 0.1);
    border-top-color: rgba(59, 130, 246, 0.2);
}

.target-title {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.375rem;
    line-height: 1.3;
    text-align: center;
    word-break: break-word;
}

.target-host {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-bottom: 0.375rem;
    word-break: break-all;
    line-height: 1.4;
    text-align: center;
}

.target-count {
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
}

.target-count.collapsed {
    background-color: #f1f5f9;
    color: #64748b;
}

.target-count.expanded {
    background-color: #dbeafe;
    color: #1e40af;
}

html.dark .target-count.collapsed {
    background-color: #334155;
    color: #94a3b8;
}

html.dark .target-count.expanded {
    background-color: rgba(59, 130, 246, 0.2);
    color: #60a5fa;
}

/* 目标卡片展开指示器 */
.target-expand-indicator {
    position: absolute;
    top: 0.6rem;
    right: 0.6rem;
    width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(148, 163, 184, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.target-expand-indicator.expanded {
    transform: rotate(180deg);
    background-color: rgba(59, 130, 246, 0.1);
}

html.dark .target-expand-indicator {
    background-color: rgba(148, 163, 184, 0.2);
}

html.dark .target-expand-indicator.expanded {
    background-color: rgba(59, 130, 246, 0.2);
}

/* 目标操作按钮 */
.target-action-btn {
    width: 1.75rem;
    height: 1.75rem;
    background-color: transparent;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.target-action-btn:hover {
    background-color: #f1f5f9;
    border-color: #cbd5e1;
    color: #374151;
    transform: translateY(-1px);
}

.target-action-btn:active {
    transform: translateY(0);
}

.target-action-btn.save-btn {
    border-color: #fed7aa;
    color: #ea580c;
}

.target-action-btn.save-btn:hover {
    background-color: #fff7ed;
    border-color: #fb923c;
    color: #c2410c;
}

html.dark .target-action-btn {
    border-color: #475569;
    color: #94a3b8;
}

html.dark .target-action-btn:hover {
    background-color: #334155;
    border-color: #64748b;
    color: #cbd5e1;
}

html.dark .target-action-btn.save-btn {
    border-color: #fb923c;
    color: #fb923c;
}

html.dark .target-action-btn.save-btn:hover {
    background-color: rgba(251, 146, 60, 0.1);
    border-color: #fed7aa;
    color: #fed7aa;
}

/* 节点卡片样式 */
.node-card {
    width: 100%;
    height: 6rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.node-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    transform: translateY(-0.125rem);
}

.node-card:active {
    transform: scale(0.95);
}

.node-card.unselected {
    background-color: white;
    border: 2px solid #e2e8f0;
    color: #1e293b;
}

.node-card.selected {
    background-color: #f1f5f9;
    border: 2px solid #cbd5e1;
    color: #1e293b;
}

html.dark .node-card.unselected {
    background-color: #1e293b;
    border: 2px solid #334155;
    color: #e2e8f0;
}

html.dark .node-card.selected {
    background-color: #0f172a;
    border: 2px solid #475569;
    color: #e2e8f0;
}

.node-content {
    padding: 0.75rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    text-align: left;
    overflow: hidden;
    position: relative;
}

/* 勾选框定位到右上角 */
.node-checkbox {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    width: 1rem;
    height: 1rem;
    background-color: white;
    border: 2px solid #374151;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
    cursor: pointer;
    z-index: 10;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.node-checkbox:checked {
    background-color: #059669;
    border-color: #059669;
    color: white;
}

.node-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
}

/* 深色模式下的勾选框 */
html.dark .node-checkbox {
    background-color: #1f2937;
    border-color: #6b7280;
}

html.dark .node-checkbox:checked {
    background-color: #047857;
    border-color: #047857;
}

/* 节点标题行 - 图标和标题水平排列 */
.node-title-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    width: calc(100% - 2rem); /* 为右上角勾选框留出空间 */
}

.node-icon {
    font-size: 1rem;
    opacity: 0.9;
    flex-shrink: 0;
}

.node-title {
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1.25;
    flex: 1;
    min-width: 0; /* 允许文本截断 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 国旗图标样式 */
.node-flag {
    width: 1rem;
    height: 0.75rem;
    border-radius: 0.125rem;
    object-fit: cover;
    flex-shrink: 0;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.node-status {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    padding: 0.125rem 0.375rem;
    border-radius: 9999px;
}

.node-status.unselected {
    background-color: #e2e8f0;
    color: #64748b;
}

.node-status.selected {
    background-color: #cbd5e1;
    color: #475569;
}

html.dark .node-status.unselected {
    background-color: #334155;
    color: #94a3b8;
}

html.dark .node-status.selected {
    background-color: #475569;
    color: #cbd5e1;
}

/* 离线节点状态样式 */
.offline-node {
    opacity: 0.7;
    filter: grayscale(30%);
}

/* 高对比度状态标签样式 */
.node-status.status-online {
    background-color: #059669;  /* green-600 */
    color: white;
    font-weight: 500;
}

.node-status.status-offline {
    background-color: #dc2626;  /* red-600 */
    color: white;
    font-weight: 500;
}

.node-status.selected.status-online {
    background-color: #059669;  /* green-600 - 保持绿色背景 */
    color: white;
    font-weight: 500;
}

.node-status.selected.status-offline {
    background-color: #dc2626;  /* red-600 - 保持红色背景 */
    color: white;
    font-weight: 500;
}

/* 深色模式下的状态标签 */
html.dark .node-status.status-online {
    background-color: #047857;  /* green-700 */
    color: white;
}

html.dark .node-status.status-offline {
    background-color: #b91c1c;  /* red-700 */
    color: white;
}

/* 深色模式下选中状态的状态标签 */
html.dark .node-status.selected.status-online {
    background-color: #047857;  /* green-700 - 保持绿色背景 */
    color: white;
    font-weight: 500;
}

html.dark .node-status.selected.status-offline {
    background-color: #b91c1c;  /* red-700 - 保持红色背景 */
    color: white;
    font-weight: 500;
}

/* 修复深色模式下的文本对比度 */
html.dark .node-card.unselected .node-title,
html.dark .node-card.unselected .node-region {
    color: #cbd5e1;
}

html.dark .target-card.collapsed .target-title,
html.dark .target-card.collapsed .target-host {
    color: #cbd5e1;
}

/* T006: 性能优化 */
.category-card, .target-card, .node-card {
    /* 启用硬件加速 */
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* 减少重绘 */
.category-card:hover, .target-card:hover, .node-card:hover {
    will-change: transform, box-shadow;
}

.category-card:not(:hover), .target-card:not(:hover), .node-card:not(:hover) {
    will-change: auto;
}

/* T006: 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    /* 触摸设备上增大点击区域 */
    .action-btn, .target-action-btn {
        min-width: 44px;
        min-height: 44px;
    }

    /* 移除hover效果，使用active效果 */
    .category-card:hover, .target-card:hover, .node-card:hover {
        transform: none;
        box-shadow: inherit;
    }

    .category-card:active, .target-card:active, .node-card:active {
        transform: scale(0.98);
        transition-duration: 0.1s;
    }
}

/* T006: 高对比度模式支持 */
@media (prefers-contrast: high) {
    .category-card, .target-card, .node-card {
        border-width: 3px;
    }

    .category-card.expanded, .target-card.expanded {
        border-width: 4px;
    }
}

/* T006: 打印样式优化 */
@media print {
    .action-btn,
    .target-action-btn {
        display: none !important;
    }

    .category-card, .target-card, .node-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
}