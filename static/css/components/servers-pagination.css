/**
 * 服务器分页组件样式
 * @description 专门为服务器列表分页功能设计的样式
 * @created 2025-01-26
 * @updated 2025-01-26 - 添加移动端卡片支持
 */

/* 分页容器基础样式 */
.servers-pagination-container {
    min-height: 480px; /* 最小高度确保良好的视觉效果 */
}

/* 移动端卡片拖拽样式 - 仅保留SortableJS需要的样式 */
.mobile-server-card.sortable-ghost {
    opacity: 0.5;
    transform: scale(0.95);
}

.mobile-server-card.sortable-chosen {
    transform: scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.mobile-server-card.sortable-drag {
    transform: rotate(2deg);
}

/* 分页按钮基础样式 */
.pagination-btn {
    @apply px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 active:scale-95 flex items-center gap-1;
}

.pagination-btn-default {
    @apply bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 border border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600;
}

.pagination-btn-active {
    @apply bg-blue-500 text-white shadow-md;
}

.pagination-btn:disabled {
    @apply opacity-50 cursor-not-allowed;
}

.pagination-btn:disabled:hover {
    @apply bg-white dark:bg-slate-700;
}

/* 服务器行操作按钮基础样式 */
.server-action-btn {
    @apply w-8 h-8 bg-white/80 dark:bg-slate-700/60 text-slate-600 dark:text-slate-300 rounded-lg 
           focus:outline-none focus:ring-2 transition-all duration-200 backdrop-blur-sm 
           border border-slate-200/60 dark:border-slate-600/60 active:scale-95 
           flex items-center justify-center;
}

.server-action-btn-install {
    @apply hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 focus:ring-blue-500/50;
}

.server-action-btn-reset {
    @apply hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:text-purple-600 dark:hover:text-purple-400 focus:ring-purple-500/50;
}

.server-action-btn-edit {
    @apply hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-600 dark:hover:text-green-400 focus:ring-green-500/50;
}

.server-action-btn-delete {
    @apply hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400 focus:ring-red-500/50;
}

/* 分页信息样式 */
.pagination-info {
    @apply text-sm text-slate-500 dark:text-slate-400 flex items-center gap-2;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .servers-pagination-container {
        min-height: 360px;
    }
    
    .pagination-btn {
        @apply px-2 py-1.5 text-xs;
    }
    
    .server-action-btn {
        @apply w-7 h-7;
    }
    
    .pagination-info {
        @apply text-xs;
    }
    
    /* 移动端分页按钮优化 */
    .pagination-controls button {
        min-width: 40px;
        min-height: 40px;
    }
    
    /* 移动端操作按钮间距优化 */
    .mobile-server-card .action-buttons {
        gap: 0.5rem;
    }
}

/* 响应式分页按钮优化 */
@media (max-width: 640px) {
    /* 移动端页码按钮优化 */
    #page-numbers button {
        width: 40px !important;
        height: 40px !important;
        /* font-size removed - use Tailwind text-sm */
    }
    
    /* 移动端分页控制按钮优化 */
    .pagination-controls > div button {
        width: 40px !important;
        height: 40px !important;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .mobile-server-card {
        margin-left: -0.5rem;
        margin-right: -0.5rem;
        border-radius: 1rem;
    }
    
    .mobile-server-card .action-buttons button {
        /* font-size removed - use Tailwind text-xs */
        padding: 0.5rem;
        height: 2.5rem;
    }
    
    .mobile-server-card .action-buttons button span {
        display: none; /* 超小屏幕隐藏按钮文字，只显示图标 */
    }
    
    .mobile-server-card .action-buttons button .ti {
        /* font-size removed - use Tailwind text-base */
    }
}

/* 加载状态样式 */
.servers-loading {
    @apply opacity-50 pointer-events-none;
}

.servers-loading tbody {
    @apply animate-pulse;
}

.servers-loading .mobile-server-card {
    @apply animate-pulse;
}

/* 空状态样式优化 */
.servers-empty-state {
    @apply py-16;
}

/* 确保拖拽时的视觉反馈 */
.sortable-ghost {
    @apply opacity-50;
}

.sortable-chosen {
    @apply scale-105;
}

.sortable-drag {
    @apply rotate-1;
}

/* 已移除桌面端表格hover效果 - 现在通过Tailwind实现 */

/* 分页按钮组容器优化 */
.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

@media (min-width: 640px) {
    .pagination-controls {
        gap: 0.5rem;
    }
}

/* 页码数字按钮特殊样式 */
#page-numbers {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    overflow-x: auto;
    @apply scrollbar-none;
}

/* 焦点状态优化 */
button:focus-visible,
a:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* 活动状态反馈 */
button:active,
a:active {
    transform: scale(0.95);
}

/* 禁用状态优化 */
button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

button:disabled:hover {
    transform: none;
}

/* 表格行拖拽样式 - 现在通过Tailwind实现，保留必要的SortableJS类名 */

/* 确保下拉菜单在最顶层显示 */
.server-dropdown {
    position: relative;
}

.server-dropdown > div[id^="dropdown-"] {
    z-index: 9999 !important;
    position: absolute !important;
}

/* 表格容器overflow设置已迁移到HTML中的Tailwind类 */
