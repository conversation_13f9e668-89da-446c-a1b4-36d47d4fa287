/* AI Dashboard Component Styles */

/* 主容器 */
.ai-dashboard {
    background: transparent;
    min-height: 400px;
}

/* 头部控制面板 */
.ai-dashboard-header {
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.1) 0%, rgba(99, 102, 241, 0.05) 100%);
    border: 2px solid rgba(168, 85, 247, 0.2);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    backdrop-filter: blur(8px);
}

.ai-dashboard-title {
    margin-bottom: 16px;
}

.ai-dashboard-title h2 {
    display: flex;
    align-items: center;
    gap: 12px;
    /* font-size removed - use Tailwind text-2xl */
    font-weight: 700;
    color: rgb(51, 65, 85);
    margin: 0 0 8px 0;
}

.dark .ai-dashboard-title h2 {
    color: rgb(226, 232, 240);
}

.ai-dashboard-title h2 i {
    color: rgb(168, 85, 247);
    /* font-size removed - use custom text-[1.75rem] */
}

.ai-dashboard-title p {
    /* font-size removed - use Tailwind text-sm */
    color: rgb(100, 116, 139);
    margin: 0;
}

.dark .ai-dashboard-title p {
    color: rgb(148, 163, 184);
}

.ai-dashboard-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: center;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-group label {
    /* font-size removed - use Tailwind text-sm */
    font-weight: 500;
    color: rgb(71, 85, 105);
    white-space: nowrap;
}

.dark .control-group label {
    color: rgb(203, 213, 225);
}

.form-control {
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 8px;
    color: rgb(51, 65, 85);
    /* font-size removed - use Tailwind text-sm */
    transition: all 0.2s ease;
}

.dark .form-control {
    background: rgba(51, 65, 85, 0.6);
    border-color: rgba(71, 85, 105, 0.6);
    color: rgb(226, 232, 240);
}

.form-control:focus {
    outline: none;
    border-color: rgba(168, 85, 247, 0.6);
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    /* font-size removed - use Tailwind text-sm */
    font-weight: 500;
    border-radius: 12px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn:hover {
    transform: scale(1.05);
}

.btn:active {
    transform: scale(0.95);
}

.btn-primary {
    background: linear-gradient(135deg, rgb(168, 85, 247) 0%, rgb(147, 51, 234) 100%);
    color: white;
    box-shadow: 0 4px 6px rgba(168, 85, 247, 0.2);
}

.btn-primary:hover {
    background: linear-gradient(135deg, rgb(147, 51, 234) 0%, rgb(126, 34, 206) 100%);
    box-shadow: 0 6px 8px rgba(168, 85, 247, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, rgb(100, 116, 139) 0%, rgb(71, 85, 105) 100%);
    color: white;
    box-shadow: 0 4px 6px rgba(100, 116, 139, 0.2);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, rgb(71, 85, 105) 0%, rgb(51, 65, 85) 100%);
    box-shadow: 0 6px 8px rgba(100, 116, 139, 0.3);
}

.btn-outline {
    background: rgba(255, 255, 255, 0.8);
    color: rgb(71, 85, 105);
    border: 2px solid rgba(226, 232, 240, 0.8);
}

.dark .btn-outline {
    background: rgba(51, 65, 85, 0.6);
    color: rgb(203, 213, 225);
    border-color: rgba(71, 85, 105, 0.6);
}

.btn-outline:hover {
    background: rgba(168, 85, 247, 0.1);
    border-color: rgba(168, 85, 247, 0.3);
    color: rgb(168, 85, 247);
}

.btn-large {
    padding: 12px 24px;
    /* font-size removed - use Tailwind text-base */
}

/* 主要内容区域 */
.ai-dashboard-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* 概览卡片 */
.ai-overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
}

.ai-card {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(8px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .ai-card {
    background: rgba(51, 65, 85, 0.6);
    border-color: rgba(71, 85, 105, 0.6);
}

.ai-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.ai-card-header h3 {
    /* font-size removed - use Tailwind text-base */
    font-weight: 600;
    color: rgb(51, 65, 85);
    margin: 0;
}

.dark .ai-card-header h3 {
    color: rgb(226, 232, 240);
}

.ai-card-header i {
    color: rgb(168, 85, 247);
    /* font-size removed - use Tailwind text-xl */
}

.ai-card-content {
    text-align: center;
}

.ai-metric-large {
    /* font-size removed - use Tailwind text-3xl */
    font-weight: 700;
    color: rgb(51, 65, 85);
    margin-bottom: 4px;
    font-family: var(--font-mono);
}

.dark .ai-metric-large {
    color: rgb(226, 232, 240);
}

.ai-metric-label {
    /* font-size removed - use Tailwind text-sm */
    color: rgb(100, 116, 139);
    margin-bottom: 8px;
}

.dark .ai-metric-label {
    color: rgb(148, 163, 184);
}

.ai-metric-details {
    /* font-size removed - use Tailwind text-xs */
    color: rgb(148, 163, 184);
}

.dark .ai-metric-details {
    color: rgb(100, 116, 139);
}

.status-item {
    display: inline-block;
    margin-right: 12px;
}

/* 状态类 */
.status-online {
    color: rgb(34, 197, 94) !important;
}

.status-offline {
    color: rgb(239, 68, 68) !important;
}

.score-excellent {
    color: rgb(34, 197, 94) !important;
}

.score-good {
    color: rgb(59, 130, 246) !important;
}

.score-warning {
    color: rgb(245, 158, 11) !important;
}

.score-critical {
    color: rgb(239, 68, 68) !important;
}

/* AI分析部分 */
.ai-analysis-section,
.ai-recommendations-section {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    padding: 24px;
    backdrop-filter: blur(8px);
}

.dark .ai-analysis-section,
.dark .ai-recommendations-section {
    background: rgba(51, 65, 85, 0.6);
    border-color: rgba(71, 85, 105, 0.6);
}

.ai-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.ai-section-header h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    /* font-size removed - use Tailwind text-lg */
    font-weight: 600;
    color: rgb(51, 65, 85);
    margin: 0;
}

.dark .ai-section-header h3 {
    color: rgb(226, 232, 240);
}

.ai-section-header h3 i {
    color: rgb(168, 85, 247);
}

.ai-section-controls {
    display: flex;
    gap: 8px;
}

.ai-analysis-content {
    min-height: 200px;
}

.ai-analysis-placeholder {
    text-align: center;
    padding: 48px 24px;
}

.ai-analysis-placeholder i {
    /* font-size removed - use Tailwind text-5xl */
    color: rgb(168, 85, 247);
    margin-bottom: 16px;
    display: block;
}

.ai-analysis-placeholder h4 {
    /* font-size removed - use Tailwind text-xl */
    font-weight: 600;
    color: rgb(51, 65, 85);
    margin: 0 0 8px 0;
}

.dark .ai-analysis-placeholder h4 {
    color: rgb(226, 232, 240);
}

.ai-analysis-placeholder p {
    color: rgb(100, 116, 139);
    margin: 0 0 24px 0;
}

.dark .ai-analysis-placeholder p {
    color: rgb(148, 163, 184);
}

/* 报告显示 */
.ai-report-summary {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
    border-radius: 8px;
    padding: 20px;
}

.ai-report-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.ai-report-item {
    background: rgba(255, 255, 255, 0.6);
    border-radius: 8px;
    padding: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
}

.dark .ai-report-item {
    background: rgba(71, 85, 105, 0.3);
    border-color: rgba(100, 116, 139, 0.3);
}

.ai-report-item h4 {
    /* font-size removed - use Tailwind text-base */
    font-weight: 600;
    color: rgb(51, 65, 85);
    margin: 0 0 12px 0;
}

.dark .ai-report-item h4 {
    color: rgb(226, 232, 240);
}

.ai-analysis-result {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.result-metric {
    display: flex;
    justify-content: space-between;
    /* font-size removed - use Tailwind text-sm */
}

.metric-label {
    color: rgb(100, 116, 139);
}

.dark .metric-label {
    color: rgb(148, 163, 184);
}

.metric-value {
    font-weight: 600;
    color: rgb(51, 65, 85);
    font-family: var(--font-mono);
}

.dark .metric-value {
    color: rgb(226, 232, 240);
}

.result-warnings {
    color: rgb(245, 158, 11);
    /* font-size removed - use Tailwind text-sm */
    font-weight: 500;
}

.result-ok {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgb(34, 197, 94);
    /* font-size removed - use Tailwind text-sm */
    font-weight: 500;
}

.warning-item {
    /* font-size removed - use Tailwind text-xs */
    color: rgb(180, 83, 9);
    margin-top: 4px;
}

.dark .warning-item {
    color: rgb(251, 191, 36);
}

/* 推荐建议 */
.ai-recommendations {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.ai-recommendations-placeholder {
    text-align: center;
    padding: 32px 16px;
    color: rgb(100, 116, 139);
}

.dark .ai-recommendations-placeholder {
    color: rgb(148, 163, 184);
}

.ai-recommendations-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 32px 16px;
    color: rgb(34, 197, 94);
}

.ai-recommendations-empty i {
    /* font-size removed - use Tailwind text-3xl */
}

.ai-recommendation-item {
    background: rgba(255, 255, 255, 0.6);
    border-radius: 8px;
    padding: 16px;
    border-left: 4px solid;
}

.dark .ai-recommendation-item {
    background: rgba(71, 85, 105, 0.3);
}

.ai-recommendation-item.priority-critical {
    border-left-color: rgb(239, 68, 68);
}

.ai-recommendation-item.priority-high {
    border-left-color: rgb(245, 158, 11);
}

.ai-recommendation-item.priority-medium {
    border-left-color: rgb(59, 130, 246);
}

.ai-recommendation-item.priority-low {
    border-left-color: rgb(34, 197, 94);
}

.recommendation-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.recommendation-icon i {
    /* font-size removed - use Tailwind text-2xl */
    color: rgb(168, 85, 247);
}

.recommendation-title h4 {
    /* font-size removed - use Tailwind text-base */
    font-weight: 600;
    color: rgb(51, 65, 85);
    margin: 0 0 4px 0;
}

.dark .recommendation-title h4 {
    color: rgb(226, 232, 240);
}

.recommendation-priority {
    /* font-size removed - use Tailwind text-xs */
    padding: 2px 8px;
    border-radius: 4px;
    font-weight: 500;
    background: rgb(243, 244, 246);
    color: rgb(55, 65, 81);
}

.dark .recommendation-priority {
    background: rgb(75, 85, 99);
    color: rgb(209, 213, 219);
}

.recommendation-content {
    /* font-size removed - use Tailwind text-sm */
    line-height: 1.5;
}

.recommendation-description {
    color: rgb(71, 85, 105);
    margin-bottom: 8px;
}

.dark .recommendation-description {
    color: rgb(148, 163, 184);
}

.recommendation-action,
.recommendation-impact {
    margin-bottom: 4px;
}

.recommendation-action strong,
.recommendation-impact strong {
    color: rgb(51, 65, 85);
}

.dark .recommendation-action strong,
.dark .recommendation-impact strong {
    color: rgb(203, 213, 225);
}

/* 错误状态 */
.ai-error-state {
    text-align: center;
    padding: 48px 24px;
}

.ai-error-state i {
    /* font-size removed - use Tailwind text-5xl */
    color: rgb(239, 68, 68);
    margin-bottom: 16px;
    display: block;
}

.ai-error-state h4 {
    /* font-size removed - use Tailwind text-xl */
    font-weight: 600;
    color: rgb(51, 65, 85);
    margin: 0 0 8px 0;
}

.dark .ai-error-state h4 {
    color: rgb(226, 232, 240);
}

.ai-error-state p {
    color: rgb(239, 68, 68);
    margin: 0 0 24px 0;
}

/* 状态指示器 */
.ai-status-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    padding: 12px 16px;
    border-radius: 8px;
    /* font-size removed - use Tailwind text-sm */
    font-weight: 500;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.ai-status-indicator.show {
    opacity: 1;
    transform: translateX(0);
}

.ai-status-indicator.info {
    background: rgb(59, 130, 246);
    color: white;
}

.ai-status-indicator.success {
    background: rgb(34, 197, 94);
    color: white;
}

.ai-status-indicator.warning {
    background: rgb(245, 158, 11);
    color: white;
}

.ai-status-indicator.error {
    background: rgb(239, 68, 68);
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ai-dashboard-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .control-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .ai-overview-cards {
        grid-template-columns: 1fr;
    }
    
    .ai-report-grid {
        grid-template-columns: 1fr;
    }
    
    .ai-section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .ai-section-controls {
        justify-content: center;
    }
}

/* 禁用状态 */
.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn:disabled:hover {
    transform: none !important;
}