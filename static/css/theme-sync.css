/**
 * 额外的主题同步样式
 * 确保dashboard和list页面中的卡片元素完全同步主题变化
 */

/* Dashboard卡片样式 */
.rounded-lg[class*="bg-white"], 
.rounded-lg[class*="dark:bg-slate"],
.rounded-lg[class*="bg-slate"] {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
}

/* 确保进度条背景统一变化 */
.bg-gray-200, .dark\:bg-gray-700, 
.bg-slate-200, .dark\:bg-slate-700,
[class*="progress-bar-bg"], [class*="progress-bg"] {
  background-color: var(--border-color) !important;
}

/* 强化仪表盘进度条颜色 */
.h-1[class*="bg-blue-500"], 
.h-1[class*="bg-green-500"],
.h-1[class*="bg-indigo-500"] {
  background-color: var(--accent-color, #3b82f6) !important;
}

/* 表格和列表样式 */
table, th, td {
  transition: none !important;
}

/* 确保在切换瞬间所有背景色都应用到位 */
.bg-white, .dark\:bg-slate-700, 
.bg-slate-50, .dark\:bg-slate-800,
[class*="bg-slate-7"], [class*="bg-slate-8"], [class*="bg-slate-9"],
[class*="dark:bg-slate-7"], [class*="dark:bg-slate-8"], [class*="dark:bg-slate-9"] {
  background-color: var(--card-bg) !important;
}

/* 确保dashboard里的特殊元素也响应主题变化 */
.dashboard-inner-card, 
[class*="dashboard-card"], 
[class*="metric-card"] {
  background-color: var(--card-bg) !important;
}

/* 确保卡片边框与背景同步变化 */
.border, .border-t, .border-b, .border-l, .border-r,
[class*="border-slate-"], [class*="dark:border-slate-"],
[class*="border-gray-"], [class*="dark:border-gray-"] {
  border-color: var(--border-color) !important;
}
