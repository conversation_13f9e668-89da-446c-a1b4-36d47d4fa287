/******************************************
 * 通用筛选器样式
 * 使用属性选择器实现筛选功能
 * 配合 universal-filter-manager.js 使用
 ******************************************/

/******************************************
 * 分组筛选
 * 改用 JavaScript 动态控制，因为 CSS 无法处理动态分组
 * 旧的静态规则已被注释，保留以供参考
 ******************************************/

/* 
 * 以下规则已废弃，改用 JavaScript 控制
 * 原因：CSS 属性选择器无法动态匹配任意分组名称
 * 
 * #groups-container[data-group]:not([data-group="all"]) .server-card:not([data-group=""]) {
 *     display: none !important;
 * }
 * 
 * #groups-container[data-group="production"] .server-card[data-group="production"],
 * #groups-container[data-group="development"] .server-card[data-group="development"],
 * #groups-container[data-group="testing"] .server-card[data-group="testing"],
 * #groups-container[data-group="staging"] .server-card[data-group="staging"] {
 *     display: block !important;
 * }
 * 
 * #groups-container[data-group]:not([data-group="all"]):not([data-group=""]) .server-card {
 *     display: none !important;
 * }
 * 
 * #groups-container[data-group]:not([data-group="all"]):not([data-group=""]) .server-card[data-group] {
 *     display: none !important;
 * }
 */

/* 分组筛选 - 使用 JavaScript 设置的 data 属性 */
.server-card[data-hidden-by-group="true"] {
    display: none !important;
}

/******************************************
 * 状态筛选
 * 根据容器的 data-status 属性控制卡片显示
 ******************************************/

/* 状态筛选 - 采用先隐藏后显示的策略 */

/* 显示匹配状态的卡片 - 保持Grid布局兼容 */
/* 同时支持 card-grid-container 和 list-grid-container */
#card-grid-container[data-status="ONLINE"] .server-card:not([data-status="ONLINE"]),
#list-grid-container[data-status="ONLINE"] .server-card:not([data-status="ONLINE"]) {
    display: none !important;
}

#card-grid-container[data-status="OFFLINE"] .server-card:not([data-status="OFFLINE"]),
#list-grid-container[data-status="OFFLINE"] .server-card:not([data-status="OFFLINE"]) {
    display: none !important;
}

/* ALL状态显示所有卡片 - 不强制display:block，让其他筛选条件生效 */
#card-grid-container[data-status="ALL"] .server-card,
#list-grid-container[data-status="ALL"] .server-card {
    /* 移除!important，允许其他筛选生效 */
}

/******************************************
 * 到期时间筛选
 * 改用 JavaScript 动态控制，因为 CSS 无法进行数值比较
 * 使用 data-hidden-by-expiry 属性标记隐藏状态
 ******************************************/

/* 到期时间筛选 - 使用 JavaScript 设置的 data 属性 */
.server-card[data-hidden-by-expiry="true"] {
    display: none !important;
}

/******************************************
 * 地区筛选
 * 根据 data-region 属性进行匹配
 ******************************************/

/* 地区筛选 - 通用规则支持任意地区代码 */
/* 使用JavaScript设置data-region-filtered属性进行筛选 */

/* 隐藏被地区筛选标记的卡片 */
.server-card[data-region-filtered="true"] {
    display: none !important;
}


/******************************************
 * 标签筛选
 * 由 JavaScript 控制的特殊类
 ******************************************/
.server-card.tag-hidden {
    display: none !important;
}

/******************************************
 * 组合筛选优先级处理
 * 确保多个筛选条件同时生效
 ******************************************/

/* 注释掉强制隐藏规则，允许组合筛选正常工作 */
/*
.server-card[style*="display: none"],
.server-card[style*="display:none"] {
    display: none !important;
}
*/

/******************************************
 * 性能优化
 * 使用 will-change 提示浏览器优化
 ******************************************/
#groups-container {
    will-change: contents;
}

.server-card {
    will-change: display;
}

/******************************************
 * 过渡效果（可选）
 * 为筛选添加平滑过渡
 ******************************************/
.server-card {
    transition: opacity 0.2s ease-in-out;
}

.server-card[style*="display: none"],
.server-card.tag-hidden {
    opacity: 0 !important;
    transition: opacity 0.2s ease-in-out;
}

/******************************************
 * 辅助样式
 * 确保筛选后的布局正确
 ******************************************/

/* 防止空容器显示 */
#groups-container:empty::after {
    content: "暂无匹配的服务器";
    display: block;
    text-align: center;
    padding: 2rem;
    color: #666;
    font-size: 1rem;
}

/* 确保网格布局在筛选后正确显示 */
/* 注意：不要在 #groups-container 上设置网格布局，
   因为实际的网格布局应该在 #card-grid-container 上 */

/* 移除隐藏元素的间距 - 注释掉破坏Grid布局的规则 */
/* 原有的absolute定位会破坏Grid布局，已删除 */