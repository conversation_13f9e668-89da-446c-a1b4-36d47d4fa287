/**
 * TailwindCSS 组件定义
 * @description 基于现有CSS分析，使用@apply定义的可复用组件类
 * @created 2025-08-12 - 从现有CSS样式中提取和标准化
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  
  /* ===== 按钮组件 ===== */
  
  /* 基础按钮样式 - 所有按钮的共同属性 */
  .btn-base {
    @apply relative rounded-md transition-all duration-200 ease-in-out font-normal;
    @apply flex items-center justify-center;
    @apply border shadow-sm;
  }
  
  /* 主要按钮 - 品牌色按钮 */
  .btn-primary {
    @apply btn-base bg-primary-500 text-white border-primary-600;
    @apply hover:bg-primary-600 hover:shadow-md hover:-translate-y-px;
    @apply dark:bg-primary-600 dark:border-primary-700 dark:hover:bg-primary-700;
  }
  
  /* 次要按钮 - 中性色按钮 */
  .btn-secondary {
    @apply btn-base bg-white text-slate-800 border-slate-300;
    @apply hover:bg-slate-50 hover:shadow-md hover:-translate-y-px;
    @apply dark:bg-slate-700 dark:text-slate-200 dark:border-slate-600;
    @apply dark:hover:bg-slate-600;
  }
  
  /* 危险按钮 - 错误/删除操作 */
  .btn-danger {
    @apply btn-base bg-error-500 text-white border-error-600;
    @apply hover:bg-error-600 hover:shadow-md hover:-translate-y-px;
    @apply dark:bg-error-600 dark:border-error-700 dark:hover:bg-error-700;
  }
  
  /* 成功按钮 - 确认操作 */
  .btn-success {
    @apply btn-base bg-success-500 text-white border-success-600;
    @apply hover:bg-success-600 hover:shadow-md hover:-translate-y-px;
    @apply dark:bg-success-600 dark:border-success-700 dark:hover:bg-success-700;
  }
  
  /* 警告按钮 - 警告操作 */
  .btn-warning {
    @apply btn-base bg-warning-500 text-white border-warning-600;
    @apply hover:bg-warning-600 hover:shadow-md hover:-translate-y-px;
    @apply dark:bg-warning-600 dark:border-warning-700 dark:hover:bg-warning-700;
  }
  
  /* 文本按钮 - 兼容现有btn-text类 */
  .btn-text {
    @apply btn-base bg-slate-50 text-slate-800 border-slate-200;
    @apply hover:bg-slate-100 hover:shadow-md hover:-translate-y-px;
    @apply dark:bg-slate-700 dark:text-slate-200 dark:border-slate-600;
    @apply dark:hover:bg-slate-600;
  }
  
  /* ===== 卡片组件 ===== */
  
  /* 基础卡片样式 - 所有卡片的共同属性 */
  .card-base {
    @apply rounded-lg border border-slate-200 bg-white;
    @apply relative overflow-hidden transition-all duration-150 ease-in-out;
    @apply dark:bg-slate-800 dark:border-slate-700;
    @apply shadow-card;
  }
  
  /* 可悬浮卡片 - 带hover效果 */
  .card-hover {
    @apply card-base;
    @apply hover:shadow-card-hover hover:-translate-y-px;
    @apply hover:bg-slate-50 hover:border-slate-300;
    @apply dark:hover:bg-slate-700 dark:hover:border-slate-600;
  }
  
  /* 边框强调卡片 - 带颜色边框 */
  .card-bordered {
    @apply card-base border-2;
  }
  
  /* 服务器状态卡片 - 专用于服务器显示 */
  .card-server {
    @apply card-hover;
    @apply hover:shadow-lg hover:scale-[1.02];
    @apply will-change-transform transform-gpu;
  }
  
  /* Admin管理卡片 - 管理后台专用 */
  .card-admin {
    @apply card-base p-6;
    @apply hover:shadow-md;
  }
  
  /* Dashboard仪表盘卡片 */
  .card-dashboard {
    @apply card-base;
    @apply shadow-card;
    @apply dark:shadow-xl dark:shadow-black/10;
  }
  
  /* License许可证卡片 */
  .card-license {
    @apply card-hover cursor-pointer;
    @apply bg-gradient-to-br from-white/90 to-slate-50/80;
    @apply hover:border-primary-300 hover:shadow-primary-100;
    @apply dark:from-slate-800/80 dark:to-slate-900/60;
    @apply dark:hover:border-primary-600 dark:hover:shadow-primary-900/20;
  }
  
  /* ===== 容器组件 ===== */
  
  /* 图表容器 - 统一图表区域样式 */
  .container-chart {
    @apply p-6 bg-white rounded-lg border border-slate-200;
    @apply dark:bg-slate-800 dark:border-slate-700;
    @apply shadow-card;
  }
  
  /* 区块容器 - 页面区块分割 */
  .container-section {
    @apply p-6 space-y-6;
    @apply bg-white rounded-lg border border-slate-200;
    @apply dark:bg-slate-800 dark:border-slate-700;
    @apply shadow-card;
  }
  
  /* 内容区域 - 通用内容容器 */
  .container-content {
    @apply max-w-7xl mx-auto px-4 py-6;
  }
  
  /* ===== 表单组件 ===== */
  
  /* 基础输入框 */
  .input-base {
    @apply w-full px-3 py-2 border rounded-md;
    @apply border-slate-300 bg-white text-slate-900;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
    @apply dark:border-slate-600 dark:bg-slate-700 dark:text-slate-100;
    @apply dark:focus:ring-primary-400;
    @apply transition-colors duration-200;
  }
  
  /* 错误状态输入框 */
  .input-error {
    @apply input-base border-error-500;
    @apply focus:ring-error-500;
    @apply dark:border-error-600 dark:focus:ring-error-400;
  }
  
  /* 成功状态输入框 */
  .input-success {
    @apply input-base border-success-500;
    @apply focus:ring-success-500;
    @apply dark:border-success-600 dark:focus:ring-success-400;
  }
  
  /* ===== 布局组件 ===== */
  
  /* 居中flex布局 */
  .flex-center {
    @apply flex items-center justify-center;
  }
  
  /* 响应式网格 - 自适应列数 */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }
  
  /* 服务器卡片网格 - 专用于服务器展示 */
  .grid-servers {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }
  
  /* 管理页面布局 */
  .layout-admin {
    @apply max-w-screen-xl mx-auto px-4 py-6;
  }
  
  /* ===== 文字组件 ===== */
  
  /* 主标题 */
  .text-title {
    @apply text-2xl font-bold text-slate-900;
    @apply dark:text-slate-100;
  }
  
  /* 副标题 */
  .text-subtitle {
    @apply text-lg font-semibold text-slate-700;
    @apply dark:text-slate-300;
  }
  
  /* 说明文字 */
  .text-caption {
    @apply text-sm text-slate-500;
    @apply dark:text-slate-400;
  }
  
  /* 错误文字 */
  .text-error {
    @apply text-sm text-error-600;
    @apply dark:text-error-400;
  }
  
  /* 成功文字 */
  .text-success {
    @apply text-sm text-success-600;
    @apply dark:text-success-400;
  }
  
  /* ===== 状态指示器组件 ===== */
  
  /* 状态点 - 在线/离线指示 */
  .status-dot {
    @apply w-2 h-2 rounded-full;
  }
  
  /* 在线状态 */
  .status-online {
    @apply status-dot bg-success-500 animate-pulse-slow;
  }
  
  /* 离线状态 */
  .status-offline {
    @apply status-dot bg-error-500;
  }
  
  /* 警告状态 */
  .status-warning {
    @apply status-dot bg-warning-500;
  }
  
  /* ===== 下拉菜单组件 ===== */
  
  /* 下拉菜单容器 */
  .dropdown-menu {
    @apply absolute z-50 mt-1 bg-white border border-slate-200 rounded-md shadow-lg;
    @apply dark:bg-slate-800 dark:border-slate-700;
    @apply animate-fade-in;
  }
  
  /* 下拉菜单项 */
  .dropdown-item {
    @apply px-4 py-2 text-sm text-slate-700 cursor-pointer;
    @apply hover:bg-slate-50 hover:text-slate-900;
    @apply dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-slate-100;
    @apply transition-colors duration-150;
  }
  
  /* 下拉菜单项激活状态 */
  .dropdown-item-active {
    @apply dropdown-item bg-primary-50 text-primary-700;
    @apply dark:bg-primary-900/20 dark:text-primary-300;
  }
  
  /* ===== 徽章组件 ===== */
  
  /* 基础徽章 */
  .badge-base {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
  }
  
  /* 主色徽章 */
  .badge-primary {
    @apply badge-base bg-primary-100 text-primary-800;
    @apply dark:bg-primary-900/20 dark:text-primary-300;
  }
  
  /* 成功徽章 */
  .badge-success {
    @apply badge-base bg-success-100 text-success-800;
    @apply dark:bg-success-900/20 dark:text-success-300;
  }
  
  /* 错误徽章 */
  .badge-error {
    @apply badge-base bg-error-100 text-error-800;
    @apply dark:bg-error-900/20 dark:text-error-300;
  }
  
  /* 警告徽章 */
  .badge-warning {
    @apply badge-base bg-warning-100 text-warning-800;
    @apply dark:bg-warning-900/20 dark:text-warning-300;
  }
  
  /* ===== 动画组件 ===== */
  
  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-slate-300 border-t-primary-500;
  }
  
  /* 脉冲动画 */
  .loading-pulse {
    @apply animate-pulse-slow bg-slate-200 rounded;
    @apply dark:bg-slate-700;
  }
  
  /* 淡入动画 */
  .animate-in {
    @apply animate-fade-in;
  }
  
  /* ===== 响应式辅助类 ===== */
  
  /* 移动端隐藏 */
  .mobile-hidden {
    @apply hidden sm:block;
  }
  
  /* 桌面端隐藏 */
  .desktop-hidden {
    @apply block sm:hidden;
  }
  
  /* 平板端隐藏 */
  .tablet-hidden {
    @apply hidden md:block lg:hidden xl:block;
  }
}

/* 
  使用示例：
  
  1. 按钮使用：
  <button class="btn-primary px-4 py-2">保存</button>
  <button class="btn-secondary px-3 py-1 text-sm">取消</button>
  
  2. 卡片使用：
  <div class="card-hover p-6">
    <h3 class="text-subtitle">服务器状态</h3>
    <p class="text-caption">在线状态正常</p>
  </div>
  
  3. 表单使用：
  <input type="text" class="input-base" placeholder="请输入...">
  <input type="email" class="input-error" placeholder="邮箱格式错误">
  
  4. 布局使用：
  <div class="container-content">
    <div class="grid-responsive">
      <div class="card-server">服务器1</div>
      <div class="card-server">服务器2</div>
    </div>
  </div>
  
  5. 状态指示：
  <span class="flex-center gap-2">
    <span class="status-online"></span>
    <span class="text-success">在线</span>
  </span>
  
  优势：
  - 统一设计系统，确保视觉一致性
  - 自动支持暗色模式切换
  - 响应式设计内置
  - 易于维护和扩展
  - 遵循TailwindCSS最佳实践
*/