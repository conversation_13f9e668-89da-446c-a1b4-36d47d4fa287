/**
 * 主题过渡效果增强CSS
 * 用于确保主题切换时的平滑过渡体验
 */

/* 定义主题变量，方便全局统一管理 */
:root {
  /* 文本颜色 */
  --primary-text: #1e293b;
  --secondary-text: #64748b;
  
  /* 背景颜色 */
  --card-bg: var(--light-card-bg-color, #ffffff);
  --bg-color: var(--light-bg-color, #f8fafc);
  
  /* 边框颜色 */
  --border-color: rgba(0, 0, 0, 0.1);
  
  /* 主题过渡时间 */
  --theme-transition-duration: 200ms;
}

/* 深色主题变量 */
:root.dark {
  --primary-text: #f8fafc;
  --secondary-text: #cbd5e1;
  --card-bg: var(--dark-card-bg-color, #1e293b);
  --bg-color: var(--dark-bg-color, #0f172a);
  --border-color: rgba(255, 255, 255, 0.1);
}

/* 主题切换时应用统一过渡效果 */
body {
  transition-property: background-color, color, border-color, box-shadow;
  transition-duration: var(--theme-transition-duration);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 主题切换中时暂时禁用所有过渡效果，避免闪烁 */
html.theme-switching * {
  transition: none !important;
}

/* 卡片样式统一，使用CSS变量 */
.card, .server-card, .dashboard-inner-card, .dashboard-outer-card, 
.rounded-lg, [class*="dashboard-card"], [class*="metric-card"] {
  background-color: var(--card-bg) !important;
  color: var(--primary-text) !important;
  border-color: var(--border-color) !important;
  transition: none !important; /* 禁止独立过渡，只使用统一的过渡效果 */
}

/* 表格和列表样式统一 */
table, th, td, li {
  color: var(--primary-text);
}

/* 强制覆盖Tailwind文本颜色类，确保主题切换一致性 */
.text-primary, .text-slate-800, .text-slate-900, 
h1, h2, h3, h4, h5, h6, 
.card-title, .server-name, .dashboard-title,
[class*="text-slate-7"], [class*="text-slate-8"], [class*="text-slate-9"],
.dark\:text-white, .dark\:text-slate-100, .dark\:text-slate-200 {
  color: var(--primary-text) !important;
  transition: none !important;
}

.text-secondary, .text-slate-600, .text-slate-700, 
.text-gray-500, .text-gray-600, 
.card-subtitle, .server-subtitle, .dashboard-subtitle,
[class*="text-slate-4"], [class*="text-slate-5"], [class*="text-slate-6"],
[class*="text-gray-4"], [class*="text-gray-5"],
.dark\:text-slate-300, .dark\:text-slate-400, .dark\:text-slate-500,
.dark\:text-gray-300, .dark\:text-gray-400 {
  color: var(--secondary-text) !important;
  transition: none !important;
}

/* 确保主题切换遮罩层有足够的z-index和模糊效果 */
#theme-transition-overlay {
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: 9999 !important;
}

/* 主题初始化完成前隐藏内容，避免闪烁 */
html:not(.theme-initialized) .card,
html:not(.theme-initialized) .server-card,
html:not(.theme-initialized) .dashboard-inner-card,
html:not(.theme-initialized) .dashboard-outer-card {
  opacity: 0;
}

html.theme-initialized .card,
html.theme-initialized .server-card,
html.theme-initialized .dashboard-inner-card,
html.theme-initialized .dashboard-outer-card {
  opacity: 1;
  transition: opacity 150ms ease;
}
