/* 导入统一字体管理 */
@import 'typography.css';
@import 'components/metrics-simplified.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局字体映射 - 统一所有自定义类使用 Tailwind 字体系统 */
@layer components {
  /* 映射原有的度量类到 Tailwind 的等宽字体 */
  .metric-number,
  .speed-metric,
  .metric-large {
    @apply font-mono font-semibold;
  }
  
  /* 确保 License Key 也使用相同的等宽字体 */
  .license-key-display {
    @apply font-mono;
  }

  /* 简化版度量显示系统 - 替代复杂的 CSS 伪元素方案 */
  .metric-container {
    @apply inline-flex items-baseline;
  }

  .metric-value {
    @apply font-mono font-semibold;
    font-variant-numeric: tabular-nums;
    font-feature-settings: "tnum" 1;
  }

  .metric-unit {
    @apply text-sm opacity-70 ml-1 w-10 text-left;
  }

  /* 覆盖旧系统的样式 - 确保简化系统正常工作 */
  /* 使用高权重选择器替代!important */
  body:not(.legacy-mode) #current-download-speed-value,
  body:not(.legacy-mode) #current-upload-speed-value,
  body:not(.legacy-mode) #mobile-download-speed-value,
  body:not(.legacy-mode) #mobile-upload-speed-value,
  body:not(.legacy-mode) #total-download-value,
  body:not(.legacy-mode) #total-upload-value {
    /* 移除旧系统的内边距和定位 */
    padding-right: 0;
    position: static;
    min-width: auto;
    display: inline;

    /* 确保字体一致性 - 统一PC和移动端 */
    font-family: var(--font-mono);
    font-weight: 600; /* 使用 semibold 以获得更好的可读性 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* 移除旧系统的字体设置 */
    letter-spacing: normal;
    font-variant-numeric: tabular-nums;
    font-feature-settings: "tnum" 1;
  }

  /* 禁用旧系统的伪元素 */
  #current-download-speed-value::after,
  #current-upload-speed-value::after,
  #mobile-download-speed-value::after,
  #mobile-upload-speed-value::after,
  #total-download-value::after,
  #total-upload-value::after {
    display: none !important;
  }

  /* 确保容器布局正确 */
  #current-download-speed,
  #current-upload-speed,
  #mobile-download-speed,
  #mobile-upload-speed,
  #total-download,
  #total-upload {
    /* 移除旧系统的样式 */
    padding-right: 0 !important;
    min-width: auto !important;
  }

  /* 不同尺寸变体 */
  .metric-value-lg {
    @apply text-2xl;
  }

  .metric-value-md {
    @apply text-xl;
  }

  .metric-value-sm {
    @apply text-lg;
  }

  .metric-value-xs {
    @apply text-base;
  }

  /* 特殊状态 */
  .metric-animating {
    @apply transition-none;
  }
}

/* 导入主题样式 */
@import 'components/theme.css';

/* 导入自定义变量样式 */
@import 'components/components.css';

/* 导入监控页面样式 */
@import 'components/monitor.css';

/* 导航栏样式 */
@layer components {
  /* 导航链接基础样式 */
  .nav-link {
    @apply flex items-center justify-center
           px-2.5 py-1.5 rounded-md
           text-slate-600 dark:text-slate-300
           hover:bg-slate-100 dark:hover:bg-slate-800
           hover:text-indigo-600 dark:hover:text-indigo-400
           transition-all duration-200;
    margin-left: 0.25rem;
    margin-right: 0.25rem;
  }

  /* 移动设备下导航链接间距更小 */
  @media (max-width: 768px) {
    .nav-link {
      @apply px-2 py-1;
      margin-left: 0.125rem;
      margin-right: 0.125rem;
    }
  }

  /* 在平板设备上适当增加间距 */
  @media (min-width: 769px) and (max-width: 1024px) {
    .nav-link {
      @apply px-3 py-1.5;
      margin-left: 0.375rem;
      margin-right: 0.375rem;
    }
  }

  /* 在大屏幕上最大间距 */
  @media (min-width: 1025px) {
    .nav-link {
      @apply px-3.5 py-2;
      margin-left: 0.5rem;
      margin-right: 0.5rem;
    }
  }

  /* 活动状态导航链接 */
  .nav-link-active {
    @apply bg-indigo-50 dark:bg-indigo-900/30
           text-indigo-600 dark:text-indigo-400
           ring-1 ring-indigo-200 dark:ring-indigo-700;
  }

  /* 活动状态下的悬停效果 */
  .nav-link-active:hover {
    @apply bg-indigo-100 dark:bg-indigo-800/40;
  }
}

/* 卡片样式系统 */
@layer components {
  /* 卡片样式已移动到 static/css/components/card.css */
  /* 以下是兼容性类，实际样式已由全局卡片样式控制 */
  .card {
    /* 类保留但内容已移动到 card.css */
  }

  .card-padding {
    /* 类保留但内容已移动到 card.css */
  }

  .card-padding-sm {
    /* 类保留但内容已移动到 card.css */
  }

  .card-hover {
    /* 类保留但内容已移动到 card.css */
  }

  /* 服务器卡片 - 特殊样式 */
  .server-card {
    /* 类保留但内容已移动到 card.css */
  }

  /* 仪表盘外层卡片 - 特殊样式 */
  .dashboard-outer-card {
    /* 类保留但内容已移动到 card.css */
  }

  /* 使用mt和mb类的元素在dark模式下的背景色修复 - 已移除透明设置 */
  /* 现在dashboard-outer-card使用与其他卡片一致的背景色 */

  /* 仪表盘内层卡片 */
  .dashboard-inner-card {
    /* 类保留但内容已移动到 card.css */
  }

  /* 卡片状态样式 - 离线卡片处理已移至card.css */

  /* 卡片动作区域 */
  /* 解释：
  1. 移除上边距，使按钮与内容无缝衔接
  2. 使用负边距使按钮容器扩展到卡片边缘
  3. 确保flex布局让按钮真正占据全宽
  */
  .card-action {
    @apply flex mt-0 -mx-4 -mb-4;
  }

  /* 详情按钮样式 - 常规版本 */
  /* 解释：
  1. text-xs 使用更小的文本大小，与卡片其他内容保持一致
  2. text-gray-500 使用较浅的文本颜色，让按钮显得更低调
  3. hover:text-gray-700 鼠标悬停时文本颜色变深，提供交互反馈
  4. transition-colors 添加颜色过渡效果
  */
  .detail-btn {
    @apply flex items-center text-xs text-gray-500 dark:text-gray-400
           hover:text-gray-700 dark:hover:text-gray-200
           py-1.5 px-2.5 rounded transition-colors duration-200
           border border-transparent hover:border-gray-200 dark:hover:border-gray-700;
  }

  /* 详情按钮图标样式 */
  .detail-btn i {
    @apply text-sm mr-1.5;
    /* font-size removed - use Tailwind text-sm */
  }

  /* 详情按钮全宽版本 */
  /* 解释：
  1. w-full 使按钮占据父元素的全部宽度
  2. justify-center 使按钮内容居中对齐
  3. 只保留底部圆角，与卡片底部圆角保持一致
  4. 只设置顶部边框，视觉上形成分隔线
  5. 移除所有边距，确保按钮完全贴合卡片边缘
  6. items-center 确保内容垂直居中
  */
  .detail-btn-fullwidth {
    @apply w-full items-center justify-center text-xs text-gray-500 dark:text-gray-400
           hover:text-gray-700 dark:hover:text-gray-200
           py-1.5 transition-colors duration-200
           bg-gray-50 dark:bg-gray-800/30
           hover:bg-gray-100 dark:hover:bg-gray-800/50
           border-t border-gray-100 dark:border-gray-700
           rounded-none rounded-b-lg;
  }

  /* 全宽详情按钮图标样式 */
  /* 解释：
  1. 设置适当的右边距，与文字保持协调距离
  2. 使用inline-flex确保图标与文字对齐
  3. 使用Tailwind text-sm确保一致性
  4. items-center确保图标垂直居中
  */
  .detail-btn-fullwidth i {
    @apply inline-flex items-center mr-1 text-sm;
    vertical-align: middle;
    line-height: 1;
  }
}

/* 地区标签样式已转换为内联样式 */

/* 自定义工具类 */
@layer utilities {
  /* 过渡效果 */
  .theme-transition {
    @apply transition-all duration-150;
  }

  /* 滚动条工具类 - 使用全局CSS变量 */
  .scrollbar-thin {
    scrollbar-width: var(--scrollbar-width);
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: var(--scrollbar-width);
    height: var(--scrollbar-width);
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
    border-radius: var(--scrollbar-radius);
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: var(--scrollbar-radius);
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
  }

  /* 隐藏滚动条 */
  .scrollbar-none {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }

  /* 保持兼容性 */
  .no-scrollbar {
    @apply scrollbar-none;
  }

  /* 主题边框颜色 */
  .theme-border {
    border-width: 1px;
    border-style: solid;
    /* 完全移除边框相关的过渡效果以防止页面加载时闪烁 */
    transition: none !important;
  }

  /* 在夜间模式下调整卡片边框颜色，而不是移除边框 */
  :root.dark .theme-border {
    border-color: var(--dark-border-color, #1f293c);
  }


  /* 防止初始加载时的边框闪烁 */
  html:not(.theme-initialized) .theme-border {
    opacity: 0; /* 完全隐藏边框直到主题初始化完成 */
  }

  html.theme-initialized .theme-border {
    opacity: 1;
    transition: opacity 0.08s ease !important; /* 加快边框透明度的过渡效果 */
  }

  /* 进度条背景 */
  .progress-bg {
    background-color: var(--light-progress-bg-color, #f3f4f6);
  }
  :root.dark .progress-bg {
    background-color: var(--dark-progress-bg-color, #374151);
  }

  /* 自定义进度条背景 - 增加权重确保样式生效 */
  :root:not(.dark) .progress-bar-bg {
    background-color: #e0e2e7 !important;
  }
  :root.dark .progress-bar-bg {
    background-color: rgba(55, 65, 81, 0.5) !important;
  }

  /* 强制覆盖Tailwind的bg-gray类用于进度条背景 */
  :root:not(.dark) div[class*="bg-gray-100"].rounded-full.overflow-hidden {
    background-color: #f3f4f6 !important;
  }
  :root.dark div[class*="bg-gray-700"].rounded-full.overflow-hidden,
  :root.dark div[class*="dark:bg-gray-700"].rounded-full.overflow-hidden {
    background-color: rgba(55, 65, 81, 0.5) !important;
  }
}
