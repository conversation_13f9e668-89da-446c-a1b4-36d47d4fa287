/**
 * 全局滚动条样式
 * 统一的滚动条设计，支持亮色和暗色主题
 */

/* CSS变量定义 */
:root {
  /* 滚动条尺寸 */
  --scrollbar-width: 8px;
  --scrollbar-height: 8px;
  
  /* 亮色主题颜色 */
  --scrollbar-track: #f1f5f9;
  --scrollbar-track-hover: #e2e8f0;
  --scrollbar-thumb: #cbd5e1;
  --scrollbar-thumb-hover: #94a3b8;
  --scrollbar-thumb-active: #64748b;
  
  /* 圆角 */
  --scrollbar-radius: 4px;
}

/* 暗色主题颜色 */
:root.dark {
  --scrollbar-track: #1e293b;
  --scrollbar-track-hover: #334155;
  --scrollbar-thumb: #475569;
  --scrollbar-thumb-hover: #64748b;
  --scrollbar-thumb-active: #94a3b8;
}

/* 全局滚动条样式 - Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

/* 全局滚动条样式 - Webkit浏览器 */
*::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: var(--scrollbar-height);
}

*::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: var(--scrollbar-radius);
}

*::-webkit-scrollbar-track:hover {
  background: var(--scrollbar-track-hover);
}

*::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: var(--scrollbar-radius);
  transition: background-color 0.2s ease;
}

*::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

*::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active);
}

/* 滚动条角落（横竖滚动条交汇处） */
*::-webkit-scrollbar-corner {
  background: var(--scrollbar-track);
}

/* 特殊组件的滚动条样式覆盖 */

/* 更细的滚动条 */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* 极细滚动条 */
.scrollbar-micro {
  scrollbar-width: thin;
}

.scrollbar-micro::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

/* 隐藏滚动条但保持可滚动 */
.scrollbar-none {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

/* 自动隐藏滚动条 - 仅在滚动时显示 */
.scrollbar-auto-hide {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s;
}

.scrollbar-auto-hide:hover {
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

.scrollbar-auto-hide::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: var(--scrollbar-height);
}

.scrollbar-auto-hide::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-auto-hide::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: var(--scrollbar-radius);
}

.scrollbar-auto-hide:hover::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
}

.scrollbar-auto-hide:hover::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
}

/* 代码块和预格式化文本的滚动条 */
pre::-webkit-scrollbar,
code::-webkit-scrollbar {
  height: 6px;
}

/* 表格容器的滚动条 */
.table-container::-webkit-scrollbar,
.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

/* 侧边栏的滚动条 */
.sidebar::-webkit-scrollbar,
aside::-webkit-scrollbar {
  width: 6px;
}

/* 模态框内容的滚动条 */
.modal-content::-webkit-scrollbar,
[role="dialog"]::-webkit-scrollbar {
  width: 6px;
}

/* 下拉菜单的滚动条 - 更细更轻量 */
.dropdown-menu::-webkit-scrollbar,
select[multiple]::-webkit-scrollbar {
  width: 4px;
}

/* 移动设备优化 - 隐藏滚动条但保持可滚动 */
@media (max-width: 768px) {
  /* 移动端默认隐藏滚动条 */
  body::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
  
  /* 但保持特定区域的滚动条可见 */
  .keep-scrollbar::-webkit-scrollbar {
    width: var(--scrollbar-width);
    height: var(--scrollbar-height);
  }
}

/* 打印时隐藏滚动条 */
@media print {
  *::-webkit-scrollbar {
    display: none;
  }
  
  * {
    scrollbar-width: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --scrollbar-thumb: #64748b;
    --scrollbar-thumb-hover: #475569;
    --scrollbar-thumb-active: #334155;
  }
  
  :root.dark {
    --scrollbar-thumb: #94a3b8;
    --scrollbar-thumb-hover: #cbd5e1;
    --scrollbar-thumb-active: #e2e8f0;
  }
}

/* 动画和过渡效果 */
@media (prefers-reduced-motion: no-preference) {
  *::-webkit-scrollbar-thumb {
    transition: background-color 0.2s ease-in-out;
  }
  
  *::-webkit-scrollbar-track {
    transition: background-color 0.2s ease-in-out;
  }
}