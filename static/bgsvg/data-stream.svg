<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="mainBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1"/>
      <stop offset="30%" style="stop-color:#f8fafc;stop-opacity:1"/>
      <stop offset="70%" style="stop-color:#f1f5f9;stop-opacity:1"/>
      <stop offset="100%" style="stop-color:#e0f2fe;stop-opacity:1"/>
    </linearGradient>
    
    <linearGradient id="streamGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0ea5e9;stop-opacity:0"/>
      <stop offset="30%" style="stop-color:#3b82f6;stop-opacity:0.6"/>
      <stop offset="70%" style="stop-color:#8b5cf6;stop-opacity:0.8"/>
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:0"/>
    </linearGradient>
    
    <linearGradient id="nodeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#dbeafe;stop-opacity:0.8"/>
      <stop offset="100%" style="stop-color:#bfdbfe;stop-opacity:0.6"/>
    </linearGradient>
    
    <radialGradient id="pulseGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8"/>
      <stop offset="70%" style="stop-color:#06b6d4;stop-opacity:0.3"/>
      <stop offset="100%" style="stop-color:#0ea5e9;stop-opacity:0"/>
    </radialGradient>
    
    <!-- 数据包图案 -->
    <pattern id="dataPacket" x="0" y="0" width="40" height="20" patternUnits="userSpaceOnUse">
      <rect x="5" y="5" width="12" height="10" fill="#3b82f6" opacity="0.3" rx="2"/>
      <rect x="23" y="5" width="12" height="10" fill="#8b5cf6" opacity="0.3" rx="2"/>
    </pattern>
    
    <!-- 光晕效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 主背景 -->
  <rect width="1920" height="1080" fill="url(#mainBg)"/>
  
  <!-- 轻微的网格背景 -->
  <pattern id="lightGrid" width="60" height="60" patternUnits="userSpaceOnUse">
    <path d="M 60 0 L 0 0 0 60" fill="none" stroke="#e2e8f0" stroke-width="0.5" opacity="0.2"/>
  </pattern>
  <rect width="1920" height="1080" fill="url(#lightGrid)"/>
  
  <!-- 主要数据流管道 -->
  <g>
    <!-- 水平主管道 -->
    <rect x="100" y="400" width="1720" height="8" fill="#e2e8f0" rx="4" opacity="0.5"/>
    <rect x="100" y="402" width="1720" height="4" fill="url(#streamGradient)" rx="2">
      <animate attributeName="x" values="100;200;100" dur="4s" repeatCount="indefinite"/>
    </rect>
    
    <!-- 数据流动效果 -->
    <g>
      <!-- 流动的数据包 -->
      <rect x="100" y="398" width="20" height="12" fill="#3b82f6" opacity="0.6" rx="2">
        <animateTransform attributeName="transform" type="translate" 
                          values="0,0;1720,0;0,0" dur="6s" repeatCount="indefinite"/>
      </rect>
      <rect x="400" y="398" width="15" height="12" fill="#8b5cf6" opacity="0.7" rx="2">
        <animateTransform attributeName="transform" type="translate" 
                          values="0,0;1420,0;0,0" dur="5s" repeatCount="indefinite"/>
      </rect>
      <rect x="800" y="398" width="25" height="12" fill="#06b6d4" opacity="0.6" rx="2">
        <animateTransform attributeName="transform" type="translate" 
                          values="0,0;1020,0;0,0" dur="7s" repeatCount="indefinite"/>
      </rect>
    </g>
    
    <!-- 垂直分支管道 -->
    <rect x="396" y="200" width="8" height="200" fill="#e2e8f0" rx="4" opacity="0.4"/>
    <rect x="796" y="450" width="8" height="300" fill="#e2e8f0" rx="4" opacity="0.4"/>
    <rect x="1296" y="100" width="8" height="300" fill="#e2e8f0" rx="4" opacity="0.4"/>
    <rect x="1596" y="450" width="8" height="250" fill="#e2e8f0" rx="4" opacity="0.4"/>
  </g>
  
  <!-- 数据中心节点 -->
  <g>
    <!-- 主数据中心 -->
    <g transform="translate(960,300)">
      <circle cx="0" cy="0" r="60" fill="url(#nodeGradient)" opacity="0.8"/>
      <circle cx="0" cy="0" r="60" fill="none" stroke="#3b82f6" stroke-width="2" opacity="0.4"/>
      <circle cx="0" cy="0" r="40" fill="url(#pulseGradient)" opacity="0.6">
        <animate attributeName="r" values="40;50;40" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.3;0.6" dur="3s" repeatCount="indefinite"/>
      </circle>
      
      <!-- 数据中心图标 -->
      <rect x="-15" y="-20" width="30" height="40" fill="#3b82f6" opacity="0.6" rx="3"/>
      <rect x="-12" y="-15" width="6" height="3" fill="#ffffff" opacity="0.8" rx="1"/>
      <rect x="-4" y="-15" width="6" height="3" fill="#ffffff" opacity="0.8" rx="1"/>
      <rect x="4" y="-15" width="6" height="3" fill="#ffffff" opacity="0.8" rx="1"/>
      <rect x="-12" y="-8" width="6" height="3" fill="#ffffff" opacity="0.8" rx="1"/>
      <rect x="-4" y="-8" width="6" height="3" fill="#ffffff" opacity="0.8" rx="1"/>
      <rect x="4" y="-8" width="6" height="3" fill="#ffffff" opacity="0.8" rx="1"/>
      <rect x="-12" y="-1" width="6" height="3" fill="#ffffff" opacity="0.8" rx="1"/>
      <rect x="-4" y="-1" width="6" height="3" fill="#ffffff" opacity="0.8" rx="1"/>
      <rect x="4" y="-1" width="6" height="3" fill="#ffffff" opacity="0.8" rx="1"/>
      <rect x="-12" y="6" width="6" height="3" fill="#ffffff" opacity="0.8" rx="1"/>
      <rect x="-4" y="6" width="6" height="3" fill="#ffffff" opacity="0.8" rx="1"/>
      <rect x="4" y="6" width="6" height="3" fill="#ffffff" opacity="0.8" rx="1"/>
    </g>
    
    <!-- 边缘节点 -->
    <circle cx="400" cy="200" r="25" fill="url(#nodeGradient)" opacity="0.7">
      <animate attributeName="r" values="25;30;25" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="800" cy="750" r="25" fill="url(#nodeGradient)" opacity="0.7">
      <animate attributeName="r" values="25;30;25" dur="3.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1300" cy="150" r="25" fill="url(#nodeGradient)" opacity="0.7">
      <animate attributeName="r" values="25;30;25" dur="5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1600" cy="700" r="25" fill="url(#nodeGradient)" opacity="0.7">
      <animate attributeName="r" values="25;30;25" dur="4.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 数据传输可视化 -->
  <g>
    <!-- 从中心向外的数据流 -->
    <g stroke="#3b82f6" stroke-width="2" fill="none" opacity="0.4">
      <path d="M 960,300 Q 800,250 400,200">
        <animate attributeName="stroke-dasharray" values="0,1000;50,950;0,1000" dur="3s" repeatCount="indefinite"/>
      </path>
      <path d="M 960,300 Q 880,500 800,750">
        <animate attributeName="stroke-dasharray" values="0,1000;50,950;0,1000" dur="3.5s" repeatCount="indefinite"/>
      </path>
      <path d="M 960,300 Q 1130,200 1300,150">
        <animate attributeName="stroke-dasharray" values="0,1000;50,950;0,1000" dur="4s" repeatCount="indefinite"/>
      </path>
      <path d="M 960,300 Q 1280,500 1600,700">
        <animate attributeName="stroke-dasharray" values="0,1000;50,950;0,1000" dur="4.5s" repeatCount="indefinite"/>
      </path>
    </g>
  </g>
  
  <!-- 实时数据监控区域 -->
  <g transform="translate(200,600)">
    <rect x="0" y="0" width="300" height="180" fill="url(#nodeGradient)" rx="8" opacity="0.5"/>
    
    <!-- 实时数据条 -->
    <g>
      <rect x="20" y="140" width="8" height="20" fill="#3b82f6" opacity="0.7">
        <animate attributeName="height" values="20;40;20" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="y" values="140;120;140" dur="2s" repeatCount="indefinite"/>
      </rect>
      <rect x="40" y="130" width="8" height="30" fill="#8b5cf6" opacity="0.7">
        <animate attributeName="height" values="30;50;30" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="y" values="130;110;130" dur="2.5s" repeatCount="indefinite"/>
      </rect>
      <rect x="60" y="135" width="8" height="25" fill="#06b6d4" opacity="0.7">
        <animate attributeName="height" values="25;45;25" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="y" values="135;115;135" dur="3s" repeatCount="indefinite"/>
      </rect>
      <rect x="80" y="125" width="8" height="35" fill="#10b981" opacity="0.7">
        <animate attributeName="height" values="35;55;35" dur="2.2s" repeatCount="indefinite"/>
        <animate attributeName="y" values="125;105;125" dur="2.2s" repeatCount="indefinite"/>
      </rect>
      <rect x="100" y="140" width="8" height="20" fill="#f59e0b" opacity="0.7">
        <animate attributeName="height" values="20;40;20" dur="2.8s" repeatCount="indefinite"/>
        <animate attributeName="y" values="140;120;140" dur="2.8s" repeatCount="indefinite"/>
      </rect>
    </g>
    
    <!-- 数据流标识 -->
    <text x="20" y="30" font-family="system-ui" font-size="14" fill="#64748b" opacity="0.8">实时数据流</text>
    <text x="20" y="50" font-family="system-ui" font-size="12" fill="#94a3b8" opacity="0.6">Data Stream Monitor</text>
  </g>
  
  <!-- 网络拓扑概览 -->
  <g transform="translate(1400,600)">
    <rect x="0" y="0" width="350" height="200" fill="url(#nodeGradient)" rx="8" opacity="0.4"/>
    
    <!-- 简化的网络拓扑 -->
    <g>
      <!-- 连接线 -->
      <line x1="50" y1="100" x2="150" y2="100" stroke="#3b82f6" stroke-width="2" opacity="0.5"/>
      <line x1="150" y1="100" x2="250" y2="100" stroke="#3b82f6" stroke-width="2" opacity="0.5"/>
      <line x1="150" y1="100" x2="150" y2="50" stroke="#8b5cf6" stroke-width="2" opacity="0.5"/>
      <line x1="150" y1="100" x2="150" y2="150" stroke="#06b6d4" stroke-width="2" opacity="0.5"/>
      
      <!-- 节点 -->
      <circle cx="50" cy="100" r="12" fill="#3b82f6" opacity="0.8"/>
      <circle cx="150" cy="100" r="15" fill="#8b5cf6" opacity="0.8"/>
      <circle cx="250" cy="100" r="12" fill="#06b6d4" opacity="0.8"/>
      <circle cx="150" cy="50" r="10" fill="#10b981" opacity="0.8"/>
      <circle cx="150" cy="150" r="10" fill="#f59e0b" opacity="0.8"/>
      
      <!-- 状态指示器 -->
      <circle cx="300" cy="50" r="6" fill="#10b981" opacity="0.9">
        <animate attributeName="opacity" values="0.9;0.4;0.9" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="300" cy="70" r="6" fill="#10b981" opacity="0.9">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="300" cy="90" r="6" fill="#f59e0b" opacity="0.9">
        <animate attributeName="opacity" values="0.9;0.4;0.9" dur="1.5s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <text x="20" y="30" font-family="system-ui" font-size="14" fill="#64748b" opacity="0.8">网络状态</text>
  </g>
  
  <!-- 装饰性数据流粒子 -->
  <g opacity="0.3">
    <circle cx="300" cy="200" r="2" fill="#3b82f6">
      <animateMotion dur="12s" repeatCount="indefinite">
        <path d="M 0,0 Q 300,100 600,0 Q 900,-100 1200,0 Q 1500,100 1800,0"/>
      </animateMotion>
    </circle>
    <circle cx="500" cy="800" r="1.5" fill="#8b5cf6">
      <animateMotion dur="15s" repeatCount="indefinite">
        <path d="M 0,0 Q -200,-50 -400,0 Q -600,50 -800,0 Q -1000,-50 -1200,0"/>
      </animateMotion>
    </circle>
    <circle cx="1200" cy="500" r="2.5" fill="#06b6d4">
      <animateMotion dur="10s" repeatCount="indefinite">
        <path d="M 0,0 Q 150,150 300,0 Q 450,-150 600,0"/>
      </animateMotion>
    </circle>
  </g>
  
  <!-- 轻微的光效 -->
  <g opacity="0.2">
    <ellipse cx="480" cy="400" rx="100" ry="20" fill="url(#pulseGradient)">
      <animate attributeName="rx" values="100;150;100" dur="6s" repeatCount="indefinite"/>
    </ellipse>
    <ellipse cx="1440" cy="400" rx="120" ry="25" fill="url(#pulseGradient)">
      <animate attributeName="rx" values="120;170;120" dur="8s" repeatCount="indefinite"/>
    </ellipse>
  </g>
</svg> 