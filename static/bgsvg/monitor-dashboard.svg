<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <radialGradient id="mainGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1"/>
      <stop offset="50%" style="stop-color:#f1f5f9;stop-opacity:1"/>
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1"/>
    </radialGradient>
    
    <linearGradient id="chartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.05"/>
    </linearGradient>
    
    <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:0.3"/>
      <stop offset="50%" style="stop-color:#06b6d4;stop-opacity:0.2"/>
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:0.3"/>
    </linearGradient>
    
    <!-- 仪表盘图标滤镜 -->
    <filter id="dashboardGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 主背景 -->
  <rect width="1920" height="1080" fill="url(#mainGradient)"/>
  
  <!-- 网格背景 -->
  <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
    <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#cbd5e1" stroke-width="0.5" opacity="0.3"/>
  </pattern>
  <rect width="1920" height="1080" fill="url(#grid)"/>
  
  <!-- 大型仪表盘圆形区域 -->
  <circle cx="1440" cy="300" r="180" fill="url(#chartGradient)" opacity="0.6"/>
  <circle cx="1440" cy="300" r="180" fill="none" stroke="#8b5cf6" stroke-width="2" opacity="0.2"/>
  
  <!-- 仪表盘扇形指示器 -->
  <g transform="translate(1440,300)">
    <path d="M 0,-160 A 160,160 0 0,1 113.14,-113.14" fill="none" stroke="#10b981" stroke-width="4" opacity="0.6"/>
    <path d="M 113.14,-113.14 A 160,160 0 0,1 160,0" fill="none" stroke="#f59e0b" stroke-width="4" opacity="0.6"/>
    <path d="M 160,0 A 160,160 0 0,1 113.14,113.14" fill="none" stroke="#ef4444" stroke-width="3" opacity="0.4"/>
    
    <!-- 中心点 -->
    <circle cx="0" cy="0" r="8" fill="#8b5cf6" opacity="0.8"/>
    
    <!-- 刻度线 -->
    <g stroke="#64748b" stroke-width="1" opacity="0.4">
      <line x1="0" y1="-150" x2="0" y2="-140"/>
      <line x1="106.07" y1="-106.07" x2="99.41" y2="-99.41"/>
      <line x1="150" y1="0" x2="140" y2="0"/>
      <line x1="106.07" y1="106.07" x2="99.41" y2="99.41"/>
      <line x1="0" y1="150" x2="0" y2="140"/>
      <line x1="-106.07" y1="106.07" x2="-99.41" y2="99.41"/>
      <line x1="-150" y1="0" x2="-140" y2="0"/>
      <line x1="-106.07" y1="-106.07" x2="-99.41" y2="-99.41"/>
    </g>
  </g>
  
  <!-- 数据条形图区域 -->
  <g transform="translate(200,200)">
    <rect x="0" y="0" width="300" height="200" fill="url(#chartGradient)" rx="8" opacity="0.4"/>
    
    <!-- 条形图 -->
    <g fill="url(#lineGradient)">
      <rect x="20" y="120" width="25" height="60" rx="2" opacity="0.7">
        <animate attributeName="height" values="60;80;60" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="y" values="120;100;120" dur="3s" repeatCount="indefinite"/>
      </rect>
      <rect x="60" y="100" width="25" height="80" rx="2" opacity="0.7">
        <animate attributeName="height" values="80;100;80" dur="2.5s" repeatCount="indefinite"/>
        <animate attributeName="y" values="100;80;100" dur="2.5s" repeatCount="indefinite"/>
      </rect>
      <rect x="100" y="140" width="25" height="40" rx="2" opacity="0.7">
        <animate attributeName="height" values="40;70;40" dur="4s" repeatCount="indefinite"/>
        <animate attributeName="y" values="140;110;140" dur="4s" repeatCount="indefinite"/>
      </rect>
      <rect x="140" y="90" width="25" height="90" rx="2" opacity="0.7">
        <animate attributeName="height" values="90;110;90" dur="3.5s" repeatCount="indefinite"/>
        <animate attributeName="y" values="90;70;90" dur="3.5s" repeatCount="indefinite"/>
      </rect>
      <rect x="180" y="110" width="25" height="70" rx="2" opacity="0.7">
        <animate attributeName="height" values="70;95;70" dur="2.8s" repeatCount="indefinite"/>
        <animate attributeName="y" values="110;85;110" dur="2.8s" repeatCount="indefinite"/>
      </rect>
      <rect x="220" y="130" width="25" height="50" rx="2" opacity="0.7">
        <animate attributeName="height" values="50;75;50" dur="3.2s" repeatCount="indefinite"/>
        <animate attributeName="y" values="130;105;130" dur="3.2s" repeatCount="indefinite"/>
      </rect>
      <rect x="260" y="95" width="25" height="85" rx="2" opacity="0.7">
        <animate attributeName="height" values="85;105;85" dur="2.7s" repeatCount="indefinite"/>
        <animate attributeName="y" values="95;75;95" dur="2.7s" repeatCount="indefinite"/>
      </rect>
    </g>
    
    <!-- 基准线 -->
    <line x1="10" y1="180" x2="290" y2="180" stroke="#64748b" stroke-width="1" opacity="0.3"/>
  </g>
  
  <!-- 网络节点连接图 -->
  <g transform="translate(600,600)">
    <!-- 连接线 -->
    <g stroke="url(#lineGradient)" stroke-width="2" fill="none" opacity="0.5">
      <line x1="0" y1="0" x2="100" y2="50">
        <animate attributeName="stroke-opacity" values="0.3;0.8;0.3" dur="3s" repeatCount="indefinite"/>
      </line>
      <line x1="100" y1="50" x2="200" y2="0">
        <animate attributeName="stroke-opacity" values="0.8;0.3;0.8" dur="3s" repeatCount="indefinite"/>
      </line>
      <line x1="200" y1="0" x2="300" y2="80">
        <animate attributeName="stroke-opacity" values="0.3;0.8;0.3" dur="3s" repeatCount="indefinite"/>
      </line>
      <line x1="0" y1="0" x2="150" y2="150">
        <animate attributeName="stroke-opacity" values="0.5;1;0.5" dur="4s" repeatCount="indefinite"/>
      </line>
      <line x1="150" y1="150" x2="300" y2="80">
        <animate attributeName="stroke-opacity" values="1;0.5;1" dur="4s" repeatCount="indefinite"/>
      </line>
    </g>
    
    <!-- 节点 -->
    <circle cx="0" cy="0" r="8" fill="#10b981" opacity="0.8">
      <animate attributeName="r" values="8;12;8" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="100" cy="50" r="6" fill="#3b82f6" opacity="0.8">
      <animate attributeName="r" values="6;10;6" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="200" cy="0" r="7" fill="#8b5cf6" opacity="0.8">
      <animate attributeName="r" values="7;11;7" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="300" cy="80" r="8" fill="#06b6d4" opacity="0.8">
      <animate attributeName="r" values="8;12;8" dur="2.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="150" cy="150" r="10" fill="#8b5cf6" opacity="0.9">
      <animate attributeName="r" values="10;14;10" dur="3.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 波形图区域 -->
  <g transform="translate(1100,700)">
    <rect x="0" y="0" width="400" height="150" fill="url(#chartGradient)" rx="8" opacity="0.3"/>
    
    <!-- 波形线 -->
    <path d="M 20,75 Q 70,40 120,75 T 220,75 Q 270,110 320,75 Q 345,60 370,75" 
          fill="none" stroke="#8b5cf6" stroke-width="3" opacity="0.6">
      <animate attributeName="d" 
               values="M 20,75 Q 70,40 120,75 T 220,75 Q 270,110 320,75 Q 345,60 370,75;
                       M 20,75 Q 70,110 120,75 T 220,75 Q 270,40 320,75 Q 345,90 370,75;
                       M 20,75 Q 70,40 120,75 T 220,75 Q 270,110 320,75 Q 345,60 370,75"
               dur="4s" repeatCount="indefinite"/>
    </path>
    
    <!-- 基准线 -->
    <line x1="20" y1="75" x2="370" y2="75" stroke="#64748b" stroke-width="1" opacity="0.2" stroke-dasharray="5,5"/>
  </g>
  
  <!-- 装饰性几何图形 -->
  <g opacity="0.1">
    <!-- 六边形 -->
    <polygon points="300,800 330,815 330,845 300,860 270,845 270,815" fill="#8b5cf6"/>
    <polygon points="1600,200 1630,215 1630,245 1600,260 1570,245 1570,215" fill="#3b82f6"/>
    
    <!-- 三角形 -->
    <polygon points="100,900 120,870 140,900" fill="#10b981"/>
    <polygon points="1800,800 1820,770 1840,800" fill="#06b6d4"/>
  </g>
  
  <!-- 数据点动画 -->
  <g opacity="0.3">
    <circle cx="500" cy="300" r="3" fill="#8b5cf6">
      <animateMotion dur="8s" repeatCount="indefinite">
        <path d="M 0,0 Q 200,100 400,0 Q 600,-100 800,0"/>
      </animateMotion>
    </circle>
    <circle cx="800" cy="500" r="2" fill="#3b82f6">
      <animateMotion dur="10s" repeatCount="indefinite">
        <path d="M 0,0 Q -200,-100 -400,0 Q -600,100 -800,0"/>
      </animateMotion>
    </circle>
  </g>
</svg> 