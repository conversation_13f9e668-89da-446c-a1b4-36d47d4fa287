<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080" preserveAspectRatio="none">
    <defs>
        <!-- 深度监控暗色渐变 -->
        <linearGradient id="deep-bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#0c0a1e" />
            <stop offset="50%" stop-color="#1a1625" />
            <stop offset="100%" stop-color="#0f0f23" />
        </linearGradient>
        
        <!-- 安全监控渐变 -->
        <linearGradient id="security-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="#f59e0b" />
            <stop offset="50%" stop-color="#ef4444" />
            <stop offset="100%" stop-color="#dc2626" />
        </linearGradient>
        
        <!-- 网络扫描渐变 -->
        <linearGradient id="scan-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stop-color="#3b82f6" />
            <stop offset="50%" stop-color="#1d4ed8" />
            <stop offset="100%" stop-color="#1e3a8a" />
        </linearGradient>
        
        <!-- 强光效滤镜 -->
        <filter id="strong-glow" x="-30%" y="-30%" width="160%" height="160%">
            <feGaussianBlur stdDeviation="4" result="blur"/>
            <feComposite in="SourceGraphic" in2="blur" operator="over"/>
        </filter>
        
        <!-- 扫描线图案 -->
        <pattern id="scan-lines" x="0" y="0" width="100%" height="20" patternUnits="userSpaceOnUse">
            <rect x="0" y="0" width="100%" height="1" fill="#1e40af" opacity="0.3"/>
            <rect x="0" y="10" width="100%" height="0.5" fill="#3b82f6" opacity="0.2"/>
        </pattern>
        
        <!-- 威胁检测辉光 -->
        <radialGradient id="threat-glow" cx="50%" cy="50%" r="70%">
            <stop offset="0%" stop-color="#ef4444" stop-opacity="0.4" />
            <stop offset="100%" stop-color="#ef4444" stop-opacity="0" />
        </radialGradient>
        
        <!-- 安全辉光 -->
        <radialGradient id="safe-glow" cx="50%" cy="50%" r="60%">
            <stop offset="0%" stop-color="#10b981" stop-opacity="0.3" />
            <stop offset="100%" stop-color="#10b981" stop-opacity="0" />
        </radialGradient>
    </defs>
    
    <!-- 主背景 -->
    <rect width="1920" height="1080" fill="url(#deep-bg-gradient)"/>
    
    <!-- 扫描线效果 -->
    <rect width="1920" height="1080" fill="url(#scan-lines)"/>
    
    <!-- 顶部监控条 -->
    <rect x="0" y="0" width="1920" height="60" fill="rgba(15, 23, 42, 0.8)" stroke="#1e40af" stroke-width="1"/>
    <rect x="50" y="15" width="200" height="30" rx="15" fill="rgba(30, 64, 175, 0.3)" stroke="#3b82f6" stroke-width="1"/>
    <rect x="300" y="15" width="150" height="30" rx="15" fill="rgba(16, 185, 129, 0.3)" stroke="#10b981" stroke-width="1"/>
    <rect x="500" y="15" width="180" height="30" rx="15" fill="rgba(239, 68, 68, 0.3)" stroke="#ef4444" stroke-width="1"/>
    
    <!-- 状态指示灯 -->
    <circle cx="1800" cy="30" r="8" fill="#10b981" filter="url(#strong-glow)">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1830" cy="30" r="8" fill="#f59e0b" filter="url(#strong-glow)">
        <animate attributeName="opacity" values="1;0.4;1" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1860" cy="30" r="8" fill="#ef4444" filter="url(#strong-glow)">
        <animate attributeName="opacity" values="0.8;1;0.8" dur="1.5s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 左侧监控面板 -->
    <g opacity="0.8">
        <rect x="50" y="150" width="300" height="600" rx="10" fill="rgba(15, 23, 42, 0.7)" stroke="#1e40af" stroke-width="2"/>
        
        <!-- 网络拓扑显示 -->
        <rect x="70" y="180" width="260" height="150" rx="5" fill="rgba(30, 64, 175, 0.2)" stroke="#3b82f6" stroke-width="1"/>
        <text x="200" y="200" text-anchor="middle" fill="#3b82f6" font-family="monospace" font-size="12">NETWORK TOPOLOGY</text>
        
        <!-- 节点状态显示 -->
        <circle cx="120" cy="240" r="15" fill="url(#safe-glow)" stroke="#10b981" stroke-width="2"/>
        <circle cx="120" cy="240" r="8" fill="#10b981"/>
        <text x="150" y="246" fill="#10b981" font-family="monospace" font-size="10">NODE-01 ONLINE</text>
        
        <circle cx="120" cy="280" r="15" fill="url(#threat-glow)" stroke="#ef4444" stroke-width="2"/>
        <circle cx="120" cy="280" r="8" fill="#ef4444">
            <animate attributeName="opacity" values="1;0.5;1" dur="1s" repeatCount="indefinite"/>
        </circle>
        <text x="150" y="286" fill="#ef4444" font-family="monospace" font-size="10">NODE-02 ALERT</text>
        
        <circle cx="120" cy="320" r="15" fill="url(#safe-glow)" stroke="#10b981" stroke-width="2"/>
        <circle cx="120" cy="320" r="8" fill="#10b981"/>
        <text x="150" y="326" fill="#10b981" font-family="monospace" font-size="10">NODE-03 SECURE</text>
        
        <!-- 实时数据流 -->
        <rect x="70" y="380" width="260" height="200" rx="5" fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" stroke-width="1"/>
        <text x="200" y="400" text-anchor="middle" fill="#ef4444" font-family="monospace" font-size="12">THREAT DETECTION</text>
        
        <!-- 数据条 -->
        <rect x="90" y="430" width="180" height="8" fill="rgba(16, 185, 129, 0.3)" rx="4"/>
        <rect x="90" y="430" width="140" height="8" fill="#10b981" rx="4">
            <animate attributeName="width" values="140;200;140" dur="4s" repeatCount="indefinite"/>
        </rect>
        <text x="90" y="455" fill="#10b981" font-family="monospace" font-size="9">SECURE TRAFFIC: 78%</text>
        
        <rect x="90" y="470" width="180" height="8" fill="rgba(245, 158, 11, 0.3)" rx="4"/>
        <rect x="90" y="470" width="60" height="8" fill="#f59e0b" rx="4">
            <animate attributeName="width" values="60;90;60" dur="3s" repeatCount="indefinite"/>
        </rect>
        <text x="90" y="495" fill="#f59e0b" font-family="monospace" font-size="9">SUSPICIOUS: 15%</text>
        
        <rect x="90" y="510" width="180" height="8" fill="rgba(239, 68, 68, 0.3)" rx="4"/>
        <rect x="90" y="510" width="30" height="8" fill="#ef4444" rx="4">
            <animate attributeName="width" values="30;50;30" dur="2s" repeatCount="indefinite"/>
        </rect>
        <text x="90" y="535" fill="#ef4444" font-family="monospace" font-size="9">THREATS: 7%</text>
    </g>
    
    <!-- 中央监控屏幕 -->
    <g opacity="0.9">
        <rect x="450" y="200" width="1020" height="650" rx="15" fill="rgba(15, 23, 42, 0.8)" stroke="#1e40af" stroke-width="3"/>
        
        <!-- 扫描雷达 -->
        <circle cx="960" cy="525" r="200" fill="none" stroke="#1e40af" stroke-width="2" opacity="0.5"/>
        <circle cx="960" cy="525" r="150" fill="none" stroke="#1e40af" stroke-width="1" opacity="0.3"/>
        <circle cx="960" cy="525" r="100" fill="none" stroke="#1e40af" stroke-width="1" opacity="0.3"/>
        <circle cx="960" cy="525" r="50" fill="none" stroke="#1e40af" stroke-width="1" opacity="0.3"/>
        
        <!-- 十字准线 -->
        <line x1="760" y1="525" x2="1160" y2="525" stroke="#1e40af" stroke-width="1" opacity="0.4"/>
        <line x1="960" y1="325" x2="960" y2="725" stroke="#1e40af" stroke-width="1" opacity="0.4"/>
        
        <!-- 扫描光束 -->
        <line x1="960" y1="525" x2="1060" y2="425" stroke="#3b82f6" stroke-width="3" filter="url(#strong-glow)">
            <animateTransform attributeName="transform" attributeType="XML" type="rotate" values="0 960 525;360 960 525" dur="8s" repeatCount="indefinite"/>
        </line>
        
        <!-- 检测到的节点 -->
        <circle cx="1000" cy="450" r="8" fill="#10b981" filter="url(#strong-glow)"/>
        <circle cx="880" cy="580" r="8" fill="#f59e0b" filter="url(#strong-glow)">
            <animate attributeName="opacity" values="1;0.5;1" dur="1.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="1080" cy="600" r="8" fill="#ef4444" filter="url(#strong-glow)">
            <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite"/>
        </circle>
        
        <!-- 数据面板 -->
        <rect x="1250" y="250" width="200" height="500" rx="8" fill="rgba(30, 64, 175, 0.2)" stroke="#3b82f6" stroke-width="1"/>
        <text x="1350" y="280" text-anchor="middle" fill="#3b82f6" font-family="monospace" font-size="11">NETWORK STATUS</text>
        
        <text x="1270" y="320" fill="#10b981" font-family="monospace" font-size="9">NODES ONLINE: 24</text>
        <text x="1270" y="340" fill="#f59e0b" font-family="monospace" font-size="9">ALERTS: 3</text>
        <text x="1270" y="360" fill="#ef4444" font-family="monospace" font-size="9">THREATS: 1</text>
        
        <!-- 实时波形 -->
        <path d="M 1270,400 L 1280,390 L 1290,410 L 1300,380 L 1310,420 L 1320,370 L 1330,430 L 1340,360 L 1350,440 L 1360,350 L 1370,450 L 1380,340 L 1390,460 L 1400,330 L 1410,470"
              fill="none" stroke="#3b82f6" stroke-width="2">
            <animate attributeName="d" values="M 1270,400 L 1280,390 L 1290,410 L 1300,380 L 1310,420 L 1320,370 L 1330,430 L 1340,360 L 1350,440 L 1360,350 L 1370,450 L 1380,340 L 1390,460 L 1400,330 L 1410,470;
                                                     M 1270,400 L 1280,410 L 1290,380 L 1300,430 L 1310,370 L 1320,440 L 1330,360 L 1340,450 L 1350,350 L 1360,460 L 1370,340 L 1380,470 L 1390,330 L 1400,480 L 1410,320;
                                                     M 1270,400 L 1280,390 L 1290,410 L 1300,380 L 1310,420 L 1320,370 L 1330,430 L 1340,360 L 1350,440 L 1360,350 L 1370,450 L 1380,340 L 1390,460 L 1400,330 L 1410,470"
                     dur="3s" repeatCount="indefinite"/>
        </path>
    </g>
    
    <!-- 右侧安全警报面板 -->
    <g opacity="0.7">
        <rect x="1550" y="150" width="320" height="600" rx="10" fill="rgba(15, 23, 42, 0.7)" stroke="#ef4444" stroke-width="2"/>
        <text x="1710" y="185" text-anchor="middle" fill="#ef4444" font-family="monospace" font-size="14">SECURITY ALERTS</text>
        
        <!-- 警报项目 -->
        <rect x="1570" y="210" width="280" height="40" rx="5" fill="rgba(239, 68, 68, 0.2)" stroke="#ef4444" stroke-width="1">
            <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
        </rect>
        <text x="1580" y="230" fill="#ef4444" font-family="monospace" font-size="10">HIGH: INTRUSION DETECTED</text>
        <text x="1580" y="242" fill="#ef4444" font-family="monospace" font-size="8">IP: 192.168.1.unknown</text>
        
        <rect x="1570" y="270" width="280" height="40" rx="5" fill="rgba(245, 158, 11, 0.2)" stroke="#f59e0b" stroke-width="1"/>
        <text x="1580" y="290" fill="#f59e0b" font-family="monospace" font-size="10">MED: UNUSUAL TRAFFIC</text>
        <text x="1580" y="302" fill="#f59e0b" font-family="monospace" font-size="8">PORT: 8080 - 443</text>
        
        <rect x="1570" y="330" width="280" height="40" rx="5" fill="rgba(16, 185, 129, 0.2)" stroke="#10b981" stroke-width="1"/>
        <text x="1580" y="350" fill="#10b981" font-family="monospace" font-size="10">LOW: SYSTEM UPDATE</text>
        <text x="1580" y="362" fill="#10b981" font-family="monospace" font-size="8">STATUS: COMPLETED</text>
    </g>
    
    <!-- 底部状态栏 -->
    <rect x="0" y="1020" width="1920" height="60" fill="rgba(15, 23, 42, 0.8)" stroke="#1e40af" stroke-width="1"/>
    <text x="50" y="1045" fill="#3b82f6" font-family="monospace" font-size="12">SYSTEM: OPERATIONAL</text>
    <text x="300" y="1045" fill="#10b981" font-family="monospace" font-size="12">UPTIME: 99.7%</text>
    <text x="500" y="1045" fill="#f59e0b" font-family="monospace" font-size="12">LOAD: 67%</text>
    <text x="1600" y="1045" fill="#3b82f6" font-family="monospace" font-size="12">dstatus v2.0 - SECURE MODE</text>
    
    <!-- 边框装饰 -->
    <rect x="20" y="20" width="1880" height="1040" rx="20" fill="none" stroke="url(#security-gradient)" stroke-width="2" opacity="0.5"/>
    <rect x="40" y="40" width="1840" height="1000" rx="15" fill="none" stroke="#1e40af" stroke-width="1" opacity="0.3" stroke-dasharray="10 5"/>
</svg> 