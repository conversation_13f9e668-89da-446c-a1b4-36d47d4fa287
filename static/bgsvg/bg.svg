<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <defs>
    <!-- Enhanced gradients -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1a0933" />
      <stop offset="50%" stop-color="#0f1e3d" />
      <stop offset="100%" stop-color="#070b34" />
    </linearGradient>
    
    <radialGradient id="sideGlow1" cx="10%" cy="50%" r="70%" fx="10%" fy="50%">
      <stop offset="0%" stop-color="#5000aa" stop-opacity="0.3" />
      <stop offset="100%" stop-color="#000000" stop-opacity="0" />
    </radialGradient>
    
    <radialGradient id="sideGlow2" cx="90%" cy="50%" r="70%" fx="90%" fy="50%">
      <stop offset="0%" stop-color="#004395" stop-opacity="0.3" />
      <stop offset="100%" stop-color="#000000" stop-opacity="0" />
    </radialGradient>
    
    <!-- Enhanced filters for better glow effects -->
    <filter id="blueGlow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="3" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <filter id="purpleGlow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="2.5" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <filter id="whiteGlow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="2" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <filter id="neonGlow" x="-30%" y="-30%" width="160%" height="160%">
      <feGaussianBlur stdDeviation="4" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <!-- Circuit patterns -->
    <pattern id="circuitPattern" x="0" y="0" width="200" height="200" patternUnits="userSpaceOnUse">
      <path d="M10 10 H50 V50 H90 V90" stroke="#304090" stroke-width="1" fill="none" />
      <path d="M110 10 V50 H150 V90" stroke="#304090" stroke-width="1" fill="none" />
      <path d="M10 110 H50 V150 H90" stroke="#304090" stroke-width="1" fill="none" />
      <path d="M110 110 H150 V150 H190" stroke="#304090" stroke-width="1" fill="none" />
      <circle cx="50" cy="50" r="3" fill="#4060c0" />
      <circle cx="150" cy="50" r="3" fill="#4060c0" />
      <circle cx="50" cy="150" r="3" fill="#4060c0" />
      <circle cx="150" cy="150" r="3" fill="#4060c0" />
    </pattern>
  </defs>
  
  <!-- Main background -->
  <rect width="100%" height="100%" fill="url(#bgGradient)" />
  
  <!-- Side glow effects -->
  <rect width="100%" height="100%" fill="url(#sideGlow1)" />
  <rect width="100%" height="100%" fill="url(#sideGlow2)" />
  
  <!-- Circuit pattern background -->
  <rect width="100%" height="100%" fill="url(#circuitPattern)" opacity="0.15" />
  
  <!-- Enhanced grid pattern -->
  <g opacity="0.15">
    <path d="M0 100 H1200 M0 200 H1200 M0 300 H1200 M0 400 H1200 M0 500 H1200 M0 600 H1200 M0 700 H1200" stroke="#8a8aff" stroke-width="0.5" />
    <path d="M100 0 V800 M200 0 V800 M300 0 V800 M400 0 V800 M500 0 V800 M600 0 V800 M700 0 V800 M800 0 V800 M900 0 V800 M1000 0 V800 M1100 0 V800" stroke="#8a8aff" stroke-width="0.5" />
  </g>
  
  <!-- Left side elements (concentrated) -->
  <g class="left-side">
    <!-- Digital HUD elements on left -->
    <g transform="translate(50, 100)" filter="url(#blueGlow)" opacity="0.9">
      <rect x="0" y="0" width="150" height="80" rx="5" fill="none" stroke="#00d0ff" stroke-width="1" />
      <line x1="10" y1="20" x2="140" y2="20" stroke="#00d0ff" stroke-width="1" />
      <text x="75" y="15" font-family="monospace" font-size="10" fill="#00d0ff" text-anchor="middle">SYSTEM//:DATA</text>
      <text x="10" y="35" font-family="monospace" font-size="8" fill="#00d0ff">INIT_SEQUENCE</text>
      <text x="10" y="50" font-family="monospace" font-size="8" fill="#00d0ff">STATUS: ACTIVE</text>
      <text x="10" y="65" font-family="monospace" font-size="8" fill="#00d0ff">CLK: 3.2GHZ</text>
      
      <animate attributeName="opacity" values="0.7;1;0.7" dur="4s" repeatCount="indefinite" />
    </g>
    
    <!-- Circuit board pattern on left -->
    <g transform="translate(20, 220)" opacity="0.8">
      <path d="M0 0 H100 V150 H0 Z" fill="none" stroke="#8030a0" stroke-width="1" />
      <path d="M0 30 H100 M0 60 H100 M0 90 H100 M0 120 H100" stroke="#8030a0" stroke-width="0.5" />
      <path d="M20 0 V150 M40 0 V150 M60 0 V150 M80 0 V150" stroke="#8030a0" stroke-width="0.5" />
      
      <circle cx="20" cy="30" r="3" fill="#d020ff" filter="url(#purpleGlow)">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite" begin="0s" />
      </circle>
      <circle cx="60" cy="60" r="3" fill="#d020ff" filter="url(#purpleGlow)">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite" begin="0.5s" />
      </circle>
      <circle cx="40" cy="90" r="3" fill="#d020ff" filter="url(#purpleGlow)">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite" begin="1s" />
      </circle>
      <circle cx="80" cy="120" r="3" fill="#d020ff" filter="url(#purpleGlow)">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite" begin="1.5s" />
      </circle>
    </g>
    
    <!-- Left side data visualization -->
    <g transform="translate(30, 400)" filter="url(#blueGlow)">
      <rect x="0" y="0" width="120" height="180" rx="5" fill="rgba(0,30,60,0.3)" stroke="#0080ff" stroke-width="1" />
      
      <!-- Bar chart-like elements -->
      <rect x="10" y="160" width="10" height="10" fill="#00a0ff">
        <animate attributeName="height" values="10;80;10" dur="10s" repeatCount="indefinite" />
        <animate attributeName="y" values="160;90;160" dur="10s" repeatCount="indefinite" />
      </rect>
      <rect x="30" y="160" width="10" height="10" fill="#00a0ff">
        <animate attributeName="height" values="10;120;10" dur="15s" repeatCount="indefinite" />
        <animate attributeName="y" values="160;50;160" dur="15s" repeatCount="indefinite" />
      </rect>
      <rect x="50" y="160" width="10" height="10" fill="#00a0ff">
        <animate attributeName="height" values="10;60;10" dur="12s" repeatCount="indefinite" />
        <animate attributeName="y" values="160;110;160" dur="12s" repeatCount="indefinite" />
      </rect>
      <rect x="70" y="160" width="10" height="10" fill="#00a0ff">
        <animate attributeName="height" values="10;100;10" dur="8s" repeatCount="indefinite" />
        <animate attributeName="y" values="160;70;160" dur="8s" repeatCount="indefinite" />
      </rect>
      <rect x="90" y="160" width="10" height="10" fill="#00a0ff">
        <animate attributeName="height" values="10;70;10" dur="13s" repeatCount="indefinite" />
        <animate attributeName="y" values="160;100;160" dur="13s" repeatCount="indefinite" />
      </rect>
      
      <!-- Digital numbers changing -->
      <text x="60" y="20" font-family="monospace" font-size="10" fill="#00d0ff" text-anchor="middle">SYSTEM_AI</text>
      <text x="30" y="40" font-family="monospace" font-size="12" fill="#00d0ff" id="changingNumber">
        <animate attributeName="textContent" values="8472;9385;2741;5038;8472" dur="5s" repeatCount="indefinite" />
      </text>
      <text x="90" y="40" font-family="monospace" font-size="12" fill="#00d0ff" id="changingNumber2">
        <animate attributeName="textContent" values="3720;1856;7429;3720" dur="4s" repeatCount="indefinite" />
      </text>
    </g>
    
    <!-- Pulsing circle pattern left -->
    <g transform="translate(100, 650)">
      <circle cx="0" cy="0" r="40" fill="none" stroke="#ff00aa" stroke-width="1">
        <animate attributeName="r" values="40;60;40" dur="4s" repeatCount="indefinite" />
        <animate attributeName="opacity" values="0.8;0.2;0.8" dur="4s" repeatCount="indefinite" />
      </circle>
      <circle cx="0" cy="0" r="30" fill="none" stroke="#ff00aa" stroke-width="1">
        <animate attributeName="r" values="30;50;30" dur="4s" repeatCount="indefinite" begin="0.5s" />
        <animate attributeName="opacity" values="0.8;0.2;0.8" dur="4s" repeatCount="indefinite" begin="0.5s" />
      </circle>
      <circle cx="0" cy="0" r="20" fill="none" stroke="#ff00aa" stroke-width="1">
        <animate attributeName="r" values="20;40;20" dur="4s" repeatCount="indefinite" begin="1s" />
        <animate attributeName="opacity" values="0.8;0.2;0.8" dur="4s" repeatCount="indefinite" begin="1s" />
      </circle>
      <circle cx="0" cy="0" r="10" fill="#ff00aa" opacity="0.8">
        <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite" />
      </circle>
    </g>
    
    <!-- Animated dots in random positions on left -->
    <g filter="url(#whiteGlow)">
      <!-- Group 1 -->
      <circle cx="50" cy="50" r="1" fill="#ffffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="3s" repeatCount="indefinite" begin="0s" />
      </circle>
      <circle cx="150" cy="80" r="1.2" fill="#00ffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="4s" repeatCount="indefinite" begin="0.5s" />
      </circle>
      <circle cx="100" cy="150" r="0.8" fill="#ffffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="2.5s" repeatCount="indefinite" begin="1s" />
      </circle>
      <circle cx="80" cy="200" r="1" fill="#ffaa00">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="3.5s" repeatCount="indefinite" begin="1.5s" />
      </circle>
      
      <!-- Group 2 -->
      <circle cx="120" cy="270" r="1" fill="#ffffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="3s" repeatCount="indefinite" begin="0.2s" />
      </circle>
      <circle cx="60" cy="320" r="1.2" fill="#00ffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="4s" repeatCount="indefinite" begin="0.7s" />
      </circle>
      <circle cx="130" cy="380" r="0.8" fill="#ffffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="2.5s" repeatCount="indefinite" begin="1.2s" />
      </circle>
      <circle cx="50" cy="450" r="1" fill="#ffaa00">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="3.5s" repeatCount="indefinite" begin="1.7s" />
      </circle>
      
      <!-- Group 3 -->
      <circle cx="140" cy="520" r="1" fill="#ffffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="3s" repeatCount="indefinite" begin="0.4s" />
      </circle>
      <circle cx="70" cy="580" r="1.2" fill="#00ffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="4s" repeatCount="indefinite" begin="0.9s" />
      </circle>
      <circle cx="120" cy="640" r="0.8" fill="#ffffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="2.5s" repeatCount="indefinite" begin="1.4s" />
      </circle>
      <circle cx="50" cy="700" r="1" fill="#ffaa00">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="3.5s" repeatCount="indefinite" begin="1.9s" />
      </circle>
    </g>
    
    <!-- Dynamic data streams on left side -->
    <g filter="url(#blueGlow)">
      <!-- Stream 1 -->
      <path d="M0 120 C50 110, 100 130, 150 120" stroke="#00c0ff" stroke-width="1" fill="none" opacity="0">
        <animate attributeName="opacity" values="0;0.8;0" dur="4s" repeatCount="indefinite" begin="0s" />
      </path>
      <circle cx="0" cy="120" r="1.5" fill="#00c0ff">
        <animate attributeName="cx" values="0;150" dur="4s" repeatCount="indefinite" begin="0s" />
        <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite" begin="0s" />
      </circle>
      
      <!-- Stream 2 -->
      <path d="M0 180 C50 200, 100 170, 150 190" stroke="#00c0ff" stroke-width="1" fill="none" opacity="0">
        <animate attributeName="opacity" values="0;0.8;0" dur="4s" repeatCount="indefinite" begin="1s" />
      </path>
      <circle cx="0" cy="180" r="1.5" fill="#00c0ff">
        <animate attributeName="cx" values="0;150" dur="4s" repeatCount="indefinite" begin="1s" />
        <animate attributeName="cy" values="180;190" dur="4s" repeatCount="indefinite" begin="1s" />
        <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite" begin="1s" />
      </circle>
      
      <!-- Stream 3 -->
      <path d="M0 240 C50 220, 100 250, 150 230" stroke="#00c0ff" stroke-width="1" fill="none" opacity="0">
        <animate attributeName="opacity" values="0;0.8;0" dur="4s" repeatCount="indefinite" begin="2s" />
      </path>
      <circle cx="0" cy="240" r="1.5" fill="#00c0ff">
        <animate attributeName="cx" values="0;150" dur="4s" repeatCount="indefinite" begin="2s" />
        <animate attributeName="cy" values="240;230" dur="4s" repeatCount="indefinite" begin="2s" />
        <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite" begin="2s" />
      </circle>
      
      <!-- Stream 4 -->
      <path d="M0 300 C50 320, 100 290, 150 310" stroke="#00c0ff" stroke-width="1" fill="none" opacity="0">
        <animate attributeName="opacity" values="0;0.8;0" dur="4s" repeatCount="indefinite" begin="3s" />
      </path>
      <circle cx="0" cy="300" r="1.5" fill="#00c0ff">
        <animate attributeName="cx" values="0;150" dur="4s" repeatCount="indefinite" begin="3s" />
        <animate attributeName="cy" values="300;310" dur="4s" repeatCount="indefinite" begin="3s" />
        <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite" begin="3s" />
      </circle>
    </g>
  </g>
  
  <!-- Right side elements (concentrated) -->
  <g class="right-side">
    <!-- Digital cyber pattern on right -->
    <g transform="translate(940, 100)" filter="url(#purpleGlow)" opacity="0.9">
      <rect x="0" y="0" width="180" height="100" rx="5" fill="rgba(40,0,60,0.3)" stroke="#d020ff" stroke-width="1" />
      <text x="90" y="20" font-family="monospace" font-size="12" fill="#d020ff" text-anchor="middle">NEURAL_NET</text>
      
      <!-- Neural net visualization -->
      <circle cx="30" cy="40" r="4" fill="#d020ff" />
      <circle cx="70" cy="40" r="4" fill="#d020ff" />
      <circle cx="110" cy="40" r="4" fill="#d020ff" />
      <circle cx="150" cy="40" r="4" fill="#d020ff" />
      
      <circle cx="50" cy="60" r="4" fill="#d020ff" />
      <circle cx="90" cy="60" r="4" fill="#d020ff" />
      <circle cx="130" cy="60" r="4" fill="#d020ff" />
      
      <circle cx="70" cy="80" r="4" fill="#d020ff" />
      <circle cx="110" cy="80" r="4" fill="#d020ff" />
      
      <!-- Neural connections -->
      <path d="M30 40 L50 60 M30 40 L90 60 M70 40 L50 60 M70 40 L90 60 M70 40 L130 60 M110 40 L90 60 M110 40 L130 60 M150 40 L130 60 M50 60 L70 80 M50 60 L110 80 M90 60 L70 80 M90 60 L110 80 M130 60 L110 80" stroke="#d020ff" stroke-width="0.5" />
      
      <!-- Animation for neural activity -->
      <circle cx="30" cy="40" r="6" fill="#ff20ff" opacity="0">
        <animate attributeName="opacity" values="0;0.8;0" dur="3s" repeatCount="indefinite" begin="0s" />
      </circle>
      <circle cx="70" cy="40" r="6" fill="#ff20ff" opacity="0">
        <animate attributeName="opacity" values="0;0.8;0" dur="3s" repeatCount="indefinite" begin="0.3s" />
      </circle>
      <circle cx="50" cy="60" r="6" fill="#ff20ff" opacity="0">
        <animate attributeName="opacity" values="0;0.8;0" dur="3s" repeatCount="indefinite" begin="0.6s" />
      </circle>
      <circle cx="90" cy="60" r="6" fill="#ff20ff" opacity="0">
        <animate attributeName="opacity" values="0;0.8;0" dur="3s" repeatCount="indefinite" begin="0.9s" />
      </circle>
      <circle cx="70" cy="80" r="6" fill="#ff20ff" opacity="0">
        <animate attributeName="opacity" values="0;0.8;0" dur="3s" repeatCount="indefinite" begin="1.2s" />
      </circle>
    </g>
    
    <!-- Digital hexagon pattern on right -->
    <g transform="translate(980, 230)" opacity="0.8">
      <path d="M50 0 L100 25 L100 75 L50 100 L0 75 L0 25 Z" fill="none" stroke="#00c0ff" stroke-width="1" />
      <path d="M40 20 L80 20 L100 55 L80 90 L40 90 L20 55 Z" fill="none" stroke="#00c0ff" stroke-width="1" />
      <path d="M50 30 L70 30 L90 50 L70 70 L50 70 L30 50 Z" fill="none" stroke="#00c0ff" stroke-width="1" />
      
      <circle cx="50" cy="50" r="5" fill="#00c0ff" filter="url(#blueGlow)">
        <animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite" />
        <animate attributeName="r" values="5;8;5" dur="3s" repeatCount="indefinite" />
      </circle>
      
      <!-- Animated connection lines -->
      <line x1="50" y1="0" x2="50" y2="20" stroke="#00c0ff" stroke-width="1">
        <animate attributeName="y2" values="0;20;0" dur="2s" repeatCount="indefinite" begin="0s" />
        <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="0s" />
      </line>
      <line x1="100" y1="25" x2="80" y2="30" stroke="#00c0ff" stroke-width="1">
        <animate attributeName="x2" values="100;80;100" dur="2s" repeatCount="indefinite" begin="0.4s" />
        <animate attributeName="y2" values="25;30;25" dur="2s" repeatCount="indefinite" begin="0.4s" />
        <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="0.4s" />
      </line>
      <line x1="100" y1="75" x2="80" y2="70" stroke="#00c0ff" stroke-width="1">
        <animate attributeName="x2" values="100;80;100" dur="2s" repeatCount="indefinite" begin="0.8s" />
        <animate attributeName="y2" values="75;70;75" dur="2s" repeatCount="indefinite" begin="0.8s" />
        <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="0.8s" />
      </line>
      <line x1="50" y1="100" x2="50" y2="80" stroke="#00c0ff" stroke-width="1">
        <animate attributeName="y2" values="100;80;100" dur="2s" repeatCount="indefinite" begin="1.2s" />
        <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="1.2s" />
      </line>
      <line x1="0" y1="75" x2="20" y2="70" stroke="#00c0ff" stroke-width="1">
        <animate attributeName="x2" values="0;20;0" dur="2s" repeatCount="indefinite" begin="1.6s" />
        <animate attributeName="y2" values="75;70;75" dur="2s" repeatCount="indefinite" begin="1.6s" />
        <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="1.6s" />
      </line>
      <line x1="0" y1="25" x2="20" y2="30" stroke="#00c0ff" stroke-width="1">
        <animate attributeName="x2" values="0;20;0" dur="2s" repeatCount="indefinite" begin="2s" />
        <animate attributeName="y2" values="25;30;25" dur="2s" repeatCount="indefinite" begin="2s" />
        <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="2s" />
      </line>
    </g>
    
    <!-- Right side data visualization -->
    <g transform="translate(1050, 400)" filter="url(#purpleGlow)">
      <rect x="0" y="0" width="120" height="180" rx="5" fill="rgba(40,0,60,0.3)" stroke="#d020ff" stroke-width="1" />
      
      <!-- Circular graph -->
      <circle cx="60" cy="90" r="50" fill="none" stroke="#d020ff" stroke-width="1" />
      <circle cx="60" cy="90" r="40" fill="none" stroke="#d020ff" stroke-width="1" />
      <circle cx="60" cy="90" r="30" fill="none" stroke="#d020ff" stroke-width="1" />
      <circle cx="60" cy="90" r="20" fill="none" stroke="#d020ff" stroke-width="1" />
      
      <!-- Graph points -->
      <circle cx="60" cy="40" r="3" fill="#ff60ff" />
      <circle cx="102" cy="69" r="3" fill="#ff60ff" />
      <circle cx="93" cy="132" r="3" fill="#ff60ff" />
      <circle cx="27" cy="132" r="3" fill="#ff60ff" />
      <circle cx="18" cy="69" r="3" fill="#ff60ff" />
      
      <!-- Graph lines -->
      <path d="M60 40 L102 69 L93 132 L27 132 L18 69 Z" fill="none" stroke="#ff60ff" stroke-width="1" />
      
      <!-- Animated pulse -->
      <circle cx="60" cy="90" r="0" fill="#ff60ff" opacity="0.5">
        <animate attributeName="r" values="0;50;0" dur="4s" repeatCount="indefinite" />
        <animate attributeName="opacity" values="0.5;0;0.5" dur="4s" repeatCount="indefinite" />
      </circle>
      
      <text x="60" y="20" font-family="monospace" font-size="10" fill="#ff60ff" text-anchor="middle">QUANTUM_FIELD</text>
    </g>
    
    <!-- Pulsing circle pattern right -->
    <g transform="translate(1100, 650)">
      <circle cx="0" cy="0" r="40" fill="none" stroke="#00d0ff" stroke-width="1">
        <animate attributeName="r" values="40;60;40" dur="4s" repeatCount="indefinite" />
        <animate attributeName="opacity" values="0.8;0.2;0.8" dur="4s" repeatCount="indefinite" />
      </circle>
      <circle cx="0" cy="0" r="30" fill="none" stroke="#00d0ff" stroke-width="1">
        <animate attributeName="r" values="30;50;30" dur="4s" repeatCount="indefinite" begin="0.5s" />
        <animate attributeName="opacity" values="0.8;0.2;0.8" dur="4s" repeatCount="indefinite" begin="0.5s" />
      </circle>
      <circle cx="0" cy="0" r="20" fill="none" stroke="#00d0ff" stroke-width="1">
        <animate attributeName="r" values="20;40;20" dur="4s" repeatCount="indefinite" begin="1s" />
        <animate attributeName="opacity" values="0.8;0.2;0.8" dur="4s" repeatCount="indefinite" begin="1s" />
      </circle>
      <circle cx="0" cy="0" r="10" fill="#00d0ff" opacity="0.8">
        <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite" />
      </circle>
    </g>
    
    <!-- Animated dots in random positions on right -->
    <g filter="url(#whiteGlow)">
      <!-- Group 1 -->
      <circle cx="950" cy="50" r="1" fill="#ffffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="3s" repeatCount="indefinite" begin="0.1s" />
      </circle>
      <circle cx="1050" cy="80" r="1.2" fill="#00ffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="4s" repeatCount="indefinite" begin="0.6s" />
      </circle>
      <circle cx="1000" cy="150" r="0.8" fill="#ffffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="2.5s" repeatCount="indefinite" begin="1.1s" />
      </circle>
      <circle cx="1080" cy="200" r="1" fill="#ffaa00">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="3.5s" repeatCount="indefinite" begin="1.6s" />
      </circle>
      
      <!-- Group 2 -->
      <circle cx="1020" cy="270" r="1" fill="#ffffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="3s" repeatCount="indefinite" begin="0.3s" />
      </circle>
      <circle cx="960" cy="320" r="1.2" fill="#00ffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="4s" repeatCount="indefinite" begin="0.8s" />
      </circle>
      <circle cx="1030" cy="380" r="0.8" fill="#ffffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="2.5s" repeatCount="indefinite" begin="1.3s" />
      </circle>
      <circle cx="950" cy="450" r="1" fill="#ffaa00">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="3.5s" repeatCount="indefinite" begin="1.8s" />
      </circle>
      
      <!-- Group 3 -->
      <circle cx="1040" cy="520" r="1" fill="#ffffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="3s" repeatCount="indefinite" begin="0.5s" />
      </circle>
      <circle cx="970" cy="580" r="1.2" fill="#00ffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="4s" repeatCount="indefinite" begin="1s" />
      </circle>
      <circle cx="1020" cy="640" r="0.8" fill="#ffffff">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="2.5s" repeatCount="indefinite" begin="1.5s" />
      </circle>
      <circle cx="950" cy="700" r="1" fill="#ffaa00">
        <animate attributeName="opacity" values="0.2;1;0.2" dur="3.5s" repeatCount="indefinite" begin="2s" />
      </circle>
    </g>
    
    <!-- Dynamic data streams on right side -->
    <g filter="url(#purpleGlow)">
      <!-- Stream 1 -->
      <path d="M1050 120 C1100 110, 1150 130, 1200 120" stroke="#d020ff" stroke-width="1" fill="none" opacity="0">
        <animate attributeName="opacity" values="0;0.8;0" dur="4s" repeatCount="indefinite" begin="0.5s" />
      </path>
      <circle cx="1050" cy="120" r="1.5" fill="#d020ff">
        <animate attributeName="cx" values="1050;1200" dur="4s" repeatCount="indefinite" begin="0.5s" />
        <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite" begin="0.5s" />
      </circle>
      
      <!-- Stream 2 -->
      <path d="M1050 180 C1100 200, 1150 170, 1200 190" stroke="#d020ff" stroke-width="1" fill="none" opacity="0">
        <animate attributeName="opacity" values="0;0.8;0" dur="4s" repeatCount="indefinite" begin="1.5s" />
      </path>
      <circle cx="1050" cy="180" r="1.5" fill="#d020ff">
        <animate attributeName="cx" values="1050;1200" dur="4s" repeatCount="indefinite" begin="1.5s" />
        <animate attributeName="cy" values="180;190" dur="4s" repeatCount="indefinite" begin="1.5s" />
        <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite" begin="1.5s" />
      </circle>
      
      <!-- Stream 3 -->
      <path d="M1050 240 C1100 220, 1150 250, 1200 230" stroke="#d020ff" stroke-width="1" fill="none" opacity="0">
        <animate attributeName="opacity" values="0;0.8;0" dur="4s" repeatCount="indefinite" begin="2.5s" />
      </path>
      <circle cx="1050" cy="240" r="1.5" fill="#d020ff">
        <animate attributeName="cx" values="1050;1200" dur="4s" repeatCount="indefinite" begin="2.5s" />
        <animate attributeName="cy" values="240;230" dur="4s" repeatCount="indefinite" begin="2.5s" />
        <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite" begin="2.5s" />
      </circle>
      
      <!-- Stream 4 -->
      <path d="M1050 300 C1100 320, 1150 290, 1200 310" stroke="#d020ff" stroke-width="1" fill="none" opacity="0">
        <animate attributeName="opacity" values="0;0.8;0" dur="4s" repeatCount="indefinite" begin="3.5s" />
      </path>
      <circle cx="1050" cy="300" r="1.5" fill="#d020ff">
        <animate attributeName="cx" values="1050;1200" dur="4s" repeatCount="indefinite" begin="3.5s" />
        <animate attributeName="cy" values="300;310" dur="4s" repeatCount="indefinite" begin="3.5s" />
        <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite" begin="3.5s" />
      </circle>
    </g>
  </g>
  
  <!-- Center area data streams that cross the entire viewport -->
  <g filter="url(#whiteGlow)" opacity="0.6">
    <!-- Horizontal streams -->
    <path d="M0 50 L1200 50" stroke="#ffffff" stroke-width="0.5" stroke-dasharray="5,15" />
    <path d="M0 150 L1200 150" stroke="#ffffff" stroke-width="0.5" stroke-dasharray="3,10" />
    <path d="M0 250 L1200 250" stroke="#ffffff" stroke-width="0.5" stroke-dasharray="8,12" />
    <path d="M0 350 L1200 350" stroke="#ffffff" stroke-width="0.5" stroke-dasharray="2,8" />
    <path d="M0 450 L1200 450" stroke="#ffffff" stroke-width="0.5" stroke-dasharray="10,15" />
    <path d="M0 550 L1200 550" stroke="#ffffff" stroke-width="0.5" stroke-dasharray="4,10" />
    <path d="M0 650 L1200 650" stroke="#ffffff" stroke-width="0.5" stroke-dasharray="7,12" />
    <path d="M0 750 L1200 750" stroke="#ffffff" stroke-width="0.5" stroke-dasharray="5,8" />
    
    <!-- Moving dots on the horizontal lines -->
    <circle cx="0" cy="50" r="1" fill="#ffffff">
      <animate attributeName="cx" values="0;1200" dur="20s" repeatCount="indefinite" />
    </circle>
    <circle cx="0" cy="150" r="1" fill="#ffffff">
      <animate attributeName="cx" values="0;1200" dur="25s" repeatCount="indefinite" begin="5s" />
    </circle>
    <circle cx="0" cy="250" r="1" fill="#ffffff">
      <animate attributeName="cx" values="0;1200" dur="15s" repeatCount="indefinite" begin="2s" />
    </circle>
    <circle cx="0" cy="350" r="1" fill="#ffffff">
      <animate attributeName="cx" values="0;1200" dur="18s" repeatCount="indefinite" begin="7s" />
    </circle>
    <circle cx="0" cy="450" r="1" fill="#ffffff">
      <animate attributeName="cx" values="0;1200" dur="22s" repeatCount="indefinite" begin="1s" />
    </circle>
    <circle cx="0" cy="550" r="1" fill="#ffffff">
      <animate attributeName="cx" values="0;1200" dur="23s" repeatCount="indefinite" begin="4s" />
    </circle>
    <circle cx="0" cy="650" r="1" fill="#ffffff">
      <animate attributeName="cx" values="0;1200" dur="17s" repeatCount="indefinite" begin="3s" />
    </circle>
    <circle cx="0" cy="750" r="1" fill="#ffffff">
      <animate attributeName="cx" values="0;1200" dur="21s" repeatCount="indefinite" begin="8s" />
    </circle>
  </g>
  
  <!-- Digital code blocks floating across -->
  <g opacity="0.5">
    <text x="200" y="100" font-family="monospace" font-size="10" fill="#00ffaa">function init() {</text>
    <text x="200" y="115" font-family="monospace" font-size="10" fill="#00ffaa">  return system.boot();</text>
    <text x="200" y="130" font-family="monospace" font-size="10" fill="#00ffaa">}</text>
    
    <text x="800" y="200" font-family="monospace" font-size="10" fill="#ffaa00">class Quantum {</text>
    <text x="800" y="215" font-family="monospace" font-size="10" fill="#ffaa00">  constructor() {</text>
    <text x="800" y="230" font-family="monospace" font-size="10" fill="#ffaa00">    this.state = 0;</text>
    <text x="800" y="245" font-family="monospace" font-size="10" fill="#ffaa00">  }</text>
    <text x="800" y="260" font-family="monospace" font-size="10" fill="#ffaa00">}</text>
    
    <text x="100" y="350" font-family="monospace" font-size="10" fill="#ff00aa">async function connect() {</text>
    <text x="100" y="365" font-family="monospace" font-size="10" fill="#ff00aa">  await node.init();</text>
    <text x="100" y="380" font-family="monospace" font-size="10" fill="#ff00aa">  return status;</text>
    <text x="100" y="395" font-family="monospace" font-size="10" fill="#ff00aa">}</text>
    
    <text x="700" y="500" font-family="monospace" font-size="10" fill="#00d0ff">const matrix = [</text>
    <text x="700" y="515" font-family="monospace" font-size="10" fill="#00d0ff">  [1, 0, 1],</text>
    <text x="700" y="530" font-family="monospace" font-size="10" fill="#00d0ff">  [0, 1, 0],</text>
    <text x="700" y="545" font-family="monospace" font-size="10" fill="#00d0ff">  [1, 0, 1]</text>
    <text x="700" y="560" font-family="monospace" font-size="10" fill="#00d0ff">];</text>
  </g>
  
  <!-- Binary data streams cascading down -->
  <g opacity="0.2">
    <text x="250" y="50" font-family="monospace" font-size="8" fill="#ffffff">
      <animate attributeName="y" values="50;800" dur="20s" repeatCount="indefinite" />
      10101010 01010101 11001100
    </text>
    <text x="450" y="30" font-family="monospace" font-size="8" fill="#ffffff">
      <animate attributeName="y" values="30;800" dur="25s" repeatCount="indefinite" />
      01011010 10101010 11100111
    </text>
    <text x="650" y="20" font-family="monospace" font-size="8" fill="#ffffff">
      <animate attributeName="y" values="20;800" dur="18s" repeatCount="indefinite" />
      11001100 00110011 10101010
    </text>
    <text x="850" y="40" font-family="monospace" font-size="8" fill="#ffffff">
      <animate attributeName="y" values="40;800" dur="22s" repeatCount="indefinite" />
      00110011 11001100 01010101
    </text>
  </g>
  
  <!-- Additional glowing circles for visual interest -->
  <circle cx="300" cy="200" r="2" fill="#00ffff" filter="url(#blueGlow)">
    <animate attributeName="opacity" values="0.2;1;0.2" dur="5s" repeatCount="indefinite" />
  </circle>
  <circle cx="500" cy="350" r="2" fill="#ff00aa" filter="url(#purpleGlow)">
    <animate attributeName="opacity" values="0.2;1;0.2" dur="4s" repeatCount="indefinite" begin="1s" />
  </circle>
  <circle cx="700" cy="150" r="2" fill="#ffaa00" filter="url(#purpleGlow)">
    <animate attributeName="opacity" values="0.2;1;0.2" dur="6s" repeatCount="indefinite" begin="2s" />
  </circle>
  <circle cx="800" cy="450" r="2" fill="#00ffaa" filter="url(#blueGlow)">
    <animate attributeName="opacity" values="0.2;1;0.2" dur="5s" repeatCount="indefinite" begin="3s" />
  </circle>
  
  <!-- Overall pulse effect -->
  <rect width="100%" height="100%" fill="url(#bgGradient)" opacity="0.2">
    <animate attributeName="opacity" values="0.1;0.3;0.1" dur="8s" repeatCount="indefinite" />
  </rect>
</svg>