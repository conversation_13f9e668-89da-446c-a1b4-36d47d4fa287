<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080" preserveAspectRatio="none">
    <defs>
        <!-- 深空数据中心渐变 -->
        <linearGradient id="space-bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#0a0118" />
            <stop offset="50%" stop-color="#1a0b2e" />
            <stop offset="100%" stop-color="#16213e" />
        </linearGradient>
        
        <!-- 数据流渐变 -->
        <linearGradient id="dataflow-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="#00d4ff" />
            <stop offset="50%" stop-color="#8b5cf6" />
            <stop offset="100%" stop-color="#06b6d4" />
        </linearGradient>
        
        <!-- 量子渐变 -->
        <linearGradient id="quantum-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stop-color="#a855f7" />
            <stop offset="50%" stop-color="#3b82f6" />
            <stop offset="100%" stop-color="#06b6d4" />
        </linearGradient>
        
        <!-- 数据中心光效 -->
        <filter id="datacenter-glow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="6" result="blur"/>
            <feComposite in="SourceGraphic" in2="blur" operator="over"/>
        </filter>
        
        <!-- 粒子场图案 -->
        <pattern id="particle-field" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
            <circle cx="10" cy="10" r="0.5" fill="#3b82f6" opacity="0.3"/>
            <circle cx="40" cy="30" r="0.8" fill="#8b5cf6" opacity="0.4"/>
            <circle cx="70" cy="50" r="0.3" fill="#06b6d4" opacity="0.2"/>
            <circle cx="25" cy="70" r="0.6" fill="#a855f7" opacity="0.3"/>
        </pattern>
        
        <!-- 数据核心辉光 -->
        <radialGradient id="core-glow" cx="50%" cy="50%" r="80%">
            <stop offset="0%" stop-color="#8b5cf6" stop-opacity="0.5" />
            <stop offset="50%" stop-color="#3b82f6" stop-opacity="0.3" />
            <stop offset="100%" stop-color="#06b6d4" stop-opacity="0" />
        </radialGradient>
        
        <!-- 量子波动 -->
        <radialGradient id="quantum-wave" cx="50%" cy="50%" r="60%">
            <stop offset="0%" stop-color="#00d4ff" stop-opacity="0.4" />
            <stop offset="100%" stop-color="#00d4ff" stop-opacity="0" />
        </radialGradient>
    </defs>
    
    <!-- 主背景 -->
    <rect width="1920" height="1080" fill="url(#space-bg-gradient)"/>
    
    <!-- 粒子场 -->
    <rect width="1920" height="1080" fill="url(#particle-field)"/>
    
    <!-- 星域背景点 -->
    <g opacity="0.7">
        <circle cx="150" cy="100" r="1" fill="#00d4ff" opacity="0.8">
            <animate attributeName="opacity" values="0.8;0.3;0.8" dur="4s" repeatCount="indefinite"/>
        </circle>
        <circle cx="300" cy="180" r="1.5" fill="#8b5cf6" opacity="0.6">
            <animate attributeName="opacity" values="0.6;1;0.6" dur="5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="800" cy="120" r="0.8" fill="#06b6d4" opacity="0.9">
            <animate attributeName="opacity" values="0.9;0.4;0.9" dur="3s" repeatCount="indefinite"/>
        </circle>
        <circle cx="1200" cy="200" r="1.2" fill="#a855f7" opacity="0.7">
            <animate attributeName="opacity" values="0.7;0.2;0.7" dur="6s" repeatCount="indefinite"/>
        </circle>
        <circle cx="1600" cy="150" r="0.6" fill="#00d4ff" opacity="0.5">
            <animate attributeName="opacity" values="0.5;1;0.5" dur="4.5s" repeatCount="indefinite"/>
        </circle>
        
        <circle cx="200" cy="900" r="1.3" fill="#8b5cf6" opacity="0.8">
            <animate attributeName="opacity" values="0.8;0.3;0.8" dur="7s" repeatCount="indefinite"/>
        </circle>
        <circle cx="600" cy="950" r="0.9" fill="#06b6d4" opacity="0.6">
            <animate attributeName="opacity" values="0.6;1;0.6" dur="3.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="1000" cy="980" r="1.1" fill="#00d4ff" opacity="0.7">
            <animate attributeName="opacity" values="0.7;0.2;0.7" dur="5.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="1400" cy="920" r="0.7" fill="#a855f7" opacity="0.9">
            <animate attributeName="opacity" values="0.9;0.4;0.9" dur="4s" repeatCount="indefinite"/>
        </circle>
        <circle cx="1800" cy="950" r="1.4" fill="#3b82f6" opacity="0.5">
            <animate attributeName="opacity" values="0.5;1;0.5" dur="6s" repeatCount="indefinite"/>
        </circle>
    </g>
    
    <!-- 中央数据核心 -->
    <g opacity="0.9">
        <circle cx="960" cy="540" r="180" fill="url(#core-glow)" filter="url(#datacenter-glow)"/>
        
        <!-- 旋转数据环 -->
        <circle cx="960" cy="540" r="120" fill="none" stroke="#8b5cf6" stroke-width="2" opacity="0.6" stroke-dasharray="20 10">
            <animateTransform attributeName="transform" attributeType="XML" type="rotate" values="0 960 540;360 960 540" dur="20s" repeatCount="indefinite"/>
        </circle>
        <circle cx="960" cy="540" r="100" fill="none" stroke="#3b82f6" stroke-width="1.5" opacity="0.5" stroke-dasharray="15 8">
            <animateTransform attributeName="transform" attributeType="XML" type="rotate" values="360 960 540;0 960 540" dur="15s" repeatCount="indefinite"/>
        </circle>
        <circle cx="960" cy="540" r="80" fill="none" stroke="#06b6d4" stroke-width="1" opacity="0.4" stroke-dasharray="10 5">
            <animateTransform attributeName="transform" attributeType="XML" type="rotate" values="0 960 540;360 960 540" dur="12s" repeatCount="indefinite"/>
        </circle>
        
        <!-- 中央核心 -->
        <circle cx="960" cy="540" r="40" fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="2" filter="url(#datacenter-glow)"/>
        <circle cx="960" cy="540" r="20" fill="#8b5cf6" opacity="0.8">
            <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
        </circle>
        
        <!-- 数据流射线 -->
        <line x1="960" y1="540" x2="1080" y2="540" stroke="url(#dataflow-gradient)" stroke-width="3" opacity="0.7">
            <animate attributeName="opacity" values="0.7;0.3;0.7" dur="3s" repeatCount="indefinite"/>
        </line>
        <line x1="960" y1="540" x2="840" y2="540" stroke="url(#dataflow-gradient)" stroke-width="3" opacity="0.7">
            <animate attributeName="opacity" values="0.3;0.7;0.3" dur="3s" repeatCount="indefinite"/>
        </line>
        <line x1="960" y1="540" x2="960" y2="420" stroke="url(#dataflow-gradient)" stroke-width="3" opacity="0.6">
            <animate attributeName="opacity" values="0.6;0.2;0.6" dur="4s" repeatCount="indefinite"/>
        </line>
        <line x1="960" y1="540" x2="960" y2="660" stroke="url(#dataflow-gradient)" stroke-width="3" opacity="0.6">
            <animate attributeName="opacity" values="0.2;0.6;0.2" dur="4s" repeatCount="indefinite"/>
        </line>
    </g>
    
    <!-- 左侧数据服务器集群 -->
    <g opacity="0.8">
        <!-- 主服务器塔 -->
        <rect x="150" y="300" width="80" height="400" rx="10" fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" stroke-width="2" filter="url(#datacenter-glow)"/>
        
        <!-- 服务器单元 -->
        <rect x="160" y="320" width="60" height="20" rx="3" fill="rgba(139, 92, 246, 0.4)" stroke="#8b5cf6" stroke-width="1"/>
        <rect x="170" y="325" width="40" height="3" fill="#8b5cf6" opacity="0.8"/>
        <rect x="170" y="332" width="40" height="3" fill="#06b6d4" opacity="0.6"/>
        
        <rect x="160" y="360" width="60" height="20" rx="3" fill="rgba(6, 182, 212, 0.4)" stroke="#06b6d4" stroke-width="1"/>
        <rect x="170" y="365" width="40" height="3" fill="#06b6d4" opacity="0.8"/>
        <rect x="170" y="372" width="40" height="3" fill="#8b5cf6" opacity="0.6"/>
        
        <rect x="160" y="400" width="60" height="20" rx="3" fill="rgba(139, 92, 246, 0.4)" stroke="#8b5cf6" stroke-width="1"/>
        <rect x="170" y="405" width="40" height="3" fill="#8b5cf6" opacity="0.8">
            <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2s" repeatCount="indefinite"/>
        </rect>
        <rect x="170" y="412" width="40" height="3" fill="#06b6d4" opacity="0.6">
            <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
        </rect>
        
        <!-- 数据传输线 -->
        <path d="M 230,400 Q 400,350 580,400" fill="none" stroke="#8b5cf6" stroke-width="2" opacity="0.5">
            <animate attributeName="opacity" values="0.5;0.9;0.5" dur="4s" repeatCount="indefinite"/>
        </path>
        
        <!-- 状态指示器 -->
        <circle cx="210" cy="310" r="4" fill="#00d4ff" filter="url(#datacenter-glow)">
            <animate attributeName="opacity" values="1;0.4;1" dur="1.5s" repeatCount="indefinite"/>
        </circle>
    </g>
    
    <!-- 右侧量子计算阵列 -->
    <g opacity="0.8">
        <!-- 量子处理器 -->
        <rect x="1680" y="250" width="120" height="500" rx="15" fill="rgba(168, 85, 247, 0.2)" stroke="#a855f7" stroke-width="2" filter="url(#datacenter-glow)"/>
        
        <!-- 量子比特可视化 -->
        <circle cx="1740" cy="300" r="15" fill="url(#quantum-wave)" stroke="#00d4ff" stroke-width="2">
            <animate attributeName="r" values="15;25;15" dur="3s" repeatCount="indefinite"/>
        </circle>
        <circle cx="1740" cy="300" r="8" fill="#00d4ff" opacity="0.9"/>
        
        <circle cx="1740" cy="380" r="15" fill="url(#quantum-wave)" stroke="#a855f7" stroke-width="2">
            <animate attributeName="r" values="15;20;15" dur="4s" repeatCount="indefinite"/>
        </circle>
        <circle cx="1740" cy="380" r="8" fill="#a855f7" opacity="0.9"/>
        
        <circle cx="1740" cy="460" r="15" fill="url(#quantum-wave)" stroke="#06b6d4" stroke-width="2">
            <animate attributeName="r" values="15;22;15" dur="3.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="1740" cy="460" r="8" fill="#06b6d4" opacity="0.9"/>
        
        <!-- 量子纠缠线 -->
        <path d="M 1740,300 Q 1720,340 1740,380 Q 1760,420 1740,460" 
              fill="none" stroke="url(#quantum-gradient)" stroke-width="2" opacity="0.6">
            <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
        </path>
        
        <!-- 处理状态 -->
        <rect x="1690" y="280" width="100" height="8" rx="4" fill="rgba(0, 212, 255, 0.3)"/>
        <rect x="1690" y="280" width="70" height="8" rx="4" fill="#00d4ff">
            <animate attributeName="width" values="70;100;70" dur="5s" repeatCount="indefinite"/>
        </rect>
        
        <!-- 量子连接到中央核心 -->
        <path d="M 1680,450 Q 1400,400 1140,500" fill="none" stroke="#a855f7" stroke-width="2" opacity="0.4" stroke-dasharray="15 10">
            <animate attributeName="stroke-dashoffset" values="0;25;0" dur="3s" repeatCount="indefinite"/>
        </path>
    </g>
    
    <!-- 数据传输隧道 -->
    <g opacity="0.6">
        <!-- 上方数据流 -->
        <path d="M 0,200 Q 480,150 960,200 T 1920,200" 
              fill="none" stroke="url(#dataflow-gradient)" stroke-width="4" opacity="0.5">
            <animate attributeName="opacity" values="0.5;0.8;0.5" dur="6s" repeatCount="indefinite"/>
        </path>
        
        <!-- 数据包流动 -->
        <circle cx="100" cy="200" r="6" fill="#00d4ff" opacity="0.8">
            <animateMotion dur="8s" repeatCount="indefinite">
                <mpath href="#data-path-1"/>
            </animateMotion>
        </circle>
        
        <!-- 下方数据流 -->
        <path d="M 0,880 Q 480,930 960,880 T 1920,880" 
              fill="none" stroke="url(#dataflow-gradient)" stroke-width="4" opacity="0.5">
            <animate attributeName="opacity" values="0.8;0.5;0.8" dur="7s" repeatCount="indefinite"/>
        </path>
        
        <circle cx="100" cy="880" r="5" fill="#8b5cf6" opacity="0.7">
            <animateMotion dur="10s" repeatCount="indefinite">
                <mpath href="#data-path-2"/>
            </animateMotion>
        </circle>
    </g>
    
    <!-- 隐藏的路径定义 -->
    <defs>
        <path id="data-path-1" d="M 0,200 Q 480,150 960,200 T 1920,200"/>
        <path id="data-path-2" d="M 0,880 Q 480,930 960,880 T 1920,880"/>
    </defs>
    
    <!-- 顶部数据状态栏 -->
    <rect x="0" y="0" width="1920" height="50" fill="rgba(10, 1, 24, 0.9)" stroke="#3b82f6" stroke-width="1"/>
    <rect x="50" y="15" width="300" height="20" rx="10" fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="1"/>
    <text x="70" y="28" fill="#3b82f6" font-family="monospace" font-size="10">DATA CENTER STATUS: OPTIMAL</text>
    
    <rect x="400" y="15" width="200" height="20" rx="10" fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="1"/>
    <text x="420" y="28" fill="#8b5cf6" font-family="monospace" font-size="10">QUANTUM: ACTIVE</text>
    
    <rect x="650" y="15" width="250" height="20" rx="10" fill="rgba(6, 182, 212, 0.3)" stroke="#06b6d4" stroke-width="1"/>
    <text x="670" y="28" fill="#06b6d4" font-family="monospace" font-size="10">PROCESSING: 847.3 PB/s</text>
    
    <!-- 底部系统信息 -->
    <rect x="0" y="1030" width="1920" height="50" fill="rgba(10, 1, 24, 0.9)" stroke="#8b5cf6" stroke-width="1"/>
    <text x="50" y="1055" fill="#8b5cf6" font-family="monospace" font-size="11">DEEP SPACE DATA CENTER - QUANTUM NETWORK v3.0</text>
    <text x="1600" y="1055" fill="#06b6d4" font-family="monospace" font-size="11">UPLINK: STABLE</text>
    
    <!-- 边框装饰 -->
    <rect x="10" y="10" width="1900" height="1060" rx="25" fill="none" stroke="url(#dataflow-gradient)" stroke-width="2" opacity="0.4"/>
    <rect x="30" y="30" width="1860" height="1020" rx="20" fill="none" stroke="#3b82f6" stroke-width="1" opacity="0.3" stroke-dasharray="20 15"/>
    
    <!-- 四角科技装饰 -->
    <g opacity="0.5">
        <!-- 左上 -->
        <path d="M 0,0 L 100,0 L 120,20 L 20,20 L 20,120 L 0,100 Z" fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="1"/>
        
        <!-- 右上 -->
        <path d="M 1920,0 L 1820,0 L 1800,20 L 1900,20 L 1900,120 L 1920,100 Z" fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="1"/>
        
        <!-- 左下 -->
        <path d="M 0,1080 L 100,1080 L 120,1060 L 20,1060 L 20,960 L 0,980 Z" fill="rgba(6, 182, 212, 0.3)" stroke="#06b6d4" stroke-width="1"/>
        
        <!-- 右下 -->
        <path d="M 1920,1080 L 1820,1080 L 1800,1060 L 1900,1060 L 1900,960 L 1920,980 Z" fill="rgba(168, 85, 247, 0.3)" stroke="#a855f7" stroke-width="1"/>
    </g>
</svg> 