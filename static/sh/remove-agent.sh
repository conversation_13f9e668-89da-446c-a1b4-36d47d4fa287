#!/bin/bash
# DStatus探针卸载脚本

echo "[步骤1/3] 停止服务..."
# 尝试多种方式停止服务
systemctl stop nekonekostatus 2>/dev/null || \
rc-service nekonekostatus stop 2>/dev/null || \
/etc/init.d/nekonekostatus stop 2>/dev/null || \
/usr/local/bin/nekonekostatus-stop 2>/dev/null || \
pkill -f "neko-status -c /etc/neko-status/config.yaml" 2>/dev/null || \
echo "服务未运行或无法停止"
# 强制结束残留进程
pkill -9 -f "neko-status" 2>/dev/null || true
sleep 1 # 等待进程退出

echo "[步骤2/3] 删除服务文件..."
# 删除服务文件
if command -v systemctl &> /dev/null; then
    systemctl disable nekonekostatus 2>/dev/null
    rm -f /etc/systemd/system/nekonekostatus.service
    systemctl daemon-reload
    echo "删除 systemd 服务成功"
elif command -v rc-service &> /dev/null && [ -f /etc/alpine-release ]; then
    rc-update del nekonekostatus default 2>/dev/null
    rm -f /etc/init.d/nekonekostatus
    echo "删除 OpenRC 服务成功"
elif [ -f /etc/init.d/nekonekostatus ]; then
    if command -v update-rc.d &> /dev/null; then
        update-rc.d -f nekonekostatus remove 2>/dev/null
    elif command -v chkconfig &> /dev/null; then
        chkconfig nekonekostatus off 2>/dev/null
    fi
    rm -f /etc/init.d/nekonekostatus
    echo "删除 init.d 服务成功"
else
    rm -f /usr/local/bin/nekonekostatus-start /usr/local/bin/nekonekostatus-stop
    echo "删除启动脚本成功"
fi

echo "[步骤3/3] 删除探针和配置文件..."
# 删除探针和配置文件
rm -f /usr/bin/neko-status
rm -rf /etc/neko-status
rm -f /var/run/nekonekostatus.pid
echo "删除探针和配置文件成功"

echo "[完成] 探针完全删除成功！" 