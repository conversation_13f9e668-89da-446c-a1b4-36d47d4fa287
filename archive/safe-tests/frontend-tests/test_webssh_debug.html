<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>WebSSH Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-button { 
            padding: 10px 20px; 
            margin: 10px; 
            background: #4CAF50; 
            color: white; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
        }
        .log { 
            background: #f5f5f5; 
            padding: 10px; 
            margin: 10px 0; 
            border: 1px solid #ddd; 
            font-family: monospace; 
            white-space: pre-wrap; 
        }
        .feature-locked {
            opacity: 0.5;
            cursor: not-allowed !important;
        }
    </style>
</head>
<body>
    <h1>WebSSH功能调试测试</h1>
    
    <div>
        <button class="test-button" onclick="webssh('test')" data-feature="webssh">
            WebSSH测试按钮
        </button>
    </div>
    
    <div>
        <button class="test-button" onclick="runTests()">运行测试</button>
        <button class="test-button" onclick="clearLog()">清除日志</button>
    </div>
    
    <div id="log" class="log"></div>
    
    <script>
        const logDiv = document.getElementById('log');
        
        function log(message, data) {
            const timestamp = new Date().toLocaleTimeString();
            let logMessage = `[${timestamp}] ${message}`;
            if (data !== undefined) {
                logMessage += ': ' + JSON.stringify(data, null, 2);
            }
            logDiv.textContent += logMessage + '\n';
            console.log(message, data);
        }
        
        function clearLog() {
            logDiv.textContent = '';
        }
        
        // 原始的webssh函数
        function webssh(id) {
            log('原始webssh函数被调用', id);
            alert('WebSSH功能被调用！');
        }
        
        // 手动测试功能
        async function runTests() {
            log('===== 开始测试 =====');
            
            // 1. 检查FeatureWall
            if (typeof window.FeatureWall === 'undefined') {
                log('错误: FeatureWall未定义');
                
                // 尝试手动加载
                log('尝试手动加载feature-wall.js...');
                const script = document.createElement('script');
                script.src = '/js/feature-wall.js';
                script.onload = () => {
                    log('feature-wall.js加载成功');
                    setTimeout(runTestsAfterLoad, 1000);
                };
                script.onerror = () => {
                    log('feature-wall.js加载失败');
                };
                document.head.appendChild(script);
                return;
            }
            
            runTestsAfterLoad();
        }
        
        async function runTestsAfterLoad() {
            // 2. 获取功能墙信息
            const info = window.FeatureWall.getFeatureWallInfo();
            log('功能墙信息', info);
            
            // 3. 检查WebSSH权限
            const hasWebSSH = window.FeatureWall.hasFeature('webssh');
            log('WebSSH权限', hasWebSSH);
            
            // 4. 手动调用API
            try {
                const response = await fetch('/api/license/feature-wall');
                const data = await response.json();
                log('API响应', data);
            } catch (error) {
                log('API调用失败', error.message);
            }
            
            // 5. 检查按钮状态
            const button = document.querySelector('[data-feature="webssh"]');
            log('按钮状态', {
                exists: !!button,
                classList: button ? Array.from(button.classList) : [],
                onclick: button && button.onclick ? '已设置' : '未设置',
                title: button ? button.title : null
            });
            
            // 6. 手动应用限制
            log('手动应用功能限制...');
            window.FeatureWall.applyFeatureRestrictions();
            
            // 7. 再次检查按钮
            setTimeout(() => {
                const buttonAfter = document.querySelector('[data-feature="webssh"]');
                log('应用限制后的按钮状态', {
                    classList: buttonAfter ? Array.from(buttonAfter.classList) : [],
                    onclick: buttonAfter && buttonAfter.onclick ? '已设置' : '未设置',
                    title: buttonAfter ? buttonAfter.title : null
                });
            }, 500);
        }
        
        // 页面加载完成后自动运行测试
        window.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成');
            // 延迟一秒确保所有脚本加载完成
            setTimeout(runTests, 1000);
        });
    </script>
    
    <!-- 尝试加载feature-wall.js -->
    <script src="/js/feature-wall.js"></script>
</body>
</html>