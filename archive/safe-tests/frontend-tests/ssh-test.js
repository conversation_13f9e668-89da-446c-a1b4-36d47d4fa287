/**
 * SSH连接测试相关函数
 */

// 函数别名，保持与HTML调用一致
const testSSHConnection = testSSH;

// SSH连接测试
async function testSSH() {
    try {
        startloading();

        // 获取SSH配置
        const sshConfig = {
            host: V('edit_ssh_host'),
            port: parseInt(V('edit_ssh_port')) || 22,
            username: V('edit_ssh_username'),
            password: V('edit_ssh_password'),
            privateKey: V('edit_ssh_privateKey')
        };

        // 验证必填字段
        if (!sshConfig.host) {
            notice('请输入SSH主机地址', 'error');
            return;
        }

        if (!sshConfig.username) {
            notice('请输入SSH用户名', 'error');
            return;
        }

        // 验证认证方式
        if (!sshConfig.password && !sshConfig.privateKey) {
            notice('请输入SSH密码或私钥', 'error');
            return;
        }

        console.log('SSH测试配置:', {
            host: sshConfig.host,
            port: sshConfig.port,
            username: sshConfig.username,
            hasPassword: !!sshConfig.password,
            hasPrivateKey: !!sshConfig.privateKey
        });

        // 发送测试请求
        const res = await postjson('/admin/test-ssh', sshConfig);

        if (res.status) {
            notice('SSH连接测试成功', 'success');
            console.log('SSH测试结果:', res.data);

            // 如果返回了系统信息，显示详细信息
            if (res.data && res.data.systemInfo) {
                const info = res.data.systemInfo;
                let details = 'SSH连接成功！\\n\\n系统信息：\\n';
                if (info.hostname) details += `主机名: ${info.hostname}\\n`;
                if (info.platform) details += `系统: ${info.platform}\\n`;
                if (info.arch) details += `架构: ${info.arch}\\n`;
                if (info.kernel) details += `内核: ${info.kernel}\\n`;
                if (info.uptime) details += `运行时间: ${info.uptime}\\n`;

                // 使用更友好的方式显示详细信息
                setTimeout(() => {
                    if (confirm(details + '\\n是否查看完整的系统信息？')) {
                        console.log('完整系统信息:', info);
                    }
                }, 500);
            }
        } else {
            const errorMsg = res.data || 'SSH连接测试失败';
            notice(errorMsg, 'error');
            console.error('SSH测试失败:', errorMsg);

            // 提供常见问题的解决建议
            if (errorMsg.includes('timeout') || errorMsg.includes('ETIMEDOUT')) {
                setTimeout(() => {
                    notice('连接超时，请检查：\\n1. 主机地址是否正确\\n2. 端口是否开放\\n3. 网络连接是否正常', 'info');
                }, 1000);
            } else if (errorMsg.includes('authentication') || errorMsg.includes('Authentication')) {
                setTimeout(() => {
                    notice('认证失败，请检查：\\n1. 用户名是否正确\\n2. 密码或私钥是否正确\\n3. 用户是否有SSH登录权限', 'info');
                }, 1000);
            } else if (errorMsg.includes('refused') || errorMsg.includes('ECONNREFUSED')) {
                setTimeout(() => {
                    notice('连接被拒绝，请检查：\\n1. SSH服务是否启动\\n2. 端口是否正确\\n3. 防火墙设置', 'info');
                }, 1000);
            }
        }
    } catch (error) {
        console.error('SSH测试异常:', error);
        notice('SSH测试失败: ' + (error.message || '发生未知错误'), 'error');
    } finally {
        endloading();
    }
}

// 切换SSH认证方式
function toggleSSHAuth() {
    const passwordContainer = document.getElementById('ssh-password-container');
    const privateKeyContainer = document.getElementById('ssh-privatekey-container');
    const authToggle = document.getElementById('ssh-auth-toggle');
    const authText = document.getElementById('ssh-auth-text');

    // 获取当前状态（true = 密码认证，false = 私钥认证）
    const isPasswordAuth = !passwordContainer.classList.contains('hidden');

    if (isPasswordAuth) {
        // 切换到私钥认证
        passwordContainer.classList.add('hidden');
        privateKeyContainer.classList.remove('hidden');
        authToggle.classList.add('translate-x-5', 'bg-blue-500');
        authToggle.classList.remove('bg-slate-400');
        authText.textContent = '私钥认证';
    } else {
        // 切换到密码认证
        passwordContainer.classList.remove('hidden');
        privateKeyContainer.classList.add('hidden');
        authToggle.classList.remove('translate-x-5', 'bg-blue-500');
        authToggle.classList.add('bg-slate-400');
        authText.textContent = '密码认证';
    }
}

// 切换API模式
function toggleAPIMode() {
    const checkbox = document.getElementById('edit_api_mode');
    const toggle = document.getElementById('edit-api-mode-toggle');
    const text = document.getElementById('edit-api-mode-text');

    // 切换复选框状态
    checkbox.checked = !checkbox.checked;

    // 更新开关样式和文本
    if (checkbox.checked) {
        // 主动模式
        toggle.classList.add('translate-x-5', 'bg-blue-500');
        toggle.classList.remove('bg-slate-400');
        text.textContent = '主动模式';
    } else {
        // 被动模式
        toggle.classList.remove('translate-x-5', 'bg-blue-500');
        toggle.classList.add('bg-slate-400');
        text.textContent = '被动模式';
    }
}
