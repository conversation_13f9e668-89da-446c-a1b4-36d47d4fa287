<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSSH功能墙测试</title>
    <link rel="stylesheet" href="/css/material-icons.css">
    <link rel="stylesheet" href="/css/style.min.css">
    <link rel="stylesheet" href="/css/components/theme.css">
    <link rel="stylesheet" href="/css/components/feature-wall.css">
    <style>
        body {
            background: #0f172a;
            color: #e2e8f0;
            padding: 40px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: #1e293b;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid #334155;
        }
        .btn-icon {
            padding: 8px 16px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn-icon:hover {
            background: #2563eb;
        }
        .status {
            padding: 16px;
            background: #1e293b;
            border-radius: 4px;
            margin-bottom: 16px;
        }
        .log-output {
            background: #0f172a;
            padding: 16px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSSH功能墙测试页面</h1>
        
        <div class="card">
            <h2>测试按钮</h2>
            <p>这是一个模拟stat.html页面中的WebSSH按钮：</p>
            <button onclick="webssh('test-server-id')" class="btn-icon" title="Web SSH" data-feature="webssh">
                <i class="material-icons">open_in_browser</i>
                WebSSH
            </button>
        </div>
        
        <div class="card">
            <h2>功能墙状态</h2>
            <div id="status" class="status">
                <div>正在加载...</div>
            </div>
            
            <button onclick="refreshStatus()" class="btn-icon">
                <i class="material-icons">refresh</i>
                刷新状态
            </button>
            
            <button onclick="testRestriction()" class="btn-icon">
                <i class="material-icons">bug_report</i>
                测试限制
            </button>
        </div>
        
        <div class="card">
            <h2>调试日志</h2>
            <div id="log" class="log-output"></div>
            <button onclick="clearLog()" class="btn-icon">
                <i class="material-icons">clear</i>
                清除日志
            </button>
        </div>
    </div>
    
    <script>
        // 日志功能
        const logElement = document.getElementById('log');
        
        function log(message, data) {
            const time = new Date().toLocaleTimeString();
            let logMessage = `[${time}] ${message}`;
            if (data !== undefined) {
                logMessage += '\n' + JSON.stringify(data, null, 2);
            }
            logElement.textContent += logMessage + '\n\n';
            console.log(message, data);
        }
        
        function clearLog() {
            logElement.textContent = '';
        }
        
        // 原始的webssh函数
        function webssh(serverId) {
            log('✅ WebSSH函数被调用！', { serverId });
            alert('WebSSH功能被调用！服务器ID: ' + serverId);
        }
        
        // 刷新状态
        async function refreshStatus() {
            const statusElement = document.getElementById('status');
            
            try {
                // 1. 检查FeatureWall是否存在
                if (typeof window.FeatureWall === 'undefined') {
                    statusElement.innerHTML = '<div style="color: #ef4444;">❌ FeatureWall未加载</div>';
                    log('错误: FeatureWall未定义');
                    return;
                }
                
                // 2. 获取功能墙信息
                const info = window.FeatureWall.getFeatureWallInfo();
                const hasWebSSH = window.FeatureWall.hasFeature('webssh');
                
                // 3. 调用API获取最新状态
                const response = await fetch('/api/license/feature-wall');
                const apiData = await response.json();
                
                // 4. 更新状态显示
                statusElement.innerHTML = `
                    <div><strong>FeatureWall状态:</strong> <span style="color: #10b981;">✅ 已加载</span></div>
                    <div><strong>当前套餐:</strong> ${info?.currentPlan?.name || '未知'}</div>
                    <div><strong>WebSSH权限:</strong> <span style="color: ${hasWebSSH ? '#10b981' : '#ef4444'};">${hasWebSSH ? '✅ 已启用' : '❌ 已禁用'}</span></div>
                    <div><strong>动态功能墙:</strong> ${info?.isDynamic ? '是' : '否'}</div>
                    <div><strong>数据格式:</strong> ${info?.dataFormat || '未知'}</div>
                    <hr style="margin: 16px 0; border-color: #334155;">
                    <div><strong>API响应 - WebSSH状态:</strong></div>
                    <div>available: ${apiData?.features?.webssh?.available ? '✅' : '❌'}</div>
                    <div>requiredPlan: ${apiData?.features?.webssh?.requiredPlan || '未知'}</div>
                `;
                
                log('状态已刷新', { featureWallInfo: info, apiData });
                
            } catch (error) {
                statusElement.innerHTML = '<div style="color: #ef4444;">❌ 获取状态失败</div>';
                log('获取状态失败', error.message);
            }
        }
        
        // 测试限制功能
        function testRestriction() {
            log('开始测试限制功能...');
            
            // 1. 查找WebSSH按钮
            const button = document.querySelector('[data-feature="webssh"]');
            if (!button) {
                log('错误: 找不到WebSSH按钮');
                return;
            }
            
            log('找到WebSSH按钮', {
                classList: Array.from(button.classList),
                title: button.title,
                onclick: button.onclick ? '已设置' : '未设置',
                hasFeatureLocked: button.classList.contains('feature-locked')
            });
            
            // 2. 手动应用限制
            if (window.FeatureWall) {
                log('手动调用applyFeatureRestrictions...');
                window.FeatureWall.applyFeatureRestrictions();
                
                // 等待一下再检查
                setTimeout(() => {
                    log('应用限制后的按钮状态', {
                        classList: Array.from(button.classList),
                        title: button.title,
                        onclick: button.onclick ? '已设置' : '未设置',
                        hasFeatureLocked: button.classList.contains('feature-locked')
                    });
                }, 500);
            }
        }
        
        // 监听DOMContentLoaded
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成');
            
            // 延迟一秒后刷新状态
            setTimeout(() => {
                log('自动刷新状态...');
                refreshStatus();
            }, 1000);
        });
        
        // 监听FeatureWall初始化
        let checkInterval = setInterval(() => {
            if (window.FeatureWall) {
                log('FeatureWall已加载');
                clearInterval(checkInterval);
            }
        }, 500);
    </script>
    
    <!-- 引入功能墙脚本 -->
    <script src="/js/feature-wall.js"></script>
</body>
</html>