/**
 * 通用SSH连接测试函数
 * 支持 add 和 edit 页面
 */

async function testSSHConnectionCommon(prefix = 'edit') {
    try {
        // 显示加载状态，重置结果区域
        startloading();
        const resultEl = document.getElementById('ssh-test-result');
        if (resultEl) {
            resultEl.classList.add('hidden');
            resultEl.textContent = '';
            resultEl.classList.remove('text-green-500', 'text-red-500');
        }

        // 获取SSH配置
        const sshConfig = {
            host: V(`${prefix}_ssh_host`),
            port: parseInt(V(`${prefix}_ssh_port`)) || 22,
            username: V(`${prefix}_ssh_username`),
            password: V(`${prefix}_ssh_password`),
            privateKey: V(`${prefix}_ssh_privateKey`)
        };

        // 如果是编辑页面，添加服务器ID用于获取已保存密码
        if (prefix === 'edit') {
            const sidElement = document.getElementById('sid');
            if (sidElement) {
                const serverId = sidElement.value;
                if (serverId) {
                    sshConfig.serverId = serverId;
                    
                    // 检测是否使用占位符密码
                    if (sshConfig.password && /^••+$/.test(sshConfig.password)) {
                        console.log('[SSH测试] 检测到占位符密码，将使用已保存的密码进行测试');
                    }
                }
            }
        }

        // 添加私钥密码（如果有）
        const passphraseId = `${prefix}_ssh_passphrase`;
        if (document.getElementById(passphraseId)) {
            const passphrase = V(passphraseId);
            if (passphrase && passphrase.trim() !== '') {
                sshConfig.passphrase = passphrase;
            }
        }

        // 验证必填字段
        if (!sshConfig.host) {
            notice('请输入SSH主机地址', 'error');
            return;
        }

        if (!sshConfig.username) {
            notice('请输入SSH用户名', 'error');
            return;
        }

        // 验证认证方式
        if (!sshConfig.password && !sshConfig.privateKey) {
            notice('请输入SSH密码或私钥', 'error');
            return;
        }

        console.log('SSH测试配置:', {
            host: sshConfig.host,
            port: sshConfig.port,
            username: sshConfig.username,
            hasPassword: !!sshConfig.password,
            hasPrivateKey: !!sshConfig.privateKey,
            hasPassphrase: !!sshConfig.passphrase
        });

        // 发送测试请求
        const res = await postjson('/admin/test-ssh', sshConfig);

        if (res.status) {
            // 测试成功
            notice('SSH连接测试成功', 'success');
            
            // 更新结果显示
            if (resultEl) {
                resultEl.textContent = `✅ 连接成功 - ${res.data || 'SSH服务正常'}`;
                resultEl.classList.remove('hidden', 'text-red-500');
                resultEl.classList.add('text-green-500');
            }
        } else {
            // 测试失败
            const errorMessage = res.msg || res.data || 'SSH连接测试失败';
            notice(errorMessage, 'error');
            
            // 更新结果显示
            if (resultEl) {
                resultEl.textContent = `❌ 连接失败 - ${errorMessage}`;
                resultEl.classList.remove('hidden', 'text-green-500');
                resultEl.classList.add('text-red-500');
            }
        }
    } catch (error) {
        console.error('SSH测试失败:', error);
        notice(error.message || 'SSH连接测试失败', 'error');
        
        const resultEl = document.getElementById('ssh-test-result');
        if (resultEl) {
            resultEl.textContent = `❌ 测试失败 - ${error.message}`;
            resultEl.classList.remove('hidden', 'text-green-500');
            resultEl.classList.add('text-red-500');
        }
    } finally {
        endloading();
    }
}

// 为 add 页面创建别名函数
function testSSHConnectionForAdd() {
    return testSSHConnectionCommon('add');
}

// 为 edit 页面创建别名函数
function testSSHConnectionForEdit() {
    return testSSHConnectionCommon('edit');
}

// 保持向后兼容
const testSSHConnection = testSSHConnectionForEdit;