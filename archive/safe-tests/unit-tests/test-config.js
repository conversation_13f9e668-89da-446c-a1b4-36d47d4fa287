/**
 * 测试环境配置
 * 支持SQLite和PostgreSQL双数据库测试环境
 */

const path = require('path');
const fs = require('fs');

// 测试数据目录
const TEST_DATA_DIR = path.join(__dirname, '../test-data');
if (!fs.existsSync(TEST_DATA_DIR)) {
    fs.mkdirSync(TEST_DATA_DIR, { recursive: true });
}

const config = {
    // 测试环境类型
    environment: 'test',
    
    // 测试超时设置
    timeout: {
        unit: 10000,      // 10秒
        integration: 30000, // 30秒
        performance: 120000, // 2分钟
        stress: 300000    // 5分钟
    },

    // 数据库配置
    databases: {
        sqlite: {
            type: 'sqlite',
            path: path.join(TEST_DATA_DIR, 'test.db'),
            backup_path: path.join(TEST_DATA_DIR, 'backups'),
            config: {
                // WAL模式配置
                journal_mode: 'WAL',
                synchronous: 'NORMAL',
                cache_size: -64000,
                temp_store: 'MEMORY',
                mmap_size: 268435456,
                busy_timeout: 5000
            }
        },
        postgresql: {
            type: 'postgresql',
            connection: process.env.TEST_POSTGRES_URL || 'postgresql://postgres:password@localhost:5432/dstatus_test',
            config: {
                // 连接池配置
                max: 10,
                idleTimeoutMillis: 30000,
                connectionTimeoutMillis: 5000
            }
        }
    },

    // 测试数据配置
    testData: {
        // 测试服务器数量
        serverCount: 50,
        
        // 测试时间范围（小时）
        timeRange: 24,
        
        // 负载数据点数量
        loadDataPoints: 1000,
        
        // 流量数据大小
        trafficDataSize: 100,
        
        // 模拟并发用户数
        concurrentUsers: 10
    },

    // 性能基准配置
    performance: {
        // 基准测试重复次数
        iterations: 10,
        
        // 预热次数
        warmupIterations: 3,
        
        // 期望响应时间（毫秒）
        expectedResponseTimes: {
            database_query: 100,
            api_endpoint: 500,
            complex_query: 1000
        },
        
        // 错误率阈值
        errorRateThreshold: 0.01, // 1%
        
        // 性能差异容忍度
        performanceTolerancePercent: 20
    },

    // 压力测试配置
    stress: {
        // 并发连接数
        concurrentConnections: [10, 50, 100, 200],
        
        // 测试持续时间（秒）
        duration: 60,
        
        // 内存使用限制（MB）
        memoryLimitMB: 512,
        
        // CPU使用限制（%）
        cpuLimitPercent: 80
    },

    // 报告配置
    reporting: {
        outputDir: path.join(__dirname, '../test-results'),
        formats: ['json', 'html', 'console'],
        includeDetails: true,
        generateCharts: true
    },

    // 清理配置
    cleanup: {
        // 是否在测试后自动清理
        autoCleanup: true,
        
        // 保留测试数据库文件（用于调试）
        keepTestDatabases: false,
        
        // 清理旧的测试结果（天）
        cleanupOlderThan: 7
    }
};

// 根据环境变量覆盖配置
if (process.env.DB_TYPE) {
    config.activeDatabase = process.env.DB_TYPE;
}

if (process.env.TEST_ITERATIONS) {
    config.performance.iterations = parseInt(process.env.TEST_ITERATIONS);
}

if (process.env.TEST_CONCURRENT_USERS) {
    config.testData.concurrentUsers = parseInt(process.env.TEST_CONCURRENT_USERS);
}

// 确保输出目录存在
if (!fs.existsSync(config.reporting.outputDir)) {
    fs.mkdirSync(config.reporting.outputDir, { recursive: true });
}

module.exports = config;