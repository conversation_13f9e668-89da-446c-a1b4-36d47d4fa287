# DStatus 测试套件

PostgreSQL迁移项目的全面测试套件，验证双数据库兼容性、性能基准和系统稳定性。

## 目录结构

```
tests/
├── README.md                    # 本文件
├── config/                      # 测试配置
│   ├── test-config.js          # 测试环境配置
│   └── database-setup.js       # 数据库测试环境设置
├── unit/                        # 单元测试
│   ├── database/               # 数据库相关测试
│   │   ├── adapters/           # 适配器测试
│   │   │   ├── sqlite.test.js
│   │   │   ├── postgresql.test.js
│   │   │   └── compatibility.test.js
│   │   ├── models/             # 数据模型测试
│   │   └── migrations/         # 迁移测试
│   └── api/                    # API单元测试
├── integration/                 # 集成测试
│   ├── database-compatibility.test.js
│   ├── api-endpoints.test.js
│   └── cross-database.test.js
├── performance/                 # 性能测试
│   ├── benchmark/              # 基准测试
│   │   ├── database-operations.js
│   │   ├── api-performance.js
│   │   └── comparison-report.js
│   └── stress/                 # 压力测试
│       ├── concurrent-access.js
│       ├── load-testing.js
│       └── memory-usage.js
├── fixtures/                    # 测试数据
│   ├── sample-data.json
│   └── test-schemas.sql
└── utils/                       # 测试工具
    ├── test-helpers.js
    ├── database-utils.js
    └── assertion-helpers.js
```

## 运行测试

### 单个测试套件
```bash
# 数据库兼容性测试
npm run test:compatibility

# 性能基准测试  
npm run test:performance

# 压力测试
npm run test:stress

# 完整测试套件
npm run test:all
```

### 特定数据库测试
```bash
# 仅SQLite测试
DB_TYPE=sqlite npm run test

# 仅PostgreSQL测试
DB_TYPE=postgresql npm run test

# 双数据库对比测试
npm run test:cross-database
```

## 测试覆盖率

目标：>90%代码覆盖率

- ✅ 数据库适配器层
- ✅ API端点功能
- ✅ 数据模型操作
- ✅ 迁移脚本
- ✅ 性能基准对比

## 测试数据管理

- **隔离性**：每个测试使用独立的测试数据库
- **清理**：测试后自动清理测试数据
- **一致性**：SQLite和PostgreSQL使用相同的测试数据
- **完整性**：模拟真实生产数据场景

## 性能基准

### 期望指标
- **查询响应时间**: <500ms (95th percentile)
- **并发处理**: >100 concurrent connections
- **内存使用**: <512MB 峰值
- **错误率**: <0.1%

### 对比标准
- PostgreSQL性能应达到SQLite的80%以上
- API响应时间差异<20%
- 数据一致性100%

## 问题报告

测试失败时会生成详细报告：
- `test-results/compatibility-report.json`
- `test-results/performance-report.json`  
- `test-results/stress-test-report.json`

## 持续集成

配置GitHub Actions自动运行：
- 每次PR自动执行完整测试套件
- 每日定时性能基准测试
- 发布前的完整验证流程