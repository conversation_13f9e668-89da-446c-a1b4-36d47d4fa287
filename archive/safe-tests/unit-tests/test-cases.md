# 网络质量功能墙测试用例

## 测试概览

本文档包含DStatus网络质量监控功能的商业化功能墙系统的完整测试用例，确保各套餐权限、时间范围限制和功能墙UI的正确实现。

## 测试环境要求

### 前置条件
- DStatus系统正常运行（端口5555）
- License Server后端服务运行（端口3200）
- License Server管理前端运行（端口3100）
- 测试数据库包含完整套餐配置
- 浏览器支持JavaScript和WebSocket

### 测试数据准备
1. **套餐配置**：Free Plan, Standard Plan, Pro Plan, Enterprise Plan
2. **功能权限**：BASIC_MONITORING (bit=1), NETWORK_QUALITY (bit=128)
3. **限制配置**：不同套餐的时间范围限制
4. **测试许可证**：覆盖各个套餐级别

## 测试用例

### TC-001: 基础权限验证

#### TC-001-01: Free Plan用户访问网络质量页面
**测试步骤：**
1. 使用Free Plan许可证登录DStatus
2. 访问 `/network-quality` 页面
3. 观察页面显示状态

**预期结果：**
- 显示功能墙界面
- 主要内容区域被隐藏
- 显示当前套餐信息："免费版"
- 显示升级按钮和功能说明
- 控制台输出权限检查失败日志

#### TC-001-02: Pro Plan用户访问网络质量页面
**测试步骤：**
1. 使用Pro Plan许可证登录DStatus
2. 访问 `/network-quality` 页面
3. 观察页面显示状态

**预期结果：**
- 功能墙界面隐藏
- 显示正常的网络质量监控界面
- 时间范围按钮根据套餐限制显示
- 控制台输出权限检查成功日志

### TC-002: 时间范围限制测试

#### TC-002-01: Free Plan时间范围限制
**测试步骤：**
1. 使用Free Plan许可证（无NETWORK_QUALITY权限）
2. 访问网络质量页面
3. 验证无法访问任何时间范围

**预期结果：**
- 页面显示功能墙
- 所有时间范围按钮不可用
- 显示升级提示

#### TC-002-02: Standard Plan时间范围限制
**测试步骤：**
1. 使用Standard Plan许可证（限制24小时）
2. 访问网络质量页面
3. 尝试点击各个时间范围按钮

**预期结果：**
- 1小时、6小时、24小时按钮可用
- 7天、30天按钮禁用且显示锁定图标
- 点击禁用按钮显示升级提示模态框

#### TC-002-03: Pro Plan时间范围限制
**测试步骤：**
1. 使用Pro Plan许可证（限制7天）
2. 访问网络质量页面
3. 尝试点击各个时间范围按钮

**预期结果：**
- 1小时到7天按钮可用
- 30天按钮禁用且显示锁定图标
- 点击30天按钮显示升级提示

#### TC-002-04: Enterprise Plan时间范围限制
**测试步骤：**
1. 使用Enterprise Plan许可证（无限制）
2. 访问网络质量页面
3. 尝试点击所有时间范围按钮

**预期结果：**
- 所有时间范围按钮可用
- 无锁定图标显示
- 所有时间范围都能正常切换

### TC-003: API权限保护测试

#### TC-003-01: 网络质量API权限验证
**测试步骤：**
1. 使用Free Plan许可证
2. 直接调用网络质量API: `GET /api/network-quality/nodes-overview`
3. 检查响应状态

**预期结果：**
- 返回403 Forbidden状态码
- 响应包含权限错误信息
- 前端自动显示功能墙

#### TC-003-02: 时间范围API权限验证
**测试步骤：**
1. 使用Standard Plan许可证（限制24小时）
2. 调用API: `GET /api/network-quality/nodes-overview?timeRange=7d`
3. 检查响应状态

**预期结果：**
- 返回403 Forbidden状态码
- 响应包含时间范围超限错误信息
- 前端显示相应错误提示

### TC-004: 前端UI交互测试

#### TC-004-01: 功能墙UI显示测试
**测试步骤：**
1. 使用Free Plan许可证访问网络质量页面
2. 检查功能墙界面各元素

**预期结果：**
- 功能墙容器visible
- 搜索工具栏隐藏
- 时间范围选择器隐藏
- 节点容器隐藏
- 显示功能图标、标题、描述
- 显示当前套餐信息
- 显示升级按钮

#### TC-004-02: 升级按钮功能测试
**测试步骤：**
1. 在功能墙界面点击"升级套餐"按钮
2. 检查跳转行为

**预期结果：**
- 打开新标签页
- 跳转到升级页面：`/upgrade?source=network_quality&plan=pro`
- URL包含正确的来源参数

#### TC-004-03: 时间范围升级提示测试
**测试步骤：**
1. 使用Standard Plan许可证
2. 点击禁用的"7天"时间范围按钮
3. 检查升级提示模态框

**预期结果：**
- 显示时间范围升级模态框
- 模态框包含正确的时间范围信息
- 显示升级益处列表
- "立即升级"按钮可用
- 支持ESC键和遮罩层关闭

### TC-005: 缓存和配置刷新测试

#### TC-005-01: 许可证缓存刷新测试
**测试步骤：**
1. 使用Free Plan许可证登录
2. 在License Server中升级到Pro Plan
3. 点击DStatus中的"刷新配置"按钮
4. 刷新网络质量页面

**预期结果：**
- 配置刷新成功
- 页面重新加载后显示新的权限状态
- 时间范围按钮根据新套餐更新

#### TC-005-02: 套餐限制配置更新测试
**测试步骤：**
1. 在License Server管理界面修改Pro Plan的时间范围限制
2. 点击DStatus中的"刷新配置"按钮
3. 检查网络质量页面的时间范围按钮状态

**预期结果：**
- 时间范围按钮状态根据新配置更新
- 禁用/启用状态正确反映新限制
- 用户体验无中断

### TC-006: 错误处理和边界条件测试

#### TC-006-01: 网络错误处理测试
**测试步骤：**
1. 断开License Server连接
2. 访问网络质量页面
3. 观察错误处理行为

**预期结果：**
- 权限检查超时后默认允许访问
- 显示网络错误提示
- 系统不会崩溃，保持基本功能

#### TC-006-02: 无效许可证处理测试
**测试步骤：**
1. 使用过期或无效的许可证
2. 访问网络质量页面
3. 检查系统行为

**预期结果：**
- 显示许可证无效提示
- 引导用户进行许可证配置
- 阻止功能访问

#### TC-006-03: 浏览器兼容性测试
**测试步骤：**
1. 在Chrome、Firefox、Safari中测试
2. 检查功能墙和时间范围限制
3. 验证JavaScript兼容性

**预期结果：**
- 所有主流浏览器功能正常
- UI显示一致
- 无JavaScript错误

### TC-007: 性能和用户体验测试

#### TC-007-01: 页面加载性能测试
**测试步骤：**
1. 清除浏览器缓存
2. 访问网络质量页面
3. 测量加载时间和资源使用

**预期结果：**
- 页面加载时间 < 3秒
- 功能墙显示 < 1秒
- 权限检查 < 500ms
- 无内存泄漏

#### TC-007-02: 响应式设计测试
**测试步骤：**
1. 在不同屏幕尺寸下测试功能墙
2. 检查移动设备适配性
3. 验证升级模态框显示

**预期结果：**
- 移动设备上功能墙正常显示
- 升级按钮和模态框适配小屏幕
- 触摸交互正常

### TC-008: 集成测试

#### TC-008-01: 完整功能流程测试
**测试步骤：**
1. Free Plan用户访问，验证功能墙
2. 升级到Standard Plan，验证部分权限
3. 升级到Pro Plan，验证完整权限
4. 测试时间范围切换功能

**预期结果：**
- 每个阶段的权限状态正确
- 升级流程无缝衔接
- 无数据丢失或状态错误

#### TC-008-02: 多用户并发测试
**测试步骤：**
1. 多个不同套餐用户同时访问
2. 检查权限隔离和性能
3. 验证缓存机制正确性

**预期结果：**
- 用户权限正确隔离
- 系统性能稳定
- 无权限泄漏或冲突

## 测试数据和预期结果

### 套餐配置示例
```json
{
  "Free Plan": {
    "features": 1,  // BASIC_MONITORING only
    "limits": {}
  },
  "Standard Plan": {
    "features": 129, // BASIC_MONITORING + NETWORK_QUALITY
    "limits": {
      "networkQuality": {
        "maxTimeRange": 86400  // 24 hours
      }
    }
  },
  "Pro Plan": {
    "features": 129,
    "limits": {
      "networkQuality": {
        "maxTimeRange": 604800  // 7 days
      }
    }
  },
  "Enterprise Plan": {
    "features": 129,
    "limits": {
      "networkQuality": {
        "maxTimeRange": null  // unlimited
      }
    }
  }
}
```

### 时间范围映射
- 1h: 3600秒
- 6h: 21600秒
- 24h: 86400秒
- 7d: 604800秒
- 30d: 2592000秒

## 自动化测试建议

### 单元测试
- `NetworkQualityFeatureWall.isTimeRangeAllowed()`
- `NetworkQualityFeatureWall.getTimeRangeInSeconds()`
- 权限检查API调用

### 集成测试
- E2E页面访问流程
- API权限验证
- 升级流程测试

### 性能测试
- 权限检查响应时间
- 页面加载性能
- 内存使用监控

## 验收标准

### 功能性标准
- [ ] 所有测试用例通过率 ≥ 95%
- [ ] 无严重缺陷
- [ ] 权限控制100%准确

### 性能标准
- [ ] 页面加载时间 < 3秒
- [ ] 权限检查 < 500ms
- [ ] 内存使用 < 100MB增量

### 用户体验标准
- [ ] 升级流程直观易懂
- [ ] 错误提示友好清晰
- [ ] 响应式设计适配良好

### 安全标准
- [ ] 无权限绕过漏洞
- [ ] API访问控制严格
- [ ] 敏感信息不泄露

## 回归测试检查项

在每次更新后，应执行以下关键检查：

1. **基础权限验证**：各套餐的基本访问权限
2. **时间范围限制**：按钮禁用状态和API限制
3. **升级流程**：功能墙和升级提示的正常显示
4. **缓存机制**：配置更新和权限刷新
5. **错误处理**：网络异常和无效许可证处理

## 测试报告模板

### 测试执行总结
- 执行日期：
- 测试环境：
- 执行用例总数：
- 通过用例数：
- 失败用例数：
- 总体通过率：

### 缺陷总结
- 严重缺陷：
- 一般缺陷：
- 轻微缺陷：
- 建议改进：

### 性能指标
- 平均页面加载时间：
- 权限检查平均响应时间：
- 内存使用峰值：

### 建议和后续计划
- 需要修复的问题：
- 优化建议：
- 下版本改进计划：