/**
 * 数据库测试环境设置
 * 用于初始化和管理测试数据库环境
 */

const fs = require('fs');
const path = require('path');
const testConfig = require('./test-config');

class DatabaseTestSetup {
    constructor() {
        this.config = testConfig;
        this.databases = new Map();
    }

    /**
     * 初始化测试数据库环境
     */
    async initializeTestEnvironment(dbType = 'both') {
        console.log(`🔧 初始化测试环境: ${dbType}`);
        
        if (dbType === 'both' || dbType === 'sqlite') {
            await this.setupSQLiteTest();
        }
        
        if (dbType === 'both' || dbType === 'postgresql') {
            await this.setupPostgreSQLTest();
        }
        
        console.log('✅ 测试环境初始化完成');
    }

    /**
     * 设置SQLite测试环境
     */
    async setupSQLiteTest() {
        console.log('📝 设置SQLite测试环境...');
        
        const dbConfig = this.config.databases.sqlite;
        
        // 确保测试目录存在
        const testDataDir = path.dirname(dbConfig.path);
        if (!fs.existsSync(testDataDir)) {
            fs.mkdirSync(testDataDir, { recursive: true });
        }
        
        // 如果存在旧的测试数据库，先删除
        if (fs.existsSync(dbConfig.path)) {
            fs.unlinkSync(dbConfig.path);
        }
        
        // 创建SQLite适配器实例
        const SQLiteAdapter = require('../../database/adapters/sqlite');
        const adapter = new SQLiteAdapter(dbConfig);
        
        // 连接数据库
        await adapter.connect();
        
        // 初始化表结构
        await this.createTableStructures(adapter);
        
        // 插入测试数据
        await this.insertTestData(adapter, 'sqlite');
        
        this.databases.set('sqlite', adapter);
        console.log('✅ SQLite测试环境设置完成');
    }

    /**
     * 设置PostgreSQL测试环境
     */
    async setupPostgreSQLTest() {
        console.log('📝 设置PostgreSQL测试环境...');
        
        const dbConfig = this.config.databases.postgresql;
        
        try {
            // 创建PostgreSQL适配器实例
            const PostgresAdapter = require('../../database/adapters/postgresql');
            const adapter = new PostgresAdapter(dbConfig);
            
            // 连接数据库
            await adapter.connect();
            
            // 清理现有测试数据
            await this.cleanupPostgreSQLTestData(adapter);
            
            // 初始化表结构
            await this.createTableStructures(adapter);
            
            // 插入测试数据
            await this.insertTestData(adapter, 'postgresql');
            
            this.databases.set('postgresql', adapter);
            console.log('✅ PostgreSQL测试环境设置完成');
            
        } catch (error) {
            console.warn('⚠️ PostgreSQL测试环境设置失败，可能是数据库未启动:', error.message);
            console.log('💡 跳过PostgreSQL测试，仅使用SQLite');
        }
    }

    /**
     * 创建表结构
     */
    async createTableStructures(adapter) {
        const dbType = adapter.type;
        
        // 创建服务器表
        await adapter.run(`
            CREATE TABLE IF NOT EXISTS servers (
                sid TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                data TEXT NOT NULL,
                top INTEGER DEFAULT 0,
                status INTEGER DEFAULT 1,
                expire_time INTEGER,
                group_id TEXT DEFAULT 'default'
            )
        `);

        // 创建分组表
        await adapter.run(`
            CREATE TABLE IF NOT EXISTS groups (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                top INTEGER DEFAULT 0
            )
        `);

        // 创建流量统计表
        await adapter.run(`
            CREATE TABLE IF NOT EXISTS traffic (
                sid TEXT PRIMARY KEY,
                hs TEXT,
                ds TEXT,
                ms TEXT
            )
        `);

        // 创建负载统计表（分钟级）
        await adapter.run(`
            CREATE TABLE IF NOT EXISTS load_m (
                sid TEXT,
                cpu REAL,
                mem REAL,
                swap REAL,
                ibw REAL,
                obw REAL,
                expire_time INTEGER,
                PRIMARY KEY(sid)
            )
        `);

        // 创建负载统计表（小时级）
        await adapter.run(`
            CREATE TABLE IF NOT EXISTS load_h (
                sid TEXT,
                cpu REAL,
                mem REAL,
                swap REAL,
                ibw REAL,
                obw REAL,
                expire_time INTEGER,
                PRIMARY KEY(sid)
            )
        `);

        // 创建负载归档表
        const strftime = dbType === 'sqlite' ? "strftime('%s', 'now')" : "EXTRACT(EPOCH FROM NOW())::INTEGER";
        await adapter.run(`
            CREATE TABLE IF NOT EXISTS load_archive (
                id ${dbType === 'sqlite' ? 'INTEGER PRIMARY KEY AUTOINCREMENT' : 'SERIAL PRIMARY KEY'},
                sid TEXT NOT NULL,
                cpu REAL,
                mem REAL,
                swap REAL,
                ibw REAL,
                obw REAL,
                expire_time INTEGER,
                created_at INTEGER DEFAULT (${strftime})
            )
        `);

        // 创建索引
        await adapter.run(`
            CREATE INDEX IF NOT EXISTS idx_load_archive_sid_time
            ON load_archive(sid, created_at)
        `);

        // 创建设置表
        await adapter.run(`
            CREATE TABLE IF NOT EXISTS setting (
                key TEXT PRIMARY KEY,
                val TEXT
            )
        `);

        console.log(`📋 ${dbType.toUpperCase()} 表结构创建完成`);
    }

    /**
     * 插入测试数据
     */
    async insertTestData(adapter, dbType) {
        console.log(`📊 插入${dbType.toUpperCase()}测试数据...`);
        
        // 插入默认分组
        await adapter.run(`
            INSERT OR IGNORE INTO groups (id, name, top) 
            VALUES ('default', '默认分组', 0)
        `);
        
        await adapter.run(`
            INSERT OR IGNORE INTO groups (id, name, top) 
            VALUES ('test', '测试分组', 1)
        `);

        // 插入测试服务器
        const serverCount = this.config.testData.serverCount;
        for (let i = 1; i <= serverCount; i++) {
            const sid = `test-server-${i.toString().padStart(3, '0')}`;
            const groupId = i <= 10 ? 'test' : 'default';
            
            const serverData = JSON.stringify({
                name: `测试服务器 ${i}`,
                location: `测试地点 ${i}`,
                type: 'VPS',
                provider: 'TestProvider'
            });

            await adapter.run(`
                INSERT OR IGNORE INTO servers (sid, name, data, group_id, status) 
                VALUES (?, ?, ?, ?, 1)
            `, [sid, `测试服务器 ${i}`, serverData, groupId]);

            // 插入负载数据
            const cpu = Math.random() * 100;
            const mem = Math.random() * 100;
            const swap = Math.random() * 50;
            const now = Math.floor(Date.now() / 1000);

            await adapter.run(`
                INSERT OR IGNORE INTO load_m (sid, cpu, mem, swap, ibw, obw, expire_time) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [sid, cpu, mem, swap, Math.random() * 1000, Math.random() * 1000, now + 3600]);

            await adapter.run(`
                INSERT OR IGNORE INTO load_h (sid, cpu, mem, swap, ibw, obw, expire_time) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [sid, cpu, mem, swap, Math.random() * 1000, Math.random() * 1000, now + 86400]);

            // 插入流量数据
            const trafficData = {
                hourly: Array.from({length: 24}, () => Math.floor(Math.random() * 1000000)),
                daily: Array.from({length: 30}, () => Math.floor(Math.random() * 50000000)),
                monthly: Array.from({length: 12}, () => Math.floor(Math.random() * 1000000000))
            };

            await adapter.run(`
                INSERT OR IGNORE INTO traffic (sid, hs, ds, ms) 
                VALUES (?, ?, ?, ?)
            `, [sid, JSON.stringify(trafficData.hourly), JSON.stringify(trafficData.daily), JSON.stringify(trafficData.monthly)]);
        }

        // 插入历史负载数据
        await this.insertLoadArchiveData(adapter, dbType);

        console.log(`✅ ${dbType.toUpperCase()} 测试数据插入完成 (${serverCount} 服务器)`);
    }

    /**
     * 插入负载归档测试数据
     */
    async insertLoadArchiveData(adapter, dbType) {
        const dataPoints = this.config.testData.loadDataPoints;
        const now = Math.floor(Date.now() / 1000);
        
        console.log(`📈 插入${dataPoints}个负载归档数据点...`);
        
        for (let i = 0; i < dataPoints; i++) {
            const sid = `test-server-${((i % 50) + 1).toString().padStart(3, '0')}`;
            const timestamp = now - (dataPoints - i) * 2; // 每2秒一个数据点
            
            await adapter.run(`
                INSERT INTO load_archive (sid, cpu, mem, swap, ibw, obw, expire_time, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                sid,
                Math.random() * 100,
                Math.random() * 100,
                Math.random() * 50,
                Math.random() * 1000,
                Math.random() * 1000,
                timestamp + 3600,
                timestamp
            ]);
        }
        
        console.log('✅ 负载归档数据插入完成');
    }

    /**
     * 清理PostgreSQL测试数据
     */
    async cleanupPostgreSQLTestData(adapter) {
        const tables = ['load_archive', 'traffic', 'load_h', 'load_m', 'servers', 'groups', 'setting'];
        
        for (const table of tables) {
            try {
                await adapter.run(`TRUNCATE TABLE ${table} CASCADE`);
            } catch (error) {
                // 表可能不存在，忽略错误
            }
        }
    }

    /**
     * 获取测试数据库实例
     */
    getDatabase(type) {
        return this.databases.get(type);
    }

    /**
     * 获取所有可用的测试数据库
     */
    getAvailableDatabases() {
        return Array.from(this.databases.keys());
    }

    /**
     * 清理测试环境
     */
    async cleanup() {
        console.log('🧹 清理测试环境...');
        
        for (const [type, adapter] of this.databases) {
            try {
                await adapter.disconnect();
                console.log(`✅ ${type.toUpperCase()} 连接已关闭`);
            } catch (error) {
                console.error(`❌ 关闭${type.toUpperCase()}连接失败:`, error.message);
            }
        }
        
        // 删除SQLite测试文件
        if (this.config.cleanup.autoCleanup && !this.config.cleanup.keepTestDatabases) {
            const sqliteDbPath = this.config.databases.sqlite.path;
            if (fs.existsSync(sqliteDbPath)) {
                fs.unlinkSync(sqliteDbPath);
                console.log('🗑️ SQLite测试数据库文件已删除');
            }
        }
        
        this.databases.clear();
        console.log('✅ 测试环境清理完成');
    }
}

module.exports = DatabaseTestSetup;