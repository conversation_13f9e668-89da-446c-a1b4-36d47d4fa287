# API限制优化测试用例规范

## 📋 概述

本文档定义了负载详情和带宽监控API优化的完整测试用例规范，确保优化后的API既能提供足够的数据，又能维持良好的性能。

## 🎯 测试目标

1. **数据完整性**：确保API能返回足够的历史数据
2. **性能要求**：维持良好的响应时间和资源使用
3. **稳定性**：不同负载下系统稳定运行
4. **兼容性**：确保前端和现有集成不受影响

## 📊 测试用例定义

### TC-001: 负载详情API数据完整性测试

**测试目的**: 验证优化后的负载API能提供完整的历史数据

#### 测试前置条件
- 数据库包含至少24小时的负载监控数据
- 至少有5个活跃监控节点
- 系统轮询间隔配置为3秒

#### 测试用例

| 用例ID | 时间范围 | 预期数据点数 | 当前限制后数据点 | 优化后期望数据点 | 数据丢失率标准 |
|--------|----------|--------------|------------------|------------------|----------------|
| TC-001-1 | 10分钟 | ~200 | 200 | 200 | 0% |
| TC-001-2 | 1小时 | ~1200 | 1000 | 1200 | <5% |
| TC-001-3 | 2小时 | ~2400 | 1000 | 2400 | <5% |
| TC-001-4 | 4小时 | ~4800 | 1000 | 3000 | <40% |
| TC-001-5 | 24小时 | ~28800 | 1000 | 3000 | <90% |

#### 验收标准
- ✅ 10分钟和1小时范围数据完整性≥95%
- ✅ 2-4小时范围数据丢失率<20%（当前44-63%）
- ✅ 24小时范围可以获取到采样数据（当前62.8%丢失）
- ✅ 数据采样算法合理，保留关键趋势信息

### TC-002: 带宽监控API时间范围测试

**测试目的**: 验证优化后的带宽API支持更长的时间范围查询

#### 测试用例

| 用例ID | 时间范围 | 当前状态 | 优化后期望状态 | 最大数据点数 |
|--------|----------|----------|----------------|--------------|
| TC-002-1 | 10分钟 | ✅ 支持 | ✅ 支持 | 300 |
| TC-002-2 | 1小时 | ✅ 支持 | ✅ 支持 | 1800 |
| TC-002-3 | 4小时 | ❌ 拒绝 | ✅ 支持 | 7200 |
| TC-002-4 | 12小时 | ❌ 拒绝 | ✅ 支持 | 10000 |
| TC-002-5 | 24小时 | ❌ 拒绝 | ✅ 支持 | 10000 |

#### 验收标准
- ✅ 支持最大24小时时间范围查询
- ✅ 超过限制时优雅降级到合理采样
- ✅ 返回的时间戳序列连续且合理
- ✅ 带宽数据单位转换正确（Mbps）

### TC-003: API性能基准测试

**测试目的**: 确保优化后的API性能符合要求

#### 性能要求标准

| 指标 | 当前基准 | 优化后要求 | 测试条件 |
|------|----------|------------|----------|
| 查询响应时间 | <100ms | <200ms | 单节点4小时数据 |
| 内存使用量 | <1MB | <5MB | 单次API调用 |
| CPU使用率 | <5% | <10% | 并发10个请求 |
| 数据库连接时间 | <10ms | <20ms | 数据库查询 |

#### 测试用例

```javascript
// TC-003-1: 响应时间测试
describe('API响应时间测试', () => {
  test('负载API 4小时数据查询 <200ms', async () => {
    const startTime = performance.now();
    const response = await fetch('/api/stats/test-node/load/recent?duration=240&interval=2');
    const endTime = performance.now();
    expect(endTime - startTime).toBeLessThan(200);
    expect(response.data.timestamps.length).toBeGreaterThan(1000);
  });
  
  test('带宽API 24小时数据查询 <500ms', async () => {
    const startTime = performance.now();
    const response = await fetch('/api/stats/test-node/bandwidth/history?range=24h');
    const endTime = performance.now();
    expect(endTime - startTime).toBeLessThan(500);
  });
});
```

### TC-004: 数据质量一致性测试

**测试目的**: 验证优化后数据的准确性和一致性

#### 测试用例

| 用例ID | 测试项目 | 验证标准 | 数据来源 |
|--------|----------|----------|----------|
| TC-004-1 | CPU数据范围 | 0-100% | load_archive |
| TC-004-2 | 内存数据范围 | 0-100% | load_archive |
| TC-004-3 | 带宽数据合理性 | >0 Mbps, <10Gbps | load_archive.ibw/obw |
| TC-004-4 | 时间戳连续性 | 递增序列 | created_at字段 |
| TC-004-5 | 采样一致性 | 采样间隔合理 | 前后数据点对比 |

#### 验收标准
- ✅ 数据有效性≥99%
- ✅ 时间戳严格递增
- ✅ 采样算法保持数据趋势
- ✅ 无明显的数据跳跃或异常值

### TC-005: 边界条件测试

**测试目的**: 验证极端条件下API的稳定性

#### 测试用例

| 用例ID | 测试场景 | 输入条件 | 期望行为 |
|--------|----------|----------|----------|
| TC-005-1 | 无数据节点 | 查询不存在的节点 | 返回空数组，状态码200 |
| TC-005-2 | 超大时间范围 | duration=10080（7天） | 优雅采样，不崩溃 |
| TC-005-3 | 无效时间参数 | startTime > endTime | 返回错误提示 |
| TC-005-4 | 并发请求 | 同时50个API请求 | 全部正常响应 |
| TC-005-5 | 内存压力 | 大量历史数据查询 | 内存使用可控 |

### TC-006: 回归测试用例

**测试目的**: 确保优化不破坏现有功能

#### 前端兼容性测试

```javascript
// TC-006-1: 前端图表渲染测试
describe('前端集成测试', () => {
  test('负载图表正常渲染', async () => {
    // 模拟前端调用
    const response = await fetch('/api/stats/test-node/load/recent?duration=60');
    const data = response.data;
    
    expect(data.cpu).toBeDefined();
    expect(data.mem).toBeDefined();
    expect(data.swap).toBeDefined();
    expect(data.timestamps).toBeDefined();
    expect(data.timestamps.length).toBeGreaterThan(0);
  });
  
  test('带宽图表数据格式兼容', async () => {
    const response = await fetch('/api/stats/test-node/bandwidth/history?range=1h');
    expect(response.data.ibw).toBeDefined();
    expect(response.data.obw).toBeDefined();
  });
});
```

#### API响应格式测试

```json
// TC-006-2: 负载API响应格式验证
{
  "status": "success",
  "data": {
    "cpu": [1.2, 2.5, 3.1],
    "mem": [45.6, 46.2, 47.1],
    "swap": [0.1, 0.1, 0.2],
    "timestamps": [1634567890000, 1634567892000, 1634567894000]
  }
}

// TC-006-3: 带宽API响应格式验证
{
  "status": "success",
  "data": {
    "timestamps": [1634567890000],
    "ibw": [12.5],
    "obw": [8.3]
  }
}
```

## 🔧 测试执行计划

### 阶段1: 预优化基准测试
```bash
# 执行当前状态的完整测试
node scripts/test-api-limits-tdd.js

# 记录基准性能数据
node scripts/performance-baseline.js
```

### 阶段2: 优化实施
1. 修改 `/modules/api/load.js`
2. 修改 `/modules/api/load-archive.js`
3. 配置化限制参数

### 阶段3: 优化后验证测试
```bash
# 执行优化后的完整测试套件
node scripts/test-api-limits-tdd.js

# 性能对比测试
node scripts/performance-comparison.js

# 前端集成测试
npm run test:frontend-integration
```

### 阶段4: 生产验证
```bash
# 压力测试
node scripts/stress-test-apis.js

# 内存泄漏检测
node --expose-gc scripts/memory-leak-test.js

# 长期稳定性测试
node scripts/long-term-stability.js
```

## 📈 成功标准总结

### 必须满足的要求（P0）
- ✅ 负载API 2-4小时数据丢失率从44-63%降低到<20%
- ✅ 带宽API支持4-24小时时间范围查询
- ✅ API响应时间保持在可接受范围（<200ms）
- ✅ 前端图表正常显示和交互

### 期望优化的目标（P1）
- ✅ 数据完整性提升50%以上
- ✅ 用户体验明显改善
- ✅ 系统资源使用合理增长（<2倍）
- ✅ 配置化管理支持

### 额外收益（P2）
- ✅ 代码可维护性提升
- ✅ 监控数据更丰富
- ✅ 为未来扩展预留空间

## 🚨 风险控制

### 性能风险
- **监控指标**: 响应时间、内存使用、CPU占用
- **熔断机制**: 查询时间超过5秒自动降级
- **资源限制**: 单次查询最大10MB内存使用

### 稳定性风险
- **渐进式部署**: 先在测试环境验证
- **回滚方案**: 保留原始限制配置
- **监控告警**: 异常情况自动通知

---

*本测试规范版本: v1.0*  
*最后更新: 2025-01-06*  
*负责人: Claude Code TDD Team*