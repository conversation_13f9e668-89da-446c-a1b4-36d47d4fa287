/**
 * 测试辅助工具
 * 提供通用的测试功能和断言辅助
 */

const testConfig = require('../config/test-config');

class TestHelpers {
    /**
     * 生成随机测试数据
     */
    static generateRandomData() {
        return {
            // 生成随机服务器ID
            randomServerId: () => `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            
            // 生成随机服务器数据
            randomServerData: () => ({
                name: `测试服务器 ${Math.floor(Math.random() * 1000)}`,
                data: JSON.stringify({
                    location: ['北京', '上海', '广州', '深圳'][Math.floor(Math.random() * 4)],
                    type: 'VPS',
                    provider: 'TestProvider'
                }),
                group_id: ['default', 'test'][Math.floor(Math.random() * 2)],
                status: 1
            }),
            
            // 生成随机负载数据
            randomLoadData: () => ({
                cpu: Math.random() * 100,
                mem: Math.random() * 100,
                swap: Math.random() * 50,
                ibw: Math.random() * 1000,
                obw: Math.random() * 1000,
                expire_time: Math.floor(Date.now() / 1000) + 3600
            }),
            
            // 生成随机流量数据
            randomTrafficData: () => ({
                hs: JSON.stringify(Array.from({length: 24}, () => Math.floor(Math.random() * 1000000))),
                ds: JSON.stringify(Array.from({length: 30}, () => Math.floor(Math.random() * 50000000))),
                ms: JSON.stringify(Array.from({length: 12}, () => Math.floor(Math.random() * **********)))
            })
        };
    }

    /**
     * 性能测试辅助函数
     */
    static performance = {
        /**
         * 测量函数执行时间
         */
        async measureExecutionTime(func, ...args) {
            const startTime = process.hrtime.bigint();
            const result = await func(...args);
            const endTime = process.hrtime.bigint();
            
            return {
                result,
                executionTimeMs: Number(endTime - startTime) / 1000000
            };
        },

        /**
         * 批量性能测试
         */
        async batchPerformanceTest(func, iterations = 10, ...args) {
            const results = [];
            
            // 预热
            for (let i = 0; i < 3; i++) {
                await func(...args);
            }
            
            // 正式测试
            for (let i = 0; i < iterations; i++) {
                const measurement = await this.measureExecutionTime(func, ...args);
                results.push(measurement.executionTimeMs);
            }
            
            return this.calculateStatistics(results);
        },

        /**
         * 计算性能统计数据
         */
        calculateStatistics(times) {
            times.sort((a, b) => a - b);
            
            const avg = times.reduce((sum, time) => sum + time, 0) / times.length;
            const min = times[0];
            const max = times[times.length - 1];
            const median = times[Math.floor(times.length / 2)];
            const p95 = times[Math.floor(times.length * 0.95)];
            const p99 = times[Math.floor(times.length * 0.99)];
            
            return {
                count: times.length,
                average: avg,
                minimum: min,
                maximum: max,
                median,
                p95,
                p99,
                standardDeviation: Math.sqrt(
                    times.reduce((sum, time) => sum + Math.pow(time - avg, 2), 0) / times.length
                )
            };
        }
    };

    /**
     * 数据库测试辅助函数
     */
    static database = {
        /**
         * 验证数据库连接
         */
        async verifyConnection(adapter) {
            try {
                const result = await adapter.get('SELECT 1 as test');
                return result && result.test === 1;
            } catch (error) {
                return false;
            }
        },

        /**
         * 比较两个数据库的数据一致性
         */
        async compareDataConsistency(adapter1, adapter2, tableName, primaryKey = 'id') {
            const data1 = await adapter1.all(`SELECT * FROM ${tableName} ORDER BY ${primaryKey}`);
            const data2 = await adapter2.all(`SELECT * FROM ${tableName} ORDER BY ${primaryKey}`);
            
            if (data1.length !== data2.length) {
                return {
                    consistent: false,
                    reason: 'Row count mismatch',
                    count1: data1.length,
                    count2: data2.length
                };
            }
            
            for (let i = 0; i < data1.length; i++) {
                const row1 = data1[i];
                const row2 = data2[i];
                
                for (const key in row1) {
                    if (row1[key] !== row2[key]) {
                        return {
                            consistent: false,
                            reason: 'Data mismatch',
                            field: key,
                            row: i,
                            value1: row1[key],
                            value2: row2[key]
                        };
                    }
                }
            }
            
            return { consistent: true, rowCount: data1.length };
        },

        /**
         * 执行数据库健康检查
         */
        async healthCheck(adapter) {
            const checks = {
                connection: false,
                basicQuery: false,
                transactions: false,
                indexes: false
            };
            
            try {
                // 连接检查
                checks.connection = await this.verifyConnection(adapter);
                
                // 基本查询检查
                await adapter.get('SELECT COUNT(*) as count FROM servers');
                checks.basicQuery = true;
                
                // 事务检查
                await adapter.beginTransaction();
                await adapter.commitTransaction();
                checks.transactions = true;
                
                // 索引检查
                if (adapter.type === 'sqlite') {
                    const indexes = await adapter.all("SELECT name FROM sqlite_master WHERE type='index'");
                    checks.indexes = indexes.length > 0;
                } else {
                    const indexes = await adapter.all("SELECT indexname FROM pg_indexes WHERE tablename='load_archive'");
                    checks.indexes = indexes.length > 0;
                }
                
            } catch (error) {
                console.error('健康检查失败:', error.message);
            }
            
            return checks;
        }
    };

    /**
     * 断言辅助函数
     */
    static assertions = {
        /**
         * 断言性能在可接受范围内
         */
        assertPerformance(actualMs, expectedMs, tolerancePercent = 20) {
            const tolerance = expectedMs * (tolerancePercent / 100);
            const acceptable = actualMs <= expectedMs + tolerance;
            
            if (!acceptable) {
                throw new Error(
                    `性能不符合预期: 实际${actualMs}ms, 期望${expectedMs}ms±${tolerancePercent}% (${expectedMs + tolerance}ms)`
                );
            }
            
            return true;
        },

        /**
         * 断言数据库兼容性
         */
        assertDatabaseCompatibility(result1, result2, operation) {
            if (JSON.stringify(result1) !== JSON.stringify(result2)) {
                throw new Error(
                    `数据库兼容性测试失败: ${operation}\n` +
                    `SQLite结果: ${JSON.stringify(result1)}\n` +
                    `PostgreSQL结果: ${JSON.stringify(result2)}`
                );
            }
            
            return true;
        },

        /**
         * 断言错误率在可接受范围内
         */
        assertErrorRate(errors, total, maxErrorRate = 0.01) {
            const errorRate = errors / total;
            
            if (errorRate > maxErrorRate) {
                throw new Error(
                    `错误率过高: ${(errorRate * 100).toFixed(2)}%, 最大允许: ${(maxErrorRate * 100).toFixed(2)}%`
                );
            }
            
            return true;
        }
    };

    /**
     * 并发测试辅助函数
     */
    static concurrency = {
        /**
         * 并发执行函数
         */
        async runConcurrent(func, concurrency, totalRequests, ...args) {
            const results = [];
            const errors = [];
            const promises = [];
            
            let completed = 0;
            let started = 0;
            
            const worker = async () => {
                while (started < totalRequests) {
                    const requestId = ++started;
                    
                    try {
                        const startTime = Date.now();
                        const result = await func(...args);
                        const duration = Date.now() - startTime;
                        
                        results.push({
                            requestId,
                            duration,
                            success: true,
                            result
                        });
                    } catch (error) {
                        errors.push({
                            requestId,
                            error: error.message,
                            success: false
                        });
                    }
                    
                    completed++;
                }
            };
            
            // 启动并发工作者
            for (let i = 0; i < Math.min(concurrency, totalRequests); i++) {
                promises.push(worker());
            }
            
            await Promise.all(promises);
            
            return {
                totalRequests,
                successfulRequests: results.length,
                failedRequests: errors.length,
                errorRate: errors.length / totalRequests,
                results,
                errors,
                averageResponseTime: results.reduce((sum, r) => sum + r.duration, 0) / results.length
            };
        }
    };

    /**
     * 报告生成辅助函数
     */
    static reporting = {
        /**
         * 生成测试报告
         */
        generateReport(testName, results, startTime, endTime) {
            return {
                testName,
                timestamp: new Date().toISOString(),
                duration: endTime - startTime,
                environment: {
                    nodeVersion: process.version,
                    platform: process.platform,
                    arch: process.arch,
                    memoryUsage: process.memoryUsage()
                },
                results,
                summary: this.generateSummary(results)
            };
        },

        /**
         * 生成测试摘要
         */
        generateSummary(results) {
            const total = results.length;
            const passed = results.filter(r => r.success || r.passed).length;
            const failed = total - passed;
            
            return {
                total,
                passed,
                failed,
                successRate: passed / total,
                failureRate: failed / total
            };
        },

        /**
         * 保存报告到文件
         */
        async saveReport(report, filename) {
            const fs = require('fs').promises;
            const path = require('path');
            
            const outputPath = path.join(testConfig.reporting.outputDir, filename);
            await fs.writeFile(outputPath, JSON.stringify(report, null, 2));
            
            console.log(`📄 报告已保存: ${outputPath}`);
            return outputPath;
        }
    };

    /**
     * 等待指定时间
     */
    static async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 重试执行函数
     */
    static async retry(func, maxRetries = 3, delay = 1000) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                return await func();
            } catch (error) {
                if (i === maxRetries - 1) throw error;
                await this.sleep(delay);
            }
        }
    }
}

module.exports = TestHelpers;