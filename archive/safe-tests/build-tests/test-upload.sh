#!/bin/bash

# DStatus 上传功能测试脚本

set -e

echo "🧪 DStatus 上传功能测试"
echo "======================"

# 配置
UPLOAD_URL="https://down.vps.mom/upload-to-vps.php"
API_KEY="H*7F6z3n2b4CXeDMPTaj_CEm"

echo "📋 测试配置:"
echo "   上传URL: $UPLOAD_URL"
echo "   API密钥: ${API_KEY:0:8}..."
echo ""

# 测试1: 检查服务器状态
echo "🔍 测试1: 检查服务器状态"
response=$(curl -s -X GET \
    -H "X-API-Key: $API_KEY" \
    "$UPLOAD_URL?status=1")

echo "响应: $response"

if echo "$response" | grep -q '"success":true'; then
    echo "✅ 服务器状态检查成功"
else
    echo "❌ 服务器状态检查失败"
fi
echo ""

# 测试2: 创建测试文件并上传
echo "🔍 测试2: 创建测试文件并上传"

# 创建一个小的测试文件
test_file="/tmp/test-dstatus.txt"
echo "这是一个测试文件，用于验证DStatus上传功能。创建时间: $(date)" > "$test_file"

echo "📤 上传测试文件..."
response=$(curl -s -X POST \
    -H "X-API-Key: $API_KEY" \
    -F "file=@$test_file" \
    -F "target_filename=test-upload.txt" \
    "$UPLOAD_URL")

echo "响应: $response"

if echo "$response" | grep -q '"success":true'; then
    echo "✅ 测试文件上传成功"
    
    # 尝试下载验证
    echo "🔍 验证下载..."
    if curl -s -f "https://down.vps.mom/downloads/test-upload.txt" > /dev/null; then
        echo "✅ 文件下载验证成功"
    else
        echo "❌ 文件下载验证失败"
    fi
else
    echo "❌ 测试文件上传失败"
fi

# 清理测试文件
rm -f "$test_file"
echo ""

# 测试3: 检查现有文件
echo "🔍 测试3: 检查现有DStatus文件"
response=$(curl -s -X GET \
    -H "X-API-Key: $API_KEY" \
    "$UPLOAD_URL?status=1")

if echo "$response" | grep -q '"success":true'; then
    echo "📋 当前文件列表:"
    echo "$response" | grep -o '"filename":"[^"]*"' | sed 's/"filename":"//g; s/"//g' | while read filename; do
        echo "  📁 $filename"
    done
else
    echo "❌ 无法获取文件列表"
fi
echo ""

echo "🎯 测试完成！"
echo ""
echo "📋 下一步："
echo "1. 如果测试成功，可以运行实际上传："
echo "   export DSTATUS_API_KEY=\"$API_KEY\""
echo "   ./scripts/upload-client.sh"
echo ""
echo "2. 验证下载链接："
echo "   https://down.vps.mom/downloads/dstatus-docker.tar.gz"
echo "   https://down.vps.mom/downloads/dstatus-complete.tar.gz"
