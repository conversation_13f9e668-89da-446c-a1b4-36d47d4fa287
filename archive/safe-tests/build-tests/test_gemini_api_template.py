#!/usr/bin/env python3
"""
测试 Gemini API 连接 - 模板版本
使用环境变量替代硬编码的API密钥
"""

import os
import google.generativeai as genai

# API Key - 从环境变量读取
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', 'YOUR_GEMINI_API_KEY_HERE')

def test_gemini():
    """测试 Gemini API"""
    if GEMINI_API_KEY == 'YOUR_GEMINI_API_KEY_HERE':
        print("❌ 请设置 GEMINI_API_KEY 环境变量")
        print("使用方法: export GEMINI_API_KEY=your_actual_api_key")
        return
        
    try:
        # 配置 API
        genai.configure(api_key=GEMINI_API_KEY)
        
        # 列出可用模型
        print("🔍 可用的 Gemini 模型:")
        available_models = []
        for model in genai.list_models():
            if 'generateContent' in model.supported_generation_methods:
                print(f"   - {model.name}")
                if 'flash' in model.name.lower():
                    available_models.append(model.name)
        
        # 测试不同的模型
        test_models = [
            'gemini-2.0-flash-exp',
            'gemini-1.5-flash-latest', 
            'gemini-1.5-flash',
            'gemini-1.5-flash-001',
            'gemini-1.5-flash-002',
        ]
        
        print("\n🧪 测试 Flash 模型...")
        tested = False
        
        for model_name in test_models:
            try:
                print(f"\n尝试模型: {model_name}")
                model = genai.GenerativeModel(model_name)
                
                response = model.generate_content("请用一句话介绍 DStatus 项目")
                
                print(f"✅ 模型 {model_name} 测试成功!")
                print(f"回复: {response.text}")
                tested = True
                break
            except Exception as e:
                print(f"✗ 模型 {model_name} 不可用: {str(e)[:100]}")
        
        if not tested:
            print("\n❌ 所有 Flash 模型测试都失败了")
            if available_models:
                print(f"\n建议使用以下可用的 Flash 模型之一:")
                for m in available_models:
                    print(f"   - {m}")
        
    except Exception as e:
        print(f"\n❌ API 测试失败: {e}")
        print("\n可能的原因:")
        print("1. API Key 无效或过期")
        print("2. 网络连接问题")
        print("3. 需要安装 google-generativeai: pip install google-generativeai")

if __name__ == '__main__':
    test_gemini()