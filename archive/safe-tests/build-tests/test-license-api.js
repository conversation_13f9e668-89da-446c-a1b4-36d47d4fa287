#!/usr/bin/env node
"use strict";

const axios = require('axios');

// License Server 配置
const LICENSE_SERVER_URL = 'https://dstatus_api.vps.mom';

// 测试许可证密钥（从日志中获取）
const TEST_LICENSE_KEY = '********-********-632A7C9D-0C32302B';
const TEST_INSTANCE_ID = 'c2dfdea5-67bd-408e-abd6-b5e3a0279b97';

// 打印分隔线
function printSeparator(title) {
    console.log('\n' + '='.repeat(60));
    console.log(title);
    console.log('='.repeat(60));
}

// 测试验证 API
async function testVerifyAPI() {
    printSeparator('测试 /api/verification/verify');
    
    const payload = {
        licenseKey: TEST_LICENSE_KEY,
        instanceId: TEST_INSTANCE_ID,
        nodeCount: 10,
        features: [],
        version: '1.0.0'
    };
    
    console.log('请求参数:', JSON.stringify(payload, null, 2));
    
    try {
        const response = await axios.post(`${LICENSE_SERVER_URL}/api/verification/verify`, payload, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        console.log('\n响应状态:', response.status);
        console.log('响应数据:', JSON.stringify(response.data, null, 2));
        
        // 分析响应结构
        if (response.data.success && response.data.data) {
            const data = response.data.data;
            console.log('\n数据分析:');
            console.log('- isBound:', data.isBound);
            console.log('- license.maxNodes:', data.license?.maxNodes);
            console.log('- license.type:', data.license?.type);
            console.log('- featureDetails 数量:', data.featureDetails?.length || 0);
        }
        
    } catch (error) {
        console.error('\n请求失败:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 测试许可证状态 API
async function testStatusAPI() {
    printSeparator('测试 /api/license/status');
    
    const payload = {
        licenseKey: TEST_LICENSE_KEY,
        instanceId: TEST_INSTANCE_ID
    };
    
    console.log('请求参数:', JSON.stringify(payload, null, 2));
    
    try {
        const response = await axios.post(`${LICENSE_SERVER_URL}/api/license/status`, payload, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        console.log('\n响应状态:', response.status);
        console.log('响应数据:', JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.error('\n请求失败:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 测试套餐配置 API
async function testPlansAPI() {
    printSeparator('测试 /api/admin/plans');
    
    try {
        const response = await axios.get(`${LICENSE_SERVER_URL}/api/admin/plans`, {
            timeout: 10000
        });
        
        console.log('\n响应状态:', response.status);
        console.log('套餐数量:', Object.keys(response.data).length);
        
        // 显示每个套餐的信息
        for (const [key, plan] of Object.entries(response.data)) {
            console.log(`\n套餐: ${key}`);
            console.log('- displayName:', plan.displayName);
            console.log('- maxNodes:', plan.maxNodes);
            console.log('- featuresMask:', plan.featuresMask);
            console.log('- features:', plan.features?.join(', ') || 'None');
        }
        
    } catch (error) {
        console.error('\n请求失败:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 测试功能列表 API
async function testFeaturesAPI() {
    printSeparator('测试 /api/admin/features');
    
    try {
        const response = await axios.get(`${LICENSE_SERVER_URL}/api/admin/features?active=true`, {
            timeout: 10000
        });
        
        console.log('\n响应状态:', response.status);
        console.log('功能数据:', JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.error('\n请求失败:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 主函数
async function main() {
    console.log('License Server API 测试工具');
    console.log('服务器地址:', LICENSE_SERVER_URL);
    console.log('测试开始时间:', new Date().toISOString());
    
    // 运行所有测试
    await testVerifyAPI();
    await testStatusAPI();
    await testPlansAPI();
    await testFeaturesAPI();
    
    printSeparator('测试完成');
    console.log('结束时间:', new Date().toISOString());
}

// 运行测试
main().catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
});