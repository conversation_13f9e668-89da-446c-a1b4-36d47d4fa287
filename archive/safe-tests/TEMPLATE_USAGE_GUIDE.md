# 测试模板使用指南

## 概述
本目录包含项目的安全测试文件，以及敏感测试文件的脱敏模板版本。

## 🔒 安全注意事项
- **敏感测试文件已被移至 `../sensitive-tests/` 目录**
- **敏感文件已被添加到 `.gitignore`，不会被提交到 GitHub**
- **本目录的模板文件可以安全地提交到版本控制系统**

## 📁 目录结构
```
safe-tests/
├── unit-tests/          # 单元测试
├── integration-tests/   # 集成测试（包含模板）
├── frontend-tests/      # 前端测试
├── build-tests/         # 构建测试（包含模板）
└── TEMPLATE_USAGE_GUIDE.md  # 本文档
```

## 🛠️ 模板文件使用方法

### Gemini API 测试模板
**文件**: `build-tests/test_gemini_api_template.py`

**使用步骤**:
1. 获取 Gemini API 密钥：https://makersuite.google.com/app/apikey
2. 设置环境变量：
   ```bash
   export GEMINI_API_KEY="your_actual_api_key_here"
   ```
3. 安装依赖：
   ```bash
   pip install google-generativeai
   ```
4. 运行测试：
   ```bash
   python3 build-tests/test_gemini_api_template.py
   ```

### PostgreSQL 连接测试模板
**文件**: `integration-tests/test_postgresql_connection_template.js`

**使用步骤**:
1. 设置数据库连接环境变量（选择其中一种）：
   ```bash
   # 方法1: 使用 DATABASE_URL
   export DATABASE_URL="postgresql://username:password@host:port/database"
   
   # 方法2: 使用 POSTGRES_CONNECTION_STRING
   export POSTGRES_CONNECTION_STRING="postgresql://username:password@host:port/database"
   ```
2. 替换连接字符串中的占位符：
   - `username`: 数据库用户名
   - `password`: 数据库密码
   - `host`: 数据库主机地址
   - `port`: 数据库端口 (通常是5432)
   - `database`: 数据库名称
3. 运行测试：
   ```bash
   node integration-tests/test_postgresql_connection_template.js
   ```

## 🚫 请勿执行的操作
- **不要** 在模板文件中硬编码真实的API密钥或数据库密码
- **不要** 将包含敏感信息的文件提交到版本控制系统
- **不要** 在公共场所分享包含真实凭据的配置文件

## 📋 测试分类说明

### 单元测试 (unit-tests/)
- 测试单个函数或组件的功能
- 不依赖外部服务
- 可以安全地在CI/CD环境中运行

### 集成测试 (integration-tests/)
- 测试组件间的集成
- 可能需要数据库或外部服务
- 包含TDD工作空间的测试文件

### 前端测试 (frontend-tests/)
- HTML页面和JavaScript功能测试
- 性能测试页面
- 用户界面对齐测试

### 构建测试 (build-tests/)
- 构建脚本和流程测试
- 许可证API测试（使用公开测试数据）
- 上传和部署测试

## 🔄 从敏感版本迁移
如果你有敏感测试文件的访问权限，可以：
1. 参考敏感文件的完整功能实现
2. 将敏感信息替换为环境变量
3. 保持核心测试逻辑不变
4. 确保模板版本能够正常工作

## 📞 支持
如果在使用模板时遇到问题：
1. 检查环境变量是否正确设置
2. 确认依赖包已正确安装
3. 验证网络连接和服务可用性
4. 查看错误日志获取详细信息