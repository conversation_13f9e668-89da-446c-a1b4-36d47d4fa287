/**
 * 双数据库兼容性测试套件
 * 验证SQLite和PostgreSQL适配器的功能一致性
 */

const assert = require('assert');
const DatabaseSetup = require('../config/database-setup');
const TestHelpers = require('../utils/test-helpers');
const testConfig = require('../config/test-config');

class DatabaseCompatibilityTest {
    constructor() {
        this.dbSetup = null;
        this.sqliteAdapter = null;
        this.postgresAdapter = null;
        this.testData = null;
        this.testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            errors: []
        };
    }

    async initialize() {
        console.log('\n🚀 初始化双数据库兼容性测试环境...');
        this.dbSetup = new DatabaseSetup();
        await this.dbSetup.initializeTestEnvironment();
        
        this.sqliteAdapter = this.dbSetup.getDatabase('sqlite');
        this.postgresAdapter = this.dbSetup.getDatabase('postgresql');
        
        // 准备测试数据
        this.testData = TestHelpers.generateRandomData();
        
        console.log('✅ 测试环境初始化完成');
    }

    async cleanup() {
        console.log('\n🧹 清理测试环境...');
        if (this.dbSetup) {
            await this.dbSetup.cleanup();
        }
    }

    async runTest(testName, testFn) {
        this.testResults.total++;
        try {
            await testFn();
            this.testResults.passed++;
            console.log(`  ✅ ${testName}`);
        } catch (error) {
            this.testResults.failed++;
            this.testResults.errors.push({ test: testName, error: error.message });
            console.log(`  ❌ ${testName}: ${error.message}`);
        }
    }

    async runAllTests() {
        console.log('\n📋 数据库兼容性测试');
        console.log('='.repeat(50));
        
        await this.initialize();
        
        try {
            // 基础连接测试
            await this.testBasicConnections();
            
            // 基础查询兼容性测试
            await this.testQueryCompatibility();
            
            // 数据插入兼容性测试
            await this.testInsertCompatibility();
            
            // 事务兼容性测试
            await this.testTransactionCompatibility();
            
            // 数据一致性验证
            await this.testDataConsistency();
            
            // 索引和性能测试
            await this.testIndexPerformance();
            
            // 错误处理兼容性
            await this.testErrorHandling();
            
        } finally {
            await this.cleanup();
        }
        
        this.printResults();
        
        if (this.testResults.failed > 0) {
            throw new Error(`兼容性测试失败: ${this.testResults.failed}个测试未通过`);
        }
    }

    async testBasicConnections() {
        console.log('\n📋 基础连接测试');
        
        await this.runTest('SQLite数据库连接', async () => {
            if (!this.sqliteAdapter) {
                console.log('    ⚠️ SQLite适配器不可用，跳过测试');
                return;
            }
            
            const isConnected = await TestHelpers.database.verifyConnection(this.sqliteAdapter);
            assert.strictEqual(isConnected, true, 'SQLite连接验证失败');
        });

        await this.runTest('PostgreSQL数据库连接', async () => {
            if (!this.postgresAdapter) {
                console.log('    ⚠️ PostgreSQL适配器不可用，跳过测试');
                return;
            }
            
            const isConnected = await TestHelpers.database.verifyConnection(this.postgresAdapter);
            assert.strictEqual(isConnected, true, 'PostgreSQL连接验证失败');
        });
    }

    async testQueryCompatibility() {
        console.log('\n📋 基础查询兼容性测试');
        
        await this.runTest('SELECT查询结果一致性', async () => {
            if (!this.postgresAdapter) {
                console.log('    ⚠️ PostgreSQL不可用，跳过对比测试');
                return;
            }

            const sqliteResult = await this.sqliteAdapter.get('SELECT 1 as test_value, \'hello\' as test_string');
            const postgresResult = await this.postgresAdapter.get('SELECT 1 as test_value, \'hello\' as test_string');
            
            TestHelpers.assertions.assertDatabaseCompatibility(
                sqliteResult, 
                postgresResult, 
                'SELECT基础查询'
            );
        });

        await this.runTest('COUNT查询结果一致性', async () => {
            if (!this.postgresAdapter) {
                console.log('    ⚠️ PostgreSQL不可用，跳过对比测试');
                return;
            }

            const sqliteCount = await this.sqliteAdapter.get('SELECT COUNT(*) as count FROM servers');
            const postgresCount = await this.postgresAdapter.get('SELECT COUNT(*) as count FROM servers');
            
            assert.strictEqual(
                sqliteCount.count, 
                postgresCount.count, 
                '服务器数量统计不一致'
            );
        });

        await this.runTest('复杂JOIN查询结果一致性', async () => {
            if (!this.postgresAdapter) {
                console.log('    ⚠️ PostgreSQL不可用，跳过对比测试');
                return;
            }

            const query = `
                SELECT 
                    s.sid, 
                    s.name, 
                    g.name as group_name,
                    COUNT(la.id) as load_count
                FROM servers s
                LEFT JOIN groups g ON s.group_id = g.id
                LEFT JOIN load_archive la ON s.sid = la.sid
                GROUP BY s.sid, s.name, g.name
                ORDER BY s.sid
                LIMIT 10
            `;

            const sqliteResults = await this.sqliteAdapter.all(query);
            const postgresResults = await this.postgresAdapter.all(query);
            
            assert.strictEqual(
                sqliteResults.length, 
                postgresResults.length, 
                'JOIN查询结果数量不一致'
            );
            
            // 比较每一行数据
            for (let i = 0; i < sqliteResults.length; i++) {
                const sqliteRow = sqliteResults[i];
                const postgresRow = postgresResults[i];
                
                assert.strictEqual(sqliteRow.sid, postgresRow.sid, `第${i}行sid不一致`);
                assert.strictEqual(sqliteRow.name, postgresRow.name, `第${i}行name不一致`);
                assert.strictEqual(sqliteRow.group_name, postgresRow.group_name, `第${i}行group_name不一致`);
            }
        });
    }

    async testInsertCompatibility() {
        console.log('\n📋 数据插入兼容性测试');
        
        await this.runTest('插入服务器数据一致性', async () => {
            if (!this.postgresAdapter) {
                console.log('    ⚠️ PostgreSQL不可用，跳过对比测试');
                return;
            }

            const serverData = this.testData.randomServerData();
            const serverId = this.testData.randomServerId();

            // 插入到两个数据库
            await this.sqliteAdapter.run(
                'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
                [serverId, serverData.name, serverData.data, serverData.group_id, serverData.status]
            );

            await this.postgresAdapter.run(
                'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
                [serverId, serverData.name, serverData.data, serverData.group_id, serverData.status]
            );

            // 验证插入结果
            const sqliteResult = await this.sqliteAdapter.get('SELECT * FROM servers WHERE sid = ?', [serverId]);
            const postgresResult = await this.postgresAdapter.get('SELECT * FROM servers WHERE sid = ?', [serverId]);

            TestHelpers.assertions.assertDatabaseCompatibility(
                sqliteResult, 
                postgresResult, 
                '服务器数据插入'
            );
        });

        await this.runTest('批量插入性能可接受性', async () => {
            if (!this.postgresAdapter) {
                console.log('    ⚠️ PostgreSQL不可用，跳过对比测试');
                return;
            }

            const batchSize = 50; // 减少批量大小以加快测试
            const testServers = Array.from({length: batchSize}, (_, i) => ({
                sid: `batch-test-${i}-${Date.now()}`,
                ...this.testData.randomServerData()
            }));

            // 测试SQLite批量插入性能
            const sqlitePerf = await TestHelpers.performance.measureExecutionTime(async () => {
                for (const server of testServers) {
                    await this.sqliteAdapter.run(
                        'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
                        [server.sid, server.name, server.data, server.group_id, server.status]
                    );
                }
            });

            // 测试PostgreSQL批量插入性能
            const postgresPerf = await TestHelpers.performance.measureExecutionTime(async () => {
                for (const server of testServers) {
                    await this.postgresAdapter.run(
                        'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
                        [`pg-${server.sid}`, server.name, server.data, server.group_id, server.status]
                    );
                }
            });

            // 验证性能差异在合理范围内（PostgreSQL应在SQLite的5倍以内）
            const performanceRatio = postgresPerf.executionTimeMs / sqlitePerf.executionTimeMs;
            assert(
                performanceRatio <= 5, 
                `PostgreSQL批量插入性能比SQLite慢${performanceRatio.toFixed(2)}倍，超出预期范围`
            );

            console.log(`    📊 批量插入性能对比: SQLite ${sqlitePerf.executionTimeMs.toFixed(2)}ms, PostgreSQL ${postgresPerf.executionTimeMs.toFixed(2)}ms`);
        });
    }

    async testTransactionCompatibility() {
        console.log('\n📋 事务兼容性测试');
        
        await this.runTest('事务提交一致性', async () => {
            if (!this.postgresAdapter) {
                console.log('    ⚠️ PostgreSQL不可用，跳过对比测试');
                return;
            }

            const testServerId1 = `tx-test-commit-1-${Date.now()}`;
            const testServerId2 = `tx-test-commit-2-${Date.now()}`;

            // SQLite事务测试
            await this.sqliteAdapter.beginTransaction();
            await this.sqliteAdapter.run(
                'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
                [testServerId1, '事务测试1', '{}', 'default', 1]
            );
            await this.sqliteAdapter.commitTransaction();

            // PostgreSQL事务测试
            await this.postgresAdapter.beginTransaction();
            await this.postgresAdapter.run(
                'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
                [testServerId2, '事务测试2', '{}', 'default', 1]
            );
            await this.postgresAdapter.commitTransaction();

            // 验证提交成功
            const sqliteResult = await this.sqliteAdapter.get('SELECT * FROM servers WHERE sid = ?', [testServerId1]);
            const postgresResult = await this.postgresAdapter.get('SELECT * FROM servers WHERE sid = ?', [testServerId2]);

            assert(sqliteResult, 'SQLite事务提交失败');
            assert(postgresResult, 'PostgreSQL事务提交失败');
        });

        await this.runTest('事务回滚一致性', async () => {
            if (!this.postgresAdapter) {
                console.log('    ⚠️ PostgreSQL不可用，跳过对比测试');
                return;
            }

            const testServerId1 = `tx-test-rollback-1-${Date.now()}`;
            const testServerId2 = `tx-test-rollback-2-${Date.now()}`;

            // SQLite事务回滚测试
            await this.sqliteAdapter.beginTransaction();
            await this.sqliteAdapter.run(
                'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
                [testServerId1, '回滚测试1', '{}', 'default', 1]
            );
            await this.sqliteAdapter.rollbackTransaction();

            // PostgreSQL事务回滚测试
            await this.postgresAdapter.beginTransaction();
            await this.postgresAdapter.run(
                'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
                [testServerId2, '回滚测试2', '{}', 'default', 1]
            );
            await this.postgresAdapter.rollbackTransaction();

            // 验证回滚成功（数据不应该存在）
            const sqliteResult = await this.sqliteAdapter.get('SELECT * FROM servers WHERE sid = ?', [testServerId1]);
            const postgresResult = await this.postgresAdapter.get('SELECT * FROM servers WHERE sid = ?', [testServerId2]);

            assert.strictEqual(sqliteResult, null, 'SQLite事务回滚失败');
            assert.strictEqual(postgresResult, null, 'PostgreSQL事务回滚失败');
        });
    }

    async testDataConsistency() {
        console.log('\n📋 数据一致性验证测试');
        
        await this.runTest('负载归档数据查询一致性', async () => {
            if (!this.postgresAdapter) {
                console.log('    ⚠️ PostgreSQL不可用，跳过对比测试');
                return;
            }

            const query = `
                SELECT 
                    sid,
                    AVG(cpu) as avg_cpu,
                    MAX(mem) as max_mem,
                    COUNT(*) as record_count
                FROM load_archive 
                WHERE created_at > ? 
                GROUP BY sid 
                ORDER BY sid 
                LIMIT 10
            `;

            const timeThreshold = Math.floor(Date.now() / 1000) - 3600; // 最近1小时

            const sqliteResults = await this.sqliteAdapter.all(query, [timeThreshold]);
            const postgresResults = await this.postgresAdapter.all(query, [timeThreshold]);

            assert.strictEqual(
                sqliteResults.length, 
                postgresResults.length, 
                '负载归档查询结果数量不一致'
            );

            // 比较统计数据
            for (let i = 0; i < sqliteResults.length; i++) {
                const sqliteRow = sqliteResults[i];
                const postgresRow = postgresResults[i];
                
                assert.strictEqual(sqliteRow.sid, postgresRow.sid, `第${i}行sid不一致`);
                
                // 数值字段允许小的浮点误差
                const cpuDiff = Math.abs(sqliteRow.avg_cpu - postgresRow.avg_cpu);
                const memDiff = Math.abs(sqliteRow.max_mem - postgresRow.max_mem);
                
                assert(cpuDiff < 0.01, `第${i}行avg_cpu差异过大: ${cpuDiff}`);
                assert(memDiff < 0.01, `第${i}行max_mem差异过大: ${memDiff}`);
                assert.strictEqual(sqliteRow.record_count, postgresRow.record_count, `第${i}行record_count不一致`);
            }
        });

        await this.runTest('JSON数据处理一致性', async () => {
            if (!this.postgresAdapter) {
                console.log('    ⚠️ PostgreSQL不可用，跳过对比测试');
                return;
            }

            const testData = {
                complex: {
                    nested: {
                        array: [1, 2, 3],
                        string: 'test中文',
                        boolean: true,
                        null: null
                    }
                }
            };

            const jsonString = JSON.stringify(testData);
            const testSid = `json-test-server-${Date.now()}`;

            // 插入JSON数据
            await this.sqliteAdapter.run(
                'INSERT OR REPLACE INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
                [testSid, 'JSON测试', jsonString, 'default', 1]
            );

            await this.postgresAdapter.run(
                'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?) ON CONFLICT (sid) DO UPDATE SET data = ?',
                [testSid, 'JSON测试', jsonString, 'default', 1, jsonString]
            );

            // 查询并比较JSON数据
            const sqliteResult = await this.sqliteAdapter.get('SELECT data FROM servers WHERE sid = ?', [testSid]);
            const postgresResult = await this.postgresAdapter.get('SELECT data FROM servers WHERE sid = ?', [testSid]);

            assert.strictEqual(sqliteResult.data, postgresResult.data, 'JSON数据不一致');

            // 验证JSON可以正确解析
            const sqliteParsed = JSON.parse(sqliteResult.data);
            const postgresParsed = JSON.parse(postgresResult.data);

            assert.deepStrictEqual(sqliteParsed, postgresParsed, '解析后的JSON数据不一致');
        });
    }

    async testIndexPerformance() {
        console.log('\n📋 索引和性能测试');
        
        await this.runTest('索引查询性能可接受性', async () => {
            if (!this.postgresAdapter) {
                console.log('    ⚠️ PostgreSQL不可用，跳过对比测试');
                return;
            }

            const query = 'SELECT * FROM load_archive WHERE sid = ? ORDER BY created_at DESC LIMIT 100';
            const testSid = 'test-server-001';

            // 测试SQLite索引查询性能
            const sqlitePerf = await TestHelpers.performance.batchPerformanceTest(
                this.sqliteAdapter.all.bind(this.sqliteAdapter),
                3, // 减少迭代次数
                query,
                testSid
            );

            // 测试PostgreSQL索引查询性能
            const postgresPerf = await TestHelpers.performance.batchPerformanceTest(
                this.postgresAdapter.all.bind(this.postgresAdapter),
                3, // 减少迭代次数
                query,
                testSid
            );

            // 验证性能在可接受范围内
            assert(
                sqlitePerf.average < 1000,
                `SQLite查询性能过慢: ${sqlitePerf.average.toFixed(2)}ms`
            );

            assert(
                postgresPerf.average < 2000,
                `PostgreSQL查询性能过慢: ${postgresPerf.average.toFixed(2)}ms`
            );

            console.log(`    📊 索引查询性能: SQLite ${sqlitePerf.average.toFixed(2)}ms, PostgreSQL ${postgresPerf.average.toFixed(2)}ms`);
        });
    }

    async testErrorHandling() {
        console.log('\n📋 错误处理兼容性测试');
        
        await this.runTest('约束违反错误处理', async () => {
            if (!this.postgresAdapter) {
                console.log('    ⚠️ PostgreSQL不可用，跳过对比测试');
                return;
            }

            const duplicateSid = `duplicate-test-${Date.now()}`;

            // 插入第一条记录
            await this.sqliteAdapter.run(
                'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
                [duplicateSid, '重复测试', '{}', 'default', 1]
            );

            await this.postgresAdapter.run(
                'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
                [duplicateSid, '重复测试', '{}', 'default', 1]
            );

            // 尝试插入重复记录，应该抛出错误
            let sqliteError = null;
            let postgresError = null;

            try {
                await this.sqliteAdapter.run(
                    'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
                    [duplicateSid, '重复测试2', '{}', 'default', 1]
                );
            } catch (error) {
                sqliteError = error;
            }

            try {
                await this.postgresAdapter.run(
                    'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
                    [duplicateSid, '重复测试2', '{}', 'default', 1]
                );
            } catch (error) {
                postgresError = error;
            }

            // 验证两个数据库都抛出了错误
            assert(sqliteError, 'SQLite应该抛出重复键错误');
            assert(postgresError, 'PostgreSQL应该抛出重复键错误');
        });
    }

    printResults() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 数据库兼容性测试结果');
        console.log('='.repeat(60));
        console.log(`总测试数: ${this.testResults.total}`);
        console.log(`通过测试: ${this.testResults.passed}`);
        console.log(`失败测试: ${this.testResults.failed}`);
        
        if (this.testResults.failed > 0) {
            console.log('\n❌ 失败的测试:');
            this.testResults.errors.forEach(error => {
                console.log(`  • ${error.test}: ${error.error}`);
            });
        } else {
            console.log('\n✅ 所有兼容性测试通过！');
        }
        
        console.log('='.repeat(60));
    }
}

// 运行测试的主函数
async function runDatabaseCompatibilityTest() {
    const test = new DatabaseCompatibilityTest();
    await test.runAllTests();
}

module.exports = { DatabaseCompatibilityTest, runDatabaseCompatibilityTest };

// 如果直接运行此文件
if (require.main === module) {
    runDatabaseCompatibilityTest().catch(console.error);
}