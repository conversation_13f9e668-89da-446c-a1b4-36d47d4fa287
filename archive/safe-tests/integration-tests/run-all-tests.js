/**
 * 统一测试运行器
 * 执行所有数据库兼容性、性能和压力测试
 */

const path = require('path');
const fs = require('fs').promises;
const testConfig = require('./config/test-config');

// 导入测试模块
const { runDatabaseBenchmark } = require('./performance/benchmark/database-operations');
const { runConcurrentStressTest } = require('./performance/stress/concurrent-access');

class TestRunner {
    constructor() {
        this.results = {
            timestamp: new Date().toISOString(),
            test_suite: 'DStatus 双数据库兼容性完整测试套件',
            environment: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                memoryUsage: process.memoryUsage()
            },
            test_results: {},
            overall_summary: {}
        };
    }

    /**
     * 运行所有测试套件
     */
    async runAllTests() {
        console.log('🚀 开始DStatus双数据库兼容性完整测试...\n');
        
        const startTime = Date.now();
        
        try {
            // 1. 运行兼容性测试
            await this.runCompatibilityTests();
            
            // 2. 运行性能基准测试
            await this.runPerformanceTests();
            
            // 3. 运行压力测试
            await this.runStressTests();
            
            // 4. 运行端到端集成测试
            await this.runIntegrationTests();
            
            const endTime = Date.now();
            this.results.total_duration = endTime - startTime;
            
            // 生成最终报告
            await this.generateFinalReport();
            
            console.log('\n✅ 所有测试完成！');
            
        } catch (error) {
            console.error('\n❌ 测试套件执行失败:', error);
            throw error;
        }
    }

    /**
     * 运行兼容性测试
     */
    async runCompatibilityTests() {
        console.log('📋 步骤 1/4: 数据库兼容性测试');
        console.log('=' .repeat(50));
        
        try {
            // 执行兼容性测试
            const { runDatabaseCompatibilityTest } = require('./integration/database-compatibility.test.js');
            await runDatabaseCompatibilityTest();
            
            this.results.test_results.compatibility = {
                status: 'completed',
                message: '数据库兼容性测试完成'
            };
            
            console.log('✅ 兼容性测试完成\n');
            
        } catch (error) {
            this.results.test_results.compatibility = {
                status: 'failed',
                error: error.message
            };
            console.error('❌ 兼容性测试失败:', error.message);
            throw error;
        }
    }

    /**
     * 运行性能基准测试
     */
    async runPerformanceTests() {
        console.log('📊 步骤 2/4: 性能基准测试');
        console.log('=' .repeat(50));
        
        try {
            await runDatabaseBenchmark();
            
            this.results.test_results.performance = {
                status: 'completed',
                message: '性能基准测试完成'
            };
            
            console.log('✅ 性能基准测试完成\n');
            
        } catch (error) {
            this.results.test_results.performance = {
                status: 'failed',
                error: error.message
            };
            console.error('❌ 性能基准测试失败:', error.message);
            // 性能测试失败不阻止后续测试
        }
    }

    /**
     * 运行压力测试
     */
    async runStressTests() {
        console.log('⚡ 步骤 3/4: 并发压力测试');
        console.log('=' .repeat(50));
        
        try {
            await runConcurrentStressTest();
            
            this.results.test_results.stress = {
                status: 'completed',
                message: '压力测试完成'
            };
            
            console.log('✅ 压力测试完成\n');
            
        } catch (error) {
            this.results.test_results.stress = {
                status: 'failed',
                error: error.message
            };
            console.error('❌ 压力测试失败:', error.message);
            // 压力测试失败不阻止后续测试
        }
    }

    /**
     * 运行端到端集成测试
     */
    async runIntegrationTests() {
        console.log('🔗 步骤 4/4: 端到端集成测试');
        console.log('=' .repeat(50));
        
        try {
            await this.runEndToEndTests();
            
            this.results.test_results.e2e = {
                status: 'completed',
                message: '端到端测试完成'
            };
            
            console.log('✅ 端到端测试完成\n');
            
        } catch (error) {
            this.results.test_results.e2e = {
                status: 'failed',
                error: error.message
            };
            console.error('❌ 端到端测试失败:', error.message);
            // 端到端测试失败不阻止报告生成
        }
    }

    /**
     * 执行端到端测试
     */
    async runEndToEndTests() {
        const DatabaseSetup = require('./config/database-setup');
        const TestHelpers = require('./utils/test-helpers');
        
        console.log('  🚀 初始化端到端测试环境...');
        
        const dbSetup = new DatabaseSetup();
        await dbSetup.initializeTestEnvironment();
        
        const sqliteAdapter = dbSetup.getDatabase('sqlite');
        const postgresAdapter = dbSetup.getDatabase('postgresql');
        
        try {
            // 测试完整的数据流程
            await this.testCompleteDataFlow(sqliteAdapter, postgresAdapter);
            
            // 测试API级别的操作
            await this.testApiLevelOperations(sqliteAdapter, postgresAdapter);
            
            // 测试数据迁移场景
            await this.testDataMigrationScenario(sqliteAdapter, postgresAdapter);
            
        } finally {
            await dbSetup.cleanup();
        }
    }

    /**
     * 测试完整数据流程
     */
    async testCompleteDataFlow(sqliteAdapter, postgresAdapter) {
        console.log('  📝 测试完整数据流程...');
        
        if (!postgresAdapter) {
            console.log('    ⚠️ PostgreSQL不可用，跳过对比测试');
            return;
        }

        const testData = TestHelpers.generateRandomData();
        const serverId = 'e2e-flow-test';
        
        // 1. 创建服务器
        const serverData = testData.randomServerData();
        
        await sqliteAdapter.run(
            'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
            [serverId, serverData.name, serverData.data, serverData.group_id, serverData.status]
        );
        
        await postgresAdapter.run(
            'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
            [serverId, serverData.name, serverData.data, serverData.group_id, serverData.status]
        );
        
        // 2. 添加负载数据
        const loadData = testData.randomLoadData();
        for (let i = 0; i < 10; i++) {
            await sqliteAdapter.run(
                'INSERT INTO load_archive (sid, cpu, mem, swap, ibw, obw, expire_time) VALUES (?, ?, ?, ?, ?, ?, ?)',
                [serverId, loadData.cpu + i, loadData.mem + i, loadData.swap, loadData.ibw, loadData.obw, loadData.expire_time]
            );
            
            await postgresAdapter.run(
                'INSERT INTO load_archive (sid, cpu, mem, swap, ibw, obw, expire_time) VALUES (?, ?, ?, ?, ?, ?, ?)',
                [serverId, loadData.cpu + i, loadData.mem + i, loadData.swap, loadData.ibw, loadData.obw, loadData.expire_time]
            );
        }
        
        // 3. 查询和验证数据一致性
        const sqliteServer = await sqliteAdapter.get('SELECT * FROM servers WHERE sid = ?', [serverId]);
        const postgresServer = await postgresAdapter.get('SELECT * FROM servers WHERE sid = ?', [serverId]);
        
        if (JSON.stringify(sqliteServer) !== JSON.stringify(postgresServer)) {
            throw new Error('服务器数据不一致');
        }
        
        const sqliteLoadCount = await sqliteAdapter.get('SELECT COUNT(*) as count FROM load_archive WHERE sid = ?', [serverId]);
        const postgresLoadCount = await postgresAdapter.get('SELECT COUNT(*) as count FROM load_archive WHERE sid = ?', [serverId]);
        
        if (sqliteLoadCount.count !== postgresLoadCount.count) {
            throw new Error('负载数据数量不一致');
        }
        
        console.log('    ✅ 完整数据流程测试通过');
    }

    /**
     * 测试API级别的操作
     */
    async testApiLevelOperations(sqliteAdapter, postgresAdapter) {
        console.log('  🔌 测试API级别操作...');
        
        // 模拟API常用的复杂查询
        const apiQueries = [
            {
                name: '服务器列表查询',
                sql: 'SELECT s.*, g.name as group_name FROM servers s LEFT JOIN groups g ON s.group_id = g.id ORDER BY s.sid LIMIT 20'
            },
            {
                name: '负载统计查询',
                sql: 'SELECT sid, AVG(cpu) as avg_cpu, MAX(mem) as max_mem, COUNT(*) as records FROM load_archive GROUP BY sid LIMIT 10'
            },
            {
                name: '最新数据查询',
                sql: 'SELECT * FROM load_archive WHERE created_at > ? ORDER BY created_at DESC LIMIT 50',
                params: [Math.floor(Date.now() / 1000) - 3600]
            }
        ];
        
        for (const query of apiQueries) {
            const params = query.params || [];
            
            if (sqliteAdapter) {
                const sqliteResult = await sqliteAdapter.all(query.sql, params);
                console.log(`    SQLite ${query.name}: ${sqliteResult.length} 条记录`);
            }
            
            if (postgresAdapter) {
                const postgresResult = await postgresAdapter.all(query.sql, params);
                console.log(`    PostgreSQL ${query.name}: ${postgresResult.length} 条记录`);
            }
        }
        
        console.log('    ✅ API级别操作测试通过');
    }

    /**
     * 测试数据迁移场景
     */
    async testDataMigrationScenario(sqliteAdapter, postgresAdapter) {
        console.log('  🔄 测试数据迁移场景...');
        
        if (!postgresAdapter) {
            console.log('    ⚠️ PostgreSQL不可用，跳过迁移测试');
            return;
        }

        // 模拟从SQLite迁移数据到PostgreSQL的场景
        const migrationData = [
            { sid: 'migrate-1', name: '迁移测试1', data: '{}', group_id: 'default', status: 1 },
            { sid: 'migrate-2', name: '迁移测试2', data: '{}', group_id: 'default', status: 1 },
            { sid: 'migrate-3', name: '迁移测试3', data: '{}', group_id: 'default', status: 1 }
        ];
        
        // 1. 在SQLite中插入测试数据
        for (const server of migrationData) {
            await sqliteAdapter.run(
                'INSERT OR REPLACE INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?)',
                [server.sid, server.name, server.data, server.group_id, server.status]
            );
        }
        
        // 2. 从SQLite读取数据
        const sourceData = await sqliteAdapter.all('SELECT * FROM servers WHERE sid LIKE \'migrate-%\'');
        
        // 3. 迁移到PostgreSQL
        for (const server of sourceData) {
            await postgresAdapter.run(
                'INSERT INTO servers (sid, name, data, group_id, status) VALUES (?, ?, ?, ?, ?) ON CONFLICT (sid) DO UPDATE SET name = ?, data = ?, group_id = ?, status = ?',
                [server.sid, server.name, server.data, server.group_id, server.status, server.name, server.data, server.group_id, server.status]
            );
        }
        
        // 4. 验证迁移结果
        const targetData = await postgresAdapter.all('SELECT * FROM servers WHERE sid LIKE \'migrate-%\' ORDER BY sid');
        
        if (sourceData.length !== targetData.length) {
            throw new Error('迁移数据数量不匹配');
        }
        
        for (let i = 0; i < sourceData.length; i++) {
            const source = sourceData[i];
            const target = targetData[i];
            
            if (source.sid !== target.sid || source.name !== target.name) {
                throw new Error(`迁移数据不匹配: ${source.sid}`);
            }
        }
        
        console.log(`    ✅ 数据迁移测试通过 (${sourceData.length} 条记录)`);
    }

    /**
     * 运行Node.js测试文件
     */
    async runNodeTestFile(testFile) {
        const testPath = path.join(__dirname, testFile);
        
        try {
            // 动态加载并运行测试文件
            delete require.cache[require.resolve(testPath)];
            const testModule = require(testPath);
            
            // 如果模块导出了运行函数，调用它
            if (typeof testModule === 'function') {
                await testModule();
            } else if (testModule.run && typeof testModule.run === 'function') {
                await testModule.run();
            }
            
        } catch (error) {
            console.error(`测试文件 ${testFile} 执行失败:`, error.message);
            throw error;
        }
    }

    /**
     * 生成最终测试报告
     */
    async generateFinalReport() {
        console.log('\n📄 生成最终测试报告...');
        
        // 计算总体摘要
        this.results.overall_summary = this.calculateOverallSummary();
        
        // 保存详细报告
        const reportPath = path.join(testConfig.reporting.outputDir, 'final-test-report.json');
        await fs.writeFile(reportPath, JSON.stringify(this.results, null, 2));
        
        // 生成人类可读的摘要报告
        const summaryPath = path.join(testConfig.reporting.outputDir, 'test-summary.md');
        const summaryContent = this.generateMarkdownSummary();
        await fs.writeFile(summaryPath, summaryContent);
        
        console.log(`✅ 详细报告已保存: ${reportPath}`);
        console.log(`✅ 摘要报告已保存: ${summaryPath}`);
        
        // 打印测试摘要
        this.printTestSummary();
    }

    /**
     * 计算总体摘要
     */
    calculateOverallSummary() {
        const summary = {
            total_test_categories: Object.keys(this.results.test_results).length,
            successful_categories: 0,
            failed_categories: 0,
            test_duration_minutes: Math.round(this.results.total_duration / 1000 / 60),
            overall_status: 'unknown'
        };
        
        for (const [category, result] of Object.entries(this.results.test_results)) {
            if (result.status === 'completed') {
                summary.successful_categories++;
            } else {
                summary.failed_categories++;
            }
        }
        
        // 确定总体状态
        if (summary.failed_categories === 0) {
            summary.overall_status = 'all_passed';
        } else if (summary.successful_categories > summary.failed_categories) {
            summary.overall_status = 'mostly_passed';
        } else {
            summary.overall_status = 'failed';
        }
        
        return summary;
    }

    /**
     * 生成Markdown摘要
     */
    generateMarkdownSummary() {
        const summary = this.results.overall_summary;
        const timestamp = new Date(this.results.timestamp).toLocaleString('zh-CN');
        
        let content = `# DStatus 双数据库兼容性测试报告\n\n`;
        content += `**测试时间**: ${timestamp}\n`;
        content += `**测试持续时间**: ${summary.test_duration_minutes} 分钟\n`;
        content += `**Node.js版本**: ${this.results.environment.nodeVersion}\n`;
        content += `**平台**: ${this.results.environment.platform} (${this.results.environment.arch})\n\n`;
        
        // 总体状态
        const statusIcon = summary.overall_status === 'all_passed' ? '✅' : 
                          summary.overall_status === 'mostly_passed' ? '⚠️' : '❌';
        content += `## ${statusIcon} 总体状态\n\n`;
        content += `- **成功测试类别**: ${summary.successful_categories}/${summary.total_test_categories}\n`;
        content += `- **失败测试类别**: ${summary.failed_categories}/${summary.total_test_categories}\n\n`;
        
        // 详细结果
        content += `## 📋 详细测试结果\n\n`;
        
        for (const [category, result] of Object.entries(this.results.test_results)) {
            const icon = result.status === 'completed' ? '✅' : '❌';
            const categoryName = {
                compatibility: '数据库兼容性测试',
                performance: '性能基准测试',
                stress: '并发压力测试',
                e2e: '端到端集成测试'
            }[category] || category;
            
            content += `### ${icon} ${categoryName}\n\n`;
            content += `**状态**: ${result.status}\n`;
            content += `**说明**: ${result.message || result.error || '无'}\n\n`;
        }
        
        // 结论和建议
        content += `## 🎯 结论和建议\n\n`;
        
        if (summary.overall_status === 'all_passed') {
            content += `🎉 所有测试均已通过！PostgreSQL迁移架构已准备就绪，可以安全地在生产环境中使用。\n\n`;
            content += `### 推荐下一步行动:\n`;
            content += `1. ✅ 部署PostgreSQL生产环境\n`;
            content += `2. ✅ 配置数据迁移工具\n`;
            content += `3. ✅ 建立监控和备份策略\n`;
        } else if (summary.overall_status === 'mostly_passed') {
            content += `⚠️ 大部分测试通过，但存在一些非关键问题。建议在生产部署前解决这些问题。\n\n`;
            content += `### 需要关注的问题:\n`;
            for (const [category, result] of Object.entries(this.results.test_results)) {
                if (result.status === 'failed') {
                    content += `- **${category}**: ${result.error}\n`;
                }
            }
        } else {
            content += `❌ 测试发现重大问题，不建议立即部署到生产环境。需要先解决这些问题。\n\n`;
        }
        
        content += `\n---\n`;
        content += `*此报告由DStatus自动化测试套件生成*`;
        
        return content;
    }

    /**
     * 打印测试摘要
     */
    printTestSummary() {
        console.log('\n🎯 测试执行摘要');
        console.log('='.repeat(60));
        
        const summary = this.results.overall_summary;
        
        console.log(`测试持续时间: ${summary.test_duration_minutes} 分钟`);
        console.log(`成功测试类别: ${summary.successful_categories}/${summary.total_test_categories}`);
        console.log(`失败测试类别: ${summary.failed_categories}/${summary.total_test_categories}`);
        
        console.log('\n📋 测试类别结果:');
        for (const [category, result] of Object.entries(this.results.test_results)) {
            const icon = result.status === 'completed' ? '✅' : '❌';
            const categoryName = {
                compatibility: '兼容性测试',
                performance: '性能测试',
                stress: '压力测试',
                e2e: '集成测试'
            }[category] || category;
            
            console.log(`  ${icon} ${categoryName}: ${result.status}`);
        }
        
        console.log('\n' + '='.repeat(60));
        
        if (summary.overall_status === 'all_passed') {
            console.log('🎉 恭喜！所有测试通过，PostgreSQL迁移架构验证成功！');
        } else if (summary.overall_status === 'mostly_passed') {
            console.log('⚠️ 大部分测试通过，请查看报告了解详细问题');
        } else {
            console.log('❌ 测试发现问题，请检查详细报告');
        }
    }
}

// 主函数
async function runAllTests() {
    const runner = new TestRunner();
    
    try {
        await runner.runAllTests();
        return runner.results;
    } catch (error) {
        console.error('❌ 测试套件执行失败:', error);
        process.exit(1);
    }
}

module.exports = { TestRunner, runAllTests };

// 如果直接运行此文件
if (require.main === module) {
    runAllTests().catch(console.error);
}