#!/usr/bin/env node
/**
 * 测试 4：应用启动追踪
 * 目标：追踪应用启动过程，找出阻塞点
 * 方式：分析启动日志和时间线
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('='.repeat(60));
console.log('应用启动追踪测试');
console.log('='.repeat(60));
console.log('测试时间:', new Date().toISOString());
console.log('');

async function runTests() {
    const results = {
        timestamp: new Date().toISOString(),
        events: [],
        analysis: {}
    };

    try {
        // 1. 分析现有的启动日志
        console.log('[1] 分析启动日志...');
        
        const logPath = path.join(__dirname, '../data/logs/SYSTEM-2025-07.log');
        
        if (fs.existsSync(logPath)) {
            const logContent = fs.readFileSync(logPath, 'utf8');
            const lines = logContent.split('\n').filter(line => line.trim());
            
            // 提取最近的启动序列（最后 200 行）
            const recentLines = lines.slice(-200);
            
            // 分析关键事件
            const keyEvents = [];
            let serverRunningTime = null;
            let firstBlockingTime = null;
            let blockingCount = 0;
            
            recentLines.forEach(line => {
                try {
                    const log = JSON.parse(line);
                    
                    // 记录服务器启动
                    if (log.message.includes('server running @')) {
                        serverRunningTime = new Date(log.timestamp);
                        keyEvents.push({
                            time: log.timestamp,
                            event: 'SERVER_RUNNING',
                            message: log.message
                        });
                    }
                    
                    // 记录第一次阻塞
                    if (log.message.includes('上次负载数据保存仍在进行中')) {
                        blockingCount++;
                        if (!firstBlockingTime) {
                            firstBlockingTime = new Date(log.timestamp);
                            keyEvents.push({
                                time: log.timestamp,
                                event: 'FIRST_BLOCKING',
                                message: log.message
                            });
                        }
                    }
                    
                    // 记录数据库连接
                    if (log.message.includes('PostgreSQL 连接成功')) {
                        keyEvents.push({
                            time: log.timestamp,
                            event: 'DB_CONNECTED',
                            message: log.message
                        });
                    }
                    
                    // 记录归档间隔设置
                    if (log.message.includes('负载归档间隔设置为')) {
                        keyEvents.push({
                            time: log.timestamp,
                            event: 'ARCHIVE_TIMER_SET',
                            message: log.message
                        });
                    }
                } catch (e) {
                    // 忽略解析错误
                }
            });
            
            results.events = keyEvents;
            results.analysis.blockingCount = blockingCount;
            
            if (serverRunningTime && firstBlockingTime) {
                const delayMs = firstBlockingTime - serverRunningTime;
                results.analysis.timeToFirstBlocking = delayMs;
                console.log(`✓ 服务器启动到首次阻塞的时间: ${delayMs}ms`);
            }
            
            console.log(`  发现 ${keyEvents.length} 个关键事件`);
            console.log(`  阻塞消息出现次数: ${blockingCount}`);
        } else {
            console.log('✗ 未找到系统日志文件');
        }

        // 2. 检查启动时的内存使用
        console.log('\n[2] 检查内存使用情况...');
        
        const performanceLogPath = path.join(__dirname, '../data/logs/PERFORMANCE-2025-07.log');
        if (fs.existsSync(performanceLogPath)) {
            const perfContent = fs.readFileSync(performanceLogPath, 'utf8');
            const perfLines = perfContent.split('\n').filter(line => line.trim());
            const recentPerf = perfLines.slice(-10);
            
            const memoryWarnings = [];
            recentPerf.forEach(line => {
                try {
                    const log = JSON.parse(line);
                    if (log.message.includes('内存使用率极高')) {
                        memoryWarnings.push({
                            time: log.timestamp,
                            message: log.message
                        });
                    }
                } catch (e) {
                    // 忽略解析错误
                }
            });
            
            results.analysis.memoryWarnings = memoryWarnings;
            console.log(`  发现 ${memoryWarnings.length} 个内存警告`);
            
            if (memoryWarnings.length > 0) {
                console.log('  最近的内存警告:');
                memoryWarnings.slice(-3).forEach(warning => {
                    console.log(`    - ${warning.message}`);
                });
            }
        }

        // 3. 测试简单的 HTTP 请求
        console.log('\n[3] 测试端口 5555 连接...');
        
        const http = require('http');
        const testPort = () => {
            return new Promise((resolve) => {
                const options = {
                    hostname: 'localhost',
                    port: 5555,
                    path: '/',
                    method: 'GET',
                    timeout: 5000
                };
                
                const req = http.request(options, (res) => {
                    resolve({
                        status: 'CONNECTED',
                        statusCode: res.statusCode
                    });
                });
                
                req.on('error', (err) => {
                    resolve({
                        status: 'FAILED',
                        error: err.message
                    });
                });
                
                req.on('timeout', () => {
                    req.destroy();
                    resolve({
                        status: 'TIMEOUT',
                        error: 'Request timeout after 5 seconds'
                    });
                });
                
                req.end();
            });
        };
        
        const portTest = await testPort();
        results.analysis.portTest = portTest;
        
        if (portTest.status === 'CONNECTED') {
            console.log(`✓ 端口 5555 可访问，状态码: ${portTest.statusCode}`);
        } else {
            console.log(`✗ 端口 5555 不可访问: ${portTest.error}`);
        }

        // 4. 分析时间线
        console.log('\n[4] 分析启动时间线...');
        
        if (results.events.length > 0) {
            console.log('  关键事件时间线:');
            
            const firstEvent = new Date(results.events[0].time);
            results.events.forEach(event => {
                const eventTime = new Date(event.time);
                const elapsed = eventTime - firstEvent;
                console.log(`    +${elapsed}ms: ${event.event}`);
            });
        }

        // 5. 诊断结论
        console.log('\n[5] 诊断结论:');
        
        if (results.analysis.blockingCount > 10) {
            console.log('  ⚠️  检测到持续的阻塞状态');
            console.log('  原因: saveAllLoadData 函数执行时间超过定时器间隔');
        }
        
        if (results.analysis.memoryWarnings && results.analysis.memoryWarnings.length > 0) {
            console.log('  ⚠️  检测到高内存使用');
            console.log('  可能影响数据库操作性能');
        }
        
        if (results.analysis.portTest && results.analysis.portTest.status !== 'CONNECTED') {
            console.log('  ⚠️  HTTP 服务不可访问');
            console.log('  可能被长时间运行的操作阻塞');
        }

    } catch (error) {
        console.error('\n发生未预期的错误:', error);
        results.error = error.message;
    }

    // 保存测试结果
    const resultPath = path.join(__dirname, 'test-4-app-startup-trace-results.json');
    fs.writeFileSync(resultPath, JSON.stringify(results, null, 2));
    console.log(`\n测试结果已保存到: ${resultPath}`);

    return results;
}

// 执行测试
runTests().catch(console.error);