#!/usr/bin/env node
/**
 * 测试 2：数据库操作性能诊断
 * 目标：检查数据库操作是否存在性能瓶颈，特别是 getServers() 和负载数据保存
 * 方式：无害测试，只读取数据和测量时间
 */

const fs = require('fs');
const path = require('path');

console.log('='.repeat(60));
console.log('数据库操作性能诊断测试');
console.log('='.repeat(60));
console.log('测试时间:', new Date().toISOString());
console.log('工作目录:', process.cwd());
console.log('');

async function runTests() {
    const results = {
        timestamp: new Date().toISOString(),
        tests: [],
        db: null // 保存数据库实例引用
    };

    try {
        // 1. 初始化数据库模块
        console.log('[1] 初始化数据库模块...');
        const dbStartTime = Date.now();
        const dbInit = require('../database');
        const db = await dbInit(); // 需要调用异步函数
        const dbInitTime = Date.now() - dbStartTime;
        
        results.tests.push({
            test: '数据库模块初始化',
            status: 'PASS',
            initTime: dbInitTime,
            dbType: db.type
        });
        console.log(`✓ 数据库模块初始化成功，耗时: ${dbInitTime}ms`);
        console.log(`  数据库类型: ${db.type}`);
        
        // 保存数据库实例
        results.db = db;

        // 等待数据库连接稳定
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 2. 测试 getServers() 性能
        console.log('\n[2] 测试 getServers() 查询性能...');
        
        try {
            // 第一次查询（冷启动）
            const coldStart = Date.now();
            const servers1 = await db.getServers();
            const coldTime = Date.now() - coldStart;
            
            // 第二次查询（热查询）
            const warmStart = Date.now();
            const servers2 = await db.getServers();
            const warmTime = Date.now() - warmStart;
            
            results.tests.push({
                test: 'getServers() 查询性能',
                status: 'PASS',
                coldStartTime: coldTime,
                warmStartTime: warmTime,
                serverCount: servers1.length
            });
            
            console.log('✓ getServers() 查询成功');
            console.log(`  服务器数量: ${servers1.length}`);
            console.log(`  冷启动耗时: ${coldTime}ms`);
            console.log(`  热查询耗时: ${warmTime}ms`);
            
            // 输出前5个服务器的信息（脱敏）
            if (servers1.length > 0) {
                console.log('  示例服务器:');
                servers1.slice(0, 5).forEach((server, idx) => {
                    console.log(`    ${idx + 1}. SID: ${server.sid?.substring(0, 8)}... (${server.name || 'unnamed'})`);
                });
            }
        } catch (error) {
            results.tests.push({
                test: 'getServers() 查询性能',
                status: 'FAIL',
                error: error.message
            });
            console.error('✗ getServers() 查询失败:', error.message);
        }

        // 3. 测试 load_archive 表操作
        console.log('\n[3] 测试 load_archive 表操作...');
        
        try {
            // 测试查询记录数
            const countStart = Date.now();
            const countResult = await db.DB.get('SELECT COUNT(*) as count FROM load_archive');
            const countTime = Date.now() - countStart;
            
            console.log(`✓ load_archive 表记录数查询成功`);
            console.log(`  记录总数: ${countResult.count}`);
            console.log(`  查询耗时: ${countTime}ms`);
            
            // 测试查询最新记录
            const latestStart = Date.now();
            const latestRecords = await db.DB.all(`
                SELECT sid, cpu, mem, swap, created_at 
                FROM load_archive 
                ORDER BY created_at DESC 
                LIMIT 10
            `);
            const latestTime = Date.now() - latestStart;
            
            results.tests.push({
                test: 'load_archive 表查询',
                status: 'PASS',
                totalRecords: countResult.count,
                countQueryTime: countTime,
                latestQueryTime: latestTime,
                sampleRecords: latestRecords.length
            });
            
            console.log(`  最新记录查询耗时: ${latestTime}ms`);
            console.log(`  返回记录数: ${latestRecords.length}`);
        } catch (error) {
            results.tests.push({
                test: 'load_archive 表查询',
                status: 'FAIL',
                error: error.message
            });
            console.error('✗ load_archive 表查询失败:', error.message);
        }

        // 4. 测试单条记录插入性能（使用测试数据）
        console.log('\n[4] 测试插入操作性能（使用测试 SID）...');
        
        try {
            const testSid = 'test-' + Date.now();
            
            // 测试直接 SQL 插入
            const insertStart = Date.now();
            await db.DB.run(`
                INSERT INTO load_archive (sid, cpu, mem, swap, ibw, obw, expire_time)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [testSid, 50.5, 60.2, 10.1, 1000, 2000, Math.floor(Date.now() / 1000) + 86400]);
            const insertTime = Date.now() - insertStart;
            
            console.log(`✓ 插入测试记录成功`);
            console.log(`  插入耗时: ${insertTime}ms`);
            
            // 清理测试数据
            await db.DB.run('DELETE FROM load_archive WHERE sid = ?', [testSid]);
            
            results.tests.push({
                test: '单条记录插入性能',
                status: 'PASS',
                insertTime: insertTime
            });
        } catch (error) {
            results.tests.push({
                test: '单条记录插入性能',
                status: 'FAIL',
                error: error.message
            });
            console.error('✗ 插入测试失败:', error.message);
        }

        // 5. 测试事务性能
        console.log('\n[5] 测试事务操作性能...');
        
        try {
            const testSid = 'test-tx-' + Date.now();
            
            const txStart = Date.now();
            const client = await db.DB.beginTransaction();
            
            try {
                // 在事务中执行多个操作
                await client.query(
                    'INSERT INTO load_archive (sid, cpu, mem, swap, ibw, obw, expire_time) VALUES ($1, $2, $3, $4, $5, $6, $7)',
                    [testSid, 40, 50, 5, 500, 600, Math.floor(Date.now() / 1000) + 86400]
                );
                
                await client.query(
                    'SELECT COUNT(*) as count FROM load_archive WHERE sid = $1',
                    [testSid]
                );
                
                await db.DB.commitTransaction(client);
                const txTime = Date.now() - txStart;
                
                console.log(`✓ 事务操作成功`);
                console.log(`  事务耗时: ${txTime}ms`);
                
                // 清理测试数据
                await db.DB.run('DELETE FROM load_archive WHERE sid = ?', [testSid]);
                
                results.tests.push({
                    test: '事务操作性能',
                    status: 'PASS',
                    transactionTime: txTime
                });
            } catch (error) {
                await db.DB.rollbackTransaction(client);
                throw error;
            }
        } catch (error) {
            results.tests.push({
                test: '事务操作性能',
                status: 'FAIL',
                error: error.message
            });
            console.error('✗ 事务测试失败:', error.message);
        }

        // 6. 测试批量查询性能（模拟 saveAllLoadData）
        console.log('\n[6] 模拟 saveAllLoadData 批量操作...');
        
        try {
            const servers = await db.getServers();
            const serverCount = Math.min(servers.length, 10); // 最多测试10个服务器
            
            const batchStart = Date.now();
            let processedCount = 0;
            
            for (let i = 0; i < serverCount; i++) {
                const server = servers[i];
                
                // 模拟检查服务器状态
                const checkStart = Date.now();
                const existingRecords = await db.DB.get(
                    'SELECT COUNT(*) as count FROM load_archive WHERE sid = ?',
                    [server.sid]
                );
                const checkTime = Date.now() - checkStart;
                
                // 记录每个操作的时间
                if (i === 0) {
                    console.log(`  首个服务器查询耗时: ${checkTime}ms`);
                }
                
                processedCount++;
            }
            
            const totalTime = Date.now() - batchStart;
            const avgTime = totalTime / processedCount;
            
            results.tests.push({
                test: '批量操作性能模拟',
                status: 'PASS',
                totalServers: servers.length,
                testedServers: processedCount,
                totalTime: totalTime,
                averageTimePerServer: avgTime
            });
            
            console.log(`✓ 批量操作模拟完成`);
            console.log(`  总服务器数: ${servers.length}`);
            console.log(`  测试服务器数: ${processedCount}`);
            console.log(`  总耗时: ${totalTime}ms`);
            console.log(`  平均每服务器: ${avgTime.toFixed(2)}ms`);
            console.log(`  预估全部处理时间: ${(avgTime * servers.length / 1000).toFixed(2)}秒`);
        } catch (error) {
            results.tests.push({
                test: '批量操作性能模拟',
                status: 'FAIL',
                error: error.message
            });
            console.error('✗ 批量操作模拟失败:', error.message);
        }

    } catch (error) {
        console.error('\n发生未预期的错误:', error);
        results.error = error.message;
    }

    // 保存测试结果（移除 db 实例以避免循环引用）
    const resultPath = path.join(__dirname, 'test-2-database-operations-results.json');
    const { db, ...resultsToSave } = results;
    fs.writeFileSync(resultPath, JSON.stringify(resultsToSave, null, 2));
    console.log(`\n测试结果已保存到: ${resultPath}`);

    // 统计结果
    const passed = results.tests.filter(t => t.status === 'PASS').length;
    const failed = results.tests.filter(t => t.status === 'FAIL').length;
    console.log(`\n测试总结: ${passed} 通过, ${failed} 失败`);
    
    // 性能分析
    console.log('\n性能分析:');
    const batchTest = results.tests.find(t => t.test === '批量操作性能模拟');
    if (batchTest && batchTest.status === 'PASS') {
        const estimatedTime = (batchTest.averageTimePerServer * batchTest.totalServers) / 1000;
        console.log(`- 如果 saveAllLoadData 处理所有 ${batchTest.totalServers} 个服务器`);
        console.log(`  预计需要约 ${estimatedTime.toFixed(2)} 秒`);
        
        if (estimatedTime > 4) {
            console.log('⚠️  警告: 预计处理时间超过归档间隔（4秒）');
            console.log('  这可能是导致 "上次负载数据保存仍在进行中" 的原因');
        }
    }
    
    // 断开数据库连接
    try {
        const db = results.db; // 从结果中获取数据库实例
        if (db && db.DB && db.DB.disconnect) {
            await db.DB.disconnect();
            console.log('\n数据库连接已断开');
        }
    } catch (error) {
        console.error('断开数据库连接时出错:', error.message);
    }
    
    return results;
}

// 执行测试
runTests().catch(console.error);