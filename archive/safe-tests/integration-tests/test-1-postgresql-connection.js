#!/usr/bin/env node
/**
 * 测试 1：PostgreSQL 数据库连接诊断
 * 目标：验证是否能正常连接到 PostgreSQL 数据库
 * 方式：无害测试，只读取配置和测试连接
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
const { parse } = require('pg-connection-string');

console.log('='.repeat(60));
console.log('PostgreSQL 数据库连接诊断测试');
console.log('='.repeat(60));
console.log('测试时间:', new Date().toISOString());
console.log('工作目录:', process.cwd());
console.log('');

async function runTests() {
    const results = {
        timestamp: new Date().toISOString(),
        tests: []
    };

    try {
        // 1. 读取配置文件
        console.log('[1] 读取配置文件...');
        const configPath = path.join(__dirname, '../config/database.json');
        const configContent = fs.readFileSync(configPath, 'utf8');
        const config = JSON.parse(configContent);
        
        results.tests.push({
            test: '读取配置文件',
            status: 'PASS',
            activeDatabase: config.activeDatabase
        });
        console.log('✓ 配置文件读取成功，当前数据库:', config.activeDatabase);

        // 2. 获取 PostgreSQL 连接配置
        console.log('\n[2] 解析 PostgreSQL 连接配置...');
        const dbConfig = config.databases.postgresql;
        const connectionConfig = typeof dbConfig.connection === 'string'
            ? parse(dbConfig.connection)
            : dbConfig.connection;
        
        results.tests.push({
            test: '解析连接配置',
            status: 'PASS',
            host: connectionConfig.host,
            port: connectionConfig.port,
            database: connectionConfig.database
        });
        console.log('✓ 连接配置解析成功');
        console.log(`  主机: ${connectionConfig.host}`);
        console.log(`  端口: ${connectionConfig.port}`);
        console.log(`  数据库: ${connectionConfig.database}`);

        // 3. 基本连接测试（不使用连接池配置）
        console.log('\n[3] 测试基本数据库连接...');
        const basicPool = new Pool(connectionConfig);
        const startTime = Date.now();
        
        try {
            const client = await basicPool.connect();
            const connectTime = Date.now() - startTime;
            
            const queryStart = Date.now();
            const result = await client.query('SELECT 1 as test');
            const queryTime = Date.now() - queryStart;
            
            client.release();
            await basicPool.end();
            
            results.tests.push({
                test: '基本连接测试',
                status: 'PASS',
                connectTime: connectTime,
                queryTime: queryTime
            });
            console.log(`✓ 基本连接测试成功`);
            console.log(`  连接耗时: ${connectTime}ms`);
            console.log(`  查询耗时: ${queryTime}ms`);
        } catch (error) {
            results.tests.push({
                test: '基本连接测试',
                status: 'FAIL',
                error: error.message
            });
            console.error('✗ 基本连接测试失败:', error.message);
            return results;
        }

        // 4. 测试带超时配置的连接池
        console.log('\n[4] 测试带超时配置的连接池...');
        const poolConfig = {
            ...connectionConfig,
            max: 20,
            connectionTimeoutMillis: 5000,
            idleTimeoutMillis: 30000,
            query_timeout: 30000
        };
        
        const configuredPool = new Pool(poolConfig);
        
        try {
            const client = await configuredPool.connect();
            await client.query('SELECT 1');
            client.release();
            
            results.tests.push({
                test: '配置连接池测试',
                status: 'PASS',
                maxConnections: poolConfig.max,
                connectionTimeout: poolConfig.connectionTimeoutMillis
            });
            console.log('✓ 配置连接池测试成功');
            console.log(`  最大连接数: ${poolConfig.max}`);
            console.log(`  连接超时: ${poolConfig.connectionTimeoutMillis}ms`);
            
            // 清理连接池
            await configuredPool.end();
        } catch (error) {
            results.tests.push({
                test: '配置连接池测试',
                status: 'FAIL',
                error: error.message
            });
            console.error('✗ 配置连接池测试失败:', error.message);
        }

        // 5. 测试查询超时
        console.log('\n[5] 测试查询超时设置...');
        const timeoutPool = new Pool(connectionConfig);
        
        try {
            const client = await timeoutPool.connect();
            // 设置语句超时
            await client.query('SET statement_timeout = 1000'); // 1秒超时
            
            // 测试正常查询
            const normalStart = Date.now();
            await client.query('SELECT 1');
            const normalTime = Date.now() - normalStart;
            
            console.log(`✓ 正常查询完成，耗时: ${normalTime}ms`);
            
            // 测试超时查询（使用 pg_sleep）
            try {
                await client.query('SELECT pg_sleep(2)'); // 睡眠2秒，应该超时
                results.tests.push({
                    test: '查询超时测试',
                    status: 'FAIL',
                    error: '超时机制未生效'
                });
                console.error('✗ 查询超时测试失败：超时机制未生效');
            } catch (timeoutError) {
                if (timeoutError.message.includes('timeout')) {
                    results.tests.push({
                        test: '查询超时测试',
                        status: 'PASS',
                        message: '超时机制正常工作'
                    });
                    console.log('✓ 查询超时测试成功：超时机制正常工作');
                } else {
                    throw timeoutError;
                }
            }
            
            client.release();
            await timeoutPool.end();
        } catch (error) {
            results.tests.push({
                test: '查询超时测试',
                status: 'FAIL',
                error: error.message
            });
            console.error('✗ 查询超时测试失败:', error.message);
        }

        // 6. 测试事务操作
        console.log('\n[6] 测试事务操作...');
        const transactionPool = new Pool(connectionConfig);
        
        try {
            const client = await transactionPool.connect();
            
            // 开始事务
            const txStart = Date.now();
            await client.query('BEGIN');
            
            // 执行查询
            await client.query('SELECT 1');
            
            // 提交事务
            await client.query('COMMIT');
            const txTime = Date.now() - txStart;
            
            client.release();
            await transactionPool.end();
            
            results.tests.push({
                test: '事务操作测试',
                status: 'PASS',
                transactionTime: txTime
            });
            console.log(`✓ 事务操作测试成功，耗时: ${txTime}ms`);
        } catch (error) {
            results.tests.push({
                test: '事务操作测试',
                status: 'FAIL',
                error: error.message
            });
            console.error('✗ 事务操作测试失败:', error.message);
        }

    } catch (error) {
        console.error('\n发生未预期的错误:', error);
        results.error = error.message;
    }

    // 保存测试结果
    const resultPath = path.join(__dirname, 'test-1-postgresql-connection-results.json');
    fs.writeFileSync(resultPath, JSON.stringify(results, null, 2));
    console.log(`\n测试结果已保存到: ${resultPath}`);

    // 统计结果
    const passed = results.tests.filter(t => t.status === 'PASS').length;
    const failed = results.tests.filter(t => t.status === 'FAIL').length;
    console.log(`\n测试总结: ${passed} 通过, ${failed} 失败`);
    
    return results;
}

// 执行测试
runTests().catch(console.error);