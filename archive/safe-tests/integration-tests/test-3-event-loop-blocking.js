#!/usr/bin/env node
/**
 * 测试 3：事件循环阻塞验证
 * 目标：验证长时间运行的 saveAllLoadData 是否会阻塞 HTTP 服务
 * 方式：创建简单的 HTTP 服务器并模拟长时间操作
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('='.repeat(60));
console.log('事件循环阻塞验证测试');
console.log('='.repeat(60));
console.log('测试时间:', new Date().toISOString());
console.log('');

async function runTests() {
    const results = {
        timestamp: new Date().toISOString(),
        tests: []
    };

    try {
        // 1. 创建简单的 HTTP 服务器
        console.log('[1] 创建 HTTP 服务器...');
        
        let requestCount = 0;
        let blockingInProgress = false;
        
        const server = http.createServer((req, res) => {
            requestCount++;
            const requestTime = new Date().toISOString();
            console.log(`  收到 HTTP 请求 #${requestCount} at ${requestTime}`);
            
            res.writeHead(200, { 'Content-Type': 'text/plain' });
            res.end(`OK - Request #${requestCount} at ${requestTime}\n`);
        });
        
        // 启动服务器
        await new Promise((resolve) => {
            server.listen(15555, () => {
                console.log('✓ HTTP 服务器启动在端口 15555');
                resolve();
            });
        });
        
        results.tests.push({
            test: 'HTTP 服务器启动',
            status: 'PASS',
            port: 15555
        });

        // 2. 测试正常情况下的 HTTP 响应
        console.log('\n[2] 测试正常 HTTP 响应...');
        
        const normalStart = Date.now();
        const response1 = await makeHttpRequest('http://localhost:15555/test1');
        const normalTime = Date.now() - normalStart;
        
        results.tests.push({
            test: '正常 HTTP 响应',
            status: response1 ? 'PASS' : 'FAIL',
            responseTime: normalTime
        });
        console.log(`✓ 正常响应时间: ${normalTime}ms`);

        // 3. 模拟 saveAllLoadData 长时间操作
        console.log('\n[3] 开始模拟长时间数据库操作...');
        
        // 设置类似的定时器（4秒）
        const archiveInterval = 4000;
        let saveAllLoadDataInProgress = false;
        let executionCount = 0;
        
        const simulateSaveAllLoadData = async () => {
            if (saveAllLoadDataInProgress) {
                console.log('[模拟] 上次负载数据保存仍在进行中，跳过本次执行');
                return;
            }
            
            saveAllLoadDataInProgress = true;
            executionCount++;
            const startTime = Date.now();
            console.log(`[模拟] 开始执行 saveAllLoadData #${executionCount}...`);
            
            try {
                // 模拟处理 33 个服务器，每个需要 540ms
                for (let i = 0; i < 33; i++) {
                    // 模拟数据库操作（使用 setTimeout 包装的 Promise）
                    await new Promise(resolve => setTimeout(resolve, 540));
                    
                    // 每处理 5 个服务器打印进度
                    if ((i + 1) % 5 === 0) {
                        const elapsed = Date.now() - startTime;
                        console.log(`  处理进度: ${i + 1}/33 服务器，已用时 ${elapsed}ms`);
                    }
                }
                
                const totalTime = Date.now() - startTime;
                console.log(`[模拟] saveAllLoadData #${executionCount} 完成，总耗时: ${totalTime}ms`);
            } finally {
                saveAllLoadDataInProgress = false;
            }
        };
        
        // 启动定时器
        const timer = setInterval(simulateSaveAllLoadData, archiveInterval);
        
        // 4. 在长时间操作期间测试 HTTP 响应
        console.log('\n[4] 等待 2 秒后测试 HTTP 响应...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log('  尝试发送 HTTP 请求（预期会延迟）...');
        const blockedStart = Date.now();
        const response2 = await makeHttpRequest('http://localhost:15555/test2');
        const blockedTime = Date.now() - blockedStart;
        
        results.tests.push({
            test: '阻塞期间 HTTP 响应',
            status: response2 ? 'PASS' : 'FAIL',
            responseTime: blockedTime,
            isBlocked: blockedTime > 1000
        });
        console.log(`  阻塞期间响应时间: ${blockedTime}ms ${blockedTime > 1000 ? '(检测到阻塞!)' : ''}`);
        
        // 5. 等待一个完整周期并收集统计
        console.log('\n[5] 等待 25 秒收集统计数据...');
        await new Promise(resolve => setTimeout(resolve, 25000));
        
        // 停止定时器
        clearInterval(timer);
        
        // 发送最后一个请求测试恢复情况
        const recoveryStart = Date.now();
        const response3 = await makeHttpRequest('http://localhost:15555/test3');
        const recoveryTime = Date.now() - recoveryStart;
        
        results.tests.push({
            test: '恢复后 HTTP 响应',
            status: response3 ? 'PASS' : 'FAIL',
            responseTime: recoveryTime
        });
        
        // 关闭服务器
        await new Promise((resolve) => {
            server.close(() => {
                console.log('\n✓ HTTP 服务器已关闭');
                resolve();
            });
        });
        
        // 统计结果
        results.summary = {
            totalRequests: requestCount,
            simulatedExecutions: executionCount,
            expectedExecutions: Math.floor(25000 / archiveInterval),
            blockedRatio: (executionCount / Math.floor(25000 / archiveInterval) * 100).toFixed(2) + '%'
        };
        
        console.log('\n测试总结:');
        console.log(`  HTTP 请求总数: ${requestCount}`);
        console.log(`  模拟执行次数: ${executionCount}`);
        console.log(`  预期执行次数: ${Math.floor(25000 / archiveInterval)}`);
        console.log(`  阻塞率: ${results.summary.blockedRatio}`);
        
    } catch (error) {
        console.error('\n发生未预期的错误:', error);
        results.error = error.message;
    }

    // 保存测试结果
    const resultPath = path.join(__dirname, 'test-3-event-loop-blocking-results.json');
    fs.writeFileSync(resultPath, JSON.stringify(results, null, 2));
    console.log(`\n测试结果已保存到: ${resultPath}`);

    return results;
}

// HTTP 请求辅助函数
function makeHttpRequest(url) {
    return new Promise((resolve) => {
        const startTime = Date.now();
        http.get(url, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                const responseTime = Date.now() - startTime;
                resolve({ data, responseTime });
            });
        }).on('error', (error) => {
            console.error('  HTTP 请求失败:', error.message);
            resolve(null);
        });
    });
}

// 执行测试
runTests().catch(console.error);