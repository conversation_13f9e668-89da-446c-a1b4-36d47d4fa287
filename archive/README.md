# DStatus 项目测试文件归档

## 📋 概述
本目录包含 DStatus 项目的测试文件归档，按照安全级别和文件类型进行组织管理。

## 🏗️ 目录结构
```
archive/
├── sensitive-tests/          # ⚠️  敏感测试文件（不提交到GitHub）
│   ├── gemini-api-tests/     # 包含真实Gemini API密钥的测试文件
│   └── database-tests/       # 包含真实数据库连接信息的测试文件
├── safe-tests/               # ✅ 安全测试文件（可提交到GitHub）
│   ├── unit-tests/           # 单元测试文件
│   ├── integration-tests/    # 集成测试文件（含脱敏模板）
│   ├── frontend-tests/       # 前端测试文件
│   └── build-tests/          # 构建测试文件（含脱敏模板）
└── README.md                 # 本文档
```

## 🔒 安全策略

### 敏感文件处理
- **敏感文件位置**: `sensitive-tests/` 目录
- **访问控制**: 已添加到 `.gitignore`，仅本地保留
- **包含内容**: 
  - 17个包含真实Gemini API密钥的文件
  - 7个包含真实数据库连接信息的文件
- **安全措施**: 绝不提交到版本控制系统

### 安全文件管理
- **安全文件位置**: `safe-tests/` 目录
- **访问控制**: 可以安全地提交到GitHub
- **脱敏处理**: 为敏感测试创建了模板版本
- **分类管理**: 按测试类型组织文件结构

## 📊 归档统计

### 敏感测试文件 (sensitive-tests/)
```
gemini-api-tests/ (17个文件)
├── Python脚本: 16个
└── JavaScript模块: 1个

database-tests/ (7个文件)
├── Shell脚本: 2个
├── JavaScript文件: 4个
└── Markdown文档: 1个
```

### 安全测试文件 (safe-tests/)
```
unit-tests/ (4个文件)
├── 测试配置: test-config.js
├── 测试工具: test-helpers.js
├── 测试用例: test-cases.md
└── 报告模板: test-report-template.md

integration-tests/ (5个文件)
├── TDD测试: 4个 .js文件
└── 脱敏模板: 1个模板文件

frontend-tests/ (11个文件)
├── HTML测试页面: 10个
└── 对齐测试目录: alignment-test/

build-tests/ (4个文件)
├── 构建脚本: test-build.sh
├── API测试: test-license-api.js
├── 上传测试: test-upload.sh
└── 脱敏模板: test_gemini_api_template.py
```

## 🛠️ 使用指南

### 对于开发者
1. **使用安全测试**: 直接使用 `safe-tests/` 目录下的文件
2. **运行模板测试**: 参考 `safe-tests/TEMPLATE_USAGE_GUIDE.md`
3. **配置环境变量**: 设置必要的API密钥和数据库连接
4. **避免敏感信息**: 不要在代码中硬编码敏感信息

### 对于新团队成员
1. **了解结构**: 阅读本README和模板使用指南
2. **设置环境**: 根据模板文件配置开发环境
3. **运行测试**: 从安全测试文件开始熟悉项目
4. **遵循规范**: 确保新增测试文件遵循安全准则

## 📝 文件迁移记录

### 迁移日期
2025-07-31

### 迁移范围
- **源位置**: 项目根目录、scripts/、modules/、database/、tdd-workspace/ 等
- **目标位置**: archive/ 目录下的分类子目录
- **处理方式**: 移动敏感文件，保留安全文件，创建脱敏模板

### 迁移验证
- ✅ 敏感文件已从原位置移除
- ✅ 敏感文件已添加到 `.gitignore`
- ✅ 安全文件已按类型分类存储
- ✅ 脱敏模板已创建并测试
- ✅ 使用指南已编写完成

## 🔄 维护建议

### 定期审查
- 每月检查新增测试文件的敏感信息
- 更新脱敏模板以反映最新功能
- 验证 `.gitignore` 规则的有效性

### 新文件处理
1. **安全检查**: 检查是否包含API密钥、密码等敏感信息
2. **分类存储**: 根据文件类型和安全级别选择存储位置
3. **模板创建**: 为敏感测试文件创建脱敏版本
4. **文档更新**: 更新相关使用指南和说明文档

## ⚠️ 重要提醒

### 绝对禁止
- ❌ 将 `sensitive-tests/` 目录提交到版本控制
- ❌ 在模板文件中硬编码真实凭据
- ❌ 在公共场所分享敏感测试文件
- ❌ 绕过安全检查流程

### 建议实践
- ✅ 使用环境变量管理敏感配置
- ✅ 定期更新和轮换API密钥
- ✅ 为新功能创建相应的测试模板
- ✅ 保持归档文档的时效性

## 📞 支持与反馈
如有关于测试文件归档的问题或建议，请通过以下方式联系：
- 创建项目Issue讨论
- 更新相关文档说明
- 提交改进建议

---

**归档创建日期**: 2025-07-31  
**最后更新**: 2025-07-31  
**维护状态**: ✅ 活跃维护