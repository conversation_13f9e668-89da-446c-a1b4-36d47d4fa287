#!/bin/bash

# DStatus 通用文件上传脚本
# 用于将文件上传到 down.vps.mom 的指定目录
# 
# 用法:
#   ./upload-to-downloads.sh <本地文件> <远程路径>
#   ./upload-to-downloads.sh <本地文件> <远程路径> [--overwrite]
#
# 示例:
#   ./upload-to-downloads.sh install-dstatus.sh scripts/install-dstatus-agent.sh
#   ./upload-to-downloads.sh ../static/js/install-dstatus.sh scripts/install-dstatus-agent.sh --overwrite

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "🚀 DStatus 通用文件上传工具"
echo "==========================="

# 参数检查
if [ $# -lt 2 ]; then
    echo -e "${RED}❌ 参数不足${NC}"
    echo ""
    echo "用法:"
    echo "  $0 <本地文件> <远程路径> [--overwrite]"
    echo ""
    echo "示例:"
    echo "  $0 install-dstatus.sh scripts/install-dstatus-agent.sh"
    echo "  $0 /path/to/file.sh docs/readme.txt --overwrite"
    echo ""
    echo "远程路径说明:"
    echo "  - scripts/filename   上传到脚本目录"
    echo "  - downloads/filename 上传到下载目录"
    echo "  - docs/filename      上传到文档目录"
    exit 1
fi

# 获取参数
LOCAL_FILE="$1"
REMOTE_PATH="$2"
OVERWRITE=false

# 检查是否有 --overwrite 参数
if [ "${3:-}" = "--overwrite" ]; then
    OVERWRITE=true
fi

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
UPLOAD_URL="https://down.vps.mom/upload-to-vps-v2.php"
API_KEY="${DSTATUS_API_KEY:-}"

# 检查API密钥
if [ -z "$API_KEY" ]; then
    echo -e "${RED}❌ 请设置 DSTATUS_API_KEY 环境变量${NC}"
    echo ""
    echo "使用方法:"
    echo "  export DSTATUS_API_KEY=\"your-api-key\""
    echo "  $0 $@"
    exit 1
fi

# 检查本地文件
if [ ! -f "$LOCAL_FILE" ]; then
    echo -e "${RED}❌ 本地文件不存在: $LOCAL_FILE${NC}"
    exit 1
fi

# 获取文件信息
LOCAL_FILENAME=$(basename "$LOCAL_FILE")
LOCAL_FILESIZE=$(du -h "$LOCAL_FILE" | cut -f1)

# 显示上传信息
echo -e "${BLUE}📋 上传信息:${NC}"
echo "   本地文件: $LOCAL_FILE ($LOCAL_FILESIZE)"
echo "   远程路径: $REMOTE_PATH"
echo "   目标URL: https://down.vps.mom/$REMOTE_PATH"
echo "   覆盖模式: $OVERWRITE"
echo "   API密钥: ${API_KEY:0:8}..."
echo ""

# 确认上传
if [ "$OVERWRITE" = false ]; then
    echo -e "${YELLOW}⚠️  注意: 如果远程文件已存在，上传将失败${NC}"
    echo "   使用 --overwrite 参数强制覆盖"
    echo ""
fi

# 执行上传
echo -e "${BLUE}📤 开始上传...${NC}"

# 使用curl上传文件
RESPONSE=$(curl -s -X POST \
    -H "X-API-Key: $API_KEY" \
    -F "file=@$LOCAL_FILE" \
    -F "target_path=$REMOTE_PATH" \
    -F "overwrite=$OVERWRITE" \
    "$UPLOAD_URL" 2>&1) || {
    echo -e "${RED}❌ 上传请求失败${NC}"
    echo "错误信息: $RESPONSE"
    exit 1
}

# 检查响应
if echo "$RESPONSE" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 上传成功！${NC}"
    
    # 尝试解析响应信息
    if command -v jq &> /dev/null; then
        FILE_SIZE=$(echo "$RESPONSE" | jq -r '.data.size // empty' 2>/dev/null)
        FILE_SHA256=$(echo "$RESPONSE" | jq -r '.data.sha256 // empty' 2>/dev/null)
        BACKUP_FILE=$(echo "$RESPONSE" | jq -r '.data.backup_file // empty' 2>/dev/null)
        
        [ -n "$FILE_SIZE" ] && echo "   文件大小: $FILE_SIZE 字节"
        [ -n "$FILE_SHA256" ] && echo "   SHA256: ${FILE_SHA256:0:16}..."
        [ -n "$BACKUP_FILE" ] && echo "   备份文件: $BACKUP_FILE"
    fi
    
    echo ""
    echo -e "${GREEN}🔗 访问链接:${NC}"
    echo "   https://down.vps.mom/$REMOTE_PATH"
    echo ""
    
    # 如果是脚本文件，显示一键执行命令
    if [[ "$REMOTE_PATH" == scripts/*.sh ]]; then
        echo -e "${GREEN}🚀 一键执行命令:${NC}"
        echo "   curl -fsSL https://down.vps.mom/$REMOTE_PATH | bash"
        echo ""
    fi
    
else
    echo -e "${RED}❌ 上传失败${NC}"
    
    # 尝试解析错误信息
    if command -v jq &> /dev/null; then
        ERROR_MSG=$(echo "$RESPONSE" | jq -r '.error // empty' 2>/dev/null)
        if [ -n "$ERROR_MSG" ]; then
            echo "错误信息: $ERROR_MSG"
        else
            echo "响应内容: $RESPONSE"
        fi
    else
        echo "响应内容: $RESPONSE"
    fi
    
    exit 1
fi

# 验证上传
echo -e "${BLUE}🔍 验证上传...${NC}"
VERIFY_URL="https://down.vps.mom/$REMOTE_PATH"
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$VERIFY_URL")

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ 文件已成功上传并可访问${NC}"
else
    echo -e "${YELLOW}⚠️  文件已上传但无法验证访问 (HTTP $HTTP_CODE)${NC}"
    echo "   可能需要等待几秒钟或检查文件权限"
fi

echo ""
echo -e "${GREEN}✨ 上传完成！${NC}"