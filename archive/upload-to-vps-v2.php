<?php
/**
 * DStatus 通用文件上传脚本 V2
 * 支持上传文件到指定子目录
 * 
 * 用法:
 * - POST 请求上传文件
 * - 参数 target_path: 目标路径（如 scripts/install-dstatus-agent.sh）
 * - 参数 overwrite: true/false 是否覆盖已存在的文件
 */

// 配置
$config = [
    'base_dir' => '/www/wwwroot/down.vps.mom/',  // 基础目录
    'allowed_dirs' => [        // 允许上传的子目录
        'downloads',
        'scripts', 
        'docs',
        'install'
    ],
    'allowed_extensions' => [  // 允许的文件扩展名
        '.sh', '.tar.gz', '.gz', '.zip', '.txt', '.md', '.pdf'
    ],
    'max_file_size' => 100 * 1024 * 1024, // 100MB
    'api_key' => 'H*7F6z3n2b4CXeDMPTaj_CEm', // API密钥
];

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// 日志函数
function writeLog($message) {
    $logFile = '/www/wwwroot/down.vps.mom/dstatus-upload.log';
    $timestamp = date('Y-m-d H:i:s');
    @file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

// 验证API密钥
function validateApiKey($config) {
    $apiKey = $_SERVER['HTTP_X_API_KEY'] ?? $_POST['api_key'] ?? '';
    
    if (empty($apiKey) || $apiKey !== $config['api_key']) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid API key'
        ]);
        writeLog("Unauthorized access attempt from " . $_SERVER['REMOTE_ADDR']);
        exit;
    }
}

// 验证目标路径
function validateTargetPath($targetPath, $config) {
    // 移除开头的斜杠
    $targetPath = ltrim($targetPath, '/');
    
    // 防止路径遍历攻击
    if (strpos($targetPath, '..') !== false || strpos($targetPath, '//') !== false) {
        throw new Exception('Invalid target path');
    }
    
    // 获取目录部分
    $pathParts = explode('/', $targetPath);
    if (count($pathParts) < 2) {
        throw new Exception('Target path must include directory and filename');
    }
    
    $targetDir = $pathParts[0];
    
    // 验证目录是否在允许列表中
    if (!in_array($targetDir, $config['allowed_dirs'])) {
        throw new Exception('Directory not allowed: ' . $targetDir);
    }
    
    // 验证文件扩展名
    $extension = '';
    foreach ($config['allowed_extensions'] as $ext) {
        if (substr($targetPath, -strlen($ext)) === $ext) {
            $extension = $ext;
            break;
        }
    }
    
    if (empty($extension)) {
        throw new Exception('File extension not allowed');
    }
    
    return $targetPath;
}

// 备份现有文件
function backupExistingFile($targetPath) {
    if (file_exists($targetPath)) {
        $backupPath = $targetPath . '.backup.' . date('Y-m-d-H-i-s');
        if (rename($targetPath, $backupPath)) {
            writeLog("Backed up existing file to: $backupPath");
            return $backupPath;
        }
    }
    return null;
}

// 状态查询接口 (处理GET请求)
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['status'])) {
    try {
        validateApiKey($config);

        $baseDir = rtrim($config['base_dir'], '/');
        $files = [];
        
        // 扫描所有允许的目录
        foreach ($config['allowed_dirs'] as $dir) {
            $dirPath = $baseDir . '/' . $dir;
            if (is_dir($dirPath)) {
                $dirFiles = scandir($dirPath);
                foreach ($dirFiles as $file) {
                    if ($file === '.' || $file === '..' || strpos($file, '.backup.') !== false) {
                        continue;
                    }
                    
                    $filePath = $dirPath . '/' . $file;
                    if (is_file($filePath)) {
                        // 检查文件扩展名
                        $hasValidExt = false;
                        foreach ($config['allowed_extensions'] as $ext) {
                            if (substr($file, -strlen($ext)) === $ext) {
                                $hasValidExt = true;
                                break;
                            }
                        }
                        
                        if ($hasValidExt) {
                            $files[] = [
                                'path' => $dir . '/' . $file,
                                'filename' => $file,
                                'directory' => $dir,
                                'size' => filesize($filePath),
                                'modified' => date('Y-m-d H:i:s', filemtime($filePath)),
                                'url' => 'https://down.vps.mom/' . $dir . '/' . $file
                            ];
                        }
                    }
                }
            }
        }

        echo json_encode([
            'success' => true,
            'files' => $files,
            'allowed_dirs' => $config['allowed_dirs'],
            'allowed_extensions' => $config['allowed_extensions']
        ]);
        exit;

    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
        exit;
    }
}

// 主处理逻辑 (处理POST请求)
try {
    // 只允许 POST 请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        throw new Exception('Method not allowed');
    }
    
    // 验证API密钥
    validateApiKey($config);
    
    // 检查是否有文件上传
    if (!isset($_FILES['file']) || empty($_FILES['file']['name'])) {
        throw new Exception('No file uploaded');
    }
    
    // 检查目标路径参数
    if (!isset($_POST['target_path']) || empty($_POST['target_path'])) {
        throw new Exception('Target path not specified');
    }
    
    $file = $_FILES['file'];
    $targetPath = $_POST['target_path'];
    $overwrite = isset($_POST['overwrite']) && $_POST['overwrite'] === 'true';
    
    // 验证文件上传
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload error: ' . $file['error']);
    }
    
    // 检查文件大小
    if ($file['size'] > $config['max_file_size']) {
        throw new Exception('File too large. Max size: ' . ($config['max_file_size'] / 1024 / 1024) . 'MB');
    }
    
    // 验证目标路径
    $targetPath = validateTargetPath($targetPath, $config);
    
    // 构建完整目标路径
    $fullTargetPath = rtrim($config['base_dir'], '/') . '/' . $targetPath;
    
    // 检查文件是否已存在
    if (file_exists($fullTargetPath) && !$overwrite) {
        throw new Exception('File already exists. Use overwrite=true to replace.');
    }
    
    // 确保目标目录存在
    $targetDir = dirname($fullTargetPath);
    if (!is_dir($targetDir)) {
        if (!mkdir($targetDir, 0755, true)) {
            throw new Exception('Failed to create target directory');
        }
    }
    
    // 备份现有文件（如果需要覆盖）
    $backupPath = null;
    if ($overwrite && file_exists($fullTargetPath)) {
        $backupPath = backupExistingFile($fullTargetPath);
    }
    
    // 移动上传的文件
    if (!move_uploaded_file($file['tmp_name'], $fullTargetPath)) {
        throw new Exception('Failed to move uploaded file');
    }
    
    // 设置文件权限
    chmod($fullTargetPath, 0644);
    
    // 如果是脚本文件，设置可执行权限
    if (substr($targetPath, -3) === '.sh') {
        chmod($fullTargetPath, 0755);
    }
    
    // 获取文件信息
    $fileSize = filesize($fullTargetPath);
    $fileHash = hash_file('sha256', $fullTargetPath);
    
    // 记录日志
    writeLog("Successfully uploaded: $targetPath ($fileSize bytes, SHA256: $fileHash) from " . $_SERVER['REMOTE_ADDR']);
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => 'File uploaded successfully',
        'data' => [
            'path' => $targetPath,
            'filename' => basename($targetPath),
            'directory' => dirname($targetPath),
            'size' => $fileSize,
            'sha256' => $fileHash,
            'upload_time' => date('Y-m-d H:i:s'),
            'backup_file' => $backupPath ? basename($backupPath) : null,
            'url' => 'https://down.vps.mom/' . $targetPath
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
    writeLog("Upload failed: " . $e->getMessage() . " from " . $_SERVER['REMOTE_ADDR']);
}
?>