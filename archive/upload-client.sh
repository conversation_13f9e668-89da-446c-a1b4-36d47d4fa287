#!/bin/bash

# DStatus 部署包上传客户端
# 用于将构建好的部署包上传到 down.vps.mom

set -e

echo "🚀 DStatus 部署包上传工具"
echo "=========================="

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_DIR/dist-docker"

# 服务器配置
UPLOAD_URL="https://down.vps.mom/upload-to-vps.php"
API_KEY="${DSTATUS_API_KEY:-your-secure-api-key-here}"

# 检查API密钥
if [ "$API_KEY" = "your-secure-api-key-here" ]; then
    echo "❌ 请设置 DSTATUS_API_KEY 环境变量"
    echo ""
    echo "使用方法:"
    echo "  export DSTATUS_API_KEY=\"your-actual-api-key\""
    echo "  $0"
    exit 1
fi

# 显示配置信息
echo "📋 配置信息:"
echo "   上传URL: $UPLOAD_URL"
echo "   构建目录: $BUILD_DIR"
echo "   API密钥: ${API_KEY:0:8}..."
echo ""

# 上传文件函数
upload_file() {
    local file_path="$1"
    local target_name="$2"
    
    if [ ! -f "$file_path" ]; then
        echo "❌ 文件不存在: $file_path"
        return 1
    fi
    
    local file_size=$(du -h "$file_path" | cut -f1)
    echo "📤 上传文件: $(basename "$file_path") ($file_size)"
    
    # 使用curl上传文件
    local response=$(curl -s -X POST \
        -H "X-API-Key: $API_KEY" \
        -F "file=@$file_path" \
        -F "target_filename=$target_name" \
        "$UPLOAD_URL")
    
    # 检查响应
    if echo "$response" | jq -e '.success' >/dev/null 2>&1; then
        local uploaded_size=$(echo "$response" | jq -r '.data.size')
        local sha256=$(echo "$response" | jq -r '.data.sha256')
        echo "  ✅ 上传成功"
        echo "  📏 大小: $uploaded_size 字节"
        echo "  🔐 SHA256: ${sha256:0:16}..."
        
        # 检查备份信息
        local backup_file=$(echo "$response" | jq -r '.data.backup_file // empty')
        if [ -n "$backup_file" ]; then
            echo "  💾 已备份原文件: $backup_file"
        fi
        
        return 0
    else
        local error=$(echo "$response" | jq -r '.error // "Unknown error"')
        echo "  ❌ 上传失败: $error"
        return 1
    fi
}

# 检查服务器状态
check_server_status() {
    echo "🔍 检查服务器状态..."
    
    local response=$(curl -s -X GET \
        -H "X-API-Key: $API_KEY" \
        "$UPLOAD_URL?status=1")
    
    if echo "$response" | jq -e '.success' >/dev/null 2>&1; then
        echo "✅ 服务器连接正常"
        echo ""
        echo "📋 当前文件列表:"
        echo "$response" | jq -r '.files[] | "  📁 \(.filename) (\(.size) bytes, 修改时间: \(.modified))"'
        echo ""
        return 0
    else
        echo "❌ 服务器连接失败"
        echo "响应: $response"
        return 1
    fi
}

# 主函数
main() {
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        echo "❌ 需要安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        echo "❌ 需要安装 jq"
        exit 1
    fi
    
    # 检查服务器状态
    if ! check_server_status; then
        echo "❌ 无法连接到服务器，请检查网络和API密钥"
        exit 1
    fi
    
    # 查找要上传的文件
    local docker_package=""
    local complete_package=""
    local binary_file=""
    
    # 查找Docker部署包
    if [ -f "$BUILD_DIR/dstatus-docker.tar.gz" ]; then
        docker_package="$BUILD_DIR/dstatus-docker.tar.gz"
    else
        # 查找版本化的Docker包
        docker_package=$(find "$PROJECT_DIR" -name "dstatus-docker-v*.tar.gz" | head -1)
    fi
    
    # 查找完整部署包
    complete_package=$(find "$PROJECT_DIR" -name "dstatus-complete-v*.tar.gz" | head -1)
    
    # 查找二进制文件
    if [ -f "$PROJECT_DIR/dstatus-linux-x64" ]; then
        binary_file="$PROJECT_DIR/dstatus-linux-x64"
    fi
    
    # 显示找到的文件
    echo "🔍 找到的文件:"
    [ -n "$docker_package" ] && echo "  📦 Docker部署包: $(basename "$docker_package")"
    [ -n "$complete_package" ] && echo "  📦 完整部署包: $(basename "$complete_package")"
    [ -n "$binary_file" ] && echo "  📦 二进制文件: $(basename "$binary_file")"
    echo ""
    
    # 上传文件
    local upload_count=0
    local success_count=0
    
    if [ -n "$docker_package" ]; then
        upload_count=$((upload_count + 1))
        if upload_file "$docker_package" "dstatus-docker.tar.gz"; then
            success_count=$((success_count + 1))
        fi
        echo ""
    fi
    
    if [ -n "$complete_package" ]; then
        upload_count=$((upload_count + 1))
        if upload_file "$complete_package" "dstatus-complete.tar.gz"; then
            success_count=$((success_count + 1))
        fi
        echo ""
    fi
    
    if [ -n "$binary_file" ]; then
        upload_count=$((upload_count + 1))
        if upload_file "$binary_file" "dstatus-linux-x64"; then
            success_count=$((success_count + 1))
        fi
        echo ""
    fi
    
    # 显示结果
    echo "📊 上传结果:"
    echo "   总文件数: $upload_count"
    echo "   成功上传: $success_count"
    echo "   失败数量: $((upload_count - success_count))"
    
    if [ $success_count -eq $upload_count ] && [ $upload_count -gt 0 ]; then
        echo ""
        echo "🎉 所有文件上传成功！"
        echo ""
        echo "📋 验证链接:"
        echo "   Docker部署包: https://down.vps.mom/downloads/dstatus-docker.tar.gz"
        echo "   完整部署包: https://down.vps.mom/downloads/dstatus-complete.tar.gz"
        echo "   二进制文件: https://down.vps.mom/downloads/dstatus-linux-x64"
        echo ""
        echo "🔗 一键安装命令:"
        echo "   curl -fsSL https://client.vps.mom/install.sh | bash -s -- --license-key=\"YOUR_LICENSE_KEY\""
    else
        echo ""
        echo "⚠️  部分文件上传失败，请检查错误信息"
        exit 1
    fi
}

# 运行主函数
main "$@"
