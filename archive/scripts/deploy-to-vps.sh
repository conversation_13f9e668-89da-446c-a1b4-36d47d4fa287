#!/bin/bash

# DStatus 部署到 down.vps.mom 脚本
# 用于将构建好的 Docker 部署包上传到下载服务器

set -e

echo "🚀 DStatus 部署到 down.vps.mom"
echo "=============================="

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_DIR/dist-docker"
PACKAGE_FILE="$BUILD_DIR/dstatus-docker.tar.gz"
VERSION=${1:-"latest"}

# 上传配置 (从环境变量获取)
UPLOAD_URL=${UPLOAD_URL:-""}
UPLOAD_TOKEN=${UPLOAD_TOKEN:-""}
UPLOAD_API_KEY=${UPLOAD_API_KEY:-""}

echo "📋 部署信息:"
echo "   版本: $VERSION"
echo "   包文件: $PACKAGE_FILE"
echo "   上传URL: ${UPLOAD_URL:-"未配置"}"
echo ""

# 检查包文件是否存在
if [ ! -f "$PACKAGE_FILE" ]; then
    echo "❌ 部署包不存在: $PACKAGE_FILE"
    echo ""
    echo "请先运行构建脚本:"
    echo "  ./scripts/build-docker-package.sh"
    exit 1
fi

echo "✅ 找到部署包: $(du -h "$PACKAGE_FILE" | cut -f1)"
echo ""

# 检查上传配置
if [ -z "$UPLOAD_URL" ]; then
    echo "⚠️  上传配置未设置"
    echo ""
    echo "请设置以下环境变量:"
    echo "  export UPLOAD_URL=\"https://down.vps.mom/api/upload\""
    echo "  export UPLOAD_TOKEN=\"your-upload-token\""
    echo "  export UPLOAD_API_KEY=\"your-api-key\""
    echo ""
    echo "或者手动上传文件:"
    echo "  文件: $PACKAGE_FILE"
    echo "  目标: https://down.vps.mom/downloads/dstatus-docker.tar.gz"
    echo ""
    exit 0
fi

# 验证包文件
echo "🔍 验证包文件..."
if tar -tzf "$PACKAGE_FILE" >/dev/null 2>&1; then
    echo "✅ 包文件格式正确"
    
    # 显示包内容摘要
    echo "📋 包内容摘要:"
    tar -tzf "$PACKAGE_FILE" | head -10
    if [ $(tar -tzf "$PACKAGE_FILE" | wc -l) -gt 10 ]; then
        echo "... 还有 $(($(tar -tzf "$PACKAGE_FILE" | wc -l) - 10)) 个文件"
    fi
else
    echo "❌ 包文件格式错误"
    exit 1
fi
echo ""

# 上传文件
echo "📤 上传到 down.vps.mom..."

# 方法1: 使用 curl 上传 (API 方式)
if command -v curl &> /dev/null; then
    echo "🔄 使用 curl 上传..."
    
    # 构建上传命令
    UPLOAD_CMD="curl -X POST"
    
    # 添加认证头
    if [ -n "$UPLOAD_TOKEN" ]; then
        UPLOAD_CMD="$UPLOAD_CMD -H \"Authorization: Bearer $UPLOAD_TOKEN\""
    fi
    
    if [ -n "$UPLOAD_API_KEY" ]; then
        UPLOAD_CMD="$UPLOAD_CMD -H \"X-API-Key: $UPLOAD_API_KEY\""
    fi
    
    # 添加文件和其他参数
    UPLOAD_CMD="$UPLOAD_CMD -F \"file=@$PACKAGE_FILE\""
    UPLOAD_CMD="$UPLOAD_CMD -F \"filename=dstatus-docker.tar.gz\""
    UPLOAD_CMD="$UPLOAD_CMD -F \"version=$VERSION\""
    UPLOAD_CMD="$UPLOAD_CMD -F \"overwrite=true\""
    UPLOAD_CMD="$UPLOAD_CMD \"$UPLOAD_URL\""
    
    echo "📤 执行上传..."
    echo "命令: $UPLOAD_CMD"
    echo ""
    
    # 执行上传 (这里需要根据实际API调整)
    # eval $UPLOAD_CMD
    
    echo "⚠️  上传功能待实现"
    echo "请联系管理员配置上传API接口"
    
else
    echo "❌ 未找到 curl 命令"
    exit 1
fi

echo ""
echo "📋 上传完成后的验证:"
echo "1. 检查下载链接: https://down.vps.mom/downloads/dstatus-docker.tar.gz"
echo "2. 测试一键安装: curl -fsSL https://client.vps.mom/install.sh | bash -s -- --license-key=\"test\""
echo ""

echo "✨ 部署脚本执行完成！"
echo ""
echo "📝 注意事项:"
echo "- 确保上传成功后更新 CDN 缓存"
echo "- 通知用户新版本已发布"
echo "- 更新文档中的版本号"
