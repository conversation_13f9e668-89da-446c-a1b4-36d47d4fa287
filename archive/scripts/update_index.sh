#!/bin/bash

# DStatus Project Index Update Script
# This script generates an updated project index

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "Updating DStatus project index..."
echo "Project root: $PROJECT_ROOT"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is required but not installed."
    exit 1
fi

# Run the Python script to generate index
python3 "$SCRIPT_DIR/generate_index.py" "$PROJECT_ROOT"

# Check if the command was successful
if [ $? -eq 0 ]; then
    echo "✅ Index updated successfully!"
    echo "Location: $PROJECT_ROOT/INDEX.md"
    
    # Show statistics
    echo ""
    echo "📊 Project Statistics:"
    grep -A 2 "## Statistics" "$PROJECT_ROOT/INDEX.md" | tail -n 2
else
    echo "❌ Failed to update index"
    exit 1
fi
