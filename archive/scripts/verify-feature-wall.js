#!/usr/bin/env node
/**
 * 验证网络质量功能墙是否正确初始化
 */

const fetch = require('node-fetch');
const cheerio = require('cheerio');

async function verifyFeatureWall() {
    const baseUrl = 'http://localhost:5555';
    
    console.log('========================================');
    console.log('网络质量功能墙验证脚本');
    console.log('========================================\n');
    
    try {
        // 1. 检查页面是否能够访问
        console.log('1. 检查 /network-quality 页面访问...');
        const response = await fetch(`${baseUrl}/network-quality`, {
            headers: {
                'Accept': 'text/html'
            },
            redirect: 'manual' // 不自动跟随重定向
        });
        
        console.log(`   状态码: ${response.status}`);
        console.log(`   Location: ${response.headers.get('location') || '无重定向'}`);
        
        if (response.status === 302 || response.status === 301) {
            console.log('   ❌ 页面被重定向，功能墙无法初始化');
            return false;
        }
        
        if (response.status !== 200) {
            console.log(`   ❌ 页面返回异常状态码: ${response.status}`);
            return false;
        }
        
        console.log('   ✅ 页面可以正常访问\n');
        
        // 2. 检查页面内容
        console.log('2. 检查页面内容...');
        const html = await response.text();
        const $ = cheerio.load(html);
        
        // 检查标题
        const title = $('title').text();
        console.log(`   页面标题: ${title}`);
        
        // 检查功能墙容器
        const featureWallContainer = $('#feature-wall-container').length > 0;
        console.log(`   功能墙容器存在: ${featureWallContainer ? '✅' : '❌'}`);
        
        // 检查脚本加载
        const scriptLoaded = $('script[src*="network-quality-feature-wall.js"]').length > 0;
        console.log(`   功能墙脚本已加载: ${scriptLoaded ? '✅' : '❌'}`);
        
        // 检查其他必要元素
        const elementsToCheck = [
            { selector: '#loading-container', name: '加载容器' },
            { selector: '#nodes-container', name: '节点容器' },
            { selector: '#empty-container', name: '空状态容器' },
            { selector: '.time-range-btn', name: '时间范围按钮' }
        ];
        
        console.log('\n3. 检查页面元素...');
        elementsToCheck.forEach(({ selector, name }) => {
            const exists = $(selector).length > 0;
            console.log(`   ${name}: ${exists ? '✅' : '❌'}`);
        });
        
        // 3. 检查 JavaScript 控制台输出（模拟）
        console.log('\n4. 功能墙初始化检查...');
        console.log('   请在浏览器中打开 http://localhost:5555/network-quality');
        console.log('   并查看控制台输出，应该看到以下日志：');
        console.log('   - [FeatureWall] DOMContentLoaded event fired');
        console.log('   - [FeatureWall] Is network quality page: true');
        console.log('   - [FeatureWall] Initializing NetworkQualityFeatureWall...');
        console.log('   - [FeatureWall] init() called');
        
        console.log('\n========================================');
        console.log('验证完成！');
        console.log('========================================');
        
        return true;
        
    } catch (error) {
        console.error('验证失败:', error.message);
        return false;
    }
}

// 执行验证
verifyFeatureWall().then(success => {
    process.exit(success ? 0 : 1);
});