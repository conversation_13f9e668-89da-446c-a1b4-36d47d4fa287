#!/usr/bin/env python3
"""
准备清理后的高质量文档集合用于 Gemini 分析
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

# 项目根目录
ROOT = Path(__file__).parent.parent

# 优先保留的核心文档列表
PRIORITY_DOCUMENTS = [
    # 项目概述
    'readme.md',
    'CORE_MEMORY.md',
    'plan.md',
    
    # License Server 核心文档
    'license-server/README.md',
    'license-server/USER_API_DOCUMENTATION.md',
    'license-server/plan/technical-architecture.md',
    'license-server/plan/api-design.md',
    
    # API 详细文档
    'license-server/backend/src/docs/LICENSE_BINDING_API.md',
    'license-server/backend/src/docs/USER_SESSION_API.md',
    
    # 前端架构
    'frontend/docs/architecture-design.md',
    
    # 部署和配置
    'docs/deployment_guide.md',
    'license-server/NETWORK_SETUP.md',
    
    # 计划文档（最新的）
    'license-server/plan/README.md',
    'license-server/plan/database-schema.md',
    
    # 业务文档
    'demo-license-management.md',
    '优惠码系统实现计划报告.md'
]

def collect_clean_documents():
    """收集清理后的文档"""
    documents = {}
    missing = []
    
    print("📚 收集核心文档...")
    
    for doc_path in PRIORITY_DOCUMENTS:
        full_path = ROOT / doc_path
        if full_path.exists():
            try:
                content = full_path.read_text(encoding='utf-8')
                documents[doc_path] = {
                    'content': content,
                    'size': len(content),
                    'size_kb': len(content) / 1024
                }
                print(f"✅ {doc_path} ({len(content)/1024:.1f}KB)")
            except Exception as e:
                print(f"❌ 读取失败 {doc_path}: {e}")
                missing.append(doc_path)
        else:
            missing.append(doc_path)
    
    if missing:
        print(f"\n⚠️  缺失的文档 ({len(missing)} 个):")
        for doc in missing:
            print(f"   - {doc}")
    
    return documents

def analyze_document_content(documents):
    """分析文档内容质量"""
    stats = {
        'total_size': sum(doc['size'] for doc in documents.values()),
        'total_documents': len(documents),
        'categories': {
            'api': [],
            'architecture': [],
            'deployment': [],
            'business': [],
            'overview': []
        }
    }
    
    # 分类文档
    for path, doc in documents.items():
        if 'api' in path.lower() or 'binding' in path.lower() or 'session' in path.lower():
            stats['categories']['api'].append(path)
        elif 'architecture' in path.lower() or 'design' in path.lower():
            stats['categories']['architecture'].append(path)
        elif 'deploy' in path.lower() or 'network' in path.lower() or 'setup' in path.lower():
            stats['categories']['deployment'].append(path)
        elif '优惠' in path or 'license-management' in path:
            stats['categories']['business'].append(path)
        else:
            stats['categories']['overview'].append(path)
    
    return stats

def prepare_documents_for_gemini(documents, max_total_size=500000):
    """准备发送给 Gemini 的文档内容"""
    # 按优先级排序（API和架构文档优先）
    priority_order = []
    
    # 1. 概述文档
    for path in ['readme.md', 'CORE_MEMORY.md', 'license-server/README.md']:
        if path in documents:
            priority_order.append(path)
    
    # 2. API 文档
    api_docs = [p for p in documents.keys() if 'api' in p.lower()]
    priority_order.extend(sorted(api_docs, key=lambda x: documents[x]['size'], reverse=True))
    
    # 3. 架构文档
    arch_docs = [p for p in documents.keys() if 'architecture' in p.lower() or 'design' in p.lower()]
    priority_order.extend([p for p in arch_docs if p not in priority_order])
    
    # 4. 其他文档
    remaining = [p for p in documents.keys() if p not in priority_order]
    priority_order.extend(remaining)
    
    # 构建文档内容
    included_docs = []
    total_size = 0
    
    for path in priority_order:
        if path in documents:
            doc = documents[path]
            if total_size + doc['size'] <= max_total_size:
                included_docs.append({
                    'path': path,
                    'content': doc['content'],
                    'size': doc['size']
                })
                total_size += doc['size']
            else:
                print(f"⚠️  跳过 {path} (超出大小限制)")
    
    return included_docs, total_size

def save_clean_document_set(included_docs):
    """保存清理后的文档集"""
    output_dir = ROOT / 'docs' / 'clean_documents'
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存文档列表
    manifest = {
        'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'total_documents': len(included_docs),
        'total_size_kb': sum(doc['size'] for doc in included_docs) / 1024,
        'documents': [
            {
                'path': doc['path'],
                'size_kb': doc['size'] / 1024
            }
            for doc in included_docs
        ]
    }
    
    manifest_file = output_dir / 'document_manifest.json'
    import json
    with open(manifest_file, 'w', encoding='utf-8') as f:
        json.dump(manifest, f, ensure_ascii=False, indent=2)
    
    # 保存合并文档
    combined_content = []
    for doc in included_docs:
        combined_content.append(f"\n{'='*80}")
        combined_content.append(f"文档: {doc['path']}")
        combined_content.append(f"大小: {doc['size']/1024:.1f}KB")
        combined_content.append('='*80)
        combined_content.append(doc['content'])
    
    combined_file = output_dir / 'combined_documents.md'
    combined_file.write_text('\n'.join(combined_content), encoding='utf-8')
    
    return manifest_file, combined_file

def main():
    """主函数"""
    print("="*60)
    print("🧹 准备清理后的文档集")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # 收集文档
    documents = collect_clean_documents()
    
    # 分析内容
    print(f"\n📊 文档统计:")
    stats = analyze_document_content(documents)
    print(f"  总文档数: {stats['total_documents']}")
    print(f"  总大小: {stats['total_size']/1024:.1f}KB")
    
    print("\n📂 文档分类:")
    for cat, docs in stats['categories'].items():
        if docs:
            print(f"  {cat}: {len(docs)} 个文档")
    
    # 准备 Gemini 文档
    print("\n🎯 准备 Gemini 分析文档...")
    included_docs, total_size = prepare_documents_for_gemini(documents)
    
    print(f"\n✅ 最终文档集:")
    print(f"  文档数: {len(included_docs)}")
    print(f"  总大小: {total_size/1024:.1f}KB")
    
    # 保存清理后的文档
    manifest_file, combined_file = save_clean_document_set(included_docs)
    
    print(f"\n💾 已保存:")
    print(f"  - 文档清单: {manifest_file}")
    print(f"  - 合并文档: {combined_file}")
    
    # 显示包含的文档
    print("\n📋 包含的文档:")
    for i, doc in enumerate(included_docs, 1):
        print(f"  {i}. {doc['path']} ({doc['size']/1024:.1f}KB)")

if __name__ == '__main__':
    main()