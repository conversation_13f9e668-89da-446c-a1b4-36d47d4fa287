#!/usr/bin/env node
'use strict';

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const dbConfig = require('../database/config');

// Get database path
const paths = dbConfig.getPaths();
const dbPath = paths.main;

console.log('========================================');
console.log('DStatus Database Analysis Report');
console.log('========================================');
console.log(`Database: ${dbPath}`);
console.log(`Date: ${new Date().toISOString()}`);
console.log('');

// Open database
const db = new Database(dbPath, { readonly: true });

// 1. Database File Analysis
console.log('1. DATABASE FILE ANALYSIS');
console.log('-------------------------');
const stats = fs.statSync(dbPath);
console.log(`File Size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
console.log(`Last Modified: ${stats.mtime}`);
console.log('');

// 2. Current Settings
console.log('2. CURRENT DATABASE SETTINGS');
console.log('----------------------------');
const pragmas = [
    'journal_mode',
    'synchronous', 
    'page_size',
    'cache_size',
    'auto_vacuum',
    'temp_store',
    'mmap_size'
];

pragmas.forEach(pragma => {
    const result = db.prepare(`PRAGMA ${pragma}`).get();
    console.log(`${pragma}: ${result[pragma]}`);
});
console.log('');

// 3. Table Analysis
console.log('3. TABLE ANALYSIS');
console.log('-----------------');
const tables = db.prepare(`
    SELECT name, sql 
    FROM sqlite_master 
    WHERE type='table' 
    ORDER BY name
`).all();

const tableStats = [];
tables.forEach(table => {
    if (table.name.startsWith('sqlite_')) return;
    
    const count = db.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
    const pageCount = db.prepare(`SELECT COUNT(*) as pages FROM dbstat WHERE name='${table.name}'`).get();
    
    tableStats.push({
        name: table.name,
        rows: count.count,
        pages: pageCount.pages || 0
    });
});

// Sort by row count
tableStats.sort((a, b) => b.rows - a.rows);

console.log('Table Name                 | Row Count    | Pages');
console.log('---------------------------|--------------|-------');
tableStats.forEach(stat => {
    console.log(`${stat.name.padEnd(26)} | ${stat.rows.toString().padStart(12)} | ${stat.pages.toString().padStart(5)}`);
});
console.log('');

// 4. Index Analysis
console.log('4. INDEX ANALYSIS');
console.log('-----------------');
const indexes = db.prepare(`
    SELECT name, tbl_name 
    FROM sqlite_master 
    WHERE type='index' AND name NOT LIKE 'sqlite_%'
    ORDER BY tbl_name, name
`).all();

let currentTable = '';
indexes.forEach(index => {
    if (index.tbl_name !== currentTable) {
        currentTable = index.tbl_name;
        console.log(`\n${currentTable}:`);
    }
    console.log(`  - ${index.name}`);
});
console.log('');

// 5. Data Growth Analysis
console.log('5. DATA GROWTH ANALYSIS');
console.log('-----------------------');
const growthTables = ['load_archive', 'tcping_archive', 'tcping_5m', 'tcping_m'];
growthTables.forEach(tableName => {
    try {
        const oldestRecord = db.prepare(`
            SELECT MIN(created_at) as oldest 
            FROM ${tableName} 
            WHERE created_at IS NOT NULL
        `).get();
        
        const newestRecord = db.prepare(`
            SELECT MAX(created_at) as newest 
            FROM ${tableName} 
            WHERE created_at IS NOT NULL
        `).get();
        
        const count = db.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).get();
        
        if (oldestRecord.oldest && newestRecord.newest) {
            const timeSpan = newestRecord.newest - oldestRecord.oldest;
            const days = timeSpan / 86400;
            const rowsPerDay = days > 0 ? Math.round(count.count / days) : 0;
            
            console.log(`${tableName}:`);
            console.log(`  Total Rows: ${count.count}`);
            console.log(`  Date Range: ${days.toFixed(1)} days`);
            console.log(`  Growth Rate: ~${rowsPerDay} rows/day`);
            console.log('');
        }
    } catch (err) {
        // Table might not have created_at column
    }
});

// 6. Query Performance Analysis
console.log('6. POTENTIAL PERFORMANCE ISSUES');
console.log('-------------------------------');
const issues = [];

// Check journal mode
const journalMode = db.prepare('PRAGMA journal_mode').get();
if (journalMode.journal_mode !== 'wal') {
    issues.push({
        severity: 'HIGH',
        issue: 'Database not using WAL mode',
        impact: 'Reduced concurrent read/write performance',
        solution: "PRAGMA journal_mode=WAL"
    });
}

// Check for missing indexes on foreign keys
const missingIndexes = [];
tables.forEach(table => {
    if (table.name.startsWith('sqlite_')) return;
    
    // Check for columns that might need indexes
    const columns = db.prepare(`PRAGMA table_info(${table.name})`).all();
    columns.forEach(col => {
        if (col.name.endsWith('_id') || col.name === 'sid' || col.name === 'created_at') {
            // Check if index exists
            const hasIndex = indexes.some(idx => 
                idx.tbl_name === table.name && 
                idx.name.includes(col.name)
            );
            
            if (!hasIndex && table.name !== 'servers') {
                missingIndexes.push(`${table.name}.${col.name}`);
            }
        }
    });
});

if (missingIndexes.length > 0) {
    issues.push({
        severity: 'MEDIUM',
        issue: 'Missing indexes on potential foreign keys',
        impact: 'Slower JOIN operations',
        solution: `Create indexes for: ${missingIndexes.join(', ')}`
    });
}

// Check for large tables without cleanup
const largeTables = tableStats.filter(t => t.rows > 100000);
if (largeTables.length > 0) {
    issues.push({
        severity: 'MEDIUM',
        issue: 'Large tables detected',
        impact: 'Increased storage and slower queries over time',
        solution: `Implement data archival/cleanup for: ${largeTables.map(t => t.name).join(', ')}`
    });
}

// Display issues
if (issues.length > 0) {
    issues.forEach(issue => {
        console.log(`[${issue.severity}] ${issue.issue}`);
        console.log(`  Impact: ${issue.impact}`);
        console.log(`  Solution: ${issue.solution}`);
        console.log('');
    });
} else {
    console.log('No major performance issues detected.');
}

// 7. Recommendations
console.log('7. OPTIMIZATION RECOMMENDATIONS');
console.log('-------------------------------');
console.log('1. Enable WAL Mode (HIGH PRIORITY):');
console.log('   - Run: PRAGMA journal_mode=WAL');
console.log('   - Benefits: 300-500% improvement in concurrent operations');
console.log('');
console.log('2. Implement Data Archival:');
console.log('   - Archive tcping_5m data older than 7 days');
console.log('   - Archive load_archive data older than 30 days');
console.log('   - Keep summary data in aggregated tables');
console.log('');
console.log('3. Add Missing Indexes:');
missingIndexes.forEach(idx => {
    const [table, column] = idx.split('.');
    console.log(`   - CREATE INDEX idx_${table}_${column} ON ${table}(${column});`);
});
console.log('');
console.log('4. Vacuum Database Periodically:');
console.log('   - Run VACUUM monthly to reclaim space');
console.log('   - Consider PRAGMA auto_vacuum=INCREMENTAL');
console.log('');
console.log('5. Optimize Cache Size:');
console.log('   - Current: 2000 pages');
console.log('   - Recommended: PRAGMA cache_size=-64000 (64MB)');

// Close database
db.close();

console.log('\n========================================');
console.log('Analysis Complete');
console.log('========================================');