#!/usr/bin/env python3
"""
Generate project index for DStatus
Only indexes up to 2 levels deep, excluding certain directories
"""
import os
import sys
from pathlib import Path
from datetime import datetime

# 项目根目录
ROOT = Path(__file__).parent.parent if len(sys.argv) <= 1 else Path(sys.argv[1])

# 忽略的目录和文件
IGNORE_DIRS = {
    '.git', '.github', '.vscode', '.idea', '__pycache__', 
    'node_modules', '.next', 'dist', 'build', '.cache',
    'coverage', '.pytest_cache', '.mypy_cache', 'venv'
}

IGNORE_FILES = {
    '.DS_Store', 'Thumbs.db', '*.pyc', '*.pyo', '*.log',
    '*.swp', '*.swo', '*~', '.env', '.env.local'
}

def should_exclude(path):
    """Check if path should be excluded"""
    name = path.name
    
    # Check directory exclusions
    if path.is_dir() and name in IGNORE_DIRS:
        return True
    
    # Check file exclusions
    if path.is_file():
        if name in IGNORE_FILES:
            return True
        for pattern in IGNORE_FILES:
            if pattern.startswith('*') and name.endswith(pattern[1:]):
                return True
    
    return False

def generate_tree(root_path, max_depth=2):
    """Generate directory tree structure up to max_depth"""
    root = Path(root_path)
    tree_lines = []
    
    def add_directory(path, prefix="", depth=0):
        if depth > max_depth:
            return
            
        try:
            items = sorted(path.iterdir(), key=lambda x: (not x.is_dir(), x.name.lower()))
            
            visible_items = [item for item in items if not should_exclude(item)]
            
            for i, item in enumerate(visible_items):
                is_last = i == len(visible_items) - 1
                current_prefix = "└── " if is_last else "├── "
                next_prefix = "    " if is_last else "│   "
                
                # Add item to tree
                if item.is_dir():
                    tree_lines.append(f"{prefix}{current_prefix}{item.name}/")
                    if depth < max_depth:
                        add_directory(item, prefix + next_prefix, depth + 1)
                else:
                    tree_lines.append(f"{prefix}{current_prefix}{item.name}")
                    
        except PermissionError:
            pass
    
    # Add root directory
    tree_lines.append(f"{root.name}/")
    add_directory(root, "", 0)
    
    return tree_lines

def count_files_and_dirs(root_path, max_depth=2):
    """Count files and directories up to max_depth"""
    root = Path(root_path)
    file_count = 0
    dir_count = 0
    
    def count_items(path, depth=0):
        nonlocal file_count, dir_count
        
        if depth > max_depth:
            return
            
        try:
            for item in path.iterdir():
                if should_exclude(item):
                    continue
                    
                if item.is_dir():
                    dir_count += 1
                    if depth < max_depth:
                        count_items(item, depth + 1)
                else:
                    file_count += 1
        except PermissionError:
            pass
    
    count_items(root, 0)
    return file_count, dir_count

def collect_markdown_files(root_path):
    """Collect all markdown files in the project"""
    markdown_files = {}
    root = Path(root_path)
    
    for path in root.rglob('*.md'):
        # Skip if in ignored directory
        if any(ignored in str(path) for ignored in IGNORE_DIRS):
            continue
        
        # Skip INDEX.md itself
        if path.name == 'INDEX.md':
            continue
            
        rel_path = path.relative_to(root)
        category = str(rel_path.parent).split('/')[0] if '/' in str(rel_path) else 'root'
        
        if category not in markdown_files:
            markdown_files[category] = []
        
        markdown_files[category].append({
            'name': path.name,
            'path': str(rel_path),
            'size': path.stat().st_size
        })
    
    # Sort files in each category
    for category in markdown_files:
        markdown_files[category].sort(key=lambda x: x['name'].lower())
    
    return markdown_files

def generate_index():
    """生成索引文件"""
    # Generate header
    header = f"""# DStatus Project Index

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Overview

This index shows the project structure up to 2 levels deep and lists all Markdown documentation.

"""
    
    # Count files and directories
    file_count, dir_count = count_files_and_dirs(ROOT)
    stats = f"""## Statistics

- Total Directories: {dir_count}
- Total Files: {file_count}

## Project Structure

```
"""
    
    # Generate tree
    tree_lines = generate_tree(ROOT)
    tree = '\n'.join(tree_lines)
    
    # Generate documentation section
    markdown_files = collect_markdown_files(ROOT)
    doc_section = """```

## Documentation Files

All Markdown files in the project, organized by directory:

"""
    
    # Add markdown files by category
    for category in sorted(markdown_files.keys()):
        if category == 'root':
            doc_section += "### Root Directory\n\n"
        else:
            doc_section += f"### /{category}/\n\n"
        
        for file in markdown_files[category]:
            # Format file size
            size = file['size']
            if size < 1024:
                size_str = f"{size} B"
            elif size < 1024 * 1024:
                size_str = f"{size / 1024:.1f} KB"
            else:
                size_str = f"{size / 1024 / 1024:.1f} MB"
            
            doc_section += f"- [{file['name']}]({file['path']}) ({size_str})\n"
        
        doc_section += "\n"
    
    # Generate footer
    footer = """## Quick Navigation

### Core Directories

- `/modules/` - Application modules
- `/views/` - View templates
- `/static/` - Static assets (CSS, JS, images)
- `/database/` - Database related code
- `/scripts/` - Utility scripts
- `/license-server/` - License server application
- `/docs/` - Documentation
- `/test/` - Test files

### Configuration Files

- `package.json` - Node.js dependencies
- `nekonekostatus.js` - Main application file
- `docker-compose.yml` - Docker configuration
- `tailwind.config.js` - Tailwind CSS configuration

### License Server Structure

- `/license-server/backend/` - Backend API
- `/license-server/frontend/` - Admin frontend
- `/license-server/user-frontend/` - User frontend

---

*Note: This index shows directories up to 2 levels deep and includes all Markdown documentation files.*
"""
    
    # 组合所有内容
    content = header + stats + tree + doc_section + footer
    
    # 写入索引文件
    with open(ROOT / 'INDEX.md', 'w', encoding='utf-8') as f:
        f.write(content)

if __name__ == '__main__':
    generate_index()
    print(f"索引已更新到 {ROOT / 'INDEX.md'}")
