#!/bin/bash

# 安装 Gemini API 依赖

echo "📦 安装 Gemini API 所需的 Python 包..."

# 检查 pip 是否可用
if ! command -v pip3 &> /dev/null; then
    echo "❌ 错误: pip3 未安装"
    echo "请先安装 Python 3 和 pip"
    exit 1
fi

# 安装 google-generativeai
echo "🔧 安装 google-generativeai..."
pip3 install google-generativeai

# 验证安装
echo ""
echo "✅ 验证安装..."
python3 -c "import google.generativeai as genai; print('google-generativeai 安装成功！')"

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 安装完成！"
    echo ""
    echo "使用方法:"
    echo "1. 生成提示词（不需要 API）:"
    echo "   python3 scripts/generate_gemini_prompt.py"
    echo ""
    echo "2. 使用 API 自动生成总结:"
    echo "   python3 scripts/summarize_docs_with_gemini.py"
    echo ""
    echo "3. 测试 API 连接:"
    echo "   python3 scripts/test_gemini_api.py"
else
    echo ""
    echo "❌ 安装失败，请检查错误信息"
fi