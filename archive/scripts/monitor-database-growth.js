#!/usr/bin/env node
'use strict';

/**
 * 数据库增长监控脚本
 * 分析各表的数据增长模式，帮助优化数据保留策略
 */

const Database = require('better-sqlite3');
const dbConfig = require('../database/config');
const fs = require('fs');
const path = require('path');

const dbPath = dbConfig.getPaths().main;
const db = new Database(dbPath, { readonly: true });

console.log('=== DStatus 数据库增长分析 ===');
console.log(`分析时间: ${new Date().toLocaleString()}\n`);

// 要分析的表
const tables = [
    // 负载数据表
    { name: 'load_archive', category: '负载数据', interval: '2秒' },
    { name: 'load_m', category: '负载数据', interval: '1分钟' },
    { name: 'load_h', category: '负载数据', interval: '1小时' },
    
    // TCPing 数据表
    { name: 'tcping_archive', category: '网络监控', interval: '实时' },
    { name: 'tcping_m', category: '网络监控', interval: '1分钟' },
    { name: 'tcping_5m', category: '网络监控', interval: '5分钟' },
    { name: 'tcping_h', category: '网络监控', interval: '1小时' },
    { name: 'tcping_d', category: '网络监控', interval: '1天' },
    { name: 'tcping_month', category: '网络监控', interval: '1月' },
    
    // 其他表
    { name: 'servers', category: '配置数据', interval: 'N/A' },
    { name: 'traffic', category: '流量统计', interval: '实时' },
    { name: 'ai_reports', category: 'AI分析', interval: '按需' },
    { name: 'monitor_targets', category: '监控配置', interval: 'N/A' },
    { name: 'monitor_regions', category: '监控配置', interval: 'N/A' }
];

// 获取表信息
function getTableInfo(tableName) {
    try {
        // 检查表是否存在
        const exists = db.prepare(`
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name=?
        `).get(tableName);
        
        if (!exists) return null;
        
        // 获取记录数
        const count = db.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).get().count;
        
        // 获取时间范围（如果有 created_at 字段）
        let timeRange = null;
        const hasCreatedAt = db.prepare(`
            SELECT name FROM pragma_table_info('${tableName}') 
            WHERE name='created_at'
        `).get();
        
        if (hasCreatedAt) {
            const range = db.prepare(`
                SELECT 
                    MIN(created_at) as min_time,
                    MAX(created_at) as max_time
                FROM ${tableName}
                WHERE created_at IS NOT NULL
            `).get();
            
            if (range.min_time && range.max_time) {
                timeRange = {
                    oldest: new Date(range.min_time * 1000),
                    newest: new Date(range.max_time * 1000),
                    days: Math.floor((range.max_time - range.min_time) / (24 * 60 * 60))
                };
            }
        }
        
        // 估算表大小（粗略计算）
        const avgRowSize = 100; // 假设平均每行100字节
        const estimatedSize = count * avgRowSize;
        
        // 计算增长率（如果有时间范围）
        let growthRate = null;
        if (timeRange && timeRange.days > 0) {
            growthRate = count / timeRange.days; // 每天的记录数
        }
        
        return {
            exists: true,
            count,
            timeRange,
            estimatedSize,
            growthRate
        };
    } catch (err) {
        console.error(`分析表 ${tableName} 时出错:`, err.message);
        return null;
    }
}

// 格式化文件大小
function formatSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
}

// 生成分析报告
function generateReport() {
    const report = {
        timestamp: new Date().toISOString(),
        database: {
            path: dbPath,
            size: fs.statSync(dbPath).size
        },
        tables: {},
        summary: {
            totalTables: 0,
            totalRecords: 0,
            totalEstimatedSize: 0,
            largestTables: [],
            fastestGrowing: []
        }
    };
    
    console.log('📊 表统计信息\n');
    console.log('类别 | 表名 | 记录数 | 预估大小 | 数据时间范围 | 日增长率');
    console.log('-'.repeat(80));
    
    // 按类别分组显示
    const categories = [...new Set(tables.map(t => t.category))];
    
    for (const category of categories) {
        const categoryTables = tables.filter(t => t.category === category);
        
        for (const table of categoryTables) {
            const info = getTableInfo(table.name);
            
            if (!info || !info.exists) {
                console.log(`${category} | ${table.name} | 不存在`);
                continue;
            }
            
            report.tables[table.name] = {
                ...info,
                category: table.category,
                interval: table.interval
            };
            
            report.summary.totalTables++;
            report.summary.totalRecords += info.count;
            report.summary.totalEstimatedSize += info.estimatedSize;
            
            // 记录最大的表
            report.summary.largestTables.push({
                name: table.name,
                count: info.count,
                size: info.estimatedSize
            });
            
            // 记录增长最快的表
            if (info.growthRate) {
                report.summary.fastestGrowing.push({
                    name: table.name,
                    rate: info.growthRate
                });
            }
            
            // 输出表信息
            const timeRangeStr = info.timeRange 
                ? `${info.timeRange.oldest.toLocaleDateString()} - ${info.timeRange.newest.toLocaleDateString()} (${info.timeRange.days}天)`
                : 'N/A';
            
            const growthRateStr = info.growthRate 
                ? `${Math.round(info.growthRate)}/天`
                : 'N/A';
            
            console.log(
                `${category} | ${table.name} | ${info.count.toLocaleString()} | ${formatSize(info.estimatedSize)} | ${timeRangeStr} | ${growthRateStr}`
            );
        }
    }
    
    // 排序并限制结果
    report.summary.largestTables.sort((a, b) => b.size - a.size);
    report.summary.largestTables = report.summary.largestTables.slice(0, 5);
    
    report.summary.fastestGrowing.sort((a, b) => b.rate - a.rate);
    report.summary.fastestGrowing = report.summary.fastestGrowing.slice(0, 5);
    
    // 显示摘要
    console.log('\n📈 增长分析摘要\n');
    console.log(`数据库总大小: ${formatSize(report.database.size)}`);
    console.log(`活跃表数量: ${report.summary.totalTables}`);
    console.log(`总记录数: ${report.summary.totalRecords.toLocaleString()}`);
    console.log(`预估数据大小: ${formatSize(report.summary.totalEstimatedSize)}`);
    
    console.log('\n🏆 最大的表（按记录数）:');
    report.summary.largestTables.forEach((table, index) => {
        console.log(`  ${index + 1}. ${table.name}: ${table.count.toLocaleString()} 条记录 (${formatSize(table.size)})`);
    });
    
    console.log('\n🚀 增长最快的表（按日增长率）:');
    report.summary.fastestGrowing.forEach((table, index) => {
        console.log(`  ${index + 1}. ${table.name}: ${Math.round(table.rate).toLocaleString()} 条/天`);
    });
    
    // 提供优化建议
    console.log('\n💡 优化建议:\n');
    
    // 检查大表
    const veryLargeTables = report.summary.largestTables.filter(t => t.count > 100000);
    if (veryLargeTables.length > 0) {
        console.log('⚠️  以下表记录数超过 100,000，建议缩短保留期限:');
        veryLargeTables.forEach(t => {
            console.log(`   - ${t.name}: ${t.count.toLocaleString()} 条记录`);
        });
    }
    
    // 检查快速增长的表
    const fastGrowingTables = report.summary.fastestGrowing.filter(t => t.rate > 10000);
    if (fastGrowingTables.length > 0) {
        console.log('\n⚠️  以下表每日增长超过 10,000 条记录，需要关注:');
        fastGrowingTables.forEach(t => {
            console.log(`   - ${t.name}: ${Math.round(t.rate).toLocaleString()} 条/天`);
        });
    }
    
    // 保存报告到文件
    const reportDir = path.join(path.dirname(dbPath), 'reports');
    if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
    }
    
    const reportFile = path.join(reportDir, `db-growth-${new Date().toISOString().split('T')[0]}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    console.log(`\n📄 详细报告已保存到: ${reportFile}`);
    
    return report;
}

// 比较历史报告
function compareHistoricalReports() {
    const reportDir = path.join(path.dirname(dbPath), 'reports');
    if (!fs.existsSync(reportDir)) return;
    
    const files = fs.readdirSync(reportDir)
        .filter(f => f.startsWith('db-growth-') && f.endsWith('.json'))
        .sort()
        .slice(-7); // 最近7天的报告
    
    if (files.length < 2) {
        console.log('\n📊 历史数据不足，无法进行趋势分析');
        return;
    }
    
    console.log('\n📊 7日增长趋势\n');
    
    const reports = files.map(f => {
        const content = fs.readFileSync(path.join(reportDir, f), 'utf8');
        return JSON.parse(content);
    });
    
    // 分析主要表的增长趋势
    const mainTables = ['load_archive', 'load_m', 'tcping_m', 'servers'];
    
    for (const tableName of mainTables) {
        const trend = reports.map(r => ({
            date: r.timestamp.split('T')[0],
            count: r.tables[tableName]?.count || 0
        }));
        
        if (trend.some(t => t.count > 0)) {
            console.log(`${tableName}:`);
            trend.forEach((t, i) => {
                const growth = i > 0 ? t.count - trend[i-1].count : 0;
                const growthStr = growth > 0 ? `+${growth}` : `${growth}`;
                console.log(`  ${t.date}: ${t.count.toLocaleString()} (${growthStr})`);
            });
            console.log('');
        }
    }
}

// 主函数
function main() {
    try {
        // 生成当前报告
        const report = generateReport();
        
        // 比较历史报告
        compareHistoricalReports();
        
        // 检查是否需要立即清理
        if (report.database.size > 1024 * 1024 * 1024) { // 1GB
            console.log('\n⚠️  警告: 数据库大小超过 1GB，建议立即运行清理:');
            console.log('   npm run cleanup');
        }
        
        console.log('\n✅ 分析完成！');
    } catch (err) {
        console.error('分析过程出错:', err);
        process.exit(1);
    } finally {
        db.close();
    }
}

// 运行主函数
main();