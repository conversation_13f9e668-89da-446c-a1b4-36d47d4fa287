// puppeteer-group-tab.js
// 用于自动访问 https://dev.ipxxxx.com 并点击分组筛选的第3个分组

const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: false }); // 可见浏览器，便于调试
  const page = await browser.newPage();
  await page.goto('https://dev.ipxxxx.com/', { waitUntil: 'networkidle2' });

  // 等待分组筛选区域出现（假设为按钮或tab，需根据实际页面结构调整选择器）
  await page.waitForSelector('.el-tabs__item, .tab-group, .group-tab, button, li', { timeout: 10000 });

  // 获取所有分组按钮，点击第3个（索引2）
  const groupTabs = await page.$$('.el-tabs__item, .tab-group, .group-tab, button, li');
  if (groupTabs.length >= 3) {
    await groupTabs[2].click();
    console.log('已切换到第3个分组');
  } else {
    console.log('未找到足够的分组按钮，请检查选择器');
  }

  // 保持页面，便于观察
  await page.waitForTimeout(5000);

  await browser.close();
})();
