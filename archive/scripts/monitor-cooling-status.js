#!/usr/bin/env node
/**
 * 监控被动模式服务器冷却状态
 * 
 * 运行方式：node scripts/monitor-cooling-status.js
 */

const { getNodeCoolingStatus } = require('../modules/api/cooling_logic');
const db = require('../database');

async function displayCoolingStatus() {
    console.clear();
    console.log('=== 被动模式服务器冷却状态监控 ===');
    console.log('时间:', new Date().toLocaleString());
    console.log('');

    const coolingStatus = getNodeCoolingStatus();
    const servers = await db.getServers();
    
    // 统计信息
    let totalServers = 0;
    let coolingServers = 0;
    let passiveModeServers = 0;

    console.log('服务器冷却状态:');
    console.log('-'.repeat(80));
    console.log('服务器名称'.padEnd(30) + '模式'.padEnd(10) + '失败次数'.padEnd(10) + '冷却状态'.padEnd(10) + '剩余时间(分钟)');
    console.log('-'.repeat(80));

    for (const server of servers) {
        if (server.status <= 0) continue;
        
        totalServers++;
        const isActiveMode = server.data?.api?.mode === true;
        
        if (!isActiveMode) {
            passiveModeServers++;
            const status = coolingStatus[server.sid] || { failCount: 0, cooling: false, remainingTime: 0 };
            
            if (status.cooling) {
                coolingServers++;
                // 高亮显示冷却中的服务器
                console.log(
                    `\x1b[31m${server.name.padEnd(30)}${(isActiveMode ? '主动' : '被动').padEnd(10)}${status.failCount.toString().padEnd(10)}${'冷却中'.padEnd(10)}${status.remainingTime}\x1b[0m`
                );
            } else if (status.failCount > 0) {
                // 显示有失败记录但未冷却的服务器
                console.log(
                    `\x1b[33m${server.name.padEnd(30)}${(isActiveMode ? '主动' : '被动').padEnd(10)}${status.failCount.toString().padEnd(10)}${'正常'.padEnd(10)}${'-'}\x1b[0m`
                );
            }
        }
    }

    console.log('-'.repeat(80));
    console.log(`\n统计信息:`);
    console.log(`- 总服务器数: ${totalServers}`);
    console.log(`- 被动模式服务器: ${passiveModeServers}`);
    console.log(`- 冷却中的服务器: ${coolingServers}`);
    console.log(`\n按 Ctrl+C 退出监控`);
}

// 主函数
async function main() {
    try {
        // 初次显示
        await displayCoolingStatus();
        
        // 每5秒刷新一次
        setInterval(async () => {
            await displayCoolingStatus();
        }, 5000);
        
    } catch (error) {
        console.error('监控冷却状态时出错:', error);
        process.exit(1);
    }
}

// 优雅退出
process.on('SIGINT', () => {
    console.log('\n监控已停止');
    process.exit(0);
});

// 运行主函数
main();