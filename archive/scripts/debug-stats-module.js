#!/usr/bin/env node
/**
 * Stats模块调试工具
 * 
 * 用于调试stats模块的getStatsData函数为什么返回空对象
 */

async function debugStatsModule() {
    console.log('=== Stats模块调试工具 ===');
    console.log('时间:', new Date().toLocaleString());
    console.log('');

    try {
        // 1. 初始化数据库
        console.log('1. 初始化数据库...');
        const dbFactory = require('../database');
        const db = await dbFactory();
        console.log('✅ 数据库初始化成功');

        // 2. 获取服务器列表
        console.log('\n2. 获取服务器列表...');
        const servers = await db.getServers();
        console.log(`✅ 获取到 ${servers.length} 个服务器`);

        // 3. 尝试获取stats模块
        console.log('\n3. 检查stats模块状态...');
        
        // 尝试直接require stats模块
        try {
            const statsModule = require('../modules/stats');
            console.log('✅ 成功require stats模块');
            
            if (statsModule.getGlobalStats) {
                const globalStats = statsModule.getGlobalStats();
                console.log(`📊 globalStats记录数: ${Object.keys(globalStats).length}`);
                
                if (Object.keys(globalStats).length > 0) {
                    console.log('📋 globalStats示例:');
                    const firstKey = Object.keys(globalStats)[0];
                    console.log(`  ${firstKey}:`, globalStats[firstKey]);
                }
            } else {
                console.log('❌ getGlobalStats函数不存在');
            }
        } catch (error) {
            console.log('❌ require stats模块失败:', error.message);
        }

        // 4. 检查主应用的stats模块
        console.log('\n4. 检查主应用的stats模块...');
        
        // 尝试通过HTTP请求检查
        const fetch = require('node-fetch');
        try {
            const response = await fetch('http://localhost:5555/api/allnode_status');
            const data = await response.json();
            console.log('📡 API响应:', {
                success: data.success,
                timestamp: data.timestamp,
                dataKeys: Object.keys(data.data || {}),
                dataCount: Object.keys(data.data || {}).length
            });
        } catch (error) {
            console.log('❌ API请求失败:', error.message);
        }

        // 5. 检查服务器配置
        console.log('\n5. 检查服务器配置...');
        const enabledServers = servers.filter(s => s.status > 0);
        console.log(`📊 启用的服务器数: ${enabledServers.length}`);
        
        const activeServers = enabledServers.filter(s => s.data?.api?.mode === true);
        const passiveServers = enabledServers.filter(s => s.data?.api?.mode !== true);
        console.log(`📊 主动模式服务器: ${activeServers.length}`);
        console.log(`📊 被动模式服务器: ${passiveServers.length}`);

        // 6. 检查API配置
        console.log('\n6. 检查API配置...');
        const serversWithApi = enabledServers.filter(s => s.data?.api?.key);
        console.log(`📊 有API配置的服务器: ${serversWithApi.length}`);

        if (serversWithApi.length > 0) {
            console.log('📋 API配置示例:');
            const sample = serversWithApi[0];
            console.log(`  ${sample.name}: mode=${sample.data.api.mode}, hasKey=${!!sample.data.api.key}`);
        }

        console.log('\n=== 调试完成 ===');

    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
    }
}

// 运行调试
debugStatsModule().catch(console.error);
