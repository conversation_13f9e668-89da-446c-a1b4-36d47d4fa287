#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

class WALOptimizationStrategy {
    constructor() {
        this.dbPath = path.join(__dirname, '../data/db.db');
        this.backupPath = path.join(__dirname, '../data/backups');
        this.results = {
            timestamp: new Date().toISOString(),
            optimizations: [],
            beforeMetrics: {},
            afterMetrics: {},
            performanceGains: {}
        };
    }

    async createBackup() {
        console.log('📦 创建数据库备份...');
        
        const backupFile = path.join(this.backupPath, `db-before-optimization-${Date.now()}.db`);
        
        // 确保备份目录存在
        if (!fs.existsSync(this.backupPath)) {
            fs.mkdirSync(this.backupPath, { recursive: true });
        }
        
        return new Promise((resolve, reject) => {
            const sourceDb = new sqlite3.Database(this.dbPath);
            
            sourceDb.serialize(() => {
                sourceDb.run(`VACUUM INTO '${backupFile}'`, (err) => {
                    if (err) {
                        console.error('❌ 备份失败:', err.message);
                        reject(err);
                    } else {
                        console.log(`✅ 备份完成: ${backupFile}`);
                        resolve(backupFile);
                    }
                    sourceDb.close();
                });
            });
        });
    }

    async measureCurrentPerformance() {
        console.log('📊 测量当前性能基线...');
        
        const metrics = {
            fileSize: {
                db: this.getFileSize(this.dbPath),
                wal: this.getFileSize(this.dbPath + '-wal'),
                shm: this.getFileSize(this.dbPath + '-shm')
            },
            queryPerformance: await this.measureQueryPerformance(),
            databaseConfig: await this.getDatabaseConfig()
        };
        
        this.results.beforeMetrics = metrics;
        return metrics;
    }

    async measureQueryPerformance() {
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(this.dbPath);
            
            const testQueries = [
                {
                    name: 'tcping_count',
                    sql: 'SELECT COUNT(*) as count FROM tcping_m',
                    description: 'TCPing数据计数'
                },
                {
                    name: 'load_recent',
                    sql: 'SELECT AVG(cpu) as avg_cpu FROM load_m WHERE created_at > (strftime("%s", "now") - 3600)',
                    description: '最近1小时平均CPU'
                },
                {
                    name: 'server_load_join',
                    sql: 'SELECT s.name, AVG(l.cpu) as avg_cpu FROM servers s LEFT JOIN load_m l ON s.sid = l.sid WHERE l.created_at > (strftime("%s", "now") - 86400) GROUP BY s.sid LIMIT 10',
                    description: '服务器负载统计'
                }
            ];

            const results = {};
            let completed = 0;

            testQueries.forEach(query => {
                const startTime = Date.now();
                
                db.get(query.sql, (err, row) => {
                    const endTime = Date.now();
                    const duration = endTime - startTime;
                    
                    if (err) {
                        console.error(`❌ ${query.name} 查询失败:`, err.message);
                        results[query.name] = { error: err.message, duration: 0 };
                    } else {
                        results[query.name] = {
                            duration,
                            description: query.description,
                            result: row
                        };
                        console.log(`   - ${query.description}: ${duration}ms`);
                    }
                    
                    completed++;
                    if (completed === testQueries.length) {
                        db.close();
                        resolve(results);
                    }
                });
            });
        });
    }

    async getDatabaseConfig() {
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(this.dbPath);
            
            const pragmas = [
                'journal_mode', 'wal_autocheckpoint', 'page_size', 'cache_size', 
                'mmap_size', 'synchronous', 'temp_store', 'locking_mode'
            ];
            
            const config = {};
            let completed = 0;

            pragmas.forEach(pragma => {
                db.get(`PRAGMA ${pragma}`, (err, row) => {
                    if (err) {
                        console.error(`❌ ${pragma} 查询失败:`, err.message);
                        config[pragma] = null;
                    } else {
                        config[pragma] = Object.values(row)[0];
                    }
                    
                    completed++;
                    if (completed === pragmas.length) {
                        db.close();
                        resolve(config);
                    }
                });
            });
        });
    }

    async optimizeWALCheckpoint() {
        console.log('🔄 优化WAL Checkpoint策略...');
        
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(this.dbPath);
            
            // 执行完整的checkpoint
            const startTime = Date.now();
            
            db.run('PRAGMA wal_checkpoint(FULL)', (err) => {
                if (err) {
                    console.error('❌ Checkpoint失败:', err.message);
                    reject(err);
                    return;
                }
                
                const checkpointTime = Date.now() - startTime;
                
                // 设置更积极的checkpoint策略
                db.run('PRAGMA wal_autocheckpoint=500', (err) => {
                    if (err) {
                        console.error('❌ 设置checkpoint间隔失败:', err.message);
                        reject(err);
                        return;
                    }
                    
                    const optimization = {
                        name: 'WAL Checkpoint',
                        action: 'Reduced wal_autocheckpoint from 1000 to 500 pages',
                        checkpointDuration: checkpointTime,
                        expectedImpact: 'Prevent WAL file growth beyond 2MB typically'
                    };
                    
                    this.results.optimizations.push(optimization);
                    console.log(`✅ ${optimization.action} (${checkpointTime}ms)`);
                    
                    db.close();
                    resolve(optimization);
                });
            });
        });
    }

    async optimizeMemoryCache() {
        console.log('🧠 优化内存缓存配置...');
        
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(this.dbPath);
            
            // 设置64MB缓存 (16384 pages * 4KB)
            db.run('PRAGMA cache_size=16384', (err) => {
                if (err) {
                    console.error('❌ 设置缓存大小失败:', err.message);
                    reject(err);
                    return;
                }
                
                // 启用mmap (256MB)
                db.run('PRAGMA mmap_size=268435456', (err) => {
                    if (err) {
                        console.error('❌ 设置mmap失败:', err.message);
                        reject(err);
                        return;
                    }
                    
                    const optimization = {
                        name: 'Memory Cache',
                        action: 'Increased cache_size to 16384 pages (64MB) and enabled mmap (256MB)',
                        expectedImpact: 'Improve query performance by 20-30%'
                    };
                    
                    this.results.optimizations.push(optimization);
                    console.log(`✅ ${optimization.action}`);
                    
                    db.close();
                    resolve(optimization);
                });
            });
        });
    }

    async optimizeSynchronousMode() {
        console.log('⚡ 优化同步模式...');
        
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(this.dbPath);
            
            // 设置NORMAL同步模式 (比FULL快，但仍然安全)
            db.run('PRAGMA synchronous=NORMAL', (err) => {
                if (err) {
                    console.error('❌ 设置同步模式失败:', err.message);
                    reject(err);
                    return;
                }
                
                const optimization = {
                    name: 'Synchronous Mode',
                    action: 'Changed synchronous from FULL to NORMAL',
                    expectedImpact: 'Improve write performance by 2-3x while maintaining data integrity'
                };
                
                this.results.optimizations.push(optimization);
                console.log(`✅ ${optimization.action}`);
                
                db.close();
                resolve(optimization);
            });
        });
    }

    async optimizeTempStore() {
        console.log('💾 优化临时存储...');
        
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(this.dbPath);
            
            // 设置临时表存储在内存中
            db.run('PRAGMA temp_store=MEMORY', (err) => {
                if (err) {
                    console.error('❌ 设置临时存储失败:', err.message);
                    reject(err);
                    return;
                }
                
                const optimization = {
                    name: 'Temp Store',
                    action: 'Set temp_store to MEMORY',
                    expectedImpact: 'Improve temporary table performance'
                };
                
                this.results.optimizations.push(optimization);
                console.log(`✅ ${optimization.action}`);
                
                db.close();
                resolve(optimization);
            });
        });
    }

    async runVacuum() {
        console.log('🧹 执行数据库清理...');
        
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(this.dbPath);
            
            const startTime = Date.now();
            
            // 先checkpoint，然后vacuum
            db.run('PRAGMA wal_checkpoint(TRUNCATE)', (err) => {
                if (err) {
                    console.error('❌ Checkpoint失败:', err.message);
                    reject(err);
                    return;
                }
                
                db.run('VACUUM', (err) => {
                    const vacuumTime = Date.now() - startTime;
                    
                    if (err) {
                        console.error('❌ VACUUM失败:', err.message);
                        reject(err);
                        return;
                    }
                    
                    const optimization = {
                        name: 'Database Vacuum',
                        action: 'Executed VACUUM to reclaim space and defragment',
                        duration: vacuumTime,
                        expectedImpact: 'Reduce database size and improve query performance'
                    };
                    
                    this.results.optimizations.push(optimization);
                    console.log(`✅ ${optimization.action} (${vacuumTime}ms)`);
                    
                    db.close();
                    resolve(optimization);
                });
            });
        });
    }

    async createOptimizedConfiguration() {
        console.log('⚙️  创建优化配置持久化...');
        
        const configScript = `-- SQLite优化配置
-- 自动应用于数据库连接

-- WAL模式配置
PRAGMA journal_mode=WAL;
PRAGMA wal_autocheckpoint=500;

-- 内存优化
PRAGMA cache_size=16384;          -- 64MB缓存
PRAGMA mmap_size=268435456;       -- 256MB内存映射
PRAGMA temp_store=MEMORY;         -- 临时表存储在内存

-- 性能优化
PRAGMA synchronous=NORMAL;        -- 平衡性能和安全性
PRAGMA locking_mode=NORMAL;       -- 正常锁定模式

-- 查询优化
PRAGMA optimize;                  -- 优化查询计划
`;

        const configPath = path.join(__dirname, '../config/sqlite-optimization.sql');
        const configDir = path.dirname(configPath);
        
        if (!fs.existsSync(configDir)) {
            fs.mkdirSync(configDir, { recursive: true });
        }
        
        fs.writeFileSync(configPath, configScript);
        
        console.log(`✅ 优化配置已保存: ${configPath}`);
        
        return configPath;
    }

    async createMaintenanceScript() {
        console.log('🔧 创建维护脚本...');
        
        const maintenanceScript = `#!/usr/bin/env node

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class DatabaseMaintenance {
    constructor() {
        this.dbPath = path.join(__dirname, '../data/db.db');
    }

    async performMaintenance() {
        console.log('🔄 开始数据库维护...');
        
        const db = new sqlite3.Database(this.dbPath);
        
        // 1. 检查WAL文件大小
        const walSize = require('fs').statSync(this.dbPath + '-wal').size;
        const dbSize = require('fs').statSync(this.dbPath).size;
        const ratio = walSize / dbSize;
        
        console.log(\`WAL文件大小比例: \${(ratio * 100).toFixed(1)}%\`);
        
        if (ratio > 0.3) {
            console.log('🔄 执行checkpoint...');
            await this.runCheckpoint(db);
        }
        
        // 2. 更新统计信息
        await this.updateStatistics(db);
        
        // 3. 清理过期数据
        await this.cleanupExpiredData(db);
        
        db.close();
        console.log('✅ 维护完成');
    }

    async runCheckpoint(db) {
        return new Promise((resolve, reject) => {
            db.run('PRAGMA wal_checkpoint(TRUNCATE)', (err) => {
                if (err) {
                    console.error('❌ Checkpoint失败:', err.message);
                    reject(err);
                } else {
                    console.log('✅ Checkpoint完成');
                    resolve();
                }
            });
        });
    }

    async updateStatistics(db) {
        return new Promise((resolve, reject) => {
            db.run('PRAGMA optimize', (err) => {
                if (err) {
                    console.error('❌ 统计更新失败:', err.message);
                    reject(err);
                } else {
                    console.log('✅ 统计信息已更新');
                    resolve();
                }
            });
        });
    }

    async cleanupExpiredData(db) {
        return new Promise((resolve, reject) => {
            const cutoffTime = Math.floor(Date.now() / 1000) - (30 * 24 * 60 * 60); // 30天前
            
            db.run('DELETE FROM tcping_m WHERE created_at < ?', [cutoffTime], (err) => {
                if (err) {
                    console.error('❌ 数据清理失败:', err.message);
                    reject(err);
                } else {
                    console.log('✅ 过期数据已清理');
                    resolve();
                }
            });
        });
    }
}

// 如果直接运行
if (require.main === module) {
    const maintenance = new DatabaseMaintenance();
    maintenance.performMaintenance().catch(console.error);
}

module.exports = DatabaseMaintenance;
`;

        const scriptPath = path.join(__dirname, '../scripts/database-maintenance.js');
        fs.writeFileSync(scriptPath, maintenanceScript, { mode: 0o755 });
        
        console.log(`✅ 维护脚本已创建: ${scriptPath}`);
        
        return scriptPath;
    }

    async measureOptimizationResults() {
        console.log('📈 测量优化结果...');
        
        const afterMetrics = await this.measureCurrentPerformance();
        this.results.afterMetrics = afterMetrics;
        
        // 计算性能提升
        const gains = this.calculatePerformanceGains();
        this.results.performanceGains = gains;
        
        return gains;
    }

    calculatePerformanceGains() {
        const before = this.results.beforeMetrics;
        const after = this.results.afterMetrics;
        
        const gains = {
            diskSpace: {
                walReduction: before.fileSize.wal - after.fileSize.wal,
                totalReduction: (before.fileSize.db + before.fileSize.wal) - (after.fileSize.db + after.fileSize.wal),
                reductionPercentage: ((before.fileSize.wal - after.fileSize.wal) / before.fileSize.wal) * 100
            },
            queryPerformance: {},
            configurationChanges: {}
        };
        
        // 比较查询性能
        Object.keys(before.queryPerformance).forEach(queryName => {
            const beforeTime = before.queryPerformance[queryName].duration;
            const afterTime = after.queryPerformance[queryName].duration;
            
            if (beforeTime > 0 && afterTime > 0) {
                gains.queryPerformance[queryName] = {
                    before: beforeTime,
                    after: afterTime,
                    improvement: ((beforeTime - afterTime) / beforeTime) * 100
                };
            }
        });
        
        return gains;
    }

    getFileSize(filePath) {
        try {
            return fs.statSync(filePath).size;
        } catch (err) {
            return 0;
        }
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async generateOptimizationReport() {
        console.log('📊 生成优化报告...');
        
        const report = {
            ...this.results,
            summary: {
                totalOptimizations: this.results.optimizations.length,
                diskSpaceSaved: this.results.performanceGains.diskSpace ? 
                    this.formatBytes(this.results.performanceGains.diskSpace.totalReduction) : 'N/A',
                averageQueryImprovement: this.calculateAverageQueryImprovement(),
                recommendedMaintenanceSchedule: 'Every 24 hours for checkpoint, weekly for full maintenance'
            },
            nextSteps: [
                'Monitor WAL file size daily',
                'Run database-maintenance.js weekly',
                'Consider implementing automated monitoring',
                'Evaluate query patterns for further optimization'
            ]
        };
        
        const reportPath = path.join(__dirname, '../data/optimization-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log(`📋 优化报告已保存: ${reportPath}`);
        
        return report;
    }

    calculateAverageQueryImprovement() {
        const queryPerf = this.results.performanceGains.queryPerformance;
        const improvements = Object.values(queryPerf).map(q => q.improvement).filter(i => i > 0);
        
        if (improvements.length === 0) return 'N/A';
        
        const average = improvements.reduce((a, b) => a + b, 0) / improvements.length;
        return `${average.toFixed(1)}%`;
    }

    async runFullOptimization() {
        console.log('🚀 开始WAL优化策略实施...\n');
        
        try {
            // 1. 创建备份
            await this.createBackup();
            
            // 2. 测量基线性能
            await this.measureCurrentPerformance();
            
            // 3. 执行优化
            await this.optimizeWALCheckpoint();
            await this.optimizeMemoryCache();
            await this.optimizeSynchronousMode();
            await this.optimizeTempStore();
            
            // 4. 清理数据库
            await this.runVacuum();
            
            // 5. 创建配置文件
            await this.createOptimizedConfiguration();
            
            // 6. 创建维护脚本
            await this.createMaintenanceScript();
            
            // 7. 测量结果
            await this.measureOptimizationResults();
            
            // 8. 生成报告
            const report = await this.generateOptimizationReport();
            
            console.log('\n🎉 优化完成！');
            console.log(`✅ 完成 ${this.results.optimizations.length} 项优化`);
            console.log(`💾 磁盘空间节省: ${report.summary.diskSpaceSaved}`);
            console.log(`⚡ 平均查询性能提升: ${report.summary.averageQueryImprovement}`);
            console.log(`📋 详细报告: data/optimization-report.json`);
            
            return report;
            
        } catch (error) {
            console.error('❌ 优化过程中发生错误:', error);
            throw error;
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const optimizer = new WALOptimizationStrategy();
    optimizer.runFullOptimization()
        .then(report => {
            console.log('\n✅ 优化策略实施完成！');
            console.log('建议重启应用以应用所有优化配置。');
        })
        .catch(error => {
            console.error('❌ 优化失败:', error);
            process.exit(1);
        });
}

module.exports = WALOptimizationStrategy;