/**
 * 生成light:前缀到dark:前缀的映射关系
 * 此脚本帮助将非标准的light:前缀转换为Tailwind支持的dark:前缀
 * 
 * 使用方法:
 * 1. 运行: node scripts/generate-dark-class-mapping.js
 * 2. 脚本会生成映射关系文件，可以用于替换模板中的类
 */

const fs = require('fs');
const path = require('path');

// 通用的背景色映射
const bgMapping = {
  'bg-slate-200': 'bg-slate-800',
  'bg-slate-200/60': 'bg-slate-800/60',
  'bg-slate-300': 'bg-slate-700',
  'bg-white': 'bg-slate-800',
  'bg-white/90': 'bg-slate-900/50',
  'bg-gray-50': 'bg-gray-900',
  'bg-gray-100': 'bg-gray-800',
  'bg-gray-200': 'bg-gray-700',
};

// 通用的文本颜色映射
const textMapping = {
  'text-gray-700': 'text-gray-300',
  'text-gray-800': 'text-gray-300',
  'text-slate-700': 'text-slate-300',
  'text-slate-800': 'text-slate-200',
  'text-black': 'text-white',
};

// 通用的边框颜色映射
const borderMapping = {
  'border-gray-300/50': 'border-gray-700/50',
  'border-gray-200/50': 'border-gray-700/50',
  'border-slate-300': 'border-slate-700',
  'border-slate-300/50': 'border-slate-700/50',
};

// 创建完整的映射
const fullMapping = {
  ...bgMapping,
  ...textMapping,
  ...borderMapping,
};

// 生成light:到dark:的映射关系
const lightToDarkMapping = {};

Object.entries(fullMapping).forEach(([lightClass, darkClass]) => {
  lightToDarkMapping[`light:${lightClass}`] = `dark:${lightClass}`;
});

// 生成替换规则
const replacementRules = Object.entries(lightToDarkMapping).map(([lightClass, darkClass]) => {
  return `${lightClass} => ${darkClass}`;
}).join('\n');

// 生成替换脚本
const replacementScript = `
/**
 * 以下是light:前缀到dark:前缀的映射关系
 * 使用这些规则手动替换HTML模板中的类
 */

${replacementRules}

/**
 * 要使用dark:模式，需要注意：
 * 1. dark:表示"当处于暗色模式时应用此样式"，而不是像light:那样表示"当处于亮色模式时应用此样式"
 * 2. 正确的用法是提供默认样式(暗色模式)，然后用dark:前缀提供亮色模式的替代样式
 * 3. 例如: class="bg-slate-800 dark:bg-white text-white dark:text-slate-800"
 */
`;

// 写入文件
const outputDir = path.join(__dirname, '../docs');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

fs.writeFileSync(
  path.join(outputDir, 'dark-class-mapping.js'),
  replacementScript,
  'utf8'
);

console.log('映射关系已生成到 docs/dark-class-mapping.js');

// 生成查找非标准前缀的grep命令
const grepCommand = `grep -r "light:" --include="*.html" views/`;
console.log('\n可以使用以下命令查找所有使用light:前缀的模板:');
console.log(grepCommand);

// 生成处理字符串的函数示例
const processingFunction = `
/**
 * 处理HTML模板中的class属性，将light:前缀替换为dark:前缀
 * @param {string} classString - 原始的class字符串
 * @return {string} 处理后的class字符串
 */
function processClassString(classString) {
  // 分割class字符串
  const classes = classString.split(/\\s+/);
  const result = [];
  
  for (const cls of classes) {
    if (cls.startsWith('light:')) {
      // 1. 提取light:后面的实际类名
      const actualClass = cls.substring(6);
      
      // 2. 将当前类添加为默认类(暗色模式)
      result.push(actualClass);
      
      // 3. 根据映射关系添加dark:前缀的类(亮色模式)
      const darkMapping = fullMapping[actualClass];
      if (darkMapping) {
        result.push(\`dark:\${darkMapping}\`);
      } else {
        // 如果没有映射，直接转换为dark:前缀
        result.push(\`dark:\${actualClass}\`);
      }
    } else if (!cls.startsWith('dark:')) {
      // 保留其他非dark:前缀的类
      result.push(cls);
    }
  }
  
  return result.join(' ');
}
`;

// 将处理函数添加到输出
fs.appendFileSync(
  path.join(outputDir, 'dark-class-mapping.js'),
  processingFunction,
  'utf8'
);

console.log('处理函数已添加到输出文件'); 