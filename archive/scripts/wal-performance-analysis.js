#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

class WALPerformanceAnalyzer {
    constructor() {
        this.dbPath = path.join(__dirname, '../data/db.db');
        this.walPath = this.dbPath + '-wal';
        this.shmPath = this.dbPath + '-shm';
        this.results = {
            timestamp: new Date().toISOString(),
            currentStatus: {},
            performanceMetrics: {},
            optimizationRecommendations: []
        };
    }

    async analyzeCurrentStatus() {
        console.log('🔍 分析当前WAL文件状态...');
        
        // 获取文件大小
        const stats = {
            db: this.getFileSize(this.dbPath),
            wal: this.getFileSize(this.walPath),
            shm: this.getFileSize(this.shmPath)
        };

        this.results.currentStatus = {
            files: stats,
            walToDbRatio: stats.wal / stats.db,
            totalSize: stats.db + stats.wal + stats.shm,
            walSizeFormatted: this.formatBytes(stats.wal),
            dbSizeFormatted: this.formatBytes(stats.db),
            totalSizeFormatted: this.formatBytes(stats.db + stats.wal + stats.shm)
        };

        console.log(`📊 当前状态:`);
        console.log(`   - 主数据库: ${this.results.currentStatus.dbSizeFormatted}`);
        console.log(`   - WAL文件: ${this.results.currentStatus.walSizeFormatted}`);
        console.log(`   - WAL/DB比例: ${(this.results.currentStatus.walToDbRatio * 100).toFixed(1)}%`);
        console.log(`   - 总占用空间: ${this.results.currentStatus.totalSizeFormatted}`);

        return this.results.currentStatus;
    }

    async analyzeDatabaseConfig() {
        console.log('⚙️  分析数据库配置...');
        
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(this.dbPath, sqlite3.OPEN_READONLY);
            
            const queries = [
                'PRAGMA journal_mode',
                'PRAGMA wal_autocheckpoint',
                'PRAGMA page_size',
                'PRAGMA page_count',
                'PRAGMA cache_size',
                'PRAGMA mmap_size',
                'PRAGMA synchronous',
                'PRAGMA temp_store'
            ];

            const config = {};
            let completed = 0;

            queries.forEach(query => {
                const pragma = query.replace('PRAGMA ', '');
                db.get(query, (err, row) => {
                    if (err) {
                        console.error(`❌ ${pragma} 查询失败:`, err.message);
                    } else {
                        config[pragma] = Object.values(row)[0];
                    }
                    
                    completed++;
                    if (completed === queries.length) {
                        this.results.performanceMetrics.databaseConfig = config;
                        
                        console.log(`📋 数据库配置:`);
                        Object.entries(config).forEach(([key, value]) => {
                            console.log(`   - ${key}: ${value}`);
                        });
                        
                        db.close();
                        resolve(config);
                    }
                });
            });
        });
    }

    async performCheckpoint() {
        console.log('🔄 执行WAL checkpoint...');
        
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(this.dbPath);
            
            const startTime = Date.now();
            db.run('PRAGMA wal_checkpoint(FULL)', (err) => {
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                if (err) {
                    console.error('❌ Checkpoint失败:', err.message);
                    reject(err);
                } else {
                    console.log(`✅ Checkpoint完成，耗时: ${duration}ms`);
                    
                    // 获取checkpoint后的文件大小
                    const newWalSize = this.getFileSize(this.walPath);
                    const reduction = this.results.currentStatus.files.wal - newWalSize;
                    
                    this.results.performanceMetrics.checkpoint = {
                        duration,
                        walSizeBefore: this.results.currentStatus.files.wal,
                        walSizeAfter: newWalSize,
                        reduction,
                        reductionPercentage: (reduction / this.results.currentStatus.files.wal) * 100
                    };
                    
                    console.log(`📉 WAL文件减少: ${this.formatBytes(reduction)} (${this.results.performanceMetrics.checkpoint.reductionPercentage.toFixed(1)}%)`);
                    
                    db.close();
                    resolve(this.results.performanceMetrics.checkpoint);
                }
            });
        });
    }

    async measureQueryPerformance() {
        console.log('⏱️  测量查询性能...');
        
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(this.dbPath);
            
            const testQueries = [
                {
                    name: 'tcping_m_select',
                    sql: 'SELECT COUNT(*) FROM tcping_m WHERE timestamp > datetime("now", "-1 hour")',
                    description: 'TCPing分钟数据查询'
                },
                {
                    name: 'load_m_select',
                    sql: 'SELECT AVG(cpu) FROM load_m WHERE timestamp > datetime("now", "-1 hour")',
                    description: '系统负载分钟数据查询'
                },
                {
                    name: 'complex_join',
                    sql: 'SELECT s.name, COUNT(l.timestamp) FROM servers s LEFT JOIN load_m l ON s.sid = l.sid WHERE l.timestamp > datetime("now", "-1 day") GROUP BY s.sid',
                    description: '复杂连接查询'
                }
            ];

            const performanceResults = {};
            let completed = 0;

            testQueries.forEach(query => {
                const startTime = Date.now();
                
                db.get(query.sql, (err, row) => {
                    const endTime = Date.now();
                    const duration = endTime - startTime;
                    
                    if (err) {
                        console.error(`❌ ${query.name} 查询失败:`, err.message);
                        performanceResults[query.name] = { error: err.message, duration: 0 };
                    } else {
                        performanceResults[query.name] = {
                            duration,
                            description: query.description,
                            result: row
                        };
                        console.log(`   - ${query.description}: ${duration}ms`);
                    }
                    
                    completed++;
                    if (completed === testQueries.length) {
                        this.results.performanceMetrics.queryPerformance = performanceResults;
                        db.close();
                        resolve(performanceResults);
                    }
                });
            });
        });
    }

    calculateOptimizationImpact() {
        console.log('📊 计算优化影响...');
        
        const current = this.results.currentStatus;
        const config = this.results.performanceMetrics.databaseConfig;
        
        // 计算预期的性能提升
        const optimizationImpact = {
            diskSpace: {
                currentTotal: current.totalSize,
                projectedSaving: current.files.wal * 0.8, // 假设WAL文件可减少80%
                projectedTotal: current.files.db + (current.files.wal * 0.2) + current.files.shm
            },
            memory: {
                currentCacheSize: parseInt(config.cache_size) * parseInt(config.page_size),
                recommendedCacheSize: 64 * 1024 * 1024, // 64MB
                improvementPotential: '20-30%'
            },
            io: {
                currentWalCheckpoint: parseInt(config.wal_autocheckpoint),
                recommendedCheckpoint: 1000,
                estimatedIOReduction: '40-60%'
            }
        };

        this.results.performanceMetrics.optimizationImpact = optimizationImpact;

        console.log(`💡 优化影响预估:`);
        console.log(`   - 磁盘空间节省: ${this.formatBytes(optimizationImpact.diskSpace.projectedSaving)}`);
        console.log(`   - 内存使用优化: ${optimizationImpact.memory.improvementPotential}`);
        console.log(`   - I/O性能提升: ${optimizationImpact.io.estimatedIOReduction}`);

        return optimizationImpact;
    }

    generateRecommendations() {
        console.log('🎯 生成优化建议...');
        
        const current = this.results.currentStatus;
        const config = this.results.performanceMetrics.databaseConfig;
        
        const recommendations = [];

        // WAL文件大小检查
        if (current.walToDbRatio > 0.5) {
            recommendations.push({
                priority: 'HIGH',
                category: 'WAL Management',
                issue: `WAL文件过大 (${(current.walToDbRatio * 100).toFixed(1)}% of DB size)`,
                solution: 'Implement automatic checkpoint strategy with smaller intervals',
                impact: 'Reduce disk usage by 60-80% and improve query performance'
            });
        }

        // 内存缓存检查
        const currentCacheSize = parseInt(config.cache_size) * parseInt(config.page_size);
        if (currentCacheSize < 32 * 1024 * 1024) { // 小于32MB
            recommendations.push({
                priority: 'MEDIUM',
                category: 'Memory Cache',
                issue: `缓存大小过小 (${this.formatBytes(currentCacheSize)})`,
                solution: 'Increase cache_size to 16384 pages (64MB)',
                impact: 'Improve query performance by 20-30%'
            });
        }

        // Checkpoint间隔检查
        const checkpointInterval = parseInt(config.wal_autocheckpoint);
        if (checkpointInterval > 1000) {
            recommendations.push({
                priority: 'HIGH',
                category: 'Checkpoint Strategy',
                issue: `Checkpoint间隔过大 (${checkpointInterval} pages)`,
                solution: 'Reduce wal_autocheckpoint to 1000 pages',
                impact: 'Prevent WAL file growth and improve stability'
            });
        }

        // 同步模式检查
        if (config.synchronous === 'FULL') {
            recommendations.push({
                priority: 'MEDIUM',
                category: 'Synchronous Mode',
                issue: 'Synchronous mode is FULL (slower but safer)',
                solution: 'Consider NORMAL mode for better performance',
                impact: 'Improve write performance by 2-3x'
            });
        }

        this.results.optimizationRecommendations = recommendations;

        console.log(`📝 优化建议 (${recommendations.length}项):`);
        recommendations.forEach((rec, index) => {
            console.log(`   ${index + 1}. [${rec.priority}] ${rec.category}: ${rec.issue}`);
            console.log(`      解决方案: ${rec.solution}`);
            console.log(`      预期影响: ${rec.impact}`);
        });

        return recommendations;
    }

    async generateMonitoringScript() {
        console.log('🔧 生成监控脚本...');
        
        const monitoringScript = `#!/bin/bash
# SQLite WAL 性能监控脚本
# 自动生成于: ${new Date().toISOString()}

DB_PATH="${this.dbPath}"
WAL_PATH="${this.walPath}"
LOG_PATH="/Users/<USER>/code/license-server/dstatus/data/logs/wal-monitor.log"

# 获取文件大小
get_file_size() {
    if [ -f "$1" ]; then
        stat -f%z "$1" 2>/dev/null || echo "0"
    else
        echo "0"
    fi
}

# 记录日志
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_PATH"
}

# 检查WAL文件大小
check_wal_size() {
    local db_size=$(get_file_size "$DB_PATH")
    local wal_size=$(get_file_size "$WAL_PATH")
    
    if [ "$db_size" -gt 0 ] && [ "$wal_size" -gt 0 ]; then
        local ratio=$(echo "scale=2; $wal_size / $db_size * 100" | bc)
        
        if (( $(echo "$ratio > 50" | bc -l) )); then
            log_message "WARNING: WAL file is \${ratio}% of DB size (WAL: $wal_size bytes, DB: $db_size bytes)"
            
            # 执行checkpoint
            sqlite3 "$DB_PATH" "PRAGMA wal_checkpoint(FULL);" 2>/dev/null
            if [ $? -eq 0 ]; then
                log_message "INFO: Checkpoint executed successfully"
            else
                log_message "ERROR: Checkpoint failed"
            fi
        else
            log_message "INFO: WAL size is healthy (\${ratio}% of DB size)"
        fi
    fi
}

# 监控性能指标
monitor_performance() {
    local start_time=$(date +%s%N)
    
    # 执行测试查询
    sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM tcping_m WHERE timestamp > datetime('now', '-1 hour');" > /dev/null 2>&1
    
    local end_time=$(date +%s%N)
    local duration=$(echo "scale=2; ($end_time - $start_time) / 1000000" | bc)
    
    log_message "INFO: Test query executed in \${duration}ms"
    
    # 记录内存使用
    local memory_usage=$(ps -o pid,vsz,rss,comm | grep -E "(node|sqlite)" | awk '{sum+=$3} END {print sum}')
    if [ ! -z "$memory_usage" ]; then
        log_message "INFO: Memory usage: \${memory_usage}KB"
    fi
}

# 主监控循环
main() {
    log_message "INFO: WAL monitoring started"
    
    while true; do
        check_wal_size
        monitor_performance
        sleep 300  # 5分钟检查一次
    done
}

# 信号处理
trap 'log_message "INFO: WAL monitoring stopped"; exit 0' SIGTERM SIGINT

# 启动监控
main
`;

        const scriptPath = path.join(__dirname, '../scripts/wal-monitor.sh');
        fs.writeFileSync(scriptPath, monitoringScript, { mode: 0o755 });
        
        console.log(`✅ 监控脚本已生成: ${scriptPath}`);
        
        return scriptPath;
    }

    getFileSize(filePath) {
        try {
            return fs.statSync(filePath).size;
        } catch (err) {
            return 0;
        }
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async generateReport() {
        console.log('📄 生成性能分析报告...');
        
        const report = {
            ...this.results,
            summary: {
                walHealth: this.results.currentStatus.walToDbRatio < 0.5 ? 'GOOD' : 'NEEDS_ATTENTION',
                optimizationPriority: this.results.optimizationRecommendations.filter(r => r.priority === 'HIGH').length > 0 ? 'HIGH' : 'MEDIUM',
                estimatedImprovements: {
                    diskSpaceSaving: this.formatBytes(this.results.performanceMetrics.optimizationImpact.diskSpace.projectedSaving),
                    performanceGain: '20-40%',
                    memoryEfficiency: '30-50%'
                }
            }
        };

        const reportPath = path.join(__dirname, '../data/wal-analysis-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log(`📊 分析报告已保存: ${reportPath}`);
        
        return report;
    }

    async runFullAnalysis() {
        console.log('🚀 开始WAL性能分析...\n');
        
        try {
            // 1. 分析当前状态
            await this.analyzeCurrentStatus();
            
            // 2. 分析数据库配置
            await this.analyzeDatabaseConfig();
            
            // 3. 测量查询性能
            await this.measureQueryPerformance();
            
            // 4. 执行checkpoint测试
            await this.performCheckpoint();
            
            // 5. 计算优化影响
            this.calculateOptimizationImpact();
            
            // 6. 生成建议
            this.generateRecommendations();
            
            // 7. 生成监控脚本
            await this.generateMonitoringScript();
            
            // 8. 生成报告
            const report = await this.generateReport();
            
            console.log('\n🎉 分析完成！');
            console.log(`📋 发现 ${this.results.optimizationRecommendations.length} 项优化建议`);
            console.log(`💾 预估可节省磁盘空间: ${report.summary.estimatedImprovements.diskSpaceSaving}`);
            console.log(`⚡ 预估性能提升: ${report.summary.estimatedImprovements.performanceGain}`);
            
            return report;
            
        } catch (error) {
            console.error('❌ 分析过程中发生错误:', error);
            throw error;
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const analyzer = new WALPerformanceAnalyzer();
    analyzer.runFullAnalysis()
        .then(report => {
            console.log('\n分析完成，查看详细报告:');
            console.log('cat data/wal-analysis-report.json');
        })
        .catch(error => {
            console.error('分析失败:', error);
            process.exit(1);
        });
}

module.exports = WALPerformanceAnalyzer;