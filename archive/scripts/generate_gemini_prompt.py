#!/usr/bin/env python3
"""
生成用于 Gemini 的文档总结提示词
无需 API，直接生成提示词文本
"""

import os
import sys
from pathlib import Path
from datetime import datetime
from typing import List, Tuple

# 项目根目录
ROOT = Path(__file__).parent.parent

# 忽略的目录
IGNORE_DIRS = {
    '.git', '.github', '.vscode', '.idea', '__pycache__', 
    'node_modules', '.next', 'dist', 'build', '.cache',
    'coverage', '.pytest_cache', '.mypy_cache', 'venv'
}

# 核心文档优先级（这些文档应该优先分析）
PRIORITY_DOCS = [
    'CORE_MEMORY.md',
    'PROJECT_STATUS.md',
    'plan.md',
    'README.md',
    'readme.md'
]

def collect_markdown_files(root_path: Path) -> List[Tuple[Path, float, int]]:
    """收集所有 Markdown 文件并按修改时间排序"""
    markdown_files = []
    
    for path in root_path.rglob('*.md'):
        # 跳过忽略的目录
        if any(ignored in str(path) for ignored in IGNORE_DIRS):
            continue
        
        # 跳过 INDEX.md
        if path.name == 'INDEX.md':
            continue
        
        # 获取文件修改时间
        mtime = path.stat().st_mtime
        
        # 设置优先级
        priority = 0
        if path.name in PRIORITY_DOCS:
            priority = PRIORITY_DOCS.index(path.name) + 1
        
        markdown_files.append((path, mtime, priority))
    
    # 先按优先级排序，再按修改时间降序排序
    markdown_files.sort(key=lambda x: (-x[2], -x[1]))
    
    return markdown_files

def read_file_content(file_path: Path, max_size: int = 10000) -> str:
    """读取文件内容，限制大小"""
    try:
        content = file_path.read_text(encoding='utf-8')
        if len(content) > max_size:
            # 保留开头和结尾
            head = content[:max_size//2]
            tail = content[-max_size//2:]
            content = f"{head}\n\n[...内容被截断...]\n\n{tail}"
        return content
    except Exception as e:
        return f"[读取文件出错: {e}]"

def generate_prompt(files: List[Tuple[Path, float, int]], root: Path) -> str:
    """生成 Gemini 提示词"""
    prompt_parts = []
    
    # 添加系统指令
    prompt_parts.append("""# DStatus 项目文档分析任务

你是一个专业的技术文档分析专家。请分析以下 DStatus 项目的文档集合，生成一个综合性的项目总结。

## 项目背景
DStatus 是一个分布式服务器监控系统，包含客户端监控程序和 SaaS 授权服务（License Server）。

## 分析原则

### 1. 时间线权重原则
- 文档按修改时间排序，越新的文档可信度越高
- 如果早期文档和近期文档有冲突，以近期文档为准
- 识别项目的演进过程，理解决策变更的原因

### 2. 优先级文档
以下文档具有最高优先级，应重点分析：
- CORE_MEMORY.md - 核心记忆文档
- PROJECT_STATUS.md - 项目状态
- plan.md - 项目计划
- README.md - 项目说明

### 3. 内容整合策略
- 提取关键技术决策和架构设计
- 识别已完成、进行中和待处理的任务
- 理解各模块之间的关系和交互
- 追踪重要的变更历史

---

## 文档清单
""")
    
    # 添加文档列表
    for i, (file_path, mtime, priority) in enumerate(files):
        mod_time = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
        rel_path = file_path.relative_to(root)
        priority_mark = "⭐ " if priority > 0 else ""
        prompt_parts.append(f"{i+1}. {priority_mark}{rel_path} (修改时间: {mod_time})")
    
    prompt_parts.append("\n---\n\n## 文档内容\n")
    
    # 添加文档内容
    for i, (file_path, mtime, priority) in enumerate(files):
        mod_time = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
        rel_path = file_path.relative_to(root)
        content = read_file_content(file_path)
        
        prompt_parts.append(f"""
### 📄 文档 {i+1}: {rel_path}
- **修改时间**: {mod_time}
- **优先级**: {'高' if priority > 0 else '普通'}
- **文件大小**: {file_path.stat().st_size / 1024:.1f} KB

```markdown
{content}
```

---
""")
    
    # 添加总结要求
    prompt_parts.append("""
## 输出格式要求

**重要**：请严格按照以下JSON格式输出，不要包含任何其他内容。输出必须是合法的JSON，可以被直接解析。

```json
{
  "project_overview": {
    "name": "项目名称",
    "version": "版本号",
    "core_value": "核心价值描述",
    "main_modules": ["模块1", "模块2"],
    "tech_stack": ["技术1", "技术2"],
    "architecture": {
      "client": "客户端架构描述",
      "server": "服务端架构描述"
    },
    "development_status": "当前开发状态",
    "maturity_level": "成熟度等级"
  },
  "timeline_evolution": {
    "phases": [
      {
        "phase_name": "阶段名称",
        "time_period": "时间段",
        "key_changes": ["变更1", "变更2"],
        "decisions": ["决策1", "决策2"]
      }
    ],
    "deprecated_features": [
      {
        "feature": "功能名称",
        "reason": "废弃原因",
        "replacement": "替代方案"
      }
    ],
    "technical_debt": ["技术债务1", "技术债务2"]
  },
  "system_architecture": {
    "dstatus_client": {
      "core_modules": ["模块1", "模块2"],
      "monitoring_mechanism": "监控机制描述",
      "data_flow": "数据流描述",
      "auth_integration": "授权集成描述",
      "admin_features": ["功能1", "功能2"]
    },
    "license_server": {
      "backend_api": {
        "framework": "框架名称",
        "database": "数据库类型",
        "key_endpoints": ["端点1", "端点2"]
      },
      "frontend_apps": {
        "admin": "管理端描述",
        "user": "用户端描述"
      },
      "auth_mechanism": "认证机制描述"
    }
  },
  "recent_progress": {
    "completed_features": [
      {
        "feature": "功能名称",
        "completion_date": "完成日期",
        "description": "功能描述"
      }
    ],
    "ongoing_tasks": [
      {
        "task": "任务名称",
        "status": "进行状态",
        "priority": "优先级"
      }
    ],
    "recent_fixes": ["修复1", "修复2"],
    "pending_issues": ["问题1", "问题2"]
  },
  "key_technical_info": {
    "api_design": {
      "main_endpoints": [
        {
          "endpoint": "端点路径",
          "method": "HTTP方法",
          "description": "端点描述"
        }
      ],
      "auth_mechanism": "认证机制",
      "data_format": "数据格式"
    },
    "deployment": {
      "architecture": "部署架构",
      "requirements": ["需求1", "需求2"],
      "config_items": [
        {
          "name": "配置项名称",
          "description": "配置项描述",
          "example": "示例值"
        }
      ],
      "docker_support": true
    }
  },
  "conflicts_resolutions": [
    {
      "conflict": "冲突描述",
      "documents": ["文档1", "文档2"],
      "resolution": "解决方案",
      "reason": "原因"
    }
  ],
  "project_management": {
    "collaboration_mode": "协作模式",
    "development_process": "开发流程",
    "testing_strategy": "测试策略",
    "documentation_status": "文档状态"
  },
  "risks_challenges": {
    "technical_risks": ["风险1", "风险2"],
    "known_limitations": ["限制1", "限制2"],
    "scalability_concerns": ["扩展性问题1", "扩展性问题2"],
    "security_analysis": "安全性分析"
  },
  "roadmap": {
    "short_term": {
      "timeframe": "1-3个月",
      "goals": ["目标1", "目标2"]
    },
    "medium_term": {
      "timeframe": "3-6个月",
      "goals": ["目标1", "目标2"]
    },
    "long_term": {
      "vision": "长期愿景",
      "goals": ["目标1", "目标2"]
    },
    "priorities": ["优先级1", "优先级2"]
  },
  "quick_reference": {
    "common_commands": [
      {
        "command": "命令",
        "description": "描述"
      }
    ],
    "important_paths": [
      {
        "path": "路径",
        "description": "描述"
      }
    ],
    "troubleshooting": ["故障排查步骤1", "故障排查步骤2"],
    "resources": ["资源链接1", "资源链接2"]
  },
  "analysis_metadata": {
    "generated_at": "生成时间",
    "documents_analyzed": 0,
    "confidence_level": "高/中/低",
    "key_findings": ["关键发现1", "关键发现2"]
  }
}
```

**注意事项**：
1. 输出必须是合法的JSON格式，不要包含markdown标记或其他文本
2. 所有字符串值都应该是简洁明了的描述
3. 数组元素不宜过多，保持在5-10个以内
4. 如果某个字段没有相关信息，使用空字符串""或空数组[]
5. 时间格式使用 "YYYY-MM-DD" 格式
6. 布尔值使用 true/false，不要用字符串

请确保输出可以被JSON.parse()直接解析。
""")
    
    return '\n'.join(prompt_parts)

def main():
    """主函数"""
    print("🚀 开始生成 Gemini 文档分析提示词...")
    
    # 收集所有 Markdown 文件
    markdown_files = collect_markdown_files(ROOT)
    print(f"📄 找到 {len(markdown_files)} 个 Markdown 文件")
    
    if not markdown_files:
        print("没有找到任何 Markdown 文件")
        return
    
    # 限制文档数量 - Gemini 1.5 Flash 支持 1M tokens
    MAX_DOCS = 50  # 增加到 50 个文档
    MAX_PROMPT_CHARS = 3200000  # 3.2M 字符（约 800k tokens）
    
    if len(markdown_files) > MAX_DOCS:
        print(f"⚠️  文档数量过多，只处理前 {MAX_DOCS} 个高优先级文档")
        markdown_files = markdown_files[:MAX_DOCS]
    
    # 生成提示词
    print("📝 正在生成提示词...")
    prompt = generate_prompt(markdown_files, ROOT)
    
    # 保存提示词
    output_dir = ROOT / 'docs' / 'prompts'
    output_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    prompt_file = output_dir / f'gemini_prompt_{timestamp}.txt'
    prompt_file.write_text(prompt, encoding='utf-8')
    
    # 同时保存为最新版本
    latest_file = output_dir / 'LATEST_GEMINI_PROMPT.txt'
    latest_file.write_text(prompt, encoding='utf-8')
    
    # 统计信息
    total_chars = len(prompt)
    total_tokens = total_chars // 4  # 粗略估算
    
    print(f"\n✅ 提示词已生成并保存到:")
    print(f"   - {prompt_file}")
    print(f"   - {latest_file}")
    print(f"\n📊 统计信息:")
    print(f"   - 总字符数: {total_chars:,}")
    print(f"   - 预估 tokens: {total_tokens:,}")
    print(f"   - 包含文档: {len(markdown_files)} 个")
    
    # 显示优先级文档
    priority_files = [f for f in markdown_files if f[2] > 0]
    if priority_files:
        print(f"\n⭐ 高优先级文档:")
        for file_path, _, _ in priority_files[:5]:
            print(f"   - {file_path.name}")
    
    print("\n💡 使用提示:")
    print("1. 将生成的提示词复制到 Gemini 或其他 AI 模型")
    print("2. 如果提示词过长，可以减少 MAX_DOCS 参数")
    print("3. 生成的总结建议保存到 docs/summaries/ 目录")

if __name__ == '__main__':
    main()