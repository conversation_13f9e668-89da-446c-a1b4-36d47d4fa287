#!/bin/bash

# 修复所有admin页面的顶部padding问题
# 将 style="padding-top: calc(1.5rem + env(safe-area-inset-top));" 移除

echo "开始修复admin页面的顶部padding问题..."

# 获取所有需要修复的文件
files=(
    "views/admin/groups.html"
    "views/admin/advanced-settings.html"
    "views/admin/ssh_scripts.html"
    "views/admin/setting.html"
    "views/admin/servers/edit.html"
    "views/admin/servers/add.html"
    "views/admin/servers.html"
    "views/admin/server.html"
    "views/admin/personalization.html"
    "views/admin/network-quality.html"
    "views/admin/log_management.html"
    "views/admin/license-management.html"
    "views/admin/api_docs.html"
    "views/admin/analytics.html"
)

# 计数器
fixed_count=0

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        # 检查文件是否包含需要修复的代码
        if grep -q 'style="padding-top: calc(1\.5rem + env(safe-area-inset-top));"' "$file"; then
            echo "修复文件: $file"
            # 使用sed进行替换，移除整个style属性
            sed -i '' 's/ style="padding-top: calc(1\.5rem + env(safe-area-inset-top));"//g' "$file"
            ((fixed_count++))
        else
            echo "跳过文件: $file (已修复或无需修复)"
        fi
    else
        echo "警告: 文件不存在 - $file"
    fi
done

echo "修复完成！共修复了 $fixed_count 个文件。"