#!/usr/bin/env node

/**
 * API文档自动生成脚本
 * 使用swagger-autogen自动扫描路由文件并生成OpenAPI文档
 */

const swaggerAutogen = require('swagger-autogen')();

// 输出文件路径
const outputFile = './docs/swagger-output.json';

// 需要扫描的路由文件
const endpointsFiles = [
    './modules/api/index.js',
    './modules/api/tcping.js',
    './modules/api/ping.js',
    './modules/api/performance.js',
    './modules/api/latest.js',
    './modules/api/allnode_status.js',
    './modules/api/load.js',
    './modules/api/load-archive.js',
    './modules/api/api_routes/tcping_routes.js',
    './modules/api/api_routes/server_info_routes.js',
    './modules/api/api_routes/monitor_crud_routes.js',
    './modules/api/api_routes/monitor_data_routes.js',
    './modules/admin/index.js',
    './modules/autodiscovery/index.js'
];

// Swagger文档配置
const doc = {
    info: {
        version: '1.0.0',
        title: 'DStatus API Documentation',
        description: 'DStatus 服务器监控系统 API 文档\n\n这是一个完整的服务器监控系统API，提供了服务器状态监控、网络测试、性能分析等功能。',
        contact: {
            name: 'DStatus Team',
            email: '<EMAIL>',
            url: 'https://github.com/fev125/dstatus'
        },
        license: {
            name: 'MIT',
            url: 'https://opensource.org/licenses/MIT'
        }
    },
    host: 'localhost:5555',
    basePath: '/',
    schemes: ['http', 'https'],
    consumes: ['application/json'],
    produces: ['application/json'],
    tags: [
        {
            name: 'Authentication',
            description: '认证相关接口'
        },
        {
            name: 'Server Management',
            description: '服务器管理接口'
        },
        {
            name: 'Monitoring',
            description: '监控相关接口'
        },
        {
            name: 'Network Testing',
            description: '网络测试接口'
        },
        {
            name: 'Performance',
            description: '性能监控接口'
        },
        {
            name: 'Statistics',
            description: '统计数据接口'
        },
        {
            name: 'Administration',
            description: '管理员接口'
        },
        {
            name: 'Auto Discovery',
            description: '自动发现接口'
        }
    ],
    securityDefinitions: {
        ApiKeyAuth: {
            type: 'apiKey',
            in: 'query',
            name: 'key',
            description: 'API密钥认证'
        },
        AdminAuth: {
            type: 'apiKey',
            in: 'header',
            name: 'Cookie',
            description: '管理员Cookie认证'
        },
        BearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            description: 'JWT Bearer Token认证'
        }
    },
    definitions: {
        ApiResponse: {
            type: 'object',
            properties: {
                success: {
                    type: 'boolean',
                    description: '请求是否成功'
                },
                message: {
                    type: 'string',
                    description: '响应消息'
                },
                data: {
                    type: 'object',
                    description: '响应数据'
                }
            }
        },
        AdminResponse: {
            type: 'object',
            properties: {
                status: {
                    type: 'integer',
                    description: '状态码 (1=成功, 0=失败)'
                },
                data: {
                    type: 'object',
                    description: '响应数据'
                },
                msg: {
                    type: 'string',
                    description: '响应消息'
                }
            }
        },
        ServerInfo: {
            type: 'object',
            properties: {
                id: {
                    type: 'string',
                    description: '服务器ID'
                },
                sid: {
                    type: 'string',
                    description: '服务器SID'
                },
                name: {
                    type: 'string',
                    description: '服务器名称'
                },
                ip: {
                    type: 'string',
                    description: '服务器IP地址'
                },
                disabled: {
                    type: 'boolean',
                    description: '是否禁用'
                },
                data: {
                    type: 'object',
                    properties: {
                        api: {
                            type: 'object',
                            properties: {
                                mode: {
                                    type: 'boolean',
                                    description: 'API模式'
                                },
                                key: {
                                    type: 'string',
                                    description: 'API密钥'
                                },
                                port: {
                                    type: 'integer',
                                    description: 'API端口'
                                }
                            }
                        },
                        metadata: {
                            type: 'object',
                            properties: {
                                region: {
                                    type: 'string',
                                    description: '服务器地区'
                                }
                            }
                        }
                    }
                }
            }
        },
        TcpingResult: {
            type: 'object',
            properties: {
                host: {
                    type: 'string',
                    description: '目标主机'
                },
                port: {
                    type: 'integer',
                    description: '目标端口'
                },
                success_rate: {
                    type: 'number',
                    description: '成功率 (0-1)'
                },
                avg_time: {
                    type: 'number',
                    description: '平均响应时间 (毫秒)'
                },
                min_time: {
                    type: 'number',
                    description: '最小响应时间 (毫秒)'
                },
                max_time: {
                    type: 'number',
                    description: '最大响应时间 (毫秒)'
                },
                results: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            success: {
                                type: 'boolean',
                                description: '是否成功'
                            },
                            time: {
                                type: 'number',
                                description: '响应时间 (毫秒)'
                            },
                            error: {
                                type: 'string',
                                description: '错误信息'
                            }
                        }
                    }
                }
            }
        },
        PingResult: {
            type: 'object',
            properties: {
                host: {
                    type: 'string',
                    description: '目标主机'
                },
                success_rate: {
                    type: 'number',
                    description: '成功率 (0-1)'
                },
                avg_time: {
                    type: 'number',
                    description: '平均响应时间 (毫秒)'
                },
                min_time: {
                    type: 'number',
                    description: '最小响应时间 (毫秒)'
                },
                max_time: {
                    type: 'number',
                    description: '最大响应时间 (毫秒)'
                },
                packet_loss: {
                    type: 'number',
                    description: '丢包率 (0-1)'
                },
                results: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            success: {
                                type: 'boolean',
                                description: '是否成功'
                            },
                            time: {
                                type: 'number',
                                description: '响应时间 (毫秒)'
                            },
                            error: {
                                type: 'string',
                                description: '错误信息'
                            }
                        }
                    }
                }
            }
        },
        PerformanceData: {
            type: 'object',
            properties: {
                timestamp: {
                    type: 'integer',
                    description: '时间戳'
                },
                cpu: {
                    type: 'number',
                    description: 'CPU使用率 (%)'
                },
                memory: {
                    type: 'number',
                    description: '内存使用率 (%)'
                },
                disk: {
                    type: 'number',
                    description: '磁盘使用率 (%)'
                },
                network: {
                    type: 'object',
                    properties: {
                        rx: {
                            type: 'number',
                            description: '接收字节数'
                        },
                        tx: {
                            type: 'number',
                            description: '发送字节数'
                        }
                    }
                }
            }
        },
        MonitorRegion: {
            type: 'object',
            properties: {
                id: {
                    type: 'string',
                    description: '地区ID'
                },
                name: {
                    type: 'string',
                    description: '地区名称'
                },
                description: {
                    type: 'string',
                    description: '地区描述'
                }
            }
        },
        MonitorTarget: {
            type: 'object',
            properties: {
                id: {
                    type: 'string',
                    description: '目标ID'
                },
                region_id: {
                    type: 'string',
                    description: '地区ID'
                },
                name: {
                    type: 'string',
                    description: '目标名称'
                },
                host: {
                    type: 'string',
                    description: '目标主机'
                },
                port: {
                    type: 'integer',
                    description: '目标端口'
                },
                description: {
                    type: 'string',
                    description: '目标描述'
                },
                mode: {
                    type: 'string',
                    enum: ['all', 'specific'],
                    description: '监控模式'
                },
                node_ids: {
                    type: 'array',
                    items: {
                        type: 'string'
                    },
                    description: '指定节点ID列表'
                },
                test_type: {
                    type: 'string',
                    enum: ['tcping', 'ping'],
                    description: '测试类型'
                }
            }
        },
        LogStats: {
            type: 'object',
            properties: {
                totalFiles: {
                    type: 'integer',
                    description: '日志文件总数'
                },
                totalSize: {
                    type: 'integer',
                    description: '日志文件总大小 (字节)'
                },
                totalSizeFormatted: {
                    type: 'string',
                    description: '格式化的总大小'
                },
                files: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            path: {
                                type: 'string',
                                description: '文件路径'
                            },
                            name: {
                                type: 'string',
                                description: '文件名'
                            },
                            size: {
                                type: 'integer',
                                description: '文件大小 (字节)'
                            },
                            sizeFormatted: {
                                type: 'string',
                                description: '格式化的文件大小'
                            },
                            modified: {
                                type: 'string',
                                description: '修改时间'
                            },
                            type: {
                                type: 'string',
                                description: '日志类型'
                            }
                        }
                    }
                }
            }
        },
        Error: {
            type: 'object',
            properties: {
                success: {
                    type: 'boolean',
                    example: false
                },
                message: {
                    type: 'string',
                    description: '错误信息'
                },
                error: {
                    type: 'string',
                    description: '详细错误信息'
                }
            }
        }
    }
};

console.log('🚀 开始生成API文档...');
console.log('📁 扫描路由文件:', endpointsFiles.length, '个');
console.log('📄 输出文件:', outputFile);

swaggerAutogen(outputFile, endpointsFiles, doc).then(() => {
    console.log('✅ API文档生成完成!');
    console.log('📖 文档文件已保存到:', outputFile);
    console.log('🌐 启动服务器后可访问: http://localhost:5555/api-docs');
}).catch(err => {
    console.error('❌ API文档生成失败:', err);
    process.exit(1);
}); 