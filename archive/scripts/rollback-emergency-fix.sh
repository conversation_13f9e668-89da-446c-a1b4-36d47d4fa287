#!/bin/bash

# 紧急止血措施回滚脚本
# 用于在出现问题时快速回滚所有Phase 0的修改

echo "======================================"
echo "    紧急止血措施回滚脚本"
echo "======================================"
echo ""
echo "⚠️  警告：此脚本将回滚所有紧急措施！"
echo ""
read -p "确定要继续吗？(yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "回滚已取消"
    exit 0
fi

echo ""
echo "开始回滚..."

# 创建备份目录
backup_dir="./backups/rollback-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$backup_dir"

# 备份当前文件
echo "1. 备份当前文件..."
cp nekonekostatus.js "$backup_dir/"
cp database/adapters/postgresql.js "$backup_dir/"
cp modules/stats/index.js "$backup_dir/"

# 使用git回滚文件
echo ""
echo "2. 使用git回滚文件..."
git checkout HEAD -- nekonekostatus.js
git checkout HEAD -- database/adapters/postgresql.js
git checkout HEAD -- modules/stats/index.js

echo ""
echo "3. 验证回滚结果..."

# 检查关键修改是否已回滚
if grep -q "MAX_WEBSOCKET_CONNECTIONS = 20" nekonekostatus.js; then
    echo "❌ WebSocket限制仍然存在"
else
    echo "✅ WebSocket限制已回滚"
fi

if grep -q "UPDATE_INTERVAL = 10000" nekonekostatus.js; then
    echo "❌ 更新间隔仍为10秒"
else
    echo "✅ 更新间隔已回滚"
fi

if grep -q "max: 40," database/adapters/postgresql.js; then
    echo "❌ 数据库连接池仍为40"
else
    echo "✅ 数据库连接池已回滚"
fi

echo ""
echo "======================================"
echo "回滚完成！"
echo ""
echo "备份文件保存在: $backup_dir"
echo ""
echo "请执行以下操作："
echo "1. pm2 restart dzstatus"
echo "2. pm2 logs dzstatus --lines 100"
echo "3. 监控系统状态"
echo "======================================"