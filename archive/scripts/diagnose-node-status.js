#!/usr/bin/env node
/**
 * 节点状态综合诊断工具
 * 
 * 用于诊断tcping功能中"监控目标没有可用的监控节点"问题
 * 检查所有节点的在线状态、API配置、冷却状态等信息
 * 
 * 运行方式：node scripts/diagnose-node-status.js
 */

const { getNodeCoolingStatus, isNodeCooling } = require('../modules/api/cooling_logic');

// 数据库初始化
let db = null;

async function initDatabase() {
    if (!db) {
        const dbFactory = require('../database');
        db = await dbFactory();
    }
    return db;
}

// 模拟scheduler中的在线状态检查逻辑
function checkOnlineStatus(server, statsData) {
    const serverStats = statsData[server.sid];
    if (!serverStats) {
        return { online: false, reason: 'statsData中无此节点记录' };
    }
    
    if (serverStats.stat === false) {
        return { online: false, reason: 'stat为false（连接失败）' };
    }
    
    if (serverStats.stat && serverStats.stat.offline) {
        return { online: false, reason: 'stat.offline为true' };
    }
    
    return { online: true, reason: '在线状态正常' };
}

// 检查API配置
function checkApiConfig(server) {
    if (!server.data) {
        return { valid: false, reason: 'server.data不存在' };
    }
    
    if (!server.data.api) {
        return { valid: false, reason: 'server.data.api不存在' };
    }
    
    if (!server.data.api.key) {
        return { valid: false, reason: 'API密钥缺失' };
    }
    
    const mode = server.data.api.mode;
    const modeText = mode === true ? '主动模式' : mode === false ? '被动模式' : '未定义';
    
    return { 
        valid: true, 
        reason: `API配置完整 (${modeText})`,
        mode: mode,
        key: server.data.api.key.substring(0, 8) + '...',
        port: server.data.api.port || '未设置'
    };
}

// 检查冷却状态
function checkCoolingStatus(server) {
    const cooling = isNodeCooling(server.sid);
    if (!cooling) {
        return { cooling: false, reason: '未处于冷却状态' };
    }
    
    const coolingStatus = getNodeCoolingStatus();
    const status = coolingStatus[server.sid];
    if (status) {
        return { 
            cooling: true, 
            reason: `冷却中 (失败${status.failCount}次, 剩余${status.remainingTime}分钟)` 
        };
    }
    
    return { cooling: true, reason: '冷却状态异常（无详细信息）' };
}

// 模拟scheduler的节点过滤逻辑
function simulateSchedulerFiltering(server, statsData) {
    const results = {
        server: {
            sid: server.sid,
            name: server.name,
            status: server.status,
            disabled: server.disabled || false
        },
        checks: {},
        finalResult: { available: false, reason: '' }
    };
    
    // 1. 检查服务器状态
    if (server.status <= 0) {
        results.checks.serverStatus = { pass: false, reason: `服务器状态为${server.status}（已禁用）` };
        results.finalResult = { available: false, reason: '服务器已禁用' };
        return results;
    }
    results.checks.serverStatus = { pass: true, reason: '服务器状态正常' };
    
    // 2. 检查在线状态
    const onlineCheck = checkOnlineStatus(server, statsData);
    results.checks.onlineStatus = { pass: onlineCheck.online, reason: onlineCheck.reason };
    
    // 3. 检查冷却状态
    const coolingCheck = checkCoolingStatus(server);
    results.checks.coolingStatus = { pass: !coolingCheck.cooling, reason: coolingCheck.reason };
    
    // 4. 检查API配置
    const apiCheck = checkApiConfig(server);
    results.checks.apiConfig = { 
        pass: apiCheck.valid, 
        reason: apiCheck.reason,
        details: apiCheck.valid ? {
            mode: apiCheck.mode,
            key: apiCheck.key,
            port: apiCheck.port
        } : null
    };
    
    // 5. 综合判断（模拟scheduler逻辑）
    const allChecks = [
        results.checks.serverStatus.pass,
        results.checks.onlineStatus.pass,
        results.checks.coolingStatus.pass,
        results.checks.apiConfig.pass
    ];
    
    if (allChecks.every(check => check)) {
        results.finalResult = { available: true, reason: '所有检查通过，节点可用' };
    } else {
        const failedChecks = [];
        if (!results.checks.serverStatus.pass) failedChecks.push('服务器状态');
        if (!results.checks.onlineStatus.pass) failedChecks.push('在线状态');
        if (!results.checks.coolingStatus.pass) failedChecks.push('冷却状态');
        if (!results.checks.apiConfig.pass) failedChecks.push('API配置');
        
        results.finalResult = { 
            available: false, 
            reason: `检查失败: ${failedChecks.join(', ')}` 
        };
    }
    
    return results;
}

async function displayDiagnosticReport() {
    console.clear();
    console.log('=== 节点状态综合诊断报告 ===');
    console.log('时间:', new Date().toLocaleString());
    console.log('');
    
    try {
        // 初始化数据库
        console.log('正在初始化数据库...');
        await initDatabase();

        // 获取数据
        console.log('正在获取数据...');
        const servers = await db.getServers();
        
        // 获取statsData（通过API调用，更准确）
        let statsData = {};
        let statsModuleInfo = { available: false, cacheSize: 0, statsSize: 0 };

        try {
            console.log('📡 通过API获取statsData...');
            const fetch = require('node-fetch');
            const response = await fetch('http://localhost:5555/api/allnode_status');
            const apiData = await response.json();

            if (apiData.success && apiData.data) {
                statsData = apiData.data;
                statsModuleInfo.available = true;
                statsModuleInfo.statsSize = Object.keys(statsData).length;
                console.log(`✅ 通过API成功获取 ${statsModuleInfo.statsSize} 条statsData记录`);
            } else {
                console.warn('⚠️  API返回失败或无数据');
            }

        } catch (error) {
            console.warn('⚠️  通过API获取statsData失败:', error.message);

            // 降级：尝试直接require stats模块
            try {
                console.log('📡 降级：尝试直接require stats模块...');
                const statsModule = require('../modules/stats');
                if (statsModule && typeof statsModule.getStatsData === 'function') {
                    statsData = await statsModule.getStatsData(true);
                    statsModuleInfo.available = true;
                    statsModuleInfo.statsSize = Object.keys(statsData).length;
                }
            } catch (requireError) {
                console.warn('⚠️  直接require也失败:', requireError.message);
            }
        }
        
        console.log(`\n📊 数据概览:`);
        console.log(`- 总服务器数: ${servers.length}`);
        console.log(`- statsData记录数: ${Object.keys(statsData).length}`);
        console.log(`- stats模块状态: ${statsModuleInfo.available ? '✅ 可用' : '❌ 不可用'}`);
        if (statsModuleInfo.available) {
            console.log(`- 内部stats记录数: ${statsModuleInfo.statsSize}`);
        }
        console.log('');
        
        // 统计信息
        let totalServers = 0;
        let enabledServers = 0;
        let availableNodes = 0;
        let onlineNodes = 0;
        let coolingNodes = 0;
        let apiConfiguredNodes = 0;
        
        const diagnosticResults = [];
        
        // 诊断每个服务器
        for (const server of servers) {
            totalServers++;
            
            if (server.status > 0) {
                enabledServers++;
                
                const result = simulateSchedulerFiltering(server, statsData);
                diagnosticResults.push(result);
                
                if (result.checks.onlineStatus?.pass) onlineNodes++;
                if (!result.checks.coolingStatus?.pass) coolingNodes++;
                if (result.checks.apiConfig?.pass) apiConfiguredNodes++;
                if (result.finalResult.available) availableNodes++;
            }
        }
        
        // 显示统计信息
        console.log('📈 统计信息:');
        console.log('-'.repeat(60));
        console.log(`总服务器数:     ${totalServers}`);
        console.log(`启用的服务器:   ${enabledServers}`);
        console.log(`在线节点:       ${onlineNodes}`);
        console.log(`API配置完整:    ${apiConfiguredNodes}`);
        console.log(`冷却中节点:     ${coolingNodes}`);
        console.log(`最终可用节点:   ${availableNodes}`);
        console.log('');
        
        // 显示详细诊断结果
        console.log('🔍 详细诊断结果:');
        console.log('-'.repeat(120));
        console.log('节点名称'.padEnd(25) + '状态'.padEnd(8) + '在线'.padEnd(8) + '冷却'.padEnd(8) + 'API'.padEnd(8) + '最终结果'.padEnd(12) + '失败原因');
        console.log('-'.repeat(120));
        
        for (const result of diagnosticResults) {
            const server = result.server;
            const checks = result.checks;
            const final = result.finalResult;
            
            const statusIcon = checks.serverStatus?.pass ? '✅' : '❌';
            const onlineIcon = checks.onlineStatus?.pass ? '✅' : '❌';
            const coolingIcon = checks.coolingStatus?.pass ? '✅' : '❌';
            const apiIcon = checks.apiConfig?.pass ? '✅' : '❌';
            const finalIcon = final.available ? '🟢' : '🔴';
            
            const color = final.available ? '\x1b[32m' : '\x1b[31m';
            const reset = '\x1b[0m';
            
            console.log(
                `${color}${server.name.padEnd(25)}${statusIcon.padEnd(8)}${onlineIcon.padEnd(8)}${coolingIcon.padEnd(8)}${apiIcon.padEnd(8)}${finalIcon.padEnd(12)}${final.reason}${reset}`
            );
        }
        
        console.log('-'.repeat(120));
        
        // 显示问题节点的详细信息
        const problemNodes = diagnosticResults.filter(r => !r.finalResult.available);
        if (problemNodes.length > 0) {
            console.log('\n🚨 问题节点详细信息:');
            console.log('-'.repeat(80));
            
            for (const node of problemNodes.slice(0, 10)) { // 只显示前10个问题节点
                console.log(`\n节点: ${node.server.name} (${node.server.sid})`);
                console.log(`  服务器状态: ${node.checks.serverStatus?.reason || 'N/A'}`);
                console.log(`  在线状态: ${node.checks.onlineStatus?.reason || 'N/A'}`);
                console.log(`  冷却状态: ${node.checks.coolingStatus?.reason || 'N/A'}`);
                console.log(`  API配置: ${node.checks.apiConfig?.reason || 'N/A'}`);
                if (node.checks.apiConfig?.details) {
                    const details = node.checks.apiConfig.details;
                    console.log(`    - 模式: ${details.mode === true ? '主动' : details.mode === false ? '被动' : '未定义'}`);
                    console.log(`    - 密钥: ${details.key}`);
                    console.log(`    - 端口: ${details.port}`);
                }
            }
            
            if (problemNodes.length > 10) {
                console.log(`\n... 还有 ${problemNodes.length - 10} 个问题节点未显示`);
            }
        }
        
        // 针对特定监控目标的分析
        console.log('\n🎯 特定监控目标分析:');
        console.log('-'.repeat(60));
        
        // 分析target_bfa6018345120ff4 (模式: all)
        console.log('\n目标: target_bfa6018345120ff4 (广州移动, 模式: all)');
        console.log(`  需要: 任意可用节点`);
        console.log(`  结果: ${availableNodes > 0 ? '✅ 有可用节点' : '❌ 无可用节点'} (${availableNodes}个)`);
        
        // 分析target_0ef9fb170aa15235 (模式: specific)
        console.log('\n目标: target_0ef9fb170aa15235 (广州cn2_ping, 模式: specific)');
        console.log(`  需要: 指定的特定节点可用`);
        console.log(`  注意: 需要检查具体指定的节点ID列表`);
        
        console.log('\n💡 建议:');
        if (availableNodes === 0) {
            console.log('- 🔴 当前没有可用节点，这解释了为什么监控目标找不到可用节点');
            console.log('- 🔧 请检查上述问题节点的详细信息，重点关注API配置和在线状态');
        } else {
            console.log(`- 🟢 当前有 ${availableNodes} 个可用节点`);
            console.log('- 🔍 如果仍然报告无可用节点，可能是scheduler逻辑或特定目标配置问题');
        }
        
        console.log('\n按 Ctrl+C 退出诊断');
        
    } catch (error) {
        console.error('❌ 诊断过程中出错:', error);
        console.error('详细错误:', error.stack);
    }
}

// 主函数
async function main() {
    try {
        await displayDiagnosticReport();
        
        // 提供选项：一次性运行或持续监控
        const args = process.argv.slice(2);
        if (args.includes('--watch') || args.includes('-w')) {
            console.log('\n🔄 持续监控模式，每30秒刷新一次...');
            setInterval(async () => {
                await displayDiagnosticReport();
            }, 30000);
        } else {
            console.log('\n✅ 诊断完成。使用 --watch 参数可启用持续监控模式。');
            process.exit(0);
        }
        
    } catch (error) {
        console.error('❌ 诊断工具启动失败:', error);
        process.exit(1);
    }
}

// 优雅退出
process.on('SIGINT', () => {
    console.log('\n👋 诊断工具已停止');
    process.exit(0);
});

// 运行主函数
main();
