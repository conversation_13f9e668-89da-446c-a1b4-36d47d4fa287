#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const { promisify } = require('util');

class PerformanceMonitor {
    constructor() {
        this.dbPath = path.join(__dirname, '../data/db.db');
        this.logPath = path.join(__dirname, '../data/logs/performance-monitor.log');
        this.metricsPath = path.join(__dirname, '../data/performance-metrics.json');
        this.isRunning = false;
        this.interval = 60000; // 1分钟
        this.history = [];
        this.loadHistory();
    }

    loadHistory() {
        try {
            if (fs.existsSync(this.metricsPath)) {
                const data = fs.readFileSync(this.metricsPath, 'utf8');
                this.history = JSON.parse(data);
            }
        } catch (error) {
            console.error('加载历史数据失败:', error);
            this.history = [];
        }
    }

    saveHistory() {
        try {
            fs.writeFileSync(this.metricsPath, JSON.stringify(this.history, null, 2));
        } catch (error) {
            console.error('保存历史数据失败:', error);
        }
    }

    log(message) {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] ${message}`;
        
        console.log(logEntry);
        
        try {
            fs.appendFileSync(this.logPath, logEntry + '\n');
        } catch (error) {
            console.error('写入日志失败:', error);
        }
    }

    async collectMetrics() {
        const metrics = {
            timestamp: new Date().toISOString(),
            epochTime: Date.now(),
            files: await this.getFileSizes(),
            database: await this.getDatabaseMetrics(),
            queries: await this.measureQueryPerformance(),
            system: await this.getSystemMetrics()
        };

        // 计算衍生指标
        metrics.calculated = {
            walToDbRatio: metrics.files.wal / metrics.files.db,
            totalDbSize: metrics.files.db + metrics.files.wal + metrics.files.shm
        };
        
        // 计算健康评分
        metrics.calculated.walHealthScore = this.calculateWalHealthScore(metrics);
        metrics.calculated.performanceScore = this.calculatePerformanceScore(metrics);

        return metrics;
    }

    async getFileSizes() {
        const getSize = (filePath) => {
            try {
                return fs.statSync(filePath).size;
            } catch (err) {
                return 0;
            }
        };

        return {
            db: getSize(this.dbPath),
            wal: getSize(this.dbPath + '-wal'),
            shm: getSize(this.dbPath + '-shm')
        };
    }

    async getDatabaseMetrics() {
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(this.dbPath, sqlite3.OPEN_READONLY);
            
            const queries = [
                'PRAGMA page_count',
                'PRAGMA freelist_count',
                'PRAGMA cache_size',
                'PRAGMA wal_autocheckpoint',
                'PRAGMA synchronous',
                'PRAGMA journal_mode'
            ];

            const results = {};
            let completed = 0;

            queries.forEach(query => {
                const pragma = query.replace('PRAGMA ', '');
                db.get(query, (err, row) => {
                    if (err) {
                        results[pragma] = null;
                    } else {
                        results[pragma] = Object.values(row)[0];
                    }
                    
                    completed++;
                    if (completed === queries.length) {
                        db.close();
                        resolve(results);
                    }
                });
            });
        });
    }

    async measureQueryPerformance() {
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(this.dbPath, sqlite3.OPEN_READONLY);
            
            const testQueries = [
                {
                    name: 'count_tcping',
                    sql: 'SELECT COUNT(*) as count FROM tcping_m',
                    timeout: 5000
                },
                {
                    name: 'count_load',
                    sql: 'SELECT COUNT(*) as count FROM load_m',
                    timeout: 5000
                },
                {
                    name: 'recent_servers',
                    sql: 'SELECT COUNT(*) as count FROM servers WHERE last_online > (strftime("%s", "now") - 86400)',
                    timeout: 5000
                },
                {
                    name: 'avg_cpu_recent',
                    sql: 'SELECT AVG(cpu) as avg_cpu FROM load_m WHERE created_at > (strftime("%s", "now") - 3600)',
                    timeout: 5000
                }
            ];

            const results = {};
            let completed = 0;

            testQueries.forEach(query => {
                const startTime = Date.now();
                let timedOut = false;
                
                // 设置超时
                const timeout = setTimeout(() => {
                    timedOut = true;
                    results[query.name] = {
                        duration: query.timeout,
                        error: 'TIMEOUT',
                        success: false
                    };
                    
                    completed++;
                    if (completed === testQueries.length) {
                        db.close();
                        resolve(results);
                    }
                }, query.timeout);

                db.get(query.sql, (err, row) => {
                    if (timedOut) return;
                    
                    clearTimeout(timeout);
                    const duration = Date.now() - startTime;
                    
                    if (err) {
                        results[query.name] = {
                            duration: duration,
                            error: err.message,
                            success: false
                        };
                    } else {
                        results[query.name] = {
                            duration: duration,
                            result: row,
                            success: true
                        };
                    }
                    
                    completed++;
                    if (completed === testQueries.length) {
                        db.close();
                        resolve(results);
                    }
                });
            });
        });
    }

    async getSystemMetrics() {
        const { exec } = require('child_process');
        const execPromise = promisify(exec);
        
        try {
            // 获取系统内存信息
            const memInfo = await execPromise('vm_stat');
            const memory = this.parseMemoryInfo(memInfo.stdout);
            
            // 获取磁盘使用情况
            const diskInfo = await execPromise(`df -h "${path.dirname(this.dbPath)}"`);
            const disk = this.parseDiskInfo(diskInfo.stdout);
            
            // 获取进程信息
            const processInfo = await execPromise('ps -o pid,vsz,rss,comm | grep -E "(node|sqlite)" | head -10');
            const processes = this.parseProcessInfo(processInfo.stdout);
            
            return {
                memory,
                disk,
                processes,
                loadAverage: require('os').loadavg(),
                uptime: require('os').uptime()
            };
        } catch (error) {
            this.log(`获取系统指标失败: ${error.message}`);
            return {
                memory: {},
                disk: {},
                processes: [],
                loadAverage: [0, 0, 0],
                uptime: 0
            };
        }
    }

    parseMemoryInfo(vmStatOutput) {
        const lines = vmStatOutput.split('\n');
        const memory = {};
        
        lines.forEach(line => {
            if (line.includes('Pages free:')) {
                memory.free = parseInt(line.split(':')[1].trim().replace('.', ''));
            } else if (line.includes('Pages active:')) {
                memory.active = parseInt(line.split(':')[1].trim().replace('.', ''));
            } else if (line.includes('Pages inactive:')) {
                memory.inactive = parseInt(line.split(':')[1].trim().replace('.', ''));
            }
        });
        
        // 转换为MB (假设页面大小为4KB)
        const pageSize = 4096;
        Object.keys(memory).forEach(key => {
            memory[key] = Math.round(memory[key] * pageSize / 1024 / 1024);
        });
        
        return memory;
    }

    parseDiskInfo(dfOutput) {
        const lines = dfOutput.split('\n');
        if (lines.length < 2) return {};
        
        const dataLine = lines[1].split(/\s+/);
        return {
            filesystem: dataLine[0],
            size: dataLine[1],
            used: dataLine[2],
            available: dataLine[3],
            usedPercentage: dataLine[4],
            mountpoint: dataLine[5]
        };
    }

    parseProcessInfo(psOutput) {
        const lines = psOutput.split('\n').filter(line => line.trim());
        return lines.map(line => {
            const parts = line.trim().split(/\s+/);
            return {
                pid: parseInt(parts[0]),
                vsz: parseInt(parts[1]), // Virtual memory size in KB
                rss: parseInt(parts[2]), // Resident set size in KB
                command: parts[3]
            };
        });
    }

    calculateWalHealthScore(metrics) {
        const ratio = metrics.calculated.walToDbRatio;
        
        if (ratio < 0.1) return 100;      // 优秀
        if (ratio < 0.3) return 80;       // 良好
        if (ratio < 0.5) return 60;       // 一般
        if (ratio < 1.0) return 40;       // 较差
        return 20;                        // 很差
    }

    calculatePerformanceScore(metrics) {
        let score = 100;
        
        // 查询性能评分
        const queryTimes = Object.values(metrics.queries)
            .filter(q => q.success)
            .map(q => q.duration);
        
        if (queryTimes.length > 0) {
            const avgQueryTime = queryTimes.reduce((a, b) => a + b, 0) / queryTimes.length;
            
            if (avgQueryTime > 1000) score -= 30;      // 超过1秒
            else if (avgQueryTime > 500) score -= 20;  // 超过500ms
            else if (avgQueryTime > 100) score -= 10;  // 超过100ms
        }
        
        // WAL健康评分
        const walScore = this.calculateWalHealthScore(metrics);
        score = Math.min(score, walScore);
        
        // 文件大小评分
        const totalSize = metrics.calculated.totalDbSize;
        if (totalSize > 1024 * 1024 * 1024) score -= 20; // 超过1GB
        else if (totalSize > 500 * 1024 * 1024) score -= 10; // 超过500MB
        
        return Math.max(0, score);
    }

    async analyzePerformanceTrends() {
        if (this.history.length < 2) {
            return { trend: 'insufficient_data', message: '需要更多数据来分析趋势' };
        }

        const recent = this.history.slice(-10); // 最近10个数据点
        const trends = {
            walSize: this.calculateTrend(recent.map(m => m.files.wal)),
            queryPerformance: this.calculateQueryTrend(recent),
            healthScore: this.calculateTrend(recent.map(m => m.calculated.performanceScore))
        };

        return trends;
    }

    calculateTrend(values) {
        if (values.length < 2) return 0;
        
        const n = values.length;
        const sumX = n * (n - 1) / 2;
        const sumY = values.reduce((a, b) => a + b, 0);
        const sumXY = values.reduce((acc, val, idx) => acc + val * idx, 0);
        const sumXX = n * (n - 1) * (2 * n - 1) / 6;
        
        const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        
        return slope;
    }

    calculateQueryTrend(metrics) {
        const queryNames = ['count_tcping', 'count_load', 'recent_servers', 'avg_cpu_recent'];
        const trends = {};
        
        queryNames.forEach(queryName => {
            const times = metrics
                .filter(m => m.queries[queryName] && m.queries[queryName].success)
                .map(m => m.queries[queryName].duration);
            
            if (times.length > 1) {
                trends[queryName] = this.calculateTrend(times);
            }
        });
        
        return trends;
    }

    async generateAlert(metrics) {
        const alerts = [];
        
        // WAL文件大小警告
        if (metrics.calculated.walToDbRatio > 0.5) {
            alerts.push({
                level: 'WARNING',
                type: 'WAL_SIZE',
                message: `WAL文件过大: ${(metrics.calculated.walToDbRatio * 100).toFixed(1)}% of DB size`,
                recommendation: 'Execute PRAGMA wal_checkpoint(FULL)'
            });
        }
        
        // 查询性能警告
        const slowQueries = Object.entries(metrics.queries)
            .filter(([name, query]) => query.success && query.duration > 1000);
        
        if (slowQueries.length > 0) {
            alerts.push({
                level: 'WARNING',
                type: 'SLOW_QUERY',
                message: `发现慢查询: ${slowQueries.map(([name]) => name).join(', ')}`,
                recommendation: 'Check query optimization and consider adding indexes'
            });
        }
        
        // 数据库大小警告
        if (metrics.calculated.totalDbSize > 1024 * 1024 * 1024) {
            alerts.push({
                level: 'INFO',
                type: 'DB_SIZE',
                message: `数据库总大小: ${this.formatBytes(metrics.calculated.totalDbSize)}`,
                recommendation: 'Consider data archiving or cleanup'
            });
        }
        
        return alerts;
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async start() {
        if (this.isRunning) {
            this.log('监控已在运行中');
            return;
        }

        this.isRunning = true;
        this.log('启动性能监控...');

        const monitorLoop = async () => {
            try {
                const metrics = await this.collectMetrics();
                this.history.push(metrics);
                
                // 保留最近1000个数据点
                if (this.history.length > 1000) {
                    this.history = this.history.slice(-1000);
                }
                
                this.saveHistory();
                
                // 生成警告
                const alerts = await this.generateAlert(metrics);
                if (alerts.length > 0) {
                    alerts.forEach(alert => {
                        this.log(`[${alert.level}] ${alert.type}: ${alert.message}`);
                    });
                }
                
                // 分析趋势
                const trends = await this.analyzePerformanceTrends();
                
                // 记录关键指标
                this.log(`指标收集完成 - WAL比例: ${(metrics.calculated.walToDbRatio * 100).toFixed(1)}%, 性能评分: ${metrics.calculated.performanceScore}`);
                
                if (this.isRunning) {
                    setTimeout(monitorLoop, this.interval);
                }
            } catch (error) {
                this.log(`监控错误: ${error.message}`);
                if (this.isRunning) {
                    setTimeout(monitorLoop, this.interval);
                }
            }
        };

        monitorLoop();
    }

    stop() {
        this.isRunning = false;
        this.log('停止性能监控');
    }

    async generateReport() {
        const recent = this.history.slice(-24); // 最近24个数据点
        if (recent.length === 0) {
            return { error: '没有可用数据' };
        }

        const latest = recent[recent.length - 1];
        const trends = await this.analyzePerformanceTrends();
        
        const report = {
            timestamp: new Date().toISOString(),
            current: {
                walHealth: this.calculateWalHealthScore(latest),
                performanceScore: latest.calculated.performanceScore,
                walSize: this.formatBytes(latest.files.wal),
                dbSize: this.formatBytes(latest.files.db),
                totalSize: this.formatBytes(latest.calculated.totalDbSize)
            },
            trends,
            alerts: await this.generateAlert(latest),
            recommendations: this.generateRecommendations(recent),
            dataPoints: recent.length,
            monitoringPeriod: recent.length > 1 ? 
                `${Math.round((latest.epochTime - recent[0].epochTime) / 60000)} minutes` : 
                '1 data point'
        };

        return report;
    }

    generateRecommendations(recentData) {
        const recommendations = [];
        
        if (recentData.length === 0) return recommendations;
        
        const latest = recentData[recentData.length - 1];
        
        // WAL文件建议
        if (latest.calculated.walToDbRatio > 0.3) {
            recommendations.push({
                priority: 'HIGH',
                category: 'WAL Management',
                action: 'Implement more frequent checkpoints',
                reason: `WAL文件比例过高: ${(latest.calculated.walToDbRatio * 100).toFixed(1)}%`
            });
        }
        
        // 查询性能建议
        const avgQueryTime = Object.values(latest.queries)
            .filter(q => q.success)
            .reduce((sum, q, _, arr) => sum + q.duration / arr.length, 0);
        
        if (avgQueryTime > 200) {
            recommendations.push({
                priority: 'MEDIUM',
                category: 'Query Performance',
                action: 'Review and optimize slow queries',
                reason: `平均查询时间: ${avgQueryTime.toFixed(1)}ms`
            });
        }
        
        // 数据库大小建议
        if (latest.calculated.totalDbSize > 500 * 1024 * 1024) {
            recommendations.push({
                priority: 'MEDIUM',
                category: 'Database Size',
                action: 'Consider data archiving or cleanup',
                reason: `总数据库大小: ${this.formatBytes(latest.calculated.totalDbSize)}`
            });
        }
        
        return recommendations;
    }
}

// 信号处理
let monitor;

process.on('SIGINT', () => {
    console.log('\n收到中断信号，正在停止监控...');
    if (monitor) {
        monitor.stop();
    }
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n收到终止信号，正在停止监控...');
    if (monitor) {
        monitor.stop();
    }
    process.exit(0);
});

// 命令行参数处理
if (require.main === module) {
    const args = process.argv.slice(2);
    monitor = new PerformanceMonitor();
    
    if (args.includes('--report')) {
        // 生成报告
        monitor.generateReport().then(report => {
            console.log('📊 性能监控报告:');
            console.log(JSON.stringify(report, null, 2));
        }).catch(console.error);
    } else if (args.includes('--daemon')) {
        // 守护进程模式
        monitor.start();
    } else {
        // 单次运行
        monitor.collectMetrics().then(metrics => {
            console.log('📊 当前性能指标:');
            console.log(`WAL大小: ${monitor.formatBytes(metrics.files.wal)}`);
            console.log(`数据库大小: ${monitor.formatBytes(metrics.files.db)}`);
            console.log(`WAL比例: ${(metrics.calculated.walToDbRatio * 100).toFixed(1)}%`);
            console.log(`性能评分: ${metrics.calculated.performanceScore}/100`);
            console.log(`WAL健康评分: ${metrics.calculated.walHealthScore}/100`);
        }).catch(console.error);
    }
}

module.exports = PerformanceMonitor;