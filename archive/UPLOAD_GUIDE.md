# DStatus 部署包上传指南

## 📋 概述

本指南说明如何将构建好的 DStatus 部署包上传到 down.vps.mom 服务器。

## 🔧 服务器端设置

### 1. 上传 PHP 脚本

将 `upload-to-vps.php` 上传到服务器：

```bash
# 上传到服务器
scp scripts/upload-to-vps.php <EMAIL>:/var/www/html/upload-dstatus.php

# 设置权限
ssh <EMAIL> "chmod 644 /var/www/html/upload-dstatus.php"
```

### 2. 配置 PHP 脚本

编辑服务器上的 `upload-dstatus.php`：

```php
$config = [
    'upload_dir' => '/var/www/html/downloads/',  // 确保目录存在
    'api_key' => 'your-secure-api-key-here',    // 设置安全的API密钥
];
```

### 3. 创建必要目录

```bash
ssh <EMAIL> "mkdir -p /var/www/html/downloads && chmod 755 /var/www/html/downloads"
```

### 4. 配置日志

```bash
ssh <EMAIL> "touch /var/log/dstatus-upload.log && chmod 666 /var/log/dstatus-upload.log"
```

## 💻 客户端使用

### 1. 设置 API 密钥

```bash
export DSTATUS_API_KEY="your-secure-api-key-here"
```

### 2. 运行上传脚本

```bash
# 上传所有构建好的文件
./scripts/upload-client.sh
```

### 3. 验证上传结果

脚本会自动验证上传结果并显示下载链接：

- Docker部署包: https://down.vps.mom/downloads/dstatus-docker.tar.gz
- 完整部署包: https://down.vps.mom/downloads/dstatus-complete.tar.gz
- 二进制文件: https://down.vps.mom/downloads/dstatus-linux-x64

## 🔄 自动化集成

### GitHub Actions 集成

可以将上传步骤添加到 GitHub Actions 工作流中：

```yaml
- name: Upload to down.vps.mom
  env:
    DSTATUS_API_KEY: ${{ secrets.DSTATUS_API_KEY }}
  run: |
    ./scripts/upload-client.sh
```

需要在 GitHub 仓库设置中添加 `DSTATUS_API_KEY` 密钥。

## 🛡️ 安全注意事项

1. **API 密钥安全**
   - 使用强密码作为 API 密钥
   - 不要在代码中硬编码密钥
   - 定期更换密钥

2. **文件验证**
   - 脚本会自动验证文件类型和大小
   - 支持 SHA256 校验和验证
   - 自动备份现有文件

3. **访问控制**
   - 限制上传脚本的访问权限
   - 监控上传日志
   - 设置合理的文件大小限制

## 📊 监控和日志

### 查看上传日志

```bash
ssh <EMAIL> "tail -f /var/log/dstatus-upload.log"
```

### 检查文件状态

```bash
# 使用客户端脚本检查
curl -H "X-API-Key: your-api-key" "https://down.vps.mom/upload-dstatus.php?status=1"
```

## 🔧 故障排除

### 常见问题

1. **上传失败**
   - 检查 API 密钥是否正确
   - 确认服务器磁盘空间充足
   - 检查网络连接

2. **权限错误**
   - 确保上传目录有写权限
   - 检查 PHP 脚本权限

3. **文件大小限制**
   - 检查 PHP 配置中的 `upload_max_filesize`
   - 调整 `post_max_size` 设置

### 手动上传

如果自动上传失败，可以手动上传：

```bash
# 使用 curl 手动上传
curl -X POST \
  -H "X-API-Key: your-api-key" \
  -F "file=@dstatus-docker.tar.gz" \
  -F "target_filename=dstatus-docker.tar.gz" \
  https://down.vps.mom/upload-dstatus.php
```

## 📝 维护

### 定期清理

建议定期清理备份文件：

```bash
ssh <EMAIL> "find /var/www/html/downloads -name '*.backup.*' -mtime +7 -delete"
```

### 更新脚本

当有新版本时，记得更新服务器上的 PHP 脚本。
