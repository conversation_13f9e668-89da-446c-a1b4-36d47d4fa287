<?php
/**
 * DStatus 部署包上传脚本
 * 用于将构建好的 Docker 部署包上传到 down.vps.mom
 */

// 配置
$config = [
    'upload_dir' => '/www/wwwroot/down.vps.mom/downloads/',  // 服务器上的目标目录
    'allowed_files' => [
        'dstatus-docker.tar.gz',
        'dstatus-complete.tar.gz',
        'dstatus-linux-x64',
        'install.sh',
        'beta/dstatus-docker-beta.tar.gz',
        'beta/dstatus-complete-beta.tar.gz',
        'beta/dstatus-linux-x64-beta',
        'beta/install-beta.sh'
    ],
    'max_file_size' => 300 * 1024 * 1024, // 300MB
    'api_key' => 'H*7F6z3n2b4CXeDMPTaj_CEm', // API密钥
];

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// 日志函数
function writeLog($message) {
    $logFile = '/www/wwwroot/down.vps.mom/dstatus-upload.log';
    $timestamp = date('Y-m-d H:i:s');
    @file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

// 验证API密钥
function validateApiKey($config) {
    $apiKey = $_SERVER['HTTP_X_API_KEY'] ?? $_POST['api_key'] ?? '';
    
    if (empty($apiKey) || $apiKey !== $config['api_key']) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid API key'
        ]);
        writeLog("Unauthorized access attempt from " . $_SERVER['REMOTE_ADDR']);
        exit;
    }
}

// 验证文件
function validateFile($file, $config) {
    // 检查文件是否上传成功
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload error: ' . $file['error']);
    }
    
    // 检查文件大小
    if ($file['size'] > $config['max_file_size']) {
        throw new Exception('File too large. Max size: ' . ($config['max_file_size'] / 1024 / 1024) . 'MB');
    }
    
    // 检查文件名
    $filename = basename($file['name']);
    $targetFilename = $_POST['target_filename'] ?? $filename;
    $allowed = false;
    
    // 检查文件名是否在允许列表中
    foreach ($config['allowed_files'] as $allowedFile) {
        if (fnmatch($allowedFile, $targetFilename) || 
            fnmatch($allowedFile, $filename) ||
            strpos($filename, str_replace('.tar.gz', '', basename($allowedFile))) !== false) {
            $allowed = true;
            break;
        }
    }
    
    if (!$allowed) {
        throw new Exception('File type not allowed: ' . $filename);
    }
    
    return $filename;
}

// 备份现有文件
function backupExistingFile($targetPath) {
    if (file_exists($targetPath)) {
        $backupPath = $targetPath . '.backup.' . date('Y-m-d-H-i-s');
        if (rename($targetPath, $backupPath)) {
            writeLog("Backed up existing file to: $backupPath");
            return $backupPath;
        }
    }
    return null;
}

// 状态查询接口 (处理GET请求)
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['status'])) {
    try {
        validateApiKey($config);

        $files = [];
        foreach ($config['allowed_files'] as $pattern) {
            $filepath = rtrim($config['upload_dir'], '/') . '/' . $pattern;
            if (file_exists($filepath)) {
                $files[] = [
                    'filename' => basename($filepath),
                    'size' => filesize($filepath),
                    'modified' => date('Y-m-d H:i:s', filemtime($filepath)),
                    'sha256' => hash_file('sha256', $filepath)
                ];
            }
        }

        echo json_encode([
            'success' => true,
            'files' => $files
        ]);
        exit;

    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
        exit;
    }
}

// 主处理逻辑 (处理POST请求)
try {
    // 只允许 POST 请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        throw new Exception('Method not allowed');
    }
    
    // 验证API密钥
    validateApiKey($config);
    
    // 检查是否有文件上传
    if (!isset($_FILES['file']) || empty($_FILES['file']['name'])) {
        throw new Exception('No file uploaded');
    }
    
    $file = $_FILES['file'];
    $filename = validateFile($file, $config);
    
    // 确定目标文件名
    $targetFilename = $_POST['target_filename'] ?? $filename;
    
    // 特殊处理：如果是版本化的文件名，转换为标准名称
    if (preg_match('/dstatus-docker-v[\d.]+-beta\.tar\.gz/', $targetFilename)) {
        $targetFilename = 'beta/dstatus-docker-beta.tar.gz';
    } elseif (preg_match('/dstatus-complete-v[\d.]+-beta\.tar\.gz/', $targetFilename)) {
        $targetFilename = 'beta/dstatus-complete-beta.tar.gz';
    } elseif (preg_match('/dstatus-docker-v[\d.]+\.tar\.gz/', $targetFilename)) {
        $targetFilename = 'dstatus-docker.tar.gz';
    } elseif (preg_match('/dstatus-complete-v[\d.]+\.tar\.gz/', $targetFilename)) {
        $targetFilename = 'dstatus-complete.tar.gz';
    }
    
    // 构建目标路径
    $targetPath = rtrim($config['upload_dir'], '/') . '/' . $targetFilename;
    
    // 确保目标目录存在
    $targetDir = dirname($targetPath);
    if (!is_dir($targetDir)) {
        if (!mkdir($targetDir, 0755, true)) {
            throw new Exception('Failed to create target directory');
        }
    }
    
    // 备份现有文件
    $backupPath = backupExistingFile($targetPath);
    
    // 移动上传的文件
    if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
        throw new Exception('Failed to move uploaded file');
    }
    
    // 设置文件权限
    chmod($targetPath, 0644);
    
    // 获取文件信息
    $fileSize = filesize($targetPath);
    $fileHash = hash_file('sha256', $targetPath);
    
    // 记录日志
    writeLog("Successfully uploaded: $targetFilename ($fileSize bytes, SHA256: $fileHash)");
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => 'File uploaded successfully',
        'data' => [
            'filename' => $targetFilename,
            'size' => $fileSize,
            'sha256' => $fileHash,
            'upload_time' => date('Y-m-d H:i:s'),
            'backup_file' => $backupPath ? basename($backupPath) : null
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
    writeLog("Upload failed: " . $e->getMessage());
}
?>
