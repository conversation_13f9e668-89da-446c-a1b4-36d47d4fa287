#!/bin/bash

# 修复 ARM64 原生模块路径问题
# 将原生模块复制到程序期望的位置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在正确的目录
if [ ! -f "dstatus-linux-arm64" ]; then
    log_error "请在 DStatus 安装目录中运行此脚本"
    exit 1
fi

log_info "修复 ARM64 原生模块路径..."

# 创建所有可能的目标路径
# 根据错误信息，程序会在这些位置查找原生模块
POSSIBLE_PATHS=(
    "node_modules/better-sqlite3/build"
    "node_modules/better-sqlite3/build/Release"
    "node_modules/better-sqlite3/compiled/20.19.1/linux/arm64"
    "node_modules/better-sqlite3/lib/binding/node-v115-linux-arm64"
)

# 检查原生模块是否存在
if [ ! -f "native-modules/linux-arm64/better_sqlite3.node" ]; then
    log_error "原生模块不存在: native-modules/linux-arm64/better_sqlite3.node"
    exit 1
fi

log_info "找到原生模块，大小: $(du -h native-modules/linux-arm64/better_sqlite3.node | cut -f1)"

# 创建目录并复制文件
for path in "${POSSIBLE_PATHS[@]}"; do
    log_info "创建目录: $path"
    mkdir -p "$path"
    
    log_info "复制原生模块到: $path/better_sqlite3.node"
    cp native-modules/linux-arm64/better_sqlite3.node "$path/"
done

# 特殊处理：创建符号链接
ln -sf ../../../native-modules/linux-arm64/better_sqlite3.node \
    node_modules/better-sqlite3/build/better_sqlite3.node 2>/dev/null || true

log_success "原生模块路径修复完成！"

# 验证修复
echo ""
log_info "验证修复结果..."
for path in "${POSSIBLE_PATHS[@]}"; do
    if [ -f "$path/better_sqlite3.node" ]; then
        echo "  ✓ $path/better_sqlite3.node"
    else
        echo "  ✗ $path/better_sqlite3.node"
    fi
done

echo ""
log_info "现在可以尝试启动 DStatus："
echo "  ./start.sh"