#!/bin/bash
# 验证 ARM64 二进制文件是否正确包含原生模块

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔍 验证 ARM64 二进制文件..."

# 检查二进制文件是否存在
if [ ! -f "dstatus-linux-arm64" ]; then
    log_error "二进制文件不存在: dstatus-linux-arm64"
    exit 1
fi

log_info "二进制文件信息:"
ls -lh dstatus-linux-arm64
file dstatus-linux-arm64

# 检查二进制文件中是否包含原生模块
log_info "检查原生模块是否被打包..."
if strings dstatus-linux-arm64 | grep -q "better_sqlite3.node"; then
    log_success "找到 better_sqlite3.node 引用"
else
    log_error "未找到 better_sqlite3.node 引用"
fi

# 测试启动（不实际运行）
log_info "测试二进制文件启动..."
timeout 2s ./dstatus-linux-arm64 --version 2>&1 || true

# 检查 pkg 缓存目录
log_info "检查 pkg 缓存目录..."
PKG_CACHE_DIR="$HOME/.cache/pkg"
if [ -d "$PKG_CACHE_DIR" ]; then
    echo "pkg 缓存目录存在: $PKG_CACHE_DIR"
    find "$PKG_CACHE_DIR" -name "*.node" -type f 2>/dev/null | head -5
fi

echo ""
log_success "验证完成！"
echo ""
echo "下一步："
echo "1. 运行 ./dstatus-linux-arm64 测试实际功能"
echo "2. 检查数据库操作是否正常"
echo "3. 如果出现原生模块错误，检查 $HOME/.cache/pkg 目录"