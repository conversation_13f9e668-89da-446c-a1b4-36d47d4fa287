#!/bin/bash

# DStatus ARM Beta 安装脚本上传工具
# 上传 ARM 相关文件到 down.vps.mom

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ARM_BETA_DIR="$(dirname "$SCRIPT_DIR")"
UPLOAD_URL="https://down.vps.mom/upload-to-vps.php"
API_KEY="${DSTATUS_API_KEY:-}"

# 文件列表（相对于 arm-beta 目录）
FILES_TO_UPLOAD=(
    "scripts/install-arm-beta.sh:install-arm-beta.sh"
    "docs/ARM-BETA-README.md:ARM-BETA-README.md"
)

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 API 密钥
if [ -z "$API_KEY" ]; then
    log_error "请设置 DSTATUS_API_KEY 环境变量"
    echo "使用方法: export DSTATUS_API_KEY=your-api-key"
    exit 1
fi

log_info "开始上传 ARM Beta 相关文件..."
echo "上传端点: $UPLOAD_URL"
echo ""

# 上传函数
upload_file() {
    local source_file="$1"
    local target_name="$2"
    local file_path="$ARM_BETA_DIR/$source_file"
    
    if [ ! -f "$file_path" ]; then
        log_error "文件不存在: $file_path"
        return 1
    fi
    
    local file_size=$(du -h "$file_path" | cut -f1)
    log_info "上传文件: $source_file ($file_size) -> $target_name"
    
    # 上传文件（使用更安全的方式传递 API 密钥）
    local response=$(
        export CURL_API_KEY="${API_KEY}"
        curl -s -X POST \
            -H "X-API-Key: ${CURL_API_KEY}" \
            -F "file=@${file_path}" \
            -F "target_filename=${target_name}" \
            "${UPLOAD_URL}"
    )
    
    # 检查响应
    if echo "$response" | jq -e '.success' >/dev/null 2>&1; then
        local uploaded_size=$(echo "$response" | jq -r '.data.size')
        local sha256=$(echo "$response" | jq -r '.data.sha256')
        log_success "上传成功"
        echo "  📏 大小: $uploaded_size 字节"
        echo "  🔐 SHA256: ${sha256:0:16}..."
        echo "  🔗 下载链接: https://down.vps.mom/downloads/$target_name"
        return 0
    else
        local error=$(echo "$response" | jq -r '.error // "Unknown error"' 2>/dev/null || echo "Unknown error")
        log_error "上传失败: $error"
        echo "  响应: $response"
        return 1
    fi
}

# 检查依赖
if ! command -v jq &> /dev/null; then
    log_error "缺少 jq 工具，请先安装: apt-get install jq 或 yum install jq"
    exit 1
fi

# 上传所有文件
success_count=0
total_count=${#FILES_TO_UPLOAD[@]}

for file_pair in "${FILES_TO_UPLOAD[@]}"; do
    IFS=':' read -r source_file target_name <<< "$file_pair"
    echo ""
    if upload_file "$source_file" "$target_name"; then
        ((success_count++))
    fi
done

echo ""
echo "=================================="
log_info "上传完成"
echo "  总文件数: $total_count"
echo "  成功上传: $success_count"
echo "  失败数量: $((total_count - success_count))"
echo ""

if [ $success_count -eq $total_count ]; then
    log_success "所有文件上传成功！"
    echo ""
    echo "📋 下载链接:"
    echo "  安装脚本: https://down.vps.mom/downloads/install-arm-beta.sh"
    echo "  说明文档: https://down.vps.mom/downloads/ARM-BETA-README.md"
    echo ""
    echo "🚀 一键安装命令:"
    echo "  curl -fsSL https://down.vps.mom/downloads/install-arm-beta.sh -o install-arm-beta.sh"
    echo "  chmod +x install-arm-beta.sh"
    echo "  sudo ./install-arm-beta.sh"
else
    log_error "部分文件上传失败"
    exit 1
fi