#!/bin/bash
# 准备 ARM64 原生模块

set -e

ARCH=${1:-"arm64"}
PLATFORM=${2:-"linux"}

echo "🔧 准备 ${PLATFORM}-${ARCH} 原生模块..."

# 安装 better-sqlite3 并构建原生模块
echo "📦 构建 better-sqlite3 for ${PLATFORM}-${ARCH}..."

# 检测是否需要交叉编译
CURRENT_ARCH=$(uname -m)
if [ "$CURRENT_ARCH" = "x86_64" ] && [ "$ARCH" = "arm64" ]; then
    echo "⚠️  检测到交叉编译需求: x86_64 -> arm64"
    echo "🔧 安装交叉编译工具..."
    
    # 设置交叉编译环境变量
    export CC=aarch64-linux-gnu-gcc
    export CXX=aarch64-linux-gnu-g++
    export AR=aarch64-linux-gnu-ar
    export LINK=aarch64-linux-gnu-g++
    export npm_config_arch=arm64
    export npm_config_target_arch=arm64
    export npm_config_disturl=https://nodejs.org/dist
    export npm_config_runtime=node
    export npm_config_target=$(node -v)
    
    echo "📦 使用交叉编译构建..."
fi

npm install better-sqlite3@11.7.0 --build-from-source \
    --target_arch=${ARCH} \
    --target_platform=${PLATFORM} \
    --target_libc=glibc \
    --legacy-peer-deps

# 创建原生模块目录
mkdir -p native-modules/${PLATFORM}-${ARCH}

# 查找并复制构建的原生模块
find node_modules/better-sqlite3/build -name "better_sqlite3.node" -type f | while read file; do
    echo "找到原生模块: $file"
    cp "$file" native-modules/${PLATFORM}-${ARCH}/
done

# 验证文件
if [ -f native-modules/${PLATFORM}-${ARCH}/better_sqlite3.node ]; then
    echo "✅ ${PLATFORM}-${ARCH} 原生模块准备完成"
    file native-modules/${PLATFORM}-${ARCH}/better_sqlite3.node
else
    echo "❌ ${PLATFORM}-${ARCH} 原生模块未找到"
    exit 1
fi