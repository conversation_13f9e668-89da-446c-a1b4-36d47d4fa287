#!/bin/bash
# ARM64 构建脚本

set -e

# 设置清理机制，确保失败时恢复 package.json
trap 'if [ -f package.json.backup ]; then mv package.json.backup package.json; fi' EXIT

echo "🔨 开始 ARM64 构建..."

# 临时添加 ARM64 目标到 package.json
echo "📝 更新 package.json 添加 ARM64 目标..."
cp package.json package.json.backup
jq '.pkg.targets += ["node20-linux-arm64"] | .pkg.targets = (.pkg.targets | unique)' package.json > package.json.tmp && mv package.json.tmp package.json

# 显示更新后的目标
echo "构建目标:"
jq '.pkg.targets' package.json

# 添加版本信息（如果提供了构建号）
BUILD_NUMBER=${1:-"0"}
if [ "$BUILD_NUMBER" != "0" ]; then
    echo "📝 注入版本信息..."
    # 创建临时版本文件
    cat > beta-version.js << EOF
// ARM64 Beta 版本信息
console.log('\\n========================================');
console.log('DStatus ARM64 Beta Build #${BUILD_NUMBER}');
console.log('构建时间: $(date +"%Y-%m-%d %H:%M:%S")');
console.log('⚠️  内部测试版本 - 不建议生产使用');
console.log('========================================\\n');
EOF
fi

# CSS 文件已经在 prepare-css.sh 中准备
# 确保原生模块在正确位置
echo "📦 准备原生模块..."
if [ -f native-modules/linux-arm64/better_sqlite3.node ]; then
    echo "找到预编译的 ARM64 原生模块"
    mkdir -p node_modules/better-sqlite3/build/Release
    cp native-modules/linux-arm64/better_sqlite3.node node_modules/better-sqlite3/build/Release/
    echo "✅ 原生模块已复制到标准位置"
else
    echo "⚠️  未找到预编译的原生模块，尝试使用系统模块"
fi

# 构建 ARM64 二进制文件
export NODE_ENV=production
echo "🔨 构建 Linux ARM64 二进制文件..."
npx @yao-pkg/pkg . --target node20-linux-arm64 --output dstatus-linux-arm64 --debug

# 清理临时文件
rm -f beta-version.js

# 恢复原始 package.json
mv package.json.backup package.json

# 验证构建结果
if [ -f ./dstatus-linux-arm64 ]; then
    echo "✅ ARM64 二进制文件构建成功"
    ls -lh ./dstatus-linux-arm64
    file ./dstatus-linux-arm64
else
    echo "❌ ARM64 二进制文件构建失败"
    exit 1
fi