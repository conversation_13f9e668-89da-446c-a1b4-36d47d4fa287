#!/bin/bash
# ARM64 部署包打包脚本

set -e

VERSION=${1:-"test"}

echo "📦 创建 ARM64 部署包..."

# 创建部署目录
DEPLOY_DIR="dstatus-arm64-complete"
rm -rf "${DEPLOY_DIR}"
mkdir -p "${DEPLOY_DIR}"

# 复制二进制文件
cp dstatus-linux-arm64 "${DEPLOY_DIR}/"
chmod +x "${DEPLOY_DIR}/dstatus-linux-arm64"

# 复制前端文件
cp -r static "${DEPLOY_DIR}/"
cp -r views "${DEPLOY_DIR}/"

# 原生模块现在已经被打包到二进制文件中，无需单独复制
echo "✅ 原生模块已包含在二进制文件中"

# 混淆前端JavaScript
if command -v terser &> /dev/null; then
    echo "🔐 混淆前端代码..."
    find "${DEPLOY_DIR}/static/js" -name "*.js" -type f | while read file; do
        if [[ ! "$file" =~ \.min\.js$ ]]; then
            terser "$file" --compress --mangle -o "$file" 2>/dev/null || true
        fi
    done
fi

# 创建数据目录
mkdir -p "${DEPLOY_DIR}/data/temp"

# 创建启动脚本
cat > "${DEPLOY_DIR}/start.sh" << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
echo "🚀 启动 DStatus (ARM64)..."
echo "🌐 Web 界面: http://localhost:5555"
echo "⚙️ 管理面板：http://localhost:5555/login"
echo ""

# 记录 PID
./dstatus-linux-arm64 &
PID=$!
echo $PID > dstatus.pid
echo "✅ DStatus 已启动 (PID: $PID)"
wait $PID
EOF
chmod +x "${DEPLOY_DIR}/start.sh"

# 创建停止脚本
cat > "${DEPLOY_DIR}/stop.sh" << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
echo "🛑 停止 DStatus..."

if [ -f dstatus.pid ]; then
    PID=$(cat dstatus.pid)
    if kill -0 $PID 2>/dev/null; then
        kill $PID
        rm dstatus.pid
        echo "✅ 已停止 DStatus (PID: $PID)"
    else
        echo "⚠️  进程不存在 (PID: $PID)"
        rm dstatus.pid
    fi
else
    echo "❌ 未找到 PID 文件"
fi
EOF
chmod +x "${DEPLOY_DIR}/stop.sh"

# 创建压缩包（使用 beta 标识）
tar -czf dstatus-arm64-beta-${VERSION}.tar.gz "${DEPLOY_DIR}"

echo "✅ ARM64 Beta 部署包创建完成！"
ls -lh dstatus-arm64-beta-${VERSION}.tar.gz