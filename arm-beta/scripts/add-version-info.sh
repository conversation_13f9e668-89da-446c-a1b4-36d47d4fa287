#!/bin/bash
# 添加版本信息到启动脚本

VERSION=${1:-"unknown"}
BUILD_NUMBER=${2:-"0"}

echo "📝 添加版本信息..."

# 创建版本信息脚本
cat > version-info.js << EOF
// ARM64 Beta 版本信息
const VERSION_INFO = {
    type: 'ARM64 Beta',
    version: '${VERSION}',
    buildNumber: '${BUILD_NUMBER}',
    buildDate: '$(date +"%Y-%m-%d %H:%M:%S")',
    warning: '⚠️  内部测试版本 - 不建议生产使用'
};

// 在启动时显示版本信息
console.log('\\n========================================');
console.log(\`DStatus \${VERSION_INFO.type} Build #\${VERSION_INFO.buildNumber}\`);
console.log(\`构建时间: \${VERSION_INFO.buildDate}\`);
console.log(VERSION_INFO.warning);
console.log('========================================\\n');

module.exports = VERSION_INFO;
EOF

echo "✅ 版本信息已创建"