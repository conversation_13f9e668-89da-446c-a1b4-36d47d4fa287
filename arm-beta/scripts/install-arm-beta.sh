#!/bin/bash

# DStatus ARM64 Beta 测试版安装脚本
# ⚠️ 仅用于 ARM64 架构的内部测试
# 
# 下载地址：
# curl -fsSL https://down.vps.mom/downloads/install-arm-beta.sh -o install-arm-beta.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# 配置
GITHUB_REPO="fev125/dzstatus"
INSTALL_DIR="/opt/dstatus-arm-beta"
SERVICE_NAME="dstatus-arm-beta"
GITHUB_API="https://api.github.com/repos/${GITHUB_REPO}"

# 默认参数
RELEASE_TAG=""
FORCE_INSTALL=false
PORT="5555"
SKIP_SERVICE=false

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo -e "${MAGENTA}DStatus ARM64 Beta 测试版安装脚本${NC}"
    echo ""
    echo "⚠️  此脚本仅用于安装 ARM64 测试版本，不建议生产使用"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --tag TAG             指定版本标签 (默认: 最新的 arm-beta-*)"
    echo "  --port PORT           服务端口 (默认: 5555)"
    echo "  --install-dir DIR     安装目录 (默认: /opt/dstatus-arm-beta)"
    echo "  --force               强制重新安装"
    echo "  --skip-service        跳过 systemd 服务配置"
    echo "  -h, --help            显示此帮助信息"
    echo ""
    echo "安装示例:"
    echo "  $0                    # 安装最新的 ARM Beta 版本"
    echo "  $0 --tag arm-beta-2   # 安装指定版本"
    echo "  $0 --port 8080        # 使用自定义端口"
    echo ""
    echo "注意事项:"
    echo "  - 仅支持 ARM64 (aarch64) 架构"
    echo "  - 需要 root 权限"
    echo "  - 测试版本可能不稳定"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --tag)
                RELEASE_TAG="$2"
                shift 2
                ;;
            --port)
                PORT="$2"
                shift 2
                ;;
            --install-dir)
                INSTALL_DIR="$2"
                shift 2
                ;;
            --force)
                FORCE_INSTALL=true
                shift
                ;;
            --skip-service)
                SKIP_SERVICE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查系统要求
check_system() {
    log_info "检查系统要求..."
    
    # 检查架构
    ARCH=$(uname -m)
    if [[ "$ARCH" != "aarch64" ]] && [[ "$ARCH" != "arm64" ]]; then
        log_error "此脚本仅支持 ARM64 架构，当前架构: $ARCH"
        exit 1
    fi
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法检测操作系统"
        exit 1
    fi
    
    # 检查 root 权限
    if [[ $EUID -ne 0 ]]; then
        log_error "请使用 root 权限运行此脚本"
        exit 1
    fi
    
    # 检查必要工具
    for cmd in curl tar jq; do
        if ! command -v $cmd &> /dev/null; then
            log_error "缺少必要工具: $cmd"
            log_info "请先安装: apt-get install $cmd 或 yum install $cmd"
            exit 1
        fi
    done
    
    log_success "系统检查通过"
}

# 获取最新的 ARM Beta 版本
get_latest_release() {
    if [[ -n "$RELEASE_TAG" ]]; then
        log_info "使用指定版本: $RELEASE_TAG"
        return
    fi
    
    log_info "获取最新的 ARM Beta 版本..."
    
    # 默认使用最新的 ARM Beta 版本
    # 由于 down.vps.mom 没有 API，我们默认使用 arm-beta-2
    RELEASE_TAG="arm-beta-2"
    log_info "使用默认版本: $RELEASE_TAG"
    log_warning "如需使用其他版本，请使用 --tag 参数指定"
}

# 下载并安装
install_arm_beta() {
    log_info "开始安装 DStatus ARM64 Beta ${RELEASE_TAG}..."
    
    # 检查是否已安装
    if [[ -d "$INSTALL_DIR" ]] && [[ "$FORCE_INSTALL" != "true" ]]; then
        log_error "已存在安装目录: $INSTALL_DIR"
        log_info "使用 --force 选项强制重新安装"
        exit 1
    fi
    
    # 创建临时目录
    TEMP_DIR=$(mktemp -d)
    trap 'rm -rf "$TEMP_DIR"' EXIT
    cd "$TEMP_DIR"
    
    # 获取下载 URL
    # 从 RELEASE_TAG 中提取版本号（例如 arm-beta-2 -> 2）
    local version_num="${RELEASE_TAG#arm-beta-}"
    local download_url="https://down.vps.mom/downloads/dstatus-arm64-beta-${version_num}.tar.gz"
    
    log_info "下载地址: $download_url"
    
    # 下载文件
    if ! curl -L -o "dstatus-arm-beta.tar.gz" "$download_url"; then
        log_error "下载失败"
        rm -rf "$TEMP_DIR"
        exit 1
    fi
    
    # 创建安装目录
    if [[ "$FORCE_INSTALL" == "true" ]] && [[ -d "$INSTALL_DIR" ]]; then
        log_warning "删除现有安装..."
        systemctl stop $SERVICE_NAME 2>/dev/null || true
        rm -rf "$INSTALL_DIR"
    fi
    
    mkdir -p "$INSTALL_DIR"
    
    # 解压文件
    log_info "解压文件..."
    tar -xzf dstatus-arm-beta.tar.gz
    
    # 复制文件到安装目录
    cp -r dstatus-arm64-complete/* "$INSTALL_DIR/"
    
    # 设置权限
    chmod +x "$INSTALL_DIR/dstatus-linux-arm64"
    chmod +x "$INSTALL_DIR"/*.sh
    
    # 清理临时文件（由 trap 自动处理）
    cd /
    
    log_success "文件安装完成"
}

# 配置环境变量
configure_environment() {
    log_info "配置环境变量..."
    
    # 创建环境配置文件
    cat > "$INSTALL_DIR/.env" << EOF
# DStatus ARM Beta 环境配置
PORT=$PORT
NODE_ENV=production
DB_PATH=$INSTALL_DIR/data
DSTATUS_LOG_DIR=$INSTALL_DIR/logs
EOF
    
    # 创建必要的目录
    mkdir -p "$INSTALL_DIR/data" "$INSTALL_DIR/logs"
    
    log_success "环境配置完成"
}

# 配置 systemd 服务
configure_service() {
    if [[ "$SKIP_SERVICE" == "true" ]]; then
        log_info "跳过服务配置"
        return
    fi
    
    log_info "配置 systemd 服务..."
    
    # 创建 systemd 服务文件
    cat > "/etc/systemd/system/${SERVICE_NAME}.service" << EOF
[Unit]
Description=DStatus ARM64 Beta Server Monitor
After=network.target

[Service]
Type=simple
User=root
# 注意：测试版本暂时使用 root 权限
# 生产环境建议创建专用用户：useradd -r -s /bin/false dstatus
WorkingDirectory=$INSTALL_DIR
Environment="NODE_ENV=production"
Environment="PORT=$PORT"
Environment="DB_PATH=$INSTALL_DIR/data"
Environment="DSTATUS_LOG_DIR=$INSTALL_DIR/logs"
ExecStart=$INSTALL_DIR/dstatus-linux-arm64
Restart=on-failure
RestartSec=10
StandardOutput=append:$INSTALL_DIR/logs/service.log
StandardError=append:$INSTALL_DIR/logs/service-error.log

# 资源限制
LimitNOFILE=65535
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载 systemd
    systemctl daemon-reload
    
    log_success "服务配置完成"
}

# 显示安装信息
show_install_info() {
    echo ""
    echo -e "${GREEN}===============================================${NC}"
    echo -e "${GREEN}✅ DStatus ARM64 Beta 安装成功！${NC}"
    echo -e "${GREEN}===============================================${NC}"
    echo ""
    echo -e "${YELLOW}⚠️  警告：这是内部测试版本，不建议生产使用${NC}"
    echo ""
    echo "安装信息:"
    echo "  - 版本: $RELEASE_TAG"
    echo "  - 安装目录: $INSTALL_DIR"
    echo "  - 服务端口: $PORT"
    echo ""
    
    if [[ "$SKIP_SERVICE" != "true" ]]; then
        echo "服务管理命令:"
        echo "  - 启动服务: systemctl start $SERVICE_NAME"
        echo "  - 停止服务: systemctl stop $SERVICE_NAME"
        echo "  - 查看状态: systemctl status $SERVICE_NAME"
        echo "  - 查看日志: journalctl -u $SERVICE_NAME -f"
        echo ""
        echo "立即启动服务:"
        echo "  systemctl start $SERVICE_NAME"
    else
        echo "手动启动命令:"
        echo "  cd $INSTALL_DIR && ./start.sh"
    fi
    
    echo ""
    echo "访问地址:"
    echo "  - Web 界面: http://localhost:$PORT"
    echo "  - 管理面板: http://localhost:$PORT/login"
    echo ""
    echo -e "${MAGENTA}反馈问题请标注 [ARM-BETA] 在 GitHub Issues${NC}"
    echo ""
}

# 主函数
main() {
    # 显示测试版本警告
    echo ""
    echo -e "${YELLOW}========================================${NC}"
    echo -e "${YELLOW}⚠️  DStatus ARM64 Beta 测试版安装脚本${NC}"
    echo -e "${YELLOW}   仅供内部测试使用，不建议生产环境${NC}"
    echo -e "${YELLOW}========================================${NC}"
    echo ""
    
    # 解析参数
    parse_args "$@"
    
    # 系统检查
    check_system
    
    # 获取版本
    get_latest_release
    
    # 安装
    install_arm_beta
    
    # 配置环境
    configure_environment
    
    # 配置服务
    configure_service
    
    # 显示安装信息
    show_install_info
}

# 执行主函数
main "$@"