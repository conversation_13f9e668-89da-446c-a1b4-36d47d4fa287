# DStatus ARM64 Beta 测试版本

⚠️ **警告：这是内部测试版本，不建议在生产环境使用**

## 概述

这个目录包含所有 ARM64 Beta 测试版本相关的文件，集中管理便于维护。ARM Beta 版本主要用于：
- 验证 ARM64 架构兼容性
- 测试原生模块功能
- 收集性能数据
- 准备正式版本的 ARM 支持

## 目录结构

```
arm-beta/
├── scripts/                      # ARM 相关脚本
│   ├── build-arm.sh             # 构建 ARM64 二进制文件
│   ├── package-arm.sh           # 打包 ARM64 部署包
│   ├── prepare-native-modules.sh # 准备 ARM64 原生模块
│   ├── install-arm-beta.sh      # ARM Beta 安装脚本
│   ├── upload-arm-beta.sh       # 上传脚本到 down.vps.mom
│   ├── add-version-info.sh      # 添加版本信息脚本
│   ├── verify-binary.sh         # 验证二进制文件完整性
│   └── fix-native-modules.sh    # 原生模块修复脚本（已弃用）
├── docs/                        # ARM 相关文档
│   └── ARM-BETA-README.md       # ARM Beta 安装和使用说明
└── README.md                    # 本文件
```

## 快速开始

### 1. 一键安装（推荐）

```bash
# 下载并运行安装脚本
curl -fsSL https://down.vps.mom/downloads/install-arm-beta.sh | sudo bash

# 或者下载后手动运行
curl -fsSL https://down.vps.mom/downloads/install-arm-beta.sh -o install-arm-beta.sh
chmod +x install-arm-beta.sh
sudo ./install-arm-beta.sh --tag arm-beta-2  # 指定版本
```

### 2. 手动构建

如果需要自行构建 ARM64 版本：

```bash
# 克隆仓库
git clone https://github.com/fev125/dzstatus.git
cd dzstatus

# 准备原生模块
./arm-beta/scripts/prepare-native-modules.sh arm64 linux

# 构建二进制文件（123 是构建号）
./arm-beta/scripts/build-arm.sh 123

# 打包部署文件
./arm-beta/scripts/package-arm.sh 123
```

## 开发指南

### 原生模块处理（重要更新）

从最新版本开始，原生模块（better_sqlite3.node）会被自动打包到二进制文件中：

- **自动打包**：pkg 配置已更新，原生模块会被包含在二进制文件内
- **无需单独分发**：不再需要在部署包中单独包含 native-modules 目录
- **运行时提取**：pkg 会自动将原生模块提取到 `$HOME/.cache/pkg/` 目录

这解决了之前需要手动复制原生模块的问题，使部署更加简洁。

### 工作流集成

ARM 构建已集成到 GitHub Actions：
- 工作流文件：`.github/workflows/build-arm-test.yml`
- 触发方式：手动触发（workflow_dispatch）
- 自动步骤：
  1. 构建 ARM64 二进制文件
  2. 创建预发布版本（tag: `arm-beta-*`）
  3. 上传安装脚本到 down.vps.mom

### 脚本说明

#### build-arm.sh
- 功能：构建 ARM64 二进制文件
- 参数：构建号（可选）
- 特性：
  - 临时添加 ARM64 构建目标
  - 自动复制原生模块到标准位置
  - 注入版本信息（如果提供构建号）
  - 自动恢复 package.json
  - 使用 --debug 标志显示打包详情

#### prepare-native-modules.sh
- 功能：构建 ARM64 原生模块（better_sqlite3）
- 参数：架构（arm64）、平台（linux）
- 输出：`native-modules/linux-arm64/better_sqlite3.node`

#### package-arm.sh
- 功能：创建完整的部署包
- 参数：版本号
- 输出：`dstatus-arm64-beta-{version}.tar.gz`
- 包含：
  - ARM64 二进制文件（含原生模块）
  - 前端资源（已压缩）
  - 启动/停止脚本
  - PID 文件管理

#### verify-binary.sh
- 功能：验证构建的二进制文件
- 检查项：
  - 二进制文件完整性
  - 原生模块引用
  - 基本启动测试
  - pkg 缓存目录状态

#### install-arm-beta.sh
- 功能：自动安装 ARM Beta 版本
- 特性：
  - 架构检查（仅支持 ARM64）
  - 自动获取最新版本
  - systemd 服务配置
  - 独立的安装目录（`/opt/dstatus-arm-beta`）

#### upload-arm-beta.sh
- 功能：上传文件到 down.vps.mom
- 需要：`DSTATUS_API_KEY` 环境变量
- 上传：
  - 安装脚本
  - README 文档

### 本地测试

```bash
# 设置 API 密钥
export DSTATUS_API_KEY=your-api-key

# 上传文件
./arm-beta/scripts/upload-arm-beta.sh

# 在 ARM64 系统上测试安装
ssh arm-server
curl -fsSL https://down.vps.mom/downloads/install-arm-beta.sh | sudo bash
```

## 版本管理

### 版本标签格式
- 格式：`arm-beta-{build_number}`
- 示例：`arm-beta-2`、`arm-beta-15`
- 类型：预发布版本（Pre-release）

### 版本隔离
- 独立的标签命名空间
- 不影响生产版本（`v1.0.*`）
- 文件名包含 `beta` 标识

## 支持的平台

- **架构**：ARM64 (aarch64)
- **系统**：Linux
- **测试平台**：
  - 树莓派 4 (Raspberry Pi 4)
  - AWS Graviton
  - Oracle Cloud ARM
  - Apple Silicon (Linux VM)

## 常见问题

### 1. 原生模块编译失败
```bash
# 使用 legacy-peer-deps 解决依赖冲突
npm install better-sqlite3 --build-from-source --legacy-peer-deps
```

### 2. 二进制文件无法运行
- 确保系统是 ARM64 架构：`uname -m` 应显示 `aarch64`
- 检查执行权限：`chmod +x dstatus-linux-arm64`

### 3. 服务启动失败
```bash
# 查看服务日志
sudo journalctl -u dstatus-arm-beta -n 100

# 手动运行测试
cd /opt/dstatus-arm-beta
sudo ./dstatus-linux-arm64
```

## 贡献指南

1. **所有 ARM 相关修改**都应在 `arm-beta/` 目录中进行
2. **测试通过后**再考虑合并到主构建流程
3. **保持向后兼容**，不要影响现有的 x64 构建
4. **文档更新**同步进行

## 未来计划

- [ ] 正式支持 ARM64 架构
- [ ] 多架构 Docker 镜像
- [ ] 性能优化
- [ ] 自动化测试套件

## 联系与反馈

发现问题请在 GitHub Issues 中反馈，标注 `[ARM-BETA]`：
https://github.com/fev125/dzstatus/issues