# DStatus ARM64 Beta 测试版安装指南

⚠️ **警告：这是内部测试版本，不建议在生产环境使用**

## 系统要求

- **架构**：ARM64 (aarch64)
- **系统**：Linux (Ubuntu/Debian/CentOS/RHEL)
- **权限**：需要 root 权限
- **工具**：curl, tar, jq

## 快速安装

### 方式一：使用安装脚本（推荐）

```bash
# 下载安装脚本
curl -fsSL https://down.vps.mom/downloads/install-arm-beta.sh -o install-arm-beta.sh

# 添加执行权限
chmod +x install-arm-beta.sh

# 安装最新的 ARM Beta 版本
sudo ./install-arm-beta.sh

# 或指定特定版本
sudo ./install-arm-beta.sh --tag arm-beta-2

# 使用自定义端口
sudo ./install-arm-beta.sh --port 8080
```

### 方式二：手动安装

```bash
# 1. 下载最新的 ARM Beta 版本
wget https://github.com/fev125/dzstatus/releases/download/arm-beta-2/dstatus-arm64-beta-2.tar.gz

# 2. 创建安装目录
sudo mkdir -p /opt/dstatus-arm-beta

# 3. 解压文件
sudo tar -xzf dstatus-arm64-beta-2.tar.gz -C /opt/dstatus-arm-beta --strip-components=1

# 4. 设置权限
sudo chmod +x /opt/dstatus-arm-beta/dstatus-linux-arm64
sudo chmod +x /opt/dstatus-arm-beta/*.sh

# 5. 启动服务
cd /opt/dstatus-arm-beta
sudo ./start.sh
```

## 安装脚本选项

| 选项 | 说明 | 默认值 |
|------|------|--------|
| `--tag TAG` | 指定版本标签 | 最新的 arm-beta-* |
| `--port PORT` | 服务端口 | 5555 |
| `--install-dir DIR` | 安装目录 | /opt/dstatus-arm-beta |
| `--force` | 强制重新安装 | false |
| `--skip-service` | 跳过 systemd 服务配置 | false |

## 服务管理

### 使用 systemd（默认）

```bash
# 启动服务
sudo systemctl start dstatus-arm-beta

# 停止服务
sudo systemctl stop dstatus-arm-beta

# 查看状态
sudo systemctl status dstatus-arm-beta

# 开机自启
sudo systemctl enable dstatus-arm-beta

# 查看日志
sudo journalctl -u dstatus-arm-beta -f
```

### 手动管理

```bash
# 启动
cd /opt/dstatus-arm-beta
sudo ./start.sh

# 停止
sudo ./stop.sh
```

## 验证安装

1. 检查服务状态：
   ```bash
   sudo systemctl status dstatus-arm-beta
   ```

2. 访问 Web 界面：
   - 主界面：http://localhost:5555
   - 管理面板：http://localhost:5555/login

3. 查看版本信息：
   服务启动时会显示 Beta 版本信息

## 卸载

```bash
# 停止服务
sudo systemctl stop dstatus-arm-beta
sudo systemctl disable dstatus-arm-beta

# 删除服务文件
sudo rm /etc/systemd/system/dstatus-arm-beta.service

# 删除安装目录
sudo rm -rf /opt/dstatus-arm-beta

# 重载 systemd
sudo systemctl daemon-reload
```

## 已知问题

1. **性能未优化**：测试版本可能存在性能问题
2. **功能限制**：部分功能可能不稳定
3. **原生模块**：需要 ARM64 版本的 better_sqlite3.node

## 故障排除

### 服务无法启动
```bash
# 检查日志
sudo journalctl -u dstatus-arm-beta -n 100

# 手动运行查看错误
cd /opt/dstatus-arm-beta
sudo ./dstatus-linux-arm64
```

### 权限问题
```bash
# 修复权限
sudo chown -R root:root /opt/dstatus-arm-beta
sudo chmod -R 755 /opt/dstatus-arm-beta
sudo chmod +x /opt/dstatus-arm-beta/dstatus-linux-arm64
```

### 端口被占用
```bash
# 查看端口占用
sudo lsof -i :5555

# 使用其他端口
sudo ./install-arm-beta.sh --port 8080
```

## 反馈与支持

发现问题请在 GitHub Issues 中反馈，并标注 `[ARM-BETA]`：
https://github.com/fev125/dzstatus/issues

## 安全提醒

- 这是测试版本，可能存在未知的安全问题
- 不要在生产环境或包含敏感数据的系统上使用
- 建议在隔离的测试环境中运行

## 版本说明

ARM Beta 版本使用独立的版本标签（arm-beta-*），不会影响正式版本。每个版本都会在 GitHub Releases 中标记为预发布版本。