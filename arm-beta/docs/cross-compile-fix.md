# ARM64 交叉编译修复

## 问题发现

通过分析 GitHub Actions 构建日志，发现了一个关键问题：

```
native-modules/linux-arm64/better_sqlite3.node: ELF 64-bit LSB shared object, x86-64, version 1 (SYSV)
```

虽然我们指定了 `--target_arch=arm64`，但实际编译出来的是 **x86-64 架构**的二进制文件。

## 问题原因

GitHub Actions 运行在 x86-64 环境上，当我们试图为 ARM64 构建原生模块时，需要正确的交叉编译配置。之前的构建脚本缺少必要的交叉编译工具链。

## 解决方案

### 1. 安装交叉编译工具链

在 GitHub Actions 中添加 ARM64 交叉编译工具：

```yaml
- name: Install build tools
  run: |
    sudo apt-get install -y gcc-aarch64-linux-gnu g++-aarch64-linux-gnu
```

### 2. 配置交叉编译环境

在 `prepare-native-modules.sh` 中检测并设置交叉编译环境：

```bash
# 检测是否需要交叉编译
CURRENT_ARCH=$(uname -m)
if [ "$CURRENT_ARCH" = "x86_64" ] && [ "$ARCH" = "arm64" ]; then
    export CC=aarch64-linux-gnu-gcc
    export CXX=aarch64-linux-gnu-g++
    export AR=aarch64-linux-gnu-ar
    export LINK=aarch64-linux-gnu-g++
    export npm_config_arch=arm64
    export npm_config_target_arch=arm64
fi
```

### 3. 关键环境变量

- `CC`: C 编译器
- `CXX`: C++ 编译器  
- `AR`: 归档工具
- `LINK`: 链接器
- `npm_config_arch`: npm 架构配置
- `npm_config_target_arch`: 目标架构

## 预期结果

修复后，编译的原生模块应该显示：

```
native-modules/linux-arm64/better_sqlite3.node: ELF 64-bit LSB shared object, aarch64, version 1 (SYSV)
```

注意 `aarch64` 而不是 `x86-64`。

## 测试步骤

1. 提交修改并触发新的构建
2. 检查构建日志中的 `file` 命令输出
3. 下载构建产物在 ARM64 系统上测试
4. 验证原生模块能正确加载

## 相关文件

- `.github/workflows/build-arm-test.yml`
- `arm-beta/scripts/prepare-native-modules.sh`