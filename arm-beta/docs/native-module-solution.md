# ARM64 原生模块永久解决方案

## 问题背景

之前的 ARM64 构建存在原生模块（better_sqlite3.node）无法正确加载的问题：
- 程序在运行时寻找模块路径：`/snapshot/dzstatus/node_modules/better-sqlite3/build/Release/`
- 实际模块位置：`native-modules/linux-arm64/better_sqlite3.node`
- 导致运行时错误："Could not locate the bindings file"

## 解决方案

采用 @yao-pkg/pkg 官方推荐的方式，通过修改 `package.json` 的 pkg 配置来包含原生模块：

### 1. 更新 package.json

```json
"pkg": {
    "assets": [
        "views/**/*",
        "static/**/*",
        "node_modules/better-sqlite3/build/Release/better_sqlite3.node",
        "node_modules/better-sqlite3/build/Release/*.node",
        "native-modules/**/*.node"
    ]
}
```

### 2. 更新构建脚本

`build-arm.sh` 现在会：
1. 检查预编译的原生模块是否存在
2. 复制到标准位置：`node_modules/better-sqlite3/build/Release/`
3. 使用 `--debug` 标志构建，显示打包详情

### 3. 简化打包流程

`package-arm.sh` 不再需要单独打包原生模块，因为它已被包含在二进制文件中。

## 工作原理

1. **打包时**：pkg 将原生模块包含在二进制文件的虚拟文件系统中
2. **运行时**：pkg 自动将原生模块提取到 `$HOME/.cache/pkg/` 目录
3. **加载时**：程序能够正确找到并加载原生模块

## 测试步骤

### 本地测试

```bash
# 1. 运行测试构建脚本
cd /path/to/dstatus
./arm-beta/test-build.sh

# 2. 验证二进制文件
./arm-beta/scripts/verify-binary.sh

# 3. 复制到 ARM64 系统测试
scp dstatus-arm64-beta-test.tar.gz arm-server:~
ssh arm-server
tar -xzf dstatus-arm64-beta-test.tar.gz
cd dstatus-arm64-complete
./start.sh
```

### GitHub Actions 测试

```bash
# 触发工作流
gh workflow run "ARM Build Test"

# 查看运行状态
gh run list --workflow="ARM Build Test"
```

## 验证方法

1. **检查二进制文件**：
   ```bash
   strings dstatus-linux-arm64 | grep better_sqlite3.node
   ```

2. **检查 pkg 缓存**：
   ```bash
   ls -la ~/.cache/pkg/
   ```

3. **运行测试**：
   ```bash
   ./dstatus-linux-arm64
   # 不应出现 "Could not locate the bindings file" 错误
   ```

## 优势

- ✅ **永久解决**：不需要运行时补丁
- ✅ **部署简单**：单个二进制文件包含所有依赖
- ✅ **官方推荐**：符合 pkg 最佳实践
- ✅ **跨平台兼容**：可扩展到其他架构

## 注意事项

1. **首次运行**：可能需要创建 `~/.cache/pkg/` 目录
2. **权限问题**：确保用户有权限写入缓存目录
3. **缓存清理**：旧版本的原生模块会保留在缓存中

## 未来改进

1. 自动化原生模块构建和测试
2. 支持更多架构（RISC-V、MIPS 等）
3. 优化二进制文件大小
4. 添加原生模块版本检查