# CSS 文件缺失问题

## 问题描述
ARM64 构建时缺少 `style.min.css` 文件，导致页面样式无法加载。

## 根本原因
pkg 打包工具使用虚拟文件系统，只包含构建时存在的文件。`style.min.css` 在构建时不存在，所以无法被访问。

## 临时解决方案
1. 访问页面后，在浏览器控制台执行：
```javascript
// 动态加载存在的CSS文件
const link = document.createElement('link');
link.rel = 'stylesheet';
link.href = '/css/tailwind.css';
document.head.appendChild(link);

// 加载其他必要的CSS
['theme-sync.css', 'theme-transition.css', 'material-icons.css'].forEach(css => {
    const l = document.createElement('link');
    l.rel = 'stylesheet';
    l.href = '/css/' + css;
    document.head.appendChild(l);
});
```

## 永久解决方案
需要在构建脚本中确保 `style.min.css` 存在：
1. 在 `package-arm.sh` 中添加 CSS 构建步骤
2. 或者在源代码中直接引用多个 CSS 文件而不是 style.min.css

## 影响
- 仅影响页面样式
- 不影响 ARM64 核心功能
- 不影响数据库和后端服务
EOF < /dev/null