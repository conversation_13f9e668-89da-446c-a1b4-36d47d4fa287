// 日志配置文件
module.exports = {
  // 日志级别: DEBUG, INFO, WARN, ERROR
  level: process.env.LOG_LEVEL || 'ERROR',
  
  // 日志输出目标
  outputs: {
    console: true,
    file: {
      enabled: true,
      path: './logs/dstatus.log',
      maxSize: '10M',
      maxFiles: 5
    }
  },
  
  // 聚合配置
  aggregation: {
    tcping: {
      enabled: true,
      interval: 3600000  // 1小时
    },
    nodeErrors: {
      enabled: true,
      interval: 300000   // 5分钟
    },
    performance: {
      enabled: true,
      cpuThreshold: 80,
      memoryThreshold: 85
    }
  }
};