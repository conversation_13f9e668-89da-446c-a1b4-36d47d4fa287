{"activeDatabase": "sqlite", "databases": {"sqlite": {"type": "sqlite", "path": "./data/db.db", "description": "本地 SQLite 数据库，适合开发和小型部署"}, "postgresql": {"type": "postgresql", "connection": "****************************************/database", "description": "PostgreSQL 生产数据库服务器 - 请使用环境变量配置真实连接信息"}}, "options": {"enableMigration": true, "enableBackup": true, "enableValidation": true}, "lastUpdated": "2025-07-30T10:00:00.000Z", "updatedBy": "system"}