/**
 * License 套餐信息缓存模块
 * 重构为统一配置服务的包装器，保持API兼容性
 * 
 * 注意：这个模块现在是unifiedConfigService的包装器
 * 新代码应该直接使用unifiedConfigService
 */

const unifiedConfigService = require('../modules/license-enhanced/unifiedConfigService');

class LicensePlansCache {
  constructor() {
    // 保留这些属性以保持兼容性
    this.plans = null;
    this.lastFetchTime = 0;
    this.cacheTimeout = 3600000; // 1小时缓存
    this.serverUrl = process.env.LICENSE_SERVER_URL || 'https://dstatus_api.vps.mom';
  }

  /**
   * 获取套餐信息
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Object>} 套餐信息
   */
  async getPlans(forceRefresh = false) {
    // 委托给统一配置服务
    const plans = await unifiedConfigService.getPlans(forceRefresh);
    
    // 保存到实例属性以保持兼容性
    this.plans = plans;
    this.lastFetchTime = Date.now();
    
    return plans;
  }

  /**
   * 根据套餐类型获取节点限制
   * @param {string} planType - 套餐类型
   * @returns {Promise<number>} 最大节点数
   */
  async getMaxNodes(planType) {
    const plan = await unifiedConfigService.getPlan(planType);
    if (!plan) {
      console.error(`[LicensePlansCache] 未找到套餐类型: ${planType}`);
      return 5; // 返回免费套餐的默认值
    }
    return plan.maxNodes;
  }

  /**
   * 根据套餐类型获取套餐信息
   * @param {string} planType - 套餐类型
   * @returns {Promise<Object>} 套餐信息
   */
  async getPlanInfo(planType) {
    return await unifiedConfigService.getPlan(planType);
  }

  /**
   * 解析功能掩码
   * @param {number} featuresMask - 功能掩码
   * @returns {Array<string>} 功能列表
   */
  async parseFeatures(featuresMask) {
    return await unifiedConfigService.parseFeatures(featuresMask);
  }

  /**
   * 获取升级建议
   * @param {string} currentPlan - 当前套餐
   * @returns {Promise<string>} 升级建议
   */
  async getUpgradeSuggestion(currentPlan) {
    return await unifiedConfigService.getUpgradeSuggestion(currentPlan);
  }

  /**
   * 清除缓存
   */
  clearCache() {
    // 清除本地属性（兼容性）
    this.plans = null;
    this.lastFetchTime = 0;
    
    // 同时清除统一配置服务的缓存
    unifiedConfigService.clearCache();
    
    console.log('[LicensePlansCache] 缓存已清除');
  }
}

// 导出单例
module.exports = new LicensePlansCache();