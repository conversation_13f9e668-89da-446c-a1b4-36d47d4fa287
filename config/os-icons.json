{"mode": "brand", "items": [{"name": "ubuntu", "file": "ubuntu.svg", "slug": "ubuntu", "brand": "#E95420", "keywords": ["ubuntu"]}, {"name": "debian", "file": "debian.svg", "slug": "debian", "brand": "#A81D33", "keywords": ["debian"]}, {"name": "redhat", "file": "redhat.svg", "slug": "redhat", "brand": "#EE0000", "keywords": ["red hat", "redhat", "rhel"]}, {"name": "centos", "file": "centos.svg", "slug": "centos", "brand": "#A3186C", "keywords": ["centos"]}, {"name": "fedora", "file": "fedora.svg", "slug": "fedora", "brand": "#51A2DA", "keywords": ["fedora"]}, {"name": "opensuse", "file": "opensuse.svg", "slug": "opensuse", "brand": "#73BA25", "keywords": ["opensuse", "suse"]}, {"name": "<PERSON><PERSON><PERSON>", "file": "archlinux.svg", "slug": "<PERSON><PERSON><PERSON>", "brand": "#1793D1", "keywords": ["arch", "arch linux"]}, {"name": "alpinelinux", "file": "alpinelinux.svg", "slug": "alpinelinux", "brand": "#0D597F", "keywords": ["alpine", "alpine linux"]}, {"name": "<PERSON><PERSON><PERSON>", "file": "rockylinux.svg", "slug": "<PERSON><PERSON><PERSON>", "brand": "#10B981", "keywords": ["rocky", "rocky linux"]}, {"name": "almalinux", "file": "almalinux.svg", "slug": "almalinux", "brand": "#0078D4", "keywords": ["alma", "alma linux"]}, {"name": "linuxmint", "file": "linuxmint.svg", "slug": "linuxmint", "brand": "#87CF3E", "keywords": ["mint", "linux mint"]}, {"name": "manjaro", "file": "manjaro.svg", "slug": "manjaro", "brand": "#35BF5C", "keywords": ["manjaro"]}, {"name": "gentoo", "file": "gentoo.svg", "slug": "gentoo", "brand": "#54487A", "keywords": ["gentoo"]}, {"name": "kali", "file": "kalilinux.svg", "slug": "<PERSON><PERSON><PERSON><PERSON>", "brand": "#557C94", "keywords": ["kali", "kali linux"]}, {"name": "popos", "file": "popos.svg", "slug": "popos", "brand": "#48B9C7", "keywords": ["popos", "pop!_os"]}, {"name": "elementary", "file": "elementary.svg", "slug": "elementary", "brand": "#64BAFF", "keywords": ["elementary"]}, {"name": "freebsd", "file": "freebsd.svg", "slug": "freebsd", "brand": "#AB2B28", "keywords": ["freebsd"]}, {"name": "openbsd", "file": "openbsd.svg", "slug": "openbsd", "brand": "#F2CA30", "keywords": ["openbsd"]}, {"name": "linux", "file": "linux.svg", "slug": "linux", "brand": "#000000", "keywords": ["linux"]}, {"name": "apple", "file": "apple.svg", "slug": "apple", "brand": "#000000", "keywords": ["darwin", "mac", "macos", "osx", "apple"]}, {"name": "android", "file": "android.svg", "slug": "android", "brand": "#3DDC84", "keywords": ["android"]}, {"name": "docker", "file": "docker.svg", "slug": "docker", "brand": "#2496ED", "keywords": ["docker"]}]}