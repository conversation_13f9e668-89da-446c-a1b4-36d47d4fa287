// 管理后台菜单配置
module.exports = {
    // 菜单配置
    menuItems: [
        {
            // 监控管理分组
            section: '监控管理',
            items: [
                {
                    id: 'monitor',
                    path: '/admin/monitor',
                    icon: 'activity',
                    text: '网络监控配置',
                    permission: 'admin', // 基础权限要求
                    badge: null
                },
                {
                    id: 'servers',
                    path: '/admin/servers',
                    icon: 'server-2',
                    text: '服务器管理',
                    permission: 'admin',
                    badge: null
                },
                {
                    id: 'groups',
                    path: '/admin/groups',
                    icon: 'folders',
                    text: '分组管理',
                    permission: 'admin',
                    badge: null
                },
                {
                    id: 'autodiscovery',
                    path: '/admin/autodiscovery',
                    icon: 'device-tablet',
                    text: '自动发现',
                    permission: 'admin',
                    badge: null
                }
            ]
        },
        {
            // 系统管理分组
            section: '系统管理',
            items: [
                {
                    id: 'ssh-scripts',
                    path: '/admin/ssh_scripts',
                    icon: 'terminal-2',
                    text: 'SSH脚本',
                    permission: 'admin',
                    badge: null
                },
                {
                    id: 'setting',
                    path: '/admin/setting',
                    icon: 'settings',
                    text: '系统设置',
                    permission: 'admin',
                    badge: null
                },
                {
                    id: 'notification',
                    path: '/admin/notification',
                    icon: 'bell',
                    text: '通知设置',
                    permission: 'admin',
                    badge: null
                },
                {
                    id: 'advanced-settings',
                    path: '/admin/advanced-settings',
                    icon: 'adjustments',
                    text: '高级设置',
                    permission: 'admin',
                    badge: null
                },
                {
                    id: 'personalization',
                    path: '/admin/personalization',
                    icon: 'brush',
                    text: '美化设置',
                    permission: 'admin',
                    badge: null
                },
                {
                    id: 'log-management',
                    path: '/admin/log-management',
                    icon: 'file-text',
                    text: '日志管理',
                    permission: 'admin',
                    badge: null
                },
                {
                    id: 'license-management',
                    path: '/admin/license-management',
                    icon: 'key',
                    text: '授权管理',
                    permission: 'admin',
                    badge: null
                },
                {
                    id: 'analytics',
                    path: '/admin/analytics',
                    icon: 'chart-line',
                    text: '高级分析',
                    permission: 'admin',
                    badge: null
                }
            ]
        }
    ],
    
    // 底部菜单
    bottomItems: [
        {
            id: 'back-to-frontend',
            path: '/',
            icon: 'home',
            text: '返回前台',
            permission: null, // 不需要权限
            external: false
        }
    ],

    // 根据权限过滤菜单（已移除功能特性控制）
    filterMenuItems(userPermission) {
        const filteredMenu = [];
        
        for (const section of this.menuItems) {
            const filteredSection = {
                section: section.section,
                items: []
            };
            
            for (const item of section.items) {
                // 只检查基础权限
                if (item.permission && userPermission !== item.permission) {
                    continue;
                }
                
                filteredSection.items.push(item);
            }
            
            // 只添加有项目的分组
            if (filteredSection.items.length > 0) {
                filteredMenu.push(filteredSection);
            }
        }
        
        return filteredMenu;
    },

    // 获取当前页面的菜单ID
    getCurrentMenuId(path) {
        for (const section of this.menuItems) {
            for (const item of section.items) {
                if (item.path === path) {
                    return item.id;
                }
            }
        }
        
        // 检查底部菜单
        for (const item of this.bottomItems) {
            if (item.path === path) {
                return item.id;
            }
        }
        
        return null;
    }
};