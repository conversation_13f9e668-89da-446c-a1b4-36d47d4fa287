#!/usr/bin/env node

/**
 * DStatus 开发环境智能启动脚本
 * 自动处理端口占用、启动 CSS 监听、使用 nodemon 热重载
 */

const { spawn, exec } = require('child_process');
const os = require('os');
const fs = require('fs');
const path = require('path');

// 配置
const CONFIG = {
    port: 5555,
    appName: 'DStatus',
    mainFile: 'dstatus.js',
};

// 控制台颜色
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    magenta: '\x1b[35m',
};

// 日志函数
const log = {
    info: (msg) => console.log(`${colors.blue}ℹ${colors.reset}  ${msg}`),
    success: (msg) => console.log(`${colors.green}✓${colors.reset}  ${msg}`),
    error: (msg) => console.log(`${colors.red}✗${colors.reset}  ${msg}`),
    warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset}  ${msg}`),
    step: (msg) => console.log(`${colors.cyan}►${colors.reset}  ${msg}`),
    dev: (msg) => console.log(`${colors.magenta}🔧${colors.reset} ${msg}`),
};

// 获取操作系统类型
const isWindows = os.platform() === 'win32';
const isMacOS = os.platform() === 'darwin';

/**
 * 执行命令并返回结果
 */
function execCommand(command) {
    return new Promise((resolve, reject) => {
        exec(command, { maxBuffer: 1024 * 1024 }, (error, stdout, stderr) => {
            if (error && !stderr.includes('No such process')) {
                reject(error);
            } else {
                resolve(stdout.trim());
            }
        });
    });
}

/**
 * 检查命令是否存在
 */
async function commandExists(command) {
    try {
        const checkCmd = isWindows ? `where ${command}` : `which ${command}`;
        await execCommand(checkCmd);
        return true;
    } catch {
        return false;
    }
}

/**
 * 获取占用指定端口的进程信息
 */
async function getProcessOnPort(port) {
    try {
        let command;
        if (isWindows) {
            command = `netstat -ano | findstr :${port} | findstr LISTENING`;
        } else if (isMacOS) {
            command = `lsof -i :${port} -P -n | grep LISTEN`;
        } else {
            command = `ss -tulpn | grep :${port} || lsof -i :${port} -P -n | grep LISTEN`;
        }

        const result = await execCommand(command);
        if (result) {
            if (isWindows) {
                const parts = result.split(/\s+/);
                const pid = parts[parts.length - 1];
                return { pid, command: 'unknown' };
            } else {
                const parts = result.split(/\s+/);
                const pid = parts[1];
                const processName = parts[0];
                return { pid, command: processName };
            }
        }
        return null;
    } catch (error) {
        return null;
    }
}

/**
 * 杀死占用端口的进程
 */
async function killProcessOnPort(port) {
    const processInfo = await getProcessOnPort(port);
    
    if (!processInfo) {
        return true;
    }

    log.warning(`端口 ${port} 被占用 (PID: ${processInfo.pid})`);
    
    try {
        const killCommand = isWindows 
            ? `taskkill /F /PID ${processInfo.pid}`
            : `kill -9 ${processInfo.pid}`;
            
        await execCommand(killCommand);
        log.success(`已终止进程 PID: ${processInfo.pid}`);
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        return true;
    } catch (error) {
        log.error(`无法终止进程: ${error.message}`);
        return false;
    }
}

/**
 * 检查开发依赖
 */
async function checkDevDependencies() {
    log.step('检查开发依赖...');
    
    const dependencies = [
        { name: 'nodemon', package: 'nodemon', global: false },
        { name: 'concurrently', package: 'concurrently', global: false },
        { name: 'tailwindcss', package: 'tailwindcss', global: false }
    ];
    
    const missing = [];
    
    for (const dep of dependencies) {
        const exists = await commandExists(dep.name);
        if (!exists) {
            // 检查 node_modules/.bin 目录
            const binPath = path.join(process.cwd(), 'node_modules', '.bin', dep.name);
            if (!fs.existsSync(binPath)) {
                missing.push(dep.package);
            }
        }
    }
    
    if (missing.length > 0) {
        log.warning('缺少开发依赖:');
        log.info(`请运行: npm install ${missing.join(' ')}`);
        return false;
    }
    
    log.success('所有开发依赖已安装');
    return true;
}

/**
 * 检查 CSS 文件
 */
function checkCSSFiles() {
    const tailwindInput = path.join(process.cwd(), 'static', 'css', 'tailwind.css');
    const cssOutput = path.join(process.cwd(), 'static', 'css', 'style.min.css');
    
    if (!fs.existsSync(tailwindInput)) {
        log.error(`未找到 Tailwind CSS 输入文件: ${tailwindInput}`);
        return false;
    }
    
    if (!fs.existsSync(cssOutput)) {
        log.warning('未找到编译后的 CSS 文件，将自动生成...');
    }
    
    return true;
}

// 全局退出状态标志
let isExiting = false;

/**
 * 启动开发服务器
 */
async function startDevServer() {
    return new Promise((resolve, reject) => {
        log.dev('启动开发服务器...');
        
        // 使用 npm run dev:raw 命令避免递归
        const child = spawn('npm', ['run', 'dev:raw'], {
            stdio: 'inherit',
            shell: isWindows,
            cwd: process.cwd()
        });

        child.on('error', (error) => {
            log.error(`启动失败: ${error.message}`);
            reject(error);
        });

        child.on('exit', (code, signal) => {
            if (code !== 0 && !isExiting) {
                log.error(`开发服务器异常退出 (代码: ${code}, 信号: ${signal})`);
            }
        });

        child.on('spawn', () => {
            setTimeout(() => {
                console.log('\n' + colors.bright + colors.green + '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━' + colors.reset);
                console.log(colors.bright + '🚀 DStatus 开发服务器已启动！' + colors.reset);
                console.log(colors.bright + colors.green + '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━' + colors.reset);
                console.log('\n📍 访问地址:');
                console.log(`   ${colors.cyan}http://localhost:${CONFIG.port}${colors.reset}`);
                console.log('\n🔧 开发特性:');
                console.log('   • Nodemon 热重载已启用');
                console.log('   • Tailwind CSS 实时编译');
                console.log('   • 文件变更自动重启');
                console.log('\n📝 监听文件:');
                console.log('   • *.js, *.json, *.html');
                console.log('   • modules/, views/, database/');
                console.log('\n⌨️  快捷键:');
                console.log('   • Ctrl+C 停止服务器');
                console.log('   • rs 手动重启服务器');
                console.log(colors.bright + colors.green + '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━' + colors.reset + '\n');
            }, 2000);
            resolve(child);
        });

        // 处理退出信号 - 防止重复监听        
        const handleExit = (signal) => {
            if (isExiting) return;
            isExiting = true;
            
            console.log('\n' + colors.yellow + '正在停止开发服务器...' + colors.reset);
            
            if (child && !child.killed) {
                child.kill('SIGTERM');
            }
            
            setTimeout(() => {
                process.exit(0);
            }, 1000);
        };

        process.once('SIGINT', () => handleExit('SIGINT'));
        process.once('SIGTERM', () => handleExit('SIGTERM'));
    });
}

/**
 * 杀掉可能遗留的 CSS 监听进程（Tailwind --watch）
 */
async function killCssWatchers() {
    log.step('检查并清理遗留的 CSS 监听进程...');
    try {
        if (isWindows) {
            // 优先使用 WMIC 查询包含 tailwindcss 与 --watch 的进程
            const query = 'wmic process where "CommandLine like \'%tailwindcss%\' and CommandLine like \'%--watch%\'" get ProcessId /format:csv';
            const out = await execCommand(query).catch(() => '');
            const lines = (out || '').split(/\r?\n/).map(s => s.trim()).filter(Boolean);
            const pids = [];
            for (const line of lines) {
                // CSV 格式: Node,<PID>
                const parts = line.split(',');
                const pid = parts[parts.length - 1];
                if (/^\d+$/.test(pid)) pids.push(pid);
            }
            if (pids.length) {
                for (const pid of pids) {
                    await execCommand(`taskkill /F /PID ${pid}`).catch(() => {});
                    log.success(`已终止 Tailwind Watcher 进程 (PID: ${pid})`);
                }
            } else {
                // 回退：尝试直接终止可能存在的 tailwindcss.exe 进程
                await execCommand('taskkill /F /IM tailwindcss.exe').catch(() => {});
            }
        } else {
            // macOS / Linux: 模式匹配命令行
            await execCommand("pkill -f 'tailwindcss .*--watch'").catch(() => {});
            await execCommand("pkill -f 'node .*tailwind.*--watch'").catch(() => {});
        }
        log.success('CSS 监听进程清理完成');
    } catch (e) {
        log.warning(`清理 CSS 监听进程时出现问题: ${e.message}`);
    }
}

/**
 * 主函数
 */
async function main() {
    console.log(`\n${colors.bright}${colors.cyan}🔧 DStatus 开发环境启动器${colors.reset}\n`);
    
    // 显示环境信息
    log.info(`工作目录: ${process.cwd()}`);
    log.info(`Node.js 版本: ${process.version}`);
    log.info(`操作系统: ${os.platform()} (${os.arch()})`);
    
    // 检查开发依赖
    if (!await checkDevDependencies()) {
        process.exit(1);
    }
    
    // 检查 CSS 文件
    if (!checkCSSFiles()) {
        process.exit(1);
    }
    
    // 检查并处理端口占用
    log.step(`检查端口 ${CONFIG.port}...`);
    const processOnPort = await getProcessOnPort(CONFIG.port);
    
    if (processOnPort) {
        const killed = await killProcessOnPort(CONFIG.port);
        if (!killed) {
            log.error('无法释放端口');
            process.exit(1);
        }
    } else {
        log.success(`端口 ${CONFIG.port} 可用`);
    }
    
    // 先尝试清理可能遗留的 CSS 监听
    await killCssWatchers();

    // 启动开发服务器
    try {
        await startDevServer();
    } catch (error) {
        log.error(`启动失败: ${error.message}`);
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch((error) => {
        log.error(`错误: ${error.message}`);
        process.exit(1);
    });
}
