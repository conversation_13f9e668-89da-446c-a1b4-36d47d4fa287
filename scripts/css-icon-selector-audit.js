#!/usr/bin/env node

/**
 * CSS图标选择器审计脚本
 * 验证所有CSS文件中的图标选择器是否正确使用Tabler Icons
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * 扫描CSS文件中的图标选择器
 */
function auditCSSFiles() {
    console.log('🔍 开始审计CSS文件中的图标选择器...\n');
    
    // 查找所有CSS文件
    const cssFiles = glob.sync('static/css/**/*.css', { 
        ignore: ['**/*.backup*', '**/*.disabled*', '**/node_modules/**'] 
    });
    
    const results = {
        totalFiles: cssFiles.length,
        filesWithIcons: 0,
        tablerIconSelectors: 0,
        legacyIconSelectors: 0,
        issues: [],
        validSelectors: []
    };
    
    cssFiles.forEach(file => {
        const content = fs.readFileSync(file, 'utf8');
        
        // 查找Tabler Icons选择器
        const tablerMatches = content.match(/\.ti\s*[{,\s]/g) || [];
        if (tablerMatches.length > 0) {
            results.filesWithIcons++;
            results.tablerIconSelectors += tablerMatches.length;
            results.validSelectors.push({
                file,
                selectors: tablerMatches.length,
                type: 'Tabler Icons'
            });
        }
        
        // 查找遗留的图标选择器
        const legacyPatterns = [
            /\.material-icons\s*[{,\s]/g,
            /\.fa-[a-z-]+\s*[{,\s]/g,
            /\.mdi-[a-z-]+\s*[{,\s]/g,
            /\.ionicons\s*[{,\s]/g
        ];
        
        legacyPatterns.forEach((pattern, index) => {
            const matches = content.match(pattern) || [];
            if (matches.length > 0) {
                results.legacyIconSelectors += matches.length;
                const types = ['Material Icons', 'Font Awesome', 'Material Design Icons', 'Ionicons'];
                results.issues.push({
                    file,
                    type: types[index],
                    selectors: matches,
                    count: matches.length
                });
            }
        });
    });
    
    return results;
}

/**
 * 生成审计报告
 */
function generateReport(results) {
    console.log('📊 CSS图标选择器审计报告');
    console.log('='.repeat(50));
    console.log(`总CSS文件数: ${results.totalFiles}`);
    console.log(`包含图标选择器的文件: ${results.filesWithIcons}`);
    console.log(`Tabler Icons选择器: ${results.tablerIconSelectors}`);
    console.log(`遗留图标选择器: ${results.legacyIconSelectors}`);
    
    if (results.validSelectors.length > 0) {
        console.log('\n✅ 正确使用Tabler Icons的文件:');
        results.validSelectors.forEach(item => {
            console.log(`  ${item.file}: ${item.selectors}个选择器`);
        });
    }
    
    if (results.issues.length > 0) {
        console.log('\n❌ 发现遗留图标选择器:');
        results.issues.forEach(issue => {
            console.log(`  ${issue.file} (${issue.type}):`);
            issue.selectors.forEach(selector => {
                console.log(`    - ${selector.trim()}`);
            });
        });
    } else {
        console.log('\n🎉 所有CSS文件都正确使用了Tabler Icons选择器！');
    }
    
    // 生成JSON报告
    const reportData = {
        timestamp: new Date().toISOString(),
        summary: {
            totalFiles: results.totalFiles,
            filesWithIcons: results.filesWithIcons,
            tablerIconSelectors: results.tablerIconSelectors,
            legacyIconSelectors: results.legacyIconSelectors,
            status: results.legacyIconSelectors === 0 ? 'CLEAN' : 'NEEDS_ATTENTION'
        },
        validSelectors: results.validSelectors,
        issues: results.issues
    };
    
    if (!fs.existsSync('reports')) {
        fs.mkdirSync('reports');
    }
    
    fs.writeFileSync('reports/css-icon-selector-audit.json', JSON.stringify(reportData, null, 2));
    console.log('\n📋 详细报告已保存到: reports/css-icon-selector-audit.json');
    
    return results.legacyIconSelectors === 0;
}

/**
 * 主函数
 */
function main() {
    try {
        const results = auditCSSFiles();
        const isClean = generateReport(results);
        
        if (isClean) {
            console.log('\n✅ CSS图标选择器审计通过！');
            process.exit(0);
        } else {
            console.log('\n⚠️  发现需要处理的遗留图标选择器');
            process.exit(1);
        }
    } catch (error) {
        console.error('❌ 审计过程中发生错误:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { auditCSSFiles, generateReport };
