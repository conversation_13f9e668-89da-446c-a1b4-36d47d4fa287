#!/usr/bin/env node
'use strict';

const dbConfig = require('../database/config');

// This script cleans up old data from large tables
// Run this periodically (e.g., daily via cron)

async function initDatabase() {
    const dbModule = require('../database/index');
    return await dbModule();
}

let db;

console.log('Starting database cleanup...');

// Cleanup configuration
const cleanupTasks = [
    {
        table: 'tcping_5m',
        retentionDays: 7,
        description: '5-minute TCPing data'
    },
    {
        table: 'tcping_m',
        retentionDays: 30,
        description: 'Minute-level TCPing data'
    },
    {
        table: 'load_archive',
        retentionDays: 30,
        description: 'Load archive data'
    }
];

async function runCleanup() {
    try {
        db = await initDatabase();
        const now = Math.floor(Date.now() / 1000);

        for (const task of cleanupTasks) {
            const cutoff = now - (task.retentionDays * 24 * 60 * 60);
            
            try {
                console.log(`\nCleaning up ${task.description} older than ${task.retentionDays} days...`);
                
                // Count rows to delete
                const toDelete = await db.DB.get(`
                    SELECT COUNT(*) as count 
                    FROM ${task.table} 
                    WHERE created_at < ?
                `, [cutoff]);
                
                if (toDelete.count > 0) {
                    // Delete old data
                    const result = await db.DB.run(`
                        DELETE FROM ${task.table} 
                        WHERE created_at < ?
                    `, [cutoff]);
                    
                    console.log(`  Deleted ${result.changes} rows from ${task.table}`);
                } else {
                    console.log(`  No old data to clean up in ${task.table}`);
                }
            } catch (err) {
                console.error(`  Error cleaning up ${task.table}:`, err.message);
            }
        }

        // Vacuum to reclaim space (SQLite specific)
        if (db.type === 'sqlite') {
            console.log('\nRunning VACUUM to reclaim space...');
            try {
                await db.DB.run('VACUUM');
                console.log('✓ Database vacuumed successfully');
            } catch (err) {
                console.error('✗ Failed to vacuum:', err.message);
            }
        }

        await db.DB.disconnect();
        console.log('\nCleanup completed.');
    } catch (error) {
        console.error('Script execution failed:', error);
        process.exit(1);
    }
}

// Run the cleanup
runCleanup();
