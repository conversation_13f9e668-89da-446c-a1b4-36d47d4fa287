#!/usr/bin/env node

/**
 * 版本号注入脚本
 * 在构建过程中将版本号注入到相关文件中
 * 
 * 使用方式：
 * 1. 默认：使用 package.json 中的现有版本号
 *    node scripts/inject-version.js
 * 
 * 2. 生成新版本：基于当前日期生成新版本号
 *    node scripts/inject-version.js --new-version
 *    或 GENERATE_NEW_VERSION=1 node scripts/inject-version.js
 * 
 * 3. 手动指定版本：创建 version.txt 文件
 *    echo "25.08.03" > version.txt
 *    node scripts/inject-version.js
 */

const fs = require('fs');
const path = require('path');

/**
 * 基于当前日期生成版本号 (YY.MM.BUILD_NUMBER)
 */
function generateVersionFromDate() {
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2); // 年份后两位
    const month = (now.getMonth() + 1).toString().padStart(2, '0'); // 月份补零
    const day = now.getDate().toString().padStart(2, '0'); // 日期补零
    
    // 构建号基于日期数字
    const buildNumber = day;
    
    return `${year}.${month}.${buildNumber}`;
}

function injectVersion() {
    // 优先从 package.json 读取稳定版本号
    const packageFile = path.join(__dirname, '../package.json');
    let version = '25.08.01'; // 默认版本
    
    try {
        // 首先尝试从 package.json 读取现有版本
        if (fs.existsSync(packageFile)) {
            const packageData = JSON.parse(fs.readFileSync(packageFile, 'utf8'));
            if (packageData.version) {
                version = packageData.version;
                console.log('📋 使用 package.json 中的版本:', version);
            }
        }
        
        // 其次检查是否有手动指定的版本文件
        const versionFile = path.join(__dirname, '../version.txt');
        if (fs.existsSync(versionFile)) {
            const fileVersion = fs.readFileSync(versionFile, 'utf8').trim();
            if (fileVersion) {
                version = fileVersion;
                console.log('📋 使用 version.txt 中的版本:', version);
            }
        }
        
        // 检查是否通过环境变量或命令行参数强制生成新版本
        if (process.env.GENERATE_NEW_VERSION || process.argv.includes('--new-version')) {
            version = generateVersionFromDate();
            console.log('🎯 生成新版本号:', version);
        }
        
    } catch (error) {
        console.warn('⚠️  版本读取出错，使用默认版本:', error.message);
    }
    
    console.log('🎯 注入版本号:', version);
    
    // 1. 更新 package.json
    updatePackageJson(version);
    
    // 2. 更新网页模板
    updateWebTemplate(version);
    
    // 3. 创建版本信息文件
    createVersionInfo(version);
    
    console.log('✅ 版本号注入完成');
}

function updatePackageJson(version) {
    const packageFile = path.join(__dirname, '../package.json');
    try {
        const packageData = JSON.parse(fs.readFileSync(packageFile, 'utf8'));
        
        // 只有在版本不同时才更新 package.json
        if (packageData.version !== version) {
            packageData.version = version;
            fs.writeFileSync(packageFile, JSON.stringify(packageData, null, 2));
            console.log('✅ 更新 package.json 版本:', version);
        } else {
            console.log('📋 package.json 版本已是最新:', version);
        }
    } catch (error) {
        console.error('❌ 更新 package.json 失败:', error.message);
    }
}

function updateWebTemplate(version) {
    const footerFile = path.join(__dirname, '../views/footer.html');
    try {
        let content = fs.readFileSync(footerFile, 'utf8');
        
        // 替换硬编码的版本号
        content = content.replace(
            /v\d+\.\d+\.\d+/g,
            `v${version}`
        );
        
        fs.writeFileSync(footerFile, content);
        console.log('✅ 更新网页模板版本:', version);
    } catch (error) {
        console.error('❌ 更新网页模板失败:', error.message);
    }
}

function createVersionInfo(version) {
    const now = new Date();
    const versionInfo = {
        version: version,
        buildTime: now.toISOString(),
        buildDate: now.toLocaleDateString('zh-CN'),
        format: 'YY.MM.BUILD_NUMBER',
        projectName: 'dstatus'
    };
    
    try {
        // 创建版本信息 JavaScript 文件
        const versionJs = `// 自动生成的版本信息
window.VERSION_INFO = ${JSON.stringify(versionInfo, null, 2)};`;
        
        fs.writeFileSync(
            path.join(__dirname, '../static/js/version-info.js'),
            versionJs
        );
        
        // 创建版本信息 JSON 文件
        fs.writeFileSync(
            path.join(__dirname, '../static/version.json'),
            JSON.stringify(versionInfo, null, 2)
        );
        
        console.log('✅ 创建版本信息文件');
    } catch (error) {
        console.error('❌ 创建版本信息文件失败:', error.message);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    injectVersion();
}

module.exports = { injectVersion };