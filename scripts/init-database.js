#!/usr/bin/env node
'use strict';

async function initDatabase() {
    try {
        console.log('🚀 开始数据库初始化...');
        
        // 加载数据库模块，会自动运行迁移
        const db = await require('../database/index.js')();
        
        // 验证关键表是否存在
        const criticalTables = [
            'servers', 'groups', 'monitor_regions', 'monitor_targets',
            'tcping_archive', 'tcping_m', 'tcping_5m', 'tcping_h'
        ];
        
        console.log('📋 验证数据库表结构...');
        for (const table of criticalTables) {
            const result = await db.get(
                `SELECT name FROM sqlite_master WHERE type='table' AND name=?`,
                [table]
            );
            if (!result) {
                console.error(`❌ 缺失关键表: ${table}`);
                process.exit(1);
            }
            console.log(`✅ ${table} 表存在`);
        }
        
        // 检查迁移版本
        const version = await db.get('SELECT MAX(version) as version FROM db_migrations WHERE status = "success"');
        console.log(`📌 当前数据库版本: ${version?.version || 0}`);
        
        // 添加demo数据（如果需要）
        if (process.env.INIT_DEMO_DATA === 'true') {
            const regionCount = await db.get('SELECT COUNT(*) as count FROM monitor_regions');
            if (regionCount.count === 0) {
                await db.run(
                    'INSERT INTO monitor_regions (id, name, description) VALUES (?, ?, ?)',
                    ['demo-region', 'Demo地区', '演示用地区']
                );
                console.log('✅ 已添加demo数据');
            }
        }
        
        console.log('✅ 数据库初始化完成！');
        process.exit(0);
    } catch (error) {
        console.error('❌ 数据库初始化失败:', error.message);
        console.error(error.stack);
        process.exit(1);
    }
}

// 直接执行
initDatabase();