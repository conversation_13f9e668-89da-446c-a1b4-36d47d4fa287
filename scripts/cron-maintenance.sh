#!/bin/bash

# DStatus 数据库维护计划任务配置
# 
# 使用方法:
# 1. 给脚本添加执行权限: chmod +x cron-maintenance.sh
# 2. 编辑 crontab: crontab -e
# 3. 添加以下内容（根据需要调整路径）:
#
# # DStatus 数据库维护任务
# # 每天凌晨 3:00 执行数据清理
# 0 3 * * * /path/to/dstatus/scripts/cron-maintenance.sh cleanup
# 
# # 每周日凌晨 4:00 执行数据库优化
# 0 4 * * 0 /path/to/dstatus/scripts/cron-maintenance.sh vacuum
#
# # 每天早上 9:00 生成增长分析报告
# 0 9 * * * /path/to/dstatus/scripts/cron-maintenance.sh analyze
#
# # 每月 1 号凌晨 2:00 执行完整维护
# 0 2 1 * * /path/to/dstatus/scripts/cron-maintenance.sh full

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_DIR="$( dirname "$SCRIPT_DIR" )"

# 日志目录
LOG_DIR="$PROJECT_DIR/data/logs"
mkdir -p "$LOG_DIR"

# 日志文件
LOG_FILE="$LOG_DIR/maintenance-$(date +%Y%m%d).log"

# 记录日志
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 切换到项目目录
cd "$PROJECT_DIR" || exit 1

# 检查 Node.js 环境
if ! command -v node &> /dev/null; then
    log "错误: Node.js 未安装或不在 PATH 中"
    exit 1
fi

# 执行维护任务
case "$1" in
    cleanup)
        log "开始执行数据清理任务..."
        npm run cleanup >> "$LOG_FILE" 2>&1
        if [ $? -eq 0 ]; then
            log "数据清理任务完成"
        else
            log "数据清理任务失败"
            exit 1
        fi
        ;;
        
    vacuum)
        log "开始执行数据库优化..."
        node -e "
            const Database = require('better-sqlite3');
            const dbConfig = require('./database/config');
            const db = new Database(dbConfig.getPaths().main);
            console.log('执行 VACUUM...');
            db.prepare('VACUUM').run();
            console.log('执行 ANALYZE...');
            db.prepare('ANALYZE').run();
            db.close();
            console.log('数据库优化完成');
        " >> "$LOG_FILE" 2>&1
        if [ $? -eq 0 ]; then
            log "数据库优化完成"
        else
            log "数据库优化失败"
            exit 1
        fi
        ;;
        
    analyze)
        log "开始生成数据库增长分析报告..."
        node "$SCRIPT_DIR/monitor-database-growth.js" >> "$LOG_FILE" 2>&1
        if [ $? -eq 0 ]; then
            log "增长分析报告生成完成"
        else
            log "增长分析报告生成失败"
            exit 1
        fi
        ;;
        
    full)
        log "开始执行完整维护..."
        
        # 1. 数据清理
        log "步骤 1/4: 数据清理"
        npm run cleanup >> "$LOG_FILE" 2>&1
        
        # 2. 数据库优化
        log "步骤 2/4: 数据库优化"
        node -e "
            const Database = require('better-sqlite3');
            const dbConfig = require('./database/config');
            const db = new Database(dbConfig.getPaths().main);
            db.prepare('VACUUM').run();
            db.prepare('ANALYZE').run();
            db.close();
        " >> "$LOG_FILE" 2>&1
        
        # 3. 备份数据库
        log "步骤 3/4: 备份数据库"
        BACKUP_DIR="$PROJECT_DIR/data/backups"
        mkdir -p "$BACKUP_DIR"
        BACKUP_FILE="$BACKUP_DIR/dstatus-$(date +%Y%m%d-%H%M%S).db"
        cp "$PROJECT_DIR/data/dstatus.db" "$BACKUP_FILE"
        
        # 删除超过 30 天的备份
        find "$BACKUP_DIR" -name "dstatus-*.db" -mtime +30 -delete
        
        # 4. 生成报告
        log "步骤 4/4: 生成分析报告"
        node "$SCRIPT_DIR/monitor-database-growth.js" >> "$LOG_FILE" 2>&1
        
        log "完整维护完成"
        ;;
        
    backup)
        log "开始备份数据库..."
        BACKUP_DIR="$PROJECT_DIR/data/backups"
        mkdir -p "$BACKUP_DIR"
        BACKUP_FILE="$BACKUP_DIR/dstatus-$(date +%Y%m%d-%H%M%S).db"
        
        # 创建备份
        cp "$PROJECT_DIR/data/dstatus.db" "$BACKUP_FILE"
        
        # 压缩备份
        gzip "$BACKUP_FILE"
        
        log "数据库备份完成: ${BACKUP_FILE}.gz"
        
        # 删除旧备份
        find "$BACKUP_DIR" -name "dstatus-*.db.gz" -mtime +30 -delete
        log "已清理 30 天前的旧备份"
        ;;
        
    *)
        echo "使用方法: $0 {cleanup|vacuum|analyze|full|backup}"
        echo ""
        echo "任务说明:"
        echo "  cleanup  - 清理过期数据"
        echo "  vacuum   - 优化数据库（VACUUM + ANALYZE）"
        echo "  analyze  - 生成数据库增长分析报告"
        echo "  full     - 执行完整维护（清理+优化+备份+分析）"
        echo "  backup   - 备份数据库"
        exit 1
        ;;
esac

# 清理旧日志（保留 30 天）
find "$LOG_DIR" -name "maintenance-*.log" -mtime +30 -delete

log "维护任务执行完毕"