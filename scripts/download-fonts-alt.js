#!/usr/bin/env node
/**
 * Roboto Mono 字体下载脚本 - 替代方案
 * 使用 Google Fonts CSS API 动态获取字体 URL
 */

const https = require('https');
const fs = require('fs-extra');
const path = require('path');

const FONTS_DIR = path.join(__dirname, '..', 'static', 'fonts', 'roboto-mono');

/**
 * 获取 Google Fonts CSS
 */
function getGoogleFontsCSS() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'fonts.googleapis.com',
      path: '/css2?family=Roboto+Mono:wght@400;500;600&display=swap',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      }
    };
    
    https.get(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => resolve(data));
    }).on('error', reject);
  });
}

/**
 * 解析 CSS 提取字体 URL
 */
function parseFontUrls(css) {
  const fonts = [];
  const regex = /@font-face\s*{[^}]*}/g;
  const matches = css.match(regex) || [];
  
  matches.forEach(match => {
    const weightMatch = match.match(/font-weight:\s*(\d+)/);
    const urlMatch = match.match(/url\(([^)]+\.woff2)\)/);
    
    if (weightMatch && urlMatch) {
      const weight = weightMatch[1];
      const url = urlMatch[1].replace(/['"]/g, '');
      
      let filename = '';
      if (weight === '400') {
        filename = 'roboto-mono-v23-latin-regular.woff2';
      } else {
        filename = `roboto-mono-v23-latin-${weight}.woff2`;
      }
      
      fonts.push({
        weight,
        url,
        filename
      });
    }
  });
  
  return fonts;
}

/**
 * 下载文件
 */
function downloadFile(url, destPath) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(destPath);
    
    const download = (downloadUrl) => {
      https.get(downloadUrl, (response) => {
        if (response.statusCode === 302 || response.statusCode === 301) {
          download(response.headers.location);
        } else if (response.statusCode === 200) {
          response.pipe(file);
          file.on('finish', () => {
            file.close();
            resolve();
          });
        } else {
          reject(new Error(`HTTP ${response.statusCode}`));
        }
      }).on('error', reject);
    };
    
    download(url);
  });
}

/**
 * 使用 curl 下载（备用方案）
 */
function downloadWithCurl(url, destPath) {
  return new Promise((resolve, reject) => {
    const { exec } = require('child_process');
    const cmd = `curl -L -o "${destPath}" "${url}"`;
    
    exec(cmd, (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else {
        resolve();
      }
    });
  });
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始下载 Roboto Mono 字体文件...\n');
  console.log('📡 获取 Google Fonts CSS...');
  
  fs.ensureDirSync(FONTS_DIR);
  
  try {
    const css = await getGoogleFontsCSS();
    const fonts = parseFontUrls(css);
    
    console.log(`✅ 找到 ${fonts.length} 个字体文件\n`);
    
    let successCount = 0;
    
    for (const font of fonts) {
      const destPath = path.join(FONTS_DIR, font.filename);
      
      if (fs.existsSync(destPath)) {
        const stats = fs.statSync(destPath);
        if (stats.size > 1000) {
          console.log(`✅ ${font.filename} 已存在 (${Math.round(stats.size / 1024)}KB)`);
          successCount++;
          continue;
        }
      }
      
      console.log(`📥 下载 ${font.filename} (weight: ${font.weight})...`);
      
      try {
        // 尝试直接下载
        await downloadFile(font.url, destPath);
        const stats = fs.statSync(destPath);
        console.log(`✅ ${font.filename} 下载成功 (${Math.round(stats.size / 1024)}KB)`);
        successCount++;
      } catch (error) {
        console.log(`⚠️  直接下载失败，尝试使用 curl...`);
        try {
          await downloadWithCurl(font.url, destPath);
          const stats = fs.statSync(destPath);
          console.log(`✅ ${font.filename} 通过 curl 下载成功 (${Math.round(stats.size / 1024)}KB)`);
          successCount++;
        } catch (curlError) {
          console.error(`❌ ${font.filename} 下载失败`);
        }
      }
    }
    
    console.log('\n' + '='.repeat(50));
    console.log(`📊 下载完成！成功: ${successCount}/${fonts.length}`);
    
    if (successCount === fonts.length) {
      console.log('🎉 所有字体文件下载成功！');
      await updateFontCSS();
    }
    
  } catch (error) {
    console.error('❌ 获取字体信息失败:', error.message);
    console.log('\n尝试使用备用 CDN...');
    await downloadFromAlternativeCDN();
  }
}

/**
 * 从备用 CDN 下载
 */
async function downloadFromAlternativeCDN() {
  console.log('📡 使用 jsDelivr CDN 作为备用源...\n');
  
  const fonts = [
    {
      weight: '400',
      filename: 'roboto-mono-regular.woff2',
      url: 'https://cdn.jsdelivr.net/fontsource/fonts/roboto-mono@latest/latin-400-normal.woff2'
    },
    {
      weight: '500',
      filename: 'roboto-mono-500.woff2',
      url: 'https://cdn.jsdelivr.net/fontsource/fonts/roboto-mono@latest/latin-500-normal.woff2'
    },
    {
      weight: '600',
      filename: 'roboto-mono-600.woff2',
      url: 'https://cdn.jsdelivr.net/fontsource/fonts/roboto-mono@latest/latin-600-normal.woff2'
    }
  ];
  
  let successCount = 0;
  
  for (const font of fonts) {
    const destPath = path.join(FONTS_DIR, font.filename);
    console.log(`📥 下载 ${font.filename}...`);
    
    try {
      await downloadWithCurl(font.url, destPath);
      const stats = fs.statSync(destPath);
      console.log(`✅ ${font.filename} 下载成功 (${Math.round(stats.size / 1024)}KB)`);
      successCount++;
    } catch (error) {
      console.error(`❌ ${font.filename} 下载失败`);
    }
  }
  
  if (successCount > 0) {
    await updateFontCSSForAlt();
  }
}

/**
 * 更新字体 CSS（备用文件名）
 */
async function updateFontCSSForAlt() {
  console.log('\n📝 更新字体 CSS 配置...');
  
  const cssPath = path.join(__dirname, '..', 'static', 'css', 'fonts', 'roboto-mono.css');
  
  const cssContent = `/* Roboto Mono Local Font Definitions */
/* 自动生成的本地字体配置 */

@font-face {
  font-family: 'Roboto Mono';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local('Roboto Mono Regular'), local('RobotoMono-Regular'),
       url('/fonts/roboto-mono/roboto-mono-regular.woff2') format('woff2');
}

@font-face {
  font-family: 'Roboto Mono';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local('Roboto Mono Medium'), local('RobotoMono-Medium'),
       url('/fonts/roboto-mono/roboto-mono-500.woff2') format('woff2');
}

@font-face {
  font-family: 'Roboto Mono';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: local('Roboto Mono SemiBold'), local('RobotoMono-SemiBold'),
       url('/fonts/roboto-mono/roboto-mono-600.woff2') format('woff2');
}

/* 回退到系统等宽字体 */
.font-mono {
  font-family: 'Roboto Mono', 'Courier New', Courier, monospace;
}

/* 应用到度量数字 */
.metric-number,
.speed-metric {
  font-family: 'Roboto Mono', monospace !important;
}`;
  
  fs.writeFileSync(cssPath, cssContent);
  console.log('✅ 字体 CSS 文件已更新');
}

/**
 * 更新字体 CSS
 */
async function updateFontCSS() {
  console.log('\n📝 更新字体 CSS 配置...');
  
  const cssPath = path.join(__dirname, '..', 'static', 'css', 'fonts', 'roboto-mono.css');
  
  const cssContent = `/* Roboto Mono Local Font Definitions */
/* 自动生成的本地字体配置 */

@font-face {
  font-family: 'Roboto Mono';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local('Roboto Mono Regular'), local('RobotoMono-Regular'),
       url('/fonts/roboto-mono/roboto-mono-v23-latin-regular.woff2') format('woff2');
}

@font-face {
  font-family: 'Roboto Mono';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local('Roboto Mono Medium'), local('RobotoMono-Medium'),
       url('/fonts/roboto-mono/roboto-mono-v23-latin-500.woff2') format('woff2');
}

@font-face {
  font-family: 'Roboto Mono';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: local('Roboto Mono SemiBold'), local('RobotoMono-SemiBold'),
       url('/fonts/roboto-mono/roboto-mono-v23-latin-600.woff2') format('woff2');
}

/* 回退到系统等宽字体 */
.font-mono {
  font-family: 'Roboto Mono', 'Courier New', Courier, monospace;
}

/* 应用到度量数字 */
.metric-number,
.speed-metric {
  font-family: 'Roboto Mono', monospace !important;
}`;
  
  fs.writeFileSync(cssPath, cssContent);
  console.log('✅ 字体 CSS 文件已更新');
}

// 执行
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}