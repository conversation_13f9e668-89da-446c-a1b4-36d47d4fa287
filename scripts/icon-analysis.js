#!/usr/bin/env node

/**
 * 图标使用分析和清单生成脚本
 * 为DStatus项目图标系统统一任务生成详细清单
 */

const fs = require('fs');
const path = require('path');

// Font Awesome 到 Tabler Icons 映射表
const FA_TO_TABLER_MAPPING = {
  // 网络/设备
  'fa-network-wired': 'ti-router',
  'fa-server': 'ti-server',
  'fa-shield-alt': 'ti-shield-check',
  'fa-tachometer-alt': 'ti-gauge',
  'fa-bullseye': 'ti-target',
  'fa-globe': 'ti-world',
  'fa-globe-americas': 'ti-world',
  'fa-ethernet': 'ti-ethernet',
  'fa-terminal': 'ti-terminal-2',
  
  // 状态/操作
  'fa-exclamation-circle': 'ti-alert-circle',
  'fa-exclamation-triangle': 'ti-alert-triangle',
  'fa-check-circle': 'ti-circle-check',
  'fa-times': 'ti-x',
  'fa-check': 'ti-check',
  'fa-info-circle': 'ti-info-circle',
  'fa-question-circle': 'ti-help-circle',
  
  // 导航
  'fa-chevron-left': 'ti-chevron-left',
  'fa-chevron-right': 'ti-chevron-right',
  'fa-arrow-up': 'ti-arrow-up',
  'fa-arrow-left': 'ti-arrow-left',
  
  // 功能
  'fa-search': 'ti-search',
  'fa-search-minus': 'ti-search-off',
  'fa-copy': 'ti-copy',
  'fa-download': 'ti-download',
  'fa-upload': 'ti-upload',
  'fa-history': 'ti-history',
  'fa-trash': 'ti-trash',
  'fa-trash-alt': 'ti-trash',
  'fa-cogs': 'ti-settings',
  'fa-save': 'ti-device-floppy',
  'fa-sync': 'ti-refresh',
  'fa-sync-alt': 'ti-refresh',
  'fa-edit': 'ti-edit',
  'fa-plus': 'ti-plus',
  'fa-plus-circle': 'ti-circle-plus',
  
  // 其他
  'fa-eye': 'ti-eye',
  'fa-eye-slash': 'ti-eye-off',
  'fa-sliders-h': 'ti-adjustments-horizontal',
  'fa-exchange-alt': 'ti-arrows-left-right',
  'fa-star': 'ti-star',
  'fa-key': 'ti-key',
  'fa-link': 'ti-link',
  'fa-keyboard': 'ti-keyboard',
  'fa-home': 'ti-home',
  'fa-clock': 'ti-clock',
  'fa-chart-line': 'ti-chart-line',
  'fa-chart-bar': 'ti-chart-bar',
  'fa-database': 'ti-database',
  'fa-microchip': 'ti-cpu',
  'fa-flask': 'ti-flask',
  'fa-tags': 'ti-tags',
  'fa-map-marker-alt': 'ti-map-pin',
  'fa-eraser': 'ti-eraser',
  'fa-broom': 'ti-broom',
  
  // 操作系统图标 (保持原样或使用通用图标)
  'fa-linux': 'ti-brand-ubuntu',
  'fa-windows': 'ti-brand-windows',
  'fa-apple': 'ti-brand-apple',
  'fa-android': 'ti-brand-android',
  'fa-ubuntu': 'ti-brand-ubuntu',
  'fa-debian': 'ti-brand-debian',
  'fa-centos': 'ti-brand-redhat',
  'fa-redhat': 'ti-brand-redhat',
  'fa-fedora': 'ti-brand-fedora',
  'fa-suse': 'ti-brand-suse',
  'fa-freebsd': 'ti-brand-freebsd',
  'fa-docker': 'ti-brand-docker',
  
  // 特殊情况
  'fa-off': 'ti-power',
  'fa-solid': '', // 这是修饰符，不是图标名
  'fa-regular': '' // 这是修饰符，不是图标名
};

// Material Icons 映射表 (从现有脚本导入)
const MATERIAL_ICONS_MAPPING = {
  'check_circle': 'ti-circle-check',
  'refresh': 'ti-refresh',
  'info': 'ti-info-circle',
  'warning': 'ti-alert-triangle',
  'error': 'ti-alert-circle',
  'close': 'ti-x',
  'check': 'ti-check',
  'search': 'ti-search',
  'edit': 'ti-edit',
  'delete': 'ti-trash',
  'add': 'ti-plus',
  'download': 'ti-download',
  'upload': 'ti-upload',
  'visibility': 'ti-eye',
  'visibility_off': 'ti-eye-off',
  'settings': 'ti-settings',
  'history': 'ti-history',
  'copy': 'ti-copy',
  'content_copy': 'ti-copy'
};

// 批次分类定义
const BATCH_CLASSIFICATION = {
  'P1': [
    'views/admin/network-quality.html',
    'views/admin/analytics.html',
    'views/admin/autodiscovery.html',
    'views/admin/servers/add.html',
    'views/admin/servers/edit.html',
    'views/admin/servers/partials/install-progress-modal.html',
    'views/stat.html',
    'views/stats/card.html'
  ],
  'P2-1': [
    'static/js/card/quick-tag-editor.js',
    'static/js/ai-analytics/ai-dashboard.js',
    'static/js/monitor-optimized.js'
  ],
  'P2-2': [
    // 其余所有文件
  ]
};

/**
 * 解析扫描结果文件
 */
function parseScanResults(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.trim().split('\n');
  const results = [];
  
  lines.forEach(line => {
    const match = line.match(/^([^:]+):(\d+):(.+)$/);
    if (match) {
      const [, file, lineNumber, content] = match;
      
      // 提取图标类名
      const iconMatches = content.match(/(?:fa-[a-z-]+|material-icons|mdi-[a-z-]+|ionicons)/g);
      if (iconMatches) {
        iconMatches.forEach(iconClass => {
          results.push({
            file: file.trim(),
            lineNumber: parseInt(lineNumber),
            content: content.trim(),
            iconClass: iconClass,
            iconType: getIconType(iconClass)
          });
        });
      }
    }
  });
  
  return results;
}

/**
 * 确定图标类型
 */
function getIconType(iconClass) {
  if (iconClass.startsWith('fa-')) return 'FontAwesome';
  if (iconClass === 'material-icons') return 'MaterialIcons';
  if (iconClass.startsWith('mdi-')) return 'MaterialDesignIcons';
  if (iconClass === 'ionicons') return 'Ionicons';
  return 'Unknown';
}

/**
 * 获取建议的Tabler Icons映射
 */
function getSuggestedMapping(iconClass, iconType) {
  if (iconType === 'FontAwesome') {
    return FA_TO_TABLER_MAPPING[iconClass] || 'NEEDS_MANUAL_MAPPING';
  }
  if (iconType === 'MaterialIcons') {
    // Material Icons需要从内容中提取实际图标名
    return 'EXTRACT_FROM_CONTENT';
  }
  return 'UNSUPPORTED_TYPE';
}

/**
 * 分类文件到批次
 */
function classifyToBatch(filePath) {
  if (BATCH_CLASSIFICATION.P1.includes(filePath)) return 'P1';
  if (BATCH_CLASSIFICATION['P2-1'].includes(filePath)) return 'P2-1';
  return 'P2-2';
}

/**
 * 生成CSV清单
 */
function generateCSV(results) {
  const header = 'File,Line,IconClass,IconType,SuggestedMapping,Batch,Content\n';
  const rows = results.map(result => {
    const suggestedMapping = getSuggestedMapping(result.iconClass, result.iconType);
    const batch = classifyToBatch(result.file);
    
    // CSV转义
    const escapedContent = `"${result.content.replace(/"/g, '""')}"`;
    
    return `${result.file},${result.lineNumber},${result.iconClass},${result.iconType},${suggestedMapping},${batch},${escapedContent}`;
  }).join('\n');
  
  return header + rows;
}

/**
 * 主函数
 */
function main() {
  console.log('🔍 开始分析图标使用情况...\n');
  
  const scanResultsFile = 'icon-scan-results.txt';
  if (!fs.existsSync(scanResultsFile)) {
    console.error('❌ 扫描结果文件不存在:', scanResultsFile);
    process.exit(1);
  }
  
  // 解析扫描结果
  const results = parseScanResults(scanResultsFile);
  console.log(`📊 发现 ${results.length} 个图标引用`);
  
  // 统计信息
  const stats = {
    total: results.length,
    byType: {},
    byBatch: {}
  };
  
  results.forEach(result => {
    stats.byType[result.iconType] = (stats.byType[result.iconType] || 0) + 1;
    const batch = classifyToBatch(result.file);
    stats.byBatch[batch] = (stats.byBatch[batch] || 0) + 1;
  });
  
  console.log('\n📈 统计信息:');
  console.log('按类型分布:', stats.byType);
  console.log('按批次分布:', stats.byBatch);
  
  // 生成CSV清单
  const csvContent = generateCSV(results);
  const csvFile = 'reports/icon-usage-inventory.csv';
  
  // 确保reports目录存在
  if (!fs.existsSync('reports')) {
    fs.mkdirSync('reports');
  }
  
  fs.writeFileSync(csvFile, csvContent, 'utf8');
  console.log(`\n✅ 图标使用清单已生成: ${csvFile}`);
  
  // 生成摘要报告
  const summaryReport = {
    timestamp: new Date().toISOString(),
    totalIcons: results.length,
    statistics: stats,
    batchClassification: BATCH_CLASSIFICATION,
    mappingCoverage: {
      fontAwesome: Object.keys(FA_TO_TABLER_MAPPING).length,
      materialIcons: Object.keys(MATERIAL_ICONS_MAPPING).length
    }
  };
  
  fs.writeFileSync('reports/icon-analysis-summary.json', JSON.stringify(summaryReport, null, 2));
  console.log('📋 分析摘要已生成: reports/icon-analysis-summary.json');
}

if (require.main === module) {
  main();
}

module.exports = {
  FA_TO_TABLER_MAPPING,
  MATERIAL_ICONS_MAPPING,
  parseScanResults,
  generateCSV
};
