#!/usr/bin/env node

/**
 * 批量替换Material Icons为Tabler Icons
 * 专门处理JavaScript文件中的innerHTML和模板字符串
 */

const fs = require('fs');
const path = require('path');

// Material Icons 到 Tabler Icons 映射表
const MATERIAL_ICONS_MAPPING = {
  'category': 'ti-category',
  'folder': 'ti-folder',
  'keyboard_arrow_down': 'ti-chevron-down',
  'keyboard_arrow_up': 'ti-chevron-up',
  'edit': 'ti-edit',
  'delete': 'ti-trash',
  'add': 'ti-plus',
  'checklist': 'ti-list-check',
  'select_all': 'ti-select-all',
  'deselect': 'ti-square-off',
  'wifi': 'ti-wifi',
  'computer': 'ti-device-desktop',
  'error_outline': 'ti-alert-circle',
  'refresh': 'ti-refresh',
  'check_circle': 'ti-circle-check',
  'warning': 'ti-alert-triangle',
  'error': 'ti-alert-circle',
  'info': 'ti-info-circle',
  'download': 'ti-download',
  'psychology': 'ti-brain',
  'auto_awesome': 'ti-sparkles',
  'lightbulb': 'ti-bulb',
  'hourglass_empty': 'ti-hourglass-empty',
  'visibility': 'ti-eye',
  'group': 'ti-users',
  'vpn_key': 'ti-key',
  'drag_handle': 'ti-grip-horizontal',
  'more_vert': 'ti-dots-vertical',
  'public': 'ti-world',
  'cancel': 'ti-x',
  'location_on': 'ti-map-pin',
  'upgrade': 'ti-arrow-up-circle',
  'star': 'ti-star',
  'signal_cellular_alt': 'ti-signal-4g',
  'close': 'ti-x',
  'speed': 'ti-gauge',
  'favorite': 'ti-heart',
  'router': 'ti-router',
  'show_chart': 'ti-chart-line',
  'schedule': 'ti-clock',
  'check': 'ti-check'
};

/**
 * 替换单个文件中的Material Icons
 */
function replaceIconsInFile(filePath) {
  console.log(`\n🔄 处理文件: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return 0;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let changes = 0;
  
  // 模式1: <i class="material-icons">icon_name</i>
  const pattern1 = /<i\s+class="material-icons[^"]*">([^<]+)<\/i>/g;
  content = content.replace(pattern1, (match, iconName) => {
    const trimmedIcon = iconName.trim();
    if (MATERIAL_ICONS_MAPPING[trimmedIcon]) {
      const tablerIcon = MATERIAL_ICONS_MAPPING[trimmedIcon];
      changes++;
      console.log(`  ✅ ${trimmedIcon} → ${tablerIcon}`);
      return `<i class="ti ${tablerIcon}"></i>`;
    }
    return match;
  });
  
  // 模式2: <i class="material-icons text-*">icon_name</i>
  const pattern2 = /<i\s+class="material-icons([^"]*)"[^>]*>([^<]+)<\/i>/g;
  content = content.replace(pattern2, (match, additionalClasses, iconName) => {
    const trimmedIcon = iconName.trim();
    if (MATERIAL_ICONS_MAPPING[trimmedIcon]) {
      const tablerIcon = MATERIAL_ICONS_MAPPING[trimmedIcon];
      changes++;
      console.log(`  ✅ ${trimmedIcon} → ${tablerIcon} (with classes)`);
      return `<i class="ti ${tablerIcon}${additionalClasses}"></i>`;
    }
    return match;
  });
  
  // 模式3: 动态图标生成 - 处理模板字符串中的图标引用
  const pattern3 = /\$\{[^}]*\?\s*'([^']+)'\s*:\s*'([^']+)'\}/g;
  content = content.replace(pattern3, (match, icon1, icon2) => {
    let replaced = match;
    if (MATERIAL_ICONS_MAPPING[icon1]) {
      replaced = replaced.replace(icon1, MATERIAL_ICONS_MAPPING[icon1]);
      changes++;
      console.log(`  ✅ 条件图标: ${icon1} → ${MATERIAL_ICONS_MAPPING[icon1]}`);
    }
    if (MATERIAL_ICONS_MAPPING[icon2]) {
      replaced = replaced.replace(icon2, MATERIAL_ICONS_MAPPING[icon2]);
      changes++;
      console.log(`  ✅ 条件图标: ${icon2} → ${MATERIAL_ICONS_MAPPING[icon2]}`);
    }
    return replaced;
  });
  
  // 模式4: 状态配置对象中的图标
  const pattern4 = /icon:\s*'([^']+)'/g;
  content = content.replace(pattern4, (match, iconName) => {
    if (MATERIAL_ICONS_MAPPING[iconName]) {
      const tablerIcon = MATERIAL_ICONS_MAPPING[iconName];
      changes++;
      console.log(`  ✅ 配置图标: ${iconName} → ${tablerIcon}`);
      return `icon: '${tablerIcon}'`;
    }
    return match;
  });
  
  if (changes > 0) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ ${filePath}: 替换了 ${changes} 个图标`);
  } else {
    console.log(`⏸️  ${filePath}: 无需替换`);
  }
  
  return changes;
}

/**
 * 主函数
 */
function main() {
  const files = process.argv.slice(2);
  
  if (files.length === 0) {
    console.log('用法: node batch-replace-material-icons.js <file1> [file2] ...');
    process.exit(1);
  }
  
  console.log('🚀 开始批量替换Material Icons...');
  
  let totalChanges = 0;
  files.forEach(file => {
    totalChanges += replaceIconsInFile(file);
  });
  
  console.log(`\n🎉 替换完成！总计替换了 ${totalChanges} 个图标`);
}

if (require.main === module) {
  main();
}

module.exports = { replaceIconsInFile, MATERIAL_ICONS_MAPPING };
