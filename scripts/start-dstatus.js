#!/usr/bin/env node

/**
 * DStatus 智能启动脚本
 * 自动处理端口占用、提供清晰的启动信息
 */

const { spawn, exec } = require('child_process');
const os = require('os');
const path = require('path');

// 配置
const CONFIG = {
    port: 5555,
    appName: 'DStatus',
    mainFile: 'nekonekostatus.js',
    retryAttempts: 3,
    retryDelay: 2000, // 2秒
};

// 控制台颜色
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
};

// 日志函数
const log = {
    info: (msg) => console.log(`${colors.blue}ℹ${colors.reset}  ${msg}`),
    success: (msg) => console.log(`${colors.green}✓${colors.reset}  ${msg}`),
    error: (msg) => console.log(`${colors.red}✗${colors.reset}  ${msg}`),
    warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset}  ${msg}`),
    step: (msg) => console.log(`${colors.cyan}►${colors.reset}  ${msg}`),
};

// 获取操作系统类型
const isWindows = os.platform() === 'win32';
const isMacOS = os.platform() === 'darwin';
const isLinux = os.platform() === 'linux';

/**
 * 执行命令并返回结果
 */
function execCommand(command) {
    return new Promise((resolve, reject) => {
        exec(command, (error, stdout, stderr) => {
            if (error && !stderr.includes('No such process')) {
                reject(error);
            } else {
                resolve(stdout.trim());
            }
        });
    });
}

/**
 * 获取占用指定端口的进程信息
 */
async function getProcessOnPort(port) {
    try {
        let command;
        if (isWindows) {
            command = `netstat -ano | findstr :${port} | findstr LISTENING`;
        } else if (isMacOS) {
            // macOS 使用 lsof
            command = `lsof -i :${port} -P -n | grep LISTEN`;
        } else {
            // Linux 优先使用 ss，失败后回退到 lsof
            command = `ss -tulpn | grep :${port} || lsof -i :${port} -P -n | grep LISTEN`;
        }

        const result = await execCommand(command);
        if (result) {
            // 解析进程信息
            if (isWindows) {
                const parts = result.split(/\s+/);
                const pid = parts[parts.length - 1];
                return { pid, command: 'unknown' };
            } else {
                const parts = result.split(/\s+/);
                const pid = parts[1];
                const processName = parts[0];
                return { pid, command: processName };
            }
        }
        return null;
    } catch (error) {
        return null;
    }
}

/**
 * 杀死占用端口的进程
 */
async function killProcessOnPort(port) {
    const processInfo = await getProcessOnPort(port);
    
    if (!processInfo) {
        log.info(`端口 ${port} 未被占用`);
        return true;
    }

    log.warning(`发现端口 ${port} 被占用 (PID: ${processInfo.pid}, 进程: ${processInfo.command})`);
    
    try {
        const killCommand = isWindows 
            ? `taskkill /F /PID ${processInfo.pid}`
            : `kill -9 ${processInfo.pid}`;
            
        await execCommand(killCommand);
        log.success(`已终止进程 PID: ${processInfo.pid}`);
        
        // 等待一秒确保端口释放
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 验证端口是否已释放
        const stillOccupied = await getProcessOnPort(port);
        if (stillOccupied) {
            log.error(`端口 ${port} 仍被占用，可能需要管理员权限`);
            return false;
        }
        
        return true;
    } catch (error) {
        log.error(`无法终止进程: ${error.message}`);
        return false;
    }
}

/**
 * 检查 Node.js 版本
 */
function checkNodeVersion() {
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.split('.')[0].substring(1));
    
    if (majorVersion < 20) {
        log.error(`Node.js 版本过低: ${nodeVersion}`);
        log.error(`DStatus 需要 Node.js >= 20.17.0 或 >= 22.9.0`);
        return false;
    }
    
    log.info(`Node.js 版本: ${nodeVersion}`);
    return true;
}

/**
 * 启动 DStatus 服务
 */
function startDStatus(mode = 'production') {
    return new Promise((resolve, reject) => {
        log.step(`启动 ${CONFIG.appName} (${mode} 模式)...`);
        
        const env = { ...process.env };
        const args = [];
        
        // 根据模式选择启动命令
        let command;
        if (mode === 'development') {
            command = 'npm';
            args.push('run', 'dev');
        } else {
            command = 'node';
            args.push(CONFIG.mainFile);
        }
        
        const child = spawn(command, args, {
            env,
            stdio: 'inherit',
            cwd: process.cwd(),
            shell: isWindows
        });

        child.on('error', (error) => {
            log.error(`启动失败: ${error.message}`);
            reject(error);
        });

        child.on('spawn', () => {
            log.success(`${CONFIG.appName} 已启动`);
            log.info(`访问地址: http://localhost:${CONFIG.port}`);
            resolve(child);
        });

        // 处理退出信号
        process.on('SIGINT', () => {
            log.info('收到退出信号，正在关闭服务...');
            child.kill('SIGTERM');
            process.exit(0);
        });

        process.on('SIGTERM', () => {
            child.kill('SIGTERM');
            process.exit(0);
        });
    });
}

/**
 * 主启动流程
 */
async function main() {
    console.log(`\n${colors.bright}${colors.cyan}🚀 ${CONFIG.appName} 启动器${colors.reset}\n`);
    
    // 解析命令行参数
    const args = process.argv.slice(2);
    const mode = args.includes('--dev') ? 'development' : 'production';
    const force = args.includes('--force');
    
    // 显示启动信息
    log.info(`启动模式: ${mode}`);
    log.info(`工作目录: ${process.cwd()}`);
    log.info(`操作系统: ${os.platform()} (${os.arch()})`);
    
    // 检查 Node.js 版本
    if (!checkNodeVersion()) {
        process.exit(1);
    }
    
    // 检查并处理端口占用
    log.step(`检查端口 ${CONFIG.port}...`);
    
    const processOnPort = await getProcessOnPort(CONFIG.port);
    if (processOnPort) {
        if (!force) {
            log.warning(`端口 ${CONFIG.port} 已被占用`);
            log.info(`使用 --force 参数强制终止占用进程`);
            process.exit(1);
        }
        
        const killed = await killProcessOnPort(CONFIG.port);
        if (!killed) {
            log.error('无法释放端口，请手动检查');
            if (!isWindows) {
                log.info(`尝试使用: sudo lsof -i :${CONFIG.port} 查看占用进程`);
                log.info(`然后使用: sudo kill -9 <PID> 终止进程`);
            }
            process.exit(1);
        }
    } else {
        log.success(`端口 ${CONFIG.port} 可用`);
    }
    
    // 启动服务
    let attempts = 0;
    while (attempts < CONFIG.retryAttempts) {
        try {
            await startDStatus(mode);
            break;
        } catch (error) {
            attempts++;
            if (attempts < CONFIG.retryAttempts) {
                log.warning(`启动失败，${CONFIG.retryDelay / 1000} 秒后重试 (${attempts}/${CONFIG.retryAttempts})...`);
                await new Promise(resolve => setTimeout(resolve, CONFIG.retryDelay));
            } else {
                log.error('启动失败，已达到最大重试次数');
                process.exit(1);
            }
        }
    }
}

// 运行主函数
if (require.main === module) {
    main().catch((error) => {
        log.error(`启动器错误: ${error.message}`);
        process.exit(1);
    });
}

module.exports = { killProcessOnPort, startDStatus };