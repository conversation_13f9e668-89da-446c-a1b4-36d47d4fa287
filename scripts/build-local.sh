#!/bin/bash
# DStatus 本地构建脚本 - 现代化版本

set -e

echo "🚀 DStatus 本地构建脚本"
echo "======================"

# 检查 Node.js 版本
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 20 ]; then
  echo "❌ 需要 Node.js 20 或更高版本，当前版本: $(node --version)"
  exit 1
fi

# 设置环境变量
export NODE_ENV=production
export LICENSE_SERVER_URL=${LICENSE_SERVER_URL:-"https://dstatus_api.vps.mom"}

echo "📋 构建信息:"
echo "   Node.js: $(node --version)"
echo "   npm: $(npm --version)"
echo "   License Server: $LICENSE_SERVER_URL"
echo ""

# 清理旧文件
echo "🧹 清理旧文件..."
rm -rf dist/
rm -f dstatus-*.tar.gz

# 注入版本号 (使用现有版本，不自动生成新版本)
echo "🎯 注入版本信息..."
node scripts/inject-version.js

# 安装依赖
echo "📦 安装生产依赖..."
npm ci --production --silent

# 检查并安装 @yao-pkg/pkg
if ! command -v pkg >/dev/null 2>&1; then
  echo "🔧 安装 @yao-pkg/pkg..."
  npm install -g @yao-pkg/pkg
fi

# 显示 pkg 版本
echo "📋 pkg 版本: $(pkg --version)"

# 构建选择
echo ""
echo "选择构建平台:"
echo "1) Linux x64 (默认)"
echo "2) Linux ARM64"
echo "3) macOS ARM64"
echo "4) 全部平台"
echo ""
read -p "请选择 (1-4, 默认为 1): " choice

case "$choice" in
  "2")
    TARGET="node20-linux-arm64"
    PLATFORM="linux-arm64"
    ;;
  "3")
    TARGET="node20-macos-arm64"
    PLATFORM="macos-arm64"
    ;;
  "4")
    echo "🔨 构建全部平台..."
    mkdir -p dist
    
    echo "📦 构建 Linux x64..."
    pkg . -t node20-linux-x64 -o dist/dstatus-linux-x64
    
    echo "📦 构建 Linux ARM64..."
    pkg . -t node20-linux-arm64 -o dist/dstatus-linux-arm64
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
      echo "📦 构建 macOS ARM64..."
      pkg . -t node20-macos-arm64 -o dist/dstatus-macos-arm64
    fi
    
    echo "✅ 全部构建完成！"
    ls -lh dist/
    exit 0
    ;;
  *)
    TARGET="node20-linux-x64"
    PLATFORM="linux-x64"
    ;;
esac

echo "🔨 构建 $PLATFORM 版本..."
mkdir -p dist
pkg . -t "$TARGET" -o "dist/dstatus-$PLATFORM"

# 创建部署包
echo "📦 创建部署包..."
mkdir -p "dist-pkg-$PLATFORM"
cp "dist/dstatus-$PLATFORM" "dist-pkg-$PLATFORM/"
cp -r views "dist-pkg-$PLATFORM/"
cp -r static "dist-pkg-$PLATFORM/"
mkdir -p "dist-pkg-$PLATFORM/data/temp" "dist-pkg-$PLATFORM/data/backups"

# 创建启动脚本
cat > "dist-pkg-$PLATFORM/start.sh" << EOF
#!/bin/bash
cd "\$(dirname "\$0")"
chmod +x dstatus-$PLATFORM
echo "🚀 启动 DStatus ($PLATFORM)..."
echo "🌐 Web 界面: http://localhost:5555"
echo "⚙️ 管理面板：http://localhost:5555/login"
echo ""
./dstatus-$PLATFORM
EOF
chmod +x "dist-pkg-$PLATFORM/start.sh"

# 创建停止脚本
cat > "dist-pkg-$PLATFORM/stop.sh" << 'EOF'
#!/bin/bash
echo "🛑 停止 DStatus..."
pkill -f "dstatus-" || echo "没有运行的实例"
echo "✅ 已停止"
EOF
chmod +x "dist-pkg-$PLATFORM/stop.sh"

# 创建说明文件
cat > "dist-pkg-$PLATFORM/README.md" << EOF
# DStatus $PLATFORM 本地构建包

## 快速启动
\`\`\`bash
./start.sh
\`\`\`

## 停止服务
\`\`\`bash
./stop.sh
\`\`\`

## 访问地址
- Web界面: http://localhost:5555
- 管理面板：http://localhost:5555/login

## 构建信息
- 平台: $PLATFORM
- 构建时间: $(date)
- Node.js 版本: $(node --version)
- License Server: $LICENSE_SERVER_URL
EOF

# 打包
cd "dist-pkg-$PLATFORM"
tar -czf "../dstatus-$PLATFORM-local.tar.gz" .
cd ..

# 清理临时目录
rm -rf "dist-pkg-$PLATFORM"

echo ""
echo "✅ 构建完成！"
echo ""
echo "📦 输出文件: dstatus-$PLATFORM-local.tar.gz"
ls -lh "dstatus-$PLATFORM-local.tar.gz"
echo ""
echo "📋 部署步骤:"
echo "1. 上传文件到服务器"
echo "2. 解压: tar -xzf dstatus-$PLATFORM-local.tar.gz"
echo "3. 启动: ./start.sh"
echo ""