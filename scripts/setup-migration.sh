#!/bin/bash

# DStatus 图标迁移环境设置脚本

echo "🚀 DStatus 图标迁移工具 - 环境设置"
echo "======================================="

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 请先安装 Node.js"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"

# 检查并安装glob包（如果需要）
echo "📦 检查依赖包..."

if ! node -e "require('glob')" 2>/dev/null; then
    echo "📥 安装 glob 包..."
    npm install glob --save-dev
else
    echo "✅ glob 包已安装"
fi

# 设置脚本权限
chmod +x scripts/icon-migration.js

echo ""
echo "🎉 设置完成！现在可以运行迁移脚本："
echo ""
echo "   node scripts/icon-migration.js"
echo ""
echo "💡 迁移前建议："
echo "   1. 备份项目: git add . && git commit -m 'backup before icon migration'"
echo "   2. 确保已配置 Tabler Icons CDN"
echo "   3. 迁移后测试页面显示效果"