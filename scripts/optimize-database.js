#!/usr/bin/env node
'use strict';

const path = require('path');
const fs = require('fs');
const dbConfig = require('../database/config');

async function initDatabase() {
    const dbModule = require('../database/index');
    return await dbModule();
}

// Get database path
const paths = dbConfig.getPaths();
const dbPath = paths.main;

console.log('========================================');
console.log('DStatus Database Optimization');
console.log('========================================');
console.log(`Database: ${dbPath}`);
console.log(`Date: ${new Date().toISOString()}`);
console.log('');

// Backup database first
const backupPath = paths.backup(Date.now());
const backupDir = path.dirname(backupPath);
if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
}

console.log('1. CREATING BACKUP');
console.log('------------------');
console.log(`Backing up to: ${backupPath}`);
fs.copyFileSync(dbPath, backupPath);
console.log('Backup completed successfully.\n');

async function runOptimization() {
    try {
        // Open database using adapter
        const db = await initDatabase();
        
        console.log('2. APPLYING OPTIMIZATIONS');
        console.log('-------------------------');
        
        // Apply SQLite-specific optimizations
        if (db.type === 'sqlite') {
            console.log('Enabling SQLite WAL mode...');
            try {
                await db.DB.run("PRAGMA journal_mode = WAL");
                await db.DB.run("PRAGMA synchronous = NORMAL");
                console.log('✓ WAL mode enabled successfully');
            } catch (err) {
                console.error('✗ Failed to enable WAL mode:', err.message);
            }

            console.log('\nOptimizing SQLite cache size...');
            try {
                await db.DB.run("PRAGMA cache_size = -64000"); // 64MB
                console.log('✓ Cache size set to 64MB');
            } catch (err) {
                console.error('✗ Failed to set cache size:', err.message);
            }

            console.log('\nSetting SQLite busy timeout...');
            try {
                await db.DB.run("PRAGMA busy_timeout = 5000"); // 5 seconds
                console.log('✓ Busy timeout set to 5000ms');
            } catch (err) {
                console.error('✗ Failed to set busy timeout:', err.message);
            }
        } else {
            console.log('PostgreSQL mode: Skipping SQLite-specific optimizations');
        }

        // Create missing indexes
        console.log('\n3. CREATING MISSING INDEXES');
        console.log('---------------------------');

        const indexes = [
            { table: 'lt', column: 'sid' },
            { table: 'traffic', column: 'sid' },
            { table: 'traffic_calibration', column: 'sid' },
            { table: 'tcping_5m', column: 'sid' },
            { table: 'tcping_5m', column: 'created_at' },
            { table: 'tcping_m', column: 'sid' },
            { table: 'tcping_m', column: 'created_at' },
            { table: 'tcping_h', column: 'sid' },
            { table: 'tcping_h', column: 'created_at' },
            { table: 'tcping_d', column: 'sid' },
            { table: 'tcping_d', column: 'created_at' },
            { table: 'tcping_archive', column: 'created_at' },
            { table: 'load_archive', column: 'created_at' },
            { table: 'load_m', column: 'created_at' },
            { table: 'load_h', column: 'created_at' }
        ];

        let indexesCreated = 0;
        for (const { table, column } of indexes) {
            const indexName = `idx_${table}_${column}`;
            try {
                // Check if index already exists (cross-database compatible)
                let existingIndex;
                if (db.type === 'sqlite') {
                    existingIndex = await db.DB.get(`
                        SELECT name FROM sqlite_master 
                        WHERE type='index' AND name=?
                    `, [indexName]);
                } else {
                    // PostgreSQL: check information_schema
                    existingIndex = await db.DB.get(`
                        SELECT indexname FROM pg_indexes 
                        WHERE indexname=?
                    `, [indexName]);
                }
                
                if (!existingIndex) {
                    await db.DB.run(`CREATE INDEX IF NOT EXISTS ${indexName} ON ${table}(${column})`);
                    console.log(`✓ Created index: ${indexName}`);
                    indexesCreated++;
                }
            } catch (err) {
                console.error(`✗ Failed to create index ${indexName}:`, err.message);
            }
        }

        console.log(`\nTotal indexes created: ${indexesCreated}`);

        // Analyze tables for query optimizer
        console.log('\n4. ANALYZING TABLES');
        console.log('-------------------');
        try {
            if (db.type === 'sqlite') {
                await db.DB.run('ANALYZE');
                console.log('✓ SQLite database statistics updated');
            } else {
                await db.DB.run('ANALYZE;');
                console.log('✓ PostgreSQL database statistics updated');
            }
        } catch (err) {
            console.error('✗ Failed to analyze:', err.message);
        }

// Data cleanup for large tables
console.log('\n5. DATA CLEANUP RECOMMENDATIONS');
console.log('--------------------------------');

// Check data retention
const retentionQueries = [
    {
        table: 'tcping_5m',
        query: `SELECT COUNT(*) as total, 
                MIN(created_at) as oldest,
                MAX(created_at) as newest
                FROM tcping_5m WHERE created_at IS NOT NULL`,
        retention: 7 * 24 * 60 * 60 // 7 days
    },
    {
        table: 'tcping_m',
        query: `SELECT COUNT(*) as total,
                MIN(created_at) as oldest,
                MAX(created_at) as newest
                FROM tcping_m WHERE created_at IS NOT NULL`,
        retention: 30 * 24 * 60 * 60 // 30 days
    },
    {
        table: 'load_archive',
        query: `SELECT COUNT(*) as total,
                MIN(created_at) as oldest,
                MAX(created_at) as newest
                FROM load_archive WHERE created_at IS NOT NULL`,
        retention: 30 * 24 * 60 * 60 // 30 days
    }
];

        for (const { table, query, retention } of retentionQueries) {
            try {
                const result = await db.DB.get(query);
                if (result && result.oldest) {
                    const now = Math.floor(Date.now() / 1000);
                    const cutoff = now - retention;
                    const oldRows = await db.DB.get(`
                        SELECT COUNT(*) as count 
                        FROM ${table} 
                        WHERE created_at < ?
                    `, [cutoff]);
                    
                    if (oldRows.count > 0) {
                        console.log(`\n${table}:`);
                        console.log(`  Total rows: ${result.total}`);
                        console.log(`  Rows older than retention period: ${oldRows.count}`);
                        console.log(`  Recommended cleanup command:`);
                        console.log(`  DELETE FROM ${table} WHERE created_at < ${cutoff};`);
                    }
                }
            } catch (err) {
                // Ignore errors for tables without created_at
            }
        }

// Create cleanup script
console.log('\n6. CREATING CLEANUP SCRIPT');
console.log('--------------------------');

const cleanupScript = `#!/usr/bin/env node
'use strict';

const Database = require('better-sqlite3');
const dbConfig = require('../database/config');

// This script cleans up old data from large tables
// Run this periodically (e.g., daily via cron)

const db = new Database(dbConfig.getPaths().main);

console.log('Starting database cleanup...');

// Cleanup configuration
const cleanupTasks = [
    {
        table: 'tcping_5m',
        retentionDays: 7,
        description: '5-minute TCPing data'
    },
    {
        table: 'tcping_m',
        retentionDays: 30,
        description: 'Minute-level TCPing data'
    },
    {
        table: 'load_archive',
        retentionDays: 30,
        description: 'Load archive data'
    }
];

const now = Math.floor(Date.now() / 1000);

cleanupTasks.forEach(task => {
    const cutoff = now - (task.retentionDays * 24 * 60 * 60);
    
    try {
        console.log(\`\\nCleaning up \${task.description} older than \${task.retentionDays} days...\`);
        
        // Count rows to delete
        const toDelete = db.prepare(\`
            SELECT COUNT(*) as count 
            FROM \${task.table} 
            WHERE created_at < ?
        \`).get(cutoff);
        
        if (toDelete.count > 0) {
            // Delete old data
            const result = db.prepare(\`
                DELETE FROM \${task.table} 
                WHERE created_at < ?
            \`).run(cutoff);
            
            console.log(\`  Deleted \${result.changes} rows from \${task.table}\`);
        } else {
            console.log(\`  No old data to clean up in \${task.table}\`);
        }
    } catch (err) {
        console.error(\`  Error cleaning up \${task.table}:\`, err.message);
    }
});

// Vacuum to reclaim space
console.log('\\nRunning VACUUM to reclaim space...');
try {
    db.prepare('VACUUM').run();
    console.log('✓ Database vacuumed successfully');
} catch (err) {
    console.error('✗ Failed to vacuum:', err.message);
}

db.close();
console.log('\\nCleanup completed.');
`;

fs.writeFileSync(path.join(__dirname, 'cleanup-database.js'), cleanupScript);
console.log('Created cleanup script: scripts/cleanup-database.js');

// Final recommendations
console.log('\n7. POST-OPTIMIZATION RECOMMENDATIONS');
console.log('------------------------------------');
console.log('1. Schedule regular cleanup:');
console.log('   - Add to cron: 0 2 * * * node /path/to/scripts/cleanup-database.js');
console.log('');
console.log('2. Monitor database size:');
console.log('   - Check weekly: node scripts/analyze-database.js');
console.log('');
console.log('3. Update application code:');
console.log('   - Ensure proper transaction handling');
console.log('   - Use prepared statements for better performance');
console.log('');
console.log('4. Consider implementing:');
console.log('   - Data aggregation for historical data');
console.log('   - Separate archive database for old data');

        // Check final database state
        console.log('\n8. FINAL DATABASE STATE');
        console.log('-----------------------');
        
        if (db.type === 'sqlite') {
            const finalPragmas = ['journal_mode', 'synchronous', 'cache_size', 'busy_timeout'];
            for (const pragma of finalPragmas) {
                try {
                    const result = await db.DB.get(`PRAGMA ${pragma}`);
                    console.log(`${pragma}: ${result[pragma]}`);
                } catch (err) {
                    console.log(`${pragma}: Unable to retrieve`);
                }
            }
        } else {
            console.log('PostgreSQL configuration optimizations applied');
        }

        await db.DB.disconnect();

        console.log('\n========================================');
        console.log('Optimization Complete');
        console.log('========================================');
        console.log('\nIMPORTANT: Restart the DStatus application to ensure all');
        console.log('optimizations take effect properly.');

    } catch (error) {
        console.error('Optimization failed:', error);
        process.exit(1);
    }
}

// Run the optimization
runOptimization();