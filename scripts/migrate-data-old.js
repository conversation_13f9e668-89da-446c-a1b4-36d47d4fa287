#!/usr/bin/env node
/**
 * DStatus 数据库迁移工具 - MVP版本
 * 支持SQLite和PostgreSQL之间的数据迁移
 * 
 * 使用方法：
 * node scripts/migrate-data.js --export servers --output test.json
 * node scripts/migrate-data.js --import test.json --target postgresql
 * node scripts/migrate-data.js --verify
 */

const path = require('path');
const fs = require('fs').promises;

// 设置工作目录到项目根目录
process.chdir(path.join(__dirname, '..'));

class DataMigrator {
    constructor() {
        this.db = null;
        this.targetDb = null;
        this.supportedTables = [
            'servers',
            'groups', 
            'traffic',
            'load_m',
            'load_h', 
            'load_archive',
            'ssh_scripts',
            'setting'
        ];
    }

    /**
     * 初始化数据库连接
     */
    async initialize() {
        try {
            console.log('🔧 正在初始化数据库连接...');
            this.db = await require('../database/index.js')();
            console.log(`✅ 数据库连接成功 (${this.db.type})`);
            return true;
        } catch (error) {
            console.error('❌ 数据库连接失败:', error.message);
            return false;
        }
    }

    /**
     * 导出指定表的数据到JSON文件
     */
    async exportTableData(tableName, outputFile) {
        console.log(`\n📤 开始导出表: ${tableName}`);
        
        if (!this.supportedTables.includes(tableName)) {
            throw new Error(`不支持的表名: ${tableName}`);
        }

        try {
            // 查询表数据
            const data = await this.db.all(`SELECT * FROM ${tableName}`);
            console.log(`   📊 找到 ${data.length} 条记录`);

            // 构建导出对象
            const exportData = {
                version: '1.0.0',
                exported_at: new Date().toISOString(),
                source_database: this.db.type,
                table: tableName,
                record_count: data.length,
                data: data
            };

            // 写入文件
            await fs.writeFile(outputFile, JSON.stringify(exportData, null, 2));
            console.log(`   ✅ 数据已导出到: ${outputFile}`);
            
            return {
                table: tableName,
                records: data.length,
                file: outputFile
            };

        } catch (error) {
            console.error(`   ❌ 导出失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 从JSON文件导入数据到目标数据库
     */
    async importTableData(inputFile, targetType = 'postgresql') {
        console.log(`\n📥 开始导入数据: ${inputFile}`);
        
        try {
            // 读取文件
            const fileContent = await fs.readFile(inputFile, 'utf8');
            const importData = JSON.parse(fileContent);
            
            console.log(`   📋 文件信息:`);
            console.log(`      版本: ${importData.version}`);
            console.log(`      源数据库: ${importData.source_database}`);
            console.log(`      目标表: ${importData.table}`);
            console.log(`      记录数: ${importData.record_count}`);

            // 如果目标类型与当前数据库类型相同，跳过导入
            if (this.db.type === targetType) {
                console.log(`   ⚠️ 当前数据库已经是 ${targetType}，跳过导入`);
                return {
                    skipped: true,
                    reason: '目标数据库类型与当前相同'
                };
            }

            // TODO: 实现切换到目标数据库的逻辑
            console.log(`   ⚠️ 目标数据库 ${targetType} 连接功能待实现`);
            console.log(`   📊 将导入 ${importData.data.length} 条记录到表 ${importData.table}`);

            // MVP版本：仅模拟导入过程
            let importedCount = 0;
            for (const record of importData.data) {
                // 在完整版本中，这里会执行实际的插入操作
                importedCount++;
                
                // 显示进度（每100条记录）
                if (importedCount % 100 === 0 || importedCount === importData.data.length) {
                    console.log(`   📈 已处理: ${importedCount}/${importData.data.length}`);
                }
            }

            console.log(`   ✅ 导入完成: ${importedCount} 条记录`);
            
            return {
                table: importData.table,
                imported: importedCount,
                target: targetType
            };

        } catch (error) {
            console.error(`   ❌ 导入失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 验证数据完整性
     */
    async verifyData() {
        console.log(`\n🔍 开始数据完整性验证...`);
        
        try {
            const results = {};
            
            for (const tableName of this.supportedTables) {
                try {
                    const count = await this.db.get(`SELECT COUNT(*) as count FROM ${tableName}`);
                    results[tableName] = count.count;
                    console.log(`   📊 ${tableName}: ${count.count} 条记录`);
                } catch (error) {
                    results[tableName] = `错误: ${error.message}`;
                    console.log(`   ❌ ${tableName}: 查询失败 - ${error.message}`);
                }
            }

            console.log(`\n📈 数据库摘要:`);
            const totalRecords = Object.values(results).reduce((sum, count) => {
                return typeof count === 'number' ? sum + count : sum;
            }, 0);
            console.log(`   总记录数: ${totalRecords}`);
            console.log(`   数据库类型: ${this.db.type}`);
            
            return results;

        } catch (error) {
            console.error(`   ❌ 验证失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 清理资源
     */
    async cleanup() {
        if (this.db && this.db.DB && typeof this.db.DB.disconnect === 'function') {
            try {
                await this.db.DB.disconnect();
                console.log('🧹 数据库连接已关闭');
            } catch (error) {
                console.error('⚠️ 关闭数据库连接时出错:', error.message);
            }
        }
    }
}

/**
 * 命令行参数解析
 */
function parseArgs() {
    const args = process.argv.slice(2);
    const options = {};
    
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        if (arg.startsWith('--')) {
            const key = arg.substring(2);
            const value = args[i + 1] && !args[i + 1].startsWith('--') ? args[i + 1] : true;
            options[key] = value;
            if (value !== true) i++; // 跳过已处理的值
        }
    }
    
    return options;
}

/**
 * 显示使用帮助
 */
function showHelp() {
    console.log(`
🔄 DStatus 数据库迁移工具 - MVP版本

使用方法:
  node scripts/migrate-data.js --export <table> --output <file>  导出指定表到JSON文件
  node scripts/migrate-data.js --import <file> [--target <db>]   从JSON文件导入数据
  node scripts/migrate-data.js --verify                         验证数据完整性
  node scripts/migrate-data.js --help                          显示此帮助信息

支持的表:
  servers, groups, traffic, load_m, load_h, load_archive, ssh_scripts, setting

示例:
  node scripts/migrate-data.js --export servers --output servers.json
  node scripts/migrate-data.js --import servers.json --target postgresql
  node scripts/migrate-data.js --verify

参数说明:
  --export <table>    要导出的表名
  --output <file>     导出文件路径
  --import <file>     要导入的JSON文件
  --target <db>       目标数据库类型 (postgresql|sqlite)
  --verify           验证当前数据库的数据完整性
  --help             显示帮助信息
`);
}

/**
 * 主函数
 */
async function main() {
    const options = parseArgs();
    
    // 显示帮助
    if (options.help) {
        showHelp();
        return;
    }

    const migrator = new DataMigrator();
    
    try {
        // 初始化
        const initialized = await migrator.initialize();
        if (!initialized) {
            process.exit(1);
        }

        // 导出操作
        if (options.export) {
            if (!options.output) {
                console.error('❌ 导出操作需要指定 --output 参数');
                process.exit(1);
            }
            
            await migrator.exportTableData(options.export, options.output);
        }
        
        // 导入操作
        else if (options.import) {
            const targetType = options.target || 'postgresql';
            await migrator.importTableData(options.import, targetType);
        }
        
        // 验证操作
        else if (options.verify) {
            await migrator.verifyData();
        }
        
        // 没有指定操作
        else {
            console.error('❌ 请指定操作类型 (--export, --import, 或 --verify)');
            console.log('使用 --help 查看详细说明');
            process.exit(1);
        }

        console.log('\n✅ 操作完成');
        
    } catch (error) {
        console.error('\n❌ 操作失败:', error.message);
        process.exit(1);
    } finally {
        await migrator.cleanup();
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { DataMigrator };