#!/usr/bin/env node
"use strict";

/**
 * 数据库迁移工具
 * 支持 SQLite 和 PostgreSQL 之间的双向数据迁移
 */

const path = require('path');
const fs = require('fs');

// 导入数据库适配器
const SQLiteAdapter = require('../database/adapters/sqlite');
const PostgreSQLAdapter = require('../database/adapters/postgresql');

// 导入迁移模块
const DataExporter = require('../database/migration/exporter');
const DataImporter = require('../database/migration/importer');

class MigrationTool {
    constructor() {
        this.sourceAdapter = null;
        this.targetAdapter = null;
        this.exporter = null;
        this.importer = null;
    }

    /**
     * 解析命令行参数
     */
    parseArgs() {
        const args = process.argv.slice(2);
        const options = {};

        for (let i = 0; i < args.length; i++) {
            const arg = args[i];
            
            if (arg === '--source' && i + 1 < args.length) {
                options.source = args[++i];
            } else if (arg === '--target' && i + 1 < args.length) {
                options.target = args[++i];
            } else if (arg === '--sqlite-path' && i + 1 < args.length) {
                options.sqlitePath = args[++i];
            } else if (arg === '--postgresql-url' && i + 1 < args.length) {
                options.postgresqlUrl = args[++i];
            } else if (arg === '--batch-size' && i + 1 < args.length) {
                options.batchSize = parseInt(args[++i], 10);
            } else if (arg === '--help' || arg === '-h') {
                this.showHelp();
                process.exit(0);
            } else if (arg === '--dry-run') {
                options.dryRun = true;
            } else if (arg === '--tables' && i + 1 < args.length) {
                options.tables = args[++i].split(',');
            }
        }

        return options;
    }

    /**
     * 显示帮助信息
     */
    showHelp() {
        console.log(`
DStatus 数据库迁移工具

用法:
  node scripts/migrate-data.js --source <SOURCE_DB> --target <TARGET_DB> [选项]

必需参数:
  --source <TYPE>      源数据库类型 (sqlite | postgresql)
  --target <TYPE>      目标数据库类型 (sqlite | postgresql)

可选参数:
  --sqlite-path <PATH>        SQLite 数据库文件路径 (默认: ./data/db.db)
  --postgresql-url <URL>      PostgreSQL 连接字符串
  --batch-size <SIZE>         批量导入大小 (默认: 100)
  --dry-run                   仅显示统计信息，不执行实际迁移
  --tables <TABLES>           指定要迁移的表，逗号分隔 (默认: 所有表)
  --help, -h                  显示此帮助信息

示例:
  # SQLite 迁移到 PostgreSQL
  node scripts/migrate-data.js --source sqlite --target postgresql

  # 指定自定义路径和连接字符串
  node scripts/migrate-data.js \\
    --source sqlite --target postgresql \\
    --sqlite-path ./custom/path/db.db \\
    --postgresql-url "postgresql://user:pass@localhost:5432/db"

  # 仅迁移特定表
  node scripts/migrate-data.js \\
    --source sqlite --target postgresql \\
    --tables groups,servers

  # 干运行模式（仅显示统计信息）
  node scripts/migrate-data.js \\
    --source sqlite --target postgresql \\
    --dry-run
        `);
    }

    /**
     * 创建数据库适配器
     */
    async createAdapter(type, options) {
        if (type === 'sqlite') {
            const dbPath = options.sqlitePath || path.join(__dirname, '../data/db.db');
            
            if (!fs.existsSync(dbPath)) {
                throw new Error(`SQLite 数据库文件不存在: ${dbPath}`);
            }
            
            const adapter = new SQLiteAdapter({ path: dbPath });
            await adapter.connect();
            return adapter;
        } else if (type === 'postgresql') {
            const connection = options.postgresqlUrl || process.env.DATABASE_URL;
            
            if (!connection) {
                throw new Error('PostgreSQL 连接字符串未提供，请使用 --postgresql-url 参数或设置 DATABASE_URL 环境变量');
            }
            
            const adapter = new PostgreSQLAdapter({ connection });
            await adapter.connect();
            return adapter;
        } else {
            throw new Error(`不支持的数据库类型: ${type}`);
        }
    }

    /**
     * 获取默认的表迁移顺序
     */
    getDefaultTableOrder() {
        return [
            'groups',
            'servers',
            'traffic',
            'lt',
            'traffic_calibration',
            'autodiscovery_servers'
        ];
    }

    /**
     * 执行数据迁移
     */
    async migrate(options) {
        const startTime = Date.now();
        
        try {
            // 创建适配器
            console.log('[迁移] 连接数据库...');
            this.sourceAdapter = await this.createAdapter(options.source, options);
            this.targetAdapter = await this.createAdapter(options.target, options);
            
            // 创建导出器和导入器
            this.exporter = new DataExporter(this.sourceAdapter);
            this.importer = new DataImporter(this.targetAdapter);
            
            // 设置批量大小
            if (options.batchSize) {
                this.importer.setBatchSize(options.batchSize);
            }
            
            // 获取要迁移的表
            const tables = options.tables || this.getDefaultTableOrder();
            
            // 获取统计信息
            console.log('[迁移] 收集源数据库统计信息...');
            const stats = await this.exporter.getExportStatistics();
            
            // 显示统计信息
            this.showStatistics(stats, tables);
            
            if (options.dryRun) {
                console.log('[迁移] 干运行模式，跳过实际迁移');
                return;
            }
            
            // 开始迁移
            console.log('[迁移] 开始数据迁移...');
            const migrationStats = {};
            
            for (const table of tables) {
                if (stats[table] === 0) {
                    console.log(`[迁移] 跳过空表 ${table}`);
                    migrationStats[table] = 0;
                    continue;
                }
                
                try {
                    const count = await this.migrateTable(table);
                    migrationStats[table] = count;
                } catch (error) {
                    console.error(`[迁移] 表 ${table} 迁移失败:`, error.message);
                    throw error;
                }
            }
            
            // 显示迁移结果
            const endTime = Date.now();
            const duration = (endTime - startTime) / 1000;
            
            console.log('\n[迁移] 迁移完成！');
            console.log('='.repeat(50));
            console.log('迁移统计:');
            
            let totalMigrated = 0;
            for (const [table, count] of Object.entries(migrationStats)) {
                console.log(`  ${table}: ${count} 条记录`);
                totalMigrated += count;
            }
            
            console.log(`\n总计: ${totalMigrated} 条记录`);
            console.log(`耗时: ${duration.toFixed(2)} 秒`);
            console.log(`平均速度: ${(totalMigrated / duration).toFixed(2)} 条/秒`);
            
        } catch (error) {
            console.error('[迁移] 迁移失败:', error.message);
            throw error;
        } finally {
            // 关闭数据库连接
            if (this.sourceAdapter) {
                await this.sourceAdapter.disconnect();
            }
            if (this.targetAdapter) {
                await this.targetAdapter.disconnect();
            }
        }
    }

    /**
     * 迁移单个表
     */
    async migrateTable(tableName) {
        const exportMethod = `export${tableName.charAt(0).toUpperCase() + tableName.slice(1).replace(/_([a-z])/g, (m, c) => c.toUpperCase())}`;
        const importMethod = `import${tableName.charAt(0).toUpperCase() + tableName.slice(1).replace(/_([a-z])/g, (m, c) => c.toUpperCase())}`;
        
        if (typeof this.exporter[exportMethod] !== 'function') {
            throw new Error(`不支持导出表 ${tableName}: 方法 ${exportMethod} 不存在`);
        }
        
        if (typeof this.importer[importMethod] !== 'function') {
            throw new Error(`不支持导入表 ${tableName}: 方法 ${importMethod} 不存在`);
        }
        
        const dataStream = this.exporter[exportMethod]();
        return await this.importer[importMethod](dataStream);
    }

    /**
     * 显示统计信息
     */
    showStatistics(stats, tables) {
        console.log('\n源数据库统计信息:');
        console.log('='.repeat(30));
        
        let totalRecords = 0;
        for (const table of tables) {
            const count = stats[table] || 0;
            console.log(`  ${table}: ${count} 条记录`);
            totalRecords += count;
        }
        
        console.log(`\n总计: ${totalRecords} 条记录`);
        console.log('');
    }
}

// 主函数
async function main() {
    const tool = new MigrationTool();
    const options = tool.parseArgs();
    
    // 验证必需参数
    if (!options.source || !options.target) {
        console.error('错误: 缺少必需参数 --source 和 --target');
        tool.showHelp();
        process.exit(1);
    }
    
    if (options.source === options.target) {
        console.error('错误: 源数据库和目标数据库类型不能相同');
        process.exit(1);
    }
    
    try {
        await tool.migrate(options);
        console.log('\n迁移成功完成！');
        process.exit(0);
    } catch (error) {
        console.error('\n迁移失败:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此脚本，则执行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('未处理的错误:', error);
        process.exit(1);
    });
}

module.exports = MigrationTool;