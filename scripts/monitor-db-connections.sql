-- PostgreSQL 数据库连接监控脚本
-- 用于监控Phase 0实施后的数据库连接状态

-- 1. 查看当前活跃连接数
SELECT count(*) as active_connections,
       'Active connections' as metric
FROM pg_stat_activity 
WHERE state = 'active';

-- 2. 查看连接分布
SELECT datname as database,
       state,
       count(*) as connection_count
FROM pg_stat_activity
WHERE datname IS NOT NULL
GROUP BY datname, state
ORDER BY datname, state;

-- 3. 查看长时间运行的查询
SELECT pid,
       now() - pg_stat_activity.query_start AS duration,
       query,
       state
FROM pg_stat_activity
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
  AND state != 'idle'
ORDER BY duration DESC;

-- 4. 查看连接池使用情况
SELECT count(*) as total_connections,
       sum(case when state = 'active' then 1 else 0 end) as active,
       sum(case when state = 'idle' then 1 else 0 end) as idle,
       sum(case when state = 'idle in transaction' then 1 else 0 end) as idle_in_transaction
FROM pg_stat_activity
WHERE datname = current_database();

-- 5. 查看是否接近连接限制
SELECT setting::int as max_connections,
       (SELECT count(*) FROM pg_stat_activity) as current_connections,
       setting::int - (SELECT count(*) FROM pg_stat_activity) as available_connections,
       ROUND(((SELECT count(*) FROM pg_stat_activity)::numeric / setting::int) * 100, 2) as usage_percentage
FROM pg_settings
WHERE name = 'max_connections';

-- 6. 建议的告警阈值
SELECT 
  CASE 
    WHEN (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') > 30 THEN '⚠️ 警告: 活跃连接数超过30！'
    WHEN ((SELECT count(*) FROM pg_stat_activity)::numeric / (SELECT setting::int FROM pg_settings WHERE name = 'max_connections')) > 0.8 THEN '⚠️ 警告: 连接池使用率超过80%！'
    ELSE '✅ 数据库连接状态正常'
  END as status;