#!/bin/bash

# DStatus 发布构建脚本
# 处理版本发布的所有逻辑，包括文件命名、上传、Release创建等

set -e

# 参数解析
RELEASE_TYPE="${1:-stable}"
BUILD_NUMBER="${2:-1}"
GITHUB_TOKEN="${3:-}"
DSTATUS_API_KEY="${4:-}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 版本信息计算
calculate_version_info() {
    # 生成基于日期的版本号格式：YY.MM.BUILD_NUMBER
    local YEAR=$(date +%y)  # 25 (2025)
    local MONTH=$(date +%m) # 07 (7月)
    local DATE_VERSION="${YEAR}.${MONTH}.${BUILD_NUMBER}"
    
    if [ "$RELEASE_TYPE" = "beta" ]; then
        VERSION_TAG="v${DATE_VERSION}-beta"
        RELEASE_NAME="DStatus Beta ${DATE_VERSION}"
        DOCKER_PACKAGE="dstatus-docker-v${DATE_VERSION}-beta.tar.gz"
        COMPLETE_PACKAGE="dstatus-complete-v${DATE_VERSION}-beta.tar.gz"
        DOCKER_TARGET="beta/dstatus-docker-beta.tar.gz"
        COMPLETE_TARGET="beta/dstatus-complete-beta.tar.gz"
        BINARY_TARGET="beta/dstatus-linux-x64-beta"
        INSTALL_TARGET="beta/install-beta.sh"
        INSTALL_SCRIPT="scripts/install-beta.sh"
        PRERELEASE="true"
        MAKE_LATEST="false"
    else
        VERSION_TAG="v${DATE_VERSION}"
        RELEASE_NAME="DStatus ${DATE_VERSION}"
        DOCKER_PACKAGE="dstatus-docker-v${DATE_VERSION}.tar.gz"
        COMPLETE_PACKAGE="dstatus-complete-v${DATE_VERSION}.tar.gz"
        DOCKER_TARGET="dstatus-docker.tar.gz"
        COMPLETE_TARGET="dstatus-complete.tar.gz"
        BINARY_TARGET="dstatus-linux-x64"
        INSTALL_TARGET="install.sh"
        INSTALL_SCRIPT="scripts/install.sh"
        PRERELEASE="false"
        MAKE_LATEST="true"
    fi
    
    # 将版本号保存到文件以供后续使用
    echo "${DATE_VERSION}" > version.txt
    echo "生成版本号: ${VERSION_TAG}"
}

# 创建 GitHub Release
create_github_release() {
    log "创建 GitHub Release: $VERSION_TAG"
    
    # 生成 Release 描述
    local release_body
    if [ "$RELEASE_TYPE" = "beta" ]; then
        release_body="## DStatus Beta Release

⚠️ **内部测试版本** - 仅供开发和测试使用，请勿在生产环境中使用！

**Build Info:**
- Node.js version: 20
- PKG tool: @yao-pkg/pkg
- Platform: Linux x64
- Build number: #${BUILD_NUMBER}

**Downloads:**
1. **Docker 一键部署包** (强烈推荐): \`${DOCKER_PACKAGE}\`
2. **完整部署包**: \`${COMPLETE_PACKAGE}\`
3. **仅二进制文件**: \`dstatus-linux-x64\`

**Beta 版本一键安装:**
\`\`\`bash
curl -fsSL https://down.vps.mom/downloads/beta/install-beta.sh | bash -s -- --license-key=\"YOUR_LICENSE_KEY\"
\`\`\`"
    else
        release_body="## DStatus Complete Release

**Build Info:**
- Node.js version: 20
- PKG tool: @yao-pkg/pkg
- Platform: Linux x64
- Build number: #${BUILD_NUMBER}

**Downloads:**
1. **Docker 一键部署包** (强烈推荐): \`${DOCKER_PACKAGE}\`
2. **完整部署包**: \`${COMPLETE_PACKAGE}\`
3. **仅二进制文件**: \`dstatus-linux-x64\`

**Docker 一键安装:**
\`\`\`bash
curl -fsSL https://down.vps.mom/downloads/install.sh | bash -s -- --license-key=\"YOUR_LICENSE_KEY\"
\`\`\`"
    fi
    
    # 使用 gh CLI 创建 Release
    gh release create "$VERSION_TAG" \
        --title "$RELEASE_NAME" \
        --notes "$release_body" \
        --prerelease="$PRERELEASE" \
        --latest="$MAKE_LATEST" \
        --draft=false \
        "dstatus-linux-x64" \
        "$DOCKER_PACKAGE" \
        "$COMPLETE_PACKAGE"
}

# 上传文件到 down.vps.mom
upload_to_vps() {
    log "开始上传文件到 down.vps.mom..."
    
    if [ -z "$DSTATUS_API_KEY" ]; then
        warn "DSTATUS_API_KEY 未设置，跳过上传"
        return 0
    fi
    
    # 上传函数
    upload_file() {
        local file_path="$1"
        local target_name="$2"
        
        if [ ! -f "$file_path" ]; then
            error "文件不存在: $file_path"
            return 1
        fi
        
        local file_size=$(du -h "$file_path" | cut -f1)
        log "上传文件: $(basename "$file_path") ($file_size)"
        
        local response=$(curl -s -X POST \
            -H "X-API-Key: $DSTATUS_API_KEY" \
            -F "file=@$file_path" \
            -F "target_filename=$target_name" \
            "https://down.vps.mom/upload-to-vps.php")

            
        
        if echo "$response" | jq -e '.success' >/dev/null 2>&1; then
            success "上传成功: $target_name"
            return 0
        else
            error "上传失败: $(echo "$response" | jq -r '.error // "Unknown error"')"
            return 1
        fi
    }
    
    # 上传文件
    local success_count=0
    local total_count=0
    
    # 上传 Docker 包
    if [ -f "$DOCKER_PACKAGE" ]; then
        total_count=$((total_count + 1))
        if upload_file "$DOCKER_PACKAGE" "$DOCKER_TARGET"; then
            success_count=$((success_count + 1))
        fi
    fi
    
    # 上传完整包
    if [ -f "$COMPLETE_PACKAGE" ]; then
        total_count=$((total_count + 1))
        if upload_file "$COMPLETE_PACKAGE" "$COMPLETE_TARGET"; then
            success_count=$((success_count + 1))
        fi
    fi
    
    # 上传二进制文件
    if [ -f "dstatus-linux-x64" ]; then
        total_count=$((total_count + 1))
        if upload_file "dstatus-linux-x64" "$BINARY_TARGET"; then
            success_count=$((success_count + 1))
        fi
    fi
    
    # 上传安装脚本
    if [ -f "$INSTALL_SCRIPT" ]; then
        total_count=$((total_count + 1))
        if upload_file "$INSTALL_SCRIPT" "$INSTALL_TARGET"; then
            success_count=$((success_count + 1))
        fi
    fi
    
    log "上传结果: $success_count/$total_count 成功"
    
    if [ $success_count -eq $total_count ] && [ $total_count -gt 0 ]; then
        success "所有文件上传成功！"
        print_download_links
    else
        warn "部分文件上传失败"
    fi
}

# 打印下载链接
print_download_links() {
    log "下载链接:"
    if [ "$RELEASE_TYPE" = "beta" ]; then
        echo "   Docker部署包: https://down.vps.mom/downloads/beta/dstatus-docker-beta.tar.gz"
        echo "   完整部署包: https://down.vps.mom/downloads/beta/dstatus-complete-beta.tar.gz"
        echo "   二进制文件: https://down.vps.mom/downloads/beta/dstatus-linux-x64-beta"
        echo "   安装脚本: https://down.vps.mom/downloads/beta/install-beta.sh"
    else
        echo "   Docker部署包: https://down.vps.mom/downloads/dstatus-docker.tar.gz"
        echo "   完整部署包: https://down.vps.mom/downloads/dstatus-complete.tar.gz"
        echo "   二进制文件: https://down.vps.mom/downloads/dstatus-linux-x64"
        echo "   安装脚本: https://down.vps.mom/downloads/install.sh"
    fi
}

# 生成 GitHub Actions 摘要
generate_summary() {
    log "生成构建摘要..."
    
    if [ "$RELEASE_TYPE" = "beta" ]; then
        cat >> "$GITHUB_STEP_SUMMARY" << EOF
## 📝 测试版本构建完成

⚠️ **注意**: 这是测试版本，仅供开发和测试使用！

### 📦 生成的文件:
- Linux x64 二进制文件: dstatus-linux-x64
- Docker部署包: $DOCKER_PACKAGE
- 完整部署包: $COMPLETE_PACKAGE

### 🚀 GitHub Release:
Beta Release已创建，标记为预发布版本

### 📤 下载链接:
- Docker部署包: https://down.vps.mom/downloads/beta/dstatus-docker-beta.tar.gz
- 完整部署包: https://down.vps.mom/downloads/beta/dstatus-complete-beta.tar.gz
- 二进制文件: https://down.vps.mom/downloads/beta/dstatus-linux-x64-beta
- 安装脚本: https://down.vps.mom/downloads/beta/install-beta.sh

### 🚀 一键安装:
\`\`\`bash
curl -fsSL https://down.vps.mom/downloads/beta/install-beta.sh | bash -s -- --license-key="YOUR_LICENSE_KEY"
\`\`\`
EOF
    else
        cat >> "$GITHUB_STEP_SUMMARY" << EOF
## 🎉 构建完成

### 📦 生成的文件:
- Linux x64 二进制文件: dstatus-linux-x64
- Docker部署包: $DOCKER_PACKAGE
- 完整部署包: $COMPLETE_PACKAGE

### 🚀 GitHub Release:
Release已创建，包含所有构建文件

### 📤 下载链接:
- Docker部署包: https://down.vps.mom/downloads/dstatus-docker.tar.gz
- 完整部署包: https://down.vps.mom/downloads/dstatus-complete.tar.gz
- 二进制文件: https://down.vps.mom/downloads/dstatus-linux-x64
- 安装脚本: https://down.vps.mom/downloads/install.sh

### 🚀 一键安装:
\`\`\`bash
curl -fsSL https://down.vps.mom/downloads/install.sh | bash -s -- --license-key="YOUR_LICENSE_KEY"
\`\`\`
EOF
    fi
}

# 主函数
main() {
    log "开始 DStatus 发布构建..."
    log "发布类型: $RELEASE_TYPE"
    log "构建号: $BUILD_NUMBER"
    
    # 计算版本信息
    calculate_version_info
    
    # 验证必要文件存在
    if [ ! -f "dstatus-linux-x64" ]; then
        error "二进制文件不存在: dstatus-linux-x64"
        exit 1
    fi
    
    # 创建 GitHub Release
    if [ -n "$GITHUB_TOKEN" ]; then
        create_github_release
    else
        warn "GITHUB_TOKEN 未设置，跳过创建 Release"
    fi
    
    # 上传文件
    upload_to_vps
    
    # 生成摘要
    if [ -n "$GITHUB_STEP_SUMMARY" ]; then
        generate_summary
    fi
    
    success "发布构建完成！"
}

# 运行主函数
main "$@"