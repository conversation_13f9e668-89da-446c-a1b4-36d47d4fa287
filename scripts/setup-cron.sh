#!/bin/bash

# DStatus 数据库自动维护任务设置脚本

echo "=== DStatus 数据库自动维护设置 ==="
echo ""

# 获取当前脚本目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 创建 cron 任务
CRON_JOBS="
# DStatus 数据库自动维护任务
# 每天凌晨 3 点执行数据清理
0 3 * * * cd $PROJECT_ROOT && npm run cleanup >> $PROJECT_ROOT/data/logs/cron-cleanup.log 2>&1

# 每周日凌晨 4 点执行数据库优化
0 4 * * 0 cd $PROJECT_ROOT && node scripts/optimize-database.js >> $PROJECT_ROOT/data/logs/cron-optimize.log 2>&1

# 每天上午 9 点生成数据库增长分析报告
0 9 * * * cd $PROJECT_ROOT && node scripts/monitor-database-growth.js >> $PROJECT_ROOT/data/logs/cron-analysis.log 2>&1

# 每月 1 号凌晨 5 点执行完整维护（包括备份）
0 5 1 * * cd $PROJECT_ROOT && bash scripts/cron-maintenance.sh monthly >> $PROJECT_ROOT/data/logs/cron-monthly.log 2>&1
"

echo "将添加以下定时任务："
echo "$CRON_JOBS"
echo ""

# 询问用户是否继续
read -p "是否添加这些定时任务到 crontab？(y/n) " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    # 备份现有的 crontab
    crontab -l > /tmp/crontab.backup 2>/dev/null || true
    echo "已备份现有 crontab 到 /tmp/crontab.backup"
    
    # 添加新任务
    (crontab -l 2>/dev/null || true; echo "$CRON_JOBS") | crontab -
    
    echo "✅ 定时任务已成功添加！"
    echo ""
    echo "可以使用以下命令查看当前的定时任务："
    echo "  crontab -l"
    echo ""
    echo "日志文件位置："
    echo "  - 清理日志: $PROJECT_ROOT/data/logs/cron-cleanup.log"
    echo "  - 优化日志: $PROJECT_ROOT/data/logs/cron-optimize.log"
    echo "  - 分析日志: $PROJECT_ROOT/data/logs/cron-analysis.log"
    echo "  - 月度维护: $PROJECT_ROOT/data/logs/cron-monthly.log"
else
    echo "已取消操作"
    echo ""
    echo "您可以手动添加定时任务，将以下内容添加到 crontab -e："
    echo "$CRON_JOBS"
fi

# 创建日志目录
mkdir -p "$PROJECT_ROOT/data/logs"
echo ""
echo "提示：首次运行前，建议手动测试各个脚本："
echo "  npm run cleanup                    # 执行数据清理"
echo "  node scripts/optimize-database.js  # 优化数据库"
echo "  node scripts/monitor-database-growth.js  # 增长分析"