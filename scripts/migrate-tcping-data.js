/**
 * 数据迁移脚本 - 将现有TCPing数据迁移到归档表
 * 
 * 用法：node scripts/migrate-tcping-data.js
 */

const path = require('path');

// 使用数据库适配器
async function main() {
    try {
        // 通过数据库适配器连接
        const db = await require('../database/index.js')();
        
        console.log('开始迁移TCPing数据到归档表...');

        // 检查归档表是否存在
        const tableExists = await db.get(`
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='tcping_archive'
        `);

        if (!tableExists) {
            console.error('归档表不存在，请先创建表');
            process.exit(1);
        }

        // 开始事务
        const client = await db.beginTransaction();

        try {
            // 从各个表迁移数据
            const tables = ['tcping_m', 'tcping_h', 'tcping_d', 'tcping_month'];
            
            for (const table of tables) {
                console.log(`从 ${table} 表迁移数据...`);
                
                // 获取表中的所有数据
                const data = await db.all(`
                    SELECT * FROM ${table}
                    WHERE sid IS NOT NULL
                `);
                
                console.log(`找到 ${data.length} 条记录`);
                
                let insertCount = 0;
                
                for (const item of data) {
                    if (!item.sid) continue;
                    
                    // 分解时间
                    const date = new Date(item.created_at * 1000);
                    const year = date.getFullYear();
                    const month = date.getMonth() + 1;
                    const day = date.getDate();
                    const hour = date.getHours();
                    const minute = date.getMinutes();
                    
                    // 插入数据（通过适配器）
                    await db.run(`
                        INSERT INTO tcping_archive (
                            target_id, sid, success_rate, avg_time, min_time, max_time,
                            created_at, year, month, day, hour, minute
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        item.target_id,
                        item.sid,
                        item.success_rate,
                        item.avg_time,
                        item.min_time,
                        item.max_time,
                        item.created_at,
                        year,
                        month,
                        day,
                        hour,
                        minute
                    ]);
                    
                    insertCount++;
                }
                
                console.log(`成功迁移 ${insertCount} 条记录`);
            }
            
            // 提交事务
            await db.commitTransaction(client);
            console.log('数据迁移完成');
        } catch (err) {
            // 回滚事务
            await db.rollbackTransaction(client);
            console.error('数据迁移失败:', err);
            throw err;
        }

        // 关闭数据库连接
        await db.disconnect();
    } catch (error) {
        console.error('脚本执行失败:', error);
        process.exit(1);
    }
}

// 执行主函数
main();
