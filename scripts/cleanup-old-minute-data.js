#!/usr/bin/env node
'use strict';

/**
 * 清理超过1天的分钟级数据
 * 配合数据保留策略调整使用
 */

const Database = require('better-sqlite3');
const path = require('path');

// 数据库路径
const dbPath = path.join(__dirname, '../data/db.db');
const db = new Database(dbPath);

// WAL模式由database/index.js统一管理，此处无需重复设置

console.log('=== 清理超过1天的分钟级数据 ===');
console.log(`数据库路径: ${dbPath}`);

// 计算1天前的时间戳
const oneDayAgo = Math.floor(Date.now() / 1000) - (24 * 60 * 60);
const oneDayAgoDate = new Date(oneDayAgo * 1000);

console.log(`\n将删除早于 ${oneDayAgoDate.toLocaleString()} 的数据`);

// 开始事务
db.prepare('BEGIN').run();

try {
    // 统计将被删除的记录数
    const countResult = db.prepare('SELECT COUNT(*) as count FROM load_m WHERE created_at < ?').get(oneDayAgo);
    console.log(`\n发现 ${countResult.count} 条需要清理的记录`);
    
    if (countResult.count > 0) {
        // 执行清理
        const deleteResult = db.prepare('DELETE FROM load_m WHERE created_at < ?').run(oneDayAgo);
        console.log(`已删除 ${deleteResult.changes} 条记录`);
        
        // 验证剩余记录
        const remainingCount = db.prepare('SELECT COUNT(*) as count FROM load_m').get();
        console.log(`\nload_m 表剩余记录数: ${remainingCount.count}`);
        
        // 统计每个服务器的记录数
        const serverStats = db.prepare(`
            SELECT 
                sid,
                COUNT(*) as records,
                MIN(created_at) as oldest,
                MAX(created_at) as newest
            FROM load_m 
            GROUP BY sid
            LIMIT 5
        `).all();
        
        console.log('\n各服务器记录统计（前5个）:');
        serverStats.forEach(stat => {
            const oldestDate = new Date(stat.oldest * 1000);
            const newestDate = new Date(stat.newest * 1000);
            console.log(`- ${stat.sid}: ${stat.records} 条记录`);
            console.log(`  时间范围: ${oldestDate.toLocaleString()} ~ ${newestDate.toLocaleString()}`);
        });
    } else {
        console.log('没有需要清理的记录');
    }
    
    // 提交事务
    db.prepare('COMMIT').run();
    console.log('\n✅ 清理完成！');
    
    // 执行 VACUUM 优化数据库
    console.log('\n正在优化数据库...');
    db.prepare('VACUUM').run();
    console.log('✅ 数据库优化完成！');
    
} catch (error) {
    // 回滚事务
    db.prepare('ROLLBACK').run();
    console.error('\n❌ 清理失败:', error.message);
    process.exit(1);
} finally {
    db.close();
}

console.log('\n=== 清理脚本执行完毕 ===');