# DStatus 脚本工具

## check-time-limits.js

### 功能
检查当前套餐的时间范围限制，显示各时间范围的访问权限。

### 使用方法

```bash
# 使用 npm script（推荐）
npm run check:time-limits

# 直接运行
node scripts/check-time-limits.js

# 指定API地址
node scripts/check-time-limits.js --api http://*************:5555

# 显示帮助信息
node scripts/check-time-limits.js --help
```

### 输出信息
- 当前实例ID和API地址
- 套餐类型和许可证状态
- 最大时间范围限制
- 各时间范围的详细权限检查
- 允许/限制的时间范围统计
- 使用提示和说明

### 支持的时间范围
- 1小时 (1h)
- 3小时 (3h)
- 6小时 (6h)
- 12小时 (12h)
- 24小时 (24h)
- 3天 (3d)
- 7天 (7d)
- 30天 (30d)

### 依赖
- Node.js 20.17.0+ 或 22.9.0+
- axios（网络请求）
- better-sqlite3（数据库访问）

### 注意事项
- 需要 DStatus 服务正在运行
- 需要已激活有效的许可证
- 基础版套餐通常限制为24小时内的数据
- 专业版套餐支持所有时间范围