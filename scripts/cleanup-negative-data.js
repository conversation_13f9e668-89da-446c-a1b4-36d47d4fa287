#!/usr/bin/env node
'use strict';

/**
 * 清理数据库中的 -1 填充数据
 * 只删除所有监控值都是 -1 的记录，保留真实数据
 */

const Database = require('better-sqlite3');
const path = require('path');

// 数据库路径
const dbPath = path.join(__dirname, '../data/db.db');
const db = new Database(dbPath);

// WAL模式由database/index.js统一管理，此处无需重复设置

console.log('=== 开始清理 -1 填充数据 ===');
console.log(`数据库路径: ${dbPath}`);

// 需要清理的表
const tables = ['load_m', 'load_h', 'load_archive'];

// 开始事务
db.prepare('BEGIN').run();

try {
    tables.forEach(table => {
        console.log(`\n正在检查表: ${table}`);
        
        // 首先统计有多少记录是全 -1 的
        const countQuery = `
            SELECT COUNT(*) as count 
            FROM ${table} 
            WHERE cpu = -1 AND mem = -1 AND swap = -1 
            AND (ibw = -1 OR ibw IS NULL) 
            AND (obw = -1 OR obw IS NULL)
        `;
        
        const result = db.prepare(countQuery).get();
        console.log(`发现 ${result.count} 条需要清理的记录`);
        
        if (result.count > 0) {
            // 执行清理
            const deleteQuery = `
                DELETE FROM ${table} 
                WHERE cpu = -1 AND mem = -1 AND swap = -1 
                AND (ibw = -1 OR ibw IS NULL) 
                AND (obw = -1 OR obw IS NULL)
            `;
            
            const deleteResult = db.prepare(deleteQuery).run();
            console.log(`已删除 ${deleteResult.changes} 条记录`);
            
            // 验证剩余记录数
            const remainingCount = db.prepare(`SELECT COUNT(*) as count FROM ${table}`).get();
            console.log(`表 ${table} 剩余记录数: ${remainingCount.count}`);
        }
    });
    
    // 提交事务
    db.prepare('COMMIT').run();
    console.log('\n✅ 清理完成！');
    
    // 执行 VACUUM 优化数据库
    console.log('\n正在优化数据库...');
    db.prepare('VACUUM').run();
    console.log('✅ 数据库优化完成！');
    
} catch (error) {
    // 回滚事务
    db.prepare('ROLLBACK').run();
    console.error('\n❌ 清理失败:', error.message);
    process.exit(1);
} finally {
    db.close();
}

console.log('\n=== 清理脚本执行完毕 ===');