/**
 * 数据库迁移执行脚本
 * 用于将迁移应用到实际数据库
 */

const path = require('path');
const DatabaseMigration = require('../database/migrations');
const AddGroupField = require('../database/migrations/scripts/002_add_group_field');

async function initDatabase() {
    const dbModule = require('../database/index');
    return await dbModule();
}

async function migrate() {
    console.log('Starting database migration...');
    
    try {
        // 连接数据库使用适配器
        const dbInstance = await initDatabase();
        console.log('Connected to database using adapter');

        // 初始化迁移管理器
        const migration = new DatabaseMigration(dbInstance.DB);
        await migration.init();
        
        // 注册迁移脚本
        migration.register(
            AddGroupField.version,
            AddGroupField.name,
            AddGroupField.up,
            AddGroupField.down
        );
        
        // 执行迁移
        await migration.migrate();
        
        // 验证迁移结果 - 使用跨数据库兼容的查询
        let tables;
        if (dbInstance.type === 'sqlite') {
            tables = await dbInstance.DB.all("SELECT name FROM sqlite_master WHERE type='table' AND name='groups'");
        } else {
            tables = await dbInstance.DB.all("SELECT tablename as name FROM pg_tables WHERE tablename='groups'");
        }
        
        console.log('\nVerification results:');
        console.log('- Groups table exists:', tables.length > 0);
        
        if (tables.length > 0) {
            const defaultGroup = await dbInstance.DB.get("SELECT * FROM groups WHERE id = 'default'");
            console.log('- Default group exists:', !!defaultGroup);
            
            // 检查group_id字段是否存在 - 跨数据库兼容
            let hasGroupId = false;
            if (dbInstance.type === 'sqlite') {
                const columns = await dbInstance.DB.all("PRAGMA table_info(servers)");
                hasGroupId = columns.some(col => col.name === 'group_id');
            } else {
                const columns = await dbInstance.DB.all(`
                    SELECT column_name FROM information_schema.columns 
                    WHERE table_name = 'servers' AND column_name = 'group_id'
                `);
                hasGroupId = columns.length > 0;
            }
            console.log('- Group_id field exists:', hasGroupId);
            
            if (hasGroupId) {
                const servers = await dbInstance.DB.get("SELECT COUNT(*) as count FROM servers WHERE group_id IS NULL");
                console.log('- Servers without group:', servers.count);
            }
        }
        
        // 关闭数据库连接
        await dbInstance.DB.disconnect();
        
        console.log('\nMigration completed successfully!');
    } catch (error) {
        console.error('\nMigration failed:', error);
        process.exit(1);
    }
}

// 执行迁移
migrate(); 