#!/bin/bash

# DStatus Docker 部署包构建脚本
# 用于创建完整的 Docker 一键安装包

set -e

echo "🐳 DStatus Docker 部署包构建脚本"
echo "=================================="

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_DIR/dist-docker"
PACKAGE_NAME="dstatus-docker"
VERSION=${1:-"latest"}

echo "📋 构建信息:"
echo "   项目目录: $PROJECT_DIR"
echo "   构建目录: $BUILD_DIR"
echo "   版本: $VERSION"
echo ""

# 检查必要文件
echo "🔍 检查必要文件..."

REQUIRED_FILES=(
    "$PROJECT_DIR/static"
    "$PROJECT_DIR/views"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -e "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
    echo "  ✓ $file"
done

# 检查二进制文件（可选，由GitHub Actions生成）
if [ ! -f "$PROJECT_DIR/dstatus-linux-x64" ]; then
    echo "⚠️  本地未找到 dstatus-linux-x64，将创建占位符"
    echo "   注意：实际部署时需要从GitHub Actions下载真实的二进制文件"
    # 创建占位符文件
    echo '#!/bin/bash' > "$PROJECT_DIR/dstatus-linux-x64"
    echo 'echo "这是占位符文件，请从GitHub Actions下载真实的二进制文件"' >> "$PROJECT_DIR/dstatus-linux-x64"
    chmod +x "$PROJECT_DIR/dstatus-linux-x64"
else
    echo "  ✓ $PROJECT_DIR/dstatus-linux-x64"
fi

# 检查并安装前端混淆工具
if ! command -v terser &> /dev/null; then
    echo "📦 安装前端混淆工具..."
    npm install -g terser
fi

echo "✅ 文件检查完成"
echo ""

# 清理并创建构建目录
echo "🧹 清理构建目录..."
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR/$PACKAGE_NAME"

cd "$BUILD_DIR/$PACKAGE_NAME"

# 复制二进制文件
echo "📦 复制二进制文件..."
cp "$PROJECT_DIR/dstatus-linux-x64" .
chmod +x dstatus-linux-x64

# 复制并混淆前端文件
echo "🔐 复制并混淆前端文件..."
cp -r "$PROJECT_DIR/static" .
cp -r "$PROJECT_DIR/views" .

# 混淆 JavaScript 文件
echo "  🔐 混淆 JavaScript..."
find static/js -name "*.js" -type f | while read file; do
    if [[ ! "$file" =~ \.min\.js$ ]]; then
        terser "$file" --compress --mangle -o "$file" 2>/dev/null || true
        echo "    ✓ 混淆: $(basename "$file")"
    fi
done

# 压缩 CSS 文件 (简单压缩)
echo "  📦 压缩 CSS..."
find static/css -name "*.css" -type f | while read file; do
    if [[ ! "$file" =~ \.min\.css$ ]]; then
        # 简单的CSS压缩：移除注释和多余空白
        sed -i 's|/\*.*\*/||g; s/[[:space:]]\+/ /g; s/; /;/g; s/ {/{/g; s/{ /{/g; s/ }/}/g' "$file" 2>/dev/null || true
        echo "    ✓ 压缩: $(basename "$file")"
    fi
done

# 复制 better_sqlite3.node (从正确位置)
if [ -f "$PROJECT_DIR/native-modules/linux-x64/better_sqlite3.node" ]; then
    echo "📦 复制 better_sqlite3.node..."
    cp "$PROJECT_DIR/native-modules/linux-x64/better_sqlite3.node" .
elif [ -f "$PROJECT_DIR/node_modules/better-sqlite3/build/Release/better_sqlite3.node" ]; then
    echo "📦 复制 better_sqlite3.node (从 node_modules)..."
    cp "$PROJECT_DIR/node_modules/better-sqlite3/build/Release/better_sqlite3.node" .
else
    echo "❌ 未找到 better_sqlite3.node"
    echo "   请确保文件存在于以下位置之一："
    echo "   - $PROJECT_DIR/native-modules/linux-x64/better_sqlite3.node"
    echo "   - $PROJECT_DIR/node_modules/better-sqlite3/build/Release/better_sqlite3.node"
    exit 1
fi

# 创建 Dockerfile
echo "🐳 创建 Dockerfile..."
cat > Dockerfile << 'EOF'
# DStatus 服务器监控系统 Docker 镜像
FROM debian:bullseye-slim

# 安装必要的依赖
RUN apt-get update && \
    apt-get install -y ca-certificates tzdata wget libstdc++6 libgcc-s1 && \
    rm -rf /var/lib/apt/lists/*

# 设置时区
ENV TZ=Asia/Shanghai

# 创建必要的目录并设置权限
RUN mkdir -p /app /etc/dstatus /app/data /app/logs /app/data/backups /app/data/temp && \
    chmod -R 777 /app/data /app/logs

# 复制文件
COPY dstatus-linux-x64 /app/dstatus
COPY better_sqlite3.node /app/
COPY static/ /app/static/
COPY views/ /app/views/

# 设置执行权限
RUN chmod +x /app/dstatus

# 设置工作目录
WORKDIR /app

# 暴露端口
EXPOSE 5555

# 数据卷
VOLUME ["/app/data", "/app/logs"]

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:5555/health || exit 1

# 启动命令 - docker-compose.yml 中会设置用户为 root
CMD ["/app/dstatus"]
EOF

# 创建 docker-compose.yml
echo "🐳 创建 docker-compose.yml..."
cat > docker-compose.yml << 'EOF'
services:
  dstatus:
    build: .
    container_name: dstatus-monitor
    user: "0:0"  # 以 root 用户运行容器解决权限问题
    ports:
      - "5555:5555"  # 可修改左侧端口号
    volumes:
      - ./data:/app/data:rw
      - ./logs:/app/logs:rw  # 修复：从 /logs 改为 /app/logs
      - ./config.yaml:/etc/dstatus/config.yaml:ro
    environment:
      - TZ=Asia/Shanghai
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5555"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  dstatus_data:
    driver: local

networks:
  default:
    name: dstatus_network
EOF

# DStatus 使用数据库存储配置，无需配置文件
echo "✅ DStatus 配置通过Web管理面板设置"

echo "✅ Docker 文件创建完成"
echo ""

# 创建启动脚本
echo "🚀 创建启动脚本..."
cat > start.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"

echo "🚀 启动 DStatus Docker 服务..."

# 检查并创建必要的目录
echo "📁 检查数据目录..."
mkdir -p data logs

# 修复目录权限（确保容器内的 root 用户可以写入）
echo "🔧 修复目录权限..."
if [[ "$(id -u)" == "0" ]]; then
    # 以 root 用户运行，设置正确的权限
    chown -R root:root . 2>/dev/null || true
    chmod -R 755 .
    chmod +x *.sh
    chmod -R 777 data logs  # 给容器内 root 用户完全权限
    echo "✅ 权限修复完成"
else
    # 非 root 用户，尝试设置写入权限
    chmod -R 777 data logs 2>/dev/null || {
        echo "⚠️  权限设置可能不完整，如果启动失败请运行: sudo ./fix-permissions.sh"
    }
fi

# 检查 docker-compose.yml 中的端口配置
EXTERNAL_PORT=$(grep -o '"[0-9]*:5555"' docker-compose.yml | grep -o '[0-9]*' | head -1)
if [[ -z "$EXTERNAL_PORT" ]]; then
    EXTERNAL_PORT="5555"
fi

echo "📋 配置信息:"
echo "   外部端口: $EXTERNAL_PORT"
echo "   内部端口: 5555"
echo "   数据目录: ./data (权限已修复)"
echo "   日志目录: ./logs (权限已修复)"

# 启动服务
echo "🚀 启动容器..."
docker-compose up -d

if [[ $? -eq 0 ]]; then
    echo "✅ 服务已启动"
    echo "🌐 访问地址: http://localhost:$EXTERNAL_PORT"
    echo ""
    echo "🔧 管理命令:"
    echo "   停止服务: ./stop.sh"
    echo "   查看日志: docker-compose logs -f"
    echo "   查看状态: docker-compose ps"
    echo ""
    echo "💡 提示:"
    echo "   如需修改端口，请重新运行安装脚本："
    echo "   curl -fsSL https://down.vps.mom/install.sh | bash -s -- --license-key=\"YOUR_LICENSE\" --port=NEW_PORT"
else
    echo "❌ 服务启动失败"
    echo "💡 请检查:"
    echo "   - Docker 是否正常运行"
    echo "   - 端口 $EXTERNAL_PORT 是否被占用"
    echo "   - 配置文件是否正确"
    exit 1
fi
EOF
chmod +x start.sh

# 创建停止脚本
echo "🛑 创建停止脚本..."
cat > stop.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
echo "🛑 停止 DStatus Docker 服务..."
docker-compose down
echo "✅ 服务已停止"
EOF
chmod +x stop.sh

# 创建更新脚本
echo "🔄 创建更新脚本..."
cat > update.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
echo "🔄 更新 DStatus Docker 服务..."

# 停止服务
docker-compose down

# 重新构建镜像
docker-compose build --no-cache

# 启动服务
docker-compose up -d

echo "✅ 更新完成"
echo "🌐 访问地址: http://localhost:5555"
EOF
chmod +x update.sh

# 创建日志查看脚本
echo "📋 创建日志查看脚本..."
cat > logs.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
echo "📋 查看 DStatus 日志..."
docker-compose logs -f
EOF
chmod +x logs.sh

# 创建权限修复脚本
echo "🔧 创建权限修复脚本..."
cat > fix-permissions.sh << 'EOF'
#!/bin/bash
# DStatus Docker 权限修复脚本

echo "🔧 修复 DStatus Docker 目录权限..."

# 停止容器（如果运行中）
echo "🛑 停止现有容器..."
docker-compose down 2>/dev/null || true

# 创建目录（如果不存在）
mkdir -p data logs data/backups data/temp

# 检查是否以 root 用户运行
if [[ $EUID -eq 0 ]]; then
    echo "✅ 以 root 用户运行，设置正确的目录权限..."

    # 设置 root 所有者
    chown -R root:root .
    
    # 设置目录权限
    chmod -R 755 .
    chmod +x *.sh
    
    # 给数据和日志目录完全权限（容器内需要写权限）
    chmod -R 777 data logs

    echo "✅ 权限修复完成"
    echo "📋 目录权限:"
    ls -la data logs
else
    echo "⚠️  建议以 root 用户运行此脚本以获得最佳效果"
    echo "💡 或者运行: sudo ./fix-permissions.sh"
    echo ""
    echo "🔄 尝试设置基本权限..."

    # 创建目录（如果不存在）
    mkdir -p data logs data/backups data/temp

    # 设置写入权限
    chmod -R 777 data logs 2>/dev/null && {
        echo "✅ 基本权限设置完成"
    } || {
        echo "❌ 权限设置失败，请以 root 用户运行"
        exit 1
    }
fi

echo ""
echo "🚀 现在可以运行 ./start.sh 启动服务"
EOF
chmod +x fix-permissions.sh

# 创建 README
echo "📖 创建 README..."
cat > README.md << EOF
# DStatus Docker 部署包

## 快速启动

\`\`\`bash
# 启动服务（自动修复权限）
./start.sh

# 如果启动失败，手动修复权限
sudo ./fix-permissions.sh

# 停止服务
./stop.sh

# 查看日志
./logs.sh

# 更新服务
./update.sh
\`\`\`

## 手动操作

\`\`\`bash
# 构建并启动
docker-compose up -d

# 停止服务
docker-compose down

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f
\`\`\`

## 配置说明

### 端口配置
编辑 \`docker-compose.yml\` 文件中的端口映射：
\`\`\`yaml
ports:
  - "5555:5555"  # 左侧为宿主机端口，可修改
\`\`\`

### 许可证配置
许可证在Web管理面板中设置：
\`\`\`
1. 启动服务后访问 http://localhost:5555
2. 登录管理面板
3. 在许可证管理页面输入许可证密钥
\`\`\`

## 数据持久化

以下目录会自动创建并持久化：
- \`./data/\` - 数据库文件 (映射到容器内 /app/data)
- \`./logs/\` - 日志文件 (映射到容器内 /logs)
- 配置通过Web管理面板设置

## 权限问题解决

如果遇到权限相关错误（如 \`EACCES: permission denied\`），请使用以下方法：

### 方法1：自动修复（推荐）
\`\`\`bash
# start.sh 会自动尝试修复权限
./start.sh
\`\`\`

### 方法2：手动修复
\`\`\`bash
# 使用专用的权限修复脚本
sudo ./fix-permissions.sh
./start.sh
\`\`\`

### 方法3：手动设置权限
\`\`\`bash
# 创建目录并设置权限
mkdir -p data logs
sudo chown -R 1000:1000 data logs
# 或者设置写入权限
chmod -R 777 data logs
\`\`\`

## 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- Linux x64 系统

## 构建信息

- 版本: $VERSION
- 构建时间: $(date)
- 平台: Linux x64
- Node.js: 20.x

## 支持

如有问题，请查看日志文件或联系技术支持。
EOF

# 创建数据和日志目录
echo "📁 创建数据目录..."
mkdir -p data logs

# 创建 .gitignore
cat > .gitignore << 'EOF'
# 数据文件
data/
logs/
*.log

# 临时文件
*.tmp
*.temp

# 系统文件
.DS_Store
Thumbs.db
EOF

# 创建压缩包
echo "📦 创建压缩包..."
cd "$BUILD_DIR"

# 创建 tar.gz 压缩包
tar -czf "${PACKAGE_NAME}.tar.gz" "$PACKAGE_NAME"

# 显示结果
echo ""
echo "✅ Docker 部署包构建完成！"
echo "=================================="
echo "📦 压缩包: $BUILD_DIR/${PACKAGE_NAME}.tar.gz"
echo "📏 大小: $(du -h "${PACKAGE_NAME}.tar.gz" | cut -f1)"
echo "📁 内容目录: $BUILD_DIR/$PACKAGE_NAME"
echo ""

# 显示包内容
echo "📋 包含文件:"
tar -tzf "${PACKAGE_NAME}.tar.gz" | head -20
if [ $(tar -tzf "${PACKAGE_NAME}.tar.gz" | wc -l) -gt 20 ]; then
    echo "... 还有 $(($(tar -tzf "${PACKAGE_NAME}.tar.gz" | wc -l) - 20)) 个文件"
fi
echo ""

echo "🚀 部署说明:"
echo "1. 上传 ${PACKAGE_NAME}.tar.gz 到服务器"
echo "2. 解压: tar -xzf ${PACKAGE_NAME}.tar.gz"
echo "3. 进入目录: cd $PACKAGE_NAME"
echo "4. 访问 Web 管理面板设置许可证"
echo "5. 启动服务: ./start.sh"
echo ""

echo "🔗 一键安装命令:"
echo "curl -fsSL https://client.vps.mom/install.sh | bash -s -- --license-key=\"YOUR_LICENSE_KEY\""
echo ""

echo "✨ 构建完成！"
