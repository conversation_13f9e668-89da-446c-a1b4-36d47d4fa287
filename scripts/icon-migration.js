#!/usr/bin/env node

/**
 * Material Icons → Tabler Icons 自动化迁移脚本
 * 专为 DStatus 项目设计
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Material Icons → Tabler Icons 完整映射表
const ICON_MAPPING = {
  // 🔄 状态类图标
  'check_circle': 'ti-circle-check',
  'refresh': 'ti-refresh',
  'info': 'ti-info-circle',
  'warning': 'ti-alert-triangle',
  'error': 'ti-alert-circle',
  'close': 'ti-x',
  'check': 'ti-check',
  'cancel': 'ti-x',
  'done_all': 'ti-checks',
  
  // 📁 文件夹和导航
  'folder': 'ti-folder',
  'folder_open': 'ti-folder-open',
  'home': 'ti-home',
  'arrow_back': 'ti-arrow-left',
  'arrow_forward': 'ti-arrow-right',
  'chevron_left': 'ti-chevron-left',
  'chevron_right': 'ti-chevron-right',
  'expand_more': 'ti-chevron-down',
  'unfold_more': 'ti-chevrons-down',
  'keyboard_arrow_down': 'ti-chevron-down',
  'keyboard_arrow_up': 'ti-chevron-up',
  'arrow_upward': 'ti-arrow-up',
  'arrow_downward': 'ti-arrow-down',
  'arrow_drop_down': 'ti-chevron-down',
  
  // ⚙️ 设置和配置
  'settings': 'ti-settings',
  'tune': 'ti-adjustments',
  'build': 'ti-tool',
  'extension': 'ti-puzzle',
  'api': 'ti-api',
  'code': 'ti-code',
  
  // 🖥️ 系统和设备
  'dns': 'ti-server-2',
  'computer': 'ti-device-desktop',
  'terminal': 'ti-terminal-2',
  'devices': 'ti-devices',
  'devices_other': 'ti-device-tablet',
  'router': 'ti-router',
  'wifi': 'ti-wifi',
  'wifi_off': 'ti-wifi-off',
  'network_check': 'ti-network',
  'network_ping': 'ti-ping-pong',
  'signal_cellular_alt': 'ti-signal-4g',
  
  // 📊 数据和分析
  'analytics': 'ti-chart-line',
  'dashboard': 'ti-dashboard',
  'assessment': 'ti-chart-bar',
  'bar_chart': 'ti-chart-bar',
  'show_chart': 'ti-chart-line',
  'trending_up': 'ti-trending-up',
  'trending_down': 'ti-trending-down',
  'speed': 'ti-gauge',
  'insights': 'ti-bulb',
  'data_usage': 'ti-chart-donut',
  
  // 📦 存储和内存
  'storage': 'ti-database',
  'memory': 'ti-cpu',
  'sd_card': 'ti-device-sd-card',
  'cloud_download': 'ti-cloud-download',
  'cloud_upload': 'ti-cloud-upload',
  'cloud_done': 'ti-cloud-check',
  
  // 👁️ 可见性
  'visibility': 'ti-eye',
  'visibility_off': 'ti-eye-off',
  'search': 'ti-search',
  'search_off': 'ti-search-off',
  
  // ✏️ 编辑操作
  'edit': 'ti-edit',
  'add': 'ti-plus',
  'add_circle': 'ti-circle-plus',
  'add_location': 'ti-map-pin-plus',
  'delete': 'ti-trash',
  'delete_sweep': 'ti-trash-x',
  'delete_forever': 'ti-trash-off',
  'save': 'ti-device-floppy',
  'save_alt': 'ti-download',
  'content_copy': 'ti-copy',
  'undo': 'ti-arrow-back-up',
  'redo': 'ti-arrow-forward-up',
  
  // 📥 上传下载
  'download': 'ti-download',
  'upload': 'ti-upload',
  'upload_file': 'ti-file-upload',
  'publish': 'ti-send',
  
  // 🔒 安全和权限
  'lock': 'ti-lock',
  'lock_outline': 'ti-lock-open',
  'key': 'ti-key',
  'vpn_key': 'ti-key',
  'security': 'ti-shield-check',
  'verified': 'ti-shield-check',
  'fingerprint': 'ti-fingerprint',
  
  // 👤 用户和账户
  'account_circle': 'ti-user-circle',
  'login': 'ti-login',
  'logout': 'ti-logout',
  'group': 'ti-users',
  'group_work': 'ti-users-group',
  
  // 🕒 时间
  'schedule': 'ti-clock',
  'timer': 'ti-hourglass',
  'history': 'ti-history',
  'date_range': 'ti-calendar',
  'event_note': 'ti-calendar-event',
  'update': 'ti-refresh',
  'autorenew': 'ti-refresh-dot',
  'sync': 'ti-refresh',
  'sync_alt': 'ti-arrows-exchange',
  'sync_problem': 'ti-refresh-alert',
  
  // 🎨 界面元素
  'menu': 'ti-menu-2',
  'more_vert': 'ti-dots-vertical',
  'sort': 'ti-arrows-sort',
  'filter_list': 'ti-filter',
  'list': 'ti-list',
  'view_list': 'ti-list',
  'view_module': 'ti-grid-dots',
  'grid_view': 'ti-layout-grid',
  'drag_handle': 'ti-grip-horizontal',
  'drag_indicator': 'ti-grip-vertical',
  'fullscreen': 'ti-arrows-maximize',
  'open_in_new': 'ti-external-link',
  'open_in_browser': 'ti-browser',
  'open_with': 'ti-external-link',
  
  // 📄 文档和描述
  'description': 'ti-file-text',
  'pages': 'ti-files',
  'collections': 'ti-folder-plus',
  'inbox': 'ti-inbox',
  
  // 🔧 工具和实用
  'bug_report': 'ti-bug',
  'help_outline': 'ti-help-circle',
  'info_outline': 'ti-info-circle',
  'lightbulb': 'ti-bulb',
  'psychology': 'ti-brain',
  'science': 'ti-atom',
  'school': 'ti-school',
  'workspace_premium': 'ti-premium-rights',
  'recommend': 'ti-thumb-up',
  'star': 'ti-star',
  'favorite': 'ti-heart',
  
  // 🎨 个性化
  'palette': 'ti-palette',
  'brush': 'ti-brush',
  'dark_mode': 'ti-moon',
  'brightness_6': 'ti-sun',
  'blur_on': 'ti-blur',
  'eco': 'ti-leaf',
  
  // 🔄 状态切换
  'toggle_on': 'ti-toggle-right',
  'power_off': 'ti-power',
  'power_settings_new': 'ti-power',
  'play_arrow': 'ti-player-play',
  'cached': 'ti-refresh',
  'restore': 'ti-restore',
  'rotate_right': 'ti-rotate-clockwise',
  
  // 📱 移动和响应式
  'straighten': 'ti-ruler',
  'balance': 'ti-scale',
  'swap_horiz': 'ti-arrows-horizontal',
  'swap_vert': 'ti-arrows-vertical',
  
  // 🌐 网络和位置
  'public': 'ti-world',
  'location_on': 'ti-map-pin',
  'map': 'ti-map',
  'travel_explore': 'ti-compass',
  'wifi_protected_setup': 'ti-wifi-2',
  'settings_ethernet': 'ti-ethernet',
  'link': 'ti-link',
  'link_off': 'ti-unlink',
  
  // 🛍️ 电商和购物
  'shopping_cart': 'ti-shopping-cart',
  'local_offer': 'ti-tag',
  'card_membership': 'ti-credit-card',
  
  // 📱 应用和页面
  'web': 'ti-world-www',
  'language': 'ti-language',
  'keyboard': 'ti-keyboard',
  'notifications': 'ti-bell',
  
  // 📊 高级功能
  'auto_awesome': 'ti-sparkles',
  'smart_toy': 'ti-robot',
  'extension': 'ti-puzzle',
  'flash_on': 'ti-bolt',
  'target': 'ti-target',
  
  // 📑 页面导航
  'first_page': 'ti-chevrons-left',
  'last_page': 'ti-chevrons-right',
  'preview': 'ti-eye',
  
  // 其他常用
  'clear': 'ti-x',
  'clear_all': 'ti-clear-all',
  'select_all': 'ti-select-all',
  'deselect': 'ti-select',
  'label': 'ti-tag',
  'category': 'ti-category',
  'format_align_left': 'ti-align-left',
  'zoom_out_map': 'ti-zoom-out',
  'pending': 'ti-clock-pause',
  'health_and_safety': 'ti-shield-heart',
  'upgrade': 'ti-arrow-up-circle',
  'settings_backup_restore': 'ti-settings-automation'
};

/**
 * 获取所有需要处理的HTML文件
 */
function getViewFiles() {
  const viewsDir = path.join(__dirname, '../views');
  return glob.sync('**/*.html', { cwd: viewsDir, absolute: true });
}

/**
 * 替换单个文件中的图标
 */
function replaceIconsInFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changes = 0;
  
  // 正则表达式匹配 Material Icons
  const materialIconRegex = /<i\s+class="([^"]*material-icons[^"]*)"[^>]*>([^<]+)<\/i>/g;
  
  content = content.replace(materialIconRegex, (match, classAttr, iconName) => {
    const trimmedIconName = iconName.trim();
    
    // 跳过动态图标（包含模板语法）
    if (trimmedIconName.includes('${') || trimmedIconName.includes('{%')) {
      return match;
    }
    
    const tablerIcon = ICON_MAPPING[trimmedIconName];
    if (tablerIcon) {
      changes++;
      // 保留原有的其他class，但替换material-icons为tabler图标
      const newClass = classAttr.replace(/material-icons/g, tablerIcon);
      return `<i class="${newClass}"></i>`;
    }
    
    return match;
  });
  
  if (changes > 0) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ ${path.relative(process.cwd(), filePath)}: 替换了 ${changes} 个图标`);
  } else {
    console.log(`⏸️  ${path.relative(process.cwd(), filePath)}: 无需替换`);
  }
  
  return changes;
}

/**
 * 主执行函数
 */
function main() {
  console.log('🚀 开始 Material Icons → Tabler Icons 自动化迁移...\n');
  
  const files = getViewFiles();
  let totalChanges = 0;
  
  console.log(`📁 发现 ${files.length} 个模板文件\n`);
  
  files.forEach(file => {
    totalChanges += replaceIconsInFile(file);
  });
  
  console.log(`\n🎉 迁移完成！`);
  console.log(`📊 总计替换了 ${totalChanges} 个图标`);
  console.log(`📋 涉及 ${files.length} 个文件`);
  
  // 输出未映射的图标提醒
  console.log('\n💡 提示：动态图标（包含${或{%的）需要手动处理');
  console.log('💡 如发现显示异常的图标，请检查 ICON_MAPPING 映射表');
}

// 执行脚本
if (require.main === module) {
  main();
}

module.exports = { ICON_MAPPING, replaceIconsInFile };