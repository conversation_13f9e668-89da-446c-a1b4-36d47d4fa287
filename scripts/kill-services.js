#!/usr/bin/env node

/**
 * 杀死所有DStatus相关服务进程的脚本
 * 确保启动新服务前清理旧进程
 */

const { exec } = require('child_process');
const os = require('os');

// 定义要杀死的进程模式
const processPatterns = [
    'nekonekostatus.js',      // DStatus 主进程
    'nodemon.*nekonekostatus', // DStatus 开发模式
    'next.*dev.*3100',        // License Server 前端开发
    'next.*dev.*4000',        // 用户前端生产模式
    'next.*dev.*4001',        // 用户前端开发模式
    'node.*app.js',           // License Server 后端
    'concurrently.*dev:full', // 全量启动进程
    'concurrently.*start:full', // 全量生产进程
    // CSS 监听（Tailwind）
    'tailwindcss .*--watch',
    'node .*tailwind.*--watch'
];

// 获取操作系统类型
const isWindows = os.platform() === 'win32';

/**
 * 杀死匹配模式的进程
 * @param {string} pattern - 进程匹配模式
 * @returns {Promise<void>}
 */
function killProcessByPattern(pattern) {
    return new Promise((resolve) => {
        let command;
        
        if (isWindows) {
            // Windows 系统
            command = `tasklist | findstr /i "${pattern}" && taskkill /f /im "${pattern}" 2>nul || echo No process found for ${pattern}`;
        } else {
            // Unix/Linux/macOS 系统
            command = `pkill -f "${pattern}" 2>/dev/null || echo "No process found for ${pattern}"`;
        }

        exec(command, (error, stdout, stderr) => {
            if (stdout.trim()) {
                console.log(`🔄 ${pattern}: ${stdout.trim()}`);
            }
            resolve();
        });
    });
}

/**
 * 杀死占用特定端口的进程
 * @param {number} port - 端口号
 * @returns {Promise<void>}
 */
function killProcessByPort(port) {
    return new Promise((resolve) => {
        let command;
        
        if (isWindows) {
            // Windows 系统
            command = `netstat -ano | findstr :${port} | findstr LISTENING`;
        } else {
            // Unix/Linux/macOS 系统
            command = `lsof -ti:${port}`;
        }

        exec(command, (error, stdout, stderr) => {
            if (stdout.trim()) {
                const pids = stdout.trim().split('\n');
                pids.forEach(pid => {
                    const cleanPid = isWindows ? pid.trim().split(/\s+/).pop() : pid.trim();
                    if (cleanPid && cleanPid.match(/^\d+$/)) {
                        const killCmd = isWindows ? `taskkill /f /pid ${cleanPid}` : `kill -9 ${cleanPid}`;
                        exec(killCmd, (killError) => {
                            if (!killError) {
                                console.log(`🔄 已杀死端口 ${port} 上的进程 (PID: ${cleanPid})`);
                            }
                        });
                    }
                });
            }
            resolve();
        });
    });
}

/**
 * 主函数：杀死所有相关进程
 */
async function killAllServices() {
    console.log('🛑 正在停止所有DStatus相关服务...\n');

    // 按端口杀死进程
    // 注意：如果您的生产环境使用这些端口，请在下面的数组中移除对应端口
    const ports = [5555, 3200, 3100, 4001]; // 已移除 4000 以保护生产环境
    
    // 从环境变量中读取要跳过的端口
    const skipPorts = process.env.SKIP_PORTS ? process.env.SKIP_PORTS.split(',').map(p => parseInt(p.trim())) : [];
    const portsToKill = ports.filter(port => !skipPorts.includes(port));
    
    console.log('📍 检查并杀死占用端口的进程:');
    if (skipPorts.length > 0) {
        console.log(`⚠️  跳过端口: ${skipPorts.join(', ')}`);
    }
    for (const port of portsToKill) {
        await killProcessByPort(port);
    }

    console.log('\n🔍 检查并杀死匹配模式的进程:');
    // 按进程名模式杀死
    for (const pattern of processPatterns) {
        await killProcessByPattern(pattern);
    }

    // 等待一秒确保进程完全终止
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('\n✅ 进程清理完成！\n');
}

// 如果直接运行此脚本
if (require.main === module) {
    killAllServices()
        .then(() => {
            console.log('🎉 所有服务已停止，可以安全启动新服务');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ 停止服务时出错:', error);
            process.exit(1);
        });
}

module.exports = { killAllServices }; 
