#!/bin/bash

# DStatus 一键安装脚本
# 支持 Docker 和传统安装方式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
DOWNLOAD_BASE="https://down.vps.mom/downloads"
INSTALL_DIR="/opt/dstatus"
SERVICE_NAME="dstatus"

# 默认参数
INSTALL_METHOD="docker"  # 只支持Docker安装
LICENSE_KEY=""
PORT="5555"
FORCE_INSTALL=false
UPDATE_MODE=false
AUTO_INSTALL_DOCKER=true
SKIP_DOCKER_INSTALL=false

# 显示帮助信息
show_help() {
    echo "DStatus 一键安装脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --method METHOD        安装方式: docker (默认且唯一支持)"
    echo "  --license-key KEY      许可证密钥 (安装时必需)"
    echo "  --port PORT           服务端口 (默认: 5555)"
    echo "  --install-dir DIR     安装目录 (默认: /opt/dstatus)"
    echo "  --force               强制重新安装"
    echo "  --update              更新模式 (自动检测安装方式并更新)"
    echo "  --skip-docker         跳过 Docker 自动安装"
    echo "  --no-auto-docker      禁用 Docker 自动安装"
    echo "  -h, --help            显示此帮助信息"
    echo ""
    echo "安装示例:"
    echo "  $0 --license-key=\"YOUR_LICENSE_KEY\""
    echo "  $0 --license-key=YOUR_LICENSE_KEY"
    echo "  $0 --license-key=YOUR_LICENSE_KEY --port=8080"
    echo ""
    echo "Docker 安装说明:"
    echo "  - 仅支持 Docker 安装方式"
    echo "  - 自动检测并安装 Docker（如果未安装）"
    echo "  - --skip-docker：跳过 Docker 自动安装"
    echo "  - --no-auto-docker：禁用 Docker 自动安装"
    echo ""
    echo "更新示例:"
    echo "  $0 --update"
    echo "  $0 --update --install-dir=/custom/path"
    echo ""
    echo "更新说明:"
    echo "  - 自动检测现有Docker安装"
    echo "  - 停止现有服务和容器"
    echo "  - 强制重新构建Docker镜像（避免缓存问题）"
    echo "  - 保留用户数据、配置和日志"
    echo "  - 更新时无需提供许可证密钥"
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_system() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        log_error "此脚本仅支持 Linux 系统"
        exit 1
    fi
    
    # 检查权限
    if [[ $EUID -ne 0 ]]; then
        log_error "请使用 root 权限运行此脚本"
        exit 1
    fi
    
    # 检查必要命令
    for cmd in curl wget tar; do
        if ! command -v $cmd &> /dev/null; then
            log_error "缺少必要命令: $cmd"
            exit 1
        fi
    done
    
    log_success "系统检查通过"
}

# 检查并安装 Docker
check_and_install_docker() {
    if [[ "$SKIP_DOCKER_INSTALL" == "true" ]] || [[ "$AUTO_INSTALL_DOCKER" == "false" ]]; then
        log_info "跳过 Docker 自动安装"
        return
    fi

    # 检查 Docker 是否已安装
    if command -v docker &> /dev/null; then
        log_info "Docker 已安装，版本: $(docker --version)"

        # 检查 Docker 服务是否运行
        if ! systemctl is-active --quiet docker; then
            log_info "启动 Docker 服务..."
            systemctl start docker
            systemctl enable docker
        fi

        # 检查 Docker Compose
        if ! command -v docker-compose &> /dev/null; then
            log_info "安装 Docker Compose..."
            install_docker_compose
        fi

        return
    fi

    log_info "未检测到 Docker，开始自动安装..."

    # 检测系统类型
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$ID
        VER=$VERSION_ID
    else
        log_error "无法检测系统类型"
        exit 1
    fi

    case $OS in
        ubuntu|debian)
            install_docker_debian
            ;;
        centos|rhel|fedora)
            install_docker_redhat
            ;;
        *)
            log_warning "不支持的系统类型: $OS，尝试使用官方安装脚本..."
            install_docker_official
            ;;
    esac

    # 验证安装
    if command -v docker &> /dev/null; then
        log_success "Docker 安装成功"

        # 启动 Docker 服务
        if ! systemctl start docker; then
            log_error "Docker 服务启动失败"
            exit 1
        fi

        if ! systemctl enable docker; then
            log_warning "Docker 服务自启动设置失败"
        fi

        # 等待 Docker 服务完全启动
        sleep 3

        # 验证 Docker 服务是否正常运行
        if ! docker info &> /dev/null; then
            log_error "Docker 服务未正常运行"
            exit 1
        fi

        # 添加当前用户到 docker 组（如果不是 root）
        if [[ $EUID -ne 0 ]] && [[ -n "$SUDO_USER" ]]; then
            usermod -aG docker $SUDO_USER
            log_info "已将用户 $SUDO_USER 添加到 docker 组"
        fi

        # 安装 Docker Compose
        install_docker_compose

        # 最终验证
        if ! command -v docker-compose &> /dev/null; then
            log_error "Docker Compose 安装失败"
            exit 1
        fi

        log_success "Docker 和 Docker Compose 安装完成"
    else
        log_error "Docker 安装失败，无法继续使用 Docker 方式安装"
        log_error "请检查系统环境或手动安装 Docker"
        exit 1
    fi
}

# 安装 Docker (Debian/Ubuntu)
install_docker_debian() {
    log_info "在 Debian/Ubuntu 系统上安装 Docker..."

    # 更新包索引
    log_info "更新软件包索引..."
    if ! apt-get update -qq; then
        log_error "软件包索引更新失败"
        exit 1
    fi

    # 安装必要的包
    log_info "安装必要的依赖包..."
    if ! apt-get install -y -qq ca-certificates curl gnupg lsb-release; then
        log_error "依赖包安装失败"
        exit 1
    fi

    # 添加 Docker 官方 GPG 密钥
    log_info "添加 Docker 官方 GPG 密钥..."
    mkdir -p /etc/apt/keyrings
    if ! curl -fsSL https://download.docker.com/linux/$OS/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg; then
        log_error "Docker GPG 密钥添加失败"
        exit 1
    fi

    # 设置稳定版仓库
    log_info "设置 Docker 软件源..."
    echo \
        "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/$OS \
        $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

    # 更新包索引
    log_info "更新软件包索引（包含 Docker 源）..."
    if ! apt-get update -qq; then
        log_error "Docker 软件源更新失败"
        exit 1
    fi

    # 安装 Docker Engine
    log_info "安装 Docker Engine..."
    if ! apt-get install -y -qq docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin; then
        log_error "Docker Engine 安装失败"
        exit 1
    fi

    log_success "Docker Engine 安装完成"
}

# 安装 Docker (CentOS/RHEL/Fedora)
install_docker_redhat() {
    log_info "在 CentOS/RHEL/Fedora 系统上安装 Docker..."

    # 安装必要的包
    if command -v dnf &> /dev/null; then
        dnf install -y -q yum-utils
        dnf config-manager --add-repo https://download.docker.com/linux/$OS/docker-ce.repo
        dnf install -y -q docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    else
        yum install -y -q yum-utils
        yum-config-manager --add-repo https://download.docker.com/linux/$OS/docker-ce.repo
        yum install -y -q docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    fi
}

# 使用官方安装脚本安装 Docker
install_docker_official() {
    log_info "使用 Docker 官方安装脚本..."

    # 下载并执行官方安装脚本
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    rm get-docker.sh
}

# 安装 Docker Compose (独立版本)
install_docker_compose() {
    # 检查是否已有 docker-compose 命令
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose 已安装，版本: $(docker-compose --version)"
        return
    fi

    # 检查是否有 docker compose 插件
    if docker compose version &> /dev/null; then
        log_info "Docker Compose 插件已安装"
        # 创建 docker-compose 别名
        if [[ ! -f /usr/local/bin/docker-compose ]]; then
            cat > /usr/local/bin/docker-compose << 'EOF'
#!/bin/bash
docker compose "$@"
EOF
            chmod +x /usr/local/bin/docker-compose
            log_info "已创建 docker-compose 命令别名"
        fi
        return
    fi

    log_info "安装 Docker Compose..."

    # 获取最新版本号
    COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)

    if [[ -z "$COMPOSE_VERSION" ]]; then
        log_warning "无法获取最新版本，使用默认版本 v2.24.0"
        COMPOSE_VERSION="v2.24.0"
    fi

    log_info "下载 Docker Compose $COMPOSE_VERSION..."

    # 下载 Docker Compose
    if ! curl -L "https://github.com/docker/compose/releases/download/$COMPOSE_VERSION/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose; then
        log_error "Docker Compose 下载失败"
        exit 1
    fi

    # 设置执行权限
    chmod +x /usr/local/bin/docker-compose

    # 验证安装
    if command -v docker-compose &> /dev/null; then
        log_success "Docker Compose 安装成功，版本: $(docker-compose --version)"
    else
        log_error "Docker Compose 安装失败，命令不可用"
        exit 1
    fi
}

# 确保Docker可用
ensure_docker_available() {
    log_info "确保 Docker 环境可用..."

    # 检查并安装 Docker
    check_and_install_docker

    # 验证 Docker 安装是否成功
    if ! command -v docker &> /dev/null || ! command -v docker-compose &> /dev/null; then
        log_error "Docker 安装失败，无法安装 DStatus"
        log_error "请检查系统环境或手动安装 Docker"
        exit 1
    fi

    log_success "Docker 环境准备完成"
}

# 下载文件
download_file() {
    local url="$1"
    local output="$2"
    
    log_info "下载: $url"
    
    if command -v wget &> /dev/null; then
        wget -q --show-progress -O "$output" "$url"
    else
        curl -L -o "$output" "$url"
    fi
    
    if [[ $? -ne 0 ]]; then
        log_error "下载失败: $url"
        exit 1
    fi
}

# Docker 安装
install_docker() {
    log_info "使用 Docker 方式安装 DStatus..."

    # 确保 Docker 已安装
    if ! command -v docker &> /dev/null || ! command -v docker-compose &> /dev/null; then
        log_info "Docker 未完全安装，开始安装..."
        check_and_install_docker
    fi

    # 创建安装目录
    mkdir -p "$INSTALL_DIR"
    cd "$INSTALL_DIR"

    # 下载 Docker 部署包
    download_file "$DOWNLOAD_BASE/dstatus-docker.tar.gz" "dstatus-docker.tar.gz"

    # 解压到当前目录（不创建子目录）
    log_info "解压部署包..."
    tar -xzf dstatus-docker.tar.gz --strip-components=1
    rm dstatus-docker.tar.gz

    # 修复权限和配置（确保兼容性）
    log_info "修复安装权限和配置..."
    
    # 确保所有文件权限正确
    chown -R root:root "$INSTALL_DIR"
    find "$INSTALL_DIR" -type f -exec chmod 644 {} \;
    find "$INSTALL_DIR" -type d -exec chmod 755 {} \;
    chmod +x "$INSTALL_DIR"/*.sh 2>/dev/null || true
    
    # 创建必要的数据目录
    mkdir -p "$INSTALL_DIR"/{data,logs,data/backups,data/temp}
    chmod -R 777 "$INSTALL_DIR"/data "$INSTALL_DIR"/logs
    
    # 修复 docker-compose.yml 配置（确保包含权限修复）
    if [[ -f "docker-compose.yml" ]]; then
        # 检查并修复用户配置
        if ! grep -q "user:" docker-compose.yml; then
            log_info "添加用户配置到 docker-compose.yml..."
            sed -i '/container_name: dstatus-monitor/a\    user: "0:0"' docker-compose.yml
        fi
        
        # 检查并修复日志挂载路径
        if grep -q "./logs:/logs" docker-compose.yml; then
            log_info "修复日志挂载路径..."
            sed -i 's|./logs:/logs|./logs:/app/logs:rw|g' docker-compose.yml
        fi
        
        # 确保数据目录有读写权限
        if ! grep -q ":rw" docker-compose.yml; then
            sed -i 's|./data:/app/data|./data:/app/data:rw|g' docker-compose.yml
        fi
        
        # 添加配置文件挂载（如果不存在）
        if ! grep -q "config.yaml" docker-compose.yml && [[ -f "config.yaml" ]]; then
            sed -i '/\/app\/logs:rw/a\      - ./config.yaml:/etc/dstatus/config.yaml:ro' docker-compose.yml
        fi
    fi

    # 配置许可证自动激活
    if [[ -n "$LICENSE_KEY" ]]; then
        log_info "配置许可证自动激活..."

        # 添加环境变量到docker-compose.yml
        if ! grep -q "DSTATUS_AUTO_LICENSE" docker-compose.yml; then
            # 在environment部分添加许可证环境变量
            sed -i.bak '/environment:/a\
      - DSTATUS_AUTO_LICENSE='"$LICENSE_KEY" docker-compose.yml
            # 删除备份文件
            rm -f docker-compose.yml.bak

            if grep -q "DSTATUS_AUTO_LICENSE" docker-compose.yml; then
                log_success "许可证将在系统启动时自动激活"
            else
                log_warning "许可证环境变量配置失败，请在Web管理面板中手动激活"
            fi
        else
            log_info "许可证环境变量已存在，跳过配置"
        fi
    fi

    # 配置端口（直接修改 docker-compose.yml）
    if [[ "$PORT" != "5555" ]]; then
        log_info "配置自定义端口: $PORT"
        log_info "修改 Docker Compose 配置..."

        # 直接修改 docker-compose.yml 中的端口映射
        sed -i.bak "s/\"5555:5555\"/\"$PORT:5555\"/g" docker-compose.yml

        # 验证修改是否成功
        if grep -q "\"$PORT:5555\"" docker-compose.yml; then
            log_success "端口配置成功：外部端口 $PORT -> 内部端口 5555"
            rm -f docker-compose.yml.bak
        else
            log_error "端口配置失败"
            # 恢复备份
            if [[ -f docker-compose.yml.bak ]]; then
                mv docker-compose.yml.bak docker-compose.yml
            fi
            exit 1
        fi
    fi

    # 最终权限和启动前检查
    log_info "执行启动前检查..."
    
    # 确保权限修复脚本存在且可执行
    if [[ -f "fix-permissions.sh" ]]; then
        chmod +x fix-permissions.sh
        log_info "权限修复脚本已准备就绪"
    fi
    
    # 确保启动脚本可执行
    if [[ -f "start.sh" ]]; then
        chmod +x start.sh
    else
        log_error "启动脚本不存在"
        exit 1
    fi

    # 启动服务
    log_info "启动 DStatus 服务..."
    ./start.sh

    # 创建系统服务
    create_docker_service

    log_success "Docker 安装完成！"
    log_info "访问地址: http://localhost:$PORT"
}

# 二进制安装已移除，仅支持Docker安装

# 创建 Docker 系统服务
create_docker_service() {
    cat > /etc/systemd/system/${SERVICE_NAME}.service << EOF
[Unit]
Description=DStatus Monitoring System (Docker)
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$INSTALL_DIR
ExecStart=/bin/bash -c './start.sh'
ExecStop=/bin/bash -c './stop.sh'
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable $SERVICE_NAME
}

# 二进制系统服务已移除，仅支持Docker安装

# 更新 DStatus
update_dstatus() {
    log_info "开始更新 DStatus..."

    # 检查是否已安装
    if [[ ! -d "$INSTALL_DIR" ]]; then
        log_error "未找到现有安装，请使用安装模式"
        exit 1
    fi

    # 检测当前安装方式（仅支持Docker）
    if [[ -f "$INSTALL_DIR/docker-compose.yml" ]]; then
        log_info "检测到 Docker 安装"
    else
        log_error "未检测到有效的Docker安装"
        exit 1
    fi

    # 停止现有服务
    log_info "停止现有服务..."
    if systemctl is-active --quiet $SERVICE_NAME; then
        systemctl stop $SERVICE_NAME
        log_info "服务已停止"
    fi

    # 如果是Docker安装，额外停止Docker容器
    if [[ "$INSTALL_METHOD" == "docker" ]] && [[ -f "$INSTALL_DIR/stop.sh" ]]; then
        log_info "停止 Docker 容器..."
        cd "$INSTALL_DIR"
        ./stop.sh 2>/dev/null || true
    fi

    # 备份配置文件
    log_info "备份配置文件..."
    BACKUP_DIR="$INSTALL_DIR/backup-$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"

    if [[ -f "$INSTALL_DIR/config.yaml" ]]; then
        cp "$INSTALL_DIR/config.yaml" "$BACKUP_DIR/"
        log_info "已备份 config.yaml"
    fi

    # 备份数据目录
    if [[ -d "$INSTALL_DIR/data" ]]; then
        cp -r "$INSTALL_DIR/data" "$BACKUP_DIR/"
        log_info "已备份 data 目录"
    fi

    # 执行Docker更新
    update_docker

    log_success "更新完成！"
}

# 更新 Docker 安装
update_docker() {
    log_info "更新 Docker 安装..."

    cd "$INSTALL_DIR"

    # 停止现有服务和容器
    log_info "停止现有服务..."
    if systemctl is-active --quiet $SERVICE_NAME; then
        systemctl stop $SERVICE_NAME
    fi

    # 停止并删除现有容器
    if [[ -f "docker-compose.yml" ]]; then
        log_info "停止 Docker 容器..."
        docker-compose down 2>/dev/null || true

        # 删除旧的Docker镜像以强制重建
        log_info "删除旧的 Docker 镜像..."
        local old_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -E "(dstatus|dstatus-docker)" | head -5)
        if [[ -n "$old_images" ]]; then
            echo "$old_images" | xargs docker rmi -f 2>/dev/null || true
        fi
    fi

    # 下载新版本
    log_info "下载新版本..."
    download_file "$DOWNLOAD_BASE/dstatus-docker.tar.gz" "dstatus-docker-new.tar.gz"

    # 创建临时目录
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"

    # 解压新版本
    tar -xzf "$INSTALL_DIR/dstatus-docker-new.tar.gz" --strip-components=1

    # 保留配置文件
    if [[ -f "$INSTALL_DIR/config.yaml" ]]; then
        cp "$INSTALL_DIR/config.yaml" ./config.yaml
        log_info "保留现有配置文件"
    fi

    # 保留数据目录
    if [[ -d "$INSTALL_DIR/data" ]]; then
        rm -rf ./data
        cp -r "$INSTALL_DIR/data" ./data
        log_info "保留现有数据目录"
    fi

    # 保留日志目录
    if [[ -d "$INSTALL_DIR/logs" ]]; then
        rm -rf ./logs
        cp -r "$INSTALL_DIR/logs" ./logs
        log_info "保留现有日志目录"
    fi

    # 替换文件
    cd "$INSTALL_DIR"
    rm -f dstatus-docker-new.tar.gz

    # 备份当前文件（除了数据和配置）
    for file in *; do
        if [[ "$file" != "data" ]] && [[ "$file" != "logs" ]] && [[ "$file" != "config.yaml" ]] && [[ ! "$file" =~ ^backup- ]]; then
            rm -rf "$file"
        fi
    done

    # 复制新文件
    cp -r "$TEMP_DIR"/* ./

    # 清理临时目录
    rm -rf "$TEMP_DIR"

    # 重新应用权限修复（更新后确保配置正确）
    log_info "重新应用权限修复..."
    
    # 修复文件权限
    chown -R root:root "$INSTALL_DIR"
    find "$INSTALL_DIR" -type f -exec chmod 644 {} \;
    find "$INSTALL_DIR" -type d -exec chmod 755 {} \;
    chmod +x "$INSTALL_DIR"/*.sh 2>/dev/null || true
    
    # 确保数据目录权限正确
    chmod -R 777 "$INSTALL_DIR"/data "$INSTALL_DIR"/logs
    
    # 修复 docker-compose.yml 配置（防止更新包配置不正确）
    if [[ -f "docker-compose.yml" ]]; then
        # 确保用户配置存在
        if ! grep -q "user:" docker-compose.yml; then
            sed -i '/container_name: dstatus-monitor/a\    user: "0:0"' docker-compose.yml
        fi
        
        # 修复日志挂载路径
        if grep -q "./logs:/logs" docker-compose.yml; then
            sed -i 's|./logs:/logs|./logs:/app/logs:rw|g' docker-compose.yml
        fi
        
        # 确保读写权限
        if ! grep -q ":rw" docker-compose.yml; then
            sed -i 's|./data:/app/data|./data:/app/data:rw|g' docker-compose.yml
        fi
    fi

    # 修复Dockerfile启动命令（如果需要）
    if [[ -f "Dockerfile" ]] && ! grep -q '"-c"' Dockerfile; then
        log_info "修复 Dockerfile 启动命令..."
        sed -i 's|CMD \["/app/dstatus"\]|CMD ["/app/dstatus", "-c", "/etc/dstatus/config.yaml"]|g' Dockerfile
    fi

    # 强制重新构建Docker镜像
    log_info "重新构建 Docker 镜像..."
    docker-compose build --no-cache

    # 启动服务
    log_info "启动更新后的服务..."
    ./start.sh

    # 重新启动系统服务
    if systemctl list-unit-files | grep -q "$SERVICE_NAME.service"; then
        systemctl start $SERVICE_NAME
    fi
}

# 二进制更新已移除，仅支持Docker安装

# 检查服务状态
check_service() {
    log_info "检查服务状态..."

    sleep 5

    if systemctl is-active --quiet $SERVICE_NAME; then
        log_success "DStatus 服务运行正常"

        # 检查端口
        if netstat -tlnp | grep ":$PORT " > /dev/null; then
            log_success "服务端口 $PORT 正在监听"
        else
            log_warning "服务端口 $PORT 未监听，请检查配置"
        fi
    else
        log_error "DStatus 服务启动失败"
        log_info "查看日志: journalctl -u $SERVICE_NAME -f"
        exit 1
    fi
}

# 显示安装结果
show_result() {
    echo ""
    echo "🎉 DStatus 安装完成！"
    echo ""
    echo "📋 安装信息:"
    echo "   安装方式: Docker"
    echo "   安装目录: $INSTALL_DIR"
    echo "   服务端口: $PORT"
    echo "   访问地址: http://localhost:$PORT"
    echo ""
    echo "🔧 管理命令:"
    echo "   启动服务: systemctl start $SERVICE_NAME"
    echo "   停止服务: systemctl stop $SERVICE_NAME"
    echo "   重启服务: systemctl restart $SERVICE_NAME"
    echo "   查看状态: systemctl status $SERVICE_NAME"
    echo "   查看日志: journalctl -u $SERVICE_NAME -f"
    echo ""
    echo "📖 更多信息请访问: https://github.com/fev125/dzstatus"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --method)
            INSTALL_METHOD="$2"
            shift 2
            ;;
        --method=*)
            INSTALL_METHOD="${1#*=}"
            shift
            ;;
        --license-key)
            LICENSE_KEY="$2"
            shift 2
            ;;
        --license-key=*)
            LICENSE_KEY="${1#*=}"
            shift
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --port=*)
            PORT="${1#*=}"
            shift
            ;;
        --install-dir)
            INSTALL_DIR="$2"
            shift 2
            ;;
        --install-dir=*)
            INSTALL_DIR="${1#*=}"
            shift
            ;;
        --force)
            FORCE_INSTALL=true
            shift
            ;;
        --update)
            UPDATE_MODE=true
            shift
            ;;
        --skip-docker)
            SKIP_DOCKER_INSTALL=true
            shift
            ;;
        --no-auto-docker)
            AUTO_INSTALL_DOCKER=false
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主安装流程
main() {
    echo "🚀 DStatus 一键安装脚本"
    echo "========================"

    # 检查系统
    check_system

    # 更新模式 - 在许可证检查之前
    if [[ "$UPDATE_MODE" == true ]]; then
        log_info "运行更新模式..."
        update_dstatus
        check_service
        show_result
        return
    fi

    # 安装模式需要许可证密钥
    if [[ -z "$LICENSE_KEY" ]]; then
        log_error "请提供许可证密钥: --license-key=\"YOUR_LICENSE_KEY\""
        exit 1
    fi

    # 确保Docker环境可用
    ensure_docker_available

    # 检查是否已安装
    if [[ -d "$INSTALL_DIR" ]] && [[ "$FORCE_INSTALL" != true ]] && [[ "$UPDATE_MODE" != true ]]; then
        log_warning "DStatus 似乎已经安装在 $INSTALL_DIR"
        log_info "使用 --force 参数强制重新安装，或使用 --update 参数更新"
        exit 1
    fi

    # 执行安装
    # 仅支持Docker安装
    install_docker

    # 检查服务
    check_service

    # 显示结果
    show_result
}

# 运行主函数
main "$@"
