#!/usr/bin/env node

/**
 * 日志文件迁移脚本
 * 将现有的日志文件迁移到统一的 data/logs 目录
 */

const fs = require('fs');
const path = require('path');

const ROOT_DIR = path.join(__dirname, '..');
const OLD_LOGS_DIR = path.join(ROOT_DIR, 'logs');
const NEW_LOGS_DIR = path.join(ROOT_DIR, 'data/logs');

console.log('开始日志文件迁移...');

// 确保新目录存在
if (!fs.existsSync(NEW_LOGS_DIR)) {
    fs.mkdirSync(NEW_LOGS_DIR, { recursive: true });
    console.log(`创建目录: ${NEW_LOGS_DIR}`);
}

// 迁移函数
function migrateFile(oldPath, newPath) {
    try {
        if (fs.existsSync(oldPath)) {
            if (fs.existsSync(newPath)) {
                console.log(`目标文件已存在，跳过: ${path.basename(newPath)}`);
                return;
            }
            
            fs.copyFileSync(oldPath, newPath);
            console.log(`迁移文件: ${path.basename(oldPath)} -> ${path.relative(ROOT_DIR, newPath)}`);
            
            // 验证迁移成功后删除原文件
            if (fs.existsSync(newPath)) {
                fs.unlinkSync(oldPath);
                console.log(`删除原文件: ${path.relative(ROOT_DIR, oldPath)}`);
            }
        }
    } catch (error) {
        console.error(`迁移文件失败 ${oldPath}:`, error.message);
    }
}

// 迁移性能日志
const oldPerformanceLog = path.join(OLD_LOGS_DIR, 'performance.log');
const newPerformanceLog = path.join(NEW_LOGS_DIR, 'performance.log');
migrateFile(oldPerformanceLog, newPerformanceLog);

// 迁移性能日志的轮转文件
for (let i = 1; i <= 5; i++) {
    const oldRotated = path.join(OLD_LOGS_DIR, `performance.log.${i}`);
    const newRotated = path.join(NEW_LOGS_DIR, `performance.${i}.log`);
    migrateFile(oldRotated, newRotated);
}

// 迁移通知日志（如果在旧目录中）
if (fs.existsSync(OLD_LOGS_DIR)) {
    const files = fs.readdirSync(OLD_LOGS_DIR);
    
    for (const file of files) {
        if (file.startsWith('notification-') && file.endsWith('.log')) {
            const oldPath = path.join(OLD_LOGS_DIR, file);
            const newPath = path.join(NEW_LOGS_DIR, file);
            migrateFile(oldPath, newPath);
        }
    }
}

// 检查data/logs目录中的通知日志（可能已经在正确位置）
const dataLogsDir = path.join(ROOT_DIR, 'data/logs');
if (fs.existsSync(dataLogsDir) && dataLogsDir !== NEW_LOGS_DIR) {
    const files = fs.readdirSync(dataLogsDir);
    
    for (const file of files) {
        if (file.endsWith('.log')) {
            const oldPath = path.join(dataLogsDir, file);
            const newPath = path.join(NEW_LOGS_DIR, file);
            
            if (oldPath !== newPath) {
                migrateFile(oldPath, newPath);
            }
        }
    }
}

// 清理空的旧目录
try {
    if (fs.existsSync(OLD_LOGS_DIR)) {
        const remainingFiles = fs.readdirSync(OLD_LOGS_DIR);
        if (remainingFiles.length === 0) {
            fs.rmdirSync(OLD_LOGS_DIR);
            console.log(`删除空目录: ${path.relative(ROOT_DIR, OLD_LOGS_DIR)}`);
        } else {
            console.log(`旧目录仍有文件，保留: ${path.relative(ROOT_DIR, OLD_LOGS_DIR)}`);
        }
    }
} catch (error) {
    console.error('清理旧目录失败:', error.message);
}

// 显示迁移结果
console.log('\n迁移完成！');
console.log('统一日志目录:', path.relative(ROOT_DIR, NEW_LOGS_DIR));

if (fs.existsSync(NEW_LOGS_DIR)) {
    const files = fs.readdirSync(NEW_LOGS_DIR);
    console.log(`当前日志文件 (${files.length}个):`);
    
    files.forEach(file => {
        const filePath = path.join(NEW_LOGS_DIR, file);
        const stats = fs.statSync(filePath);
        const size = (stats.size / 1024).toFixed(2);
        console.log(`  - ${file} (${size}KB)`);
    });
}

console.log('\n注意事项:');
console.log('1. 所有日志文件已迁移到 data/logs 目录');
console.log('2. 该目录可以通过 Docker 卷挂载实现持久化存储');
console.log('3. 日志轮转和管理功能已统一到日志管理模块');
console.log('4. 建议重启应用以确保新的日志路径生效'); 