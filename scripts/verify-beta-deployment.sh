#!/bin/bash

# Beta版本部署验证脚本
# 用于验证部署到服务器的Beta版本是否包含最新的代码修复

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 检查参数
BETA_URL="${1:-https://down.vps.mom/downloads/beta/dstatus-linux-x64-beta}"

log "开始验证Beta版本部署..."
log "Beta版本URL: $BETA_URL"

# 创建临时目录
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"
log "临时目录: $TEMP_DIR"

# 下载Beta版本
log "下载Beta版本二进制文件..."
if curl -fsSL "$BETA_URL" -o dstatus-beta; then
    success "下载成功"
else
    error "下载失败"
    exit 1
fi

# 设置执行权限
chmod +x dstatus-beta

# 显示文件信息
log "文件信息:"
ls -lh dstatus-beta
file dstatus-beta
echo "MD5: $(md5sum dstatus-beta | cut -d' ' -f1)"
echo "SHA256: $(sha256sum dstatus-beta | cut -d' ' -f1)"

# 验证关键代码特征
log "验证代码特征..."

# 检查1: cleanupExpiredInstances
if strings dstatus-beta | grep -q "cleanupExpiredInstances"; then
    success "✅ 找到 cleanupExpiredInstances 方法"
else
    error "❌ 未找到 cleanupExpiredInstances 方法"
fi

# 检查2: Instance retrieval failed 错误消息
if strings dstatus-beta | grep -q "Instance retrieval failed"; then
    success "✅ 找到 'Instance retrieval failed' 错误消息"
else
    error "❌ 未找到 'Instance retrieval failed' 错误消息"
fi

# 检查3: bindingManager相关代码
if strings dstatus-beta | grep -q "bindingManager"; then
    success "✅ 找到 bindingManager 相关代码"
else
    warn "⚠️ 未找到 bindingManager 相关代码（可能被混淆）"
fi

# 检查4: 检查版本信息
log "检查版本信息..."
if strings dstatus-beta | grep -E "v[0-9]+\.[0-9]+\.[0-9]+-beta" | head -5; then
    success "✅ 找到Beta版本标识"
else
    warn "⚠️ 未找到明确的Beta版本标识"
fi

# 快速运行测试（如果可能）
log "尝试运行版本检查..."
if timeout 2s ./dstatus-beta --version 2>/dev/null; then
    success "✅ 二进制文件可以运行"
else
    warn "⚠️ 无法获取版本信息（可能需要配置文件）"
fi

# 比较文件大小
FILE_SIZE=$(stat -c%s dstatus-beta 2>/dev/null || stat -f%z dstatus-beta 2>/dev/null)
if [ $FILE_SIZE -gt 52428800 ]; then
    success "✅ 文件大小正常: $(($FILE_SIZE / 1024 / 1024))MB"
else
    warn "⚠️ 文件大小可能异常: $(($FILE_SIZE / 1024 / 1024))MB"
fi

# 清理
cd /
rm -rf "$TEMP_DIR"

echo ""
echo "========================================="
echo "验证摘要:"
echo "========================================="
echo "如果看到多个✅，说明Beta版本包含了最新的代码修复。"
echo "如果看到❌，说明Beta版本可能使用了旧代码，需要检查构建流程。"
echo ""
echo "建议的下一步操作:"
echo "1. 如果验证失败，检查GitHub Actions的构建日志"
echo "2. 确保构建时使用了最新的代码"
echo "3. 考虑手动触发一次新的Beta构建"
echo "========================================="