#!/usr/bin/env node

/**
 * P2-1批次Material Icons到Tabler Icons映射表
 * 专门为ai-dashboard.js和monitor-optimized.js设计
 */

// 扩展的Material Icons映射表
const EXTENDED_MATERIAL_ICONS_MAPPING = {
  // 基础映射（从现有脚本继承）
  'download': 'ti-download',
  'psychology': 'ti-brain',
  'auto_awesome': 'ti-sparkles',
  'lightbulb': 'ti-bulb',
  'error': 'ti-alert-circle',
  'check_circle': 'ti-circle-check',
  'edit': 'ti-edit',
  'delete': 'ti-trash',
  'add': 'ti-plus',
  'folder': 'ti-folder',
  'keyboard_arrow_down': 'ti-chevron-down',
  'keyboard_arrow_up': 'ti-chevron-up',
  'wifi': 'ti-wifi',
  'computer': 'ti-device-desktop',
  'refresh': 'ti-refresh',
  'select_all': 'ti-select-all',
  'category': 'ti-category',
  
  // P2-1特定映射
  'hourglass_empty': 'ti-hourglass-empty',
  'error_outline': 'ti-alert-circle',
  'checklist': 'ti-list-check',
  'deselect': 'ti-square-off',
  
  // 推荐系统图标映射
  'warning': 'ti-alert-triangle',
  'info': 'ti-info-circle',
  'security': 'ti-shield-check',
  'performance': 'ti-gauge',
  'optimization': 'ti-adjustments',
  'maintenance': 'ti-tool'
};

/**
 * 获取推荐类型对应的图标
 */
function getRecommendationIcon(type) {
  const iconMap = {
    'warning': 'ti-alert-triangle',
    'error': 'ti-alert-circle', 
    'info': 'ti-info-circle',
    'security': 'ti-shield-check',
    'performance': 'ti-gauge',
    'optimization': 'ti-adjustments',
    'maintenance': 'ti-tool'
  };
  return iconMap[type] || 'ti-info-circle';
}

/**
 * 获取状态配置
 */
function getStatusConfig() {
  return {
    online: { icon: 'ti-circle-check', color: 'text-green-600', text: '在线' },
    offline: { icon: 'ti-circle-x', color: 'text-red-600', text: '离线' },
    warning: { icon: 'ti-alert-triangle', color: 'text-yellow-600', text: '警告' },
    error: { icon: 'ti-alert-circle', color: 'text-red-600', text: '错误' },
    loading: { icon: 'ti-loader', color: 'text-blue-600', text: '加载中' }
  };
}

module.exports = {
  EXTENDED_MATERIAL_ICONS_MAPPING,
  getRecommendationIcon,
  getStatusConfig
};
