#!/usr/bin/env node
'use strict';

/**
 * 数据保留设置迁移脚本
 * 为现有DStatus安装添加数据保留配置的默认值
 * 确保向后兼容性，不影响现有数据
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// 获取数据库路径
const dbPath = path.join(__dirname, '..', 'data', 'db.db');

// 检查数据库文件是否存在
if (!fs.existsSync(dbPath)) {
    console.error('❌ 数据库文件不存在:', dbPath);
    console.error('请确保在DStatus项目根目录运行此脚本');
    process.exit(1);
}

// 连接到数据库
console.log('🔗 连接到数据库:', dbPath);
const db = new Database(dbPath);

// WAL模式由database/index.js统一管理，此处无需重复设置

// 定义默认配置
const DEFAULT_SETTINGS = {
    'data_retention_archive_hours': 3,      // 实时数据保留3小时
    'data_retention_minute_days': 14,       // 分钟级数据保留14天
    'data_retention_hour_days': 90          // 小时级数据保留90天
};

console.log('\n📊 开始数据保留设置迁移...\n');

// 检查setting表是否存在
const settingTableExists = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name='setting'
`).get();

if (!settingTableExists) {
    console.error('❌ setting表不存在，请先确保DStatus正常运行');
    db.close();
    process.exit(1);
}

// 获取setting表结构
const settingColumns = db.prepare('PRAGMA table_info(setting)').all();
console.log('📋 setting表结构:', settingColumns.map(c => `${c.name}(${c.type})`).join(', '));

// 检查并添加配置项
let addedCount = 0;
let existingCount = 0;

for (const [key, defaultValue] of Object.entries(DEFAULT_SETTINGS)) {
    try {
        // 检查配置是否已存在
        const existing = db.prepare('SELECT val FROM setting WHERE key = ?').get(key);
        
        if (existing) {
            console.log(`✓ 配置已存在: ${key} = ${existing.val}`);
            existingCount++;
        } else {
            // 添加新配置
            db.prepare('INSERT INTO setting (key, val) VALUES (?, ?)').run(key, defaultValue);
            console.log(`✅ 已添加配置: ${key} = ${defaultValue}`);
            addedCount++;
        }
    } catch (error) {
        console.error(`❌ 处理配置 ${key} 时出错:`, error.message);
    }
}

// 验证配置是否正确添加
console.log('\n🔍 验证迁移结果:');
for (const key of Object.keys(DEFAULT_SETTINGS)) {
    const result = db.prepare('SELECT val FROM setting WHERE key = ?').get(key);
    if (result) {
        console.log(`  ✓ ${key}: ${result.val}`);
    } else {
        console.log(`  ❌ ${key}: 未找到`);
    }
}

// 检查数据库中的节点数量
const nodeCount = db.prepare('SELECT COUNT(*) as count FROM servers WHERE status > 0').get();
console.log(`\n📈 当前活跃节点数: ${nodeCount.count}`);

// 计算预期存储空间
const archiveHours = DEFAULT_SETTINGS['data_retention_archive_hours'];
const minuteDays = DEFAULT_SETTINGS['data_retention_minute_days'];
const hourDays = DEFAULT_SETTINGS['data_retention_hour_days'];

const recordSize = 79; // 字节
const pollingInterval = 3000; // 默认3秒
const nodes = nodeCount.count;

const archiveRecords = nodes * Math.ceil((archiveHours * 60 * 60 * 1000) / pollingInterval);
const minuteRecords = nodes * (minuteDays * 1440);
const hourRecords = nodes * (hourDays * 24);

const totalMB = Math.round((archiveRecords + minuteRecords + hourRecords) * recordSize / 1024 / 1024);

console.log(`\n💾 预期存储空间 (${nodes}个节点):
  • 实时数据 (${archiveHours}小时): ${Math.round(archiveRecords * recordSize / 1024 / 1024)}MB
  • 分钟级数据 (${minuteDays}天): ${Math.round(minuteRecords * recordSize / 1024 / 1024)}MB
  • 小时级数据 (${hourDays}天): ${Math.round(hourRecords * recordSize / 1024 / 1024)}MB
  • 总计: ~${totalMB}MB`);

// 关闭数据库连接
db.close();

// 输出迁移总结
console.log(`\n📝 迁移总结:
  • 新增配置项: ${addedCount}
  • 已存在配置: ${existingCount}
  • 总配置项数: ${Object.keys(DEFAULT_SETTINGS).length}`);

if (addedCount > 0) {
    console.log('\n🎉 数据保留设置迁移完成！');
    console.log('📌 用户现在可以通过"高级设置"页面自定义数据保留时间');
    console.log('📌 配置更改将在下次系统重启或数据清理时生效');
    console.log('📌 建议重启DStatus服务以应用新配置');
} else {
    console.log('\n✨ 所有配置已存在，无需迁移');
}

console.log('\n✅ 迁移脚本执行完成');