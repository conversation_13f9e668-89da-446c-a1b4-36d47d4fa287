#!/usr/bin/env node

/**
 * SSH密码批量加密升级脚本
 * 将数据库中的明文SSH密码升级为加密格式
 */

const Database = require('better-sqlite3');
const { encryptSSHData } = require('../modules/servers/simple-crypto');

class SSHEncryptionUpgrader {
    constructor() {
        this.db = new Database('./data/db.db');
        this.stats = {
            total: 0,
            upgraded: 0,
            skipped: 0,
            errors: 0
        };
    }

    /**
     * 检查密码是否已加密
     */
    isEncrypted(password) {
        return password && password.includes(':') && password.length > 32;
    }

    /**
     * 分析需要升级的服务器
     */
    analyzeServers() {
        console.log('🔍 分析需要升级的服务器...');
        
        const servers = this.db.prepare('SELECT sid, name, data FROM servers').all();
        const needUpgrade = [];

        servers.forEach(server => {
            const data = JSON.parse(server.data);
            if (data.ssh) {
                const needsUpgrade = 
                    (data.ssh.password && !this.isEncrypted(data.ssh.password)) ||
                    (data.ssh.passphrase && !this.isEncrypted(data.ssh.passphrase));
                
                if (needsUpgrade) {
                    needUpgrade.push({
                        sid: server.sid,
                        name: server.name,
                        hasPassword: !!data.ssh.password,
                        hasPassphrase: !!data.ssh.passphrase,
                        passwordEncrypted: this.isEncrypted(data.ssh.password),
                        passphraseEncrypted: this.isEncrypted(data.ssh.passphrase)
                    });
                }
            }
        });

        console.log(`📊 分析结果: 发现 ${needUpgrade.length} 个服务器需要升级`);
        return needUpgrade;
    }

    /**
     * 升级单个服务器的SSH密码
     */
    upgradeServer(sid) {
        try {
            // 获取原始数据
            const server = this.db.prepare('SELECT data FROM servers WHERE sid = ?').get(sid);
            if (!server) {
                throw new Error('服务器不存在');
            }

            const data = JSON.parse(server.data);
            
            // 检查是否需要升级
            if (!data.ssh) {
                return { success: true, message: '无SSH配置，跳过' };
            }

            const passwordNeedsUpgrade = data.ssh.password && !this.isEncrypted(data.ssh.password);
            const passphraseNeedsUpgrade = data.ssh.passphrase && !this.isEncrypted(data.ssh.passphrase);

            if (!passwordNeedsUpgrade && !passphraseNeedsUpgrade) {
                return { success: true, message: '已加密，跳过' };
            }

            // 备份原始SSH数据
            const originalSSH = { ...data.ssh };

            // 加密SSH数据
            const encryptedSSH = encryptSSHData(data.ssh);
            
            // 更新数据
            const updatedData = {
                ...data,
                ssh: encryptedSSH
            };

            // 保存到数据库
            this.db.prepare('UPDATE servers SET data = ? WHERE sid = ?')
                .run(JSON.stringify(updatedData), sid);

            // 验证加密结果
            const verification = this.db.prepare('SELECT data FROM servers WHERE sid = ?').get(sid);
            const verifyData = JSON.parse(verification.data);
            
            const passwordUpgraded = !verifyData.ssh.password || this.isEncrypted(verifyData.ssh.password);
            const passphraseUpgraded = !verifyData.ssh.passphrase || this.isEncrypted(verifyData.ssh.passphrase);

            if (passwordUpgraded && passphraseUpgraded) {
                return { 
                    success: true, 
                    message: '升级成功',
                    details: {
                        passwordUpgraded: passwordNeedsUpgrade,
                        passphraseUpgraded: passphraseNeedsUpgrade
                    }
                };
            } else {
                throw new Error('加密验证失败');
            }

        } catch (error) {
            return { 
                success: false, 
                message: `升级失败: ${error.message}` 
            };
        }
    }

    /**
     * 执行批量升级
     */
    async performUpgrade(dryRun = false) {
        console.log('🚀 开始SSH密码加密升级...');
        console.log('📋 模式:', dryRun ? '预览模式（不会实际修改）' : '实际升级模式');
        console.log('');

        const needUpgrade = this.analyzeServers();
        
        if (needUpgrade.length === 0) {
            console.log('✅ 所有SSH密码均已加密，无需升级');
            return;
        }

        console.log('📝 升级计划:');
        needUpgrade.forEach((server, index) => {
            console.log(`${index + 1}. [${server.sid.substring(0, 8)}...] ${server.name}`);
            if (server.hasPassword && !server.passwordEncrypted) {
                console.log('   🔒 password: 明文 → 加密');
            }
            if (server.hasPassphrase && !server.passphraseEncrypted) {
                console.log('   🔒 passphrase: 明文 → 加密');
            }
        });
        console.log('');

        if (dryRun) {
            console.log('ℹ️  这是预览模式，不会实际修改数据');
            console.log('💡 要执行实际升级，请运行: node scripts/upgrade-ssh-encryption.js --execute');
            return;
        }

        // 开始事务
        console.log('🔄 开始批量升级...');
        this.db.prepare('BEGIN').run();

        try {
            for (let i = 0; i < needUpgrade.length; i++) {
                const server = needUpgrade[i];
                this.stats.total++;

                console.log(`[${i + 1}/${needUpgrade.length}] 升级 ${server.name}...`);
                
                const result = this.upgradeServer(server.sid);
                
                if (result.success) {
                    if (result.message === '升级成功') {
                        this.stats.upgraded++;
                        console.log(`   ✅ ${result.message}`);
                        if (result.details) {
                            if (result.details.passwordUpgraded) console.log('      - password已加密');
                            if (result.details.passphraseUpgraded) console.log('      - passphrase已加密');
                        }
                    } else {
                        this.stats.skipped++;
                        console.log(`   ⏭️  ${result.message}`);
                    }
                } else {
                    this.stats.errors++;
                    console.log(`   ❌ ${result.message}`);
                }
            }

            // 提交事务
            this.db.prepare('COMMIT').run();
            console.log('✅ 事务已提交');

        } catch (error) {
            // 回滚事务
            this.db.prepare('ROLLBACK').run();
            console.error('❌ 升级过程中出错，已回滚:', error.message);
            throw error;
        }

        // 输出统计结果
        console.log('');
        console.log('📊 升级完成统计:');
        console.log(`   处理总数: ${this.stats.total}`);
        console.log(`   升级成功: ${this.stats.upgraded}`);
        console.log(`   跳过: ${this.stats.skipped}`);
        console.log(`   错误: ${this.stats.errors}`);
        console.log(`   成功率: ${((this.stats.upgraded / Math.max(this.stats.total, 1)) * 100).toFixed(1)}%`);
    }

    /**
     * 验证升级结果
     */
    verifyUpgrade() {
        console.log('🔍 验证升级结果...');
        
        const servers = this.db.prepare('SELECT sid, name, data FROM servers').all();
        let totalPasswords = 0;
        let encryptedPasswords = 0;
        let totalPassphrases = 0;
        let encryptedPassphrases = 0;

        servers.forEach(server => {
            const data = JSON.parse(server.data);
            if (data.ssh) {
                if (data.ssh.password) {
                    totalPasswords++;
                    if (this.isEncrypted(data.ssh.password)) {
                        encryptedPasswords++;
                    }
                }
                if (data.ssh.passphrase) {
                    totalPassphrases++;
                    if (this.isEncrypted(data.ssh.passphrase)) {
                        encryptedPassphrases++;
                    }
                }
            }
        });

        console.log('📈 验证结果:');
        console.log(`   SSH密码: ${encryptedPasswords}/${totalPasswords} 已加密 (${((encryptedPasswords / Math.max(totalPasswords, 1)) * 100).toFixed(1)}%)`);
        console.log(`   私钥密码: ${encryptedPassphrases}/${totalPassphrases} 已加密 (${((encryptedPassphrases / Math.max(totalPassphrases, 1)) * 100).toFixed(1)}%)`);
        
        if (encryptedPasswords === totalPasswords && encryptedPassphrases === totalPassphrases) {
            console.log('✅ 所有SSH密码均已成功加密');
        } else {
            console.log('⚠️  仍有密码未加密，请检查');
        }
    }

    close() {
        this.db.close();
    }
}

// 主程序
async function main() {
    const upgrader = new SSHEncryptionUpgrader();
    
    try {
        const args = process.argv.slice(2);
        const isExecute = args.includes('--execute');
        const isVerify = args.includes('--verify');

        if (isVerify) {
            upgrader.verifyUpgrade();
        } else {
            await upgrader.performUpgrade(!isExecute);
            
            if (isExecute) {
                console.log('');
                upgrader.verifyUpgrade();
            }
        }
        
    } catch (error) {
        console.error('❌ 升级失败:', error.message);
        process.exit(1);
    } finally {
        upgrader.close();
    }
}

// 如果直接运行脚本
if (require.main === module) {
    main();
}

module.exports = SSHEncryptionUpgrader;