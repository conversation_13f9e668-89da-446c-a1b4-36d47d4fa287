#!/usr/bin/env node
"use strict";

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(query) {
    return new Promise(resolve => rl.question(query, resolve));
}

async function main() {
    console.log('=== DzStatus 数据库切换工具 ===\n');
    
    try {
        // 读取当前配置
        const configPath = path.join(__dirname, '../config/database.json');
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        const currentDB = config.activeDatabase;
        
        console.log(`当前数据库: ${currentDB}`);
        console.log('\n可用的数据库:');
        
        let index = 1;
        const dbOptions = [];
        for (const [key, db] of Object.entries(config.databases)) {
            console.log(`${index}. ${key} - ${db.description}`);
            dbOptions.push(key);
            index++;
        }
        
        const choice = await question('\n请选择要切换到的数据库 (输入编号或 q 退出): ');
        
        if (choice.toLowerCase() === 'q') {
            console.log('👋 退出');
            rl.close();
            return;
        }
        
        const selectedIndex = parseInt(choice) - 1;
        if (selectedIndex >= 0 && selectedIndex < dbOptions.length) {
            const targetDB = dbOptions[selectedIndex];
            
            if (targetDB === currentDB) {
                console.log('\n⚠️  已经是当前数据库，无需切换');
            } else {
                console.log(`\n准备切换: ${currentDB} → ${targetDB}`);
                
                console.log('\n⚠️  警告:');
                console.log('- 切换数据库不会迁移数据');
                console.log('- 如需迁移数据，请使用 database/migrate-db.js');
                console.log('- 切换后需要重启应用');
                
                const confirm = await question('\n确定要切换吗? (y/n): ');
                
                if (confirm.toLowerCase() === 'y') {
                    config.activeDatabase = targetDB;
                    config.lastUpdated = new Date().toISOString();
                    config.updatedBy = 'switch-tool';
                    
                    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
                    
                    console.log('\n✅ 数据库配置已切换到:', targetDB);
                    console.log('🔄 请重启应用以生效');
                    
                    // 显示新数据库的连接信息
                    const dbConfig = config.databases[targetDB];
                    if (targetDB === 'sqlite') {
                        console.log(`\nSQLite 路径: ${dbConfig.path}`);
                    } else if (targetDB === 'postgresql') {
                        const connUrl = new URL(dbConfig.connection);
                        console.log(`\nPostgreSQL 信息:`);
                        console.log(`- 主机: ${connUrl.hostname}`);
                        console.log(`- 端口: ${connUrl.port}`);
                        console.log(`- 数据库: ${connUrl.pathname.slice(1)}`);
                    }
                } else {
                    console.log('❌ 已取消');
                }
            }
        } else {
            console.log('❌ 无效的选择');
        }
        
    } catch (error) {
        console.error('❌ 错误:', error.message);
    } finally {
        rl.close();
    }
}

main().catch(console.error);