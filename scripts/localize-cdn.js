#!/usr/bin/env node
/**
 * CDN 资源本地化脚本
 * 将远程 CDN 资源下载并配置为本地资源
 */

const fs = require('fs-extra');
const path = require('path');
const https = require('https');

// 配置路径
const STATIC_DIR = path.join(__dirname, '..', 'static');
const TABLER_ICONS_DIR = path.join(STATIC_DIR, 'libs', 'tabler-icons');
const FONTS_DIR = path.join(STATIC_DIR, 'fonts', 'roboto-mono');
const CSS_FONTS_DIR = path.join(STATIC_DIR, 'css', 'fonts');

/**
 * 下载文件
 */
async function downloadFile(url, dest) {
    const file = fs.createWriteStream(dest);
    return new Promise((resolve, reject) => {
        https.get(url, (response) => {
            response.pipe(file);
            file.on('finish', () => {
                file.close(resolve);
            });
        }).on('error', reject);
    });
}

/**
 * 本地化 Tabler Icons
 */
async function localizeTablerIcons() {
    console.log('📦 处理 Tabler Icons...');
    
    // 创建目录
    fs.ensureDirSync(TABLER_ICONS_DIR);
    fs.ensureDirSync(path.join(TABLER_ICONS_DIR, 'fonts'));
    
    // 检查 node_modules 中是否已安装
    const nodeModulesPath = path.join(__dirname, '..', 'node_modules', '@tabler', 'icons-webfont', 'dist');
    
    if (fs.existsSync(nodeModulesPath)) {
        // 复制 CSS 文件
        const cssSource = path.join(nodeModulesPath, 'tabler-icons.min.css');
        const cssDest = path.join(TABLER_ICONS_DIR, 'tabler-icons.min.css');
        
        // 读取并修改 CSS 文件中的字体路径
        let cssContent = fs.readFileSync(cssSource, 'utf8');
        // 修改字体路径为相对路径
        cssContent = cssContent.replace(/url\("fonts\//g, 'url("/libs/tabler-icons/fonts/');
        cssContent = cssContent.replace(/url\('fonts\//g, "url('/libs/tabler-icons/fonts/");
        fs.writeFileSync(cssDest, cssContent);
        
        // 复制字体文件
        const fontsSource = path.join(nodeModulesPath, 'fonts');
        const fontsDest = path.join(TABLER_ICONS_DIR, 'fonts');
        
        if (fs.existsSync(fontsSource)) {
            fs.copySync(fontsSource, fontsDest);
            console.log('✅ Tabler Icons 本地化完成');
            
            // 列出复制的字体文件
            const fontFiles = fs.readdirSync(fontsDest);
            console.log(`   复制了 ${fontFiles.length} 个字体文件:`, fontFiles.join(', '));
        } else {
            console.log('⚠️  未找到字体文件目录');
        }
    } else {
        console.log('❌ 未找到 @tabler/icons-webfont，请先运行: npm install @tabler/icons-webfont@3.34.0');
        return false;
    }
    
    return true;
}

/**
 * 创建 Roboto Mono 本地字体配置
 */
async function createRobotoMonoConfig() {
    console.log('\n📦 处理 Roboto Mono 字体...');
    
    // 创建目录
    fs.ensureDirSync(FONTS_DIR);
    fs.ensureDirSync(CSS_FONTS_DIR);
    
    // 创建本地字体 CSS 文件
    const fontCss = `/* Roboto Mono Local Font Definitions */
/* 本地化字体文件，需要手动下载 woff2 文件 */

@font-face {
  font-family: 'Roboto Mono';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local('Roboto Mono Regular'), local('RobotoMono-Regular'),
       url('/fonts/roboto-mono/roboto-mono-v23-latin-regular.woff2') format('woff2'),
       url('/fonts/roboto-mono/roboto-mono-v23-latin-regular.woff') format('woff');
}

@font-face {
  font-family: 'Roboto Mono';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local('Roboto Mono Medium'), local('RobotoMono-Medium'),
       url('/fonts/roboto-mono/roboto-mono-v23-latin-500.woff2') format('woff2'),
       url('/fonts/roboto-mono/roboto-mono-v23-latin-500.woff') format('woff');
}

@font-face {
  font-family: 'Roboto Mono';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: local('Roboto Mono SemiBold'), local('RobotoMono-SemiBold'),
       url('/fonts/roboto-mono/roboto-mono-v23-latin-600.woff2') format('woff2'),
       url('/fonts/roboto-mono/roboto-mono-v23-latin-600.woff') format('woff');
}

/* 回退到系统等宽字体 */
.font-mono {
  font-family: 'Roboto Mono', 'Courier New', Courier, monospace;
}`;
    
    const fontCssPath = path.join(CSS_FONTS_DIR, 'roboto-mono.css');
    fs.writeFileSync(fontCssPath, fontCss);
    console.log('✅ Roboto Mono CSS 文件创建完成');
    
    // 创建字体下载说明
    const downloadInstructions = `# Roboto Mono 字体下载说明

## 自动下载方法
运行以下命令下载字体文件：
\`\`\`bash
node scripts/download-fonts.js
\`\`\`

## 手动下载方法
1. 访问 Google Fonts Helper:
   https://gwfh.mranftl.com/fonts/roboto-mono?subsets=latin

2. 选择以下字重：
   - Regular 400
   - Medium 500
   - SemiBold 600

3. 下载 woff2 格式文件

4. 将下载的文件重命名并放置到此目录：
   - roboto-mono-v23-latin-regular.woff2 (400)
   - roboto-mono-v23-latin-500.woff2 (500)
   - roboto-mono-v23-latin-600.woff2 (600)

## 验证
确保以下文件存在：
- roboto-mono-v23-latin-regular.woff2
- roboto-mono-v23-latin-500.woff2
- roboto-mono-v23-latin-600.woff2
`;
    
    fs.writeFileSync(path.join(FONTS_DIR, 'README.md'), downloadInstructions);
    console.log('📌 字体下载说明已创建: static/fonts/roboto-mono/README.md');
    
    return true;
}

/**
 * 更新 HTML 和 CSS 文件中的引用
 */
async function updateReferences() {
    console.log('\n📝 更新文件引用...');
    
    // 1. 更新 views/base.html
    const baseHtmlPath = path.join(__dirname, '..', 'views', 'base.html');
    if (fs.existsSync(baseHtmlPath)) {
        let baseHtml = fs.readFileSync(baseHtmlPath, 'utf8');
        
        // 查找并替换 Tabler Icons CDN 引用
        const tablerPattern = /<link[^>]*href="https:\/\/[^"]*tabler-icons[^"]*"[^>]*>/g;
        const tablerReplacement = '<link rel="stylesheet" href="/libs/tabler-icons/tabler-icons.min.css">';
        
        if (baseHtml.match(tablerPattern)) {
            // 替换主要引用和备用引用（包括 onerror 逻辑）
            baseHtml = baseHtml.replace(
                /<link rel="stylesheet" href="https:\/\/cdn\.jsdelivr\.net\/npm\/@tabler\/icons-webfont@[^"]+"\s+id="tabler-icons-css"\s+onerror="[^"]+"\s+crossorigin="anonymous">/,
                '<link rel="stylesheet" href="/libs/tabler-icons/tabler-icons.min.css" id="tabler-icons-css">'
            );
            
            fs.writeFileSync(baseHtmlPath, baseHtml);
            console.log('✅ views/base.html 已更新');
        } else {
            console.log('ℹ️  views/base.html 中未找到 CDN 引用，可能已经本地化');
        }
    } else {
        console.log('⚠️  未找到 views/base.html');
    }
    
    // 2. 更新 static/css/components/webfonts-metrics.css
    const webfontsCssPath = path.join(STATIC_DIR, 'css', 'components', 'webfonts-metrics.css');
    if (fs.existsSync(webfontsCssPath)) {
        let webfontsCss = fs.readFileSync(webfontsCssPath, 'utf8');
        
        // 替换 Google Fonts 引用
        const googleFontsPattern = /@import\s+url\(['"]https:\/\/fonts\.googleapis\.com[^'"]+['"]\);?/;
        const localFontsImport = '@import url(\'/css/fonts/roboto-mono.css\');';
        
        if (webfontsCss.match(googleFontsPattern)) {
            webfontsCss = webfontsCss.replace(googleFontsPattern, localFontsImport);
            fs.writeFileSync(webfontsCssPath, webfontsCss);
            console.log('✅ webfonts-metrics.css 已更新');
        } else {
            console.log('ℹ️  webfonts-metrics.css 中未找到 Google Fonts 引用');
        }
    } else {
        console.log('⚠️  未找到 webfonts-metrics.css');
    }
    
    return true;
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 开始 CDN 资源本地化...\n');
    
    try {
        // 1. 本地化 Tabler Icons
        const tablerSuccess = await localizeTablerIcons();
        
        // 2. 创建 Roboto Mono 配置
        const robotoSuccess = await createRobotoMonoConfig();
        
        // 3. 更新文件引用
        const updateSuccess = await updateReferences();
        
        // 汇总结果
        console.log('\n' + '='.repeat(50));
        console.log('🎉 CDN 资源本地化完成！\n');
        
        console.log('📋 完成清单:');
        console.log(`1. ${tablerSuccess ? '✅' : '❌'} Tabler Icons 本地化`);
        console.log(`2. ${robotoSuccess ? '✅' : '❌'} Roboto Mono 配置创建`);
        console.log(`3. ${updateSuccess ? '✅' : '❌'} 文件引用更新`);
        
        console.log('\n⚠️  注意事项:');
        console.log('1. Roboto Mono 字体文件需要手动下载');
        console.log('   查看: static/fonts/roboto-mono/README.md');
        console.log('2. 重启服务后生效: npm run restart');
        console.log('3. 清除浏览器缓存后测试');
        
        console.log('\n💡 验证方法:');
        console.log('1. 打开浏览器开发者工具 Network 面板');
        console.log('2. 刷新页面，检查是否还有 CDN 请求');
        console.log('3. 断网测试图标和字体是否正常显示');
        
    } catch (error) {
        console.error('❌ 本地化过程中出错:', error);
        process.exit(1);
    }
}

// 执行主函数
if (require.main === module) {
    main();
}

module.exports = { localizeTablerIcons, createRobotoMonoConfig, updateReferences };