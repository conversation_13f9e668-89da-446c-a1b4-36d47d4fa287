#!/usr/bin/env node
'use strict';

/**
 * 增强版数据库清理脚本
 * 实施优化的数据保留策略
 */

const Database = require('better-sqlite3');
const dbConfig = require('../database/config');
const path = require('path');
const fs = require('fs');

// 获取数据库路径
const dbPath = dbConfig.getPaths().main;
const db = new Database(dbPath);

// WAL模式由database/index.js统一管理，此处无需重复设置

console.log('=== DStatus 数据库清理工具 ===');
console.log(`数据库路径: ${dbPath}`);
console.log(`开始时间: ${new Date().toLocaleString()}\n`);

// 清理配置 - 根据数据保留策略文档
const cleanupTasks = [
    // 负载数据清理
    {
        table: 'load_archive',
        timeField: 'created_at',
        retentionHours: 3,
        description: '实时负载数据（2秒级）'
    },
    {
        table: 'load_m',
        timeField: 'created_at',
        retentionDays: 7,
        description: '分钟级负载数据'
    },
    {
        table: 'load_h',
        timeField: 'created_at',
        retentionDays: 30,
        description: '小时级负载数据'
    },
    
    // TCPing 数据清理
    {
        table: 'tcping_archive',
        timeField: 'created_at',
        retentionHours: 1,
        description: 'TCPing 原始数据'
    },
    {
        table: 'tcping_m',
        timeField: 'created_at',
        retentionDays: 7,
        description: 'TCPing 分钟级数据'
    },
    {
        table: 'tcping_5m',
        timeField: 'created_at',
        retentionDays: 14,
        description: 'TCPing 5分钟级数据'
    },
    {
        table: 'tcping_h',
        timeField: 'created_at',
        retentionDays: 30,
        description: 'TCPing 小时级数据'
    },
    {
        table: 'tcping_d',
        timeField: 'created_at',
        retentionDays: 365, // 优化为1年
        description: 'TCPing 天级数据'
    },
    {
        table: 'tcping_month',
        timeField: 'created_at',
        retentionDays: 1095, // 优化为3年
        description: 'TCPing 月级数据'
    },
    
    // AI 报告清理
    {
        table: 'ai_reports',
        timeField: 'created_at',
        retentionDays: 180, // 6个月
        description: 'AI 分析报告'
    }
];

// 统计信息
let totalDeleted = 0;
let totalSpaceSaved = 0;

// 获取表大小（近似值）
function getTableSize(tableName) {
    try {
        const result = db.prepare(`
            SELECT COUNT(*) as count,
                   AVG(LENGTH(CAST(rowid AS TEXT))) as avg_row_size
            FROM ${tableName}
        `).get();
        
        // 粗略估算：行数 * 平均行大小 * 100 字节（假设平均每行数据）
        return result.count * 100;
    } catch (err) {
        return 0;
    }
}

// 检查表是否存在
function tableExists(tableName) {
    const result = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
    `).get(tableName);
    return !!result;
}

// 清理单个表
function cleanupTable(task) {
    if (!tableExists(task.table)) {
        console.log(`⚠️  表 ${task.table} 不存在，跳过清理`);
        return;
    }
    
    const now = Math.floor(Date.now() / 1000);
    let cutoff;
    
    if (task.retentionHours) {
        cutoff = now - (task.retentionHours * 60 * 60);
    } else if (task.retentionDays) {
        cutoff = now - (task.retentionDays * 24 * 60 * 60);
    }
    
    try {
        // 获取清理前的大小
        const sizeBefore = getTableSize(task.table);
        
        // 统计要删除的记录数
        const toDelete = db.prepare(`
            SELECT COUNT(*) as count 
            FROM ${task.table} 
            WHERE ${task.timeField} < ?
        `).get(cutoff);
        
        if (toDelete.count > 0) {
            console.log(`\n📊 清理 ${task.description} (${task.table})`);
            console.log(`   保留期限: ${task.retentionHours ? task.retentionHours + '小时' : task.retentionDays + '天'}`);
            console.log(`   待删除记录: ${toDelete.count.toLocaleString()} 条`);
            
            // 执行删除
            const startTime = Date.now();
            const result = db.prepare(`
                DELETE FROM ${task.table} 
                WHERE ${task.timeField} < ?
            `).run(cutoff);
            
            const duration = Date.now() - startTime;
            
            // 获取清理后的大小
            const sizeAfter = getTableSize(task.table);
            const spaceSaved = Math.max(0, sizeBefore - sizeAfter);
            
            console.log(`   ✅ 已删除: ${result.changes.toLocaleString()} 条记录`);
            console.log(`   ⏱️  耗时: ${duration}ms`);
            console.log(`   💾 释放空间: ~${(spaceSaved / 1024 / 1024).toFixed(2)} MB`);
            
            totalDeleted += result.changes;
            totalSpaceSaved += spaceSaved;
        } else {
            console.log(`\n✨ ${task.description} (${task.table}) - 无需清理`);
        }
    } catch (err) {
        console.error(`\n❌ 清理 ${task.table} 失败:`, err.message);
    }
}

// 清理无效数据
function cleanupInvalidData() {
    console.log('\n🔍 清理无效数据...');
    
    // 清理 load_archive 中的无效记录
    try {
        const invalidLoad = db.prepare(`
            DELETE FROM load_archive 
            WHERE cpu < 0 OR mem < 0 OR 
                  (cpu = 0 AND mem = 0 AND swap = 0)
        `).run();
        
        if (invalidLoad.changes > 0) {
            console.log(`   ✅ 删除 ${invalidLoad.changes} 条无效负载数据`);
            totalDeleted += invalidLoad.changes;
        }
    } catch (err) {
        console.error('   ❌ 清理无效负载数据失败:', err.message);
    }
    
    // 清理孤立的流量数据
    try {
        const orphanTraffic = db.prepare(`
            DELETE FROM traffic 
            WHERE sid NOT IN (SELECT sid FROM servers)
        `).run();
        
        if (orphanTraffic.changes > 0) {
            console.log(`   ✅ 删除 ${orphanTraffic.changes} 条孤立流量数据`);
            totalDeleted += orphanTraffic.changes;
        }
    } catch (err) {
        console.error('   ❌ 清理孤立流量数据失败:', err.message);
    }
}

// 优化数据库
function optimizeDatabase() {
    console.log('\n🔧 优化数据库...');
    
    try {
        // 分析表以更新统计信息
        console.log('   📊 分析表统计信息...');
        db.prepare('ANALYZE').run();
        
        // 执行 VACUUM 释放空间
        console.log('   🗜️  执行 VACUUM 压缩数据库...');
        const vacuumStart = Date.now();
        db.prepare('VACUUM').run();
        const vacuumDuration = Date.now() - vacuumStart;
        
        console.log(`   ✅ 数据库优化完成 (耗时: ${vacuumDuration}ms)`);
    } catch (err) {
        console.error('   ❌ 数据库优化失败:', err.message);
    }
}

// 生成清理报告
function generateReport() {
    console.log('\n📋 清理报告');
    console.log('='.repeat(50));
    
    // 获取数据库文件大小
    const stats = fs.statSync(dbPath);
    const dbSize = stats.size / 1024 / 1024; // MB
    
    console.log(`数据库大小: ${dbSize.toFixed(2)} MB`);
    console.log(`删除记录总数: ${totalDeleted.toLocaleString()} 条`);
    console.log(`预计释放空间: ~${(totalSpaceSaved / 1024 / 1024).toFixed(2)} MB`);
    console.log(`完成时间: ${new Date().toLocaleString()}`);
    
    // 记录到日志文件
    const logDir = path.join(path.dirname(dbPath), 'logs');
    if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
    }
    
    const logFile = path.join(logDir, `cleanup-${new Date().toISOString().split('T')[0]}.log`);
    const logContent = `
清理时间: ${new Date().toLocaleString()}
数据库大小: ${dbSize.toFixed(2)} MB
删除记录数: ${totalDeleted}
释放空间: ~${(totalSpaceSaved / 1024 / 1024).toFixed(2)} MB
`;
    
    fs.appendFileSync(logFile, logContent);
    console.log(`\n日志已保存到: ${logFile}`);
}

// 主函数
async function main() {
    try {
        // 开始事务
        console.log('🚀 开始数据库清理...\n');
        
        // 执行清理任务
        for (const task of cleanupTasks) {
            cleanupTable(task);
        }
        
        // 清理无效数据
        cleanupInvalidData();
        
        // 优化数据库
        optimizeDatabase();
        
        // 生成报告
        generateReport();
        
        console.log('\n✅ 数据库清理完成！');
    } catch (err) {
        console.error('\n❌ 清理过程出错:', err);
        process.exit(1);
    } finally {
        db.close();
    }
}

// 处理命令行参数
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
    console.log(`
使用方法:
  node cleanup-database-enhanced.js [选项]

选项:
  --dry-run    模拟运行，只显示将要删除的数据
  --force      强制执行，跳过确认
  --help, -h   显示帮助信息

示例:
  node cleanup-database-enhanced.js
  node cleanup-database-enhanced.js --dry-run
`);
    process.exit(0);
}

// 运行主函数
main().catch(err => {
    console.error('致命错误:', err);
    process.exit(1);
});