#!/usr/bin/env node

/**
 * 批量替换Font Awesome图标为Tabler Icons
 */

const fs = require('fs');
const path = require('path');

// Font Awesome 到 Tabler Icons 映射表
const FA_TO_TABLER_MAPPING = {
  'fa-network-wired': 'ti-router',
  'fa-server': 'ti-server',
  'fa-shield-alt': 'ti-shield-check',
  'fa-tachometer-alt': 'ti-gauge',
  'fa-bullseye': 'ti-target',
  'fa-globe': 'ti-world',
  'fa-globe-americas': 'ti-world',
  'fa-ethernet': 'ti-ethernet',
  'fa-terminal': 'ti-terminal-2',
  'fa-exclamation-circle': 'ti-alert-circle',
  'fa-exclamation-triangle': 'ti-alert-triangle',
  'fa-check-circle': 'ti-circle-check',
  'fa-times': 'ti-x',
  'fa-check': 'ti-check',
  'fa-info-circle': 'ti-info-circle',
  'fa-question-circle': 'ti-help-circle',
  'fa-chevron-left': 'ti-chevron-left',
  'fa-chevron-right': 'ti-chevron-right',
  'fa-arrow-up': 'ti-arrow-up',
  'fa-arrow-left': 'ti-arrow-left',
  'fa-search': 'ti-search',
  'fa-search-minus': 'ti-search-off',
  'fa-copy': 'ti-copy',
  'fa-download': 'ti-download',
  'fa-upload': 'ti-upload',
  'fa-history': 'ti-history',
  'fa-trash': 'ti-trash',
  'fa-trash-alt': 'ti-trash',
  'fa-cogs': 'ti-settings',
  'fa-save': 'ti-device-floppy',
  'fa-sync': 'ti-refresh',
  'fa-sync-alt': 'ti-refresh',
  'fa-edit': 'ti-edit',
  'fa-plus': 'ti-plus',
  'fa-plus-circle': 'ti-circle-plus',
  'fa-eye': 'ti-eye',
  'fa-eye-slash': 'ti-eye-off',
  'fa-sliders-h': 'ti-adjustments-horizontal',
  'fa-exchange-alt': 'ti-arrows-left-right',
  'fa-star': 'ti-star',
  'fa-key': 'ti-key',
  'fa-link': 'ti-link',
  'fa-keyboard': 'ti-keyboard',
  'fa-home': 'ti-home',
  'fa-clock': 'ti-clock',
  'fa-chart-line': 'ti-chart-line',
  'fa-chart-bar': 'ti-chart-bar',
  'fa-database': 'ti-database',
  'fa-microchip': 'ti-cpu',
  'fa-flask': 'ti-flask',
  'fa-tags': 'ti-tags',
  'fa-map-marker-alt': 'ti-map-pin',
  'fa-eraser': 'ti-eraser',
  'fa-broom': 'ti-broom',
  'fa-linux': 'ti-brand-ubuntu',
  'fa-windows': 'ti-brand-windows',
  'fa-apple': 'ti-brand-apple',
  'fa-android': 'ti-brand-android',
  'fa-ubuntu': 'ti-brand-ubuntu',
  'fa-debian': 'ti-brand-debian',
  'fa-centos': 'ti-brand-redhat',
  'fa-redhat': 'ti-brand-redhat',
  'fa-fedora': 'ti-brand-fedora',
  'fa-suse': 'ti-brand-suse',
  'fa-freebsd': 'ti-brand-freebsd',
  'fa-docker': 'ti-brand-docker',
  'fa-off': 'ti-power'
};

/**
 * 替换单个文件中的Font Awesome图标
 */
function replaceIconsInFile(filePath) {
  console.log(`\n🔄 处理文件: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return 0;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let changes = 0;
  
  // 正则表达式匹配 Font Awesome 图标 (包括fa-solid, fa-regular, fas等)
  const faIconRegex = /<i\s+class="([^"]*(?:fa-solid|fa-regular|fas)[^"]*fa-[a-z-]+[^"]*)"/g;
  
  content = content.replace(faIconRegex, (match, classAttr) => {
    // 提取所有fa-*类名
    const faClasses = classAttr.match(/fa-[a-z-]+/g) || [];
    let newClassAttr = classAttr;
    
    // 移除Font Awesome修饰符
    newClassAttr = newClassAttr.replace(/(?:fa-solid|fa-regular|fas)\s*/g, '');
    
    // 替换每个fa-*图标
    faClasses.forEach(faClass => {
      if (!['fa-solid', 'fa-regular', 'fas'].includes(faClass) && FA_TO_TABLER_MAPPING[faClass]) {
        const tablerIcon = FA_TO_TABLER_MAPPING[faClass];
        newClassAttr = newClassAttr.replace(faClass, `ti ${tablerIcon}`);
        changes++;
        console.log(`  ✅ ${faClass} → ${tablerIcon}`);
      }
    });
    
    return `<i class="${newClassAttr.trim()}"`;
  });
  
  if (changes > 0) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ ${filePath}: 替换了 ${changes} 个图标`);
  } else {
    console.log(`⏸️  ${filePath}: 无需替换`);
  }
  
  return changes;
}

/**
 * 主函数
 */
function main() {
  const files = process.argv.slice(2);
  
  if (files.length === 0) {
    console.log('用法: node batch-replace-fa-icons.js <file1> [file2] ...');
    process.exit(1);
  }
  
  console.log('🚀 开始批量替换Font Awesome图标...');
  
  let totalChanges = 0;
  files.forEach(file => {
    totalChanges += replaceIconsInFile(file);
  });
  
  console.log(`\n🎉 替换完成！总计替换了 ${totalChanges} 个图标`);
}

if (require.main === module) {
  main();
}

module.exports = { replaceIconsInFile, FA_TO_TABLER_MAPPING };
