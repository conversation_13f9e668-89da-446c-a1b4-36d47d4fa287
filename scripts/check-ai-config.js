#!/usr/bin/env node

/**
 * AI配置检查脚本
 * 用于验证 Gemini AI 功能是否正确配置
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 DStatus AI配置检查\n');

// 检查环境变量
console.log('1. 检查环境变量配置:');
const geminiApiKey = process.env.GEMINI_API_KEY;

if (geminiApiKey) {
    console.log('   ✅ GEMINI_API_KEY 已设置');
    console.log(`   📋 密钥长度: ${geminiApiKey.length} 字符`);
    console.log(`   📋 密钥前缀: ${geminiApiKey.substring(0, 8)}...`);
} else {
    console.log('   ❌ GEMINI_API_KEY 未设置');
    console.log('   💡 请设置环境变量或创建 .env 文件');
}

// 检查 .env 文件
console.log('\n2. 检查 .env 文件:');
const envFile = path.join(__dirname, '../.env');
if (fs.existsSync(envFile)) {
    console.log('   ✅ .env 文件存在');
    try {
        const envContent = fs.readFileSync(envFile, 'utf8');
        if (envContent.includes('GEMINI_API_KEY=') && !envContent.includes('your_gemini_api_key_here')) {
            console.log('   ✅ .env 文件中包含 GEMINI_API_KEY 配置');
        } else if (envContent.includes('GEMINI_API_KEY=')) {
            console.log('   ⚠️  .env 文件中有 GEMINI_API_KEY 但未填入实际值');
        } else {
            console.log('   ❌ .env 文件中未找到 GEMINI_API_KEY 配置');
        }
    } catch (error) {
        console.log('   ❌ 无法读取 .env 文件:', error.message);
    }
} else {
    console.log('   ❌ .env 文件不存在');
    console.log('   💡 建议复制 .env.example 为 .env 并配置');
}

// 检查 gemini-service.js 文件
console.log('\n3. 检查 AI 服务文件:');
const geminiServiceFile = path.join(__dirname, '../modules/analytics/ai/gemini-service.js');
if (fs.existsSync(geminiServiceFile)) {
    console.log('   ✅ gemini-service.js 文件存在');
} else {
    console.log('   ❌ gemini-service.js 文件缺失');
}

// 检查依赖
console.log('\n4. 检查依赖包:');
try {
    require('@google/generative-ai');
    console.log('   ✅ @google/generative-ai 包已安装');
} catch (error) {
    console.log('   ❌ @google/generative-ai 包未安装');
    console.log('   💡 运行: npm install @google/generative-ai');
}

// 测试 AI 服务初始化
console.log('\n5. 测试 AI 服务初始化:');
try {
    const GeminiAnalyticsService = require('../modules/analytics/ai/gemini-service');
    const service = new GeminiAnalyticsService();
    
    if (geminiApiKey) {
        console.log('   ✅ AI 服务类可以实例化');
        console.log('   📋 API 密钥已配置，可以尝试初始化');
    } else {
        console.log('   ⚠️  AI 服务类可以实例化，但缺少 API 密钥');
    }
} catch (error) {
    console.log('   ❌ AI 服务初始化失败:', error.message);
}

// 提供建议
console.log('\n💡 配置建议:');

if (!geminiApiKey) {
    console.log('');
    console.log('🚀 快速配置步骤:');
    console.log('1. 访问 https://makersuite.google.com/app/apikey 获取 API 密钥');
    console.log('2. 复制 .env.example 为 .env：');
    console.log('   cp .env.example .env');
    console.log('3. 编辑 .env 文件，填入 API 密钥：');
    console.log('   GEMINI_API_KEY=你的实际API密钥');
    console.log('4. 重启 DStatus 服务');
    console.log('');
    console.log('📖 详细指南: docs/Gemini-AI-配置指南.md');
} else {
    console.log('');
    console.log('✅ AI 功能配置看起来正常!');
    console.log('🎉 您可以在管理面板中使用 AI 智能分析功能');
    console.log('');
    console.log('📍 访问路径: http://localhost:5555/login → AI 智能分析');
}

console.log('');