# CLAUDE.local.md - 项目记忆库

## 用户偏好
- 语言偏好：中文优先，文档命名使用中文，避免表情符号，内容精简直接
- 文件组织：所有临时文件放 `工作区/` 目录，使用子目录分类，避免重复创建

## 技术债务记录  
- dstatus.js 1799行需拆分，目标<500行/模块，分离路由、中间件、服务
- 前端文件大小已调整为实用主义标准，5189个console.log待清理，12处硬编码密钥

## 性能优化机会
- 启用Gzip压缩可减少70%传输量（npm install compression + 1行代码）  
- 静态资源需设置缓存头maxAge:'7d'，可减少90%重复请求

## 项目关键信息
- DStatus分布式监控系统，Node.js+Express+SQLite/PostgreSQL，端口5555
- 双数据库适配器模式，16个核心业务模块，商业许可证管理系统
- 两个数据库：主库data/db.db(监控数据)，license.db(独立项目license-server许可证)
- Litestream仅备份license.db，不备份DStatus主数据库，属于辅助工具非核心功能
- 测试现状：无标准框架，npm test指向不存在文件，需要从零建立TDD体系

## 历史决策
- 2025-08-12: 建立工作区目录结构，分离临时文件与生产代码
- CLAUDE.md作为规范（宪法），CLAUDE.local.md作为记忆库，工作区管理任务
- 2025-08-13: 移动litestream文件到license-server/归档，确认非DStatus核心功能
- 2025-08-13: 确定TDD+Agent-First混合工作流，适合DStatus大规模重构需求
- 2025-08-13: 安装Mocha+Chai测试框架，建立TDD基础，npm test正常运行
- 2025-08-13: 配置Hooks系统，建立D-TDD-C任务管理流程（文档先行+TDD）
- 2025-08-12: 调整文件大小标准：JS≤500行，HTML≤800行，配置文件无限制，实用主义方法
- 2025-08-13: 创建subagent-librarian配置专家，负责创建和配置SubAgent，精确触发条件
- 2025-08-13: 配置计划验证hooks，要求95%信心度，禁止假设，必须基于实际代码
- 2025-08-13: 创建plan-reviewer审查专家，自动审查计划是否符合CLAUDE.md规范
- 2025-08-13: 修复24小时带宽监控：getMaxPoints()限制300点导致截断，改用智能采样实现完整24小时覆盖

## 常用检查命令
- 查找超限文件：`find . -name "*.js" -exec wc -l {} \; | awk '$1 > 500'`
- 检查console.log：`grep -r "console\." --include="*.js" | wc -l`

## 知识库文档
- Hooks和SubAgents：docs/Claude-Code-Hooks-SubAgents-知识库.md（持久参考）
- SubAgent配置专家：.claude/agents/subagent-librarian.md（仅在创建/配置时触发）
- Hooks第一阶段Day3任务，SubAgents第三阶段用于重构

## 代码架构原则
- 文件限制（2025调整）：JS/TS≤500行，HTML≤800行，配置无限制，目录≤8文件
- 七大坏味道：僵化、冗余、循环依赖、脆弱性、晦涩性、数据泥团、过度复杂

## MCP 服务器配置（已验证）
- 目标：在 Claude Code 中全局添加 shrimp-task-manager（MCP）
- 正确命令（全局 user 作用域）：
  - `claude mcp add -s user -e DATA_DIR=/Users/<USER>/code/mcp/task/date -e TEMPLATES_USE=cn -e ENABLE_GUI=false -- shrimp-task-manager npx -y mcp-shrimp-task-manager`
- 验证：`claude mcp get shrimp-task-manager`（期望 Status: ✓ Connected）
- 维护：
  - 列表：`claude mcp list`
  - 删除：`claude mcp remove shrimp-task-manager -s user`
- 语法要点：
  - 本 CLI 形态为：`claude mcp add <name> <commandOrUrl> [args...]`
  - 环境变量用 `-e KEY=VALUE`；可重复多次（或一次包含多对，按 CLI 实现）
  - 将 `-y` 等参数直接传给 `npx`；不要使用 `--command/--args`
  - 避免把 `--` 放在 `npx` 之前（会变成 `npx -- -y ...` 导致启动失败）
