{"all": true, "include": ["modules/**/*.js", "database/**/*.js", "*.js"], "exclude": ["tests/**", "test/**", "scripts/**", "archive/**", "arm-beta/**", "tdd-workspace/**", "工作区/**", "node_modules/**", "coverage/**", "dist/**", "*.config.js", "build-*.js"], "reporter": ["text", "text-summary", "html"], "check-coverage": true, "lines": 80, "functions": 80, "branches": 80, "statements": 80, "watermarks": {"lines": [80, 95], "functions": [80, 95], "branches": [80, 95], "statements": [80, 95]}}