/** @type {import('tailwindcss').Config} */
module.exports = {
    darkMode: 'class',
    content: [
      "./views/**/*.{html,js}",
      "./static/js/**/*.{js,jsx,ts,tsx}",
      "./static/css/**/*.{css,scss}",
    ],
    cache: true, // 启用缓存
    corePlugins: {
      preflight: true,
    },
    plugins: [],
    future: {
      purgeLayersByDefault: true,
    },
    theme: {
      extend: {
        // 统一字体系统
        fontFamily: {
          // 统一入口：首先使用 CSS 变量，运行时可灵活覆盖
          // 回退列表保持精简，CJK 兼容性由 --font-sans 在 CSS 中保证
          'sans': [
            'var(--font-sans)',
            'ui-sans-serif',
            'system-ui',
            'sans-serif'
          ],
          // 等宽字体优先走变量，回退到系统等宽
          'mono': [
            'var(--font-mono)',
            'ui-monospace',
            'monospace'
          ]
        },
        // 核心颜色系统
        colors: {
          primary: {
            50: '#f0f4ff',
            100: '#e0e9ff',
            200: '#c7d6fe',
            300: '#a5b8fc',
            400: '#8b9cf8',
            500: '#484cdc',  /* 主色调 */
            600: '#3d41c7',
            700: '#3336b2',
            800: '#292c9d',
            900: '#1f2288',
            950: '#151873',
          },
          secondary: {
            50: '#f8fafc',
            100: '#f1f5f9',
            200: '#e2e8f0',
            300: '#cbd5e1',
            400: '#94a3b8',
            500: '#64748b',
            600: '#475569',
            700: '#334155',
            800: '#1e293b',
            900: '#0f172a',
            950: '#020617',
          },
          // 功能色
          accent: {
            50: '#faf5ff',
            100: '#f3e8ff',
            200: '#e9d5ff',
            300: '#d8b4fe',
            400: '#c084fc',
            500: '#8b5cf6',  /* 紫色强调色 */
            600: '#7c3aed',
            700: '#6d28d9',
            800: '#5b21b6',
            900: '#4c1d95',
          },
          warning: {
            50: '#fffbeb',
            100: '#fef3c7',
            200: '#fde68a',
            300: '#fcd34d',
            400: '#fbbf24',
            500: '#f59e0b',  /* 琥珀色警告 */
            600: '#d97706',
            700: '#b45309',
            800: '#92400e',
            900: '#78350f',
          },
          error: {
            50: '#fef2f2',
            100: '#fee2e2',
            200: '#fecaca',
            300: '#fca5a5',
            400: '#f87171',
            500: '#ef4444',  /* 红色错误 */
            600: '#dc2626',
            700: '#b91c1c',
            800: '#991b1b',
            900: '#7f1d1d',
          },
          success: {
            50: '#ecfdf5',
            100: '#d1fae5',
            200: '#a7f3d0',
            300: '#6ee7b7',
            400: '#34d399',
            500: '#10b981',  /* 翠绿色成功 */
            600: '#059669',
            700: '#047857',
            800: '#065f46',
            900: '#064e3b',
          },
        },
        // 核心动画
        keyframes: {
          'pulse-slow': {
            '0%, 100%': { opacity: '1' },
            '50%': { opacity: '0.5' }
          },
          'fade-in': {
            'from': { opacity: '0', transform: 'translateY(10px) scale(0.98)' },
            'to': { opacity: '1', transform: 'translateY(0) scale(1)' }
          },
          'fade-out': {
            'from': { opacity: '1', transform: 'translateY(0) scale(1)' },
            'to': { opacity: '0', transform: 'translateY(10px) scale(0.98)' }
          },
        },
        animation: {
          'pulse-slow': 'pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
          'fade-in': 'fade-in 0.3s ease forwards',
          'fade-out': 'fade-out 0.3s ease forwards',
        },
        // 布局相关
        maxWidth: {
          'container': '1440px'
        },
        // 主题相关变量
        borderRadius: {
          'theme': '0.5rem',
        },
        // 阴影系统
        boxShadow: {
          'card': '0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.03)',
          'card-hover': '0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03)',
          'tag-active': '0 0 0 2px rgba(99, 102, 241, 0.3)',
        },
        // 过渡属性
        transitionProperty: {
          'height': 'height',
          'width': 'width',
          'transform-opacity': 'transform, opacity'
        },
        // 自定义过渡时间和缓动函数
        transitionDuration: {
          '800': '800ms',
        },
        transitionTimingFunction: {
          'network': 'cubic-bezier(0.22, 1, 0.36, 1)',
          'smooth': 'cubic-bezier(0.22, 1, 0.36, 1)',
        },
      },
      // 响应式断点
      screens: {
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      }
    },
    // 保留必要的safelist配置
    safelist: [
      {
        pattern: /bg-(blue|green|purple|red|yellow)-\d+/,
        variants: ['hover', 'dark', 'dark:hover'],
      }
    ],
    plugins: [
      // 隐藏滚动条插件
      function({ addUtilities }) {
        const newUtilities = {
          '.scrollbar-hide': {
            /* Firefox */
            'scrollbar-width': 'none',
            /* Safari和Chrome */
            '&::-webkit-scrollbar': {
              'display': 'none'
            }
          },
          '.scrollbar-thin': {
            /* Firefox */
            'scrollbar-width': 'thin',
            /* Safari和Chrome */
            '&::-webkit-scrollbar': {
              'width': '6px',
              'height': '6px'
            },
            '&::-webkit-scrollbar-track': {
              'background': 'transparent'
            },
            '&::-webkit-scrollbar-thumb': {
              'background': 'rgba(100, 116, 139, 0.5)',
              'border-radius': '9999px'
            }
          }
        }
        addUtilities(newUtilities, ['responsive']);
      },
      // 伪元素插件
      function({ addVariant }) {
        addVariant('before', '&::before');
        addVariant('after', '&::after');
      }
    ],
  }
