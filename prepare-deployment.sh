#!/bin/bash
# 准备部署包脚本（不包含 node_modules）

set -e

echo "📦 准备 DStatus 部署包..."
echo "========================"

# 创建临时目录
DEPLOY_DIR="dstatus-deploy"
rm -rf "${DEPLOY_DIR}"
mkdir -p "${DEPLOY_DIR}"

# 复制必要文件
echo "📋 复制项目文件..."
cp package.json "${DEPLOY_DIR}/"
cp nekonekostatus.js "${DEPLOY_DIR}/"
cp core.js "${DEPLOY_DIR}/"
cp -r modules "${DEPLOY_DIR}/"
cp -r database "${DEPLOY_DIR}/"
cp -r config "${DEPLOY_DIR}/" 2>/dev/null || true
cp -r static "${DEPLOY_DIR}/"
cp -r views "${DEPLOY_DIR}/"
cp pkg-*.js "${DEPLOY_DIR}/" 2>/dev/null || true
cp -r ssh "${DEPLOY_DIR}/" 2>/dev/null || true

# 混淆前端代码
echo "🔐 混淆前端代码..."
if ! command -v terser &> /dev/null; then
    npm install -g terser
fi

find "${DEPLOY_DIR}/static/js" -name "*.js" -type f | while read file; do
    if [[ ! "$file" =~ \.min\.js$ ]]; then
        terser "$file" --compress --mangle -o "$file" 2>/dev/null || true
    fi
done

# 复制安装脚本
cp install-and-run.sh "${DEPLOY_DIR}/"
chmod +x "${DEPLOY_DIR}/install-and-run.sh"

# 创建 README
cat > "${DEPLOY_DIR}/README.md" << 'EOF'
# DStatus Linux 部署指南

## 系统要求
- Linux x64 系统（Ubuntu、Debian、CentOS 等）
- Node.js 20.x 或更高版本
- Python3 和构建工具（用于编译 better-sqlite3）

## 安装步骤

1. 安装 Node.js（如果尚未安装）：
   ```bash
   # Ubuntu/Debian
   curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
   sudo apt-get install -y nodejs build-essential python3

   # CentOS/RHEL
   curl -fsSL https://rpm.nodesource.com/setup_20.x | sudo bash -
   sudo yum install -y nodejs gcc-c++ make python3
   ```

2. 解压并进入目录：
   ```bash
   tar -xzf dstatus-deploy.tar.gz
   cd dstatus-deploy
   ```

3. 运行安装脚本：
   ```bash
   ./install-and-run.sh
   ```

## 作为系统服务运行

创建 systemd 服务文件 `/etc/systemd/system/dstatus.service`：

```ini
[Unit]
Description=DStatus Server Monitor
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/dstatus-deploy
ExecStart=/usr/bin/node nekonekostatus.js
Restart=on-failure
RestartSec=10
StandardOutput=append:/var/log/dstatus.log
StandardError=append:/var/log/dstatus.log

[Install]
WantedBy=multi-user.target
```

启用并启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable dstatus
sudo systemctl start dstatus
```

## 端口
- 主服务：5555

## 安全说明
- 前端 JavaScript 已混淆压缩
- 建议使用防火墙限制访问
- 定期备份 data 目录
EOF

# 创建压缩包
echo "📦 创建部署包..."
tar -czf dstatus-deploy.tar.gz "${DEPLOY_DIR}"

# 清理
rm -rf "${DEPLOY_DIR}"

echo ""
echo "✅ 部署包创建完成！"
echo ""
echo "📦 文件: dstatus-deploy.tar.gz"
echo "📏 大小: $(du -h dstatus-deploy.tar.gz | cut -f1)"
echo ""
echo "部署步骤："
echo "1. 上传 dstatus-deploy.tar.gz 到服务器"
echo "2. 解压: tar -xzf dstatus-deploy.tar.gz"
echo "3. 进入目录: cd dstatus-deploy"
echo "4. 运行: ./install-and-run.sh"
echo ""
echo "注意：目标服务器需要安装 Node.js 20.x 和构建工具"