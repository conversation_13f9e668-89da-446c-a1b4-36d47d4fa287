本文件用于指导 Claude Code（claude.ai/code） 在本仓库中的工作方式。

⸻



1. Project Overview

DStatus：分布式服务器监控系统（含商业许可证管理）。提供实时监控、分析与多层授权，支持 AI 辅助分析。
	•	Tech Stack：Node.js + Express + SQLite/PostgreSQL + Nunjucks + Vanilla JS
	•	Architecture：模块化单体（Modular Monolith），双数据库适配器
	•	Default Port：5555
	•	License Server：https://dstatus_api.vps.mom
	•	Version：2.25.08.005.beta
	•	Node Requirements：>=20.17.0 || >=22.9.0
	•	Main Entry：dstatus.js

⸻

2. 用户空间契约（强制）

以下统称“用户空间”（不可破坏）：
	•	探针协议（agent ↔ server）、HTTP/WS API（字段与默认值）
	•	CLI 子命令与 flags
	•	配置文件 schema（含默认值）
	•	数据库 schema / 索引 / 迁移语义

兼容性宪章：
	•	层级：stable / soft-deprecated / experimental
	•	弃用：公告 ≥2 个次版本，并提供兼容层或自动迁移脚本；只增字段，不改默认值/语义。
	•	协议：以显式版本协商（如 X-DStatus-Proto: v1|v2），默认回退旧版。
	•	发布与回滚：所有新能力挂 feature flag；提供 UPGRADING.md 回滚剧本（含 DB down）。

仓库新增/要求文件（建议立即补齐）：
	•	COMPATIBILITY.md（兼容宪章落地细则）
	•	UPGRADING.md（升级/回滚剧本与步骤，含 DB down）
	•	API_CHANGELOG.(json|yml)（机器可读契约变更清单）
	•	MIGRATIONS/（sqlite/pg 成对 up/down 脚本 + 往返快照一致性测试）

⸻

3. ⚠️ 关键数据库设计警示（保留并强化）

禁止修改 database/load.js 第 51 行 ORDER BY created_at DESC。

该处 shift + ORDER BY created_at DESC 实现滑动窗口：
	•	保留最新 N 条，删除最老记录
	•	改为 ASC 会删除最新记录，直接破坏核心监控
	•	监控子系统依赖 DESC 取最新数据

时间序显示问题：只能在前端排序，绝不修改数据库层排序。

契约测试建议（新增）：

# 生成与回放固定“负载写入 → 读取窗口”的金样测试
npm run test:migration       # 迁移正确性
npm run test:performance    # 时间窗读取性能基线
npm run test:stress         # 高并发下窗口一致性


⸻

4. 基本开发命令

**开发环境**
- `npm run dev` - 智能开发启动（端口检测+CSS/JS热重载）
- `npm run test:server` - 测试服务器（端口5556，独立数据库）

**测试**
- `npm test` - 完整测试套件
- `npm run test:migration` - 数据库迁移测试
- `npm run test:performance` - 性能基线测试

**构建**
- `npm run build` - 交互式本地构建
- `npm run build:all` - 跨平台构建


⸻

5. 高层架构（总览）

DStatus Monitoring System
├─ 🌐 Frontend: Nunjucks SSR + Vanilla JS
│  ├─ views/
│  ├─ static/js/
│  └─ static/css/  # TailwindCSS
│
├─ 🔌 API: Express + WebSocket (express-ws)
│  ├─ modules/api/
│  └─ 中间件：鉴权 / 限流 / 日志
│
├─ 💼 业务层（16 模块）
│  ├─ license-enhanced/   # 商业许可核心
│  ├─ servers/            # 服务器管理
│  ├─ stats/              # 统计处理
│  ├─ analytics/          # AI 分析
│  └─ notification/ 等
│
├─ 🗄️ 数据层（双适配器）
│  ├─ adapters/sqlite.js
│  └─ adapters/postgresql.js
│  ├─ 业务表：servers/groups/load/traffic
│  └─ 缓存：Redis 兼容
│
└─ 🔧 基设施
   ├─ 日志 / 配置 / 定时任务（node-schedule）
   └─ 进程（PM2）


⸻

6. 数据库架构与方言层

SQLite（默认，WAL 优化）
	•	性能：WAL 提升 300–500%
	•	配置：64MB cache / mmap / 自动 checkpoint
	•	适用：开发、小中型部署

PostgreSQL（生产）
	•	可扩展：连接池（20）
	•	能力：高级索引、FTS、JSON
	•	适用：大规模生产

切换数据库：DB_TYPE=postgresql

方言接口（强烈建议）：统一封装 upsert/批量写入/时间桶化，业务层不直接 import sqlite3/pg。

⸻

7. 核心业务模块（摘要）
	•	license-enhanced/（17 子模块，商业授权）
	•	api/（REST）
	•	servers/（主机与 SSH）
	•	stats/（实时统计）
	•	analytics/（Gemini AI 分析）
	•	notification/、groups/、ssh_scripts/、autodiscovery/、admin/ …

⸻

8. 许可证体系

位图特性：

const FEATURES = {
  BASIC_MONITORING: 1,
  WEBSSH: 2,
  AUTO_DISCOVERY: 4,
  ADVANCED_ANALYTICS: 8,
  API_ACCESS: 16,
  CUSTOM_ALERTS: 32,
  AI_ANALYTICS: 64
};

等级： Free(5 节点) / Standard(20 + WebSSH) / Professional(50 + 高级分析) / Enterprise(无限 + 全特性)

⸻

9. 配置

# 基本
NODE_ENV=production|development
DSTATUS_PORT=5555
DB_TYPE=sqlite|postgresql

# PostgreSQL
DB_HOST=localhost
DB_PORT=5432
DB_USER=dstatus
DB_PASSWORD=your_password
DB_NAME=dstatus

# License
LICENSE_SERVER_URL=https://dstatus_api.vps.mom
LICENSE_KEY=your_license_key

# AI
GEMINI_API_KEY=your_gemini_api_key


⸻

10. 代码质量与结构红线（强制）
	•	业务函数≤ 3 层缩进；超过先改数据结构消分支。
	•	禁止“布尔参数”触发行为分叉；用策略对象或枚举。
	•	单一职责：每文件/模块只做一件事。
	•	不直接导入 sqlite3/pg；只经适配器/方言接口。
	•	软删除：使用 deleted_at。
	•	不硬编码限额：从 DB 读取 plan 限制。
	•	SSR-first：Nunjucks + 渐进增强；WS 使用 express-ws；Tailwind 组件化。
	•	安全：硬件指纹许可绑定、JWT 会话、输入验证、参数化查询、输出转义。
	•	命名：files-kebab、Class Pascal、fn/var camel、CONST_UPPER、db snake_case。


⸻

11. 性能与资源红线（CI 守门）
	•	单实例：CPU < 2%，RSS < 80MB，FD < 1024
	•	500 并发 WS：P95 推送 < 1s
	•	CI 必含：
	1.	契约回归：旧版 CLI/Agent/API 黑盒回放（固定输入/输出金样）
	2.	性能回归：固定数据/速率的 P95/P99 基线（越界即 fail）

⸻

12. 迁移与发布纪律
	•	迁移成对：所有 DB 迁移 up/down 成对；加入“往返一致性”测试。
	•	双写 + 影子读：引入新表/索引/字段时，先双写/影子读一到两个版本，监控对比吻合再切流。
	•	只增不改默认：新增字段/行为只增不改旧默认。
	•	Feature flags：新能力默认关闭，可一键回退。
	•	UPGRADING.md：必须写明回滚剧本（含 DB down）。

⸻

13. 测试策略
	•	目录：tests/unit / tests/integration / tests/performance / tests/stress
	•	TDD（红-绿-重构）：npm run test:watch
	•	覆盖率：≥ 80%
	•	示例（略，沿用原文）

实用校验脚本：




# 敏感硬编码
grep -r -E "(api[_-]?key|password|secret)" --include="*.js"


⸻

14. 构建系统

使用 @yao-pkg/pkg 生成独立二进制：
	•	Targets：Linux(x64/arm64)、macOS(arm64)
	•	Assets：views/static/native modules
	•	优化：DCE、打包资源
	•	原生模块：预编译 better-sqlite3
	•	平台自适应：Apple/ARM64 完整支持

⸻

15. AI 集成（Gemini）
	•	模型：Gemini 1.5 Pro
	•	能力：智能报表、异常检测、优化建议
	•	前提：Pro/Enterprise + API Key
	•	文档：docs/Gemini-AI-配置指南.md

⸻

16. Git 工作流与 PR 模板（落地守门）

提交前：

npm test
npm run test:coverage
# 规模与调试检查（建议做成 pre-commit 钩子）

PR 必填（模板建议放 .github/PULL_REQUEST_TEMPLATE.md）：
	•	这是真问题吗？证据：
	•	更简单的实现？对比：
	•	破坏面（API/CLI/配置/DB/协议）与缓解：
	•	兼容与弃用：stable/soft-deprecated/experimental 标注；替代与迁移：
	•	迁移：up/down 是否成对；是否双写/影子读；回滚步骤：
	•	性能与资源：P95/P99/CPU/RSS/FD 变化：
	•	复杂度控制：最大缩进层级、函数职责单一性、是否移除布尔分岔：

⸻

17. Claude Code 使用守则（关键）

默认不反问，直接交付最小可行改动。

编辑前必做：
	1.	Read 文件，确认待改内容存在且唯一；
	2.	分析是否触及“用户空间契约”；如触及，停止并改走“只增/兼容层/flag/迁移”；
	3.	若函数缩进 >3 层或充满 if，先重构数据结构消分支，再动业务。

编辑时：
	•	生成最小 diff；保留格式/风格；所有异步await。
	•	禁止直接改数据库排序与关键路径；如需窗口语义变化，只能在前端或策略层处理。
	•	不新增布尔分岔；使用策略对象/枚举。

编辑后：
	•	运行相关测试（含契约回归/性能回归）；
	•	如涉及数据结构/协议，更新 API_CHANGELOG 与 UPGRADING.md；
	•	提交 PR，按模板填写破坏面与回滚。

⸻

18. 质量检查清单（落地）
	•	COMPATIBILITY.md / UPGRADING.md / API_CHANGELOG / MIGRATIONS 是否齐全
	•	关键窗口逻辑仍为 ORDER BY created_at DESC（DB 层），前端自行排序
	•	新代码无布尔行为分岔，缩进 ≤3 层
	•	契约回归与性能回归过线（越界 fail）
	•	如有新存储路径：已双写 + 影子读至少 1 个版本
	•	文档与脚本同步更新

⸻

19. 思维框架（精简版）
	•	第一性原理：先列出不可变事实与不变量
	•	系统思考：识别反馈环与约束（CPU/RSS/FD/延迟）
	•	MECE：方案拆分不重不漏；5W2H 明确责任/验收/回滚

⸻
## 工具使用

### 文档工具
1. **查看官方文档**
   - `resolve-library-id` - 解析库名到 Context7 ID
   - `get-library-docs` - 获取最新官方文档



2. **搜索真实代码**
   - `searchGitHub` - 搜索 GitHub 上的实际使用案例


### 编写规范文档工具
编写需求和设计文档时使用 `specs-workflow`：

1. **检查进度**: `action.type="check"` 
2. **初始化**: `action.type="init"`
3. **更新任务**: `action.type="complete_task"`

路径：`/docs/specs/*`


一句话：
别动用户空间契约；先把特殊情况变成一般情况；用最笨但最清晰的实现；一切以回归结果说话。
- 用户没有要求的情况下，不应该主动帮用户重启项目和启动项目，
- 功能模块需要独立文件夹和文件 需要符合行业开发标准
- 单次任务中的测试文件需要清理